package payments

import (
	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"

	internalErrs "git.redmadrobot.com/zaman/backend/zaman/errs"
)

const (
	CodePaymentsIncorrectInput                = "PaymentsIncorrectInputError"
	CodePaymentsIncorrectUserProfile          = "UserProfileIncorrect"
	CodePaymentsInvalidOTP                    = "InvalidOTP"
	CodeInvalidPayerBinIIN                    = "InvalidPayerBinIINError"
	CodeInvalidBeneficiaryBinIIN              = "InvalidBeneficiaryBinIINError"
	CodeInvalidPayerAccount                   = "InvalidPayerAccountError"
	CodeInvalidBeneficiaryAccount             = "InvalidBeneficiaryAccountError"
	CodeAmountIsNotDecimal                    = "AmountIsNotDecimalError"
	CodeBeneficiaryNameTooLong                = "InvalidBeneficiaryTooLongError"
	CodePaymentDetailsTooLong                 = "PaymentDetailsTooLongError"
	CodeInvalidActualSenderBinIIN             = "InvalidActualSenderBinIINError"
	CodeInvalidActualBeneficiaryBinIIN        = "InvalidActualBeneficiaryBinIINError"
	CodeInvalidTransactionID                  = "InvalidTransactionIDError"
	CodeInvalidAttemptID                      = "InvalidAttemptIDError"
	CodeTransactionNotFound                   = "TransactionNotFoundError"
	CodeInvalidCurrency                       = "InvalidCurrencyError"
	CodeTransactionStepNotFound               = "TransactionStepNotFoundError"
	CodePaymentsInvalidAccountNumber          = "InvalidAccountNumber"
	CodeInvalidIdempotencyID                  = "InvalidIdempotencyIDError"
	CodeDublicatePayment                      = "DublicatePayment"
	CodeUserBlocked                           = "UserBlocked"
	CodeOTPNumberAttemptsExceeded             = "OTPNumberAttemptsExceeded"
	CodeTransactionTypeNotAvailableForReceipt = "TransactionTypeNotAvailableForReceipt"
	CodeUserAccountNotFound                   = "UserAccountNotFound"
	CodeBankByIbanCodeNotFound                = "BankByIbanCodeNotFound"
	CodeTaxPayerNotFound                      = "TaxPayerNotFound"
	CodeInvalidPurposeCode                    = "InvalidPurposeCode"
	CodeInvalidBeneficiaryCode                = "InvalidBeneficiaryCode"
	CodeInvalidBeneficiaryBankBic             = "InvalidBeneficiaryBankBic"
	CodeGrossErrorsWhenExecutePayment         = "GrossErrorsWhenExecutePayment"
	CodeColvirTimeoutExpired                  = "ColvirTimeoutExpired"
	CodeInvalidPhoneNumber                    = "InvalidPhoneNumber"
	CodePhoneOperatorNotFound                 = "PhoneOperatorNotFound"
	CodeClientNotFoundByPhoneNumber           = "ClientNotFoundByPhoneNumber"
	CodeMonthlyPaymentLimitExceeded           = "MonthlyPaymentLimitExceeded"
	CodeOneTimePaymentLimitExceeded           = "OneTimePaymentLimitExceeded"
	CodeNotEnoughMoneyInAccounts              = "NotEnoughMoneyInAccounts"
	CodeAntifraudСheckFailed                  = "AntifraudСheckFailed"
	CodeAMLСheckFailed                        = "AMLСheckFailed"
	CodeNoAvailableAccountsForPayment         = "NoAvailableAccountsForPayment"
	CodeInvalidOperDate                       = "InvalidOperDate"
	CodePaymentErrorPC                        = "PaymentErrorPC"
	CodePaymentErrorColvir                    = "PaymentErrorColvir"
	CodeRejectedByAP                          = "RejectedByAP"
	CodeInvalidMCC                            = "InvalidMCC"
	CodeErrorNoAccount                        = "ErrorNoAccount"
	CodeErrorNoCard                           = "ErrorNoCard"
	CodeKaspiSuspended                        = "KaspiSuspended"
	CodeOverLimit                             = "OverLimit"
)

func PaymentsErrs() PaymentsErrors {
	return &PaymentsErrorsList{
		errMap: map[string]internalErrs.Reason{
			CodePaymentsIncorrectInput:                {1, "PaymentsIncorrectInputError", errs.TypeIllegalArgument, "Некорректный ввод"},
			CodePaymentsIncorrectUserProfile:          {2, "UserProfileIncorrect", errs.TypeNotFound, "Профиль пользователя не найден или не заполнен"},
			CodePaymentsInvalidOTP:                    {3, "CodePaymentsInvalidOTP", errs.TypeIllegalArgument, "Неверный одноразовый пароль"},
			CodeInvalidPayerBinIIN:                    {5, "InvalidPayerBinIINError", errs.TypeIllegalArgument, "ИИН / БИН пользователя введен не верно"},
			CodeInvalidBeneficiaryBinIIN:              {6, "InvalidBeneficiaryBinIINError", errs.TypeIllegalArgument, "ИИН / БИН бенефициара введен не верно"},
			CodeInvalidPayerAccount:                   {7, "InvalidPayerAccountError", errs.TypeIllegalArgument, "Счет пользователя введен не верно"},
			CodeInvalidBeneficiaryAccount:             {8, "InvalidBeneficiaryAccountError", errs.TypeIllegalArgument, "Счет бенефициара введен не верно"},
			CodeAmountIsNotDecimal:                    {9, "AmountIsNotDecimalError", errs.TypeIllegalArgument, "Сумма введена не верно"},
			CodeBeneficiaryNameTooLong:                {10, "InvalidBeneficiaryTooLongError", errs.TypeIllegalArgument, "Имя бенефициара слишком длинное"},
			CodePaymentDetailsTooLong:                 {11, "PaymentDetailsTooLongError", errs.TypeIllegalArgument, "Детали платежа слишком длинное"},
			CodeInvalidActualSenderBinIIN:             {12, "InvalidActualSenderBinIINError", errs.TypeIllegalArgument, "ИИН / БИН реального плательщика введен не верно"},
			CodeInvalidActualBeneficiaryBinIIN:        {13, "InvalidActualBeneficiaryBinIINError", errs.TypeIllegalArgument, "ИИН / БИН реального бенефициара введен не верно"},
			CodeInvalidTransactionID:                  {14, "InvalidTransactionIDError", errs.TypeIllegalArgument, "ID транзакции введен не верно"},
			CodeInvalidAttemptID:                      {15, "InvalidAttemptIDError", errs.TypeIllegalArgument, "AttemptID отп кода введен не верно"},
			CodeTransactionNotFound:                   {16, "TransactionNotFoundError", errs.TypeNotFound, "Транзакция платежа не найдена"},
			CodeInvalidCurrency:                       {17, "InvalidCurrencyError", errs.TypeIllegalArgument, "Неверный значение валюты перевода"},
			CodeTransactionStepNotFound:               {18, "TransactionStepNotFoundError", errs.TypeNotFound, "Шаг транзакции не найден"},
			CodePaymentsInvalidAccountNumber:          {19, "InvalidAccountNumber", errs.TypeIllegalArgument, "Неверный номер счета"},
			CodeInvalidIdempotencyID:                  {20, "InvalidIdempotencyIDError", errs.TypeIllegalArgument, "Не верный формат idempotency ID "},
			CodeDublicatePayment:                      {21, "DublicatePayment", errs.TypeIllegalArgument, "Дубликат платежа, платёж с таким idempotency id уже сущевствует"},
			CodeUserBlocked:                           {22, "UserBlocked", errs.TypeIllegalArgument, "Пользователь заблокирован"},
			CodeOTPNumberAttemptsExceeded:             {23, "OTPNumberAttemptsExceeded", errs.TypeIllegalArgument, "Превышено количество попыток ввода отп"},
			CodeTransactionTypeNotAvailableForReceipt: {24, "TransactionTypeNotAvailableForReceipt", errs.TypeIllegalArgument, "Недоступный тип транзакции для квитанции"},
			CodeUserAccountNotFound:                   {25, "UserAccountNotFound", errs.TypeIllegalArgument, "У пользователя не найден введённый номер счёта"},
			CodeBankByIbanCodeNotFound:                {26, "BankByIbanCodeNotFound", errs.TypeIllegalArgument, "Банк по ibanCode не найден"},
			CodeTaxPayerNotFound:                      {27, "TaxPayerNotFound", errs.TypeIllegalArgument, "Налогоплатильщик не найден по ИИН/БИН"},
			CodeInvalidPurposeCode:                    {28, "InvalidPurposeCode", errs.TypeIllegalArgument, "Неверный код назначения платежа"},
			CodeInvalidBeneficiaryCode:                {29, "InvalidBeneficiaryCode", errs.TypeIllegalArgument, "Неверный код бенефициара"},
			CodeInvalidBeneficiaryBankBic:             {30, "InvalidBeneficiaryBankBic", errs.TypeIllegalArgument, "Неверный бик банка бенефициара"},
			CodeGrossErrorsWhenExecutePayment:         {31, "GrossErrorsWhenExecutePayment", errs.TypeIllegalArgument, "Обнаружены грубые ошибки при выполнении платежа"},
			CodeColvirTimeoutExpired:                  {32, "ColvirTimeoutExpired", errs.TypeIllegalArgument, "Превышено ожидание запроса на создание платежа"},
			CodeInvalidPhoneNumber:                    {33, "InvalidPhoneNumber", errs.TypeIllegalArgument, "Неверный номер телефона"},
			CodePhoneOperatorNotFound:                 {34, "PhoneOperatorNotFound", errs.TypeIllegalArgument, "Оператор по номеру телефона не найден"},
			CodeClientNotFoundByPhoneNumber:           {35, "ClientNotFoundByPhoneNumber", errs.TypeIllegalArgument, "Клиент банка не найден по номеру телефона"},
			CodeMonthlyPaymentLimitExceeded:           {36, "MonthlyPaymentLimitExceeded", errs.TypeIllegalArgument, "Превышен месячный лимит на перевод"},
			CodeOneTimePaymentLimitExceeded:           {37, "OneTimePaymentLimitExceeded", errs.TypeIllegalArgument, "Превышен разовый лимит на перевод"},
			CodeNotEnoughMoneyInAccounts:              {38, "NotEnoughMoneyInAccounts", errs.TypeIllegalArgument, "Недостаточно средств на счёте для выполнения операции"},
			CodeAntifraudСheckFailed:                  {39, "AntifraudСheckFailed", errs.TypeIllegalArgument, "Проверка антифрода не пройдена"},
			CodeAMLСheckFailed:                        {40, "AMLСheckFailed", errs.TypeIllegalArgument, "Проверка AML не пройдена"},
			CodeNoAvailableAccountsForPayment:         {41, "NoAvailableAccountsForPayment", errs.TypeIllegalArgument, "Нет доступных счетов для перевода"},
			CodeInvalidOperDate:                       {42, CodeInvalidOperDate, errs.TypeIllegalArgument, "Операционная дата не определена"},
			CodePaymentErrorPC:                        {43, CodePaymentErrorPC, errs.TypeIllegalArgument, "Ошибка от процессинга"},
			CodePaymentErrorColvir:                    {44, CodePaymentErrorColvir, errs.TypeIllegalArgument, "Ошибка проведения платежа в Colvir"},
			CodeRejectedByAP:                          {45, CodeRejectedByAP, errs.TypeIllegalArgument, "Ошибка обращения к ПС Астана Плат"},
			CodeInvalidMCC:                            {46, CodeInvalidMCC, errs.TypeIllegalArgument, "МСС входит в список запрещенных к проведению"},
			CodeErrorNoAccount:                        {47, CodeErrorNoAccount, errs.TypeIllegalArgument, "Счет арестован или остаток < 0"},
			CodeErrorNoCard:                           {48, CodeErrorNoCard, errs.TypeIllegalArgument, "У клиента нет счета в процессинге"},
			CodeKaspiSuspended:                        {49, CodeKaspiSuspended, errs.TypeInternal, "Получена ошибка при запросе на расшифровку QR-токена либо метод недоступен"},
			CodeOverLimit:                             {50, CodeOverLimit, errs.TypeIllegalArgument, "Платеж выше установленного лимита"},
		},
	}
}
