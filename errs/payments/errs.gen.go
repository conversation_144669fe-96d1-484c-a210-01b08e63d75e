// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package payments

import (
	internalErrs "git.redmadrobot.com/zaman/backend/zaman/errs"
)

type (
	PaymentsErrorsList struct {
		errMap map[string]internalErrs.Reason
	}

	PaymentsErrors interface {
	        CodeAMLСheckFailedError() error
	        CodeAMLСheckFailedDetailedError(details map[string]string) error
	        CodeAmountIsNotDecimalError() error
	        CodeAmountIsNotDecimalDetailedError(details map[string]string) error
	        CodeAntifraudСheckFailedError() error
	        CodeAntifraudСheckFailedDetailedError(details map[string]string) error
	        CodeBankByIbanCodeNotFoundError() error
	        CodeBankByIbanCodeNotFoundDetailedError(details map[string]string) error
	        CodeBeneficiaryNameTooLongError() error
	        CodeBeneficiaryNameTooLongDetailedError(details map[string]string) error
	        CodeClientNotFoundByPhoneNumberError() error
	        CodeClientNotFoundByPhoneNumberDetailedError(details map[string]string) error
	        CodeColvirTimeoutExpiredError() error
	        CodeColvirTimeoutExpiredDetailedError(details map[string]string) error
	        CodeDublicatePaymentError() error
	        CodeDublicatePaymentDetailedError(details map[string]string) error
	        CodeErrorNoAccountError() error
	        CodeErrorNoAccountDetailedError(details map[string]string) error
	        CodeErrorNoCardError() error
	        CodeErrorNoCardDetailedError(details map[string]string) error
	        CodeGrossErrorsWhenExecutePaymentError() error
	        CodeGrossErrorsWhenExecutePaymentDetailedError(details map[string]string) error
	        CodeInvalidActualBeneficiaryBinIINError() error
	        CodeInvalidActualBeneficiaryBinIINDetailedError(details map[string]string) error
	        CodeInvalidActualSenderBinIINError() error
	        CodeInvalidActualSenderBinIINDetailedError(details map[string]string) error
	        CodeInvalidAttemptIDError() error
	        CodeInvalidAttemptIDDetailedError(details map[string]string) error
	        CodeInvalidBeneficiaryAccountError() error
	        CodeInvalidBeneficiaryAccountDetailedError(details map[string]string) error
	        CodeInvalidBeneficiaryBankBicError() error
	        CodeInvalidBeneficiaryBankBicDetailedError(details map[string]string) error
	        CodeInvalidBeneficiaryBinIINError() error
	        CodeInvalidBeneficiaryBinIINDetailedError(details map[string]string) error
	        CodeInvalidBeneficiaryCodeError() error
	        CodeInvalidBeneficiaryCodeDetailedError(details map[string]string) error
	        CodeInvalidCurrencyError() error
	        CodeInvalidCurrencyDetailedError(details map[string]string) error
	        CodeInvalidIdempotencyIDError() error
	        CodeInvalidIdempotencyIDDetailedError(details map[string]string) error
	        CodeInvalidMCCError() error
	        CodeInvalidMCCDetailedError(details map[string]string) error
	        CodeInvalidOperDateError() error
	        CodeInvalidOperDateDetailedError(details map[string]string) error
	        CodeInvalidPayerAccountError() error
	        CodeInvalidPayerAccountDetailedError(details map[string]string) error
	        CodeInvalidPayerBinIINError() error
	        CodeInvalidPayerBinIINDetailedError(details map[string]string) error
	        CodeInvalidPhoneNumberError() error
	        CodeInvalidPhoneNumberDetailedError(details map[string]string) error
	        CodeInvalidPurposeCodeError() error
	        CodeInvalidPurposeCodeDetailedError(details map[string]string) error
	        CodeInvalidTransactionIDError() error
	        CodeInvalidTransactionIDDetailedError(details map[string]string) error
	        CodeKaspiSuspendedError() error
	        CodeKaspiSuspendedDetailedError(details map[string]string) error
	        CodeMonthlyPaymentLimitExceededError() error
	        CodeMonthlyPaymentLimitExceededDetailedError(details map[string]string) error
	        CodeNoAvailableAccountsForPaymentError() error
	        CodeNoAvailableAccountsForPaymentDetailedError(details map[string]string) error
	        CodeNotEnoughMoneyInAccountsError() error
	        CodeNotEnoughMoneyInAccountsDetailedError(details map[string]string) error
	        CodeOTPNumberAttemptsExceededError() error
	        CodeOTPNumberAttemptsExceededDetailedError(details map[string]string) error
	        CodeOneTimePaymentLimitExceededError() error
	        CodeOneTimePaymentLimitExceededDetailedError(details map[string]string) error
	        CodeOverLimitError() error
	        CodeOverLimitDetailedError(details map[string]string) error
	        CodePaymentDetailsTooLongError() error
	        CodePaymentDetailsTooLongDetailedError(details map[string]string) error
	        CodePaymentErrorColvirError() error
	        CodePaymentErrorColvirDetailedError(details map[string]string) error
	        CodePaymentErrorPCError() error
	        CodePaymentErrorPCDetailedError(details map[string]string) error
	        IncorrectInputError() error
	        IncorrectInputDetailedError(details map[string]string) error
	        IncorrectUserProfileError() error
	        IncorrectUserProfileDetailedError(details map[string]string) error
	        InvalidAccountNumberError() error
	        InvalidAccountNumberDetailedError(details map[string]string) error
	        InvalidOTPError() error
	        InvalidOTPDetailedError(details map[string]string) error
	        CodePhoneOperatorNotFoundError() error
	        CodePhoneOperatorNotFoundDetailedError(details map[string]string) error
	        CodeRejectedByAPError() error
	        CodeRejectedByAPDetailedError(details map[string]string) error
	        CodeTaxPayerNotFoundError() error
	        CodeTaxPayerNotFoundDetailedError(details map[string]string) error
	        CodeTransactionNotFoundError() error
	        CodeTransactionNotFoundDetailedError(details map[string]string) error
	        CodeTransactionStepNotFoundError() error
	        CodeTransactionStepNotFoundDetailedError(details map[string]string) error
	        CodeTransactionTypeNotAvailableForReceiptError() error
	        CodeTransactionTypeNotAvailableForReceiptDetailedError(details map[string]string) error
	        CodeUserAccountNotFoundError() error
	        CodeUserAccountNotFoundDetailedError(details map[string]string) error
	        CodeUserBlockedError() error
	        CodeUserBlockedDetailedError(details map[string]string) error
	}
)
func (e *PaymentsErrorsList) CodeAMLСheckFailedError() error {
	return e.errMap[CodeAMLСheckFailed].Err()
}

func (e *PaymentsErrorsList) CodeAMLСheckFailedDetailedError(details map[string]string) error {
	err := e.errMap[CodeAMLСheckFailed]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeAmountIsNotDecimalError() error {
	return e.errMap[CodeAmountIsNotDecimal].Err()
}

func (e *PaymentsErrorsList) CodeAmountIsNotDecimalDetailedError(details map[string]string) error {
	err := e.errMap[CodeAmountIsNotDecimal]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeAntifraudСheckFailedError() error {
	return e.errMap[CodeAntifraudСheckFailed].Err()
}

func (e *PaymentsErrorsList) CodeAntifraudСheckFailedDetailedError(details map[string]string) error {
	err := e.errMap[CodeAntifraudСheckFailed]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeBankByIbanCodeNotFoundError() error {
	return e.errMap[CodeBankByIbanCodeNotFound].Err()
}

func (e *PaymentsErrorsList) CodeBankByIbanCodeNotFoundDetailedError(details map[string]string) error {
	err := e.errMap[CodeBankByIbanCodeNotFound]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeBeneficiaryNameTooLongError() error {
	return e.errMap[CodeBeneficiaryNameTooLong].Err()
}

func (e *PaymentsErrorsList) CodeBeneficiaryNameTooLongDetailedError(details map[string]string) error {
	err := e.errMap[CodeBeneficiaryNameTooLong]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeClientNotFoundByPhoneNumberError() error {
	return e.errMap[CodeClientNotFoundByPhoneNumber].Err()
}

func (e *PaymentsErrorsList) CodeClientNotFoundByPhoneNumberDetailedError(details map[string]string) error {
	err := e.errMap[CodeClientNotFoundByPhoneNumber]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeColvirTimeoutExpiredError() error {
	return e.errMap[CodeColvirTimeoutExpired].Err()
}

func (e *PaymentsErrorsList) CodeColvirTimeoutExpiredDetailedError(details map[string]string) error {
	err := e.errMap[CodeColvirTimeoutExpired]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeDublicatePaymentError() error {
	return e.errMap[CodeDublicatePayment].Err()
}

func (e *PaymentsErrorsList) CodeDublicatePaymentDetailedError(details map[string]string) error {
	err := e.errMap[CodeDublicatePayment]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeErrorNoAccountError() error {
	return e.errMap[CodeErrorNoAccount].Err()
}

func (e *PaymentsErrorsList) CodeErrorNoAccountDetailedError(details map[string]string) error {
	err := e.errMap[CodeErrorNoAccount]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeErrorNoCardError() error {
	return e.errMap[CodeErrorNoCard].Err()
}

func (e *PaymentsErrorsList) CodeErrorNoCardDetailedError(details map[string]string) error {
	err := e.errMap[CodeErrorNoCard]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeGrossErrorsWhenExecutePaymentError() error {
	return e.errMap[CodeGrossErrorsWhenExecutePayment].Err()
}

func (e *PaymentsErrorsList) CodeGrossErrorsWhenExecutePaymentDetailedError(details map[string]string) error {
	err := e.errMap[CodeGrossErrorsWhenExecutePayment]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeInvalidActualBeneficiaryBinIINError() error {
	return e.errMap[CodeInvalidActualBeneficiaryBinIIN].Err()
}

func (e *PaymentsErrorsList) CodeInvalidActualBeneficiaryBinIINDetailedError(details map[string]string) error {
	err := e.errMap[CodeInvalidActualBeneficiaryBinIIN]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeInvalidActualSenderBinIINError() error {
	return e.errMap[CodeInvalidActualSenderBinIIN].Err()
}

func (e *PaymentsErrorsList) CodeInvalidActualSenderBinIINDetailedError(details map[string]string) error {
	err := e.errMap[CodeInvalidActualSenderBinIIN]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeInvalidAttemptIDError() error {
	return e.errMap[CodeInvalidAttemptID].Err()
}

func (e *PaymentsErrorsList) CodeInvalidAttemptIDDetailedError(details map[string]string) error {
	err := e.errMap[CodeInvalidAttemptID]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeInvalidBeneficiaryAccountError() error {
	return e.errMap[CodeInvalidBeneficiaryAccount].Err()
}

func (e *PaymentsErrorsList) CodeInvalidBeneficiaryAccountDetailedError(details map[string]string) error {
	err := e.errMap[CodeInvalidBeneficiaryAccount]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeInvalidBeneficiaryBankBicError() error {
	return e.errMap[CodeInvalidBeneficiaryBankBic].Err()
}

func (e *PaymentsErrorsList) CodeInvalidBeneficiaryBankBicDetailedError(details map[string]string) error {
	err := e.errMap[CodeInvalidBeneficiaryBankBic]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeInvalidBeneficiaryBinIINError() error {
	return e.errMap[CodeInvalidBeneficiaryBinIIN].Err()
}

func (e *PaymentsErrorsList) CodeInvalidBeneficiaryBinIINDetailedError(details map[string]string) error {
	err := e.errMap[CodeInvalidBeneficiaryBinIIN]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeInvalidBeneficiaryCodeError() error {
	return e.errMap[CodeInvalidBeneficiaryCode].Err()
}

func (e *PaymentsErrorsList) CodeInvalidBeneficiaryCodeDetailedError(details map[string]string) error {
	err := e.errMap[CodeInvalidBeneficiaryCode]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeInvalidCurrencyError() error {
	return e.errMap[CodeInvalidCurrency].Err()
}

func (e *PaymentsErrorsList) CodeInvalidCurrencyDetailedError(details map[string]string) error {
	err := e.errMap[CodeInvalidCurrency]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeInvalidIdempotencyIDError() error {
	return e.errMap[CodeInvalidIdempotencyID].Err()
}

func (e *PaymentsErrorsList) CodeInvalidIdempotencyIDDetailedError(details map[string]string) error {
	err := e.errMap[CodeInvalidIdempotencyID]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeInvalidMCCError() error {
	return e.errMap[CodeInvalidMCC].Err()
}

func (e *PaymentsErrorsList) CodeInvalidMCCDetailedError(details map[string]string) error {
	err := e.errMap[CodeInvalidMCC]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeInvalidOperDateError() error {
	return e.errMap[CodeInvalidOperDate].Err()
}

func (e *PaymentsErrorsList) CodeInvalidOperDateDetailedError(details map[string]string) error {
	err := e.errMap[CodeInvalidOperDate]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeInvalidPayerAccountError() error {
	return e.errMap[CodeInvalidPayerAccount].Err()
}

func (e *PaymentsErrorsList) CodeInvalidPayerAccountDetailedError(details map[string]string) error {
	err := e.errMap[CodeInvalidPayerAccount]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeInvalidPayerBinIINError() error {
	return e.errMap[CodeInvalidPayerBinIIN].Err()
}

func (e *PaymentsErrorsList) CodeInvalidPayerBinIINDetailedError(details map[string]string) error {
	err := e.errMap[CodeInvalidPayerBinIIN]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeInvalidPhoneNumberError() error {
	return e.errMap[CodeInvalidPhoneNumber].Err()
}

func (e *PaymentsErrorsList) CodeInvalidPhoneNumberDetailedError(details map[string]string) error {
	err := e.errMap[CodeInvalidPhoneNumber]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeInvalidPurposeCodeError() error {
	return e.errMap[CodeInvalidPurposeCode].Err()
}

func (e *PaymentsErrorsList) CodeInvalidPurposeCodeDetailedError(details map[string]string) error {
	err := e.errMap[CodeInvalidPurposeCode]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeInvalidTransactionIDError() error {
	return e.errMap[CodeInvalidTransactionID].Err()
}

func (e *PaymentsErrorsList) CodeInvalidTransactionIDDetailedError(details map[string]string) error {
	err := e.errMap[CodeInvalidTransactionID]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeKaspiSuspendedError() error {
	return e.errMap[CodeKaspiSuspended].Err()
}

func (e *PaymentsErrorsList) CodeKaspiSuspendedDetailedError(details map[string]string) error {
	err := e.errMap[CodeKaspiSuspended]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeMonthlyPaymentLimitExceededError() error {
	return e.errMap[CodeMonthlyPaymentLimitExceeded].Err()
}

func (e *PaymentsErrorsList) CodeMonthlyPaymentLimitExceededDetailedError(details map[string]string) error {
	err := e.errMap[CodeMonthlyPaymentLimitExceeded]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeNoAvailableAccountsForPaymentError() error {
	return e.errMap[CodeNoAvailableAccountsForPayment].Err()
}

func (e *PaymentsErrorsList) CodeNoAvailableAccountsForPaymentDetailedError(details map[string]string) error {
	err := e.errMap[CodeNoAvailableAccountsForPayment]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeNotEnoughMoneyInAccountsError() error {
	return e.errMap[CodeNotEnoughMoneyInAccounts].Err()
}

func (e *PaymentsErrorsList) CodeNotEnoughMoneyInAccountsDetailedError(details map[string]string) error {
	err := e.errMap[CodeNotEnoughMoneyInAccounts]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeOTPNumberAttemptsExceededError() error {
	return e.errMap[CodeOTPNumberAttemptsExceeded].Err()
}

func (e *PaymentsErrorsList) CodeOTPNumberAttemptsExceededDetailedError(details map[string]string) error {
	err := e.errMap[CodeOTPNumberAttemptsExceeded]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeOneTimePaymentLimitExceededError() error {
	return e.errMap[CodeOneTimePaymentLimitExceeded].Err()
}

func (e *PaymentsErrorsList) CodeOneTimePaymentLimitExceededDetailedError(details map[string]string) error {
	err := e.errMap[CodeOneTimePaymentLimitExceeded]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeOverLimitError() error {
	return e.errMap[CodeOverLimit].Err()
}

func (e *PaymentsErrorsList) CodeOverLimitDetailedError(details map[string]string) error {
	err := e.errMap[CodeOverLimit]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodePaymentDetailsTooLongError() error {
	return e.errMap[CodePaymentDetailsTooLong].Err()
}

func (e *PaymentsErrorsList) CodePaymentDetailsTooLongDetailedError(details map[string]string) error {
	err := e.errMap[CodePaymentDetailsTooLong]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodePaymentErrorColvirError() error {
	return e.errMap[CodePaymentErrorColvir].Err()
}

func (e *PaymentsErrorsList) CodePaymentErrorColvirDetailedError(details map[string]string) error {
	err := e.errMap[CodePaymentErrorColvir]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodePaymentErrorPCError() error {
	return e.errMap[CodePaymentErrorPC].Err()
}

func (e *PaymentsErrorsList) CodePaymentErrorPCDetailedError(details map[string]string) error {
	err := e.errMap[CodePaymentErrorPC]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) IncorrectInputError() error {
	return e.errMap[CodePaymentsIncorrectInput].Err()
}

func (e *PaymentsErrorsList) IncorrectInputDetailedError(details map[string]string) error {
	err := e.errMap[CodePaymentsIncorrectInput]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) IncorrectUserProfileError() error {
	return e.errMap[CodePaymentsIncorrectUserProfile].Err()
}

func (e *PaymentsErrorsList) IncorrectUserProfileDetailedError(details map[string]string) error {
	err := e.errMap[CodePaymentsIncorrectUserProfile]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) InvalidAccountNumberError() error {
	return e.errMap[CodePaymentsInvalidAccountNumber].Err()
}

func (e *PaymentsErrorsList) InvalidAccountNumberDetailedError(details map[string]string) error {
	err := e.errMap[CodePaymentsInvalidAccountNumber]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) InvalidOTPError() error {
	return e.errMap[CodePaymentsInvalidOTP].Err()
}

func (e *PaymentsErrorsList) InvalidOTPDetailedError(details map[string]string) error {
	err := e.errMap[CodePaymentsInvalidOTP]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodePhoneOperatorNotFoundError() error {
	return e.errMap[CodePhoneOperatorNotFound].Err()
}

func (e *PaymentsErrorsList) CodePhoneOperatorNotFoundDetailedError(details map[string]string) error {
	err := e.errMap[CodePhoneOperatorNotFound]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeRejectedByAPError() error {
	return e.errMap[CodeRejectedByAP].Err()
}

func (e *PaymentsErrorsList) CodeRejectedByAPDetailedError(details map[string]string) error {
	err := e.errMap[CodeRejectedByAP]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeTaxPayerNotFoundError() error {
	return e.errMap[CodeTaxPayerNotFound].Err()
}

func (e *PaymentsErrorsList) CodeTaxPayerNotFoundDetailedError(details map[string]string) error {
	err := e.errMap[CodeTaxPayerNotFound]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeTransactionNotFoundError() error {
	return e.errMap[CodeTransactionNotFound].Err()
}

func (e *PaymentsErrorsList) CodeTransactionNotFoundDetailedError(details map[string]string) error {
	err := e.errMap[CodeTransactionNotFound]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeTransactionStepNotFoundError() error {
	return e.errMap[CodeTransactionStepNotFound].Err()
}

func (e *PaymentsErrorsList) CodeTransactionStepNotFoundDetailedError(details map[string]string) error {
	err := e.errMap[CodeTransactionStepNotFound]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeTransactionTypeNotAvailableForReceiptError() error {
	return e.errMap[CodeTransactionTypeNotAvailableForReceipt].Err()
}

func (e *PaymentsErrorsList) CodeTransactionTypeNotAvailableForReceiptDetailedError(details map[string]string) error {
	err := e.errMap[CodeTransactionTypeNotAvailableForReceipt]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeUserAccountNotFoundError() error {
	return e.errMap[CodeUserAccountNotFound].Err()
}

func (e *PaymentsErrorsList) CodeUserAccountNotFoundDetailedError(details map[string]string) error {
	err := e.errMap[CodeUserAccountNotFound]
	return err.WithDetails(details)
}
func (e *PaymentsErrorsList) CodeUserBlockedError() error {
	return e.errMap[CodeUserBlocked].Err()
}

func (e *PaymentsErrorsList) CodeUserBlockedDetailedError(details map[string]string) error {
	err := e.errMap[CodeUserBlocked]
	return err.WithDetails(details)
}