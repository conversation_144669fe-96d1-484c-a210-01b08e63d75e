package paymentsSme

import (
	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"
	internalErrs "git.redmadrobot.com/zaman/backend/zaman/errs"
)

// Добавь в этот файл коды ошибок для сервиса в формате Code{ServiceName} - после запусти генерацию ошибок для сервиса
// meroving gen-errs - после того - раскоментируй код с функцией и заполни errMap как указано в примере

const (
	CodePaymentsSmeTransactionNotFound            = "PaymentsSmeTransactionNotFound"
	CodePaymentsSmeValidationError                = "PaymentsSmeValidationError"
	CodePaymentsSmeInternalError                  = "PaymentsSmeInternalError"
	CodePaymentsSmeOriginNotSME                   = "PaymentsSmeOriginNotSME"
	CodePaymentsSmeIntegrationError               = "PaymentsSmeIntegrationError"
	CodePaymentsSmeForbidden                      = "PaymentsSmeForbidden"
	CodePaymentsSmeAbsTransactionDocumentNotFound = "PaymentsSmeAbsTransactionDocumentNotFound"
	CodePaymentsSmeGeneratedDocumentNotFound      = "PaymentsSmeGeneratedDocumentNotFound"
	CodePaymentsSmePaymentNotFound                = "PaymentsSmePaymentNotFound"
	CodeUserBlocked                               = "UserBlocked"
	CodeSmeIpInfoNotFound                         = "SmeIpInfoNotFound"
	CodeEmployeesNotFound                         = "EmployeesNotFound"
	CodeEmployeeNotFound                          = "EmployeeNotFound"
	CodeInvalidEmployeeID                         = "InvalidEmployeeID"
	CodeEmployeeAlreadyExists                     = "EmployeeAlreadyExists"
	CodePaymentsSmeTransactionNumberIsEmpty       = "TransactionNumberIsEmpty"
)

func PaymentsSmeErrs() PaymentsSmeErrors {
	return &PaymentsSmeErrorsList{
		errMap: map[string]internalErrs.Reason{
			CodePaymentsSmeTransactionNotFound:            {0, CodePaymentsSmeTransactionNotFound, errs.TypeInternal, "Transaction not found"},
			CodePaymentsSmeInternalError:                  {0, CodePaymentsSmeInternalError, errs.TypeInternal, "Internal error"},
			CodePaymentsSmeValidationError:                {1, CodePaymentsSmeValidationError, errs.TypeIllegalArgument, "Validation error"},
			CodePaymentsSmeOriginNotSME:                   {2, CodePaymentsSmeOriginNotSME, errs.TypeForbidden, "Origin not sme"},
			CodePaymentsSmeIntegrationError:               {3, CodePaymentsSmeIntegrationError, errs.TypeServiceUnavailable, "Integration error"},
			CodePaymentsSmeForbidden:                      {4, CodePaymentsSmeForbidden, errs.TypeForbidden, "Forbidden"},
			CodePaymentsSmeAbsTransactionDocumentNotFound: {5, CodePaymentsSmeAbsTransactionDocumentNotFound, errs.TypeNotFound, "Abs transaction document not found"},
			CodePaymentsSmeGeneratedDocumentNotFound:      {6, CodePaymentsSmeGeneratedDocumentNotFound, errs.TypeNotFound, "Generated document not found"},
			CodePaymentsSmePaymentNotFound:                {7, CodePaymentsSmePaymentNotFound, errs.TypeNotFound, "PaymentNotFound"},
			CodeUserBlocked:                               {8, CodeUserBlocked, errs.TypeForbidden, "User blocked"},
			CodeSmeIpInfoNotFound:                         {9, CodeSmeIpInfoNotFound, errs.TypeForbidden, "IpInfo not found"},
			CodeEmployeesNotFound:                         {10, CodeEmployeesNotFound, errs.TypeNotFound, "Employees not found"},
			CodeEmployeeNotFound:                          {11, CodeEmployeeNotFound, errs.TypeNotFound, "Employees not found"},
			CodeInvalidEmployeeID:                         {12, CodeInvalidEmployeeID, errs.TypeForbidden, "Invalid employee id"},
			CodeEmployeeAlreadyExists:                     {13, CodeEmployeeAlreadyExists, errs.TypeForbidden, "Employee already exists"},
			CodePaymentsSmeTransactionNumberIsEmpty:       {14, CodePaymentsSmeTransactionNumberIsEmpty, errs.TypeForbidden, "Transaction number is empty"},
		},
	}
}
