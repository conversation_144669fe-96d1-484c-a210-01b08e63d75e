// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package paymentsSme

import (
	internalErrs "git.redmadrobot.com/zaman/backend/zaman/errs"
)

type (
	PaymentsSmeErrorsList struct {
		errMap map[string]internalErrs.Reason
	}

	PaymentsSmeErrors interface {
		CodeEmployeeAlreadyExistsError() error
		CodeEmployeeAlreadyExistsDetailedError(details map[string]string) error
		CodeEmployeeNotFoundError() error
		CodeEmployeeNotFoundDetailedError(details map[string]string) error
		CodeEmployeesNotFoundError() error
		CodeEmployeesNotFoundDetailedError(details map[string]string) error
		CodeInvalidEmployeeIDError() error
		CodeInvalidEmployeeIDDetailedError(details map[string]string) error
		AbsTransactionDocumentNotFoundError() error
		AbsTransactionDocumentNotFoundDetailedError(details map[string]string) error
		ForbiddenError() error
		ForbiddenDetailedError(details map[string]string) error
		GeneratedDocumentNotFoundError() error
		GeneratedDocumentNotFoundDetailedError(details map[string]string) error
		IntegrationErrorError() error
		IntegrationErrorDetailedError(details map[string]string) error
		InternalErrorError() error
		InternalErrorDetailedError(details map[string]string) error
		OriginNotSMEError() error
		OriginNotSMEDetailedError(details map[string]string) error
		PaymentNotFoundError() error
		PaymentNotFoundDetailedError(details map[string]string) error
		TransactionNotFoundError() error
		TransactionNotFoundDetailedError(details map[string]string) error
		TransactionNumberIsEmptyError() error
		TransactionNumberIsEmptyDetailedError(details map[string]string) error
		ValidationErrorError() error
		ValidationErrorDetailedError(details map[string]string) error
		CodeSmeIpInfoNotFoundError() error
		CodeSmeIpInfoNotFoundDetailedError(details map[string]string) error
		CodeUserBlockedError() error
		CodeUserBlockedDetailedError(details map[string]string) error
	}
)

func (e *PaymentsSmeErrorsList) CodeEmployeeAlreadyExistsError() error {
	return e.errMap[CodeEmployeeAlreadyExists].Err()
}

func (e *PaymentsSmeErrorsList) CodeEmployeeAlreadyExistsDetailedError(details map[string]string) error {
	err := e.errMap[CodeEmployeeAlreadyExists]
	return err.WithDetails(details)
}
func (e *PaymentsSmeErrorsList) CodeEmployeeNotFoundError() error {
	return e.errMap[CodeEmployeeNotFound].Err()
}

func (e *PaymentsSmeErrorsList) CodeEmployeeNotFoundDetailedError(details map[string]string) error {
	err := e.errMap[CodeEmployeeNotFound]
	return err.WithDetails(details)
}
func (e *PaymentsSmeErrorsList) CodeEmployeesNotFoundError() error {
	return e.errMap[CodeEmployeesNotFound].Err()
}

func (e *PaymentsSmeErrorsList) CodeEmployeesNotFoundDetailedError(details map[string]string) error {
	err := e.errMap[CodeEmployeesNotFound]
	return err.WithDetails(details)
}
func (e *PaymentsSmeErrorsList) CodeInvalidEmployeeIDError() error {
	return e.errMap[CodeInvalidEmployeeID].Err()
}

func (e *PaymentsSmeErrorsList) CodeInvalidEmployeeIDDetailedError(details map[string]string) error {
	err := e.errMap[CodeInvalidEmployeeID]
	return err.WithDetails(details)
}
func (e *PaymentsSmeErrorsList) AbsTransactionDocumentNotFoundError() error {
	return e.errMap[CodePaymentsSmeAbsTransactionDocumentNotFound].Err()
}

func (e *PaymentsSmeErrorsList) AbsTransactionDocumentNotFoundDetailedError(details map[string]string) error {
	err := e.errMap[CodePaymentsSmeAbsTransactionDocumentNotFound]
	return err.WithDetails(details)
}
func (e *PaymentsSmeErrorsList) ForbiddenError() error {
	return e.errMap[CodePaymentsSmeForbidden].Err()
}

func (e *PaymentsSmeErrorsList) ForbiddenDetailedError(details map[string]string) error {
	err := e.errMap[CodePaymentsSmeForbidden]
	return err.WithDetails(details)
}
func (e *PaymentsSmeErrorsList) GeneratedDocumentNotFoundError() error {
	return e.errMap[CodePaymentsSmeGeneratedDocumentNotFound].Err()
}

func (e *PaymentsSmeErrorsList) GeneratedDocumentNotFoundDetailedError(details map[string]string) error {
	err := e.errMap[CodePaymentsSmeGeneratedDocumentNotFound]
	return err.WithDetails(details)
}
func (e *PaymentsSmeErrorsList) IntegrationErrorError() error {
	return e.errMap[CodePaymentsSmeIntegrationError].Err()
}

func (e *PaymentsSmeErrorsList) IntegrationErrorDetailedError(details map[string]string) error {
	err := e.errMap[CodePaymentsSmeIntegrationError]
	return err.WithDetails(details)
}
func (e *PaymentsSmeErrorsList) InternalErrorError() error {
	return e.errMap[CodePaymentsSmeInternalError].Err()
}

func (e *PaymentsSmeErrorsList) InternalErrorDetailedError(details map[string]string) error {
	err := e.errMap[CodePaymentsSmeInternalError]
	return err.WithDetails(details)
}
func (e *PaymentsSmeErrorsList) OriginNotSMEError() error {
	return e.errMap[CodePaymentsSmeOriginNotSME].Err()
}

func (e *PaymentsSmeErrorsList) OriginNotSMEDetailedError(details map[string]string) error {
	err := e.errMap[CodePaymentsSmeOriginNotSME]
	return err.WithDetails(details)
}
func (e *PaymentsSmeErrorsList) PaymentNotFoundError() error {
	return e.errMap[CodePaymentsSmePaymentNotFound].Err()
}

func (e *PaymentsSmeErrorsList) PaymentNotFoundDetailedError(details map[string]string) error {
	err := e.errMap[CodePaymentsSmePaymentNotFound]
	return err.WithDetails(details)
}
func (e *PaymentsSmeErrorsList) TransactionNotFoundError() error {
	return e.errMap[CodePaymentsSmeTransactionNotFound].Err()
}

func (e *PaymentsSmeErrorsList) TransactionNotFoundDetailedError(details map[string]string) error {
	err := e.errMap[CodePaymentsSmeTransactionNotFound]
	return err.WithDetails(details)
}
func (e *PaymentsSmeErrorsList) TransactionNumberIsEmptyError() error {
	return e.errMap[CodePaymentsSmeTransactionNumberIsEmpty].Err()
}

func (e *PaymentsSmeErrorsList) TransactionNumberIsEmptyDetailedError(details map[string]string) error {
	err := e.errMap[CodePaymentsSmeTransactionNumberIsEmpty]
	return err.WithDetails(details)
}
func (e *PaymentsSmeErrorsList) ValidationErrorError() error {
	return e.errMap[CodePaymentsSmeValidationError].Err()
}

func (e *PaymentsSmeErrorsList) ValidationErrorDetailedError(details map[string]string) error {
	err := e.errMap[CodePaymentsSmeValidationError]
	return err.WithDetails(details)
}
func (e *PaymentsSmeErrorsList) CodeSmeIpInfoNotFoundError() error {
	return e.errMap[CodeSmeIpInfoNotFound].Err()
}

func (e *PaymentsSmeErrorsList) CodeSmeIpInfoNotFoundDetailedError(details map[string]string) error {
	err := e.errMap[CodeSmeIpInfoNotFound]
	return err.WithDetails(details)
}
func (e *PaymentsSmeErrorsList) CodeUserBlockedError() error {
	return e.errMap[CodeUserBlocked].Err()
}

func (e *PaymentsSmeErrorsList) CodeUserBlockedDetailedError(details map[string]string) error {
	err := e.errMap[CodeUserBlocked]
	return err.WithDetails(details)
}
