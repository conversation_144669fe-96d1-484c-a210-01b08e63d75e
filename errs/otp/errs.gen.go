// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package otp

import (
	internalErrs "git.redmadrobot.com/zaman/backend/zaman/errs"
)

type (
	OtpErrorsList struct {
		errMap map[string]internalErrs.Reason
	}

	OtpErrors interface {
		AttemptNotFoundError() error
		AttemptNotFoundDetailedError(details map[string]string) error
		EmptyAttemptIDError() error
		EmptyAttemptIDDetailedError(details map[string]string) error
		InvalidCodeError() error
		InvalidCodeDetailedError(details map[string]string) error
		MaxCodeAttemptsExceededError() error
		MaxCodeAttemptsExceededDetailedError(details map[string]string) error
		MaxCodeChecksExceededError() error
		MaxCodeChecksExceededDetailedError(details map[string]string) error
		NewAttemptTimeNotExceededError() error
		NewAttemptTimeNotExceededDetailedError(details map[string]string) error
	}
)

func (e *OtpErrorsList) AttemptNotFoundError() error {
	return e.errMap[CodeOtpAttemptNotFound].Err()
}

func (e *OtpErrorsList) AttemptNotFoundDetailedError(details map[string]string) error {
	err := e.errMap[CodeOtpAttemptNotFound]
	return err.WithDetails(details)
}
func (e *OtpErrorsList) EmptyAttemptIDError() error {
	return e.errMap[CodeOtpEmptyAttemptID].Err()
}

func (e *OtpErrorsList) EmptyAttemptIDDetailedError(details map[string]string) error {
	err := e.errMap[CodeOtpEmptyAttemptID]
	return err.WithDetails(details)
}
func (e *OtpErrorsList) InvalidCodeError() error {
	return e.errMap[CodeOtpInvalidCode].Err()
}

func (e *OtpErrorsList) InvalidCodeDetailedError(details map[string]string) error {
	err := e.errMap[CodeOtpInvalidCode]
	return err.WithDetails(details)
}
func (e *OtpErrorsList) MaxCodeAttemptsExceededError() error {
	return e.errMap[CodeOtpMaxCodeAttemptsExceeded].Err()
}

func (e *OtpErrorsList) MaxCodeAttemptsExceededDetailedError(details map[string]string) error {
	err := e.errMap[CodeOtpMaxCodeAttemptsExceeded]
	return err.WithDetails(details)
}
func (e *OtpErrorsList) MaxCodeChecksExceededError() error {
	return e.errMap[CodeOtpMaxCodeChecksExceeded].Err()
}

func (e *OtpErrorsList) MaxCodeChecksExceededDetailedError(details map[string]string) error {
	err := e.errMap[CodeOtpMaxCodeChecksExceeded]
	return err.WithDetails(details)
}
func (e *OtpErrorsList) NewAttemptTimeNotExceededError() error {
	return e.errMap[CodeOtpNewAttemptTimeNotExceeded].Err()
}

func (e *OtpErrorsList) NewAttemptTimeNotExceededDetailedError(details map[string]string) error {
	err := e.errMap[CodeOtpNewAttemptTimeNotExceeded]
	return err.WithDetails(details)
}
