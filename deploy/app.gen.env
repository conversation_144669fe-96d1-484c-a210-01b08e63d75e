# Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
DOCKER_BUILDKIT=0
COMPOSE_DOCKER_CLI_BUILD=0



##########INFRA INSTANCE ENVS##########
# grafana instance
ZAMAN_GRAFANA_PASSWORD_ADMIN_USER=zaman
ZAMAN_GRAFANA_PASSWORD=zaman



# minio instance
ZAMAN_MINIO_ROOT_USER=zaman
ZAMAN_MINIO_ROOT_PASSWORD=zamanzaman



# keycloak instance
ZAMAN_KEYCLOAK_ADMIN=zaman
ZAMAN_KEYCLOAK_ADMIN_PASSWORD=zamanzaman
ZAMAN_KEYCLOAK_LOG_LEVEL=info



##########SERVICE ENVS##########

# Service: users
# grpc server
USERS_GRPC_SERVER_REFLECTION=true
USERS_GRPC_SERVER_HOST=zaman-users-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
USERS_DB_HOST=zaman-users-postgres
# Ports are defined in app-ports.gen.env
USERS_DB_DATABASE=users
USERS_DB_USER=users
USERS_DB_PASSWORD=users
USERS_DB_DEBUG=true

# kafka provider
USERS_KAFKA_HOSTS=zaman-kafka:9092

# redis provider
USERS_REDIS_HOST=zaman-redis
USERS_REDIS_PORT=6379

# jaeger provider
USERS_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
USERS_OTEL_SERVICE_NAME=users
USERS_OTEL_SAMPLER_TRACE_RATIO=50

# s3 provider
USERS_AWS_SERVICE_URL=users
USERS_AWS_BUCKET_NAME=zaman
USERS_AWS_ACCESS_KEY_ID=
USERS_AWS_SECRET_ACCESS_KEY=


# other
USERS_LOG_LEVEL=debug
USERS_LOG_PRETTY=false
USERS_DEBUG=true
USERS_ENVIRONMENT=dev

# Service: otp
# grpc server
OTP_GRPC_SERVER_REFLECTION=true
OTP_GRPC_SERVER_HOST=zaman-otp-srv
# Ports are defined in app-ports.gen.env
# kafka provider
OTP_KAFKA_HOSTS=zaman-kafka:9092

# redis provider
OTP_REDIS_HOST=zaman-redis
OTP_REDIS_PORT=6379

# jaeger provider
OTP_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
OTP_OTEL_SERVICE_NAME=otp
OTP_OTEL_SAMPLER_TRACE_RATIO=50


# other
OTP_LOG_LEVEL=debug
OTP_LOG_PRETTY=false
OTP_DEBUG=true
OTP_ENVIRONMENT=dev

# Service: documents
# grpc server
DOCUMENTS_GRPC_SERVER_REFLECTION=true
DOCUMENTS_GRPC_SERVER_HOST=zaman-documents-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
DOCUMENTS_DB_HOST=zaman-documents-postgres
# Ports are defined in app-ports.gen.env
DOCUMENTS_DB_DATABASE=documents
DOCUMENTS_DB_USER=documents
DOCUMENTS_DB_PASSWORD=documents
DOCUMENTS_DB_DEBUG=true

# kafka provider
DOCUMENTS_KAFKA_HOSTS=zaman-kafka:9092

# jaeger provider
DOCUMENTS_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
DOCUMENTS_OTEL_SERVICE_NAME=documents
DOCUMENTS_OTEL_SAMPLER_TRACE_RATIO=50

# s3 provider
DOCUMENTS_AWS_SERVICE_URL=documents
DOCUMENTS_AWS_BUCKET_NAME=zaman
DOCUMENTS_AWS_ACCESS_KEY_ID=
DOCUMENTS_AWS_SECRET_ACCESS_KEY=

# gotenberg provider
DOCUMENTS_GB_BASE_URL=http://zaman-gotenberg:3001


# other
DOCUMENTS_LOG_LEVEL=debug
DOCUMENTS_LOG_PRETTY=false
DOCUMENTS_DEBUG=true
DOCUMENTS_ENVIRONMENT=dev

# Service: notifications
# grpc server
NOTIFICATIONS_GRPC_SERVER_REFLECTION=true
NOTIFICATIONS_GRPC_SERVER_HOST=zaman-notifications-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
NOTIFICATIONS_DB_HOST=zaman-notifications-postgres
# Ports are defined in app-ports.gen.env
NOTIFICATIONS_DB_DATABASE=notifications
NOTIFICATIONS_DB_USER=notifications
NOTIFICATIONS_DB_PASSWORD=notifications
NOTIFICATIONS_DB_DEBUG=true

# kafka provider
NOTIFICATIONS_KAFKA_HOSTS=zaman-kafka:9092

# redis provider
NOTIFICATIONS_REDIS_HOST=zaman-redis
NOTIFICATIONS_REDIS_PORT=6379

# jaeger provider
NOTIFICATIONS_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
NOTIFICATIONS_OTEL_SERVICE_NAME=notifications
NOTIFICATIONS_OTEL_SAMPLER_TRACE_RATIO=50

# s3 provider
NOTIFICATIONS_AWS_SERVICE_URL=notifications
NOTIFICATIONS_AWS_BUCKET_NAME=zaman
NOTIFICATIONS_AWS_ACCESS_KEY_ID=
NOTIFICATIONS_AWS_SECRET_ACCESS_KEY=


# other
NOTIFICATIONS_LOG_LEVEL=debug
NOTIFICATIONS_LOG_PRETTY=false
NOTIFICATIONS_DEBUG=true
NOTIFICATIONS_ENVIRONMENT=dev

# Service: keycloakProxy
# grpc server
KEYCLOAKPROXY_GRPC_SERVER_REFLECTION=true
KEYCLOAKPROXY_GRPC_SERVER_HOST=zaman-keycloak-proxy-srv
# Ports are defined in app-ports.gen.env
# jaeger provider
KEYCLOAKPROXY_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
KEYCLOAKPROXY_OTEL_SERVICE_NAME=keycloakProxy
KEYCLOAKPROXY_OTEL_SAMPLER_TRACE_RATIO=50

# keycloak provider
KEYCLOAKPROXY_KC_BASE_URL=http://zaman-keycloak:8080
KEYCLOAKPROXY_KC_MOBILE_GW_REALM=zaman-mobile
KEYCLOAKPROXY_KC_SME_GW_REALM=zaman-sme
KEYCLOAKPROXY_KC_PAYMENTSGW_GW_REALM=zaman-paymentsgw
KEYCLOAKPROXY_KC_BALANCEGW_GW_REALM=zaman-balancegw
KEYCLOAKPROXY_KC_DOCUMENTSGW_GW_REALM=zaman-documentsgw
KEYCLOAKPROXY_KC_CRMGW_GW_REALM=zaman-crmgw
KEYCLOAKPROXY_KC_COLLECTIONGW_GW_REALM=zaman-collectiongw
KEYCLOAKPROXY_KC_LANDING_GW_REALM=zaman-landing
KEYCLOAKPROXY_KC_CLIENT_ID=zaman-app-admin
KEYCLOAKPROXY_KC_CLIENT_SECRET=SLjF8CuDrsL8OeqhfcPORPVdjojzmKkA
KEYCLOAKPROXY_KC_APP_ADMIN_USERNAME=zaman-app
KEYCLOAKPROXY_KC_APP_ADMIN_PASSWORD=zaman


# other
KEYCLOAKPROXY_LOG_LEVEL=debug
KEYCLOAKPROXY_LOG_PRETTY=false
KEYCLOAKPROXY_DEBUG=true
KEYCLOAKPROXY_ENVIRONMENT=dev

# Service: kgdBridge
# grpc server
KGDBRIDGE_GRPC_SERVER_REFLECTION=true
KGDBRIDGE_GRPC_SERVER_HOST=zaman-kgd-bridge-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
KGDBRIDGE_DB_HOST=zaman-kgdBridge-postgres
# Ports are defined in app-ports.gen.env
KGDBRIDGE_DB_DATABASE=kgdBridge
KGDBRIDGE_DB_USER=kgdBridge
KGDBRIDGE_DB_PASSWORD=kgdBridge
KGDBRIDGE_DB_DEBUG=true

# db mongo provider
KGDBRIDGE_MONGO_HOST=zaman-kgdBridge-mongo
# Ports are defined in app-ports.gen.env
KGDBRIDGE_MONGO_DATABASE=kgdBridge
KGDBRIDGE_MONGO_USERNAME=kgdBridge
KGDBRIDGE_MONGO_PASSWORD=kgdBridge
KGDBRIDGE_MONGO_OPTIONS=authSource=admin

# kafka provider
KGDBRIDGE_KAFKA_HOSTS=zaman-kafka:9092

# jaeger provider
KGDBRIDGE_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
KGDBRIDGE_OTEL_SERVICE_NAME=kgdBridge
KGDBRIDGE_OTEL_SAMPLER_TRACE_RATIO=50


# other
KGDBRIDGE_LOG_LEVEL=debug
KGDBRIDGE_LOG_PRETTY=false
KGDBRIDGE_DEBUG=true
KGDBRIDGE_ENVIRONMENT=dev

# Service: btsBridge
# grpc server
BTSBRIDGE_GRPC_SERVER_REFLECTION=true
BTSBRIDGE_GRPC_SERVER_HOST=zaman-bts-bridge-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
BTSBRIDGE_DB_HOST=zaman-btsBridge-postgres
# Ports are defined in app-ports.gen.env
BTSBRIDGE_DB_DATABASE=btsBridge
BTSBRIDGE_DB_USER=btsBridge
BTSBRIDGE_DB_PASSWORD=btsBridge
BTSBRIDGE_DB_DEBUG=true

# db mongo provider
BTSBRIDGE_MONGO_HOST=zaman-btsBridge-mongo
# Ports are defined in app-ports.gen.env
BTSBRIDGE_MONGO_DATABASE=btsBridge
BTSBRIDGE_MONGO_USERNAME=btsBridge
BTSBRIDGE_MONGO_PASSWORD=btsBridge
BTSBRIDGE_MONGO_OPTIONS=authSource=admin

# kafka provider
BTSBRIDGE_KAFKA_HOSTS=zaman-kafka:9092

# jaeger provider
BTSBRIDGE_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
BTSBRIDGE_OTEL_SERVICE_NAME=btsBridge
BTSBRIDGE_OTEL_SAMPLER_TRACE_RATIO=50


# other
BTSBRIDGE_LOG_LEVEL=debug
BTSBRIDGE_LOG_PRETTY=false
BTSBRIDGE_DEBUG=true
BTSBRIDGE_ENVIRONMENT=dev

# Service: smsBridge
# grpc server
SMSBRIDGE_GRPC_SERVER_REFLECTION=true
SMSBRIDGE_GRPC_SERVER_HOST=zaman-sms-bridge-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
SMSBRIDGE_DB_HOST=zaman-smsBridge-postgres
# Ports are defined in app-ports.gen.env
SMSBRIDGE_DB_DATABASE=smsBridge
SMSBRIDGE_DB_USER=smsBridge
SMSBRIDGE_DB_PASSWORD=smsBridge
SMSBRIDGE_DB_DEBUG=true

# db mongo provider
SMSBRIDGE_MONGO_HOST=zaman-smsBridge-mongo
# Ports are defined in app-ports.gen.env
SMSBRIDGE_MONGO_DATABASE=smsBridge
SMSBRIDGE_MONGO_USERNAME=smsBridge
SMSBRIDGE_MONGO_PASSWORD=smsBridge
SMSBRIDGE_MONGO_OPTIONS=authSource=admin

# jaeger provider
SMSBRIDGE_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
SMSBRIDGE_OTEL_SERVICE_NAME=smsBridge
SMSBRIDGE_OTEL_SAMPLER_TRACE_RATIO=50


# other
SMSBRIDGE_LOG_LEVEL=debug
SMSBRIDGE_LOG_PRETTY=false
SMSBRIDGE_DEBUG=true
SMSBRIDGE_ENVIRONMENT=dev

# Service: loans
# grpc server
LOANS_GRPC_SERVER_REFLECTION=true
LOANS_GRPC_SERVER_HOST=zaman-loans-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
LOANS_DB_HOST=zaman-loans-postgres
# Ports are defined in app-ports.gen.env
LOANS_DB_DATABASE= loans
LOANS_DB_USER=loans
LOANS_DB_PASSWORD=loans
LOANS_DB_DEBUG=true

# kafka provider
LOANS_KAFKA_HOSTS=zaman-kafka:9092

# chronos provider
LOANS_CHRONOS_TIME_ZONE=Local
LOANS_CHRONOS_SECONDS_REQUIRED=false

# jaeger provider
LOANS_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
LOANS_OTEL_SERVICE_NAME=loans
LOANS_OTEL_SAMPLER_TRACE_RATIO=50


# other
LOANS_LOG_LEVEL=debug
LOANS_LOG_PRETTY=false
LOANS_DEBUG=true
LOANS_ENVIRONMENT=dev

# Service: colvirBridge
# grpc server
COLVIRBRIDGE_GRPC_SERVER_REFLECTION=true
COLVIRBRIDGE_GRPC_SERVER_HOST=zaman-colvir-bridge-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
COLVIRBRIDGE_DB_HOST=zaman-colvirBridge-postgres
# Ports are defined in app-ports.gen.env
COLVIRBRIDGE_DB_DATABASE=colvirBridge
COLVIRBRIDGE_DB_USER=colvirBridge
COLVIRBRIDGE_DB_PASSWORD=colvirBridge
COLVIRBRIDGE_DB_DEBUG=true

# db mongo provider
COLVIRBRIDGE_MONGO_HOST=zaman-colvirBridge-mongo
# Ports are defined in app-ports.gen.env
COLVIRBRIDGE_MONGO_DATABASE=colvirBridge
COLVIRBRIDGE_MONGO_USERNAME=colvirBridge
COLVIRBRIDGE_MONGO_PASSWORD=colvirBridge
COLVIRBRIDGE_MONGO_OPTIONS=authSource=admin

# kafka provider
COLVIRBRIDGE_KAFKA_HOSTS=zaman-kafka:9092

# jaeger provider
COLVIRBRIDGE_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
COLVIRBRIDGE_OTEL_SERVICE_NAME=colvirBridge
COLVIRBRIDGE_OTEL_SAMPLER_TRACE_RATIO=50


# other
COLVIRBRIDGE_LOG_LEVEL=debug
COLVIRBRIDGE_LOG_PRETTY=false
COLVIRBRIDGE_DEBUG=true
COLVIRBRIDGE_ENVIRONMENT=dev

# Service: payments
# grpc server
PAYMENTS_GRPC_SERVER_REFLECTION=true
PAYMENTS_GRPC_SERVER_HOST=zaman-payments-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
PAYMENTS_DB_HOST=zaman-payments-postgres
# Ports are defined in app-ports.gen.env
PAYMENTS_DB_DATABASE=payments
PAYMENTS_DB_USER=payments
PAYMENTS_DB_PASSWORD=payments
PAYMENTS_DB_DEBUG=true

# kafka provider
PAYMENTS_KAFKA_HOSTS=zaman-kafka:9092

# jaeger provider
PAYMENTS_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
PAYMENTS_OTEL_SERVICE_NAME=payments
PAYMENTS_OTEL_SAMPLER_TRACE_RATIO=50


# other
PAYMENTS_LOG_LEVEL=debug
PAYMENTS_LOG_PRETTY=false
PAYMENTS_DEBUG=true
PAYMENTS_ENVIRONMENT=dev

# Service: cardsAccounts
# grpc server
CARDSACCOUNTS_GRPC_SERVER_REFLECTION=true
CARDSACCOUNTS_GRPC_SERVER_HOST=zaman-cards-accounts-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
CARDSACCOUNTS_DB_HOST=zaman-cardsAccounts-postgres
# Ports are defined in app-ports.gen.env
CARDSACCOUNTS_DB_DATABASE=cardsAccounts
CARDSACCOUNTS_DB_USER=cardsAccounts
CARDSACCOUNTS_DB_PASSWORD=cardsAccounts
CARDSACCOUNTS_DB_DEBUG=true

# kafka provider
CARDSACCOUNTS_KAFKA_HOSTS=zaman-kafka:9092

# chronos provider
CARDSACCOUNTS_CHRONOS_TIME_ZONE=Local
CARDSACCOUNTS_CHRONOS_SECONDS_REQUIRED=false

# jaeger provider
CARDSACCOUNTS_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
CARDSACCOUNTS_OTEL_SERVICE_NAME=cardsAccounts
CARDSACCOUNTS_OTEL_SAMPLER_TRACE_RATIO=50


# other
CARDSACCOUNTS_LOG_LEVEL=debug
CARDSACCOUNTS_LOG_PRETTY=false
CARDSACCOUNTS_DEBUG=true
CARDSACCOUNTS_ENVIRONMENT=dev

# Service: dictionary
# grpc server
DICTIONARY_GRPC_SERVER_REFLECTION=true
DICTIONARY_GRPC_SERVER_HOST=zaman-dictionary-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
DICTIONARY_DB_HOST=zaman-dictionary-postgres
# Ports are defined in app-ports.gen.env
DICTIONARY_DB_DATABASE=dictionary
DICTIONARY_DB_USER=dictionary
DICTIONARY_DB_PASSWORD=dictionary
DICTIONARY_DB_DEBUG=true

# redis provider
DICTIONARY_REDIS_HOST=zaman-redis
DICTIONARY_REDIS_PORT=6379

# jaeger provider
DICTIONARY_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
DICTIONARY_OTEL_SERVICE_NAME=dictionary
DICTIONARY_OTEL_SAMPLER_TRACE_RATIO=50

# s3 provider
DICTIONARY_AWS_SERVICE_URL=dictionary
DICTIONARY_AWS_BUCKET_NAME=zaman
DICTIONARY_AWS_ACCESS_KEY_ID=
DICTIONARY_AWS_SECRET_ACCESS_KEY=


# other
DICTIONARY_LOG_LEVEL=debug
DICTIONARY_LOG_PRETTY=false
DICTIONARY_DEBUG=true
DICTIONARY_ENVIRONMENT=dev

# Service: pkbBridge
# grpc server
PKBBRIDGE_GRPC_SERVER_REFLECTION=true
PKBBRIDGE_GRPC_SERVER_HOST=zaman-pkb-bridge-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
PKBBRIDGE_DB_HOST=zaman-pkbBridge-postgres
# Ports are defined in app-ports.gen.env
PKBBRIDGE_DB_DATABASE=pkbBridge
PKBBRIDGE_DB_USER=pkbBridge
PKBBRIDGE_DB_PASSWORD=pkbBridge
PKBBRIDGE_DB_DEBUG=true

# db mongo provider
PKBBRIDGE_MONGO_HOST=zaman-pkbBridge-mongo
# Ports are defined in app-ports.gen.env
PKBBRIDGE_MONGO_DATABASE=pkbBridge
PKBBRIDGE_MONGO_USERNAME=pkbBridge
PKBBRIDGE_MONGO_PASSWORD=pkbBridge
PKBBRIDGE_MONGO_OPTIONS=authSource=admin

# kafka provider
PKBBRIDGE_KAFKA_HOSTS=zaman-kafka:9092

# jaeger provider
PKBBRIDGE_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
PKBBRIDGE_OTEL_SERVICE_NAME=pkbBridge
PKBBRIDGE_OTEL_SAMPLER_TRACE_RATIO=50

# s3 provider
PKBBRIDGE_AWS_SERVICE_URL=pkbBridge
PKBBRIDGE_AWS_BUCKET_NAME=zaman
PKBBRIDGE_AWS_ACCESS_KEY_ID=
PKBBRIDGE_AWS_SECRET_ACCESS_KEY=


# other
PKBBRIDGE_LOG_LEVEL=debug
PKBBRIDGE_LOG_PRETTY=false
PKBBRIDGE_DEBUG=true
PKBBRIDGE_ENVIRONMENT=dev

# Service: amlBridge
# grpc server
AMLBRIDGE_GRPC_SERVER_REFLECTION=true
AMLBRIDGE_GRPC_SERVER_HOST=zaman-aml-bridge-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
AMLBRIDGE_DB_HOST=zaman-amlBridge-postgres
# Ports are defined in app-ports.gen.env
AMLBRIDGE_DB_DATABASE=amlBridge
AMLBRIDGE_DB_USER=amlBridge
AMLBRIDGE_DB_PASSWORD=amlBridge
AMLBRIDGE_DB_DEBUG=true

# db mongo provider
AMLBRIDGE_MONGO_HOST=zaman-amlBridge-mongo
# Ports are defined in app-ports.gen.env
AMLBRIDGE_MONGO_DATABASE=amlBridge
AMLBRIDGE_MONGO_USERNAME=amlBridge
AMLBRIDGE_MONGO_PASSWORD=amlBridge
AMLBRIDGE_MONGO_OPTIONS=authSource=admin

# kafka provider
AMLBRIDGE_KAFKA_HOSTS=zaman-kafka:9092

# jaeger provider
AMLBRIDGE_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
AMLBRIDGE_OTEL_SERVICE_NAME=amlBridge
AMLBRIDGE_OTEL_SAMPLER_TRACE_RATIO=50


# other
AMLBRIDGE_LOG_LEVEL=debug
AMLBRIDGE_LOG_PRETTY=false
AMLBRIDGE_DEBUG=true
AMLBRIDGE_ENVIRONMENT=dev

# Service: liveness
# grpc server
LIVENESS_GRPC_SERVER_REFLECTION=true
LIVENESS_GRPC_SERVER_HOST=zaman-liveness-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
LIVENESS_DB_HOST=zaman-liveness-postgres
# Ports are defined in app-ports.gen.env
LIVENESS_DB_DATABASE=liveness
LIVENESS_DB_USER=liveness
LIVENESS_DB_PASSWORD=liveness
LIVENESS_DB_DEBUG=true

# kafka provider
LIVENESS_KAFKA_HOSTS=zaman-kafka:9092

# chronos provider
LIVENESS_CHRONOS_TIME_ZONE=Local
LIVENESS_CHRONOS_SECONDS_REQUIRED=false

# jaeger provider
LIVENESS_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
LIVENESS_OTEL_SERVICE_NAME=liveness
LIVENESS_OTEL_SAMPLER_TRACE_RATIO=50


# other
LIVENESS_LOG_LEVEL=debug
LIVENESS_LOG_PRETTY=false
LIVENESS_DEBUG=true
LIVENESS_ENVIRONMENT=dev

# Service: taskManager
# grpc server
TASKMANAGER_GRPC_SERVER_REFLECTION=true
TASKMANAGER_GRPC_SERVER_HOST=zaman-task-manager-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
TASKMANAGER_DB_HOST=zaman-taskManager-postgres
# Ports are defined in app-ports.gen.env
TASKMANAGER_DB_DATABASE=taskManager
TASKMANAGER_DB_USER=taskManager
TASKMANAGER_DB_PASSWORD=taskManager
TASKMANAGER_DB_DEBUG=true

# kafka provider
TASKMANAGER_KAFKA_HOSTS=zaman-kafka:9092

# jaeger provider
TASKMANAGER_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
TASKMANAGER_OTEL_SERVICE_NAME=taskManager
TASKMANAGER_OTEL_SAMPLER_TRACE_RATIO=50


# other
TASKMANAGER_LOG_LEVEL=debug
TASKMANAGER_LOG_PRETTY=false
TASKMANAGER_DEBUG=true
TASKMANAGER_ENVIRONMENT=dev

# Service: juicyscoreBridge
# grpc server
JUICYSCOREBRIDGE_GRPC_SERVER_REFLECTION=true
JUICYSCOREBRIDGE_GRPC_SERVER_HOST=zaman-juicyscore-bridge-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
JUICYSCOREBRIDGE_DB_HOST=zaman-juicyscoreBridge-postgres
# Ports are defined in app-ports.gen.env
JUICYSCOREBRIDGE_DB_DATABASE=juicyscoreBridge
JUICYSCOREBRIDGE_DB_USER=juicyscoreBridge
JUICYSCOREBRIDGE_DB_PASSWORD=juicyscoreBridge
JUICYSCOREBRIDGE_DB_DEBUG=true

# db mongo provider
JUICYSCOREBRIDGE_MONGO_HOST=zaman-juicyscoreBridge-mongo
# Ports are defined in app-ports.gen.env
JUICYSCOREBRIDGE_MONGO_DATABASE=juicyscoreBridge
JUICYSCOREBRIDGE_MONGO_USERNAME=juicyscoreBridge
JUICYSCOREBRIDGE_MONGO_PASSWORD=juicyscoreBridge
JUICYSCOREBRIDGE_MONGO_OPTIONS=authSource=admin

# kafka provider
JUICYSCOREBRIDGE_KAFKA_HOSTS=zaman-kafka:9092

# jaeger provider
JUICYSCOREBRIDGE_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
JUICYSCOREBRIDGE_OTEL_SERVICE_NAME=juicyscoreBridge
JUICYSCOREBRIDGE_OTEL_SAMPLER_TRACE_RATIO=50


# other
JUICYSCOREBRIDGE_LOG_LEVEL=debug
JUICYSCOREBRIDGE_LOG_PRETTY=false
JUICYSCOREBRIDGE_DEBUG=true
JUICYSCOREBRIDGE_ENVIRONMENT=dev

# Service: jiraBridge
# grpc server
JIRABRIDGE_GRPC_SERVER_REFLECTION=true
JIRABRIDGE_GRPC_SERVER_HOST=zaman-jira-bridge-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
JIRABRIDGE_DB_HOST=zaman-jiraBridge-postgres
# Ports are defined in app-ports.gen.env
JIRABRIDGE_DB_DATABASE=jiraBridge
JIRABRIDGE_DB_USER=jiraBridge
JIRABRIDGE_DB_PASSWORD=jiraBridge
JIRABRIDGE_DB_DEBUG=true

# db mongo provider
JIRABRIDGE_MONGO_HOST=zaman-jiraBridge-mongo
# Ports are defined in app-ports.gen.env
JIRABRIDGE_MONGO_DATABASE=jiraBridge
JIRABRIDGE_MONGO_USERNAME=jiraBridge
JIRABRIDGE_MONGO_PASSWORD=jiraBridge
JIRABRIDGE_MONGO_OPTIONS=authSource=admin

# kafka provider
JIRABRIDGE_KAFKA_HOSTS=zaman-kafka:9092

# jaeger provider
JIRABRIDGE_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
JIRABRIDGE_OTEL_SERVICE_NAME=jiraBridge
JIRABRIDGE_OTEL_SAMPLER_TRACE_RATIO=50


# other
JIRABRIDGE_LOG_LEVEL=debug
JIRABRIDGE_LOG_PRETTY=false
JIRABRIDGE_DEBUG=true
JIRABRIDGE_ENVIRONMENT=dev

# Service: fileGuard
# grpc server
FILEGUARD_GRPC_SERVER_REFLECTION=true
FILEGUARD_GRPC_SERVER_HOST=zaman-file-guard-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
FILEGUARD_DB_HOST=zaman-fileGuard-postgres
# Ports are defined in app-ports.gen.env
FILEGUARD_DB_DATABASE=fileGuard
FILEGUARD_DB_USER=fileGuard
FILEGUARD_DB_PASSWORD=fileGuard
FILEGUARD_DB_DEBUG=true

# kafka provider
FILEGUARD_KAFKA_HOSTS=zaman-kafka:9092

# jaeger provider
FILEGUARD_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
FILEGUARD_OTEL_SERVICE_NAME=fileGuard
FILEGUARD_OTEL_SAMPLER_TRACE_RATIO=50

# s3 provider
FILEGUARD_AWS_SERVICE_URL=fileGuard
FILEGUARD_AWS_BUCKET_NAME=zaman
FILEGUARD_AWS_ACCESS_KEY_ID=
FILEGUARD_AWS_SECRET_ACCESS_KEY=


# other
FILEGUARD_LOG_LEVEL=debug
FILEGUARD_LOG_PRETTY=false
FILEGUARD_DEBUG=true
FILEGUARD_ENVIRONMENT=dev

# Service: scoring
# grpc server
SCORING_GRPC_SERVER_REFLECTION=true
SCORING_GRPC_SERVER_HOST=zaman-scoring-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
SCORING_DB_HOST=zaman-scoring-postgres
# Ports are defined in app-ports.gen.env
SCORING_DB_DATABASE=scoring
SCORING_DB_USER=scoring
SCORING_DB_PASSWORD=scoring
SCORING_DB_DEBUG=true

# kafka provider
SCORING_KAFKA_HOSTS=zaman-kafka:9092

# jaeger provider
SCORING_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
SCORING_OTEL_SERVICE_NAME=scoring
SCORING_OTEL_SAMPLER_TRACE_RATIO=50

# s3 provider
SCORING_AWS_SERVICE_URL=scoring
SCORING_AWS_BUCKET_NAME=zaman
SCORING_AWS_ACCESS_KEY_ID=
SCORING_AWS_SECRET_ACCESS_KEY=


# other
SCORING_LOG_LEVEL=debug
SCORING_LOG_PRETTY=false
SCORING_DEBUG=true
SCORING_ENVIRONMENT=dev

# Service: seonBridge
# grpc server
SEONBRIDGE_GRPC_SERVER_REFLECTION=true
SEONBRIDGE_GRPC_SERVER_HOST=zaman-seon-bridge-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
SEONBRIDGE_DB_HOST=zaman-seonBridge-postgres
# Ports are defined in app-ports.gen.env
SEONBRIDGE_DB_DATABASE=seonBridge
SEONBRIDGE_DB_USER=seonBridge
SEONBRIDGE_DB_PASSWORD=seonBridge
SEONBRIDGE_DB_DEBUG=true

# db mongo provider
SEONBRIDGE_MONGO_HOST=zaman-seonBridge-mongo
# Ports are defined in app-ports.gen.env
SEONBRIDGE_MONGO_DATABASE=seonBridge
SEONBRIDGE_MONGO_USERNAME=seonBridge
SEONBRIDGE_MONGO_PASSWORD=seonBridge
SEONBRIDGE_MONGO_OPTIONS=authSource=admin

# kafka provider
SEONBRIDGE_KAFKA_HOSTS=zaman-kafka:9092

# jaeger provider
SEONBRIDGE_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
SEONBRIDGE_OTEL_SERVICE_NAME=seonBridge
SEONBRIDGE_OTEL_SAMPLER_TRACE_RATIO=50


# other
SEONBRIDGE_LOG_LEVEL=debug
SEONBRIDGE_LOG_PRETTY=false
SEONBRIDGE_DEBUG=true
SEONBRIDGE_ENVIRONMENT=dev

# Service: sprBridge
# grpc server
SPRBRIDGE_GRPC_SERVER_REFLECTION=true
SPRBRIDGE_GRPC_SERVER_HOST=zaman-spr-bridge-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
SPRBRIDGE_DB_HOST=zaman-sprBridge-postgres
# Ports are defined in app-ports.gen.env
SPRBRIDGE_DB_DATABASE=sprBridge
SPRBRIDGE_DB_USER=sprBridge
SPRBRIDGE_DB_PASSWORD=sprBridge
SPRBRIDGE_DB_DEBUG=true

# db mongo provider
SPRBRIDGE_MONGO_HOST=zaman-sprBridge-mongo
# Ports are defined in app-ports.gen.env
SPRBRIDGE_MONGO_DATABASE=sprBridge
SPRBRIDGE_MONGO_USERNAME=sprBridge
SPRBRIDGE_MONGO_PASSWORD=sprBridge
SPRBRIDGE_MONGO_OPTIONS=authSource=admin

# kafka provider
SPRBRIDGE_KAFKA_HOSTS=zaman-kafka:9092

# jaeger provider
SPRBRIDGE_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
SPRBRIDGE_OTEL_SERVICE_NAME=sprBridge
SPRBRIDGE_OTEL_SAMPLER_TRACE_RATIO=50


# other
SPRBRIDGE_LOG_LEVEL=debug
SPRBRIDGE_LOG_PRETTY=false
SPRBRIDGE_DEBUG=true
SPRBRIDGE_ENVIRONMENT=dev

# Service: altScoreBridge
# grpc server
ALTSCOREBRIDGE_GRPC_SERVER_REFLECTION=true
ALTSCOREBRIDGE_GRPC_SERVER_HOST=zaman-alt-score-bridge-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
ALTSCOREBRIDGE_DB_HOST=zaman-altScoreBridge-postgres
# Ports are defined in app-ports.gen.env
ALTSCOREBRIDGE_DB_DATABASE=altScoreBridge
ALTSCOREBRIDGE_DB_USER=altScoreBridge
ALTSCOREBRIDGE_DB_PASSWORD=altScoreBridge
ALTSCOREBRIDGE_DB_DEBUG=true

# db mongo provider
ALTSCOREBRIDGE_MONGO_HOST=zaman-altScoreBridge-mongo
# Ports are defined in app-ports.gen.env
ALTSCOREBRIDGE_MONGO_DATABASE=altScoreBridge
ALTSCOREBRIDGE_MONGO_USERNAME=altScoreBridge
ALTSCOREBRIDGE_MONGO_PASSWORD=altScoreBridge
ALTSCOREBRIDGE_MONGO_OPTIONS=authSource=admin

# kafka provider
ALTSCOREBRIDGE_KAFKA_HOSTS=zaman-kafka:9092

# jaeger provider
ALTSCOREBRIDGE_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
ALTSCOREBRIDGE_OTEL_SERVICE_NAME=altScoreBridge
ALTSCOREBRIDGE_OTEL_SAMPLER_TRACE_RATIO=50


# other
ALTSCOREBRIDGE_LOG_LEVEL=debug
ALTSCOREBRIDGE_LOG_PRETTY=false
ALTSCOREBRIDGE_DEBUG=true
ALTSCOREBRIDGE_ENVIRONMENT=dev

# Service: qazpostBridge
# grpc server
QAZPOSTBRIDGE_GRPC_SERVER_REFLECTION=true
QAZPOSTBRIDGE_GRPC_SERVER_HOST=zaman-qazpost-bridge-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
QAZPOSTBRIDGE_DB_HOST=zaman-qazpostBridge-postgres
# Ports are defined in app-ports.gen.env
QAZPOSTBRIDGE_DB_DATABASE=qazpostBridge
QAZPOSTBRIDGE_DB_USER=qazpostBridge
QAZPOSTBRIDGE_DB_PASSWORD=qazpostBridge
QAZPOSTBRIDGE_DB_DEBUG=true

# db mongo provider
QAZPOSTBRIDGE_MONGO_HOST=zaman-qazpostBridge-mongo
# Ports are defined in app-ports.gen.env
QAZPOSTBRIDGE_MONGO_DATABASE=qazpostBridge
QAZPOSTBRIDGE_MONGO_USERNAME=qazpostBridge
QAZPOSTBRIDGE_MONGO_PASSWORD=qazpostBridge
QAZPOSTBRIDGE_MONGO_OPTIONS=authSource=admin

# kafka provider
QAZPOSTBRIDGE_KAFKA_HOSTS=zaman-kafka:9092

# jaeger provider
QAZPOSTBRIDGE_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
QAZPOSTBRIDGE_OTEL_SERVICE_NAME=qazpostBridge
QAZPOSTBRIDGE_OTEL_SAMPLER_TRACE_RATIO=50


# other
QAZPOSTBRIDGE_LOG_LEVEL=debug
QAZPOSTBRIDGE_LOG_PRETTY=false
QAZPOSTBRIDGE_DEBUG=true
QAZPOSTBRIDGE_ENVIRONMENT=dev

# Service: deposits
# grpc server
DEPOSITS_GRPC_SERVER_REFLECTION=true
DEPOSITS_GRPC_SERVER_HOST=zaman-deposits-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
DEPOSITS_DB_HOST=zaman-deposits-postgres
# Ports are defined in app-ports.gen.env
DEPOSITS_DB_DATABASE=deposits
DEPOSITS_DB_USER=deposits
DEPOSITS_DB_PASSWORD=deposits
DEPOSITS_DB_DEBUG=true

# kafka provider
DEPOSITS_KAFKA_HOSTS=zaman-kafka:9092

# jaeger provider
DEPOSITS_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
DEPOSITS_OTEL_SERVICE_NAME=deposits
DEPOSITS_OTEL_SAMPLER_TRACE_RATIO=50


# other
DEPOSITS_LOG_LEVEL=debug
DEPOSITS_LOG_PRETTY=false
DEPOSITS_DEBUG=true
DEPOSITS_ENVIRONMENT=dev

# Service: bsasBridge
# grpc server
BSASBRIDGE_GRPC_SERVER_REFLECTION=true
BSASBRIDGE_GRPC_SERVER_HOST=zaman-bsas-bridge-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
BSASBRIDGE_DB_HOST=zaman-bsasBridge-postgres
# Ports are defined in app-ports.gen.env
BSASBRIDGE_DB_DATABASE=bsasBridge
BSASBRIDGE_DB_USER=bsasBridge
BSASBRIDGE_DB_PASSWORD=bsasBridge
BSASBRIDGE_DB_DEBUG=true

# db mongo provider
BSASBRIDGE_MONGO_HOST=zaman-bsasBridge-mongo
# Ports are defined in app-ports.gen.env
BSASBRIDGE_MONGO_DATABASE=bsasBridge
BSASBRIDGE_MONGO_USERNAME=bsasBridge
BSASBRIDGE_MONGO_PASSWORD=bsasBridge
BSASBRIDGE_MONGO_OPTIONS=authSource=admin

# kafka provider
BSASBRIDGE_KAFKA_HOSTS=zaman-kafka:9092

# jaeger provider
BSASBRIDGE_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
BSASBRIDGE_OTEL_SERVICE_NAME=bsasBridge
BSASBRIDGE_OTEL_SAMPLER_TRACE_RATIO=50


# other
BSASBRIDGE_LOG_LEVEL=debug
BSASBRIDGE_LOG_PRETTY=false
BSASBRIDGE_DEBUG=true
BSASBRIDGE_ENVIRONMENT=dev

# Service: apBridge
# grpc server
APBRIDGE_GRPC_SERVER_REFLECTION=true
APBRIDGE_GRPC_SERVER_HOST=zaman-ap-bridge-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
APBRIDGE_DB_HOST=zaman-apBridge-postgres
# Ports are defined in app-ports.gen.env
APBRIDGE_DB_DATABASE=apBridge
APBRIDGE_DB_USER=apBridge
APBRIDGE_DB_PASSWORD=apBridge
APBRIDGE_DB_DEBUG=true

# db mongo provider
APBRIDGE_MONGO_HOST=zaman-apBridge-mongo
# Ports are defined in app-ports.gen.env
APBRIDGE_MONGO_DATABASE=apBridge
APBRIDGE_MONGO_USERNAME=apBridge
APBRIDGE_MONGO_PASSWORD=apBridge
APBRIDGE_MONGO_OPTIONS=authSource=admin

# kafka provider
APBRIDGE_KAFKA_HOSTS=zaman-kafka:9092

# jaeger provider
APBRIDGE_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
APBRIDGE_OTEL_SERVICE_NAME=apBridge
APBRIDGE_OTEL_SAMPLER_TRACE_RATIO=50


# other
APBRIDGE_LOG_LEVEL=debug
APBRIDGE_LOG_PRETTY=false
APBRIDGE_DEBUG=true
APBRIDGE_ENVIRONMENT=dev

# Service: processingBridge
# grpc server
PROCESSINGBRIDGE_GRPC_SERVER_REFLECTION=true
PROCESSINGBRIDGE_GRPC_SERVER_HOST=zaman-processing-bridge-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
PROCESSINGBRIDGE_DB_HOST=zaman-processingBridge-postgres
# Ports are defined in app-ports.gen.env
PROCESSINGBRIDGE_DB_DATABASE=processingBridge
PROCESSINGBRIDGE_DB_USER=processingBridge
PROCESSINGBRIDGE_DB_PASSWORD=processingBridge
PROCESSINGBRIDGE_DB_DEBUG=true

# db mongo provider
PROCESSINGBRIDGE_MONGO_HOST=zaman-processingBridge-mongo
# Ports are defined in app-ports.gen.env
PROCESSINGBRIDGE_MONGO_DATABASE=processingBridge
PROCESSINGBRIDGE_MONGO_USERNAME=processingBridge
PROCESSINGBRIDGE_MONGO_PASSWORD=processingBridge
PROCESSINGBRIDGE_MONGO_OPTIONS=authSource=admin

# kafka provider
PROCESSINGBRIDGE_KAFKA_HOSTS=zaman-kafka:9092

# jaeger provider
PROCESSINGBRIDGE_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
PROCESSINGBRIDGE_OTEL_SERVICE_NAME=processingBridge
PROCESSINGBRIDGE_OTEL_SAMPLER_TRACE_RATIO=50


# other
PROCESSINGBRIDGE_LOG_LEVEL=debug
PROCESSINGBRIDGE_LOG_PRETTY=false
PROCESSINGBRIDGE_DEBUG=true
PROCESSINGBRIDGE_ENVIRONMENT=dev

# Service: collection
# grpc server
COLLECTION_GRPC_SERVER_REFLECTION=true
COLLECTION_GRPC_SERVER_HOST=zaman-collection-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
COLLECTION_DB_HOST=zaman-collection-postgres
# Ports are defined in app-ports.gen.env
COLLECTION_DB_DATABASE=collection
COLLECTION_DB_USER=collection
COLLECTION_DB_PASSWORD=collection
COLLECTION_DB_DEBUG=true

# kafka provider
COLLECTION_KAFKA_HOSTS=zaman-kafka:9092

# chronos provider
COLLECTION_CHRONOS_TIME_ZONE=Local
COLLECTION_CHRONOS_SECONDS_REQUIRED=false

# jaeger provider
COLLECTION_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
COLLECTION_OTEL_SERVICE_NAME=collection
COLLECTION_OTEL_SAMPLER_TRACE_RATIO=50

# s3 provider
COLLECTION_AWS_SERVICE_URL=collection
COLLECTION_AWS_BUCKET_NAME=zaman
COLLECTION_AWS_ACCESS_KEY_ID=
COLLECTION_AWS_SECRET_ACCESS_KEY=


# other
COLLECTION_LOG_LEVEL=debug
COLLECTION_LOG_PRETTY=false
COLLECTION_DEBUG=true
COLLECTION_ENVIRONMENT=dev

# Service: paymentsSme
# grpc server
PAYMENTSSME_GRPC_SERVER_REFLECTION=true
PAYMENTSSME_GRPC_SERVER_HOST=zaman-payments-sme-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
PAYMENTSSME_DB_HOST=zaman-paymentsSme-postgres
# Ports are defined in app-ports.gen.env
PAYMENTSSME_DB_DATABASE=paymentsSme
PAYMENTSSME_DB_USER=paymentsSme
PAYMENTSSME_DB_PASSWORD=paymentsSme
PAYMENTSSME_DB_DEBUG=true

# kafka provider
PAYMENTSSME_KAFKA_HOSTS=zaman-kafka:9092

# jaeger provider
PAYMENTSSME_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
PAYMENTSSME_OTEL_SERVICE_NAME=paymentsSme
PAYMENTSSME_OTEL_SAMPLER_TRACE_RATIO=50


# other
PAYMENTSSME_LOG_LEVEL=debug
PAYMENTSSME_LOG_PRETTY=false
PAYMENTSSME_DEBUG=true
PAYMENTSSME_ENVIRONMENT=dev

# Service: referral
# grpc server
REFERRAL_GRPC_SERVER_REFLECTION=true
REFERRAL_GRPC_SERVER_HOST=zaman-referral-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
REFERRAL_DB_HOST=zaman-referral-postgres
# Ports are defined in app-ports.gen.env
REFERRAL_DB_DATABASE=referral
REFERRAL_DB_USER=referral
REFERRAL_DB_PASSWORD=referral
REFERRAL_DB_DEBUG=true

# chronos provider
REFERRAL_CHRONOS_TIME_ZONE=Local
REFERRAL_CHRONOS_SECONDS_REQUIRED=false

# jaeger provider
REFERRAL_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
REFERRAL_OTEL_SERVICE_NAME=referral
REFERRAL_OTEL_SAMPLER_TRACE_RATIO=50


# other
REFERRAL_LOG_LEVEL=debug
REFERRAL_LOG_PRETTY=false
REFERRAL_DEBUG=true
REFERRAL_ENVIRONMENT=dev

# Service: antifraud
# grpc server
ANTIFRAUD_GRPC_SERVER_REFLECTION=true
ANTIFRAUD_GRPC_SERVER_HOST=zaman-antifraud-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
ANTIFRAUD_DB_HOST=zaman-antifraud-postgres
# Ports are defined in app-ports.gen.env
ANTIFRAUD_DB_DATABASE=antifraud
ANTIFRAUD_DB_USER=antifraud
ANTIFRAUD_DB_PASSWORD=antifraud
ANTIFRAUD_DB_DEBUG=true

# chronos provider
ANTIFRAUD_CHRONOS_TIME_ZONE=Local
ANTIFRAUD_CHRONOS_SECONDS_REQUIRED=false

# jaeger provider
ANTIFRAUD_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
ANTIFRAUD_OTEL_SERVICE_NAME=antifraud
ANTIFRAUD_OTEL_SAMPLER_TRACE_RATIO=50


# other
ANTIFRAUD_LOG_LEVEL=debug
ANTIFRAUD_LOG_PRETTY=false
ANTIFRAUD_DEBUG=true
ANTIFRAUD_ENVIRONMENT=dev

# Service: crm
# grpc server
CRM_GRPC_SERVER_REFLECTION=true
CRM_GRPC_SERVER_HOST=zaman-crm-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
CRM_DB_HOST=zaman-crm-postgres
# Ports are defined in app-ports.gen.env
CRM_DB_DATABASE=crm
CRM_DB_USER=crm
CRM_DB_PASSWORD=crm
CRM_DB_DEBUG=true

# jaeger provider
CRM_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
CRM_OTEL_SERVICE_NAME=crm
CRM_OTEL_SAMPLER_TRACE_RATIO=50


# other
CRM_LOG_LEVEL=debug
CRM_LOG_PRETTY=false
CRM_DEBUG=true
CRM_ENVIRONMENT=dev

# Service: tokenize
# grpc server
TOKENIZE_GRPC_SERVER_REFLECTION=true
TOKENIZE_GRPC_SERVER_HOST=zaman-tokenize-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
TOKENIZE_DB_HOST=zaman-tokenize-postgres
# Ports are defined in app-ports.gen.env
TOKENIZE_DB_DATABASE=tokenize
TOKENIZE_DB_USER=tokenize
TOKENIZE_DB_PASSWORD=tokenize
TOKENIZE_DB_DEBUG=true


# other
TOKENIZE_LOG_LEVEL=debug
TOKENIZE_LOG_PRETTY=false
TOKENIZE_DEBUG=true
TOKENIZE_ENVIRONMENT=dev

# Service: balanceUpdater
# grpc server
BALANCEUPDATER_GRPC_SERVER_REFLECTION=true
BALANCEUPDATER_GRPC_SERVER_HOST=zaman-balance-updater-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
BALANCEUPDATER_DB_HOST=zaman-balanceUpdater-postgres
# Ports are defined in app-ports.gen.env
BALANCEUPDATER_DB_DATABASE=balanceUpdater
BALANCEUPDATER_DB_USER=balanceUpdater
BALANCEUPDATER_DB_PASSWORD=balanceUpdater
BALANCEUPDATER_DB_DEBUG=true

# kafka provider
BALANCEUPDATER_KAFKA_HOSTS=zaman-kafka:9092


# other
BALANCEUPDATER_LOG_LEVEL=debug
BALANCEUPDATER_LOG_PRETTY=false
BALANCEUPDATER_DEBUG=true
BALANCEUPDATER_ENVIRONMENT=dev

# Service: kaspiBridge
# grpc server
KASPIBRIDGE_GRPC_SERVER_REFLECTION=true
KASPIBRIDGE_GRPC_SERVER_HOST=zaman-kaspi-bridge-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
KASPIBRIDGE_DB_HOST=zaman-kaspiBridge-postgres
# Ports are defined in app-ports.gen.env
KASPIBRIDGE_DB_DATABASE=kaspiBridge
KASPIBRIDGE_DB_USER=kaspiBridge
KASPIBRIDGE_DB_PASSWORD=kaspiBridge
KASPIBRIDGE_DB_DEBUG=true

# db mongo provider
KASPIBRIDGE_MONGO_HOST=zaman-kaspiBridge-mongo
# Ports are defined in app-ports.gen.env
KASPIBRIDGE_MONGO_DATABASE=kaspiBridge
KASPIBRIDGE_MONGO_USERNAME=kaspiBridge
KASPIBRIDGE_MONGO_PASSWORD=kaspiBridge
KASPIBRIDGE_MONGO_OPTIONS=authSource=admin

# kafka provider
KASPIBRIDGE_KAFKA_HOSTS=zaman-kafka:9092

# jaeger provider
KASPIBRIDGE_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
KASPIBRIDGE_OTEL_SERVICE_NAME=kaspiBridge
KASPIBRIDGE_OTEL_SAMPLER_TRACE_RATIO=50


# other
KASPIBRIDGE_LOG_LEVEL=debug
KASPIBRIDGE_LOG_PRETTY=false
KASPIBRIDGE_DEBUG=true
KASPIBRIDGE_ENVIRONMENT=dev

# Service: bitrixBridge
# grpc server
BITRIXBRIDGE_GRPC_SERVER_REFLECTION=true
BITRIXBRIDGE_GRPC_SERVER_HOST=zaman-bitrix-bridge-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
BITRIXBRIDGE_DB_HOST=zaman-bitrixBridge-postgres
# Ports are defined in app-ports.gen.env
BITRIXBRIDGE_DB_DATABASE=bitrixBridge
BITRIXBRIDGE_DB_USER=bitrixBridge
BITRIXBRIDGE_DB_PASSWORD=bitrixBridge
BITRIXBRIDGE_DB_DEBUG=true

# db mongo provider
BITRIXBRIDGE_MONGO_HOST=zaman-bitrixBridge-mongo
# Ports are defined in app-ports.gen.env
BITRIXBRIDGE_MONGO_DATABASE=bitrixBridge
BITRIXBRIDGE_MONGO_USERNAME=bitrixBridge
BITRIXBRIDGE_MONGO_PASSWORD=bitrixBridge
BITRIXBRIDGE_MONGO_OPTIONS=authSource=admin

# kafka provider
BITRIXBRIDGE_KAFKA_HOSTS=zaman-kafka:9092

# jaeger provider
BITRIXBRIDGE_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
BITRIXBRIDGE_OTEL_SERVICE_NAME=bitrixBridge
BITRIXBRIDGE_OTEL_SAMPLER_TRACE_RATIO=50


# other
BITRIXBRIDGE_LOG_LEVEL=debug
BITRIXBRIDGE_LOG_PRETTY=false
BITRIXBRIDGE_DEBUG=true
BITRIXBRIDGE_ENVIRONMENT=dev

# Service: foreignActivity
# grpc server
FOREIGNACTIVITY_GRPC_SERVER_REFLECTION=true
FOREIGNACTIVITY_GRPC_SERVER_HOST=zaman-foreign-activity-srv
# Ports are defined in app-ports.gen.env
# db postgres provider
FOREIGNACTIVITY_DB_HOST=zaman-foreignActivity-postgres
# Ports are defined in app-ports.gen.env
FOREIGNACTIVITY_DB_DATABASE=foreignActivity
FOREIGNACTIVITY_DB_USER=foreignActivity
FOREIGNACTIVITY_DB_PASSWORD=foreignActivity
FOREIGNACTIVITY_DB_DEBUG=true

# kafka provider
FOREIGNACTIVITY_KAFKA_HOSTS=zaman-kafka:9092

# redis provider
FOREIGNACTIVITY_REDIS_HOST=zaman-redis
FOREIGNACTIVITY_REDIS_PORT=6379

# jaeger provider
FOREIGNACTIVITY_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
FOREIGNACTIVITY_OTEL_SERVICE_NAME=foreignActivity
FOREIGNACTIVITY_OTEL_SAMPLER_TRACE_RATIO=50


# other
FOREIGNACTIVITY_LOG_LEVEL=debug
FOREIGNACTIVITY_LOG_PRETTY=false
FOREIGNACTIVITY_DEBUG=true
FOREIGNACTIVITY_ENVIRONMENT=dev


##########GATEWAYS ENVS##########

# Gateway: mobile
MOBILE_HTTP_SERVER_TIMEOUT=15000ms
MOBILE_HTTP_SERVER_CACHE_TTL=300s
MOBILE_HTTP_SERVER_BASE_URL=/api/v1
MOBILE_HTTP_SERVER_MAX_RATE=10
MOBILE_HTTP_SERVER_RATE_PERIOD=1m
MOBILE_HTTP_SERVER_CORS_ORIGINS=[*]
MOBILE_HTTP_SERVER_CORS_METHODS=[GET POST PUT DELETE OPTIONS]
MOBILE_HTTP_SERVER_CORS_HEADERS=[*]
MOBILE_HTTP_SERVER_CORS_CREDENTIALS=true
# security jwt
MOBILE_JWT_SECRET=zaman-secret

# spec credentials
MOBILE_SPEC_BASIC_AUTH_LOGIN=admin
MOBILE_SPEC_BASIC_AUTH_PASSWORD=zaman

# other
MOBILE_LOG_LEVEL=debug
MOBILE_LOG_PRETTY=false
MOBILE_DEBUG=true
MOBILE_ENVIRONMENT=dev

MOBILE_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
MOBILE_OTEL_SERVICE_NAME=mobile
MOBILE_OTEL_SAMPLER_TRACE_RATIO=50

# Gateway: sme
SME_HTTP_SERVER_TIMEOUT=15000ms
SME_HTTP_SERVER_CACHE_TTL=300s
SME_HTTP_SERVER_BASE_URL=/api/v1
SME_HTTP_SERVER_MAX_RATE=10
SME_HTTP_SERVER_RATE_PERIOD=1m
SME_HTTP_SERVER_CORS_ORIGINS=[*]
SME_HTTP_SERVER_CORS_METHODS=[GET POST PUT DELETE OPTIONS]
SME_HTTP_SERVER_CORS_HEADERS=[*]
SME_HTTP_SERVER_CORS_CREDENTIALS=true
# security jwt
SME_JWT_SECRET=zaman-secret

# spec credentials
SME_SPEC_BASIC_AUTH_LOGIN=admin
SME_SPEC_BASIC_AUTH_PASSWORD=zaman

# other
SME_LOG_LEVEL=debug
SME_LOG_PRETTY=false
SME_DEBUG=true
SME_ENVIRONMENT=dev

SME_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
SME_OTEL_SERVICE_NAME=sme
SME_OTEL_SAMPLER_TRACE_RATIO=50

# Gateway: paymentsgw
PAYMENTSGW_HTTP_SERVER_TIMEOUT=30000ms
PAYMENTSGW_HTTP_SERVER_CACHE_TTL=300s
PAYMENTSGW_HTTP_SERVER_BASE_URL=/api/v1
PAYMENTSGW_HTTP_SERVER_MAX_RATE=10
PAYMENTSGW_HTTP_SERVER_RATE_PERIOD=1m
PAYMENTSGW_HTTP_SERVER_CORS_ORIGINS=[*]
PAYMENTSGW_HTTP_SERVER_CORS_METHODS=[GET POST PUT DELETE OPTIONS]
PAYMENTSGW_HTTP_SERVER_CORS_HEADERS=[*]
PAYMENTSGW_HTTP_SERVER_CORS_CREDENTIALS=true

# spec credentials
PAYMENTSGW_SPEC_BASIC_AUTH_LOGIN=admin
PAYMENTSGW_SPEC_BASIC_AUTH_PASSWORD=zaman

# other
PAYMENTSGW_LOG_LEVEL=debug
PAYMENTSGW_LOG_PRETTY=false
PAYMENTSGW_DEBUG=true
PAYMENTSGW_ENVIRONMENT=dev

PAYMENTSGW_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
PAYMENTSGW_OTEL_SERVICE_NAME=paymentsgw
PAYMENTSGW_OTEL_SAMPLER_TRACE_RATIO=50

# Gateway: balancegw
BALANCEGW_HTTP_SERVER_TIMEOUT=15000ms
BALANCEGW_HTTP_SERVER_CACHE_TTL=300s
BALANCEGW_HTTP_SERVER_BASE_URL=/api/v1
BALANCEGW_HTTP_SERVER_MAX_RATE=10
BALANCEGW_HTTP_SERVER_RATE_PERIOD=1m
BALANCEGW_HTTP_SERVER_CORS_ORIGINS=[*]
BALANCEGW_HTTP_SERVER_CORS_METHODS=[GET POST PUT DELETE OPTIONS]
BALANCEGW_HTTP_SERVER_CORS_HEADERS=[*]
BALANCEGW_HTTP_SERVER_CORS_CREDENTIALS=true

# spec credentials
BALANCEGW_SPEC_BASIC_AUTH_LOGIN=admin
BALANCEGW_SPEC_BASIC_AUTH_PASSWORD=zaman

# other
BALANCEGW_LOG_LEVEL=debug
BALANCEGW_LOG_PRETTY=false
BALANCEGW_DEBUG=true
BALANCEGW_ENVIRONMENT=dev

BALANCEGW_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
BALANCEGW_OTEL_SERVICE_NAME=balancegw
BALANCEGW_OTEL_SAMPLER_TRACE_RATIO=50

# Gateway: documentsgw
DOCUMENTSGW_HTTP_SERVER_TIMEOUT=15000ms
DOCUMENTSGW_HTTP_SERVER_CACHE_TTL=300s
DOCUMENTSGW_HTTP_SERVER_BASE_URL=/documents
DOCUMENTSGW_HTTP_SERVER_MAX_RATE=10
DOCUMENTSGW_HTTP_SERVER_RATE_PERIOD=1m
DOCUMENTSGW_HTTP_SERVER_CORS_ORIGINS=[*]
DOCUMENTSGW_HTTP_SERVER_CORS_METHODS=[GET POST PUT DELETE OPTIONS]
DOCUMENTSGW_HTTP_SERVER_CORS_HEADERS=[*]
DOCUMENTSGW_HTTP_SERVER_CORS_CREDENTIALS=true
# security jwt
DOCUMENTSGW_JWT_SECRET=zaman-secret

# spec credentials
DOCUMENTSGW_SPEC_BASIC_AUTH_LOGIN=admin
DOCUMENTSGW_SPEC_BASIC_AUTH_PASSWORD=zaman

# other
DOCUMENTSGW_LOG_LEVEL=debug
DOCUMENTSGW_LOG_PRETTY=false
DOCUMENTSGW_DEBUG=true
DOCUMENTSGW_ENVIRONMENT=dev

DOCUMENTSGW_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
DOCUMENTSGW_OTEL_SERVICE_NAME=documentsgw
DOCUMENTSGW_OTEL_SAMPLER_TRACE_RATIO=50

# Gateway: crmgw
CRMGW_HTTP_SERVER_TIMEOUT=15000ms
CRMGW_HTTP_SERVER_CACHE_TTL=300s
CRMGW_HTTP_SERVER_BASE_URL=/api/v1
CRMGW_HTTP_SERVER_MAX_RATE=10
CRMGW_HTTP_SERVER_RATE_PERIOD=1m
CRMGW_HTTP_SERVER_CORS_ORIGINS=[*]
CRMGW_HTTP_SERVER_CORS_METHODS=[GET POST PUT DELETE OPTIONS]
CRMGW_HTTP_SERVER_CORS_HEADERS=[*]
CRMGW_HTTP_SERVER_CORS_CREDENTIALS=true

# spec credentials
CRMGW_SPEC_BASIC_AUTH_LOGIN=admin
CRMGW_SPEC_BASIC_AUTH_PASSWORD=zaman

# other
CRMGW_LOG_LEVEL=debug
CRMGW_LOG_PRETTY=false
CRMGW_DEBUG=true
CRMGW_ENVIRONMENT=dev

CRMGW_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
CRMGW_OTEL_SERVICE_NAME=crmgw
CRMGW_OTEL_SAMPLER_TRACE_RATIO=50

# Gateway: collectiongw
COLLECTIONGW_HTTP_SERVER_TIMEOUT=15000ms
COLLECTIONGW_HTTP_SERVER_CACHE_TTL=300s
COLLECTIONGW_HTTP_SERVER_BASE_URL=/api/v1
COLLECTIONGW_HTTP_SERVER_MAX_RATE=10
COLLECTIONGW_HTTP_SERVER_RATE_PERIOD=1m
COLLECTIONGW_HTTP_SERVER_CORS_ORIGINS=[*]
COLLECTIONGW_HTTP_SERVER_CORS_METHODS=[GET POST PUT DELETE OPTIONS]
COLLECTIONGW_HTTP_SERVER_CORS_HEADERS=[*]
COLLECTIONGW_HTTP_SERVER_CORS_CREDENTIALS=true
# security jwt
COLLECTIONGW_JWT_SECRET=zaman-secret

# spec credentials
COLLECTIONGW_SPEC_BASIC_AUTH_LOGIN=admin
COLLECTIONGW_SPEC_BASIC_AUTH_PASSWORD=zaman

# other
COLLECTIONGW_LOG_LEVEL=debug
COLLECTIONGW_LOG_PRETTY=false
COLLECTIONGW_DEBUG=true
COLLECTIONGW_ENVIRONMENT=dev

COLLECTIONGW_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
COLLECTIONGW_OTEL_SERVICE_NAME=collectiongw
COLLECTIONGW_OTEL_SAMPLER_TRACE_RATIO=50

# Gateway: landing
LANDING_HTTP_SERVER_TIMEOUT=15000ms
LANDING_HTTP_SERVER_CACHE_TTL=300s
LANDING_HTTP_SERVER_BASE_URL=/api/v1
LANDING_HTTP_SERVER_MAX_RATE=10
LANDING_HTTP_SERVER_RATE_PERIOD=1m
LANDING_HTTP_SERVER_CORS_ORIGINS=[*]
LANDING_HTTP_SERVER_CORS_METHODS=[GET POST PUT DELETE OPTIONS]
LANDING_HTTP_SERVER_CORS_HEADERS=[*]
LANDING_HTTP_SERVER_CORS_CREDENTIALS=true

# spec credentials
LANDING_SPEC_BASIC_AUTH_LOGIN=admin
LANDING_SPEC_BASIC_AUTH_PASSWORD=zaman

# other
LANDING_LOG_LEVEL=debug
LANDING_LOG_PRETTY=false
LANDING_DEBUG=true
LANDING_ENVIRONMENT=dev

LANDING_OTEL_EXPORTER_ENDPOINT=http://zaman-jaeger:4318
LANDING_OTEL_SERVICE_NAME=landing
LANDING_OTEL_SAMPLER_TRACE_RATIO=50

