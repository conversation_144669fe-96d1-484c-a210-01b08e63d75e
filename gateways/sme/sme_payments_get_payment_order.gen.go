// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package sme

import (
	"context"
	"net/http"

	"git.redmadrobot.com/backend-go/rmr-pkg/grpcx"
	"git.redmadrobot.com/backend-go/rmr-pkg/httpx"

	"git.redmadrobot.com/zaman/backend/zaman/gateways/sme/mappers"
	"git.redmadrobot.com/zaman/backend/zaman/gateways/sme/openapi"
)

func (s *Server) SmePaymentsGetPaymentOrder(w http.ResponseWriter, r *http.Request, transactionID openapi.PaymentsTransactionID) {
	httpx.NewSecureServerHandler[any, *openapi.SmePaymentsGetPaymentOrderResponse](w, r).
		WithResponseJSON().
		WithMethod(func(ctx context.Context, _ any) (*openapi.SmePaymentsGetPaymentOrderResponse, error) {
			var smePaymentsGetPaymentOrder mappers.SmePaymentsGetPaymentOrder
			smePaymentsGetPaymentOrder.SmePaymentsGetPaymentOrderRequest(transactionID)
			if paymentsSmeSmePaymentsGetPaymentOrderGRPCRequestMapErr := smePaymentsGetPaymentOrder.PaymentssmeSmePaymentsGetPaymentOrderGRPCRequest(); paymentsSmeSmePaymentsGetPaymentOrderGRPCRequestMapErr != nil {
				return nil, paymentsSmeSmePaymentsGetPaymentOrderGRPCRequestMapErr
			}
			paymentsSmeSmePaymentsGetPaymentOrderGRPC, err := s.Providers.Paymentssme.SmePaymentsGetPaymentOrder(ctx, smePaymentsGetPaymentOrder.GRPC.PaymentssmeSmePaymentsGetPaymentOrder.Request)
			if err != nil {
				return nil, grpcx.ErrorFromGRPC(err)
			}
			smePaymentsGetPaymentOrder.GRPC.PaymentssmeSmePaymentsGetPaymentOrder.Response = paymentsSmeSmePaymentsGetPaymentOrderGRPC

			if smePaymentsGetPaymentOrderResponseMapErr := smePaymentsGetPaymentOrder.SmePaymentsGetPaymentOrderResponse(); smePaymentsGetPaymentOrderResponseMapErr != nil {
				return nil, smePaymentsGetPaymentOrderResponseMapErr
			}
			return smePaymentsGetPaymentOrder.Openapi.Response, nil

		}).Handle()
}
