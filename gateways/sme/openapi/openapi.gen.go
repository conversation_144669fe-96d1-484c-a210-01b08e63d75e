// Package openapi provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.5.0 DO NOT EDIT.
package openapi

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"
	"time"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/go-chi/chi/v5"
	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

const (
	BearerTokenAuthScopes = "BearerTokenAuth.Scopes"
)

// Defines values for AccountDocumentType.
const (
	AccountAvailabilitySMEIP AccountDocumentType = "accountAvailabilitySMEIP"
	AccountStatementSMEIP    AccountDocumentType = "accountStatementSMEIP"
	AccountStatementSMEIPKz  AccountDocumentType = "accountStatementSMEIPKz"
	AvailableBalanceSMEIP    AccountDocumentType = "availableBalanceSMEIP"
)

// Defines values for ClientVerificationResponseStatus.
const (
	ClientVerificationResponseStatusDONE       ClientVerificationResponseStatus = "DONE"
	ClientVerificationResponseStatusERROR      ClientVerificationResponseStatus = "ERROR"
	ClientVerificationResponseStatusINPROGRESS ClientVerificationResponseStatus = "IN_PROGRESS"
)

// Defines values for ClientVerificationResponseVerificationResult.
const (
	ClientVerificationResponseVerificationResultAPPROVED ClientVerificationResponseVerificationResult = "APPROVED"
	ClientVerificationResponseVerificationResultPENDING  ClientVerificationResponseVerificationResult = "PENDING"
	ClientVerificationResponseVerificationResultREJECTED ClientVerificationResponseVerificationResult = "REJECTED"
)

// Defines values for CreateConvertationRequestConversionType.
const (
	BUYCURRENCY  CreateConvertationRequestConversionType = "BUY_CURRENCY"
	SELLCURRENCY CreateConvertationRequestConversionType = "SELL_CURRENCY"
)

// Defines values for CreateConvertationResponseStatus.
const (
	CreateConvertationResponseStatusCOMPLETED CreateConvertationResponseStatus = "COMPLETED"
	CreateConvertationResponseStatusREJECTED  CreateConvertationResponseStatus = "REJECTED"
)

// Defines values for CreatePaymentResponseReason.
const (
	CreatePaymentResponseReasonInsufficientFunds      CreatePaymentResponseReason = "Insufficient funds"
	CreatePaymentResponseReasonNoActiveAccount        CreatePaymentResponseReason = "No active account"
	CreatePaymentResponseReasonUnableToProcessPayment CreatePaymentResponseReason = "Unable to process payment"
	CreatePaymentResponseReasonValidationError        CreatePaymentResponseReason = "Validation error"
	CreatePaymentResponseReasonWorktimeExceeded       CreatePaymentResponseReason = "Worktime exceeded"
)

// Defines values for CreatePaymentResponseReasonCode.
const (
	N1005 CreatePaymentResponseReasonCode = "1005"
	N1013 CreatePaymentResponseReasonCode = "1013"
	N1040 CreatePaymentResponseReasonCode = "1040"
	N1041 CreatePaymentResponseReasonCode = "1041"
	N1042 CreatePaymentResponseReasonCode = "1042"
)

// Defines values for CreatePaymentResponseStatus.
const (
	CreatePaymentResponseStatusCOMPLETED   CreatePaymentResponseStatus = "COMPLETED"
	CreatePaymentResponseStatusINITIALIZED CreatePaymentResponseStatus = "INITIALIZED"
	CreatePaymentResponseStatusINPROGRESS  CreatePaymentResponseStatus = "IN_PROGRESS"
	CreatePaymentResponseStatusREJECTED    CreatePaymentResponseStatus = "REJECTED"
)

// Defines values for CurrencyCode.
const (
	CurrencyCodeCNY CurrencyCode = "CNY"
	CurrencyCodeEUR CurrencyCode = "EUR"
	CurrencyCodeKZT CurrencyCode = "KZT"
	CurrencyCodeRUB CurrencyCode = "RUB"
	CurrencyCodeUSD CurrencyCode = "USD"
)

// Defines values for DeviceInfoSystemType.
const (
	Android DeviceInfoSystemType = "Android"
	IOS     DeviceInfoSystemType = "iOS"
	Web     DeviceInfoSystemType = "Web"
)

// Defines values for DisbursementMode.
const (
	LoanDisbursementAutoMode   DisbursementMode = "loanDisbursementAutoMode"
	LoanDisbursementAutoModeV2 DisbursementMode = "loanDisbursementAutoModeV2"
	LoanDisbursementManualMode DisbursementMode = "loanDisbursementManualMode"
)

// Defines values for DocumentForAccountOpeningType.
const (
	AdditionalAccountOpeningApplicationSMEIP DocumentForAccountOpeningType = "AdditionalAccountOpeningApplicationSMEIP"
)

// Defines values for DocumentType.
const (
	DocumentTypeAccessionAgreementSMEIP        DocumentType = "accessionAgreementSMEIP"
	DocumentTypeAccountOpeningApplicationSMEIP DocumentType = "accountOpeningApplicationSMEIP"
	DocumentTypeBankServiceAgreementSMEIP      DocumentType = "bankServiceAgreementSMEIP"
	DocumentTypeComplexConsentClientSMEIP      DocumentType = "complexConsentClientSMEIP"
	DocumentTypeFinancingTermsInfoSMEIP        DocumentType = "financingTermsInfoSMEIP"
	DocumentTypePersonalDataAgreementSMEIP     DocumentType = "personalDataAgreementSMEIP"
)

// Defines values for LoanAppDocumentType.
const (
	LoanAppDocumentTypeComplexConsentClientSMEIP LoanAppDocumentType = "complexConsentClientSMEIP"
)

// Defines values for LoanDetailsProductType.
const (
	LoanDetailsProductTypeLOAN      LoanDetailsProductType = "LOAN"
	LoanDetailsProductTypeREFINANCE LoanDetailsProductType = "REFINANCE"
)

// Defines values for NextStep.
const (
	DboSignRequired          NextStep = "dboSignRequired"
	IdentificationRequired   NextStep = "identificationRequired"
	ReidentificationRequired NextStep = "reidentificationRequired"
)

// Defines values for PaymentsOperationType.
const (
	PaymentsOperationTypeCREDIT PaymentsOperationType = "CREDIT"
	PaymentsOperationTypeDEBIT  PaymentsOperationType = "DEBIT"
)

// Defines values for PaymentsTransactionStatus.
const (
	PaymentsTransactionStatusCOMPLETED   PaymentsTransactionStatus = "COMPLETED"
	PaymentsTransactionStatusINITIALIZED PaymentsTransactionStatus = "INITIALIZED"
	PaymentsTransactionStatusINPROGRESS  PaymentsTransactionStatus = "IN_PROGRESS"
	PaymentsTransactionStatusREJECTED    PaymentsTransactionStatus = "REJECTED"
)

// Defines values for PublicDocumentType.
const (
	FinancingTermsInfoSMEIP    PublicDocumentType = "financingTermsInfoSMEIP"
	PersonalDataAgreementSMEIP PublicDocumentType = "personalDataAgreementSMEIP"
)

// Defines values for ScheduleItemStatus.
const (
	END     ScheduleItemStatus = "END"
	OVERDUE ScheduleItemStatus = "OVERDUE"
	PLAN    ScheduleItemStatus = "PLAN"
)

// Defines values for SmePaymentsGetPaymentOrderByTrNumberResponseTransactionStatus.
const (
	SmePaymentsGetPaymentOrderByTrNumberResponseTransactionStatusCOMPLETED   SmePaymentsGetPaymentOrderByTrNumberResponseTransactionStatus = "COMPLETED"
	SmePaymentsGetPaymentOrderByTrNumberResponseTransactionStatusINITIALIZED SmePaymentsGetPaymentOrderByTrNumberResponseTransactionStatus = "INITIALIZED"
	SmePaymentsGetPaymentOrderByTrNumberResponseTransactionStatusINPROGRESS  SmePaymentsGetPaymentOrderByTrNumberResponseTransactionStatus = "IN_PROGRESS"
	SmePaymentsGetPaymentOrderByTrNumberResponseTransactionStatusREJECTED    SmePaymentsGetPaymentOrderByTrNumberResponseTransactionStatus = "REJECTED"
)

// Defines values for TaxPayerType.
const (
	INDIVIDUAL  TaxPayerType = "INDIVIDUAL"
	LEGALENTITY TaxPayerType = "LEGAL_ENTITY"
)

// Defines values for TransactionType.
const (
	OTHER            TransactionType = "OTHER"
	PAYMENTBYACCOUNT TransactionType = "PAYMENT_BY_ACCOUNT"
	PAYMENTMOBILE    TransactionType = "PAYMENT_MOBILE"
	PAYMENTTERMINAL  TransactionType = "PAYMENT_TERMINAL"
)

// Defines values for UserAccountClientType.
const (
	IP UserAccountClientType = "IP"
)

// Defines values for UserAccountCurrency.
const (
	UserAccountCurrencyCNY UserAccountCurrency = "CNY"
	UserAccountCurrencyEUR UserAccountCurrency = "EUR"
	UserAccountCurrencyKZT UserAccountCurrency = "KZT"
	UserAccountCurrencyRUB UserAccountCurrency = "RUB"
	UserAccountCurrencyUSD UserAccountCurrency = "USD"
)

// Defines values for UserAccountStatus.
const (
	ACTIVE   UserAccountStatus = "ACTIVE"
	ARCHIVED UserAccountStatus = "ARCHIVED"
	ARRESTED UserAccountStatus = "ARRESTED"
	BLOCKED  UserAccountStatus = "BLOCKED"
	CLOSED   UserAccountStatus = "CLOSED"
	MISTAKEN UserAccountStatus = "MISTAKEN"
	REOPENED UserAccountStatus = "REOPENED"
)

// Defines values for UserAccountType.
const (
	UserAccountTypeBUCO   UserAccountType = "BUCO"
	UserAccountTypeBUFB   UserAccountType = "BUFB"
	UserAccountTypeCURR   UserAccountType = "CURR"
	UserAccountTypeLOAN   UserAccountType = "LOAN"
	UserAccountTypeOTHERS UserAccountType = "OTHERS"
	UserAccountTypeTU     UserAccountType = "TU"
)

// Defines values for AcceptLanguage.
const (
	AcceptLanguageEn AcceptLanguage = "en"
	AcceptLanguageKk AcceptLanguage = "kk"
	AcceptLanguageRu AcceptLanguage = "ru"
)

// Defines values for PaymentsGetTransactionsOperationType.
const (
	PaymentsGetTransactionsOperationTypeCREDIT PaymentsGetTransactionsOperationType = "CREDIT"
	PaymentsGetTransactionsOperationTypeDEBIT  PaymentsGetTransactionsOperationType = "DEBIT"
)

// Defines values for TaskStatusQueryParam.
const (
	TaskStatusQueryParamCompleted  TaskStatusQueryParam = "completed"
	TaskStatusQueryParamInProgress TaskStatusQueryParam = "in-progress"
	TaskStatusQueryParamPending    TaskStatusQueryParam = "pending"
)

// Defines values for GetBtsDataForAuthParamsAcceptLanguage.
const (
	GetBtsDataForAuthParamsAcceptLanguageEn GetBtsDataForAuthParamsAcceptLanguage = "en"
	GetBtsDataForAuthParamsAcceptLanguageKk GetBtsDataForAuthParamsAcceptLanguage = "kk"
	GetBtsDataForAuthParamsAcceptLanguageRu GetBtsDataForAuthParamsAcceptLanguage = "ru"
)

// Defines values for DocumentForSignParamsAcceptLanguage.
const (
	DocumentForSignParamsAcceptLanguageEn DocumentForSignParamsAcceptLanguage = "en"
	DocumentForSignParamsAcceptLanguageKk DocumentForSignParamsAcceptLanguage = "kk"
	DocumentForSignParamsAcceptLanguageRu DocumentForSignParamsAcceptLanguage = "ru"
)

// Defines values for DictGetLocationsParamsAcceptLanguage.
const (
	DictGetLocationsParamsAcceptLanguageEn DictGetLocationsParamsAcceptLanguage = "en"
	DictGetLocationsParamsAcceptLanguageKk DictGetLocationsParamsAcceptLanguage = "kk"
	DictGetLocationsParamsAcceptLanguageRu DictGetLocationsParamsAcceptLanguage = "ru"
)

// Defines values for ConfirmSignDocumentsBatchParamsAcceptLanguage.
const (
	ConfirmSignDocumentsBatchParamsAcceptLanguageEn ConfirmSignDocumentsBatchParamsAcceptLanguage = "en"
	ConfirmSignDocumentsBatchParamsAcceptLanguageKk ConfirmSignDocumentsBatchParamsAcceptLanguage = "kk"
	ConfirmSignDocumentsBatchParamsAcceptLanguageRu ConfirmSignDocumentsBatchParamsAcceptLanguage = "ru"
)

// Defines values for RequestDocumentPublicParamsAcceptLanguage.
const (
	RequestDocumentPublicParamsAcceptLanguageEn RequestDocumentPublicParamsAcceptLanguage = "en"
	RequestDocumentPublicParamsAcceptLanguageKk RequestDocumentPublicParamsAcceptLanguage = "kk"
	RequestDocumentPublicParamsAcceptLanguageRu RequestDocumentPublicParamsAcceptLanguage = "ru"
)

// Defines values for SignDocumentByIDsParamsAcceptLanguage.
const (
	SignDocumentByIDsParamsAcceptLanguageEn SignDocumentByIDsParamsAcceptLanguage = "en"
	SignDocumentByIDsParamsAcceptLanguageKk SignDocumentByIDsParamsAcceptLanguage = "kk"
	SignDocumentByIDsParamsAcceptLanguageRu SignDocumentByIDsParamsAcceptLanguage = "ru"
)

// Defines values for ConfirmSignDocumentsParamsAcceptLanguage.
const (
	ConfirmSignDocumentsParamsAcceptLanguageEn ConfirmSignDocumentsParamsAcceptLanguage = "en"
	ConfirmSignDocumentsParamsAcceptLanguageKk ConfirmSignDocumentsParamsAcceptLanguage = "kk"
	ConfirmSignDocumentsParamsAcceptLanguageRu ConfirmSignDocumentsParamsAcceptLanguage = "ru"
)

// Defines values for GetDocumentByIDParamsAcceptLanguage.
const (
	GetDocumentByIDParamsAcceptLanguageEn GetDocumentByIDParamsAcceptLanguage = "en"
	GetDocumentByIDParamsAcceptLanguageKk GetDocumentByIDParamsAcceptLanguage = "kk"
	GetDocumentByIDParamsAcceptLanguageRu GetDocumentByIDParamsAcceptLanguage = "ru"
)

// Defines values for SignDocumentByIDParamsAcceptLanguage.
const (
	SignDocumentByIDParamsAcceptLanguageEn SignDocumentByIDParamsAcceptLanguage = "en"
	SignDocumentByIDParamsAcceptLanguageKk SignDocumentByIDParamsAcceptLanguage = "kk"
	SignDocumentByIDParamsAcceptLanguageRu SignDocumentByIDParamsAcceptLanguage = "ru"
)

// Defines values for ConfirmSignDocumentByIDParamsAcceptLanguage.
const (
	ConfirmSignDocumentByIDParamsAcceptLanguageEn ConfirmSignDocumentByIDParamsAcceptLanguage = "en"
	ConfirmSignDocumentByIDParamsAcceptLanguageKk ConfirmSignDocumentByIDParamsAcceptLanguage = "kk"
	ConfirmSignDocumentByIDParamsAcceptLanguageRu ConfirmSignDocumentByIDParamsAcceptLanguage = "ru"
)

// Defines values for DocumentsForLoanAppParamsAcceptLanguage.
const (
	DocumentsForLoanAppParamsAcceptLanguageEn DocumentsForLoanAppParamsAcceptLanguage = "en"
	DocumentsForLoanAppParamsAcceptLanguageKk DocumentsForLoanAppParamsAcceptLanguage = "kk"
	DocumentsForLoanAppParamsAcceptLanguageRu DocumentsForLoanAppParamsAcceptLanguage = "ru"
)

// Defines values for GetLoanSurveyParamsAcceptLanguage.
const (
	GetLoanSurveyParamsAcceptLanguageEn GetLoanSurveyParamsAcceptLanguage = "en"
	GetLoanSurveyParamsAcceptLanguageKk GetLoanSurveyParamsAcceptLanguage = "kk"
	GetLoanSurveyParamsAcceptLanguageRu GetLoanSurveyParamsAcceptLanguage = "ru"
)

// Defines values for SaveLoanSurveyParamsAcceptLanguage.
const (
	SaveLoanSurveyParamsAcceptLanguageEn SaveLoanSurveyParamsAcceptLanguage = "en"
	SaveLoanSurveyParamsAcceptLanguageKk SaveLoanSurveyParamsAcceptLanguage = "kk"
	SaveLoanSurveyParamsAcceptLanguageRu SaveLoanSurveyParamsAcceptLanguage = "ru"
)

// Defines values for GetBtsDataForLoanAppParamsAcceptLanguage.
const (
	GetBtsDataForLoanAppParamsAcceptLanguageEn GetBtsDataForLoanAppParamsAcceptLanguage = "en"
	GetBtsDataForLoanAppParamsAcceptLanguageKk GetBtsDataForLoanAppParamsAcceptLanguage = "kk"
	GetBtsDataForLoanAppParamsAcceptLanguageRu GetBtsDataForLoanAppParamsAcceptLanguage = "ru"
)

// Defines values for GetLoansDetailsParamsAcceptLanguage.
const (
	GetLoansDetailsParamsAcceptLanguageEn GetLoansDetailsParamsAcceptLanguage = "en"
	GetLoansDetailsParamsAcceptLanguageKk GetLoansDetailsParamsAcceptLanguage = "kk"
	GetLoansDetailsParamsAcceptLanguageRu GetLoansDetailsParamsAcceptLanguage = "ru"
)

// Defines values for LoansPostEdsBtsDataParamsAcceptLanguage.
const (
	LoansPostEdsBtsDataParamsAcceptLanguageEn LoansPostEdsBtsDataParamsAcceptLanguage = "en"
	LoansPostEdsBtsDataParamsAcceptLanguageKk LoansPostEdsBtsDataParamsAcceptLanguage = "kk"
	LoansPostEdsBtsDataParamsAcceptLanguageRu LoansPostEdsBtsDataParamsAcceptLanguage = "ru"
)

// Defines values for LoansPostIdentifyBtsDataSmeParamsAcceptLanguage.
const (
	LoansPostIdentifyBtsDataSmeParamsAcceptLanguageEn LoansPostIdentifyBtsDataSmeParamsAcceptLanguage = "en"
	LoansPostIdentifyBtsDataSmeParamsAcceptLanguageKk LoansPostIdentifyBtsDataSmeParamsAcceptLanguage = "kk"
	LoansPostIdentifyBtsDataSmeParamsAcceptLanguageRu LoansPostIdentifyBtsDataSmeParamsAcceptLanguage = "ru"
)

// Defines values for GetScoringResultParamsAcceptLanguage.
const (
	GetScoringResultParamsAcceptLanguageEn GetScoringResultParamsAcceptLanguage = "en"
	GetScoringResultParamsAcceptLanguageKk GetScoringResultParamsAcceptLanguage = "kk"
	GetScoringResultParamsAcceptLanguageRu GetScoringResultParamsAcceptLanguage = "ru"
)

// Defines values for LoansConfirmSignDocumentByIDParamsAcceptLanguage.
const (
	LoansConfirmSignDocumentByIDParamsAcceptLanguageEn LoansConfirmSignDocumentByIDParamsAcceptLanguage = "en"
	LoansConfirmSignDocumentByIDParamsAcceptLanguageKk LoansConfirmSignDocumentByIDParamsAcceptLanguage = "kk"
	LoansConfirmSignDocumentByIDParamsAcceptLanguageRu LoansConfirmSignDocumentByIDParamsAcceptLanguage = "ru"
)

// Defines values for GetTransactionsParamsDirection.
const (
	CREDIT GetTransactionsParamsDirection = "CREDIT"
	DEBIT  GetTransactionsParamsDirection = "DEBIT"
)

// Defines values for GetTasksParamsStatus.
const (
	GetTasksParamsStatusCompleted  GetTasksParamsStatus = "completed"
	GetTasksParamsStatusInProgress GetTasksParamsStatus = "in-progress"
	GetTasksParamsStatusPending    GetTasksParamsStatus = "pending"
)

// Defines values for GetLoansParamsAcceptLanguage.
const (
	GetLoansParamsAcceptLanguageEn GetLoansParamsAcceptLanguage = "en"
	GetLoansParamsAcceptLanguageKk GetLoansParamsAcceptLanguage = "kk"
	GetLoansParamsAcceptLanguageRu GetLoansParamsAcceptLanguage = "ru"
)

// Defines values for UsersUpdateUserLocaleParamsAcceptLanguage.
const (
	UsersUpdateUserLocaleParamsAcceptLanguageEn UsersUpdateUserLocaleParamsAcceptLanguage = "en"
	UsersUpdateUserLocaleParamsAcceptLanguageKk UsersUpdateUserLocaleParamsAcceptLanguage = "kk"
	UsersUpdateUserLocaleParamsAcceptLanguageRu UsersUpdateUserLocaleParamsAcceptLanguage = "ru"
)

// APIError Общий формат ошибки API. Возвращает цифровой код
type APIError struct {
	Error string `json:"error"`

	// Fields Объект с описанием деталей ошибок
	Fields *map[string]string `json:"fields,omitempty"`
}

// Account Информация по счету
type Account struct {
	// Amount Баланс счета
	Amount Money `json:"amount"`

	// BankBic БИК банка
	BankBic string `json:"bankBic"`

	// BankBin БИН банка
	BankBin string `json:"bankBin"`

	// BankName Наименование банка
	BankName string `json:"bankName"`

	// FullName ФИО клиента
	FullName string `json:"fullName"`

	// Iban Счет для погашения (IBAN)
	Iban string `json:"iban"`

	// IbanLastDigits Замаскированный номер счета списания (отображать только 4 последние символа)
	IbanLastDigits string `json:"ibanLastDigits"`

	// Iin ИИН клиента
	Iin string `json:"iin"`
}

// AccountDocumentResponse defines model for AccountDocumentResponse.
type AccountDocumentResponse struct {
	Document *Document `json:"document,omitempty"`
}

// AccountDocumentType Тип (шаблон) документа для счета SME
type AccountDocumentType string

// AdditionalIndividualType Дополнительный тип для ФЛ если есть
type AdditionalIndividualType struct {
	// Name Наименование дополнительного бенефициара для данного доп.  типа ФЛ
	Name string `json:"name"`

	// Type Список кодов 32 - ИП, 64 — адвокат, 128 — частный нотариус, 256 — частный судебный исполнитель
	Type int `json:"type"`
}

// AttachedDocData defines model for AttachedDocData.
type AttachedDocData struct {
	// BankID Идентификатор банка, согласно справочнику
	BankID string `json:"bankID"`

	// DocID Идентификатор документа из временного хранилища
	DocID string `json:"docID"`
}

// AuthConfirmResponse defines model for AuthConfirmResponse.
type AuthConfirmResponse struct {
	NextStep *NextStep  `json:"nextStep,omitempty"`
	Tokens   AuthTokens `json:"tokens"`

	// UserID Идентификатор пользователя
	UserID string `json:"userID"`
}

// AuthRefreshResponse defines model for AuthRefreshResponse.
type AuthRefreshResponse struct {
	IPName     string     `json:"IPName"`
	FirstName  string     `json:"firstName"`
	LastName   string     `json:"lastName"`
	MiddleName *string    `json:"middleName,omitempty"`
	NextStep   NextStep   `json:"nextStep"`
	Tokens     AuthTokens `json:"tokens"`
}

// AuthTokens defines model for AuthTokens.
type AuthTokens struct {
	// Access Токен авторизации
	Access string `json:"access"`

	// Refresh Токен обновления авторизации
	Refresh string `json:"refresh"`
}

// Bank defines model for Bank.
type Bank struct {
	// Bic БИК банка
	Bic *string `json:"bic,omitempty"`

	// Code Код банка
	Code *string `json:"code,omitempty"`

	// Name Наименование банка на языке локализации
	Name *string `json:"name,omitempty"`
}

// BankItem defines model for BankItem.
type BankItem struct {
	Bank *Bank `json:"bank,omitempty"`
}

// BankStatements defines model for BankStatements.
type BankStatements struct {
	// Banks Банки для загрузки выписок
	Banks []ExternalBank `json:"banks"`

	// Formats Разрешенные форматы для выписок
	Formats string `json:"formats"`

	// MaxFileSize Максимальное кол-во мегабайт для выписок
	MaxFileSize float32 `json:"maxFileSize"`

	// Months Кол-во месяцев, за которые нужна выписка
	Months float32 `json:"months"`
}

// BidXMLLine defines model for BidXMLLine.
type BidXMLLine struct {
	Supplier string `json:"supplier"`
	Volume   string `json:"volume"`
}

// BidXMLRequestInput defines model for BidXMLRequestInput.
type BidXMLRequestInput struct {
	EcertNo         string `json:"ecertNo"`
	MemberShortName string `json:"memberShortName"`
}

// BodyItem defines model for BodyItem.
type BodyItem struct {
	SerialNumber  int    `json:"serialNumber"`
	StatusCode    int    `json:"statusCode"`
	StatusMessage string `json:"statusMessage"`
}

// BtsDataForAuthResp defines model for BtsDataForAuthResp.
type BtsDataForAuthResp struct {
	// Link Ссылка для прохождения идентификации
	Link string `json:"link"`

	// RedirectURI Ссылка для редиректа
	RedirectURI string `json:"redirectURI"`
}

// BtsDataResp defines model for BtsDataResp.
type BtsDataResp struct {
	// Link Ссылка для прохождения идентификации
	Link string `json:"link"`

	// RedirectURI Ссылка для редиректа
	RedirectURI string `json:"redirectURI"`
}

// CalculationResultResponse defines model for CalculationResultResponse.
type CalculationResultResponse struct {
	// CalculationResult Массив результатов расчетов платежей
	CalculationResult []CalculationTermData `json:"calculationResult"`
}

// CalculationTermData defines model for CalculationTermData.
type CalculationTermData struct {
	// MonthlyPayment Предварительный ежемесячный платеж
	MonthlyPayment int `json:"monthlyPayment"`

	// OverpayAmount Сумма наценки
	OverpayAmount int `json:"overpayAmount"`

	// TermInterest Холдер условий кредита
	TermInterest LoanTermInterestData `json:"termInterest"`

	// TotalAmount Общая сумма (сумма кредита + сумма наценки)
	TotalAmount int `json:"totalAmount"`
}

// CancelLoanApplicationResp defines model for CancelLoanApplicationResp.
type CancelLoanApplicationResp struct {
	// ApplicationID Уникальный идентификатор заявки на кредит
	ApplicationID string `json:"applicationID"`
}

// ClientResponse defines model for ClientResponse.
type ClientResponse struct {
	// Birthdate Дата рождения клиента
	Birthdate string `json:"birthdate"`

	// EnterpriseAddressKATOCode Код КАТО адреса регистрации ИП
	EnterpriseAddressKATOCode *string `json:"enterpriseAddressKATOCode,omitempty"`

	// EnterpriseAddressKATOId Номер КАТО адреса регистрации ИП
	EnterpriseAddressKATOId *string `json:"enterpriseAddressKATOId,omitempty"`

	// EnterpriseName Название ИП клиента
	EnterpriseName *string `json:"enterpriseName,omitempty"`

	// Iin ИИН клиента
	Iin string `json:"iin"`

	// Name Имя клиента
	Name string `json:"name"`

	// Patronymic Отчество клиента
	Patronymic *string `json:"patronymic,omitempty"`

	// Surname Фамилия клиента
	Surname string `json:"surname"`
}

// ClientVerificationResponse defines model for ClientVerificationResponse.
type ClientVerificationResponse struct {
	// RejectionReason Причина отказа (если результат `Rejected`):
	//   * `IpNotFound`
	//   * `InactiveTaxpayer`
	//   * `TaxDebtPresent`
	//   * `AmlRejection`
	//   * `null` – если причина отсутствует
	RejectionReason *string `json:"rejectionReason"`

	// Status Статус прохождения проверки:
	//   * `IN_PROGRESS` – проверка в процессе
	//   * `DONE` – проверка завершена
	//   * `ERROR` – ошибка (какой-то из сервисов недоступен или ответил с ошибкой)
	Status ClientVerificationResponseStatus `json:"status"`

	// VerificationResult Результат проверки (Доступность открытия счёта):
	//   * `APPROVED` – проверки по клиенту прошли успешно
	//   * `REJECTED` – проверки не пройдены
	//   * `PENDING` – проверка в статусе InProgress или Error
	VerificationResult ClientVerificationResponseVerificationResult `json:"verificationResult"`
}

// ClientVerificationResponseStatus Статус прохождения проверки:
//   - `IN_PROGRESS` – проверка в процессе
//   - `DONE` – проверка завершена
//   - `ERROR` – ошибка (какой-то из сервисов недоступен или ответил с ошибкой)
type ClientVerificationResponseStatus string

// ClientVerificationResponseVerificationResult Результат проверки (Доступность открытия счёта):
//   - `APPROVED` – проверки по клиенту прошли успешно
//   - `REJECTED` – проверки не пройдены
//   - `PENDING` – проверка в статусе InProgress или Error
type ClientVerificationResponseVerificationResult string

// ConfirmPaymentByAccountResponse defines model for ConfirmPaymentByAccountResponse.
type ConfirmPaymentByAccountResponse struct {
	// Message Информация из бэк по статусу
	Message string `json:"message"`

	// Status Статус операции
	Status string `json:"status"`
}

// ConfirmPaymentSmeResponse defines model for ConfirmPaymentSmeResponse.
type ConfirmPaymentSmeResponse struct {
	// OtpNeeded Необходимость подтвердить платёж OTP
	OtpNeeded *bool `json:"otpNeeded,omitempty"`

	// Reason Описание причины состояния платежа
	Reason *string `json:"reason,omitempty"`

	// ReasonCode Код состояния платежа
	ReasonCode *string `json:"reasonCode,omitempty"`

	// Status Статус платежа
	Status string `json:"status"`
}

// ConfirmSignDocumentsResponse defines model for ConfirmSignDocumentsResponse.
type ConfirmSignDocumentsResponse struct {
	// Documents Подписанные документы
	Documents []Document `json:"documents"`
}

// ConversionSumRequestBody Данный объект содержит данные для расчёта сумм конвертации.
type ConversionSumRequestBody struct {
	// FromCurrency Код валюты (например, KZT)
	FromCurrency string `json:"fromCurrency"`

	// ToAmount Сумма зачисления
	ToAmount float32 `json:"toAmount"`

	// ToCurrency Код валюты (например, CHY, RUB)
	ToCurrency string `json:"toCurrency"`
}

// ConversionSumResponse Данный объект содержит результаты расчёта сумм конвертации.
type ConversionSumResponse struct {
	// FromAmount Сумма списания
	FromAmount *float32 `json:"fromAmount,omitempty"`

	// FromCurrency Валюта суммы списания (например, KZT)
	FromCurrency *string `json:"fromCurrency,omitempty"`

	// Rate Курс валют, по которому проводился расчёт
	Rate *string `json:"rate,omitempty"`

	// ToAmount Сумма зачисления
	ToAmount *float32 `json:"toAmount,omitempty"`

	// ToCurrency Валюта суммы зачисления (например, CHY, RUB)
	ToCurrency *string `json:"toCurrency,omitempty"`
}

// CreateConvertationRequest defines model for CreateConvertationRequest.
type CreateConvertationRequest struct {
	// ConversionType Тип конвертации
	ConversionType CreateConvertationRequestConversionType `json:"conversionType"`

	// FromAccount Номер счета списания
	FromAccount string `json:"fromAccount"`

	// FromAmount Сумма списания
	FromAmount float32 `json:"fromAmount"`

	// FromCurrency Валюта суммы списания
	FromCurrency string `json:"fromCurrency"`

	// Goal Цель покупки валюты
	Goal *string `json:"goal,omitempty"`

	// IdempotencyID Ключ идемпотентности
	IdempotencyID string `json:"idempotencyID"`

	// Rate Курс валют, по которому проводился расчёт
	Rate string `json:"rate"`

	// ToAccount Номер счета зачисления
	ToAccount string `json:"toAccount"`

	// ToAmount Сумма зачисления
	ToAmount float32 `json:"toAmount"`

	// ToCurrency Валюта суммы зачисления
	ToCurrency string `json:"toCurrency"`
}

// CreateConvertationRequestConversionType Тип конвертации
type CreateConvertationRequestConversionType string

// CreateConvertationResponse defines model for CreateConvertationResponse.
type CreateConvertationResponse struct {
	Reason *RejectionReason `json:"reason,omitempty"`

	// Status Статус операции
	Status CreateConvertationResponseStatus `json:"status"`

	// TransactionID ID транзакции
	TransactionID string `json:"transactionID"`
}

// CreateConvertationResponseStatus Статус операции
type CreateConvertationResponseStatus string

// CreateOrderResp defines model for CreateOrderResp.
type CreateOrderResp struct {
	Body   []BodyItem `json:"body"`
	Header HeaderResp `json:"header"`
}

// CreatePaymentByAccountResponse defines model for CreatePaymentByAccountResponse.
type CreatePaymentByAccountResponse struct {
	// Message Информация из бэк по статусу
	Message string `json:"message"`

	// OtpRequired Признак нужно ли проводить проверки по отп
	OtpRequired bool             `json:"otpRequired"`
	OtpResponse *OtpFullResponse `json:"otpResponse,omitempty"`

	// Status Статус операции
	Status string `json:"status"`

	// TransactionID Идентификатор транзакции в БД
	TransactionID openapi_types.UUID `json:"transactionID"`
}

// CreatePaymentResponse defines model for CreatePaymentResponse.
type CreatePaymentResponse struct {
	// OtpNeeded Необходимость подтвердить платёж OTP
	OtpNeeded bool `json:"otpNeeded"`

	// Reason Описание причины состояния платежа
	Reason *CreatePaymentResponseReason `json:"reason,omitempty"`

	// ReasonCode Код состояния платежа
	ReasonCode *CreatePaymentResponseReasonCode `json:"reasonCode,omitempty"`

	// Status Статус создания платежа
	Status CreatePaymentResponseStatus `json:"status"`

	// TransactionID Идентификатор созданной транзакции (опциональное поле - может отсутствовать при ошибках валидации)
	TransactionID *openapi_types.UUID `json:"transactionID,omitempty"`
}

// CreatePaymentResponseReason Описание причины состояния платежа
type CreatePaymentResponseReason string

// CreatePaymentResponseReasonCode Код состояния платежа
type CreatePaymentResponseReasonCode string

// CreatePaymentResponseStatus Статус создания платежа
type CreatePaymentResponseStatus string

// Currency defines model for Currency.
type Currency struct {
	// Currency Код валюты
	Currency CurrencyCode `json:"currency"`
}

// CurrencyAccount defines model for CurrencyAccount.
type CurrencyAccount struct {
	// AccountExists Наличие счета ИП в данной валюте
	AccountExists bool `json:"accountExists"`

	// Currency Код валюты
	Currency CurrencyCode `json:"currency"`
}

// CurrencyCheckResponse defines model for CurrencyCheckResponse.
type CurrencyCheckResponse struct {
	// Currencies Массив объектов с доступными валютами для открытия нового счета
	Currencies []CurrencyAccount `json:"currencies"`

	// IsAccountOpeningAvailable Доступность открытия счета
	IsAccountOpeningAvailable bool `json:"isAccountOpeningAvailable"`
}

// CurrencyCode Код валюты
type CurrencyCode string

// DeviceInfo defines model for DeviceInfo.
type DeviceInfo struct {
	// AppVersion Версия приложения
	AppVersion string `json:"appVersion"`

	// DeviceModel Модель устройства
	DeviceModel string `json:"deviceModel"`

	// InstallationID Идентификатор установки приложения
	InstallationID openapi_types.UUID `json:"installationID"`

	// SystemType Тип операционной системы
	SystemType DeviceInfoSystemType `json:"systemType"`

	// SystemVersion Версия операционной системы
	SystemVersion string `json:"systemVersion"`
}

// DeviceInfoSystemType Тип операционной системы
type DeviceInfoSystemType string

// DictDocument defines model for DictDocument.
type DictDocument struct {
	// Data Данные документа справочника в виде объекта JSON (зависит от структуры справочника)
	Data map[string]interface{} `json:"data"`

	// DictId Идентификатор справочника к которому относится документ
	DictId openapi_types.UUID `json:"dict_id"`

	// Id Идентификатор документа справочника
	Id *openapi_types.UUID `json:"id,omitempty"`

	// Name Имя документа справочника (не обязательно)
	Name string `json:"name"`

	// OrderNum Поле для настройки ручной сортировки при необходимости
	OrderNum int `json:"order_num"`

	// Valid Соответствует-ли документ настройкам схемы для данного словаря
	Valid *bool `json:"valid,omitempty"`
}

// DictDocumentFilters defines model for DictDocumentFilters.
type DictDocumentFilters struct {
	// DictId ID или имя словаря
	DictId     string   `json:"dict_id"`
	Filters    []Filter `json:"filters"`
	Pagination *struct {
		// Count Размер страницы
		Count int32 `json:"count"`

		// Page Номер страницы начиная с 0
		Page int32 `json:"page"`
	} `json:"pagination,omitempty"`
	Sort *[]string `json:"sort,omitempty"`
}

// DictDocumentList defines model for DictDocumentList.
type DictDocumentList struct {
	// List Список документов справочника
	List []DictDocument `json:"list"`
}

// DisbursementMode Режим работы биржи
type DisbursementMode string

// Document defines model for Document.
type Document struct {
	// ID Идентификатор документа
	ID openapi_types.UUID `json:"ID"`

	// FileLink Ссылка на физический файл
	FileLink string `json:"fileLink"`

	// Signed Был ли документ подписан пользователем
	Signed bool `json:"signed"`

	// Title Название документа
	Title string `json:"title"`

	// Type Тип (шаблон) документа
	Type DocumentType `json:"type"`

	// Version Версия документа, которая соотносится с типом документа (версия шаблона)
	Version int `json:"version"`
}

// DocumentForAccountOpening defines model for DocumentForAccountOpening.
type DocumentForAccountOpening struct {
	// ID Идентификатор документа
	ID openapi_types.UUID `json:"ID"`

	// FileLink Ссылка на физический файл
	FileLink string `json:"fileLink"`

	// Signed Был ли документ подписан пользователем
	Signed bool `json:"signed"`

	// Title Название документа
	Title string                        `json:"title"`
	Type  DocumentForAccountOpeningType `json:"type"`

	// Version Версия документа, которая соотносится с типом документа (версия шаблона)
	Version int `json:"version"`
}

// DocumentForAccountOpeningType defines model for DocumentForAccountOpening.Type.
type DocumentForAccountOpeningType string

// DocumentType Тип (шаблон) документа
type DocumentType string

// DocumentsForSignForAccountOpeningResponse defines model for DocumentsForSignForAccountOpeningResponse.
type DocumentsForSignForAccountOpeningResponse struct {
	// Documents Список документов
	Documents []DocumentForAccountOpening `json:"documents"`
}

// DocumentsForSignResponse defines model for DocumentsForSignResponse.
type DocumentsForSignResponse struct {
	// Documents Документы на подпись
	Documents []Document `json:"documents"`
}

// EarlyRepayAmount Сумма ЧДП/ПДП
type EarlyRepayAmount struct {
	// CurrencyCode Валюта
	CurrencyCode string `json:"currencyCode"`

	// Value Сумма досрочного погашения
	Value string `json:"value"`
}

// EarlyRepayContract Оплата финансирования
type EarlyRepayContract struct {
	// Number Номер кредитного договора
	Number string `json:"number"`

	// Title Заголовок для документа
	Title string `json:"title"`
}

// EducationType defines model for EducationType.
type EducationType struct {
	// ID Идентификатор уровня образования
	ID string `json:"ID"`

	// Code Код уровня образования
	Code string `json:"code"`

	// Name Название уровня образования
	Name string `json:"name"`
}

// Email Электронная почта
type Email = string

// EmployeeInfo Информация о сотруднике ИП
type EmployeeInfo struct {
	// Birthday Дата рождения сотрудника
	Birthday openapi_types.Date `json:"birthday"`

	// Country Страна
	Country string `json:"country"`

	// DisplayOrder Порядок отображения в списке
	DisplayOrder int `json:"displayOrder"`

	// EmployerIinBin ИИН/БИН организации
	EmployerIinBin *string `json:"employerIinBin,omitempty"`

	// Id Идентификатор записи о сотруднике
	Id string `json:"id"`

	// Iin ИИН сотрудника
	Iin string `json:"iin"`

	// LastName Фамилия сотрудника
	LastName string `json:"lastName"`

	// MiddleName Отчество сотрудника
	MiddleName *string `json:"middleName,omitempty"`

	// Name Имя сотрудника
	Name string `json:"name"`
}

// EmployeeItem Информация о сотруднике для платежа ОПВ
type EmployeeItem struct {
	// Amount Сумма взноса для данного сотрудника
	Amount string `json:"amount"`

	// Birthday Дата рождения сотрудника
	Birthday openapi_types.Date `json:"birthday"`

	// Country Страна
	Country string `json:"country"`

	// Iin ИИН сотрудника
	Iin string `json:"iin"`

	// LastName Фамилия сотрудника
	LastName string `json:"lastName"`

	// MiddleName Отчество сотрудника
	MiddleName *string `json:"middleName,omitempty"`

	// Name Имя сотрудника
	Name string `json:"name"`

	// ValuePeriod Период взноса (YYYYMM)
	ValuePeriod string `json:"valuePeriod"`
}

// EmploymentType defines model for EmploymentType.
type EmploymentType struct {
	// ID Идентификатор типа занятости
	ID string `json:"ID"`

	// Code Код типа занятости
	Code string `json:"code"`

	// Name Название типа занятости
	Name string `json:"name"`
}

// ErrorReason defines model for ErrorReason.
type ErrorReason struct {
	// Code Код ошибки
	Code string `json:"code"`

	// Message Описание ошибки
	Message *string `json:"message,omitempty"`

	// Title Заголовок ошибки
	Title string `json:"title"`
}

// ExternalBank defines model for ExternalBank.
type ExternalBank struct {
	// ID Уникальный идентификатор
	ID string `json:"ID"`

	// Instructions Инструкция для выгрузки выписки из банка в виде списка шагов, т.к. так проще отображать на UI
	Instructions string `json:"instructions"`

	// LogoUrl Логотип банка (ссылка на изображение)
	LogoUrl string `json:"logoUrl"`

	// Name Наименование банка для отображения на UI клиенту
	Name string `json:"name"`
}

// ExternalBankLoan defines model for ExternalBankLoan.
type ExternalBankLoan struct {
	ID string `json:"ID"`

	// BankBIN БИН банка
	BankBIN string `json:"bankBIN"`

	// BankName Наименование банка
	BankName string `json:"bankName"`

	// ContractDate Дата договора
	ContractDate time.Time `json:"contractDate"`

	// ContractNumber Номер договора
	ContractNumber string `json:"contractNumber"`

	// Image Ссылка на изображение
	Image string `json:"image"`

	// OutstandingAmount Сумма задолженности по одному банку
	OutstandingAmount Money `json:"outstandingAmount"`

	// PaymentAmount Ежемесячный платеж по одному банку
	PaymentAmount Money `json:"paymentAmount"`
}

// FileUploadRequestBody defines model for FileUploadRequestBody.
type FileUploadRequestBody struct {
	// File Контент документ для загрузки
	File openapi_types.File `json:"file"`

	// Filename Имя файла вместе с расширением
	Filename string `json:"filename"`
}

// FileUploadResponse defines model for FileUploadResponse.
type FileUploadResponse struct {
	// ID ID загруженного файла
	ID string `json:"ID"`
}

// Filter defines model for Filter.
type Filter struct {
	// Field Поле данных
	Field *string   `json:"field,omitempty"`
	Group *[]Filter `json:"group,omitempty"`

	// Not Флаг использования обратного условия фильтра
	Not *bool `json:"not,omitempty"`

	// Operation Операция
	Operation string `json:"operation"`

	// Value Значение
	Value *string `json:"value,omitempty"`
}

// GetApprovedLoanAppStatusResp defines model for GetApprovedLoanAppStatusResp.
type GetApprovedLoanAppStatusResp struct {
	// ApplicationStatus Статус заявки на кредит.
	ApplicationStatus string `json:"applicationStatus"`

	// IsInProgress Признак наличия в процессе выполнения внутренних проверок
	IsInProgress bool `json:"isInProgress"`

	// NextStep Следующий шаг.
	NextStep *string      `json:"nextStep,omitempty"`
	Reason   *ErrorReason `json:"reason,omitempty"`
}

// GetBankListResp defines model for GetBankListResp.
type GetBankListResp struct {
	// Banks Список банков
	Banks *[]BankItem `json:"banks,omitempty"`
}

// GetBankStatementV2Bank defines model for GetBankStatementV2Bank.
type GetBankStatementV2Bank struct {
	// ID БИК банка
	ID string `json:"ID"`

	// Instructions Инструкция для выгрузки выписки из банка
	Instructions *string `json:"instructions,omitempty"`

	// IsStatementValid Флаг корректного файла выписки
	IsStatementValid *bool `json:"isStatementValid,omitempty"`

	// LogoUrl Логотип банка (ссылка на изображение)
	LogoUrl string `json:"logoUrl"`

	// Name Наименование банка
	Name string `json:"name"`

	// StatementName Наименование файла выписки (возвращается только при наличии некорректного файла)
	StatementName *string `json:"statementName,omitempty"`
}

// GetBankStatementV2Instruction Инструкция
type GetBankStatementV2Instruction struct {
	// Description Описание инструкции
	Description string `json:"description"`

	// Title Заголовок инструкции
	Title string `json:"title"`
}

// GetBankStatementV2Response defines model for GetBankStatementV2Response.
type GetBankStatementV2Response struct {
	// Banks Информация по банкам (Справочник банков для загрузки выписки)
	Banks []GetBankStatementV2Bank `json:"banks"`

	// MaxFileSizeMb Максимальное количество мегабайт для выписки (из конфига, напр. "10")
	MaxFileSizeMb float32 `json:"maxFileSizeMb"`

	// PeriodMonths Период (количество месяцев), за который нужна выписка (из конфига, напр. "6")
	PeriodMonths float32 `json:"periodMonths"`

	// StatementHint Баннер с подсказкой для клиента о преимуществе выписки
	StatementHint GetBankStatementV2StatementHint `json:"statementHint"`
}

// GetBankStatementV2StatementHint Баннер с подсказкой для клиента о преимуществе выписки
type GetBankStatementV2StatementHint struct {
	// Description Описание
	Description string `json:"description"`

	// Instruction Инструкция
	Instruction GetBankStatementV2Instruction `json:"instruction"`

	// Title Заголовок
	Title string `json:"title"`
}

// GetEducationTypesResp defines model for GetEducationTypesResp.
type GetEducationTypesResp struct {
	// EducationTypes Список справочников по типам образования
	EducationTypes []EducationType `json:"educationTypes"`
}

// GetEmploymentTypesResp defines model for GetEmploymentTypesResp.
type GetEmploymentTypesResp struct {
	// EmploymentTypes Список справочников по типам занятости
	EmploymentTypes []EmploymentType `json:"employmentTypes"`
}

// GetInternalChecksResultResp defines model for GetInternalChecksResultResp.
type GetInternalChecksResultResp struct {
	// ApplicationStatus Статус заявки на кредит.
	ApplicationStatus string `json:"applicationStatus"`

	// IsInProgress Признак наличия в процессе выполнения внутренних проверок
	IsInProgress bool         `json:"isInProgress"`
	Reason       *ErrorReason `json:"reason,omitempty"`
}

// GetKbeKodListResp defines model for GetKbeKodListResp.
type GetKbeKodListResp struct {
	// Codes Список Кбе/КОд кодов
	Codes *[]KbeKodItem `json:"codes,omitempty"`
}

// GetKbkListResp defines model for GetKbkListResp.
type GetKbkListResp struct {
	// Codes Список КБК
	Codes *[]KbkItem `json:"codes,omitempty"`
}

// GetKnpListResp defines model for GetKnpListResp.
type GetKnpListResp struct {
	// Codes Список КНП
	Codes *[]KnpItem `json:"codes,omitempty"`
}

// GetLoansDetailsEarlyRepayment defines model for GetLoansDetailsEarlyRepayment.
type GetLoansDetailsEarlyRepayment struct {
	AllDebtToPay     *GetLoansDetailsEarlyRepaymentAllDebtToPay     `json:"allDebtToPay,omitempty"`
	IsAvailable      bool                                           `json:"isAvailable"`
	PartialRepayment *GetLoansDetailsEarlyRepaymentPartialRepayment `json:"partialRepayment,omitempty"`
	Reason           *ErrorReason                                   `json:"reason,omitempty"`
}

// GetLoansDetailsEarlyRepaymentAllDebtToPay defines model for GetLoansDetailsEarlyRepaymentAllDebtToPay.
type GetLoansDetailsEarlyRepaymentAllDebtToPay struct {
	// Amount Сумма
	Amount Money  `json:"amount"`
	Label  string `json:"label"`
}

// GetLoansDetailsEarlyRepaymentPartialRepayment defines model for GetLoansDetailsEarlyRepaymentPartialRepayment.
type GetLoansDetailsEarlyRepaymentPartialRepayment struct {
	Limit       GetLoansDetailsEarlyRepaymentPartialRepaymentLimit       `json:"limit"`
	NextPayment GetLoansDetailsEarlyRepaymentPartialRepaymentNextPayment `json:"nextPayment"`
}

// GetLoansDetailsEarlyRepaymentPartialRepaymentLimit defines model for GetLoansDetailsEarlyRepaymentPartialRepaymentLimit.
type GetLoansDetailsEarlyRepaymentPartialRepaymentLimit struct {
	Hint string `json:"hint"`

	// MaxAmount Сумма
	MaxAmount Money `json:"maxAmount"`

	// MinAmount Сумма
	MinAmount Money `json:"minAmount"`
}

// GetLoansDetailsEarlyRepaymentPartialRepaymentNextPayment defines model for GetLoansDetailsEarlyRepaymentPartialRepaymentNextPayment.
type GetLoansDetailsEarlyRepaymentPartialRepaymentNextPayment struct {
	Hint string `json:"hint"`
}

// GetLoansDetailsResponse defines model for GetLoansDetailsResponse.
type GetLoansDetailsResponse struct {
	EarlyRepayment GetLoansDetailsEarlyRepayment `json:"earlyRepayment"`

	// Links Блок с URL
	Links Links `json:"links"`

	// Loan Информация по кредиту
	Loan LoanDetails `json:"loan"`

	// PaymentHint Подсказка в карточке кредита
	PaymentHint PaymentHintData `json:"paymentHint"`
}

// GetLocationsResp defines model for GetLocationsResp.
type GetLocationsResp struct {
	// Locations Список локаций
	Locations []Location `json:"locations"`
}

// GetOrderResp defines model for GetOrderResp.
type GetOrderResp struct {
	Body   []OrderResultBodyResp `json:"body"`
	Header HeaderResp            `json:"header"`
	Status StatusResp            `json:"status"`
}

// GetOtcResp defines model for GetOtcResp.
type GetOtcResp struct {
	BUYER              string        `json:"BUYER"`
	CURRENCY           string        `json:"CURRENCY"`
	ECERTNO            string        `json:"ECERTNO"`
	LINE               []OtcLineItem `json:"LINE"`
	MURABAHAVALUE      string        `json:"MURABAHAVALUE"`
	PNAME              string        `json:"PNAME"`
	PRICE              string        `json:"PRICE"`
	PRICEMYREQUIVALENT string        `json:"PRICE_MYR_EQUIVALENT"`
	PVOLUME            string        `json:"PVOLUME"`
	REPORTINGTIMEDATE  string        `json:"REPORTINGTIMEDATE"`
	SELLER             string        `json:"SELLER"`
	TOTALVALUE         string        `json:"TOTALVALUE"`
	VALUEDATE          string        `json:"VALUEDATE"`
}

// GetRelationTypesResp defines model for GetRelationTypesResp.
type GetRelationTypesResp struct {
	// RelationTypes Список справочников по видам отношений к контактным лицам
	RelationTypes []RelationType `json:"relationTypes"`
}

// GetScoringResultRespExternalBankLoansInfo defines model for GetScoringResultRespExternalBankLoansInfo.
type GetScoringResultRespExternalBankLoansInfo struct {
	// Items Блок с информацией по каждому займу
	Items []ExternalBankLoan `json:"items"`
	Title string             `json:"title"`

	// TotalPaymentAmount Общая сумма ежемесячного платежа при рефинансировании
	TotalPaymentAmount Money `json:"totalPaymentAmount"`
}

// GetScoringResultRespRefinancingInfo defines model for GetScoringResultRespRefinancingInfo.
type GetScoringResultRespRefinancingInfo struct {
	Conditions            *RefinancingConditions                     `json:"conditions,omitempty"`
	ExternalBankLoansInfo *GetScoringResultRespExternalBankLoansInfo `json:"externalBankLoansInfo,omitempty"`
}

// GetSurveyAddress defines model for GetSurveyAddress.
type GetSurveyAddress struct {
	// Building Номер или название здания
	Building *string   `json:"building,omitempty"`
	District *KatoData `json:"district,omitempty"`

	// Flat Номер квартиры или офиса
	Flat *string `json:"flat"`

	// IsFull Флаг полностью заполненного адреса
	IsFull         bool      `json:"isFull"`
	Locality       *KatoData `json:"locality,omitempty"`
	Region         *KatoData `json:"region,omitempty"`
	Settlement     *KatoData `json:"settlement,omitempty"`
	SettlementArea *KatoData `json:"settlementArea,omitempty"`

	// Street Улица адреса
	Street *string `json:"street,omitempty"`
}

// GetSurveyContactPerson defines model for GetSurveyContactPerson.
type GetSurveyContactPerson struct {
	// FirstName Имя контактного лица
	FirstName string `json:"firstName"`

	// LastName Фамилия контактного лица
	LastName string `json:"lastName"`

	// Phone Номер телефона
	Phone   PhoneNumber  `json:"phone"`
	RelType RelationType `json:"relType"`
}

// GetSurveyResult defines model for GetSurveyResult.
type GetSurveyResult struct {
	// AdditionalAmount Сумма дополнительного источника дохода за последние 6 месяцев
	AdditionalAmount *string           `json:"additionalAmount,omitempty"`
	Address          *GetSurveyAddress `json:"address,omitempty"`

	// ApplicationID Идентификатор заявки
	ApplicationID *string `json:"applicationID,omitempty"`

	// Children Количество детей
	Children *int32 `json:"children,omitempty"`

	// ContactPersons Список контактных лиц
	ContactPersons *[]GetSurveyContactPerson `json:"contactPersons,omitempty"`
	EducationType  *EducationType            `json:"educationType,omitempty"`

	// Email Электронная почта
	Email   *Email          `json:"email,omitempty"`
	EmpType *EmploymentType `json:"empType,omitempty"`
}

// GetSystemResp defines model for GetSystemResp.
type GetSystemResp struct {
	Line           []BidXMLLine `json:"line"`
	SystemResponse SystemResp   `json:"systemResponse"`
}

// GetTaskDetailsResponse defines model for GetTaskDetailsResponse.
type GetTaskDetailsResponse struct {
	// CreatedAt Дата создания задачи
	CreatedAt time.Time `json:"created_at"`

	// Payload Полезная нагрузка задачи
	Payload map[string]interface{} `json:"payload"`

	// Status Статус задачи
	Status string `json:"status"`

	// TaskID Идентификатор задачи
	TaskID openapi_types.UUID `json:"taskID"`

	// TaskType Тип задачи
	TaskType string `json:"task_type"`

	// UpdatedAt Дата обновления задачи
	UpdatedAt time.Time `json:"updated_at"`
}

// GetTasksListResponse defines model for GetTasksListResponse.
type GetTasksListResponse struct {
	// Page Номер страницы
	Page int `json:"page"`

	// PageSize Размер страницы
	PageSize int `json:"page_size"`

	// Tasks Список задач
	Tasks []Task `json:"tasks"`

	// TotalCount Общее количество задач
	TotalCount int `json:"total_count"`
}

// GetTaxAuthorityListResp defines model for GetTaxAuthorityListResp.
type GetTaxAuthorityListResp struct {
	// Ugds Список УГД
	Ugds *[]TaxAuthorityItem `json:"ugds,omitempty"`
}

// HeaderRequest defines model for HeaderRequest.
type HeaderRequest struct {
	MemberShortName string `json:"memberShortName"`
	Uuid            string `json:"uuid"`
}

// HeaderResp defines model for HeaderResp.
type HeaderResp struct {
	ErrorCode       *string `json:"errorCode"`
	ErrorMsg        *string `json:"errorMsg"`
	MemberShortName string  `json:"memberShortName"`
	Uuid            string  `json:"uuid"`
}

// Health defines model for Health.
type Health struct {
	// AltScoreBridge Статус сервиса altScoreBridge
	AltScoreBridge bool `json:"altScoreBridge"`

	// AmlBridge Статус сервиса amlBridge
	AmlBridge bool `json:"amlBridge"`

	// Antifraud Статус сервиса antifraud
	Antifraud bool `json:"antifraud"`

	// ApBridge Статус сервиса apBridge
	ApBridge bool `json:"apBridge"`

	// BalanceUpdater Статус сервиса balanceUpdater
	BalanceUpdater bool `json:"balanceUpdater"`

	// BitrixBridge Статус сервиса bitrixBridge
	BitrixBridge bool `json:"bitrixBridge"`

	// BsasBridge Статус сервиса bsasBridge
	BsasBridge bool `json:"bsasBridge"`

	// BtsBridge Статус сервиса btsBridge
	BtsBridge bool `json:"btsBridge"`

	// CardsAccounts Статус сервиса cardsAccounts
	CardsAccounts bool `json:"cardsAccounts"`

	// Collection Статус сервиса collection
	Collection bool `json:"collection"`

	// ColvirBridge Статус сервиса colvirBridge
	ColvirBridge bool `json:"colvirBridge"`

	// Crm Статус сервиса crm
	Crm bool `json:"crm"`

	// Deposits Статус сервиса deposits
	Deposits bool `json:"deposits"`

	// Dictionary Статус сервиса dictionary
	Dictionary bool `json:"dictionary"`

	// Documents Статус сервиса documents
	Documents bool `json:"documents"`

	// FileGuard Статус сервиса fileGuard
	FileGuard bool `json:"fileGuard"`

	// ForeignActivity Статус сервиса foreignActivity
	ForeignActivity bool `json:"foreignActivity"`

	// JiraBridge Статус сервиса jiraBridge
	JiraBridge bool `json:"jiraBridge"`

	// JuicyscoreBridge Статус сервиса juicyscoreBridge
	JuicyscoreBridge bool `json:"juicyscoreBridge"`

	// KaspiBridge Статус сервиса kaspiBridge
	KaspiBridge bool `json:"kaspiBridge"`

	// KeycloakProxy Статус сервиса keycloakProxy
	KeycloakProxy bool `json:"keycloakProxy"`

	// KgdBridge Статус сервиса kgdBridge
	KgdBridge bool `json:"kgdBridge"`

	// Liveness Статус сервиса liveness
	Liveness bool `json:"liveness"`

	// Loans Статус сервиса loans
	Loans bool `json:"loans"`

	// Notifications Статус сервиса notifications
	Notifications bool `json:"notifications"`

	// Otp Статус сервиса otp
	Otp bool `json:"otp"`

	// Payments Статус сервиса payments
	Payments bool `json:"payments"`

	// PaymentsSme Статус сервиса paymentsSme
	PaymentsSme bool `json:"paymentsSme"`

	// PkbBridge Статус сервиса pkbBridge
	PkbBridge bool `json:"pkbBridge"`

	// ProcessingBridge Статус сервиса processingBridge
	ProcessingBridge bool `json:"processingBridge"`

	// QazpostBridge Статус сервиса qazpostBridge
	QazpostBridge bool `json:"qazpostBridge"`

	// Referral Статус сервиса referral
	Referral bool `json:"referral"`

	// Scoring Статус сервиса scoring
	Scoring bool `json:"scoring"`

	// SeonBridge Статус сервиса seonBridge
	SeonBridge bool `json:"seonBridge"`

	// SmsBridge Статус сервиса smsBridge
	SmsBridge bool `json:"smsBridge"`

	// SprBridge Статус сервиса sprBridge
	SprBridge bool `json:"sprBridge"`

	// TaskManager Статус сервиса taskManager
	TaskManager bool `json:"taskManager"`

	// Tokenize Статус сервиса tokenize
	Tokenize bool `json:"tokenize"`

	// Users Статус сервиса users
	Users bool `json:"users"`
}

// InputRequest defines model for InputRequest.
type InputRequest struct {
	Ecertno         string `json:"ecertno"`
	Membershortname string `json:"membershortname"`
}

// InterestAmountData Наценка
type InterestAmountData struct {
	// CurrencyCode Валюта
	CurrencyCode string `json:"currencyCode"`

	// Value Сумма наценки
	Value string `json:"value"`
}

// IpInfo defines model for IpInfo.
type IpInfo struct {
	// IssuingAuthority Организация, выдавшая документ
	IssuingAuthority string `json:"issuingAuthority"`

	// RegistrationCert Номер свидетельства о регистрации ИП
	RegistrationCert string `json:"registrationCert"`

	// RegistrationDate Дата регистрации ИП "yyyy-mm-dd"
	RegistrationDate string `json:"registrationDate"`
}

// KatoData defines model for KatoData.
type KatoData struct {
	// ID Идентификатор КАТО
	ID int32 `json:"ID"`

	// Code Код КАТО
	Code string `json:"code"`

	// Name Название КАТО
	Name string `json:"name"`

	// ParentID Идентификатор родительской КАТО
	ParentID int32 `json:"parentID"`
}

// KbeKodCode defines model for KbeKodCode.
type KbeKodCode struct {
	// Name Наименование Кбе/КОд на языке локализации
	Name *string `json:"name,omitempty"`

	// Residency Признак резидента
	Residency *bool `json:"residency,omitempty"`

	// Value Код Кбе/КОд
	Value *string `json:"value,omitempty"`
}

// KbeKodItem defines model for KbeKodItem.
type KbeKodItem struct {
	Code *KbeKodCode `json:"code,omitempty"`
}

// KbkCode defines model for KbkCode.
type KbkCode struct {
	// Name Наименование КБК на языке локализации
	Name *string `json:"name,omitempty"`

	// Value Код КБК
	Value *string `json:"value,omitempty"`
}

// KbkItem defines model for KbkItem.
type KbkItem struct {
	Code *KbkCode `json:"code,omitempty"`
}

// KnpCode defines model for KnpCode.
type KnpCode struct {
	// Name Наименование КНП на языке локализации
	Name *string `json:"name,omitempty"`

	// Value Код КНП
	Value *string `json:"value,omitempty"`
}

// KnpItem defines model for KnpItem.
type KnpItem struct {
	Code *KnpCode `json:"code,omitempty"`
}

// LinkObject defines model for LinkObject.
type LinkObject struct {
	// Title Титры
	Title string `json:"title"`

	// Url Url
	Url string `json:"url"`
}

// Links Блок с URL
type Links struct {
	// EarlyRepayment URL сайта банка с информацией по досрочному погашению
	EarlyRepayment LinkObject `json:"earlyRepayment"`

	// Faq URL сайта банка с информацией по вопросам и ответам
	Faq LinkObject `json:"faq"`

	// Inquiry URL сайта банка с информацией по справке
	Inquiry LinkObject `json:"inquiry"`
}

// Loan defines model for Loan.
type Loan struct {
	// ID Уникальный идентификатор кредита
	ID string `json:"ID"`

	// Amount Сумма
	Amount *Money `json:"amount,omitempty"`

	// ApplicationDueDate Срок окончания одобренной заявки на кредит (5 дней)
	ApplicationDueDate *openapi_types.Date `json:"applicationDueDate,omitempty"`

	// HasDelay Флаг наличия просроченных платежей по кредиту
	HasDelay bool `json:"hasDelay"`

	// NextPayment Объект “Следующий платеж”
	NextPayment *LoansNextPayment `json:"nextPayment,omitempty"`

	// PercentPaid Поле для заполнения виджета оставшейся выплате по кредиту
	PercentPaid *int `json:"percentPaid,omitempty"`

	// ProductType Тип кредитного продукта
	ProductType string `json:"productType"`

	// Status Статус кредита
	Status       *string      `json:"status,omitempty"`
	StatusReason *ErrorReason `json:"statusReason,omitempty"`
}

// LoanAmount Сумма кредита
type LoanAmount struct {
	// Max Максимальная сумма кредита
	Max int `json:"max"`

	// Min Минимальная сумма кредита
	Min int `json:"min"`
}

// LoanAppAmountData Объект суммы кредита
type LoanAppAmountData struct {
	// CurrencyCode Валюта кредита
	CurrencyCode string `json:"currencyCode"`

	// Value Сумма кредита
	Value string `json:"value"`
}

// LoanAppConditionsAmountData Сумма финансирования, одобренная СПР
type LoanAppConditionsAmountData struct {
	// CurrencyCode Валюта
	CurrencyCode string `json:"currencyCode"`

	// Value Сумма финансирования
	Value string `json:"value"`
}

// LoanAppDocumentType Тип (шаблон) документа
type LoanAppDocumentType string

// LoanApplicationCheckResponse defines model for LoanApplicationCheckResponse.
type LoanApplicationCheckResponse struct {
	// HasActive Признак наличия активной кредитной заявки
	HasActive bool         `json:"hasActive"`
	Reason    *ErrorReason `json:"reason,omitempty"`
}

// LoanApplicationDocument defines model for LoanApplicationDocument.
type LoanApplicationDocument struct {
	// ID Идентификатор документа
	ID openapi_types.UUID `json:"ID"`

	// FileLink Ссылка на физический файл
	FileLink string `json:"fileLink"`

	// Signed Был ли документ подписан пользователем
	Signed bool `json:"signed"`

	// Title Название документа
	Title string `json:"title"`

	// Type Тип (шаблон) документа
	Type LoanAppDocumentType `json:"type"`

	// Version Версия документа, которая соотносится с типом документа (версия шаблона)
	Version int `json:"version"`
}

// LoanApplicationDocumentsResp defines model for LoanApplicationDocumentsResp.
type LoanApplicationDocumentsResp struct {
	// Documents Документы сгенерированные по запросу
	Documents []LoanApplicationDocument `json:"documents"`
}

// LoanApplicationResponse defines model for LoanApplicationResponse.
type LoanApplicationResponse struct {
	// ApplicationID ID заявки
	ApplicationID string `json:"applicationID"`

	// ApplicationStatus Присвоенный статус заявки
	ApplicationStatus string `json:"applicationStatus"`
}

// LoanCalcDataResponse defines model for LoanCalcDataResponse.
type LoanCalcDataResponse struct {
	// Purpose Массив доступных условий кредита
	Purpose []LoanPurposeData `json:"purpose"`
}

// LoanDetails Информация по кредиту
type LoanDetails struct {
	// Account Информация по счету
	Account Account `json:"account"`

	// Details Детали финансирования
	Details    LoanDetailsObject `json:"details"`
	HasOverdue bool              `json:"hasOverdue"`

	// NextPayment Информация о ближайшем платеже
	NextPayment NextPayment `json:"nextPayment"`

	// PaidInfo Информация для виджета
	PaidInfo PaidInfo `json:"paidInfo"`

	// ProductType Тип продукта LOAN - Кредит REFINANCE - Рефинансирование
	ProductType LoanDetailsProductType `json:"productType"`

	// Schedule График погашения
	Schedule []ScheduleItem `json:"schedule"`
}

// LoanDetailsProductType Тип продукта LOAN - Кредит REFINANCE - Рефинансирование
type LoanDetailsProductType string

// LoanDetailsContract Подписанный договор
type LoanDetailsContract struct {
	// DocID ID подписанного договора
	DocID string `json:"docID"`

	// FileLink Ссылка на подписанный договор, хранящийся в S3 в общей папке
	FileLink string `json:"fileLink"`

	// Number Номер кредитного договора
	Number string `json:"number"`

	// Title Заголовок для документа
	Title string `json:"title"`
}

// LoanDetailsObject Детали финансирования
type LoanDetailsObject struct {
	// Contract Подписанный договор
	Contract LoanDetailsContract `json:"contract"`

	// EndDate Дата окончания договора
	EndDate openapi_types.Date `json:"endDate"`

	// InterestAmount Сумма наценки на весь срок финансирования
	InterestAmount Money `json:"interestAmount"`

	// LoanAmount Сумма финансирования (на весь срок)
	LoanAmount Money `json:"loanAmount"`

	// StartDate Дата начала договора
	StartDate openapi_types.Date `json:"startDate"`

	// Term Срок кредита (в месяцах)
	Term Term `json:"term"`
}

// LoanPurposeData defines model for LoanPurposeData.
type LoanPurposeData struct {
	// ID Идентификатор цели кредита
	ID string `json:"ID"`

	// Amount Сумма кредита
	Amount      LoanAmount `json:"amount"`
	Description string     `json:"description"`

	// TermInterest Массив доступных целей кредита
	TermInterest []LoanTermInterestData `json:"termInterest"`
}

// LoanTermInterestData Холдер условий кредита
type LoanTermInterestData struct {
	// ID Идентификатор объекта условий кредита
	ID string `json:"ID"`

	// DefaultInterest Признак срока кредита по подсветки по умолчанию
	DefaultInterest bool `json:"defaultInterest"`

	// Interest Процентная ставка кредита
	Interest int `json:"interest"`

	// Term Срок кредита (в месяцах)
	Term Term `json:"term"`
}

// LoansNextPayment Объект “Следующий платеж”
type LoansNextPayment struct {
	// Amount Сумма
	Amount *Money `json:"amount,omitempty"`

	// Date Дата ближайшего платежа
	Date openapi_types.Date `json:"date"`
}

// LoansResponse defines model for LoansResponse.
type LoansResponse struct {
	// Loans Список кредитов
	Loans []Loan `json:"loans"`
	Offer *Offer `json:"offer,omitempty"`
}

// Location defines model for Location.
type Location struct {
	// ID Идентификатор локации
	ID int32 `json:"ID"`

	// Code Код локации
	Code string `json:"code"`

	// Name Название локации
	Name string `json:"name"`

	// ParentID Идентификатор родительской локации
	ParentID int32 `json:"parentID"`
}

// Money Сумма
type Money struct {
	// CurrencyCode Код валюты
	CurrencyCode CurrencyCode `json:"currencyCode"`

	// Value Сумма
	Value string `json:"value"`
}

// NextPayment Информация о ближайшем платеже
type NextPayment struct {
	// Amount Сумма ближайшего платежа
	Amount Money `json:"amount"`

	// Date Дата ближайшего платежа
	Date openapi_types.Date `json:"date"`
}

// NextPaymentAmountData Ежемесячный платеж
type NextPaymentAmountData struct {
	// CurrencyCode Валюта
	CurrencyCode string `json:"currencyCode"`

	// Value Сумма ежемесячного платежа
	Value string `json:"value"`
}

// NextStep defines model for NextStep.
type NextStep string

// Offer defines model for Offer.
type Offer struct {
	// SubText Подтекст с информацией по сумме и сроку кредита (ограничение по символам - 20)
	SubText string `json:"subText"`

	// Text Текст с предложением оформить кредит (для баннера) (ограничение по символам - 20)
	Text string `json:"text"`

	// Title Заголовок для баннера (ограничение по символам - 20)
	Title string `json:"title"`
}

// OnboardingTextData Холдер текстовок
type OnboardingTextData struct {
	// Code Уникальный код текста
	Code string `json:"code"`

	// Description Текст для отображения
	Description string `json:"description"`

	// Image Ссылка в S3 на изображение
	Image string `json:"image"`
}

// OnboardingTextsResponse defines model for OnboardingTextsResponse.
type OnboardingTextsResponse struct {
	// Texts Массив текстов и изображений для onboarding экрана
	Texts []OnboardingTextData `json:"texts"`
}

// OrderResultBodyResp defines model for OrderResultBodyResp.
type OrderResultBodyResp struct {
	// BidErrNo Номер кода ошибки для BID
	BidErrNo *string `json:"bidErrNo,omitempty"`

	// BidMsg Сообщение для BID
	BidMsg *string `json:"bidMsg,omitempty"`

	// BidOption BID заказ – купить товар
	BidOption string `json:"bidOption"`

	// BidValue Сумма покупки/продажи
	BidValue string `json:"bidValue"`

	// ClientName ФИО клиента на латинском
	ClientName string `json:"clientName"`

	// Currency Валюта
	Currency string `json:"currency"`
	EcertNo  string `json:"ecertNo"`

	// OrderTime Время заказа (hhmmssmss)
	OrderTime       *string `json:"orderTime,omitempty"`
	OtcCounterParty string  `json:"otcCounterParty"`

	// OtcErrNo Номер кода ошибки для OTC
	OtcErrNo *string `json:"otcErrNo,omitempty"`

	// OtcMsg Сообщение для OTC
	OtcMsg           *string `json:"otcMsg,omitempty"`
	OtcMurabaha      string  `json:"otcMurabaha"`
	OtcMurabahaValue string  `json:"otcMurabahaValue"`

	// OtcOption Внебиржевой заказ – передача права собственности на товар клиенту
	OtcOption string `json:"otcOption"`

	// Price Цена
	Price *string `json:"price,omitempty"`

	// ProductCode Код продукта
	ProductCode string `json:"productCode"`

	// PurchaseTime Время покупки (hhmmssmss)
	PurchaseTime *string `json:"purchaseTime,omitempty"`

	// PurchaseType Тип покупки
	PurchaseType string `json:"purchaseType"`

	// RegTime Время регистрации (hhmmssmss)
	RegTime *string `json:"regTime,omitempty"`

	// ReportTime Время внебиржевой отчетности (hhmmssmss)
	ReportTime *string `json:"reportTime,omitempty"`

	// ResultTime Время генерации данных результата (hhmmssmss)
	ResultTime *string `json:"resultTime,omitempty"`

	// SellingTime Время СТБ (hhmmssmss)
	SellingTime *string `json:"sellingTime,omitempty"`

	// SerialNumber Запись количества заказов
	SerialNumber int `json:"serialNumber"`

	// StbErrNo Номер кода ошибки для STB
	StbErrNo *string `json:"stbErrNo,omitempty"`

	// StbMsg Сообщение для СТБ
	StbMsg *string `json:"stbMsg,omitempty"`

	// StbOption Заказ STB – Продать товар клиенту
	StbOption string `json:"stbOption"`

	// Tenor Значение тенора
	Tenor string `json:"tenor"`

	// Unit Единица
	Unit *string `json:"unit,omitempty"`

	// ValueDate Дата запроса покупки/продажи Передает дату по Малазии
	ValueDate string `json:"valueDate"`
}

// OrderResultItemRequest defines model for OrderResultItemRequest.
type OrderResultItemRequest struct {
	ForceYN       string  `json:"forceYN"`
	MaxWaitTime   *string `json:"maxWaitTime,omitempty"`
	SerialNumber  *string `json:"serialNumber,omitempty"`
	WaitAllDoneYN *string `json:"waitAllDoneYN,omitempty"`
}

// OtcLineItem defines model for OtcLineItem.
type OtcLineItem struct {
	SUPPLIER *string `json:"SUPPLIER,omitempty"`
	VOLUME   *string `json:"VOLUME,omitempty"`
}

// OtpFullResponse defines model for OtpFullResponse.
type OtpFullResponse struct {
	// AttemptID Идентификатор для валидации отп кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// AttemptsLeft Количество оставшихся попыток
	AttemptsLeft int `json:"attemptsLeft"`

	// AttemptsTimeout Количество секунд жизни попытки отп валидации
	AttemptsTimeout int `json:"attemptsTimeout"`

	// CodeChecksLeft Количество оставшихся попыток проверки кода отп
	CodeChecksLeft int `json:"codeChecksLeft"`

	// RetryTime Количество секунд до следующей отправки
	RetryTime int `json:"retryTime"`
}

// OtpResponse defines model for OtpResponse.
type OtpResponse struct {
	// AttemptID Идентификатор попытки для проверки кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// RetryTime Количество секунд до следующей отправки
	RetryTime int `json:"retryTime"`
}

// Overdue Просроченный платеж
type Overdue struct {
	// Days Количество дней просрочки
	Days *int `json:"days,omitempty"`

	// FineDebt Сумма пени
	FineDebt *Money `json:"fineDebt,omitempty"`

	// Hint Подсказка по просроченному платежу
	Hint string `json:"hint"`
}

// PaidInfo Информация для виджета
type PaidInfo struct {
	// PercentPaid Поле для заполнения виджета оставшейся суммы выплаты по кредиту
	PercentPaid string `json:"percentPaid"`

	// RemainingAmount Оставшаяся сумма выплаты по кредиту
	RemainingAmount Money `json:"remainingAmount"`

	// Text Текст под виджетом оставшейся суммы выплаты по кредиту
	Text string `json:"text"`
}

// PaymentData Данные для платежа ОПВ (Обязательные пенсионные взносы)
type PaymentData struct {
	// Amount Общая сумма платежа
	Amount string `json:"amount"`

	// BeneficiaryBINIIN БИН/ИИН получателя платежа
	BeneficiaryBINIIN *string `json:"beneficiaryBINIIN,omitempty"`

	// BeneficiaryName Наименование получателя платежа
	BeneficiaryName *string `json:"beneficiaryName,omitempty"`

	// Employees Список сотрудников
	Employees []EmployeeItem `json:"employees"`

	// Kbk Код Бюджетной Классификации
	Kbk *string `json:"kbk,omitempty"`

	// PayerAccount Счет плательщика
	PayerAccount string `json:"payerAccount"`

	// PaymentPeriod Период платежа (MMYYYY)
	PaymentPeriod string `json:"paymentPeriod"`

	// PurposeCode Код назначения платежа
	PurposeCode string `json:"purposeCode"`

	// PurposeDetails Детали назначения платежа
	PurposeDetails string `json:"purposeDetails"`

	// RealBeneficiaryBINIIN ИИН фактического получателя
	RealBeneficiaryBINIIN *string `json:"realBeneficiaryBINIIN,omitempty"`

	// RealBeneficiaryCountry Страна резидентства фактического получателя
	RealBeneficiaryCountry *string `json:"realBeneficiaryCountry,omitempty"`

	// RealBeneficiaryName ФИО фактического получателя
	RealBeneficiaryName *string `json:"realBeneficiaryName,omitempty"`

	// SignatoryA Подпись ответственного лица (ФИО)
	SignatoryA string `json:"signatoryA"`
}

// PaymentDetails Детали платежа
type PaymentDetails struct {
	// BaseAmount Сумма финансирования (за 1 месяц)
	BaseAmount Money `json:"baseAmount"`

	// Date Дата платежа
	Date openapi_types.Date `json:"date"`

	// InterestAmount Сумма наценки (заполняется в зависимости от наличия просрочки)
	InterestAmount Money `json:"interestAmount"`

	// Overdue Просроченный платеж
	Overdue *Overdue `json:"overdue,omitempty"`
}

// PaymentHintData Подсказка в карточке кредита
type PaymentHintData struct {
	// InfoUrl URL сайта банка с информацией по способам внесения платежей
	InfoUrl string `json:"infoUrl"`

	// Text Текст подсказки
	Text string `json:"text"`

	// Title Заголовок подсказки
	Title string `json:"title"`
}

// PaymentsCheckAccountIinResponse defines model for PaymentsCheckAccountIinResponse.
type PaymentsCheckAccountIinResponse struct {
	// AdditionalIndividualType Дополнительный тип для ФЛ если есть
	AdditionalIndividualType *AdditionalIndividualType `json:"additionalIndividualType,omitempty"`

	// BankBic БИК банка
	BankBic string `json:"bankBic"`

	// BankName Название банка
	BankName string `json:"bankName"`

	// Name Наименование ЮЛ/ФЛ
	Name string `json:"name"`

	// TaxPayerType Тип налогоплательщика
	TaxPayerType TaxPayerType `json:"taxPayerType"`
}

// PaymentsGetHistoryItem defines model for PaymentsGetHistoryItem.
type PaymentsGetHistoryItem struct {
	// ActualClientCountry Страна фактического клиента (опционально)
	ActualClientCountry *string `json:"actualClientCountry,omitempty"`

	// ActualClientIinBin ИИН/БИН фактического клиента (опционально)
	ActualClientIinBin *string `json:"actualClientIinBin,omitempty"`

	// ActualClientName Имя фактического клиента (опционально)
	ActualClientName *string `json:"actualClientName,omitempty"`

	// ActualCounterpartyCountry Страна фактического контрагента (опционально)
	ActualCounterpartyCountry *string `json:"actualCounterpartyCountry,omitempty"`

	// ActualCounterpartyIinBin ИИН/БИН фактического контрагента (опционально)
	ActualCounterpartyIinBin *string `json:"actualCounterpartyIinBin,omitempty"`

	// ActualCounterpartyName Имя фактического контрагента (опционально)
	ActualCounterpartyName *string `json:"actualCounterpartyName,omitempty"`

	// Amount Сумма платежа
	Amount float64 `json:"amount"`

	// ClientAccount Номер счёта клиента
	ClientAccount string `json:"clientAccount"`

	// ClientBankBic БИК банка
	ClientBankBic string `json:"clientBankBic"`

	// ClientBankName Наименование банка клиента
	ClientBankName string `json:"clientBankName"`

	// ClientIinBin ИИН/БИН клиента
	ClientIinBin string `json:"clientIinBin"`

	// ClientName Наименование клиента
	ClientName string `json:"clientName"`

	// CounterpartyAccount Счёт контрагента
	CounterpartyAccount string `json:"counterpartyAccount"`

	// CounterpartyBankBic БИК банка контрагента
	CounterpartyBankBic string `json:"counterpartyBankBic"`

	// CounterpartyBankName Название банка контрагента
	CounterpartyBankName string `json:"counterpartyBankName"`

	// CounterpartyIinBin ИИН/БИН контрагента
	CounterpartyIinBin string `json:"counterpartyIinBin"`

	// CounterpartyKbe КБЕ контрагента
	CounterpartyKbe string `json:"counterpartyKbe"`

	// CounterpartyName Название контрагента
	CounterpartyName string `json:"counterpartyName"`

	// Currency Валюта платежа
	Currency string `json:"currency"`

	// Date Дата платежа
	Date openapi_types.Date `json:"date"`

	// Knp Код назначения платежа (КНП)
	Knp string `json:"knp"`

	// PaymentDetails Детали платежа
	PaymentDetails string `json:"paymentDetails"`

	// ValueDate Дата исполнения операции
	ValueDate *openapi_types.Date `json:"valueDate,omitempty"`
}

// PaymentsGetHistoryResponse defines model for PaymentsGetHistoryResponse.
type PaymentsGetHistoryResponse struct {
	// Payments Список элементов истории платежей
	Payments *[]PaymentsGetHistoryItem `json:"payments,omitempty"`
}

// PaymentsGetTransactionByIDResponse defines model for PaymentsGetTransactionByIDResponse.
type PaymentsGetTransactionByIDResponse struct {
	// Amount Сумма операции
	Amount float64 `json:"amount"`

	// BeneficiaryAccount Номер счёта получателя
	BeneficiaryAccount *string `json:"beneficiaryAccount,omitempty"`

	// BeneficiaryIinBin ИИН/БИН получателя
	BeneficiaryIinBin *string `json:"beneficiaryIinBin,omitempty"`

	// BeneficiaryName Наименование получателя
	BeneficiaryName *string `json:"beneficiaryName,omitempty"`

	// Commission Коммиссия
	Commission float64 `json:"commission"`

	// Currency Валюта операции
	Currency string `json:"currency"`

	// Direction Тип операции
	Direction PaymentsOperationType `json:"direction"`

	// Kbe Код бенефициара
	Kbe *string `json:"kbe,omitempty"`

	// Knp Код назначения платежа (КНП)
	Knp string `json:"knp"`

	// MobileOperator Мобильный оператор
	MobileOperator *string `json:"mobileOperator,omitempty"`

	// PayerAccount Номер счёта отправителя
	PayerAccount *string `json:"payerAccount,omitempty"`

	// PayerIinBin ИИН/БИН отправителя
	PayerIinBin *string `json:"payerIinBin,omitempty"`

	// PayerName Наименование отправителя
	PayerName *string `json:"payerName,omitempty"`

	// PhoneNumber Номер телефона
	PhoneNumber *string `json:"phoneNumber,omitempty"`

	// Status Статус транзакции
	Status PaymentsTransactionStatus `json:"status"`

	// TransactionDate Дата операции
	TransactionDate time.Time `json:"transactionDate"`

	// TransactionDetails Детали транзакции
	TransactionDetails string `json:"transactionDetails"`

	// TransactionID Идентификатор транзакции
	TransactionID string `json:"transactionID"`

	// TransactionNumber Пользовательский номер транзакции
	TransactionNumber string `json:"transactionNumber"`

	// TransactionType Внутренний тип перевода
	TransactionType TransactionType `json:"transactionType"`

	// ValueDate Дата исполнения операции
	ValueDate *time.Time `json:"valueDate,omitempty"`
}

// PaymentsGetTransactionReceiptResponse defines model for PaymentsGetTransactionReceiptResponse.
type PaymentsGetTransactionReceiptResponse struct {
	// FileLink Ссылка на файл
	FileLink string `json:"fileLink"`

	// Title Наименование документа
	Title string `json:"title"`
}

// PaymentsGetTransactionsResponse defines model for PaymentsGetTransactionsResponse.
type PaymentsGetTransactionsResponse struct {
	// Limit Лимит количества возвращаемых транзакций
	Limit int64 `json:"limit"`

	// Offset Смещение для пагинации
	Offset int64 `json:"offset"`

	// StartDate Начальная дата для фильтрации транзакций
	StartDate time.Time `json:"startDate"`

	// TotalCount Общее количество транзакций
	TotalCount int64 `json:"totalCount"`

	// Transactions Список транзакций
	Transactions []PaymentsTransaction `json:"transactions"`
}

// PaymentsOperationType Тип операции
type PaymentsOperationType string

// PaymentsTransaction defines model for PaymentsTransaction.
type PaymentsTransaction struct {
	// AccountNumber Номер cчёта
	AccountNumber string `json:"accountNumber"`

	// Amount Сумма транзакции
	Amount float64 `json:"amount"`

	// Category Категория операции
	Category *string `json:"category,omitempty"`

	// Currency Валюта транзакции
	Currency string `json:"currency"`

	// Direction Тип операции
	Direction PaymentsOperationType `json:"direction"`

	// LogoUrl URL изображения логотипа контрагента
	LogoUrl *string `json:"logoUrl,omitempty"`

	// Status Статус транзакции
	Status PaymentsTransactionStatus `json:"status"`

	// TransactionDate Дата операции
	TransactionDate openapi_types.Date `json:"transactionDate"`

	// TransactionID Уникальный идентификатор транзакции
	TransactionID string `json:"transactionID"`

	// TransactionType Внутренний тип перевода
	TransactionType TransactionType `json:"transactionType"`
}

// PaymentsTransactionStatus Статус транзакции
type PaymentsTransactionStatus string

// PhoneNumber Номер телефона
type PhoneNumber = string

// PostChangeDisbursementControlResponse defines model for PostChangeDisbursementControlResponse.
type PostChangeDisbursementControlResponse struct {
	// Mode Активированные режим работы биржи
	Mode string `json:"mode"`
}

// PostEarlyRepayResp defines model for PostEarlyRepayResp.
type PostEarlyRepayResp struct {
	// Contract Оплата финансирования
	Contract *EarlyRepayContract `json:"contract,omitempty"`

	// RepayAmount Сумма ЧДП/ПДП
	RepayAmount EarlyRepayAmount `json:"repayAmount"`
}

// PostEdsBtsDataResp defines model for PostEdsBtsDataResp.
type PostEdsBtsDataResp struct {
	// Amount Объект суммы кредита
	Amount LoanAppAmountData `json:"amount"`

	// Subtitle Подзаголовок сообщения пользователю
	Subtitle string `json:"subtitle"`

	// Term Текст по сроку кредита
	Term string `json:"term"`

	// Title Заголовок сообщения пользователю
	Title string `json:"title"`
}

// PostIdentifyBtsDataSmeResp defines model for PostIdentifyBtsDataSmeResp.
type PostIdentifyBtsDataSmeResp struct {
	// ApplicationStatus Статус заявки на кредит
	ApplicationStatus string       `json:"applicationStatus"`
	Reason            *ErrorReason `json:"reason,omitempty"`
}

// PublicDocumentType Тип (шаблон) публичного документа
type PublicDocumentType string

// PublishLoanAppResp defines model for PublishLoanAppResp.
type PublishLoanAppResp struct {
	// ApplicationStatus Статус заявки на кредит
	ApplicationStatus string `json:"applicationStatus"`
}

// RefinancingConditions defines model for RefinancingConditions.
type RefinancingConditions struct {
	ID string `json:"ID"`

	// CreditAmount Сумма зачисления (полученная клиентом на руки)
	CreditAmount Money `json:"creditAmount"`

	// InterestAmount Наценка
	InterestAmount Money `json:"interestAmount"`

	// PaymentAmount Ежемесячный платеж
	PaymentAmount Money `json:"paymentAmount"`

	// RefAmount Сумма на погашение кредитов в других банках
	RefAmount Money `json:"refAmount"`

	// Term Срок кредита (в месяцах)
	Term  Term   `json:"term"`
	Title string `json:"title"`

	// TotalAmount Общая сумма
	TotalAmount Money `json:"totalAmount"`
}

// RejectionReason defines model for RejectionReason.
type RejectionReason struct {
	// Code Код ошибки
	Code string `json:"code"`

	// Message Сообщение о причине отказа
	Message string `json:"message"`

	// Title Заголовок причины отказа
	Title string `json:"title"`
}

// RelationType defines model for RelationType.
type RelationType struct {
	// ID Идентификатор родства контактного лица
	ID string `json:"ID"`

	// Code Код родства контактного лица
	Code string `json:"code"`

	// Name Название родства контактного лица
	Name string `json:"name"`
}

// RequestItem defines model for RequestItem.
type RequestItem struct {
	BidOption        string `json:"bidOption"`
	BidValue         string `json:"bidValue"`
	ClientName       string `json:"clientName"`
	Currency         string `json:"currency"`
	ECertNo          string `json:"eCertNo"`
	OtcCounterParty  string `json:"otcCounterParty"`
	OtcMurabaha      string `json:"otcMurabaha"`
	OtcMurabahaValue string `json:"otcMurabahaValue"`
	OtcOption        string `json:"otcOption"`
	ProductCode      string `json:"productCode"`
	PurchaseType     string `json:"purchaseType"`
	SerialNumber     string `json:"serialNumber"`
	StbOption        string `json:"stbOption"`
	Tenor            string `json:"tenor"`
	ValueDate        string `json:"valueDate"`
}

// SaveSurveyAddress defines model for SaveSurveyAddress.
type SaveSurveyAddress struct {
	// Building Номер или название здания.
	Building string `json:"building"`

	// Flat Номер квартиры или офиса.
	Flat *string `json:"flat,omitempty"`

	// KatoCodes Коды КАТО
	KatoCodes []string `json:"katoCodes"`

	// Street Улица адреса.
	Street string `json:"street"`
}

// SaveSurveyContactPerson defines model for SaveSurveyContactPerson.
type SaveSurveyContactPerson struct {
	// FirstName Имя контактного лица.
	FirstName string `json:"firstName"`

	// LastName Фамилия контактного лица.
	LastName string `json:"lastName"`

	// Phone Номер телефона
	Phone PhoneNumber `json:"phone"`

	// RelID Идентификатор родства контактного лица.
	RelID string `json:"relID"`
}

// SaveSurveyResp defines model for SaveSurveyResp.
type SaveSurveyResp struct {
	// SurveyID Идентификатор анкеты пользователя.
	SurveyID string `json:"surveyID"`
}

// ScheduleItem График погашения
type ScheduleItem struct {
	// Date Дата платежа
	Date openapi_types.Date `json:"date"`

	// Details Детали платежа
	Details PaymentDetails `json:"details"`

	// PaymentAmount Сумма платежа (Сумма финансирования (baseAmount) + Наценка (interestAmount))
	PaymentAmount Money `json:"paymentAmount"`

	// Status Статус платежа PLAN - К оплате END - Погашен OVERDUE - Просрочен
	Status ScheduleItemStatus `json:"status"`
}

// ScheduleItemStatus Статус платежа PLAN - К оплате END - Погашен OVERDUE - Просрочен
type ScheduleItemStatus string

// ScoringLoanConditions defines model for ScoringLoanConditions.
type ScoringLoanConditions struct {
	// Amount Сумма финансирования, одобренная СПР
	Amount LoanAppConditionsAmountData `json:"amount"`

	// Hint Текст под условиями предложения
	Hint string `json:"hint"`

	// InterestAmount Наценка
	InterestAmount InterestAmountData `json:"interestAmount"`

	// NextPaymentAmount Ежемесячный платеж
	NextPaymentAmount NextPaymentAmountData `json:"nextPaymentAmount"`

	// Term Срок кредита (в месяцах)
	Term Term `json:"term"`

	// Title Заголовок экрана
	Title string `json:"title"`
}

// ScoringResultResp defines model for ScoringResultResp.
type ScoringResultResp struct {
	// ApplicationStatus Статус заявки на кредит
	ApplicationStatus string                 `json:"applicationStatus"`
	LoanConditions    *ScoringLoanConditions `json:"loanConditions,omitempty"`

	// ProductType Тип продукта (LOAN/REFINANCING)
	ProductType *string                              `json:"productType,omitempty"`
	Reason      *ErrorReason                         `json:"reason,omitempty"`
	Refinancing *GetScoringResultRespRefinancingInfo `json:"refinancing,omitempty"`
}

// SellResp defines model for SellResp.
type SellResp struct {
	// BUYER Name of the buyer
	BUYER string `json:"BUYER"`

	// CURRENCY Currency code
	CURRENCY string `json:"CURRENCY"`

	// ECERTNO Unique certificate number
	ECERTNO string `json:"ECERTNO"`

	// LINE List of suppliers and volumes
	LINE []OtcLineItem `json:"LINE"`

	// PNAME Product name
	PNAME string `json:"PNAME"`

	// PRICE Price per unit (formatted with commas)
	PRICE string `json:"PRICE"`

	// PRICEMYREQUIVALENT Price equivalent in MYR (formatted with commas)
	PRICEMYREQUIVALENT string `json:"PRICE_MYR_EQUIVALENT"`

	// PVOLUME Product volume/quantity
	PVOLUME string `json:"PVOLUME"`

	// SELLER Name of the seller
	SELLER string `json:"SELLER"`

	// SELLINGTIMEDATE Timestamp of the trade execution
	SELLINGTIMEDATE string `json:"SELLINGTIMEDATE"`

	// TOTALVALUE Total value of the trade (formatted with commas)
	TOTALVALUE string `json:"TOTALVALUE"`

	// VALUEDATE Value date of the trade
	VALUEDATE string `json:"VALUEDATE"`
}

// SignedDocumentsBatchResponse defines model for SignedDocumentsBatchResponse.
type SignedDocumentsBatchResponse struct {
	// Documents Подписанные документы
	Documents []Document `json:"documents"`
}

// SmePaymentsCreateOtpResponse defines model for SmePaymentsCreateOtpResponse.
type SmePaymentsCreateOtpResponse struct {
	// AttemptId Идентификатор попытки подписи
	AttemptId *string `json:"attemptId,omitempty"`

	// AttemptsLeft Количество попыток перегенерации кода
	AttemptsLeft *string `json:"attemptsLeft,omitempty"`

	// AttemptsTimeout Время между генерациями кода
	AttemptsTimeout *string `json:"attemptsTimeout,omitempty"`

	// CodeChecksLeft Количество попыток ввода кода
	CodeChecksLeft *string `json:"codeChecksLeft,omitempty"`

	// CodeTtl Время жизни OTP
	CodeTtl *string `json:"codeTtl,omitempty"`

	// Error Описание ошибки
	Error *string `json:"error,omitempty"`

	// NewAttemptDelay Задержка перед следующей перегенерацией
	NewAttemptDelay *string `json:"newAttemptDelay,omitempty"`
}

// SmePaymentsDeleteEmployeeListResponse defines model for SmePaymentsDeleteEmployeeListResponse.
type SmePaymentsDeleteEmployeeListResponse struct {
	// Employees Список сотрудников
	Employees []EmployeeInfo `json:"employees"`
}

// SmePaymentsDeviceInfo defines model for SmePaymentsDeviceInfo.
type SmePaymentsDeviceInfo struct {
	// AppVersion Версия МП
	AppVersion *string `json:"appVersion,omitempty"`

	// DeviceModel Модель устройства
	DeviceModel *string `json:"deviceModel,omitempty"`

	// InstallationId Идентификатор установки
	InstallationId *string `json:"installationId,omitempty"`

	// SystemType ОС устройства
	SystemType *string `json:"systemType,omitempty"`

	// SystemVersion Версия ОС
	SystemVersion *string `json:"systemVersion,omitempty"`
}

// SmePaymentsGetEmployeeListResponse defines model for SmePaymentsGetEmployeeListResponse.
type SmePaymentsGetEmployeeListResponse struct {
	// Employees Список сотрудников
	Employees []EmployeeInfo `json:"employees"`
}

// SmePaymentsGetPaymentOrderByTrNumberResponse defines model for SmePaymentsGetPaymentOrderByTrNumberResponse.
type SmePaymentsGetPaymentOrderByTrNumberResponse struct {
	// Id Идентификатор транзакции
	Id string `json:"id"`

	// Link Ссылка на файл документа
	Link string `json:"link"`

	// Title Название документа
	Title string `json:"title"`

	// TransactionStatus Статус транзакции
	TransactionStatus SmePaymentsGetPaymentOrderByTrNumberResponseTransactionStatus `json:"transactionStatus"`

	// Version Версия документа
	Version int `json:"version"`
}

// SmePaymentsGetPaymentOrderByTrNumberResponseTransactionStatus Статус транзакции
type SmePaymentsGetPaymentOrderByTrNumberResponseTransactionStatus string

// SmePaymentsGetPaymentOrderResponse defines model for SmePaymentsGetPaymentOrderResponse.
type SmePaymentsGetPaymentOrderResponse struct {
	// Link Ссылка на файл документа
	Link string `json:"link"`

	// Title Название документа
	Title string `json:"title"`

	// Version Версия документа
	Version string `json:"version"`
}

// SmePaymentsOtpValidateResponse defines model for SmePaymentsOtpValidateResponse.
type SmePaymentsOtpValidateResponse struct {
	// Error Описание ошибки
	Error *string `json:"error,omitempty"`

	// Success Успешность проверки кода
	Success *bool `json:"success,omitempty"`
}

// SmePaymentsWorktimeResponse defines model for SmePaymentsWorktimeResponse.
type SmePaymentsWorktimeResponse struct {
	// Date Текущая дата в системах банка (GMT+5 Astana)
	Date string `json:"date"`

	// IsDateOperational Открыт опердень или нет
	IsDateOperational bool `json:"isDateOperational"`

	// IsEndOfWorkTime Проверка на опер день после рабочего времени
	IsEndOfWorkTime bool `json:"isEndOfWorkTime"`

	// NextOperationalDate Следующий рабочий день
	NextOperationalDate *string `json:"nextOperationalDate,omitempty"`
}

// StatusResp defines model for StatusResp.
type StatusResp struct {
	ProcessingCount int `json:"processingCount"`
	TotalOrderCount int `json:"totalOrderCount"`
}

// SystemResp defines model for SystemResp.
type SystemResp struct {
	// BidNo Номер заявки заказа
	BidNo string `json:"bidNo"`

	// Buyer Имя покупателя
	Buyer string `json:"buyer"`

	// Currency Валюта
	Currency string `json:"currency"`

	// ECertNo Номер электронного сертификата
	ECertNo string `json:"eCertNo"`
	Msg     string `json:"msg"`

	// Owner Имя владельца
	Owner string `json:"owner"`

	// PName Наименование продукта
	PName string `json:"pName"`

	// PVolume Соответствующий объем
	PVolume string `json:"pVolume"`

	// Price Цена
	Price string `json:"price"`

	// PriceMYREquiv Цена продукта, эквивалентная MYR
	PriceMYREquiv string `json:"priceMYREquiv"`

	// PurchaseTimeDate Дата и время покупки
	PurchaseTimeDate string `json:"purchaseTimeDate"`
	SuccessYN        string `json:"successYN"`

	// TotalValue Итоговая сумма
	TotalValue string `json:"totalValue"`

	// ValueDate Дата валютирования
	ValueDate string `json:"valueDate"`
}

// Task defines model for Task.
type Task struct {
	CreatedAt *time.Time              `json:"created_at,omitempty"`
	Payload   *map[string]interface{} `json:"payload,omitempty"`
	Status    *string                 `json:"status,omitempty"`
	TaskID    *openapi_types.UUID     `json:"taskID,omitempty"`
	Type      *string                 `json:"type,omitempty"`
	UpdatedAt *time.Time              `json:"updated_at,omitempty"`
}

// TaxAuthorityItem defines model for TaxAuthorityItem.
type TaxAuthorityItem struct {
	Ugd *TaxAuthorityUgd `json:"ugd,omitempty"`
}

// TaxAuthorityUgd defines model for TaxAuthorityUgd.
type TaxAuthorityUgd struct {
	// Bin БИН УГД
	Bin *string `json:"bin,omitempty"`

	// Code Код УГД
	Code *string `json:"code,omitempty"`

	// Name Наименование УГД на языке локализации
	Name *string `json:"name,omitempty"`

	// RegionCode Код региона
	RegionCode *string `json:"regionCode,omitempty"`

	// RegionName Название региона
	RegionName *string `json:"regionName,omitempty"`
}

// TaxPayerType Тип налогоплательщика
type TaxPayerType string

// Term Срок кредита (в месяцах)
type Term = int

// TransactionType Внутренний тип перевода
type TransactionType string

// UpdateUserLocaleResp defines model for UpdateUserLocaleResp.
type UpdateUserLocaleResp = map[string]interface{}

// UserAccount defines model for UserAccount.
type UserAccount struct {
	// ID Идентификатор счёта
	ID     string            `json:"ID"`
	Arrest UserAccountArrest `json:"arrest"`

	// AvailableBalance Доступный баланс (balance-plansum-partiallyDebtAmount)
	AvailableBalance *float64 `json:"availableBalance,omitempty"`

	// Balance Баланс (остаток счёта)
	Balance *float64 `json:"balance,omitempty"`

	// BalanceNatval Баланс счета в нац. валюте (KZT)
	BalanceNatval *float64 `json:"balanceNatval,omitempty"`

	// ClientType Тип клиента
	ClientType UserAccountClientType `json:"clientType"`

	// CloseDate Дата закрытия счёта
	CloseDate *openapi_types.Date `json:"closeDate,omitempty"`

	// Currency Валюта счёта
	Currency UserAccountCurrency `json:"currency"`

	// DocumentNumber Номер договора
	DocumentNumber *string `json:"documentNumber,omitempty"`

	// Iban Номер счета iban
	Iban string `json:"iban"`

	// OpenDate Дата открытия счёта
	OpenDate openapi_types.Date `json:"openDate"`

	// PlanSum Плановые суммы
	PlanSum *float64 `json:"planSum,omitempty"`

	// Status Статус счёта
	Status UserAccountStatus `json:"status"`

	// Type Тип счёта
	Type UserAccountType `json:"type"`
}

// UserAccountClientType Тип клиента
type UserAccountClientType string

// UserAccountCurrency Валюта счёта
type UserAccountCurrency string

// UserAccountStatus Статус счёта
type UserAccountStatus string

// UserAccountType Тип счёта
type UserAccountType string

// UserAccountArrest defines model for UserAccountArrest.
type UserAccountArrest struct {
	// Blocking Признак полной или частичной блокировки, накладывает ограничения на определенную сумму - сумму ареста
	Blocking *bool `json:"blocking,omitempty"`
}

// UserAccountResponse defines model for UserAccountResponse.
type UserAccountResponse struct {
	Account UserAccount `json:"account"`
}

// UserAccountsSMEResponse defines model for UserAccountsSMEResponse.
type UserAccountsSMEResponse struct {
	// Accounts Счета пользователя
	Accounts []UserAccount `json:"accounts"`
}

// UserCardsResponse defines model for UserCardsResponse.
type UserCardsResponse struct {
	// Accounts Счета пользователя
	Accounts []UserAccount `json:"accounts"`
}

// ValidationError Ошибка валидации данных в теле запроса
type ValidationError struct {
	// Error Код ошибки
	Error string `json:"error"`

	// Fields Объект с описанием ошибок валидации полей
	Fields map[string]string `json:"fields"`
}

// AcceptLanguage defines model for AcceptLanguage.
type AcceptLanguage string

// AccountIDPathParam defines model for AccountIDPathParam.
type AccountIDPathParam = openapi_types.UUID

// ApplicationIDPathParam defines model for ApplicationIDPathParam.
type ApplicationIDPathParam = openapi_types.UUID

// BankFilterBics defines model for BankFilterBics.
type BankFilterBics = []string

// DocumentIDPathParam defines model for DocumentIDPathParam.
type DocumentIDPathParam = openapi_types.UUID

// EmployeeID defines model for EmployeeID.
type EmployeeID = openapi_types.UUID

// KbeKodFilterCodes defines model for KbeKodFilterCodes.
type KbeKodFilterCodes = []string

// KbeKodFilterResidency defines model for KbeKodFilterResidency.
type KbeKodFilterResidency = bool

// KbkFilterCodes defines model for KbkFilterCodes.
type KbkFilterCodes = []string

// KnpFilterCodes defines model for KnpFilterCodes.
type KnpFilterCodes = []string

// KnpFilterGroups defines model for KnpFilterGroups.
type KnpFilterGroups = []string

// PaymentsGetHistoryClientIinBin defines model for PaymentsGetHistoryClientIinBin.
type PaymentsGetHistoryClientIinBin = string

// PaymentsGetTransactionByIDTransactionID defines model for PaymentsGetTransactionByIDTransactionID.
type PaymentsGetTransactionByIDTransactionID = openapi_types.UUID

// PaymentsGetTransactionsAccounts defines model for PaymentsGetTransactionsAccounts.
type PaymentsGetTransactionsAccounts = []string

// PaymentsGetTransactionsCards defines model for PaymentsGetTransactionsCards.
type PaymentsGetTransactionsCards = []string

// PaymentsGetTransactionsCounterparty defines model for PaymentsGetTransactionsCounterparty.
type PaymentsGetTransactionsCounterparty = string

// PaymentsGetTransactionsEndDate defines model for PaymentsGetTransactionsEndDate.
type PaymentsGetTransactionsEndDate = time.Time

// PaymentsGetTransactionsLimit defines model for PaymentsGetTransactionsLimit.
type PaymentsGetTransactionsLimit = int64

// PaymentsGetTransactionsMaxAmount defines model for PaymentsGetTransactionsMaxAmount.
type PaymentsGetTransactionsMaxAmount = string

// PaymentsGetTransactionsMinAmount defines model for PaymentsGetTransactionsMinAmount.
type PaymentsGetTransactionsMinAmount = string

// PaymentsGetTransactionsOffset defines model for PaymentsGetTransactionsOffset.
type PaymentsGetTransactionsOffset = int64

// PaymentsGetTransactionsOperationType defines model for PaymentsGetTransactionsOperationType.
type PaymentsGetTransactionsOperationType string

// PaymentsGetTransactionsStartDate defines model for PaymentsGetTransactionsStartDate.
type PaymentsGetTransactionsStartDate = time.Time

// PaymentsTransactionID defines model for PaymentsTransactionID.
type PaymentsTransactionID = openapi_types.UUID

// PaymentsTransactionNumber defines model for PaymentsTransactionNumber.
type PaymentsTransactionNumber = string

// SmePaymentsOtpAttemptID defines model for SmePaymentsOtpAttemptID.
type SmePaymentsOtpAttemptID = openapi_types.UUID

// TaskCreatedAfterQueryParam defines model for TaskCreatedAfterQueryParam.
type TaskCreatedAfterQueryParam = int64

// TaskCreatedBeforeQueryParam defines model for TaskCreatedBeforeQueryParam.
type TaskCreatedBeforeQueryParam = int64

// TaskIDPathParam defines model for TaskIDPathParam.
type TaskIDPathParam = openapi_types.UUID

// TaskPageQueryParam defines model for TaskPageQueryParam.
type TaskPageQueryParam = int64

// TaskPageSizeQueryParam defines model for TaskPageSizeQueryParam.
type TaskPageSizeQueryParam = int64

// TaskStatusQueryParam defines model for TaskStatusQueryParam.
type TaskStatusQueryParam string

// TaskTypeQueryParam defines model for TaskTypeQueryParam.
type TaskTypeQueryParam = string

// TaxAuthorityFilterRegions defines model for TaxAuthorityFilterRegions.
type TaxAuthorityFilterRegions = []string

// UserAgent defines model for UserAgent.
type UserAgent = string

// RespEmpty defines model for RespEmpty.
type RespEmpty = map[string]interface{}

// AccountDocumentRequest defines model for AccountDocumentRequest.
type AccountDocumentRequest struct {
	// AccountID Идентификатор счета
	AccountID *openapi_types.UUID `json:"accountID,omitempty"`

	// Language Язык документа
	Language *string `json:"language,omitempty"`

	// PeriodFrom Начало периода (только для выписки по счету)
	PeriodFrom *openapi_types.Date `json:"periodFrom"`

	// PeriodTo Конец периода (только для выписки по счету)
	PeriodTo *openapi_types.Date `json:"periodTo"`
}

// AuthRefreshBody defines model for AuthRefreshBody.
type AuthRefreshBody struct {
	DeviceInfo DeviceInfo `json:"deviceInfo"`
	Ipinfo     *IpInfo    `json:"ipinfo,omitempty"`

	// Phone Номер телефона
	Phone PhoneNumber `json:"phone"`

	// RefreshToken Токен обновления авторизации
	RefreshToken string `json:"refreshToken"`
}

// BatchDocsOtpBody defines model for BatchDocsOtpBody.
type BatchDocsOtpBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код подтверждения
	Code string `json:"code"`

	// DocIDs Идентификаторы подписываемых документов
	DocIDs []openapi_types.UUID `json:"docIDs"`
}

// CalculationBody defines model for CalculationBody.
type CalculationBody struct {
	// Amount Значение, внесенное клиентом по сумме кредита
	Amount int `json:"amount"`

	// PurposeID ID выбранной клиентом цели
	PurposeID string `json:"purposeID"`
}

// ConfirmPaymentByAccount defines model for ConfirmPaymentByAccount.
type ConfirmPaymentByAccount struct {
	// AttemptID Идентификатор для валидации отп кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код валидации отп
	Code string `json:"code"`
}

// ConfirmSignDocumentsReqBody defines model for ConfirmSignDocumentsReqBody.
type ConfirmSignDocumentsReqBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код подтверждения
	Code string `json:"code"`
}

// CreateOrderBody defines model for CreateOrderBody.
type CreateOrderBody struct {
	Header  HeaderRequest `json:"header"`
	Request []RequestItem `json:"request"`
}

// CreatePaymentByAccount defines model for CreatePaymentByAccount.
type CreatePaymentByAccount struct {
	// ActualBeneficiary Фактический получатель (ФИО/Наименование)
	ActualBeneficiary *string `json:"actualBeneficiary,omitempty"`

	// ActualBeneficiaryBinIIN ИИН/БИН фактического получателя
	ActualBeneficiaryBinIIN *string `json:"actualBeneficiaryBinIIN,omitempty"`

	// ActualBeneficiaryCountry Страна резидентство фактического получателя
	ActualBeneficiaryCountry *string `json:"actualBeneficiaryCountry,omitempty"`

	// ActualBeneficiaryIsLegal Флаг юр лица фактического получателя
	ActualBeneficiaryIsLegal *bool `json:"actualBeneficiaryIsLegal,omitempty"`

	// ActualSender Фактический отправитель (ФИО/Наименование)
	ActualSender *string `json:"actualSender,omitempty"`

	// ActualSenderBinIIN ИИН/БИН фактического отправителя
	ActualSenderBinIIN *string `json:"actualSenderBinIIN,omitempty"`

	// ActualSenderCountry Страна резидентство фактического отправителя
	ActualSenderCountry *string `json:"actualSenderCountry,omitempty"`

	// ActualSenderIsLegal Флаг юр лица фактического отправителя
	ActualSenderIsLegal *bool `json:"actualSenderIsLegal,omitempty"`

	// Amount Сумма операции
	Amount float64 `json:"amount"`

	// BeneficiaryAccount IBAN cчета  получателя
	BeneficiaryAccount string `json:"beneficiaryAccount"`

	// BeneficiaryBank БИК банка получателя
	BeneficiaryBank string `json:"beneficiaryBank"`

	// BeneficiaryBankName Наименование банка получателя
	BeneficiaryBankName string `json:"beneficiaryBankName"`

	// BeneficiaryBinIIN ИИН/БИН получателя
	BeneficiaryBinIIN string `json:"beneficiaryBinIIN"`

	// BeneficiaryCountry Страна резидентство
	BeneficiaryCountry *string `json:"beneficiaryCountry,omitempty"`

	// BeneficiaryName ФИО/Наименование получателя
	BeneficiaryName string `json:"beneficiaryName"`

	// BeneficiaryTaxPayerType Тип налогоплательщика
	BeneficiaryTaxPayerType *TaxPayerType `json:"beneficiaryTaxPayerType,omitempty"`

	// BeneficiaryType Тип получателя
	BeneficiaryType int32 `json:"beneficiaryType"`

	// Currency Валюта операции
	Currency string `json:"currency"`

	// Date Дата операции
	Date *time.Time `json:"date,omitempty"`

	// IdempotencyID Ключ идемпотентности
	IdempotencyID string `json:"idempotencyID"`

	// Kbe Код бенефициара
	Kbe string `json:"kbe"`

	// Knp КНП, purposeCode
	Knp string `json:"knp"`

	// Kod Код отправителя
	Kod *int32 `json:"kod,omitempty"`

	// PayerAccount Счет клиента
	PayerAccount string `json:"payerAccount"`

	// PayerBinIIN ИИН/БИН отправителя
	PayerBinIIN string `json:"payerBinIIN"`

	// PayerName ФИО/Наименование
	PayerName string `json:"payerName"`

	// PaymentDetails Назначение платежа
	PaymentDetails string `json:"paymentDetails"`

	// ValueDate Дата валютирования
	ValueDate *time.Time `json:"valueDate,omitempty"`
}

// CreatePaymentRequest defines model for CreatePaymentRequest.
type CreatePaymentRequest struct {
	// IdempotencyKey Ключ идемпотентности
	IdempotencyKey string `json:"idempotencyKey"`

	// PaymentCode Код платежа
	PaymentCode string `json:"paymentCode"`

	// PaymentData Данные для платежа ОПВ (Обязательные пенсионные взносы)
	PaymentData PaymentData `json:"paymentData"`
}

// DictDocGetListByFiltersReq defines model for DictDocGetListByFiltersReq.
type DictDocGetListByFiltersReq = DictDocumentFilters

// DocumentsForSignRequest defines model for DocumentsForSignRequest.
type DocumentsForSignRequest struct {
	Currencies *[]Currency `json:"currencies,omitempty"`
}

// GetOrderBody defines model for GetOrderBody.
type GetOrderBody struct {
	Header  HeaderRequest          `json:"header"`
	Request OrderResultItemRequest `json:"request"`
}

// GetOtcBody defines model for GetOtcBody.
type GetOtcBody struct {
	Input InputRequest `json:"input"`
}

// GetSystemBody defines model for GetSystemBody.
type GetSystemBody struct {
	Header BidXMLRequestInput `json:"header"`
}

// IdentifyBody defines model for IdentifyBody.
type IdentifyBody struct {
	// Code Код BTS
	Code string `json:"code"`
}

// LoanApplicationDocumentsBody defines model for LoanApplicationDocumentsBody.
type LoanApplicationDocumentsBody struct {
	// ApplicationID Идентификатор заявки.
	ApplicationID openapi_types.UUID `json:"applicationID"`

	// DocTypes Тип (шаблон) документа
	DocTypes []LoanAppDocumentType `json:"docTypes"`
}

// LoansApplicationBody defines model for LoansApplicationBody.
type LoansApplicationBody struct {
	// Amount Сумма кредита, указанная клиентом на калькуляторе
	Amount float32 `json:"amount"`

	// JuicySessionID ID сессии в системе JUICYSCORE
	JuicySessionID string `json:"juicySessionID"`

	// PurposeID ID цели кредита, выбранная клиентом при расчете сумм
	PurposeID string `json:"purposeID"`

	// TermInterestID ID срока (и наценки) по кредиту из справочника termInterest
	TermInterestID string `json:"termInterestID"`
}

// LogInBody defines model for LogInBody.
type LogInBody struct {
	DeviceInfo DeviceInfo `json:"deviceInfo"`

	// Iin ИИН
	Iin    string  `json:"iin"`
	Ipinfo *IpInfo `json:"ipinfo,omitempty"`

	// Phone Номер телефона
	Phone PhoneNumber `json:"phone"`
}

// LogoutBody defines model for LogoutBody.
type LogoutBody struct {
	// InstallationID Идентификатор установки приложения
	InstallationID openapi_types.UUID `json:"installationID"`
}

// OtpBody defines model for OtpBody.
type OtpBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код подтверждения
	Code *string `json:"code,omitempty"`
}

// PaymentsCheckAccountIinRequest defines model for PaymentsCheckAccountIinRequest.
type PaymentsCheckAccountIinRequest struct {
	// Account Номер счёта
	Account string `json:"account"`

	// ClientIinBin ИИН/БИН клиента
	ClientIinBin string `json:"clientIinBin"`
}

// PostChangeDisbursementControlRequestBody defines model for PostChangeDisbursementControlRequestBody.
type PostChangeDisbursementControlRequestBody struct {
	// Mode Режим работы биржи
	Mode DisbursementMode `json:"mode"`

	// Token Авторизационный токен
	Token string `json:"token"`
}

// PostEarlyRepayRequestBody defines model for PostEarlyRepayRequestBody.
type PostEarlyRepayRequestBody struct {
	// RepayAmount Сумма ЧДП/ПДП
	RepayAmount EarlyRepayAmount `json:"repayAmount"`

	// RepayType ODONLY - ЧДП
	// WSHD_FULL - ПДП
	RepayType string `json:"repayType"`
}

// PostEdsBtsDataRequestBody defines model for PostEdsBtsDataRequestBody.
type PostEdsBtsDataRequestBody struct {
	// Code Код
	Code string `json:"code"`
}

// PostIdentifyBtsDataSmeRequestBody defines model for PostIdentifyBtsDataSmeRequestBody.
type PostIdentifyBtsDataSmeRequestBody struct {
	// Code Код
	Code string `json:"code"`
}

// ProfileDeleteBody defines model for ProfileDeleteBody.
type ProfileDeleteBody struct {
	// InstallationID Идентификатор установки приложения
	InstallationID openapi_types.UUID `json:"installationID"`
}

// PublishLoanAppDataRequestBody defines model for PublishLoanAppDataRequestBody.
type PublishLoanAppDataRequestBody struct {
	// BankStatements Данные прикрепленных документов
	BankStatements *[]AttachedDocData `json:"bankStatements,omitempty"`
}

// SaveSurveyBody defines model for SaveSurveyBody.
type SaveSurveyBody struct {
	// AdditionalAmount Сумма дополнительного источника дохода за последние 6 месяцев
	AdditionalAmount *string           `json:"additionalAmount,omitempty"`
	Address          SaveSurveyAddress `json:"address"`

	// ApplicationID Идентификатор заявки.
	ApplicationID string `json:"applicationID"`

	// ContactPersons Список контактных лиц.
	ContactPersons []SaveSurveyContactPerson `json:"contactPersons"`

	// EducationID Идентификатор уровня образования.
	EducationID string `json:"educationID"`

	// Email Электронная почта
	Email Email `json:"email"`

	// EmpID Идентификатор типа занятости.
	EmpID string `json:"empID"`
}

// SellBody defines model for SellBody.
type SellBody struct {
	Input InputRequest `json:"input"`
}

// SignDocumentsByIDReqBody defines model for SignDocumentsByIDReqBody.
type SignDocumentsByIDReqBody struct {
	DocIDs []openapi_types.UUID `json:"docIDs"`
}

// SmePaymentsAddEmployeeRequestBody Информация о сотруднике для добавления в ИП
type SmePaymentsAddEmployeeRequestBody struct {
	// Birthday Дата рождения сотрудника
	Birthday openapi_types.Date `json:"birthday"`

	// Country Страна
	Country string `json:"country"`

	// DisplayOrder Порядок отображения в списке
	DisplayOrder int `json:"displayOrder"`

	// EmployerIinBin ИИН/БИН организации
	EmployerIinBin string `json:"employerIinBin"`

	// Iin ИИН сотрудника
	Iin string `json:"iin"`

	// LastName Фамилия сотрудника
	LastName string `json:"lastName"`

	// MiddleName Отчество сотрудника
	MiddleName *string `json:"middleName,omitempty"`

	// Name Имя сотрудника
	Name string `json:"name"`
}

// SmePaymentsCreateOtpRequestBody defines model for SmePaymentsCreateOtpRequestBody.
type SmePaymentsCreateOtpRequestBody struct {
	DeviceInfo SmePaymentsDeviceInfo `json:"deviceInfo"`
}

// SmePaymentsOtpValidateRequestBody defines model for SmePaymentsOtpValidateRequestBody.
type SmePaymentsOtpValidateRequestBody struct {
	// Code Введенный код
	Code string `json:"code"`

	// TransactionID Идентификатор транзакции, которую подтверждаем
	TransactionID string `json:"transactionID"`
}

// SmePaymentsUpdateEmployeeRequestBody Информация о сотруднике для добавления в ИП
type SmePaymentsUpdateEmployeeRequestBody struct {
	// Birthday Дата рождения сотрудника
	Birthday openapi_types.Date `json:"birthday"`

	// Country Страна
	Country string `json:"country"`

	// DisplayOrder Порядок отображения в списке
	DisplayOrder int `json:"displayOrder"`

	// EmployerIinBin ИИН/БИН организации
	EmployerIinBin string `json:"employerIinBin"`

	// Iin ИИН сотрудника
	Iin string `json:"iin"`

	// LastName Фамилия сотрудника
	LastName string `json:"lastName"`

	// MiddleName Отчество сотрудника
	MiddleName *string `json:"middleName,omitempty"`

	// Name Имя сотрудника
	Name string `json:"name"`
}

// CreateAccountsDocumentJSONBody defines parameters for CreateAccountsDocument.
type CreateAccountsDocumentJSONBody struct {
	// AccountID Идентификатор счета
	AccountID *openapi_types.UUID `json:"accountID,omitempty"`

	// Language Язык документа
	Language *string `json:"language,omitempty"`

	// PeriodFrom Начало периода (только для выписки по счету)
	PeriodFrom *openapi_types.Date `json:"periodFrom"`

	// PeriodTo Конец периода (только для выписки по счету)
	PeriodTo *openapi_types.Date `json:"periodTo"`
}

// CreateAccountsDocumentParams defines parameters for CreateAccountsDocument.
type CreateAccountsDocumentParams struct {
	// DocType Тип запрашиваемого документа
	DocType AccountDocumentType `form:"docType" json:"docType"`
}

// DocumentsForSignJSONBody defines parameters for DocumentsForSign.
type DocumentsForSignJSONBody struct {
	Currencies *[]Currency `json:"currencies,omitempty"`
}

// GetBtsDataForAuthParams defines parameters for GetBtsDataForAuth.
type GetBtsDataForAuthParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *GetBtsDataForAuthParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// GetBtsDataForAuthParamsAcceptLanguage defines parameters for GetBtsDataForAuth.
type GetBtsDataForAuthParamsAcceptLanguage string

// AuthConfirmJSONBody defines parameters for AuthConfirm.
type AuthConfirmJSONBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код подтверждения
	Code *string `json:"code,omitempty"`
}

// DocumentForSignParams defines parameters for DocumentForSign.
type DocumentForSignParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *DocumentForSignParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// DocumentForSignParamsAcceptLanguage defines parameters for DocumentForSign.
type DocumentForSignParamsAcceptLanguage string

// AuthIdentifyJSONBody defines parameters for AuthIdentify.
type AuthIdentifyJSONBody struct {
	// Code Код BTS
	Code string `json:"code"`
}

// AuthLoginJSONBody defines parameters for AuthLogin.
type AuthLoginJSONBody struct {
	DeviceInfo DeviceInfo `json:"deviceInfo"`

	// Iin ИИН
	Iin    string  `json:"iin"`
	Ipinfo *IpInfo `json:"ipinfo,omitempty"`

	// Phone Номер телефона
	Phone PhoneNumber `json:"phone"`
}

// AuthLogoutJSONBody defines parameters for AuthLogout.
type AuthLogoutJSONBody struct {
	// InstallationID Идентификатор установки приложения
	InstallationID openapi_types.UUID `json:"installationID"`
}

// AuthRefreshJSONBody defines parameters for AuthRefresh.
type AuthRefreshJSONBody struct {
	DeviceInfo DeviceInfo `json:"deviceInfo"`
	Ipinfo     *IpInfo    `json:"ipinfo,omitempty"`

	// Phone Номер телефона
	Phone PhoneNumber `json:"phone"`

	// RefreshToken Токен обновления авторизации
	RefreshToken string `json:"refreshToken"`
}

// DictGetLocationsParams defines parameters for DictGetLocations.
type DictGetLocationsParams struct {
	// ParentID Идентификатор родительской локации. 0 - для получения корневых локаций. Не требуется если указан параметр parentIds
	ParentID *int32 `form:"parentID,omitempty" json:"parentID,omitempty"`

	// ParentIds Идентификаторы родительских локаций. Не требуется если указан параметр parentID
	ParentIds *[]int32 `form:"parentIds,omitempty" json:"parentIds,omitempty"`

	// AcceptLanguage Язык запроса
	AcceptLanguage *DictGetLocationsParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// DictGetLocationsParamsAcceptLanguage defines parameters for DictGetLocations.
type DictGetLocationsParamsAcceptLanguage string

// ConfirmSignDocumentsBatchJSONBody defines parameters for ConfirmSignDocumentsBatch.
type ConfirmSignDocumentsBatchJSONBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код подтверждения
	Code string `json:"code"`

	// DocIDs Идентификаторы подписываемых документов
	DocIDs []openapi_types.UUID `json:"docIDs"`
}

// ConfirmSignDocumentsBatchParams defines parameters for ConfirmSignDocumentsBatch.
type ConfirmSignDocumentsBatchParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *ConfirmSignDocumentsBatchParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// ConfirmSignDocumentsBatchParamsAcceptLanguage defines parameters for ConfirmSignDocumentsBatch.
type ConfirmSignDocumentsBatchParamsAcceptLanguage string

// CreateOrderBsasJSONBody defines parameters for CreateOrderBsas.
type CreateOrderBsasJSONBody struct {
	Header  HeaderRequest `json:"header"`
	Request []RequestItem `json:"request"`
}

// GetOrderBsasJSONBody defines parameters for GetOrderBsas.
type GetOrderBsasJSONBody struct {
	Header  HeaderRequest          `json:"header"`
	Request OrderResultItemRequest `json:"request"`
}

// GetOtcBsasJSONBody defines parameters for GetOtcBsas.
type GetOtcBsasJSONBody struct {
	Input InputRequest `json:"input"`
}

// GetSystemBsasJSONBody defines parameters for GetSystemBsas.
type GetSystemBsasJSONBody struct {
	Header BidXMLRequestInput `json:"header"`
}

// GetSellBsasJSONBody defines parameters for GetSellBsas.
type GetSellBsasJSONBody struct {
	Input InputRequest `json:"input"`
}

// RequestDocumentPublicParams defines parameters for RequestDocumentPublic.
type RequestDocumentPublicParams struct {
	// DocType Тип запрашиваемого документа
	DocType PublicDocumentType `form:"docType" json:"docType"`

	// AcceptLanguage Язык запроса
	AcceptLanguage *RequestDocumentPublicParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// RequestDocumentPublicParamsAcceptLanguage defines parameters for RequestDocumentPublic.
type RequestDocumentPublicParamsAcceptLanguage string

// SignDocumentByIDsJSONBody defines parameters for SignDocumentByIDs.
type SignDocumentByIDsJSONBody struct {
	DocIDs []openapi_types.UUID `json:"docIDs"`
}

// SignDocumentByIDsParams defines parameters for SignDocumentByIDs.
type SignDocumentByIDsParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *SignDocumentByIDsParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// SignDocumentByIDsParamsAcceptLanguage defines parameters for SignDocumentByIDs.
type SignDocumentByIDsParamsAcceptLanguage string

// ConfirmSignDocumentsJSONBody defines parameters for ConfirmSignDocuments.
type ConfirmSignDocumentsJSONBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код подтверждения
	Code string `json:"code"`
}

// ConfirmSignDocumentsParams defines parameters for ConfirmSignDocuments.
type ConfirmSignDocumentsParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *ConfirmSignDocumentsParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// ConfirmSignDocumentsParamsAcceptLanguage defines parameters for ConfirmSignDocuments.
type ConfirmSignDocumentsParamsAcceptLanguage string

// GetDocumentByIDParams defines parameters for GetDocumentByID.
type GetDocumentByIDParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *GetDocumentByIDParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// GetDocumentByIDParamsAcceptLanguage defines parameters for GetDocumentByID.
type GetDocumentByIDParamsAcceptLanguage string

// SignDocumentByIDParams defines parameters for SignDocumentByID.
type SignDocumentByIDParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *SignDocumentByIDParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// SignDocumentByIDParamsAcceptLanguage defines parameters for SignDocumentByID.
type SignDocumentByIDParamsAcceptLanguage string

// ConfirmSignDocumentByIDJSONBody defines parameters for ConfirmSignDocumentByID.
type ConfirmSignDocumentByIDJSONBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код подтверждения
	Code *string `json:"code,omitempty"`
}

// ConfirmSignDocumentByIDParams defines parameters for ConfirmSignDocumentByID.
type ConfirmSignDocumentByIDParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *ConfirmSignDocumentByIDParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// ConfirmSignDocumentByIDParamsAcceptLanguage defines parameters for ConfirmSignDocumentByID.
type ConfirmSignDocumentByIDParamsAcceptLanguage string

// CreateLoanApplicationJSONBody defines parameters for CreateLoanApplication.
type CreateLoanApplicationJSONBody struct {
	// Amount Сумма кредита, указанная клиентом на калькуляторе
	Amount float32 `json:"amount"`

	// JuicySessionID ID сессии в системе JUICYSCORE
	JuicySessionID string `json:"juicySessionID"`

	// PurposeID ID цели кредита, выбранная клиентом при расчете сумм
	PurposeID string `json:"purposeID"`

	// TermInterestID ID срока (и наценки) по кредиту из справочника termInterest
	TermInterestID string `json:"termInterestID"`
}

// CalculateLoanTermsJSONBody defines parameters for CalculateLoanTerms.
type CalculateLoanTermsJSONBody struct {
	// Amount Значение, внесенное клиентом по сумме кредита
	Amount int `json:"amount"`

	// PurposeID ID выбранной клиентом цели
	PurposeID string `json:"purposeID"`
}

// ChangeDisbursementControlJSONBody defines parameters for ChangeDisbursementControl.
type ChangeDisbursementControlJSONBody struct {
	// Mode Режим работы биржи
	Mode DisbursementMode `json:"mode"`

	// Token Авторизационный токен
	Token string `json:"token"`
}

// DocumentsForLoanAppJSONBody defines parameters for DocumentsForLoanApp.
type DocumentsForLoanAppJSONBody struct {
	// ApplicationID Идентификатор заявки.
	ApplicationID openapi_types.UUID `json:"applicationID"`

	// DocTypes Тип (шаблон) документа
	DocTypes []LoanAppDocumentType `json:"docTypes"`
}

// DocumentsForLoanAppParams defines parameters for DocumentsForLoanApp.
type DocumentsForLoanAppParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *DocumentsForLoanAppParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// DocumentsForLoanAppParamsAcceptLanguage defines parameters for DocumentsForLoanApp.
type DocumentsForLoanAppParamsAcceptLanguage string

// GetLoanSurveyParams defines parameters for GetLoanSurvey.
type GetLoanSurveyParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *GetLoanSurveyParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// GetLoanSurveyParamsAcceptLanguage defines parameters for GetLoanSurvey.
type GetLoanSurveyParamsAcceptLanguage string

// SaveLoanSurveyJSONBody defines parameters for SaveLoanSurvey.
type SaveLoanSurveyJSONBody struct {
	// AdditionalAmount Сумма дополнительного источника дохода за последние 6 месяцев
	AdditionalAmount *string           `json:"additionalAmount,omitempty"`
	Address          SaveSurveyAddress `json:"address"`

	// ApplicationID Идентификатор заявки.
	ApplicationID string `json:"applicationID"`

	// ContactPersons Список контактных лиц.
	ContactPersons []SaveSurveyContactPerson `json:"contactPersons"`

	// EducationID Идентификатор уровня образования.
	EducationID string `json:"educationID"`

	// Email Электронная почта
	Email Email `json:"email"`

	// EmpID Идентификатор типа занятости.
	EmpID string `json:"empID"`
}

// SaveLoanSurveyParams defines parameters for SaveLoanSurvey.
type SaveLoanSurveyParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *SaveLoanSurveyParamsAcceptLanguage `json:"Accept-Language,omitempty"`

	// UserAgent Юзер агент
	UserAgent UserAgent `json:"User-Agent"`
}

// SaveLoanSurveyParamsAcceptLanguage defines parameters for SaveLoanSurvey.
type SaveLoanSurveyParamsAcceptLanguage string

// GetBtsDataForLoanAppParams defines parameters for GetBtsDataForLoanApp.
type GetBtsDataForLoanAppParams struct {
	Type string `form:"type" json:"type"`

	// AcceptLanguage Язык запроса
	AcceptLanguage GetBtsDataForLoanAppParamsAcceptLanguage `json:"Accept-Language"`
}

// GetBtsDataForLoanAppParamsAcceptLanguage defines parameters for GetBtsDataForLoanApp.
type GetBtsDataForLoanAppParamsAcceptLanguage string

// GetLoansDetailsParams defines parameters for GetLoansDetails.
type GetLoansDetailsParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *GetLoansDetailsParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// GetLoansDetailsParamsAcceptLanguage defines parameters for GetLoansDetails.
type GetLoansDetailsParamsAcceptLanguage string

// LoansPostEarlyRepayJSONBody defines parameters for LoansPostEarlyRepay.
type LoansPostEarlyRepayJSONBody struct {
	// RepayAmount Сумма ЧДП/ПДП
	RepayAmount EarlyRepayAmount `json:"repayAmount"`

	// RepayType ODONLY - ЧДП
	// WSHD_FULL - ПДП
	RepayType string `json:"repayType"`
}

// LoansPostEdsBtsDataJSONBody defines parameters for LoansPostEdsBtsData.
type LoansPostEdsBtsDataJSONBody struct {
	// Code Код
	Code string `json:"code"`
}

// LoansPostEdsBtsDataParams defines parameters for LoansPostEdsBtsData.
type LoansPostEdsBtsDataParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *LoansPostEdsBtsDataParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// LoansPostEdsBtsDataParamsAcceptLanguage defines parameters for LoansPostEdsBtsData.
type LoansPostEdsBtsDataParamsAcceptLanguage string

// LoansPostIdentifyBtsDataSmeJSONBody defines parameters for LoansPostIdentifyBtsDataSme.
type LoansPostIdentifyBtsDataSmeJSONBody struct {
	// Code Код
	Code string `json:"code"`
}

// LoansPostIdentifyBtsDataSmeParams defines parameters for LoansPostIdentifyBtsDataSme.
type LoansPostIdentifyBtsDataSmeParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *LoansPostIdentifyBtsDataSmeParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// LoansPostIdentifyBtsDataSmeParamsAcceptLanguage defines parameters for LoansPostIdentifyBtsDataSme.
type LoansPostIdentifyBtsDataSmeParamsAcceptLanguage string

// PublishLoanAppDataJSONBody defines parameters for PublishLoanAppData.
type PublishLoanAppDataJSONBody struct {
	// BankStatements Данные прикрепленных документов
	BankStatements *[]AttachedDocData `json:"bankStatements,omitempty"`
}

// GetScoringResultParams defines parameters for GetScoringResult.
type GetScoringResultParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage GetScoringResultParamsAcceptLanguage `json:"Accept-Language"`
}

// GetScoringResultParamsAcceptLanguage defines parameters for GetScoringResult.
type GetScoringResultParamsAcceptLanguage string

// LoansConfirmSignDocumentByIDJSONBody defines parameters for LoansConfirmSignDocumentByID.
type LoansConfirmSignDocumentByIDJSONBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код подтверждения
	Code *string `json:"code,omitempty"`
}

// LoansConfirmSignDocumentByIDParams defines parameters for LoansConfirmSignDocumentByID.
type LoansConfirmSignDocumentByIDParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *LoansConfirmSignDocumentByIDParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// LoansConfirmSignDocumentByIDParamsAcceptLanguage defines parameters for LoansConfirmSignDocumentByID.
type LoansConfirmSignDocumentByIDParamsAcceptLanguage string

// OtpRetryJSONBody defines parameters for OtpRetry.
type OtpRetryJSONBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код подтверждения
	Code *string `json:"code,omitempty"`
}

// PaymentsCheckAccountIinJSONBody defines parameters for PaymentsCheckAccountIin.
type PaymentsCheckAccountIinJSONBody struct {
	// Account Номер счёта
	Account string `json:"account"`

	// ClientIinBin ИИН/БИН клиента
	ClientIinBin string `json:"clientIinBin"`
}

// ConfirmPaymentByAccountJSONBody defines parameters for ConfirmPaymentByAccount.
type ConfirmPaymentByAccountJSONBody struct {
	// AttemptID Идентификатор для валидации отп кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код валидации отп
	Code string `json:"code"`
}

// CreatePaymentByAccountJSONBody defines parameters for CreatePaymentByAccount.
type CreatePaymentByAccountJSONBody struct {
	// ActualBeneficiary Фактический получатель (ФИО/Наименование)
	ActualBeneficiary *string `json:"actualBeneficiary,omitempty"`

	// ActualBeneficiaryBinIIN ИИН/БИН фактического получателя
	ActualBeneficiaryBinIIN *string `json:"actualBeneficiaryBinIIN,omitempty"`

	// ActualBeneficiaryCountry Страна резидентство фактического получателя
	ActualBeneficiaryCountry *string `json:"actualBeneficiaryCountry,omitempty"`

	// ActualBeneficiaryIsLegal Флаг юр лица фактического получателя
	ActualBeneficiaryIsLegal *bool `json:"actualBeneficiaryIsLegal,omitempty"`

	// ActualSender Фактический отправитель (ФИО/Наименование)
	ActualSender *string `json:"actualSender,omitempty"`

	// ActualSenderBinIIN ИИН/БИН фактического отправителя
	ActualSenderBinIIN *string `json:"actualSenderBinIIN,omitempty"`

	// ActualSenderCountry Страна резидентство фактического отправителя
	ActualSenderCountry *string `json:"actualSenderCountry,omitempty"`

	// ActualSenderIsLegal Флаг юр лица фактического отправителя
	ActualSenderIsLegal *bool `json:"actualSenderIsLegal,omitempty"`

	// Amount Сумма операции
	Amount float64 `json:"amount"`

	// BeneficiaryAccount IBAN cчета  получателя
	BeneficiaryAccount string `json:"beneficiaryAccount"`

	// BeneficiaryBank БИК банка получателя
	BeneficiaryBank string `json:"beneficiaryBank"`

	// BeneficiaryBankName Наименование банка получателя
	BeneficiaryBankName string `json:"beneficiaryBankName"`

	// BeneficiaryBinIIN ИИН/БИН получателя
	BeneficiaryBinIIN string `json:"beneficiaryBinIIN"`

	// BeneficiaryCountry Страна резидентство
	BeneficiaryCountry *string `json:"beneficiaryCountry,omitempty"`

	// BeneficiaryName ФИО/Наименование получателя
	BeneficiaryName string `json:"beneficiaryName"`

	// BeneficiaryTaxPayerType Тип налогоплательщика
	BeneficiaryTaxPayerType *TaxPayerType `json:"beneficiaryTaxPayerType,omitempty"`

	// BeneficiaryType Тип получателя
	BeneficiaryType int32 `json:"beneficiaryType"`

	// Currency Валюта операции
	Currency string `json:"currency"`

	// Date Дата операции
	Date *time.Time `json:"date,omitempty"`

	// IdempotencyID Ключ идемпотентности
	IdempotencyID string `json:"idempotencyID"`

	// Kbe Код бенефициара
	Kbe string `json:"kbe"`

	// Knp КНП, purposeCode
	Knp string `json:"knp"`

	// Kod Код отправителя
	Kod *int32 `json:"kod,omitempty"`

	// PayerAccount Счет клиента
	PayerAccount string `json:"payerAccount"`

	// PayerBinIIN ИИН/БИН отправителя
	PayerBinIIN string `json:"payerBinIIN"`

	// PayerName ФИО/Наименование
	PayerName string `json:"payerName"`

	// PaymentDetails Назначение платежа
	PaymentDetails string `json:"paymentDetails"`

	// ValueDate Дата валютирования
	ValueDate *time.Time `json:"valueDate,omitempty"`
}

// GetPaymentHistoryParams defines parameters for GetPaymentHistory.
type GetPaymentHistoryParams struct {
	// ClientIinBin ИИН/БИН клиента
	ClientIinBin PaymentsGetHistoryClientIinBin `form:"clientIinBin" json:"clientIinBin"`

	// Limit Лимит количества возвращаемых транзакций
	Limit *PaymentsGetTransactionsLimit `form:"limit,omitempty" json:"limit,omitempty"`

	// Offset Смещение для пагинации
	Offset *PaymentsGetTransactionsOffset `form:"offset,omitempty" json:"offset,omitempty"`
}

// GetTransactionsParams defines parameters for GetTransactions.
type GetTransactionsParams struct {
	// StartDate Начальная дата для фильтрации транзакций
	StartDate *PaymentsGetTransactionsStartDate `form:"startDate,omitempty" json:"startDate,omitempty"`

	// EndDate Конечная дата для фильтрации транзакций
	EndDate *PaymentsGetTransactionsEndDate `form:"endDate,omitempty" json:"endDate,omitempty"`

	// Accounts Список номеров счетов для фильтрации транзакций
	Accounts PaymentsGetTransactionsAccounts `form:"accounts" json:"accounts"`

	// Cards Список карт для фильтрации транзакций
	Cards *PaymentsGetTransactionsCards `form:"cards,omitempty" json:"cards,omitempty"`

	// Direction Тип операции (например, CREDIT или DEBIT)
	Direction *GetTransactionsParamsDirection `form:"direction,omitempty" json:"direction,omitempty"`

	// Counterparty Контрагент для фильтрации (получатель или отправитель средств)
	Counterparty *PaymentsGetTransactionsCounterparty `form:"counterparty,omitempty" json:"counterparty,omitempty"`

	// MinAmount Минимальная сумма транзакции
	MinAmount *PaymentsGetTransactionsMinAmount `form:"minAmount,omitempty" json:"minAmount,omitempty"`

	// MaxAmount Максимальная сумма транзакции
	MaxAmount *PaymentsGetTransactionsMaxAmount `form:"maxAmount,omitempty" json:"maxAmount,omitempty"`

	// Limit Лимит количества возвращаемых транзакций
	Limit *PaymentsGetTransactionsLimit `form:"limit,omitempty" json:"limit,omitempty"`

	// Offset Смещение для пагинации
	Offset *PaymentsGetTransactionsOffset `form:"offset,omitempty" json:"offset,omitempty"`
}

// GetTransactionsParamsDirection defines parameters for GetTransactions.
type GetTransactionsParamsDirection string

// GetBankListParams defines parameters for GetBankList.
type GetBankListParams struct {
	// FilterBics Список БИК кодов банков для фильтрации
	FilterBics *BankFilterBics `form:"filter.bics,omitempty" json:"filter.bics,omitempty"`
}

// GetKbeKodListParams defines parameters for GetKbeKodList.
type GetKbeKodListParams struct {
	// FilterCodes Список кодов для фильтрации
	FilterCodes *KbeKodFilterCodes `form:"filter.codes,omitempty" json:"filter.codes,omitempty"`

	// FilterResidency Признак резидента для фильтрации
	FilterResidency *KbeKodFilterResidency `form:"filter.residency,omitempty" json:"filter.residency,omitempty"`
}

// GetKbkListParams defines parameters for GetKbkList.
type GetKbkListParams struct {
	// FilterCodes Список кодов КБК для фильтрации
	FilterCodes *KbkFilterCodes `form:"filter.codes,omitempty" json:"filter.codes,omitempty"`
}

// GetKnpListParams defines parameters for GetKnpList.
type GetKnpListParams struct {
	// FilterCodes Список кодов КНП для фильтрации
	FilterCodes *KnpFilterCodes `form:"filter.codes,omitempty" json:"filter.codes,omitempty"`

	// FilterGroups Список кодов групп КНП для фильтрации
	FilterGroups *KnpFilterGroups `form:"filter.groups,omitempty" json:"filter.groups,omitempty"`
}

// GetTaxAuthorityListParams defines parameters for GetTaxAuthorityList.
type GetTaxAuthorityListParams struct {
	// FilterRegions Список кодов регионов УГД для фильтрации
	FilterRegions *TaxAuthorityFilterRegions `form:"filter.regions,omitempty" json:"filter.regions,omitempty"`
}

// SmePaymentsCreateEmployeeJSONBody defines parameters for SmePaymentsCreateEmployee.
type SmePaymentsCreateEmployeeJSONBody struct {
	// Birthday Дата рождения сотрудника
	Birthday openapi_types.Date `json:"birthday"`

	// Country Страна
	Country string `json:"country"`

	// DisplayOrder Порядок отображения в списке
	DisplayOrder int `json:"displayOrder"`

	// EmployerIinBin ИИН/БИН организации
	EmployerIinBin string `json:"employerIinBin"`

	// Iin ИИН сотрудника
	Iin string `json:"iin"`

	// LastName Фамилия сотрудника
	LastName string `json:"lastName"`

	// MiddleName Отчество сотрудника
	MiddleName *string `json:"middleName,omitempty"`

	// Name Имя сотрудника
	Name string `json:"name"`
}

// SmePaymentsUpdateEmployeeByIDJSONBody defines parameters for SmePaymentsUpdateEmployeeByID.
type SmePaymentsUpdateEmployeeByIDJSONBody struct {
	// Birthday Дата рождения сотрудника
	Birthday openapi_types.Date `json:"birthday"`

	// Country Страна
	Country string `json:"country"`

	// DisplayOrder Порядок отображения в списке
	DisplayOrder int `json:"displayOrder"`

	// EmployerIinBin ИИН/БИН организации
	EmployerIinBin string `json:"employerIinBin"`

	// Iin ИИН сотрудника
	Iin string `json:"iin"`

	// LastName Фамилия сотрудника
	LastName string `json:"lastName"`

	// MiddleName Отчество сотрудника
	MiddleName *string `json:"middleName,omitempty"`

	// Name Имя сотрудника
	Name string `json:"name"`
}

// SmePaymentsCreateOtpJSONBody defines parameters for SmePaymentsCreateOtp.
type SmePaymentsCreateOtpJSONBody struct {
	DeviceInfo SmePaymentsDeviceInfo `json:"deviceInfo"`
}

// SmePaymentsOtpValidateJSONBody defines parameters for SmePaymentsOtpValidate.
type SmePaymentsOtpValidateJSONBody struct {
	// Code Введенный код
	Code string `json:"code"`

	// TransactionID Идентификатор транзакции, которую подтверждаем
	TransactionID string `json:"transactionID"`
}

// CreatePaymentJSONBody defines parameters for CreatePayment.
type CreatePaymentJSONBody struct {
	// IdempotencyKey Ключ идемпотентности
	IdempotencyKey string `json:"idempotencyKey"`

	// PaymentCode Код платежа
	PaymentCode string `json:"paymentCode"`

	// PaymentData Данные для платежа ОПВ (Обязательные пенсионные взносы)
	PaymentData PaymentData `json:"paymentData"`
}

// GetTasksParams defines parameters for GetTasks.
type GetTasksParams struct {
	// Status Статус задачи
	Status *GetTasksParamsStatus `form:"status,omitempty" json:"status,omitempty"`

	// Type Тип задачи
	Type *TaskTypeQueryParam `form:"type,omitempty" json:"type,omitempty"`

	// CreatedAfter Дата создания после (timestamp)
	CreatedAfter *TaskCreatedAfterQueryParam `form:"created_after,omitempty" json:"created_after,omitempty"`

	// CreatedBefore Дата создания до (timestamp)
	CreatedBefore *TaskCreatedBeforeQueryParam `form:"created_before,omitempty" json:"created_before,omitempty"`

	// Page Номер страницы
	Page *TaskPageQueryParam `form:"page,omitempty" json:"page,omitempty"`

	// PageSize Размер страницы
	PageSize *TaskPageSizeQueryParam `form:"page_size,omitempty" json:"page_size,omitempty"`
}

// GetTasksParamsStatus defines parameters for GetTasks.
type GetTasksParamsStatus string

// GetLoansParams defines parameters for GetLoans.
type GetLoansParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage GetLoansParamsAcceptLanguage `json:"Accept-Language"`
}

// GetLoansParamsAcceptLanguage defines parameters for GetLoans.
type GetLoansParamsAcceptLanguage string

// UsersUpdateUserLocaleParams defines parameters for UsersUpdateUserLocale.
type UsersUpdateUserLocaleParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage UsersUpdateUserLocaleParamsAcceptLanguage `json:"Accept-Language"`
}

// UsersUpdateUserLocaleParamsAcceptLanguage defines parameters for UsersUpdateUserLocale.
type UsersUpdateUserLocaleParamsAcceptLanguage string

// ProfileDeleteJSONBody defines parameters for ProfileDelete.
type ProfileDeleteJSONBody struct {
	// InstallationID Идентификатор установки приложения
	InstallationID openapi_types.UUID `json:"installationID"`
}

// CreateAccountsDocumentJSONRequestBody defines body for CreateAccountsDocument for application/json ContentType.
type CreateAccountsDocumentJSONRequestBody CreateAccountsDocumentJSONBody

// DocumentsForSignJSONRequestBody defines body for DocumentsForSign for application/json ContentType.
type DocumentsForSignJSONRequestBody DocumentsForSignJSONBody

// AuthConfirmJSONRequestBody defines body for AuthConfirm for application/json ContentType.
type AuthConfirmJSONRequestBody AuthConfirmJSONBody

// AuthIdentifyJSONRequestBody defines body for AuthIdentify for application/json ContentType.
type AuthIdentifyJSONRequestBody AuthIdentifyJSONBody

// AuthLoginJSONRequestBody defines body for AuthLogin for application/json ContentType.
type AuthLoginJSONRequestBody AuthLoginJSONBody

// AuthLogoutJSONRequestBody defines body for AuthLogout for application/json ContentType.
type AuthLogoutJSONRequestBody AuthLogoutJSONBody

// AuthRefreshJSONRequestBody defines body for AuthRefresh for application/json ContentType.
type AuthRefreshJSONRequestBody AuthRefreshJSONBody

// DictDocGetListByFiltersJSONRequestBody defines body for DictDocGetListByFilters for application/json ContentType.
type DictDocGetListByFiltersJSONRequestBody = DictDocumentFilters

// ConfirmSignDocumentsBatchJSONRequestBody defines body for ConfirmSignDocumentsBatch for application/json ContentType.
type ConfirmSignDocumentsBatchJSONRequestBody ConfirmSignDocumentsBatchJSONBody

// CreateOrderBsasJSONRequestBody defines body for CreateOrderBsas for application/json ContentType.
type CreateOrderBsasJSONRequestBody CreateOrderBsasJSONBody

// GetOrderBsasJSONRequestBody defines body for GetOrderBsas for application/json ContentType.
type GetOrderBsasJSONRequestBody GetOrderBsasJSONBody

// GetOtcBsasJSONRequestBody defines body for GetOtcBsas for application/json ContentType.
type GetOtcBsasJSONRequestBody GetOtcBsasJSONBody

// GetSystemBsasJSONRequestBody defines body for GetSystemBsas for application/json ContentType.
type GetSystemBsasJSONRequestBody GetSystemBsasJSONBody

// GetSellBsasJSONRequestBody defines body for GetSellBsas for application/json ContentType.
type GetSellBsasJSONRequestBody GetSellBsasJSONBody

// SignDocumentByIDsJSONRequestBody defines body for SignDocumentByIDs for application/json ContentType.
type SignDocumentByIDsJSONRequestBody SignDocumentByIDsJSONBody

// ConfirmSignDocumentsJSONRequestBody defines body for ConfirmSignDocuments for application/json ContentType.
type ConfirmSignDocumentsJSONRequestBody ConfirmSignDocumentsJSONBody

// ConfirmSignDocumentByIDJSONRequestBody defines body for ConfirmSignDocumentByID for application/json ContentType.
type ConfirmSignDocumentByIDJSONRequestBody ConfirmSignDocumentByIDJSONBody

// PostUnsecurePdfFileMultipartRequestBody defines body for PostUnsecurePdfFile for multipart/form-data ContentType.
type PostUnsecurePdfFileMultipartRequestBody = FileUploadRequestBody

// FaConversionSumJSONRequestBody defines body for FaConversionSum for application/json ContentType.
type FaConversionSumJSONRequestBody = ConversionSumRequestBody

// CreateConvertationJSONRequestBody defines body for CreateConvertation for application/json ContentType.
type CreateConvertationJSONRequestBody = CreateConvertationRequest

// CreateLoanApplicationJSONRequestBody defines body for CreateLoanApplication for application/json ContentType.
type CreateLoanApplicationJSONRequestBody CreateLoanApplicationJSONBody

// CalculateLoanTermsJSONRequestBody defines body for CalculateLoanTerms for application/json ContentType.
type CalculateLoanTermsJSONRequestBody CalculateLoanTermsJSONBody

// ChangeDisbursementControlJSONRequestBody defines body for ChangeDisbursementControl for application/json ContentType.
type ChangeDisbursementControlJSONRequestBody ChangeDisbursementControlJSONBody

// DocumentsForLoanAppJSONRequestBody defines body for DocumentsForLoanApp for application/json ContentType.
type DocumentsForLoanAppJSONRequestBody DocumentsForLoanAppJSONBody

// SaveLoanSurveyJSONRequestBody defines body for SaveLoanSurvey for application/json ContentType.
type SaveLoanSurveyJSONRequestBody SaveLoanSurveyJSONBody

// LoansPostEarlyRepayJSONRequestBody defines body for LoansPostEarlyRepay for application/json ContentType.
type LoansPostEarlyRepayJSONRequestBody LoansPostEarlyRepayJSONBody

// LoansPostEdsBtsDataJSONRequestBody defines body for LoansPostEdsBtsData for application/json ContentType.
type LoansPostEdsBtsDataJSONRequestBody LoansPostEdsBtsDataJSONBody

// LoansPostIdentifyBtsDataSmeJSONRequestBody defines body for LoansPostIdentifyBtsDataSme for application/json ContentType.
type LoansPostIdentifyBtsDataSmeJSONRequestBody LoansPostIdentifyBtsDataSmeJSONBody

// PublishLoanAppDataJSONRequestBody defines body for PublishLoanAppData for application/json ContentType.
type PublishLoanAppDataJSONRequestBody PublishLoanAppDataJSONBody

// LoansConfirmSignDocumentByIDJSONRequestBody defines body for LoansConfirmSignDocumentByID for application/json ContentType.
type LoansConfirmSignDocumentByIDJSONRequestBody LoansConfirmSignDocumentByIDJSONBody

// OtpRetryJSONRequestBody defines body for OtpRetry for application/json ContentType.
type OtpRetryJSONRequestBody OtpRetryJSONBody

// PaymentsCheckAccountIinJSONRequestBody defines body for PaymentsCheckAccountIin for application/json ContentType.
type PaymentsCheckAccountIinJSONRequestBody PaymentsCheckAccountIinJSONBody

// ConfirmPaymentByAccountJSONRequestBody defines body for ConfirmPaymentByAccount for application/json ContentType.
type ConfirmPaymentByAccountJSONRequestBody ConfirmPaymentByAccountJSONBody

// CreatePaymentByAccountJSONRequestBody defines body for CreatePaymentByAccount for application/json ContentType.
type CreatePaymentByAccountJSONRequestBody CreatePaymentByAccountJSONBody

// SmePaymentsCreateEmployeeJSONRequestBody defines body for SmePaymentsCreateEmployee for application/json ContentType.
type SmePaymentsCreateEmployeeJSONRequestBody SmePaymentsCreateEmployeeJSONBody

// SmePaymentsUpdateEmployeeByIDJSONRequestBody defines body for SmePaymentsUpdateEmployeeByID for application/json ContentType.
type SmePaymentsUpdateEmployeeByIDJSONRequestBody SmePaymentsUpdateEmployeeByIDJSONBody

// SmePaymentsCreateOtpJSONRequestBody defines body for SmePaymentsCreateOtp for application/json ContentType.
type SmePaymentsCreateOtpJSONRequestBody SmePaymentsCreateOtpJSONBody

// SmePaymentsOtpValidateJSONRequestBody defines body for SmePaymentsOtpValidate for application/json ContentType.
type SmePaymentsOtpValidateJSONRequestBody SmePaymentsOtpValidateJSONBody

// CreatePaymentJSONRequestBody defines body for CreatePayment for application/json ContentType.
type CreatePaymentJSONRequestBody CreatePaymentJSONBody

// ProfileDeleteJSONRequestBody defines body for ProfileDelete for application/json ContentType.
type ProfileDeleteJSONRequestBody ProfileDeleteJSONBody

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Метод для получения документов из счетов
	// (POST /accounts/documents)
	CreateAccountsDocument(w http.ResponseWriter, r *http.Request, params CreateAccountsDocumentParams)
	// Проверка клиента при открытии доп.счета ИП
	// (POST /accounts/open/client-verification)
	ClientVerification(w http.ResponseWriter, r *http.Request)
	// Доступные к открытию счета пользователя в валютах
	// (GET /accounts/open/currency-check)
	CurrencyCheck(w http.ResponseWriter, r *http.Request)
	// Получение документов на подписание при открытии счета
	// (POST /accounts/open/documents-for-sign)
	DocumentsForSign(w http.ResponseWriter, r *http.Request)
	// Метод для получения ссылки liveness
	// (POST /auth/bts-data)
	GetBtsDataForAuth(w http.ResponseWriter, r *http.Request, params GetBtsDataForAuthParams)
	// Подтверждение авторизации
	// (POST /auth/confirm)
	AuthConfirm(w http.ResponseWriter, r *http.Request)
	// Метод для генерации и получения документа для подписи
	// (POST /auth/document-for-sign)
	DocumentForSign(w http.ResponseWriter, r *http.Request, params DocumentForSignParams)
	// Идентификация пользователя
	// (POST /auth/identify)
	AuthIdentify(w http.ResponseWriter, r *http.Request)
	// Авторизация пользователя по телефону
	// (POST /auth/login)
	AuthLogin(w http.ResponseWriter, r *http.Request)
	// Выход из системы
	// (POST /auth/logout)
	AuthLogout(w http.ResponseWriter, r *http.Request)
	// Обновление токенов пользователя
	// (POST /auth/refresh)
	AuthRefresh(w http.ResponseWriter, r *http.Request)
	// Просмотр списка документов справочника по фильтрам данных
	// (POST /dict/doc/by-filters)
	DictDocGetListByFilters(w http.ResponseWriter, r *http.Request)
	// Получение локаций по родительским локациям
	// (GET /dictionaries/locations)
	DictGetLocations(w http.ResponseWriter, r *http.Request, params DictGetLocationsParams)
	// Подтверждение подписи нескольких документов через ОТП
	// (POST /documents/batch-sign-confirm)
	ConfirmSignDocumentsBatch(w http.ResponseWriter, r *http.Request, params ConfirmSignDocumentsBatchParams)
	// Метод ввода и обработки новых заказов в BSAS
	// (POST /documents/bsas-create-order)
	CreateOrderBsas(w http.ResponseWriter, r *http.Request)
	// Метод получения результата обработки заказа в BSAS
	// (POST /documents/bsas-get-order)
	GetOrderBsas(w http.ResponseWriter, r *http.Request)
	// Метод получения сведений об ОТС информации из системы
	// (POST /documents/bsas-get-otc)
	GetOtcBsas(w http.ResponseWriter, r *http.Request)
	// Получение сведений о сопоставлении из BSAS
	// (POST /documents/bsas-get-system)
	GetSystemBsas(w http.ResponseWriter, r *http.Request)
	// Метод получения сведений о продажах из системы
	// (POST /documents/bsas-sell)
	GetSellBsas(w http.ResponseWriter, r *http.Request)
	// Метод для получения документов, доступных для просмотра всем
	// (POST /documents/public)
	RequestDocumentPublic(w http.ResponseWriter, r *http.Request, params RequestDocumentPublicParams)
	// Отправка ОТП для подтверждения подписания
	// (POST /documents/sign)
	SignDocumentByIDs(w http.ResponseWriter, r *http.Request, params SignDocumentByIDsParams)
	// Принятие кода ОТП по подписанию переданных документов
	// (POST /documents/sign-confirm)
	ConfirmSignDocuments(w http.ResponseWriter, r *http.Request, params ConfirmSignDocumentsParams)
	// Просмотр сгенерированного ранее документа
	// (GET /documents/{docID})
	GetDocumentByID(w http.ResponseWriter, r *http.Request, docID DocumentIDPathParam, params GetDocumentByIDParams)
	// Подпись сгенерированного ранее документа
	// (POST /documents/{docID}/sign)
	SignDocumentByID(w http.ResponseWriter, r *http.Request, docID DocumentIDPathParam, params SignDocumentByIDParams)
	// Подтверждение подписи документа через ОТП
	// (POST /documents/{docID}/sign-confirm)
	ConfirmSignDocumentByID(w http.ResponseWriter, r *http.Request, docID DocumentIDPathParam, params ConfirmSignDocumentByIDParams)
	// Загрузка PDF файла
	// (POST /fileguard)
	PostUnsecurePdfFile(w http.ResponseWriter, r *http.Request)
	// Расчёт сумм конвертации
	// (POST /foreign-activity/conversion-sum)
	FaConversionSum(w http.ResponseWriter, r *http.Request)
	// Создать конвертацию
	// (POST /foreign-activity/create-convertation)
	CreateConvertation(w http.ResponseWriter, r *http.Request)
	// Проверка на работоспособность
	// (GET /health)
	Health(w http.ResponseWriter, r *http.Request)
	// Создание кредитной заявки
	// (POST /loans/application)
	CreateLoanApplication(w http.ResponseWriter, r *http.Request)
	// Получение справочных значений банков для загрузки выписки
	// (GET /loans/bank-statement)
	GetBankStatement(w http.ResponseWriter, r *http.Request)
	// Получение данных для расчета кредита
	// (GET /loans/calc-data)
	GetLoansCalcData(w http.ResponseWriter, r *http.Request)
	// Расчет параметров платежей для всех сроков кредитов
	// (POST /loans/calculation)
	CalculateLoanTerms(w http.ResponseWriter, r *http.Request)
	// Метод для изменения флага режима работы биржи
	// (POST /loans/change-disbursement-mode)
	ChangeDisbursementControl(w http.ResponseWriter, r *http.Request)
	// Проверка клиента на активные заявки на кредит
	// (POST /loans/client-application/check)
	LoansClientApplicationCheck(w http.ResponseWriter, r *http.Request)
	// Метод для генерации и получения документа для подписи
	// (POST /loans/documents)
	DocumentsForLoanApp(w http.ResponseWriter, r *http.Request, params DocumentsForLoanAppParams)
	// Получение справочника по типам образования
	// (GET /loans/education-types)
	LoansGetEducationTypes(w http.ResponseWriter, r *http.Request)
	// Получение справочника по типам занятости
	// (GET /loans/employment-types)
	LoansGetEmploymentTypes(w http.ResponseWriter, r *http.Request)
	// Получение onboarding текстовок для кредита SME
	// (GET /loans/onboarding-texts)
	GetLoansOnboardingTexts(w http.ResponseWriter, r *http.Request)
	// Получение справочника по видам отношений к контактным лицам
	// (GET /loans/relation-types)
	LoansGetRelationTypes(w http.ResponseWriter, r *http.Request)
	// Получение анкеты пользователя
	// (GET /loans/survey)
	GetLoanSurvey(w http.ResponseWriter, r *http.Request, params GetLoanSurveyParams)
	// Сохранение анкеты пользователя.
	// (POST /loans/survey)
	SaveLoanSurvey(w http.ResponseWriter, r *http.Request, params SaveLoanSurveyParams)
	// Получение справочных значений банков для загрузки выписки
	// (GET /loans/{applicationID}/bank-statement)
	GetBankStatementV2(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam)
	// Метод для получения ссылки (liveness, eds)
	// (GET /loans/{applicationID}/bts-data)
	GetBtsDataForLoanApp(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params GetBtsDataForLoanAppParams)
	// Отмена заявки на кредит
	// (POST /loans/{applicationID}/cancel)
	LoansCancelLoanApplication(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam)
	// Получение детальной информации по выданному займу
	// (GET /loans/{applicationID}/details)
	GetLoansDetails(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params GetLoansDetailsParams)
	// Метод для проведения досрочного погашения (ЧДП/ПДП)
	// (POST /loans/{applicationID}/early-repay)
	LoansPostEarlyRepay(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam)
	// Метод для идентификации после ЭЦП
	// (POST /loans/{applicationID}/eds)
	LoansPostEdsBtsData(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params LoansPostEdsBtsDataParams)
	// Метод для идентификации после liveness
	// (POST /loans/{applicationID}/identify)
	LoansPostIdentifyBtsDataSme(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params LoansPostIdentifyBtsDataSmeParams)
	// Получение  результата внутренних проверок кредитной заявки
	// (GET /loans/{applicationID}/internal-checks-result)
	LoansGetInternalChecksResult(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam)
	// Метод для публикации заявки с отправкой в СПР
	// (POST /loans/{applicationID}/publish)
	PublishLoanAppData(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam)
	// Метод для получения условий одобренного предложения от СПР
	// (GET /loans/{applicationID}/scoring-result)
	GetScoringResult(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params GetScoringResultParams)
	// Получение статуса подтвержденной заявки на кредит
	// (GET /loans/{applicationID}/status)
	LoansGetApprovedLoanAppStatus(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam)
	// Подтверждение подписи документа через ОТП
	// (POST /loans/{applicationID}/{docID}/sign-confirm)
	LoansConfirmSignDocumentByID(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, docID DocumentIDPathParam, params LoansConfirmSignDocumentByIDParams)
	// Перезапрос кода подтверждения
	// (POST /otp/retry)
	OtpRetry(w http.ResponseWriter, r *http.Request)
	// Проверка ИИН/БИН и номера счёта получателя
	// (POST /payments/check-account-iin)
	PaymentsCheckAccountIin(w http.ResponseWriter, r *http.Request)
	// Проверка отп и выполнение платежа для внутренних переводов
	// (POST /payments/confirm-payment-by-account)
	ConfirmPaymentByAccount(w http.ResponseWriter, r *http.Request)
	// Создание платежа для внутренних переводов
	// (POST /payments/create-payment-by-account)
	CreatePaymentByAccount(w http.ResponseWriter, r *http.Request)
	// История платежей
	// (GET /payments/history)
	GetPaymentHistory(w http.ResponseWriter, r *http.Request, params GetPaymentHistoryParams)
	// История операций
	// (GET /payments/transactions)
	GetTransactions(w http.ResponseWriter, r *http.Request, params GetTransactionsParams)
	// Получение транзакции по индентификатору
	// (GET /payments/transactions/{transactionID})
	GetTransactionByID(w http.ResponseWriter, r *http.Request, transactionID PaymentsGetTransactionByIDTransactionID)
	// Получение квитанции по платежу
	// (GET /payments/transactions/{transactionID}/receipt)
	GetTransactionReceipt(w http.ResponseWriter, r *http.Request, transactionID PaymentsGetTransactionByIDTransactionID)
	// Получение данных по клиенту
	// (GET /smepayments/client)
	SmePaymentsClient(w http.ResponseWriter, r *http.Request)
	// Получить справочник банков (SME)
	// (GET /smepayments/dictionaries/banks)
	GetBankList(w http.ResponseWriter, r *http.Request, params GetBankListParams)
	// Получить справочник Кбе/Код (SME)
	// (GET /smepayments/dictionaries/code)
	GetKbeKodList(w http.ResponseWriter, r *http.Request, params GetKbeKodListParams)
	// Получить справочник КБК (SME)
	// (GET /smepayments/dictionaries/kbk)
	GetKbkList(w http.ResponseWriter, r *http.Request, params GetKbkListParams)
	// Получить справочник КНП (SME)
	// (GET /smepayments/dictionaries/knp)
	GetKnpList(w http.ResponseWriter, r *http.Request, params GetKnpListParams)
	// Получить справочник УГД (SME)
	// (GET /smepayments/dictionaries/tax-authorities)
	GetTaxAuthorityList(w http.ResponseWriter, r *http.Request, params GetTaxAuthorityListParams)
	// Получение списка сотрудников ИП
	// (GET /smepayments/employee-list)
	SmePaymentsGetEmployeeList(w http.ResponseWriter, r *http.Request)
	// Добавление нового сотрудника в список сотрудников ИП
	// (POST /smepayments/employee-list/new)
	SmePaymentsCreateEmployee(w http.ResponseWriter, r *http.Request)
	// Удаление сотрудника из списка сотрудников ИП
	// (DELETE /smepayments/employee-list/{employeeID}/delete)
	SmePaymentsDeleteEmployeeByID(w http.ResponseWriter, r *http.Request, employeeID EmployeeID)
	// Редактирование сотрудника из списка сотрудников ИП
	// (PUT /smepayments/employee-list/{employeeID}/edit)
	SmePaymentsUpdateEmployeeByID(w http.ResponseWriter, r *http.Request, employeeID EmployeeID)
	// Создание OTP для подписи транзакции
	// (POST /smepayments/otp)
	SmePaymentsCreateOtp(w http.ResponseWriter, r *http.Request)
	// Перегенерация OTP кода
	// (POST /smepayments/otp/{attemptID}/resend)
	SmePaymentsOtpResend(w http.ResponseWriter, r *http.Request, attemptID SmePaymentsOtpAttemptID)
	// Валидация OTP кода
	// (POST /smepayments/otp/{attemptID}/validate)
	SmePaymentsOtpValidate(w http.ResponseWriter, r *http.Request, attemptID SmePaymentsOtpAttemptID)
	// Создание платежа (SME)
	// (POST /smepayments/transactions)
	CreatePayment(w http.ResponseWriter, r *http.Request)
	// Подтверждение платежа в бюджет (SME)
	// (POST /smepayments/transactions/{transactionID}/confirm)
	ConfirmPaymentSme(w http.ResponseWriter, r *http.Request, transactionID PaymentsTransactionID)
	// Получение ссылки на печатную форму платежного поручения
	// (GET /smepayments/transactions/{transactionID}/payment-order)
	SmePaymentsGetPaymentOrder(w http.ResponseWriter, r *http.Request, transactionID PaymentsTransactionID)
	// Получение ссылки на печатную форму платежного поручения
	// (GET /smepayments/transactions/{transactionNumber}/payment-order-by-tr-number)
	SmePaymentsGetPaymentOrderByTrNumber(w http.ResponseWriter, r *http.Request, transactionNumber PaymentsTransactionNumber)
	// Проверка операционного дня банка
	// (GET /smepayments/worktime)
	SmePaymentsWorktime(w http.ResponseWriter, r *http.Request)
	// Получение списка задач
	// (GET /tasks)
	GetTasks(w http.ResponseWriter, r *http.Request, params GetTasksParams)
	// Получение детальной информации о задаче
	// (GET /tasks/{taskID})
	GetTaskByID(w http.ResponseWriter, r *http.Request, taskID TaskIDPathParam)
	// Список текущих счетов пользователя
	// (GET /user/accounts)
	GetUserAccountsSME(w http.ResponseWriter, r *http.Request)
	// Получение счета пользователя
	// (GET /user/accounts/{accountID})
	GetUserAccounts(w http.ResponseWriter, r *http.Request, accountID AccountIDPathParam)
	// Список текущих карт и счетов пользователя
	// (GET /user/cards)
	UserCards(w http.ResponseWriter, r *http.Request)
	// Получение информации о кредитах пользователя
	// (GET /user/loans)
	GetLoans(w http.ResponseWriter, r *http.Request, params GetLoansParams)
	// Метод для обновления локализации пользователя
	// (POST /user/locale)
	UsersUpdateUserLocale(w http.ResponseWriter, r *http.Request, params UsersUpdateUserLocaleParams)
	// Удаление профиля из системы
	// (POST /user/profile)
	ProfileDelete(w http.ResponseWriter, r *http.Request)
}

// Unimplemented server implementation that returns http.StatusNotImplemented for each endpoint.

type Unimplemented struct{}

// Метод для получения документов из счетов
// (POST /accounts/documents)
func (_ Unimplemented) CreateAccountsDocument(w http.ResponseWriter, r *http.Request, params CreateAccountsDocumentParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Проверка клиента при открытии доп.счета ИП
// (POST /accounts/open/client-verification)
func (_ Unimplemented) ClientVerification(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Доступные к открытию счета пользователя в валютах
// (GET /accounts/open/currency-check)
func (_ Unimplemented) CurrencyCheck(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение документов на подписание при открытии счета
// (POST /accounts/open/documents-for-sign)
func (_ Unimplemented) DocumentsForSign(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для получения ссылки liveness
// (POST /auth/bts-data)
func (_ Unimplemented) GetBtsDataForAuth(w http.ResponseWriter, r *http.Request, params GetBtsDataForAuthParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Подтверждение авторизации
// (POST /auth/confirm)
func (_ Unimplemented) AuthConfirm(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для генерации и получения документа для подписи
// (POST /auth/document-for-sign)
func (_ Unimplemented) DocumentForSign(w http.ResponseWriter, r *http.Request, params DocumentForSignParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Идентификация пользователя
// (POST /auth/identify)
func (_ Unimplemented) AuthIdentify(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Авторизация пользователя по телефону
// (POST /auth/login)
func (_ Unimplemented) AuthLogin(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Выход из системы
// (POST /auth/logout)
func (_ Unimplemented) AuthLogout(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Обновление токенов пользователя
// (POST /auth/refresh)
func (_ Unimplemented) AuthRefresh(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Просмотр списка документов справочника по фильтрам данных
// (POST /dict/doc/by-filters)
func (_ Unimplemented) DictDocGetListByFilters(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение локаций по родительским локациям
// (GET /dictionaries/locations)
func (_ Unimplemented) DictGetLocations(w http.ResponseWriter, r *http.Request, params DictGetLocationsParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Подтверждение подписи нескольких документов через ОТП
// (POST /documents/batch-sign-confirm)
func (_ Unimplemented) ConfirmSignDocumentsBatch(w http.ResponseWriter, r *http.Request, params ConfirmSignDocumentsBatchParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод ввода и обработки новых заказов в BSAS
// (POST /documents/bsas-create-order)
func (_ Unimplemented) CreateOrderBsas(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод получения результата обработки заказа в BSAS
// (POST /documents/bsas-get-order)
func (_ Unimplemented) GetOrderBsas(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод получения сведений об ОТС информации из системы
// (POST /documents/bsas-get-otc)
func (_ Unimplemented) GetOtcBsas(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение сведений о сопоставлении из BSAS
// (POST /documents/bsas-get-system)
func (_ Unimplemented) GetSystemBsas(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод получения сведений о продажах из системы
// (POST /documents/bsas-sell)
func (_ Unimplemented) GetSellBsas(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для получения документов, доступных для просмотра всем
// (POST /documents/public)
func (_ Unimplemented) RequestDocumentPublic(w http.ResponseWriter, r *http.Request, params RequestDocumentPublicParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Отправка ОТП для подтверждения подписания
// (POST /documents/sign)
func (_ Unimplemented) SignDocumentByIDs(w http.ResponseWriter, r *http.Request, params SignDocumentByIDsParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Принятие кода ОТП по подписанию переданных документов
// (POST /documents/sign-confirm)
func (_ Unimplemented) ConfirmSignDocuments(w http.ResponseWriter, r *http.Request, params ConfirmSignDocumentsParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Просмотр сгенерированного ранее документа
// (GET /documents/{docID})
func (_ Unimplemented) GetDocumentByID(w http.ResponseWriter, r *http.Request, docID DocumentIDPathParam, params GetDocumentByIDParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Подпись сгенерированного ранее документа
// (POST /documents/{docID}/sign)
func (_ Unimplemented) SignDocumentByID(w http.ResponseWriter, r *http.Request, docID DocumentIDPathParam, params SignDocumentByIDParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Подтверждение подписи документа через ОТП
// (POST /documents/{docID}/sign-confirm)
func (_ Unimplemented) ConfirmSignDocumentByID(w http.ResponseWriter, r *http.Request, docID DocumentIDPathParam, params ConfirmSignDocumentByIDParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Загрузка PDF файла
// (POST /fileguard)
func (_ Unimplemented) PostUnsecurePdfFile(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Расчёт сумм конвертации
// (POST /foreign-activity/conversion-sum)
func (_ Unimplemented) FaConversionSum(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Создать конвертацию
// (POST /foreign-activity/create-convertation)
func (_ Unimplemented) CreateConvertation(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Проверка на работоспособность
// (GET /health)
func (_ Unimplemented) Health(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Создание кредитной заявки
// (POST /loans/application)
func (_ Unimplemented) CreateLoanApplication(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение справочных значений банков для загрузки выписки
// (GET /loans/bank-statement)
func (_ Unimplemented) GetBankStatement(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение данных для расчета кредита
// (GET /loans/calc-data)
func (_ Unimplemented) GetLoansCalcData(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Расчет параметров платежей для всех сроков кредитов
// (POST /loans/calculation)
func (_ Unimplemented) CalculateLoanTerms(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для изменения флага режима работы биржи
// (POST /loans/change-disbursement-mode)
func (_ Unimplemented) ChangeDisbursementControl(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Проверка клиента на активные заявки на кредит
// (POST /loans/client-application/check)
func (_ Unimplemented) LoansClientApplicationCheck(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для генерации и получения документа для подписи
// (POST /loans/documents)
func (_ Unimplemented) DocumentsForLoanApp(w http.ResponseWriter, r *http.Request, params DocumentsForLoanAppParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение справочника по типам образования
// (GET /loans/education-types)
func (_ Unimplemented) LoansGetEducationTypes(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение справочника по типам занятости
// (GET /loans/employment-types)
func (_ Unimplemented) LoansGetEmploymentTypes(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение onboarding текстовок для кредита SME
// (GET /loans/onboarding-texts)
func (_ Unimplemented) GetLoansOnboardingTexts(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение справочника по видам отношений к контактным лицам
// (GET /loans/relation-types)
func (_ Unimplemented) LoansGetRelationTypes(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение анкеты пользователя
// (GET /loans/survey)
func (_ Unimplemented) GetLoanSurvey(w http.ResponseWriter, r *http.Request, params GetLoanSurveyParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Сохранение анкеты пользователя.
// (POST /loans/survey)
func (_ Unimplemented) SaveLoanSurvey(w http.ResponseWriter, r *http.Request, params SaveLoanSurveyParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение справочных значений банков для загрузки выписки
// (GET /loans/{applicationID}/bank-statement)
func (_ Unimplemented) GetBankStatementV2(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для получения ссылки (liveness, eds)
// (GET /loans/{applicationID}/bts-data)
func (_ Unimplemented) GetBtsDataForLoanApp(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params GetBtsDataForLoanAppParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Отмена заявки на кредит
// (POST /loans/{applicationID}/cancel)
func (_ Unimplemented) LoansCancelLoanApplication(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение детальной информации по выданному займу
// (GET /loans/{applicationID}/details)
func (_ Unimplemented) GetLoansDetails(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params GetLoansDetailsParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для проведения досрочного погашения (ЧДП/ПДП)
// (POST /loans/{applicationID}/early-repay)
func (_ Unimplemented) LoansPostEarlyRepay(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для идентификации после ЭЦП
// (POST /loans/{applicationID}/eds)
func (_ Unimplemented) LoansPostEdsBtsData(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params LoansPostEdsBtsDataParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для идентификации после liveness
// (POST /loans/{applicationID}/identify)
func (_ Unimplemented) LoansPostIdentifyBtsDataSme(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params LoansPostIdentifyBtsDataSmeParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение  результата внутренних проверок кредитной заявки
// (GET /loans/{applicationID}/internal-checks-result)
func (_ Unimplemented) LoansGetInternalChecksResult(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для публикации заявки с отправкой в СПР
// (POST /loans/{applicationID}/publish)
func (_ Unimplemented) PublishLoanAppData(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для получения условий одобренного предложения от СПР
// (GET /loans/{applicationID}/scoring-result)
func (_ Unimplemented) GetScoringResult(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params GetScoringResultParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение статуса подтвержденной заявки на кредит
// (GET /loans/{applicationID}/status)
func (_ Unimplemented) LoansGetApprovedLoanAppStatus(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Подтверждение подписи документа через ОТП
// (POST /loans/{applicationID}/{docID}/sign-confirm)
func (_ Unimplemented) LoansConfirmSignDocumentByID(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, docID DocumentIDPathParam, params LoansConfirmSignDocumentByIDParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Перезапрос кода подтверждения
// (POST /otp/retry)
func (_ Unimplemented) OtpRetry(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Проверка ИИН/БИН и номера счёта получателя
// (POST /payments/check-account-iin)
func (_ Unimplemented) PaymentsCheckAccountIin(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Проверка отп и выполнение платежа для внутренних переводов
// (POST /payments/confirm-payment-by-account)
func (_ Unimplemented) ConfirmPaymentByAccount(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Создание платежа для внутренних переводов
// (POST /payments/create-payment-by-account)
func (_ Unimplemented) CreatePaymentByAccount(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// История платежей
// (GET /payments/history)
func (_ Unimplemented) GetPaymentHistory(w http.ResponseWriter, r *http.Request, params GetPaymentHistoryParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// История операций
// (GET /payments/transactions)
func (_ Unimplemented) GetTransactions(w http.ResponseWriter, r *http.Request, params GetTransactionsParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение транзакции по индентификатору
// (GET /payments/transactions/{transactionID})
func (_ Unimplemented) GetTransactionByID(w http.ResponseWriter, r *http.Request, transactionID PaymentsGetTransactionByIDTransactionID) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение квитанции по платежу
// (GET /payments/transactions/{transactionID}/receipt)
func (_ Unimplemented) GetTransactionReceipt(w http.ResponseWriter, r *http.Request, transactionID PaymentsGetTransactionByIDTransactionID) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение данных по клиенту
// (GET /smepayments/client)
func (_ Unimplemented) SmePaymentsClient(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получить справочник банков (SME)
// (GET /smepayments/dictionaries/banks)
func (_ Unimplemented) GetBankList(w http.ResponseWriter, r *http.Request, params GetBankListParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получить справочник Кбе/Код (SME)
// (GET /smepayments/dictionaries/code)
func (_ Unimplemented) GetKbeKodList(w http.ResponseWriter, r *http.Request, params GetKbeKodListParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получить справочник КБК (SME)
// (GET /smepayments/dictionaries/kbk)
func (_ Unimplemented) GetKbkList(w http.ResponseWriter, r *http.Request, params GetKbkListParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получить справочник КНП (SME)
// (GET /smepayments/dictionaries/knp)
func (_ Unimplemented) GetKnpList(w http.ResponseWriter, r *http.Request, params GetKnpListParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получить справочник УГД (SME)
// (GET /smepayments/dictionaries/tax-authorities)
func (_ Unimplemented) GetTaxAuthorityList(w http.ResponseWriter, r *http.Request, params GetTaxAuthorityListParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение списка сотрудников ИП
// (GET /smepayments/employee-list)
func (_ Unimplemented) SmePaymentsGetEmployeeList(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Добавление нового сотрудника в список сотрудников ИП
// (POST /smepayments/employee-list/new)
func (_ Unimplemented) SmePaymentsCreateEmployee(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Удаление сотрудника из списка сотрудников ИП
// (DELETE /smepayments/employee-list/{employeeID}/delete)
func (_ Unimplemented) SmePaymentsDeleteEmployeeByID(w http.ResponseWriter, r *http.Request, employeeID EmployeeID) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Редактирование сотрудника из списка сотрудников ИП
// (PUT /smepayments/employee-list/{employeeID}/edit)
func (_ Unimplemented) SmePaymentsUpdateEmployeeByID(w http.ResponseWriter, r *http.Request, employeeID EmployeeID) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Создание OTP для подписи транзакции
// (POST /smepayments/otp)
func (_ Unimplemented) SmePaymentsCreateOtp(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Перегенерация OTP кода
// (POST /smepayments/otp/{attemptID}/resend)
func (_ Unimplemented) SmePaymentsOtpResend(w http.ResponseWriter, r *http.Request, attemptID SmePaymentsOtpAttemptID) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Валидация OTP кода
// (POST /smepayments/otp/{attemptID}/validate)
func (_ Unimplemented) SmePaymentsOtpValidate(w http.ResponseWriter, r *http.Request, attemptID SmePaymentsOtpAttemptID) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Создание платежа (SME)
// (POST /smepayments/transactions)
func (_ Unimplemented) CreatePayment(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Подтверждение платежа в бюджет (SME)
// (POST /smepayments/transactions/{transactionID}/confirm)
func (_ Unimplemented) ConfirmPaymentSme(w http.ResponseWriter, r *http.Request, transactionID PaymentsTransactionID) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение ссылки на печатную форму платежного поручения
// (GET /smepayments/transactions/{transactionID}/payment-order)
func (_ Unimplemented) SmePaymentsGetPaymentOrder(w http.ResponseWriter, r *http.Request, transactionID PaymentsTransactionID) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение ссылки на печатную форму платежного поручения
// (GET /smepayments/transactions/{transactionNumber}/payment-order-by-tr-number)
func (_ Unimplemented) SmePaymentsGetPaymentOrderByTrNumber(w http.ResponseWriter, r *http.Request, transactionNumber PaymentsTransactionNumber) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Проверка операционного дня банка
// (GET /smepayments/worktime)
func (_ Unimplemented) SmePaymentsWorktime(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение списка задач
// (GET /tasks)
func (_ Unimplemented) GetTasks(w http.ResponseWriter, r *http.Request, params GetTasksParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение детальной информации о задаче
// (GET /tasks/{taskID})
func (_ Unimplemented) GetTaskByID(w http.ResponseWriter, r *http.Request, taskID TaskIDPathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Список текущих счетов пользователя
// (GET /user/accounts)
func (_ Unimplemented) GetUserAccountsSME(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение счета пользователя
// (GET /user/accounts/{accountID})
func (_ Unimplemented) GetUserAccounts(w http.ResponseWriter, r *http.Request, accountID AccountIDPathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Список текущих карт и счетов пользователя
// (GET /user/cards)
func (_ Unimplemented) UserCards(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение информации о кредитах пользователя
// (GET /user/loans)
func (_ Unimplemented) GetLoans(w http.ResponseWriter, r *http.Request, params GetLoansParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для обновления локализации пользователя
// (POST /user/locale)
func (_ Unimplemented) UsersUpdateUserLocale(w http.ResponseWriter, r *http.Request, params UsersUpdateUserLocaleParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Удаление профиля из системы
// (POST /user/profile)
func (_ Unimplemented) ProfileDelete(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandlerFunc   func(w http.ResponseWriter, r *http.Request, err error)
}

type MiddlewareFunc func(http.Handler) http.Handler

// CreateAccountsDocument operation middleware
func (siw *ServerInterfaceWrapper) CreateAccountsDocument(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params CreateAccountsDocumentParams

	// ------------- Required query parameter "docType" -------------

	if paramValue := r.URL.Query().Get("docType"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "docType"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "docType", r.URL.Query(), &params.DocType)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "docType", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.CreateAccountsDocument(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// ClientVerification operation middleware
func (siw *ServerInterfaceWrapper) ClientVerification(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ClientVerification(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// CurrencyCheck operation middleware
func (siw *ServerInterfaceWrapper) CurrencyCheck(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.CurrencyCheck(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DocumentsForSign operation middleware
func (siw *ServerInterfaceWrapper) DocumentsForSign(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DocumentsForSign(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetBtsDataForAuth operation middleware
func (siw *ServerInterfaceWrapper) GetBtsDataForAuth(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetBtsDataForAuthParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage GetBtsDataForAuthParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetBtsDataForAuth(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// AuthConfirm operation middleware
func (siw *ServerInterfaceWrapper) AuthConfirm(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.AuthConfirm(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DocumentForSign operation middleware
func (siw *ServerInterfaceWrapper) DocumentForSign(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params DocumentForSignParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage DocumentForSignParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DocumentForSign(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// AuthIdentify operation middleware
func (siw *ServerInterfaceWrapper) AuthIdentify(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.AuthIdentify(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// AuthLogin operation middleware
func (siw *ServerInterfaceWrapper) AuthLogin(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.AuthLogin(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// AuthLogout operation middleware
func (siw *ServerInterfaceWrapper) AuthLogout(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.AuthLogout(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// AuthRefresh operation middleware
func (siw *ServerInterfaceWrapper) AuthRefresh(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.AuthRefresh(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DictDocGetListByFilters operation middleware
func (siw *ServerInterfaceWrapper) DictDocGetListByFilters(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DictDocGetListByFilters(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DictGetLocations operation middleware
func (siw *ServerInterfaceWrapper) DictGetLocations(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params DictGetLocationsParams

	// ------------- Optional query parameter "parentID" -------------

	err = runtime.BindQueryParameter("form", true, false, "parentID", r.URL.Query(), &params.ParentID)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "parentID", Err: err})
		return
	}

	// ------------- Optional query parameter "parentIds" -------------

	err = runtime.BindQueryParameter("form", false, false, "parentIds", r.URL.Query(), &params.ParentIds)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "parentIds", Err: err})
		return
	}

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage DictGetLocationsParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DictGetLocations(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// ConfirmSignDocumentsBatch operation middleware
func (siw *ServerInterfaceWrapper) ConfirmSignDocumentsBatch(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params ConfirmSignDocumentsBatchParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage ConfirmSignDocumentsBatchParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ConfirmSignDocumentsBatch(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// CreateOrderBsas operation middleware
func (siw *ServerInterfaceWrapper) CreateOrderBsas(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.CreateOrderBsas(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetOrderBsas operation middleware
func (siw *ServerInterfaceWrapper) GetOrderBsas(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetOrderBsas(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetOtcBsas operation middleware
func (siw *ServerInterfaceWrapper) GetOtcBsas(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetOtcBsas(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetSystemBsas operation middleware
func (siw *ServerInterfaceWrapper) GetSystemBsas(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetSystemBsas(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetSellBsas operation middleware
func (siw *ServerInterfaceWrapper) GetSellBsas(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetSellBsas(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// RequestDocumentPublic operation middleware
func (siw *ServerInterfaceWrapper) RequestDocumentPublic(w http.ResponseWriter, r *http.Request) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params RequestDocumentPublicParams

	// ------------- Required query parameter "docType" -------------

	if paramValue := r.URL.Query().Get("docType"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "docType"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "docType", r.URL.Query(), &params.DocType)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "docType", Err: err})
		return
	}

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage RequestDocumentPublicParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.RequestDocumentPublic(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// SignDocumentByIDs operation middleware
func (siw *ServerInterfaceWrapper) SignDocumentByIDs(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params SignDocumentByIDsParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage SignDocumentByIDsParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SignDocumentByIDs(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// ConfirmSignDocuments operation middleware
func (siw *ServerInterfaceWrapper) ConfirmSignDocuments(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params ConfirmSignDocumentsParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage ConfirmSignDocumentsParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ConfirmSignDocuments(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetDocumentByID operation middleware
func (siw *ServerInterfaceWrapper) GetDocumentByID(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "docID" -------------
	var docID DocumentIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "docID", chi.URLParam(r, "docID"), &docID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "docID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetDocumentByIDParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage GetDocumentByIDParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetDocumentByID(w, r, docID, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// SignDocumentByID operation middleware
func (siw *ServerInterfaceWrapper) SignDocumentByID(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "docID" -------------
	var docID DocumentIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "docID", chi.URLParam(r, "docID"), &docID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "docID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params SignDocumentByIDParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage SignDocumentByIDParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SignDocumentByID(w, r, docID, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// ConfirmSignDocumentByID operation middleware
func (siw *ServerInterfaceWrapper) ConfirmSignDocumentByID(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "docID" -------------
	var docID DocumentIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "docID", chi.URLParam(r, "docID"), &docID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "docID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params ConfirmSignDocumentByIDParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage ConfirmSignDocumentByIDParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ConfirmSignDocumentByID(w, r, docID, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// PostUnsecurePdfFile operation middleware
func (siw *ServerInterfaceWrapper) PostUnsecurePdfFile(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PostUnsecurePdfFile(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// FaConversionSum operation middleware
func (siw *ServerInterfaceWrapper) FaConversionSum(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.FaConversionSum(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// CreateConvertation operation middleware
func (siw *ServerInterfaceWrapper) CreateConvertation(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.CreateConvertation(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// Health operation middleware
func (siw *ServerInterfaceWrapper) Health(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.Health(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// CreateLoanApplication operation middleware
func (siw *ServerInterfaceWrapper) CreateLoanApplication(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.CreateLoanApplication(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetBankStatement operation middleware
func (siw *ServerInterfaceWrapper) GetBankStatement(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetBankStatement(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetLoansCalcData operation middleware
func (siw *ServerInterfaceWrapper) GetLoansCalcData(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetLoansCalcData(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// CalculateLoanTerms operation middleware
func (siw *ServerInterfaceWrapper) CalculateLoanTerms(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.CalculateLoanTerms(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// ChangeDisbursementControl operation middleware
func (siw *ServerInterfaceWrapper) ChangeDisbursementControl(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ChangeDisbursementControl(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoansClientApplicationCheck operation middleware
func (siw *ServerInterfaceWrapper) LoansClientApplicationCheck(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoansClientApplicationCheck(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DocumentsForLoanApp operation middleware
func (siw *ServerInterfaceWrapper) DocumentsForLoanApp(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params DocumentsForLoanAppParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage DocumentsForLoanAppParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DocumentsForLoanApp(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoansGetEducationTypes operation middleware
func (siw *ServerInterfaceWrapper) LoansGetEducationTypes(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoansGetEducationTypes(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoansGetEmploymentTypes operation middleware
func (siw *ServerInterfaceWrapper) LoansGetEmploymentTypes(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoansGetEmploymentTypes(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetLoansOnboardingTexts operation middleware
func (siw *ServerInterfaceWrapper) GetLoansOnboardingTexts(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetLoansOnboardingTexts(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoansGetRelationTypes operation middleware
func (siw *ServerInterfaceWrapper) LoansGetRelationTypes(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoansGetRelationTypes(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetLoanSurvey operation middleware
func (siw *ServerInterfaceWrapper) GetLoanSurvey(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetLoanSurveyParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage GetLoanSurveyParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetLoanSurvey(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// SaveLoanSurvey operation middleware
func (siw *ServerInterfaceWrapper) SaveLoanSurvey(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params SaveLoanSurveyParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage SaveLoanSurveyParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	// ------------- Required header parameter "User-Agent" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("User-Agent")]; found {
		var UserAgent UserAgent
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "User-Agent", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "User-Agent", valueList[0], &UserAgent, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: true})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "User-Agent", Err: err})
			return
		}

		params.UserAgent = UserAgent

	} else {
		err := fmt.Errorf("Header parameter User-Agent is required, but not found")
		siw.ErrorHandlerFunc(w, r, &RequiredHeaderError{ParamName: "User-Agent", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SaveLoanSurvey(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetBankStatementV2 operation middleware
func (siw *ServerInterfaceWrapper) GetBankStatementV2(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetBankStatementV2(w, r, applicationID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetBtsDataForLoanApp operation middleware
func (siw *ServerInterfaceWrapper) GetBtsDataForLoanApp(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetBtsDataForLoanAppParams

	// ------------- Required query parameter "type" -------------

	if paramValue := r.URL.Query().Get("type"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "type"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "type", r.URL.Query(), &params.Type)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "type", Err: err})
		return
	}

	headers := r.Header

	// ------------- Required header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage GetBtsDataForLoanAppParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: true})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = AcceptLanguage

	} else {
		err := fmt.Errorf("Header parameter Accept-Language is required, but not found")
		siw.ErrorHandlerFunc(w, r, &RequiredHeaderError{ParamName: "Accept-Language", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetBtsDataForLoanApp(w, r, applicationID, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoansCancelLoanApplication operation middleware
func (siw *ServerInterfaceWrapper) LoansCancelLoanApplication(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoansCancelLoanApplication(w, r, applicationID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetLoansDetails operation middleware
func (siw *ServerInterfaceWrapper) GetLoansDetails(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetLoansDetailsParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage GetLoansDetailsParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetLoansDetails(w, r, applicationID, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoansPostEarlyRepay operation middleware
func (siw *ServerInterfaceWrapper) LoansPostEarlyRepay(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoansPostEarlyRepay(w, r, applicationID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoansPostEdsBtsData operation middleware
func (siw *ServerInterfaceWrapper) LoansPostEdsBtsData(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params LoansPostEdsBtsDataParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage LoansPostEdsBtsDataParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoansPostEdsBtsData(w, r, applicationID, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoansPostIdentifyBtsDataSme operation middleware
func (siw *ServerInterfaceWrapper) LoansPostIdentifyBtsDataSme(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params LoansPostIdentifyBtsDataSmeParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage LoansPostIdentifyBtsDataSmeParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoansPostIdentifyBtsDataSme(w, r, applicationID, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoansGetInternalChecksResult operation middleware
func (siw *ServerInterfaceWrapper) LoansGetInternalChecksResult(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoansGetInternalChecksResult(w, r, applicationID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// PublishLoanAppData operation middleware
func (siw *ServerInterfaceWrapper) PublishLoanAppData(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PublishLoanAppData(w, r, applicationID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetScoringResult operation middleware
func (siw *ServerInterfaceWrapper) GetScoringResult(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetScoringResultParams

	headers := r.Header

	// ------------- Required header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage GetScoringResultParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: true})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = AcceptLanguage

	} else {
		err := fmt.Errorf("Header parameter Accept-Language is required, but not found")
		siw.ErrorHandlerFunc(w, r, &RequiredHeaderError{ParamName: "Accept-Language", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetScoringResult(w, r, applicationID, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoansGetApprovedLoanAppStatus operation middleware
func (siw *ServerInterfaceWrapper) LoansGetApprovedLoanAppStatus(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoansGetApprovedLoanAppStatus(w, r, applicationID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoansConfirmSignDocumentByID operation middleware
func (siw *ServerInterfaceWrapper) LoansConfirmSignDocumentByID(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	// ------------- Path parameter "docID" -------------
	var docID DocumentIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "docID", chi.URLParam(r, "docID"), &docID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "docID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params LoansConfirmSignDocumentByIDParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage LoansConfirmSignDocumentByIDParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoansConfirmSignDocumentByID(w, r, applicationID, docID, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// OtpRetry operation middleware
func (siw *ServerInterfaceWrapper) OtpRetry(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.OtpRetry(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// PaymentsCheckAccountIin operation middleware
func (siw *ServerInterfaceWrapper) PaymentsCheckAccountIin(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PaymentsCheckAccountIin(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// ConfirmPaymentByAccount operation middleware
func (siw *ServerInterfaceWrapper) ConfirmPaymentByAccount(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ConfirmPaymentByAccount(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// CreatePaymentByAccount operation middleware
func (siw *ServerInterfaceWrapper) CreatePaymentByAccount(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.CreatePaymentByAccount(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetPaymentHistory operation middleware
func (siw *ServerInterfaceWrapper) GetPaymentHistory(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetPaymentHistoryParams

	// ------------- Required query parameter "clientIinBin" -------------

	if paramValue := r.URL.Query().Get("clientIinBin"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "clientIinBin"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "clientIinBin", r.URL.Query(), &params.ClientIinBin)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "clientIinBin", Err: err})
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", r.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "limit", Err: err})
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", r.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "offset", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetPaymentHistory(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetTransactions operation middleware
func (siw *ServerInterfaceWrapper) GetTransactions(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetTransactionsParams

	// ------------- Optional query parameter "startDate" -------------

	err = runtime.BindQueryParameter("form", true, false, "startDate", r.URL.Query(), &params.StartDate)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "startDate", Err: err})
		return
	}

	// ------------- Optional query parameter "endDate" -------------

	err = runtime.BindQueryParameter("form", true, false, "endDate", r.URL.Query(), &params.EndDate)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "endDate", Err: err})
		return
	}

	// ------------- Required query parameter "accounts" -------------

	if paramValue := r.URL.Query().Get("accounts"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "accounts"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "accounts", r.URL.Query(), &params.Accounts)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "accounts", Err: err})
		return
	}

	// ------------- Optional query parameter "cards" -------------

	err = runtime.BindQueryParameter("form", true, false, "cards", r.URL.Query(), &params.Cards)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "cards", Err: err})
		return
	}

	// ------------- Optional query parameter "direction" -------------

	err = runtime.BindQueryParameter("form", true, false, "direction", r.URL.Query(), &params.Direction)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "direction", Err: err})
		return
	}

	// ------------- Optional query parameter "counterparty" -------------

	err = runtime.BindQueryParameter("form", true, false, "counterparty", r.URL.Query(), &params.Counterparty)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "counterparty", Err: err})
		return
	}

	// ------------- Optional query parameter "minAmount" -------------

	err = runtime.BindQueryParameter("form", true, false, "minAmount", r.URL.Query(), &params.MinAmount)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "minAmount", Err: err})
		return
	}

	// ------------- Optional query parameter "maxAmount" -------------

	err = runtime.BindQueryParameter("form", true, false, "maxAmount", r.URL.Query(), &params.MaxAmount)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "maxAmount", Err: err})
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", r.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "limit", Err: err})
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", r.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "offset", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetTransactions(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetTransactionByID operation middleware
func (siw *ServerInterfaceWrapper) GetTransactionByID(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "transactionID" -------------
	var transactionID PaymentsGetTransactionByIDTransactionID

	err = runtime.BindStyledParameterWithOptions("simple", "transactionID", chi.URLParam(r, "transactionID"), &transactionID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "transactionID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetTransactionByID(w, r, transactionID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetTransactionReceipt operation middleware
func (siw *ServerInterfaceWrapper) GetTransactionReceipt(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "transactionID" -------------
	var transactionID PaymentsGetTransactionByIDTransactionID

	err = runtime.BindStyledParameterWithOptions("simple", "transactionID", chi.URLParam(r, "transactionID"), &transactionID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "transactionID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetTransactionReceipt(w, r, transactionID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// SmePaymentsClient operation middleware
func (siw *ServerInterfaceWrapper) SmePaymentsClient(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SmePaymentsClient(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetBankList operation middleware
func (siw *ServerInterfaceWrapper) GetBankList(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetBankListParams

	// ------------- Optional query parameter "filter.bics" -------------

	err = runtime.BindQueryParameter("form", true, false, "filter.bics", r.URL.Query(), &params.FilterBics)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "filter.bics", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetBankList(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetKbeKodList operation middleware
func (siw *ServerInterfaceWrapper) GetKbeKodList(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetKbeKodListParams

	// ------------- Optional query parameter "filter.codes" -------------

	err = runtime.BindQueryParameter("form", true, false, "filter.codes", r.URL.Query(), &params.FilterCodes)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "filter.codes", Err: err})
		return
	}

	// ------------- Optional query parameter "filter.residency" -------------

	err = runtime.BindQueryParameter("form", true, false, "filter.residency", r.URL.Query(), &params.FilterResidency)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "filter.residency", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetKbeKodList(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetKbkList operation middleware
func (siw *ServerInterfaceWrapper) GetKbkList(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetKbkListParams

	// ------------- Optional query parameter "filter.codes" -------------

	err = runtime.BindQueryParameter("form", true, false, "filter.codes", r.URL.Query(), &params.FilterCodes)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "filter.codes", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetKbkList(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetKnpList operation middleware
func (siw *ServerInterfaceWrapper) GetKnpList(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetKnpListParams

	// ------------- Optional query parameter "filter.codes" -------------

	err = runtime.BindQueryParameter("form", true, false, "filter.codes", r.URL.Query(), &params.FilterCodes)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "filter.codes", Err: err})
		return
	}

	// ------------- Optional query parameter "filter.groups" -------------

	err = runtime.BindQueryParameter("form", true, false, "filter.groups", r.URL.Query(), &params.FilterGroups)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "filter.groups", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetKnpList(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetTaxAuthorityList operation middleware
func (siw *ServerInterfaceWrapper) GetTaxAuthorityList(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetTaxAuthorityListParams

	// ------------- Optional query parameter "filter.regions" -------------

	err = runtime.BindQueryParameter("form", true, false, "filter.regions", r.URL.Query(), &params.FilterRegions)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "filter.regions", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetTaxAuthorityList(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// SmePaymentsGetEmployeeList operation middleware
func (siw *ServerInterfaceWrapper) SmePaymentsGetEmployeeList(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SmePaymentsGetEmployeeList(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// SmePaymentsCreateEmployee operation middleware
func (siw *ServerInterfaceWrapper) SmePaymentsCreateEmployee(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SmePaymentsCreateEmployee(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// SmePaymentsDeleteEmployeeByID operation middleware
func (siw *ServerInterfaceWrapper) SmePaymentsDeleteEmployeeByID(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "employeeID" -------------
	var employeeID EmployeeID

	err = runtime.BindStyledParameterWithOptions("simple", "employeeID", chi.URLParam(r, "employeeID"), &employeeID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "employeeID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SmePaymentsDeleteEmployeeByID(w, r, employeeID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// SmePaymentsUpdateEmployeeByID operation middleware
func (siw *ServerInterfaceWrapper) SmePaymentsUpdateEmployeeByID(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "employeeID" -------------
	var employeeID EmployeeID

	err = runtime.BindStyledParameterWithOptions("simple", "employeeID", chi.URLParam(r, "employeeID"), &employeeID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "employeeID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SmePaymentsUpdateEmployeeByID(w, r, employeeID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// SmePaymentsCreateOtp operation middleware
func (siw *ServerInterfaceWrapper) SmePaymentsCreateOtp(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SmePaymentsCreateOtp(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// SmePaymentsOtpResend operation middleware
func (siw *ServerInterfaceWrapper) SmePaymentsOtpResend(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "attemptID" -------------
	var attemptID SmePaymentsOtpAttemptID

	err = runtime.BindStyledParameterWithOptions("simple", "attemptID", chi.URLParam(r, "attemptID"), &attemptID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "attemptID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SmePaymentsOtpResend(w, r, attemptID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// SmePaymentsOtpValidate operation middleware
func (siw *ServerInterfaceWrapper) SmePaymentsOtpValidate(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "attemptID" -------------
	var attemptID SmePaymentsOtpAttemptID

	err = runtime.BindStyledParameterWithOptions("simple", "attemptID", chi.URLParam(r, "attemptID"), &attemptID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "attemptID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SmePaymentsOtpValidate(w, r, attemptID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// CreatePayment operation middleware
func (siw *ServerInterfaceWrapper) CreatePayment(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.CreatePayment(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// ConfirmPaymentSme operation middleware
func (siw *ServerInterfaceWrapper) ConfirmPaymentSme(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "transactionID" -------------
	var transactionID PaymentsTransactionID

	err = runtime.BindStyledParameterWithOptions("simple", "transactionID", chi.URLParam(r, "transactionID"), &transactionID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "transactionID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ConfirmPaymentSme(w, r, transactionID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// SmePaymentsGetPaymentOrder operation middleware
func (siw *ServerInterfaceWrapper) SmePaymentsGetPaymentOrder(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "transactionID" -------------
	var transactionID PaymentsTransactionID

	err = runtime.BindStyledParameterWithOptions("simple", "transactionID", chi.URLParam(r, "transactionID"), &transactionID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "transactionID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SmePaymentsGetPaymentOrder(w, r, transactionID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// SmePaymentsGetPaymentOrderByTrNumber operation middleware
func (siw *ServerInterfaceWrapper) SmePaymentsGetPaymentOrderByTrNumber(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "transactionNumber" -------------
	var transactionNumber PaymentsTransactionNumber

	err = runtime.BindStyledParameterWithOptions("simple", "transactionNumber", chi.URLParam(r, "transactionNumber"), &transactionNumber, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "transactionNumber", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SmePaymentsGetPaymentOrderByTrNumber(w, r, transactionNumber)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// SmePaymentsWorktime operation middleware
func (siw *ServerInterfaceWrapper) SmePaymentsWorktime(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SmePaymentsWorktime(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetTasks operation middleware
func (siw *ServerInterfaceWrapper) GetTasks(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetTasksParams

	// ------------- Optional query parameter "status" -------------

	err = runtime.BindQueryParameter("form", true, false, "status", r.URL.Query(), &params.Status)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "status", Err: err})
		return
	}

	// ------------- Optional query parameter "type" -------------

	err = runtime.BindQueryParameter("form", true, false, "type", r.URL.Query(), &params.Type)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "type", Err: err})
		return
	}

	// ------------- Optional query parameter "created_after" -------------

	err = runtime.BindQueryParameter("form", true, false, "created_after", r.URL.Query(), &params.CreatedAfter)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "created_after", Err: err})
		return
	}

	// ------------- Optional query parameter "created_before" -------------

	err = runtime.BindQueryParameter("form", true, false, "created_before", r.URL.Query(), &params.CreatedBefore)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "created_before", Err: err})
		return
	}

	// ------------- Optional query parameter "page" -------------

	err = runtime.BindQueryParameter("form", true, false, "page", r.URL.Query(), &params.Page)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "page", Err: err})
		return
	}

	// ------------- Optional query parameter "page_size" -------------

	err = runtime.BindQueryParameter("form", true, false, "page_size", r.URL.Query(), &params.PageSize)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "page_size", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetTasks(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetTaskByID operation middleware
func (siw *ServerInterfaceWrapper) GetTaskByID(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "taskID" -------------
	var taskID TaskIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "taskID", chi.URLParam(r, "taskID"), &taskID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "taskID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetTaskByID(w, r, taskID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetUserAccountsSME operation middleware
func (siw *ServerInterfaceWrapper) GetUserAccountsSME(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetUserAccountsSME(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetUserAccounts operation middleware
func (siw *ServerInterfaceWrapper) GetUserAccounts(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "accountID" -------------
	var accountID AccountIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "accountID", chi.URLParam(r, "accountID"), &accountID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "accountID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetUserAccounts(w, r, accountID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// UserCards operation middleware
func (siw *ServerInterfaceWrapper) UserCards(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.UserCards(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetLoans operation middleware
func (siw *ServerInterfaceWrapper) GetLoans(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetLoansParams

	headers := r.Header

	// ------------- Required header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage GetLoansParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: true})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = AcceptLanguage

	} else {
		err := fmt.Errorf("Header parameter Accept-Language is required, but not found")
		siw.ErrorHandlerFunc(w, r, &RequiredHeaderError{ParamName: "Accept-Language", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetLoans(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// UsersUpdateUserLocale operation middleware
func (siw *ServerInterfaceWrapper) UsersUpdateUserLocale(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params UsersUpdateUserLocaleParams

	headers := r.Header

	// ------------- Required header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage UsersUpdateUserLocaleParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: true})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = AcceptLanguage

	} else {
		err := fmt.Errorf("Header parameter Accept-Language is required, but not found")
		siw.ErrorHandlerFunc(w, r, &RequiredHeaderError{ParamName: "Accept-Language", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.UsersUpdateUserLocale(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// ProfileDelete operation middleware
func (siw *ServerInterfaceWrapper) ProfileDelete(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ProfileDelete(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

type UnescapedCookieParamError struct {
	ParamName string
	Err       error
}

func (e *UnescapedCookieParamError) Error() string {
	return fmt.Sprintf("error unescaping cookie parameter '%s'", e.ParamName)
}

func (e *UnescapedCookieParamError) Unwrap() error {
	return e.Err
}

type UnmarshalingParamError struct {
	ParamName string
	Err       error
}

func (e *UnmarshalingParamError) Error() string {
	return fmt.Sprintf("Error unmarshaling parameter %s as JSON: %s", e.ParamName, e.Err.Error())
}

func (e *UnmarshalingParamError) Unwrap() error {
	return e.Err
}

type RequiredParamError struct {
	ParamName string
}

func (e *RequiredParamError) Error() string {
	return fmt.Sprintf("Query argument %s is required, but not found", e.ParamName)
}

type RequiredHeaderError struct {
	ParamName string
	Err       error
}

func (e *RequiredHeaderError) Error() string {
	return fmt.Sprintf("Header parameter %s is required, but not found", e.ParamName)
}

func (e *RequiredHeaderError) Unwrap() error {
	return e.Err
}

type InvalidParamFormatError struct {
	ParamName string
	Err       error
}

func (e *InvalidParamFormatError) Error() string {
	return fmt.Sprintf("Invalid format for parameter %s: %s", e.ParamName, e.Err.Error())
}

func (e *InvalidParamFormatError) Unwrap() error {
	return e.Err
}

type TooManyValuesForParamError struct {
	ParamName string
	Count     int
}

func (e *TooManyValuesForParamError) Error() string {
	return fmt.Sprintf("Expected one value for %s, got %d", e.ParamName, e.Count)
}

// Handler creates http.Handler with routing matching OpenAPI spec.
func Handler(si ServerInterface) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{})
}

type ChiServerOptions struct {
	BaseURL          string
	BaseRouter       chi.Router
	Middlewares      []MiddlewareFunc
	ErrorHandlerFunc func(w http.ResponseWriter, r *http.Request, err error)
}

// HandlerFromMux creates http.Handler with routing matching OpenAPI spec based on the provided mux.
func HandlerFromMux(si ServerInterface, r chi.Router) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseRouter: r,
	})
}

func HandlerFromMuxWithBaseURL(si ServerInterface, r chi.Router, baseURL string) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseURL:    baseURL,
		BaseRouter: r,
	})
}

// HandlerWithOptions creates http.Handler with additional options
func HandlerWithOptions(si ServerInterface, options ChiServerOptions) http.Handler {
	r := options.BaseRouter

	if r == nil {
		r = chi.NewRouter()
	}
	if options.ErrorHandlerFunc == nil {
		options.ErrorHandlerFunc = func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		}
	}
	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandlerFunc:   options.ErrorHandlerFunc,
	}

	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/accounts/documents", wrapper.CreateAccountsDocument)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/accounts/open/client-verification", wrapper.ClientVerification)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/accounts/open/currency-check", wrapper.CurrencyCheck)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/accounts/open/documents-for-sign", wrapper.DocumentsForSign)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/auth/bts-data", wrapper.GetBtsDataForAuth)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/auth/confirm", wrapper.AuthConfirm)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/auth/document-for-sign", wrapper.DocumentForSign)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/auth/identify", wrapper.AuthIdentify)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/auth/login", wrapper.AuthLogin)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/auth/logout", wrapper.AuthLogout)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/auth/refresh", wrapper.AuthRefresh)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/dict/doc/by-filters", wrapper.DictDocGetListByFilters)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dictionaries/locations", wrapper.DictGetLocations)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/documents/batch-sign-confirm", wrapper.ConfirmSignDocumentsBatch)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/documents/bsas-create-order", wrapper.CreateOrderBsas)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/documents/bsas-get-order", wrapper.GetOrderBsas)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/documents/bsas-get-otc", wrapper.GetOtcBsas)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/documents/bsas-get-system", wrapper.GetSystemBsas)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/documents/bsas-sell", wrapper.GetSellBsas)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/documents/public", wrapper.RequestDocumentPublic)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/documents/sign", wrapper.SignDocumentByIDs)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/documents/sign-confirm", wrapper.ConfirmSignDocuments)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/documents/{docID}", wrapper.GetDocumentByID)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/documents/{docID}/sign", wrapper.SignDocumentByID)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/documents/{docID}/sign-confirm", wrapper.ConfirmSignDocumentByID)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/fileguard", wrapper.PostUnsecurePdfFile)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/foreign-activity/conversion-sum", wrapper.FaConversionSum)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/foreign-activity/create-convertation", wrapper.CreateConvertation)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/health", wrapper.Health)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/application", wrapper.CreateLoanApplication)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/bank-statement", wrapper.GetBankStatement)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/calc-data", wrapper.GetLoansCalcData)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/calculation", wrapper.CalculateLoanTerms)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/change-disbursement-mode", wrapper.ChangeDisbursementControl)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/client-application/check", wrapper.LoansClientApplicationCheck)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/documents", wrapper.DocumentsForLoanApp)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/education-types", wrapper.LoansGetEducationTypes)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/employment-types", wrapper.LoansGetEmploymentTypes)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/onboarding-texts", wrapper.GetLoansOnboardingTexts)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/relation-types", wrapper.LoansGetRelationTypes)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/survey", wrapper.GetLoanSurvey)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/survey", wrapper.SaveLoanSurvey)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/{applicationID}/bank-statement", wrapper.GetBankStatementV2)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/{applicationID}/bts-data", wrapper.GetBtsDataForLoanApp)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/{applicationID}/cancel", wrapper.LoansCancelLoanApplication)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/{applicationID}/details", wrapper.GetLoansDetails)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/{applicationID}/early-repay", wrapper.LoansPostEarlyRepay)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/{applicationID}/eds", wrapper.LoansPostEdsBtsData)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/{applicationID}/identify", wrapper.LoansPostIdentifyBtsDataSme)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/{applicationID}/internal-checks-result", wrapper.LoansGetInternalChecksResult)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/{applicationID}/publish", wrapper.PublishLoanAppData)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/{applicationID}/scoring-result", wrapper.GetScoringResult)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/{applicationID}/status", wrapper.LoansGetApprovedLoanAppStatus)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/{applicationID}/{docID}/sign-confirm", wrapper.LoansConfirmSignDocumentByID)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/otp/retry", wrapper.OtpRetry)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/payments/check-account-iin", wrapper.PaymentsCheckAccountIin)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/payments/confirm-payment-by-account", wrapper.ConfirmPaymentByAccount)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/payments/create-payment-by-account", wrapper.CreatePaymentByAccount)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/payments/history", wrapper.GetPaymentHistory)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/payments/transactions", wrapper.GetTransactions)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/payments/transactions/{transactionID}", wrapper.GetTransactionByID)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/payments/transactions/{transactionID}/receipt", wrapper.GetTransactionReceipt)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/smepayments/client", wrapper.SmePaymentsClient)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/smepayments/dictionaries/banks", wrapper.GetBankList)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/smepayments/dictionaries/code", wrapper.GetKbeKodList)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/smepayments/dictionaries/kbk", wrapper.GetKbkList)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/smepayments/dictionaries/knp", wrapper.GetKnpList)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/smepayments/dictionaries/tax-authorities", wrapper.GetTaxAuthorityList)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/smepayments/employee-list", wrapper.SmePaymentsGetEmployeeList)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/smepayments/employee-list/new", wrapper.SmePaymentsCreateEmployee)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/smepayments/employee-list/{employeeID}/delete", wrapper.SmePaymentsDeleteEmployeeByID)
	})
	r.Group(func(r chi.Router) {
		r.Put(options.BaseURL+"/smepayments/employee-list/{employeeID}/edit", wrapper.SmePaymentsUpdateEmployeeByID)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/smepayments/otp", wrapper.SmePaymentsCreateOtp)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/smepayments/otp/{attemptID}/resend", wrapper.SmePaymentsOtpResend)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/smepayments/otp/{attemptID}/validate", wrapper.SmePaymentsOtpValidate)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/smepayments/transactions", wrapper.CreatePayment)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/smepayments/transactions/{transactionID}/confirm", wrapper.ConfirmPaymentSme)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/smepayments/transactions/{transactionID}/payment-order", wrapper.SmePaymentsGetPaymentOrder)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/smepayments/transactions/{transactionNumber}/payment-order-by-tr-number", wrapper.SmePaymentsGetPaymentOrderByTrNumber)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/smepayments/worktime", wrapper.SmePaymentsWorktime)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/tasks", wrapper.GetTasks)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/tasks/{taskID}", wrapper.GetTaskByID)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/user/accounts", wrapper.GetUserAccountsSME)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/user/accounts/{accountID}", wrapper.GetUserAccounts)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/user/cards", wrapper.UserCards)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/user/loans", wrapper.GetLoans)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/user/locale", wrapper.UsersUpdateUserLocale)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/user/profile", wrapper.ProfileDelete)
	})

	return r
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+y9e3Mbx5kv/FWmcPatld4AFHiRLKpq67zgRQoiiuTyokS2dLRDoElONJiBZwZaMX5V",
	"JYrxJSvHih3vicuOrcjJ7r5Vb20ORYsWRIpQ1fkEM18hn+RUP9090z3TPRcApGSv/klkEOh+uvvpp5/r",
	"73mv1LBbbdtClueWLrxXauuO3kIecuC/ao0GantzurXR0TcQ/qSJ3IZjtD3DtkoXSv7/8p8FD/wDzX/m",
	"7/ovg3t+L9j2d0vlkoH/vIn0JnJK5ZKlt1DpAh2uEo5XLrmNTdTSycDresf0ShdKt26VyiVkdVqlC++w",
	"/yiVS06ndKNcQnf0VtvEgzmdUrnkbbXxv13PMayN0t27ZTyH3bG8+syi7m0u4tVIyP7Cf+rv+0fBfb8b",
	"/Nrv+gf+bnDf7wX3ooXsBh/5XX/P3/X3/Rd+z//O72nBdvChvx/cj1bY1r3NaH06mxuTi97tGA5qli54",
	"TgfxK123nZaOF9rpGM1SudTS78wha8PbLF0YP1fGQ3rIwYO/U61M6pX1G++dv1sJ/z2R49+jY3fle9Nu",
	"m0ZDx7swyP4ED/09/8Dvav6Rv6v5B8E9f99/6neD+4pt4ad9TbdmSrduXTRMDzlTRsOVbMlj/6XfDbb9",
	"Hub2T/0v/C81/8Dv+U/9nr+n+U/8Xf8IPtjT/Kf+YfBQg507DD4O7gM7feB3/S7boHc7yNmKdmgdZh5Z",
	"w1Pz+xFy+zuly29fXrj89uVflMqlleXZ5ctvX67h+2B4qAXkxpYUrlF3HH0LljhjNzotdExXA/bhINjx",
	"X9BfK65I0268tjww22qb9hZC9ZlC+4KZAp9xsOM/9Y/InzT/C/+RfAdQNEuRbUiSe3kNXbabhGmn7SbK",
	"5FqOXfvl0AbMo2DR6uhouTo6ViqXqqPVgszJL2YJuUYTWY0tyYIeBff8rv8MSx7/QAPJ88zvhmez2//S",
	"nHBW6fLI+VC612zbRLpFKb/V3xl86X+KhcixHMVo9ezYWxOlMvnHuegfBU/Fave7tq/9R68jm7EFXXLs",
	"TrvIir6DC/7Sfznw4jbI1MewukV9Cwt49xLyfmq4nu1sTZsGlviGNYUJkgi1L/yvz8Bz9jVe7aHfjYvv",
	"2Boa/IBp8isprzjqVhzdcvUGJmNqqz7D/adC9h7BDT+QSV/Yd/8IXqcDcf9FwesJswwme+VrcanymclX",
	"R34PP5RYX/b3IrUyQzTL1vpccU46oyRtnf2xFr/gad1p5rhFu8G94P6wltaAOYe8DrxZyGnrjid7db70",
	"e/hWAInfkRuSuppT/ku/5x8GO8GHwKj7+Bsa/iLWmnvBfaJH+XtYaaZ/DbaJHh1sB/f9vdOqxfOE9nPf",
	"3FmrOaN7SLVKfz/4EL+uwUO8wt2sN7XIwSE6s/SqNXUPVTyjhYrctzmjZXiSlXzld/0XeG+JCD/0u3DD",
	"YGfxevb8nv/M34Nl/IboscGD4P0iizFhaulSDMs7NxEtw7A8tIGctHVc0e/UWvhkJWv5I5CyDSvaxdtP",
	"TyfYBm37hb+bKgNjZLfCmfpiniuGlUJoF9TfoZAZztMXmQvr6y7ypHIJi93f4CuMnzrG2f5LuNhdoDmN",
	"LpsMPKRjX2gjB8ziFfhJgtpv/S7WN3r+S3grIulyRC2xLnlFytr00uxMfYVJmJnZqfqKSoI0DQfB9KIG",
	"Qv0tZKBSuQRjYP0j96Yve7rjKUTL1/4uFoUcXwxbuLjh7AOIl2xFRGkEvlI1hKN7vtNaQ470CKjO0R+t",
	"dNxiKt9yCzESF7x2zfNQq+0V3Fl4Sl8GD4L7xOf0EnRyol4oyNbDiQbb3hXdvTXtIN1Dzdq6h5x/xCyn",
	"cpx8TvkZtJ5nmL2xjCHCpRds+4f+vnYKc6Dr6a228n0n093U8XyF5QxH8BRatx3UJ8VP/V4BWtdgqr6I",
	"fSVuWg8mfk2dUHhXFvWN9JPj7vJ2eJu7wQfBA8VRtZXu9tFy7sPCZC0bv0on7U/+rv+sOHE3XeNXCgrH",
	"qvlJXPZ0r+OmEvgYM0ZwP9gJtgkbPYW3qat+V7yOK30rDavSduwNB7n47w0bm/AewuzSRlYTH+gN1Qnj",
	"Bz+VSvr0Z9MH46dL4RX9Tq3jbdqO4W0xD9sGfrHzuz/ANMEKUg9s1z3N/7P/e//zQbxthAKFF+RctVQu",
	"nRsrlUvj5ws6QVZd5NQ2kFRF/av/DBgzMuNUoSo8SoUMU+TJu0u+jFxvym4aiAXRsCbL3O9L5O/4Lw3b",
	"8iihXIzkzC9dG5w10TRtx24jx6MDRmGmYm5qTiq+IvlWLpk5YomyUEJ63A/fOMewmxcdu5Wmffo9jerS",
	"XWDtXe0UuF2wWnpAohhgDuwFD+g9YCpHuH3BzmmBnLHq2NlKdbRSHeW3tUkUUatjmvpawn0cJ3vFTrHG",
	"Pzg2kserxUmObpy99kvU8CKejy7I3XIJy5sltO4gd3PKbm4NwOxNdNtooLq1Dnv0dw5aL10o/bczUdT6",
	"DPmle2Ym+ubdcsloGzl+U2+z77c3bQtlfX0Rf4lqwrBsWOGKfQtZUhne8w8wD2MT7gkRnFgNZFrWrr9H",
	"7iZENCKxmdzyaH/fESdldJf5fbqR74ymdK+xOWM3sGo+4CHpQ9HsGSeDe+YphJbJPyQCK3GTGnZT5dN6",
	"So0G8P7gm/Q9IQmfgnAxxsfGxrEwNCwmDEclM0EI0y2y0uCBYLYED5jCCk6nhLzr+Xsl7snLXHriCeTZ",
	"hVJbFowi2KycbDKtm42OCXwwKJeoPEd/AJ/Ah8wrU8Y8gEXfNnyC782+GJ3o+S9CEUe8S/tCHoL4ZIxW",
	"q9VqUmUsl9odp2270nhvfYaI1CdUf8VEPE8SEXzg7+OPBDYazbzD0cRltit5T8O21g2nRa3qqS2qXJz8",
	"3Q0v6y5sydPQZ0Pc2/zd5V6e0WoVnR9rVtDY+fOVifPnxyrn1/VGZXx9dKI6PjExWT07OvhlV9GUeSx9",
	"XxFyKMvGhsW0PHcJvftGqOYVqsM6B3BITNvWbeR4sMn9KNtpCoB6hjSKFpwmcgbkBmqgZND3U/hWSFNo",
	"jQhRsrQB6E/rHmplPi2hzcQmKXRMwxRhDa+jm1PIQutGw9AdWQjvL+DrvM8iQfhyYIEui9Od8v/if+F/",
	"cwbbDcTFTnW3XfI+nZZdlQQNU4ZVr89nBd2DXyfooglVScrwvYoss/+BTS2ws/4uFz0Q4pRuzePQS7Ob",
	"yKihMbNeYUKzCaq7c2hDN6VndYgtdC34BAu0Q78bfIBJ64+CMFeHkbCMrKbUQ65gEXm4dgAmIQQMyh8y",
	"svrgEELMsTGHispUaobHGRnT87yhUk4fh/HLeCCOf+YmJ0cmz/LWtN3BRnQ4kxUajWvRDeAkX0z7nKrN",
	"aw3mr9Fy3zFu7CnduiVZDc1cZemqu/0OPQ9uMpmnRXYZBp0w11UZVGKuDUtWZgyu2Lp0caJaHee4Gztb",
	"FdSs8XQyVvQ7i/oWcljsOU0rEL4bGyY1dC0nOrw142Oia398rCSz0xodx1Hkg34GQeVPSCQ55X6WLr+9",
	"IjuXpjxezUJjkiHzhJTLJaOJWm3bw1RLFfgvgeoPNco9LyBIeJ/q9Ed+D3hJXMHZSb3RRNWJClofPVeZ",
	"0MeblUl98mxl4q2x882JMTRWbUiV+FtrKTr8E2C0fWxEwAp38Vqlo1ht6Shf+4/KGrVnp7GuLvut3VRT",
	"oBLROfiijXlSKUT9x0SAJhMb+Usj3pmxqsxJi6fJKYNUq+GmHB0TraEx1ZR9CYriMqFNFPEZ5OmG6SqE",
	"+jPRRYNv9iG9098n9vSsbBdv62YHzWRct73wOndJciSLSee8eHE/C3dy/KbGWCd8/TlRI3uAkmI8KQqT",
	"j7D0yZc/qOSyksuWOJi4UOnH2ho8+MTRcBltDSLZVIw4neF0EPlOyc66l2nNL3JflbBOSEw5vmpxlpwH",
	"MWM0vBm7cQl5c4brTdFQrLuE3h2ae4JOAR4oOryKGOanumg7y8aGNThj0JvD2CSPs2GaXTaZpyHPll5C",
	"3uvhW0n7HVC4hNyOCW6VyGE0HG8K3gOvMeAOGFa7k7mQOv6SinwyQn6al7dcD7VO5OCmjOYvrswxzxbQ",
	"qdj8nOTXm8jyjPWtAalP9a5OrSyLQYWx8Ymz5946P0mKQ9LfvSJO0zlbt7jC0FAwDOq9Foo++y0xHcnj",
	"pW7aDfz0ukoz5FTwkb/rP/EP/Z5/dFpRq5hHYNG9YnvEzCFBdpVLHct4t4PqZEDY5bh/O1YQGy6gwJG5",
	"3JkdV1yOc30IwbWyFuzgA4P0pCOW0JsI0JHqYJr1i/f7MHhIA977ifgcF6GLfCW/7BiNrWXkugouqs9o",
	"wTYk9m+TOM8e/u8uqBj7EBX82Wp9+try9MLSrHCbJpoTo+hsdbIy2hxFlYmz49WKPto8lx38TQ8ZsnBg",
	"Yr9isUT5fkFGtwbGJsvV2Q8jnIkIYwahHnJadctDDpZ5qq0DHfvA39VO0WpuWMARvnmnSYCVX0iwg5W6",
	"Z3iLmYnTg2IRUvLKzyhG/YqGf5gyHltDWYibxngj99XZqFuvKiVFXYOXJurjDqyfSL1Xx5/vEleQaeKJ",
	"AYWAxdNP5uwNu+MNrLe4nm6a/TwzwQ6IiV1qPpOsKcjFwc/E99JYpkJsVCbfao41z05ONsfXz2e/WAnN",
	"SVhDzu17k7QjxpfzRpRzbi8rW5jeRI1bDFPEsIaWvJmV1P1h8ClVTJKbNkhNb4beKFb3Mlrz7pntetOb",
	"urWBZgx3reO4iFjQlufY5lKYFjsIz7Yow6QbwdHkV/D38WuoSNH7nSQFr4ef6OCB/1yDhxky+DJ3rkVd",
	"nzBPgf2a1R1zawm19a3hbJCDh4pK9NL2KZqafh9obOsKl/7CzML83DWtovn/4X/uP7pu/Xz5pzM3L67O",
	"zeHPHpHPcmQvsgnKAq1FtqzpTnnujO7pw9myNCE0VEsLEx+ajmQFyy30Q1uEY68bJppBJvLQm9e7z9d7",
	"sbNmGu4msyeHxstrugU1MCD8XLmnnYq3fbZhRMN/STOTj3LlpKbJlZrn6Y1N1JyxG8Sz2qdzb1m/jZY7",
	"zm00qJ9FbzYN/EXdrOWwdJ9CzK/nHwIH0XQPYDPIKCDGJW/34F8E7zPt5xkNcZOKP4oItK+d0+Bt3w4e",
	"gom1l23A6c0mVBZl7Ha0SzX6A/zboTlfMojEJ6I3vEXkuDnriUARgWQNymmQxDGSl7mi5U7zU2NaWoZF",
	"fS6jcY4rl1Cz0+hTzpA41BHWcnvUhn/Gh6ZybBNq6YaZ+R7Dl+Db7cLVx37Xf0nZD5MKn0OgZaSo8R3z",
	"TRHSxQ2MmJMRm+CEnHJwGZnmD8t1LST5Tm3VZwZP9I0KCpQ5/ydWn5WnliDvTkXF37Vmk4Gr9fvOJSGB",
	"gl+D4v6CqO1wPWVYbBHIw1Mow9kVi3D2GFRb7CE1HG+zqW+llU3fA00jtEqlQHCiY2dyslqpjlZGz0oK",
	"rySyNUcSUizHReoeN9y2qW9BGEqGqAYVKg/Ji08SGJiY+57fpmA7rC/bl2ZjUGA7J6dlis/uO5q18Eya",
	"spPhB1MmcqX42XKcUZ+TmrrrKRM2dv0XBJojD5f4X8Oj84Ipt7LZWkazaSLFfN8E9zm4m16OGX8HRUmH",
	"kA0EGJZ+N/hQNq8ln/EL/0Wulf3Ofw5VGfcy1WqLpEOEu8r8jDEmK0cXNbowMZYvLq5o1r7XHo5Snt9f",
	"zNHAu47jYriwr1XE4biqmwYWOsdqeX7m74H6ux96U4hjUProDBV2pQwz0cq7neATmSMRqu/y2cVx8opv",
	"+Wob7/abB/DNA/jmAXzzAL6mDyB84rZtyyX3fwm57dlW2ysmpCTzxM+GT6iOgAuIv2Wf5NXvwFXbDz7C",
	"5w87Qx9IgNNYrM86ji27yd/4T4LfQNEOJxrv45v2kd/1n4CXsLZYH9H8z+IAgMF9DQj6NTX3e/yDFR7g",
	"eyVEpi79YqQKUVRBZiJGV4Jt1g1kNmPOqEXht0mplVzbvxAMVC3YJonpWBjRtF//BRbvUC4Cwv15tOqe",
	"f1BKcoDIbYTyJKOEePp5XyARdyLxqkSpJrppLqyXLryTrhBdsS20Vbp7o5ysY9mFUqCjYJtHNrlbBhfo",
	"lNHIUfsirdOAX1uKX3+d49d9V8bIBlzvmGZaGnhmgK9cMtZ0Ky05PkRA7OHXKPgofPJO1adq86dVQ87p",
	"rjdjbBhSL/MfQODvkho6LpGbKoJHQqSTFjmFzytDITsVe4p3g/vBx5oAfjIhc7VSrMw9kCm7p4s+kAVD",
	"prG94FLJYeOZ2A4PkmOSiNsirk25ghGAEBHTUkcSfCMzQsq+J3HFJ6ZLLfDJzKwLEaKig16+Mss1+KAx",
	"5jBosXxltr4YxZ7Fzy//KvpL7bZumPqaYRreVvgj8pmJpnRTtxqIfH5DwgG1UAzXraZx22h2dFOx0s/l",
	"AQEWIYY6J3qJ/uJ/pUEBIuSAgQISfJwQglZBCZERkZCW8HA6P4OvCNs0vBzROI/xX/yv1I7AvPhg42Na",
	"BeyIsnZuQvvbvc81AC7bI+llwf2yNjp2Hj6HUrBt4v6nsgAA2e75Xfzul7Wxs+dkX4Q8OGxqPGG/7ILE",
	"iG2LHBqOv7AUMA0OQXrbYuEraYytaFQlFPJlgrb4HVQxbOPVS9Pp4OmUI88UhelIXMiu/0wD1WefMhtj",
	"juB9BtYHmvpvckg/uheMNOl+drxNCpChllwWuuMte6idJbnm2fdYTkd2NLLjEawkCIx1XOT0k/CELxwN",
	"+sjqGEtrjbfOTp4dHa000eS5ysS5yfWKPjbeqKw33kJjjYnquUZ1PXMz6YJCKlW7SYG11LtZX2Q6g0QR",
	"dSIzLdWGyzC5kpbRiRyhasvC2fklCuYT3RTVpq6EpCTytWgUVo3ulRPIKwQMOzGsMEp8NLFs9axSPO7/",
	"6VeFTk/qS/2p1X9VORiODwmEIP7TIXl6QJdL3zDphgAAilT0Z1ab4O8oxxWTM5Kju9JNJ6uMsi2f+bu0",
	"Ickz8nmE/kesvVyx9Nk7HnIs3SQUJ2PmxEHnqkBe8QtCzQWST8Ib3cEDCTIhoS2SmoszF6W+Hf3ORcME",
	"qNmcePgUIKznH1bA4QO88h1oprv+c87KUdIyKis/aNmWt+kqeJmbKkrrKNMEkMgBDYk2R8GO/z2ph+Bx",
	"GgXn0LkEAZK31i1FxyJuVEit9IpDBdScYUkeC7fTbpsGkvstbttmRyrvY7SFg4Q/UZMhFGIlyEEN5Hjz",
	"tvwFQnhjljdtR/VKxd0adLDkT6Xk2c0t+b13kWPoZgStnvT9EphgVrqq+vsV5LoU/DRjP/kJhdHjQ0kX",
	"QpILL9oOURfcdnJJpiHFB3lMHaEHnAlB+jq+H3P2c123IpUp5eUjfQdWl+r5Zg3LTu7Rxj/ZyigsSZwq",
	"ZXfebIu4LRzOIylWVeuXjfhX5WKalmVR9OhnUAD2MUHfZqDSu2LvI764e5+0eshVRxzRs4KcljLrUIjt",
	"JdaQsSnhyJKkdMvbNLdouE/RtG4fzGFi54o+BLpa9o58yD7mNkPATDk3Pj4+DkldRqvTKl2QQlvat5Ej",
	"ZIKrEx6Fsi9+pvOTk5OTWRMJVV85yidXuO+HB2V7KbmZxKsf6yVzivu3WGyn/YT/XqyoTXzycywwbm+I",
	"NW6xk49vu7gyOX9ZDWTGSnDlkikrp/PPLOLEs1a3/0auRdIEpUuDWhK1EKFxq1Q8kESEOdvdjaA9lmO4",
	"iObDXq6tLKRDSnzp/87/1v8GXFag1G4zcKfvSLov15GGxsrzTVtvppb5DHnilMDDM95o+sJ/lCdsMICL",
	"PCNMmv3ztu45trXVktqg8ehu9nBux7HyRKaLuv7Bc08DtWyOMsfY6mtxFTnGOnfd5VfEQb8krZqWkE5D",
	"rbJ2qMGHpHMVyV2gddnaqdARnXx+tX9agqFR859OX7huadr/rf1TvT1vexftjtX8J/aJpTc84zZa0e8A",
	"XA77fEW/M4PWvEUHucjy2Ke1lrnE6GWfWR3T/Cftb/d+z3nFXyZIxuKaoacFO1gbgEKiyE4UJ8yDq0+7",
	"dmR0/1BocC9pxGo/uIelY7hB8zcXlxYuLc0uL9M1iV/chVwaMuYHtCh9n/52ZmF+VvWjZ+D63Q/uUWN6",
	"l/5mdmlpYYn+KIpg44PFR4yt3ecVLMzDomw84h41bvc0CKc/JWng0LkUnEtcC8I90Lu6/iGJJ4cz9Pzn",
	"p8kB0BANt/BSuYSXUiqXgDqxIzv9S9KIFJldri/+KcGj8XPQTvmfc8uhUELBx5Ttsa0N79xDrn4y5O7a",
	"4uLSwtXZGekhdFmte3T9gx12lB+ROyTkJNBBl2Z/Nju9oh70iBXZ9CDtAw/8gP50cXZ+pj5/Sc1IpLyJ",
	"cKq/r9WtRdpqhp0hJEEI58TWWCqXGGWlconOJB4V99UMQ5S1v5EcolS8yfHJ1TKuFZnEefILIHjxJPit",
	"fxDmGoTbJA+a5BMFSZy/nPvSSrHDxc2AWj/VNtheex6hJpJrDPsQBic1Rl3/RcT5YmYjQWn4ODQdgk/9",
	"77WFlUUp4KmjelO+ETNLBIkdPCBJTqQG6mHU4SwDlItMlqqG9TVuTjmfPoz8aFMONAb0nhWOd+VJh2EX",
	"O64iLxauIy2z8pjAUUg/u4aDEqVY4G3kuIZtLXdasUxVZSXhc1IYFWUnwdpIqm2XOF+FJVI/xm4kpkOL",
	"jdaI0ffwPruOIyDnxN1dd+zWtBoYlO8FEHwCDmlJ38zLb69IE0Q8O4/l/Ix05qL5JwmcwnXT1j0ZBLBn",
	"D0z49E+vlbWl1anTOaKKnDU6HcEbCvt3I3n2EVf3ffBJxRPLj6GcfI7jiScU5TqaDK7i4WYZ5UQmxnOX",
	"crOao2jGHOwE97DwChmhHGopNLaADclQT6H4G13/MNiO3a/XjMEVeyibrCjnZ/WAiFcuMIZPBzGWMian",
	"d02tXrs5vbq0NDs/fa1ULi3Pzs1F/y2oXbFvJqPzmLNzAoQo0uZi+fAkm3t8YmL87PjE2bPKSfu6TlHs",
	"aqJaHamOntwFk0A7x0B9E8vcsKWI8v/OmrO/pE/vSxpRjeRvVnOfY0F71tcaUSb+K5MbET3VEXzI1VGF",
	"MCnGtArBwh/p2Nnzk+eWVqeqbBeGLcUE+LmTFGrCQpdWpzJ5N+52EphNFBrCbY5dNv6cykq1wCHFODHZ",
	"KNUVJZJW7clixkZ68xnR3zWA9Rb2M1+4sjg3uyIaxYJM5r9RtDKtPqNqps2hjow2xterY+uVifFzzcrE",
	"+uRa5fy5s29Vzk1OrE80zp+frK6fz1biYk3D02yUqOWQPJCwRvX5XHZFGBOX5IcUg81122rIWyBJvZhX",
	"503gSnceRUYkOPpYYvoT6nXdl/GPTWpIyYLl3lsCrY4pYFkiPS3ylIbymRr2Ur8V624WUit4RTmbH8iJ",
	"9i4VtdhrX+yYZvj1Qe5iuIluhyWkHXMNKJzQp/7n/dzEYvg/CUdQOXFZeSbIZPJX5yIKN2pdN1100i4j",
	"JrBpcbJhWxopSiqXfm47tzyjhTR0p0E2oFyqW25nfd1oGMjytPWO1cRHMG9rJGKh6eE7t2rpaybSPFtr",
	"OzZmPo1Ct4uvgGyO4/JisaWOVqtYIR+tjo7D/02Mkv8bI/9XFSmk3+7PBZZs8a8iqj5fX6nX5upvx/3I",
	"YhQgejUFIsVfD/OS8wsgzTil9/4UoFMyGD8hP5DUCuxrFQ1fD/97Uv4Uiz+xHG8mbLtC7CV4X9JdUmyu",
	"fPZsFZ2fqFYraGxyrTIx2pyo6G+NnqtMTJw7d/bsBNahq/0LmUgGSAUJp59Kwfm38iLyA48nUmZCb1HK",
	"3JwlIAW+nL1juJ6qy8ghFRr7grEAAes9TTh8rlPIvtS9PdwVl2Pkp+0AQIamJE8JfRJSsqZ43xrJl9rW",
	"hIDeUfDAfyHYqiSaHWaoxeNiNH2aVnvw/eALtWtgRyxRCQ2X/nGhjSzD2qixgix5XVXeYF5IZ/yc46aR",
	"cvoyv++pp5fdWjbyClCZSRwQxJSbXV3Cr84yln/T89ekxWczAiBIIs3nKjG8CBXrOsRL8StRKietzn1s",
	"7Edh6yT8oaSuGE9+xW4iU8qA4MIFlwjBWCThSyodxXJ2A/ChtbekLpEfIrZjueRCW4p0f6Co4faiB4mD",
	"nhf4o2Y1HRvmMxbw4/lztCY+m+RjBTECO6iPPz9VETB7dWQsT6YXo0BknsQZC7sXp1525/h2NZLwGU22",
	"TEOwTJa3KcDpsTzdI+43UbDuaj9bXpjXTtE0jC5sF9ELNML+wQ7+IrjVHiiGP12SrK5pNLybRrOomiOl",
	"/kDivusx3yGo9NsM4IXbjjwMX5DA3DueZ+70LLHcZ3uKJFrgU30IDjeuRFYacrGdJnJuwuXkRGy1LAvS",
	"HnL4OUekIpUmdBxAahU0NwxvWg9CBKzkPZJcJBtHYqHxqQZcVu1tbAVJy297Ue4OnzFVIS6D2J4lSd71",
	"X2C2fp9IA0WVMHgriSJ8T9ouVY5Vwhi+TK4uv89Zt581q0oKAdUlqs+EyUxdBr4iJZqvfQwnyaXvEKJk",
	"ak5b3zAs3aNSOR5VCh3SlLHC/iayKqrIOc7MmW7wAcjpXA0Qma9LycOC/12YQqMt/bq0IUmwrVXzTJto",
	"lLaBGPaM9Jxd2xH7jxcEnQy5ip1fFjPNGbJon0k/TStol6Aeq8VbvrwM/onLWiqQKF9dDNJenj/3Pb4K",
	"WuSVxGf8BKTR94JT3LR1ix+y1vHsK6S4KP6nK7rV0U3FH9nvro7J1Vzl0z5wFXue12XdMNFcjnKeI9JB",
	"uus/i3X+hrbSz/1DqWpmbFhST9yneGBNIY1filk/itpyEZuOs2k9wzNzpXjL9kuJ8JAnrYh1urqdTxdN",
	"zM9j8lFhQ18yUYMJthkyRY8gGsVVgFM0Dk8m4tFHBC1MJazqoVsqWgvHKWyLw/OVXkb2ZtmOaG3+CPic",
	"c9WT8X8UrJ9nVew2hDZbBBovuhSiwhc1usxrf03CHame6JUZDEsoiRqkOhOCp7SMHGyt1jYcJEALtQGj",
	"XDdndE9P/JFAJhi2lfgLFo8mujNtWy6yPFLHwf62bli61TCsjRXktNy6tW6rmSPeejUhR/rLKM1SZIpm",
	"kibF22CppcmOs/2s8vN4giytXePEC4AAnWTSbKKRTmo2CnTPOUMa5iRSGxvpXkguyyRPc3touZ3Z7YK0",
	"AaRWLIBEJbDgSrFAVLWa6TYic5fFFaXvHnSK0hueIsB4yIoCf01MFv8IxFu8TXgMWCis2VeX4fHlowJQ",
	"1nfEXa5qia96k/7g78JPD6m3/UAA4FU/TsDc4ZxZ68yAD2XQAYRI6cazjg5MKg+ku+RolFEYLKbPMa18",
	"hZB9jS57HKn7o6HkcNb9I0bQf4K+c0C9M2Ff0Jf4MlLm4NF39cqvapW3q5XJkZv/108qN37y/3CfVG78",
	"5Pr1EfrBjffGynKMXoZBzUIPg2BKvwGN/pGBRjcLNwqiS+uqmOQNNvWPE5sa7FAVQHU/eNScZKL4O0NA",
	"uxcyXDT/G/+R/1kKILFaR9qDFL0eVOcrHebpm3u2Wq2OgNbE8ez1600sta9fb76n4NsfkTx9c+9/gPee",
	"mhCLyDFsaQIrRJy7LDciuianrl27du3KldOxcPPYhPz8zsmOLycgvlTehCDMPP1qucP7JgbTgtWN2Ipr",
	"v8XGyqv1Fhm1L23XcWwnAqzI36ZUwOmX3i9lcnci/zRjpAKWW+pIin4xapOLR1zMx2vFYH1UmThOB5Iu",
	"XcXTGuU5fMA8kyFcogJn8oBEXp8JIJxckgWn+O4SByRYtmUtuD/iH4xopA0kA1n4DTm0JNw7eHdW61Lh",
	"bm/Yq47MsPqKPMsMnDsi8FSwHfeIgx4dU939/dNDRiflEvIkZgJZYwx/QnxTdbdtFLqfwqlHm5XFlXO2",
	"bqk4U94soT7/ujRLaFD/0Uw6qJTEtSNoQRXPaKG08edz+JRyuI+MllyYPc7HotLElo7nerrVNKyN2rA6",
	"bYiFdLCwQ0pFWLnI6lBAxaDpSfSogh2SqABv7NCI+tdMyLwMkmQ3J96YoT5fkm1pfDUJxohxIjtp2dW7",
	"aJhotW3aejMGthCreDdM1bN5xOpIJTE3OVowz+9rhqWDwiSNKqYrljRqSCykF1S13YfYFCki/YjCVNIG",
	"NYJA85DrjbSb2aDosHSOnKxdVGKiy8v3uM35XgTDD5eXR+wqqPKImIgfJjKbCkgQmm22yxpxC3uG11/W",
	"mrqnj8AY85L/Ho3+OSY71g3H7rSHkAFl2Z7UqDrE28n1ZogCvOyxo4KM87UDxNEhtXAeEr83IEZQyZld",
	"mIS3N0zHSm84JezoP5S1f/iHsna9U62OI/b/7IMG+/9/KGumcQuVNYP+n1Uk2PIHmmYVie3scHSMwaLl",
	"yfjsEvJq7bZj30ZNCiS5DOUimTCSy7nq+VJxIkekD5sboVXlqHqM6j5oO8IYhFqyRRj5on8EZTv36LXt",
	"Bu+L9ZFC3yuOWfhGBYkYKjQSCnaCT1gjMdBcR9TVYJkg65w5lAKiucwqfITNUxw3KGqG6ynqexUw8kKA",
	"mClSBQLDISx+job+IZkh3v3VsQJGT542Aydm1shZPFqYIlU3lIUHECpgeM3J9yU2/XVLyrU/HGtHVaQI",
	"u1VQ4VfuEmS4JHr4keQYvkNYlH4dVZfRbOysczldyN5KM7CSd6EeMW8+3k04roWfZDtEuvEhKaNFb9Hf",
	"7v3R/0xSaIO36zD4BBoJ0n5ToVlLXtZ9cCACrDXNMhMzjXdo5RrtThg8JEVqB5CpfT94QJomkAoVcsxP",
	"iF8RRvzf/7//Gd8z4X8f/u3e1wM6dSS7kdiLL+F5InNCLypwR3BMGOz8dyklcegGmh7FU5WPSVKwkxUi",
	"XtmSMTo0/4V2Cj8F8ZRm4UnI02iEwmnnejoUj4FEr+RaWlxZK9z9gyU2MmTg7DYgRJSAwAeYJ1A+v4NM",
	"PIo8NaJdL41Wr5dOyzBh2uBZvqLqE8L7x08pSYzah5yW9Q95ru4fkoP0cwrKQ4n8UyO7YWDy/JaFn6ta",
	"lYinGduuOA35LsVynG5ps5wjWvHA4A+2KSzyASkepmwgwD1r7LXYh4KSneA34THtxzhmMGEskbv/QUTf",
	"E/JwBR+JM/YYPAkTmfTPorCm8hf4AeQ1th+FW13WOBlLJyGaMkQB+Bpc0iqcWe/BQ4XINcRnrBgL8W9g",
	"EfEt2b8/Q7N5uFuANFhgZdjiGNNo6s9uvwJd3AoFJwsJVwrzDAnfydDipaUptJFGL4z04ANXZTfl6xMl",
	"JIpl5UrGVqDaCyHuptoM8UtD2w1Z5CvfTojRwsytiNGv2AtoKGHpJgAHuFHzlTfGexoEzUkb3ZfX0GW7",
	"qTa7G3Yzk0X9L7Fqe8b/0v/Gf8r1T83Lf4SGQkb45bVbA9P8qf9lfgqL+QguW+2ByfsaMhHzkWe1C5E3",
	"Z+uWO4M83TDdKFFZXtSmm+YMWvNW7EV9K8drqB66xg9EQDV4GI3kpWjrjmfopkBb37Mvxgcbyq3j13Cj",
	"6F7XYjurSuHKEUoql0x9jSBfZHTNgq+FmSSFaV6UnEm8ILVlDPeo5mBE6uJcPAZemOfGTRav4snFuQfe",
	"tTm2R+LWbVLdX9Y6slaMHVqGVegXcQhDAyKA0Sg8DQMvf148x1ybICMwByFqZwNKyL2++Qnun0F9F6md",
	"wuBLd0nJcZ62YnQ6Ltacx65djL5KepElatujoSgtbAHl+L4o95joGgoV12R/znrmaD9bcFblbozHJs+u",
	"PA/JUCxjWHiibJyOCaFuAgs6DGhRHggv7TdcbEwJRxrCrSlxSfGOeA35fkytXptdgosTYcxCseC83kLa",
	"Cukcl5BcISy48MMr15akdRzTs0sr8wvid2vTF0ffujg7NTZaIYATExVpoVa5NFefn81/Yl5jzrCQCgT2",
	"yupSbar209rV2tzqrEjQ6ORkeXJyckROxOJ87cpsfAULFy/OzlZqtZr0F0v16dgvzp4vn6tWR8Ymld+/",
	"eeXa0s3Zf1ytX63Nzc6vFPn51YW51TiJ4yMTo2OT42dHpViMS7OLC0sr9flLK/UrszO1ldiPq5MXxkcv",
	"VM+NvHVuVBt9S7uI1rSx6pgUU3t5dm4uzka1S3gFki+vLKzU5iQnkHoA8P0kkal0xW4M48OQ3DLlfoEk",
	"jrnZISoOJ85Osh3lCWdcFB0WZW7FnV1CZpYDxuG/MpDHgaZCEv8L9ULRikr/OYN0Ir5HEoEKHuCvYnP6",
	"A/yrvEKeX1SmoBeXp9im5YbtkDpk6omIZwi6cuy6kNyET/aQ7hcJvQghCn+fZG4R9yaUMtB0rWcQiXsB",
	"qZCFG5pDIqNEXoVuRgmYvKebi0NOVJM2L012fI0qb4WaFRK8hNaQymJQv6v0UEpWxHYy78kvobC0XX7m",
	"DdsiQAk5+DQcajr60d1yCam4K0PPzMmmKtt+uePcRlu0XadErekYZpOieqjzPCkE1VEchoLD2lVUF3qO",
	"0chUUC/rns265K6bupdRyEzRrwCBLHjAdfv7NQlA5GmdaLgXO6aZltDAvHnUqR58wooBIydfyNBcZ1NF",
	"WkNDNw1vq8hGOGgjR8CB/4WLPM9EecwX+a9qDtIL/dJzEPKk2fxUvCt2RlVwR87kRhojT9uWpze8RQC3",
	"kGUdOspaqLAtq/AcsSOkFA9UX9XH2O1N28rEJwIA0vkwqOkgcyUHqpH4YiZfSApgGW2ZUHZECEs9i6jL",
	"ZcxVFYHK5Kg7fAqYnuRSdXlQQ7J3XYozzsFEPqXtTJ/SbG1STE56jzyloulcLOZMvBes/cio5CT0SERm",
	"SWRBpOKfZjSt/iJXf2ppMv6mYTYdZCnSohNxdpJ+QprK50DWa/C3KdM4TyhywfuUtwtkSMiusUSBQXHY",
	"hkKhO8SwCNLDXPhLpDg91yyxsJjqwQVAWIUfxCCXPV8motH8xZU5bJXKNsgNp8nT54IjKoG6Lg5UJkQq",
	"Lv6K7t7KdKU1oNND86b0IQ8rehNY/aTuArAacxertPUt09ZT8sxJEI+WH3FJPrvx+ZKAjnlDj8lBOG1b",
	"d2/1IRJk+0CB0bgmSuPn+CLTd6qVSb2yfuO983cr4b8ncvx7dOyuivSbXipEdMbiO+1mNh/4Pf8JTYeM",
	"GuL1wQtx44BsPL8MzuPF2KbM86pAcAr/uyyUJ+f+trzeSY1QqsQ8vekav5KDYKYiqiYHw3uQKd3DLc8r",
	"zPFeSG1PbI/dVHVKI6bivjKZjScjA7ONn6jMEFqjjWPrVh7lnVrH27Qdw9tSB2c7G83Mnfuz/3vowZNz",
	"16Jp8wdpmQ9Y0eOxhbBuuLxpO6GimryNHZLAnX5v4iPR391IoUma0+I4tsPgvTKNMfj2FXcj15df1VpN",
	"LHKT0XCwz9GUYzQVZY5Ce5qoef2uFvutzG7UW2ZfI4c/kw5qeca6o3eaBQcNfyYdtN0Xoe0UOtd0U7ca",
	"aBWEslNs4NhvpcMbnmPc6Ydq4ZfSoV3d7Wvg6HfSYb3+RvXSBm3oTpP1E3GLDSz+VDq4bZpIlf+fNnL0",
	"O8Wwtw2nn70Qfikd2mkVHNFpSQdqorbtGkW3NPyVdEgDtkRX4dQoB41+Jx02DW4zZdTwZ7JB1w0TXero",
	"TkEhE/1MOqjtIGPDqjU84zb1phUYOvZj2QS/NBy9H7bificdtmM0ttx+34nEr2VT3NLdttHP6PwPpQOj",
	"rYZp67cWHftOwR0XfyodfKPZF83hz6TOVuM2suR5nSljhr+S+291q+h48BNpZabtGetGSkKEelDxp4re",
	"l8WGxD8oS7PttvoQCuGv0oZcbqH+Rl1uyU+8fWutHzaKfiYdlDRSNKyNvsaO/1o2xbv6r9q26/UzvvhT",
	"eQ7xOnIcaTfwlHHDX8mGdEkoqtiI7EfSAZFt9bN67nfSYVt9KUrRz6SDtvvSOKKfSQHfdffWFd3SN4oq",
	"uPwPpQPbt5Aldx+kjcp+JRuy49K2MvnHIz/J7KzDvkZkEa9YxAVe/EnhXwJez+WPkknimO7Hiaq4Iiso",
	"TLyU4A0r/sEQDkPyXgv6Aa/jRJdDYGmeaRJmYvzqczqjYEBwxpVEGgkqtihgORnAG31U041YJGFkibpE",
	"zEyK618yM7tutTue0suBGsjxLFueEgu2vItteUvuFYhXrdDBkj+VE+YhB7k0v2BG3rPta0j12A/rw18x",
	"6PkRR043iW0+RHDzeluRKeO6HcPaCP1dUsDIBNJv8LBM6mme+rv+XvARcd8nm77xsJVQoMOwqHZJ0/Mj",
	"QGfZD3ER/vbr/3lWjnCxYbgegR6ZRo6X4bxl2G8sVMl6RgJS6z0oxe0yryxr001QpCVIn1n0ZIB8pcyn",
	"XS9tbW1tVVqtSrN5vRSHqByvVM9WRt/K5IEEMZL9KicPWsYlYerAwA1kvvR/53/rf5M3xpkGxhgN1S/U",
	"ZNoQbd1BllcUXfMe64XPGIwV9BZYtxyzkaI4QGAkpE16VlCCxURVrNlAQcgMsRoMkBMe+s+CBwTGmGZ8",
	"Q40yD/UtuRau0WS9jlPr9eBWPOPgI+VJOSoRGnJGRLb0mig2jWE7yyFBs+vewhbNkvFvDetEPvW/HOAo",
	"MneOlNLl2rJbg+zXrZTNstrD2qyv/UfHuVmksC/PZtGyvv42i+6HdOQ5w7q1QP4rMbiqYv1b6NN0jw85",
	"cgEYGYbPqmPmLjvvKEBm5gw5JgiXfLu6NJdQvpLVPfkyXrmNSaa9ri7NaQB48JxEsiPImKwU4Hg/GkgE",
	"jvej+QRcnvq7r5zaPb9HC563SZ53V4vas0ION4Ak4EPcetXEcsnqB/5+UusX+SAim+y0lONSsGIHQjEW",
	"W/JIswMLFn1yeWgzHaTQHR+TunMN5FfPPwo+5FELedwI0u83vbpeO3VWg6S7ff95Ege9MjpWqY7lAeDf",
	"1N0ZZEp7AIQZubEyfMqS9BrtMzBJMb2cS7uPiBZhh5V4iwXqSyEPW6gbBRiYBlQ7Ghk4mCEGkQRAAHPP",
	"9+SeaRTHgRhF+9CtPkT5oSvOWuu5t6TJJo7d7DTS+9bJ+0cRMIOnpHl4zHKdW6jNq1DSshOr4neDKzNb",
	"uLI4N7syO6MefGngSm6iInP7wrGoSkrkyXaNLyuWx6HfyYkGFa+1UG7X2Srry5w895a0W8UfoQajr8lG",
	"5TPF8y4AWhSvVbmT7Xaqp+WbqLV9SFjwIGt383tg0tivf4eMesxhO2ToHkYFKKm7GZGY1oytnHwewNHy",
	"2H/k/+mVe7sy2sgd+1YPvfenug+nrNsmpYK9/oCxo05R3NRdcMOiwtA3JPW76+8x5SD+KjxXpLQPFdwm",
	"oj/lREJN6E1/7h96f27ZHfuRtelmdq+093BKz2EFsyvKfov1nA22/e9A0hOY0kimYsuGqZrPCAgj6KY7",
	"+REb5Fd0sA61sVHV8i+jXocC5nNCjKulH5cCzueBLAPJCvGDHrNW8PXeViCZCdPOLNUurmQ+WuK6ZFSp",
	"tm1aNxtYO0hJa+84bdtFcvUUUNC6BFGV1Evu+C+pOcbj3sdfjN0iDLNIKGDVh6mMwqhVrZcBqRQBl40b",
	"VTGeajTy2Oo0ykwyBEMacuK+LIRr2NTdhdvIaXYUWFkFTNe41aobzTyVyYvse3ntx5ipqGHzUKto/pec",
	"M2Fp9mJ9vjY/PYv/8KfUevB9Tleilmb4a6mKhAlvdqQv1+/hHfg1AQiWtYXOxaPLdAJlVn9MhWEHGLcz",
	"wxMQj7EcsljEOtyqMjg9pev0I1EdYH4robdPEgvWbqhE58v4eAWaTRfRlpITSQgva8H7pD4meEiaHVCv",
	"ibY8DkiOPVqPAm6iXf+lqqPr699iu1Cr7DI9QW7LMzgoig8kFAcKee53i3Utb3A8mVMChmx8F9/+ZlYP",
	"LImLM6Mtlhz/l0/+GG6zKSFJg/L1HtRDfawR36Z/kL6rND31GIhLm1Y7Jaf1NHXCOZn9yUi/mF3S9aCP",
	"U/EQSdVPLbbC30nGwikPCRvHUx3xFp0mwQOqu8LrKAN32fyAoD2nOo76jxpwHsu75Ti2t5hbQ0TtASh1",
	"B8EO6T0BIBwKeYacFsuY6kNh/ICan4MoiyscCbk0RpCFIth12F5VWI/q6BMzJhf+byDLn5JEogy1eDBX",
	"RS9ykOKLnDFXBj810breMb2UE43lX1BZkPB5UkWagdWT+OEBa6wHcqfnH4bS+hOp+8FIJ6NHxCm8wNRv",
	"fZ+GA9NcsONVcIcbLaxRSp3lfcsbYuaLYqSU3FUVX7kxeMwUV/jf7v1R0tyJj4cR5PeBkGWbGXL9CaRE",
	"fA+x248gPS2O+BQPE56tVEdzhQnj3gD8JeW2qU1ZZTWHAHbBcUoB7GoVIJe9vp6N9LgAX0qiVuqWynJv",
	"hP3gBpMXPO5mdxh5dfEB+86uyxxoyDl2hXcipVtSSFpKI2lyr1JCG5mhlTSOmua/myeQMrzASLrYkrpZ",
	"eknp8SIWzk+RXsNTynOIsGEJwj4kXipaN7frqTG/7Nayrz6BPR9+4LFG9+a5LorM22Q0kRVVpSyxYfEM",
	"yj811+xlYyP65AZPtPJXiU1bYK+IeDJuZ20F3VH6dmCnDrAilJ09RXd/X8PGPNXjgp24HncKn0QIOxJ2",
	"/QxTsLr+C3BYHEK+WEUbq4q5QVjr18Y1/4V/6B9pf7vfLYMhoJ3F8ncfsvslRoV0hd8Ka3tJyeQ7u+0T",
	"aFK25C5pkSPkMDG3ypOorZK/e3rwZX4aPKCp+T3S+p0RcRiN1fOPYIDnNBJH2srCkDQG+IK1QDqC03pJ",
	"YVOH4EYS1jv4ch+TPY0qIqiLYDf4CLMTtWdz54DCiZdD7pbdzwVrzdadpmFt4K/kMbvCu8A2Q+KWkso3",
	"aXIf9DbhB81lTqW10Yr4ObWdvrjxX8HZdGl7bG20yq6W/23fPdmJhzS1M3tEwabntd0LZ8644yOu3UKm",
	"Yd0aaditM0ZrY6Sdo/cvrU8ghGV3ERSPPUXVxyzkZrggRJbQaGvUxILDhmp2OLkW/BbkCL4wub0TEpbN",
	"8k2QVUg3QgI0n8R4NZqzjjNvZ8GqUkTFHr6t/hNimJMlT9WlyXZrRpOi9iQsqV7oWA+TAdIHWlBciCnW",
	"vhxa22l/u/d7jXqiiCCPfFGKga9mKxqcd8vvnmGRKjh7OSpjCHQvTVf9wv8m0XUP7hHRWkhlGt5uqRRn",
	"OkmmipWEUWogx5uX10famFFWDCnFn4HYfsGyUEkPwV3t1OZmq+W6LdeVds61vcY0VjCRs6g7pLZP9p0B",
	"OW9hZVoxeUHOSxmo4+hr+qauWgH7e8hIsi+p2Nf/DPJ5n4D/HIuSvTBJiudo1mKWYN5RTOxdeETBIfGE",
	"9Wgk8TTSz40mDYUXQGA6CFEnjWXHaMhY4N9hZDlCLQlOTqea+8k84OQ4HaexqbsomwmF65jFhuGw6cFn",
	"fkhF0WUmYYpqywwCHdS2HS971XtyRukF9yEd7D537pkz4vcge8YouScsHH3KAqnB+7SMLtgB/8h9atVm",
	"TO0i08QvW+bcj/1v/U+zR3MM3ZxXRV7/APHaLsSfJGCCu7w4Iy68pBfL9dYGlFDLK1PyRPS1ghKK7Ili",
	"LKV8+UMoSJZXpogwecTer9gDmUM+eMiy5VtNwnWMZNCZjtQR7Y5leFKvw1OSXK6CwwbDPCtyyKWcZb3e",
	"Gm1ODJIV3yKN7ssOtbX+SCKQUC6abZkIDMmrLfwbwJ+XKD5j0kpQJLiHn1Nb+B1hp5N8esVnTPJoRbpB",
	"hhJZByhgBf7Cuu000LV5VUuyn+tGKHQyr3LiC/+sG9ABz7bkU8ROgtEiXQ/XyyexiOXVxcW5erznzPLi",
	"yuzyynhVarAV7ZBzV0pT+2LHNFNyEj0Ptdpe8Zxk0uqbVqI+jUQ5NhtfhqJLjLmMVqvo/FizgsbOn69M",
	"nD8/Vjm/rjcq4+ujE9XxiYnJ6tnRPLnNlGZ3Dq17eRHK+RqmbvA+ycZ5CRWGD0BUHUjlNJsKM5jdyTtb",
	"sA2m3Y5/hJWU72mMsstPeMDtVWIXlYEP0sR2uOsWW8MCXdy7c99/KaXGQZ6zpXhvs7fkKfmEDxnuU62D",
	"q6MUclHPZRf4RLzM05fYuRgDJQ9ZfrnbQ79EMW6gd0p1Gnluxg/nVKR7HKWWSiPssZrLrMBBU99y8zcw",
	"OKLuaGGmA8VdXDcsNIPWhhz/eUn0HEi0lffffxTrtx+mNiT3JyrvDndIpnvlbWi5yOXn5omphQ8EV0ma",
	"OKGTKVXlK/W4qlVo8S9LsJbcqpZuWIa1MbzGVRyZu8FDgUxwgmaSmScyQTJexD3qkajE0HcpngjPnWxy",
	"Ayntcj6DIKLCpf45XwfC5KXY1esb/5H/mXYKElQeQmFD2NuFlY/sk8xCCIHQD/fghcb78uB0Sqw3V/+x",
	"lDDh2Wq1ShoGRg0U/sf1682fXL8+cv16872xu38ndSciC60bDUN3tqbq8/X6vBSJ4gv/6zP+F/j/WGnV",
	"TvBhuP6HqYQxYKjq6JhI3DvVyiT0asgibL4gvklBCv3f+d9of88sOSL8Ie0L2vh0hUN+LjtkqGjD//H0",
	"76VOTOiygvJ0JewB/sgO7flzUCRNZ5bOour3eWvtltrZ9WnwSXiNjxgy0yGLKITKBdMg+ej06Fh1TNHH",
	"BDm1RkNZQP4hMV6jczkMPob8roNEuJ3y0OW3Vxg3iZx0+e13apW3CTudl7MTa9SMHMOWvwhQEkZ9f8K9",
	"P3XlyrVr166djrH1WHVsQsbQ5xQEkDzadKcjaUfHOSbSObcqt+7oVOqKID6/veic/Us/2tkK66xYyXgS",
	"3As+1uguSh5G3ZzKIZuIUIKKUqjjDQtNw8SKhCwYiniK0QfOCxWKOYunJWC8Qrde//RffjsHdalxnf7n",
	"DgXn1/4j/y9/ryrn1T3b2aqll+kEH3MAPGJkQGg4p50iVMfC9N9S6Kp9fw9Lrl2I+r6o+P8THJ7QWhF/",
	"Dqk3wLL7AOj6YbayIYgN8RYnLhqXXy1IP/4JELYkTUvJd3nj9zQWJtVddPJVFPiWj3LN6k7nSS0rmEV2",
	"kiUsp3jzIHhIORQqruAvBBoYayJhFKuH+S0V3OfA78K+2JFhmhpip19TpM9xB52rsCPePz+XObgHjX6h",
	"TSlZwX5Wjr9hrdurUty0QcCwwEveg9+8oEEmcDZIni7SNjASFJR9+snFCnP82Y50846cP4epvynSc43Y",
	"GaSwgQseLCqt6kZamXnYB7NuNY3bRrOj52rcWVP9DpriWLemjIbC7viSYw6pmaBbt+ZzJl6nDlQURPGv",
	"/ldn/L/4X8kbzd1ZxA9Anq1Z4b+bKHEkcRRhvGjHuNWnne4l5P3UcPGLI48f6A2vo5sEjyWnHqNUGcQc",
	"kVPghfyAaoQMe6knjYzyVNQNa0oK5PQFsUQ/zVD6hkNFatPdY5ycxMHauuNtDX4c0N8UvvndkGga/HCG",
	"T1PfRzUwKTkA0tSqjd1ZMznlxgr7IZMoqtp6Flovfhh8ShG+eM5TZ3lNDSRwozGKumWiRz4vpflYLe9o",
	"RenNHpbjwFRPBz4gBbdljZv/sAaYoPAD2udcuc+zj7Evr8n9Kp/6/9rniHl3Jf/gufIgkwIjmW89bGPq",
	"ltUexCmlnSKgz6dTPG99GrT95dN0iZ0gxnN6NCdRUgmXq1RJkEnxTBdBYCekZFz0Sq+FhPvkMkZxgeWC",
	"I3lLODcFl6CDWSBxWGV1RWpSu0xrIKzuvMU7wH9LMNroHSKp67RZ/j2SApI07nI5xxWqcK5GtdxvVxzd",
	"cnVoMTO1VZ9JsZNyqAVJdgxNvcnJkcmzuRQFLkpSVFuQe/fS4jA5xXfxgYcQ4JEL81bLcBWAel/CprwA",
	"BtuO15+M5t3/vCI95bBV5YRNw4kajeZh7oU2Ii1MmGV9ay0l1PCEpsr+GhyrXTX0xXG+DS17zTARIVya",
	"pflHcPR0+fhbtJX3KY5T0diT/D7wqSjdNLaC0XPehWKDFr0H+UfftC00n433xHAyaTxzNx3sOg9TciKT",
	"AvZhGRt9mI13lPpoK1rni1Pk0jxCexoyrNXF+dzARSv0i8+gPLJHMnxTWvTfJQHs6EwLT5vLbxX7+nGq",
	"ZopTjjs+hZOR7WOS85LL5gVvyOy8yhQ9KoL+JGE4Ijtv5NYpllADGW1PrVYURN5Vguym4d1KBU1xcDbm",
	"hU7FYpNvQxrOidGSZuFz1aLSqok9v4dtNuCw30Cc8AUpCEnejecxhIxzE9I0PXt93UVSDQ+uXbIU4iVY",
	"h10aW0oicSjmSQMe+5rhjYWQ9qQUgETZD4nrq0uKXbiSmKxFp8tV26OuN2XS0j4zihMpoX3vNne5Mo0I",
	"+SSFzASOGbMrWXnShP0pU3YNmYU/zbTrICpyygowiU5J8Ryml2Zn6lipnJmdqq9IsUNlS5VEBkCFyqE4",
	"NJge1aeTVPFI9WERNXQPbdhSn/mX9KH8jtiT8oenX69N3md2cLXetDfUUVVZoTdeKO0lSdG/izjwXit9",
	"rx+NrGAPo5PSlzK0F/HuyX02Uk0lW8VJkz3J88toj6y4uFQS1efrK/XaXP1t6G1Tn7+5uLRwaWl2eblU",
	"FrreLM3+bHYa/1Mqq/q3XiIr+ydvnT9/fnJycnQ0mST7k//+zmhl8sb16833Rqvl0ao85WvRdr3pTd3a",
	"QDOGu9ZxXNSCiKnlOXZKGVRLnvH3u7DvRBKHHpIqvgecFNjdJ5AY+kALa2mz6/paKvwfvIrZsGeYHF4h",
	"L4xtNA6PYuvgD2q5IPiiARhuZ6JZa/Q35Wqa7pTnMqz3NGdcDgB/DmLqLgC1qHRlkrz2LJHEQTovRNWw",
	"rCgqaa19koYBm5qHosYxGiwBZSDaFQaAADla4rZUdZx1gh21Rc90uYUUx5qjQcFjRSeCZNc3RRblgG1d",
	"8rUrWOysmUajr347L4MdgsrGg4mlNeFpI8e1Ld3EW1vbcBDIMNKFB2qPdKsBOC5Oy61b67a6Pw8Q7W7S",
	"a/Nqjqiv3V5C4TKjblIqvMmkNuigpnEsSYfPwJrr0io5lkwZubq5BlV8ZBqqbkhycbATphUOPTtS7AQf",
	"hfWGNkEO+D447vVjSvhMtEmIpzdCOGpP85/CRn/nd4P3uYh48H4xRF1OLMtt7OEVhEkqiRSIvixzkJuf",
	"3/IY98d5IMF1dD/kl/CXiPq8mIzNBVlGww4caEWskeL81dml5frC/M3V+drVWn2uNjU3K408INdVQIYl",
	"0SxoGSSk8vpHzPNOUYWS1REkNgEOeZLZvsc6OwBim/89ebhDxxB/w8M39ym88E9YOU4yJ7Y7aLJptCKs",
	"WipX9CUo06Qt1P2oCvMILgaHdF7AKcg2PwVEdgmZggtkMNx5AuUTugOp7UvTwuI1BvJQYgo7Dj58Tujg",
	"QSdKAfZNOQjAzpCnjgooZ6lQZRk5Walel2RF3XQKLFg+EK8hQ2RlYUxlgjwVhxcRIHTUsDfpCTQ/WnCY",
	"aTU4zLJ+Gy13nNtoq9ZsOsiVKH9rHcNs4h1JxVDq8gV0XLTkGQWd6gYPR0oAwj+HrA1vs3RhVNYgyNS9",
	"LKymPVp30Q3uYVlNJ+5B8H7b3x2Rxu11z8bHoAIqeIpH+tL/nf+t/w3vIM8gt2VYdfLVsWSRqes5SBoV",
	"+XNYwOXvwtO2z+hOnS7Gj9GSwqnK0Vmln/W0bXl6w1sE+0cWWnPc9IzvDGmb46RN3VVDLO76L8i5DmUq",
	"CPhn+mw5vxrstHms72rR0yb0lLmj4baQLTH90OVmqQt/KwqpQhT9/RC3QOIVeTiSjbfF5pYSzvdb66Op",
	"WxykZMhZojnb+8UqGI/BVlQl12unclcpRhVzp7WfaKJ5q50SDZnTp/M3eRfpWZyjHQFJYIO1tJ+dn8Gf",
	"PuKPUFu4Ors0swp9AhOYNJwDBw9ZKpdm5/HVoL+ROGjkdayhYUaPOAwasKOVc6WNx4SmmikOk0JuVmkf",
	"bzU4TQJ4hO//EzzEolOKlC7Phko6R9IorgvfZoRa8a4EBZpSiiP15TPIduWKOM757DLY/aTLNrnWXEWm",
	"lG8IDN+r8uGaCa5Nb3gpY/V++4GemluozZ9hPTzr85fEqnXa43MYbmfw0jCvZtYvLyEvcTScU5S0QO3L",
	"tbqMCB5g8qCnVq8RgEJx5/BTrtnrmreJtLXOFlgZ0QZNrS4t17QrtbnateV6Tasvz9Wu1Ke15dmlq/Xp",
	"2WXZ3k2vLi3Nzk9fS07F+sZoFJg9mubKtSXZULPTs0sr8wuSMLtlvNtBWgOvDlpcIC1sRRmNWpu+OPrW",
	"xdmpsdEKdPCoTlSkfTzKpbn6/GxyljnD9fDWuB289chxNd1qardts9MC/TcfMjuHGynBhFmcr12RTL1I",
	"mF2jrgF+TQsXL87OVmq1mjRAulSflg5nNJDWRo7WsQxPO0X0DA81tX82vE2tYbdauitejbHJ8jiDM5JP",
	"c/PKtaWbs/+4Wr9am5udX1HNiln4tm4iy9MMS7tybWkI00fgmfJtI2d05t2ObnkGmKv5QDbLpeXZubms",
	"a+Ii04yxWlSuqq2QTmzSkevzl1bqV2ZnaisS4leMFnI9vdVm83iO3kQauoMaHWrac7gzkxfGRy9Uz428",
	"9daYNvqWdhGtaWPVMSkYzcrCSm3uam1uVTap7emmBia/OG2eQ5qcLE9OTioOCSaUrxRcBBpWgIQ5RVSY",
	"tDXFRCOTE+Hxlam0E9bOiSZ2URScnDwsfjns0kZ8SAWIVB5Dl/2wg/6U7jU21YkKaa30ZR2Vk3mhwYO8",
	"gmlI/fGXWyjEMHCQ7qFcWJrNgbA0+R7N3WFhyMZhUynEswTPPILsVE6sRpTl4MpfgInyNNhJoKYzfTpl",
	"pv4AY2OL3ANV9SlzHKRMteKZ6YuJcHAXVhalGGxYW5JmrEY8TUsq+MhS0lGP/rlGdnkGmfqWQhUPwzwU",
	"epOoqFIAVNVJk+K2HDDM3BWYQSbyEAOCwwqE+i6cMCgdbaufetUjkm5krfO20UAMMjRhUVxFjqvqXLEf",
	"3CPVXpr/R/+R3MmBB79iN5GpqEd6SmotwAalnbCeMx+Y3Nx0Pd0k4ayCsodMAdzZCxFyk5GALddDLYVt",
	"8o3/ODelZKCc+/eN/7gwh15C3o+fPS8hZi0DDP3U1opDnKzq5RrNY6kkMgvWiuQq9EgvH3lWsGxEyE4d",
	"fsZpmFeqTj6VJTfdzncFUtangqwm4NrU3wLnE80m24tivJZWOfMDYIXBtz3DwRXb8IzNXfDaV3XTwKZC",
	"iqQakmbhdhoNGouMx86gYm4/+Ij1ywk+ToVyjzcYz5DJP7edW57RSlmiIpBAnLI7NLkoKj3ag86K8M7s",
	"Q7YKnyelnbp0ZeUnZ7Wa6+mWLi0KNtwZrMmzsgdd9g5/g7Xx4B5WJqOaASIuP+YCs0LjTb7jujtrNRfW",
	"8dIVkPaPhP1lKWJ0Ji2aiqDRHZLkDJKp/SHrjLtHFFQKvS6jw0J3PG6hiuKIZAN0bi7axg/IydttN7nD",
	"yS2R3g2QSXIPX9uxMf9CViV1h0vquLDBD5JK+aX4zY39opyYSEooKDLK3oFZzZEELzPXQE4KYwBuS2XQ",
	"OGrlkw5V0H+LvCgXRl0YQYA1DqgOGMGqBts0rYvXMKTTtEjnp2QWzD9bacvfg1ZEVGNWZT+1C+M/5OjN",
	"dhW8cKr8PhFqlrtZkPr3L/jaDq3ZHDYnri3Nvtsxbqt/mlhUGUI4ALBPmriQt45kACtc1nxDusw67FA+",
	"JRrUpTxPil5JcEtVbSm/AIP/OxopF9JQ+8X32QvvRCywmyP+ztZBeDq6QOwqM54uU0khrE5IWyLcED9i",
	"yTGIaU1tlsBAWVQmwFZ095YkKRZ8XM2bJGEoX8FuW98ybb3JHVs0SxTSlkBYurdIhkS8M0xLv8NSOMbP",
	"8YVU71Qrk3pl/cZ75+9Wwn9P5Pj36NhdqW6pSo3rtJsFt+GudIvv1Drepu0YngIYs7PRzIHeGQ6yutHM",
	"nmmVjBl/kixV2wXN/7P/e//zwjmp6p8VBTqFgahx8NB/FjwgyL+HFHL7EFxvKSnJDtowbGs6I4EW2lCq",
	"QUfIIPO502RTh1OckQDZKo/wEkhnIsyUjQtCQ3SmfrU+s1qbK5VLc7OXanM3Z+dX6ivXpBbnirzu6zGp",
	"80p2q9/jALaxii3ERsYh0cpoYUKqsnr6lWTtarLJa7AD+gKpOwGt8z5rPUp8lntRIza65oWVn0LgY7F2",
	"7crs/MrNqWs3a9PTC6sQ1mAfXlmYqkNSPvtgZXbpSn2+NifdmFW47Ksucubshm6GxWCJE8Rf4TB/BksY",
	"306tZ3cc2k0wTTRw9NTID/BPb+uGqa+ZaEo3dashf+S4pHpSBPOEtnQ8Cra1U2vkl5W2qVtup1Vp645n",
	"6Ka5NYPWWMJSPpTRNSUNn/ITsp4+1G8f7U2xaeZ177bUluMnw4Ozhkt7FIh9hH/v97VTl99eOV0ERjX9",
	"SsdRPsPrKy97a5i2m6udJ7VOwWMhsFNmml1ewAF+UEY1wRJbWp0qlUuzq/gqri7PlMql6Xm52GHhtRzV",
	"1eB2IVqcskmqsaZbmYBb9Hzhu7L+1W2UjRIQmf99bDC+OcudljTMecic7aQQO+xclY/hcuUISg+uNr1S",
	"v4qF4tTcwvRlcFhOzy0swz9qS0uzy8SJeaW+vFK7PDsPn07/tH6VejkXFmfnFb5ML439pbRMry6RKPbF",
	"Kfi/6YVSubSyip8xkrIEYn45O+swao3PpRpyCnR41JRzQtEqXF2ZfpwUrkmlyrQbt+Q5/I8ATvIZ6fKk",
	"hXBUtNAK0MA+hCYi98PCWpDCROdhJseB3y2zTlFg5QYPQFDtE5/Ud9RN3eUru5gXib7m+7TWE7+1n0Tc",
	"tqNV+P8AbD4SR83r3+P2JyUeHr2WOR+yZF4Y/TzjjNzlK7OZdLjqNlERXmUi5TpvrEdYRUaoJyRItaxp",
	"3Wm6P5YFUR+3YVuzKl926LrelTbm5Xus+3shDkesr3UiQV3lOs8o8hwdkab8rBvIbMa6VCwK80livYlK",
	"2X8hfjLoQ9ITXfikvSGhiSYwJHaCiJJY25H3SgY28IgTQGt1XE9bQ5quxWV0eCbxyCPsU7jCG7Lvu6jR",
	"wfblMuYOmnOJdAc5K/YtZGHzE2QifHSRPWQ/+znWFoChQKLAX6O93fS8dunu3bukiUjyoGqL9bCK9Qkk",
	"HP/WPyIJHS943M6wnxSIXSE9W9Pe1lu6pS230HXruuV/FXwCu/ucawdFvZTUGU8H/579pRdmW5Dm8CQQ",
	"EjzQRkd+oZ3yv8N/1vx/wxL1A6znU+ENUxwkGO205ne1X4xUw1/+QvbD+kxEEzDI6ZHr1jv+4xCMswev",
	"BtRpcBxz49TIyBk4TPc0rPbfgt8S6jW90UBtr2Lq1kZH30AcTCIWEMFO1HQorBomNtgB6IPRCxOa6Lvc",
	"DoK2yO/hyHWLdvhhRcb7oO34L0a0d2qEljlKy41T/y2UPtjY0FvIQ45bIV9j38I7AGt6DKR9IHYNJN2S",
	"uAJmrMi/4/6zvrGBHNgW+u/T7AF+x0FNuwF/gn+x8d8hCDymvQF/2yRozmca7NObLd2wRja9lglb/M4S",
	"MpHuIi3rZ+tI9zoOYr+kye/uwvoycm6D0xeug3vhzJlfYY49A3+u2OsVl34hDIaWQpbWaot1Ltx4odRE",
	"tyvVydGzlfGJ8dGJ5thYqVy6UzHtDbhdHccsXSjhzfB0z2icwZ+PtKlehXUlvW1ADml1ZJRAGG3CRT/D",
	"xPsZIYWvbbueNIWFNGt9mlaBHg+xEixu/1mkwJM0C5vFkOrN0oUSycFjb36Y44dJZWwDRT9yS4w9Frtw",
	"Y/YITGMapgkI1Xc7yNlidcxYN2/QYtNIgnpOB1Exp2d2KiLEC2Asd+/eIMMh15uym1uqMaKvGCgxEq2j",
	"Js4sojjAKY1VqwxyCdHiqCjX/swvaSVAX8SHCgrI8DytlaEdb/RofyT2e45ne0Kr4AlCf5rG0AWuIiDT",
	"7J2E8T8ArXabKUNhVuV163qnWh1vrDnw/4j+F/mP0dGRUW0Ftdqm7qF527tod6wm+cYZ+hWtovn/X4ST",
	"oxHMhiPIbACXT9YEE1rduo3VIjYP5oPkHF+DEwxLVNoGljjIeIwef/e6JTzQcAEST/M7Jcv2bhoE+shA",
	"TQCA84zbqHQDM5/babV0Z2vo19fTN/CNJNCI90hpY5cz0ks3MOmRfMFC6AwxzSq3kUPKHxh0pFzcfKF4",
	"yOiDGkLYpC2G0y578dSHeKMWqmbEfARES+35L0d4D9MXkAIYk2Gwuqv84o7xxiZnS720f/L3YRMB0hU/",
	"o9JEEN6hFd3Q4UiYxToxEmTUCZeenkLsPGPWQs67obgKiRyNARmhv9tA3RmVxiZqQMxuQ1oBH3frYr6N",
	"O7I+EfyfCvsQHKORQ3Q3eD/Jw5QmSNE+VvblJ0rl3Bx27wCccLyb2xdfhKpYZd12Kq6xkSYkH8Vuyb5C",
	"hB/Fn0ohJULC6xyRcSYJy0Iu2s4yJq8PHSc+xkkoOfE5L9rMDbLQRhapakzhQy55eCh7vM/kiRYXKD9q",
	"0XsiDJv34nW8zTNrnltp0l62A1g+WB+lGbFdzTRuIwu5bvR9UFvfB//D0+g3v8a6Hjhq/b2wW1EYFAdr",
	"Urx7l5BH4S0x9+KtTthIMlaIvnJGtL5LxEI5phsnkgoBWBkPfs4j2G6T7LKorEVAuQUvCrfVxPOe+ISC",
	"2LPI9z3imxO0mrR7oDiNNNV/pKrF3KFJrV/0hqq8nRnzjGbP8y0s+Anv/NkNdmA6MWYNxtsrMDOkd4W/",
	"tr9LHkDwkL+0DdtaN0jig+LOfkayDML8ZmLG+I/9P/qPz4yMjGhl4gQMMZcw80GgmmWyaAJMXfIdxDsz",
	"Teno4wlc8Nrw5WO16yMSUx83LlhLIATvU+2YSquy8koGD7hdw2JW3LM+njLbQjkgXqJHrZz+xXiQ4u6N",
	"zGfw9b3+YyOjZ7VVFzkzaI1231ZMljBxnkE6bc8/pD50WhIQdkiXK7VHUdYs9YD4u3j4Pd5JvK/hU4wy",
	"YA+C+9p1GnS4XrqgvXe91MT03iT4IPij6yXXbgH8wfXS3awln9NYklXdIlInx3oxsc+FLF2aXJ+ZiiVf",
	"dQaRY6NarWXCiVzUDRM1s0msXZmTT1VWHcjH5Bjj8eTdDL/U2MjYmPazjrOMdKexOW97s6axYayZkm38",
	"KxYBBHarx4gDgfA+SNEuwxEHle23oXgNdrIIGNfmbW8ROS3DQ80aPkTD21I6xxSzEjcZ8SjuBw/ZroS8",
	"3M2iYlKrWZ5x0dE7zdSjUm5/dFzg4zyMuVGIKkcfWZKWDTGTFKrGR0aZz3BhZTFLaiQqnzX/G/9b/1HG",
	"FGPagte+ot+ZDsuvZ+80EGqq2HTf3wseUC9uT96TJ1F5rqgrSqoXSYMg8d4QTV8qhwupCcyWzmNKJxUX",
	"SRF9N5fXdFfQfULv9Ag+LC6DI3hIYpE0O1Iyyh658CRY2PVfMD4H6aux7DrAtFK7JkYkdgSzhSMT/vW1",
	"IpK+gtcjOiFhj8QRpt7LiWOOR0yMjFY1IuJW7EvIwgyALjp2i00ql71QEg1vJIBQyzU/EjKPrZeGCmJR",
	"jHQKR0MKV9umrTcvGmY+ukgLDajgfoafCEwqrUHNUigmNeI+n9ad5kyH8KV00nCfNaiveUIegQMKqtoL",
	"PpR4zrPeoKom5J/JRLDAZmkJYTIl50Xk59g/dttu2CKykHSnJG+lBZEkmdofpPUnYdKCqjXa1Mqy1PBj",
	"zUb6sfzCRiUnYf4toXUHuZupQvORSt2JNbuCrQt1zh+mcdeVc8Rr498Z1y7azprRbCIrOQMXrohGpU0j",
	"s4ReVZs2PONXyHI3jXYhQ4UKoO+JpyRC7e3DTBod1WobqNjs0IMTJMw2ERz9zDsGZjPES7dUM39Gn7i8",
	"gqIfOsa1RdZIh6o1VPknJdLSIxeeWHgFiRpIdyl4GBWhhg19WLn/EfGwh88Ea/ech2MmCMvHCVarKl/7",
	"++AEoGab/4JUoTwdwhLy6jyvgSU+1Ce3+ANW6Ak17Q0jNb4Y26lgG3AbQjOQQ0ZOf1DFFnvBTplGvxKG",
	"Y1lq98Gykq/wHJDfxxM8Z2/UrWN/f3lQuULvLsftZbFb+yHRRmkC6ku58cyle+7Ry/iDccWKd1t8U4V3",
	"9IT8tP0mrWcJV3iPpky7cUsqoYbs/4PZALa/blhXDLele41NhZm1DYO+pPAL1BcTtoUnQWooMO6KH8e7",
	"aColQoazapxzVlHMvPzuKng/Ult6x/xWzyKovS6+KRnETWDi5kMwvxWjhU13NXmJJ5G5zp7SWDBY0mUG",
	"Qg5XWAPD6YUksZU9iKTMjCWiZ7nYpK9AQXld9FWhMJKq4By+PJEIk1JBsg4jPKDgQTlyAexLI3YjmjJr",
	"kFzUHraOw155LJcg2KYQ/ruwpK52vUTuWfAAPLx+1z+6XlI9QHil/b1AdsdLe4LkY9DvncEPy2yr7W0p",
	"vVUFJOkeO49+b+2JCtZ0bud4K8lChdjYIaZzCh9/Q/us7SlZMk1NS7ITNdb74Sfu56+NX+FbZWBYC1vU",
	"0a0LHrzJzZDlZrxGSsS4BhYMPfgUL0XGrdhNuC78XtalHuyi5bryTaPhnWnajTNrW5V1wySRkHTDKNiG",
	"kMx9qE+nLkwafE6ktMmCMEbDm7Ebl5A3Z7je1NZFOms/+ZTyoZbQu8ebUkmmBbcAnrhYSCbaM0kYBgoN",
	"XlEmZD4bXppVXoQl4Bvxwj/6ApMuRJCuD994ITyPPE9Ligf9LsfThm3pDuYR0yab5qpzzSXZmRSuB+Ts",
	"c0raPRaWp4kTeIldTCL35eCh/0LB85hLQ1qyaryymojFyOgRF1JEc3dEq2KhpC4TOQDePKJWxPuxJY9Q",
	"q+h+UnDvgxrZ1aB9MuD8YfvjJaiRu2AWYTZo6w6yvHrT5St7q/IiNPrdmRJfdRbCRxiWNz5WSofowTZ4",
	"3h0MHiiO8tg2YQb2oG0CptO6broodR9gz6KNCKvbC+0I33lwNFnu/lqFuvmboUyX/Yrn7h+YiByufMkh",
	"BsulO3ztccO2XM/RDVrpGjm4xJ5nlFlvYPaQ/KXplm5Q51VUrnFmTfcam5BlUpGmp7Yd1NC9qK5U1ilD",
	"nggjhEgJPC2RdVjdofdV+r58SIu9n9E0oWTBDyF02diwxG4fw8kIKajFwMwzdsM9idzY1B4n8jT1WM+S",
	"RIFC8KCcMGxY6/2uMjElERr3X7zCEhCR1YoltHC5bNN2M0fdK/NfZSSvvarMtdTMlUmN8U7NdJDe3CIM",
	"lSuC9338Vu9mp/KMhYky/G3NlyojTCVN3Rly2OqEhFko/RN3kynAkXR2dbdCcFArttMkmGXZqYBcxmWX",
	"ZDgzBO37rJdgj+mNEeAz2KN72tRybVk75f8nHkxE9SZCIbgHb+EBlw3zR0CA22Z4ZZ8RL1nwftjKY/e0",
	"RKumPYyga4Sr92VB8kMct+jl5lJqOWlWY1S1ESbxhXufmr6XAq2vgVr7lIBwQcoBHNgu9VDEvEIcQkqW",
	"62KYzNQHy28grwi/S4p17sVrx6n7Lk48h3d+osx/CXkDcX74++NmezZRcZ6PHcsPi+uHy1L9XgGv0fcF",
	"2MaLDp8wAvVO3qDHEr0OciATgYcTuwleY5B74DVO5BZ4jTd3YBhc1edlIO26ct8HmJnYM+ECPpEtgLzN",
	"LxnuL+et7+a/KLXFOns7Pku5Kgr+Jw08BrgCdIATuAVcr5FiF+GYTuM1uz1JX1HxNT7r98lwkWkO8b0I",
	"m3RAGi00Nnp1T8QyMs1+Lwj89tg9M6wb+Jv3oTgXFWP2dmfNNBrHAPpXFgP+NKlDQMMII2UkA2Ub03/d",
	"arNsY93TaxsOQpjQ5Suz9cVy2Gl+BTktt26t2/A5ie7ECpFe0r5vPVLvG3wckcAjmcSvB8WhYd6VRbI9",
	"g/pCy681ciFZpAy48JgL+n5c8IITkfNzKOB/qVOdh8wPfAXUyZzSnexl1TNANDRETMlRwPc6lBjmlrMn",
	"IcOKSeD8VclZyL58mjg8W+AlFevs5LniCZwlHihYyMqTJWnEZSjvnp7aqs+4rySWJIa0tuozS+jdV57w",
	"/2XetP2sMIykKOAHGzN6LeM74yPj2uuZgz4xMs7emRm7UZ/JfmDqM8Ur099Et1KiW98IknZ3MElb/Lmo",
	"ZKNfkUYlR4CR0qXPOgcXwllMIjWf5Jf7GtOXutAmPuo0DGkccahdMi10XX+flLNT9IvkA7+n+Z9CB7rs",
	"VIVX8rLICDmJx0U+b/6EhcR5DyFZQdC136QqvKZP2VmNvmHq0t3HQgnMc8lUVHCEgiRelht2zwZnRPCp",
	"f/DmiRkggeL4BXixh+e9JlY27qbkDifTn9Ugnt+RNL9d2kBEYtRJLvTIdUvwmcEWEQsW7mhZC+6P+Acj",
	"sTxVRc0IHu1zpS33ksC9kFr0tDdQZZS9CAlNuNvkblneZBqGxynjFyH8wMyi7m0u4j+UfjRenxN+iYbt",
	"SDoJBf9EPUhDF419CxqZU7W4FJR4bfJk95LjJ++J4J4GuSQjXWb5J2VlpgvmxyhP/mv6WobcEucYynbj",
	"tR8kUJW/jPL19byMj0xor13pfzHFv25N641NJK8VJftyIFPuKRjlQfBbvNFv/FM/gOzr8LF5Ve/jMVfB",
	"DPcJ7a9W5pW/ra8h5nyqnv/GJfXGJfVGfg+veiYp2/oqk1k3TLTR0Z1mSkjhDxzcLmb2xZmLIeCuxARZ",
	"tF1v1YIdQYvN9YuGiZIJZ9z1bHVMz2jrjndm3XZaYUeefDcUj07gg5e4Ce6K/ZDxe3Ocgo8nItUrz3e7",
	"kQOSEmdVjymCZNvDvg2q9KB8WS/PhHPscmeYjv8xCuKpYTsOanh1q93x8IJJS2i5rIo5AkiyC9fKV1wc",
	"n/uUn6YxCU3Lxq9QAYruQYEBbX+Sh6JB+l9l3iJ2V/FCtEtwJ+kdtR2EtSqdNk8407At2iC44nbSgoER",
	"xz3n/ZIgS55BRmMIRU9QVrcBI+s+FXbBNnDZi+ABEe5HFLPzPm8CErDBsP9dyKUHpFUnbPIBdeImZMVF",
	"fTpcy3KnlSonBoqfRXO8QjkRoyOnqGCV8fh8Pgw+xXpOeDKKc/lhAjTvic6C/UzMJaozLTJ9OdfV93sE",
	"3Alrlx/yz2vMd1EMAGpMu2K4LrQSJNyUQtQ3ECnYgf8F/HYSq+j5T4KHEKUIu5uQixuH1MhCRcuDx/Cn",
	"iJ0ymYlTIz7z/9P/XCmWSI0tkU5eVpffx2ExJ6ye+Dd2gk+kVOCPQ1FzRPMN9oPf0u/2wPPzYVhQ/FzZ",
	"J0ZRQDvN09x3DS0/ykn0tZTNmiJU/izkauwnu6ANT5gMx6gS+HyQtzfkNaLDSxlMxeabSDe9zYzop4Ba",
	"jmVX8L4GGsTTYAes2efxkg/ieoiqHnsknAhn8iQCwU7w608JOcfIVXSG4QTr+E41wQ7ek65/wAR8cI82",
	"uAHErz5xt/itz72nHHhMeCjkuaWnbtq65Z7hNrGPfuUXZ8N+lMJNA6eGEK3HWhehfmo22b38IGxneZ9l",
	"WeFX4iHkoO0rJNqcrVs1jvy+gEp1y+XGOHYHUozmVGH2h0g2xLLQNBbLp1xAnEvRju2Kx7H7ap1G6s71",
	"g5kbjxOyPY2LhEf+y+iboZ+A3Ic13bpVcT3dg9qcdJm4H0L9Q/CMJf0lvXmfAJM/AUoPpM3JLyFvSrdu",
	"LYcTH2fnWn4iN+sdJQpaWPkFZn14hFjYUfEYru5VI/yo2W24vZ3jAIcM2oLXup9H+0KbroKCF3dTQNiL",
	"4SnmZNSGbjZCT9LQeJSUAL6AIB5ZEeTOHQYP8eL4C7YrrSBhijat4OMCuODHCb90n+UXy+8CyOVp3WzM",
	"4AUeszxm8+Q3V2llYhKUjT/kuDSiLgNSsHcAABEPqTNs94d8Y3J1QRey9Wj/6l2+N7zAWvlvQMfMUl3+",
	"FM2TNIEpti5rlPo96RgbtpcgWm6wDd+kp8sTKqsQmqZUgXoCxZx9WVvR2o4/9zqaawm5HdMr4OINzWrC",
	"Sd9BTH8/ytYUdTuS2qbc3B+2mqLwPxwj4+W5I5u6tYEqTcNd6zguvPiVFiCUFqiK7hIXMoTRueb+u/i4",
	"NZrf0cUyn7NMsP33BGLW3/td7RRLUyXOcc6T0fWfn6EAAKCySRHAYBEz3BqmbctzbLOfm7Vou55yQMFz",
	"eoxXLoOIlOv3pzi6ULxA5TuGohE7sRzH9Cozv/qjt/itPAbuznkToStnhd/NxiZq3CrQoErsxUnbZJLQ",
	"Q9ffY8oJZ/LQr3CSI3G3iKYFtHF2KcTTSydnB8N8qXz/mOrHEJePicIyMXdDG0VoGtrFgjSGN/7jtIeP",
	"h11ysHaYInXSHajTDf6U1tDuRduhLPhKyuxi7B/Vcp+w/0motfsRFi/kaWM9yJU72abB6huImh2ykxVv",
	"q40KNUFI7dMA6B2kQwNDMHzGN0aUPyeXkDfLCFoBeo4XbEycTMnKEhT1vMt8jd6Lgc5rWMj2gzJNHpZu",
	"tU17CwyWY+PpZ6RWm8RRpLHTkJ9Dak6EocXZBuPo5CJ/gNycWMRJc7KMVXJwsW2t2brTNKyNiofueIW4",
	"OPotgWA7CLsKgxIs0YF2teUrs0p/6kI43gqQcpz1S+JUaZr9gnSVch1v98flIRrmkefgRQeZx6Aj+Huk",
	"YgrEPdHDmQsQ0rjDNDlqekDUiHTC/gBsMZXIXaLknoTAFeYqLm4H3oMfiEQedJ0nIbSHwI857pLbcW6j",
	"rULNxkgYcD9qrpKncSaV3ctkuqGYqscI+gtUkhhCrvSsOPvl3qbX6RnIQ/DIgJ6dvrlJycrllKRFhmJU",
	"bLaRZKW2fhsNkXuza8lWXeTUNqA+qy9YPf02IrQePwxwOJXytfkd2/OUvsXxvCAeg4qUto78V7srA/Fv",
	"Dsn/Hrd19Zm7r0vC0NWx4veLX8gJARskyc6bz/smD+n1zENK3AfPzUhLygNZC4U6D/xDIOuUadxGFnLd",
	"soaargL5fcpzZ3RPT3Psx+j4X/6z4AEFSuCSsCkQ9SbSm8iJkKjJa1QJn6M0ROomWtexDnShdOtWqVxC",
	"VqeFd5H+B/5pp3SD65iK/zvs3ul6jmFt5GvfqbjB5fekYNpeFpK2HMhbKLAPdmjh2HPhjCTkH6cYoYet",
	"fDvFJBXyMKpxAoSlQBSpm/gkzFoit4DV2uy+TrEN4UCGGtUodkH7ExwN3WqgtMYU3wT3aaB+NyNsGfYS",
	"UTUH3lHEwYGEZF756/+uSilXXo8/cJniyc2T5JpzCRKvkONDQrAWl2SAZ1Bp+mIQzs/PYv3xeBN5umEW",
	"a1L+lNgArMgLxJOs3RBxfgQPZN0Dn+N/Kh3DM5SqYTH6a9h3Olpl7pILqgz1/EPC+bF7UebVz/1EFQvk",
	"PwtolcGDH2nayfGybB+3DOmOuVVxUFvfKtb9hSXQiEBqPZKHSTMUv2M4IXyu60PtlP8f/uf+ozP+I/x/",
	"Mh0VmHDRdr1ZTN4SUDfUp6WPRMiIlJPMfORnVbxQ+dIc853Nq7t2OXnn/9XSCq+rI6PatN0ET5pL68EB",
	"DkIBaXcUb8GSQKUIbZ2Mece0mEKhhlj+UsjE2qXGJa9kJDDX0uc+p7HavJ/q7rztLXcMT18z0bKnex23",
	"HwJ2GVgdQ7HdI2Sl5A7u+y+E8k5/P4Pqt6KTipicntlypyWDpqblMrmZJYVm4hfBdB6CHOsGD/0XGRSf",
	"jyie0q1bP7edW4a1MaNvQUomwRWS1PfTpy3MCBMqgfxdDSQraXrdgx5iLHX2QwLFRrvAYcFZnbxQrVbG",
	"Ri9Uq/RF+NL/w+kh2y/Dk+x9PklNt2DKfdJ0iR5NOOF9zf9P/9/9R6lPTdOlpvKrVu76eZxC4k/0ceJm",
	"zf04qc/rVebUK0ka5tUqwKn9XR0KKrY1/PvDvBZpN6hOZ6c8sdxCP8CblFzESd4o2ezDvVl9XYN0uJtj",
	"1n6yJn9N1Z90st8aGdXm6J26ihxjfUulP3xG/bDcgURuRhX2OnnEc27hW/j8KC2Luuui5tTWPy7NGdat",
	"wamJEUK0ln9c4n2i+9Rj9BQ85t8B3B0AhJDiv+cUcnJ/MNN7QLnXpzy2PORYulmB8iW34pB8jyLpZNIW",
	"+Xv+ESBG3aPVBF3woHA1LYnSHynGgzyZrE6JJkChNEXlBxIwldFeRIIOZWNfpwy1Y2OfYTvCjpfT+7i7",
	"0KPF3SzoFaPdYnhhIvjG96CsGTCGtGCbg3wkYfkoqIzXFWxzosh/Gj41z+NNPmHFe5r/2H/k/0mioi2S",
	"tdBHeqg2Tl96VoKcE9OxhJnfxERPKiaadS8wp6ewdH832G3YjmFtZL66uWK4nKsI7h8UpT0J5RJziVDi",
	"DgEVhm/3yKy7HpRIc9hfXKN49QW+hLxlshrVe/xfJmHjODUIYY9zSoeEFxHz9nGyx3/ZNIqBrmAPq3eD",
	"CBOwJotVgnDm4K6qA1FSXcmLRHAJebV227FvoyZ9z4jF+0PR1aXEqwtKos3M2q3XqkJE5IFUwoetUg+V",
	"Afu4MtlNYH4oLV9IxtMr6/vSv1f0TceYNx1jfmRNjF/frnBv2iu/Nh12XpeGOrbXPuMgz9lKfffouHxC",
	"XXj+yr6ViUcKGmF6kMT+Wor3gRt1ypJtB27H+eobXbxMnj9BpMrigBNv6UnCOhQJ63Vs40mF54++m6f6",
	"gfnGfxL8C3EuJt+OfmvK+5VPoXjEVP3G3w/x9Nv6FukTCXGxit5o2B3LqxiGVQTi7wv/C//rM/6n+P80",
	"Yrb0SP8mQHUn/UzwF59Qv8Wvw5a592SdyihRoO7UCEl1oy+sfMVQJ9EFRDl1IaDMpF6i2ttu/Bykmz0S",
	"Z2n/X8GZI+B9ha1uNP+v/leQF0e7+5PbFjwM7msT0uY8XUWHclbJt6+gq0yBa7TgQ5gu+C2Z9a/+V2Vy",
	"dbq0yCEEZcJj/dX/quCC/iJbECkhAXQFjeha/a9DTexfUogNdghpRKYyfYtl8ndjvYfoXqm66bAF+j3w",
	"JNCaXZJB0ePkkt5sGpj1dLNuNY3bRrOjmytbbURaC9NeHfdoHgCUpz4jvRkodQoCgo8Ty/yMnoa/r5x0",
	"xIOZob9bsINZ/wl+T0i/XD7s1k00ivo/7F3dbtzGFX4VYq8cQNJacuwmurMixxViW2q0bm5qFNRyJBOr",
	"JRck19FaMGDJcBrDQNw4vgiCpq6Ti14VXSkyojq2noF8hT5JMXNmyJnl8Gf4J1ndK6+8y5kzM+c788Mz",
	"3xc8W9QuLYBA/ssZ7cqH2n8fvtD8sX8EJDR4/Ge0+YWPyP8Tf9gL5eaI1fuwEcALmxlt4fIV2Q8JcTSO",
	"qgfsyWOmMMIPEY7t2Uk+xxJkJ+RupK8uFubmP2Z7Thplbg37G0mqW8LmMwwlXCDJqm3hirakW72l0cqG",
	"buHFRPIU+C1gBfb4Jv25bDLMqBESlDv6zpo+Qk5yfT/GdmhwKkXOoELiaiBVfcIR4fBhs2I209wzYzxW",
	"CfP2S451+zg2d8N52Cz9j9mNEZvIVSZxsojXonff4R0iti2LLBjzql+SrAC6Tjmki7HDJOVmOkUujajj",
	"FiKdTyiqZsFAWZ2pUzs3glm3tHLGj9gwwXEaN0xZyLpEgMUWKjSIrHbWcoQOPLLvQAcC4hWviXcCcTAj",
	"bF3ROo5uuRhKUZ5iwv7oJ0qO8SshQPpK0lb1hMWFuYVL2mpnDcKl8p4o7+naIcUBg1jFEaZe2OYMQKAw",
	"qBR/YkJI1QYYYlEl8UVeUv36gYWjCy+jVSiyxGXRxA7PxNXlSAJ0hJwl01pZuZUAbDJBam2NTZGJ+auH",
	"0U0YCnUaj/yTLHOuMHOWkIU2za6pOyMlo6Qr/VIm/U7oITrCCda8ivQ4qu+bjyR9k9Oc6nvlY+1qn2yT",
	"3Vu2t4y6Zl/fTrQivIPGVRhOA7mrnJ/XKNDoVeuObd+wra2Eal+wS8KwXhci1gEMD54dtIXLF4mypP8W",
	"5JzxiGXORvPa8nCDxAwGfokBQgLXvuKEvxAO99rQGdguUnlLBZPrxMZL1YCPJf6mZoTU7TJXOhdlMQDv",
	"Y8xujqoPYLcQ0jQVtWJeu+7Yrkucy/3iLrKu7aDuMGW4/07epI4jufngqUbZjw7oFf7JLaV0JXAcG6dK",
	"RQwbWWXcNV3PdlL4K78H4lmykX8WUyqSpfLRfv89LVk1gYCtnK8jVgSIkqyY1pJp5UkL4ErgFsPuDbNv",
	"eiWeX93cdJFXb+JQvPGpK5SMwSnhjZnDns+9PK7/cvvYCX+nWO5j/LiU8TC+nHVPd7xl3UMlfOSaZZQs",
	"ga4T3BJFfKI7RpnnV1lvd0aDMk35BDcEOQPd8UYlirlpWrCCKVOGvlO6jPcsfvBVqwSRSfRVF0TiuFYP",
	"Iu1d7q+V5QdKKav7kwceIfXMsf9OgSJM7N1CaXLygcJFdfj2nYa3YCNSHUZyblRxamfxgSrvUW0HdZE5",
	"ULrH6L8h6fFEf1swl5s3M/3oc1rvOXIl2qSMxBih64Jn0uGvmJcq73ilupPbR9FpHVmjKtK5ceknIJAa",
	"ybRJvGW9H54qw4q4TikEqCG/EHLc/lhCk5A/fnY50KRjQWy91IyteKP8hgQ0do+O26u/i9RAJimcLtiO",
	"uWVamulqlu1pbh99gO2+3JTdr8jkjnG1B9xTPBUQyV+rmFouCz65wWuYJFrpjolA9z9lryKV/uAplS+s",
	"37z2QRJL9w3TVQ/w+MFPzW0POUtm122ElhvbqaZ+Eu+CivEdyz3Mgnks4w8smm8EDbctfejdtR3zPjIA",
	"hQ1Fuufi+VDwDK6HRf1CJKUf0qO4cTWgPGYpJTmxUQyZXSpXrQDMH/wD/3WbJt8mIvOzDfSZbRTCJjwK",
	"6PzENlCufTf/0OfINQ1kdUe14zpqpRqyJX04hfYU2knoKobt3kZPGdrf+j+kYbpXENA9Ac21g7JXBJFR",
	"06dQnEIxhoaCGLQGyhj80X+ZgkFrUAyD1kB1RmVPXHfs4aAB2ELLVGEb9dYUtlPYxgBUDLaevjNL+9Mz",
	"kerW9Wf/O/9FMoQ7+s5VWvaoEJb5AtiKd4u8tqsbo5Omq4GV75cpWKdgjUElN1hBvxyh2W3T9VTFdqkE",
	"1xgE/fZJksoR1TklAl7fSwgduJPiUEMcIQrf+uiFEmtNPUZ+JVDCJTeSuPyHTR1lTpgRT4RuFAr/DPb9",
	"k9noAtLX/m/4I2FtJResqheSy+l4xVDQttCXKWnMLwjZRXjxGc5731H161+YuqVo05hc4d3L6Uxp71ZI",
	"ojBz3yLZzVxhVw2DldQUCSCrb8XatHPR/8U7MoTbpdOCW7BHHds/9t+y3GFK2RDsBY+CJ+xyAIgdnB80",
	"NuH7BUG7y/4EAatt5CEAb/RJ1Oyk9BdcWJFYTiiLKpjplokZzPsLJWWE0Kn5ZXmi1dPJ8qzDsxafrgKP",
	"yDDhZtBQNqP+g2qPvSFpMw+FK9T1wvL2wNCrhmXx+Vi05sxMyVNYnzKsa8dHbojb3kDhgt9qZ03gFI04",
	"nqRJVJnL3lVvUHLFG5bTFLjkdSfPobjL4mL5Uv7rU0tRil0WXO2sVXm3RNltVNy3vavDtV9IpnSRZeTg",
	"B5tg34U2h0w8aZ4L441rUZ1YxEKuMqsbW/wV9dfwvs/Z8lqJWYxrq6T/5naSwn56D85kUYqnPhdPYdU8",
	"9I+s/Gp9tHic5mw6hUgt1K7m+9Fh+Jnw+TivXElfz3Sz3D4+efkq55qC0k8ABQPkVwI1EqxnJq6CaRf8",
	"g+Ab/wj/BamqwWNKyvI6/s0H6aQCpbkEmuAgm6gwL4FAbMkR9vppv1+SURNwV0+nL5uqfdmUcdFX+f1S",
	"6mWWksTkwg3kQ40HdMI7Y5HGp4hKIpsjGry9EjM6P+uQlCEymSGkydnpOI1utsqr66VdqBpfZ1Q5tmMg",
	"R1HLItT50JiQ32tKG4bjwjehdnbwSGwTLxeM9/7RpZKMt7L046oDUjVnHiLJ1qcfWIc9O66uZ5s8BpOS",
	"ZMVZsc736+B64FEe9cAyNoH82Y3RrOfMWuS79ykMLI06DrSoioBASzqloBC1ZRoepuGh3vDwpe30PLOf",
	"cjcoTvAnUBGciKo67/AYhrREaQD+gtXcDMZYdedidSq/iKsyMOc1GbGcs6bCxtPdnls4B5FEuSN/HPyF",
	"ibf6R4StGrsWiFoSSgbCtA6t0GAXMvZ/IdQN44QXUSRdF5umnl7s9kBb7Q9D5IxyS0fh5zqjAVJ/Co5/",
	"jKubHnIKP72ENm2nQOVr+lbBp9bN+8KTtSdfuz1XKZ0lci4uIMmP/6jZbVz2tf7AG9WY/siZxWHrJ3+M",
	"v9coI/1ruqN9yMOsvYv/UeSoIXAiZIhA/U4Y0Sdp84kGJ2cZI2AguRVSYBXKu+gQ85uTTMT1UcLIDOIJ",
	"vouSVdDELjptryo4sgpeN3SR09YZf1jaNZAo2QTYJB4FTwiRYLAHPKTspF9KjSrzsdtuSLjqrt+8Vud6",
	"aKKq/BQlJ0wd4Ch4BPLUweNG1z6hhoa4btCI8thb8Ig3hJcSD8B/ZkK9jgPQSAj2NUgx8cdU+/VE0C1a",
	"uHgRr8FnNLq6+woYksK3OGG9J5Sln227BGXe1/7buT9Z/nfYff0DEO4gPwGmkYdEXHIc7AdP6bsvTt3s",
	"DdEc2SOL+WfB12RHsHihLC9maXeN1D2hd4OnVMQcnh7L8NPepZ9UScbCUgsDqIjmJljaUKzmjM3Y3Wd2",
	"RSJ39nkDSw3ytzn8TMXzu4Q2ssi0wQZCKFtpCsEeBbSVNbstqWQ6Z/z/zhlFfFUFRUQsWmnFn7QI5BSq",
	"sdspTSdE0jk+j0wY82//1+Ap23qFRGKtmZaJv76LdHg3Zel91FpsgbLzbCjtDLkapoOM1qLnDNEM5+8G",
	"2tSH215rsdXrtWZayBr2cQfSP/Cjw9admRba0fuDbVy4M8T9PBrgz67nmNZW60GtcxjpofybU34wTvxD",
	"8FiiE3JIZcDIXSTQ0sJLE/4cZOy/Patsd1VPS1V4c6oYOwVZV99Oy9X7G4X1UZjmOimfTTriN4g/VGR9",
	"zFFByjXDpPMWvVSAP94Au84c7gi6GPjuNAu0yd5JvIb/M52CHlcyWO854Czb+7NpIMszN01ktGaSEFiL",
	"owunyMQ9aTTjMThw7E0zFYTPcXsj2WOpygq9RkHkvvEygk6tjyauWVGUgBnxGW8NbIHLdIUkTfkC0hJh",
	"cx8dJexrJiQcBccR6TzFPiAZO3wfnB155Cqv04ltlLlHqm8SQ5x78rC7jO5p8G1rpjV0tnGo9byBu9hu",
	"u300a6B7c/f1vm7NOcjo64Zjb9jeXNfut/WB2b43Tw7bxRLXPX0LpZSZUd6dsCm7LNb7r6K2wh6EVMu+",
	"/at/yAjVGYxJpIt+EZdq578VJ1T+m0jDmP9fCTMKIR+JfiG+ZBLqSlgr879JPNDkfvSpuY2060PdMcSK",
	"hbHnv3nu/8t/0Xpw58H/AgAA//8i0VTxBSYDAA==",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
