// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package sme

import (
	"context"
	"net/http"

	"git.redmadrobot.com/backend-go/rmr-pkg/grpcx"
	"git.redmadrobot.com/backend-go/rmr-pkg/httpx"

	"git.redmadrobot.com/zaman/backend/zaman/gateways/sme/mappers"
	"git.redmadrobot.com/zaman/backend/zaman/gateways/sme/openapi"
)

func (s *Server) SmePaymentsGetPaymentOrderByTrNumber(w http.ResponseWriter, r *http.Request, transactionNumber openapi.PaymentsTransactionNumber) {
	httpx.NewSecureServerHandler[any, *openapi.SmePaymentsGetPaymentOrderByTrNumberResponse](w, r).
		WithResponseJSON().
		WithMethod(func(ctx context.Context, _ any) (*openapi.SmePaymentsGetPaymentOrderByTrNumberResponse, error) {
			var smePaymentsGetPaymentOrderByTrNumber mappers.SmePaymentsGetPaymentOrderByTrNumber
			smePaymentsGetPaymentOrderByTrNumber.SmePaymentsGetPaymentOrderByTrNumberRequest(transactionNumber)
			if paymentsSmeSmePaymentsGetPaymentOrderByTrNumberGRPCRequestMapErr := smePaymentsGetPaymentOrderByTrNumber.PaymentssmeSmePaymentsGetPaymentOrderByTrNumberGRPCRequest(); paymentsSmeSmePaymentsGetPaymentOrderByTrNumberGRPCRequestMapErr != nil {
				return nil, paymentsSmeSmePaymentsGetPaymentOrderByTrNumberGRPCRequestMapErr
			}
			paymentsSmeSmePaymentsGetPaymentOrderByTrNumberGRPC, err := s.Providers.Paymentssme.SmePaymentsGetPaymentOrderByTrNumber(ctx, smePaymentsGetPaymentOrderByTrNumber.GRPC.PaymentssmeSmePaymentsGetPaymentOrderByTrNumber.Request)
			if err != nil {
				return nil, grpcx.ErrorFromGRPC(err)
			}
			smePaymentsGetPaymentOrderByTrNumber.GRPC.PaymentssmeSmePaymentsGetPaymentOrderByTrNumber.Response = paymentsSmeSmePaymentsGetPaymentOrderByTrNumberGRPC

			if smePaymentsGetPaymentOrderByTrNumberResponseMapErr := smePaymentsGetPaymentOrderByTrNumber.SmePaymentsGetPaymentOrderByTrNumberResponse(); smePaymentsGetPaymentOrderByTrNumberResponseMapErr != nil {
				return nil, smePaymentsGetPaymentOrderByTrNumberResponseMapErr
			}
			return smePaymentsGetPaymentOrderByTrNumber.Openapi.Response, nil

		}).Handle()
}
