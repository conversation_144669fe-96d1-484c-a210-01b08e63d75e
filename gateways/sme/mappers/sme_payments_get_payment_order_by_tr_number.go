package mappers

import (
	"git.redmadrobot.com/zaman/backend/zaman/gateways/sme/openapi"
	pbPaymentssme "git.redmadrobot.com/zaman/backend/zaman/specs/proto/payments-sme"
)

// SmePaymentsGetPaymentOrderByTrNumber генерируется автоматически, не подлежит редактированию.
type SmePaymentsGetPaymentOrderByTrNumber struct {
	Openapi struct {
		Response *openapi.SmePaymentsGetPaymentOrderByTrNumberResponse
		Params   struct {
			TransactionNumber openapi.PaymentsTransactionNumber
		}
	}
	GRPC struct {
		PaymentssmeSmePaymentsGetPaymentOrderByTrNumber struct {
			Request  *pbPaymentssme.SmePaymentsGetPaymentOrderByTrNumberReq
			Response *pbPaymentssme.SmePaymentsGetPaymentOrderByTrNumberResp
		}
	}
}

// SmePaymentsGetPaymentOrderByTrNumberRequest генерируется автоматически, не подлежит редактированию.
func (hc *SmePaymentsGetPaymentOrderByTrNumber) SmePaymentsGetPaymentOrderByTrNumberRequest(transactionNumber openapi.PaymentsTransactionNumber) {
	hc.Openapi.Params.TransactionNumber = transactionNumber
}

func (hc *SmePaymentsGetPaymentOrderByTrNumber) PaymentssmeSmePaymentsGetPaymentOrderByTrNumberGRPCRequest() error {
	if hc.Openapi.Params.TransactionNumber == "" {
		return nil
	}

	hc.GRPC.PaymentssmeSmePaymentsGetPaymentOrderByTrNumber.Request = &pbPaymentssme.SmePaymentsGetPaymentOrderByTrNumberReq{
		TransactionNumber: hc.Openapi.Params.TransactionNumber,
	}

	return nil
}

func (hc *SmePaymentsGetPaymentOrderByTrNumber) SmePaymentsGetPaymentOrderByTrNumberResponse() error {
	if hc.GRPC.PaymentssmeSmePaymentsGetPaymentOrderByTrNumber.Response == nil {
		return nil
	}

	hc.Openapi.Response = &openapi.SmePaymentsGetPaymentOrderByTrNumberResponse{
		Id:                hc.GRPC.PaymentssmeSmePaymentsGetPaymentOrderByTrNumber.Response.GetId(),
		Title:             hc.GRPC.PaymentssmeSmePaymentsGetPaymentOrderByTrNumber.Response.GetTitle(),
		Link:              hc.GRPC.PaymentssmeSmePaymentsGetPaymentOrderByTrNumber.Response.GetLink(),
		Version:           int(hc.GRPC.PaymentssmeSmePaymentsGetPaymentOrderByTrNumber.Response.GetVersion()),
		TransactionStatus: openapi.SmePaymentsGetPaymentOrderByTrNumberResponseTransactionStatus(hc.GRPC.PaymentssmeSmePaymentsGetPaymentOrderByTrNumber.Response.GetTransactionStatus()),
	}

	return nil
}
