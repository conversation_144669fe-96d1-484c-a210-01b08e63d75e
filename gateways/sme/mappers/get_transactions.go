package mappers

import (
	openapi_types "github.com/oapi-codegen/runtime/types"
	"github.com/shopspring/decimal"
	"google.golang.org/protobuf/types/known/timestamppb"

	"git.redmadrobot.com/zaman/backend/zaman/gateways/sme/openapi"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	pbPayments "git.redmadrobot.com/zaman/backend/zaman/specs/proto/payments"
)

// GetTransactions генерируется автоматически, не подлежит редактированию.
type GetTransactions struct {
	Openapi struct {
		Response *openapi.PaymentsGetTransactionsResponse
		Params   struct {
			Params openapi.GetTransactionsParams
		}
	}
	GRPC struct {
		PaymentsGetTransactions struct {
			Request  *pbPayments.GetTransactionsReq
			Response *pbPayments.GetTransactionsResp
		}
	}
}

// GetTransactionsRequest генерируется автоматически, не подлежит редактированию.
func (hc *GetTransactions) GetTransactionsRequest(params openapi.GetTransactionsParams) {
	hc.Openapi.Params.Params = params
}

func (hc *GetTransactions) PaymentsGetTransactionsGRPCRequest() error {
	hc.GRPC.PaymentsGetTransactions.Request = &pbPayments.GetTransactionsReq{
		Accounts:     hc.Openapi.Params.Params.Accounts,
		MinAmount:    hc.Openapi.Params.Params.MinAmount,
		MaxAmount:    hc.Openapi.Params.Params.MaxAmount,
		Counterparty: hc.Openapi.Params.Params.Counterparty,
	}

	if hc.Openapi.Params.Params.StartDate != nil {
		hc.GRPC.PaymentsGetTransactions.Request.StartDate = timestamppb.New(hc.Openapi.Params.Params.StartDate.UTC())
	}

	if hc.Openapi.Params.Params.EndDate != nil {
		hc.GRPC.PaymentsGetTransactions.Request.EndDate = timestamppb.New(hc.Openapi.Params.Params.EndDate.UTC())
	}

	if hc.Openapi.Params.Params.Limit != nil {
		hc.GRPC.PaymentsGetTransactions.Request.Limit = *hc.Openapi.Params.Params.Limit
	}

	if hc.Openapi.Params.Params.Offset != nil {
		hc.GRPC.PaymentsGetTransactions.Request.Offset = *hc.Openapi.Params.Params.Offset
	}

	if hc.Openapi.Params.Params.Cards != nil {
		hc.GRPC.PaymentsGetTransactions.Request.Cards = *hc.Openapi.Params.Params.Cards
	}

	if hc.Openapi.Params.Params.Direction != nil {
		switch *hc.Openapi.Params.Params.Direction {
		case openapi.CREDIT:
			ot := pbPayments.OperationType_OTYPE_CREDIT
			hc.GRPC.PaymentsGetTransactions.Request.OperationType = &ot
		case openapi.DEBIT:
			ot := pbPayments.OperationType_OTYPE_DEBIT
			hc.GRPC.PaymentsGetTransactions.Request.OperationType = &ot
		}
	}

	return nil
}

func (hc *GetTransactions) PaymentsGetTransactionsResponse() error {
	if hc.GRPC.PaymentsGetTransactions.Response != nil {
		hc.Openapi.Response = &openapi.PaymentsGetTransactionsResponse{
			Limit:        hc.GRPC.PaymentsGetTransactions.Response.Limit,
			Offset:       hc.GRPC.PaymentsGetTransactions.Response.Offset,
			TotalCount:   hc.GRPC.PaymentsGetTransactions.Response.TotalCount,
			StartDate:    hc.GRPC.PaymentsGetTransactions.Response.StartDate.AsTime(),
			Transactions: paymentsTransactionsToOAPI(hc.GRPC.PaymentsGetTransactions.Response),
		}
	}
	return nil
}

func paymentsTransactionsToOAPI(resp *pbPayments.GetTransactionsResp) []openapi.PaymentsTransaction {
	transactions := make([]openapi.PaymentsTransaction, 0, len(resp.Transactions))
	for _, tx := range resp.Transactions {
		amountD, _ := decimal.NewFromString(tx.Amount)
		amountAPI, _ := amountD.Float64()

		txDate := tx.TransactionDate.AsTime().In(utils.KzTimeZone)
		if tx.Status == pbPayments.TransactionStatus_TS_COMPLETED {
			txDate = tx.ValueDate.AsTime().In(utils.KzTimeZone)
		}

		transactions = append(transactions, openapi.PaymentsTransaction{
			TransactionID:   tx.TransactionId,
			AccountNumber:   tx.Client.Account,
			TransactionType: paymentsTransactionTypeToOAPI(tx.TransactionType),
			Amount:          amountAPI,
			Currency:        tx.Currency,
			Direction:       paymentsTransactionOperationTypeToOAPI(tx.OperationType),
			Status:          paymentsTransactionStatusToOAPI(tx.Status),
			TransactionDate: openapi_types.Date{Time: txDate},
		})
	}

	return transactions
}

func paymentsTransactionOperationTypeToOAPI(operationType pbPayments.OperationType) openapi.PaymentsOperationType {
	switch operationType {
	default:
		fallthrough
	case pbPayments.OperationType_OTYPE_CREDIT:
		return openapi.PaymentsOperationTypeCREDIT
	case pbPayments.OperationType_OTYPE_DEBIT:
		return openapi.PaymentsOperationTypeDEBIT
	}
}

func paymentsTransactionStatusToOAPI(status pbPayments.TransactionStatus) openapi.PaymentsTransactionStatus {
	switch status {
	default:
		fallthrough
	case pbPayments.TransactionStatus_TS_IN_PROGRESS:
		return openapi.PaymentsTransactionStatusINPROGRESS
	case pbPayments.TransactionStatus_TS_COMPLETED:
		return openapi.PaymentsTransactionStatusCOMPLETED
	case pbPayments.TransactionStatus_TS_REJECTED:
		return openapi.PaymentsTransactionStatusREJECTED
	case pbPayments.TransactionStatus_TS_INITIALIZED:
		return openapi.PaymentsTransactionStatusINITIALIZED
	}
}

func paymentsTransactionTypeToOAPI(transactionType pbPayments.TransactionType) openapi.TransactionType {
	switch transactionType {
	default:
		fallthrough
	case pbPayments.TransactionType_TTYPE_OTHER:
		return openapi.OTHER
	case pbPayments.TransactionType_TTYPE_PAYMENT_BY_ACCOUNT:
		return openapi.PAYMENTBYACCOUNT
	case pbPayments.TransactionType_TTYPE_PAYMENT_MOBILE:
		return openapi.PAYMENTMOBILE
	case pbPayments.TransactionType_TTYPE_PAYMENT_TERMINAL:
		return openapi.PAYMENTTERMINAL
	}
}
