// Package openapi provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.5.0 DO NOT EDIT.
package openapi

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/go-chi/chi/v5"
)

const (
	BearerTokenAuthScopes = "BearerTokenAuth.Scopes"
)

// APIError Общий формат ошибки API
type APIError struct {
	Error string `json:"error"`

	// Fields Объект с описанием деталей ошибок
	Fields *map[string]string `json:"fields,omitempty"`
}

// BalanceUpdaterHealth Статус доступности balanceupdater
type BalanceUpdaterHealth struct {
	// Balanceupdater Статус сервиса balanceupdater (true - доступен, false - недоступен)
	Balanceupdater bool `json:"balanceupdater"`
}

// GetBalanceResponse Ответ с балансом пользователя
type GetBalanceResponse struct {
	// Balance Баланс пользователя
	Balance float64 `json:"balance"`

	// Currency Валюта баланса
	Currency string `json:"currency"`
}

// Health Статус микросервисов balance gateway
type Health struct {
	// Users Статус сервиса users
	Users bool `json:"users"`
}

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Получение баланса пользователя
	// (GET /balance)
	GetBalance(w http.ResponseWriter, r *http.Request)
	// Проверка работоспособности
	// (GET /health)
	Health(w http.ResponseWriter, r *http.Request)
	// Проверка доступности balanceupdater
	// (GET /health/balanceupdater)
	HealthBalanceUpdater(w http.ResponseWriter, r *http.Request)
	// Обработка RTP транзакций
	// (POST /rtp/transaction)
	ProcessRtpTransaction(w http.ResponseWriter, r *http.Request)
}

// Unimplemented server implementation that returns http.StatusNotImplemented for each endpoint.

type Unimplemented struct{}

// Получение баланса пользователя
// (GET /balance)
func (_ Unimplemented) GetBalance(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Проверка работоспособности
// (GET /health)
func (_ Unimplemented) Health(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Проверка доступности balanceupdater
// (GET /health/balanceupdater)
func (_ Unimplemented) HealthBalanceUpdater(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Обработка RTP транзакций
// (POST /rtp/transaction)
func (_ Unimplemented) ProcessRtpTransaction(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandlerFunc   func(w http.ResponseWriter, r *http.Request, err error)
}

type MiddlewareFunc func(http.Handler) http.Handler

// GetBalance operation middleware
func (siw *ServerInterfaceWrapper) GetBalance(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetBalance(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// Health operation middleware
func (siw *ServerInterfaceWrapper) Health(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.Health(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// HealthBalanceUpdater operation middleware
func (siw *ServerInterfaceWrapper) HealthBalanceUpdater(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.HealthBalanceUpdater(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// ProcessRtpTransaction operation middleware
func (siw *ServerInterfaceWrapper) ProcessRtpTransaction(w http.ResponseWriter, r *http.Request) {

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ProcessRtpTransaction(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

type UnescapedCookieParamError struct {
	ParamName string
	Err       error
}

func (e *UnescapedCookieParamError) Error() string {
	return fmt.Sprintf("error unescaping cookie parameter '%s'", e.ParamName)
}

func (e *UnescapedCookieParamError) Unwrap() error {
	return e.Err
}

type UnmarshalingParamError struct {
	ParamName string
	Err       error
}

func (e *UnmarshalingParamError) Error() string {
	return fmt.Sprintf("Error unmarshaling parameter %s as JSON: %s", e.ParamName, e.Err.Error())
}

func (e *UnmarshalingParamError) Unwrap() error {
	return e.Err
}

type RequiredParamError struct {
	ParamName string
}

func (e *RequiredParamError) Error() string {
	return fmt.Sprintf("Query argument %s is required, but not found", e.ParamName)
}

type RequiredHeaderError struct {
	ParamName string
	Err       error
}

func (e *RequiredHeaderError) Error() string {
	return fmt.Sprintf("Header parameter %s is required, but not found", e.ParamName)
}

func (e *RequiredHeaderError) Unwrap() error {
	return e.Err
}

type InvalidParamFormatError struct {
	ParamName string
	Err       error
}

func (e *InvalidParamFormatError) Error() string {
	return fmt.Sprintf("Invalid format for parameter %s: %s", e.ParamName, e.Err.Error())
}

func (e *InvalidParamFormatError) Unwrap() error {
	return e.Err
}

type TooManyValuesForParamError struct {
	ParamName string
	Count     int
}

func (e *TooManyValuesForParamError) Error() string {
	return fmt.Sprintf("Expected one value for %s, got %d", e.ParamName, e.Count)
}

// Handler creates http.Handler with routing matching OpenAPI spec.
func Handler(si ServerInterface) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{})
}

type ChiServerOptions struct {
	BaseURL          string
	BaseRouter       chi.Router
	Middlewares      []MiddlewareFunc
	ErrorHandlerFunc func(w http.ResponseWriter, r *http.Request, err error)
}

// HandlerFromMux creates http.Handler with routing matching OpenAPI spec based on the provided mux.
func HandlerFromMux(si ServerInterface, r chi.Router) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseRouter: r,
	})
}

func HandlerFromMuxWithBaseURL(si ServerInterface, r chi.Router, baseURL string) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseURL:    baseURL,
		BaseRouter: r,
	})
}

// HandlerWithOptions creates http.Handler with additional options
func HandlerWithOptions(si ServerInterface, options ChiServerOptions) http.Handler {
	r := options.BaseRouter

	if r == nil {
		r = chi.NewRouter()
	}
	if options.ErrorHandlerFunc == nil {
		options.ErrorHandlerFunc = func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		}
	}
	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandlerFunc:   options.ErrorHandlerFunc,
	}

	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/balance", wrapper.GetBalance)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/health", wrapper.Health)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/health/balanceupdater", wrapper.HealthBalanceUpdater)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/rtp/transaction", wrapper.ProcessRtpTransaction)
	})

	return r
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/7xYzW7bRhB+FWLbQ4sqlhInRaJT7SBJ3V/BcdEghg8ram0xpkh2uXTjCAQsuU2TJqjR",
	"nnpKEPQFaDWC6R/RrzD7RsXsUhJFUYoNJL0YNJecn2+++WaoNjHdluc6zBE+qbaJbzZZi6rLpdrKHc5d",
	"jtcN5pvc8oTlOqRK4BUcyucQw7Ehf4FE7sEZRLJrQCKfQQyHcAKxsVRbISXCHtOWZzO0wbQx8mChQsIS",
	"8bjrMS4s5mcO20TseoxUiS+45Wzhc5sWsxvqGdpoWBgBtWsT7069UhDt79CHE9k1ZMeABM4hlh2IYAAx",
	"9OHMgLfQl12I4BT6cDzOI4ETUhrad+uPmClIGJYIZz8FFmcNUl1PI9+YeqxElqlNHZP94DWoYPxLRm3R",
	"LADzDXqWXbmPsb2FRHbwHziHgb6G2KhrS4G2RPLg5Y7nu5Ad6Ms96GkIcqaNTwQPmHFlMpA+DErGJrV9",
	"dTKAfv700zFKdde1GXWmYMrFWITXPSZSyFaZ77mOz4qoJ7vQw2qpUh6qmkUwkB1IsJDnkMCpfAlHkEAP",
	"c4Y+nMqDWZAVOPhzbHKeuU2Xt6ggVdJwg7rNxgA4QavOOOZjBpwzx9wtcPIXOpF/YFkmk4iyPUO+frg2",
	"NjzkdzGwJOOvCNyL0e8MYjiRe1jdDEsS6A15YmxRwX6mu1OABj7j/uWop195J3P0YxtFfegzM+CW2L2P",
	"qqXjWGaUM77mbjNnKdAJ19Wtu8OCffUjgqqETrlVp+MwmkJ4JETrlrPp4vum6whqCiVTLWrZ+BI1t5nT",
	"+OIJbVFnYfsJKRGHKnMP8Y6xrM+NNUZb04q0VFvBBjuVB4bcgwiFRnbliylGR1gPjMwSig5pdxj3dA1S",
	"id1h3Nd2G2znSuXW1c+vVG4uLt5g12+ha9djDvUsUiWLC5WFRSwcFU2FVTnTBVtMFPI0gSPoYZTyOUSq",
	"77IhGqojTuS+fA59+BcSAyLoya4aCvGwbWCAWqaPZ3cUsomi45UGqWbEgCAdtB6oqK9VKsOqMEcFTT3P",
	"tkz1bvmRj5EPBxlefczZJqmSj8rjSVdOx1y5QHJU6XMw/CM7KHTyGQzkCz0g5qgQon69cvW9BTkaxEWh",
	"vRqN3CiPfSSfQgzxRKeQ6npBj6xvhBsl4getFuW7aPW1qtK+/A3VHYdkjpiz64jOys2R2BTT6jVKjAJw",
	"TwWOuKlRl8gD9CcPDOgptfh1niblgO+lHBtL1CSlUgn8gHRKPRTV6W8YZHalpzrHxFBZp/qIKM9MNl/F",
	"XL3ygI5kRVk6V38TOBzvFNlClacXiHfWTR6k9IeePIAj+dKYNz/O8nsGMkvuQR+OjK3V2m1DI3e7yczt",
	"GXWb3KY+ZBUL97aiml5+cwtL5EZl8f9RhjfjCuTRL9rhLkewC6aLHOPCKwtOHZ+aOrQ28Vy/gF53nIbn",
	"Wo4YzkYkbIbI+FFx//ulmoHSBucpy5QOJDiW9J2n0Jcd2YEYBigGI1HAAxjILhqcYliNuybz/VXhrWUC",
	"1WsI88Wy29idUzPfpd5nj1v2ZN0mc1OBP/j2G0PNTJXVQOVxokS6D8eFi17WzTs8XN64YI/F+7Y6sbvh",
	"t0R4qUa9MJSTE1gridzHiaR7Us3hwvBmj3aIikgXFSUf6xn/XlK5SwNbzPowzXxRXyidiXWgp8ZjDG+H",
	"q4Cxuvrd/D5/NZX96lqtCIFj7d5nfEct/uttEnBcjdsed4VrunZYLZfbTdcXYZl6VnnnKu6qlFu0bmsq",
	"NEcqsIkQkCqxXZPaeLt6s3LzWvorgTI2+Rzu6PjlwJyghZ8Iamcvpbc3wjDcCP8LAAD//6evKXLZEAAA",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
