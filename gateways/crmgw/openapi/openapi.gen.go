// Package openapi provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.5.0 DO NOT EDIT.
package openapi

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"
	"time"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/go-chi/chi/v5"
	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

const (
	ApiKeyAuthScopes      = "ApiKeyAuth.Scopes"
	BearerTokenAuthScopes = "BearerTokenAuth.Scopes"
)

// Defines values for ClientAccountResponseCurrency.
const (
	ClientAccountResponseCurrencyCNY ClientAccountResponseCurrency = "CNY"
	ClientAccountResponseCurrencyEUR ClientAccountResponseCurrency = "EUR"
	ClientAccountResponseCurrencyKZT ClientAccountResponseCurrency = "KZT"
	ClientAccountResponseCurrencyRUB ClientAccountResponseCurrency = "RUB"
	ClientAccountResponseCurrencyUSD ClientAccountResponseCurrency = "USD"
)

// Defines values for ClientAccountResponseStatus.
const (
	ACTIVE   ClientAccountResponseStatus = "ACTIVE"
	ARCHIVED ClientAccountResponseStatus = "ARCHIVED"
	ARRESTED ClientAccountResponseStatus = "ARRESTED"
	BLOCKED  ClientAccountResponseStatus = "BLOCKED"
	CLOSED   ClientAccountResponseStatus = "CLOSED"
	MISTAKEN ClientAccountResponseStatus = "MISTAKEN"
	REOPENED ClientAccountResponseStatus = "REOPENED"
)

// Defines values for ClientAccountResponseType.
const (
	ClientAccountResponseTypeBUCO   ClientAccountResponseType = "BUCO"
	ClientAccountResponseTypeBUFB   ClientAccountResponseType = "BUFB"
	ClientAccountResponseTypeCURR   ClientAccountResponseType = "CURR"
	ClientAccountResponseTypeLOAN   ClientAccountResponseType = "LOAN"
	ClientAccountResponseTypeOTHERS ClientAccountResponseType = "OTHERS"
	ClientAccountResponseTypeTU     ClientAccountResponseType = "TU"
)

// Defines values for CurrencyCode.
const (
	CurrencyCodeCNY CurrencyCode = "CNY"
	CurrencyCodeEUR CurrencyCode = "EUR"
	CurrencyCodeKZT CurrencyCode = "KZT"
	CurrencyCodeRUB CurrencyCode = "RUB"
	CurrencyCodeUSD CurrencyCode = "USD"
)

// Defines values for LoanDetailsProductType.
const (
	LoanDetailsProductTypeLOAN      LoanDetailsProductType = "LOAN"
	LoanDetailsProductTypeREFINANCE LoanDetailsProductType = "REFINANCE"
)

// Defines values for ScheduleItemStatus.
const (
	END     ScheduleItemStatus = "END"
	OVERDUE ScheduleItemStatus = "OVERDUE"
	PLAN    ScheduleItemStatus = "PLAN"
)

// Defines values for TransactionType.
const (
	OTHER            TransactionType = "OTHER"
	PAYMENTBYACCOUNT TransactionType = "PAYMENT_BY_ACCOUNT"
	PAYMENTMOBILE    TransactionType = "PAYMENT_MOBILE"
	PAYMENTTERMINAL  TransactionType = "PAYMENT_TERMINAL"
)

// Defines values for TransactionsOperationType.
const (
	TransactionsOperationTypeCREDIT TransactionsOperationType = "CREDIT"
	TransactionsOperationTypeDEBIT  TransactionsOperationType = "DEBIT"
)

// Defines values for TransactionsTransactionStatus.
const (
	COMPLETED   TransactionsTransactionStatus = "COMPLETED"
	INITIALIZED TransactionsTransactionStatus = "INITIALIZED"
	INPROGRESS  TransactionsTransactionStatus = "IN_PROGRESS"
	REJECTED    TransactionsTransactionStatus = "REJECTED"
)

// Defines values for OriginHeaderParam.
const (
	OriginHeaderParamFIZ OriginHeaderParam = "FIZ"
	OriginHeaderParamIP  OriginHeaderParam = "IP"
)

// Defines values for OriginQueryParam.
const (
	OriginQueryParamFIZ OriginQueryParam = "FIZ"
	OriginQueryParamIP  OriginQueryParam = "IP"
)

// Defines values for TransactionsOperationTypeParam.
const (
	TransactionsOperationTypeParamCREDIT TransactionsOperationTypeParam = "CREDIT"
	TransactionsOperationTypeParamDEBIT  TransactionsOperationTypeParam = "DEBIT"
)

// Defines values for GetLoanDetailsParamsOrigin.
const (
	GetLoanDetailsParamsOriginFIZ GetLoanDetailsParamsOrigin = "FIZ"
	GetLoanDetailsParamsOriginIP  GetLoanDetailsParamsOrigin = "IP"
)

// Defines values for GetAccountTransactionsParamsDirection.
const (
	CREDIT GetAccountTransactionsParamsDirection = "CREDIT"
	DEBIT  GetAccountTransactionsParamsDirection = "DEBIT"
)

// Defines values for GetClientInfoParamsOrigin.
const (
	GetClientInfoParamsOriginFIZ GetClientInfoParamsOrigin = "FIZ"
	GetClientInfoParamsOriginIP  GetClientInfoParamsOrigin = "IP"
)

// APIError Ошибка для CRM
type APIError struct {
	// Code Код ошибки
	Code *int `json:"code,omitempty"`

	// Message Текст ошибки
	Message *string `json:"message,omitempty"`
}

// Account defines model for Account.
type Account struct {
	// AccountID Идентификатор счета
	AccountID string `json:"accountID"`

	// Currency Валюта счета
	Currency string `json:"currency"`

	// Iban IBAN счета
	Iban string `json:"iban"`

	// OpenDate Дата открытия счета
	OpenDate openapi_types.Date `json:"openDate"`

	// Status Статус счета
	Status string `json:"status"`
}

// AccountArrest defines model for AccountArrest.
type AccountArrest struct {
	// Blocking Признак полной или частичной блокировки, накладывает ограничения на определенную сумму - сумму ареста
	Blocking bool `json:"blocking"`
}

// AccountInfo Информация по счету
type AccountInfo struct {
	// Amount Баланс счета
	Amount Money `json:"amount"`

	// BankBic БИК банка
	BankBic string `json:"bankBic"`

	// BankBin БИН банка
	BankBin string `json:"bankBin"`

	// BankName Наименование банка
	BankName string `json:"bankName"`

	// FullName ФИО клиента
	FullName string `json:"fullName"`

	// Iban Счет для погашения (IBAN)
	Iban string `json:"iban"`

	// IbanLastDigits Замаскированный номер счета списания
	IbanLastDigits string `json:"ibanLastDigits"`

	// Iin ИИН клиента
	Iin string `json:"iin"`
}

// Address defines model for Address.
type Address struct {
	// BeginDate Дата начала регистрации
	BeginDate *openapi_types.Date `json:"beginDate,omitempty"`

	// Building Дом
	Building *string `json:"building,omitempty"`

	// City Город
	City *string `json:"city,omitempty"`

	// Corpus Корпус
	Corpus *string `json:"corpus,omitempty"`

	// Country Страна
	Country *string `json:"country,omitempty"`

	// District Район
	District *string `json:"district,omitempty"`

	// Flat Квартира
	Flat *string `json:"flat,omitempty"`

	// Region Область/Регион
	Region *string `json:"region,omitempty"`

	// Street Улица
	Street *string `json:"street,omitempty"`
}

// ClientAccountResponse defines model for ClientAccountResponse.
type ClientAccountResponse struct {
	// AccountID Идентификатор счёта
	AccountID string        `json:"accountID"`
	Arrest    AccountArrest `json:"arrest"`

	// AvailableBalance Доступный баланс (balance-plansum-partiallyDebtAmount)
	AvailableBalance *float64 `json:"availableBalance,omitempty"`

	// Balance Баланс (остаток счёта)
	Balance *float64 `json:"balance,omitempty"`

	// BalanceNatval Баланс счета в нац. валюте (KZT)
	BalanceNatval *float64 `json:"balanceNatval,omitempty"`

	// CloseDate Дата закрытия счёта
	CloseDate *openapi_types.Date `json:"closeDate,omitempty"`

	// Currency Валюта счёта
	Currency ClientAccountResponseCurrency `json:"currency"`

	// DocumentNumber Номер договора
	DocumentNumber *string `json:"documentNumber,omitempty"`

	// Iban Номер счета iban
	Iban string `json:"iban"`

	// OpenDate Дата открытия счёта
	OpenDate openapi_types.Date `json:"openDate"`

	// PlanSum Плановые суммы
	PlanSum *float64 `json:"planSum,omitempty"`

	// Status Статус счёта
	Status ClientAccountResponseStatus `json:"status"`

	// Type Тип счёта
	Type ClientAccountResponseType `json:"type"`
}

// ClientAccountResponseCurrency Валюта счёта
type ClientAccountResponseCurrency string

// ClientAccountResponseStatus Статус счёта
type ClientAccountResponseStatus string

// ClientAccountResponseType Тип счёта
type ClientAccountResponseType string

// CurrencyCode Код валюты
type CurrencyCode string

// DepositBalance defines model for DepositBalance.
type DepositBalance struct {
	// Currency Валюта депозитного счета
	Currency string `json:"currency"`

	// DepositAmount Первоначальная сумма депозита
	DepositAmount float64 `json:"depositAmount"`

	// DepositBalance Актуальный баланс в валюте депозитного счета
	DepositBalance float64 `json:"depositBalance"`
}

// DepositDetails defines model for DepositDetails.
type DepositDetails struct {
	// IsReplenishable Доступно ли пополнение
	IsReplenishable bool `json:"isReplenishable"`

	// IsReplenishableDays Сколько дней еще доступно пополнение
	IsReplenishableDays int32 `json:"isReplenishableDays"`

	// IsWithdrawal Доступно ли снятие
	IsWithdrawal bool `json:"isWithdrawal"`

	// MinBal Минимальный баланс
	MinBal float64 `json:"minBal"`

	// PayoutMethod Способ выплаты
	PayoutMethod string `json:"payoutMethod"`
}

// DepositInterest defines model for DepositInterest.
type DepositInterest struct {
	// EffectiveRate Эффективная ставка доходности в процентах
	EffectiveRate string `json:"effectiveRate"`

	// NearestPayDate Следующая дата выплаты процентов
	NearestPayDate openapi_types.Date `json:"nearestPayDate"`

	// PayedAmount Общая сумма выплаченной доходности
	PayedAmount float64 `json:"payedAmount"`

	// Rate Доходность в процентах
	Rate string `json:"rate"`
}

// DepositProduct defines model for DepositProduct.
type DepositProduct struct {
	// AgreementCode Номер депозитного договора
	AgreementCode string `json:"agreementCode"`

	// BeginDate Дата открытия договора
	BeginDate openapi_types.Date `json:"beginDate"`

	// Code Код продукта
	Code string `json:"code"`

	// EndDate Дата окончания действия договора
	EndDate openapi_types.Date `json:"endDate"`

	// Name Название продукта
	Name string `json:"name"`

	// State Статус депозита
	State string `json:"state"`

	// Term Срок депозита в месяцах
	Term int32 `json:"term"`
}

// DepositShort defines model for DepositShort.
type DepositShort struct {
	// Currency Валюта депозита
	Currency string `json:"currency"`

	// DepositBalance Баланс депозита
	DepositBalance float64 `json:"depositBalance"`

	// EndDate Дата окончания действия договора
	EndDate openapi_types.Date `json:"endDate"`

	// Id Идентификатор депозита
	Id string `json:"id"`

	// PayedAmount Общая сумма процентов
	PayedAmount string `json:"payedAmount"`

	// ProductCode Код шаблона продукта
	ProductCode string `json:"productCode"`

	// Rate Доходность в процентах
	Rate string `json:"rate"`
}

// GetDepositDetailResp defines model for GetDepositDetailResp.
type GetDepositDetailResp struct {
	Balance  DepositBalance  `json:"balance"`
	Details  DepositDetails  `json:"details"`
	Interest DepositInterest `json:"interest"`
	Product  DepositProduct  `json:"product"`
}

// GetLoansDetailsResponse defines model for GetLoansDetailsResponse.
type GetLoansDetailsResponse struct {
	// Loan Информация по кредиту
	Loan LoanDetails `json:"loan"`
}

// Health defines model for Health.
type Health struct {
	// AltScoreBridge Статус сервиса altScoreBridge
	AltScoreBridge bool `json:"altScoreBridge"`

	// AmlBridge Статус сервиса amlBridge
	AmlBridge bool `json:"amlBridge"`

	// Antifraud Статус сервиса antifraud
	Antifraud bool `json:"antifraud"`

	// ApBridge Статус сервиса apBridge
	ApBridge bool `json:"apBridge"`

	// BalanceUpdater Статус сервиса balanceUpdater
	BalanceUpdater bool `json:"balanceUpdater"`

	// BitrixBridge Статус сервиса bitrixBridge
	BitrixBridge bool `json:"bitrixBridge"`

	// BsasBridge Статус сервиса bsasBridge
	BsasBridge bool `json:"bsasBridge"`

	// BtsBridge Статус сервиса btsBridge
	BtsBridge bool `json:"btsBridge"`

	// CardsAccounts Статус сервиса cardsAccounts
	CardsAccounts bool `json:"cardsAccounts"`

	// Collection Статус сервиса collection
	Collection bool `json:"collection"`

	// ColvirBridge Статус сервиса colvirBridge
	ColvirBridge bool `json:"colvirBridge"`

	// Crm Статус сервиса crm
	Crm bool `json:"crm"`

	// Deposits Статус сервиса deposits
	Deposits bool `json:"deposits"`

	// Dictionary Статус сервиса dictionary
	Dictionary bool `json:"dictionary"`

	// Documents Статус сервиса documents
	Documents bool `json:"documents"`

	// FileGuard Статус сервиса fileGuard
	FileGuard bool `json:"fileGuard"`

	// ForeignActivity Статус сервиса foreignActivity
	ForeignActivity bool `json:"foreignActivity"`

	// JiraBridge Статус сервиса jiraBridge
	JiraBridge bool `json:"jiraBridge"`

	// JuicyscoreBridge Статус сервиса juicyscoreBridge
	JuicyscoreBridge bool `json:"juicyscoreBridge"`

	// KaspiBridge Статус сервиса kaspiBridge
	KaspiBridge bool `json:"kaspiBridge"`

	// KeycloakProxy Статус сервиса keycloakProxy
	KeycloakProxy bool `json:"keycloakProxy"`

	// KgdBridge Статус сервиса kgdBridge
	KgdBridge bool `json:"kgdBridge"`

	// Liveness Статус сервиса liveness
	Liveness bool `json:"liveness"`

	// Loans Статус сервиса loans
	Loans bool `json:"loans"`

	// Notifications Статус сервиса notifications
	Notifications bool `json:"notifications"`

	// Otp Статус сервиса otp
	Otp bool `json:"otp"`

	// Payments Статус сервиса payments
	Payments bool `json:"payments"`

	// PaymentsSme Статус сервиса paymentsSme
	PaymentsSme bool `json:"paymentsSme"`

	// PkbBridge Статус сервиса pkbBridge
	PkbBridge bool `json:"pkbBridge"`

	// ProcessingBridge Статус сервиса processingBridge
	ProcessingBridge bool `json:"processingBridge"`

	// QazpostBridge Статус сервиса qazpostBridge
	QazpostBridge bool `json:"qazpostBridge"`

	// Referral Статус сервиса referral
	Referral bool `json:"referral"`

	// Scoring Статус сервиса scoring
	Scoring bool `json:"scoring"`

	// SeonBridge Статус сервиса seonBridge
	SeonBridge bool `json:"seonBridge"`

	// SmsBridge Статус сервиса smsBridge
	SmsBridge bool `json:"smsBridge"`

	// SprBridge Статус сервиса sprBridge
	SprBridge bool `json:"sprBridge"`

	// TaskManager Статус сервиса taskManager
	TaskManager bool `json:"taskManager"`

	// Tokenize Статус сервиса tokenize
	Tokenize bool `json:"tokenize"`

	// Users Статус сервиса users
	Users bool `json:"users"`
}

// Loan defines model for Loan.
type Loan struct {
	// ApplicationDueDate Дата подачи заявки
	ApplicationDueDate *openapi_types.Date `json:"applicationDueDate,omitempty"`

	// ApplicationID Идентификатор заявки
	ApplicationID string `json:"applicationID"`

	// LoanAmount Сумма займа
	LoanAmount LoanAmount `json:"loanAmount"`

	// ProductType Тип продукта
	ProductType string `json:"productType"`

	// Purpose Цель финансирования
	Purpose *string `json:"purpose,omitempty"`

	// Status Статус кредитной заявки
	Status *string `json:"status,omitempty"`
}

// LoanAmount Сумма займа
type LoanAmount struct {
	// CurrencyCode Код валюты
	CurrencyCode string `json:"currencyCode"`

	// Value Сумма займа
	Value string `json:"value"`
}

// LoanDetails Информация по кредиту
type LoanDetails struct {
	// Account Информация по счету
	Account AccountInfo `json:"account"`

	// Details Детали финансирования
	Details    LoanDetailsObject `json:"details"`
	HasOverdue bool              `json:"hasOverdue"`

	// NextPayment Информация о ближайшем платеже
	NextPayment NextPayment `json:"nextPayment"`

	// PaidInfo Информация о выплате по кредиту
	PaidInfo PaidInfo `json:"paidInfo"`

	// ProductType Тип продукта LOAN - Кредит REFINANCE - Рефинансирование
	ProductType LoanDetailsProductType `json:"productType"`

	// Reason Причина отказа
	Reason *Reason `json:"reason,omitempty"`

	// Schedule График погашения
	Schedule []ScheduleItem `json:"schedule"`
}

// LoanDetailsProductType Тип продукта LOAN - Кредит REFINANCE - Рефинансирование
type LoanDetailsProductType string

// LoanDetailsObject Детали финансирования
type LoanDetailsObject struct {
	// ContractNumber Номер договора
	ContractNumber string `json:"contractNumber"`

	// EndDate Дата окончания договора
	EndDate openapi_types.Date `json:"endDate"`

	// InterestAmount Сумма наценки на весь срок финансирования
	InterestAmount Money `json:"interestAmount"`

	// LoanAmount Сумма финансирования (на весь срок)
	LoanAmount Money `json:"loanAmount"`

	// StartDate Дата начала договора
	StartDate openapi_types.Date `json:"startDate"`

	// Term Срок финасирования
	Term int `json:"term"`
}

// Money Сумма
type Money struct {
	// CurrencyCode Код валюты
	CurrencyCode CurrencyCode `json:"currencyCode"`

	// Value Сумма
	Value string `json:"value"`
}

// NextPayment Информация о ближайшем платеже
type NextPayment struct {
	// Amount Сумма ближайшего платежа
	Amount Money `json:"amount"`

	// Date Дата ближайшего платежа
	Date openapi_types.Date `json:"date"`
}

// Overdue Просроченный платеж
type Overdue struct {
	// Days Количество дней просрочки
	Days *int `json:"days,omitempty"`

	// FineDebt Сумма пени
	FineDebt *Money `json:"fineDebt,omitempty"`

	// Hint Подсказка по просроченному платежу
	Hint string `json:"hint"`
}

// PaidInfo Информация о выплате по кредиту
type PaidInfo struct {
	// RemainingAmount Оставшаяся сумма выплаты по кредиту
	RemainingAmount Money `json:"remainingAmount"`
}

// PaymentDetails Детали платежа
type PaymentDetails struct {
	// BaseAmount Сумма финансирования (за 1 месяц)
	BaseAmount Money `json:"baseAmount"`

	// Date Дата платежа
	Date openapi_types.Date `json:"date"`

	// InterestAmount Сумма наценки (заполняется в зависимости от наличия просрочки)
	InterestAmount Money `json:"interestAmount"`

	// Overdue Просроченный платеж
	Overdue *Overdue `json:"overdue,omitempty"`
}

// Reason Причина отказа
type Reason struct {
	// Code Код ошибки
	Code string `json:"code"`

	// MessageText Текст ошибки
	MessageText string `json:"messageText"`

	// Title Заголовок ошибки
	Title string `json:"title"`
}

// ScheduleItem График погашения
type ScheduleItem struct {
	// Date Дата платежа
	Date openapi_types.Date `json:"date"`

	// Details Детали платежа
	Details PaymentDetails `json:"details"`

	// PaymentAmount Сумма платежа (Сумма финансирования (baseAmount) + Наценка (interestAmount))
	PaymentAmount Money `json:"paymentAmount"`

	// Status Статус платежа PLAN - К оплате END - Погашен OVERDUE - Просрочен
	Status ScheduleItemStatus `json:"status"`
}

// ScheduleItemStatus Статус платежа PLAN - К оплате END - Погашен OVERDUE - Просрочен
type ScheduleItemStatus string

// Transaction defines model for Transaction.
type Transaction struct {
	// AccountNumber Номер cчёта
	AccountNumber string `json:"accountNumber"`

	// Amount Сумма транзакции
	Amount float64 `json:"amount"`

	// Category Категория операции
	Category *string `json:"category,omitempty"`

	// Currency Валюта транзакции
	Currency string `json:"currency"`

	// Direction Тип операции
	Direction TransactionsOperationType `json:"direction"`

	// Status Статус транзакции
	Status TransactionsTransactionStatus `json:"status"`

	// TransactionDate Дата операции
	TransactionDate time.Time `json:"transactionDate"`

	// TransactionID Уникальный идентификатор транзакции
	TransactionID string `json:"transactionID"`

	// TransactionType Внутренний тип перевода
	TransactionType TransactionType `json:"transactionType"`
}

// TransactionType Внутренний тип перевода
type TransactionType string

// TransactionsOperationType Тип операции
type TransactionsOperationType string

// TransactionsResponse defines model for TransactionsResponse.
type TransactionsResponse struct {
	// Limit Лимит количества возвращаемых транзакций
	Limit int64 `json:"limit"`

	// Offset Смещение для пагинации
	Offset int64 `json:"offset"`

	// StartDate Начальная дата для фильтрации транзакций
	StartDate *time.Time `json:"startDate,omitempty"`

	// TotalCount Общее количество транзакций
	TotalCount int64 `json:"totalCount"`

	// Transactions Список транзакций
	Transactions []Transaction `json:"transactions"`
}

// TransactionsTransactionStatus Статус транзакции
type TransactionsTransactionStatus string

// UserResponse defines model for UserResponse.
type UserResponse struct {
	Accounts *[]Account `json:"accounts,omitempty"`

	// BirthDate Дата рождения
	BirthDate  *openapi_types.Date `json:"birthDate,omitempty"`
	BirthPlace *Address            `json:"birthPlace,omitempty"`

	// Citizenship Гражданство
	Citizenship *string         `json:"citizenship,omitempty"`
	Deposits    *[]DepositShort `json:"deposits,omitempty"`

	// Firstname Имя клиента
	Firstname *string `json:"firstname,omitempty"`

	// Gender Пол клиента
	Gender *string `json:"gender,omitempty"`

	// Iin ИИН клиента
	Iin string `json:"iin"`

	// IpName Наименование ИП
	IpName *string `json:"ipName,omitempty"`

	// IssuingAuthority Орган выдачи
	IssuingAuthority *string `json:"issuingAuthority,omitempty"`

	// Lastname Фамилия клиента
	Lastname *string `json:"lastname,omitempty"`
	Loans    *[]Loan `json:"loans,omitempty"`

	// OkedValueName ОКЭД
	OkedValueName *string `json:"okedValueName,omitempty"`

	// Origin Тип клиента
	Origin string `json:"origin"`

	// Patronymic Отчество клиента
	Patronymic *string `json:"patronymic,omitempty"`

	// Phone Номер телефона клиента
	Phone      string   `json:"phone"`
	RegAddress *Address `json:"regAddress,omitempty"`

	// RegistrationCert Номер сертификата
	RegistrationCert *string `json:"registrationCert,omitempty"`

	// RegistrationDate Дата регистрации
	RegistrationDate *openapi_types.Date `json:"registrationDate,omitempty"`

	// Status Статус клиента
	Status string `json:"status"`

	// UserID Уникальный идентификатор клиента
	UserID string `json:"userID"`
}

// ValidationError Ошибка валидации данных в теле запроса
type ValidationError struct {
	// Error Код ошибки
	Error string `json:"error"`

	// Fields Объект с описанием ошибок валидации полей
	Fields *map[string]string `json:"fields,omitempty"`
}

// AccountIDPathParam defines model for AccountIDPathParam.
type AccountIDPathParam = string

// ApplicationIDQueryParam defines model for ApplicationIDQueryParam.
type ApplicationIDQueryParam = string

// DepositIDQueryParam defines model for DepositIDQueryParam.
type DepositIDQueryParam = string

// IinQueryParam defines model for IinQueryParam.
type IinQueryParam = string

// OriginHeaderParam defines model for OriginHeaderParam.
type OriginHeaderParam string

// OriginQueryParam defines model for OriginQueryParam.
type OriginQueryParam string

// PhoneQueryParam defines model for PhoneQueryParam.
type PhoneQueryParam = string

// StatusQueryParam defines model for StatusQueryParam.
type StatusQueryParam = string

// TransactionsAccountParam defines model for TransactionsAccountParam.
type TransactionsAccountParam = string

// TransactionsEndDateParam defines model for TransactionsEndDateParam.
type TransactionsEndDateParam = time.Time

// TransactionsLimitParam defines model for TransactionsLimitParam.
type TransactionsLimitParam = int64

// TransactionsMaxAmountParam defines model for TransactionsMaxAmountParam.
type TransactionsMaxAmountParam = string

// TransactionsMinAmountParam defines model for TransactionsMinAmountParam.
type TransactionsMinAmountParam = string

// TransactionsOffsetParam defines model for TransactionsOffsetParam.
type TransactionsOffsetParam = int64

// TransactionsOperationTypeParam defines model for TransactionsOperationTypeParam.
type TransactionsOperationTypeParam string

// TransactionsStartDateParam defines model for TransactionsStartDateParam.
type TransactionsStartDateParam = time.Time

// UserIDQueryParam defines model for UserIDQueryParam.
type UserIDQueryParam = string

// RespEmpty defines model for RespEmpty.
type RespEmpty = map[string]interface{}

// LogoutUserRequest defines model for LogoutUserRequest.
type LogoutUserRequest struct {
	// Phone Номер телефона в формате +7XXXXXXXXXX
	Phone string `json:"phone"`
}

// GetAccountDetailsParams defines parameters for GetAccountDetails.
type GetAccountDetailsParams struct {
	// AccountID Идентификатор запрашиваемого счета (uuid)
	AccountID AccountIDPathParam `form:"accountID" json:"accountID"`

	// UserID Идентификатор пользователя (uuid)
	UserID UserIDQueryParam `form:"userID" json:"userID"`
}

// GetDepositDetailsParams defines parameters for GetDepositDetails.
type GetDepositDetailsParams struct {
	// UserID Идентификатор пользователя (uuid)
	UserID UserIDQueryParam `form:"userID" json:"userID"`

	// DepositID Идентификатор депозита
	DepositID DepositIDQueryParam `form:"depositID" json:"depositID"`
}

// GetLoanDetailsParams defines parameters for GetLoanDetails.
type GetLoanDetailsParams struct {
	// ApplicationID Идентификатор заявки на кредит (uuid)
	ApplicationID ApplicationIDQueryParam `form:"applicationID" json:"applicationID"`

	// UserID Идентификатор пользователя (uuid)
	UserID UserIDQueryParam `form:"userID" json:"userID"`

	// Status Статус заявки на кредит
	Status StatusQueryParam `form:"status" json:"status"`

	// Origin Флаг типа клиента ФЛ (FIZ) или ИП (IP)
	Origin GetLoanDetailsParamsOrigin `json:"origin"`
}

// GetLoanDetailsParamsOrigin defines parameters for GetLoanDetails.
type GetLoanDetailsParamsOrigin string

// GetAccountTransactionsParams defines parameters for GetAccountTransactions.
type GetAccountTransactionsParams struct {
	// Account Номер счета
	Account TransactionsAccountParam `form:"account" json:"account"`

	// UserID Идентификатор пользователя (uuid)
	UserID UserIDQueryParam `form:"userID" json:"userID"`

	// StartDate Начальная дата для фильтрации транзакций
	StartDate *TransactionsStartDateParam `form:"startDate,omitempty" json:"startDate,omitempty"`

	// EndDate Конечная дата для фильтрации транзакций
	EndDate *TransactionsEndDateParam `form:"endDate,omitempty" json:"endDate,omitempty"`

	// Direction Тип операции (например, CREDIT или DEBIT)
	Direction *GetAccountTransactionsParamsDirection `form:"direction,omitempty" json:"direction,omitempty"`

	// MinAmount Минимальная сумма транзакции
	MinAmount *TransactionsMinAmountParam `form:"minAmount,omitempty" json:"minAmount,omitempty"`

	// MaxAmount Максимальная сумма транзакции
	MaxAmount *TransactionsMaxAmountParam `form:"maxAmount,omitempty" json:"maxAmount,omitempty"`

	// Limit Лимит количества возвращаемых транзакций
	Limit *TransactionsLimitParam `form:"limit,omitempty" json:"limit,omitempty"`

	// Offset Смещение для пагинации
	Offset *TransactionsOffsetParam `form:"offset,omitempty" json:"offset,omitempty"`
}

// GetAccountTransactionsParamsDirection defines parameters for GetAccountTransactions.
type GetAccountTransactionsParamsDirection string

// GetClientInfoParams defines parameters for GetClientInfo.
type GetClientInfoParams struct {
	// Phone Номер телефона в формате +7XXXXXXXXXX. Нельзя использовать одновременно с iin.
	Phone *PhoneQueryParam `form:"phone,omitempty" json:"phone,omitempty"`

	// Iin ИИН клиента. Нельзя использовать одновременно с phone.
	Iin *IinQueryParam `form:"iin,omitempty" json:"iin,omitempty"`

	// Origin Флаг типа клиента ФЛ (FIZ) или ИП (IP)
	Origin GetClientInfoParamsOrigin `form:"origin" json:"origin"`
}

// GetClientInfoParamsOrigin defines parameters for GetClientInfo.
type GetClientInfoParamsOrigin string

// LogoutUserJSONBody defines parameters for LogoutUser.
type LogoutUserJSONBody struct {
	// Phone Номер телефона в формате +7XXXXXXXXXX
	Phone string `json:"phone"`
}

// LogoutUserJSONRequestBody defines body for LogoutUser for application/json ContentType.
type LogoutUserJSONRequestBody LogoutUserJSONBody

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Получение счета клиента
	// (GET /accounts)
	GetAccountDetails(w http.ResponseWriter, r *http.Request, params GetAccountDetailsParams)
	// Получение детализации депозита
	// (GET /deposits)
	GetDepositDetails(w http.ResponseWriter, r *http.Request, params GetDepositDetailsParams)
	// Проверка на работоспособность
	// (GET /health)
	Health(w http.ResponseWriter, r *http.Request)
	// Получение детальной информации по выданному займу
	// (GET /loans)
	GetLoanDetails(w http.ResponseWriter, r *http.Request, params GetLoanDetailsParams)
	// История операций по счету
	// (GET /transactions)
	GetAccountTransactions(w http.ResponseWriter, r *http.Request, params GetAccountTransactionsParams)
	// Персональные данные клиента
	// (GET /users)
	GetClientInfo(w http.ResponseWriter, r *http.Request, params GetClientInfoParams)
	// Разлогин пользователя
	// (POST /users/logout)
	LogoutUser(w http.ResponseWriter, r *http.Request)
}

// Unimplemented server implementation that returns http.StatusNotImplemented for each endpoint.

type Unimplemented struct{}

// Получение счета клиента
// (GET /accounts)
func (_ Unimplemented) GetAccountDetails(w http.ResponseWriter, r *http.Request, params GetAccountDetailsParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение детализации депозита
// (GET /deposits)
func (_ Unimplemented) GetDepositDetails(w http.ResponseWriter, r *http.Request, params GetDepositDetailsParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Проверка на работоспособность
// (GET /health)
func (_ Unimplemented) Health(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение детальной информации по выданному займу
// (GET /loans)
func (_ Unimplemented) GetLoanDetails(w http.ResponseWriter, r *http.Request, params GetLoanDetailsParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// История операций по счету
// (GET /transactions)
func (_ Unimplemented) GetAccountTransactions(w http.ResponseWriter, r *http.Request, params GetAccountTransactionsParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Персональные данные клиента
// (GET /users)
func (_ Unimplemented) GetClientInfo(w http.ResponseWriter, r *http.Request, params GetClientInfoParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Разлогин пользователя
// (POST /users/logout)
func (_ Unimplemented) LogoutUser(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandlerFunc   func(w http.ResponseWriter, r *http.Request, err error)
}

type MiddlewareFunc func(http.Handler) http.Handler

// GetAccountDetails operation middleware
func (siw *ServerInterfaceWrapper) GetAccountDetails(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, ApiKeyAuthScopes, []string{})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetAccountDetailsParams

	// ------------- Required query parameter "accountID" -------------

	if paramValue := r.URL.Query().Get("accountID"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "accountID"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "accountID", r.URL.Query(), &params.AccountID)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "accountID", Err: err})
		return
	}

	// ------------- Required query parameter "userID" -------------

	if paramValue := r.URL.Query().Get("userID"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "userID"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "userID", r.URL.Query(), &params.UserID)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "userID", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetAccountDetails(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetDepositDetails operation middleware
func (siw *ServerInterfaceWrapper) GetDepositDetails(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, ApiKeyAuthScopes, []string{})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetDepositDetailsParams

	// ------------- Required query parameter "userID" -------------

	if paramValue := r.URL.Query().Get("userID"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "userID"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "userID", r.URL.Query(), &params.UserID)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "userID", Err: err})
		return
	}

	// ------------- Required query parameter "depositID" -------------

	if paramValue := r.URL.Query().Get("depositID"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "depositID"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "depositID", r.URL.Query(), &params.DepositID)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "depositID", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetDepositDetails(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// Health operation middleware
func (siw *ServerInterfaceWrapper) Health(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.Health(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetLoanDetails operation middleware
func (siw *ServerInterfaceWrapper) GetLoanDetails(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, ApiKeyAuthScopes, []string{})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetLoanDetailsParams

	// ------------- Required query parameter "applicationID" -------------

	if paramValue := r.URL.Query().Get("applicationID"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "applicationID"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "applicationID", r.URL.Query(), &params.ApplicationID)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	// ------------- Required query parameter "userID" -------------

	if paramValue := r.URL.Query().Get("userID"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "userID"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "userID", r.URL.Query(), &params.UserID)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "userID", Err: err})
		return
	}

	// ------------- Required query parameter "status" -------------

	if paramValue := r.URL.Query().Get("status"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "status"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "status", r.URL.Query(), &params.Status)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "status", Err: err})
		return
	}

	headers := r.Header

	// ------------- Required header parameter "origin" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("origin")]; found {
		var Origin GetLoanDetailsParamsOrigin
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "origin", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "origin", valueList[0], &Origin, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: true})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "origin", Err: err})
			return
		}

		params.Origin = Origin

	} else {
		err := fmt.Errorf("Header parameter origin is required, but not found")
		siw.ErrorHandlerFunc(w, r, &RequiredHeaderError{ParamName: "origin", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetLoanDetails(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetAccountTransactions operation middleware
func (siw *ServerInterfaceWrapper) GetAccountTransactions(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, ApiKeyAuthScopes, []string{})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetAccountTransactionsParams

	// ------------- Required query parameter "account" -------------

	if paramValue := r.URL.Query().Get("account"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "account"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "account", r.URL.Query(), &params.Account)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "account", Err: err})
		return
	}

	// ------------- Required query parameter "userID" -------------

	if paramValue := r.URL.Query().Get("userID"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "userID"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "userID", r.URL.Query(), &params.UserID)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "userID", Err: err})
		return
	}

	// ------------- Optional query parameter "startDate" -------------

	err = runtime.BindQueryParameter("form", true, false, "startDate", r.URL.Query(), &params.StartDate)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "startDate", Err: err})
		return
	}

	// ------------- Optional query parameter "endDate" -------------

	err = runtime.BindQueryParameter("form", true, false, "endDate", r.URL.Query(), &params.EndDate)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "endDate", Err: err})
		return
	}

	// ------------- Optional query parameter "direction" -------------

	err = runtime.BindQueryParameter("form", true, false, "direction", r.URL.Query(), &params.Direction)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "direction", Err: err})
		return
	}

	// ------------- Optional query parameter "minAmount" -------------

	err = runtime.BindQueryParameter("form", true, false, "minAmount", r.URL.Query(), &params.MinAmount)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "minAmount", Err: err})
		return
	}

	// ------------- Optional query parameter "maxAmount" -------------

	err = runtime.BindQueryParameter("form", true, false, "maxAmount", r.URL.Query(), &params.MaxAmount)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "maxAmount", Err: err})
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", r.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "limit", Err: err})
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", r.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "offset", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetAccountTransactions(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetClientInfo operation middleware
func (siw *ServerInterfaceWrapper) GetClientInfo(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, ApiKeyAuthScopes, []string{})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetClientInfoParams

	// ------------- Optional query parameter "phone" -------------

	err = runtime.BindQueryParameter("form", true, false, "phone", r.URL.Query(), &params.Phone)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "phone", Err: err})
		return
	}

	// ------------- Optional query parameter "iin" -------------

	err = runtime.BindQueryParameter("form", true, false, "iin", r.URL.Query(), &params.Iin)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "iin", Err: err})
		return
	}

	// ------------- Required query parameter "origin" -------------

	if paramValue := r.URL.Query().Get("origin"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "origin"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "origin", r.URL.Query(), &params.Origin)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "origin", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetClientInfo(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LogoutUser operation middleware
func (siw *ServerInterfaceWrapper) LogoutUser(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, ApiKeyAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LogoutUser(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

type UnescapedCookieParamError struct {
	ParamName string
	Err       error
}

func (e *UnescapedCookieParamError) Error() string {
	return fmt.Sprintf("error unescaping cookie parameter '%s'", e.ParamName)
}

func (e *UnescapedCookieParamError) Unwrap() error {
	return e.Err
}

type UnmarshalingParamError struct {
	ParamName string
	Err       error
}

func (e *UnmarshalingParamError) Error() string {
	return fmt.Sprintf("Error unmarshaling parameter %s as JSON: %s", e.ParamName, e.Err.Error())
}

func (e *UnmarshalingParamError) Unwrap() error {
	return e.Err
}

type RequiredParamError struct {
	ParamName string
}

func (e *RequiredParamError) Error() string {
	return fmt.Sprintf("Query argument %s is required, but not found", e.ParamName)
}

type RequiredHeaderError struct {
	ParamName string
	Err       error
}

func (e *RequiredHeaderError) Error() string {
	return fmt.Sprintf("Header parameter %s is required, but not found", e.ParamName)
}

func (e *RequiredHeaderError) Unwrap() error {
	return e.Err
}

type InvalidParamFormatError struct {
	ParamName string
	Err       error
}

func (e *InvalidParamFormatError) Error() string {
	return fmt.Sprintf("Invalid format for parameter %s: %s", e.ParamName, e.Err.Error())
}

func (e *InvalidParamFormatError) Unwrap() error {
	return e.Err
}

type TooManyValuesForParamError struct {
	ParamName string
	Count     int
}

func (e *TooManyValuesForParamError) Error() string {
	return fmt.Sprintf("Expected one value for %s, got %d", e.ParamName, e.Count)
}

// Handler creates http.Handler with routing matching OpenAPI spec.
func Handler(si ServerInterface) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{})
}

type ChiServerOptions struct {
	BaseURL          string
	BaseRouter       chi.Router
	Middlewares      []MiddlewareFunc
	ErrorHandlerFunc func(w http.ResponseWriter, r *http.Request, err error)
}

// HandlerFromMux creates http.Handler with routing matching OpenAPI spec based on the provided mux.
func HandlerFromMux(si ServerInterface, r chi.Router) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseRouter: r,
	})
}

func HandlerFromMuxWithBaseURL(si ServerInterface, r chi.Router, baseURL string) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseURL:    baseURL,
		BaseRouter: r,
	})
}

// HandlerWithOptions creates http.Handler with additional options
func HandlerWithOptions(si ServerInterface, options ChiServerOptions) http.Handler {
	r := options.BaseRouter

	if r == nil {
		r = chi.NewRouter()
	}
	if options.ErrorHandlerFunc == nil {
		options.ErrorHandlerFunc = func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		}
	}
	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandlerFunc:   options.ErrorHandlerFunc,
	}

	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/accounts", wrapper.GetAccountDetails)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/deposits", wrapper.GetDepositDetails)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/health", wrapper.Health)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans", wrapper.GetLoanDetails)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/transactions", wrapper.GetAccountTransactions)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/users", wrapper.GetClientInfo)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/users/logout", wrapper.LogoutUser)
	})

	return r
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+Q9aXMbx5V/ZWo2H6QyAAK8JPIbCUI2Il4LUk4iiesaAE1wImAGmRnQolWsEklfWSmm",
	"nXgrKiWWrI1rv6UKogQL5gH+hZ5/tNWv5+ie6TkAQa54Nx9iCkC/fv363f369QO5prfauoY0y5TnH8ht",
	"xVBayEIG/GuhVtM7mlVeWlesnXXyFfm0jsyaobYtVdfkeRk/wa9xD1/Yh7hvf4r7+BR37UM8sB9K+A3u",
	"4kv7Ie7aX+I+PsFd3MPneIBf4YFkH9hf4J59iLvSlU5HrV+VM7JKAP6hg4w9OSNrSgvJ87Li4iBnZAP9",
	"oaMaqC7PW0YHZWSztoNaCsGppdxfRlrD2pHnp2YzcluxLGQQaHfy2Tklu7314Pp+1vt7OsXfhcl9OSNb",
	"e22ChGkZqtaQ9/cz8kK73VRrCll9eenfCa6jEcY+xif4FPclfIG7Ej61H+Iefo379mECPdj5/0VosoTa",
	"uqlao9ODfHGJB/gNWT/uRiy97k4Tu+wwemVVS0DsCf5Owqf4DPcdDLs5CX+He/jMfozf2McS7tsHgCF8",
	"gAeEme1D+7GEB/g1viAfwAaek/Hk35J9ILV3dA3lIhajqprMou1vz3/cvVsnpP6VkNZrhtpQtQ+QUkdG",
	"1IJ+wGe4i19JhNT4EtiLXZuEf8B/k67cKN++KuE++UbCT/Bz6Up5nXAduq+02k0y7Y3ybRf7HZjQR18H",
	"NGI3Ammdljx/x4FSXpe3otcTuz/vYDmBzRjbatbJlscu5js8IFxiPyTL6eEz3LM/xQOqBE4k8rf9EJ+D",
	"aPSk96791vvfODhSVbUcR5L3rl3LFwqTU9Mzs9ciaANcHM2q710j6mHrQSEfwbAblmJ1zFiavCDbaB/a",
	"R/ZBgm6MwNGEOYZUC5uGoplKjSBhOpYuxaZ5ZiveXr0FLiWtvqRYKAqXp8AtPfsLQhzCCK+BWbrkjzP7",
	"WALdemY/tg/B8n6O+7gv0X/gCzDJp/DhTxELQHR6bse3daOlWEQFKxbKWmoLCbeaXcWy2lIj6fk33Mfn",
	"YOvwKWFh3AeqHtiHhI0lfAK24AQW8EfqNtiP7M+GWUaTzC9ehKpZs9P+AlTNQg1khFawotxfaMVxxd8B",
	"iQNYSxfkkO6IfWAf4XPyoQjhfgTCLXc6OT2rrKhaEop9fDEuBN3JhkBwbXvbRJHYvQCh+iNoqD7uuRxM",
	"dDx+BZh3YzHSAfpbbfJaGxngTG3utSNF7r+J3SGK9ZLoAE+orhAEwcHtU+2QkYqV0lJ50zVDS6XF8maU",
	"I1dXDQQ4yCIzQwHJGRlgiI0Nu44NSzGsOLXxHe7aX3BcMG7FYboojKI6bpnIGN1/DNlBMJTH8Z50B6b8",
	"l3Ch9ykSyLQW9bqKIPha1ht6xyJ0qdCvyIc1XbOQBn8ygcDE701CogesjTb0NjIsBxa14WN0RuKciOHc",
	"A5/4dxw0fV7Xq79HNcunj79J9BOzrWsmXWEFme1Sq23tDUUlwTwBCj1jZZ4IzYn9iLIbMcJAKeK2wK++",
	"JG4WLIrOQGPo9XLJMHRDQP1nEBa/JIzsymCxskIoyG1eTa+jCD/gtYQHHpC+QNtl5BYyTaWBhFqtR8wX",
	"McJCIMwmBeiUcTMDYU7zw/VhBJjzqnzGmstXZ69Vr89lp2dn5rLTk4Vqdm56bi5bK8zN1LZrM/X81HQY",
	"34xc6xgG0mp7Ahz+DCrwK9B7EbPevL0pAqpWFS0MsLy4sBoNqFAo5Av5fH5ycnJqaor8nwiy3kYa6M0w",
	"ut+6KnpgHxI/2H4E1DuOmnIyPzmZzc9kC3k5w6te0cSO2xzvkEfMtFCz1F2UKNJs+gYomPGddW/dzI5t",
	"RTPbgmE4WpBnuWpTr90jc4fX8Rxs8xuw06eSJ7cD/JNroolNBL+zDw41fPMSn+EBkQX7IdiTU9zPSBQG",
	"RKKv7Uc0mQWig185FhJ8WPjvsRO4DMA56AHbn9E4zD6yv/L8L/tIyrL/wF3yc0Cn61O2qutNpGgh0nrr",
	"jqFZWdvWhZJ4wSh3R7NdMlk5+yikhpSWK/FKs7m2Lc/feSD/ykDb8rz8bxN+FnHC0X0TK7qG9uT9rZBK",
	"/YaIIKEYz1z7GbmqaPcW1ZoA4W/wE/yU7Awh9ClLHZ+b6WgtYvR3KUavgmMgcp2oh0fDarrZxF2NBbjd",
	"aTYjAP6An+BngSxGepWDX1CaMe7yAL+CJKvLfleIWroaBXJZMa0ltaFaItH/K+4CUxwwAtAFzn1EZONC",
	"EAlLYP/69oEjBsfCiYU7I0q9JeqUwBoyLmt6Gobm1rwNYDbX5xKf24TiU68byDQFygY11ERlfeH62oQ2",
	"RAG8ItRh/eq3UNrVjtqsi9Xdt2RzONAFoXlULZFp/AsohAF+zUHAX1N9RBYlBKYbbaENeQrgLokNSYES",
	"2UBjT2yKHP0aNK4iOHWV/F2zBIC+x138E3Fuh1redlOxhIsjYcZDsBsPA5gJF2ighqqLJOAZmBuwQfbj",
	"Cfw95ZYwnv8FiQYSnoFkQhQmMOgGQiJ8/wE5ls8DmOKviQoTyavI5ys2VaRZjmGpOM73OD1A+5soRah4",
	"pj/O4vB+Ahm2q6hNpdpEi0pT0WpimR0A6Y/wpaviXjL26UqVjsy2m4pmdlrZtmJYqtJs7i2hqkXTIVc5",
	"qdU71SYjt1qnVaWOeDUSB9YgXqH4ULrgU4Yuw02zqli7SjNhMkaF4xOqtj7PSaDwqY/ck67cvL2ZcuZa",
	"UzdRkmqkaQTej/X2PVH3DeHYe0DdjAp16yu3FuWMXLpVkTPyrQ3ilRZXfydIr2Tkul7rtJBmrdIFxgXP",
	"+DU91MQnoPOGseWizLLkmLGxBQrpCUzYfKMjyr48p1wD5wqPcM93Wx+l447UsUZo4xaKm+UPS3JGXlxe",
	"K94swZ4tr23AHwuVSmljE/5cKW9sLtwsrcKnxQ/KH8KnldLaemm1tCTcYvpBRLpPiEvxVoVwzuKtG4vw",
	"n+KanJE3b8kZeXltgUy9tvlBqbIhztdFxUXwSyYu8ricC5EcpnBUochpKTrjirEJA0+6YedGlQ7nyJdR",
	"rIGkRTpJDZz8XoSKA9JE5s658IIXoIRYt2c/hDOFi1ASlEmFC06hvalJEJ/P51Nxej1EmgA+X+NTsDgO",
	"HmGrQ5Qxq4SHolJhBlDNzaRANsCUPCFDK0kI0x2WWEKWojYFrrNqVlC7iTTV3CE2OckY44FEz3IvIfHu",
	"Z9z6uMeul+Zsg7FyJjjdkrIn1D70AMp+TP4rwclpj2wHHEuAYucxSkBmspDhjiCmJoVJOdX8jWrt1A3l",
	"Y6GNFtLBPsAX9jGcgXNTbitNU0iAlqotCsGHDoVCHBhk/VR831b29I61gqwdvS6k9CVZFh7gl24W9QwU",
	"/yNOxosLlaXkCDDASuLdDtDZo0gA1RheLmsWEied0PY2ggxYRWyP/2l/an8KCdZDKH3ytA2JNU7clO/A",
	"/sw9rqdJKBD9SxKG2Z+78bD9GR9eTOeEWUQNKQTVdWUvwkV4ATmo1/aR/ZX9x8AJELcfQQwG+CQYrxay",
	"+elsvpDKpVD2UD1SNz/DLx1kWE3so/OFW75AOFRAMBaxqam0rGpEOVEB+PbjNBsykyskcqxBqcOzDU+c",
	"0BbGMOa6odc7NVH+vWEgRNzWCCeAc1pFRkXgyzIVNMu5fD57TZiWSJMYCTqnsZNN5icJj2ULM6lCg1in",
	"55JmNuwjEMjQovDf8QsRULcWIm5JYEAuwK24cBfVwz85xQxpVjmTfpVadIbyDZuZjFsvOGCnVNlHnQqg",
	"pCqdGF+Jc3CIzIgmsZAhrgR6CGFvED6I4Tnkxo/tz4MiWJhMYXUDAqnRdCCwDcu9GaYABpDMBGTKJVCM",
	"dG7s6IY1Lp94GBd4MVWOYVyO7s8jHVPZ/LWUpkatv23ZqT/ztamZjyZnpybnpmfGZdXirapDeeFkVNvH",
	"xnX2l7jrnFxdeJNFaIDbi7mpQj6XF2cqx2sbk325Oh/ysosVhCC8xTR4kRXJ5PvI4iKTCjLbgry+Lzlx",
	"KcZA2AuS54U7KQa6wRFhVsa3TDHSc0V9fkg50vUVQtUWzud+apJByl9YBE2XdUUzneVEJ4ObOs16xaFJ",
	"IHl0CeAI40UYfICUprUj8H+a1kZNN9CiodYbKDHd5GQG4OxKCowVxVRKqzkSZG+YEKhmqduG0qkPCdQb",
	"JgTaHgnRdgyeDpvcahP9awwHODBWCF61DPX+KFhzI4WgTcUcCbA/TgjWGg2qFQe0phh1tx7ZHA4wP1QI",
	"XG82narD4SD74yLA7qrGKLTgRgpBG60hIYLPFgbkWJIhSeqNEoJUgSRK1MFlJFB/nBCsc/IwLKreMBHQ",
	"bbWJ3u8oxpBKxh8mBKobSG1oUAskPlSOAx0YLJrg96qhjMJWzDgh2I5a2zNHtROh0aIp7ilmWx0FOjtQ",
	"CBjt1Zq6cm/d0O8PSXF+qBB4oz4Szt4wEdCmuos0p5oiPUxvlBAk8T6GhAdDRMA03VK3nSLRIYHyQ0XA",
	"das9HEgyQASoreyNoBS8UXEgN1poNKgbLfGOt+9VR2Ejf5gQqKHXkGmqWmMk2MHRoin+oHzS1k1rFPj8",
	"UBFwA20jwxBm5GPgeqNEIIkSEpcCxUB0BwkBIl0bZfXMOCHY1kiOkj9MCLQ9ksfhDxMBtRTz3oqiKY1h",
	"HVx2oBCwfg9p6idDIuuNEoHsmM497fTw6JDE0lL3Z1QXsY5FUOEFTQprCVg/l91KVxMHfD9GVQUdWc5h",
	"YrUEG1ixBoPbDIG95vwD1sfxhYNjaZZpQmFiUPQZn5ELIJjgSqCNOBebV7CMDmCDPsfT9VkkFGTxvkQg",
	"TAr6X6Iwe9mJ3wNBtn+pYqmTWPJzCVmjrv0F7nO3O98i0cdfgh/16j2HgFKY3J6uXp/LFianprMzs9eu",
	"Z+euX5vNVqu1+jaanpmdu66IUCHM7OcBk/Iczi/9HM5mXAlKbBKvpmtmp8VqGyZh2DHauikC/D/0CjG9",
	"7HVBE8JcnS8t32WS+c/wS1oYah/CSTGU/kBVu3tfc+SrBezNXveML2p/imsr68slWuqTUFoT6JDA7BBP",
	"9iiGj0zrvvBzuW+gmvQcFi/O8g9VhZOU399Vmh2UHiMf3kxUVjlANA5rd74oAjElHmkvFrBbLbpc4N8n",
	"SlHhCbca0udeGZTXvHXsKObaLjLqlKyCoADdt9apFk6Cv8r8FLxqte7eu4gbtu7+bkRtIC2vLaxKWQk/",
	"ZXqHVEo3yqsLq8US+eJ7uM8XIee0isQp/3Kq17zRwpovAynODbq4ZVXor5xbcPWOsNLnL1AKD6pZcH9B",
	"zsiqhVqJG7vhTFC2UMuv55MVw1D2QhzObDhPb2bH+G3PMJf5XVZjVpUgHQ6riQwj1GzRop54NRy8CKhZ",
	"hlIbV0XqqMd3QcDJx3LOocLCuG4QMVqPXhHvwTUct1/ECZwTP6Z2Coqo46gcsuFjQy5uWnp5PIzrVcd4",
	"OjepU14yGWVXEk7gXeSFfJlwth5g1IAJZu+Jh87aA7wikjFK+RhbmGiS4/aVK6JNY3kTTSuFkOGxEC1s",
	"lTc5aUzrgN5W7OMfifUHDXouuZVcuId/BC3/ji7yMWIYROIVrZX00YD7ffUElk4BJZGxgyWt9EdKNDsx",
	"XoDg9uiACqZbh0YrJRmUQtSti+tMn4banLDVppfcTFFXubdVDS2h6pi37pJaXfCJ1Ij6aeJ1HEDN0Bta",
	"uggu3WWYPsQA2UcchcDbi98kmFe0N+uMK5VSHJhKRiiESvY9DdRSVE3VGmMzAc/cOk/7SxLN2AeR5Y20",
	"2jKMY6h8MICjmFigOqKdc9bxCIlVsBzCRD+/RXyDu1KBqfO6mkZpDKkffk535ArtgEjLxe1juFh9AE0k",
	"aLhGs3J9fO6X/w7sQ3rVnCoLGj8FtQPQRff1VhzWrnqLUIzMRqcyvhUvBhBdtScYO1ff7UNHX3TH0dDC",
	"3z2nn8Umum+9RU+LjGypljAy+Sv0HhpAIdUJrUVM6o7Buz4QP7NYunOJyMlFMCOESUHbM2ZRSRlgBzSP",
	"f7L0LkSMW4R0JbV28Tn9qvSeBP2QXEntSld41r96NX0Oi8dnfdmJyWm7KNcOlVaXyKfP2S2U1j4sVZZu",
	"QaQecjWY2JyAlDNyaXVJzsjOmOQLZvwOZNwt9i6ZxZV3MY2lIu/3pghCa7FXepOTbBG9ybzk1txcbm42",
	"3dVUxUINXVim8dTZvFfgS1Avgu/z9TYXUcUrEFxbN/yqnDgBiWxdxvNrWhDM37RNI2hG/8Pk/ECQUmna",
	"fXFTCPP3/4AE1Sl/P6kffXk8JZmZad1MW0pKURIHRIxfRSYgGUxbCqa6lev65spikOJhTBOkNCJv+Gfo",
	"N3MInuUFkPQnp4mq5GxcD2zca+6OK1xhlTPy+sLvVkqrmx8t/u6jhWJx7dbqJvPhytpiebnEfLBZqqyU",
	"VxeWhZnDaLZN22yPvYOb1BqPny+mOBVaRL7z7pSJLQkzbiPDt+uQmGKeuMTSeBsEptQDuqU0i7FF9D2y",
	"6lNB9Dw6tRnhMiMuKvbhouJpZPvDNNlp1oomJac5lDi6ZLxOpg6TJKgCgU5PqBCIsLKOtJVXy5vlheXy",
	"bTh5K69+tF5Ze79S2tiQM9yZXKX061JxM+KiPe1imNAxhF4STkNZt/tciKoZuaoa1k6C2QLP8EdqTRw/",
	"OrnrDoG73lSSrwi4rYNoox31E6SZO2o7yrkHPMBjpWydpscNU8iailzcrSQBzbZVw7QibpY9wedEEwQb",
	"NDHnw9+AbHahN3MI1wbS6kIvkXjCZ2laYA3TNortHJgv5AtT+fz0zEywJWVUc/WMrLaH7QD2BD/nqfEE",
	"P5fw14DXSzANJ8KJTLOjao2FjrWjG+LC2Wf2QxIq4AuaLnIKKIbqXdRUIrf1B+jyBS3wErY3cS1eKWYq",
	"XoSCEgEP6vdQ/UOl2UERO/AMP8X/xN/yl4toy9Hrc8JLU05P90gHI3rN5XXxjS/L0LW9lrA/3TP7kE/t",
	"xlGUplKd+LRvfyGcbYSOrdFzjt6hFTpXMd3QUmo+AzVU06J+XhEZVkLnG/KfgF/fHWKr2dmSdb+4Hdt4",
	"mmXG7EFUw8yM24j4rcOgmMlnZvLo+nQ+n0WTc9XsdKE+nVWuFWaz09OzszMz09OpakS8hsnukwDOGxZe",
	"P08qcSIH5UOlqdZhf1I1wz2hCVDQetTNpFaS0MD+DHoTO9zvPzIzsA8EuUYUMZ0w2cjwW07IaNsqatap",
	"z1Kvq1CS2Fzn5hNY65BT+5+0L4RE+GXAdk6kp3cuTpB7FFCC5pN74Ih6GD+glpIeNkqtjmlJVSQpUrDZ",
	"kd/rmN9bSqct0c9MVOsQI7VBxJyucqGt3kR7xHyFSbuwXpZuoj0vVGFbqBAlJRUrK9LCejnqZZHfZhfW",
	"y9mbiLmgoMB0ZBGLSDGQsanfQ5o7exU+uuGK769/s+n2H4eKHvjWB7VjWW3a6FkVnisR7F3MX9p/wqf2",
	"n/AFoT5B+652V4PwoAcHTq70uZ2hgw2z75gfK40GMrau5HITzt/eEyV3DFTXa/AV/HU1R6DfKe4oWgM1",
	"9QZ8s6Oalm7sTdTcTz9qKaqW27Fazavw8wpqIsVEUtKwbaRYHQO5I53zdnNtewMZu2rNpYw5PzHxidJS",
	"tAn4OqtvZ03nB17GXL5NfiAVjVbjY2cfd5FhUvLV0W42P1eYzeavT03NoOk5OSPfzzb1BpC6YzTleZkQ",
	"w1IstTZBPs+1HU2jt5GmtFV5Xp7K5aGjRluxdoDdJtgIoYGizijP7CO3BS+tUvSa2wUVo+7mIMp1eV5+",
	"H7ldDZe8Kh/2IayIpLX/kwnBQ1n7mcRRoZ73+1uBruaT+fxQ/cxjCxuE7RtFPc+9/rI82fYz8vQY8fEa",
	"owtQuNvJ56dqVQP+ixjb4Ko/f6edPIVvG5i+WL27Gg/J+Rf9RyGXlwJ2iX4/4fxAykpjMkt3NU6TAkux",
	"OvTOFtl6s9NqwV3CYdnZUhqETYlV875w2oSyA+UtgsQEGz+KhenPwQQXbTbsnB6DK3Jkf0U0WfAM/iuJ",
	"1jWwfRXcU3hazwEOREj+Atfjh5W/sCQlS5/o0bJ3KoDCRgQi+fsH+56AQ74AN/i7Af3NGXbkO1r8YmQ2",
	"yDP/OpIbg8fUTG7yWq4g8dy7qls39I5WD6P0LbtGCV+Q2S6gbBtc+jFoibR8wegMDin7kaMkdrz+ChH2",
	"1glfe/ZDh8IQxX0mwQOPr+0j6qJKcBJ7AMQ9tx/RAk3Ie72EEoEBfcSMtoXzO4uEVITT7uEdSqczg0ge",
	"o4vafVb5kqt/clv4kpjQPpIg1XLqsJR/HWuAT8IbHvJww7seJH1qmnp7/sLfFHqnw9l1L5eT1snirYL7",
	"sEKAXI4G8BJZbKGYc2sByrJCVoG9azC0SxbxTudIflnymNArdynGhJ9yfNf2R9i0RcTyf/V1YOjJm8CD",
	"NxlGlRKO4GvZcJfoBPewkaa6H/2CzBK7lP9L7uRYBdf3P5kiRteSBE/bxKrlCeijiBKMn2JCtk3+5Gw4",
	"JRH5BOQ70hIxL7YNOZp7JHLIsYI374aEEHh3cNjR/MOKQ45mHpYcdt3MY4TvVNMKyw+EnkUs14ee5Pnl",
	"ROChM2X72P7sl+LTT0lOVuyGojZRfVMv6touMqw0SMFrkLRg4YL6aGADw/gNrcOH5BVGKz9nKhT7jlb2",
	"egdEqmP/UUX7iK1cLlZWuJeH+vQ4K/QoMxgO77kg4hSHDqy8x5qf4O9EKp6mrJybecNp9uBDzClUBf9M",
	"eGr/7edKH3A1FCJl8m3QC2P2ozeK7tA1lKJm19ciWyPoETH3/CI0xWQuL5FNiY73nwufJX0sivzjNVJB",
	"Kup1VDRajmIa07Tw2kw8nSJeVr06ggtKtMABFX3vPLMXCB4iM5u0QNopQJO3MvL9rK6Y2bpeMyMewjrD",
	"P8Ia8Uv7EXQrtY/cGwkkhjlk2/4PiM9KftvHb6C4DszXOag9CLbn6WP+rspSVS0nUfEnBB/gl/YxpFsO",
	"3Zf+cpJ0Fwod7mc9jZHd9biWL4n3BU3wGipRRFy/VFWTt/ZDH7uHr44UUgUPJywd2opYN4dS9I6Gp9dU",
	"yNb5r8tBEuMAbq5EcwhroQ6dl8XOKYHpI6e8tvdfu5XZt3D3orQi91zuRPit3H2xJhaDcn434T8i+zNo",
	"y0z8D4Mn5sNpV3rJzGnY243cpP9/enZIrfU9KIwzuLYBCiKCkKyq+hqfuI6am4K1j2W6fSYydl0Pht/K",
	"JbQr0W/ljHNQ6p7I1oxW4+NsHe3m4Gw2Z6B6S6kbelW3cjW9NaG01YndAnAUD3PDUhooHqpJfpIAd8tb",
	"3AP3kF6QQSTTu9/y2pr9Rkwc9hd8DoH7Jv50i0OAc3nZb4Lp7v2t/f8NAAD//+TNWPoFiQAA",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
