// Package openapi provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.5.0 DO NOT EDIT.
package openapi

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"
	"time"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/go-chi/chi/v5"
)

const (
	BearerTokenAuthScopes = "BearerTokenAuth.Scopes"
)

// APIError Общий формат ошибки API. Возвращает цифровой код
type APIError struct {
	Error string `json:"error"`

	// Fields Объект с описанием деталей ошибок
	Fields *map[string]string `json:"fields,omitempty"`
}

// ClientCreditReportsReq defines model for ClientCreditReportsReq.
type ClientCreditReportsReq struct {
	// Iin ИИН клиента
	Iin string `json:"iin"`

	// MaxDateReport Документ формируется на основе данных за период
	MaxDateReport *time.Time `json:"maxDateReport,omitempty"`

	// ReportType Тип отчета
	ReportType *int `json:"reportType,omitempty"`
}

// ClientCreditReportsResp defines model for ClientCreditReportsResp.
type ClientCreditReportsResp struct {
	// FileLink Ссылка S3
	FileLink string `json:"fileLink"`
}

// ClientFileIncomeReq defines model for ClientFileIncomeReq.
type ClientFileIncomeReq struct {
	// Iin ИИН клиента
	Iin string `json:"iin"`

	// MaxDateReport Документ формируется на основе данных за период
	MaxDateReport *time.Time `json:"maxDateReport,omitempty"`
}

// Document defines model for Document.
type Document struct {
	// ID Идентификатор документа (UUID)
	ID string `json:"ID"`

	// FileLink Ссылка S3
	FileLink string `json:"fileLink"`

	// Type Тип документа
	Type string `json:"type"`
}

// GetDocumentResp defines model for GetDocumentResp.
type GetDocumentResp struct {
	Document Document `json:"document"`
}

// Health defines model for Health.
type Health struct {
	// AltScoreBridge Статус сервиса altScoreBridge
	AltScoreBridge bool `json:"altScoreBridge"`

	// AmlBridge Статус сервиса amlBridge
	AmlBridge bool `json:"amlBridge"`

	// Antifraud Статус сервиса antifraud
	Antifraud bool `json:"antifraud"`

	// ApBridge Статус сервиса apBridge
	ApBridge bool `json:"apBridge"`

	// BalanceUpdater Статус сервиса balanceUpdater
	BalanceUpdater bool `json:"balanceUpdater"`

	// BitrixBridge Статус сервиса bitrixBridge
	BitrixBridge bool `json:"bitrixBridge"`

	// BsasBridge Статус сервиса bsasBridge
	BsasBridge bool `json:"bsasBridge"`

	// BtsBridge Статус сервиса btsBridge
	BtsBridge bool `json:"btsBridge"`

	// CardsAccounts Статус сервиса cardsAccounts
	CardsAccounts bool `json:"cardsAccounts"`

	// Collection Статус сервиса collection
	Collection bool `json:"collection"`

	// ColvirBridge Статус сервиса colvirBridge
	ColvirBridge bool `json:"colvirBridge"`

	// Crm Статус сервиса crm
	Crm bool `json:"crm"`

	// Deposits Статус сервиса deposits
	Deposits bool `json:"deposits"`

	// Dictionary Статус сервиса dictionary
	Dictionary bool `json:"dictionary"`

	// Documents Статус сервиса documents
	Documents bool `json:"documents"`

	// FileGuard Статус сервиса fileGuard
	FileGuard bool `json:"fileGuard"`

	// ForeignActivity Статус сервиса foreignActivity
	ForeignActivity bool `json:"foreignActivity"`

	// JiraBridge Статус сервиса jiraBridge
	JiraBridge bool `json:"jiraBridge"`

	// JuicyscoreBridge Статус сервиса juicyscoreBridge
	JuicyscoreBridge bool `json:"juicyscoreBridge"`

	// KaspiBridge Статус сервиса kaspiBridge
	KaspiBridge bool `json:"kaspiBridge"`

	// KeycloakProxy Статус сервиса keycloakProxy
	KeycloakProxy bool `json:"keycloakProxy"`

	// KgdBridge Статус сервиса kgdBridge
	KgdBridge bool `json:"kgdBridge"`

	// Liveness Статус сервиса liveness
	Liveness bool `json:"liveness"`

	// Loans Статус сервиса loans
	Loans bool `json:"loans"`

	// Notifications Статус сервиса notifications
	Notifications bool `json:"notifications"`

	// Otp Статус сервиса otp
	Otp bool `json:"otp"`

	// Payments Статус сервиса payments
	Payments bool `json:"payments"`

	// PaymentsSme Статус сервиса paymentsSme
	PaymentsSme bool `json:"paymentsSme"`

	// PkbBridge Статус сервиса pkbBridge
	PkbBridge bool `json:"pkbBridge"`

	// ProcessingBridge Статус сервиса processingBridge
	ProcessingBridge bool `json:"processingBridge"`

	// QazpostBridge Статус сервиса qazpostBridge
	QazpostBridge bool `json:"qazpostBridge"`

	// Referral Статус сервиса referral
	Referral bool `json:"referral"`

	// Scoring Статус сервиса scoring
	Scoring bool `json:"scoring"`

	// SeonBridge Статус сервиса seonBridge
	SeonBridge bool `json:"seonBridge"`

	// SmsBridge Статус сервиса smsBridge
	SmsBridge bool `json:"smsBridge"`

	// SprBridge Статус сервиса sprBridge
	SprBridge bool `json:"sprBridge"`

	// TaskManager Статус сервиса taskManager
	TaskManager bool `json:"taskManager"`

	// Tokenize Статус сервиса tokenize
	Tokenize bool `json:"tokenize"`

	// Users Статус сервиса users
	Users bool `json:"users"`
}

// GetClientCreditReportsJSONRequestBody defines body for GetClientCreditReports for application/json ContentType.
type GetClientCreditReportsJSONRequestBody = ClientCreditReportsReq

// GetClientFileIncomeJSONRequestBody defines body for GetClientFileIncome for application/json ContentType.
type GetClientFileIncomeJSONRequestBody = ClientFileIncomeReq

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Получение документа - ПКБ Кредитный отчет
	// (POST /collection/client-credit-reports)
	GetClientCreditReports(w http.ResponseWriter, r *http.Request)
	// Получение документа - ПКБ Доходы
	// (POST /collection/client-file-income)
	GetClientFileIncome(w http.ResponseWriter, r *http.Request)
	// Проверка на работоспособность
	// (GET /health)
	Health(w http.ResponseWriter, r *http.Request)
}

// Unimplemented server implementation that returns http.StatusNotImplemented for each endpoint.

type Unimplemented struct{}

// Получение документа - ПКБ Кредитный отчет
// (POST /collection/client-credit-reports)
func (_ Unimplemented) GetClientCreditReports(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение документа - ПКБ Доходы
// (POST /collection/client-file-income)
func (_ Unimplemented) GetClientFileIncome(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Проверка на работоспособность
// (GET /health)
func (_ Unimplemented) Health(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandlerFunc   func(w http.ResponseWriter, r *http.Request, err error)
}

type MiddlewareFunc func(http.Handler) http.Handler

// GetClientCreditReports operation middleware
func (siw *ServerInterfaceWrapper) GetClientCreditReports(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active", "offline_access", "default-roles-bank", "uma_authorization"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetClientCreditReports(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetClientFileIncome operation middleware
func (siw *ServerInterfaceWrapper) GetClientFileIncome(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active", "offline_access", "default-roles-bank", "uma_authorization"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetClientFileIncome(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// Health operation middleware
func (siw *ServerInterfaceWrapper) Health(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.Health(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

type UnescapedCookieParamError struct {
	ParamName string
	Err       error
}

func (e *UnescapedCookieParamError) Error() string {
	return fmt.Sprintf("error unescaping cookie parameter '%s'", e.ParamName)
}

func (e *UnescapedCookieParamError) Unwrap() error {
	return e.Err
}

type UnmarshalingParamError struct {
	ParamName string
	Err       error
}

func (e *UnmarshalingParamError) Error() string {
	return fmt.Sprintf("Error unmarshaling parameter %s as JSON: %s", e.ParamName, e.Err.Error())
}

func (e *UnmarshalingParamError) Unwrap() error {
	return e.Err
}

type RequiredParamError struct {
	ParamName string
}

func (e *RequiredParamError) Error() string {
	return fmt.Sprintf("Query argument %s is required, but not found", e.ParamName)
}

type RequiredHeaderError struct {
	ParamName string
	Err       error
}

func (e *RequiredHeaderError) Error() string {
	return fmt.Sprintf("Header parameter %s is required, but not found", e.ParamName)
}

func (e *RequiredHeaderError) Unwrap() error {
	return e.Err
}

type InvalidParamFormatError struct {
	ParamName string
	Err       error
}

func (e *InvalidParamFormatError) Error() string {
	return fmt.Sprintf("Invalid format for parameter %s: %s", e.ParamName, e.Err.Error())
}

func (e *InvalidParamFormatError) Unwrap() error {
	return e.Err
}

type TooManyValuesForParamError struct {
	ParamName string
	Count     int
}

func (e *TooManyValuesForParamError) Error() string {
	return fmt.Sprintf("Expected one value for %s, got %d", e.ParamName, e.Count)
}

// Handler creates http.Handler with routing matching OpenAPI spec.
func Handler(si ServerInterface) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{})
}

type ChiServerOptions struct {
	BaseURL          string
	BaseRouter       chi.Router
	Middlewares      []MiddlewareFunc
	ErrorHandlerFunc func(w http.ResponseWriter, r *http.Request, err error)
}

// HandlerFromMux creates http.Handler with routing matching OpenAPI spec based on the provided mux.
func HandlerFromMux(si ServerInterface, r chi.Router) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseRouter: r,
	})
}

func HandlerFromMuxWithBaseURL(si ServerInterface, r chi.Router, baseURL string) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseURL:    baseURL,
		BaseRouter: r,
	})
}

// HandlerWithOptions creates http.Handler with additional options
func HandlerWithOptions(si ServerInterface, options ChiServerOptions) http.Handler {
	r := options.BaseRouter

	if r == nil {
		r = chi.NewRouter()
	}
	if options.ErrorHandlerFunc == nil {
		options.ErrorHandlerFunc = func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		}
	}
	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandlerFunc:   options.ErrorHandlerFunc,
	}

	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/collection/client-credit-reports", wrapper.GetClientCreditReports)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/collection/client-file-income", wrapper.GetClientFileIncome)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/health", wrapper.Health)
	})

	return r
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xZb28btxn/Kge2LxJMJ8l2mjR658Rr5qHDjDjBgspeQN9RMuO744VHuXECA/6TNO2c",
	"NNjQF0GHNciAvpc9q1FtS/4KD7/RQN7pdHeiHUtLgL0oYBjCkb8fH/748OHDh0+Qw/yQBSQQEao9QZw8",
	"bJFI3GAuJfrDTY+SQNzkxKXiNgkZF9Ft8lC1OCwQJBDqJw5DjzpYUBZUHkQsUN8iZ5X4WP36lJMGqqFP",
	"KsOhKnFrVDmDfnNzs6RtoZy4qCZ4i2yWElu+oB6ZDxzmkw9vSJ7bZMVmKSHU6swuzP+ec8bVb5dEDqeh",
	"GhvVEPwE+/I76MKvlnwKfbkFJ9CWOxb05bfQhX04gq41uzBftuAf0Id3cCC3oC2/gzZ05I4lv4GufCq3",
	"oA8H0IdfLTiCPhyiEiKPsB96RI1I4qHRvXIVbZZQyFlIuEgWjgzsEhshQTUUCU6DpurXoMRzdR/sulTZ",
	"i72FHHYEYpjb36ADR8rSbQv6cApduQ1t6EEXOnBiwaGaBrThGDrK+sGs+3CESgN+tvKAOALlVa4nli+P",
	"dCud44wZVShVNl67OjU1XZ2pVq9cuY5KyMeP5rAgMQjV0HR1+jO7es2embpTrdb031ejGmqqkZV9Da/h",
	"X2pBjvVke2qe2YVBU9MzVz67eu3z69WpaVRCIRaCcAX9a71qX1/+3adDCYYSFywcGfUHpZ3chZN4yKFX",
	"deWW3FVqy235yoIetJXc29DTvtNRS6HWpSf35DML3qnmU+jILegqj1oKUAk1GPexksXFgtiC+sRkIdem",
	"3dGfR8z7N3ThVA28I5/HS5+V5GpKRwNBmoSPLLqS+sJLHoXKgvxaNahHvqTBmsG2t3Jb7sExHEHbWpwZ",
	"nVrBlJTpbHtGYtBv/vex/e+C/jLHnJafnAZ5MefnjFoeJhJ25VPoKheRO2piyuzchKFtXbp7d37uskm8",
	"/8H5Bh/O2lEjZrxXmvm5QZfS+b58i4iBXOY95WbEPO/wTEUvmpISmIb/A8GeWB0dFXti0WGc3ODUbRKj",
	"pDtqmeSu3Lbktvalg/j8sQrYdNQVxjyCAzUs9r2JmFOYkTQQtMFxyx2TNIUZScOJDA3PsXMFezhwyN1Q",
	"7TQ+HnEBa6SngtNHk1idQxqpIxxNRDzEGWnFZKziPFIHczeadRzWStLqixPnoUZy5nnEianGYh7izqBd",
	"p3wSLXJIIzX3x2TkvpHIJSGL6LiSpigjJdWSYL4xJukQZ6RNAt+4pqYwE6kK5rdamI8ZZIYwIynjhDaD",
	"WUfQdSrGVKEINg3wgHI8iVtlcEbaFnU2oknPiRG0aYg1HIV0EvYs0EhMNhyP4bUFzh6NqXgeaiRvuhPZ",
	"nMJMpB5dJwGJxnTnFGWkZDgYl09DTGQBE7SR3PzHJM1DTeRMhONRKoCJKMQbEwSFFHUe5aJPJmNd9M0r",
	"Hq6tTOJGQ5iRlDOHRBENmhNxF9GmIR7ixyGLxCT8eaiJnJMG4Rx74/GmKBOlCkIqix+LcQAyEhIWTDL7",
	"DM5I60+UKA1hRtJwooxjCDORChyt/QkHuDlugpsFGonZGgno4zGNTVEmylZE+JjhIIaMkhWuXoNucSzK",
	"JhbFgFc8UrInQTbPzS7lIBIXcr9MqComsrmEKRslsher7IGRWwzDeZ3LD7I5znBz5Fw66zQj18Ti1s/k",
	"jLkLROZyZYhGuRQ7H2AzMSB76Usy3aGLjFyy8rlE4ZpUzL+WTbXViDgtTsXGorqtx1fsGwRzwu+oYWdb",
	"8R18RX/6YlCJ+eNf7qCk1K29TLcOvW5ViDCuj9OgwUYdeHZh3oJDOJavLNiXL+FIvoQeHELbuplqtBQs",
	"BfBP+b0uC+sa8Q4cxNXvgcer/20LTqAPvwxa+rq2vAW/6A3yIqmMyz1rqnzPugT/Uc0W/GzZZxXRczX4",
	"yxZ0rXvlaoq8ZwLOzxV24eXyUlCHt3CqDFSd5HNd+z7KlbqXL5XLFV3Oji7r2f4sX8bWW9hxSChsDwfN",
	"Fm4SS9OeQh+O5Qt4ly2oJSrGNbMOHMGx/F4+h44aT76y5Ct4J/d0eWmoILQLGpaXAnijdVKT2ocedKCj",
	"+pyUrfpsbMuXiS3Llz5Jazx2iDn2iSA8suNug15KgaVASQCdWK+kfPaNNgsOcu8e0LHq0de42SRci5L8",
	"VurDMXStOicuc3ST/hWz12+u4qBJPNbULas0EoxvVJzB1/s+pkF5Vfielrd+m3gER8R6H6xBsGhxMkAq",
	"tybcj/7cWCR8nToDB49qlcpj7OOgoptt1rCjpEMJCSp0sfUr1SHj1dbswjwqoXXCo3gruGTdrl6fumpX",
	"P5+Z+YzocvAj22NNvW1a3EM1pBQRWFCnor6Xw6Sqx0IS4JCiGpopV8tTcS13Ve/gyjDWVBxdk7YdXSS3",
	"4zK97qRCmuFweaPdbHfgRHGNtljstC14Az/C3y34UfvdIXTlDvTkXrJV4zK/Fo+FhOvTZN5FNXSLCEPR",
	"HpUyb4sbZxUUc8+PZz4OKqooZEEUB7PpavXjvkdGSah7X0VcHdVptVfHm9Os0pY6zfV2+RZ60F/SCcCV",
	"D2h8+hppsvanNOa1l4LcuYBqdcOJUFd5wn3qEnVaUaIOK6yOGeX7rNHwaEDuqyCmD2uXNHDLEzZnHons",
	"FRysoRJq+fg+bolVxuljPR20vLlcQlHL93W55YM6os4YmpGyu7gwcg8tq/katoxKG2yqH3I+3H75Afry",
	"WXwmnbc9hm9Ik++Nwnv1R9wYxbeC3zbE/8GGyHjaRfx/NX1waRKjnycZj8oYdD5xoJOIZ3ECdih3k9d8",
	"ua2yFXWow4nci5/8dB60rzOQfpzJ6FRNZRp91Ve+GNkJyfvPR3TaZATT6r+GXiY/SXKWU+jrF0qd1qk8",
	"Tq1CD/pwInctPePkTiZ3lSZdONL9splhHw4u4ksjC1+U/sKapqv+drgoOjp21KprS/i6vmTWiws+R9at",
	"uFU5p05EBmnPMFQ2v7Zdsl7WeVCZE9fHLmcrTJQd5ldwSCvrU2izVKReFCqpvQh5pHq+h345neYTFGB9",
	"ITFMV1kxaB3dApvLm/8NAAD//yQKJAftJAAA",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
