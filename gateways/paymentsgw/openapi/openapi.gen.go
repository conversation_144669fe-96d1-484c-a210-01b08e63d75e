// Package openapi provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.5.0 DO NOT EDIT.
package openapi

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/go-chi/chi/v5"
	"github.com/oapi-codegen/runtime"
)

const (
	BearerTokenAuthScopes = "BearerTokenAuth.Scopes"
)

// AstanaPlatError Ошибка для API Astana Plat
type AstanaPlatError struct {
	// Code Код ошибки
	Code *int `json:"code,omitempty"`

	// Message Текст ошибки
	Message *string `json:"message,omitempty"`
}

// AstanaPlatResponse defines model for AstanaPlatResponse.
type AstanaPlatResponse struct {
	AuthCode *string `json:"authCode,omitempty"`
	Code     *int    `json:"code,omitempty"`
	Date     *string `json:"date,omitempty"`
	Message  *string `json:"message,omitempty"`
	Name     *string `json:"name,omitempty"`
	Receipt  *string `json:"receipt,omitempty"`
}

// Health defines model for Health.
type Health struct {
	// AltScoreBridge Статус сервиса altScoreBridge
	AltScoreBridge bool `json:"altScoreBridge"`

	// AmlBridge Статус сервиса amlBridge
	AmlBridge bool `json:"amlBridge"`

	// Antifraud Статус сервиса antifraud
	Antifraud bool `json:"antifraud"`

	// ApBridge Статус сервиса apBridge
	ApBridge bool `json:"apBridge"`

	// BalanceUpdater Статус сервиса balanceUpdater
	BalanceUpdater bool `json:"balanceUpdater"`

	// BitrixBridge Статус сервиса bitrixBridge
	BitrixBridge bool `json:"bitrixBridge"`

	// BsasBridge Статус сервиса bsasBridge
	BsasBridge bool `json:"bsasBridge"`

	// BtsBridge Статус сервиса btsBridge
	BtsBridge bool `json:"btsBridge"`

	// CardsAccounts Статус сервиса cardsAccounts
	CardsAccounts bool `json:"cardsAccounts"`

	// Collection Статус сервиса collection
	Collection bool `json:"collection"`

	// ColvirBridge Статус сервиса colvirBridge
	ColvirBridge bool `json:"colvirBridge"`

	// Crm Статус сервиса crm
	Crm bool `json:"crm"`

	// Deposits Статус сервиса deposits
	Deposits bool `json:"deposits"`

	// Dictionary Статус сервиса dictionary
	Dictionary bool `json:"dictionary"`

	// Documents Статус сервиса documents
	Documents bool `json:"documents"`

	// FileGuard Статус сервиса fileGuard
	FileGuard bool `json:"fileGuard"`

	// ForeignActivity Статус сервиса foreignActivity
	ForeignActivity bool `json:"foreignActivity"`

	// JiraBridge Статус сервиса jiraBridge
	JiraBridge bool `json:"jiraBridge"`

	// JuicyscoreBridge Статус сервиса juicyscoreBridge
	JuicyscoreBridge bool `json:"juicyscoreBridge"`

	// KaspiBridge Статус сервиса kaspiBridge
	KaspiBridge bool `json:"kaspiBridge"`

	// KeycloakProxy Статус сервиса keycloakProxy
	KeycloakProxy bool `json:"keycloakProxy"`

	// KgdBridge Статус сервиса kgdBridge
	KgdBridge bool `json:"kgdBridge"`

	// Liveness Статус сервиса liveness
	Liveness bool `json:"liveness"`

	// Loans Статус сервиса loans
	Loans bool `json:"loans"`

	// Notifications Статус сервиса notifications
	Notifications bool `json:"notifications"`

	// Otp Статус сервиса otp
	Otp bool `json:"otp"`

	// Payments Статус сервиса payments
	Payments bool `json:"payments"`

	// PaymentsSme Статус сервиса paymentsSme
	PaymentsSme bool `json:"paymentsSme"`

	// PkbBridge Статус сервиса pkbBridge
	PkbBridge bool `json:"pkbBridge"`

	// ProcessingBridge Статус сервиса processingBridge
	ProcessingBridge bool `json:"processingBridge"`

	// QazpostBridge Статус сервиса qazpostBridge
	QazpostBridge bool `json:"qazpostBridge"`

	// Referral Статус сервиса referral
	Referral bool `json:"referral"`

	// Scoring Статус сервиса scoring
	Scoring bool `json:"scoring"`

	// SeonBridge Статус сервиса seonBridge
	SeonBridge bool `json:"seonBridge"`

	// SmsBridge Статус сервиса smsBridge
	SmsBridge bool `json:"smsBridge"`

	// SprBridge Статус сервиса sprBridge
	SprBridge bool `json:"sprBridge"`

	// TaskManager Статус сервиса taskManager
	TaskManager bool `json:"taskManager"`

	// Tokenize Статус сервиса tokenize
	Tokenize bool `json:"tokenize"`

	// Users Статус сервиса users
	Users bool `json:"users"`
}

// AstanaPlatParams defines parameters for AstanaPlat.
type AstanaPlatParams struct {
	// Action Предопределенная строка. Возможные значения -> check, payment
	Action string `form:"action" json:"action"`

	// Number ИИН пользователя
	Number string `form:"number" json:"number"`

	// Account Часть номера текущего счета, последние 6 цифр текущего счета
	Account string `form:"account" json:"account"`

	// Type Идентификатор 1 - пополнение на счет ФЛ, 2- пополнение на счет ИП
	Type uint32 `form:"type" json:"type"`

	// Amount Сумма платежа в тенге с тиынами. Разделитель ‘.’ (точка)
	Amount *string `form:"amount,omitempty" json:"amount,omitempty"`

	// Receipt Уникальный идентификатор платежа из внешней платежной системы (Терминала). Может содержать только цифры
	Receipt *string `form:"receipt,omitempty" json:"receipt,omitempty"`

	// Date Дата и время операции по часовому поясу внешней платежной системы в формате YYYY-MM-DDThh:mm:ss
	Date *string `form:"date,omitempty" json:"date,omitempty"`
}

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Проверка на работоспособность
	// (GET /health)
	Health(w http.ResponseWriter, r *http.Request)
	// Проверка пользователя по ИИН
	// (GET /payments/astanaplat)
	AstanaPlat(w http.ResponseWriter, r *http.Request, params AstanaPlatParams)
}

// Unimplemented server implementation that returns http.StatusNotImplemented for each endpoint.

type Unimplemented struct{}

// Проверка на работоспособность
// (GET /health)
func (_ Unimplemented) Health(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Проверка пользователя по ИИН
// (GET /payments/astanaplat)
func (_ Unimplemented) AstanaPlat(w http.ResponseWriter, r *http.Request, params AstanaPlatParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandlerFunc   func(w http.ResponseWriter, r *http.Request, err error)
}

type MiddlewareFunc func(http.Handler) http.Handler

// Health operation middleware
func (siw *ServerInterfaceWrapper) Health(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.Health(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// AstanaPlat operation middleware
func (siw *ServerInterfaceWrapper) AstanaPlat(w http.ResponseWriter, r *http.Request) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params AstanaPlatParams

	// ------------- Required query parameter "action" -------------

	if paramValue := r.URL.Query().Get("action"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "action"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "action", r.URL.Query(), &params.Action)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "action", Err: err})
		return
	}

	// ------------- Required query parameter "number" -------------

	if paramValue := r.URL.Query().Get("number"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "number"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "number", r.URL.Query(), &params.Number)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "number", Err: err})
		return
	}

	// ------------- Required query parameter "account" -------------

	if paramValue := r.URL.Query().Get("account"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "account"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "account", r.URL.Query(), &params.Account)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "account", Err: err})
		return
	}

	// ------------- Required query parameter "type" -------------

	if paramValue := r.URL.Query().Get("type"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "type"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "type", r.URL.Query(), &params.Type)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "type", Err: err})
		return
	}

	// ------------- Optional query parameter "amount" -------------

	err = runtime.BindQueryParameter("form", true, false, "amount", r.URL.Query(), &params.Amount)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "amount", Err: err})
		return
	}

	// ------------- Optional query parameter "receipt" -------------

	err = runtime.BindQueryParameter("form", true, false, "receipt", r.URL.Query(), &params.Receipt)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "receipt", Err: err})
		return
	}

	// ------------- Optional query parameter "date" -------------

	err = runtime.BindQueryParameter("form", true, false, "date", r.URL.Query(), &params.Date)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "date", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.AstanaPlat(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

type UnescapedCookieParamError struct {
	ParamName string
	Err       error
}

func (e *UnescapedCookieParamError) Error() string {
	return fmt.Sprintf("error unescaping cookie parameter '%s'", e.ParamName)
}

func (e *UnescapedCookieParamError) Unwrap() error {
	return e.Err
}

type UnmarshalingParamError struct {
	ParamName string
	Err       error
}

func (e *UnmarshalingParamError) Error() string {
	return fmt.Sprintf("Error unmarshaling parameter %s as JSON: %s", e.ParamName, e.Err.Error())
}

func (e *UnmarshalingParamError) Unwrap() error {
	return e.Err
}

type RequiredParamError struct {
	ParamName string
}

func (e *RequiredParamError) Error() string {
	return fmt.Sprintf("Query argument %s is required, but not found", e.ParamName)
}

type RequiredHeaderError struct {
	ParamName string
	Err       error
}

func (e *RequiredHeaderError) Error() string {
	return fmt.Sprintf("Header parameter %s is required, but not found", e.ParamName)
}

func (e *RequiredHeaderError) Unwrap() error {
	return e.Err
}

type InvalidParamFormatError struct {
	ParamName string
	Err       error
}

func (e *InvalidParamFormatError) Error() string {
	return fmt.Sprintf("Invalid format for parameter %s: %s", e.ParamName, e.Err.Error())
}

func (e *InvalidParamFormatError) Unwrap() error {
	return e.Err
}

type TooManyValuesForParamError struct {
	ParamName string
	Count     int
}

func (e *TooManyValuesForParamError) Error() string {
	return fmt.Sprintf("Expected one value for %s, got %d", e.ParamName, e.Count)
}

// Handler creates http.Handler with routing matching OpenAPI spec.
func Handler(si ServerInterface) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{})
}

type ChiServerOptions struct {
	BaseURL          string
	BaseRouter       chi.Router
	Middlewares      []MiddlewareFunc
	ErrorHandlerFunc func(w http.ResponseWriter, r *http.Request, err error)
}

// HandlerFromMux creates http.Handler with routing matching OpenAPI spec based on the provided mux.
func HandlerFromMux(si ServerInterface, r chi.Router) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseRouter: r,
	})
}

func HandlerFromMuxWithBaseURL(si ServerInterface, r chi.Router, baseURL string) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseURL:    baseURL,
		BaseRouter: r,
	})
}

// HandlerWithOptions creates http.Handler with additional options
func HandlerWithOptions(si ServerInterface, options ChiServerOptions) http.Handler {
	r := options.BaseRouter

	if r == nil {
		r = chi.NewRouter()
	}
	if options.ErrorHandlerFunc == nil {
		options.ErrorHandlerFunc = func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		}
	}
	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandlerFunc:   options.ErrorHandlerFunc,
	}

	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/health", wrapper.Health)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/payments/astanaplat", wrapper.AstanaPlat)
	})

	return r
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/7RZ3W7byBV+lcG0FzGgHyfeLrq68276kwJBjU2KIrWNYkyNJMYkhxmOvHECA7KdbVI4",
	"CwNFL9yfTZptgd7KXitWZEt5hTOvsE+ymENKIqWxY3KRG4Eg5/vmnDNnvnNm9JQ6wg9FwAMV0dpTGjkt",
	"7jN8XI4UC9iKx9SvpBTSvKrzyJFuqFwR0BqFV/oF9OEYBtAlcArn+pAsr9whMZAYJC3RUIqQS+VyJHVE",
	"nVuY/gkjOCUwmhD2aYmq7ZDTGnUDxZtc0p0S9XkUsaaN4DvowUDv6r1LSCIl3aBJd3Ymb8TGQ+4owzp1",
	"9EsehSKIcIKs3aytWl8kts9QliZOzRtcZ8oOSXky9y1gvv2D5A53Q2X5ZnPrt5x5qmVxxVP3HCH559Kt",
	"W2P5Ru9BV+/pfb1L9C70dAdOoK93oUtmsJNZN4TwOAvMtMz3CjFPYFbSQLkNydr1nKQTmJU0LGRoeIWd",
	"G8xjgcP/EJqFl/mIZ7BWeldJ93ERqzNIK3XEokLEU5yVVhVjVVeROkzWo2XHEe1Eta5PnIVayYXncSem",
	"ysU8xV1Cu+XKIrHIIK3U0s/JKH0rUZ2HInLzhnSCslK6GBImt3OSTnFWWuG0fZ579acwG2nD9fhv2kzm",
	"FJkpzEoqJHebwbKj3C1X5YzCLNg2wUNXsiJplcJZaduusx0VrRNzaNsUmywK3SLsaaCVmG87nmCbK1I8",
	"zhnxLNRK3qwXsnkCs5F67hYPeJQznScoK6VgQV4+hNjIAqHchuswQ5KTNAu1kQsV5qM0ABtRyLYLiMIE",
	"dRXlPZ8XYzVAK/HmRpE0msKspFI4PIrcoFmIexZtm+IRexKKSBXhz0Jt5JI3uJTMy8c7QdkojQiZFjkX",
	"4xhkJeQiKOJ9Cmel9Qs1SlOYlTQs1HFMYTZSxaLNuyxgzbwNbhpoJRabPHCf5DR2grJRtiMuc8pBDJkn",
	"w/R81HYlr9PaKh0Pi7Uo3VjMCt5sSUlXgnSfm17KsRLP9H4pqZptZDMNU1ol0gerdMHILIalXmf6g3SP",
	"M90cmZROJ83cMXF266d6xswBInW4sqhRpsXOCmxKA9KHvqTTnabI3CEr20vMHJNm+6/1uWM2CoLTlq7a",
	"vue0uB8fsT/nTHJ530y73I7P4Bv46tdC+kzRGv3dH+9jKA3EZBl+nWZdS6mQ7hh2N2iI+QReXrkzvnGB",
	"Y/0NDPQ3MIRT6BK4gBEcQx/O9UsYwgi+hxGB97pjXsEI3kIPhtDXh4T8ifksICtJHNeCtQDewHvo6b9A",
	"Xz+DPgygi8+HBE6IfgYj3YELs2+gR1ajr1izyeX6jUqlmjwvEJykT1YlrwsHP+HTQsWwr37RYkGTe6KJ",
	"X1pupITcrjrjt3/2mRtUWsr3FnD4l9zjLOLkQ7AGZ6ot+RhposilH/2+cY/LLdcZxzOqVatPjMtV/FwW",
	"jXKUDChR5SrPDMzGhCyv3KElusVlFMe9zrfKi5/d/LS8+MulpV/wTz6jJfq47IkmrlFberRGTTwUU65T",
	"Ne8rYXI/I0IesNClNbpUWazcxAxWLUyXamtyVdPkyiJXr3UHRnCCOoWXbScoWl/Hq32q9+EcevDOSFlf",
	"75r1gQt9QGAIXaI70IVjGOk9GOldeI+/JkWG5knv6ZcUjZMoWHfqtDa+OTK7Kr4PQytvLS7GN3iB4gFa",
	"ycLQS4Su+jCKD8zxBaJ5+rnkDVqjP6tObxiryfViNZkBM3zG1yMYplItSb/3JonPoIuJPNIvMIlNdl/o",
	"fYIeJ2qu901M+jDAcWllH8FJZrvS2qplo66u76yXaNT2fTyyWkJ/7Zgq1oxMpYA300WBoT6AHl03hlTH",
	"ClZleAEZekxdPwWMq/qv0EPmE/zUTTa2sQe3/1n8Gqc+n4TxCI7g2xKBfiwLGecwxAYNw4lOJDF/hppy",
	"Ffv/4AhezSXT9HYVU14ynyssyqtWH3twCiOc0jz1MLHNUnf1IS40WjyAboXA32AEZ7gD3sZxNfabkc8n",
	"tpfX2ouLS5w4Le5slsZNOTWySmv0UZtjuYwvXCkbl5ZpnVeyzUupnJ67dn06n75H8O2lQbpk5qDtb6D8",
	"/5SZ/w/dOPdIvDNwVbsEpx7EyYLFQO+aAJktUyJJ6p5jsIfQhx75lMT6rztXQS8NITYkPzmGZuWHei9V",
	"h8xO65CbpBzHNpOkZuWH8Z5A80wq/qtEbl1v7BG8vsQbtPQqVxrjct52A7V0y/KfhcW7N3ofLoy4GevO",
	"k9x4i6oeR3wI30OPmM7UBODAmGs0rULgP9CFs2Rb9JOcekl+6BxVfuj8g9zAID034Vq4bH38ZHnyLMd/",
	"MW4D6MY9hT6Ad6bQX7ZGs1714YzAiYm/foGr8C47xCTrXOm6Ad9h/hotN/6fQ3ehQuDfcQej9wgK7imO",
	"eYvi/9KEK950A5OqSRLrg0tCMf4zJV8s/o5md1E9T1CkLoz0jbBv6sQFC5XVmPDc7EgUgLhQ4W47jItU",
	"3oDMd2APHjx4UL57t3z79v1Wq+b7Nezrba7iv1BX+bn+EUu95d81W9l/Bcepqjqw7I5x6T83od3HshsH",
	"cLSGZ71PPorR8X+fVovT/37Ger+fKj2mGzeVSx/or9eC2dbjA03GNSp4usN4nYpU33QXOJncstfZ23yL",
	"xF9pKelZx/3xuCVpflXBVrkied1ndSk2hKo4wq+y0K1u3aTzO2NF8nIoRf3DzOVQ8roZGk+xwYLNyuaT",
	"hNnaFJn8THx9Os5pS1dljBp/zUZkZ33nxwAAAP//SDTMLOkeAAA=",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
