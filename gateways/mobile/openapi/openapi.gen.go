// Package openapi provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.5.0 DO NOT EDIT.
package openapi

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"
	"time"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/go-chi/chi/v5"
	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

const (
	BearerTokenAuthScopes = "BearerTokenAuth.Scopes"
)

// Defines values for AccountDocumentType.
const (
	AccountAvailability AccountDocumentType = "accountAvailability"
	AccountStatement    AccountDocumentType = "accountStatement"
	AvailableBalance    AccountDocumentType = "availableBalance"
)

// Defines values for ActionStatus.
const (
	Done    ActionStatus = "done"
	Waiting ActionStatus = "waiting"
)

// Defines values for AvailableAccountCurrency.
const (
	AvailableAccountCurrencyCNY AvailableAccountCurrency = "CNY"
	AvailableAccountCurrencyEUR AvailableAccountCurrency = "EUR"
	AvailableAccountCurrencyKZT AvailableAccountCurrency = "KZT"
	AvailableAccountCurrencyRUB AvailableAccountCurrency = "RUB"
	AvailableAccountCurrencyUSD AvailableAccountCurrency = "USD"
)

// Defines values for AvailableAccountStatus.
const (
	AvailableAccountStatusACTIVE   AvailableAccountStatus = "ACTIVE"
	AvailableAccountStatusARCHIVED AvailableAccountStatus = "ARCHIVED"
	AvailableAccountStatusARRESTED AvailableAccountStatus = "ARRESTED"
	AvailableAccountStatusBLOCKED  AvailableAccountStatus = "BLOCKED"
	AvailableAccountStatusCLOSED   AvailableAccountStatus = "CLOSED"
	AvailableAccountStatusMISTAKEN AvailableAccountStatus = "MISTAKEN"
	AvailableAccountStatusREOPENED AvailableAccountStatus = "REOPENED"
)

// Defines values for AvailableAccountType.
const (
	AvailableAccountTypeBUCO   AvailableAccountType = "BUCO"
	AvailableAccountTypeBUFB   AvailableAccountType = "BUFB"
	AvailableAccountTypeCURR   AvailableAccountType = "CURR"
	AvailableAccountTypeLOAN   AvailableAccountType = "LOAN"
	AvailableAccountTypeOTHERS AvailableAccountType = "OTHERS"
	AvailableAccountTypeTU     AvailableAccountType = "TU"
)

// Defines values for CurrencyCode.
const (
	CurrencyCodeCNY CurrencyCode = "CNY"
	CurrencyCodeEUR CurrencyCode = "EUR"
	CurrencyCodeKZT CurrencyCode = "KZT"
	CurrencyCodeRUB CurrencyCode = "RUB"
	CurrencyCodeUSD CurrencyCode = "USD"
)

// Defines values for DepositAccountCurrency.
const (
	DepositAccountCurrencyCNY DepositAccountCurrency = "CNY"
	DepositAccountCurrencyEUR DepositAccountCurrency = "EUR"
	DepositAccountCurrencyKZT DepositAccountCurrency = "KZT"
	DepositAccountCurrencyRUB DepositAccountCurrency = "RUB"
	DepositAccountCurrencyUSD DepositAccountCurrency = "USD"
)

// Defines values for DepositPayoutMethod.
const (
	CARD    DepositPayoutMethod = "CARD"
	DEPOSIT DepositPayoutMethod = "DEPOSIT"
)

// Defines values for DeviceInfoSystemType.
const (
	Android DeviceInfoSystemType = "Android"
	IOS     DeviceInfoSystemType = "iOS"
	Web     DeviceInfoSystemType = "Web"
)

// Defines values for DeviceInformationNetworkType.
const (
	CELLULAR DeviceInformationNetworkType = "CELLULAR"
	WIFI     DeviceInformationNetworkType = "WIFI"
)

// Defines values for DeviceInformationTimeZoneSetting.
const (
	MANUAL     DeviceInformationTimeZoneSetting = "MANUAL"
	NETWORKSET DeviceInformationTimeZoneSetting = "NETWORK_SET"
)

// Defines values for DictKATOMapFromTSOIDArgsLang.
const (
	DictKATOMapFromTSOIDArgsLangEn DictKATOMapFromTSOIDArgsLang = "en"
	DictKATOMapFromTSOIDArgsLangKz DictKATOMapFromTSOIDArgsLang = "kz"
	DictKATOMapFromTSOIDArgsLangRu DictKATOMapFromTSOIDArgsLang = "ru"
)

// Defines values for DocumentType.
const (
	DocumentTypeAccessionAgreement          DocumentType = "accessionAgreement"
	DocumentTypeAccountOpeningApplication   DocumentType = "accountOpeningApplication"
	DocumentTypeBankServiceAgreement        DocumentType = "bankServiceAgreement"
	DocumentTypeComplexConsentClient        DocumentType = "complexConsentClient"
	DocumentTypeFinancingTermsInfo          DocumentType = "financingTermsInfo"
	DocumentTypePersonalDataAgreement       DocumentType = "personalDataAgreement"
	DocumentTypePersonalFilledDataAgreement DocumentType = "personalFilledDataAgreement"
)

// Defines values for GetDepositDetailRespCurrency.
const (
	GetDepositDetailRespCurrencyCNY GetDepositDetailRespCurrency = "CNY"
	GetDepositDetailRespCurrencyEUR GetDepositDetailRespCurrency = "EUR"
	GetDepositDetailRespCurrencyKZT GetDepositDetailRespCurrency = "KZT"
	GetDepositDetailRespCurrencyRUB GetDepositDetailRespCurrency = "RUB"
	GetDepositDetailRespCurrencyUSD GetDepositDetailRespCurrency = "USD"
)

// Defines values for LoanAppDocumentType.
const (
	LoanAppDocumentTypeComplexConsentClient LoanAppDocumentType = "complexConsentClient"
)

// Defines values for LoanDetailsProductType.
const (
	LoanDetailsProductTypeLOAN      LoanDetailsProductType = "LOAN"
	LoanDetailsProductTypeREFINANCE LoanDetailsProductType = "REFINANCE"
)

// Defines values for NextStep.
const (
	DocumentSignRequired     NextStep = "documentSignRequired"
	IdentificationRequired   NextStep = "identificationRequired"
	ReidentificationRequired NextStep = "reidentificationRequired"
)

// Defines values for PaymentsOperationType.
const (
	PaymentsOperationTypeCREDIT PaymentsOperationType = "CREDIT"
	PaymentsOperationTypeDEBIT  PaymentsOperationType = "DEBIT"
)

// Defines values for PaymentsTransactionStatus.
const (
	COMPLETED  PaymentsTransactionStatus = "COMPLETED"
	INPROGRESS PaymentsTransactionStatus = "IN_PROGRESS"
	REJECTED   PaymentsTransactionStatus = "REJECTED"
)

// Defines values for PublicDocumentType.
const (
	FinancingTermsInfo    PublicDocumentType = "financingTermsInfo"
	PersonalDataAgreement PublicDocumentType = "personalDataAgreement"
)

// Defines values for ReferralStatus.
const (
	ReferralStatusActive    ReferralStatus = "active"
	ReferralStatusCompleted ReferralStatus = "completed"
)

// Defines values for ScheduleItemStatus.
const (
	END     ScheduleItemStatus = "END"
	OVERDUE ScheduleItemStatus = "OVERDUE"
	PLAN    ScheduleItemStatus = "PLAN"
)

// Defines values for TaxPayerType.
const (
	INDIVIDUAL  TaxPayerType = "INDIVIDUAL"
	LEGALENTITY TaxPayerType = "LEGAL_ENTITY"
)

// Defines values for TokenizeStatus.
const (
	TokenizeStatusNeedVerify   TokenizeStatus = "need_verify"
	TokenizeStatusNotTokenized TokenizeStatus = "not_tokenized"
	TokenizeStatusTokenized    TokenizeStatus = "tokenized"
)

// Defines values for TransactionType.
const (
	BETWEENOWNACCKZT     TransactionType = "BETWEEN_OWN_ACCKZT"
	OTHER                TransactionType = "OTHER"
	PAYMENTBYACCOUNT     TransactionType = "PAYMENT_BY_ACCOUNT"
	PAYMENTBYPHONENUMBER TransactionType = "PAYMENT_BY_PHONE_NUMBER"
	PAYMENTMOBILE        TransactionType = "PAYMENT_MOBILE"
	PAYMENTTERMINAL      TransactionType = "PAYMENT_TERMINAL"
)

// Defines values for UserAccountCurrency.
const (
	CNY UserAccountCurrency = "CNY"
	EUR UserAccountCurrency = "EUR"
	KZT UserAccountCurrency = "KZT"
	RUB UserAccountCurrency = "RUB"
	USD UserAccountCurrency = "USD"
)

// Defines values for UserAccountFinContractStatus.
const (
	UserAccountFinContractStatusACTIVE    UserAccountFinContractStatus = "ACTIVE"
	UserAccountFinContractStatusBLOCKED   UserAccountFinContractStatus = "BLOCKED"
	UserAccountFinContractStatusCLOSED    UserAccountFinContractStatus = "CLOSED"
	UserAccountFinContractStatusINOPENING UserAccountFinContractStatus = "IN_OPENING"
)

// Defines values for UserAccountStatus.
const (
	UserAccountStatusACTIVE    UserAccountStatus = "ACTIVE"
	UserAccountStatusCLOSED    UserAccountStatus = "CLOSED"
	UserAccountStatusERROR     UserAccountStatus = "ERROR"
	UserAccountStatusINOPENING UserAccountStatus = "IN_OPENING"
)

// Defines values for UserAccountType.
const (
	BUCO   UserAccountType = "BUCO"
	BUFB   UserAccountType = "BUFB"
	CURR   UserAccountType = "CURR"
	LOAN   UserAccountType = "LOAN"
	OTHERS UserAccountType = "OTHERS"
	TU     UserAccountType = "TU"
)

// Defines values for UserCardCardType.
const (
	PHYSICAL UserCardCardType = "PHYSICAL"
	VIRTUAL  UserCardCardType = "VIRTUAL"
)

// Defines values for UserCardPaymentSystem.
const (
	MASTERCARD UserCardPaymentSystem = "MASTERCARD"
)

// Defines values for UserCardStatus.
const (
	UserCardStatusACTIVE    UserCardStatus = "ACTIVE"
	UserCardStatusBLOCKED   UserCardStatus = "BLOCKED"
	UserCardStatusERROR     UserCardStatus = "ERROR"
	UserCardStatusINOPENING UserCardStatus = "IN_OPENING"
)

// Defines values for UserCardTokenizationStatus.
const (
	UserCardTokenizationStatusNeedVerify   UserCardTokenizationStatus = "need_verify"
	UserCardTokenizationStatusNotTokenized UserCardTokenizationStatus = "not_tokenized"
	UserCardTokenizationStatusTokenized    UserCardTokenizationStatus = "tokenized"
)

// Defines values for UserCardWallet.
const (
	UserCardWalletApplePay  UserCardWallet = "ApplePay"
	UserCardWalletGooglePay UserCardWallet = "GooglePay"
)

// Defines values for Wallet.
const (
	WalletApplePay  Wallet = "ApplePay"
	WalletGooglePay Wallet = "GooglePay"
)

// Defines values for AcceptLanguage.
const (
	AcceptLanguageEn AcceptLanguage = "en"
	AcceptLanguageKk AcceptLanguage = "kk"
	AcceptLanguageRu AcceptLanguage = "ru"
)

// Defines values for OnboardingSourceQueryParam.
const (
	OnboardingSourceQueryParamCommon  OnboardingSourceQueryParam = "common"
	OnboardingSourceQueryParamLanding OnboardingSourceQueryParam = "landing"
)

// Defines values for PaymentsGetTransactionsOperationType.
const (
	PaymentsGetTransactionsOperationTypeCREDIT PaymentsGetTransactionsOperationType = "CREDIT"
	PaymentsGetTransactionsOperationTypeDEBIT  PaymentsGetTransactionsOperationType = "DEBIT"
)

// Defines values for RefinancingInfoReqStep.
const (
	RefinancingInfoReqStepINPUTIBAN     RefinancingInfoReqStep = "INPUT_IBAN"
	RefinancingInfoReqStepREFCONDITIONS RefinancingInfoReqStep = "REF_CONDITIONS"
)

// Defines values for TaskStatusQueryParam.
const (
	TaskStatusQueryParamCompleted  TaskStatusQueryParam = "completed"
	TaskStatusQueryParamInProgress TaskStatusQueryParam = "in-progress"
	TaskStatusQueryParamPending    TaskStatusQueryParam = "pending"
)

// Defines values for GetBtsDataForAuthParamsAcceptLanguage.
const (
	GetBtsDataForAuthParamsAcceptLanguageEn GetBtsDataForAuthParamsAcceptLanguage = "en"
	GetBtsDataForAuthParamsAcceptLanguageKk GetBtsDataForAuthParamsAcceptLanguage = "kk"
	GetBtsDataForAuthParamsAcceptLanguageRu GetBtsDataForAuthParamsAcceptLanguage = "ru"
)

// Defines values for AuthConfirmParamsAcceptLanguage.
const (
	AuthConfirmParamsAcceptLanguageEn AuthConfirmParamsAcceptLanguage = "en"
	AuthConfirmParamsAcceptLanguageKk AuthConfirmParamsAcceptLanguage = "kk"
	AuthConfirmParamsAcceptLanguageRu AuthConfirmParamsAcceptLanguage = "ru"
)

// Defines values for DocumentForSignParamsAcceptLanguage.
const (
	DocumentForSignParamsAcceptLanguageEn DocumentForSignParamsAcceptLanguage = "en"
	DocumentForSignParamsAcceptLanguageKk DocumentForSignParamsAcceptLanguage = "kk"
	DocumentForSignParamsAcceptLanguageRu DocumentForSignParamsAcceptLanguage = "ru"
)

// Defines values for AuthIdentifyParamsAcceptLanguage.
const (
	AuthIdentifyParamsAcceptLanguageEn AuthIdentifyParamsAcceptLanguage = "en"
	AuthIdentifyParamsAcceptLanguageKk AuthIdentifyParamsAcceptLanguage = "kk"
	AuthIdentifyParamsAcceptLanguageRu AuthIdentifyParamsAcceptLanguage = "ru"
)

// Defines values for AuthLoginParamsAcceptLanguage.
const (
	AuthLoginParamsAcceptLanguageEn AuthLoginParamsAcceptLanguage = "en"
	AuthLoginParamsAcceptLanguageKk AuthLoginParamsAcceptLanguage = "kk"
	AuthLoginParamsAcceptLanguageRu AuthLoginParamsAcceptLanguage = "ru"
)

// Defines values for AuthLogoutParamsAcceptLanguage.
const (
	AuthLogoutParamsAcceptLanguageEn AuthLogoutParamsAcceptLanguage = "en"
	AuthLogoutParamsAcceptLanguageKk AuthLogoutParamsAcceptLanguage = "kk"
	AuthLogoutParamsAcceptLanguageRu AuthLogoutParamsAcceptLanguage = "ru"
)

// Defines values for AuthRefreshParamsAcceptLanguage.
const (
	AuthRefreshParamsAcceptLanguageEn AuthRefreshParamsAcceptLanguage = "en"
	AuthRefreshParamsAcceptLanguageKk AuthRefreshParamsAcceptLanguage = "kk"
	AuthRefreshParamsAcceptLanguageRu AuthRefreshParamsAcceptLanguage = "ru"
)

// Defines values for DictGetLocationsParamsAcceptLanguage.
const (
	DictGetLocationsParamsAcceptLanguageEn DictGetLocationsParamsAcceptLanguage = "en"
	DictGetLocationsParamsAcceptLanguageKk DictGetLocationsParamsAcceptLanguage = "kk"
	DictGetLocationsParamsAcceptLanguageRu DictGetLocationsParamsAcceptLanguage = "ru"
)

// Defines values for ConfirmSignDocumentsBatchParamsAcceptLanguage.
const (
	ConfirmSignDocumentsBatchParamsAcceptLanguageEn ConfirmSignDocumentsBatchParamsAcceptLanguage = "en"
	ConfirmSignDocumentsBatchParamsAcceptLanguageKk ConfirmSignDocumentsBatchParamsAcceptLanguage = "kk"
	ConfirmSignDocumentsBatchParamsAcceptLanguageRu ConfirmSignDocumentsBatchParamsAcceptLanguage = "ru"
)

// Defines values for RequestDocumentPublicParamsAcceptLanguage.
const (
	RequestDocumentPublicParamsAcceptLanguageEn RequestDocumentPublicParamsAcceptLanguage = "en"
	RequestDocumentPublicParamsAcceptLanguageKk RequestDocumentPublicParamsAcceptLanguage = "kk"
	RequestDocumentPublicParamsAcceptLanguageRu RequestDocumentPublicParamsAcceptLanguage = "ru"
)

// Defines values for SignDocumentByIDsParamsAcceptLanguage.
const (
	SignDocumentByIDsParamsAcceptLanguageEn SignDocumentByIDsParamsAcceptLanguage = "en"
	SignDocumentByIDsParamsAcceptLanguageKk SignDocumentByIDsParamsAcceptLanguage = "kk"
	SignDocumentByIDsParamsAcceptLanguageRu SignDocumentByIDsParamsAcceptLanguage = "ru"
)

// Defines values for ConfirmSignDocumentsParamsAcceptLanguage.
const (
	ConfirmSignDocumentsParamsAcceptLanguageEn ConfirmSignDocumentsParamsAcceptLanguage = "en"
	ConfirmSignDocumentsParamsAcceptLanguageKk ConfirmSignDocumentsParamsAcceptLanguage = "kk"
	ConfirmSignDocumentsParamsAcceptLanguageRu ConfirmSignDocumentsParamsAcceptLanguage = "ru"
)

// Defines values for GetDocumentByIDParamsAcceptLanguage.
const (
	GetDocumentByIDParamsAcceptLanguageEn GetDocumentByIDParamsAcceptLanguage = "en"
	GetDocumentByIDParamsAcceptLanguageKk GetDocumentByIDParamsAcceptLanguage = "kk"
	GetDocumentByIDParamsAcceptLanguageRu GetDocumentByIDParamsAcceptLanguage = "ru"
)

// Defines values for SignDocumentByIDParamsAcceptLanguage.
const (
	SignDocumentByIDParamsAcceptLanguageEn SignDocumentByIDParamsAcceptLanguage = "en"
	SignDocumentByIDParamsAcceptLanguageKk SignDocumentByIDParamsAcceptLanguage = "kk"
	SignDocumentByIDParamsAcceptLanguageRu SignDocumentByIDParamsAcceptLanguage = "ru"
)

// Defines values for ConfirmSignDocumentByIDParamsAcceptLanguage.
const (
	ConfirmSignDocumentByIDParamsAcceptLanguageEn ConfirmSignDocumentByIDParamsAcceptLanguage = "en"
	ConfirmSignDocumentByIDParamsAcceptLanguageKk ConfirmSignDocumentByIDParamsAcceptLanguage = "kk"
	ConfirmSignDocumentByIDParamsAcceptLanguageRu ConfirmSignDocumentByIDParamsAcceptLanguage = "ru"
)

// Defines values for DocumentsForLoanAppParamsAcceptLanguage.
const (
	DocumentsForLoanAppParamsAcceptLanguageEn DocumentsForLoanAppParamsAcceptLanguage = "en"
	DocumentsForLoanAppParamsAcceptLanguageKk DocumentsForLoanAppParamsAcceptLanguage = "kk"
	DocumentsForLoanAppParamsAcceptLanguageRu DocumentsForLoanAppParamsAcceptLanguage = "ru"
)

// Defines values for GetLoansOnboardingTextsParamsSource.
const (
	GetLoansOnboardingTextsParamsSourceCommon  GetLoansOnboardingTextsParamsSource = "common"
	GetLoansOnboardingTextsParamsSourceLanding GetLoansOnboardingTextsParamsSource = "landing"
)

// Defines values for GetLoanSurveyParamsAcceptLanguage.
const (
	GetLoanSurveyParamsAcceptLanguageEn GetLoanSurveyParamsAcceptLanguage = "en"
	GetLoanSurveyParamsAcceptLanguageKk GetLoanSurveyParamsAcceptLanguage = "kk"
	GetLoanSurveyParamsAcceptLanguageRu GetLoanSurveyParamsAcceptLanguage = "ru"
)

// Defines values for SaveLoanSurveyParamsAcceptLanguage.
const (
	SaveLoanSurveyParamsAcceptLanguageEn SaveLoanSurveyParamsAcceptLanguage = "en"
	SaveLoanSurveyParamsAcceptLanguageKk SaveLoanSurveyParamsAcceptLanguage = "kk"
	SaveLoanSurveyParamsAcceptLanguageRu SaveLoanSurveyParamsAcceptLanguage = "ru"
)

// Defines values for GetBtsDataForLoanAppParamsAcceptLanguage.
const (
	GetBtsDataForLoanAppParamsAcceptLanguageEn GetBtsDataForLoanAppParamsAcceptLanguage = "en"
	GetBtsDataForLoanAppParamsAcceptLanguageKk GetBtsDataForLoanAppParamsAcceptLanguage = "kk"
	GetBtsDataForLoanAppParamsAcceptLanguageRu GetBtsDataForLoanAppParamsAcceptLanguage = "ru"
)

// Defines values for GetLoansDetailsParamsAcceptLanguage.
const (
	GetLoansDetailsParamsAcceptLanguageEn GetLoansDetailsParamsAcceptLanguage = "en"
	GetLoansDetailsParamsAcceptLanguageKk GetLoansDetailsParamsAcceptLanguage = "kk"
	GetLoansDetailsParamsAcceptLanguageRu GetLoansDetailsParamsAcceptLanguage = "ru"
)

// Defines values for LoanDocumentForSignParamsAcceptLanguage.
const (
	LoanDocumentForSignParamsAcceptLanguageEn LoanDocumentForSignParamsAcceptLanguage = "en"
	LoanDocumentForSignParamsAcceptLanguageKk LoanDocumentForSignParamsAcceptLanguage = "kk"
	LoanDocumentForSignParamsAcceptLanguageRu LoanDocumentForSignParamsAcceptLanguage = "ru"
)

// Defines values for LoansPostEdsBtsDataParamsAcceptLanguage.
const (
	LoansPostEdsBtsDataParamsAcceptLanguageEn LoansPostEdsBtsDataParamsAcceptLanguage = "en"
	LoansPostEdsBtsDataParamsAcceptLanguageKk LoansPostEdsBtsDataParamsAcceptLanguage = "kk"
	LoansPostEdsBtsDataParamsAcceptLanguageRu LoansPostEdsBtsDataParamsAcceptLanguage = "ru"
)

// Defines values for LoansPostIdentifyBtsDataParamsAcceptLanguage.
const (
	LoansPostIdentifyBtsDataParamsAcceptLanguageEn LoansPostIdentifyBtsDataParamsAcceptLanguage = "en"
	LoansPostIdentifyBtsDataParamsAcceptLanguageKk LoansPostIdentifyBtsDataParamsAcceptLanguage = "kk"
	LoansPostIdentifyBtsDataParamsAcceptLanguageRu LoansPostIdentifyBtsDataParamsAcceptLanguage = "ru"
)

// Defines values for GetRefinancingInfoParamsStep.
const (
	GetRefinancingInfoParamsStepINPUTIBAN     GetRefinancingInfoParamsStep = "INPUT_IBAN"
	GetRefinancingInfoParamsStepREFCONDITIONS GetRefinancingInfoParamsStep = "REF_CONDITIONS"
)

// Defines values for GetRefinancingInfoParamsAcceptLanguage.
const (
	GetRefinancingInfoParamsAcceptLanguageEn GetRefinancingInfoParamsAcceptLanguage = "en"
	GetRefinancingInfoParamsAcceptLanguageKk GetRefinancingInfoParamsAcceptLanguage = "kk"
	GetRefinancingInfoParamsAcceptLanguageRu GetRefinancingInfoParamsAcceptLanguage = "ru"
)

// Defines values for GetScoringResultParamsAcceptLanguage.
const (
	GetScoringResultParamsAcceptLanguageEn GetScoringResultParamsAcceptLanguage = "en"
	GetScoringResultParamsAcceptLanguageKk GetScoringResultParamsAcceptLanguage = "kk"
	GetScoringResultParamsAcceptLanguageRu GetScoringResultParamsAcceptLanguage = "ru"
)

// Defines values for LoansConfirmSignDocumentsParamsAcceptLanguage.
const (
	LoansConfirmSignDocumentsParamsAcceptLanguageEn LoansConfirmSignDocumentsParamsAcceptLanguage = "en"
	LoansConfirmSignDocumentsParamsAcceptLanguageKk LoansConfirmSignDocumentsParamsAcceptLanguage = "kk"
	LoansConfirmSignDocumentsParamsAcceptLanguageRu LoansConfirmSignDocumentsParamsAcceptLanguage = "ru"
)

// Defines values for LoansConfirmSignDocumentByIDParamsAcceptLanguage.
const (
	LoansConfirmSignDocumentByIDParamsAcceptLanguageEn LoansConfirmSignDocumentByIDParamsAcceptLanguage = "en"
	LoansConfirmSignDocumentByIDParamsAcceptLanguageKk LoansConfirmSignDocumentByIDParamsAcceptLanguage = "kk"
	LoansConfirmSignDocumentByIDParamsAcceptLanguageRu LoansConfirmSignDocumentByIDParamsAcceptLanguage = "ru"
)

// Defines values for GetTransactionsParamsDirection.
const (
	CREDIT GetTransactionsParamsDirection = "CREDIT"
	DEBIT  GetTransactionsParamsDirection = "DEBIT"
)

// Defines values for GetTasksParamsStatus.
const (
	Completed  GetTasksParamsStatus = "completed"
	InProgress GetTasksParamsStatus = "in-progress"
	Pending    GetTasksParamsStatus = "pending"
)

// Defines values for GetLoansParamsAcceptLanguage.
const (
	GetLoansParamsAcceptLanguageEn GetLoansParamsAcceptLanguage = "en"
	GetLoansParamsAcceptLanguageKk GetLoansParamsAcceptLanguage = "kk"
	GetLoansParamsAcceptLanguageRu GetLoansParamsAcceptLanguage = "ru"
)

// Defines values for UsersUpdateUserLocaleParamsAcceptLanguage.
const (
	En UsersUpdateUserLocaleParamsAcceptLanguage = "en"
	Kk UsersUpdateUserLocaleParamsAcceptLanguage = "kk"
	Ru UsersUpdateUserLocaleParamsAcceptLanguage = "ru"
)

// APIError Общий формат ошибки API. Возвращает цифровой код
type APIError struct {
	Error string `json:"error"`

	// Fields Объект с описанием деталей ошибок
	Fields *map[string]string `json:"fields,omitempty"`
}

// Account Информация по счету
type Account struct {
	// Amount Баланс счета
	Amount Money `json:"amount"`

	// BankBic БИК банка
	BankBic string `json:"bankBic"`

	// BankBin БИН банка
	BankBin string `json:"bankBin"`

	// BankName Наименование банка
	BankName string `json:"bankName"`

	// FullName ФИО клиента
	FullName string `json:"fullName"`

	// Iban Счет для погашения (IBAN)
	Iban string `json:"iban"`

	// IbanLastDigits Замаскированный номер счета списания (отображать только 4 последние символа)
	IbanLastDigits string `json:"ibanLastDigits"`

	// Iin ИИН клиента
	Iin string `json:"iin"`
}

// AccountDocumentResponse defines model for AccountDocumentResponse.
type AccountDocumentResponse struct {
	Document *Document `json:"document,omitempty"`
}

// AccountDocumentType Тип (шаблон) документа для счета
type AccountDocumentType string

// ActionDetail Детальная информация о целевом действии
type ActionDetail struct {
	// ActionStatus Статус выполнения целевого действия. Возможные значения:
	// * **done** - целевое действие выполнено
	// * **waiting** - целевое действие ожидает выполнения
	ActionStatus ActionStatus `json:"actionStatus"`

	// ActionType Тип целевого действия.
	ActionType ActionType `json:"actionType"`

	// RewardForAction Сумма вознаграждения за целевое действие
	RewardForAction float64 `json:"rewardForAction"`
}

// ActionStatus Статус выполнения целевого действия. Возможные значения:
// * **done** - целевое действие выполнено
// * **waiting** - целевое действие ожидает выполнения
type ActionStatus string

// ActionType Тип целевого действия.
type ActionType = string

// AdditionalIndividualType Дополнительный тип для ФЛ если есть
type AdditionalIndividualType struct {
	// Name Наименование дополнительного бенефициара для данного доп.  типа ФЛ
	Name string `json:"name"`

	// Type Список кодов 32 - ИП, 64 — адвокат, 128 — частный нотариус, 256 — частный судебный исполнитель
	Type int `json:"type"`
}

// ApplicationForSignResponse defines model for ApplicationForSignResponse.
type ApplicationForSignResponse struct {
	// Documents Документы на подпись
	Documents []Documents `json:"documents"`
}

// AttachedDocData defines model for AttachedDocData.
type AttachedDocData struct {
	// BankID Идентификатор банка, согласно справочнику
	BankID string `json:"bankID"`

	// DocID Идентификатор документа из временного хранилища
	DocID string `json:"docID"`
}

// AuthConfirmResponse defines model for AuthConfirmResponse.
type AuthConfirmResponse struct {
	NextStep *NextStep  `json:"nextStep,omitempty"`
	Tokens   AuthTokens `json:"tokens"`

	// UserID Идентификатор пользователя
	UserID string `json:"userID"`
}

// AuthRefreshResponse defines model for AuthRefreshResponse.
type AuthRefreshResponse struct {
	FirstName  string     `json:"firstName"`
	LastName   string     `json:"lastName"`
	MiddleName *string    `json:"middleName,omitempty"`
	NextStep   NextStep   `json:"nextStep"`
	Tokens     AuthTokens `json:"tokens"`
}

// AuthTokens defines model for AuthTokens.
type AuthTokens struct {
	// Access Токен авторизации
	Access string `json:"access"`

	// Refresh Токен обновления авторизации
	Refresh string `json:"refresh"`
}

// AvailableAccount defines model for AvailableAccount.
type AvailableAccount struct {
	// ID Идентификатор счёта
	ID string `json:"ID"`

	// AvailableBalance Доступный баланс
	AvailableBalance float64 `json:"availableBalance"`

	// Currency Валюта счёта
	Currency AvailableAccountCurrency `json:"currency"`

	// Iban Номер счета iban
	Iban string `json:"iban"`

	// Status Статус счёта
	Status AvailableAccountStatus `json:"status"`

	// Type Тип счёта
	Type AvailableAccountType `json:"type"`
}

// AvailableAccountCurrency Валюта счёта
type AvailableAccountCurrency string

// AvailableAccountStatus Статус счёта
type AvailableAccountStatus string

// AvailableAccountType Тип счёта
type AvailableAccountType string

// BankStatements defines model for BankStatements.
type BankStatements struct {
	// Banks Банки для загрузки выписок
	Banks []ExternalBank `json:"banks"`

	// Formats Разрешенные форматы для выписок
	Formats string `json:"formats"`

	// MaxFileSize Максимальное кол-во мегабайт для выписок
	MaxFileSize float32 `json:"maxFileSize"`

	// Months Кол-во месяцев, за которые нужна выписка
	Months float32 `json:"months"`
}

// BtsDataForAuthResp defines model for BtsDataForAuthResp.
type BtsDataForAuthResp struct {
	// Link Ссылка для прохождения идентификации
	Link string `json:"link"`

	// RedirectURI Ссылка для редиректа
	RedirectURI string `json:"redirectURI"`
}

// BtsDataResp defines model for BtsDataResp.
type BtsDataResp struct {
	// Link Ссылка для прохождения идентификации
	Link string `json:"link"`

	// RedirectURI Ссылка для редиректа
	RedirectURI string `json:"redirectURI"`
}

// CalcDataResponse defines model for CalcDataResponse.
type CalcDataResponse struct {
	// Amount Холдер лимитов по сумме кредита
	Amount LoanAmount `json:"amount"`

	// Purpose Массив доступных целей кредита
	Purpose []LoanPurposeData `json:"purpose"`

	// TermInterest Массив доступных условий кредита
	TermInterest []LoanTermInterestData `json:"termInterest"`
}

// CalculateProfitResp defines model for CalculateProfitResp.
type CalculateProfitResp struct {
	// CalculatedProfit Рассчитанная суммарная доходность за весь срок
	CalculatedProfit float64 `json:"calculatedProfit"`
}

// CalculationResultResponse defines model for CalculationResultResponse.
type CalculationResultResponse struct {
	// CalculationResult Массив результатов расчетов платежей
	CalculationResult []CalculationTermData `json:"calculationResult"`
}

// CalculationTermData defines model for CalculationTermData.
type CalculationTermData struct {
	// MonthlyPayment Предварительный ежемесячный платеж
	MonthlyPayment int `json:"monthlyPayment"`

	// OverpayAmount Сумма наценки
	OverpayAmount int `json:"overpayAmount"`

	// TermInterest Холдер условий кредита
	TermInterest LoanTermInterestData `json:"termInterest"`

	// TotalAmount Общая сумма (сумма кредита + сумма наценки)
	TotalAmount int `json:"totalAmount"`
}

// CancelLoanApplicationResp defines model for CancelLoanApplicationResp.
type CancelLoanApplicationResp struct {
	// ApplicationID Уникальный идентификатор заявки на кредит
	ApplicationID string `json:"applicationID"`
}

// CardRequisitesResponse defines model for CardRequisitesResponse.
type CardRequisitesResponse struct {
	// Cvv CVV код карты
	Cvv string `json:"cvv"`

	// EmbossName Имя, вытесненное на карте
	EmbossName string `json:"embossName"`

	// ExpiryDate Срок действия карты в формате MM/YY
	ExpiryDate string `json:"expiryDate"`

	// Pan Полный номер карты (PAN)
	Pan string `json:"pan"`

	// Status Статус карты
	Status string `json:"status"`
}

// CardTokenInfo defines model for CardTokenInfo.
type CardTokenInfo struct {
	DeviceBindingList    *[]DeviceBinding   `json:"deviceBindingList,omitempty"`
	DeviceInformation    *DeviceInformation `json:"deviceInformation,omitempty"`
	IsPrimary            *bool              `json:"isPrimary,omitempty"`
	IssuerCardRefId      string             `json:"issuerCardRefId"`
	LastReplenishTime    *time.Time         `json:"lastReplenishTime,omitempty"`
	LastStatusChangeTime *time.Time         `json:"lastStatusChangeTime,omitempty"`
	ProvisioningTime     *time.Time         `json:"provisioningTime,omitempty"`
	PublicKeyIdentifier  *string            `json:"publicKeyIdentifier,omitempty"`

	// Status Статус по справочнику Thales (`DEPLOYMENT_ONGOING`, …)
	Status              string          `json:"status"`
	TokenAssuranceLevel *string         `json:"tokenAssuranceLevel,omitempty"`
	TokenDetails        *string         `json:"tokenDetails,omitempty"`
	TokenRequestor      *TokenRequestor `json:"tokenRequestor,omitempty"`
	TokenSuffix         *string         `json:"tokenSuffix,omitempty"`
	TokenType           *string         `json:"tokenType,omitempty"`
	VirtualCardId       *string         `json:"virtualCardId,omitempty"`
	WalletCardRefId     *string         `json:"walletCardRefId,omitempty"`
	WalletProviderId    *string         `json:"walletProviderId,omitempty"`
	WalletVirtualCardId *string         `json:"walletVirtualCardId,omitempty"`
}

// CheckActiveLoanAppExistsResp defines model for CheckActiveLoanAppExistsResp.
type CheckActiveLoanAppExistsResp struct {
	// HasActive Признак наличия активной кредитной заявки
	HasActive bool         `json:"hasActive"`
	Reason    *ErrorReason `json:"reason,omitempty"`
}

// CheckClientByPhoneNumberResponse defines model for CheckClientByPhoneNumberResponse.
type CheckClientByPhoneNumberResponse struct {
	// ClientName Наименование клиента банка в сокращённом варианте
	ClientName string `json:"clientName"`
}

// CheckPhoneNumberResponse defines model for CheckPhoneNumberResponse.
type CheckPhoneNumberResponse struct {
	PhoneOperator string `json:"phoneOperator"`
}

// CheckReferralStatusResponse Ответ с результатом проверки статуса реферальной программы
type CheckReferralStatusResponse struct {
	// IsAvailable Доступна ли реферальная программа для пользователя
	IsAvailable bool `json:"isAvailable"`

	// IsReferral Признак реферала
	IsReferral bool `json:"isReferral"`

	// OnboardingDisplayStatus Статус отображения экранов онбординга
	OnboardingDisplayStatus bool `json:"onboardingDisplayStatus"`
}

// ConfirmInternalPaymentByPhoneNumberResponse defines model for ConfirmInternalPaymentByPhoneNumberResponse.
type ConfirmInternalPaymentByPhoneNumberResponse struct {
	// Status Статус транзакции
	Status PaymentsTransactionStatus `json:"status"`
}

// ConfirmPaymentByAccountResponse defines model for ConfirmPaymentByAccountResponse.
type ConfirmPaymentByAccountResponse struct {
	// Message Информация из бэк по статусу
	Message string `json:"message"`

	// Status Статус операции
	Status string `json:"status"`
}

// ConfirmPaymentForMobileResponse defines model for ConfirmPaymentForMobileResponse.
type ConfirmPaymentForMobileResponse struct {
	// Status Статус транзакции
	Status PaymentsTransactionStatus `json:"status"`
}

// ConfirmSignDocumentsResponse defines model for ConfirmSignDocumentsResponse.
type ConfirmSignDocumentsResponse struct {
	// Documents Подписанные документы
	Documents []Document `json:"documents"`
}

// CreateDepositOfferResp defines model for CreateDepositOfferResp.
type CreateDepositOfferResp struct {
	// CalculatedProfit Рассчитанная доходность в указанной валюте
	CalculatedProfit float64 `json:"calculatedProfit"`

	// CurrencyCode Короткий код валюты (например: KZT, USD, EUR)
	CurrencyCode string `json:"currencyCode"`

	// Documents Список документов
	Documents []DepositDocument `json:"documents"`

	// EffectiveProfitRate Ставка эффективной доходности
	EffectiveProfitRate float64 `json:"effectiveProfitRate"`

	// ProfitRate Ставка доходности
	ProfitRate float64 `json:"profitRate"`
}

// CreateInternalPaymentByPhoneNumberResponse defines model for CreateInternalPaymentByPhoneNumberResponse.
type CreateInternalPaymentByPhoneNumberResponse struct {
	// OtpRequired Признак нужно ли проводить проверки по отп
	OtpRequired bool             `json:"otpRequired"`
	OtpResponse *OtpFullResponse `json:"otpResponse,omitempty"`

	// Status Статус транзакции
	Status PaymentsTransactionStatus `json:"status"`

	// TransactionID Идентификатор транзакции в БД
	TransactionID openapi_types.UUID `json:"transactionID"`
}

// CreateLoanApplicationResp Объект ответа для успешного создания заявки на кредит.
type CreateLoanApplicationResp struct {
	// ApplicationID Уникальный идентификатор, присвоенный созданной заявке на кредит.
	ApplicationID string `json:"applicationID"`

	// ApplicationStatus Статус заявки на кредит.
	ApplicationStatus string `json:"applicationStatus"`
}

// CreatePaymentByAccountResponse defines model for CreatePaymentByAccountResponse.
type CreatePaymentByAccountResponse struct {
	// Message Информация из бэк по статусу
	Message string `json:"message"`

	// OtpRequired Признак нужно ли проводить проверки по отп
	OtpRequired bool             `json:"otpRequired"`
	OtpResponse *OtpFullResponse `json:"otpResponse,omitempty"`

	// Status Статус операции
	Status string `json:"status"`

	// TransactionID Идентификатор транзакции в БД
	TransactionID openapi_types.UUID `json:"transactionID"`
}

// CreatePaymentForMobileResponse defines model for CreatePaymentForMobileResponse.
type CreatePaymentForMobileResponse struct {
	// OtpRequired Признак нужно ли проводить проверки по отп
	OtpRequired bool             `json:"otpRequired"`
	OtpResponse *OtpFullResponse `json:"otpResponse,omitempty"`

	// Status Статус транзакции
	Status PaymentsTransactionStatus `json:"status"`

	// TransactionID Идентификатор транзакции в БД
	TransactionID openapi_types.UUID `json:"transactionID"`
}

// CreatePaymentKaspiQRResponse defines model for CreatePaymentKaspiQRResponse.
type CreatePaymentKaspiQRResponse struct {
	// Status Статус транзакции
	Status PaymentsTransactionStatus `json:"status"`

	// TransactionID Идентификатор транзакции в БД
	TransactionID openapi_types.UUID `json:"transactionID"`
}

// CreateSelfTransferResponse defines model for CreateSelfTransferResponse.
type CreateSelfTransferResponse struct {
	// Status Статус транзакции
	Status PaymentsTransactionStatus `json:"status"`

	// TransactionID Идентификатор транзакции в БД
	TransactionID *openapi_types.UUID `json:"transactionID,omitempty"`
}

// CreditData Данные о кредите
type CreditData struct {
	// Amount Сумма
	Amount Money `json:"amount"`

	// Label Текст о кредите
	Label string `json:"label"`
}

// CurrencyCode Код валюты
type CurrencyCode string

// DepositAccount defines model for DepositAccount.
type DepositAccount struct {
	// ID UUID идентификатор депозита в базе данных
	ID string `json:"ID"`

	// AccountNum Номер депозитного счета (accountCode в Колвире)
	AccountNum string `json:"accountNum"`

	// AgreementCode Номер депозитного договора (code в Колвире)
	AgreementCode string `json:"agreementCode"`

	// Balance Актуальный баланс в валюте депозитного счета (balance в Колвире)
	Balance float64 `json:"balance"`

	// Currency Валюта депозитного счета (currency в Колвире)
	Currency DepositAccountCurrency `json:"currency"`

	// DepositEndDate Дата окончания действия договора
	DepositEndDate openapi_types.Date `json:"depositEndDate"`

	// IsReplenishable Доступно ли пополнение
	IsReplenishable bool `json:"isReplenishable"`

	// IsWithdrawal Доступно ли снятие
	IsWithdrawal bool `json:"isWithdrawal"`

	// NearestPayDate Следующая дата выплаты процентов (пока что, если даты нет то пишем 1970-01-01)
	NearestPayDate *openapi_types.Date `json:"nearestPayDate"`

	// PayedAmount Общая сумма выплаченных процентов
	PayedAmount float64 `json:"payedAmount"`

	// ProfitRate Ставка доходности (например: 0.15 = 15%)
	ProfitRate float64 `json:"profitRate"`
}

// DepositAccountCurrency Валюта депозитного счета (currency в Колвире)
type DepositAccountCurrency string

// DepositCondition defines model for DepositCondition.
type DepositCondition struct {
	// UID ID условия
	UID openapi_types.UUID `json:"UID"`

	// CalculatedProfit Рассчитанная доходность в указанной валюте
	CalculatedProfit float64 `json:"calculatedProfit"`

	// CurrencyCode Короткий код валюты (например: KZT, USD, EUR)
	CurrencyCode string `json:"currencyCode"`

	// EffectiveProfitRate Ставка эффективной доходности
	EffectiveProfitRate float64 `json:"effectiveProfitRate"`

	// IsDefault Если true, то данное условие используется по-умолчанию
	IsDefault bool `json:"isDefault"`

	// PayoutMethod Метод выплаты доходности
	PayoutMethod DepositPayoutMethod `json:"payoutMethod"`

	// ProfitRate Ставка доходности
	ProfitRate float64 `json:"profitRate"`

	// TermMonths Срок продукта в месяцах (например: 3, 6, 9, 12, 24, 36)
	TermMonths uint32 `json:"termMonths"`
}

// DepositConditionProfitResp defines model for DepositConditionProfitResp.
type DepositConditionProfitResp struct {
	// DepositConditions Массив с информацией о доходности по выбранному продукту/сроку/валюте
	DepositConditions []DepositCondition `json:"depositConditions"`
}

// DepositDocument defines model for DepositDocument.
type DepositDocument struct {
	// Id ID документа из БД (documents)
	Id string `json:"id"`

	// IsSignable Необходимость подписать документ.
	IsSignable *bool `json:"isSignable,omitempty"`

	// Link Ссылка S3
	Link string `json:"link"`

	// Title Название документа для фронта
	Title string `json:"title"`

	// Type Тип документа. Для подписания нужны типы из примера.
	Type string `json:"type"`
}

// DepositPayoutMethod Метод выплаты доходности
type DepositPayoutMethod string

// DeviceBinding defines model for DeviceBinding.
type DeviceBinding struct {
	DeviceBindingReference *string `json:"deviceBindingReference,omitempty"`
}

// DeviceInfo defines model for DeviceInfo.
type DeviceInfo struct {
	// AppVersion Версия приложения
	AppVersion string `json:"appVersion"`

	// DeviceModel Модель устройства
	DeviceModel string `json:"deviceModel"`

	// InstallationID Идентификатор установки приложения
	InstallationID openapi_types.UUID `json:"installationID"`

	// SystemType Тип операционной системы
	SystemType DeviceInfoSystemType `json:"systemType"`

	// SystemVersion Версия операционной системы
	SystemVersion string `json:"systemVersion"`
}

// DeviceInfoSystemType Тип операционной системы
type DeviceInfoSystemType string

// DeviceInformation defines model for DeviceInformation.
type DeviceInformation struct {
	IMEI                     *string                           `json:"IMEI,omitempty"`
	Brand                    *string                           `json:"brand,omitempty"`
	DeviceId                 *string                           `json:"deviceId,omitempty"`
	DeviceName               *string                           `json:"deviceName,omitempty"`
	DeviceParentId           *string                           `json:"deviceParentId,omitempty"`
	FirmwareVersion          *string                           `json:"firmwareVersion,omitempty"`
	FourLastDigitPhoneNumber *string                           `json:"fourLastDigitPhoneNumber,omitempty"`
	Language                 *string                           `json:"language,omitempty"`
	Manufacturer             *string                           `json:"manufacturer,omitempty"`
	Model                    *string                           `json:"model,omitempty"`
	NetworkOperator          *string                           `json:"networkOperator,omitempty"`
	NetworkType              *DeviceInformationNetworkType     `json:"networkType,omitempty"`
	OsVersion                *string                           `json:"osVersion,omitempty"`
	PhoneNumber              *string                           `json:"phoneNumber,omitempty"`
	SerialNumber             *string                           `json:"serialNumber,omitempty"`
	SimSerialNumber          *string                           `json:"simSerialNumber,omitempty"`
	TimeZone                 *string                           `json:"timeZone,omitempty"`
	TimeZoneSetting          *DeviceInformationTimeZoneSetting `json:"timeZoneSetting,omitempty"`
	TokenStorageId           *string                           `json:"tokenStorageId,omitempty"`
	TokenStorageType         *string                           `json:"tokenStorageType,omitempty"`
}

// DeviceInformationNetworkType defines model for DeviceInformation.NetworkType.
type DeviceInformationNetworkType string

// DeviceInformationTimeZoneSetting defines model for DeviceInformation.TimeZoneSetting.
type DeviceInformationTimeZoneSetting string

// DictDocument defines model for DictDocument.
type DictDocument struct {
	// Data Данные документа справочника в виде объекта JSON (зависит от структуры справочника)
	Data map[string]interface{} `json:"data"`

	// DictId Идентификатор справочника к которому относится документ
	DictId openapi_types.UUID `json:"dict_id"`

	// Id Идентификатор документа справочника
	Id *openapi_types.UUID `json:"id,omitempty"`

	// Name Имя документа справочника (не обязательно)
	Name string `json:"name"`

	// OrderNum Поле для настройки ручной сортировки при необходимости
	OrderNum int `json:"order_num"`

	// Valid Соответствует-ли документ настройкам схемы для данного словаря
	Valid *bool `json:"valid,omitempty"`
}

// DictDocumentFilters defines model for DictDocumentFilters.
type DictDocumentFilters struct {
	// DictId ID или имя словаря
	DictId     string   `json:"dict_id"`
	Filters    []Filter `json:"filters"`
	Pagination *struct {
		// Count Размер страницы
		Count int32 `json:"count"`

		// Page Номер страницы начиная с 0
		Page int32 `json:"page"`
	} `json:"pagination,omitempty"`
	Sort *[]string `json:"sort,omitempty"`
}

// DictDocumentID defines model for DictDocumentID.
type DictDocumentID struct {
	// DocId ID документа
	DocId openapi_types.UUID `json:"doc_id"`
}

// DictDocumentList defines model for DictDocumentList.
type DictDocumentList struct {
	// List Список документов справочника
	List []DictDocument `json:"list"`
}

// DictDocumentOrderUpdate defines model for DictDocumentOrderUpdate.
type DictDocumentOrderUpdate struct {
	// DictId ID или имя словаря
	DictId string   `json:"dict_id"`
	DocIds []string `json:"doc_ids"`

	// OrderNum Индекс для сортировки
	OrderNum int32 `json:"order_num"`
}

// DictGetTreeLine defines model for DictGetTreeLine.
type DictGetTreeLine struct {
	// DictId ID или имя словаря
	DictId string `json:"dict_id"`

	// IdFieldName Имя поля в древовидной структуре, соответствующее id узла дерева
	IdFieldName string `json:"idFieldName"`

	// ParentIDFieldName Имя поля в древовидной структуре, соответствующее id узла-владельца для текущего
	ParentIDFieldName string `json:"parentIDFieldName"`

	// SelectFieldName Имя поля по значению которого мы выбираем первый элемент
	SelectFieldName string `json:"selectFieldName"`

	// SelectFieldValue Значение поля по которому мы выбираем первый элемент
	SelectFieldValue string `json:"selectFieldValue"`
}

// DictKATOMapFromTSOIDArgs defines model for DictKATOMapFromTSOIDArgs.
type DictKATOMapFromTSOIDArgs struct {
	// Building Здание (дом)
	Building *string `json:"building,omitempty"`

	// City Город (населенный пункт)
	City *string `json:"city,omitempty"`

	// Corpus Корпус
	Corpus   *string        `json:"corpus,omitempty"`
	Country  *DictTSOIDAddr `json:"country,omitempty"`
	District *DictTSOIDAddr `json:"district,omitempty"`

	// Flat Квартира
	Flat *string `json:"flat,omitempty"`

	// Lang Язык данных запроса
	Lang   DictKATOMapFromTSOIDArgsLang `json:"lang"`
	Region *DictTSOIDAddr               `json:"region,omitempty"`

	// Street Улица
	Street *string `json:"street,omitempty"`
}

// DictKATOMapFromTSOIDArgsLang Язык данных запроса
type DictKATOMapFromTSOIDArgsLang string

// DictKATOMapFromTSOIDResp defines model for DictKATOMapFromTSOIDResp.
type DictKATOMapFromTSOIDResp struct {
	// AddrType Тип адреса (001 - адрес рождения, 002 - адрес регистрации, 003 - адрес фактического проживания)
	AddrType *string `json:"addr_type,omitempty"`

	// Body Корпус
	Body *string `json:"body,omitempty"`

	// Building Строение
	Building *string `json:"building,omitempty"`

	// City Город
	City *string `json:"city,omitempty"`

	// CityTdc Код вида территории для города
	CityTdc *string `json:"city_tdc,omitempty"`

	// Country Страна
	Country *string `json:"country,omitempty"`

	// District Район
	District *string `json:"district,omitempty"`

	// DistrictTdc Код вида территории для района
	DistrictTdc *string `json:"district_tdc,omitempty"`

	// Flat Квартира
	Flat *string `json:"flat,omitempty"`

	// FlatTdc Код вида территории для квартиры
	FlatTdc *string `json:"flat_tdc,omitempty"`

	// Full Полный адрес
	Full *string `json:"full,omitempty"`

	// House Дом
	House *string `json:"house,omitempty"`

	// HouseOwnership Владение
	HouseOwnership *string `json:"house_ownership,omitempty"`

	// HouseTdc Код вида территории для дома
	HouseTdc *string `json:"house_tdc,omitempty"`

	// Index Почтовый индекс
	Index *string `json:"index,omitempty"`

	// Kato КАТО код для данного объекта
	Kato *string `json:"kato,omitempty"`

	// OwnType Тип владения (по умолчанию - пусто)
	OwnType *string `json:"own_type,omitempty"`

	// Region Область
	Region *string `json:"region,omitempty"`

	// RegionCity Город-регион
	RegionCity *string `json:"region_city,omitempty"`

	// RegionTdc Код вида территории для области
	RegionTdc *string `json:"region_tdc,omitempty"`

	// Settlement Населенный пункт
	Settlement *string `json:"settlement,omitempty"`

	// SettlementTdc Код вида территории для населенного пункта
	SettlementTdc *string `json:"settlement_tdc,omitempty"`

	// Street Улица
	Street *string `json:"street,omitempty"`

	// StreetTdc Код вида территории для улицы
	StreetTdc *string `json:"street_tdc,omitempty"`

	// Zone Микрорайон
	Zone *string `json:"zone,omitempty"`

	// ZoneTdc Код вида территории для микрорайона
	ZoneTdc *string `json:"zone_tdc,omitempty"`
}

// DictTSOIDAddr defines model for DictTSOIDAddr.
type DictTSOIDAddr struct {
	// Code КАТО код элемента адреса
	Code int64 `json:"code"`

	// NameKz Наименование элемента адреса на казахском языке
	NameKz string `json:"nameKz"`

	// NameRu Наименование элемента адреса на русском языке
	NameRu string `json:"nameRu"`
}

// Dictionary defines model for Dictionary.
type Dictionary struct {
	// Description Описание справочника
	Description *string `json:"description,omitempty"`

	// Id Идентификатор справочника
	Id *openapi_types.UUID `json:"id,omitempty"`

	// Name Имя справочника
	Name string `json:"name"`

	// Schema Описание структуры документов справочника в формате JSON schema (если не нужно, используем пустой JSON "{}")
	Schema *string `json:"schema,omitempty"`
}

// DictionaryID defines model for DictionaryID.
type DictionaryID struct {
	// DictId ID справочника
	DictId openapi_types.UUID `json:"dict_id"`
}

// DictionaryList defines model for DictionaryList.
type DictionaryList struct {
	// List Список справочников
	List []Dictionary `json:"list"`
}

// Document defines model for Document.
type Document struct {
	// ID Идентификатор документа
	ID openapi_types.UUID `json:"ID"`

	// FileLink Ссылка на физический файл
	FileLink string `json:"fileLink"`

	// Signed Был ли документ подписан пользователем
	Signed bool `json:"signed"`

	// Title Название документа
	Title string `json:"title"`

	// Type Тип (шаблон) документа
	Type DocumentType `json:"type"`

	// Version Версия документа, которая соотносится с типом документа (версия шаблона)
	Version int `json:"version"`
}

// DocumentForSign Документ для подписи
type DocumentForSign struct {
	// ID Уникальный идентификатор документа
	ID string `json:"ID"`

	// FileLink Ссылка на физический файл
	FileLink string `json:"fileLink"`

	// Signed Был ли документ подписан пользователем
	Signed bool `json:"signed"`

	// Title Название документа
	Title string `json:"title"`

	// Type Тип (шаблон) документа
	Type string `json:"type"`

	// Version Версия документа, которая соотносится с типом документа (версия шаблона)
	Version int `json:"version"`
}

// DocumentForSignResp defines model for DocumentForSignResp.
type DocumentForSignResp struct {
	// Documents Список документов для подписи
	Documents []DocumentForSign `json:"documents"`
}

// DocumentType Тип (шаблон) документа
type DocumentType string

// Documents defines model for Documents.
type Documents struct {
	// ID Идентификатор документа
	ID openapi_types.UUID `json:"ID"`

	// FileLink Ссылка на физический файл
	FileLink string `json:"fileLink"`

	// Signed Был ли документ подписан пользователем
	Signed bool `json:"signed"`

	// Title Название документа
	Title string `json:"title"`

	// Type Тип документа
	Type string `json:"type"`

	// Version Версия документа, которая соотносится с типом документа (версия шаблона)
	Version int `json:"version"`
}

// DocumentsForSignResponse defines model for DocumentsForSignResponse.
type DocumentsForSignResponse struct {
	// Documents Документы на подпись
	Documents []Document `json:"documents"`
}

// EarlyRepayAmount Сумма ЧДП/ПДП
type EarlyRepayAmount struct {
	// CurrencyCode Валюта
	CurrencyCode string `json:"currencyCode"`

	// Value Сумма досрочного погашения
	Value string `json:"value"`
}

// EarlyRepayContract Оплата финансирования
type EarlyRepayContract struct {
	// Number Номер кредитного договора
	Number string `json:"number"`

	// Title Заголовок для документа
	Title string `json:"title"`
}

// EducationType defines model for EducationType.
type EducationType struct {
	// ID Идентификатор уровня образования
	ID string `json:"ID"`

	// Code Код уровня образования
	Code string `json:"code"`

	// Name Название уровня образования
	Name string `json:"name"`
}

// Email Электронная почта
type Email = string

// EmploymentType defines model for EmploymentType.
type EmploymentType struct {
	// ID Идентификатор типа занятости
	ID string `json:"ID"`

	// Code Код типа занятости
	Code string `json:"code"`

	// Name Название типа занятости
	Name string `json:"name"`
}

// ErrorReason defines model for ErrorReason.
type ErrorReason struct {
	// Code Код ошибки
	Code string `json:"code"`

	// Message Сообщение о причине отказа
	Message *string `json:"message,omitempty"`

	// Title Заголовок причины отказа
	Title string `json:"title"`
}

// ExternalBank defines model for ExternalBank.
type ExternalBank struct {
	// ID Уникальный идентификатор
	ID string `json:"ID"`

	// Instructions Инструкция для выгрузки выписки из банка в виде списка шагов, т.к. так проще отображать на UI
	Instructions string `json:"instructions"`

	// LogoUrl Логотип банка (ссылка на изображение)
	LogoUrl string `json:"logoUrl"`

	// Name Наименование банка для отображения на UI клиенту
	Name string `json:"name"`
}

// ExternalBankLoan defines model for ExternalBankLoan.
type ExternalBankLoan struct {
	// BankBIN БИН банка
	BankBIN string `json:"bankBIN"`

	// BankName Наименование банка
	BankName string `json:"bankName"`

	// ContractDate Дата договора
	ContractDate time.Time `json:"contractDate"`

	// ContractNumber Номер договора
	ContractNumber string `json:"contractNumber"`

	// Image Ссылка на изображение
	Image string `json:"image"`

	// OutstandingAmount Сумма задолженности по одному банку
	OutstandingAmount Money `json:"outstandingAmount"`

	// PaymentAmount Ежемесячный платеж по одному банку
	PaymentAmount Money `json:"paymentAmount"`
}

// FileUploadRequestBody defines model for FileUploadRequestBody.
type FileUploadRequestBody struct {
	// File Контент документ для загрузки
	File openapi_types.File `json:"file"`

	// Filename Имя файла вместе с расширением
	Filename string `json:"filename"`
}

// FileUploadResponse defines model for FileUploadResponse.
type FileUploadResponse struct {
	// ID ID загруженного файла
	ID string `json:"ID"`
}

// Filter defines model for Filter.
type Filter struct {
	// Field Поле данных
	Field *string   `json:"field,omitempty"`
	Group *[]Filter `json:"group,omitempty"`

	// Not Флаг использования обратного условия фильтра
	Not *bool `json:"not,omitempty"`

	// Operation Операция
	Operation string `json:"operation"`

	// Value Значение
	Value *string `json:"value,omitempty"`
}

// GetApprovedLoanAppStatusResp defines model for GetApprovedLoanAppStatusResp.
type GetApprovedLoanAppStatusResp struct {
	// ApplicationStatus Статус заявки на кредит.
	ApplicationStatus string `json:"applicationStatus"`

	// IsInProgress Признак наличия в процессе выполнения внутренних проверок
	IsInProgress bool `json:"isInProgress"`

	// NextStep Следующий шаг.
	NextStep *string      `json:"nextStep,omitempty"`
	Reason   *ErrorReason `json:"reason,omitempty"`
}

// GetAvailableAccountsResp defines model for GetAvailableAccountsResp.
type GetAvailableAccountsResp struct {
	// AvailableAccount Массив с информацией по активным счетам пользователя
	AvailableAccount []AvailableAccount `json:"availableAccount"`
}

// GetBankStatementV2Bank defines model for GetBankStatementV2Bank.
type GetBankStatementV2Bank struct {
	// ID БИК банка
	ID string `json:"ID"`

	// Instructions Инструкция для выгрузки выписки из банка
	Instructions *string `json:"instructions,omitempty"`

	// IsStatementValid Флаг корректного файла выписки
	IsStatementValid *bool `json:"isStatementValid,omitempty"`

	// LogoUrl Логотип банка (ссылка на изображение)
	LogoUrl string `json:"logoUrl"`

	// Name Наименование банка
	Name string `json:"name"`

	// StatementName Наименование файла выписки (возвращается только при наличии некорректного файла)
	StatementName *string `json:"statementName,omitempty"`
}

// GetBankStatementV2Instruction Инструкция
type GetBankStatementV2Instruction struct {
	// Description Описание инструкции
	Description string `json:"description"`

	// Title Заголовок инструкции
	Title string `json:"title"`
}

// GetBankStatementV2Response defines model for GetBankStatementV2Response.
type GetBankStatementV2Response struct {
	// Banks Информация по банкам (Справочник банков для загрузки выписки)
	Banks []GetBankStatementV2Bank `json:"banks"`

	// MaxFileSizeMb Максимальное количество мегабайт для выписки (из конфига, напр. "10")
	MaxFileSizeMb float32 `json:"maxFileSizeMb"`

	// PeriodMonths Период (количество месяцев), за который нужна выписка (из конфига, напр. "6")
	PeriodMonths float32 `json:"periodMonths"`

	// StatementHint Баннер с подсказкой для клиента о преимуществе выписки
	StatementHint GetBankStatementV2StatementHint `json:"statementHint"`
}

// GetBankStatementV2StatementHint Баннер с подсказкой для клиента о преимуществе выписки
type GetBankStatementV2StatementHint struct {
	// Description Описание
	Description string `json:"description"`

	// Instruction Инструкция
	Instruction GetBankStatementV2Instruction `json:"instruction"`

	// Title Заголовок
	Title string `json:"title"`
}

// GetDepositDetailResp defines model for GetDepositDetailResp.
type GetDepositDetailResp struct {
	// AccDepRet Счет для возврата вклада
	AccDepRet *string `json:"accDepRet"`

	// AccountNum Номер депозитного счета (accountCode в Колвире)
	AccountNum string `json:"accountNum"`

	// AgreementCode Номер депозитного договора (code в Колвире)
	AgreementCode string `json:"agreementCode"`

	// Balance Актуальный баланс в валюте депозитного счета (balance в Колвире)
	Balance float64 `json:"balance"`

	// BeginDate Дата открытия договора (fromDate в Колвире)
	BeginDate openapi_types.Date `json:"beginDate"`

	// Currency Валюта депозитного счета (currency в Колвире)
	Currency GetDepositDetailRespCurrency `json:"currency"`

	// DepositAmount Первоначальная сумма депозита (amount в Колвире)
	DepositAmount float64 `json:"depositAmount"`

	// Documents Список документов
	Documents []DepositDocument `json:"documents"`

	// EffectiveRate Эффективная ставка доходности (рассчитать от rate)
	EffectiveRate float64 `json:"effectiveRate"`

	// EndDate Дата окончания действия договора (toDate в Колвире)
	EndDate openapi_types.Date `json:"endDate"`

	// Id ID депозита
	Id string `json:"id"`

	// IsReplenishable Доступно ли пополнение
	IsReplenishable bool `json:"isReplenishable"`

	// IsWithdrawal Доступно ли снятие (всегда false)
	IsWithdrawal bool `json:"isWithdrawal"`

	// NearestPayDate Следующая дата выплаты процентов (пока что, если даты нет то пишем 1970-01-01)
	NearestPayDate *openapi_types.Date `json:"nearestPayDate"`

	// PayedAmount Общая сумма выплаченной доходности (payedAmount в Колвире)
	PayedAmount float64 `json:"payedAmount"`

	// PayoutMethod Метод выплаты доходности
	PayoutMethod DepositPayoutMethod `json:"payoutMethod"`

	// ProductCode Код продукта (code из раздела product)
	ProductCode string `json:"productCode"`

	// ProductName Название продукта (name из раздела product)
	ProductName string `json:"productName"`

	// Rate Доходность (rate в Колвире, конвертировать xx.x в 0.xxx)
	Rate float64 `json:"rate"`

	// ReplenishableDays Сколько дней еще доступно пополнение
	ReplenishableDays int32 `json:"replenishableDays"`

	// TermMonth Срок депозита в месяцах (term в Колвире)
	TermMonth int32 `json:"termMonth"`
}

// GetDepositDetailRespCurrency Валюта депозитного счета (currency в Колвире)
type GetDepositDetailRespCurrency string

// GetEducationTypesResp defines model for GetEducationTypesResp.
type GetEducationTypesResp struct {
	// EducationTypes Список справочников по типам образования
	EducationTypes []EducationType `json:"educationTypes"`
}

// GetEmploymentTypesResp defines model for GetEmploymentTypesResp.
type GetEmploymentTypesResp struct {
	// EmploymentTypes Список справочников по типам занятости
	EmploymentTypes []EmploymentType `json:"employmentTypes"`
}

// GetInternalChecksResultResp defines model for GetInternalChecksResultResp.
type GetInternalChecksResultResp struct {
	// ApplicationStatus Статус заявки на кредит.
	ApplicationStatus string `json:"applicationStatus"`

	// IsInProgress Признак наличия в процессе выполнения внутренних проверок
	IsInProgress bool         `json:"isInProgress"`
	Reason       *ErrorReason `json:"reason,omitempty"`
}

// GetLoansDetailsEarlyRepayment defines model for GetLoansDetailsEarlyRepayment.
type GetLoansDetailsEarlyRepayment struct {
	AllDebtToPay     *GetLoansDetailsEarlyRepaymentAllDebtToPay     `json:"allDebtToPay,omitempty"`
	IsAvailable      bool                                           `json:"isAvailable"`
	PartialRepayment *GetLoansDetailsEarlyRepaymentPartialRepayment `json:"partialRepayment,omitempty"`
	Reason           *ErrorReason                                   `json:"reason,omitempty"`
}

// GetLoansDetailsEarlyRepaymentAllDebtToPay defines model for GetLoansDetailsEarlyRepaymentAllDebtToPay.
type GetLoansDetailsEarlyRepaymentAllDebtToPay struct {
	// Amount Сумма
	Amount Money  `json:"amount"`
	Label  string `json:"label"`
}

// GetLoansDetailsEarlyRepaymentPartialRepayment defines model for GetLoansDetailsEarlyRepaymentPartialRepayment.
type GetLoansDetailsEarlyRepaymentPartialRepayment struct {
	Limit       GetLoansDetailsEarlyRepaymentPartialRepaymentLimit       `json:"limit"`
	NextPayment GetLoansDetailsEarlyRepaymentPartialRepaymentNextPayment `json:"nextPayment"`
}

// GetLoansDetailsEarlyRepaymentPartialRepaymentLimit defines model for GetLoansDetailsEarlyRepaymentPartialRepaymentLimit.
type GetLoansDetailsEarlyRepaymentPartialRepaymentLimit struct {
	Hint string `json:"hint"`

	// MaxAmount Сумма
	MaxAmount Money `json:"maxAmount"`

	// MinAmount Сумма
	MinAmount Money `json:"minAmount"`
}

// GetLoansDetailsEarlyRepaymentPartialRepaymentNextPayment defines model for GetLoansDetailsEarlyRepaymentPartialRepaymentNextPayment.
type GetLoansDetailsEarlyRepaymentPartialRepaymentNextPayment struct {
	Hint string `json:"hint"`
}

// GetLoansDetailsResponse defines model for GetLoansDetailsResponse.
type GetLoansDetailsResponse struct {
	EarlyRepayment GetLoansDetailsEarlyRepayment `json:"earlyRepayment"`

	// Links Блок с URL
	Links Links `json:"links"`

	// Loan Информация по кредиту
	Loan LoanDetails `json:"loan"`

	// PaymentHint Подсказка в карточке кредита
	PaymentHint PaymentHintData `json:"paymentHint"`
}

// GetLocationsResp defines model for GetLocationsResp.
type GetLocationsResp struct {
	// Locations Список локаций
	Locations []Location `json:"locations"`
}

// GetProductInfoResp defines model for GetProductInfoResp.
type GetProductInfoResp struct {
	// Currencies Массив с информацией о доходности по выбранному продукту/сроку/валюте
	Currencies []ProductInfoCurrencies `json:"currencies"`

	// FinalReplenishableDate Дата, до которой будет доступно пополнение
	FinalReplenishableDate *openapi_types.Date `json:"finalReplenishableDate"`

	// IsReplenishable Доступно ли пополнение
	IsReplenishable bool `json:"isReplenishable"`

	// IsWithdrawable Доступно ли снятие (его всегда передаем false)
	IsWithdrawable bool `json:"isWithdrawable"`

	// MaxProfitRate Максимальная процентная ставка (готовый процент)
	MaxProfitRate string `json:"maxProfitRate"`

	// ProductCode Идентификатор продукта (colvir id)
	ProductCode string `json:"productCode"`

	// ProductName Название продукта
	ProductName string `json:"productName"`
}

// GetRefinancingInfoResp defines model for GetRefinancingInfoResp.
type GetRefinancingInfoResp struct {
	// Description Основное описание
	Description string `json:"description"`

	// Features Преимущества рефинансирования
	Features []RefinancingFeature `json:"features"`
	Steps    RefinancingSteps     `json:"steps"`

	// Subtitle Подзаголовок
	Subtitle *string `json:"subtitle,omitempty"`

	// Title Заголовок страницы
	Title string `json:"title"`
}

// GetRelationTypesResp defines model for GetRelationTypesResp.
type GetRelationTypesResp struct {
	// RelationTypes Список справочников по видам отношений к контактным лицам
	RelationTypes []RelationType `json:"relationTypes"`
}

// GetScoringResultRespExternalBankLoansInfo defines model for GetScoringResultRespExternalBankLoansInfo.
type GetScoringResultRespExternalBankLoansInfo struct {
	// Items Блок с информацией по каждому займу
	Items []ExternalBankLoan `json:"items"`
	Title string             `json:"title"`

	// TotalOutstandingAmount Общая сумма задолженности по всем займам в других банках
	TotalOutstandingAmount Money `json:"totalOutstandingAmount"`

	// TotalPaymentAmount Общая сумма ежемесячного платежа при рефинансировании
	TotalPaymentAmount Money `json:"totalPaymentAmount"`
}

// GetScoringResultRespRefinancingInfo defines model for GetScoringResultRespRefinancingInfo.
type GetScoringResultRespRefinancingInfo struct {
	Conditions            *RefinancingConditions                     `json:"conditions,omitempty"`
	ExternalBankLoansInfo *GetScoringResultRespExternalBankLoansInfo `json:"externalBankLoansInfo,omitempty"`
}

// GetSurveyAddress defines model for GetSurveyAddress.
type GetSurveyAddress struct {
	// Building Номер или название здания
	Building *string   `json:"building,omitempty"`
	District *KatoData `json:"district,omitempty"`

	// Flat Номер квартиры или офиса
	Flat *string `json:"flat"`

	// IsFull Флаг полностью заполненного адреса
	IsFull         bool      `json:"isFull"`
	Locality       *KatoData `json:"locality,omitempty"`
	Region         *KatoData `json:"region,omitempty"`
	Settlement     *KatoData `json:"settlement,omitempty"`
	SettlementArea *KatoData `json:"settlementArea,omitempty"`

	// Street Улица адреса
	Street *string `json:"street,omitempty"`
}

// GetSurveyContactPerson defines model for GetSurveyContactPerson.
type GetSurveyContactPerson struct {
	// FirstName Имя контактного лица
	FirstName string `json:"firstName"`

	// LastName Фамилия контактного лица
	LastName string `json:"lastName"`

	// Phone Номер телефона
	Phone   PhoneNumber  `json:"phone"`
	RelType RelationType `json:"relType"`
}

// GetSurveyResult defines model for GetSurveyResult.
type GetSurveyResult struct {
	Address *GetSurveyAddress `json:"address,omitempty"`

	// ApplicationID Идентификатор заявки
	ApplicationID *string `json:"applicationID,omitempty"`

	// Children Количество детей
	Children *int32 `json:"children,omitempty"`

	// ContactPersons Список контактных лиц
	ContactPersons *[]GetSurveyContactPerson `json:"contactPersons,omitempty"`
	EducationType  *EducationType            `json:"educationType,omitempty"`

	// Email Электронная почта
	Email   *Email          `json:"email,omitempty"`
	EmpType *EmploymentType `json:"empType,omitempty"`
}

// GetTaskDetailsResponse defines model for GetTaskDetailsResponse.
type GetTaskDetailsResponse struct {
	// CreatedAt Дата создания задачи
	CreatedAt time.Time `json:"created_at"`

	// Payload Полезная нагрузка задачи
	Payload map[string]interface{} `json:"payload"`

	// Status Статус задачи
	Status string `json:"status"`

	// TaskID Идентификатор задачи
	TaskID openapi_types.UUID `json:"taskID"`

	// TaskType Тип задачи
	TaskType string `json:"task_type"`

	// UpdatedAt Дата обновления задачи
	UpdatedAt time.Time `json:"updated_at"`
}

// GetTasksListResponse defines model for GetTasksListResponse.
type GetTasksListResponse struct {
	// Page Номер страницы
	Page int `json:"page"`

	// PageSize Размер страницы
	PageSize int `json:"page_size"`

	// Tasks Список задач
	Tasks []Task `json:"tasks"`

	// TotalCount Общее количество задач
	TotalCount int `json:"total_count"`
}

// Health defines model for Health.
type Health struct {
	// AltScoreBridge Статус сервиса altScoreBridge
	AltScoreBridge bool `json:"altScoreBridge"`

	// AmlBridge Статус сервиса amlBridge
	AmlBridge bool `json:"amlBridge"`

	// Antifraud Статус сервиса antifraud
	Antifraud bool `json:"antifraud"`

	// ApBridge Статус сервиса apBridge
	ApBridge bool `json:"apBridge"`

	// BalanceUpdater Статус сервиса balanceUpdater
	BalanceUpdater bool `json:"balanceUpdater"`

	// BitrixBridge Статус сервиса bitrixBridge
	BitrixBridge bool `json:"bitrixBridge"`

	// BsasBridge Статус сервиса bsasBridge
	BsasBridge bool `json:"bsasBridge"`

	// BtsBridge Статус сервиса btsBridge
	BtsBridge bool `json:"btsBridge"`

	// CardsAccounts Статус сервиса cardsAccounts
	CardsAccounts bool `json:"cardsAccounts"`

	// Collection Статус сервиса collection
	Collection bool `json:"collection"`

	// ColvirBridge Статус сервиса colvirBridge
	ColvirBridge bool `json:"colvirBridge"`

	// Crm Статус сервиса crm
	Crm bool `json:"crm"`

	// Deposits Статус сервиса deposits
	Deposits bool `json:"deposits"`

	// Dictionary Статус сервиса dictionary
	Dictionary bool `json:"dictionary"`

	// Documents Статус сервиса documents
	Documents bool `json:"documents"`

	// FileGuard Статус сервиса fileGuard
	FileGuard bool `json:"fileGuard"`

	// ForeignActivity Статус сервиса foreignActivity
	ForeignActivity bool `json:"foreignActivity"`

	// JiraBridge Статус сервиса jiraBridge
	JiraBridge bool `json:"jiraBridge"`

	// JuicyscoreBridge Статус сервиса juicyscoreBridge
	JuicyscoreBridge bool `json:"juicyscoreBridge"`

	// KaspiBridge Статус сервиса kaspiBridge
	KaspiBridge bool `json:"kaspiBridge"`

	// KeycloakProxy Статус сервиса keycloakProxy
	KeycloakProxy bool `json:"keycloakProxy"`

	// KgdBridge Статус сервиса kgdBridge
	KgdBridge bool `json:"kgdBridge"`

	// Liveness Статус сервиса liveness
	Liveness bool `json:"liveness"`

	// Loans Статус сервиса loans
	Loans bool `json:"loans"`

	// Notifications Статус сервиса notifications
	Notifications bool `json:"notifications"`

	// Otp Статус сервиса otp
	Otp bool `json:"otp"`

	// Payments Статус сервиса payments
	Payments bool `json:"payments"`

	// PaymentsSme Статус сервиса paymentsSme
	PaymentsSme bool `json:"paymentsSme"`

	// PkbBridge Статус сервиса pkbBridge
	PkbBridge bool `json:"pkbBridge"`

	// ProcessingBridge Статус сервиса processingBridge
	ProcessingBridge bool `json:"processingBridge"`

	// QazpostBridge Статус сервиса qazpostBridge
	QazpostBridge bool `json:"qazpostBridge"`

	// Referral Статус сервиса referral
	Referral bool `json:"referral"`

	// Scoring Статус сервиса scoring
	Scoring bool `json:"scoring"`

	// SeonBridge Статус сервиса seonBridge
	SeonBridge bool `json:"seonBridge"`

	// SmsBridge Статус сервиса smsBridge
	SmsBridge bool `json:"smsBridge"`

	// SprBridge Статус сервиса sprBridge
	SprBridge bool `json:"sprBridge"`

	// TaskManager Статус сервиса taskManager
	TaskManager bool `json:"taskManager"`

	// Tokenize Статус сервиса tokenize
	Tokenize bool `json:"tokenize"`

	// Users Статус сервиса users
	Users bool `json:"users"`
}

// HexString HEX-строка (0-9a-fA-F)
type HexString = string

// InterestAmountData Наценка
type InterestAmountData struct {
	// CurrencyCode Валюта
	CurrencyCode string `json:"currencyCode"`

	// Value Сумма наценки
	Value string `json:"value"`
}

// InvitedReferral Информация о приглашенном реферале
type InvitedReferral struct {
	// CompletedActionCount Кол-во выполненных целевых действий
	CompletedActionCount int `json:"completedActionCount"`

	// DaysLeftForReferral Кол-во дней до завершения периода выполнения целевых действий
	DaysLeftForReferral int `json:"daysLeftForReferral"`

	// LeftActionCount Кол-во оставшихся целевых действий
	LeftActionCount int `json:"leftActionCount"`

	// Name Имя и первая буква фамилии Реферала
	Name string `json:"name"`

	// ReferralStatus Статус Реферала. Возможные значения:
	// * **active** - срок выполнения целевых действий ещё не истек
	// * **completed** - срок выполнения целевых действий истек
	ReferralStatus ReferralStatus `json:"referralStatus"`

	// RewardDueDate Срок выплаты вознаграждения в формате ГГГГ-ММ-ДД. Если срок истек, то будет пустым
	RewardDueDate *string `json:"rewardDueDate,omitempty"`

	// RewardSum Сумма вознаграждения за выполненные целевые действия Реферала
	RewardSum float64 `json:"rewardSum"`

	// TotalActionCount Общее кол-во целевых действий
	TotalActionCount int `json:"totalActionCount"`
}

// JobGetStatus defines model for JobGetStatus.
type JobGetStatus struct {
	// Name Имя задачи синхронизации справочника
	Name string `json:"name"`
}

// JobRun defines model for JobRun.
type JobRun struct {
	// Lang Язык исходного файла
	Lang string `json:"lang"`

	// Name Имя задачи синхронизации справочника
	Name string `json:"name"`

	// Source URI исходного файла с данными справочника
	Source string `json:"source"`
}

// JobStatus defines model for JobStatus.
type JobStatus struct {
	Info map[string]string `json:"info"`
}

// JobStatusAll defines model for JobStatusAll.
type JobStatusAll = []map[string]string

// JobStop defines model for JobStop.
type JobStop struct {
	// Name Имя задачи синхронизации справочника
	Name string `json:"name"`
}

// KatoData defines model for KatoData.
type KatoData struct {
	// ID Идентификатор КАТО
	ID int32 `json:"ID"`

	// Code Код КАТО
	Code string `json:"code"`

	// Name Название КАТО
	Name string `json:"name"`

	// ParentID Идентификатор родительской КАТО
	ParentID int32 `json:"parentID"`
}

// LinkObject defines model for LinkObject.
type LinkObject struct {
	// Title Титры
	Title string `json:"title"`

	// Url Url
	Url string `json:"url"`
}

// Links Блок с URL
type Links struct {
	// EarlyRepayment URL сайта банка с информацией по досрочному погашению
	EarlyRepayment LinkObject `json:"earlyRepayment"`

	// Faq URL сайта банка с информацией по вопросам и ответам
	Faq LinkObject `json:"faq"`

	// Inquiry URL сайта банка с информацией по справке
	Inquiry LinkObject `json:"inquiry"`
}

// Loan defines model for Loan.
type Loan struct {
	// ID Уникальный идентификатор кредита
	ID string `json:"ID"`

	// Amount Сумма
	Amount *Money `json:"amount,omitempty"`

	// ApplicationDueDate Срок окончания одобренной заявки на кредит (5 дней)
	ApplicationDueDate *openapi_types.Date `json:"applicationDueDate,omitempty"`

	// HasDelay Флаг наличия просроченных платежей по кредиту
	HasDelay bool `json:"hasDelay"`

	// NextPayment Объект “Следующий платеж”
	NextPayment *LoansNextPayment `json:"nextPayment,omitempty"`

	// PercentPaid Поле для заполнения виджета оставшейся выплате по кредиту
	PercentPaid *int `json:"percentPaid,omitempty"`

	// ProductType Тип кредитного продукта
	ProductType string `json:"productType"`

	// Status Статус кредита
	Status       *string      `json:"status,omitempty"`
	StatusReason *ErrorReason `json:"statusReason,omitempty"`
}

// LoanAmount Холдер лимитов по сумме кредита
type LoanAmount struct {
	// MaxAmount Максимальный лимит кредита
	MaxAmount int `json:"maxAmount"`

	// MinAmount Минимальный лимит кредита
	MinAmount int `json:"minAmount"`
}

// LoanAppConditionsAmountData Сумма финансирования, одобренная СПР
type LoanAppConditionsAmountData struct {
	// CurrencyCode Валюта
	CurrencyCode string `json:"currencyCode"`

	// Value Сумма финансирования
	Value string `json:"value"`
}

// LoanAppDocumentType Тип (шаблон) документа
type LoanAppDocumentType string

// LoanApplicationDocument defines model for LoanApplicationDocument.
type LoanApplicationDocument struct {
	// ID Идентификатор документа
	ID openapi_types.UUID `json:"ID"`

	// FileLink Ссылка на физический файл
	FileLink string `json:"fileLink"`

	// Signed Был ли документ подписан пользователем
	Signed bool `json:"signed"`

	// Title Название документа
	Title string `json:"title"`

	// Type Тип (шаблон) документа
	Type LoanAppDocumentType `json:"type"`

	// Version Версия документа, которая соотносится с типом документа (версия шаблона)
	Version int `json:"version"`
}

// LoanApplicationDocumentsResp defines model for LoanApplicationDocumentsResp.
type LoanApplicationDocumentsResp struct {
	// Documents Документы сгенерированные по запросу
	Documents []LoanApplicationDocument `json:"documents"`
}

// LoanDetails Информация по кредиту
type LoanDetails struct {
	// Account Информация по счету
	Account Account `json:"account"`

	// Details Детали финансирования
	Details    LoanDetailsObject `json:"details"`
	HasOverdue bool              `json:"hasOverdue"`

	// NextPayment Информация о ближайшем платеже
	NextPayment NextPayment `json:"nextPayment"`

	// PaidInfo Информация для виджета
	PaidInfo PaidInfo `json:"paidInfo"`

	// ProductType Тип продукта LOAN - Кредит REFINANCE - Рефинансирование
	ProductType LoanDetailsProductType `json:"productType"`

	// Schedule График погашения
	Schedule []ScheduleItem `json:"schedule"`
}

// LoanDetailsProductType Тип продукта LOAN - Кредит REFINANCE - Рефинансирование
type LoanDetailsProductType string

// LoanDetailsContract Подписанный договор
type LoanDetailsContract struct {
	// DocID ID подписанного договора
	DocID string `json:"docID"`

	// FileLink Ссылка на подписанный договор, хранящийся в S3 в общей папке
	FileLink string `json:"fileLink"`

	// Number Номер кредитного договора
	Number string `json:"number"`

	// Title Заголовок для документа
	Title string `json:"title"`
}

// LoanDetailsObject Детали финансирования
type LoanDetailsObject struct {
	// Contract Подписанный договор
	Contract LoanDetailsContract `json:"contract"`

	// EndDate Дата окончания договора
	EndDate openapi_types.Date `json:"endDate"`

	// InterestAmount Сумма наценки на весь срок финансирования
	InterestAmount Money `json:"interestAmount"`

	// LoanAmount Сумма финансирования (на весь срок)
	LoanAmount Money `json:"loanAmount"`

	// StartDate Дата начала договора
	StartDate openapi_types.Date `json:"startDate"`

	// Term Срок кредита (в месяцах)
	Term Term `json:"term"`
}

// LoanPurposeData defines model for LoanPurposeData.
type LoanPurposeData struct {
	// ID Идентификатор объекта условий кредита
	ID string `json:"ID"`

	// DefaultPurpose Является ли цель дефолтной
	DefaultPurpose bool `json:"defaultPurpose"`

	// Description Описание цели кредита
	Description string `json:"description"`
}

// LoanTermInterestData Холдер условий кредита
type LoanTermInterestData struct {
	// ID Идентификатор объекта условий кредита
	ID string `json:"ID"`

	// DefaultInterest Признак срока кредита по подсветки по умолчанию
	DefaultInterest bool `json:"defaultInterest"`

	// Interest Процентная ставка кредита
	Interest int `json:"interest"`

	// Term Срок кредита (в месяцах)
	Term Term `json:"term"`
}

// LoansConfirmSignDocumentsResponse defines model for LoansConfirmSignDocumentsResponse.
type LoansConfirmSignDocumentsResponse struct {
	// Documents Подписанные документы
	Documents []Document `json:"documents"`
}

// LoansNextPayment Объект “Следующий платеж”
type LoansNextPayment struct {
	// Amount Сумма
	Amount *Money `json:"amount,omitempty"`

	// Date Дата ближайшего платежа
	Date openapi_types.Date `json:"date"`
}

// LoansResponse defines model for LoansResponse.
type LoansResponse struct {
	// Loans Список кредитов
	Loans []Loan `json:"loans"`
	Offer *Offer `json:"offer,omitempty"`
}

// Location defines model for Location.
type Location struct {
	// ID Идентификатор локации
	ID int32 `json:"ID"`

	// Code Код локации
	Code string `json:"code"`

	// Name Название локации
	Name string `json:"name"`

	// ParentID Идентификатор родительской локации
	ParentID int32 `json:"parentID"`
}

// Money Сумма
type Money struct {
	// CurrencyCode Код валюты
	CurrencyCode CurrencyCode `json:"currencyCode"`

	// Value Сумма
	Value string `json:"value"`
}

// NextPayment Информация о ближайшем платеже
type NextPayment struct {
	// Amount Сумма ближайшего платежа
	Amount Money `json:"amount"`

	// Date Дата ближайшего платежа
	Date openapi_types.Date `json:"date"`
}

// NextPaymentAmountData Ежемесячный платеж
type NextPaymentAmountData struct {
	// CurrencyCode Валюта
	CurrencyCode string `json:"currencyCode"`

	// Value Сумма ежемесячного платежа
	Value string `json:"value"`
}

// NextStep defines model for NextStep.
type NextStep string

// NotifyVirtualCardChangeReq defines model for NotifyVirtualCardChangeReq.
type NotifyVirtualCardChangeReq struct {
	// Action Действие над токеном (например, DEVICE_BOUND/UNBOUND и т.п.)
	Action                 *string `json:"action,omitempty"`
	DeviceBindingReference *string `json:"deviceBindingReference,omitempty"`

	// ErrorCode Код ошибки при неуспехе операции у эмитента/кошелька
	ErrorCode *int32 `json:"errorCode,omitempty"`

	// IsPrimary Признак основного токена
	IsPrimary bool `json:"isPrimary"`

	// IssuerCardRefId Уникальный идентификатор карты-источника у эмитента
	IssuerCardRefId     string  `json:"issuerCardRefId"`
	PublicKeyIdentifier *string `json:"publicKeyIdentifier,omitempty"`

	// Source Источник изменения состояния
	Source *string `json:"source,omitempty"`

	// TokenAssuranceLevel Уровень достоверности токена
	TokenAssuranceLevel *string `json:"tokenAssuranceLevel,omitempty"`

	// TokenInfo Доп. информация о токене (может быть зашифрована)
	TokenInfo      *string `json:"tokenInfo,omitempty"`
	TokenRequestor *struct {
		Id                       *string `json:"id,omitempty"`
		MerchantId               *string `json:"merchantId,omitempty"`
		Name                     *string `json:"name,omitempty"`
		OriginalTokenRequestorId *string `json:"originalTokenRequestorId,omitempty"`
		TspId                    *string `json:"tspId,omitempty"`
		WalletId                 *string `json:"walletId,omitempty"`
	} `json:"tokenRequestor,omitempty"`
	TokenStorageId *string `json:"tokenStorageId,omitempty"`

	// TokenType Тип токена (SE, HCE, COF, ECOM, QRC)
	TokenType *string `json:"tokenType,omitempty"`

	// TokenizeStatus Минимизированный статус токена для UI-иконки.
	TokenizeStatus TokenizeStatus `json:"tokenizeStatus"`

	// VirtualCardId Уникальный идентификатор виртуальной карты (токена)
	VirtualCardId string `json:"virtualCardId"`

	// WalletCardRefId Уникальный ID карты в кошельке (если предоставлен провайдером)
	WalletCardRefId *string `json:"walletCardRefId,omitempty"`

	// WalletProviderId Идентификатор провайдера кошелька (Thales)
	WalletProviderId string `json:"walletProviderId"`

	// WalletVirtualCardId Идентификатор виртуальной карты в кошельке (для ApplePay — DPANID)
	WalletVirtualCardId *string `json:"walletVirtualCardId,omitempty"`
}

// NotifyVirtualCardChangeResp defines model for NotifyVirtualCardChangeResp.
type NotifyVirtualCardChangeResp struct {
	// ErrorCode Код ошибки
	ErrorCode string `json:"errorCode"`

	// ErrorMessage Сообщение об ошибке
	ErrorMessage string `json:"errorMessage"`
}

// Offer defines model for Offer.
type Offer struct {
	// SubText Подтекст с информацией по сумме и сроку кредита (ограничение по символам - 20)
	SubText string `json:"subText"`

	// Text Текст с предложением оформить кредит (для баннера) (ограничение по символам - 20)
	Text string `json:"text"`

	// Title Заголовок для баннера (ограничение по символам - 20)
	Title string `json:"title"`
}

// OnboardingTextData Холдер текстовок
type OnboardingTextData struct {
	// Code Уникальный код текста
	Code string `json:"code"`

	// Description Текст для отображения
	Description string `json:"description"`

	// Image Ссылка в S3 на изображение
	Image string `json:"image"`
}

// OnboardingTextsResponse defines model for OnboardingTextsResponse.
type OnboardingTextsResponse struct {
	// Texts Массив текстов и изображений для onboarding экрана
	Texts []OnboardingTextData `json:"texts"`
}

// OtpFullResponse defines model for OtpFullResponse.
type OtpFullResponse struct {
	// AttemptID Идентификатор для валидации отп кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// AttemptsLeft Количество оставшихся попыток
	AttemptsLeft int `json:"attemptsLeft"`

	// AttemptsTimeout Количество секунд жизни попытки отп валидации
	AttemptsTimeout int `json:"attemptsTimeout"`

	// CodeChecksLeft Количество оставшихся попыток проверки кода отп
	CodeChecksLeft int `json:"codeChecksLeft"`

	// RetryTime Количество секунд до следующей отправки
	RetryTime int `json:"retryTime"`
}

// OtpResponse defines model for OtpResponse.
type OtpResponse struct {
	// AttemptID Идентификатор попытки для проверки кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// RetryTime Количество секунд до следующей отправки
	RetryTime int `json:"retryTime"`
}

// Overdue Просроченный платеж
type Overdue struct {
	// Days Количество дней просрочки
	Days int `json:"days"`

	// FineDebt Сумма пени
	FineDebt Money `json:"fineDebt"`

	// Hint Подсказка по просроченному платежу
	Hint string `json:"hint"`
}

// PaidInfo Информация для виджета
type PaidInfo struct {
	// PercentPaid Поле для заполнения виджета оставшейся суммы выплаты по кредиту
	PercentPaid string `json:"percentPaid"`

	// RemainingAmount Оставшаяся сумма выплаты по кредиту
	RemainingAmount Money `json:"remainingAmount"`

	// Text Текст под виджетом оставшейся суммы выплаты по кредиту
	Text string `json:"text"`
}

// PaymentDetails Детали платежа
type PaymentDetails struct {
	// BaseAmount Сумма финансирования (за 1 месяц)
	BaseAmount Money `json:"baseAmount"`

	// Date Дата платежа
	Date openapi_types.Date `json:"date"`

	// InterestAmount Сумма наценки (заполняется в зависимости от наличия просрочки)
	InterestAmount Money `json:"interestAmount"`

	// Overdue Просроченный платеж
	Overdue *Overdue `json:"overdue,omitempty"`
}

// PaymentHintData Подсказка в карточке кредита
type PaymentHintData struct {
	// InfoUrl URL сайта банка с информацией по способам внесения платежей
	InfoUrl string `json:"infoUrl"`

	// Text Текст подсказки
	Text string `json:"text"`

	// Title Заголовок подсказки
	Title string `json:"title"`
}

// PaymentsCheckAccountIinResponse defines model for PaymentsCheckAccountIinResponse.
type PaymentsCheckAccountIinResponse struct {
	// AdditionalIndividualType Дополнительный тип для ФЛ если есть
	AdditionalIndividualType *AdditionalIndividualType `json:"additionalIndividualType,omitempty"`

	// BankBic БИК банка
	BankBic string `json:"bankBic"`

	// BankName Название банка
	BankName string `json:"bankName"`

	// Name Наименование ЮЛ/ФЛ
	Name string `json:"name"`

	// TaxPayerType Тип налогоплательщика
	TaxPayerType TaxPayerType `json:"taxPayerType"`
}

// PaymentsGetHistoryItem defines model for PaymentsGetHistoryItem.
type PaymentsGetHistoryItem struct {
	// ActualClientCountry Страна фактического клиента (опционально)
	ActualClientCountry *string `json:"actualClientCountry,omitempty"`

	// ActualClientIinBin ИИН/БИН фактического клиента (опционально)
	ActualClientIinBin *string `json:"actualClientIinBin,omitempty"`

	// ActualClientName Имя фактического клиента (опционально)
	ActualClientName *string `json:"actualClientName,omitempty"`

	// ActualCounterpartyCountry Страна фактического контрагента (опционально)
	ActualCounterpartyCountry *string `json:"actualCounterpartyCountry,omitempty"`

	// ActualCounterpartyIinBin ИИН/БИН фактического контрагента (опционально)
	ActualCounterpartyIinBin *string `json:"actualCounterpartyIinBin,omitempty"`

	// ActualCounterpartyName Имя фактического контрагента (опционально)
	ActualCounterpartyName *string `json:"actualCounterpartyName,omitempty"`

	// Amount Сумма платежа
	Amount float64 `json:"amount"`

	// ClientAccount Номер счёта клиента
	ClientAccount string `json:"clientAccount"`

	// ClientBankBic БИК банка
	ClientBankBic string `json:"clientBankBic"`

	// ClientBankName Наименование банка клиента
	ClientBankName string `json:"clientBankName"`

	// ClientIinBin ИИН/БИН клиента
	ClientIinBin string `json:"clientIinBin"`

	// ClientName Наименование клиента
	ClientName string `json:"clientName"`

	// CounterpartyAccount Счёт контрагента
	CounterpartyAccount string `json:"counterpartyAccount"`

	// CounterpartyBankBic БИК банка контрагента
	CounterpartyBankBic string `json:"counterpartyBankBic"`

	// CounterpartyBankName Название банка контрагента
	CounterpartyBankName string `json:"counterpartyBankName"`

	// CounterpartyIinBin ИИН/БИН контрагента
	CounterpartyIinBin string `json:"counterpartyIinBin"`

	// CounterpartyKbe КБЕ контрагента
	CounterpartyKbe string `json:"counterpartyKbe"`

	// CounterpartyName Название контрагента
	CounterpartyName string `json:"counterpartyName"`

	// Currency Валюта платежа
	Currency string `json:"currency"`

	// Date Дата платежа
	Date openapi_types.Date `json:"date"`

	// Knp Код назначения платежа (КНП)
	Knp string `json:"knp"`

	// PaymentDetails Детали платежа
	PaymentDetails string `json:"paymentDetails"`

	// ValueDate Дата исполнения операции
	ValueDate *openapi_types.Date `json:"valueDate,omitempty"`
}

// PaymentsGetHistoryResponse defines model for PaymentsGetHistoryResponse.
type PaymentsGetHistoryResponse struct {
	// Payments Список элементов истории платежей
	Payments *[]PaymentsGetHistoryItem `json:"payments,omitempty"`
}

// PaymentsGetTransactionByIDResponse defines model for PaymentsGetTransactionByIDResponse.
type PaymentsGetTransactionByIDResponse struct {
	// Amount Сумма операции
	Amount float64 `json:"amount"`

	// BeneficiaryAccount Номер счёта получателя
	BeneficiaryAccount *string `json:"beneficiaryAccount,omitempty"`

	// BeneficiaryIinBin ИИН/БИН получателя
	BeneficiaryIinBin *string `json:"beneficiaryIinBin,omitempty"`

	// BeneficiaryName Наименование получателя
	BeneficiaryName *string `json:"beneficiaryName,omitempty"`

	// Commission Коммиссия
	Commission float64 `json:"commission"`

	// Currency Валюта операции
	Currency string `json:"currency"`

	// Direction Тип операции
	Direction PaymentsOperationType `json:"direction"`

	// Kbe Код бенефициара
	Kbe *string `json:"kbe,omitempty"`

	// Knp Код назначения платежа (КНП)
	Knp string `json:"knp"`

	// PayerAccount Номер счёта отправителя
	PayerAccount *string `json:"payerAccount,omitempty"`

	// PayerIinBin ИИН/БИН отправителя
	PayerIinBin *string `json:"payerIinBin,omitempty"`

	// PayerName Наименование отправителя
	PayerName *string `json:"payerName,omitempty"`

	// PhoneNumber Номер телефона
	PhoneNumber *string `json:"phoneNumber,omitempty"`

	// PhoneOperatorName Наименование мобильного оператора
	PhoneOperatorName *string `json:"phoneOperatorName,omitempty"`

	// Status Статус транзакции
	Status PaymentsTransactionStatus `json:"status"`

	// TransactionDate Дата операции
	TransactionDate time.Time `json:"transactionDate"`

	// TransactionDetails Детали транзакции
	TransactionDetails string `json:"transactionDetails"`

	// TransactionID Идентификатор транзакции
	TransactionID string `json:"transactionID"`

	// TransactionNumber Пользовательский номер транзакции
	TransactionNumber string `json:"transactionNumber"`

	// TransactionType Внутренний тип перевода
	TransactionType TransactionType `json:"transactionType"`

	// ValueDate Дата исполнения операции
	ValueDate *time.Time `json:"valueDate,omitempty"`
}

// PaymentsGetTransactionReceiptResponse defines model for PaymentsGetTransactionReceiptResponse.
type PaymentsGetTransactionReceiptResponse struct {
	// FileLink Ссылка на файл
	FileLink string `json:"fileLink"`

	// Title Наименование документа
	Title string `json:"title"`
}

// PaymentsGetTransactionsResponse defines model for PaymentsGetTransactionsResponse.
type PaymentsGetTransactionsResponse struct {
	// Limit Лимит количества возвращаемых транзакций
	Limit int64 `json:"limit"`

	// Offset Смещение для пагинации
	Offset int64 `json:"offset"`

	// StartDate Начальная дата для фильтрации транзакций
	StartDate time.Time `json:"startDate"`

	// TotalCount Общее количество транзакций
	TotalCount int64 `json:"totalCount"`

	// Transactions Список транзакций
	Transactions []PaymentsTransaction `json:"transactions"`
}

// PaymentsOperationType Тип операции
type PaymentsOperationType string

// PaymentsTransaction defines model for PaymentsTransaction.
type PaymentsTransaction struct {
	// AccountNumber Номер cчёта
	AccountNumber string `json:"accountNumber"`

	// Amount Сумма транзакции
	Amount float64 `json:"amount"`

	// Counterparty Контрагент
	Counterparty string `json:"counterparty"`

	// Currency Валюта транзакции
	Currency string `json:"currency"`

	// Direction Тип операции
	Direction PaymentsOperationType `json:"direction"`

	// Status Статус транзакции
	Status PaymentsTransactionStatus `json:"status"`

	// TransactionDate Дата операции
	TransactionDate openapi_types.Date `json:"transactionDate"`

	// TransactionID Уникальный идентификатор транзакции
	TransactionID string `json:"transactionID"`

	// TransactionType Внутренний тип перевода
	TransactionType TransactionType `json:"transactionType"`
}

// PaymentsTransactionStatus Статус транзакции
type PaymentsTransactionStatus string

// PhoneNumber Номер телефона
type PhoneNumber = string

// PostEarlyRepayResp defines model for PostEarlyRepayResp.
type PostEarlyRepayResp struct {
	// Contract Оплата финансирования
	Contract *EarlyRepayContract `json:"contract,omitempty"`

	// RepayAmount Сумма ЧДП/ПДП
	RepayAmount EarlyRepayAmount `json:"repayAmount"`
}

// PostEdsBtsDataResp defines model for PostEdsBtsDataResp.
type PostEdsBtsDataResp struct {
	// Amount Сумма
	Amount Money `json:"amount"`

	// ProductType Тип продукта (обычное финансирование, рефинансирование)
	ProductType string `json:"productType"`

	// Refinancing Данные о рефинансировании
	Refinancing *RefinancingData `json:"refinancing,omitempty"`

	// Subtitle Подзаголовок сообщения пользователю
	Subtitle string `json:"subtitle"`

	// Term Текст по сроку кредита
	Term string `json:"term"`

	// Title Заголовок сообщения пользователю
	Title string `json:"title"`
}

// PostIdentifyBtsDataResp defines model for PostIdentifyBtsDataResp.
type PostIdentifyBtsDataResp struct {
	// ApplicationStatus Статус заявки на кредит
	ApplicationStatus string       `json:"applicationStatus"`
	Reason            *ErrorReason `json:"reason,omitempty"`
}

// ProductInfoCurrencies defines model for ProductInfoCurrencies.
type ProductInfoCurrencies struct {
	// Currency Код валюты (например: KZT, USD, EUR)
	Currency string `json:"currency"`

	// MaxAmount Максимальная сумма на пополнение
	MaxAmount float64 `json:"maxAmount"`

	// MinAmount Минимальная сумма на пополнение
	MinAmount float64 `json:"minAmount"`
}

// PublicDocument defines model for PublicDocument.
type PublicDocument struct {
	// ID Идентификатор документа
	ID openapi_types.UUID `json:"ID"`

	// FileLink Ссылка на физический файл
	FileLink string `json:"fileLink"`

	// Signed Был ли документ подписан пользователем
	Signed bool `json:"signed"`

	// Title Название документа
	Title string `json:"title"`

	// Type Тип (шаблон) публичного документа
	Type PublicDocumentType `json:"type"`

	// Version Версия документа, которая соотносится с типом документа (версия шаблона)
	Version int `json:"version"`
}

// PublicDocumentType Тип (шаблон) публичного документа
type PublicDocumentType string

// PublishLoanAppResp defines model for PublishLoanAppResp.
type PublishLoanAppResp struct {
	// ApplicationStatus Статус заявки на кредит
	ApplicationStatus string `json:"applicationStatus"`
}

// QRSessionTerminationResponse defines model for QRSessionTerminationResponse.
type QRSessionTerminationResponse struct {
	Result string `json:"result"`
}

// QRTokenResponse defines model for QRTokenResponse.
type QRTokenResponse struct {
	// MerchantName Наименование продавца
	MerchantName *string `json:"merchantName,omitempty"`

	// PaymentAmount Сумма платежа
	PaymentAmount *string `json:"paymentAmount,omitempty"`

	// PaymentData Объект содержащий описание всех дополнительных полей
	PaymentData *struct {
		// Parameters Массив параметров
		Parameters *[]struct {
			// Name Описание value на языке локализации
			Name *string `json:"name,omitempty"`

			// Options Элементы выпадающего списка
			Options *[]struct {
				// Description Дополнительное описание
				Description *string `json:"description,omitempty"`

				// Key Идентификатор элемента
				Key *string `json:"key,omitempty"`

				// Label Отображаемое название
				Label *string `json:"label,omitempty"`
			} `json:"options,omitempty"`

			// Regex Параметры для валидации поля
			Regex *string `json:"regex,omitempty"`

			// Value Значение параметра. Пустое — значит, клиент должен заполнить значение
			Value *string `json:"value,omitempty"`
		} `json:"parameters,omitempty"`

		// ServiceId Идентификатор сервиса
		ServiceId *int32 `json:"serviceId,omitempty"`

		// ServiceName Наименование сервиса
		ServiceName *string `json:"serviceName,omitempty"`
	} `json:"paymentData,omitempty"`

	// PaymentID Идентификатор платежа в ПС
	PaymentID string `json:"paymentID"`

	// QrCodeType Тип QR токена
	QrCodeType string `json:"qrCodeType"`
}

// ReferralActions Общая информация по целевым действиям. Обязателен, если isReferral = true
type ReferralActions struct {
	// ActionDetails Подробная информация о **всех** целевых действиях Реферала.
	// По ожидающим целевым действиям (action_status = waiting) сортировка производится в следующем порядке:
	// 1. payment_purchase
	// 2. replenishment
	// 3. murabaha
	// 4. deposit
	ActionDetails []ActionDetail `json:"actionDetails"`

	// CompletedActionCount Кол-во выполненных целевых действий
	CompletedActionCount int `json:"completedActionCount"`

	// DaysLeft Кол-во дней до завершения периода выполнения целевых действий
	DaysLeft int `json:"daysLeft"`

	// LeftActionCount Кол-во оставшихся целевых действий
	LeftActionCount int `json:"leftActionCount"`

	// RewardDueDate Срок выплаты вознаграждения в формате ГГГГ-ММ-ДД. Если срок истек, то будет пустым
	RewardDueDate *string `json:"rewardDueDate,omitempty"`

	// RewardSum Сумма вознаграждения за выполненные целевые действия
	RewardSum float64 `json:"rewardSum"`

	// TotalActionCount Общее кол-во целевых действий
	TotalActionCount int `json:"totalActionCount"`
}

// ReferralProfileResponse Профиль реферальной программы пользователя
type ReferralProfileResponse struct {
	// InvitedReferralCount Кол-во приглашенных Рефералов
	InvitedReferralCount int `json:"invitedReferralCount"`

	// InvitedReferrals Общая информация по целевым действиям приглашенных Рефералов. Обязателен, если invited_referral_count > 0.
	// Для Рефералов, у которых срок выполнения целевых действий ещё не истёк (referral_status = active), сортировка списка производится по следующему принципу:
	// * в начале списка отображаются Рефералы с наименьшим количеством выполненных действий (completed_action_count)
	InvitedReferrals []InvitedReferral `json:"invitedReferrals"`

	// IsReferral Признак Реферала. Возможные значения:
	// * **true** - пользователь является рефералом
	// * **false** - пользователь **НЕ** является рефералом
	IsReferral bool `json:"isReferral"`

	// PaidRewardSum Фактически выплаченная сумма вознаграждений
	PaidRewardSum float64 `json:"paidRewardSum"`

	// ReferralActions Общая информация по целевым действиям. Обязателен, если isReferral = true
	ReferralActions ReferralActions `json:"referralActions"`

	// ReferralLink Уникальная реферальная ссылка пользователя
	ReferralLink string `json:"referralLink"`

	// ShortReferralLink Короткая уникальная реферальная ссылка клиента
	ShortReferralLink string `json:"shortReferralLink"`

	// WaitingRewardSum Общая сумма вознаграждений, ожидающая выплаты за:
	// * действия приглашенных Рефералов
	// * собственные целевые действия Клиента, если он сам является Рефералом
	WaitingRewardSum float64 `json:"waitingRewardSum"`
}

// ReferralStatus Статус Реферала. Возможные значения:
// * **active** - срок выполнения целевых действий ещё не истек
// * **completed** - срок выполнения целевых действий истек
type ReferralStatus string

// RefinancingConditions defines model for RefinancingConditions.
type RefinancingConditions struct {
	// CreditAmount Сумма зачисления (полученная клиентом на руки)
	CreditAmount Money `json:"creditAmount"`

	// InterestAmount Наценка
	InterestAmount Money `json:"interestAmount"`

	// PaymentAmount Ежемесячный платеж
	PaymentAmount Money `json:"paymentAmount"`

	// RefAmount Сумма на погашение кредитов в других банках
	RefAmount Money `json:"refAmount"`

	// Term Срок кредита (в месяцах)
	Term  Term   `json:"term"`
	Title string `json:"title"`

	// TotalAmount Общая сумма
	TotalAmount Money `json:"totalAmount"`
}

// RefinancingData Данные о рефинансировании
type RefinancingData struct {
	// Credit Данные о кредите
	Credit CreditData `json:"credit"`

	// Hint Подсказка для пользователя
	Hint string `json:"hint"`

	// Ref Данные о кредите
	Ref CreditData `json:"ref"`
}

// RefinancingFeature defines model for RefinancingFeature.
type RefinancingFeature struct {
	// Description Описание преимущества
	Description string `json:"description"`

	// Image Ссылка на изображение
	Image string `json:"image"`
}

// RefinancingStep defines model for RefinancingStep.
type RefinancingStep struct {
	// Description Описание шага
	Description string `json:"description"`
}

// RefinancingSteps defines model for RefinancingSteps.
type RefinancingSteps struct {
	// Items Список шагов
	Items []RefinancingStep `json:"items"`

	// Title Заголовок блока шагов
	Title string `json:"title"`
}

// RelationType defines model for RelationType.
type RelationType struct {
	// ID Идентификатор родства контактного лица
	ID string `json:"ID"`

	// Code Код родства контактного лица
	Code string `json:"code"`

	// Name Название родства контактного лица
	Name string `json:"name"`
}

// SaveSurveyAddress defines model for SaveSurveyAddress.
type SaveSurveyAddress struct {
	// Building Номер или название здания.
	Building string `json:"building"`

	// Flat Номер квартиры или офиса.
	Flat *string `json:"flat,omitempty"`

	// KatoCodes Коды КАТО
	KatoCodes []string `json:"katoCodes"`

	// Street Улица адреса.
	Street string `json:"street"`
}

// SaveSurveyContactPerson defines model for SaveSurveyContactPerson.
type SaveSurveyContactPerson struct {
	// FirstName Имя контактного лица.
	FirstName string `json:"firstName"`

	// LastName Фамилия контактного лица.
	LastName string `json:"lastName"`

	// Phone Номер телефона
	Phone PhoneNumber `json:"phone"`

	// RelID Идентификатор родства контактного лица.
	RelID string `json:"relID"`
}

// SaveSurveyResp defines model for SaveSurveyResp.
type SaveSurveyResp struct {
	// SurveyID Идентификатор анкеты пользователя.
	SurveyID string `json:"surveyID"`
}

// SaveUserExternalBankLoansResp defines model for SaveUserExternalBankLoansResp.
type SaveUserExternalBankLoansResp = map[string]interface{}

// ScheduleItem График погашения
type ScheduleItem struct {
	// Date Дата платежа
	Date openapi_types.Date `json:"date"`

	// Details Детали платежа
	Details PaymentDetails `json:"details"`

	// PaymentAmount Сумма платежа (Сумма финансирования (baseAmount) + Наценка (interestAmount))
	PaymentAmount Money `json:"paymentAmount"`

	// Status Статус платежа PLAN - К оплате END - Погашен OVERDUE - Просрочен
	Status ScheduleItemStatus `json:"status"`
}

// ScheduleItemStatus Статус платежа PLAN - К оплате END - Погашен OVERDUE - Просрочен
type ScheduleItemStatus string

// ScoringLoanConditions defines model for ScoringLoanConditions.
type ScoringLoanConditions struct {
	// Amount Сумма финансирования, одобренная СПР
	Amount LoanAppConditionsAmountData `json:"amount"`

	// Hint Текст под условиями предложения
	Hint string `json:"hint"`

	// InterestAmount Наценка
	InterestAmount InterestAmountData `json:"interestAmount"`

	// NextPaymentAmount Ежемесячный платеж
	NextPaymentAmount NextPaymentAmountData `json:"nextPaymentAmount"`

	// Term Срок кредита (в месяцах)
	Term Term `json:"term"`

	// Title Заголовок экрана
	Title string `json:"title"`
}

// ScoringResultResp defines model for ScoringResultResp.
type ScoringResultResp struct {
	// ApplicationStatus Статус заявки на кредит
	ApplicationStatus string                 `json:"applicationStatus"`
	LoanConditions    *ScoringLoanConditions `json:"loanConditions,omitempty"`

	// ProductType Тип продукта (LOAN/REFINANCING)
	ProductType *string                              `json:"productType,omitempty"`
	Reason      *ErrorReason                         `json:"reason,omitempty"`
	Refinancing *GetScoringResultRespRefinancingInfo `json:"refinancing,omitempty"`
}

// SignedDocumentsBatchResponse defines model for SignedDocumentsBatchResponse.
type SignedDocumentsBatchResponse struct {
	// Documents Подписанные документы
	Documents []Document `json:"documents"`
}

// Task defines model for Task.
type Task struct {
	CreatedAt *time.Time              `json:"created_at,omitempty"`
	Payload   *map[string]interface{} `json:"payload,omitempty"`
	Status    *string                 `json:"status,omitempty"`
	TaskID    *openapi_types.UUID     `json:"taskID,omitempty"`
	Type      *string                 `json:"type,omitempty"`
	UpdatedAt *time.Time              `json:"updated_at,omitempty"`
}

// TaxPayerType Тип налогоплательщика
type TaxPayerType string

// Term Срок кредита (в месяцах)
type Term = int

// TokenRequestor defines model for TokenRequestor.
type TokenRequestor struct {
	Id                       *string `json:"id,omitempty"`
	Name                     *string `json:"name,omitempty"`
	OriginalTokenRequestorId *string `json:"originalTokenRequestorId,omitempty"`
	TspId                    *string `json:"tspId,omitempty"`
	WalletId                 *string `json:"walletId,omitempty"`
}

// TokenizeStartReq defines model for TokenizeStartReq.
type TokenizeStartReq struct {
	// CardId ID карты, которую нужно токенизировать
	CardId openapi_types.UUID `json:"cardId"`

	// Wallet Целевой кошелёк
	Wallet Wallet `json:"wallet"`
}

// TokenizeStartResp defines model for TokenizeStartResp.
type TokenizeStartResp struct {
	// AuthCode Шестнадцатеричная строка зашифрованной полезной нагрузки с открытым ключом TSH / PKCS7.
	// Контент: AES-256/CBC/PKCS7Padding; ключ: RSA-OAEP-SHA-256 (MGF1 c SHA-256).
	// Соответствует полю OPC в ответе backend.
	AuthCode string `json:"authCode"`

	// EncryptedCardInfo HEX-строка (0-9a-fA-F)
	EncryptedCardInfo HexString `json:"encryptedCardInfo"`

	// IssuerId Идентификатор эмитента
	IssuerId string `json:"issuerId"`

	// Scheme Платёжная схема токенизации
	Scheme string `json:"scheme"`

	// UserAddress Адрес пользователя
	UserAddress *string `json:"userAddress"`
}

// TokenizeStateResp defines model for TokenizeStateResp.
type TokenizeStateResp struct {
	// IsTokenized Минимизированный статус токена для UI-иконки.
	IsTokenized TokenizeStatus `json:"isTokenized"`

	// Wallet Целевой кошелёк
	Wallet *Wallet `json:"wallet,omitempty"`
}

// TokenizeStatus Минимизированный статус токена для UI-иконки.
type TokenizeStatus string

// TransactionType Внутренний тип перевода
type TransactionType string

// UpdateLoanApplicationResp Объект ответа для успешного обновления заявки на кредит.
type UpdateLoanApplicationResp struct {
	// ApplicationID Уникальный идентификатор, присвоенный созданной заявке на кредит.
	ApplicationID string `json:"applicationID"`

	// ApplicationStatus Статус заявки на кредит.
	ApplicationStatus string `json:"applicationStatus"`
}

// UpdateUserLocaleResp defines model for UpdateUserLocaleResp.
type UpdateUserLocaleResp = map[string]interface{}

// UserAccount defines model for UserAccount.
type UserAccount struct {
	// ID Идентификатор счёта
	ID     string            `json:"ID"`
	Arrest UserAccountArrest `json:"arrest"`

	// AvailableBalance Доступный баланс (balance-plansum-partiallyDebtAmount)
	AvailableBalance float64 `json:"availableBalance"`

	// Balance Баланс (остаток счёта)
	Balance float64 `json:"balance"`

	// BalanceNatval Баланс счета в нац. валюте (KZT)
	BalanceNatval float64 `json:"balanceNatval"`

	// CloseDate Дата закрытия счёта
	CloseDate *openapi_types.Date `json:"closeDate,omitempty"`

	// Currency Валюта счёта
	Currency UserAccountCurrency `json:"currency"`

	// FinContractID Идентификатор фин. контракта в процессинге
	FinContractID *openapi_types.UUID `json:"finContractID,omitempty"`

	// FinContractStatus Статус фин. контракта в процессинге
	FinContractStatus *UserAccountFinContractStatus `json:"finContractStatus,omitempty"`

	// Iban Номер счета iban
	Iban string `json:"iban"`

	// IsFinContractOpened Признак открытого фин. контракта в процессинге для данного счета
	IsFinContractOpened bool `json:"isFinContractOpened"`

	// OpenDate Дата открытия счёта
	OpenDate openapi_types.Date `json:"openDate"`

	// PlanSum Плановые суммы
	PlanSum float64 `json:"planSum"`

	// Status Статус счёта
	Status UserAccountStatus `json:"status"`

	// Type Тип счёта
	Type UserAccountType `json:"type"`
}

// UserAccountCurrency Валюта счёта
type UserAccountCurrency string

// UserAccountFinContractStatus Статус фин. контракта в процессинге
type UserAccountFinContractStatus string

// UserAccountStatus Статус счёта
type UserAccountStatus string

// UserAccountType Тип счёта
type UserAccountType string

// UserAccountArrest defines model for UserAccountArrest.
type UserAccountArrest struct {
	// Blocking Признак полной или частичной блокировки, накладывает ограничения на определенную сумму - сумму ареста
	Blocking *bool `json:"blocking,omitempty"`
}

// UserAccountResponse defines model for UserAccountResponse.
type UserAccountResponse struct {
	Account UserAccount `json:"account"`
}

// UserCard defines model for UserCard.
type UserCard struct {
	ID                 openapi_types.UUID         `json:"ID"`
	AttachedAccountID  openapi_types.UUID         `json:"attachedAccountID"`
	CardClass          string                     `json:"cardClass"`
	CardType           UserCardCardType           `json:"cardType"`
	EmbossingName      string                     `json:"embossingName"`
	MaskedPAN          string                     `json:"maskedPAN"`
	PaymentSystem      UserCardPaymentSystem      `json:"paymentSystem"`
	Status             UserCardStatus             `json:"status"`
	TokenizationStatus UserCardTokenizationStatus `json:"tokenizationStatus"`
	Wallet             UserCardWallet             `json:"wallet"`
}

// UserCardCardType defines model for UserCard.CardType.
type UserCardCardType string

// UserCardPaymentSystem defines model for UserCard.PaymentSystem.
type UserCardPaymentSystem string

// UserCardStatus defines model for UserCard.Status.
type UserCardStatus string

// UserCardTokenizationStatus defines model for UserCard.TokenizationStatus.
type UserCardTokenizationStatus string

// UserCardWallet defines model for UserCard.Wallet.
type UserCardWallet string

// UserCardsResponse defines model for UserCardsResponse.
type UserCardsResponse struct {
	// Accounts Счета пользователя
	Accounts []UserAccount `json:"accounts"`
	Cards    *[]UserCard   `json:"cards,omitempty"`
}

// UserDepositsResponse defines model for UserDepositsResponse.
type UserDepositsResponse struct {
	// Deposits Список депозитных счетов пользователя
	Deposits []DepositAccount `json:"deposits"`
}

// UserExternalBankLoan defines model for UserExternalBankLoan.
type UserExternalBankLoan struct {
	// ContractNumber Номер контракта
	ContractNumber string `json:"contractNumber"`

	// Iban IBAN банка
	Iban string `json:"iban"`
}

// ValidationError Ошибка валидации данных в теле запроса
type ValidationError struct {
	// Error Код ошибки
	Error string `json:"error"`

	// Fields Объект с описанием ошибок валидации полей
	Fields map[string]string `json:"fields"`
}

// Wallet Целевой кошелёк
type Wallet string

// AcceptLanguage defines model for AcceptLanguage.
type AcceptLanguage string

// AccountIDPathParam defines model for AccountIDPathParam.
type AccountIDPathParam = openapi_types.UUID

// ApplicationIDPathParam defines model for ApplicationIDPathParam.
type ApplicationIDPathParam = openapi_types.UUID

// CalculateProfitAmount defines model for CalculateProfitAmount.
type CalculateProfitAmount = float64

// CalculateProfitPayoutMethod Метод выплаты доходности
type CalculateProfitPayoutMethod = DepositPayoutMethod

// CalculateProfitProfitRate defines model for CalculateProfitProfitRate.
type CalculateProfitProfitRate = float64

// CalculateProfitTermMonths defines model for CalculateProfitTermMonths.
type CalculateProfitTermMonths = uint32

// CardIDPathParam defines model for CardIDPathParam.
type CardIDPathParam = openapi_types.UUID

// DepositDetailsParams defines model for DepositDetailsParams.
type DepositDetailsParams = string

// DictDocumentIDPathParam defines model for DictDocumentIDPathParam.
type DictDocumentIDPathParam = openapi_types.UUID

// DictDocumentNamePathParam defines model for DictDocumentNamePathParam.
type DictDocumentNamePathParam = string

// DictionaryIDPathParam defines model for DictionaryIDPathParam.
type DictionaryIDPathParam = openapi_types.UUID

// DictionaryNamePathParam defines model for DictionaryNamePathParam.
type DictionaryNamePathParam = string

// DocumentIDPathParam defines model for DocumentIDPathParam.
type DocumentIDPathParam = openapi_types.UUID

// EntryPointHeader defines model for EntryPointHeader.
type EntryPointHeader = string

// GetProductInfo defines model for GetProductInfo.
type GetProductInfo = string

// OnboardingSourceQueryParam defines model for OnboardingSourceQueryParam.
type OnboardingSourceQueryParam string

// PaginationCountQueryParam defines model for PaginationCountQueryParam.
type PaginationCountQueryParam = int32

// PaginationPageQueryParam defines model for PaginationPageQueryParam.
type PaginationPageQueryParam = int32

// PaymentsGetHistoryClientIinBin defines model for PaymentsGetHistoryClientIinBin.
type PaymentsGetHistoryClientIinBin = string

// PaymentsGetTransactionByIDTransactionID defines model for PaymentsGetTransactionByIDTransactionID.
type PaymentsGetTransactionByIDTransactionID = openapi_types.UUID

// PaymentsGetTransactionsAccounts defines model for PaymentsGetTransactionsAccounts.
type PaymentsGetTransactionsAccounts = []string

// PaymentsGetTransactionsCards defines model for PaymentsGetTransactionsCards.
type PaymentsGetTransactionsCards = []string

// PaymentsGetTransactionsCounterparty defines model for PaymentsGetTransactionsCounterparty.
type PaymentsGetTransactionsCounterparty = string

// PaymentsGetTransactionsEndDate defines model for PaymentsGetTransactionsEndDate.
type PaymentsGetTransactionsEndDate = string

// PaymentsGetTransactionsLimit defines model for PaymentsGetTransactionsLimit.
type PaymentsGetTransactionsLimit = int64

// PaymentsGetTransactionsMaxAmount defines model for PaymentsGetTransactionsMaxAmount.
type PaymentsGetTransactionsMaxAmount = string

// PaymentsGetTransactionsMinAmount defines model for PaymentsGetTransactionsMinAmount.
type PaymentsGetTransactionsMinAmount = string

// PaymentsGetTransactionsOffset defines model for PaymentsGetTransactionsOffset.
type PaymentsGetTransactionsOffset = int64

// PaymentsGetTransactionsOperationType defines model for PaymentsGetTransactionsOperationType.
type PaymentsGetTransactionsOperationType string

// PaymentsGetTransactionsStartDate defines model for PaymentsGetTransactionsStartDate.
type PaymentsGetTransactionsStartDate = string

// RefinancingInfoReqStep defines model for RefinancingInfoReqStep.
type RefinancingInfoReqStep string

// TaskCreatedAfterQueryParam defines model for TaskCreatedAfterQueryParam.
type TaskCreatedAfterQueryParam = int64

// TaskCreatedBeforeQueryParam defines model for TaskCreatedBeforeQueryParam.
type TaskCreatedBeforeQueryParam = int64

// TaskIDPathParam defines model for TaskIDPathParam.
type TaskIDPathParam = openapi_types.UUID

// TaskPageQueryParam defines model for TaskPageQueryParam.
type TaskPageQueryParam = int64

// TaskPageSizeQueryParam defines model for TaskPageSizeQueryParam.
type TaskPageSizeQueryParam = int64

// TaskStatusQueryParam defines model for TaskStatusQueryParam.
type TaskStatusQueryParam string

// TaskTypeQueryParam defines model for TaskTypeQueryParam.
type TaskTypeQueryParam = string

// UserAgent defines model for UserAgent.
type UserAgent = string

// ParametersCardIDPathParam defines model for parameters-CardIDPathParam.
type ParametersCardIDPathParam = openapi_types.UUID

// RespEmpty defines model for RespEmpty.
type RespEmpty = map[string]interface{}

// AccountDocumentRequest defines model for AccountDocumentRequest.
type AccountDocumentRequest struct {
	// AccountID Идентификатор счета
	AccountID *openapi_types.UUID `json:"accountID,omitempty"`

	// Language Язык документа
	Language *string `json:"language,omitempty"`

	// PeriodFrom Начало периода (только для выписки по счету)
	PeriodFrom *openapi_types.Date `json:"periodFrom"`

	// PeriodTo Конец периода (только для выписки по счету)
	PeriodTo *openapi_types.Date `json:"periodTo"`
}

// ApplicationForSignRequest defines model for ApplicationForSignRequest.
type ApplicationForSignRequest struct {
	// Currencies Список валют, для которых будет сгенерирован документ
	Currencies *[]string `json:"currencies,omitempty"`

	// DocType Тип документа, который будет сгенерирован
	DocType string `json:"docType"`
}

// AuthRefreshBody defines model for AuthRefreshBody.
type AuthRefreshBody struct {
	DeviceInfo DeviceInfo `json:"deviceInfo"`

	// Phone Номер телефона
	Phone PhoneNumber `json:"phone"`

	// RefreshToken Токен обновления авторизации
	RefreshToken string `json:"refreshToken"`
}

// BatchDocsOtpBody defines model for BatchDocsOtpBody.
type BatchDocsOtpBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код подтверждения
	Code string `json:"code"`

	// DocIDs Идентификаторы подписываемых документов
	DocIDs []openapi_types.UUID `json:"docIDs"`
}

// CalculationBody defines model for CalculationBody.
type CalculationBody struct {
	// Amount Значение, внесенное клиентом по сумме кредита
	Amount int `json:"amount"`

	// PurposeID ID выбранной клиентом цели
	PurposeID string `json:"purposeID"`
}

// CheckClientByPhoneNumberRequest defines model for CheckClientByPhoneNumberRequest.
type CheckClientByPhoneNumberRequest struct {
	// PhoneNumber Номер телефона
	PhoneNumber string `json:"phoneNumber"`
}

// CheckPhoneNumberRequest defines model for CheckPhoneNumberRequest.
type CheckPhoneNumberRequest struct {
	// PhoneNumber Номер телефона
	PhoneNumber string `json:"phoneNumber"`
}

// ConfirmInternalPaymentByPhoneNumberRequest defines model for ConfirmInternalPaymentByPhoneNumberRequest.
type ConfirmInternalPaymentByPhoneNumberRequest struct {
	// AttemptID Идентификатор для валидации отп кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код валидации отп
	Code string `json:"code"`
}

// ConfirmPaymentByAccount defines model for ConfirmPaymentByAccount.
type ConfirmPaymentByAccount struct {
	// AttemptID Идентификатор для валидации отп кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код валидации отп
	Code string `json:"code"`
}

// ConfirmPaymentForMobileRequest defines model for ConfirmPaymentForMobileRequest.
type ConfirmPaymentForMobileRequest struct {
	// AttemptID Идентификатор для валидации отп кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код валидации отп
	Code string `json:"code"`
}

// ConfirmRequestBody defines model for ConfirmRequestBody.
type ConfirmRequestBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код подтверждения
	Code string `json:"code"`
}

// ConfirmSignDocumentsReqBody defines model for ConfirmSignDocumentsReqBody.
type ConfirmSignDocumentsReqBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код подтверждения
	Code string `json:"code"`
}

// CreateDepositOfferReqBody defines model for CreateDepositOfferReqBody.
type CreateDepositOfferReqBody struct {
	// AccountId ID счет списания в пользу депозита
	AccountId string `json:"accountId"`

	// Amount Сумма депозита
	Amount float64 `json:"amount"`

	// PayoutMethod Метод выплаты доходности
	PayoutMethod DepositPayoutMethod `json:"payoutMethod"`

	// RateId ID условия/рейта
	RateId string `json:"rateId"`
}

// CreateInternalPaymentByPhoneNumberRequest defines model for CreateInternalPaymentByPhoneNumberRequest.
type CreateInternalPaymentByPhoneNumberRequest struct {
	Amount string `json:"amount"`

	// BeneficiaryPhoneNumber Номер телефона клиента банка на счёт которого происходит перевод
	BeneficiaryPhoneNumber string `json:"beneficiaryPhoneNumber"`

	// Currency Валюта перевода
	Currency string `json:"currency"`

	// IdempotencyID Ключ идемпотентности
	IdempotencyID string `json:"idempotencyID"`

	// PayerAccountNumber Номер счёта с которого списываются деньги
	PayerAccountNumber string `json:"payerAccountNumber"`
}

// CreateLoanApplicationBody Объект запроса для создания новой заявки на кредит.
type CreateLoanApplicationBody struct {
	// Amount Запрашиваемая сумма кредита.
	Amount int `json:"amount"`

	// JuicySessionID ID сессии в системе JUICYSCORE
	JuicySessionID *string `json:"juicySessionID,omitempty"`

	// PurposeID Уникальный идентификатор, представляющий цель кредита (из справочника).
	PurposeID *string `json:"purposeID,omitempty"`

	// TermInterestID Уникальный идентификатор, представляющий условия кредита (из справочника).
	TermInterestID *string `json:"termInterestID,omitempty"`
}

// CreatePaymentByAccount defines model for CreatePaymentByAccount.
type CreatePaymentByAccount struct {
	// ActualBeneficiary Фактический получатель (ФИО/Наименование)
	ActualBeneficiary *string `json:"actualBeneficiary,omitempty"`

	// ActualBeneficiaryBinIIN ИИН/БИН фактического получателя
	ActualBeneficiaryBinIIN *string `json:"actualBeneficiaryBinIIN,omitempty"`

	// ActualBeneficiaryCountry Страна резидентство фактического получателя
	ActualBeneficiaryCountry *string `json:"actualBeneficiaryCountry,omitempty"`

	// ActualBeneficiaryIsLegal Флаг юр лица фактического получателя
	ActualBeneficiaryIsLegal *bool `json:"actualBeneficiaryIsLegal,omitempty"`

	// ActualSender Фактический отправитель (ФИО/Наименование)
	ActualSender *string `json:"actualSender,omitempty"`

	// ActualSenderBinIIN ИИН/БИН фактического отправителя
	ActualSenderBinIIN *string `json:"actualSenderBinIIN,omitempty"`

	// ActualSenderCountry Страна резидентство фактического отправителя
	ActualSenderCountry *string `json:"actualSenderCountry,omitempty"`

	// ActualSenderIsLegal Флаг юр лица фактического отправителя
	ActualSenderIsLegal *bool `json:"actualSenderIsLegal,omitempty"`

	// Amount Сумма операции
	Amount float64 `json:"amount"`

	// BeneficiaryAccount IBAN cчета  получателя
	BeneficiaryAccount string `json:"beneficiaryAccount"`

	// BeneficiaryBank БИК банка получателя
	BeneficiaryBank string `json:"beneficiaryBank"`

	// BeneficiaryBankName Наименование банка получателя
	BeneficiaryBankName string `json:"beneficiaryBankName"`

	// BeneficiaryBinIIN ИИН/БИН получателя
	BeneficiaryBinIIN string `json:"beneficiaryBinIIN"`

	// BeneficiaryCountry Страна резидентство
	BeneficiaryCountry *string `json:"beneficiaryCountry,omitempty"`

	// BeneficiaryName ФИО/Наименование получателя
	BeneficiaryName string `json:"beneficiaryName"`

	// BeneficiaryTaxPayerType Тип налогоплательщика
	BeneficiaryTaxPayerType *TaxPayerType `json:"beneficiaryTaxPayerType,omitempty"`

	// BeneficiaryType Тип получателя
	BeneficiaryType int32 `json:"beneficiaryType"`

	// Currency Валюта операции
	Currency string `json:"currency"`

	// Date Дата операции
	Date *time.Time `json:"date,omitempty"`

	// IdempotencyID Ключ идемпотентности
	IdempotencyID string `json:"idempotencyID"`

	// Kbe Код бенефициара
	Kbe string `json:"kbe"`

	// Knp КНП, purposeCode
	Knp string `json:"knp"`

	// Kod Код отправителя
	Kod *int32 `json:"kod,omitempty"`

	// PayerAccount Счет клиента
	PayerAccount string `json:"payerAccount"`

	// PayerBinIIN ИИН/БИН отправителя
	PayerBinIIN string `json:"payerBinIIN"`

	// PayerName ФИО/Наименование
	PayerName string `json:"payerName"`

	// PaymentDetails Назначение платежа
	PaymentDetails string `json:"paymentDetails"`

	// ValueDate Дата валютирования
	ValueDate *time.Time `json:"valueDate,omitempty"`
}

// CreatePaymentForMobileRequest defines model for CreatePaymentForMobileRequest.
type CreatePaymentForMobileRequest struct {
	// AccountNumber Номер счёта с которого списываются деньги
	AccountNumber string `json:"accountNumber"`
	Amount        string `json:"amount"`

	// IdempotencyID Ключ идемпотентности
	IdempotencyID string `json:"idempotencyID"`

	// PhoneNumber Номер телефона на который поступят деньги
	PhoneNumber string `json:"phoneNumber"`
}

// CreatePaymentKaspiQRRequest defines model for CreatePaymentKaspiQRRequest.
type CreatePaymentKaspiQRRequest struct {
	// CustomerID ИИН пользователя
	CustomerID string `json:"customerID"`

	// IdempotencyID Ключ идемпотентности
	IdempotencyID string `json:"idempotencyID"`

	// PayerAccount IBAN счета отправителя (маскированный)
	PayerAccount string `json:"payerAccount"`

	// PaymentAmount Идентификатор возврата в ПС
	PaymentAmount string `json:"paymentAmount"`

	// PaymentID Идентификатор платежа от ПС
	PaymentID string `json:"paymentID"`
}

// CreateSelfTransferRequest defines model for CreateSelfTransferRequest.
type CreateSelfTransferRequest struct {
	Amount *string `json:"amount,omitempty"`

	// FromAccount Номер счёта с которого списываются деньги
	FromAccount *string `json:"fromAccount,omitempty"`

	// FromAccountСurrency Валюта перевода
	FromAccountСurrency *string `json:"fromAccountСurrency,omitempty"`

	// IdempotencyID Ключ идемпотентности
	IdempotencyID *string `json:"idempotencyID,omitempty"`

	// ToAccount Номер счёта на который поступят деньги
	ToAccount *string `json:"toAccount,omitempty"`

	// ToAccountСurrency Валюта перевода
	ToAccountСurrency *string `json:"toAccountСurrency,omitempty"`
}

// DepositConditionProfitReqBody defines model for DepositConditionProfitReqBody.
type DepositConditionProfitReqBody struct {
	// Amount Сумма для расчета доходности депозита
	Amount float64 `json:"amount"`

	// CurrencyCode Код валюты (например: KZT, USD, EUR)
	CurrencyCode string `json:"currencyCode"`

	// ProductCode Код продукта (например: WAKALA)
	ProductCode string `json:"productCode"`
}

// DictCreateReq defines model for DictCreateReq.
type DictCreateReq = Dictionary

// DictDeleteReq defines model for DictDeleteReq.
type DictDeleteReq = DictionaryID

// DictDocCreateReq defines model for DictDocCreateReq.
type DictDocCreateReq = DictDocument

// DictDocDeleteReq defines model for DictDocDeleteReq.
type DictDocDeleteReq = DictDocumentID

// DictDocGetListByFiltersReq defines model for DictDocGetListByFiltersReq.
type DictDocGetListByFiltersReq = DictDocumentFilters

// DictDocGetTreeLineReq defines model for DictDocGetTreeLineReq.
type DictDocGetTreeLineReq = DictGetTreeLine

// DictDocOrderUpdateReq defines model for DictDocOrderUpdateReq.
type DictDocOrderUpdateReq = DictDocumentOrderUpdate

// DictDocUpdateReq defines model for DictDocUpdateReq.
type DictDocUpdateReq = DictDocument

// DictKATOMapFromTSOIDReq defines model for DictKATOMapFromTSOIDReq.
type DictKATOMapFromTSOIDReq = DictKATOMapFromTSOIDArgs

// DictUpdateReq defines model for DictUpdateReq.
type DictUpdateReq = Dictionary

// DocsOtpBody defines model for DocsOtpBody.
type DocsOtpBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код подтверждения
	Code string `json:"code"`
}

// IdentifyBody defines model for IdentifyBody.
type IdentifyBody struct {
	// Code Код BTS
	Code string `json:"code"`
}

// JobGetStatusReq defines model for JobGetStatusReq.
type JobGetStatusReq = JobGetStatus

// JobRunReq defines model for JobRunReq.
type JobRunReq = JobRun

// JobStopReq defines model for JobStopReq.
type JobStopReq = JobStop

// LoanApplicationDocumentsBody defines model for LoanApplicationDocumentsBody.
type LoanApplicationDocumentsBody struct {
	// ApplicationID Идентификатор заявки.
	ApplicationID openapi_types.UUID `json:"applicationID"`

	// DocTypes Тип (шаблон) документа
	DocTypes []LoanAppDocumentType `json:"docTypes"`
}

// LogInBody defines model for LogInBody.
type LogInBody struct {
	DeviceInfo DeviceInfo `json:"deviceInfo"`

	// Iin ИИН
	Iin string `json:"iin"`

	// Phone Номер телефона
	Phone PhoneNumber `json:"phone"`
}

// LogoutBody defines model for LogoutBody.
type LogoutBody struct {
	// InstallationID Идентификатор установки приложения
	InstallationID openapi_types.UUID `json:"installationID"`
}

// OtpBody defines model for OtpBody.
type OtpBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код подтверждения
	Code *string `json:"code,omitempty"`
}

// OtpRetryBody defines model for OtpRetryBody.
type OtpRetryBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`
}

// PaymentsCheckAccountIinRequest defines model for PaymentsCheckAccountIinRequest.
type PaymentsCheckAccountIinRequest struct {
	// Account Номер счёта
	Account string `json:"account"`

	// ClientIinBin ИИН/БИН клиента
	ClientIinBin string `json:"clientIinBin"`
}

// PostEarlyRepayRequestBody defines model for PostEarlyRepayRequestBody.
type PostEarlyRepayRequestBody struct {
	// RepayAmount Сумма ЧДП/ПДП
	RepayAmount EarlyRepayAmount `json:"repayAmount"`

	// RepayType ODONLY - ЧДП
	// WSHD_FULL - ПДП
	RepayType string `json:"repayType"`
}

// PostEdsBtsDataRequestBody defines model for PostEdsBtsDataRequestBody.
type PostEdsBtsDataRequestBody struct {
	// Code Код
	Code string `json:"code"`
}

// PostIdentifyBtsDataRequestBody defines model for PostIdentifyBtsDataRequestBody.
type PostIdentifyBtsDataRequestBody struct {
	// Code Код
	Code string `json:"code"`
}

// ProfileDeleteBody defines model for ProfileDeleteBody.
type ProfileDeleteBody struct {
	// InstallationID Идентификатор установки приложения
	InstallationID openapi_types.UUID `json:"installationID"`
}

// PublishLoanAppDataRequestBody defines model for PublishLoanAppDataRequestBody.
type PublishLoanAppDataRequestBody struct {
	// BankStatements Данные прикрепленных документов
	BankStatements *[]AttachedDocData `json:"bankStatements,omitempty"`
}

// QRSessionTerminationRequest defines model for QRSessionTerminationRequest.
type QRSessionTerminationRequest struct {
	// CustomerID ИИН пользователя
	CustomerID string `json:"customerID"`

	// PaymentID Идентификатор платежа платежной системы Kaspi Pay
	PaymentID string `json:"paymentID"`
}

// QRTokenRequest defines model for QRTokenRequest.
type QRTokenRequest struct {
	// CustomerID ИИН пользователя
	CustomerID string `json:"customerID"`

	// QrToken Содержимое отсканированного QR-кода
	QrToken string `json:"qrToken"`
}

// ReferralAttributionRequest defines model for ReferralAttributionRequest.
type ReferralAttributionRequest struct {
	// DeepLinkSub1 Уникальный код Клиента, который пригласил Реферала
	DeepLinkSub1 string `json:"deepLinkSub1"`

	// DeepLinkValue Параметр в ссылках AppsFlyer, который указывает, на какой конкретный экран, продукт или контент нужно направить пользователя внутри мобильного приложения после клика по ссылке
	DeepLinkValue *string `json:"deepLinkValue,omitempty"`

	// Timestamp Дата и время перехода Реферала по ссылке
	Timestamp time.Time `json:"timestamp"`
}

// SaveSurveyBody defines model for SaveSurveyBody.
type SaveSurveyBody struct {
	Address SaveSurveyAddress `json:"address"`

	// ApplicationID Идентификатор заявки.
	ApplicationID string `json:"applicationID"`

	// Children Количество детей.
	Children int32 `json:"children"`

	// ContactPersons Список контактных лиц.
	ContactPersons []SaveSurveyContactPerson `json:"contactPersons"`

	// EducationID Идентификатор уровня образования.
	EducationID string `json:"educationID"`

	// Email Электронная почта
	Email Email `json:"email"`

	// EmpID Идентификатор типа занятости.
	EmpID string `json:"empID"`
}

// SaveUserExternalBankLoansBody defines model for SaveUserExternalBankLoansBody.
type SaveUserExternalBankLoansBody struct {
	ExternalLoans []UserExternalBankLoan `json:"externalLoans"`
}

// SignDocumentsByIDReqBody defines model for SignDocumentsByIDReqBody.
type SignDocumentsByIDReqBody struct {
	DocIDs []openapi_types.UUID `json:"docIDs"`
}

// UpdateLoanApplicationBody Объект запроса для создания новой заявки на кредит.
type UpdateLoanApplicationBody struct {
	// Amount Запрашиваемая сумма кредита.
	Amount int `json:"amount"`

	// JuicySessionID ID сессии в системе JUICYSCORE
	JuicySessionID string `json:"juicySessionID"`

	// PurposeID Уникальный идентификатор, представляющий цель кредита (из справочника).
	PurposeID string `json:"purposeID"`

	// TermInterestID Уникальный идентификатор, представляющий условия кредита (из справочника).
	TermInterestID string `json:"termInterestID"`
}

// ApplicationForSignJSONBody defines parameters for ApplicationForSign.
type ApplicationForSignJSONBody struct {
	// Currencies Список валют, для которых будет сгенерирован документ
	Currencies *[]string `json:"currencies,omitempty"`

	// DocType Тип документа, который будет сгенерирован
	DocType string `json:"docType"`
}

// CreateAccountsDocumentJSONBody defines parameters for CreateAccountsDocument.
type CreateAccountsDocumentJSONBody struct {
	// AccountID Идентификатор счета
	AccountID *openapi_types.UUID `json:"accountID,omitempty"`

	// Language Язык документа
	Language *string `json:"language,omitempty"`

	// PeriodFrom Начало периода (только для выписки по счету)
	PeriodFrom *openapi_types.Date `json:"periodFrom"`

	// PeriodTo Конец периода (только для выписки по счету)
	PeriodTo *openapi_types.Date `json:"periodTo"`
}

// CreateAccountsDocumentParams defines parameters for CreateAccountsDocument.
type CreateAccountsDocumentParams struct {
	// DocType Тип запрашиваемого документа
	DocType AccountDocumentType `form:"docType" json:"docType"`
}

// GetBtsDataForAuthParams defines parameters for GetBtsDataForAuth.
type GetBtsDataForAuthParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *GetBtsDataForAuthParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// GetBtsDataForAuthParamsAcceptLanguage defines parameters for GetBtsDataForAuth.
type GetBtsDataForAuthParamsAcceptLanguage string

// AuthConfirmJSONBody defines parameters for AuthConfirm.
type AuthConfirmJSONBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код подтверждения
	Code string `json:"code"`
}

// AuthConfirmParams defines parameters for AuthConfirm.
type AuthConfirmParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *AuthConfirmParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// AuthConfirmParamsAcceptLanguage defines parameters for AuthConfirm.
type AuthConfirmParamsAcceptLanguage string

// DocumentForSignParams defines parameters for DocumentForSign.
type DocumentForSignParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *DocumentForSignParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// DocumentForSignParamsAcceptLanguage defines parameters for DocumentForSign.
type DocumentForSignParamsAcceptLanguage string

// AuthIdentifyJSONBody defines parameters for AuthIdentify.
type AuthIdentifyJSONBody struct {
	// Code Код BTS
	Code string `json:"code"`
}

// AuthIdentifyParams defines parameters for AuthIdentify.
type AuthIdentifyParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *AuthIdentifyParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// AuthIdentifyParamsAcceptLanguage defines parameters for AuthIdentify.
type AuthIdentifyParamsAcceptLanguage string

// AuthLoginJSONBody defines parameters for AuthLogin.
type AuthLoginJSONBody struct {
	DeviceInfo DeviceInfo `json:"deviceInfo"`

	// Iin ИИН
	Iin string `json:"iin"`

	// Phone Номер телефона
	Phone PhoneNumber `json:"phone"`
}

// AuthLoginParams defines parameters for AuthLogin.
type AuthLoginParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *AuthLoginParamsAcceptLanguage `json:"Accept-Language,omitempty"`

	// EntryPoint Ресурс, откуда пользователь пришел в МП (например - с лендинга)
	EntryPoint *EntryPointHeader `json:"entryPoint,omitempty"`
}

// AuthLoginParamsAcceptLanguage defines parameters for AuthLogin.
type AuthLoginParamsAcceptLanguage string

// AuthLogoutJSONBody defines parameters for AuthLogout.
type AuthLogoutJSONBody struct {
	// InstallationID Идентификатор установки приложения
	InstallationID openapi_types.UUID `json:"installationID"`
}

// AuthLogoutParams defines parameters for AuthLogout.
type AuthLogoutParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *AuthLogoutParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// AuthLogoutParamsAcceptLanguage defines parameters for AuthLogout.
type AuthLogoutParamsAcceptLanguage string

// AuthRefreshJSONBody defines parameters for AuthRefresh.
type AuthRefreshJSONBody struct {
	DeviceInfo DeviceInfo `json:"deviceInfo"`

	// Phone Номер телефона
	Phone PhoneNumber `json:"phone"`

	// RefreshToken Токен обновления авторизации
	RefreshToken string `json:"refreshToken"`
}

// AuthRefreshParams defines parameters for AuthRefresh.
type AuthRefreshParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *AuthRefreshParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// AuthRefreshParamsAcceptLanguage defines parameters for AuthRefresh.
type AuthRefreshParamsAcceptLanguage string

// DepositConditionProfitJSONBody defines parameters for DepositConditionProfit.
type DepositConditionProfitJSONBody struct {
	// Amount Сумма для расчета доходности депозита
	Amount float64 `json:"amount"`

	// CurrencyCode Код валюты (например: KZT, USD, EUR)
	CurrencyCode string `json:"currencyCode"`

	// ProductCode Код продукта (например: WAKALA)
	ProductCode string `json:"productCode"`
}

// CreateDepositOfferJSONBody defines parameters for CreateDepositOffer.
type CreateDepositOfferJSONBody struct {
	// AccountId ID счет списания в пользу депозита
	AccountId string `json:"accountId"`

	// Amount Сумма депозита
	Amount float64 `json:"amount"`

	// PayoutMethod Метод выплаты доходности
	PayoutMethod DepositPayoutMethod `json:"payoutMethod"`

	// RateId ID условия/рейта
	RateId string `json:"rateId"`
}

// CalculateProfitParams defines parameters for CalculateProfit.
type CalculateProfitParams struct {
	// Amount Сумма депозита (> 0)
	Amount CalculateProfitAmount `form:"amount" json:"amount"`

	// TermMonths Срок депозита в месяцах (> 0)
	TermMonths CalculateProfitTermMonths `form:"termMonths" json:"termMonths"`

	// ProfitRate Номинальная годовая ставка (например: 0.12 = 12%)
	ProfitRate CalculateProfitProfitRate `form:"profitRate" json:"profitRate"`

	// PayoutMethod Метод выплаты доходности
	PayoutMethod CalculateProfitPayoutMethod `form:"payoutMethod" json:"payoutMethod"`
}

// DictGetListParams defines parameters for DictGetList.
type DictGetListParams struct {
	// Page Страница
	Page *PaginationPageQueryParam `form:"page,omitempty" json:"page,omitempty"`

	// Count Количество строк на странице
	Count *PaginationCountQueryParam `form:"count,omitempty" json:"count,omitempty"`
}

// GetDictionariesParams defines parameters for GetDictionaries.
type GetDictionariesParams struct {
	// Name Имя справочника
	Name string `form:"name" json:"name"`

	// Sort Список полей сортировки
	Sort *[]string `form:"sort,omitempty" json:"sort,omitempty"`

	// Page Номер страницы начиная с 0
	Page *int32 `form:"page,omitempty" json:"page,omitempty"`

	// Count Кол-во записей на странице
	Count *int32 `form:"count,omitempty" json:"count,omitempty"`
}

// DictGetLocationsParams defines parameters for DictGetLocations.
type DictGetLocationsParams struct {
	// ParentID Идентификатор родительской локации. 0 - для получения корневых локаций. Не требуется если указан параметр parentIds
	ParentID *int32 `form:"parentID,omitempty" json:"parentID,omitempty"`

	// ParentIds Идентификаторы родительских локаций. Не требуется если указан параметр parentID
	ParentIds *[]int32 `form:"parentIds,omitempty" json:"parentIds,omitempty"`

	// AcceptLanguage Язык запроса
	AcceptLanguage *DictGetLocationsParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// DictGetLocationsParamsAcceptLanguage defines parameters for DictGetLocations.
type DictGetLocationsParamsAcceptLanguage string

// ConfirmSignDocumentsBatchJSONBody defines parameters for ConfirmSignDocumentsBatch.
type ConfirmSignDocumentsBatchJSONBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код подтверждения
	Code string `json:"code"`

	// DocIDs Идентификаторы подписываемых документов
	DocIDs []openapi_types.UUID `json:"docIDs"`
}

// ConfirmSignDocumentsBatchParams defines parameters for ConfirmSignDocumentsBatch.
type ConfirmSignDocumentsBatchParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *ConfirmSignDocumentsBatchParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// ConfirmSignDocumentsBatchParamsAcceptLanguage defines parameters for ConfirmSignDocumentsBatch.
type ConfirmSignDocumentsBatchParamsAcceptLanguage string

// RequestDocumentPublicParams defines parameters for RequestDocumentPublic.
type RequestDocumentPublicParams struct {
	// DocType Тип запрашиваемого документа
	DocType PublicDocumentType `form:"docType" json:"docType"`

	// AcceptLanguage Язык запроса
	AcceptLanguage *RequestDocumentPublicParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// RequestDocumentPublicParamsAcceptLanguage defines parameters for RequestDocumentPublic.
type RequestDocumentPublicParamsAcceptLanguage string

// SignDocumentByIDsJSONBody defines parameters for SignDocumentByIDs.
type SignDocumentByIDsJSONBody struct {
	DocIDs []openapi_types.UUID `json:"docIDs"`
}

// SignDocumentByIDsParams defines parameters for SignDocumentByIDs.
type SignDocumentByIDsParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *SignDocumentByIDsParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// SignDocumentByIDsParamsAcceptLanguage defines parameters for SignDocumentByIDs.
type SignDocumentByIDsParamsAcceptLanguage string

// ConfirmSignDocumentsJSONBody defines parameters for ConfirmSignDocuments.
type ConfirmSignDocumentsJSONBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код подтверждения
	Code string `json:"code"`
}

// ConfirmSignDocumentsParams defines parameters for ConfirmSignDocuments.
type ConfirmSignDocumentsParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *ConfirmSignDocumentsParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// ConfirmSignDocumentsParamsAcceptLanguage defines parameters for ConfirmSignDocuments.
type ConfirmSignDocumentsParamsAcceptLanguage string

// GetDocumentByIDParams defines parameters for GetDocumentByID.
type GetDocumentByIDParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *GetDocumentByIDParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// GetDocumentByIDParamsAcceptLanguage defines parameters for GetDocumentByID.
type GetDocumentByIDParamsAcceptLanguage string

// SignDocumentByIDParams defines parameters for SignDocumentByID.
type SignDocumentByIDParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *SignDocumentByIDParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// SignDocumentByIDParamsAcceptLanguage defines parameters for SignDocumentByID.
type SignDocumentByIDParamsAcceptLanguage string

// ConfirmSignDocumentByIDJSONBody defines parameters for ConfirmSignDocumentByID.
type ConfirmSignDocumentByIDJSONBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код подтверждения
	Code *string `json:"code,omitempty"`
}

// ConfirmSignDocumentByIDParams defines parameters for ConfirmSignDocumentByID.
type ConfirmSignDocumentByIDParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *ConfirmSignDocumentByIDParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// ConfirmSignDocumentByIDParamsAcceptLanguage defines parameters for ConfirmSignDocumentByID.
type ConfirmSignDocumentByIDParamsAcceptLanguage string

// CreateLoanApplicationJSONBody defines parameters for CreateLoanApplication.
type CreateLoanApplicationJSONBody struct {
	// Amount Запрашиваемая сумма кредита.
	Amount int `json:"amount"`

	// JuicySessionID ID сессии в системе JUICYSCORE
	JuicySessionID *string `json:"juicySessionID,omitempty"`

	// PurposeID Уникальный идентификатор, представляющий цель кредита (из справочника).
	PurposeID *string `json:"purposeID,omitempty"`

	// TermInterestID Уникальный идентификатор, представляющий условия кредита (из справочника).
	TermInterestID *string `json:"termInterestID,omitempty"`
}

// CalculateLoanTermsJSONBody defines parameters for CalculateLoanTerms.
type CalculateLoanTermsJSONBody struct {
	// Amount Значение, внесенное клиентом по сумме кредита
	Amount int `json:"amount"`

	// PurposeID ID выбранной клиентом цели
	PurposeID string `json:"purposeID"`
}

// DocumentsForLoanAppJSONBody defines parameters for DocumentsForLoanApp.
type DocumentsForLoanAppJSONBody struct {
	// ApplicationID Идентификатор заявки.
	ApplicationID openapi_types.UUID `json:"applicationID"`

	// DocTypes Тип (шаблон) документа
	DocTypes []LoanAppDocumentType `json:"docTypes"`
}

// DocumentsForLoanAppParams defines parameters for DocumentsForLoanApp.
type DocumentsForLoanAppParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *DocumentsForLoanAppParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// DocumentsForLoanAppParamsAcceptLanguage defines parameters for DocumentsForLoanApp.
type DocumentsForLoanAppParamsAcceptLanguage string

// GetLoansOnboardingTextsParams defines parameters for GetLoansOnboardingTexts.
type GetLoansOnboardingTextsParams struct {
	// Source Источник запуска онбординга
	Source *GetLoansOnboardingTextsParamsSource `form:"source,omitempty" json:"source,omitempty"`
}

// GetLoansOnboardingTextsParamsSource defines parameters for GetLoansOnboardingTexts.
type GetLoansOnboardingTextsParamsSource string

// GetLoanSurveyParams defines parameters for GetLoanSurvey.
type GetLoanSurveyParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *GetLoanSurveyParamsAcceptLanguage `json:"Accept-Language,omitempty"`

	// UserAgent Юзер агент
	UserAgent UserAgent `json:"User-Agent"`
}

// GetLoanSurveyParamsAcceptLanguage defines parameters for GetLoanSurvey.
type GetLoanSurveyParamsAcceptLanguage string

// SaveLoanSurveyJSONBody defines parameters for SaveLoanSurvey.
type SaveLoanSurveyJSONBody struct {
	Address SaveSurveyAddress `json:"address"`

	// ApplicationID Идентификатор заявки.
	ApplicationID string `json:"applicationID"`

	// Children Количество детей.
	Children int32 `json:"children"`

	// ContactPersons Список контактных лиц.
	ContactPersons []SaveSurveyContactPerson `json:"contactPersons"`

	// EducationID Идентификатор уровня образования.
	EducationID string `json:"educationID"`

	// Email Электронная почта
	Email Email `json:"email"`

	// EmpID Идентификатор типа занятости.
	EmpID string `json:"empID"`
}

// SaveLoanSurveyParams defines parameters for SaveLoanSurvey.
type SaveLoanSurveyParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *SaveLoanSurveyParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// SaveLoanSurveyParamsAcceptLanguage defines parameters for SaveLoanSurvey.
type SaveLoanSurveyParamsAcceptLanguage string

// UpdateLoanApplicationJSONBody defines parameters for UpdateLoanApplication.
type UpdateLoanApplicationJSONBody struct {
	// Amount Запрашиваемая сумма кредита.
	Amount int `json:"amount"`

	// JuicySessionID ID сессии в системе JUICYSCORE
	JuicySessionID string `json:"juicySessionID"`

	// PurposeID Уникальный идентификатор, представляющий цель кредита (из справочника).
	PurposeID string `json:"purposeID"`

	// TermInterestID Уникальный идентификатор, представляющий условия кредита (из справочника).
	TermInterestID string `json:"termInterestID"`
}

// GetBtsDataForLoanAppParams defines parameters for GetBtsDataForLoanApp.
type GetBtsDataForLoanAppParams struct {
	Type string `form:"type" json:"type"`

	// AcceptLanguage Язык запроса
	AcceptLanguage GetBtsDataForLoanAppParamsAcceptLanguage `json:"Accept-Language"`
}

// GetBtsDataForLoanAppParamsAcceptLanguage defines parameters for GetBtsDataForLoanApp.
type GetBtsDataForLoanAppParamsAcceptLanguage string

// GetLoansDetailsParams defines parameters for GetLoansDetails.
type GetLoansDetailsParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *GetLoansDetailsParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// GetLoansDetailsParamsAcceptLanguage defines parameters for GetLoansDetails.
type GetLoansDetailsParamsAcceptLanguage string

// LoanDocumentForSignParams defines parameters for LoanDocumentForSign.
type LoanDocumentForSignParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *LoanDocumentForSignParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// LoanDocumentForSignParamsAcceptLanguage defines parameters for LoanDocumentForSign.
type LoanDocumentForSignParamsAcceptLanguage string

// LoansPostEarlyRepayJSONBody defines parameters for LoansPostEarlyRepay.
type LoansPostEarlyRepayJSONBody struct {
	// RepayAmount Сумма ЧДП/ПДП
	RepayAmount EarlyRepayAmount `json:"repayAmount"`

	// RepayType ODONLY - ЧДП
	// WSHD_FULL - ПДП
	RepayType string `json:"repayType"`
}

// LoansPostEdsBtsDataJSONBody defines parameters for LoansPostEdsBtsData.
type LoansPostEdsBtsDataJSONBody struct {
	// Code Код
	Code string `json:"code"`
}

// LoansPostEdsBtsDataParams defines parameters for LoansPostEdsBtsData.
type LoansPostEdsBtsDataParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *LoansPostEdsBtsDataParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// LoansPostEdsBtsDataParamsAcceptLanguage defines parameters for LoansPostEdsBtsData.
type LoansPostEdsBtsDataParamsAcceptLanguage string

// SaveUserExternalBankLoansJSONBody defines parameters for SaveUserExternalBankLoans.
type SaveUserExternalBankLoansJSONBody struct {
	ExternalLoans []UserExternalBankLoan `json:"externalLoans"`
}

// LoansPostIdentifyBtsDataJSONBody defines parameters for LoansPostIdentifyBtsData.
type LoansPostIdentifyBtsDataJSONBody struct {
	// Code Код
	Code string `json:"code"`
}

// LoansPostIdentifyBtsDataParams defines parameters for LoansPostIdentifyBtsData.
type LoansPostIdentifyBtsDataParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *LoansPostIdentifyBtsDataParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// LoansPostIdentifyBtsDataParamsAcceptLanguage defines parameters for LoansPostIdentifyBtsData.
type LoansPostIdentifyBtsDataParamsAcceptLanguage string

// PublishLoanAppDataJSONBody defines parameters for PublishLoanAppData.
type PublishLoanAppDataJSONBody struct {
	// BankStatements Данные прикрепленных документов
	BankStatements *[]AttachedDocData `json:"bankStatements,omitempty"`
}

// GetRefinancingInfoParams defines parameters for GetRefinancingInfo.
type GetRefinancingInfoParams struct {
	// Step Вид шага
	Step GetRefinancingInfoParamsStep `form:"step" json:"step"`

	// AcceptLanguage Язык запроса
	AcceptLanguage *GetRefinancingInfoParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// GetRefinancingInfoParamsStep defines parameters for GetRefinancingInfo.
type GetRefinancingInfoParamsStep string

// GetRefinancingInfoParamsAcceptLanguage defines parameters for GetRefinancingInfo.
type GetRefinancingInfoParamsAcceptLanguage string

// GetScoringResultParams defines parameters for GetScoringResult.
type GetScoringResultParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage GetScoringResultParamsAcceptLanguage `json:"Accept-Language"`
}

// GetScoringResultParamsAcceptLanguage defines parameters for GetScoringResult.
type GetScoringResultParamsAcceptLanguage string

// LoansConfirmSignDocumentsJSONBody defines parameters for LoansConfirmSignDocuments.
type LoansConfirmSignDocumentsJSONBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код подтверждения
	Code string `json:"code"`
}

// LoansConfirmSignDocumentsParams defines parameters for LoansConfirmSignDocuments.
type LoansConfirmSignDocumentsParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *LoansConfirmSignDocumentsParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// LoansConfirmSignDocumentsParamsAcceptLanguage defines parameters for LoansConfirmSignDocuments.
type LoansConfirmSignDocumentsParamsAcceptLanguage string

// LoansConfirmSignDocumentByIDJSONBody defines parameters for LoansConfirmSignDocumentByID.
type LoansConfirmSignDocumentByIDJSONBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код подтверждения
	Code *string `json:"code,omitempty"`
}

// LoansConfirmSignDocumentByIDParams defines parameters for LoansConfirmSignDocumentByID.
type LoansConfirmSignDocumentByIDParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *LoansConfirmSignDocumentByIDParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// LoansConfirmSignDocumentByIDParamsAcceptLanguage defines parameters for LoansConfirmSignDocumentByID.
type LoansConfirmSignDocumentByIDParamsAcceptLanguage string

// OtpRetryJSONBody defines parameters for OtpRetry.
type OtpRetryJSONBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`
}

// PaymentsCheckAccountIinJSONBody defines parameters for PaymentsCheckAccountIin.
type PaymentsCheckAccountIinJSONBody struct {
	// Account Номер счёта
	Account string `json:"account"`

	// ClientIinBin ИИН/БИН клиента
	ClientIinBin string `json:"clientIinBin"`
}

// CheckClientByPhoneNumberJSONBody defines parameters for CheckClientByPhoneNumber.
type CheckClientByPhoneNumberJSONBody struct {
	// PhoneNumber Номер телефона
	PhoneNumber string `json:"phoneNumber"`
}

// CheckPhoneNumberJSONBody defines parameters for CheckPhoneNumber.
type CheckPhoneNumberJSONBody struct {
	// PhoneNumber Номер телефона
	PhoneNumber string `json:"phoneNumber"`
}

// ConfirmInternalPaymentByPhoneNumberJSONBody defines parameters for ConfirmInternalPaymentByPhoneNumber.
type ConfirmInternalPaymentByPhoneNumberJSONBody struct {
	// AttemptID Идентификатор для валидации отп кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код валидации отп
	Code string `json:"code"`
}

// ConfirmPaymentByAccountJSONBody defines parameters for ConfirmPaymentByAccount.
type ConfirmPaymentByAccountJSONBody struct {
	// AttemptID Идентификатор для валидации отп кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код валидации отп
	Code string `json:"code"`
}

// ConfirmPaymentForMobileJSONBody defines parameters for ConfirmPaymentForMobile.
type ConfirmPaymentForMobileJSONBody struct {
	// AttemptID Идентификатор для валидации отп кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код валидации отп
	Code string `json:"code"`
}

// CreateInternalPaymentByPhoneNumberJSONBody defines parameters for CreateInternalPaymentByPhoneNumber.
type CreateInternalPaymentByPhoneNumberJSONBody struct {
	Amount string `json:"amount"`

	// BeneficiaryPhoneNumber Номер телефона клиента банка на счёт которого происходит перевод
	BeneficiaryPhoneNumber string `json:"beneficiaryPhoneNumber"`

	// Currency Валюта перевода
	Currency string `json:"currency"`

	// IdempotencyID Ключ идемпотентности
	IdempotencyID string `json:"idempotencyID"`

	// PayerAccountNumber Номер счёта с которого списываются деньги
	PayerAccountNumber string `json:"payerAccountNumber"`
}

// CreatePaymentByAccountJSONBody defines parameters for CreatePaymentByAccount.
type CreatePaymentByAccountJSONBody struct {
	// ActualBeneficiary Фактический получатель (ФИО/Наименование)
	ActualBeneficiary *string `json:"actualBeneficiary,omitempty"`

	// ActualBeneficiaryBinIIN ИИН/БИН фактического получателя
	ActualBeneficiaryBinIIN *string `json:"actualBeneficiaryBinIIN,omitempty"`

	// ActualBeneficiaryCountry Страна резидентство фактического получателя
	ActualBeneficiaryCountry *string `json:"actualBeneficiaryCountry,omitempty"`

	// ActualBeneficiaryIsLegal Флаг юр лица фактического получателя
	ActualBeneficiaryIsLegal *bool `json:"actualBeneficiaryIsLegal,omitempty"`

	// ActualSender Фактический отправитель (ФИО/Наименование)
	ActualSender *string `json:"actualSender,omitempty"`

	// ActualSenderBinIIN ИИН/БИН фактического отправителя
	ActualSenderBinIIN *string `json:"actualSenderBinIIN,omitempty"`

	// ActualSenderCountry Страна резидентство фактического отправителя
	ActualSenderCountry *string `json:"actualSenderCountry,omitempty"`

	// ActualSenderIsLegal Флаг юр лица фактического отправителя
	ActualSenderIsLegal *bool `json:"actualSenderIsLegal,omitempty"`

	// Amount Сумма операции
	Amount float64 `json:"amount"`

	// BeneficiaryAccount IBAN cчета  получателя
	BeneficiaryAccount string `json:"beneficiaryAccount"`

	// BeneficiaryBank БИК банка получателя
	BeneficiaryBank string `json:"beneficiaryBank"`

	// BeneficiaryBankName Наименование банка получателя
	BeneficiaryBankName string `json:"beneficiaryBankName"`

	// BeneficiaryBinIIN ИИН/БИН получателя
	BeneficiaryBinIIN string `json:"beneficiaryBinIIN"`

	// BeneficiaryCountry Страна резидентство
	BeneficiaryCountry *string `json:"beneficiaryCountry,omitempty"`

	// BeneficiaryName ФИО/Наименование получателя
	BeneficiaryName string `json:"beneficiaryName"`

	// BeneficiaryTaxPayerType Тип налогоплательщика
	BeneficiaryTaxPayerType *TaxPayerType `json:"beneficiaryTaxPayerType,omitempty"`

	// BeneficiaryType Тип получателя
	BeneficiaryType int32 `json:"beneficiaryType"`

	// Currency Валюта операции
	Currency string `json:"currency"`

	// Date Дата операции
	Date *time.Time `json:"date,omitempty"`

	// IdempotencyID Ключ идемпотентности
	IdempotencyID string `json:"idempotencyID"`

	// Kbe Код бенефициара
	Kbe string `json:"kbe"`

	// Knp КНП, purposeCode
	Knp string `json:"knp"`

	// Kod Код отправителя
	Kod *int32 `json:"kod,omitempty"`

	// PayerAccount Счет клиента
	PayerAccount string `json:"payerAccount"`

	// PayerBinIIN ИИН/БИН отправителя
	PayerBinIIN string `json:"payerBinIIN"`

	// PayerName ФИО/Наименование
	PayerName string `json:"payerName"`

	// PaymentDetails Назначение платежа
	PaymentDetails string `json:"paymentDetails"`

	// ValueDate Дата валютирования
	ValueDate *time.Time `json:"valueDate,omitempty"`
}

// CreatePaymentForMobileJSONBody defines parameters for CreatePaymentForMobile.
type CreatePaymentForMobileJSONBody struct {
	// AccountNumber Номер счёта с которого списываются деньги
	AccountNumber string `json:"accountNumber"`
	Amount        string `json:"amount"`

	// IdempotencyID Ключ идемпотентности
	IdempotencyID string `json:"idempotencyID"`

	// PhoneNumber Номер телефона на который поступят деньги
	PhoneNumber string `json:"phoneNumber"`
}

// CreatePaymentKaspiQRJSONBody defines parameters for CreatePaymentKaspiQR.
type CreatePaymentKaspiQRJSONBody struct {
	// CustomerID ИИН пользователя
	CustomerID string `json:"customerID"`

	// IdempotencyID Ключ идемпотентности
	IdempotencyID string `json:"idempotencyID"`

	// PayerAccount IBAN счета отправителя (маскированный)
	PayerAccount string `json:"payerAccount"`

	// PaymentAmount Идентификатор возврата в ПС
	PaymentAmount string `json:"paymentAmount"`

	// PaymentID Идентификатор платежа от ПС
	PaymentID string `json:"paymentID"`
}

// CreateSelfTransferJSONBody defines parameters for CreateSelfTransfer.
type CreateSelfTransferJSONBody struct {
	Amount *string `json:"amount,omitempty"`

	// FromAccount Номер счёта с которого списываются деньги
	FromAccount *string `json:"fromAccount,omitempty"`

	// FromAccountСurrency Валюта перевода
	FromAccountСurrency *string `json:"fromAccountСurrency,omitempty"`

	// IdempotencyID Ключ идемпотентности
	IdempotencyID *string `json:"idempotencyID,omitempty"`

	// ToAccount Номер счёта на который поступят деньги
	ToAccount *string `json:"toAccount,omitempty"`

	// ToAccountСurrency Валюта перевода
	ToAccountСurrency *string `json:"toAccountСurrency,omitempty"`
}

// GetPaymentHistoryParams defines parameters for GetPaymentHistory.
type GetPaymentHistoryParams struct {
	// ClientIinBin ИИН/БИН клиента
	ClientIinBin PaymentsGetHistoryClientIinBin `form:"clientIinBin" json:"clientIinBin"`

	// Limit Лимит количества возвращаемых транзакций
	Limit *PaymentsGetTransactionsLimit `form:"limit,omitempty" json:"limit,omitempty"`

	// Offset Смещение для пагинации
	Offset *PaymentsGetTransactionsOffset `form:"offset,omitempty" json:"offset,omitempty"`
}

// QRSessionTerminationJSONBody defines parameters for QRSessionTermination.
type QRSessionTerminationJSONBody struct {
	// CustomerID ИИН пользователя
	CustomerID string `json:"customerID"`

	// PaymentID Идентификатор платежа платежной системы Kaspi Pay
	PaymentID string `json:"paymentID"`
}

// QRTokenJSONBody defines parameters for QRToken.
type QRTokenJSONBody struct {
	// CustomerID ИИН пользователя
	CustomerID string `json:"customerID"`

	// QrToken Содержимое отсканированного QR-кода
	QrToken string `json:"qrToken"`
}

// GetTransactionsParams defines parameters for GetTransactions.
type GetTransactionsParams struct {
	// StartDate Начальная дата для фильтрации транзакций
	StartDate *PaymentsGetTransactionsStartDate `form:"startDate,omitempty" json:"startDate,omitempty"`

	// EndDate Конечная дата для фильтрации транзакций
	EndDate *PaymentsGetTransactionsEndDate `form:"endDate,omitempty" json:"endDate,omitempty"`

	// Accounts Список номеров счетов для фильтрации транзакций
	Accounts PaymentsGetTransactionsAccounts `form:"accounts" json:"accounts"`

	// Cards Список карт для фильтрации транзакций
	Cards *PaymentsGetTransactionsCards `form:"cards,omitempty" json:"cards,omitempty"`

	// Direction Тип операции (например, CREDIT или DEBIT)
	Direction *GetTransactionsParamsDirection `form:"direction,omitempty" json:"direction,omitempty"`

	// Counterparty Контрагент для фильтрации (получатель или отправитель средств)
	Counterparty *PaymentsGetTransactionsCounterparty `form:"counterparty,omitempty" json:"counterparty,omitempty"`

	// MinAmount Минимальная сумма транзакции
	MinAmount *PaymentsGetTransactionsMinAmount `form:"minAmount,omitempty" json:"minAmount,omitempty"`

	// MaxAmount Максимальная сумма транзакции
	MaxAmount *PaymentsGetTransactionsMaxAmount `form:"maxAmount,omitempty" json:"maxAmount,omitempty"`

	// Limit Лимит количества возвращаемых транзакций
	Limit *PaymentsGetTransactionsLimit `form:"limit,omitempty" json:"limit,omitempty"`

	// Offset Смещение для пагинации
	Offset *PaymentsGetTransactionsOffset `form:"offset,omitempty" json:"offset,omitempty"`
}

// GetTransactionsParamsDirection defines parameters for GetTransactions.
type GetTransactionsParamsDirection string

// ReferralProgramAttributionJSONBody defines parameters for ReferralProgramAttribution.
type ReferralProgramAttributionJSONBody struct {
	// DeepLinkSub1 Уникальный код Клиента, который пригласил Реферала
	DeepLinkSub1 string `json:"deepLinkSub1"`

	// DeepLinkValue Параметр в ссылках AppsFlyer, который указывает, на какой конкретный экран, продукт или контент нужно направить пользователя внутри мобильного приложения после клика по ссылке
	DeepLinkValue *string `json:"deepLinkValue,omitempty"`

	// Timestamp Дата и время перехода Реферала по ссылке
	Timestamp time.Time `json:"timestamp"`
}

// GetTasksParams defines parameters for GetTasks.
type GetTasksParams struct {
	// Status Статус задачи
	Status *GetTasksParamsStatus `form:"status,omitempty" json:"status,omitempty"`

	// Type Тип задачи
	Type *TaskTypeQueryParam `form:"type,omitempty" json:"type,omitempty"`

	// CreatedAfter Дата создания после (timestamp)
	CreatedAfter *TaskCreatedAfterQueryParam `form:"created_after,omitempty" json:"created_after,omitempty"`

	// CreatedBefore Дата создания до (timestamp)
	CreatedBefore *TaskCreatedBeforeQueryParam `form:"created_before,omitempty" json:"created_before,omitempty"`

	// Page Номер страницы
	Page *TaskPageQueryParam `form:"page,omitempty" json:"page,omitempty"`

	// PageSize Размер страницы
	PageSize *TaskPageSizeQueryParam `form:"page_size,omitempty" json:"page_size,omitempty"`
}

// GetTasksParamsStatus defines parameters for GetTasks.
type GetTasksParamsStatus string

// NotifyVirtualCardChangeParams defines parameters for NotifyVirtualCardChange.
type NotifyVirtualCardChangeParams struct {
	// XCorrelationId Идентификатор сессии в TSH (связывает запросы одного сценария)
	XCorrelationId string `json:"x-correlation-id"`

	// XIssuerId Идентификатор эмитента (10 символов)
	XIssuerId string `json:"x-issuer-id"`
}

// GetLoansParams defines parameters for GetLoans.
type GetLoansParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage GetLoansParamsAcceptLanguage `json:"Accept-Language"`
}

// GetLoansParamsAcceptLanguage defines parameters for GetLoans.
type GetLoansParamsAcceptLanguage string

// UsersUpdateUserLocaleParams defines parameters for UsersUpdateUserLocale.
type UsersUpdateUserLocaleParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage UsersUpdateUserLocaleParamsAcceptLanguage `json:"Accept-Language"`
}

// UsersUpdateUserLocaleParamsAcceptLanguage defines parameters for UsersUpdateUserLocale.
type UsersUpdateUserLocaleParamsAcceptLanguage string

// ProfileDeleteJSONBody defines parameters for ProfileDelete.
type ProfileDeleteJSONBody struct {
	// InstallationID Идентификатор установки приложения
	InstallationID openapi_types.UUID `json:"installationID"`
}

// ApplicationForSignJSONRequestBody defines body for ApplicationForSign for application/json ContentType.
type ApplicationForSignJSONRequestBody ApplicationForSignJSONBody

// CreateAccountsDocumentJSONRequestBody defines body for CreateAccountsDocument for application/json ContentType.
type CreateAccountsDocumentJSONRequestBody CreateAccountsDocumentJSONBody

// AuthConfirmJSONRequestBody defines body for AuthConfirm for application/json ContentType.
type AuthConfirmJSONRequestBody AuthConfirmJSONBody

// AuthIdentifyJSONRequestBody defines body for AuthIdentify for application/json ContentType.
type AuthIdentifyJSONRequestBody AuthIdentifyJSONBody

// AuthLoginJSONRequestBody defines body for AuthLogin for application/json ContentType.
type AuthLoginJSONRequestBody AuthLoginJSONBody

// AuthLogoutJSONRequestBody defines body for AuthLogout for application/json ContentType.
type AuthLogoutJSONRequestBody AuthLogoutJSONBody

// AuthRefreshJSONRequestBody defines body for AuthRefresh for application/json ContentType.
type AuthRefreshJSONRequestBody AuthRefreshJSONBody

// DepositConditionProfitJSONRequestBody defines body for DepositConditionProfit for application/json ContentType.
type DepositConditionProfitJSONRequestBody DepositConditionProfitJSONBody

// CreateDepositOfferJSONRequestBody defines body for CreateDepositOffer for application/json ContentType.
type CreateDepositOfferJSONRequestBody CreateDepositOfferJSONBody

// DictCreateJSONRequestBody defines body for DictCreate for application/json ContentType.
type DictCreateJSONRequestBody = Dictionary

// DictDeleteJSONRequestBody defines body for DictDelete for application/json ContentType.
type DictDeleteJSONRequestBody = DictionaryID

// DictDocGetListByFiltersJSONRequestBody defines body for DictDocGetListByFilters for application/json ContentType.
type DictDocGetListByFiltersJSONRequestBody = DictDocumentFilters

// DictDocCreateJSONRequestBody defines body for DictDocCreate for application/json ContentType.
type DictDocCreateJSONRequestBody = DictDocument

// DictDocDeleteJSONRequestBody defines body for DictDocDelete for application/json ContentType.
type DictDocDeleteJSONRequestBody = DictDocumentID

// DictDocOrderUpdateJSONRequestBody defines body for DictDocOrderUpdate for application/json ContentType.
type DictDocOrderUpdateJSONRequestBody = DictDocumentOrderUpdate

// DictDocGetTreeLineJSONRequestBody defines body for DictDocGetTreeLine for application/json ContentType.
type DictDocGetTreeLineJSONRequestBody = DictGetTreeLine

// DictDocUpdateJSONRequestBody defines body for DictDocUpdate for application/json ContentType.
type DictDocUpdateJSONRequestBody = DictDocument

// DictJobRunJSONRequestBody defines body for DictJobRun for application/json ContentType.
type DictJobRunJSONRequestBody = JobRun

// DictJobGetStatusJSONRequestBody defines body for DictJobGetStatus for application/json ContentType.
type DictJobGetStatusJSONRequestBody = JobGetStatus

// DictJobStopJSONRequestBody defines body for DictJobStop for application/json ContentType.
type DictJobStopJSONRequestBody = JobStop

// DictKATOMapFromTSOIDJSONRequestBody defines body for DictKATOMapFromTSOID for application/json ContentType.
type DictKATOMapFromTSOIDJSONRequestBody = DictKATOMapFromTSOIDArgs

// DictUpdateJSONRequestBody defines body for DictUpdate for application/json ContentType.
type DictUpdateJSONRequestBody = Dictionary

// GetDictionariesByFilterJSONRequestBody defines body for GetDictionariesByFilter for application/json ContentType.
type GetDictionariesByFilterJSONRequestBody = DictDocumentFilters

// ConfirmSignDocumentsBatchJSONRequestBody defines body for ConfirmSignDocumentsBatch for application/json ContentType.
type ConfirmSignDocumentsBatchJSONRequestBody ConfirmSignDocumentsBatchJSONBody

// SignDocumentByIDsJSONRequestBody defines body for SignDocumentByIDs for application/json ContentType.
type SignDocumentByIDsJSONRequestBody SignDocumentByIDsJSONBody

// ConfirmSignDocumentsJSONRequestBody defines body for ConfirmSignDocuments for application/json ContentType.
type ConfirmSignDocumentsJSONRequestBody ConfirmSignDocumentsJSONBody

// ConfirmSignDocumentByIDJSONRequestBody defines body for ConfirmSignDocumentByID for application/json ContentType.
type ConfirmSignDocumentByIDJSONRequestBody ConfirmSignDocumentByIDJSONBody

// PostUnsecurePdfFileMultipartRequestBody defines body for PostUnsecurePdfFile for multipart/form-data ContentType.
type PostUnsecurePdfFileMultipartRequestBody = FileUploadRequestBody

// CreateLoanApplicationJSONRequestBody defines body for CreateLoanApplication for application/json ContentType.
type CreateLoanApplicationJSONRequestBody CreateLoanApplicationJSONBody

// CalculateLoanTermsJSONRequestBody defines body for CalculateLoanTerms for application/json ContentType.
type CalculateLoanTermsJSONRequestBody CalculateLoanTermsJSONBody

// DocumentsForLoanAppJSONRequestBody defines body for DocumentsForLoanApp for application/json ContentType.
type DocumentsForLoanAppJSONRequestBody DocumentsForLoanAppJSONBody

// SaveLoanSurveyJSONRequestBody defines body for SaveLoanSurvey for application/json ContentType.
type SaveLoanSurveyJSONRequestBody SaveLoanSurveyJSONBody

// UpdateLoanApplicationJSONRequestBody defines body for UpdateLoanApplication for application/json ContentType.
type UpdateLoanApplicationJSONRequestBody UpdateLoanApplicationJSONBody

// LoansPostEarlyRepayJSONRequestBody defines body for LoansPostEarlyRepay for application/json ContentType.
type LoansPostEarlyRepayJSONRequestBody LoansPostEarlyRepayJSONBody

// LoansPostEdsBtsDataJSONRequestBody defines body for LoansPostEdsBtsData for application/json ContentType.
type LoansPostEdsBtsDataJSONRequestBody LoansPostEdsBtsDataJSONBody

// SaveUserExternalBankLoansJSONRequestBody defines body for SaveUserExternalBankLoans for application/json ContentType.
type SaveUserExternalBankLoansJSONRequestBody SaveUserExternalBankLoansJSONBody

// LoansPostIdentifyBtsDataJSONRequestBody defines body for LoansPostIdentifyBtsData for application/json ContentType.
type LoansPostIdentifyBtsDataJSONRequestBody LoansPostIdentifyBtsDataJSONBody

// PublishLoanAppDataJSONRequestBody defines body for PublishLoanAppData for application/json ContentType.
type PublishLoanAppDataJSONRequestBody PublishLoanAppDataJSONBody

// LoansConfirmSignDocumentsJSONRequestBody defines body for LoansConfirmSignDocuments for application/json ContentType.
type LoansConfirmSignDocumentsJSONRequestBody LoansConfirmSignDocumentsJSONBody

// LoansConfirmSignDocumentByIDJSONRequestBody defines body for LoansConfirmSignDocumentByID for application/json ContentType.
type LoansConfirmSignDocumentByIDJSONRequestBody LoansConfirmSignDocumentByIDJSONBody

// OtpRetryJSONRequestBody defines body for OtpRetry for application/json ContentType.
type OtpRetryJSONRequestBody OtpRetryJSONBody

// PaymentsCheckAccountIinJSONRequestBody defines body for PaymentsCheckAccountIin for application/json ContentType.
type PaymentsCheckAccountIinJSONRequestBody PaymentsCheckAccountIinJSONBody

// CheckClientByPhoneNumberJSONRequestBody defines body for CheckClientByPhoneNumber for application/json ContentType.
type CheckClientByPhoneNumberJSONRequestBody CheckClientByPhoneNumberJSONBody

// CheckPhoneNumberJSONRequestBody defines body for CheckPhoneNumber for application/json ContentType.
type CheckPhoneNumberJSONRequestBody CheckPhoneNumberJSONBody

// ConfirmInternalPaymentByPhoneNumberJSONRequestBody defines body for ConfirmInternalPaymentByPhoneNumber for application/json ContentType.
type ConfirmInternalPaymentByPhoneNumberJSONRequestBody ConfirmInternalPaymentByPhoneNumberJSONBody

// ConfirmPaymentByAccountJSONRequestBody defines body for ConfirmPaymentByAccount for application/json ContentType.
type ConfirmPaymentByAccountJSONRequestBody ConfirmPaymentByAccountJSONBody

// ConfirmPaymentForMobileJSONRequestBody defines body for ConfirmPaymentForMobile for application/json ContentType.
type ConfirmPaymentForMobileJSONRequestBody ConfirmPaymentForMobileJSONBody

// CreateInternalPaymentByPhoneNumberJSONRequestBody defines body for CreateInternalPaymentByPhoneNumber for application/json ContentType.
type CreateInternalPaymentByPhoneNumberJSONRequestBody CreateInternalPaymentByPhoneNumberJSONBody

// CreatePaymentByAccountJSONRequestBody defines body for CreatePaymentByAccount for application/json ContentType.
type CreatePaymentByAccountJSONRequestBody CreatePaymentByAccountJSONBody

// CreatePaymentForMobileJSONRequestBody defines body for CreatePaymentForMobile for application/json ContentType.
type CreatePaymentForMobileJSONRequestBody CreatePaymentForMobileJSONBody

// CreatePaymentKaspiQRJSONRequestBody defines body for CreatePaymentKaspiQR for application/json ContentType.
type CreatePaymentKaspiQRJSONRequestBody CreatePaymentKaspiQRJSONBody

// CreateSelfTransferJSONRequestBody defines body for CreateSelfTransfer for application/json ContentType.
type CreateSelfTransferJSONRequestBody CreateSelfTransferJSONBody

// QRSessionTerminationJSONRequestBody defines body for QRSessionTermination for application/json ContentType.
type QRSessionTerminationJSONRequestBody QRSessionTerminationJSONBody

// QRTokenJSONRequestBody defines body for QRToken for application/json ContentType.
type QRTokenJSONRequestBody QRTokenJSONBody

// ReferralProgramAttributionJSONRequestBody defines body for ReferralProgramAttribution for application/json ContentType.
type ReferralProgramAttributionJSONRequestBody ReferralProgramAttributionJSONBody

// StartTokenizationJSONRequestBody defines body for StartTokenization for application/json ContentType.
type StartTokenizationJSONRequestBody = TokenizeStartReq

// NotifyVirtualCardChangeJSONRequestBody defines body for NotifyVirtualCardChange for application/json ContentType.
type NotifyVirtualCardChangeJSONRequestBody = NotifyVirtualCardChangeReq

// ProfileDeleteJSONRequestBody defines body for ProfileDelete for application/json ContentType.
type ProfileDeleteJSONRequestBody ProfileDeleteJSONBody

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Генерация документа для выпуска виртуальной карты
	// (POST /accounts/application-for-sign)
	ApplicationForSign(w http.ResponseWriter, r *http.Request)
	// Метод для получения документов из счетов
	// (POST /accounts/documents)
	CreateAccountsDocument(w http.ResponseWriter, r *http.Request, params CreateAccountsDocumentParams)
	// Метод для получения ссылки liveness
	// (GET /auth/bts-data)
	GetBtsDataForAuth(w http.ResponseWriter, r *http.Request, params GetBtsDataForAuthParams)
	// Подтверждение авторизации
	// (POST /auth/confirm)
	AuthConfirm(w http.ResponseWriter, r *http.Request, params AuthConfirmParams)
	// Метод для генерации и получения документа для подписи
	// (POST /auth/document-for-sign)
	DocumentForSign(w http.ResponseWriter, r *http.Request, params DocumentForSignParams)
	// Идентификация пользователя
	// (POST /auth/identify)
	AuthIdentify(w http.ResponseWriter, r *http.Request, params AuthIdentifyParams)
	// Авторизация пользователя по телефону
	// (POST /auth/login)
	AuthLogin(w http.ResponseWriter, r *http.Request, params AuthLoginParams)
	// Выход из системы
	// (POST /auth/logout)
	AuthLogout(w http.ResponseWriter, r *http.Request, params AuthLogoutParams)
	// Обновление токенов пользователя
	// (POST /auth/refresh)
	AuthRefresh(w http.ResponseWriter, r *http.Request, params AuthRefreshParams)
	// Получение полных реквизитов карты
	// (GET /cards/{cardId}/requisites)
	GetRequisites(w http.ResponseWriter, r *http.Request, cardId CardIDPathParam)
	// Расчет доходности депозита
	// (POST /deposits/condition-profit)
	DepositConditionProfit(w http.ResponseWriter, r *http.Request)
	// Создание офера депозита
	// (POST /deposits/create-offer)
	CreateDepositOffer(w http.ResponseWriter, r *http.Request)
	// Получение доступных счетов пользователя
	// (GET /deposits/get-available-accounts)
	GetAvailableAccounts(w http.ResponseWriter, r *http.Request)
	// Получение информации о продукте
	// (GET /deposits/product-info/{productCode})
	GetProductInfo(w http.ResponseWriter, r *http.Request, productCode GetProductInfo)
	// Расчет доходности для указанной суммы/срока/ставки
	// (GET /deposits/utils/calculate-profit)
	CalculateProfit(w http.ResponseWriter, r *http.Request, params CalculateProfitParams)
	// Метод для создания нового справочника
	// (POST /dict/create)
	DictCreate(w http.ResponseWriter, r *http.Request)
	// Метод для удаления справочника
	// (POST /dict/delete)
	DictDelete(w http.ResponseWriter, r *http.Request)
	// Просмотр списка документов справочника по фильтрам данных
	// (POST /dict/doc/by-filters)
	DictDocGetListByFilters(w http.ResponseWriter, r *http.Request)
	// Просмотр документа по именам справочника и документа
	// (GET /dict/doc/by-name/{dict_name}/{doc_name})
	DictDocGetByName(w http.ResponseWriter, r *http.Request, dictName DictionaryNamePathParam, docName DictDocumentNamePathParam)
	// Метод для создания нового документа в справочнике
	// (POST /dict/doc/create)
	DictDocCreate(w http.ResponseWriter, r *http.Request)
	// Метод для удаления документа в справочнике
	// (POST /dict/doc/delete)
	DictDocDelete(w http.ResponseWriter, r *http.Request)
	// Просмотр документа справочника по ID документа
	// (GET /dict/doc/get/{doc_id})
	DictDocGet(w http.ResponseWriter, r *http.Request, docId DictDocumentIDPathParam)
	// Метод для установки индекса сортировки
	// (POST /dict/doc/order-update)
	DictDocOrderUpdate(w http.ResponseWriter, r *http.Request)
	// Возвращает путь для древовидной структуры справочника
	// (POST /dict/doc/tree/path)
	DictDocGetTreeLine(w http.ResponseWriter, r *http.Request)
	// Метод для изменения документа в справочнике
	// (POST /dict/doc/update)
	DictDocUpdate(w http.ResponseWriter, r *http.Request)
	// Просмотр данных справочника по его ID
	// (GET /dict/get/{dict_id})
	DictGet(w http.ResponseWriter, r *http.Request, dictId DictionaryIDPathParam)
	// Запуск задачи синхронизации справочника
	// (POST /dict/job/run)
	DictJobRun(w http.ResponseWriter, r *http.Request)
	// Получение информации о статусе задачи
	// (POST /dict/job/status)
	DictJobGetStatus(w http.ResponseWriter, r *http.Request)
	// Получение информации о статусе всех задач
	// (POST /dict/job/statusall)
	DictJobGetStatusAll(w http.ResponseWriter, r *http.Request)
	// Принудительная остановка задачи синхронизации справочника
	// (POST /dict/job/stop)
	DictJobStop(w http.ResponseWriter, r *http.Request)
	// Спец.метод преобразования адреса ЦОИД->КАТО
	// (POST /dict/kato/map/tsoid)
	DictKATOMapFromTSOID(w http.ResponseWriter, r *http.Request)
	// Просмотр списка справочников
	// (GET /dict/list)
	DictGetList(w http.ResponseWriter, r *http.Request, params DictGetListParams)
	// Метод для редактирования справочника
	// (POST /dict/update)
	DictUpdate(w http.ResponseWriter, r *http.Request)
	// Просмотр списка документов справочника по имени
	// (GET /dictionaries)
	GetDictionaries(w http.ResponseWriter, r *http.Request, params GetDictionariesParams)
	// Просмотр списка документов справочника по фильтрам данных
	// (POST /dictionaries/by-filter)
	GetDictionariesByFilter(w http.ResponseWriter, r *http.Request)
	// Получение локаций по родительским локациям
	// (GET /dictionaries/locations)
	DictGetLocations(w http.ResponseWriter, r *http.Request, params DictGetLocationsParams)
	// Подтверждение подписи нескольких документов через ОТП
	// (POST /documents/batch-sign-confirm)
	ConfirmSignDocumentsBatch(w http.ResponseWriter, r *http.Request, params ConfirmSignDocumentsBatchParams)
	// Метод для получения документов, доступных для просмотра всем
	// (POST /documents/public)
	RequestDocumentPublic(w http.ResponseWriter, r *http.Request, params RequestDocumentPublicParams)
	// Отправка ОТП для подтверждения подписания
	// (POST /documents/sign)
	SignDocumentByIDs(w http.ResponseWriter, r *http.Request, params SignDocumentByIDsParams)
	// Принятие кода ОТП по подписанию переданных документов
	// (POST /documents/sign-confirm)
	ConfirmSignDocuments(w http.ResponseWriter, r *http.Request, params ConfirmSignDocumentsParams)
	// Просмотр сгенерированного ранее документа
	// (GET /documents/{docID})
	GetDocumentByID(w http.ResponseWriter, r *http.Request, docID DocumentIDPathParam, params GetDocumentByIDParams)
	// Подпись сгенерированного ранее документа
	// (POST /documents/{docID}/sign)
	SignDocumentByID(w http.ResponseWriter, r *http.Request, docID DocumentIDPathParam, params SignDocumentByIDParams)
	// Подтверждение подписи документа через ОТП
	// (POST /documents/{docID}/sign-confirm)
	ConfirmSignDocumentByID(w http.ResponseWriter, r *http.Request, docID DocumentIDPathParam, params ConfirmSignDocumentByIDParams)
	// Загрузка PDF файла
	// (POST /fileguard)
	PostUnsecurePdfFile(w http.ResponseWriter, r *http.Request)
	// Проверка на работоспособность
	// (GET /health)
	Health(w http.ResponseWriter, r *http.Request)
	// Проверка на наличие активной кредитной заявки
	// (GET /loans/active-application-check)
	LoansCheckActiveLoanAppExists(w http.ResponseWriter, r *http.Request)
	// Метод для создания кредитной заявки.
	// (POST /loans/application)
	CreateLoanApplication(w http.ResponseWriter, r *http.Request)
	// Получение справочных значений банков для загрузки выписки
	// (GET /loans/bank-statement)
	GetBankStatement(w http.ResponseWriter, r *http.Request)
	// Получение условий выдачи кредита
	// (GET /loans/calc-data)
	GetLoansCalcData(w http.ResponseWriter, r *http.Request)
	// Расчет параметров платежей для всех сроков кредитов
	// (POST /loans/calculation)
	CalculateLoanTerms(w http.ResponseWriter, r *http.Request)
	// Метод для генерации и получения документа для подписи
	// (POST /loans/documents)
	DocumentsForLoanApp(w http.ResponseWriter, r *http.Request, params DocumentsForLoanAppParams)
	// Получение справочника по типам образования
	// (GET /loans/education-types)
	LoansGetEducationTypes(w http.ResponseWriter, r *http.Request)
	// Получение справочника по типам занятости
	// (GET /loans/employment-types)
	LoansGetEmploymentTypes(w http.ResponseWriter, r *http.Request)
	// Получение onboarding текстовок для кредита
	// (GET /loans/onboarding-texts)
	GetLoansOnboardingTexts(w http.ResponseWriter, r *http.Request, params GetLoansOnboardingTextsParams)
	// Получение справочника по видам отношений к контактным лицам
	// (GET /loans/relation-types)
	LoansGetRelationTypes(w http.ResponseWriter, r *http.Request)
	// Получение анкеты пользователя
	// (GET /loans/survey)
	GetLoanSurvey(w http.ResponseWriter, r *http.Request, params GetLoanSurveyParams)
	// Сохранение анкеты пользователя.
	// (POST /loans/survey)
	SaveLoanSurvey(w http.ResponseWriter, r *http.Request, params SaveLoanSurveyParams)
	// Метод для дозаполенения/обновление кредитной заявки.
	// (PUT /loans/{applicationID}/application)
	UpdateLoanApplication(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam)
	// Получение справочных значений банков для загрузки выписки
	// (GET /loans/{applicationID}/bank-statement)
	GetBankStatementV2(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam)
	// Метод для получения ссылки (liveness, eds)
	// (GET /loans/{applicationID}/bts-data)
	GetBtsDataForLoanApp(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params GetBtsDataForLoanAppParams)
	// Отмена заявки на кредит
	// (POST /loans/{applicationID}/cancel)
	LoansCancelLoanApplication(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam)
	// Получение детальной информации по выданному займу
	// (GET /loans/{applicationID}/details)
	GetLoansDetails(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params GetLoansDetailsParams)
	// Метод для получения документа для подписи
	// (POST /loans/{applicationID}/documents-for-sign)
	LoanDocumentForSign(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params LoanDocumentForSignParams)
	// Метод для проведения досрочного погашения (ЧДП/ПДП)
	// (POST /loans/{applicationID}/early-repay)
	LoansPostEarlyRepay(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam)
	// Метод для идентификации после ЭЦП
	// (POST /loans/{applicationID}/eds)
	LoansPostEdsBtsData(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params LoansPostEdsBtsDataParams)
	// Метод для сохранения кредитах в других банках
	// (POST /loans/{applicationID}/external-bank-loans)
	SaveUserExternalBankLoans(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam)
	// Метод для идентификации после liveness
	// (POST /loans/{applicationID}/identify)
	LoansPostIdentifyBtsData(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params LoansPostIdentifyBtsDataParams)
	// Получение  результата внутренних проверок кредитной заявки
	// (GET /loans/{applicationID}/internal-checks-result)
	LoansGetInternalChecksResult(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam)
	// Метод для публикации заявки с отправкой в СПР
	// (POST /loans/{applicationID}/publish)
	PublishLoanAppData(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam)
	// Получение справочной информации о рефинансировании
	// (GET /loans/{applicationID}/refinancing-info)
	GetRefinancingInfo(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params GetRefinancingInfoParams)
	// Метод для получения условий одобренного предложения от СПР
	// (GET /loans/{applicationID}/scoring-result)
	GetScoringResult(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params GetScoringResultParams)
	// Подтверждение подписи документов через ОТП
	// (POST /loans/{applicationID}/sign-confirm)
	LoansConfirmSignDocuments(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params LoansConfirmSignDocumentsParams)
	// Получение статуса подтвержденной заявки на кредит
	// (GET /loans/{applicationID}/status)
	LoansGetApprovedLoanAppStatus(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam)
	// Подтверждение подписи документа через ОТП
	// (POST /loans/{applicationID}/{docID}/sign-confirm)
	LoansConfirmSignDocumentByID(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, docID DocumentIDPathParam, params LoansConfirmSignDocumentByIDParams)
	// Перезапрос кода подтверждения
	// (POST /otp/retry)
	OtpRetry(w http.ResponseWriter, r *http.Request)
	// Проверка ИИН/БИН и номера счёта получателя
	// (POST /payments/check-account-iin)
	PaymentsCheckAccountIin(w http.ResponseWriter, r *http.Request)
	// Проверка клиента банка по номеру телефона
	// (POST /payments/check-client-by-phone-number)
	CheckClientByPhoneNumber(w http.ResponseWriter, r *http.Request)
	// Проверка номера телефона
	// (POST /payments/check-phone-number)
	CheckPhoneNumber(w http.ResponseWriter, r *http.Request)
	// Проверка отп и выполнение перевода по номеру телефона
	// (POST /payments/confirm-internal-payment-by-phone-number)
	ConfirmInternalPaymentByPhoneNumber(w http.ResponseWriter, r *http.Request)
	// Проверка отп и выполнение платежа для внутренних переводов
	// (POST /payments/confirm-payment-by-account)
	ConfirmPaymentByAccount(w http.ResponseWriter, r *http.Request)
	// Проверка отп и выполнение оплаты сотовой связи
	// (POST /payments/confirm-payment-for-mobile)
	ConfirmPaymentForMobile(w http.ResponseWriter, r *http.Request)
	// Создание внутрибанковского перевода по номеру телефона
	// (POST /payments/create-internal-payment-by-phone-number)
	CreateInternalPaymentByPhoneNumber(w http.ResponseWriter, r *http.Request)
	// Создание платежа для внутренних переводов
	// (POST /payments/create-payment-by-account)
	CreatePaymentByAccount(w http.ResponseWriter, r *http.Request)
	// Создание оплаты сотовой связи
	// (POST /payments/create-payment-for-mobile)
	CreatePaymentForMobile(w http.ResponseWriter, r *http.Request)
	// Создание платежа для оплаты с помощью QR
	// (POST /payments/create-payment-kaspiqr)
	CreatePaymentKaspiQR(w http.ResponseWriter, r *http.Request)
	// Перевод между своими счетами
	// (POST /payments/create-self-transfer)
	CreateSelfTransfer(w http.ResponseWriter, r *http.Request)
	// История платежей
	// (GET /payments/history)
	GetPaymentHistory(w http.ResponseWriter, r *http.Request, params GetPaymentHistoryParams)
	// Оповещения бэка о закрытии экрана проведения платежа
	// (POST /payments/qr-session-termination)
	QRSessionTermination(w http.ResponseWriter, r *http.Request)
	// Получить данные по платежу по данным QR Токена
	// (POST /payments/qr-token)
	QRToken(w http.ResponseWriter, r *http.Request)
	// История операций
	// (GET /payments/transactions)
	GetTransactions(w http.ResponseWriter, r *http.Request, params GetTransactionsParams)
	// Получение транзакции по индентификатору
	// (GET /payments/transactions/{transactionID})
	GetTransactionByID(w http.ResponseWriter, r *http.Request, transactionID PaymentsGetTransactionByIDTransactionID)
	// Получение квитанции по платежу
	// (GET /payments/transactions/{transactionID}/receipt)
	GetTransactionReceipt(w http.ResponseWriter, r *http.Request, transactionID PaymentsGetTransactionByIDTransactionID)
	// Атрибуция реферальной программы
	// (POST /referral-program/attribution)
	ReferralProgramAttribution(w http.ResponseWriter, r *http.Request)
	// Онбординг реферальной программы
	// (POST /referral-program/onboarding)
	ReferralProgramOnboarding(w http.ResponseWriter, r *http.Request)
	// Получение профиля реферальной программы
	// (GET /referral-program/profile)
	ReferralProgramProfile(w http.ResponseWriter, r *http.Request)
	// Проверка активности реферальной программы
	// (GET /referral-program/status)
	ReferralProgramStatus(w http.ResponseWriter, r *http.Request)
	// Получение списка задач
	// (GET /tasks)
	GetTasks(w http.ResponseWriter, r *http.Request, params GetTasksParams)
	// Получение детальной информации о задаче
	// (GET /tasks/{taskID})
	GetTaskByID(w http.ResponseWriter, r *http.Request, taskID TaskIDPathParam)
	// Подготовка данных для вызова SDK Thales
	// (POST /tokenize)
	StartTokenization(w http.ResponseWriter, r *http.Request)
	// Уведомление об изменении виртуальной карты (Thales → Issuer)
	// (POST /tokenize/state)
	NotifyVirtualCardChange(w http.ResponseWriter, r *http.Request, params NotifyVirtualCardChangeParams)
	// Метод получения информации по токенизации
	// (GET /tokenize/{cardId}/info)
	GetCardInfo(w http.ResponseWriter, r *http.Request, cardId ParametersCardIDPathParam)
	// Получить данные по обновлению токенизации карты
	// (GET /tokenize/{cardId}/state/update)
	GetTokenizeState(w http.ResponseWriter, r *http.Request, cardId ParametersCardIDPathParam)
	// Получение счета пользователя
	// (GET /user/accounts/{accountID})
	GetUserAccounts(w http.ResponseWriter, r *http.Request, accountID AccountIDPathParam)
	// Список текущих карт и счетов пользователя
	// (GET /user/cards)
	UserCards(w http.ResponseWriter, r *http.Request)
	// Получение депозитов пользователя
	// (GET /user/deposits)
	GetUserDeposits(w http.ResponseWriter, r *http.Request)
	// Получение детализации депозита
	// (GET /user/deposits/{depositID})
	GetDepositDetail(w http.ResponseWriter, r *http.Request, depositID DepositDetailsParams)
	// Получение информации о кредитах пользователя
	// (GET /user/loans)
	GetLoans(w http.ResponseWriter, r *http.Request, params GetLoansParams)
	// Метод для обновления локализации пользователя
	// (POST /user/locale)
	UsersUpdateUserLocale(w http.ResponseWriter, r *http.Request, params UsersUpdateUserLocaleParams)
	// Удаление профиля из системы
	// (POST /user/profile)
	ProfileDelete(w http.ResponseWriter, r *http.Request)
}

// Unimplemented server implementation that returns http.StatusNotImplemented for each endpoint.

type Unimplemented struct{}

// Генерация документа для выпуска виртуальной карты
// (POST /accounts/application-for-sign)
func (_ Unimplemented) ApplicationForSign(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для получения документов из счетов
// (POST /accounts/documents)
func (_ Unimplemented) CreateAccountsDocument(w http.ResponseWriter, r *http.Request, params CreateAccountsDocumentParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для получения ссылки liveness
// (GET /auth/bts-data)
func (_ Unimplemented) GetBtsDataForAuth(w http.ResponseWriter, r *http.Request, params GetBtsDataForAuthParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Подтверждение авторизации
// (POST /auth/confirm)
func (_ Unimplemented) AuthConfirm(w http.ResponseWriter, r *http.Request, params AuthConfirmParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для генерации и получения документа для подписи
// (POST /auth/document-for-sign)
func (_ Unimplemented) DocumentForSign(w http.ResponseWriter, r *http.Request, params DocumentForSignParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Идентификация пользователя
// (POST /auth/identify)
func (_ Unimplemented) AuthIdentify(w http.ResponseWriter, r *http.Request, params AuthIdentifyParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Авторизация пользователя по телефону
// (POST /auth/login)
func (_ Unimplemented) AuthLogin(w http.ResponseWriter, r *http.Request, params AuthLoginParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Выход из системы
// (POST /auth/logout)
func (_ Unimplemented) AuthLogout(w http.ResponseWriter, r *http.Request, params AuthLogoutParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Обновление токенов пользователя
// (POST /auth/refresh)
func (_ Unimplemented) AuthRefresh(w http.ResponseWriter, r *http.Request, params AuthRefreshParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение полных реквизитов карты
// (GET /cards/{cardId}/requisites)
func (_ Unimplemented) GetRequisites(w http.ResponseWriter, r *http.Request, cardId CardIDPathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Расчет доходности депозита
// (POST /deposits/condition-profit)
func (_ Unimplemented) DepositConditionProfit(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Создание офера депозита
// (POST /deposits/create-offer)
func (_ Unimplemented) CreateDepositOffer(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение доступных счетов пользователя
// (GET /deposits/get-available-accounts)
func (_ Unimplemented) GetAvailableAccounts(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение информации о продукте
// (GET /deposits/product-info/{productCode})
func (_ Unimplemented) GetProductInfo(w http.ResponseWriter, r *http.Request, productCode GetProductInfo) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Расчет доходности для указанной суммы/срока/ставки
// (GET /deposits/utils/calculate-profit)
func (_ Unimplemented) CalculateProfit(w http.ResponseWriter, r *http.Request, params CalculateProfitParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для создания нового справочника
// (POST /dict/create)
func (_ Unimplemented) DictCreate(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для удаления справочника
// (POST /dict/delete)
func (_ Unimplemented) DictDelete(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Просмотр списка документов справочника по фильтрам данных
// (POST /dict/doc/by-filters)
func (_ Unimplemented) DictDocGetListByFilters(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Просмотр документа по именам справочника и документа
// (GET /dict/doc/by-name/{dict_name}/{doc_name})
func (_ Unimplemented) DictDocGetByName(w http.ResponseWriter, r *http.Request, dictName DictionaryNamePathParam, docName DictDocumentNamePathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для создания нового документа в справочнике
// (POST /dict/doc/create)
func (_ Unimplemented) DictDocCreate(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для удаления документа в справочнике
// (POST /dict/doc/delete)
func (_ Unimplemented) DictDocDelete(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Просмотр документа справочника по ID документа
// (GET /dict/doc/get/{doc_id})
func (_ Unimplemented) DictDocGet(w http.ResponseWriter, r *http.Request, docId DictDocumentIDPathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для установки индекса сортировки
// (POST /dict/doc/order-update)
func (_ Unimplemented) DictDocOrderUpdate(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Возвращает путь для древовидной структуры справочника
// (POST /dict/doc/tree/path)
func (_ Unimplemented) DictDocGetTreeLine(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для изменения документа в справочнике
// (POST /dict/doc/update)
func (_ Unimplemented) DictDocUpdate(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Просмотр данных справочника по его ID
// (GET /dict/get/{dict_id})
func (_ Unimplemented) DictGet(w http.ResponseWriter, r *http.Request, dictId DictionaryIDPathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Запуск задачи синхронизации справочника
// (POST /dict/job/run)
func (_ Unimplemented) DictJobRun(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение информации о статусе задачи
// (POST /dict/job/status)
func (_ Unimplemented) DictJobGetStatus(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение информации о статусе всех задач
// (POST /dict/job/statusall)
func (_ Unimplemented) DictJobGetStatusAll(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Принудительная остановка задачи синхронизации справочника
// (POST /dict/job/stop)
func (_ Unimplemented) DictJobStop(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Спец.метод преобразования адреса ЦОИД->КАТО
// (POST /dict/kato/map/tsoid)
func (_ Unimplemented) DictKATOMapFromTSOID(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Просмотр списка справочников
// (GET /dict/list)
func (_ Unimplemented) DictGetList(w http.ResponseWriter, r *http.Request, params DictGetListParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для редактирования справочника
// (POST /dict/update)
func (_ Unimplemented) DictUpdate(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Просмотр списка документов справочника по имени
// (GET /dictionaries)
func (_ Unimplemented) GetDictionaries(w http.ResponseWriter, r *http.Request, params GetDictionariesParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Просмотр списка документов справочника по фильтрам данных
// (POST /dictionaries/by-filter)
func (_ Unimplemented) GetDictionariesByFilter(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение локаций по родительским локациям
// (GET /dictionaries/locations)
func (_ Unimplemented) DictGetLocations(w http.ResponseWriter, r *http.Request, params DictGetLocationsParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Подтверждение подписи нескольких документов через ОТП
// (POST /documents/batch-sign-confirm)
func (_ Unimplemented) ConfirmSignDocumentsBatch(w http.ResponseWriter, r *http.Request, params ConfirmSignDocumentsBatchParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для получения документов, доступных для просмотра всем
// (POST /documents/public)
func (_ Unimplemented) RequestDocumentPublic(w http.ResponseWriter, r *http.Request, params RequestDocumentPublicParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Отправка ОТП для подтверждения подписания
// (POST /documents/sign)
func (_ Unimplemented) SignDocumentByIDs(w http.ResponseWriter, r *http.Request, params SignDocumentByIDsParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Принятие кода ОТП по подписанию переданных документов
// (POST /documents/sign-confirm)
func (_ Unimplemented) ConfirmSignDocuments(w http.ResponseWriter, r *http.Request, params ConfirmSignDocumentsParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Просмотр сгенерированного ранее документа
// (GET /documents/{docID})
func (_ Unimplemented) GetDocumentByID(w http.ResponseWriter, r *http.Request, docID DocumentIDPathParam, params GetDocumentByIDParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Подпись сгенерированного ранее документа
// (POST /documents/{docID}/sign)
func (_ Unimplemented) SignDocumentByID(w http.ResponseWriter, r *http.Request, docID DocumentIDPathParam, params SignDocumentByIDParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Подтверждение подписи документа через ОТП
// (POST /documents/{docID}/sign-confirm)
func (_ Unimplemented) ConfirmSignDocumentByID(w http.ResponseWriter, r *http.Request, docID DocumentIDPathParam, params ConfirmSignDocumentByIDParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Загрузка PDF файла
// (POST /fileguard)
func (_ Unimplemented) PostUnsecurePdfFile(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Проверка на работоспособность
// (GET /health)
func (_ Unimplemented) Health(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Проверка на наличие активной кредитной заявки
// (GET /loans/active-application-check)
func (_ Unimplemented) LoansCheckActiveLoanAppExists(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для создания кредитной заявки.
// (POST /loans/application)
func (_ Unimplemented) CreateLoanApplication(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение справочных значений банков для загрузки выписки
// (GET /loans/bank-statement)
func (_ Unimplemented) GetBankStatement(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение условий выдачи кредита
// (GET /loans/calc-data)
func (_ Unimplemented) GetLoansCalcData(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Расчет параметров платежей для всех сроков кредитов
// (POST /loans/calculation)
func (_ Unimplemented) CalculateLoanTerms(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для генерации и получения документа для подписи
// (POST /loans/documents)
func (_ Unimplemented) DocumentsForLoanApp(w http.ResponseWriter, r *http.Request, params DocumentsForLoanAppParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение справочника по типам образования
// (GET /loans/education-types)
func (_ Unimplemented) LoansGetEducationTypes(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение справочника по типам занятости
// (GET /loans/employment-types)
func (_ Unimplemented) LoansGetEmploymentTypes(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение onboarding текстовок для кредита
// (GET /loans/onboarding-texts)
func (_ Unimplemented) GetLoansOnboardingTexts(w http.ResponseWriter, r *http.Request, params GetLoansOnboardingTextsParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение справочника по видам отношений к контактным лицам
// (GET /loans/relation-types)
func (_ Unimplemented) LoansGetRelationTypes(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение анкеты пользователя
// (GET /loans/survey)
func (_ Unimplemented) GetLoanSurvey(w http.ResponseWriter, r *http.Request, params GetLoanSurveyParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Сохранение анкеты пользователя.
// (POST /loans/survey)
func (_ Unimplemented) SaveLoanSurvey(w http.ResponseWriter, r *http.Request, params SaveLoanSurveyParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для дозаполенения/обновление кредитной заявки.
// (PUT /loans/{applicationID}/application)
func (_ Unimplemented) UpdateLoanApplication(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение справочных значений банков для загрузки выписки
// (GET /loans/{applicationID}/bank-statement)
func (_ Unimplemented) GetBankStatementV2(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для получения ссылки (liveness, eds)
// (GET /loans/{applicationID}/bts-data)
func (_ Unimplemented) GetBtsDataForLoanApp(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params GetBtsDataForLoanAppParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Отмена заявки на кредит
// (POST /loans/{applicationID}/cancel)
func (_ Unimplemented) LoansCancelLoanApplication(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение детальной информации по выданному займу
// (GET /loans/{applicationID}/details)
func (_ Unimplemented) GetLoansDetails(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params GetLoansDetailsParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для получения документа для подписи
// (POST /loans/{applicationID}/documents-for-sign)
func (_ Unimplemented) LoanDocumentForSign(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params LoanDocumentForSignParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для проведения досрочного погашения (ЧДП/ПДП)
// (POST /loans/{applicationID}/early-repay)
func (_ Unimplemented) LoansPostEarlyRepay(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для идентификации после ЭЦП
// (POST /loans/{applicationID}/eds)
func (_ Unimplemented) LoansPostEdsBtsData(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params LoansPostEdsBtsDataParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для сохранения кредитах в других банках
// (POST /loans/{applicationID}/external-bank-loans)
func (_ Unimplemented) SaveUserExternalBankLoans(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для идентификации после liveness
// (POST /loans/{applicationID}/identify)
func (_ Unimplemented) LoansPostIdentifyBtsData(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params LoansPostIdentifyBtsDataParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение  результата внутренних проверок кредитной заявки
// (GET /loans/{applicationID}/internal-checks-result)
func (_ Unimplemented) LoansGetInternalChecksResult(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для публикации заявки с отправкой в СПР
// (POST /loans/{applicationID}/publish)
func (_ Unimplemented) PublishLoanAppData(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение справочной информации о рефинансировании
// (GET /loans/{applicationID}/refinancing-info)
func (_ Unimplemented) GetRefinancingInfo(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params GetRefinancingInfoParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для получения условий одобренного предложения от СПР
// (GET /loans/{applicationID}/scoring-result)
func (_ Unimplemented) GetScoringResult(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params GetScoringResultParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Подтверждение подписи документов через ОТП
// (POST /loans/{applicationID}/sign-confirm)
func (_ Unimplemented) LoansConfirmSignDocuments(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, params LoansConfirmSignDocumentsParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение статуса подтвержденной заявки на кредит
// (GET /loans/{applicationID}/status)
func (_ Unimplemented) LoansGetApprovedLoanAppStatus(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Подтверждение подписи документа через ОТП
// (POST /loans/{applicationID}/{docID}/sign-confirm)
func (_ Unimplemented) LoansConfirmSignDocumentByID(w http.ResponseWriter, r *http.Request, applicationID ApplicationIDPathParam, docID DocumentIDPathParam, params LoansConfirmSignDocumentByIDParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Перезапрос кода подтверждения
// (POST /otp/retry)
func (_ Unimplemented) OtpRetry(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Проверка ИИН/БИН и номера счёта получателя
// (POST /payments/check-account-iin)
func (_ Unimplemented) PaymentsCheckAccountIin(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Проверка клиента банка по номеру телефона
// (POST /payments/check-client-by-phone-number)
func (_ Unimplemented) CheckClientByPhoneNumber(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Проверка номера телефона
// (POST /payments/check-phone-number)
func (_ Unimplemented) CheckPhoneNumber(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Проверка отп и выполнение перевода по номеру телефона
// (POST /payments/confirm-internal-payment-by-phone-number)
func (_ Unimplemented) ConfirmInternalPaymentByPhoneNumber(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Проверка отп и выполнение платежа для внутренних переводов
// (POST /payments/confirm-payment-by-account)
func (_ Unimplemented) ConfirmPaymentByAccount(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Проверка отп и выполнение оплаты сотовой связи
// (POST /payments/confirm-payment-for-mobile)
func (_ Unimplemented) ConfirmPaymentForMobile(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Создание внутрибанковского перевода по номеру телефона
// (POST /payments/create-internal-payment-by-phone-number)
func (_ Unimplemented) CreateInternalPaymentByPhoneNumber(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Создание платежа для внутренних переводов
// (POST /payments/create-payment-by-account)
func (_ Unimplemented) CreatePaymentByAccount(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Создание оплаты сотовой связи
// (POST /payments/create-payment-for-mobile)
func (_ Unimplemented) CreatePaymentForMobile(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Создание платежа для оплаты с помощью QR
// (POST /payments/create-payment-kaspiqr)
func (_ Unimplemented) CreatePaymentKaspiQR(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Перевод между своими счетами
// (POST /payments/create-self-transfer)
func (_ Unimplemented) CreateSelfTransfer(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// История платежей
// (GET /payments/history)
func (_ Unimplemented) GetPaymentHistory(w http.ResponseWriter, r *http.Request, params GetPaymentHistoryParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Оповещения бэка о закрытии экрана проведения платежа
// (POST /payments/qr-session-termination)
func (_ Unimplemented) QRSessionTermination(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получить данные по платежу по данным QR Токена
// (POST /payments/qr-token)
func (_ Unimplemented) QRToken(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// История операций
// (GET /payments/transactions)
func (_ Unimplemented) GetTransactions(w http.ResponseWriter, r *http.Request, params GetTransactionsParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение транзакции по индентификатору
// (GET /payments/transactions/{transactionID})
func (_ Unimplemented) GetTransactionByID(w http.ResponseWriter, r *http.Request, transactionID PaymentsGetTransactionByIDTransactionID) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение квитанции по платежу
// (GET /payments/transactions/{transactionID}/receipt)
func (_ Unimplemented) GetTransactionReceipt(w http.ResponseWriter, r *http.Request, transactionID PaymentsGetTransactionByIDTransactionID) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Атрибуция реферальной программы
// (POST /referral-program/attribution)
func (_ Unimplemented) ReferralProgramAttribution(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Онбординг реферальной программы
// (POST /referral-program/onboarding)
func (_ Unimplemented) ReferralProgramOnboarding(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение профиля реферальной программы
// (GET /referral-program/profile)
func (_ Unimplemented) ReferralProgramProfile(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Проверка активности реферальной программы
// (GET /referral-program/status)
func (_ Unimplemented) ReferralProgramStatus(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение списка задач
// (GET /tasks)
func (_ Unimplemented) GetTasks(w http.ResponseWriter, r *http.Request, params GetTasksParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение детальной информации о задаче
// (GET /tasks/{taskID})
func (_ Unimplemented) GetTaskByID(w http.ResponseWriter, r *http.Request, taskID TaskIDPathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Подготовка данных для вызова SDK Thales
// (POST /tokenize)
func (_ Unimplemented) StartTokenization(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Уведомление об изменении виртуальной карты (Thales → Issuer)
// (POST /tokenize/state)
func (_ Unimplemented) NotifyVirtualCardChange(w http.ResponseWriter, r *http.Request, params NotifyVirtualCardChangeParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод получения информации по токенизации
// (GET /tokenize/{cardId}/info)
func (_ Unimplemented) GetCardInfo(w http.ResponseWriter, r *http.Request, cardId ParametersCardIDPathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получить данные по обновлению токенизации карты
// (GET /tokenize/{cardId}/state/update)
func (_ Unimplemented) GetTokenizeState(w http.ResponseWriter, r *http.Request, cardId ParametersCardIDPathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение счета пользователя
// (GET /user/accounts/{accountID})
func (_ Unimplemented) GetUserAccounts(w http.ResponseWriter, r *http.Request, accountID AccountIDPathParam) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Список текущих карт и счетов пользователя
// (GET /user/cards)
func (_ Unimplemented) UserCards(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение депозитов пользователя
// (GET /user/deposits)
func (_ Unimplemented) GetUserDeposits(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение детализации депозита
// (GET /user/deposits/{depositID})
func (_ Unimplemented) GetDepositDetail(w http.ResponseWriter, r *http.Request, depositID DepositDetailsParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Получение информации о кредитах пользователя
// (GET /user/loans)
func (_ Unimplemented) GetLoans(w http.ResponseWriter, r *http.Request, params GetLoansParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Метод для обновления локализации пользователя
// (POST /user/locale)
func (_ Unimplemented) UsersUpdateUserLocale(w http.ResponseWriter, r *http.Request, params UsersUpdateUserLocaleParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Удаление профиля из системы
// (POST /user/profile)
func (_ Unimplemented) ProfileDelete(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandlerFunc   func(w http.ResponseWriter, r *http.Request, err error)
}

type MiddlewareFunc func(http.Handler) http.Handler

// ApplicationForSign operation middleware
func (siw *ServerInterfaceWrapper) ApplicationForSign(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ApplicationForSign(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// CreateAccountsDocument operation middleware
func (siw *ServerInterfaceWrapper) CreateAccountsDocument(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params CreateAccountsDocumentParams

	// ------------- Required query parameter "docType" -------------

	if paramValue := r.URL.Query().Get("docType"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "docType"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "docType", r.URL.Query(), &params.DocType)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "docType", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.CreateAccountsDocument(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetBtsDataForAuth operation middleware
func (siw *ServerInterfaceWrapper) GetBtsDataForAuth(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetBtsDataForAuthParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage GetBtsDataForAuthParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetBtsDataForAuth(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// AuthConfirm operation middleware
func (siw *ServerInterfaceWrapper) AuthConfirm(w http.ResponseWriter, r *http.Request) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params AuthConfirmParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage AuthConfirmParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.AuthConfirm(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DocumentForSign operation middleware
func (siw *ServerInterfaceWrapper) DocumentForSign(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params DocumentForSignParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage DocumentForSignParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DocumentForSign(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// AuthIdentify operation middleware
func (siw *ServerInterfaceWrapper) AuthIdentify(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params AuthIdentifyParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage AuthIdentifyParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.AuthIdentify(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// AuthLogin operation middleware
func (siw *ServerInterfaceWrapper) AuthLogin(w http.ResponseWriter, r *http.Request) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params AuthLoginParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage AuthLoginParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	// ------------- Optional header parameter "entryPoint" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("entryPoint")]; found {
		var EntryPoint EntryPointHeader
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "entryPoint", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "entryPoint", valueList[0], &EntryPoint, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "entryPoint", Err: err})
			return
		}

		params.EntryPoint = &EntryPoint

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.AuthLogin(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// AuthLogout operation middleware
func (siw *ServerInterfaceWrapper) AuthLogout(w http.ResponseWriter, r *http.Request) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params AuthLogoutParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage AuthLogoutParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.AuthLogout(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// AuthRefresh operation middleware
func (siw *ServerInterfaceWrapper) AuthRefresh(w http.ResponseWriter, r *http.Request) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params AuthRefreshParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage AuthRefreshParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.AuthRefresh(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetRequisites operation middleware
func (siw *ServerInterfaceWrapper) GetRequisites(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "cardId" -------------
	var cardId CardIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "cardId", chi.URLParam(r, "cardId"), &cardId, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "cardId", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetRequisites(w, r, cardId)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DepositConditionProfit operation middleware
func (siw *ServerInterfaceWrapper) DepositConditionProfit(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DepositConditionProfit(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// CreateDepositOffer operation middleware
func (siw *ServerInterfaceWrapper) CreateDepositOffer(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.CreateDepositOffer(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetAvailableAccounts operation middleware
func (siw *ServerInterfaceWrapper) GetAvailableAccounts(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetAvailableAccounts(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetProductInfo operation middleware
func (siw *ServerInterfaceWrapper) GetProductInfo(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "productCode" -------------
	var productCode GetProductInfo

	err = runtime.BindStyledParameterWithOptions("simple", "productCode", chi.URLParam(r, "productCode"), &productCode, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "productCode", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetProductInfo(w, r, productCode)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// CalculateProfit operation middleware
func (siw *ServerInterfaceWrapper) CalculateProfit(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params CalculateProfitParams

	// ------------- Required query parameter "amount" -------------

	if paramValue := r.URL.Query().Get("amount"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "amount"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "amount", r.URL.Query(), &params.Amount)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "amount", Err: err})
		return
	}

	// ------------- Required query parameter "termMonths" -------------

	if paramValue := r.URL.Query().Get("termMonths"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "termMonths"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "termMonths", r.URL.Query(), &params.TermMonths)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "termMonths", Err: err})
		return
	}

	// ------------- Required query parameter "profitRate" -------------

	if paramValue := r.URL.Query().Get("profitRate"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "profitRate"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "profitRate", r.URL.Query(), &params.ProfitRate)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "profitRate", Err: err})
		return
	}

	// ------------- Required query parameter "payoutMethod" -------------

	if paramValue := r.URL.Query().Get("payoutMethod"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "payoutMethod"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "payoutMethod", r.URL.Query(), &params.PayoutMethod)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "payoutMethod", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.CalculateProfit(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DictCreate operation middleware
func (siw *ServerInterfaceWrapper) DictCreate(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DictCreate(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DictDelete operation middleware
func (siw *ServerInterfaceWrapper) DictDelete(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DictDelete(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DictDocGetListByFilters operation middleware
func (siw *ServerInterfaceWrapper) DictDocGetListByFilters(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DictDocGetListByFilters(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DictDocGetByName operation middleware
func (siw *ServerInterfaceWrapper) DictDocGetByName(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "dict_name" -------------
	var dictName DictionaryNamePathParam

	err = runtime.BindStyledParameterWithOptions("simple", "dict_name", chi.URLParam(r, "dict_name"), &dictName, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "dict_name", Err: err})
		return
	}

	// ------------- Path parameter "doc_name" -------------
	var docName DictDocumentNamePathParam

	err = runtime.BindStyledParameterWithOptions("simple", "doc_name", chi.URLParam(r, "doc_name"), &docName, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "doc_name", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DictDocGetByName(w, r, dictName, docName)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DictDocCreate operation middleware
func (siw *ServerInterfaceWrapper) DictDocCreate(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DictDocCreate(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DictDocDelete operation middleware
func (siw *ServerInterfaceWrapper) DictDocDelete(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DictDocDelete(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DictDocGet operation middleware
func (siw *ServerInterfaceWrapper) DictDocGet(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "doc_id" -------------
	var docId DictDocumentIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "doc_id", chi.URLParam(r, "doc_id"), &docId, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "doc_id", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DictDocGet(w, r, docId)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DictDocOrderUpdate operation middleware
func (siw *ServerInterfaceWrapper) DictDocOrderUpdate(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DictDocOrderUpdate(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DictDocGetTreeLine operation middleware
func (siw *ServerInterfaceWrapper) DictDocGetTreeLine(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DictDocGetTreeLine(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DictDocUpdate operation middleware
func (siw *ServerInterfaceWrapper) DictDocUpdate(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DictDocUpdate(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DictGet operation middleware
func (siw *ServerInterfaceWrapper) DictGet(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "dict_id" -------------
	var dictId DictionaryIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "dict_id", chi.URLParam(r, "dict_id"), &dictId, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "dict_id", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DictGet(w, r, dictId)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DictJobRun operation middleware
func (siw *ServerInterfaceWrapper) DictJobRun(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DictJobRun(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DictJobGetStatus operation middleware
func (siw *ServerInterfaceWrapper) DictJobGetStatus(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DictJobGetStatus(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DictJobGetStatusAll operation middleware
func (siw *ServerInterfaceWrapper) DictJobGetStatusAll(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DictJobGetStatusAll(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DictJobStop operation middleware
func (siw *ServerInterfaceWrapper) DictJobStop(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DictJobStop(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DictKATOMapFromTSOID operation middleware
func (siw *ServerInterfaceWrapper) DictKATOMapFromTSOID(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DictKATOMapFromTSOID(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DictGetList operation middleware
func (siw *ServerInterfaceWrapper) DictGetList(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params DictGetListParams

	// ------------- Optional query parameter "page" -------------

	err = runtime.BindQueryParameter("form", true, false, "page", r.URL.Query(), &params.Page)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "page", Err: err})
		return
	}

	// ------------- Optional query parameter "count" -------------

	err = runtime.BindQueryParameter("form", true, false, "count", r.URL.Query(), &params.Count)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "count", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DictGetList(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DictUpdate operation middleware
func (siw *ServerInterfaceWrapper) DictUpdate(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DictUpdate(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetDictionaries operation middleware
func (siw *ServerInterfaceWrapper) GetDictionaries(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetDictionariesParams

	// ------------- Required query parameter "name" -------------

	if paramValue := r.URL.Query().Get("name"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "name"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "name", r.URL.Query(), &params.Name)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "name", Err: err})
		return
	}

	// ------------- Optional query parameter "sort" -------------

	err = runtime.BindQueryParameter("form", false, false, "sort", r.URL.Query(), &params.Sort)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "sort", Err: err})
		return
	}

	// ------------- Optional query parameter "page" -------------

	err = runtime.BindQueryParameter("form", true, false, "page", r.URL.Query(), &params.Page)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "page", Err: err})
		return
	}

	// ------------- Optional query parameter "count" -------------

	err = runtime.BindQueryParameter("form", true, false, "count", r.URL.Query(), &params.Count)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "count", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetDictionaries(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetDictionariesByFilter operation middleware
func (siw *ServerInterfaceWrapper) GetDictionariesByFilter(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetDictionariesByFilter(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DictGetLocations operation middleware
func (siw *ServerInterfaceWrapper) DictGetLocations(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params DictGetLocationsParams

	// ------------- Optional query parameter "parentID" -------------

	err = runtime.BindQueryParameter("form", true, false, "parentID", r.URL.Query(), &params.ParentID)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "parentID", Err: err})
		return
	}

	// ------------- Optional query parameter "parentIds" -------------

	err = runtime.BindQueryParameter("form", false, false, "parentIds", r.URL.Query(), &params.ParentIds)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "parentIds", Err: err})
		return
	}

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage DictGetLocationsParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DictGetLocations(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// ConfirmSignDocumentsBatch operation middleware
func (siw *ServerInterfaceWrapper) ConfirmSignDocumentsBatch(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params ConfirmSignDocumentsBatchParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage ConfirmSignDocumentsBatchParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ConfirmSignDocumentsBatch(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// RequestDocumentPublic operation middleware
func (siw *ServerInterfaceWrapper) RequestDocumentPublic(w http.ResponseWriter, r *http.Request) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params RequestDocumentPublicParams

	// ------------- Required query parameter "docType" -------------

	if paramValue := r.URL.Query().Get("docType"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "docType"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "docType", r.URL.Query(), &params.DocType)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "docType", Err: err})
		return
	}

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage RequestDocumentPublicParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.RequestDocumentPublic(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// SignDocumentByIDs operation middleware
func (siw *ServerInterfaceWrapper) SignDocumentByIDs(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params SignDocumentByIDsParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage SignDocumentByIDsParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SignDocumentByIDs(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// ConfirmSignDocuments operation middleware
func (siw *ServerInterfaceWrapper) ConfirmSignDocuments(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params ConfirmSignDocumentsParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage ConfirmSignDocumentsParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ConfirmSignDocuments(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetDocumentByID operation middleware
func (siw *ServerInterfaceWrapper) GetDocumentByID(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "docID" -------------
	var docID DocumentIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "docID", chi.URLParam(r, "docID"), &docID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "docID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetDocumentByIDParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage GetDocumentByIDParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetDocumentByID(w, r, docID, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// SignDocumentByID operation middleware
func (siw *ServerInterfaceWrapper) SignDocumentByID(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "docID" -------------
	var docID DocumentIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "docID", chi.URLParam(r, "docID"), &docID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "docID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params SignDocumentByIDParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage SignDocumentByIDParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SignDocumentByID(w, r, docID, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// ConfirmSignDocumentByID operation middleware
func (siw *ServerInterfaceWrapper) ConfirmSignDocumentByID(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "docID" -------------
	var docID DocumentIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "docID", chi.URLParam(r, "docID"), &docID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "docID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params ConfirmSignDocumentByIDParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage ConfirmSignDocumentByIDParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ConfirmSignDocumentByID(w, r, docID, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// PostUnsecurePdfFile operation middleware
func (siw *ServerInterfaceWrapper) PostUnsecurePdfFile(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PostUnsecurePdfFile(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// Health operation middleware
func (siw *ServerInterfaceWrapper) Health(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.Health(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoansCheckActiveLoanAppExists operation middleware
func (siw *ServerInterfaceWrapper) LoansCheckActiveLoanAppExists(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoansCheckActiveLoanAppExists(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// CreateLoanApplication operation middleware
func (siw *ServerInterfaceWrapper) CreateLoanApplication(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.CreateLoanApplication(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetBankStatement operation middleware
func (siw *ServerInterfaceWrapper) GetBankStatement(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetBankStatement(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetLoansCalcData operation middleware
func (siw *ServerInterfaceWrapper) GetLoansCalcData(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetLoansCalcData(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// CalculateLoanTerms operation middleware
func (siw *ServerInterfaceWrapper) CalculateLoanTerms(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.CalculateLoanTerms(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DocumentsForLoanApp operation middleware
func (siw *ServerInterfaceWrapper) DocumentsForLoanApp(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params DocumentsForLoanAppParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage DocumentsForLoanAppParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DocumentsForLoanApp(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoansGetEducationTypes operation middleware
func (siw *ServerInterfaceWrapper) LoansGetEducationTypes(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoansGetEducationTypes(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoansGetEmploymentTypes operation middleware
func (siw *ServerInterfaceWrapper) LoansGetEmploymentTypes(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoansGetEmploymentTypes(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetLoansOnboardingTexts operation middleware
func (siw *ServerInterfaceWrapper) GetLoansOnboardingTexts(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetLoansOnboardingTextsParams

	// ------------- Optional query parameter "source" -------------

	err = runtime.BindQueryParameter("form", true, false, "source", r.URL.Query(), &params.Source)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "source", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetLoansOnboardingTexts(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoansGetRelationTypes operation middleware
func (siw *ServerInterfaceWrapper) LoansGetRelationTypes(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoansGetRelationTypes(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetLoanSurvey operation middleware
func (siw *ServerInterfaceWrapper) GetLoanSurvey(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetLoanSurveyParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage GetLoanSurveyParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	// ------------- Required header parameter "User-Agent" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("User-Agent")]; found {
		var UserAgent UserAgent
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "User-Agent", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "User-Agent", valueList[0], &UserAgent, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: true})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "User-Agent", Err: err})
			return
		}

		params.UserAgent = UserAgent

	} else {
		err := fmt.Errorf("Header parameter User-Agent is required, but not found")
		siw.ErrorHandlerFunc(w, r, &RequiredHeaderError{ParamName: "User-Agent", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetLoanSurvey(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// SaveLoanSurvey operation middleware
func (siw *ServerInterfaceWrapper) SaveLoanSurvey(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params SaveLoanSurveyParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage SaveLoanSurveyParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SaveLoanSurvey(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// UpdateLoanApplication operation middleware
func (siw *ServerInterfaceWrapper) UpdateLoanApplication(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.UpdateLoanApplication(w, r, applicationID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetBankStatementV2 operation middleware
func (siw *ServerInterfaceWrapper) GetBankStatementV2(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetBankStatementV2(w, r, applicationID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetBtsDataForLoanApp operation middleware
func (siw *ServerInterfaceWrapper) GetBtsDataForLoanApp(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetBtsDataForLoanAppParams

	// ------------- Required query parameter "type" -------------

	if paramValue := r.URL.Query().Get("type"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "type"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "type", r.URL.Query(), &params.Type)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "type", Err: err})
		return
	}

	headers := r.Header

	// ------------- Required header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage GetBtsDataForLoanAppParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: true})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = AcceptLanguage

	} else {
		err := fmt.Errorf("Header parameter Accept-Language is required, but not found")
		siw.ErrorHandlerFunc(w, r, &RequiredHeaderError{ParamName: "Accept-Language", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetBtsDataForLoanApp(w, r, applicationID, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoansCancelLoanApplication operation middleware
func (siw *ServerInterfaceWrapper) LoansCancelLoanApplication(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoansCancelLoanApplication(w, r, applicationID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetLoansDetails operation middleware
func (siw *ServerInterfaceWrapper) GetLoansDetails(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetLoansDetailsParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage GetLoansDetailsParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetLoansDetails(w, r, applicationID, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoanDocumentForSign operation middleware
func (siw *ServerInterfaceWrapper) LoanDocumentForSign(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params LoanDocumentForSignParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage LoanDocumentForSignParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoanDocumentForSign(w, r, applicationID, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoansPostEarlyRepay operation middleware
func (siw *ServerInterfaceWrapper) LoansPostEarlyRepay(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoansPostEarlyRepay(w, r, applicationID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoansPostEdsBtsData operation middleware
func (siw *ServerInterfaceWrapper) LoansPostEdsBtsData(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params LoansPostEdsBtsDataParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage LoansPostEdsBtsDataParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoansPostEdsBtsData(w, r, applicationID, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// SaveUserExternalBankLoans operation middleware
func (siw *ServerInterfaceWrapper) SaveUserExternalBankLoans(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SaveUserExternalBankLoans(w, r, applicationID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoansPostIdentifyBtsData operation middleware
func (siw *ServerInterfaceWrapper) LoansPostIdentifyBtsData(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params LoansPostIdentifyBtsDataParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage LoansPostIdentifyBtsDataParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoansPostIdentifyBtsData(w, r, applicationID, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoansGetInternalChecksResult operation middleware
func (siw *ServerInterfaceWrapper) LoansGetInternalChecksResult(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoansGetInternalChecksResult(w, r, applicationID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// PublishLoanAppData operation middleware
func (siw *ServerInterfaceWrapper) PublishLoanAppData(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PublishLoanAppData(w, r, applicationID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetRefinancingInfo operation middleware
func (siw *ServerInterfaceWrapper) GetRefinancingInfo(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetRefinancingInfoParams

	// ------------- Required query parameter "step" -------------

	if paramValue := r.URL.Query().Get("step"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "step"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "step", r.URL.Query(), &params.Step)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "step", Err: err})
		return
	}

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage GetRefinancingInfoParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetRefinancingInfo(w, r, applicationID, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetScoringResult operation middleware
func (siw *ServerInterfaceWrapper) GetScoringResult(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetScoringResultParams

	headers := r.Header

	// ------------- Required header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage GetScoringResultParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: true})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = AcceptLanguage

	} else {
		err := fmt.Errorf("Header parameter Accept-Language is required, but not found")
		siw.ErrorHandlerFunc(w, r, &RequiredHeaderError{ParamName: "Accept-Language", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetScoringResult(w, r, applicationID, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoansConfirmSignDocuments operation middleware
func (siw *ServerInterfaceWrapper) LoansConfirmSignDocuments(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params LoansConfirmSignDocumentsParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage LoansConfirmSignDocumentsParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoansConfirmSignDocuments(w, r, applicationID, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoansGetApprovedLoanAppStatus operation middleware
func (siw *ServerInterfaceWrapper) LoansGetApprovedLoanAppStatus(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoansGetApprovedLoanAppStatus(w, r, applicationID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// LoansConfirmSignDocumentByID operation middleware
func (siw *ServerInterfaceWrapper) LoansConfirmSignDocumentByID(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "applicationID" -------------
	var applicationID ApplicationIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "applicationID", chi.URLParam(r, "applicationID"), &applicationID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "applicationID", Err: err})
		return
	}

	// ------------- Path parameter "docID" -------------
	var docID DocumentIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "docID", chi.URLParam(r, "docID"), &docID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "docID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params LoansConfirmSignDocumentByIDParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage LoansConfirmSignDocumentByIDParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LoansConfirmSignDocumentByID(w, r, applicationID, docID, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// OtpRetry operation middleware
func (siw *ServerInterfaceWrapper) OtpRetry(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.OtpRetry(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// PaymentsCheckAccountIin operation middleware
func (siw *ServerInterfaceWrapper) PaymentsCheckAccountIin(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PaymentsCheckAccountIin(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// CheckClientByPhoneNumber operation middleware
func (siw *ServerInterfaceWrapper) CheckClientByPhoneNumber(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.CheckClientByPhoneNumber(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// CheckPhoneNumber operation middleware
func (siw *ServerInterfaceWrapper) CheckPhoneNumber(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.CheckPhoneNumber(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// ConfirmInternalPaymentByPhoneNumber operation middleware
func (siw *ServerInterfaceWrapper) ConfirmInternalPaymentByPhoneNumber(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ConfirmInternalPaymentByPhoneNumber(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// ConfirmPaymentByAccount operation middleware
func (siw *ServerInterfaceWrapper) ConfirmPaymentByAccount(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ConfirmPaymentByAccount(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// ConfirmPaymentForMobile operation middleware
func (siw *ServerInterfaceWrapper) ConfirmPaymentForMobile(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ConfirmPaymentForMobile(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// CreateInternalPaymentByPhoneNumber operation middleware
func (siw *ServerInterfaceWrapper) CreateInternalPaymentByPhoneNumber(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.CreateInternalPaymentByPhoneNumber(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// CreatePaymentByAccount operation middleware
func (siw *ServerInterfaceWrapper) CreatePaymentByAccount(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.CreatePaymentByAccount(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// CreatePaymentForMobile operation middleware
func (siw *ServerInterfaceWrapper) CreatePaymentForMobile(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.CreatePaymentForMobile(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// CreatePaymentKaspiQR operation middleware
func (siw *ServerInterfaceWrapper) CreatePaymentKaspiQR(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.CreatePaymentKaspiQR(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// CreateSelfTransfer operation middleware
func (siw *ServerInterfaceWrapper) CreateSelfTransfer(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.CreateSelfTransfer(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetPaymentHistory operation middleware
func (siw *ServerInterfaceWrapper) GetPaymentHistory(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetPaymentHistoryParams

	// ------------- Required query parameter "clientIinBin" -------------

	if paramValue := r.URL.Query().Get("clientIinBin"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "clientIinBin"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "clientIinBin", r.URL.Query(), &params.ClientIinBin)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "clientIinBin", Err: err})
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", r.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "limit", Err: err})
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", r.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "offset", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetPaymentHistory(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// QRSessionTermination operation middleware
func (siw *ServerInterfaceWrapper) QRSessionTermination(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.QRSessionTermination(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// QRToken operation middleware
func (siw *ServerInterfaceWrapper) QRToken(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.QRToken(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetTransactions operation middleware
func (siw *ServerInterfaceWrapper) GetTransactions(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetTransactionsParams

	// ------------- Optional query parameter "startDate" -------------

	err = runtime.BindQueryParameter("form", true, false, "startDate", r.URL.Query(), &params.StartDate)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "startDate", Err: err})
		return
	}

	// ------------- Optional query parameter "endDate" -------------

	err = runtime.BindQueryParameter("form", true, false, "endDate", r.URL.Query(), &params.EndDate)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "endDate", Err: err})
		return
	}

	// ------------- Required query parameter "accounts" -------------

	if paramValue := r.URL.Query().Get("accounts"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "accounts"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "accounts", r.URL.Query(), &params.Accounts)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "accounts", Err: err})
		return
	}

	// ------------- Optional query parameter "cards" -------------

	err = runtime.BindQueryParameter("form", true, false, "cards", r.URL.Query(), &params.Cards)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "cards", Err: err})
		return
	}

	// ------------- Optional query parameter "direction" -------------

	err = runtime.BindQueryParameter("form", true, false, "direction", r.URL.Query(), &params.Direction)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "direction", Err: err})
		return
	}

	// ------------- Optional query parameter "counterparty" -------------

	err = runtime.BindQueryParameter("form", true, false, "counterparty", r.URL.Query(), &params.Counterparty)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "counterparty", Err: err})
		return
	}

	// ------------- Optional query parameter "minAmount" -------------

	err = runtime.BindQueryParameter("form", true, false, "minAmount", r.URL.Query(), &params.MinAmount)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "minAmount", Err: err})
		return
	}

	// ------------- Optional query parameter "maxAmount" -------------

	err = runtime.BindQueryParameter("form", true, false, "maxAmount", r.URL.Query(), &params.MaxAmount)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "maxAmount", Err: err})
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", r.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "limit", Err: err})
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", r.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "offset", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetTransactions(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetTransactionByID operation middleware
func (siw *ServerInterfaceWrapper) GetTransactionByID(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "transactionID" -------------
	var transactionID PaymentsGetTransactionByIDTransactionID

	err = runtime.BindStyledParameterWithOptions("simple", "transactionID", chi.URLParam(r, "transactionID"), &transactionID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "transactionID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetTransactionByID(w, r, transactionID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetTransactionReceipt operation middleware
func (siw *ServerInterfaceWrapper) GetTransactionReceipt(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "transactionID" -------------
	var transactionID PaymentsGetTransactionByIDTransactionID

	err = runtime.BindStyledParameterWithOptions("simple", "transactionID", chi.URLParam(r, "transactionID"), &transactionID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "transactionID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetTransactionReceipt(w, r, transactionID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// ReferralProgramAttribution operation middleware
func (siw *ServerInterfaceWrapper) ReferralProgramAttribution(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ReferralProgramAttribution(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// ReferralProgramOnboarding operation middleware
func (siw *ServerInterfaceWrapper) ReferralProgramOnboarding(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ReferralProgramOnboarding(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// ReferralProgramProfile operation middleware
func (siw *ServerInterfaceWrapper) ReferralProgramProfile(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ReferralProgramProfile(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// ReferralProgramStatus operation middleware
func (siw *ServerInterfaceWrapper) ReferralProgramStatus(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ReferralProgramStatus(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetTasks operation middleware
func (siw *ServerInterfaceWrapper) GetTasks(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetTasksParams

	// ------------- Optional query parameter "status" -------------

	err = runtime.BindQueryParameter("form", true, false, "status", r.URL.Query(), &params.Status)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "status", Err: err})
		return
	}

	// ------------- Optional query parameter "type" -------------

	err = runtime.BindQueryParameter("form", true, false, "type", r.URL.Query(), &params.Type)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "type", Err: err})
		return
	}

	// ------------- Optional query parameter "created_after" -------------

	err = runtime.BindQueryParameter("form", true, false, "created_after", r.URL.Query(), &params.CreatedAfter)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "created_after", Err: err})
		return
	}

	// ------------- Optional query parameter "created_before" -------------

	err = runtime.BindQueryParameter("form", true, false, "created_before", r.URL.Query(), &params.CreatedBefore)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "created_before", Err: err})
		return
	}

	// ------------- Optional query parameter "page" -------------

	err = runtime.BindQueryParameter("form", true, false, "page", r.URL.Query(), &params.Page)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "page", Err: err})
		return
	}

	// ------------- Optional query parameter "page_size" -------------

	err = runtime.BindQueryParameter("form", true, false, "page_size", r.URL.Query(), &params.PageSize)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "page_size", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetTasks(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetTaskByID operation middleware
func (siw *ServerInterfaceWrapper) GetTaskByID(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "taskID" -------------
	var taskID TaskIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "taskID", chi.URLParam(r, "taskID"), &taskID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "taskID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetTaskByID(w, r, taskID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// StartTokenization operation middleware
func (siw *ServerInterfaceWrapper) StartTokenization(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.StartTokenization(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// NotifyVirtualCardChange operation middleware
func (siw *ServerInterfaceWrapper) NotifyVirtualCardChange(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params NotifyVirtualCardChangeParams

	headers := r.Header

	// ------------- Required header parameter "x-correlation-id" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("x-correlation-id")]; found {
		var XCorrelationId string
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "x-correlation-id", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "x-correlation-id", valueList[0], &XCorrelationId, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: true})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "x-correlation-id", Err: err})
			return
		}

		params.XCorrelationId = XCorrelationId

	} else {
		err := fmt.Errorf("Header parameter x-correlation-id is required, but not found")
		siw.ErrorHandlerFunc(w, r, &RequiredHeaderError{ParamName: "x-correlation-id", Err: err})
		return
	}

	// ------------- Required header parameter "x-issuer-id" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("x-issuer-id")]; found {
		var XIssuerId string
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "x-issuer-id", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "x-issuer-id", valueList[0], &XIssuerId, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: true})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "x-issuer-id", Err: err})
			return
		}

		params.XIssuerId = XIssuerId

	} else {
		err := fmt.Errorf("Header parameter x-issuer-id is required, but not found")
		siw.ErrorHandlerFunc(w, r, &RequiredHeaderError{ParamName: "x-issuer-id", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.NotifyVirtualCardChange(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetCardInfo operation middleware
func (siw *ServerInterfaceWrapper) GetCardInfo(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "cardId" -------------
	var cardId ParametersCardIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "cardId", chi.URLParam(r, "cardId"), &cardId, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "cardId", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetCardInfo(w, r, cardId)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetTokenizeState operation middleware
func (siw *ServerInterfaceWrapper) GetTokenizeState(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "cardId" -------------
	var cardId ParametersCardIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "cardId", chi.URLParam(r, "cardId"), &cardId, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "cardId", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetTokenizeState(w, r, cardId)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetUserAccounts operation middleware
func (siw *ServerInterfaceWrapper) GetUserAccounts(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "accountID" -------------
	var accountID AccountIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "accountID", chi.URLParam(r, "accountID"), &accountID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "accountID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetUserAccounts(w, r, accountID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// UserCards operation middleware
func (siw *ServerInterfaceWrapper) UserCards(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.UserCards(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetUserDeposits operation middleware
func (siw *ServerInterfaceWrapper) GetUserDeposits(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetUserDeposits(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetDepositDetail operation middleware
func (siw *ServerInterfaceWrapper) GetDepositDetail(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "depositID" -------------
	var depositID DepositDetailsParams

	err = runtime.BindStyledParameterWithOptions("simple", "depositID", chi.URLParam(r, "depositID"), &depositID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "depositID", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetDepositDetail(w, r, depositID)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// GetLoans operation middleware
func (siw *ServerInterfaceWrapper) GetLoans(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params GetLoansParams

	headers := r.Header

	// ------------- Required header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage GetLoansParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: true})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = AcceptLanguage

	} else {
		err := fmt.Errorf("Header parameter Accept-Language is required, but not found")
		siw.ErrorHandlerFunc(w, r, &RequiredHeaderError{ParamName: "Accept-Language", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetLoans(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// UsersUpdateUserLocale operation middleware
func (siw *ServerInterfaceWrapper) UsersUpdateUserLocale(w http.ResponseWriter, r *http.Request) {

	var err error

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params UsersUpdateUserLocaleParams

	headers := r.Header

	// ------------- Required header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage UsersUpdateUserLocaleParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: true})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = AcceptLanguage

	} else {
		err := fmt.Errorf("Header parameter Accept-Language is required, but not found")
		siw.ErrorHandlerFunc(w, r, &RequiredHeaderError{ParamName: "Accept-Language", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.UsersUpdateUserLocale(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// ProfileDelete operation middleware
func (siw *ServerInterfaceWrapper) ProfileDelete(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"active"})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ProfileDelete(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

type UnescapedCookieParamError struct {
	ParamName string
	Err       error
}

func (e *UnescapedCookieParamError) Error() string {
	return fmt.Sprintf("error unescaping cookie parameter '%s'", e.ParamName)
}

func (e *UnescapedCookieParamError) Unwrap() error {
	return e.Err
}

type UnmarshalingParamError struct {
	ParamName string
	Err       error
}

func (e *UnmarshalingParamError) Error() string {
	return fmt.Sprintf("Error unmarshaling parameter %s as JSON: %s", e.ParamName, e.Err.Error())
}

func (e *UnmarshalingParamError) Unwrap() error {
	return e.Err
}

type RequiredParamError struct {
	ParamName string
}

func (e *RequiredParamError) Error() string {
	return fmt.Sprintf("Query argument %s is required, but not found", e.ParamName)
}

type RequiredHeaderError struct {
	ParamName string
	Err       error
}

func (e *RequiredHeaderError) Error() string {
	return fmt.Sprintf("Header parameter %s is required, but not found", e.ParamName)
}

func (e *RequiredHeaderError) Unwrap() error {
	return e.Err
}

type InvalidParamFormatError struct {
	ParamName string
	Err       error
}

func (e *InvalidParamFormatError) Error() string {
	return fmt.Sprintf("Invalid format for parameter %s: %s", e.ParamName, e.Err.Error())
}

func (e *InvalidParamFormatError) Unwrap() error {
	return e.Err
}

type TooManyValuesForParamError struct {
	ParamName string
	Count     int
}

func (e *TooManyValuesForParamError) Error() string {
	return fmt.Sprintf("Expected one value for %s, got %d", e.ParamName, e.Count)
}

// Handler creates http.Handler with routing matching OpenAPI spec.
func Handler(si ServerInterface) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{})
}

type ChiServerOptions struct {
	BaseURL          string
	BaseRouter       chi.Router
	Middlewares      []MiddlewareFunc
	ErrorHandlerFunc func(w http.ResponseWriter, r *http.Request, err error)
}

// HandlerFromMux creates http.Handler with routing matching OpenAPI spec based on the provided mux.
func HandlerFromMux(si ServerInterface, r chi.Router) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseRouter: r,
	})
}

func HandlerFromMuxWithBaseURL(si ServerInterface, r chi.Router, baseURL string) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseURL:    baseURL,
		BaseRouter: r,
	})
}

// HandlerWithOptions creates http.Handler with additional options
func HandlerWithOptions(si ServerInterface, options ChiServerOptions) http.Handler {
	r := options.BaseRouter

	if r == nil {
		r = chi.NewRouter()
	}
	if options.ErrorHandlerFunc == nil {
		options.ErrorHandlerFunc = func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		}
	}
	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandlerFunc:   options.ErrorHandlerFunc,
	}

	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/accounts/application-for-sign", wrapper.ApplicationForSign)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/accounts/documents", wrapper.CreateAccountsDocument)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/auth/bts-data", wrapper.GetBtsDataForAuth)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/auth/confirm", wrapper.AuthConfirm)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/auth/document-for-sign", wrapper.DocumentForSign)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/auth/identify", wrapper.AuthIdentify)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/auth/login", wrapper.AuthLogin)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/auth/logout", wrapper.AuthLogout)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/auth/refresh", wrapper.AuthRefresh)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/cards/{cardId}/requisites", wrapper.GetRequisites)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/deposits/condition-profit", wrapper.DepositConditionProfit)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/deposits/create-offer", wrapper.CreateDepositOffer)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/deposits/get-available-accounts", wrapper.GetAvailableAccounts)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/deposits/product-info/{productCode}", wrapper.GetProductInfo)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/deposits/utils/calculate-profit", wrapper.CalculateProfit)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/dict/create", wrapper.DictCreate)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/dict/delete", wrapper.DictDelete)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/dict/doc/by-filters", wrapper.DictDocGetListByFilters)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dict/doc/by-name/{dict_name}/{doc_name}", wrapper.DictDocGetByName)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/dict/doc/create", wrapper.DictDocCreate)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/dict/doc/delete", wrapper.DictDocDelete)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dict/doc/get/{doc_id}", wrapper.DictDocGet)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/dict/doc/order-update", wrapper.DictDocOrderUpdate)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/dict/doc/tree/path", wrapper.DictDocGetTreeLine)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/dict/doc/update", wrapper.DictDocUpdate)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dict/get/{dict_id}", wrapper.DictGet)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/dict/job/run", wrapper.DictJobRun)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/dict/job/status", wrapper.DictJobGetStatus)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/dict/job/statusall", wrapper.DictJobGetStatusAll)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/dict/job/stop", wrapper.DictJobStop)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/dict/kato/map/tsoid", wrapper.DictKATOMapFromTSOID)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dict/list", wrapper.DictGetList)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/dict/update", wrapper.DictUpdate)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dictionaries", wrapper.GetDictionaries)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/dictionaries/by-filter", wrapper.GetDictionariesByFilter)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dictionaries/locations", wrapper.DictGetLocations)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/documents/batch-sign-confirm", wrapper.ConfirmSignDocumentsBatch)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/documents/public", wrapper.RequestDocumentPublic)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/documents/sign", wrapper.SignDocumentByIDs)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/documents/sign-confirm", wrapper.ConfirmSignDocuments)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/documents/{docID}", wrapper.GetDocumentByID)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/documents/{docID}/sign", wrapper.SignDocumentByID)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/documents/{docID}/sign-confirm", wrapper.ConfirmSignDocumentByID)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/fileguard", wrapper.PostUnsecurePdfFile)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/health", wrapper.Health)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/active-application-check", wrapper.LoansCheckActiveLoanAppExists)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/application", wrapper.CreateLoanApplication)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/bank-statement", wrapper.GetBankStatement)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/calc-data", wrapper.GetLoansCalcData)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/calculation", wrapper.CalculateLoanTerms)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/documents", wrapper.DocumentsForLoanApp)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/education-types", wrapper.LoansGetEducationTypes)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/employment-types", wrapper.LoansGetEmploymentTypes)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/onboarding-texts", wrapper.GetLoansOnboardingTexts)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/relation-types", wrapper.LoansGetRelationTypes)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/survey", wrapper.GetLoanSurvey)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/survey", wrapper.SaveLoanSurvey)
	})
	r.Group(func(r chi.Router) {
		r.Put(options.BaseURL+"/loans/{applicationID}/application", wrapper.UpdateLoanApplication)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/{applicationID}/bank-statement", wrapper.GetBankStatementV2)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/{applicationID}/bts-data", wrapper.GetBtsDataForLoanApp)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/{applicationID}/cancel", wrapper.LoansCancelLoanApplication)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/{applicationID}/details", wrapper.GetLoansDetails)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/{applicationID}/documents-for-sign", wrapper.LoanDocumentForSign)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/{applicationID}/early-repay", wrapper.LoansPostEarlyRepay)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/{applicationID}/eds", wrapper.LoansPostEdsBtsData)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/{applicationID}/external-bank-loans", wrapper.SaveUserExternalBankLoans)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/{applicationID}/identify", wrapper.LoansPostIdentifyBtsData)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/{applicationID}/internal-checks-result", wrapper.LoansGetInternalChecksResult)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/{applicationID}/publish", wrapper.PublishLoanAppData)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/{applicationID}/refinancing-info", wrapper.GetRefinancingInfo)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/{applicationID}/scoring-result", wrapper.GetScoringResult)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/{applicationID}/sign-confirm", wrapper.LoansConfirmSignDocuments)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/loans/{applicationID}/status", wrapper.LoansGetApprovedLoanAppStatus)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/loans/{applicationID}/{docID}/sign-confirm", wrapper.LoansConfirmSignDocumentByID)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/otp/retry", wrapper.OtpRetry)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/payments/check-account-iin", wrapper.PaymentsCheckAccountIin)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/payments/check-client-by-phone-number", wrapper.CheckClientByPhoneNumber)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/payments/check-phone-number", wrapper.CheckPhoneNumber)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/payments/confirm-internal-payment-by-phone-number", wrapper.ConfirmInternalPaymentByPhoneNumber)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/payments/confirm-payment-by-account", wrapper.ConfirmPaymentByAccount)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/payments/confirm-payment-for-mobile", wrapper.ConfirmPaymentForMobile)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/payments/create-internal-payment-by-phone-number", wrapper.CreateInternalPaymentByPhoneNumber)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/payments/create-payment-by-account", wrapper.CreatePaymentByAccount)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/payments/create-payment-for-mobile", wrapper.CreatePaymentForMobile)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/payments/create-payment-kaspiqr", wrapper.CreatePaymentKaspiQR)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/payments/create-self-transfer", wrapper.CreateSelfTransfer)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/payments/history", wrapper.GetPaymentHistory)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/payments/qr-session-termination", wrapper.QRSessionTermination)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/payments/qr-token", wrapper.QRToken)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/payments/transactions", wrapper.GetTransactions)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/payments/transactions/{transactionID}", wrapper.GetTransactionByID)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/payments/transactions/{transactionID}/receipt", wrapper.GetTransactionReceipt)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/referral-program/attribution", wrapper.ReferralProgramAttribution)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/referral-program/onboarding", wrapper.ReferralProgramOnboarding)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/referral-program/profile", wrapper.ReferralProgramProfile)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/referral-program/status", wrapper.ReferralProgramStatus)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/tasks", wrapper.GetTasks)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/tasks/{taskID}", wrapper.GetTaskByID)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/tokenize", wrapper.StartTokenization)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/tokenize/state", wrapper.NotifyVirtualCardChange)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/tokenize/{cardId}/info", wrapper.GetCardInfo)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/tokenize/{cardId}/state/update", wrapper.GetTokenizeState)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/user/accounts/{accountID}", wrapper.GetUserAccounts)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/user/cards", wrapper.UserCards)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/user/deposits", wrapper.GetUserDeposits)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/user/deposits/{depositID}", wrapper.GetDepositDetail)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/user/loans", wrapper.GetLoans)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/user/locale", wrapper.UsersUpdateUserLocale)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/user/profile", wrapper.ProfileDelete)
	})

	return r
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+y9C3Mbx5Uv/lWmcPdWUQ5AAnxJ1L9S98+XbMQUyfDhbGJpeYdAg5wImIFnBrJol6ok",
	"0rKdlWPFWaficuIozmbv3qp/7V2IIiyIIqmq+wlmvkI+yb/6dPdM90z3PEDQlrPerYpFADN9uvv06fP8",
	"nfcLNavVtkxkuk7h6vuFtm7rLeQiG/6ardVQ213SzZ2OvoPwJ3Xk1Gyj7RqWWbha8P7Te+Y/9I4175nX",
	"9V7697wz/77XLRQLBv56F+l1ZBeKBVNvocJV+rpS8L5iwantopZOXtzQO023cLVw61ahWEBmp1W4+jb7",
	"o1As2J3CzWIB3dFb7SZ+md0pFAvuXhv/23Ftw9wp3L1bxGNYHdOtLqzq7u4qno2E7C+8I6/nnfr7Xt//",
	"wOt7x17X3/fO/HvhRLr+x17fO/S6Xs878c68p96Z5t/3P/J6/n44w7bu7obz09nYmFz0TsewUb1w1bU7",
	"iJ9pw7JbOp5op2PUC8VCS7+zhMwdd7dwdWK6iF/pIhu//O1yaUYvNW6+f+VuKfj3ZIZ/V8bvytem3W4a",
	"NR2vwnnWx3/kHXrHXl/zTr2u5h3797yed+T1/X3FsvDDvqJLM683a52m7qJV22oY7mwLb6RkZb72D7wT",
	"7wTPGy/RS+/Me4Zn7nW1kRudcnkCaeVLbBne6SB7j1sH8tKkBQjYu1LG/1cMV6RudbabKKTd7LS2kS2j",
	"fVXfszrudeTuWnXJDP4IHHzmHWneof/Qe+m9wJvrP8QTOvMf4G+8U3yO8e4rZtLmh0iazz/YqFG4Wvhv",
	"Y6GUGSPfOmMLqG05EXJl04H/XdNdmfz5yjvzTrw+ZkTvhf8J/q//SMOHFc8GH1//kQZT6QLPdrUR+C0+",
	"4X3vxOv5965q5dHKuPZjrTL+31U71w5pyLR7+I0D7d0GslvXLdPddaS8d887w8I2ynneoQZzue8/8j/0",
	"uv6DdF50w4Gy8SM/n45huhPj4XwM00U7wYTseqJw2dysLmjeodf37/n7/kGwcWfecw0EDf74oVyS1PDL",
	"67lESPyoU75bQK5uNB2gUrLamMjIOstpqpPXpUg2CRlGzV2wap0WSrmtCCVn3jGIHiKWVbRYtS2j/oqK",
	"WH7Cy3oLJV9AJ/gc55g2/DNp4txEpydV9BmWqdt7advh3ycqgnfonfkfeafkhlTQZtTcV3tPyJyz7Uju",
	"mQ9hW7KckUE1uhwc9sqqLouma++tWobpvkH07fjq/BkuhwP/nn+/qHln/j7M+QhfHVi+YQH8jF6X+14P",
	"/62RK9L/GP8JF8wfvcfxy1Mraf59zXuBVw+rgN6p99TrXlIZACggtSC9ZQoN22pt1WxUN9ytpm7W8Rxl",
	"c34dYb2g3qm5VbNhSWb8JdFwRAl+ynYdLBXvyD/wjtV73iYDzFt1lFO0r5jblm5j4tetjl1DP8V3r5Jz",
	"QT0JThPj1wP/Pugr3pl36j3BnByur+JOd2AwcWWpGVWzWi0LW1FsUW/KVnVV3zFM0NPnsaaaSDZe4Bde",
	"HwwiPIFDYh/tMx0Fmwb07y6emf+h11PQXaNqscQYrES04ATFIyR+Vd9JXvKvBbK6SiVXZaJmpmgPCy7n",
	"deS+YTiuZe/NNw0syQxzDg8oEWJfeF+NeZ/h/2Bd6IXXj4ql6NLxL8zHpBx1G7ZuOjrcBHN71QXuz+qC",
	"lMxTOFjHEmkbrCzm4mP/Q68fGhHi8XKFUc6n0snn4lBfgFSX9l56ff8+49UzIs6wDAytfPyHd+S9wPce",
	"nuUL/xMyPTIt2Vyfq0w/RknSPA0XEU00MsFgxrpt63tJE8bKd+psqY49rKnVYMwhzwMvFrLbuu3uKWTP",
	"KSXxKTkhibMZobfcgf8Rf8P18QEj1yFTavrBt/594tYgwu1SkuhihA5y3pxFs74gt21hll4PLoYu0Ya7",
	"xNwbzsYhOvJAZC8ZLUPmH/kDaAZ9vCHHsRsCW6pwGR8Cxb8iqpj/0H+Qh+4mDC0VEIbpTk+mCmN+Htf1",
	"O0pfzx+BlPswI969gFUp6gRKEncRslvBSAMt+HXDTCC0D8rDUMgMxhmIzJVGw0FSxxlI2F+Botj3eoyJ",
	"vZdwhsGDk0iXRV48pG1faSMbdIUNeCRG7V+8vvcS614v4VoIBUlE/y1q82uLC9UNJkwWFueqGyphUTds",
	"BMNLVTTyokKxAO9QKWjS2ay7uu0uqDxkXSz1eO/YkOWIE4yezDFrqGGYulkzzB2stK+hd9Zd1JaQ/Fuv",
	"7x1p/sfAGUplFz+b6Lqi67q2eG1rfmV5obpRXVleLxQL1eXVzY2t6tzssnyRN3Tn1ryNdBfVZxsushNV",
	"yc/pWsLl+gwvLWiVwNhn/n1sGWkjrtFCjqu32sprhAy3pePxcvM4R/Acalg2GpDiI+8sB63bMNRAxH4n",
	"wRkXBn5FbXi8Kqlmy1dMTY0YVqG3NLMFUylm3ixM1rrxXjJpf/a63rP8xG05xnsKCsfL2Ulcd3W346SZ",
	"fBDmOPDvEzY6ArnYV8s0t+NI5bRhltq2tWMjB39fs1rtJnIRZpc2SrCtMZ34skmkkl476fTB+5PF7aaD",
	"7NkdJFUY/o/3DLYq1J9VHhv8lhJ5TT7zMgwklzKGBALfv+b/muiQzPjVRgzH6SB7C9saWzZqbBn1SxcW",
	"IbhLHkaOO2fVDcQC4VgnYr7INfI9/qZmmS5dZC7OOfZLxwILPxy2bVttZLv0hWGoOI8AFGTcdyStwIeU",
	"mg8g86smx+7x+bENq37NtlpJegz470Ar60OUr6uNgK2OFZxj4tIFxZIEN/vgRuvDdRwsn39wSSBnvDw+",
	"VSpXSuUKv6x1otKYnWZT38a/I4ykIHvDSjDhPrwwkifK+UkODXFr+5eo5oY8Hx4YMVvgmmWvGzvm+dm+",
	"1rFtZNboX0leikPQWT/194vB8hyDqX7m3wOL0XsCLuyev49VGRBjZImJGwffPzE2xDIjo3OiWKhbtWTr",
	"IMbjRYFG73kWGqXqQLgXbwdk3My4ax13dw01bOTszln1vXPsVR3dNmqI+deTI/nBL/F52LVMlPbIKv7R",
	"Mo2D42kAxRvWLWRKF/zMO8arh02yJ+CvO6RBB9Bcu94hWXWvDwkqzJBMXllhUEZ3kZ93xjWf093a7oJV",
	"c1bc9jkXHQvtVjvvvQD+rZf+Q3+fSA4mT8DdAmGeY/IPybUROwc1q47UEZWXEDjZ9w6Bkb8hJOFdEMTT",
	"xPj4BL6SDJNdSRXJSBBVc/LM1H9IKSCiwn/IjAAiEqIn8sw75I986tRj/snIQcTUFrk9oouVkU1Yrodh",
	"meflEpUn6Pdg43/EvCxFzANY5NyHT/C56YmBhTPvJLhoiLeoJ2R0iRc3DcdE1fBiod2x25aDZGwL2R7+",
	"Q+8JtQmCTA+RCP9Dr4c/EtioknqGw4GLbFWy7sYuqt0iAZm5PU4cnf+Sa3OyLdGKA1+z1/M/ADVBVJF+",
	"dPlyuXzlypWZmfHx9FXgRswz/f+C07bMhmG3qibWjfUmdagNmQEGFOKB1O7C2TgKnHEkRMELcU4RrJTL",
	"6Mp4vYTGr1wpTV65Ml660tBrpYlGZbI8MTk5U56qnF/qq2hK3aKBZSXZp2B/qAH2w6a8OptyzbKvW9tG",
	"E/1wYF6dvVkLPBevthYa7se2jmbqE+OXS/Wpy1dKk/rMeGmmtl0rTU9Pbm9fRuXy5cbMq6S2Dne/sFnN",
	"vErOGnrnB/Ph294HCGfQnOCVRgPu//PuAnXu1RXZo8SlA8mU4PAIIjCHXD6efyBLQA7XiA5SgZWK7YKe",
	"v4CBf/tMuVyujM5MZUhiL4rVAIMk/RcLtu4i1XIdQBjtDLIzHo2BafI8thqV8Qk0OTV9uYSuzGyXKuP1",
	"iZI+OTVdmhyfnq5MVi5PlsvldIcAoaLIbWAxWuuQ08gA7rpgbTPYamE5Jkenpgu8G/ifbtyo/+jGjdEb",
	"N+rvj9/9BxnXbCMTNYyaods8ibm0+UjemuY9AfaGVEaaE/iR/xnND6FOGyErE5v1pAoFskiI37RHRFEO",
	"U6HIfI170ggzdS6SDFhuhAhbvfmLDdmrjTpqtS0Xv10qar+Et3+kgQLQ804gGMzCGXxxTTjS1Ixeq6Py",
	"ZAk1KtOlSX2iXprRZ6ZKk5fHr9Qnx9F4uSYVt219D9lUO86yXXT5YSfie8BEEnGs4BXy75PQMKb9E+9p",
	"BreauDhSCpWcVgyLpILty3XUlizd5BzXuQV5ZOH+5D3x/5mkOkZqHIPsiVgYnXgnwcmRWCw3itcmq1tH",
	"FveOJPmIbptRcn8arU4LElbjPptfdoza3jpyHEWeJyl06Pn3IQWqr0FqJDDHPgzf036yWZ3/+fr8ytqi",
	"wMqT9ckKmirPlCr1CipNTk2US3qlPp3uD0zwInn/yooNSCYLca/3VSpNkUgTlr/X9Q7xZvmf+r/y+t5z",
	"6mzyP4msmTbi9b1niiKHS6PpE3ARdS0gx/0WZiHcjMOdS1S7GuDWG6btXnM7enMulBmSpf0rJCrts4RD",
	"fObw4soyP0e8v3pfeH8a877yuiSTix7aLnGbXpJqVFEa5gyzWl1OS+P2P4jRxa67GGWPxPv67XJpBoKw",
	"/5CJHkialS5NmO/e1YBHnoU8Fybv5yQ0naCqs4R29KZ0r154Xe+p5n+KrY8XJA1/UAq2LauJdDMkYR2Z",
	"8jIYBYvIE4DPwSSEgPPyh4ysATiEEHNhzKGiMpGa4XFGyvA8b2SwiCL5noJFNJPVHOJUG07yRe7Wudll",
	"rcaSObTMZ4x795xu3pLMBnPVl4LWPdirlyGfRpaGITsM5x0w01E5r8TcHpasTHm5YumSxYlqdlxWz/hU",
	"Wbi1J5LJ2NDvrGLVmyUxJJnlwm8jr0nMgZATHZyaifEMpVLZTbWE86ky1erytGiWBSt5pZBNU3KNFnrF",
	"TcBb2wkOtyc06eQDEKV9r4vnKn2L2Za+5SvvcVGjmjmthYw/K0WaoBSoRHQGvuBNR+lZJY60WKkcf2jE",
	"MzNeVtnQGWWQajbckJVxUbkeVw05kKDILxPaRBGngAcKof5MzBzQGDCI1/O+ia3plGwVb+vNDlpIOW5B",
	"WhefA0X9v1kOXjQCzO0cv6gR1pG5FWQXUFyMx0Vh/BKWXvnyC5UcVnLYYhsTFSqDWFvDDMp9p06l4jD9",
	"mq+Sv25gvyp1rgr5hS8Jbf6B99J/RKog+TUdMK8i6sHTI867tsxjNwi3vqk7beOna8PIK3Vcq4VsReCL",
	"0x6jSAcS2f19cfcqzIowSVxxW2kj4DwE+5cTwuCNuhTRqfCBm5p+7bWp6ctXCuq7RVmamBB05EtBGYrR",
	"Y+9rcT8uq8fMH+TkLjRYGsl4cgySFPc2pSZ26YiLU+S5NNdpWUfNBtT7NV6xeFHDtlpq9eyiLwhudO/r",
	"/xLBHtfKudqDXBrqUS9ulTOWJNDo8bxl1g1A+iC4bOcP2GeKlkO4B0srTsBKMPMSA+sE16+cyZHEVNX5",
	"9EwjODMPZeB2b/5io6htri8UtcXNtUtZ+J3HvVHncYgIOrKRfzb75uzSrDgk+SxdrRegd2K6+3yO/I4F",
	"o+YSKbqG3snFIolpDAFuVsKwC6iJLmrY6kLSwFbtYqbMMpWSx76YeYc4YMmjv47cJcNx5/auGU0X2c5F",
	"0UFfn0rMho3QkmEOfT24VyfTsGLXkb3Zrl8gP3BDJNNysWQkjP3m7MbKdb19zbZaG+sr1YVhkxB9/6y9",
	"k8QaF7MOyRLph/qk7yTBsFpHpms09s658ImznttYj2bDTWKLbaZcSbf280zmJ9b268gl9fbD5F7+veqh",
	"1zrmkAdd65jq4dZdqz3k8fArFQNGMpeCpODzHlgBfHtQqO/RLAeTVqo6ymjRCGC6PIGcldNLCvBNVqyX",
	"tJJ0rdgasaiVUL5XLHRM450OqpIXwipHj3QEmDyYQMbTsGTtVM3vqMrWUCMIJomCaLD0R/+gdFLmKuGV",
	"VWYVCJWDVNQuWTtW57xVDIbpuHqzOQjv+wc044qEXkg5Phg3mHe/kd4pirS30szl+nh9amamPtG4kn6M",
	"ot4mcQ4Zl++He34Y93z2xV5Drr33fV/x864DAyeDylLWhsMwhxaIyujzkrLSeXBXU7QnEYGV0Zp1zSzH",
	"XdTt5t4aaut7wynesvGrQod8kggPh6a/BxrbuiLpY2VhZXnp51pJ8/7d+9x7fMP82fobC1vXNpeW8GeP",
	"yWcZYBfYAEWB1jxLVnfmXGdBd/XhLFmSqBmq/oyJDwyC7+cMbKthNBHx8fxwQQ94Qa92tpuGs8v02KGx",
	"wbZuAhoaaiE57vHnQaivxxaMpI2/pHgqp5mQNJKEyqzr6rVdVF+wanhiUlyNLGv00zVanLCB7BaF9n7V",
	"I8XDCU3yfxK4DL7wwn+oQdxcWwUr57xRpXhGTRDOzB+v/OkaoOm86tv0jq1CGvoatCWiUvYBW7IHoWKC",
	"xg9ChYuY0/jlT9dKnJqVvL5s6EFWdw01kG3rzVnXtY3tznAORB2h9pJh3lrvbFeyVqwcEx38S15TioNf",
	"EfHyFJj5PpbMGjSi+IDkW+KPpV4ESs9berMju9IekxRG0Pv2IY8AK3/3/YfeCzhSD7TZdtu51txDdowk",
	"/wBm8ozhBgG6GA2RdmFWdHKnRCr6+3TG/q/hg653WozEvgIY8WMCSs7gyE/9A3J6tSAyRpMwoLmGnHUB",
	"rsc/gBTkvgbs94SA84q9KyK3HA82S9VXlojNr01PGtxleK9JWXt9DfI0eqQrD43u0rhnN7ar0oEHSO4L",
	"SSuKXJrxuKzrt9F6x76Nzm2a1esA95ly8YXjzdIH7haH6H9LqT2r7RrNui0VarJeHQSWzut5z0dlubjJ",
	"pYN4HfWau4psxzIzNBwgJ4OUcFAFA0o7RrPqFOHSzvNDY1pahkldfJU4hh+qd2oDqpdEzJ9ihj+j4FXP",
	"+ITVDFuCWrrRTLXB4Efw63ZeKve9vvcSH7hnmCr/EXwOSQi5C/wirlBCuriAxeAgMGJjnMCxYY5Duukg",
	"e/EOqZGf081bWC0+r98b0ffBu4SuFEmbIaMkFRhOHCrrvHngj7k9iEie15UcwOkpEe++NYzYLEh6GVeK",
	"xEp/KOz+obD7h8Luiyrsjs2wKCArRjgq08GFT5y2ZTrkDKwhp73Yarv5jq1knNhJ5gq4QhRlrPGcgoLc",
	"JeuJf/UxPsOwBlTgA7b3anXRti1bISXolnwA+ar4bO5jgxAf1ydw/GdXq6Oa99toXxt/XwOCPqCKRGBa",
	"CCAm7xcQGbrwj6NlmnzHX2KMrnjuq4GadaacQi6k3lwVnpUYVkoJ6N8nhXABGBGWA0xF7IJn6Hk46zPv",
	"uBDngMidCJTHGSVo0C3vK8Yt84eBZcOBYCdIVr3ZXGkUrr6dfMFft0y0V7h7sxivmwXbxTv17/Mw63eL",
	"4FObM2oZam2ldaHwtKl4+qsMTw9ciSvNme40m0llZ6nhj2LB2NbNpGK8oLEPNli70EGSXKAj1bnZ5Uuq",
	"Vy7pjrtg7BhSt+XvweBX1CxwDdz44ocYvNYI8QVQZf4b6AfxiSYgsU9ypjTk9OIVpS2gDkGmdOUTUMeV",
	"8gaUImvBpb/CwrOYerCRHJOE3BZybcIRDLsZEDEt1ShJYl1aakKYgJc+XGJBcWqKSKieca0QWG8OGn0L",
	"POBhQG72tm409W2jaUCjNp382URzelM3a0jSswPTDYk4UJ0n9Y5Q6Rh2VurLJNgZwzYm+fBErnrPqSVO",
	"ao5jcCeGZdJsqDRvO/9bArPAtbVKf5LlztjoXd2uX7Ns8nFyLjwp3Tn1ut5TepaOQl/UM3z2+Bn3ojPu",
	"8Vfg5cx97gW9JSS+KK5XfCryIyAucVK7mKhCgWcpTJB2Euam6D8KdIIT8NTRiItQZes/unrDfE177bW6",
	"ZaLXXtNKacsWJ+WMvOBd3XANcyfbO87AtX1ElRTZ7CCIy85UnWTy0CHwYoYGAf1OcXISD3r6AhIqgrFo",
	"YGKr3bFru7ojHzfQhapm3bht1Dt6U0HF56DwkGkHoC/M27vPGjzATfZX7w8aoI6AnxdcaP4nsUNr5rym",
	"j+QEsNWQ1u0HJB0J8QfyrlGNcwj91fuD2ixPd9cdQfPTiXGtpHlfeI+L2vSk9rd7n2vQmOgQujF0/f2i",
	"Vhm/Ap8D/sP9wF0OxTD7QHMfn6GiNj41LfshWLl4458ENhNc25Flkbd+EnzFRBLAJkjPu6SZSdqt5yjY",
	"hruP/IfU/OebEnyS1asZ+H+yeEzoL6WTi0RcpWHhvN7nQI0sElcIi+Oc0oK9qPEJyrm8xUNeVOfYlY8N",
	"XhaBCDoZQOXgA9ZpDBzKv8qgX9G1YKRJ17Pj7gZ4yiouMdEdlzUwTNrkZfY7qKS7hcz0K73jkqYkwBcd",
	"RxkFTUwKk0dGOdzl2uWpmalKpVRHM9OlyemZRkkfn6iVGrXLaLw2WZ6ulRvpIRpCZkClajVpRxr1ajYM",
	"23GZYSLpOJXwZcuo15tI+fW3s0+qdQlGL3JT5CakWrCNgIJYLhyNRKlb5GTshhN03fnWGu5Q4sOBpbNn",
	"qjnnKBDXYICmaerUwJglIJX5rF6W3VJPQm9BIVctZ0rlrEAr08BIneba5lyhWFjcXCsUC5vrWIDNL/9c",
	"arcoDPSvZEYytSlj73CyaMZSamfnN6pvLRaKhbmllfk3F4HQpZV1+Mfs2tri+gb883p1fWP2zcVl+HT+",
	"jepb8Ona4srq4vLignRebqI+KaNlfnMNL9fc5rU5+M/8SqFY2NgsFAtLK7N46JWNNxbX1uUdG3nOhQuD",
	"X6ti2CKSQ5RJMCtDBp+L5WjFL2tH6i8iF3KY5fuMGGD+gfeMfB42kCM+ukxaCB/vkvVBI9ztqLp+QirA",
	"xyx3zOsJrlKsH8Wa2xHawptodeGajAVb+p1rRhN6j2Zszk27G515L0okxo2VhafgT+h6zznflJKWSlly",
	"dluW6e46qrh6OJR/338EZs1hkVjBQvZJL8wJ6Yqt/oQC8ek0y5ewR7gt4kIF1Eo5j2ScYssY7mSnHee+",
	"piGFFfw6zLAJPXwQMnsABiVn/8uiHglXEOmKvblWzTZqEAq5R3zXGTQ+mJI4VMLq/LAs4rLM680aWxe5",
	"7qZnyjWHAG6QZU6jS/KDTSOThwRegbt6/Qeh3+B5vE9Z5tqxVTK6PD1VjIUNQCEXvTsXlRscGcpMWkHr",
	"5OkO1zgBoilsSocYloaM+2vsR3XyK/ldABlfH5F5go0mxrH9e2ED+ghohv8JFZiHIEU/wU/di8nmy+WB",
	"XIUx4pOWAVIqnU4zwSlei/40hUUIiqh/QHrsE7WUfBwiiZyR1h9h2m+PtNvPwioc6ZhjMnFKfA4pixK8",
	"ObYccN8092j1jyxhk/L/IfUHib42Olt2fX7EPuYWQwAUnZ6YmJhIy4CwbiNbKIJRu7JPQQz3iGbFj3Rl",
	"ZmZmJm2gqKQY6Ehbrt5UkkpC0JGMkBFldoj2IyFzRJjdJVHTyTDBZPkS2fnososzk/OXWUPNSGaPXAKl",
	"pVHmy79ISeAp5MuWk0/Nrq/hJxzDRU6CMLl9Oz6b+bfeCnKsg67ogspcrkjR2FBr23IcRXz3C+/Ef1QE",
	"1ROfK3DlcV1BWRo0Hk2eKozutA17T4Hw+TUR2DE3Pt/XHUs9zjjwetr162M//7k4s+mxcQXSnCnNBgdX",
	"cTQMzA06sqoIOmcycRXrP1tzjdtZ4EkhhhHuirCIvA15+7aSjcAVxArEZcXjc4ZZN8ydJYPIoGyuZ/5J",
	"affroGYb37Y0IpitPJ09cLdYMJxV22jRhg1xTHbS1p+clQbp/yT1/a2hdhOZhrO7YRDWzobRjB8lIb75",
	"Xd3cQfmebtvWbcMxLNMwd3I+2dluGrU30R4t9TOQPIknGwe+VDrctY1dvYkcbeR/LiyuLq38/Pri8sbW",
	"yvLrK9Xl1/9nUfvbvf91SY7ndguZs47TsbH8XUK3UVPeEB3/jkMNlv+AVqCQPKVEjHHx1+z59U6jYdxR",
	"v5/F7+J4w4btdvQm5h4F67yrN5vITWYv8ptVvNd1ZCf+6K2UAaOJHBHmDvZbetBJ0TSWKvQ+XLxjOK4j",
	"vw13dYdKILm61afR+WNSfEJy/6njFtooQFvos6h1chpLbpV2UrCR7qTLA8ipWyM/jS5NSL9yLaSdmZW3",
	"KPw4b6KUulHZIQl5HZN0Pv8zekueaEyJBdfvvpjIQG9YzfvraMaqcWUEIN6cWTVzANpYaSNbd6WZgjJc",
	"juDnyqFZoRkD+glHj2qnFIEBUgilhs4Jq5ciQA1Y3yI5r0S80S4LYfkQdeQ9Z489pcVeJ3ADR+qHnSBY",
	"kOa2x7v6Ao8dH6wbOG3CwXh3jjyWJmYnuHYHFaUXHFvK1JMqUuZ1sw1gmduWbuNLfMFw2k19L1tKi5gI",
	"FyS1BFVuxCA98069J6CvHXl979R7mo2qmBwMd0lYETX1UsbM0rlbdUycTLlUDMECsHaFpKLonJxUOqPd",
	"r9S0tZDj6Dsoa0IsxMKf4K0KkmODjZXH4LOpGfFGGMmiJNBdGf3pi8GB0786GxXpwDtIUsjjMPmDL68/",
	"iuaK5M0LOWdaiKy17ZC9e3JX3mFQaBukKT3nWz/0RLfeleEB4oLrfZ819jq+IITcJF4QUqnOgaJAt03N",
	"CsUCajQQaFHUdasyzEmBCVZs/F/7H+A7JqoESlCM+XUoj1YydgLOSsiQRoxqVTx/CNTIF6sYZ/1ipjM1",
	"2CVkue21gNpUvT2sH3/BwE3OKKoTKyKP6FVwHQRt5tNVB0xOSGsSM6647WudZjP4uXCpDCioiwU3/DB3",
	"4StVU56BQfMhK2PzPvM+FyvWKrWJRnm8UZqcmK6XJhsz26Ur01OXS9Mzk43J2pUrM+UBIF9EujkvDr/B",
	"auZR+DuTChTPmJrNBfyEmiYGox+pWMxbpjhUJystcsPCEFKRg3INgc6YzdmTUxpP3AmJzabzpi5Gvgrp",
	"+PjqLf/ulEHOOn0cRlQ0ot0T/f8JvTKlPt+/U5mVTxEOF9HpsBS2uLPqeyrNYnp8MSbgssm17Er+Dxfh",
	"DxehpAnUhdmEfweHU5yAek3FVkE/rGguh4CN6obLshySgPPORL95L6EsOUMxcrHQ1LdRU5raihXA+6AC",
	"xofMWNlPXi6dcN4GM+dISqb2bK6k7s3N6kJiFD/SaAe46An4HXpBjZT/0H8QRcRGk1PTl0voysx2qTJe",
	"nyjpk1PTpcnx6enKZOXyZLlcljewZl3wEpOrI0SFynmQcz1C34SXHUgm+EyHJHtP3oGNAHiTf0mJ27ER",
	"JBQr9jKVPvBVPCU3K5SajdQykLewuFoaL49Plsrlirx+XZVU/xts2fgHoi3BpdXDyJzLKMu60tFSiK5M",
	"QQum0alhJu5noI69SUGecK7IWSLnipwxeq7SfVPknC2a9YXUzscEmOuj0F6M5W5EmELY+/Hy+FSpMl6a",
	"qETh3aQV8k4QxM8UrwnUPa5Mshd0wM0QePmZ4e7Wbf1dvZl1LP8+xdCKjNHQm450EBPpNnLcVV2ZFENw",
	"BPwDgHahDlPW7RAywF+wLHmSNfxh6CjURmDe4Lb7CH9U5IpQj9hjpyT4tg/IgF4fEvFPtMrM5XKpXCmV",
	"K5eiWzap3DKz06ShNGFRxd6T9XyJatwsP+LhZWOz5cmcnMrqED6Xv1HmCy6PVqa0H2uVqf9+6fzuyEjv",
	"1EipiCCwQ0kZPykRZhb3IeLljHBkTBzItIBoU7343bypQpEScIoGuWTTmwD8EJvIF5t49YIChrOAGro8",
	"T/p3VKCBxKFiLPQL9sRc+h5XHe5/ApkGPdYq9KV3VgKpg79jN9qnmaR4W9+zOu515O6SLvYZojKr/CND",
	"jntMZlpUF9mt66q6pCAXM9qlEWseYaFS138g47mJojZd1GaKWmW8qI1PFrWJaVF/GufPrap5f0QWboIw",
	"5KguDhCtCTlJGrkRNjKLpEsqeahHfuukZfnfl8C/UNwsRZtQ8FLhC/IJNYghx8g/iOybfzDGyiH8g7GI",
	"qMoTSwwlfGpcOTb5hOVc4ECCIvk5dem1oazw/8z7XBsJ4m+iuKtbtQr0cpFoluvGjqlQKr/yeuDoJosP",
	"iN7sJnjJR+3JRxHCRqXpbxlKwdYnBNp3XbftXB0bcyZG6YejNas1xmY62q435NDMblOV0PYsCiUiB0ki",
	"6HOn0S60BMciUOxlHWvzVd/GSRjVvM/DBKqjCBQX8+r6DylmCdZlgQc4UUSwLjkWoC4ELnbWKWrxT998",
	"L0OH+wJb36CgF/Y1gc9XI/dETBiQQp6jmGqvkPmsRnl2DYvGhcXVlfXqhmjj0e8kzhQ+ezs5LRzSrRA1",
	"wlPbLrN3y/PN9Xb7LWQ7AT4UvdYLFbD+Y+Zxz78HwKKPVL05JNYrHvy6VZe6w/5IwPkB4JM0AAH2Zhar",
	"yOIGxOS1y1KR8X1sPFIsOHuOi1qJsEqR6NVZoOKKHST4en2zblswnrGyXigWfoa2RSYkHyuIEdhBvf3Z",
	"qeJs1dHxLEFZRoHIPLE9FlYvSv3NxHPAFUBEfJbXF6vSfPFtWzfrChBOeGvSl0okE/L1qm4j01W8oWHY",
	"rXd1G3HbEv+N1bEDgEG+iZ+8+sLc6dCAtKRC3+w09JrbsRVPt9hBlqCyuO9a9q2EzOXgN4zfA4G5uLS0",
	"uTS7hnm1eq0q9TZbTtIKtFMm7SDb0JtJPzBa62m/cY0W+gVto6j8ch25LhXhbHrLixs/W1l7c2t9Edt3",
	"12eXN2eX5HAYUD3hWra+o2Io/ieKOgrpFcD3VI7fLhkCJHGNRI5XTL284OInGQn/zOrXtZ+sryxrI2Cn",
	"H4Ly0KepMBqR/Ew79u9hLUIOh1yQzK5u1NwtmWqaiCEjp/6Yw3hgyvsZ8f8Sgol1GlmOLLI+J4GZVzzL",
	"2GZCAWGOvcW2Jd3VR5B9w4HcSQuTLLuO7C2TBViodlEuyov+ekGK/inBlCPKACkxuOcfACX0kjmDqkaG",
	"HBte2uBAlRkIfAI0V+97W29KN+Zr7yxM0iLqCPFOlJi/VlizOMld7wSz9QPa4EmO8xf4Qrr+PV5/UmXe",
	"m6TkkDF8kRxdfp1vppx+1uQ+LgRUh4hE7GDSfeAXFdH8pRUMksmcJUTJMmLb+g5tFCbrkBf4rSljVSAO",
	"FGcvgLUJoZJYuLrvfwgqSqxXSpxN2kEKl5KHBTQmYQiNYoT2AwQFrZxl2FjtKeTy1JTV145li/WiOZs4",
	"BFzF9i+NmYimHUur38rsJsgd7KdvTyOMVc5GIV+k6B9pCd5quZvNV8PfvWl7ACSmzW4Fn3bSRONizzFZ",
	"bfEcJ72UovPLFjHSueuETwOLWGA0BwyfliOSLiEV+OlNSmL3TzT9Mng/1yFEMkzegxoepHD8cDFVu/s6",
	"cjdshJYMc7i7Ghpgt3TXkmsm1wzUrC8nKgnESf8I9LsjyFs5pF78o+BWFpU4r0eBRmP3KARPe15PM+oa",
	"YJ29oAF3+l7R7sd33Khcr2kT02nhFaC/BHiKXebO8D/knHb7wGYH8NBT7yw+OzKPLYWTADVRzc0zRfBD",
	"i8jUn4p6LSSIgF4C7mpg+C7pEUHs+kPWJe8Fg2gFLTdCd414+pNIVnX9+z1PHvQuFaiPKeHnIrZSnhif",
	"nKhMTKZfMMHZjS67ZFbi0ZGxo+qwvzm7sXJdb1+zrdbG+kp1YdbekQEHdowmcwjGlu8ocBePgMA9kWrg",
	"NcOVZbr8C13aIxIv8u9TDK6gAcNL/wB6Ju7L32rZ7Y6jjIq+xBJd/lzHdAl2RNqVSZalXreJhYffUXNz",
	"P9ho6q6UzEOKi9KnWTBSP4nkyf+EXpPHQjJapN8U54q79V6hWLA78Inog7v1nhwlbicLMEd0lo5rI+RK",
	"6zugK18W7DidQMFn4lYFok+9bm+FQQXmSiZ5AXLfZpeIYqgaHymXK1qJ+0wDDuUg94pauTwe+wkWqX2m",
	"cpPkU/zDicgPP2BICaRpIoTrg/abBEE/6AQoBqoU2YPbtGFZvhOQcKS/ptZjmBSV+zSrntly67WkfFDo",
	"HgD3lH+PQnoRKOAQmvQpG0PRkD882dJp4ZWVd4fljrbEbHvunXmnSc+df2pAHYyj6LAzsAzBTw5h6Y/5",
	"kcBilXYBSgNTCg6D7AW7VsdRdVU4UT6wZb1rItvZNdrSeAHTh5T8TF5y/hWC+0/R3MisozvypSFZeFR7",
	"4O0N2XtAeZZR+RvvL6TLEpArb+cg+EGljrJ3TYnoVMrNQ35poRES1IrF0mWwDHxJzSu5hy68ceLZf6Q/",
	"AO2NoXhyK00klZiIVh1k+p7zM8FZSLHcOnSQ6zaRAtTwq0QtKPl1QyA+ooSFlxMlQM42g1z87KkhCM4D",
	"MopcJL1HQyWxkG8f6h7OQrGrenoIy3oSH02hDEkVn1DLkngfFWmBgjwQbRJsEnIqT8S1MD0pdT2aegu9",
	"+V4OGKTEMbnu6M+8rv+AakInmv+IaLZyQY2JWOsMlwhw6d/PREAUFoCYnXRlAupU6qthmRSwLppVwU3l",
	"fVkbSq59otoLeN5IzwWFdbLTm9BTN7oEsTBdDoepBC0SIoJkeG0kTIY/5bHVz4qSLFGCQkUvNu85edGN",
	"wvt3bxQupbKPmWSZE2aRerYTfHCD7qHC+5BM3MDebSmVeSBMwsM0uCNbGYY+d1uhLEemYTTRUgbodxBQ",
	"H0DZcmgx0r6x+CJ5IT1Ixo4prYD+DL9YU0QOI+l0CoAyzPPS7MXBkwqTkgKzoBmxToO3s6UMxcbnYwI0",
	"MEa9rWK0HZvvpA8abbYYDVePkFJxOhDfcFLIGFD566sLYc7g7SD5KOCUMK+Q7m8SW9NeZOmNxgQ4Orb9",
	"8c6RQ8FGlu186N+YaOhXphrTk6Wpy5XLpcmp6fHS9kSjVhqvzUxPNKan9YY+fdFHSZpa61gt1DTMW5ly",
	"a7+Fs5deeXCus8gl8/6eIpu8CN3joLZBuQm+d0lNm9gzNV+Cb2pfVoEiWvS00kamYe5wqbmycV95eRBW",
	"Z2SUDWJesVRGZJcNivKEc0CQKWVJLnA6JrrOh1E3nKbA8RbAUtbb1s1b68i+bdTQLCu/wyIU2Y5l6s0F",
	"3dVln18zmk1Uj35LmpkZlsl/iNerie7MW6aDTJdg2MLGm7pZM8ydDWS3HMitltbI85v6g7bzymo7GUsg",
	"vpfC7ltUfpxXrxPrOaXZom4399ZQxtYj/+597j0e8x7j/8Q0uZR6Ug57IEtV6G1FWJ2jhjQyukdNPeZQ",
	"jDbxj8TJpcGuyJLdpsFvYUbJqzdvma6t11yFk+EFraEHGXRK8CKCJJxuQGikX2uQqq2GxoiCocuAMfJU",
	"av3e68KjLyhi1bEQgkjQ6MTyrJR5prgvyLwZkdKFr3fIHclu4fP12zygFJ5SNzv4EJ4lE11Ue0jBMTrY",
	"OxN6cvPSf6C3ywQjTbitKTm8pRuywNt/wBV2TCO6QQH7Sxp06kKyiOsiG//6n97WS+/Nln5RLs2Mbv33",
	"H5Vu/uj/5T4p3fzRjRuj9IOb748X7/6DbGUWW+2mtcdrXufb86DxOJTVA4rGmTq0krzX+d6VdY/zvHWg",
	"veX6HWR3/0Ns4sz/2Ot7T1RpimpMSEg7B9yNwPA7Y6ibJIG4R61A4sA/p+ziXoyv28QXR6HiqFqgXj6+",
	"C2k2dszn11DVAdqdmqq++wsscAMf9odMNQtaiCp6rx6T9MpnkZYSQZ0LeKdp60+igT0lOa/+/qh3PKpB",
	"ciur3sd7G8Xrp9XKWN3ZrEqzkawda9OWSZo/kIuFnAaewBHA0BBUfFDwI10CCFhRnkMobb3BLQwLx0o7",
	"EtA5Cr06IrimAB+Y6wgLux4uVhpXLlm6Ke/WO1ddlvbr/cL7ipuqHB/LvLU86NrJJSvRnNLAn+JKTbbW",
	"Ruz9yxm0qQyKk9FSCLZsvCjNjei4jqtD6XOohOvN5kqjcPXtTHh8N4sJavIzyKTAYvEbFnsX8BxomTfJ",
	"QqVb5R9QnBF83w6NqN+lNk5MIUnSWJhmpjKmlq1mdCIxnogwIdtk2fG6ZjTRZrtp6XXaF2qOZsmJZwyb",
	"l4rbE7rwMAe53GMe6ZLNs/q2AdEphSskOV5KXR2kaynZhX0Q7qyz58e0Gy9wqugRLrjIUbimI5sCU+fI",
	"SVtFlSWtQHDiFucbIZUknF4W0aqgykW2bDNRs65MPkvAbcTzL2qQSN5gGdPRvyvhP8dl27pjW532EArN",
	"TEtmm/4V8qyeRgLQvBkRWBc8RqCAokWMPeigFEXcU8YRLKiiVuclBJX3Efv9x0Xtxz8uajc65fIEYv9l",
	"H9TYf39c1JrGLVTUDPofM4+HIZK4L6Jpk6fTGCycnozPXkfubLttW7dRnSLrh12rEvuIXgxqfLFgOFVz",
	"1bZ2bOQ4ufuzHXLYeJDt0mOaJQeBCD/0Tv0D4BFybPsBrB4Fwibdk2WghXfcdRe1U+EKwaEL2umoPBHv",
	"vF3g4nsRWTzVdrN2UhRMVrXVkZ8NDuAEtyjXOQ8q0sJgGqkskXYJy+p/jE4p1Q8Zm5xirbDiihcXAhRv",
	"jeewrrD6+mWKtvmt2U8K3KdgYoqy7EAgH0OlHuuNH7/kIsPfMOXIT98bs0rVBgxWK6fBoVwliCWcec+8",
	"Q9In0esGeHxgzOHzcBw4JARRxyrv0/blUi7DLsmSi5+Fasi82Xg35lLOlxEI0kV4JWW08EL8270/er+V",
	"4Anh5Xrhf+o9YT5pzn5+SesgnzNkcBqfE6vKDzRSt0oE1gv/kXeCXxp0Ny5qAULDCSuc67F27dr//f+8",
	"33I73/2/L/5276tzOpMkqxFbiy9JZ0QY0/+I+T04JvQP/oeUEoX3iacqG5OoNWpsIzlZ+7S8FDbNO9FG",
	"vK/jyXXhT/gofcR+iRzCS1mvGMVlIFFuW/odbFKsG++h69vyW9M7hlvzhOvY2SNJzC9YSBiqYM8Ab9J7",
	"ClHGrvecM8uiogQEPkGkxhrwU4h5UnDKUe1GoVIWkjU5EGBkG1ZdCYb5mPAxrV5UkUghMXve4SVyFMQC",
	"9OdhgmlECnYzkD6toDyQyG8Y6c0C4vu3LjwuM+ehrYuwm5HlitKQ7VCsR+mOqQ6knIWgW9CQLFkt4GEK",
	"L0urlYQmvPS26EF5OpRA023qRTjmfMJYInf/nYi+J+Ti8j8WRzxjHZbCFvnwtSisqfwFfgB5Teo9uVNd",
	"1DgZSwch6jpEJDig4jNwaDxhLgT/kULkGuI1lo+F+Dswj/iWrN+/4omQswXtf3PMDJs94xoNunUHFeji",
	"Uig4mWGWQldzhd1Qqy2g9hqSR+9B5w+FGKf/EIBdYOgurXsMVygLvvoPDSb+KzaY2EY7hpnaq4HP5JQ0",
	"ZtBGGrbVwm9JW1CymJVSZSpL14bvafcLZfYNUQYOSboTFD6GPcD5tgXRxjIjpJtO2sYzQKkM2/6q9ZBV",
	"gJf/Rwwbni5VWm8F6gQPYPmp6Pf3NVt3UbS7wnS2s4IupKuJNuJaGY/OVPajo0a1ikItn7810fesuYo2",
	"4h1C+epTKMkEv/alHzquXFDHFWU3B22Ee38K8+foyTKUdgr1Ts1NbksW7W1A1Qmse1NNErCVvK5GXydu",
	"zM9m35xdmt36xez12WUpVhR5aDlbdlGcGlNv5aAGbtNjopxooG5j01peAK8SgPFmJyO2VKoVqbFKM4FD",
	"/DIipO/cGb2DHyqP3rlzJyaoxzMxgc2LowV9T37LHfNOwiOQQc/xkfsVzcgWpUiKsJqcygTNGLSgSOqb",
	"IesrF+mbgd+Tpg6M5weDg8R8nvfE08Cri+F1GNV7Il2HwtZCCf2H4l2H4nuY0onIjvTuoF07BIHAb4Cs",
	"XVFievXryBVSVRWBHyT8ZsAiV9pPmWYNYoNdlReaSRUTU2zTojuRGajWQsjhVC2G+KOhrYYsizLbSoiZ",
	"p6lLEaFfsRasI//8LqrdwkvRabo/RIDlEeDvLnK7ZOmmQ3wvTpjtL6/v1pvNBbTtblir+l4Gx5b61bP8",
	"i2B3gmgrh3orNISyXUNvCrQNPPpq9GVD2QB+DrnXejayssNpXZsGGLcNXRno23PTvCrZkyigQcsY7lYt",
	"wRtpysTqBfDCMvfeOCJCC5pa8WOfe9WW2BqJS7dL3fiSBg93ZvOxQ8swcz0RmTVQwr+Fp+Hc018W9zHT",
	"IsgIzECIOm6IYnJvYH5i3ahS79wl+BFkMOipcgePR4fj0lazhKhWw59CF+04JHn4KkoLm0Axui7KNSbX",
	"jkLbabKv07xqL4ihD3Hn51lVFzZ4OpxJQIZiGqtEp6+aDUs+EaqpG+j73HeOm+V8OB+JF7JhmHBMeXsj",
	"yc1XhJmJsL/PNe+Jf0BSHPJbj4Fzrzw+iKPmghxwSj8YZ4flGC7ig+uRuAzviguySY4oZHLgm4vT0NLv",
	"JPYYlaUJdFkvsNDxJvEoj9BcqgBsUXjiUsRlOjqV4L9R+JASKuclfqXmbcPWjLrMgTR815HoF/qaIt0f",
	"0agzjWMJDorUaKnoQRD3LeptiPsBFIezyMuoGEcq5N4aCqAQ1LIvJZKPuZhUa5KskzMxtC/N5ke627GR",
	"yhiLZxt0ASo4pe42kwzkZnyNUCETgI6L2nletQ6/xw92tlVB+8cg959FQ/fnS9uS9EMZJFIfbAibupJf",
	"mmneHpv/ybncGwwjEpw9NGWBFr5Dx2aW47NPc4FJHjCDzoQKj4ws0czuChKnp1im9ZplQ6tH5vaI1q05",
	"8n6OijYd3mdEP0rNiT6GDNYjpj48g7TNEyjQy+YLipbXSY5GwJuS7mau3ly5gHIveWAlrfKL3KMnwTIQ",
	"RiL9I/wDgB3ncoG6/oNgDqtDrgyT09+L1ouFIA9BxRioAJCtmyz/vL7yoCt2RTpVxiZZ2TpyfcjqrPl2",
	"zRmFKdfm+C6+fBVHJ8VMy3gGpeCx+PGOfRvtzdbrzGmYubcDl3nTZ4CYERXjGWv/oGjck7Ffwps6BMr1",
	"BJhzHkxDxCEPqDvDbEUBbTMo1tfkWOVBaj9Tn2nQy/+U9lfgtOqA00U0XVmCf01vUoTqrAuRrQkD/4QI",
	"LJ3/qVkb6bmeTIV9VqyMqnky2ZObSYw8b5muXnNXAUJLVgRoO25ye5rIXcu2MAGouqkr3/lXiKgCAw72",
	"bmgZmmrvcn1FYc2aGxnwMUV1IH7904614ZJxM2WEJe4FEUnyDhxU2KTJNkE43S3yAYC8yGRcOEWacbZr",
	"NOs2MhWB/1juNilp6IETJ0P4t8bzZaqXKKbv4RscuCRH1r3sQMgysaIgPLnCiYghyySH3vCP4NftTKNE",
	"QnWKq2tDd26luj1rNtJdVN+S3hosewvWnbusmNIF7RAzYxS09b2mpSfUGNNmVwRegqut6EbHi/dMzBox",
	"jL+E01t159YAp0a2DhTJr6XfWULmjrtbuDoxzWMFvV0uzeilxs33r9wtBf+ezPDvyvhdFelbyTB4KZPv",
	"QCPCFD6A5gzEyH8RBjzz80JURSULz08j2NKQbYo8rwoE31Tzv7NkOK6a+9tymAt1E1BlW9Etx3gPKbrf",
	"PMvzMrwGqQIwWPKs8g6vhdSKw8r/Vi0xk62nrCHiyUhJoOEHKrImqOHCsXnLtvINpDdJXlA0Eg3KPZqz",
	"jboCrSQ8/NgIhBRjUHG1yLMypVNvNQd6c/CY9KWmazRsvVPP+dLgMelL2wMR2k6gk+Ymkfakdr4XR56V",
	"vt5wbePOIFQLT0pf7ejOQC8On5O+1h3srW7SS2u6XXdYtXy+F4uPSl9uNZtIVUab9ObwOcVrbxv2IGsh",
	"PCl9td3K+Ua7JX0RTbvLuaTBU9JXCh1Icrw0fE762qRyg4S3Bo/JXtowmuj1jm7nFDLhY9KXWjYydszZ",
	"mmvcljeLSnp15GHZAL80bH0QtuKek762Y9T2nEHvidjTsiFu6U7bGOTt/IPSF6O9WtPSb63a1p2cKy4+",
	"Kn35Tn0gmoPHpJ4a4zYy5el1Ce8MnpI7f3Qz7/vgEWmhguUaDSMhGUH9UvFRKd6P2873SvxAUZrptjeA",
	"UAieSnrlegsN9tb1lnzH27e2B2Gj8DHpS20LUNzNnYHeHX1aNsQ7+ntty3EHeb/4qDyVs4FsW1qFk/De",
	"4CnZKx3ix873RvaQ9IXIMgeZPfec9LWtgRSl8DHpS9sDaRzhY1IQet25dV039Z28Ci7/oPTF1i1kys3B",
	"pLeyp2Sv7DjIzikOyCPxl0UMM/YzIot4xSIq8KJXCn8T8Houv5VMEkd0P05URRVZQWHipQRvWPEXhrAZ",
	"kvta0A94HSc8HAJL80wTMxOjR5/TGQUDgjOuJNJIULFFAcvJAN7oo5puyCIxI0vUJSJmUlT/kpvZd9Zd",
	"uYh5Y/EfS9R5QerwRog/arZ07VIEAzv44uaPpADXUBCAHBpphFCIvIsnZBVRzKTvGIL/lCOnH0faHyLU",
	"ftW8bbiovqa+PqQwNgzLyXsKIeOPg+jaCYsXA9Sg9wLScKKhWTwXF9VngSHnFa4gcPKXiNsnWvZAHPCw",
	"RC+8HiBoPIiU90IcIKzAkjm+6vqes4Qa7jXLTpg/T0dQmnbEnFGkcu7j0Df5MkSZCYswxYKNHHSXZXQ3",
	"UcPNvnZnLKMOcEgfEFSu7BSMq3qLqgN2fbYKh8S3/sQ/IDFggudFw2/4Z38WWCWS8fYbcP89IN1evlRA",
	"D5JtC+t3UiL9/K/h+Xd1u77QUWWZBnWAkYphgvZBYwY09YWDMIl0q/T+hfx/yfuj98eS97n3+ajm/Y7W",
	"E7PMWo00o/d63nGRFhVzWay0Z6X/MAIfS5JVL5fGp+TLg6e33mklS5vE2TyTcfGp/9DrCVzEYDOE8vqE",
	"7R2/krWMGPy4yewe9RoTzs/B5JOp3mQKLBdhuKJcmEmIjp9aufzhN00mr39ibb+O3JDfI61NEs8lFzbR",
	"IB331H9A+0z0IRoL2GuZG8Bm7Y76E2t7rSMJ/Td1qXHxn6SdLzkPQcp6HIs4PAR2J3eT22GvRbHgWB1b",
	"Bj+zuVZNngrF46NQx4DGd54dCCgpkgVW7IiKgwya46TXSSaU3lwVvle0wgpeHs0QETucSUiYJUk9QUxp",
	"0IHjkSYYwmp/T05JkKdz7o5vtJV41jSIJGiF8FWDdltJekVbt5Hp5m0wAznyBGAN8NnuMxy5HPNW9wUv",
	"FAmQaECbbK+WDPPWCvkrtluqDOq/QGe2e/Ke9x0Zluym3cycVN1RgJ0uGXJsSi6vd3NtKaaix0vTsiWi",
	"cgsTz0bdXFvSIDv/OQnth9CladnF0Y5kpEQp0pHsU4gZ6O9859RidYrUqdwnmb99kkV+yECiCVgf3sS9",
	"75pYToode714zb3IByHZZKWlHCdtjjKUdsR8UzbpHZyzYpnLXctgAkjwpET8Qoqvk4gSoI1MBVakHB9I",
	"WnYWm+mu7iygpr6XlA8bgROgLEmPUWhFC1nfXEZ/SLTYZycBkilzcTRkQQtFzwBHWoNSXSOlKUSAhSsB",
	"QsDc8w1FmhMsX6L6PxIsOai5Spzr9GVp9g2pk0rsVCvvIJhU4bW0IscfypZpFj0b4XvnV66vLi1uLC6o",
	"X752bhgCcmdy68KxqEpKKOGs/g329Ygkj7+AssE+a1hMhBa1XXvxeYtiRyhbT69KJBIoGDFhVacIvF+c",
	"N4Sy99iIfZB4A4xXkY8X2YXs1fK0QUZY8JDoHQ19BUmFcMW4QAT3z9feY+/P37lPNaWE7+I8rHSph97f",
	"WtphWtZMmhIQXHUMBPKH1tKvcmvptOszylJ/Zy2lmWGTt2e+gtmd/M3z422l/fveUxBspB9CKEKIO5To",
	"Es8I2jsoHwfZ8STkR/R8Tah5/I487Qmi6lAMFTuLls31r6mHNGSEG1kJ5rCrOyu3kV3vKCCaciidUX1T",
	"N+pZKvpW2e+yan5R3ACs2GklzfuSMwPWFq9Vl2eX5xfxF39OLLDscTKf6ojB01J5jwmvd6Qi6V/ggH9A",
	"WkzIWnpn4tZ1OkDVRa1UFuU2MKohBjsgbmMAWlgIWYebVQqnJ3QMfyzK+RBCgQMBjncTsGqqlnkvo+/L",
	"0Sg8zzUYH0hCeFEDNyXB4oOeXdTe0dYnoPiYtQcGAw8bT8dyiIRXvz16rjbnRbqD3JKncFDo2YvdCLRp",
	"jtfP13G+xvFkRgkYsPE5wK4TG7vKO0jwqRLDbZcqpDRQvj6E0o5Pwhho4qrSzMwLIC5pWG1ETuslaj7b",
	"qR12A4D5DO12pYcGkSz1xGob/Ju4W5vykLBwPNU8ciwME+MB1VlZ7dhty0HDiVhg0fTPFFu+K7TBBLgN",
	"pWujIkf+b+idpksJlAYYD6HvFWtRRs7zhyScQOpIPwADgMi254qE/zwdv2goup80GWwvEFF/DGBNx/4B",
	"6Z4GxfPZ2q9G0VUia6HaTMw9LE1K4QLgfTJpG/TqMQObXCoGKzvdEbct3ZmwgRHx5R8zyA+QJJhlmPz9",
	"VMo0RjIZiVBY6plPlMHtZLSwjlhWQV0PJEGIRSYKhkJ8VVV8hW+xhmG31o0d0RKT10YmWWMy3S1uUkN0",
	"LVtHjOGZWE4ETVKSnEL5WfvbvT9KeqvyHnjS8+hcQKz1lPvoCXgcv4Fo0ccM+02AfsmOh5cskeBHymVT",
	"M4KyAEMoyefOQ45WKCp0IavRIFpv0sMr8KM4yKNuqtijFrRjPp9U5GEq+8MI7UdfOHCAP/VFQw7z516J",
	"hD6hAWl0tWR7SM5Vgms51bWdxFHz/G+zOLKH55hOFluqtN+o9DiJBBATpNfwjIkMImxYgnAAiZcIbs2t",
	"emLM5XdRkC4KgBmS992nqWcDErvQ6Moy18ScecmMOjLDQpI19lo8gvIrds1jVSX4WGg0pnw0tnLLlms0",
	"9t4ybLejN+d1uz6/q5s7aA29I+sxqDAhPheyVUkvX++IWATHtDXzCTFOaSo++GaK2sLiW9X5xa25lc3l",
	"hbHNZfivhi2c/VHv5eglESSkMn5FqjXfNmpozgDYNkhNRbS1Hvfo9KTkSWTblp3crufM/9jre0+o8swa",
	"QvdA0X/p9fwHDMoT8oZZtt2B5v+axoGpI2gMghYfk+uB5t7xefMZbkjDWbWNlrzCOtKb4UwAG31KemCw",
	"nZBjiBmO00E23v811KjWh5EAw5o0l2iyOJd5KFsica8nZVvd7mw3jdqbaK9K2ZsoQTyOzDiYFwHH5Mh9",
	"9b4QqSQdz0lj8aAY4j7N0zjzHwXus8jwEsDHW8icdZyOrZs1tIRuI1nlxr+yFhjeKTHs6UC0K0YI2xjZ",
	"Sm505eAsciAJGr0claRckduTG6injYDN+A1J8H/iPySN8Z6BR77vf8C5obqRU3ulMjOduilAJhZTyHEt",
	"W5LpW49sdEX2lhaya7u66VbrErZQ6oz8Ck5NS35o2caOYerNDYHIaiaSXKed7Yfv6s0milNe4TIpuOsn",
	"nk+MaVt3LVvfQfGXYMGZZQcSY0U842kj64tF7Y35xaI2v3KtqC3Or1wvaj9dm4+K7GnVUMZ7KFshzIb4",
	"a3zZh5fVUCQVdMHiu64S3T0QYNoIP/XIDPHNkrK0ZGvzytbqAk+Cd6hFrhDAQmd9/l5S+zLIIiMIVGFn",
	"nS502if9dU4y3auE6lXbum3UkS0lOxWPnB+3G51AVxvZ2NWbyJGRk2lJ30phhC/OsefyBSeBntl2u4lW",
	"9T3tb/c+1xZWZ5erCxmWNIZHKd64Ub6W7ACvBMSOkVThVCl30p5budQhQVGWtveE911HjiNHEfvaOwvC",
	"fNQ6P/Oe8GOIrQ5WbqUq4uEMIqPL1maF+VDEVXA62xvojjIiS4re8ClLz1YOE//Cyjn/IOqrHcEqWoB7",
	"9lG4GjTlGevKh+DQ7nonWkkbL0eaMB55Z9qE5p14L7xT7W/7fdpmYkrDMkCGs18suPIZ/kWYG5MppEk8",
	"JYugjLMp94kaIOQMs2Dok7Cdvte9dP5pfuY/DIqsezwRL3gGOoUXPKeJUVDMRF5JU7JOWC3wKezWS4qA",
	"PoTgrzDf80/3a7KmAbI/C+yBwhXEaDLXXMCOFwPulh4Ic9vSbWxF4Z9kCa0EZ4FD6Y8Gk6XiRHpDH4OM",
	"4V6aKWSSFNkK+ZltEunP8YRWjvYkGZXeV6D3k/S9uNNA5tiIh6ZbCpHHJ0uQXAfY1T4k7wlURYTfruu2",
	"natjY87EqGO1UNMwb43WrNaY0doZbfMIJgo2oEVDhDBx2dJZIcH5jdkqtdePwCZQYSKb8HO2SVYwOLYR",
	"j+kh6mb1mUvYOC1sQmYhXQi3fa3TbKoXQHdd1Gq7+bNRX5AaAEjQgBI+4jsADn1JD0M0uFEpl9GV8XoJ",
	"jV+5Upq8cmW8dKWh10oTjcpkeWJycqY8VcmS1UpphnLerIDFssJ80gcIuvsLHTo4pwUbasNoIauTdTRo",
	"SXDsH3inWOn4hro2+vyAx9xaxVZRGWEgfTaHO2+xeyXQdcwAFYBAKTU2cu09vCoDLckR+YSPzZHeWXi8",
	"oERK0NGm0zPyQ17m6YutXISB4pusOEhDP0QRbqBnSrUbWU7G92dXpGsc5p5KA/aRcqo0D31d3gBbgWdO",
	"sUbEwq1jxVlsGCZaQNtDDrS8JDcJZOIaplqFvw+aB4XOfhmUOonrE1ZuBisE+cUZGi0SsIQCN03Zbq1y",
	"Kb1ZwlnBlcGVjcX27NupS2OmDZjJItiINCdbcs5aumEOtfkNR2bXfySQGYKCJJGZxSwiKTXiGp0Rk2jo",
	"qxTthsbtbHwBKe1yPoP4nTq3ns8UjQXDIv1cdAd9+ymNz7yuVuFa2V/KEi/NGRr9NvNJR/iDx+f2HTKY",
	"pj61EINWTWeY+RJrZI+9PqyLFV4CiVoy/ZkiJsxtdKYsy2gP1Uyi9zDwt9EZpJZIGmbD2pTCD5ynphzi",
	"ZWfwzAk0KAe/KgeSJZQcCyo5ZZ9BXCxBeh5bkX7WN2d3TQw2RLILge1BAhs4oC3SopqqYSZofgGAStWs",
	"G7eNekfP1H1mVvUcgLObt+aMmhRI4gvvS445ZKuLn87aAzPxRQkpSX0aVjzjX/Z/vD+MeX/1/iBvYHFn",
	"Vd9Ddpal2eB/q0DdEd4Xrhg3+6TdfR25bxiOa9l7UFQjSxPo6E1S5AnwUSpUcGbakzLJY9DxWfUkTc44",
	"BssurNnDGv+HgFh3GnruL0mtXI6KqmHOGaZU0/rC+2oMGOOrC6cisXPUBQ6OtwDZbd12986/HdBaCH75",
	"dEg0nX9zhk/TwFt1blJU9fiCnaNSbZS4dDVgwNmaqpWJ0NLlI/8zcosKnCdtewWvnTuXwA3fsZxTYoaX",
	"fFZKs7Fa1rflpTf9tRwHqvfqa7JBCm5Le2/2zTrHALkv0AHHyryfA7z7zW25F+gz73cDvjHrqmR/OU04",
	"TM6fjAuMeBhl2MbULbOtjiKTRp+k0kyqZWOR+aX3lff4kqJR2jkMWnnqaFpZXJ/YCaKnJJL3lzv/VpBJ",
	"glCJCuyYlIyKXumxkHCfXMYoDrBccMRPSXBrcRxJWCC2WUV1mUVcu0xqTKbuAME3zv41QcKgZ4hEn2j+",
	"3j0Sbokbd5mCTApVWBZoSprohq2bDsmqndurLiTYSRnUgjg7BqbezMzozFQmRWEbmahh1Azd3surLRAg",
	"kgP/IwZCIm/by42QUXznf3HemznjCDWr1TIcBWzJl7AoJ8Bg96Nh5UrW9c8q0hM2W5UjXzfssOFVFuZe",
	"aSNbaNd5azshN+gJRRv5ANrR9lV1oBd+NyA7L+Py8Zl+0v7D2zMybb6X5mXY7G/nmvsmrgeFDYJq4tNI",
	"4PlHly+Xy1euXJmZGR9XDkK4xco9lRNwvfWDLDwwpEL23lcDNDiZ8kcZN3OyNkwldcMP01ELEm97ZV9X",
	"fohMKktgiD8DM1NZqsa9OG+9Wv4RlCz0WAY/RUvgIKnjlOOx3MNmcnhFfn6ROl3Gjq3CzsjWMc558Wnz",
	"Eptr+BrqWuFtJCheEoYjQvdmZmVkDdWQ0U7oDJsTGE2JgZYERyaVFvkhVpj7OhFRRb4MSVW/RsuQ3TB/",
	"4PARY4HyoH3BIXDYr7wu1k5Je5D42Yj2Bp+elMbSrUbDkfbJ/xqOHZdeyxIkwKzs06BUvC5VMU4SfMhX",
	"DDUEZHiXhKvpMSOjggTCcmGfL5RKmXSyXLVc6rPL35p38NXmDleq9SEfJJd9wTFjehYbT5qwPkXKrgGz",
	"8LuZdBxEDVAJIiZRRml14/za4kIVa6MLi3PVDSkCmGyqkpACqHQZFJka0+sG9K4qLqkBTCneTFYovBEv",
	"zzl8PFnv1vMbAa+U1jWIXpSzPOjb0lpSdAjxBMhdLlJ9QWDDLHpHkkCIb2dK0z3FaaLiobq8tbq28vra",
	"4vp6oSiAP68t/mRxHv9TKjKGY9SARTNTqUCJN9el7caNH/2PtyulmZs3btTfr5SLlfJdabO2VctxFwPQ",
	"eXlRTVY0tfA9PJiajT+YzYSoEr6A/j7KUPy7pFuMZ1N35lxnQXd1+WxygrsMhEA5ApnhD2mFfk9LQpss",
	"sgZuyl9cUnTiMkzdrNFufiltuNhPWSq509lWqa0E+OdZLBGDYNSGxU4siThuOH2aBKqWmEuiLjE6XxLJ",
	"uWiPJrEJkJpMM9fDHDYAbwoWWMWmtPx7L5lXQ4jaTKIquReCnI3Oi0Qfp1E6ZbJqVbNhUQQW5gaXImqo",
	"nWuHTGHwH8bBGK5qb/5io6htri8UtcXNtUtZXIo5IesZUBifF0fTuHk7XKx9oej1GRSuXHD2F0pLNNAT",
	"XtHZUe9XAezgBwD07z0AuriRf4/451LccybdE/DPJSuTtdnAS/+AQCLxSD5JHQjayHYsU2/iC2N2x0aI",
	"wjcH1/sGsltOVWwBx+l6mFRnl2Kffzc3zkCXx0/X1hE4CfH8DJPiAancWjZyOk1X0sIupkzC7+QDUpwM",
	"1RgMpSN3kI7CrHW9Q/9D+XFs81hV+ZKolGkG8nRmHigRTiABOfgG3Hrg9z6LwKp6h9CB/AFhU3bJBJhx",
	"YYMjqNEAV1E06G3rLeTKO53zBZfeSxJ8g3udFEwLiIOZGgxGUWHBpU4F/SPS9ZID1IPMpmdJwHpWW+Uz",
	"+w8+SB8WS5DGhqSI6inL1O6TZGb1bJILgj+XrzytKBdnLA1dSsH1EgItYgKClM+a+rYUEOhPQqkyeIoJ",
	"nafiTVXIhg8TQZC00Q66I7VgBNbB26GuVSU3r1g//U+XsdU8I7eYVTBtv+cjviQgL3Jwd1TzHpO2wrAI",
	"f7v3uRbEifv+flHIriNH7AUpLRbLnPoMuEgYcLA1dJB926ihnNAjIAXu0SKPbjZ0TDpSTokZGynDHKnU",
	"y1+KyUfosQx67H0t44B3AJ8j8ar/6ZoCrkxxF3Gv5OmX3U2sc/Gs0oFPAgcQweireo4IraRPYq2kvZNR",
	"Dd7zCCQiU0dPi1qA12M4jBLtx5prs3YTMag9dcCYlNPcg9j5aQK9Z9prr7Gb57XXkttM+4/wR5E+2KM3",
	"TDyYBiAg/UAm972T9HXQRsg0togzUvux9q5uuIa5c4kgqd0DViL+GlKMCX/0QbxRRFVWGRUrrAUsT/yO",
	"R94R5pWrN8zKqEb3f6vdwUqGg26Y46OajdpNZBrOLv7qhjkxqrU6tr6t7+o3zMlRrY7almO4N8ys0ZlZ",
	"bnNkgkHaZzu58b2kabn/4Nyt71nT7pTBg+rdI9YUiJgGH/MeH+gjxArbo+T28/Xpr0zJqI02HU8mWlaZ",
	"n4OEsrwq/4cW+xfWYl/g1qnvVUP9YTTOF7rlFyMCPumyWrUtbFbzBpW0vJ9G2JlLnErwENuMCNenVK86",
	"oXXIEj/uI0nd5W3DRXVGUZbjSTyLT+GMfMx3cBXvF2KaJJ/KyOgXdm3nITr9iidEb9mU6i2Iw2k3OuXy",
	"BNLK+Fr9nCjWsXcXNXDhMy8PuQnui6Inl+jFVP3K/wyga6lc8T/zjrWRgLjggsZseRtdKsovaN4GS7it",
	"X8qAMAiaAo+95R9cvWG+Btpi2HamFxlERGzqgvf6fmzZoOEdMY2oJux/AjhyJ9IEEKjzld64saUbCc7+",
	"FlVmYCMvZdcWqiL7yhSGUB9MB/eN6mea91uQ1wSflbaaiKTQwjq/9hrWNF97TSupDv4nGrij+H4zojDB",
	"K0deBY2Vk9/12mveV97vsNKZ5aW8FMB0yny3bd2oryXcYX+N1eX1+av6I67Rq4ANobrvYhpDhgvLjpsY",
	"KfFF4efcGxR+90j6AplNXOTTSXI+erWwj+OMvae3dHPUMgnSWAuNrcxWN/+H3ti60/5xreO4VgsLsvHp",
	"tlH/MaOXfFJHqL2Fn9pyOtsV+iWyt4w6+V5vbNV2ddNETfKd3pSGBnYt211LXogvQcifAfwPzPZg0JWJ",
	"FuZlXI8x85fO1J36L/WCFKEULJ0kdg0vrYzcWIxaYd1Ik3B8pz/zukSsRu63PLcbfp5gItDnMyt5mvcl",
	"v5jcheideacEo+EkLhFiJEQkQmVqoMCbcJQEMStjsaJcz4nKHcnexo+9RGtJ0vGyxQvOIfrJvQ4Ce9iq",
	"BDZRyCDBVTmMcbh3c2EcMo8CZ2OLjQ+C72MHksvkCDuJS6LoNqobFwIC84w6K+8zxFIAtwlLj7gu5LxA",
	"AmUF3O73/IMA5mXoaDVfcRA1XWks5ZwDZOgRQi6/CwLgifWQjcLNQHngoeYdwUI/9fqYLYMKZf9BvuZk",
	"XKRZnro8POgryTWiBnHhhuZXuygyfnT7YwxHl0Ih04SUKWl2Z9gV7Swth6sfR5UFUlO7FcGvWNZWdkS4",
	"IE1epS7J8snyEBO/pILlL1BCU9b1GtLdjo3yhr+iMT0K8dzHthm20ViZwsCYtolotilw6BmhablVYK10",
	"zrMEkFbwNEOUISddkpslMBaTagY+pvl3mVvERddDYl3mAIx6wgK6IiXZKmwIvfLFaQplBOfKZKL5okFJ",
	"DYVLoLZfkAzyAupQFaAMCSD35399xmZ05x0ooVWcsj/cun4brXfs22hvtl63kSNh0+2O0azTzFh1U+0+",
	"0elPo1lTzyBBA5Sb0UJqG4dGU3dTmneTjrLED4XNGzowuDvxOR6Vxul115q36kiFX3qE3/Sl9xvvL96f",
	"+IOWQm7LMKvkp+OSaLBrI2kd1r+yLdS8LsTseozuxOEi2xtOKRiqGO5V8l7PW6ar19xVSIGSFfPZTjI4",
	"VQpvZtjppq4c4q/gj4Z9HcpQUI2cmpTHlRDASjcvVArl3W1CT5HbGm4J2RSTN12eo+bAd3nD+0QH7gXg",
	"pTLFaDRVQgVjqwjfdJC9eMdFtqk353TzVtB5ldOiuSdqu6jeaSIGQheZzr9AfuQHpJVXRPWXhDiGDoFT",
	"D2P3GQqlWCDoAgwvVdKbNpIZgjWEA72k/UgTbUVtRDQNLl0Sq8SSHBoiPatLs8taSfO+JLlY9CttcXkB",
	"f/qY30Jt5a3FtYXNRfgiCt7MuQvwKwvFwuIyPkz0GUl+Z7RMIWL70C0OSqrqCVG79ZqF34lZN8nNkK2S",
	"hqabhi/iunCqbZoYXjHfl9x/hIWttLuL3LiJexqSgx38rxmhZrSPaNpr5I1HBzXA02tcxD4T2dRdivUd",
	"rV6JzzUTgi7lmzVIrP2uKlqaMa5NWmc5qw9c97W0Mrs8trZ4rbo8uzxfXX5drEDB3w6pCCdXAdjryI1t",
	"DWdzQcL4YJnZ65AYH7San9Pd2u7fVb/5Dd25JXWw6hBNBSGQrda/re81Lb0uVQPCm0YCm+vcIqpOtNCF",
	"7w05zRd/vl0uzeilxs33r9wtBf+ezPDvyvjdpAKR2Beddj3nMtyVLrEIDCw/aqc0qPKUv1cBjeFXRM8T",
	"SnIXqm9VFzZnlwrFwtLi67NLW4vLG9WNn0vLIjbklYlBnlS01dkhB+Pudf0HwhmfAB3ZaGFCpIkgG5m6",
	"hCrt8VyNPdWdPBNbd2bZs7Bvn+1K+yzXFA0NhZaQfCGQf+B/qnmn/oH3DTY9+PxZUpJ1j+nq/idZyr7I",
	"jNLExM/Ir2LJUmL/QrloENdAett13F1FN8L/TV2V0GUaGGmfpCdCMRD1hJPCh2MWeYm2qWVJUbTc4hn7",
	"gAZe/QMCmA5pJRBjhkScfZI6dAxllB9BbGZj/Q1tTFt9c3798ugNk8E6EJF7VZtdXC+NT02Pzc/Nj8Fv",
	"VvU6ttj/n+AlV7W19dnSyuziamn9jVn8Y23k+uvXKlpNo39fwu/9mlZ5HdKY6b536B/QZEAwyD7VVlbn",
	"IYAR/Mzradt67RYy66MQRIu3aDRr9l7bRXXoOUmbkSRt+RvoznqoGUIHy7zp7/G2z/HoPx5NXl9Nwtyf",
	"AZ/TjX4AYaVulOllyHjXZ9c3FtfmZ9cWZMN2HGRzbrHI2L9hvpuk0IDZaTb17VgOS2ILUDgrdMqyLSmG",
	"RyHlKLmKzp6Gw35Uz99t91yygB85jfiOk1i3KwiyoIMRSQIO0Ca4DsU0jrNZLQH/nZHmG6PcVecGpGGt",
	"HdW3biPbaOzhvyx3y5UQzl18cbSPWBnnqX8AUogEdqEadp/qviCuejRvjr9+VzbeWFwrFAursz+/vri8",
	"sTX3863Z+fmVzeUN7sPrK3PVpUXug43FtevVZbiyuQdX31hZXtxa3rw+B6+cW9z42eLi8tbKz5bxO9/8",
	"hRyPZxMUE2p5MjWW8VVCFRwndkLUJ9Yn/2Me2u8JrVN5IWQVqy2V0Xh1REjZECBeijQ1xr8PG9LjmAvS",
	"K474CyOktCenNA46NFSzbTRPXShBjslkjZBt33SQvWTV9CZSut3wTziEzfOFcu4nojXZ2GZOkzwcPbPk",
	"Afzobd0AQTynN3WzJvfsQQWBf+C9ZBzyhOTVeKf+fW1kmzxZajd10+m0Sm3ddg292dxbQNvM1ZUNfH9b",
	"ScNn/ICsooG2FQzXJt8wy7p7W5pHyg+GX86OKk2//XCUA4jwetrIm7/YuJS1u4DlpMItEgQgUKJIAhC/",
	"96ne1KwwWPxLmUwl4BVrm3OFYmFxE4vCzXV8MOaX5UZNwzAZCE9efgYv6qgI4X4cLDPp2PQhaK/34WJ7",
	"CpHxDDAMAUXZYJcGo4Mt2Oz8RvUtfL9Ul7dWVheXq8uv4/tjaWX+TQBnml9aWVdAMxnbupkKvEs5D34r",
	"e4dzLZzvShvJQSIiadGcjk5vmkEWIcj+CGU+qT1mREsxJaw2Sgcy4+jLz/5YCMmTSB+TM40vVJKfGXSd",
	"y3Z2MznqpcdKziWLa2sra8k84iY5K6RDzW+ugQazeW0O/jO/UigWNjYLReYWBLVpPd2zz6NWhAhpITxL",
	"sJOUk4NLKJTiUUEruWzC/ZIzs/T+jV1j8ch806rdkkfmI4eBJVueEb0H6so+AoSA/QAx43mY7RGWePSL",
	"xAI+Bq468h/ClQBWpqSfOT4opxSlj2ootBAGdN9PQ2Y8IPmg7A+o7u4FTb6jJ+pu8vokwOSHeklGlSGu",
	"PdHPVXuEzTKV3pOl87Je20V11hot21M13a7PN3VH7uTE3zIThJ2Xt6prG8SDt/rGz9er87NL0oOIWtuW",
	"4xjmzrLKQ9bSnVuovjq7LP2WxsnW9xwagmXjc3b2zUTg7myyhN08UmFCzLSIej0MEy80fNnbXresnSZa",
	"1fHz2DiCf2aTOfGdjy4/v9jcrkZXmWcHToZJliHRAcdY2Uk9TPK7IdAe1b6QTKEG4ShKKqkxiULyXNrL",
	"4HymxS2CqamWZoGUhCesDi0aT0voA5H4EszIvr/PyrqZOnFG0FrOtYKUVOUixvIYKd2qqUcTMNSYlhkQ",
	"OOO6VyGz0lidm10We4dxUZvxspBQg//kEDzf/MWNG/X3x+/i/524+/Zs6Rfl0szN9ysTMliSqMuK3PuR",
	"ScpW6y29adThxEF4UYrh8rHX957QdqdxAJUj5snyH0CdOC0kpZAlJK9B0gYVKYajMH9nwaii97MyWpYb",
	"F6hZj3TjXBXGk+S2JIAhxZB0SINkQhOpB1FAyUTbq75fMAw8AMEeanUcV9tGmq5FVclgTyJbSdYpmKFs",
	"D38WCPnInP4XK08hqtIxzAAfzM+8Y04zzX4lOKjWsQ13bx08vbCuc0i3kQ0+0NkOZuP3C9vw0TWmEvzk",
	"Zxuhc/gq/TbcxF3XbRfu3r1LurLGZzG7Wg0smieQ5PBr75QgOEjbbpAKMSElRPuF3tLNGyaEIF56Paio",
	"ZlbvhwqEg7edd/WdHWTfHBkdHaP/vsQ00bdtVLdq8BX869IovP0P/qfAH885T2IIYQNFTxqtdOrFQbcA",
	"1eeYpnZWRv9RG/Ge4q8179+w6vkhF/1h+xk5Kpg+7R9Hy8GT/yh7sLoQgdXB1L+Nl4Z2YzkD9bpPTNKA",
	"52G2wI7OJZjtv/m/JtRreq2G2m6pqZs7HX0HcX0p8J1AYjykRi4oTCDOYxaECiqqGCxXV/TFimtI8Vw4",
	"8Jgeqbk+GdXeniW0LFFabo78t+DCKYXwYyXyM/Yrsn9vQ1oNlVmk5OsJ8MRxTND1vWdakJDU9e/5j2B5",
	"6ga403V7D5bo7fld3dxBTWsHvt0lbbbGauzTrZZumKO7bqtJfr6Gmkh3kJb2WIPUTrAnaeKOs9JYJyBL",
	"XNXnu+++OwqVn9u6eWv01ntjHQc1Os0SPmxjAcTh1QIcEe26tW00kTa7WuUgEa8W6uh2qTxTmS6Vr0xM",
	"TKHJmUKxcKfUtHbgvHbsZuFqAZ8SV3eN2hj+fLRNRQa2RvW2UbhamBgtj1YISvUuiI4xpsKMce7dUsOy",
	"S46xQ+5sy5EDoRLF4yjkJ8rePbZZCoUEnw+CVM8jssugJfkSGpaX0ichSIvByVfrWD6FtF+z7HVMOZHg",
	"yHHnrPqeSvcJf2IgZyz+FhrLJ6k+RIODVRsvl5kCQzFVudUb+yVNJCKaVSoIkGRUqiyCRI45DRWwFNxd",
	"T4tysfaEjeRo/s4Bns8kmUKSopFlj26YgERR27YJIgX9i/xRmRitaBuo1W7qLlq23GtWx6yTX4zRn2gl",
	"zfvfISInrQc9BbBX8I6mDTCuVc3bWIFi42BrJz7GV3AF47nQkAyJoIlwpWljTWhMqaVnfNlyZ5nTJj7m",
	"b2H1PpagqPkP6USZfP2IIFKkETCprXLwoyTUAjpjfHBhJ+E+1iI1onHNUahfT6Nl6ny0xMJ3+EOxjX8/",
	"J0XT2myrCS3jr+lGE9VlhOzTCj1wdVKEEXwJ4gsS2soQPysDceHl2ez1JYJ2rdGbHBACTwkk50MSg/Uf",
	"BdglNI+kjxeXIC+BMDxm/Z2PLqXN57I2a7rGNVvv1C9uVhDI2Q+0k6NhT1HQWSH5PKatvg1OFIOAsRvg",
	"Y6Hl1zfv3iwWnE6rpdt7kIsvSiQpsHEINOk/JPhWzGzqQ8LTgYhmFORBQTv7HQeT433JPtQIkhZz2t/E",
	"kwmvTCGXMvs9KZ5C2SRIh1FQb0ILP3brzUMGJDXYnSDZsiggvL6tSuZj1wWkNBH3LKjFSgRkMKHe6SB7",
	"jxWIXS3UrRr1LoX2EslYyXj5EeJFUGu86fkvb/FN38rNHR3y1bi2IdGFts5kVrEQo+pGNKpkOVS56Du8",
	"Mjo51Dt8qBJnqMc3q3zpuLtj265TqtOy9x00qGTh8Gn6WtO4jUzkOKLG7j8AWziE5vM/wGsJ0RXvMOhx",
	"HCTCSdTv15FLW2hcs2xY4ZgMkp2j8CdjoiVYIBLggo6tSCrkp8hOLA8u4N8nSGShRtyPpYByS03CZbFP",
	"aC4TS8G5RzxdBCUjw7lW7EbS0RotaxHnYopypvQdpoxTSR/nLzDhJ7wjousfBAmcEWfQd3CMpWeFP7W/",
	"iW+A/4g/szXLbBgkkV2hDfyW6FBUPaIoB5r3tfdH7+ux0dFRrSgmi4HhEGYjgmA54hKOunFTGHI8CR1D",
	"OYU572E69hr33IXeweF0E+9fLpQAdx+1u+4xyVdUHm//IbcDBMecX//w7Gaej2WiDHWYs6tVEg64W0z+",
	"YTR8cPembAG+J6JkfLQypZHI2bbrgNmjGOyxYMjQxDAOr530MQEanif4ok7DlhBUW/G6+PWR/He8i2C0",
	"BgGKGzQccKNwVXv/RqGO6d0iRXz4oxsFx2ohzey0bhTupk15WmMFOFWTSLAM8+WQrgJwTFDeU8t05LNO",
	"IXJ8JqM5qsJx5AYlIZCIMUo0DsEYBTdzAlUToxWmOq5srKYxJEuT5qTvn7y/eI9ThhjXVtz2df3OvFVH",
	"MHFn8U4Nobps7o9JOrb/kCrzZ/K+saQ/FM0xO5YY5ZTAFMqmMGWzrotabVetmn9Na6y7cNyfS0aDVeBW",
	"Jaq/B2gZAeRrImFXRivavOE6cIQXW213j4lm2YKxZgxCvAb/U1t9cy5mI93HXHKmPOz+I/9BCm3jIW14",
	"R5ctd55BwMXJCyKhXEr6OUhjvpQgbgnGUVTTEVWXx7LrioCkSMV4Lo2FeTAG8/hLHMT9TAaSyrWPWZHL",
	"ACPAjkG1g+Qthwxmvk87xBGR3ye7xBd1JOSYyCIKzKgPwwmvrkETlP7+vcUPJi/Y9TA5Wilr5BrbsF5H",
	"JmYAdM22WmxQqQ9CA/h8uGL9+/4nCsWRRJJjXefAK5A96DA5WqkEFG62m5Zev2Y0s9FF+ofSEkTSr4Z1",
	"0UvTR2a0+aaBTHceuhgQvpQOyl0RR6STGomfD+zGHx8dL2tC/qrc482xWVJCqUxHOgk9Lr0LNzOHLSJz",
	"SXdK8l6CUJfVIHyY1JyVSQuSI3GgzW2sS21Q1lv1OzFCg8au34b5uYYaNnJ2E6XuY5VOHCkmg7UPTM/v",
	"p3HZl7PUK+OrmtCuWfa2Ua8jMz4CV80VvrVHehanSc0y1iyN95Dp7BrtRBMpZsuFEN1QV8XAwAYw0yoV",
	"bXYH5RsdoMJBRN0nkmeQccfBbH8L8qJVI/+W3pFZJc0gdEyEcWmqF1ELkaQzS7dcuKPhGj2jVglpYPSI",
	"9qp6roWtQoFNmFUropcHfTzSaJ0kLB8lWK3rfOX1wAnxgDTK8E5IweHREKaQVWkaHx2vpAbaoyw2e31J",
	"vpvFBH/Bs0BNOualYhp109oSdd6u6o6D6nN7P11bMsxb52fHqJsIbsKfrvGeY1bMy8Cfz7C6cRDASDAe",
	"6aVMYqKsraEdCiJA+ddw9/Iea2ZmnoZ2EQcdSUIRT6lVz2kouY/dldFJrVpdvm44Ld2t7cZp+8L7yvsq",
	"MNeYpO6RocDjfchaddKsTP6ZKLY5XtTqAu8YH7Yil18tyqWYNa0dI8nUjm6lf18AVuYx9ZLVNCaKsMnn",
	"nfoHRdo1NuYEKkq9CTCtuG63BOSfV7Erpj6xaLr23qplmO4bSK8jezBlcMnaqZoXrgmuuO3BNEBO7haJ",
	"p4mlAL8ghhVNMX4p9wNxCb2H9Fr43gQlRIkianeCRvctRSwGLaxIu+ZBM5prWrVbUrk95PsPRgNc2qph",
	"qkXyV1HJG7gVT1msDC/KF1gOa+RWCD8W5UqCby3Fgz3B+dapIzu7dx00GYICJHOzd2Nu9mfks6ADZZrr",
	"VSrHc0rcvPeC1XGT4seYE0N5IM+uJmnxrO/JCeTmMddQTxpUHtW8L1TVAocabR4SNh1hSZz+fSG00Ndu",
	"FAjTYmUIU9j3Tm8UVFcInul34RwgQyddCPJ30N+NYTEPUQ2lGzSHXDtkGzroGfpWxVzyceGYM86Duc6B",
	"TVwqCQfhT7H04XiihFpTi/MjdeJ8JwzJjf/KOKz+osx4iKVu+w9/SGCSJTC9QjrBhEahKWHjE9xfKceq",
	"G/OJeWeJQ0+HAXo6OBAid2xInCgB6haVCBrUp6cJovMJh0xiCgrKx94nGJV34UAbjuGSAyvP13wcqb7o",
	"BfgapJib1OIdUoA6mkXKp6fHki7XwlHzyi1ABVxY1d3dVfzhxQYo8WAhrYlyJ1b8x/V7i8DAqaWO0Bfq",
	"OMy1PQ3WnG0DkV1TsrcAArmpN7V1ZN9GtkbtmAxOBoVP4dz7nzF9mFXmj9UYmHapbVsNI1GvhJzDQ3AC",
	"/YohtcQ7IH+q0eIAqrNwfiURn4D6xkj5Jo3OBfFkAbHbPyiG/cZ6YGhw6GHxuDyZXIATvkpmNsCtK3/T",
	"Gnrnwu9g1ciKROR/DZmepL0TR/1HZJOybMYg/oBsXoDkWFCW0xI9JX/OPTnuYHzOf+U/jB0IKJgpWY0G",
	"gZtQHIavgwRcmor0AUkcjbH4aaSUAkCRZTU6dMdXYOBBkmpjb/k2+FQ2ajqPemfMscAnMbNVZEH57wl3",
	"qmT51zmml50/d5BbCkDASjxujlyjkErtIxGPMgdMTEy/CGpbWY1Z4QK5TTZeZn6Llbb20hbiu+K6jIGK",
	"7DrE4PudlS1pGw4CV/A+/WveqqO7OVlTpVCILTx6sSNU1ABUjsBUQGPbE4ABIil/DJwXsOuDSjewyQL0",
	"uH1wqB+DOy7G5qtkRhQkO58eHXn8QtVocaxznY3YTkDKZGwnvk+Kw+PBZ5n9KHRco+mM1fRmrdPEqkSo",
	"WGc/BmF3XahkBLaNKTrY1qd5Vsxte0QfgM4BXPcL/CdX8syr21AOcMKS0qi/kWvyPjI/u7YwtrC4urJe",
	"3bikeU+8nvdMY11XqYVPuunFmj/FOu+MxpUftk6Blp7XSBWep12PMgQOIw9uILt13TLdXWeAh6l2rrto",
	"kIf1PavjXkfurlW/aBtbGHh41sTfm/FA8OQ5wISgfJPi3Y6xhvNedyy8OsTUS4WYMGouNTLy5NGLyjLJ",
	"eyI4UQQzOI4JRT2dPIBUTwtxj2T57EbNJfr8QLZy8PQaeudibeNgEvmy12MQ+yf+gXTphOz1V1DzS00p",
	"HoRbeNaVQIx5fZ6B66iJcjKwEF0kNA6JZxcIMQPyLHl6GDwbBQiUgRe+FJFJIt5Lr6v2X34v+TDbpudj",
	"Pas2tr1XahhNoiIkJ0j596HgZx/Q4GmCPK2MjOExqNjLqr2O3CXDcef2rtFRB+W1+Ku+DWHJckbxwLlF",
	"Jl0zSZHPq28ix/T/vCyhlFIvGeg/QCfBL06EGPkgPG3qLTT2Pv5kC//z7tj7datG/pkQMorMSQzUS4qp",
	"Eph8bo81fc6lg4cXMn6cCxgVMz0apDSLD9/8lk7Ft1EC9z08G7JqI1iAPvkEOF51PPpyeKh8J2LYWrK8",
	"OFQ6hd5AeohVO5/6HLzg27wUhqBDf99Z/wL5KDfTD0Gz/hb4/JwqN3vB/8/e9y63bWR7vgpKn+wqUv/s",
	"/PN+kq3YoxnbkiUl98PGlUBkS8YVBXAA0GuNN1WWdD2ZrJMoznprUp479ji5Nftp7qVoK6ZliXkF4BXm",
	"BfYVtvp0N9ANdOMfQVKSNR8mFkmgT3efPv/6nPM7s7qHYnUPlGHXkEvsFKM+JCulkH0SFHrNnpkYx9jE",
	"SLK352bLsCosu47sKoHEzidmaaDviOEXkTaKr6DXzxahvQc17RzGkQbDfW62NjTal0sk33+oZvV5/Chp",
	"mtuHpOXeciZuByNuizBGbr51bYQmmrp7p8yQh0a6UJLteuA/ZKneDDI2WRAv2whdN0zUXziEveUsEjJE",
	"Lpbevf8KIMLfhMCEAXBwh5WIUhhkqFyFzDz/gf+orLBefqEMiZDEHR2i9du3TC5PHA/IAhGX9dT6eX2y",
	"Ty4GJ3ayUXP7MJTl50zBqEUtZcL9Q7WTi9zmKa3k03CjV4AL6KqQ/o4EezQzb/6rtTJht5KqzUnOPTTK",
	"Z93r2v5XkFoOKTQPCRQAX7VT4g3fb62VxVYhvBby5JAMX9YB8GTxWnlbm5vnQnBOlUGbOWFL6OO3399E",
	"qGFcgEuvITdAxCzCq8Hzg7YNfmutUEJPESeXwi0FmVhvNAbCxx3gwocciaNg55lGY2wY3IjHOWNI6bYX",
	"4EurmRIrwMTsANBAUElJcqd7kZBGezRKfwlPoZgkxY+eqf0UE3No+5+Ledd115rY0JsTrmMZ9QQWfgqj",
	"k95l26F3wLev8v7uPfN+9J5AoOup9733k/dMwW+/m1mev6E3obfr0jxY0IX8++h7huHmx8dUpPa+K/Et",
	"ih87Hma2a7RrNE1cZ7UuzO2XMU2VNuegfJOLiRuG42b38oVAbV4vH4KZeT39BX3NMOFVC/oautVC9mbm",
	"pJ3w2StWy3T5h4cTKcgfvk2MFjC+P0XJbCpDMBcPlxKPLc8g6C/sOryYa6Foljziejqz00nzfgLSz4Nk",
	"lZMhDMtvJHbZKCEz+BpyZ/nB0hAVf/QOk2YnA040SS6mGjUxBvwuKVMnEyMIIgz3XnUhie41G1YdjV1a",
	"1RsOkhPlWLY7xhNhuGhDjtxPP9BtW4cidMfdBAjrVcsmakaJv8SajRKUQtKWo01QRME+9be0ScWiNfU1",
	"JNBXR6t6q+GOXZqswMiAdW+Y7oXpENveMF20hmzpEmIj822VQLKAEiHiyntDavtFUuFOQEYW1CrL6Zqa",
	"hP9loe72sb0efWdyVvpLGQ+SZ7tFJFpY9zDgsoeIcGPFCmdlD2dlDyWWPQRs3bDIouVrjEUanJG4w5sA",
	"0LrHhzNgil1MIvdjf9c7THCmAlrSNXq8/zO0ApOT0SNt1UOau+PapFZNwow9AN48ov1MH0amPE77s27H",
	"e87tQxl2V6hhxSO0yY7BDx9oTd1GpjtXd0D16xtNrJ0nVWoVfjsrqLCYwtowTGOjtQFvyaBalSvoP1Js",
	"5cAWYTaj+cOvmcQGyrUi8O0ceXQqai5lcMOHiR/FnwxlcOkvPHef+Eh9P/IlgxisjN2rhptZrVmm49q6",
	"QZv5hK22Q/P/v4en8DZmD8k3dWfsNm2jHSLPT6zobu0OQLdVpfCzTRvVdDf0MCSyV4EuJ+AOQfM8KusI",
	"xn9XltwN+uUrmmL4miILxvtCEEKXjDUzwC67jOcxkvarMPKsVXPm3ebAe2rhOaO6OOvEhogRdBLsL/Eb",
	"AwC1lVhPVhbr7ypt+lgqlHc4qlMdY7V8KHEcCOgVq54BN5501E9F/TyGkJ8Xxz/SGO/MNGyk1zcJQ2VC",
	"tfkleqrb6fh40wH6HH9as+HPCUNJ8fBKRu0YkjDjuoBEziYzgAPp3GytNIxarihrBmy4ntepyBp/ibj+",
	"gU9A+v5sefvYNm4yrCHd1WfWbIQwocRUjUAVQqY/xJeOgh5bdJTAJZDY2hT5m7HKAlmBEsBCYh2quzxG",
	"VxvkScdr42kqi/kUEZy6VVvGFlpSQC5J0pFJsinDqwYb0BEHHDXQZ5cGyLq0s3x4Yxz2Y/O3QgzW4CCm",
	"on9Suc7wOPHKpst3ihebC2bzQ+jHPWeuWkkgRpIV7aXBl4GjR5r7+ttZAD+PAyRpcn/tIQutfCI3O4px",
	"VwV5QSnisXh4nGwel1MOyBNRsuzD/eB2RFkdSa7xRInKa97Lm3OzzkjMZNFa34Tch5GjKj3Nio2UZmFK",
	"kJdOrDl8LE3XC+MXtBEC/SSK3AtMz8xatbnZdAUjLVY9M9yLG+7PBEnb7k/S5lcX8siJNKNyF4yL/YDn",
	"A1J/ZQdFpOa77HJfY/ZSN1JDCxEq4czts2HhsvchuaOkaPlxBd/RvMfek0xRmJFoFhkhQ+lvLh03eywm",
	"tt8lxGFGd4V6FoXJrsre06gOUyP1vhCg0d5IhqKCIxAkURRezDVvoeAcLObH3sGZiukjNjR4AZ5P8dyv",
	"Y2Pjy1yZTBKnjuv0TFNV9kkpQtSpkxzo8c9M3h8C3UM9WDijFc3fHvcOxiNXcAokL/y2J0pf7ld/B2gB",
	"6OkkHahyysJk31h8TZHlwLlMQwCrHX4TmtPagEYIKJUSSBqGgT/UCFLporGwoEls2pNZCkqiNlkuLhns",
	"TixYTUG04qTLPP+4rEwNwZxGefJuxlrKFTWDQGONprV4bVY7mA3c8vhGXi6MX9Ruov9BqVo2NtBNy1XT",
	"9lda4UOa+mOyAoutB4wIhmSFFQMBa8IFnncouSciRif4atn9kZjhP2de0Wt3kBzBk6zLgcy4J1US3oH/",
	"LV7os/jUCbhYDpTNqPTjgBN8ylWhxdKARq5bc4auhpEylGjnn4WkzkJSZyGp056uJOmlWiQvadVooLWW",
	"btdTmkG9hBaAr+EILsxe1fx/AxZ4K3GMFizH/cSEFUEL9dWrRkNSxMkJjY1WwzWauu1OrFr2RrWuu3p2",
	"uYHf/kmzYen1RW4AECJi3s4gxTFPROJdQYhB7W9pYOFL8+G9HjNPybL/EkRlFClM2XJyXgv72OX2MBkr",
	"fgqEZs2ybVRz58xmy8UTvgq573IJGglPkBScULNsi5Pj87Oy0zQtoWnJ+APKQRHpC0BqEDNR1A9OZ+op",
	"YmcVT0S7BmeSnNE7SG+QPrAJUeFAZre5bjKHFIwvKAMFIY0nR4ssyRrsgffdI2FWMPf2QtDB2Pn+DSFn",
	"gOeJjlBWxX/YasffwWvS9Q6olxzWo0ORV8FSK37pM68pVy8QbAqRDXTXG5ZuOhOEmarcWlZr2MjIwQ1H",
	"9P+omQHKhBZjex12mXxArzO6EE6Ej15jozAoFhZ54DomDqydGSAQ/z3TbH58z3AGCwmsGlNZpPI3rBDx",
	"EfC/IXwgsXHKWKGR2sNinh0xAvg5k9uW8mfdJ3LxYDg1OFlPw18G9g49VeG+9Ic3k0zMuAL7nXJuQEJh",
	"+PfIi4YE/x4ZVXn0ngqLAx5muDjRrvDi4o4MAT7/Hvd3BkrhsQwcv6Kb61XH1V1I7he0R3K0aDb4/pz3",
	"M9WleJC3Xu/8P5/+7f91dy9p3j9A1W1r/rfQCB+LpC6JPbwmQWrvyH+MfUV8wrUvKE33ue2cm/0yQuMX",
	"n5nUX34V9Cfr4CVKBg7fg4U7UEAtXEPuZd1cXwoWYoCHRRjIyYS8CzZDh0JCc1F/bLRQMyeY36iLs2K5",
	"3WFReKlo9tHadHIH8pq2CCG/ehOuS4/gbryl0kZweCg0BSmFz6gqanqjFvikWYvVeWBsTBwelzXTE85z",
	"O6A1espfBkjf/jfg7b2FPBXM8G0ZWxOLTG/UZjGtA0aVxmNkdnVJJFjZqbTI/I8p45dVi5ydfbLzcKuR",
	"Zu4ISNnRC8YeuXV8SzMRfiEdcQKMeeJvBijZPXKRFRIqq2EI4Mkx6y4je6NQF+Ur4dwGb/6EYy0ip9Vw",
	"c4R7CJS4xhozvYRbx/0wn6wnLBhJvlEu7uk6AINnvAxnJLhfy1WZGV76BVf13UylT2rBH/OwYp1LGKVX",
	"LZua4iPJ0Y64AWEh0KCPoWrg/F1QT0Tmm4TH8l16FchA0Ui+GeuVni2rZGoyvF2DkGz2UKzXAdyhV2nv",
	"n055f4rLKcm5SBvxQnBdpb5Fi924Rocp2UcsQ+wI10nZRCSqt2gI0t1solzdmxIbTEFtLmktJe3WK48+",
	"XkPux4ygZaBnsP1wxMGUskbS/iXrNI9TCLGf/SrNDO6TabKw9EazYW3i0z04nn5NKrHIbQD+Qs3PATVD",
	"YWhxtP44Oj7JE8jNsUkMm5NlrJKBiy1zxdLtumGuVV10z83FxeGzROcfwMAE/fpAaqSqww/zwbuWgYy8",
	"Vmn4/JLVsmtoWJ3GI3QneXXz0uVqKxbqlMcpivFOBoa2UWMAhgaFtSQ6g3hbzAeHiyWS5UQ6XzHTFHq9",
	"df0/QrxVJbcXKbnDkNrCWPlldt9rcELEer/zHIbkL4EfM5wlp2XfRZu5Wq2SSPp+2FpOUoikUgJLZLjB",
	"J+x+4iB7Zg2SYAfcCZNMiUT80q5xvB7L1SuypsdJX2QhuM/Lz+Ksp+T7iip29yLsipBvtPgl/pJOkk9K",
	"YvVCnXf0u4gMP/j2lMFQSm3zPVvGkJXihYuS635hQ9rj7xr798WSGSR/9H49mvXSyhbjfgUJCe0ATyLA",
	"epmI1RGQKvqcOTEEqSWeE5PzVPFz7bOEQ0rRwA+adNSSkmtiOzVC36QI05QdQC2fpwucx4ScnFhGZ/Es",
	"mNQcmE+nyz1sgzPEImQn3r6epdYc+9Sa2HlwnZRMmyxdJv0tf8t/5L0Fss41jLvIRI5T0VDdOa/ICHOd",
	"Wd3Vk65TI3T8l/faf8Tws4PLMdZJ9g7S64CeQlvJEoOvGlh8SS1lA5igsfX1scoYMlsbeBXpH/jR1tht",
	"Dr8B/12RIUMVPMGV+9JuuG5aK1x5J16hJtbfoVUVb4Q9kpA/SDFCN1upV8WsDWKoqkt7hamAWujGPhFQ",
	"0LrBxWf7ON0oCxtSqqbNd0CLCY6abtZQEh73M3+bAjG1BcVNU8457R7gNqmgSnYUtRBAwkCN2IElNUko",
	"Vx6PP4u36ZHFi1mdwbqP2uZkhGCvKs4Ar+Gq/rAfzs/OYsV4vI5c3Wjkg0x6RXxyivRMxJMMj5wEI0mS",
	"IZ8EQ5dFwvPsxmeWUlUWox9DFJxwlon25p9DS4AZQ9i1AM6PnIsKb37ua5HUP9LLRWgw5z862TdJOeJ/",
	"ZbJskVPGcsmqq5ZdTekbTlqAvCSFflBHSCvEFQ3vzrEknUh32vM0kRHyhsR2NoRBXsUBX5KypiPNiqQK",
	"iyU1XbXsJTzL03mAI7NUarUsAfysqVTH66Rmonn8+IAuHAuog2QKpwIKSc3/VaORjS7BaSbCghWAJxe7",
	"j099pF1pGMh0r+h2fbZFeEk6KNeX4xVpr4kHxubrAzBgv2INRN5irs6QPDo9Pj2pzdQA0HbGtpHjynp1",
	"RAJ8LwOIXI4ZacPRfe8NaXTi7/jf+V9DKy6W/u3tlx3oG3ROZFSBIN1ubFZt1NQ380H8sFpYsXlej2S2",
	"w80xk+6R6oFd7Zz3f70n3vMJ7zn+jyzIAVbMguW4H2PyFoG6UQbYRVKEzh2DRMiJjJq5YjzSe5014ciw",
	"N6PTBhl5539qaQndV6w6XEo4fOa1Ion8KKoBYj0/gmCZlpboHfFI1dnX+XO+U8Z+H8Z2ZprN3+jOTctd",
	"ahmuvtJAS67utpwiBLSZRcc6F3cIWVDsGwSmZWIxaF3h7adQ/UG4UyGT0z1bam3Iej9BSVIOZkmgmQTW",
	"ubo1f9c7TKH4w5Diy7q5/i+WvW6Ya7P6JjR5IEpWomqobxQUcgj4QV5bA8lKMNx6UMrA2nF8RdrvdWC7",
	"DrHgnPzo0uRkdXrq0uQkdSmeen8+X7IGKk+yF1RJ9Xw1TbLYV+h1wQ7va94/vL97zxNVTd2hsdZROxdF",
	"lFNA/FCVEzdq9nYmyv0aZQddJUllHq0cnFrw6NxzkW3qjSpc1cJv8vYLETM8YvmwtJ/vK/AKXhLQyeQW",
	"Bkv6XfSJg+yPKW1YdMKhG6lFp6RqKPlJ0pEzXfFQ/H+xRdTxSOKWc0+HqBL/MY87wTWH7skZrAPu8J/g",
	"LVEWS9bSH8UsQNlqz63oZkxXz12euUntr7CnNViGA2ia0t9BKyYfaEvHzfL1K7sWS9Kwc3T0k6tmIzMY",
	"pq6NDV2uwi2kHdOrXwfoFKUNfky9omSyPxif0q7To/Qpso3VTZVb8QONFnIbEsaPVDAcxLbPuIQf4P2j",
	"tCzojoPqlzdvLV43zPX+qYkQQtTarUX+rn2fReCoBOzhpdwBzwmepd2HS4/A5RJ3BcWwSc00aI3oVG2S",
	"qp+nbEjaMq/jHfk7FOjoiKlOvo8elDkVaZ14DblzlGjSM5pWF5yQRDwZ7XkkaCkLe+w7LpYxy7IvWAfL",
	"6QXOLsB1OXdyBsspcBgvTISciw70j4G2qpq/xfXZJemeYbIinpe/xYki71Wgat5E8Z5hxh3Ne+E99/4m",
	"scwWyFyoki7VJitkYcXIGZqBJYx80h2xVH4rO2CYwt2YXxMYs9g5tNGqYepmzTDXqoa5avVVdKvO2wAk",
	"lH0wBI4gtWVLyKkMczsSZc2+LCFpMZzBHJ7A6Nyh9CcitC6i3y+5qDlwvR0bVp3lVzyZKcsGv5PJTaUe",
	"k2Kn3KlZNj7haRZypjzeSJdCArq1F9gQXDYSJu4tIHLxKN0sQIs/7/IdbwPfD++RStleQ+4SmY3Kdn5n",
	"kvYHKTWENc6YNR+7CASWHiB7vLOp9H0dwR52xfowGbLi5A8ebozABBcBHCPJ+4OAva8M1daftWrOMJDH",
	"lOt1hpJ/Bkl2Bkl2giDJSkcgw0K4EAaZQrvAvUI+N5S7GGirYInjgStpiY48aDvTbNrWXVSnkQ1y93FS",
	"orZS4tUtpMLFTFutY9UTSuSBRMLLd/BKZMACBlk6MuxJwYFVmRlDAoMtHgo6g5E9s9nObLYzm+2E2GwF",
	"UWMttzlhI9feTNQu9L18LDnYf7lqlDX7nnebizBUMSEKzw5cksJACQ7wUxLIUc5aWjnPgsUgXItISstE",
	"86tKDRmXmZXkH0YwBMa+vJ1BysaYACydVDYoG9AgSD/kMBS4rMligAcXxi8wkU1FZHahDX08vUPScyIu",
	"vdsx6U0l6OtwWbsZdMp7mlpqP/P2/P9FupEUhSyIS6Kihz6QOZiqr739AAe1qUOPcmcCknuqOilFrBpG",
	"Yml2FFryR+9H768T3mP8H41Y3D2C/AuZtv5X/mNiru7RgO6/QZfVLkGjid/0U6IoHCmQNGcUQnJUvIpe",
	"1Q/2ml41dIIky4anKl/bbnQfpIsdq0b2/g9EuQWEC5b79o3m/af3F6j5IYR0SVKhv+tvaxfjcEJthXnK",
	"tf/aV9BVCQqav4Lh/G/JqP/p/aVCjk6XdgAJr+n24eucE/oP2YRIfxVoBawRA6b4PNTE/kcCsf4OIY3I",
	"KGbEsJvhLqNfLP6Ghg674XessT2boNcDJ5g2tCNpoD1OLun1uoFZT2/MmXXjrlFv6Y3lzSaCdIcDirH8",
	"gCYzQu+21wRAlFKnIMD/JjbNHzTWDVA56LgLIwMyuL+DWX8Py2dScs7nDnWJHuH6qPm7l7QL01jq/ug9",
	"r2jvX9T++eCJ5rW9V6RbOt7/ijY1/SF8DvywFaLjHMFEwbrGhkJFm37vfdkPAWYMS9U99mSXAFCLW4Rl",
	"e3qmcldyshUJqMnaGurXqbtEpczN1sYKsjP4TYEo4QRJ2mjT72uXdXP98ubcim5i5axWgY/JWSHuqUF/",
	"LlOGKSOS4stl/d6Cvols9Xh/jbk9JKAC4ZMA5qwL0vVrrms7LzZLBl3OrBnjskrQ2885jLauXHfXoIdB",
	"dWWz2rxjmahqAhfkUeNC3wKugIMuU0C6vxMYdFhGQvOpGP4gpom0Vbi8uYAJIlxZCIVQ8a5haHH12Alq",
	"vMyV1QIItbexzrNFBY3AhJHx0k7jhQtM2nCrkU/W5B3xPdqfg518YSMktRHhYgtLHY+KpC5+ydKgvwOW",
	"RxhkFAEveDhskg5CxaT/iNR9McQRrP68jr/rvZZk3cMhKeOQj+JwFz7USafo7NReuKjBUPPAKZad5Cmz",
	"ngNgbmaSieViAOba2uwnkdywVIMCGvpNPyoaIldamGUfJI6S4xtWGnT5xsLQNzjog0CCCB0ubpBXt5OJ",
	"sToV6uj2r+bTXzsUoZCFjAQ58UPCHpDwPb8D2SwAIbQD5clZhYgyJjoYXkmVCmDKs9AIFUnzywsZRBEm",
	"4YhAlBMPiXxMYhA94nmlOErva8u2bjpYCoTlnYoI50+04Pk14AMF92zheS9Q5zk9Pn1Bm19eIFyUO6qZ",
	"9SqqE24YyIuShWOKDCokYPJJVE6Q0ohliTI0sscMDFtawyVMVQrDTigOhAj10PsQj7FXDUEWRsdMtpPC",
	"9Usrb8goxGLbFDuKZ2LnTOz0d2yLCaBVy65uWCtGAw3ByRLO4lXLvkEG7luUBK8aonUVHzpBojwLNret",
	"Xi+VrdM+EzKnW8gkE3XxgkZ3BNZi4Upae9Xgzi3o8dz1jryX6dO/eFEY6YrVuGuk3ZxL29dFNqKj0Vel",
	"jf8e8N8i+ldUc1H98ubMQmorWYD2hgth1tVH8557LzTve4qGBO4HFYnDledZhWRGwW0j3UX9uOIxKT4k",
	"XxsIL93VTn3rUHRBBioS1MJwdqSQyQrZvK/5srqReNvTU9psa4X0s6bLK0nhEyrit/MqvdIDi4qL9WN9",
	"S5BC2vvaDct07zQ26SZcNzYMN0cyE2m3sUtQxPDqcdlNR3wmGLCO/yiVoA+0eRMtGxuoGEFROyWRnFRi",
	"PtRuWu7HptVau3PDMtHmnEndTEfORwFWH+25TgBPSR4/sRsIGcH16n7oDsiMvB7f8zatT9j0+IWPtBnT",
	"NVZtvVX3XiQ1042pvDbr1ARfhKm6uTpdTY9fnNRmblzPN/bMjeuqwfpEWR2OVsyl5nPFh2JTKDcABBSV",
	"Ev+Rv2nwCrpw9IfXgiWp0QjvpJ2U9wLlpG8i+7Jhzs3dVPhEkKmhTWgsV0PZDa4T2utB+01QbF4vjZz3",
	"GTmXkYlWjZqh25u5iJJqxr5I+kBYIbrDCmpeEByJwazNh5K1yUhO+avykTazAfmazk3LnUU1Y0NvKKkI",
	"Gr1zAwaiPfOQU1PMfaSAWMuWdd0y1xTDPmFQTiTyIUisPbI9WFlr0+9NauDDHsKZgXYGx8BsnA62e6Fl",
	"Ny0H5an0IfZZJAMwLwEfSfgtHxHFzNRJmQzQzfXLRi3D0HskbY2zV4tRMaVdsy3HAeZy/uUOMj++h2qt",
	"hO1+BtVobWhv+QvD+iNwO3u0N1E0eUHq2Xdj+1Sq+TGcKLBoZQw1CMxr5P5iwNI3Dc/tzxUBLrZ6cf7r",
	"lWSG8MNDxvIeSSsNYGsZCcfBQz/zr/L6V2cx4+MRMy4sNAvJ8XXdaRq/t/t3FUUqibl86PX8r/1v/O+0",
	"W4vJUv13mAz4UX8ynb5n6BI9GDfRSxRSoA5hAV9hz38LPoNwUghcB41li8jq4nuUeHLoTJeNDWS1XJlP",
	"wFCRwEQnlUVvoJJohxRJh7U01LDriI0bO16b4rkfxsus9rUrukkvUj8xXaNBRdBz70UWspVC7Tkx2qLw",
	"nF3RtoOKqbcUbT2obwpanj33/h6Um++TgM9RqFQojhStU8pMrUowZqR4J5BMPdYTJAhZ8uKyHMJDabqw",
	"lIfkg/Bik+dnKl3LoExAmkxwqNvQH3Ob7e6ZhfEuRXAL67RcStdBjdWqa+ums5qcAV1EU8j16xJqrC6z",
	"EQtrV/4tw9Ot4qgj0axl5xJnIu3EhC+HETOb1j5xkH25YdXWpQJDOi2KjEz6Qx3wSvu0ifWShWrKYNOM",
	"8+abyJ6VAkYLYJpwk0V7OL1iyBBH1Kmhdt8+bfXUb91ZMZmZTX7fMRzXIr1p5K0Df4St7kE5dcQz3ffe",
	"yDpN0/PyG/rmvB3IWMbdNcReQRIB5gzzsmFm6SvGvYFLonPgVryP5+dXVx3kDrbzYHzyieohZXP6YLrU",
	"bc/GXr+3qw5yHMMyqy6yNwxTJ6QrDYRnIDfwieZCEnv+tzRcodEkxweQQggtpL+FXoBtrkwvMZoS49hb",
	"i0uEwmWOwAL2hOw9w7Ao5OMmMM3PXMi0zXpIhB7uAW0FybV9z2ZQ9Aa9df2J0WFwVuZD4WKCU9pNEn+y",
	"y9qHROBf+LH9naBnIPvVoXZrUfN+AiNhX5qKeGsRVq0Yq8Ojw+FuOtQIjORU0ItiO5Foi1Ar5MYVSUDn",
	"370X3gtsXIVNbrwOIGUE8EzBwWVcTuk8kDHwd4mUgNl906I2Ymp4YSsMMATRDc5+9A4owLk2mWXUK7ot",
	"Mfd+jtXcHxEs9WBrSeRNErtPxj6EIOdSy2kis662xRkbCDGddnA7ystMCiAI1uIW/i0NMXS8A39Hu7VY",
	"JWvCXvcWXtYjqYHb7FJcjAmltVaZv4tsMHFk5Is1XJBKEAmjvRVhELiAWlmdf0uUZNnkrMsZb5kNXNFL",
	"kRq4vFHYj3nLv2fJ1W0X+x19GKgfm/U+38Acwj5egU9uP8/Ps9Ve3mz2M5UreCLIbuq2u9nHa24YJskd",
	"6ucd+r2+33HCnBd+6DweTPT0lefBxM91fiEycZ/7a272y1wN97ejVVoBfBvWUHHcV6Dd30kRQIWafMs3",
	"Cr9qmZ/fKLgFE5HIMJJit5Ib0xffqP45asJGNWQ0c+HxegcAHQWqXCBX0KspfLRIxz1FrESnlNJrWFg6",
	"f1e6/eUyWOb9SmQnG60i29Yb1aZtrdn6xoTuurax0kqLrwhYmO2IO9Pz9jS4FYLIv7/DYLoIvh6IT0xK",
	"jJcWKTELhJYZjpQC7iV7G/eaFFczCStVMp1o/2gh/jFimMXk7suErqkR0NX2OkydAhwHB+J/cTjkKC9E",
	"ojVe/RzX70VuoXjVIfMHSYHUn35JUzkO/Uf8if1b7Jl2CD0fPOO1VYfZMlcs3a4b5lrCWSbIo5iZD6JO",
	"L6R2HQFHP/Besfy1CDqe7Mos63QlsLIRITAfTiHToX0WpTd2Tl/T+X5dtMf72Qk9BSdUwiejOKFN21ql",
	"OeKZDTVy5f4gwIJRI9rS4/xvEEnbH86BXaAzGqChxo24mpYk/jxcAf+b7HsckxpCCPdMaryjUkN2GjkG",
	"G5GaT8XtiyasSZMEc9GeKAICjL7B9mhlo4aoeupSEQ5Z78TLgGgP2AgGXub5ldy6Bfu5217X6xRkqaLH",
	"wdWd9ZyglfSqK7ijxtL0qyCD8RUAGDzwfoE8tTca0x5g0xPcY0i/9l4SrHgq7GSRESAtbzAEP0V4+lYL",
	"2ZuZgfDwc8ubTZT/KZJOWJ9ZdZFd+OnLaNWyCwy+oK8VfGrJ+IPw5IDBPGE3rxuOm1KVFrlHBebipIU8",
	"ckHJnsDv/nij6W6WjZMp5Xn+2P3ktYkPSEBK9r1X3i/4HPDHbOI+/k/OmDUcp21RAKgM1oAydq0HcWnp",
	"wSoUs14G8ocHAIvHo6XbKXDd/BKpMR3FJRo1VxXc2Txch+ky/pBURPsD5Kp3aAFaG67y5xeuVLQmpN7+",
	"Dm1qRl0Lmqf7XzHSg+hehVyT97w9lhOBVZK3/5lJ79sBOFNIS6VWpvaFXq9f0e36ufNfYC9qafZ32vId",
	"vYGc8c/MGOPC7ewymZEiQawUvqNDIBhvEf2ecBweybBRfeySa7fQILNtIuMr0I6F8OpLVrbnP6qQ4tlf",
	"iM0Fi+0/Iia9/w1e4dFZXpF8tj7hI8NJS2LoEl4LmUs8QCylIfC8oP4SyvK3I8cIXIWkw/QPuJv6FdKd",
	"oAkWC/XBBehjaGnh79D8n553SBM+oIV5z9/Wlpd+A6f9rf8dIDU9BDTi4HBD8hOg/3ldTJ6/IwoPSrX/",
	"SDvHp7acv/SZ6W/Rt9B2n+RadpfmU52DhcZmyITDEnCw/GttoPpEHTWQi+rnJfi59CaFjlolWKg0Tx3u",
	"5SqfmdKHhMybc7MLMzcn0L2mYW/iUdoUNgxQw8AW2CP5gP6udmX+KlvVT+eWZrRzdXTXqCFtxTDrEy0T",
	"/6eiOS5qVlvN8zIpctNyjdXNTw3bbekNLHuu3NHNNRRXhdE7ctWlI6aQJTl1sRTDm3iOlc6Ss0dytDj+",
	"9x8FDUoh1cff4gFp/d3zY5UxA497B+l1KK0x9Q00dmnsXrVm2TZqwISqRn0sKpkq3LHd0O9dR+YaPkvv",
	"X6yMbRgm+3OqMuZuNvELHdc2zDV8GvNM+Vual8RywM5NxVuiJMzBcJwWsnOQPzUpkj8Zp//2YLSBgl9K",
	"1Qvonr7RbJAnrHWgLRR26ZnCY5Wxu3qjBYIJkdLOOl6aybEK+fsGchx9DX80v06o7nPuCp30s0Sy0RgP",
	"loe7AKF3bpIgyfW8g0qYdFfTG40VvbYeoB3H7gPPZ1dc/HoaJJfzt2zTw4X9q7cf3MIGnSboKeXaq/92",
	"af6maoWn4itMxyOP4TUyae9EcfQ5OAF4TRfR6lxdEmdTDDktGRLe9XlNt+uf22j1c6OumZarrcKoA9xu",
	"tXLXzk2RTT6KNNvBq/LftGn40khbAtjx94ZlqvwgNrTxd0n5dThFkPMP6HT6sl8U5wQu/iP6HlRKqrYn",
	"do32zz/+oBHGOl/cxrmP+Wiu/uWEYa5aaVHRAyz0oWMRaLjA3qKWlvbFGnLxDs+Zq9YXPHR307ZqyHEM",
	"c626Yhv1NaRNkDr3c0tGYx1r7hBzVLDs9v2v/ccs59jraN5j70lFKHbjkTk78MM/MRuLY9KKRigRkWEP",
	"/G8hUZjOCL9EZkRcC2eV24cO/1mFdwzLncaDAXMC1ao7gOBmDBwLyN0Xii16vOnGXVZw7HhsHIwh3uYk",
	"X5+I1zvRBH26cELdO0RGsZ1L0o0wa+57h/5OP1Ln38O0dklFhTTw8Ktyv0uQL+BMTbSadepTyeUMR7WQ",
	"N5FSDhJzOvzv1DOJB8hCF9xFJ+aEC1QrtfZPAFS8A2itb4TbD8ErOyVhgpSqh+xswgu4vLzfcpA9Qbvm",
	"OhP36b/y5i5z9TWKTAQZK3/iBDX8+e9SGMT6sDiYIzblQjJ1KZSlZYFZIdp2WNe1IXQWmlb4j0oAm75H",
	"oKqxLO7RBNVfGMxGJ+ivrE1PTnoHXreiUb/tj8R7Jo3GIb+TjtujYMlcj/QeFvbApvveIbaB/jdmAm+P",
	"uERhTT31jKB1SFmVQfn4LDgATwMrVCjv4zm/BtUoSlbnr3xgBCqdoGSOvF14d480dsp2AjBHkWqYAbMt",
	"DJJyScFJH5Zd5L3yd+Dmt83yT4Ykck/bMaC5PT1/C0t6+r4KCXY+8Hf9P8Fl/6Vz/Xb2KZVX85yiOmpa",
	"juEmHCTZVY5YmIoN+V/hV10Cwe8/zEYr+KWa9xQ8LPBH8WZ1Sdfc15hsPo9KoYZm2QwGfBTZOJkL8Hth",
	"dl/0fo5brsS9HJG1VP6FZNYJc8z7hH/KfyRj2on79F+JRo+MgcVLUn/H/052t/udRupVefKZjUfau8lv",
	"wimrkDvm3OaR8LQD9pEz8DtxYVB1KDYbe7N+57ydK65i+/SwdpbJZmPrhqWbuZKmlPf6B7RNUZeYALlM",
	"++tARdrF1X/BJdRB1K9S3M3M1Gqo6Vav6+ZaS19DifczdbSqtxru2KWx9fWxyhgyWxt44egf+NHW2O0K",
	"i8aPXcJ/K+5tBnRcYIWy5xvxm9HzOsR6gPuHDo0GbnsdLIKoyhdS29re4QiTGhNjImW7CGVwc2j6hM9E",
	"D1lNT+z/Hg8Nxd35XY02aYudfAVpkmgvtimcTyBOhf95ndB17M4dnC52+G4P96BFVydVLz0sZbNO+IEz",
	"Lfdzo45M11g1UH2skiFyWx6jC7WtfJkHfwa5AhtV/hieLx9VVvgOcFG0BZ8dUjdnB5bsraIYIHYMab3K",
	"LKSjFClpFV4Ajymu6zNnAypiTGKDmKR7UnENgjxttgbJHRPHJ7VP9YZRhyVSNOjMUf4iuO8inf0pkJ8T",
	"91nGHom8CYTYd+Vidxbd1ci3Y5Wxlt3AotZ1m86liQmCJ1Kto7vjf9A3dHPcRvUNvW5bK5Y7XrM2JvSm",
	"MXF3aiyeCbPk6mso+bUpr7wdTOg+k/jei3DGJCoEI7Nvv4+XBIG8C38hLgz/zRO4md0h98j0Si78VhVr",
	"4H/zzNuDCk+RJlFV89+8CDIpwuSvrkitUD7Pf6PMYRV/lBhnF2cv2u3cd1eNBtKutXS7LjyRuWTiy9tf",
	"/v8AAAD//6c4xRBv5QMA",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
