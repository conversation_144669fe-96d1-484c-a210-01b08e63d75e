// Package openapi provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.5.0 DO NOT EDIT.
package openapi

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/go-chi/chi/v5"
	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

const (
	BearerTokenAuthScopes = "BearerTokenAuth.Scopes"
)

// Defines values for DocumentType.
const (
	PersonalDataAgreementSMEIP DocumentType = "personalDataAgreementSMEIP"
)

// Defines values for AcceptLanguage.
const (
	AcceptLanguageEn AcceptLanguage = "en"
	AcceptLanguageKk AcceptLanguage = "kk"
	AcceptLanguageRu AcceptLanguage = "ru"
)

// Defines values for ConfirmSignDocumentByIDWebParamsAcceptLanguage.
const (
	ConfirmSignDocumentByIDWebParamsAcceptLanguageEn ConfirmSignDocumentByIDWebParamsAcceptLanguage = "en"
	ConfirmSignDocumentByIDWebParamsAcceptLanguageKk ConfirmSignDocumentByIDWebParamsAcceptLanguage = "kk"
	ConfirmSignDocumentByIDWebParamsAcceptLanguageRu ConfirmSignDocumentByIDWebParamsAcceptLanguage = "ru"
)

// Defines values for SignDocumentByIDWebParamsAcceptLanguage.
const (
	En SignDocumentByIDWebParamsAcceptLanguage = "en"
	Kk SignDocumentByIDWebParamsAcceptLanguage = "kk"
	Ru SignDocumentByIDWebParamsAcceptLanguage = "ru"
)

// APIError Общий формат ошибки API. Возвращает цифровой код
type APIError struct {
	Error string `json:"error"`

	// Fields Объект с описанием деталей ошибок
	Fields *map[string]string `json:"fields,omitempty"`
}

// Document defines model for Document.
type Document struct {
	// ID Идентификатор документа
	ID openapi_types.UUID `json:"ID"`

	// FileLink Ссылка на физический файл
	FileLink string `json:"fileLink"`

	// Signed Был ли документ подписан пользователем
	Signed bool `json:"signed"`

	// Title Название документа
	Title string `json:"title"`

	// Type Тип (шаблон) документа
	Type DocumentType `json:"type"`

	// Version Версия документа, которая соотносится с типом документа (версия шаблона)
	Version int `json:"version"`
}

// DocumentType Тип (шаблон) документа
type DocumentType string

// Health defines model for Health.
type Health struct {
	// AltScoreBridge Статус сервиса altScoreBridge
	AltScoreBridge bool `json:"altScoreBridge"`

	// AmlBridge Статус сервиса amlBridge
	AmlBridge bool `json:"amlBridge"`

	// Antifraud Статус сервиса antifraud
	Antifraud bool `json:"antifraud"`

	// ApBridge Статус сервиса apBridge
	ApBridge bool `json:"apBridge"`

	// BalanceUpdater Статус сервиса balanceUpdater
	BalanceUpdater bool `json:"balanceUpdater"`

	// BitrixBridge Статус сервиса bitrixBridge
	BitrixBridge bool `json:"bitrixBridge"`

	// BsasBridge Статус сервиса bsasBridge
	BsasBridge bool `json:"bsasBridge"`

	// BtsBridge Статус сервиса btsBridge
	BtsBridge bool `json:"btsBridge"`

	// CardsAccounts Статус сервиса cardsAccounts
	CardsAccounts bool `json:"cardsAccounts"`

	// Collection Статус сервиса collection
	Collection bool `json:"collection"`

	// ColvirBridge Статус сервиса colvirBridge
	ColvirBridge bool `json:"colvirBridge"`

	// Crm Статус сервиса crm
	Crm bool `json:"crm"`

	// Deposits Статус сервиса deposits
	Deposits bool `json:"deposits"`

	// Dictionary Статус сервиса dictionary
	Dictionary bool `json:"dictionary"`

	// Documents Статус сервиса documents
	Documents bool `json:"documents"`

	// FileGuard Статус сервиса fileGuard
	FileGuard bool `json:"fileGuard"`

	// ForeignActivity Статус сервиса foreignActivity
	ForeignActivity bool `json:"foreignActivity"`

	// JiraBridge Статус сервиса jiraBridge
	JiraBridge bool `json:"jiraBridge"`

	// JuicyscoreBridge Статус сервиса juicyscoreBridge
	JuicyscoreBridge bool `json:"juicyscoreBridge"`

	// KaspiBridge Статус сервиса kaspiBridge
	KaspiBridge bool `json:"kaspiBridge"`

	// KeycloakProxy Статус сервиса keycloakProxy
	KeycloakProxy bool `json:"keycloakProxy"`

	// KgdBridge Статус сервиса kgdBridge
	KgdBridge bool `json:"kgdBridge"`

	// Liveness Статус сервиса liveness
	Liveness bool `json:"liveness"`

	// Loans Статус сервиса loans
	Loans bool `json:"loans"`

	// Notifications Статус сервиса notifications
	Notifications bool `json:"notifications"`

	// Otp Статус сервиса otp
	Otp bool `json:"otp"`

	// Payments Статус сервиса payments
	Payments bool `json:"payments"`

	// PaymentsSme Статус сервиса paymentsSme
	PaymentsSme bool `json:"paymentsSme"`

	// PkbBridge Статус сервиса pkbBridge
	PkbBridge bool `json:"pkbBridge"`

	// ProcessingBridge Статус сервиса processingBridge
	ProcessingBridge bool `json:"processingBridge"`

	// QazpostBridge Статус сервиса qazpostBridge
	QazpostBridge bool `json:"qazpostBridge"`

	// Referral Статус сервиса referral
	Referral bool `json:"referral"`

	// Scoring Статус сервиса scoring
	Scoring bool `json:"scoring"`

	// SeonBridge Статус сервиса seonBridge
	SeonBridge bool `json:"seonBridge"`

	// SmsBridge Статус сервиса smsBridge
	SmsBridge bool `json:"smsBridge"`

	// SprBridge Статус сервиса sprBridge
	SprBridge bool `json:"sprBridge"`

	// TaskManager Статус сервиса taskManager
	TaskManager bool `json:"taskManager"`

	// Tokenize Статус сервиса tokenize
	Tokenize bool `json:"tokenize"`

	// Users Статус сервиса users
	Users bool `json:"users"`
}

// OtpResponse defines model for OtpResponse.
type OtpResponse struct {
	// AttemptID Идентификатор попытки для проверки кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// RetryTime Количество секунд до следующей отправки
	RetryTime int `json:"retryTime"`
}

// AcceptLanguage defines model for AcceptLanguage.
type AcceptLanguage string

// DocumentIDPathParam defines model for DocumentIDPathParam.
type DocumentIDPathParam = openapi_types.UUID

// SignConfirmWebBody defines model for SignConfirmWebBody.
type SignConfirmWebBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код подтверждения
	Code string `json:"code"`

	// PhoneNumber Номер телефона
	PhoneNumber string `json:"phoneNumber"`
}

// SignWebBody defines model for SignWebBody.
type SignWebBody struct {
	// PhoneNumber Номер телефона
	PhoneNumber string `json:"phoneNumber"`
}

// ConfirmSignDocumentByIDWebJSONBody defines parameters for ConfirmSignDocumentByIDWeb.
type ConfirmSignDocumentByIDWebJSONBody struct {
	// AttemptID Идентификатор попытки для ввода кода
	AttemptID openapi_types.UUID `json:"attemptID"`

	// Code Код подтверждения
	Code string `json:"code"`

	// PhoneNumber Номер телефона
	PhoneNumber string `json:"phoneNumber"`
}

// ConfirmSignDocumentByIDWebParams defines parameters for ConfirmSignDocumentByIDWeb.
type ConfirmSignDocumentByIDWebParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *ConfirmSignDocumentByIDWebParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// ConfirmSignDocumentByIDWebParamsAcceptLanguage defines parameters for ConfirmSignDocumentByIDWeb.
type ConfirmSignDocumentByIDWebParamsAcceptLanguage string

// SignDocumentByIDWebJSONBody defines parameters for SignDocumentByIDWeb.
type SignDocumentByIDWebJSONBody struct {
	// PhoneNumber Номер телефона
	PhoneNumber string `json:"phoneNumber"`
}

// SignDocumentByIDWebParams defines parameters for SignDocumentByIDWeb.
type SignDocumentByIDWebParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *SignDocumentByIDWebParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// SignDocumentByIDWebParamsAcceptLanguage defines parameters for SignDocumentByIDWeb.
type SignDocumentByIDWebParamsAcceptLanguage string

// ConfirmSignDocumentByIDWebJSONRequestBody defines body for ConfirmSignDocumentByIDWeb for application/json ContentType.
type ConfirmSignDocumentByIDWebJSONRequestBody ConfirmSignDocumentByIDWebJSONBody

// SignDocumentByIDWebJSONRequestBody defines body for SignDocumentByIDWeb for application/json ContentType.
type SignDocumentByIDWebJSONRequestBody SignDocumentByIDWebJSONBody

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Подтверждение подписи документа через ОТП
	// (POST /documents/{docID}/sign-confirm-web)
	ConfirmSignDocumentByIDWeb(w http.ResponseWriter, r *http.Request, docID DocumentIDPathParam, params ConfirmSignDocumentByIDWebParams)
	// Подпись сгенерированного ранее документа
	// (POST /documents/{docID}/sign-web)
	SignDocumentByIDWeb(w http.ResponseWriter, r *http.Request, docID DocumentIDPathParam, params SignDocumentByIDWebParams)
	// Проверка на работоспособность
	// (GET /health)
	Health(w http.ResponseWriter, r *http.Request)
}

// Unimplemented server implementation that returns http.StatusNotImplemented for each endpoint.

type Unimplemented struct{}

// Подтверждение подписи документа через ОТП
// (POST /documents/{docID}/sign-confirm-web)
func (_ Unimplemented) ConfirmSignDocumentByIDWeb(w http.ResponseWriter, r *http.Request, docID DocumentIDPathParam, params ConfirmSignDocumentByIDWebParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Подпись сгенерированного ранее документа
// (POST /documents/{docID}/sign-web)
func (_ Unimplemented) SignDocumentByIDWeb(w http.ResponseWriter, r *http.Request, docID DocumentIDPathParam, params SignDocumentByIDWebParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Проверка на работоспособность
// (GET /health)
func (_ Unimplemented) Health(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandlerFunc   func(w http.ResponseWriter, r *http.Request, err error)
}

type MiddlewareFunc func(http.Handler) http.Handler

// ConfirmSignDocumentByIDWeb operation middleware
func (siw *ServerInterfaceWrapper) ConfirmSignDocumentByIDWeb(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "docID" -------------
	var docID DocumentIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "docID", chi.URLParam(r, "docID"), &docID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "docID", Err: err})
		return
	}

	// Parameter object where we will unmarshal all parameters from the context
	var params ConfirmSignDocumentByIDWebParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage ConfirmSignDocumentByIDWebParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ConfirmSignDocumentByIDWeb(w, r, docID, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// SignDocumentByIDWeb operation middleware
func (siw *ServerInterfaceWrapper) SignDocumentByIDWeb(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "docID" -------------
	var docID DocumentIDPathParam

	err = runtime.BindStyledParameterWithOptions("simple", "docID", chi.URLParam(r, "docID"), &docID, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "docID", Err: err})
		return
	}

	// Parameter object where we will unmarshal all parameters from the context
	var params SignDocumentByIDWebParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage SignDocumentByIDWebParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SignDocumentByIDWeb(w, r, docID, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// Health operation middleware
func (siw *ServerInterfaceWrapper) Health(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.Health(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

type UnescapedCookieParamError struct {
	ParamName string
	Err       error
}

func (e *UnescapedCookieParamError) Error() string {
	return fmt.Sprintf("error unescaping cookie parameter '%s'", e.ParamName)
}

func (e *UnescapedCookieParamError) Unwrap() error {
	return e.Err
}

type UnmarshalingParamError struct {
	ParamName string
	Err       error
}

func (e *UnmarshalingParamError) Error() string {
	return fmt.Sprintf("Error unmarshaling parameter %s as JSON: %s", e.ParamName, e.Err.Error())
}

func (e *UnmarshalingParamError) Unwrap() error {
	return e.Err
}

type RequiredParamError struct {
	ParamName string
}

func (e *RequiredParamError) Error() string {
	return fmt.Sprintf("Query argument %s is required, but not found", e.ParamName)
}

type RequiredHeaderError struct {
	ParamName string
	Err       error
}

func (e *RequiredHeaderError) Error() string {
	return fmt.Sprintf("Header parameter %s is required, but not found", e.ParamName)
}

func (e *RequiredHeaderError) Unwrap() error {
	return e.Err
}

type InvalidParamFormatError struct {
	ParamName string
	Err       error
}

func (e *InvalidParamFormatError) Error() string {
	return fmt.Sprintf("Invalid format for parameter %s: %s", e.ParamName, e.Err.Error())
}

func (e *InvalidParamFormatError) Unwrap() error {
	return e.Err
}

type TooManyValuesForParamError struct {
	ParamName string
	Count     int
}

func (e *TooManyValuesForParamError) Error() string {
	return fmt.Sprintf("Expected one value for %s, got %d", e.ParamName, e.Count)
}

// Handler creates http.Handler with routing matching OpenAPI spec.
func Handler(si ServerInterface) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{})
}

type ChiServerOptions struct {
	BaseURL          string
	BaseRouter       chi.Router
	Middlewares      []MiddlewareFunc
	ErrorHandlerFunc func(w http.ResponseWriter, r *http.Request, err error)
}

// HandlerFromMux creates http.Handler with routing matching OpenAPI spec based on the provided mux.
func HandlerFromMux(si ServerInterface, r chi.Router) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseRouter: r,
	})
}

func HandlerFromMuxWithBaseURL(si ServerInterface, r chi.Router, baseURL string) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseURL:    baseURL,
		BaseRouter: r,
	})
}

// HandlerWithOptions creates http.Handler with additional options
func HandlerWithOptions(si ServerInterface, options ChiServerOptions) http.Handler {
	r := options.BaseRouter

	if r == nil {
		r = chi.NewRouter()
	}
	if options.ErrorHandlerFunc == nil {
		options.ErrorHandlerFunc = func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		}
	}
	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandlerFunc:   options.ErrorHandlerFunc,
	}

	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/documents/{docID}/sign-confirm-web", wrapper.ConfirmSignDocumentByIDWeb)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/documents/{docID}/sign-web", wrapper.SignDocumentByIDWeb)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/health", wrapper.Health)
	})

	return r
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+RabW8jtxH+Kws2H3KI3ny+pGd9KXznpFVxuRjxtSlquwW9S0kb71u4lGPHEGDZueul",
	"Tho06IegQHK4/IHKjhXr/CL/heE/Kkjuat9oy1KTtmg/Wd7deTicGT4zHHIXmb4b+B7xWIjquyjAFLuE",
	"ESr/WzRNErBH2Gt1cIuIJxYJTWoHzPY9VEfwDzjlh3BmwCn04YrvwYj3oI9KyBav2wRbhKIS8rBLUD2C",
	"K4/xSig028TFCriJOw5DdbS5iUqIeB0X1Vfjf1AJ0Q5aLyGyjd3AEWC0g0qI7QTid8io7bVQt1tCS77Z",
	"cYnHGkvLmLWXxXQ0en8NJzCAS74PQ/4pDOEM+nwfRnwvmUmfP4chHEMfBnABI/geRgacwAjO+AFcRNLj",
	"qQaYtZOJWr7ZWBI6k486NiUWqjPaIenpNn3qYjHbTse2UAm5ePsR8Vqsjerzb5UEHCNUAK/Wygu43Fzf",
	"vd8tj3/fu8XvubtdjYG6SikSsge+ZRPp5RW75T30vaZN3Q/IxgPf2hFPTd9jxGPiJw4CxzaxMF71w1BY",
	"cDc1lYD6AaEsAhOKuwFrLE1n9SsYwRU/5PtwBkNh53P+pQHHcAwjOIG+AWfqByoVTJebYgmZvqWL1b8L",
	"BDXSCd+HYxjwPfhBqQRD/iVKR9f83bvzOuyg7XvkccfdIFQzxDcwEqHB9wy+DwM4hwH/FEZwKfVOwN/4",
	"+f379xcWFubmajWUdvYf1tbe+MXqXHlhfW3N2p2rleZq3de0YZ5E1mpGp2j2pZQf1sfy/saHxGRJFCSh",
	"2S3JKPjX3f/fbp9bGaMbr1RFgsuNtyn1dfP5Fo74ZzCEV4aYB9+DCxHRBowkdxzJWF5cblQM+ApGcArH",
	"klc+E5zC9w3+TKwDQZoyzF9FQZ42xS4iamj0u0pNxl/G2iTWqxCnTZs4llqQlmULfbGznJEtiGjm9mcY",
	"wJnQtGeI9QlDwe5itQhOFKt0IDhQ+FFoH896BGeoaOasT5TmRW8kBF6MrWk5RUfWE8mjaTvkke1taoZ6",
	"yXv8EM7FEIaIWUMOeApD/icY8J7wtoqEPryCcx14aLc8Mf8C9F8FsAHnivsyakeMlVhfPTjnn8OpjJx+",
	"vJbgIhlzw/cdgj0xKLOZQ7SLsS9CMnao3l6FKagHu+g1Spqojn5WTeqHarRoqrELn4hvuyW0RWhoK97I",
	"6fCVIAPeE/SrGb8kV4RyJ/T5lwbvwUg+uJSFxpDv8558LPhkKO1yocExXldsHw3En0MfjuBcMc+dZJa2",
	"x0iL0EKwylyuzBh9m8wpFTFj/94U1k8i++UM8Z1Q33g9rdodvUfiyiggNBSregkzvNiihAj0lXffbiyn",
	"xk/89iuCHVFdFPK1w1ZMn5IH1La0Jd5LMS7f5wfCyj2ZNI9VKBo5WV3wYdeZCXkspgX1mN2kuGNNCToW",
	"04IGMyka3KDnBnawZ5LfBBZm2nx4A3BOVgtvM2pvz6J1RlILHeJwJuBETgvLZkNlN4GamFrhomn6nWgP",
	"c3vgrKgW3HccYjI9e92EnMhdA7tl01lskZHUQlN3SkTqaoEsEvihPa1Jx1JaSFuaBNOdKUETOS1sxK3T",
	"qjoW04EKWv9lB9MpSSYR04L6lNgtb9Fk9pbNprRCXlg3wIc2xbOEVUpOC9uxzZ1w1jxRkNYNsYnDwJ4F",
	"PS2oBSY7puPjzWXqb09p8ayoFrxlzaTzWEwH6thbxCPhlOE8ltJC+tibFk+K6MA8n9nNaEc4JWhWVAfu",
	"s2A6SCGgAwrwzgykMJa6CXLFJbOhCkEt8ObGLGGUiGlBqW+SMLS91kzYeWndEB/hTwI/ZLPgZ0V14JQ0",
	"CaXYmQ53LKWDFCQkCuKpEGMhLSDxvVlmn5LTwrozFUqJmBY0mKniSMS0u0scbr6LPdyatsBNC2qB/U3i",
	"2Z9MqexYSgfZCaO+9u3xlEgRLLdDjD9TXJQuLPKEl08p6UyQrnPTroyZOFf7pagqX8hmCqY0S6Q3VumE",
	"kXGGJl9n6oN0jZMsjkxIp4OmsE3ML/1UzZjZQKQ2Vxo2ypTYWYJNcUB60xdVukmIFDZZ2Voit03K11+6",
	"bf57LHifhIHvheQn7o1fRY1DEa3y+RQdckoY3Xliu9e1yc/jnpbsk4/kopCNiEs4kV0J8eQcBnDCD/hf",
	"+Gdx/29fnZzAsdAo3cd8qzaxyZIYJ63fuq6RGBKzQ222s2K2iatM+4BgSugT4drFjupzbMhH78S2+PUH",
	"T+IDJ7mS5dvEOG3GAtUMtr2mX7TL4nJjbPoj/gWc8S+ENaBvyNOhIxjKltxlfFIkDDGUjZwf4gMGw/g9",
	"drFnPMKeZXutNW/Ng5dwBQPVCI6c/kw1w44zLWUYGKvhx7jVInT99UqlGv2+Y8gxhsYqJZZvylfy152K",
	"QF992MZeizh+S75p2yHz6U7VjJ/+0cW2V2kz17kjP3+fOASHxJgk1iSYdSiJJYURCXXD95orhG7ZZmzO",
	"sF6tfiJmXJWvy36zHEYfjLuSKGMSY3G5keqt1ZFFtsq1hbm3yrX78/NvknsLqIS2y47fkh7qUAfVkTAH",
	"w8w2q+J5JYhOAPyAeDiwUR3NV2qVOXWO0JbBUh0TdHVXHtV1q6Hd8sqmOgQrf0w25OL1Q6YCIaDExCw5",
	"x8stmRf6IyUYZHu3Q0M6e092MS9l31y1j9VC7sNlEj7FHqb4Nte6NOQi3YMBnBrwLXwHL5CcN5XZpmGh",
	"OorO9Vbslhd3IB/sNJY+IBvSIMlh76q+pZt8Us0dBndLEyV0Z7Hd9fQJ5M51reTMIWVVc0Ip2UMRrfTp",
	"3VptqmOr2/SvFR/knP23Ce35kiHp4FL69FxGwqVsXsMQLlNrOlrnVxpv8wMxvXs/4ozGZ1i6GX07Pq3q",
	"FwK2EIdr3lqnVps3N6j8S6L/1D/zlTmj4W1hx7Ye+lb0rhq9NMoGfAODaJVc8sPxidcEzLvGu3hb4D1s",
	"E3MzfHvbJMQiVhH9hVwLx/yQP5d2Hyn8fDZLJdQRnF2fSW9U615lwYjjZNGhBFs7K7L7X1QrHzH8QOSE",
	"QuBMGG7urvEOth1iPfHTq1lrYzHEiSSYEe/xz3ND8X3xKOfZNS+TWlF9db2Ewo7rymbdNBxXpC4dTTHc",
	"EqxTWE/8EK0LRa7j6Jm4WSmnDPFjEnCBbv8HePbfQbDpKlnHSJOuaiRF8KTQyxSlko3/c9Q6lMo/g6Fi",
	"f1kPyCtTzyQ59Qr0eyMjzFVqxm8F00qlpQpFLsiMfxxF/Eky/okKen7In4qSE66gL211AQO+Lw9dn+bu",
	"dk3g6vmYqxdVOX97tpan33ABQ5FTi7TdL9D2qXo2kIX25Cxyz3hMPo60EluLxz67XjeRp0ZwxJ9Kdwi1",
	"RrFvRjIQJYmWlAMHKjQlzcGF5gaZIlxZEdw+671pRMo+9tk7fsezGt5DbLY1SfVFan8obyYM5PUEeBUt",
	"mL507Rn/Qhh6QpaZj/P3kuDcyQm8sTRtjfB/mTjHCYj34HvpFBm2msQjl98lDK69kDEpb7bHB/0twjT7",
	"+xeZakfEhmx5PVX72BN+EF3nkZclevJuyQU/jC687Mnsp65k9IRxZCo9UvcxhHkKGTG6d/ATJpNoBB39",
	"fn1txZ1w2vOoRB/BBT8w5IyjXiA/EDYZwlnEfUlfcATHeadrWhCr691cKORNf2ubjr3+MnGKXIED4XWp",
	"Cd2K64usEZbIlqHeolK0Y443547adZctslWRG/UKJZaLLepv+Kxi+m4VB3Z1a07WIFnUFYZbZBJuKD6a",
	"gLw+ntxufHtWM0mhQPy2GPjd9e4/AwAA////asSIyCwAAA==",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
