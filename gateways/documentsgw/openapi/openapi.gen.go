// Package openapi provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.5.0 DO NOT EDIT.
package openapi

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/go-chi/chi/v5"
	"github.com/oapi-codegen/runtime"
)

const (
	BearerTokenAuthScopes = "BearerTokenAuth.Scopes"
)

// Defines values for AcceptLanguage.
const (
	AcceptLanguageEn AcceptLanguage = "en"
	AcceptLanguageKk AcceptLanguage = "kk"
	AcceptLanguageRu AcceptLanguage = "ru"
)

// Defines values for DownloadPublicDocParamsAcceptLanguage.
const (
	DownloadPublicDocParamsAcceptLanguageEn DownloadPublicDocParamsAcceptLanguage = "en"
	DownloadPublicDocParamsAcceptLanguageKk DownloadPublicDocParamsAcceptLanguage = "kk"
	DownloadPublicDocParamsAcceptLanguageRu DownloadPublicDocParamsAcceptLanguage = "ru"
)

// Defines values for DownloadPrivateDocParamsAcceptLanguage.
const (
	En DownloadPrivateDocParamsAcceptLanguage = "en"
	Kk DownloadPrivateDocParamsAcceptLanguage = "kk"
	Ru DownloadPrivateDocParamsAcceptLanguage = "ru"
)

// APIError Общий формат ошибки API. Возвращает цифровой код
type APIError struct {
	Error string `json:"error"`

	// Fields Объект с описанием деталей ошибок
	Fields *map[string]string `json:"fields,omitempty"`
}

// Health defines model for Health.
type Health struct {
	// Documents Статус сервиса documents
	Documents bool `json:"documents"`
}

// AcceptLanguage defines model for AcceptLanguage.
type AcceptLanguage string

// BucketNamePathParam defines model for BucketNamePathParam.
type BucketNamePathParam = string

// DocumentNamePathParam defines model for DocumentNamePathParam.
type DocumentNamePathParam = string

// DownloadPublicDocParams defines parameters for DownloadPublicDoc.
type DownloadPublicDocParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *DownloadPublicDocParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// DownloadPublicDocParamsAcceptLanguage defines parameters for DownloadPublicDoc.
type DownloadPublicDocParamsAcceptLanguage string

// DownloadPrivateDocParams defines parameters for DownloadPrivateDoc.
type DownloadPrivateDocParams struct {
	// AcceptLanguage Язык запроса
	AcceptLanguage *DownloadPrivateDocParamsAcceptLanguage `json:"Accept-Language,omitempty"`
}

// DownloadPrivateDocParamsAcceptLanguage defines parameters for DownloadPrivateDoc.
type DownloadPrivateDocParamsAcceptLanguage string

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Проверка на работоспособность
	// (GET /health)
	Health(w http.ResponseWriter, r *http.Request)
	// Скачивание публичного документа
	// (GET /public/{docName})
	DownloadPublicDoc(w http.ResponseWriter, r *http.Request, docName DocumentNamePathParam, params DownloadPublicDocParams)
	// Скачивание приватного документа
	// (GET /{bucketName}/{docName})
	DownloadPrivateDoc(w http.ResponseWriter, r *http.Request, bucketName BucketNamePathParam, docName DocumentNamePathParam, params DownloadPrivateDocParams)
}

// Unimplemented server implementation that returns http.StatusNotImplemented for each endpoint.

type Unimplemented struct{}

// Проверка на работоспособность
// (GET /health)
func (_ Unimplemented) Health(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Скачивание публичного документа
// (GET /public/{docName})
func (_ Unimplemented) DownloadPublicDoc(w http.ResponseWriter, r *http.Request, docName DocumentNamePathParam, params DownloadPublicDocParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Скачивание приватного документа
// (GET /{bucketName}/{docName})
func (_ Unimplemented) DownloadPrivateDoc(w http.ResponseWriter, r *http.Request, bucketName BucketNamePathParam, docName DocumentNamePathParam, params DownloadPrivateDocParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandlerFunc   func(w http.ResponseWriter, r *http.Request, err error)
}

type MiddlewareFunc func(http.Handler) http.Handler

// Health operation middleware
func (siw *ServerInterfaceWrapper) Health(w http.ResponseWriter, r *http.Request) {

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{})

	r = r.WithContext(ctx)

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.Health(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DownloadPublicDoc operation middleware
func (siw *ServerInterfaceWrapper) DownloadPublicDoc(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "docName" -------------
	var docName DocumentNamePathParam

	err = runtime.BindStyledParameterWithOptions("simple", "docName", chi.URLParam(r, "docName"), &docName, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "docName", Err: err})
		return
	}

	// Parameter object where we will unmarshal all parameters from the context
	var params DownloadPublicDocParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage DownloadPublicDocParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DownloadPublicDoc(w, r, docName, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

// DownloadPrivateDoc operation middleware
func (siw *ServerInterfaceWrapper) DownloadPrivateDoc(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "bucketName" -------------
	var bucketName BucketNamePathParam

	err = runtime.BindStyledParameterWithOptions("simple", "bucketName", chi.URLParam(r, "bucketName"), &bucketName, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "bucketName", Err: err})
		return
	}

	// ------------- Path parameter "docName" -------------
	var docName DocumentNamePathParam

	err = runtime.BindStyledParameterWithOptions("simple", "docName", chi.URLParam(r, "docName"), &docName, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "docName", Err: err})
		return
	}

	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerTokenAuthScopes, []string{"not_identified", "active"})

	r = r.WithContext(ctx)

	// Parameter object where we will unmarshal all parameters from the context
	var params DownloadPrivateDocParams

	headers := r.Header

	// ------------- Optional header parameter "Accept-Language" -------------
	if valueList, found := headers[http.CanonicalHeaderKey("Accept-Language")]; found {
		var AcceptLanguage DownloadPrivateDocParamsAcceptLanguage
		n := len(valueList)
		if n != 1 {
			siw.ErrorHandlerFunc(w, r, &TooManyValuesForParamError{ParamName: "Accept-Language", Count: n})
			return
		}

		err = runtime.BindStyledParameterWithOptions("simple", "Accept-Language", valueList[0], &AcceptLanguage, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationHeader, Explode: false, Required: false})
		if err != nil {
			siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "Accept-Language", Err: err})
			return
		}

		params.AcceptLanguage = &AcceptLanguage

	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DownloadPrivateDoc(w, r, bucketName, docName, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

type UnescapedCookieParamError struct {
	ParamName string
	Err       error
}

func (e *UnescapedCookieParamError) Error() string {
	return fmt.Sprintf("error unescaping cookie parameter '%s'", e.ParamName)
}

func (e *UnescapedCookieParamError) Unwrap() error {
	return e.Err
}

type UnmarshalingParamError struct {
	ParamName string
	Err       error
}

func (e *UnmarshalingParamError) Error() string {
	return fmt.Sprintf("Error unmarshaling parameter %s as JSON: %s", e.ParamName, e.Err.Error())
}

func (e *UnmarshalingParamError) Unwrap() error {
	return e.Err
}

type RequiredParamError struct {
	ParamName string
}

func (e *RequiredParamError) Error() string {
	return fmt.Sprintf("Query argument %s is required, but not found", e.ParamName)
}

type RequiredHeaderError struct {
	ParamName string
	Err       error
}

func (e *RequiredHeaderError) Error() string {
	return fmt.Sprintf("Header parameter %s is required, but not found", e.ParamName)
}

func (e *RequiredHeaderError) Unwrap() error {
	return e.Err
}

type InvalidParamFormatError struct {
	ParamName string
	Err       error
}

func (e *InvalidParamFormatError) Error() string {
	return fmt.Sprintf("Invalid format for parameter %s: %s", e.ParamName, e.Err.Error())
}

func (e *InvalidParamFormatError) Unwrap() error {
	return e.Err
}

type TooManyValuesForParamError struct {
	ParamName string
	Count     int
}

func (e *TooManyValuesForParamError) Error() string {
	return fmt.Sprintf("Expected one value for %s, got %d", e.ParamName, e.Count)
}

// Handler creates http.Handler with routing matching OpenAPI spec.
func Handler(si ServerInterface) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{})
}

type ChiServerOptions struct {
	BaseURL          string
	BaseRouter       chi.Router
	Middlewares      []MiddlewareFunc
	ErrorHandlerFunc func(w http.ResponseWriter, r *http.Request, err error)
}

// HandlerFromMux creates http.Handler with routing matching OpenAPI spec based on the provided mux.
func HandlerFromMux(si ServerInterface, r chi.Router) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseRouter: r,
	})
}

func HandlerFromMuxWithBaseURL(si ServerInterface, r chi.Router, baseURL string) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseURL:    baseURL,
		BaseRouter: r,
	})
}

// HandlerWithOptions creates http.Handler with additional options
func HandlerWithOptions(si ServerInterface, options ChiServerOptions) http.Handler {
	r := options.BaseRouter

	if r == nil {
		r = chi.NewRouter()
	}
	if options.ErrorHandlerFunc == nil {
		options.ErrorHandlerFunc = func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		}
	}
	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandlerFunc:   options.ErrorHandlerFunc,
	}

	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/health", wrapper.Health)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/public/{docName}", wrapper.DownloadPublicDoc)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/{bucketName}/{docName}", wrapper.DownloadPrivateDoc)
	})

	return r
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xY3W7b2BF+FeJ0LxqUlOTIiW0VvfCuG9RFf4xsihYrqcYxOZS4oUj28EiJ1xDgn+0m",
	"22RRoCjQvekWAXpf2WshiizRrzDnjYo5JEXLZpwtUhR70ZuE5/+bme+bGfmA2WEvCgMIZMwaByzigvdA",
	"gtCjTduGSP6CB50+7wDNOBDbwoukFwaswfBf+Fq9wKmBr3GEl+oQE3WEI2Yyj5a7wB0QzGQB7wFrZNdZ",
	"i/tMFttd6PH0Ypf3fcka7PFjZjII+j3WaOYDZjLRZ22TwVPei3y6TPSZyeR+RN+xFF7QYcOhyT7s249B",
	"/or3YIfL7g6ZU4L7a5ypPxt4iiOc4lgdF5gjLrsF4r3FbYQA/tD3BDisIUUfroIvUG3cg3r93vp9i991",
	"1q3VNahZG3zFsWy+UV93Vty7bt1mJr0iQdB7v29y67NN65OatWHttn/0QalRW6Hd70HwHc1Sn+MI3+AF",
	"jgw8o1GiDnGGI3WMY8P1fNgl6yrwVEIQe2HQCsqNd0L7P7Cc31133T33vmXf33Cs1fqabfH7a6tWrba+",
	"umav3Ku5jr07WKlEjnubA1qtSjFR7o8hAYqjMIhBs/SB58NW+CTwQ+48zBZo3g4DCYGkTx5Fvmdz8lM1",
	"tCVIK5YCUh8Wxrih6HGi4J4XcLFf/vQ1l/8z9bWhTtQRXuJYPcc5JoY6wimO1DMc4ZyZmRI02o9SVNaW",
	"F0dh7KX33Ijk33CE32KCF5jgGSYksXO8oOCe0MVab3OcEIknOMMxDa4Enr0lRlJyu0tM+rEmAgX6J618",
	"Q0U+lS1WZjXZnd6XpoWd7Z8KEYoS3N/gqfoSJ/hmiXcGJuo5TvAUpzgxNne2Kwb+BRN8jWfqEEfqSxyR",
	"Cg31BU7U55RGtNFvDJxigufsiu4PGKRPs99VamxoskiEEQjppVyAHNc1G0zmeuA7eg93HO127u8snb1x",
	"pMS2P+EYp4T0yMAEL3FC+Y6cj2OcUYx0MsELHBP63OoEp4Vbw71PwZYspXGuq2aGvH1jm8l+BtyXXZ2d",
	"l/A6WVaIS+LwimCoY2IlcXGsDvEsBWsUxxZv7YWhDzy4ganY2i6DH4PdF57c/5iokWL6ELgA8Sh8DMFm",
	"PwW9p6ce5Nr6+W8f5ezUT+vVAkpXyigVmhe44U3LNne2cyngqfoKp+ornOM5jowOl/CE7xt5soxbQSvA",
	"V1qUmlc40Zr8IpXNjczYjJ/wTgdE+4eVSjX7vkPyusCJ0RTghLZe0l93KnR786MuDzrghx290vViGYr9",
	"qp3P7va4F1S6suff0dsfgg88BuNdx1zgsi8gP0neAdGLf+1+DGLg2bmf4ka1+hnv8aCql63QteJsg8mk",
	"J7XmP6ENhVNIfcxkAxBx6lAHBlZtY+W+VVuv1+/B6gYz2VPLDzva+X3hswYjh0guPbtK85UoK0thBAGP",
	"PNZg9UqtspKm9a7mQbW7IG0HZAlB/5GpnJg51ZVK0/SPBs5I8uok05A6ItpSgHCmXhg4x5FBOYNUpY51",
	"v3Gp/03wlDIv7VUvmQYndMLfdlgj19C1ynG3VrulUHwap6m5yKQfCHBZg/2gWjRN1SwvVrMXymrE1zi/",
	"wrWMf5eYXG2bnuscPscEZ+rE0BZn+lUn5JMJTrP2qtBygmdLOmSNZokCm+1h22Rxv9ejmlbm+u/sU8k7",
	"MeUFfFUEBefqBY5Zm4BUo/6e79nVg6x1GL49/q+y+jjBszyBGnipTvCU9KaeaVd8Sz46p/SpTtIqR27R",
	"glgOb17+d/T7W2HaYxWtbLM8dsWW6rVWd2i+80R5U0a+LuNY2WWLfdXSFmZostX/IkEXVbuMot8s6vPo",
	"WjPfClr9Wq1u7wn9P2SjdLBaqRvbwYD7nrMV2ttb6WI1WzUsA/+O44xqxJM3xvZWaURvfWNlzXjAPR+c",
	"R+FD4A45q/Qhao7OdfUlyr40MiOe4SRT08tFi9QKritnWSPvQc+rOvnr8qJ6kenkoPhhMXwPtRymE+r4",
	"FjjUqqgT9Ux3JgnOjH4MgqIwoRw00rqf0WKaFUxDF8xDHGtXzilslJXPDH18qtPU+DYJCm/AJfxvNFj2",
	"a+//0v0eSXfd+A3RLXDDX3pxj0u7e/Od0vKY6AqJF+olvtbVSjdp1BRQvRrrXwbqUPN0qvn/vUgiZeWX",
	"BaHc9RwIpOd64DCTcVt6A2DX6/J7iPydOUfDFINciMsU2oKBka4yM+v44rAHFn2SmpZ37wiwIhE6bz/S",
	"XqA5yP+WUIKKbs5Xb/YTw/bw3wEAAP//15vOtJwSAAA=",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
