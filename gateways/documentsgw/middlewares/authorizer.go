package middlewares

import (
	"context"
	"net/http"
	"slices"
	"strings"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/security"
	"git.redmadrobot.com/backend-go/rmr-pkg/httpx"
	"git.redmadrobot.com/zaman/backend/zaman/config/gateways/documentsgw"
)

const UserInfoKey = "userInfo"

func SecureAuthorizer[T security.RequestCredentials](
	authManager security.Manager,
	credentialsProvider security.HTTPCredentialsProvider[T],
	authProvider security.AuthenticationProvider[T],
	cfg documentsgw.Config,
) httpx.Middleware {
	errResponse := httpx.ServerErrorResponses(
		httpx.MakeSecureServerError,
		httpx.NewErrorToStatusMapper(httpx.DefaultErrorToStatusMapping()),
	)

	return httpx.MiddlewareFn(func(response http.ResponseWriter, request *http.Request, next http.Handler) {
		ctx := request.Context()
		logger := logs.FromContext(ctx)

		// Проверяем, требуется ли авторизация
		requirements := authManager.SecurityRequirements(ctx)

		logger.Info().Msg("authorizing user request")

		// Аутентифицируем пользователя с помощью дефолтного или своего провайдера
		authentication, err := authProvider.Authenticate(ctx, credentialsProvider(request))
		if err != nil {
			if !requirements.IsAuthorizationRequired {
				next.ServeHTTP(response, request)

				return
			}
			logger.Warn().Msgf("invalid access token credentials cause: %v", err)
			httpx.SendResponse(ctx, response, errResponse(errs.Reasons(err.Error(), errs.TypeIllegalArgument, "1.1")))

			return
		}

		// Если используется список разрешенных issuers токенов, то проверяем, что токен принадлежит этому списку
		if cfg.App.UseAllowedTokenIssuersList {
			if !slices.Contains(cfg.App.AllowedTokenIssuersList, authentication.TokenIssuer) {
				logger.Warn().Msgf("requested operation is forbidden for this token issuer: %s", authentication.TokenIssuer)
				httpx.SendResponse(ctx, response, errResponse(errs.Reasons("requested action is not permitted for current user", errs.TypeIllegalArgument, "1.1")))
				return
			}
		}

		// Добавляем данные аутентификации в логи
		logger.UpdateContext(logs.Options(
			logs.UserIDTag.Option(authentication.User.ID.String()),
			logs.UserAuthorityTag.Option(logs.TagStringArray(authentication.Authorities.ToStrings())),
			logs.TokenIDTag.Option(authentication.TokenID),
		))

		// Проверяем права доступа
		if authentication.Authorities.Meets(requirements.Authorities) {
			logger.Info().Msg("user authority granted")

			// Добавляем информацию о пользователе в контекст
			userInfo := map[string]interface{}{
				"user_id":      authentication.User.ID,
				"phone_number": authentication.User.PhoneNumber,
				"name":         authentication.User.Name,
				"session_id":   authentication.SessionID,
				"info":         authentication.User.Info,
				"roles":        strings.Join(authentication.Authorities.ToStrings(), ","),
			}
			// Используем ключ `UserInfoKey` для хранения данных в контексте
			ctx = context.WithValue(ctx, UserInfoKey, userInfo) //nolint:revive,staticcheck //FIXME

			// Обновляем контекст логгера и передаем управление дальше
			ctx = authentication.ToContext(ctx)
			ctx = logs.ToContext(ctx, logger)

			next.ServeHTTP(response, request.WithContext(ctx))

			return
		}

		logger.Warn().Msg("requested operation is forbidden")

		httpx.SendResponse(ctx, response, errResponse(errs.Reasons("requested action is not permitted for current user", errs.TypeIllegalArgument, "1.3")))
	}).Middleware()
}
