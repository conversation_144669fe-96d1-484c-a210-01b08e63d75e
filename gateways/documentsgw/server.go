// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package documentsgw

import (
	"net/http"
	"strings"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/presenters"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/security"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/security/jwt"
	"git.redmadrobot.com/backend-go/rmr-pkg/httpx"
	"git.redmadrobot.com/backend-go/rmr-pkg/httpx/mw"
	"github.com/getkin/kin-openapi/openapi3"
	"github.com/getkin/kin-openapi/routers"
	"github.com/getkin/kin-openapi/routers/legacy"
	sentry "github.com/getsentry/sentry-go/http"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/go-chi/cors"

	"git.redmadrobot.com/zaman/backend/zaman/cmd/gateways/documentsgw/providers"
	"git.redmadrobot.com/zaman/backend/zaman/gateways/documentsgw/middlewares"
	"git.redmadrobot.com/zaman/backend/zaman/gateways/documentsgw/openapi"

	cfgDocumentsgw "git.redmadrobot.com/zaman/backend/zaman/config/gateways/documentsgw"
	gwSecurity "git.redmadrobot.com/zaman/backend/zaman/gateways/documentsgw/security"
)

var _ openapi.ServerInterface = (*Server)(nil)

type (
	Server struct {
		cfg       cfgDocumentsgw.Config
		Providers providers.ServiceLocatorImpl
	}

	OpenAPI struct {
		*openapi3.T
		routers.Router
	}
)

func New(cfg cfgDocumentsgw.Config, locator providers.ServiceLocatorImpl) *Server {
	return &Server{
		cfg:       cfg,
		Providers: locator,
	}
}

func (s *Server) NewServerOptions(sentryHandler *sentry.Handler, openAPIRouter routers.Router) (openapi.ChiServerOptions, chi.Router, error) {
	validator := httpx.NewRequestValidator(openAPIRouter, httpx.DontValidateBinaryBody(), httpx.DontValidateXMLBody())

	router := chi.NewRouter()
	router.Use(cors.Handler(cors.Options{
		AllowedOrigins:   strings.Split(s.cfg.HTTP.CORS.Origins, ","),
		AllowedMethods:   strings.Split(s.cfg.HTTP.CORS.Methods, ","),
		AllowedHeaders:   strings.Split(s.cfg.HTTP.CORS.Headers, ","),
		AllowCredentials: s.cfg.HTTP.CORS.Credentials,
	}))
	router.Use(sentryHandler.Handle)
	router.Use(mw.OTELContextPropagator())

	router.Use(mw.RequestID())
	router.Use(middleware.RealIP)
	router.Use(mw.Logger(presenters.ViewLogs, presenters.DefaultViewOptions()))
	router.Use(mw.AnnotateRequestContext())
	router.Use(mw.Recoverer())

	// Spec group
	router.Group(func(r chi.Router) {
		fs := http.FileServer(http.Dir("./specs/openapi/documentsgw"))
		r.With(middleware.BasicAuth("Specification", s.cfg.App.SpecBasicAuthCredentials)).
			Handle("/spec", http.RedirectHandler("/spec/", http.StatusMovedPermanently))
		r.With(middleware.BasicAuth("Specification", s.cfg.App.SpecBasicAuthCredentials)).
			Handle("/spec/", http.RedirectHandler("/spec/swagger/", http.StatusMovedPermanently))
		r.With(middleware.BasicAuth("Specification", s.cfg.App.SpecBasicAuthCredentials)).
			Handle("/spec/*", http.StripPrefix("/spec", fs))
	})

	// Main group
	mainGroup := router.Group(func(r chi.Router) {

		customMidlewares := middlewares.NewCustomMiddlewares(s.cfg)
		for _, mw := range customMidlewares {
			r.Use(mw)
		}
		r.Use(middleware.Timeout(s.cfg.HTTP.Timeout))
	})
	jwtTokenManager, err := jwt.NewTokenManager(
		s.cfg.TokenManager,
		func() jwt.RichToken { return &gwSecurity.DocumentsgwRichToken{Claims: &gwSecurity.DocumentsgwClaims{}} },
	)
	if err != nil {
		return openapi.ChiServerOptions{}, nil, err
	}

	return openapi.ChiServerOptions{
		BaseURL:    s.cfg.HTTP.BaseURL,
		BaseRouter: router,
		Middlewares: []openapi.MiddlewareFunc{
			mw.Middleware(mw.SecureValidator(validator)),
			mw.Middleware(middlewares.SecureAuthorizer[security.BearerToken](
				security.DefaultManager{AuthoritiesContextKey: openapi.BearerTokenAuthScopes},
				httpx.BearerTokenExtract,
				jwt.NewAuthenticationProvider(jwtTokenManager),
				s.cfg,
			)),
		},
	}, mainGroup, nil
}

func NewOpenAPI(cfg cfgDocumentsgw.Config) (*OpenAPI, error) {
	var api OpenAPI
	var err error

	api.T, err = openapi.GetSwagger()
	if err != nil {
		return nil, err
	}
	api.Servers = openapi3.Servers{&openapi3.Server{URL: cfg.HTTP.BaseURL}, &openapi3.Server{URL: "/"}}
	api.Router, err = legacy.NewRouter(api.T)
	if err != nil {
		return nil, errs.Wrapf(err, "can't create application route")
	}

	return &api, nil
}
