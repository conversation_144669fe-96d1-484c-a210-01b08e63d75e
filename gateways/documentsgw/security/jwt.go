package security

import (
	"git.redmadrobot.com/backend-go/rmr-pkg/core/security"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/security/jwt"
	jwtlib "github.com/golang-jwt/jwt/v5"
)

type (
	RealmAccess struct {
		Roles []string `json:"roles"`
	}

	DocumentsgwClaims struct {
		jwtlib.RegisteredClaims
		Phone             string               `json:"phone"`
		Name              string               `json:"name"`
		Authorities       security.Authorities `json:"roles"`
		RealmAccess       RealmAccess          `json:"realm_access"`
		PreferredUsername string               `json:"preferred_username"`
		SessionID         string               `json:"sid"`
		Issuer            string               `json:"iss"`
	}
)

func (c *DocumentsgwClaims) GetID() string {
	return c.ID
}

type DocumentsgwRichToken struct {
	jwt.DefaultRichToken
	Claims *DocumentsgwClaims
}

func (r *DocumentsgwRichToken) GetPhoneNumber() string {
	// т.к. используется токен keycloak - телефон в нем не передается, но вместо name мы используем телефон пользователя
	return r.Claims.PreferredUsername
}

func (r *DocumentsgwRichToken) GetSessionID() string {
	return r.Claims.SessionID
}

func (r *DocumentsgwRichToken) GetName() string {
	return r.Claims.PreferredUsername
}

func (r *DocumentsgwRichToken) GetPhone() string {
	r.GetClaims()
	return r.Claims.Phone
}

func (r *DocumentsgwRichToken) GetAuthorities() []security.Authority {
	result := make([]security.Authority, 0, len(r.Claims.RealmAccess.Roles))
	for i := range r.Claims.RealmAccess.Roles {
		result = append(result, security.Authority(r.Claims.RealmAccess.Roles[i]))
	}
	return result
}

func (r *DocumentsgwRichToken) GetClaims() jwtlib.Claims { return r.Claims }

func (r *DocumentsgwRichToken) GetIssuer() string {
	r.GetClaims()
	return r.Claims.Issuer
}
