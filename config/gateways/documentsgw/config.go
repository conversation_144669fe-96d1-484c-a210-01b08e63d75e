package documentsgw

import (
	"strings"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/cfg/viperx"
	"github.com/spf13/viper"
)

const (
	keySpecBasicAuthLogin         = "SPEC_BASIC_AUTH_LOGIN"
	keySpecBasicAuthPassword      = "SPEC_BASIC_AUTH_PASSWORD"       //nolint
	keyUseAllowedTokenIssuersList = "USE_ALLOWED_TOKEN_ISSUERS_LIST" //nolint
	keyAllowedTokenIssuersList    = "ALLOWED_TOKEN_ISSUERS_LIST"

	defaultUseAllowedTokenIssuersLisFlag = false
)

type Application struct {
	Debug                      bool
	Environment                string
	AppPrefix                  string
	SpecBasicAuthLogin         string   `env:"SPEC_BASIC_AUTH_LOGIN"`
	SpecBasicAuthPassword      string   `env:"SPEC_BASIC_AUTH_PASSWORD"`
	UseAllowedTokenIssuersList bool     `env:"USE_ALLOWED_TOKEN_ISSUERS_LIST"`
	AllowedTokenIssuersList    []string `env:"ALLOWED_TOKEN_ISSUERS_LIST"`

	SpecBasicAuthCredentials map[string]string
}

func ApplicationFromEnv(loader *viper.Viper) *Application {
	env := viperx.Environment(loader)

	loader.SetDefault(keyUseAllowedTokenIssuersList, defaultUseAllowedTokenIssuersLisFlag)
	loader.SetDefault(keyAllowedTokenIssuersList, []string{})

	credentials := make(map[string]string)
	login := loader.GetString(keySpecBasicAuthLogin)
	if login != "" {
		credentials[login] = loader.GetString(keySpecBasicAuthPassword)
	}

	return &Application{
		Debug:                      loader.GetBool(keyDebug),
		Environment:                env.String(),
		AppPrefix:                  strings.ToLower(AppPrefix),
		SpecBasicAuthCredentials:   credentials,
		UseAllowedTokenIssuersList: loader.GetBool(keyUseAllowedTokenIssuersList),
		AllowedTokenIssuersList:    loader.GetStringSlice(keyAllowedTokenIssuersList),
	}
}
