// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package main

import (
	"context"
	"fmt"
	"log"
	"os/signal"
	"syscall"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/providers/otelx"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/sentry"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/vault"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel"

	cfgamlBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/aml-bridge"
	cfgantifraud "git.redmadrobot.com/zaman/backend/zaman/config/services/antifraud"
	cfgapBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/ap-bridge"
	cfgcolvirBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/colvir-bridge"
	cfgdictionary "git.redmadrobot.com/zaman/backend/zaman/config/services/dictionary"
	cfgdocuments "git.redmadrobot.com/zaman/backend/zaman/config/services/documents"
	cfgotp "git.redmadrobot.com/zaman/backend/zaman/config/services/otp"
	cfgpaymentsSme "git.redmadrobot.com/zaman/backend/zaman/config/services/payments-sme"
	cfgprocessingBridge "git.redmadrobot.com/zaman/backend/zaman/config/services/processing-bridge"
	cfgusers "git.redmadrobot.com/zaman/backend/zaman/config/services/users"
	pbamlBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/aml-bridge"
	pbantifraud "git.redmadrobot.com/zaman/backend/zaman/specs/proto/antifraud"
	pbapBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/ap-bridge"
	pbcolvirBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
	pbdictionary "git.redmadrobot.com/zaman/backend/zaman/specs/proto/dictionary"
	pbdocuments "git.redmadrobot.com/zaman/backend/zaman/specs/proto/documents"
	pbotp "git.redmadrobot.com/zaman/backend/zaman/specs/proto/otp"
	pbpaymentsSme "git.redmadrobot.com/zaman/backend/zaman/specs/proto/payments-sme"
	pbprocessingBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
	pbusers "git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"

	"git.redmadrobot.com/backend-go/rmr-pkg/core"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka/confluent"

	"git.redmadrobot.com/backend-go/rmr-pkg/grpcx"

	"git.redmadrobot.com/backend-go/rmr-pkg/db"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx"

	"git.redmadrobot.com/zaman/backend/zaman/config/services/payments"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments/server"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments/storage"

	"git.redmadrobot.com/zaman/backend/zaman/cmd/services/payments/providers"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments/usecase"
)

func main() {
	time.Local = time.UTC

	// отмена контекста при сигналах системы (graceful shutdown)
	// SIGTERM - это посылает кубер, чтобы завершить процесс
	rootCtx, cancel := signal.NotifyContext(context.Background(), syscall.SIGTERM)
	defer cancel()

	cfg, envs := initConfigs(rootCtx)

	// Initialize Sentry
	err := sentry.NewGRPCSentry(rootCtx, cfg.Sentry, nil)
	if err != nil {
		log.Printf("unable to connect to sentry: %v", err)
		return
	}

	// Initialize OTEL
	traceProvider, err := otelx.NewOTELTraceProvider(rootCtx, true, cfg.Trace)
	if err != nil {
		log.Printf("Failed to create OTEL trace provider: %v", err)
		return
	}
	otel.SetTracerProvider(traceProvider)

	// Create logger
	log := logs.Logger(
		rootCtx,
		cfg.Logger,
		logs.InstanceIDTag.Option(uuid.New()), logs.Options(),
	)
	defer log.Fatal().Msgf("application stopped")

	logCtx := log.WithContext(rootCtx)

	if err := run(logCtx, cancel, cfg, envs); err != nil {
		log.Info().Err(err).Msg("unable to start application")
	}
}

func initConfigs(ctx context.Context) (*payments.Config, []string) {
	cfg, envs := payments.LoadFromEnv()

	// Initialize Vault
	vClient, err := vault.NewVaultClientKV2(cfg.Vault, cfg.App.AppPrefix)
	if err == nil {
		logs.FromContext(ctx).Info().Msgf("Successfully created Vault client for %s", cfg.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClient)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envs); syncErr != nil {
			logs.FromContext(ctx).Fatal().Msgf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfg, _ = payments.LoadFromEnv()
	}

	return cfg, envs
}

func run(ctx context.Context, cancel context.CancelFunc, cfg *payments.Config, envs []string) error {
	var err error
	locator := providers.NewServiceLocator(ctx)

	// Initialize Database
	dbStorage, storageClose, err := databaseStorage(ctx, cfg)
	defer storageClose()
	if err != nil {
		return fmt.Errorf("unable to init db storage: %w", err)
	}
	locator.Register("Storage", dbStorage)

	// Initialize Kafka
	eventBus, err := confluent.NewEventBus(ctx, cfg.Kafka,
		core.ErrorCallbackFn(func(err error) bool {
			logs.FromContext(ctx).Err(err).Msg("failed event bus kafka")
			return true
		}))
	if err != nil {
		return fmt.Errorf("failed to create kafka client: %w", err)
	}
	defer eventBus.Close(ctx)
	defer cancel()
	locator.Register("Event", eventBus)

	// Initialize users gRPC client
	cfgGrpcusers, envsGrpcusers := cfgusers.LoadFromEnv()

	vClientusers, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpcusers.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpcusers.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientusers)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpcusers); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpcusers, _ = cfgusers.LoadFromEnv()
	}

	usersServer, err := grpcx.ConnectServer(cfgGrpcusers.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer usersServer.Close()
	users := pbusers.NewUsersClient(usersServer)
	locator.Register("Users", users)

	// Initialize colvir-bridge gRPC client
	cfgGrpccolvirBridge, envsGrpccolvirBridge := cfgcolvirBridge.LoadFromEnv()

	vClientcolvirBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpccolvirBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpccolvirBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientcolvirBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpccolvirBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpccolvirBridge, _ = cfgcolvirBridge.LoadFromEnv()
	}

	colvirBridgeServer, err := grpcx.ConnectServer(cfgGrpccolvirBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer colvirBridgeServer.Close()
	colvirBridge := pbcolvirBridge.NewColvirbridgeClient(colvirBridgeServer)
	locator.Register("Colvirbridge", colvirBridge)

	// Initialize otp gRPC client
	cfgGrpcotp, envsGrpcotp := cfgotp.LoadFromEnv()

	vClientotp, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpcotp.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpcotp.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientotp)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpcotp); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpcotp, _ = cfgotp.LoadFromEnv()
	}

	otpServer, err := grpcx.ConnectServer(cfgGrpcotp.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer otpServer.Close()
	otp := pbotp.NewOtpClient(otpServer)
	locator.Register("Otp", otp)

	// Initialize dictionary gRPC client
	cfgGrpcdictionary, envsGrpcdictionary := cfgdictionary.LoadFromEnv()

	vClientdictionary, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpcdictionary.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpcdictionary.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientdictionary)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpcdictionary); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpcdictionary, _ = cfgdictionary.LoadFromEnv()
	}

	dictionaryServer, err := grpcx.ConnectServer(cfgGrpcdictionary.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer dictionaryServer.Close()
	dictionary := pbdictionary.NewDictionaryClient(dictionaryServer)
	locator.Register("Dictionary", dictionary)

	// Initialize aml-bridge gRPC client
	cfgGrpcamlBridge, envsGrpcamlBridge := cfgamlBridge.LoadFromEnv()

	vClientamlBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpcamlBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpcamlBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientamlBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpcamlBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpcamlBridge, _ = cfgamlBridge.LoadFromEnv()
	}

	amlBridgeServer, err := grpcx.ConnectServer(cfgGrpcamlBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer amlBridgeServer.Close()
	amlBridge := pbamlBridge.NewAmlbridgeClient(amlBridgeServer)
	locator.Register("Amlbridge", amlBridge)

	// Initialize documents gRPC client
	cfgGrpcdocuments, envsGrpcdocuments := cfgdocuments.LoadFromEnv()

	vClientdocuments, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpcdocuments.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpcdocuments.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientdocuments)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpcdocuments); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpcdocuments, _ = cfgdocuments.LoadFromEnv()
	}

	documentsServer, err := grpcx.ConnectServer(cfgGrpcdocuments.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer documentsServer.Close()
	documents := pbdocuments.NewDocumentsClient(documentsServer)
	locator.Register("Documents", documents)

	// Initialize ap-bridge gRPC client
	cfgGrpcapBridge, envsGrpcapBridge := cfgapBridge.LoadFromEnv()

	vClientapBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpcapBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpcapBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientapBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpcapBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpcapBridge, _ = cfgapBridge.LoadFromEnv()
	}

	apBridgeServer, err := grpcx.ConnectServer(cfgGrpcapBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer apBridgeServer.Close()
	apBridge := pbapBridge.NewApbridgeClient(apBridgeServer)
	locator.Register("Apbridge", apBridge)

	// Initialize antifraud gRPC client
	cfgGrpcantifraud, envsGrpcantifraud := cfgantifraud.LoadFromEnv()

	vClientantifraud, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpcantifraud.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpcantifraud.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientantifraud)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpcantifraud); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpcantifraud, _ = cfgantifraud.LoadFromEnv()
	}

	antifraudServer, err := grpcx.ConnectServer(cfgGrpcantifraud.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer antifraudServer.Close()
	antifraud := pbantifraud.NewAntifraudClient(antifraudServer)
	locator.Register("Antifraud", antifraud)

	// Initialize processing-bridge gRPC client
	cfgGrpcprocessingBridge, envsGrpcprocessingBridge := cfgprocessingBridge.LoadFromEnv()

	vClientprocessingBridge, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpcprocessingBridge.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpcprocessingBridge.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientprocessingBridge)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpcprocessingBridge); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpcprocessingBridge, _ = cfgprocessingBridge.LoadFromEnv()
	}

	processingBridgeServer, err := grpcx.ConnectServer(cfgGrpcprocessingBridge.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer processingBridgeServer.Close()
	processingBridge := pbprocessingBridge.NewProcessingbridgeClient(processingBridgeServer)
	locator.Register("Processingbridge", processingBridge)

	// Initialize payments-sme gRPC client
	cfgGrpcpaymentsSme, envsGrpcpaymentsSme := cfgpaymentsSme.LoadFromEnv()

	vClientpaymentsSme, err := vault.NewVaultClientKV2(cfg.Vault, cfgGrpcpaymentsSme.App.AppPrefix)
	if err == nil {
		log.Printf("Successfully created Vault client for %s", cfgGrpcpaymentsSme.App.AppPrefix)
		envSyncer := vault.NewEnvSyncer(vClientpaymentsSme)
		if syncErr := envSyncer.SyncEnvWithVault(ctx, envsGrpcpaymentsSme); syncErr != nil {
			log.Printf("Error synchronizing environment variables with Vault: %v", syncErr)
		}
		cfgGrpcpaymentsSme, _ = cfgpaymentsSme.LoadFromEnv()
	}

	paymentsSmeServer, err := grpcx.ConnectServer(cfgGrpcpaymentsSme.GRPC)
	if err != nil {
		logs.FromContext(ctx).Fatal().Msgf("Failed to create gRPC client: %v", err)
	}
	defer paymentsSmeServer.Close()
	paymentsSme := pbpaymentsSme.NewPaymentssmeClient(paymentsSmeServer)
	locator.Register("Paymentssme", paymentsSme)

	// Adding custom providers to the locator using the NewCustomProviders method.
	// This method can be used to add providers that were not automatically generated
	// or require special configuration before being registered.
	err = providers.NewCustomProviders(ctx, *cfg, locator)
	if err != nil {
		log.Fatalf("Failed to create providers: %v", err)
	}

	// Initialize Use cases
	useCases := usecase.New(ctx, *locator, cfg)
	// Initialize Kafka Consumer
	useCases.InitConsumer(ctx)

	// Initialize gRPC server
	service := server.NewServerOptions(useCases, cfg)
	grpcServer, err := service.NewServer(cfg.GRPC)
	if err != nil {
		log.Fatalf("Failed to create gRPC Server: %v", err)
	}

	return grpcx.StartServer(ctx, cfg.GRPC, grpcServer)
}

func databaseStorage(ctx context.Context, cfg *payments.Config) (storage.Storage, func(), error) {
	// Migrate the database.
	err := db.Migrate(ctx, cfg.PostgresDB, db.MigrateDefault(cfg.PostgresDB))
	if err != nil {
		return nil, func() {}, fmt.Errorf("failed to migrate database: %w", err)
	}

	// Get the database driver.
	postgresDriver, err := entx.Driver(cfg.PostgresDB)
	if err != nil {
		return nil, func() {}, fmt.Errorf("failed connect to DB: %w", err)
	}

	// Initialize the ent client.
	postgresDBClient, err := storage.NewPostgresDBClient(ctx, postgresDriver, cfg.PostgresDB.Debug)
	if err != nil {
		return nil, func() {}, fmt.Errorf("failed to init ent client: %w", err)
	}

	// Define the function to close the database connections.
	storageClientsCloseFunc := func() {
		if err = postgresDriver.Close(); err != nil {
			logs.FromContext(ctx).Err(err).Msg("failed to close ent client")
		}
	}

	storageDeps := storage.StorageDependencies{
		PostgresClient: postgresDBClient,
		SQLClient:      postgresDriver,
	}

	// Return the storage.Storage object and the function to close the database connection.
	return storage.NewStorage(storageDeps), storageClientsCloseFunc, nil
}
