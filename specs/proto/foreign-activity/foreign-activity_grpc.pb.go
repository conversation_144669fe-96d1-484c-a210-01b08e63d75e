// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.32.1
// source: specs/proto/foreign-activity/foreign-activity.proto

package foreign_activity

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Foreignactivity_HealthCheck_FullMethodName        = "/foreignactivity.Foreignactivity/HealthCheck"
	Foreignactivity_CreateConvertation_FullMethodName = "/foreignactivity.Foreignactivity/CreateConvertation"
	Foreignactivity_ConversionSum_FullMethodName      = "/foreignactivity.Foreignactivity/ConversionSum"
)

// ForeignactivityClient is the client API for Foreignactivity service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ForeignactivityClient interface {
	HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error)
	// Запрос на конвертацию
	CreateConvertation(ctx context.Context, in *CreateConvertationReq, opts ...grpc.CallOption) (*CreateConvertationResp, error)
	// Расчет конвертации валют
	ConversionSum(ctx context.Context, in *ConversionSumReq, opts ...grpc.CallOption) (*ConversionSumResp, error)
}

type foreignactivityClient struct {
	cc grpc.ClientConnInterface
}

func NewForeignactivityClient(cc grpc.ClientConnInterface) ForeignactivityClient {
	return &foreignactivityClient{cc}
}

func (c *foreignactivityClient) HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResp)
	err := c.cc.Invoke(ctx, Foreignactivity_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foreignactivityClient) CreateConvertation(ctx context.Context, in *CreateConvertationReq, opts ...grpc.CallOption) (*CreateConvertationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateConvertationResp)
	err := c.cc.Invoke(ctx, Foreignactivity_CreateConvertation_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foreignactivityClient) ConversionSum(ctx context.Context, in *ConversionSumReq, opts ...grpc.CallOption) (*ConversionSumResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConversionSumResp)
	err := c.cc.Invoke(ctx, Foreignactivity_ConversionSum_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ForeignactivityServer is the server API for Foreignactivity service.
// All implementations must embed UnimplementedForeignactivityServer
// for forward compatibility.
type ForeignactivityServer interface {
	HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error)
	// Запрос на конвертацию
	CreateConvertation(context.Context, *CreateConvertationReq) (*CreateConvertationResp, error)
	// Расчет конвертации валют
	ConversionSum(context.Context, *ConversionSumReq) (*ConversionSumResp, error)
	mustEmbedUnimplementedForeignactivityServer()
}

// UnimplementedForeignactivityServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedForeignactivityServer struct{}

func (UnimplementedForeignactivityServer) HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedForeignactivityServer) CreateConvertation(context.Context, *CreateConvertationReq) (*CreateConvertationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateConvertation not implemented")
}
func (UnimplementedForeignactivityServer) ConversionSum(context.Context, *ConversionSumReq) (*ConversionSumResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConversionSum not implemented")
}
func (UnimplementedForeignactivityServer) mustEmbedUnimplementedForeignactivityServer() {}
func (UnimplementedForeignactivityServer) testEmbeddedByValue()                         {}

// UnsafeForeignactivityServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ForeignactivityServer will
// result in compilation errors.
type UnsafeForeignactivityServer interface {
	mustEmbedUnimplementedForeignactivityServer()
}

func RegisterForeignactivityServer(s grpc.ServiceRegistrar, srv ForeignactivityServer) {
	// If the following call pancis, it indicates UnimplementedForeignactivityServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Foreignactivity_ServiceDesc, srv)
}

func _Foreignactivity_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ForeignactivityServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Foreignactivity_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ForeignactivityServer).HealthCheck(ctx, req.(*HealthCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Foreignactivity_CreateConvertation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateConvertationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ForeignactivityServer).CreateConvertation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Foreignactivity_CreateConvertation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ForeignactivityServer).CreateConvertation(ctx, req.(*CreateConvertationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Foreignactivity_ConversionSum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConversionSumReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ForeignactivityServer).ConversionSum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Foreignactivity_ConversionSum_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ForeignactivityServer).ConversionSum(ctx, req.(*ConversionSumReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Foreignactivity_ServiceDesc is the grpc.ServiceDesc for Foreignactivity service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Foreignactivity_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "foreignactivity.Foreignactivity",
	HandlerType: (*ForeignactivityServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HealthCheck",
			Handler:    _Foreignactivity_HealthCheck_Handler,
		},
		{
			MethodName: "CreateConvertation",
			Handler:    _Foreignactivity_CreateConvertation_Handler,
		},
		{
			MethodName: "ConversionSum",
			Handler:    _Foreignactivity_ConversionSum_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "specs/proto/foreign-activity/foreign-activity.proto",
}
