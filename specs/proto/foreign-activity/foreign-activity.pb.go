// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v6.32.1
// source: specs/proto/foreign-activity/foreign-activity.proto

package foreign_activity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HealthCheckReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckReq) Reset() {
	*x = HealthCheckReq{}
	mi := &file_specs_proto_foreign_activity_foreign_activity_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckReq) ProtoMessage() {}

func (x *HealthCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_foreign_activity_foreign_activity_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckReq.ProtoReflect.Descriptor instead.
func (*HealthCheckReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_foreign_activity_foreign_activity_proto_rawDescGZIP(), []int{0}
}

type HealthCheckResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckResp) Reset() {
	*x = HealthCheckResp{}
	mi := &file_specs_proto_foreign_activity_foreign_activity_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResp) ProtoMessage() {}

func (x *HealthCheckResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_foreign_activity_foreign_activity_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResp.ProtoReflect.Descriptor instead.
func (*HealthCheckResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_foreign_activity_foreign_activity_proto_rawDescGZIP(), []int{1}
}

func (x *HealthCheckResp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type CreateConvertationReq struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdempotencyId  string                 `protobuf:"bytes,1,opt,name=idempotency_id,json=idempotencyId,proto3" json:"idempotency_id,omitempty"`
	FromAccount    string                 `protobuf:"bytes,2,opt,name=from_account,json=fromAccount,proto3" json:"from_account,omitempty"`
	FromAmount     float64                `protobuf:"fixed64,3,opt,name=from_amount,json=fromAmount,proto3" json:"from_amount,omitempty"`
	FromCurrency   string                 `protobuf:"bytes,4,opt,name=from_currency,json=fromCurrency,proto3" json:"from_currency,omitempty"`
	ToAccount      string                 `protobuf:"bytes,5,opt,name=to_account,json=toAccount,proto3" json:"to_account,omitempty"`
	ToAmount       float64                `protobuf:"fixed64,6,opt,name=to_amount,json=toAmount,proto3" json:"to_amount,omitempty"`
	ToCurrency     string                 `protobuf:"bytes,7,opt,name=to_currency,json=toCurrency,proto3" json:"to_currency,omitempty"`
	Rate           string                 `protobuf:"bytes,8,opt,name=rate,proto3" json:"rate,omitempty"`
	ConversionType string                 `protobuf:"bytes,9,opt,name=conversion_type,json=conversionType,proto3" json:"conversion_type,omitempty"`
	Goal           string                 `protobuf:"bytes,10,opt,name=goal,proto3" json:"goal,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CreateConvertationReq) Reset() {
	*x = CreateConvertationReq{}
	mi := &file_specs_proto_foreign_activity_foreign_activity_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateConvertationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateConvertationReq) ProtoMessage() {}

func (x *CreateConvertationReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_foreign_activity_foreign_activity_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateConvertationReq.ProtoReflect.Descriptor instead.
func (*CreateConvertationReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_foreign_activity_foreign_activity_proto_rawDescGZIP(), []int{2}
}

func (x *CreateConvertationReq) GetIdempotencyId() string {
	if x != nil {
		return x.IdempotencyId
	}
	return ""
}

func (x *CreateConvertationReq) GetFromAccount() string {
	if x != nil {
		return x.FromAccount
	}
	return ""
}

func (x *CreateConvertationReq) GetFromAmount() float64 {
	if x != nil {
		return x.FromAmount
	}
	return 0
}

func (x *CreateConvertationReq) GetFromCurrency() string {
	if x != nil {
		return x.FromCurrency
	}
	return ""
}

func (x *CreateConvertationReq) GetToAccount() string {
	if x != nil {
		return x.ToAccount
	}
	return ""
}

func (x *CreateConvertationReq) GetToAmount() float64 {
	if x != nil {
		return x.ToAmount
	}
	return 0
}

func (x *CreateConvertationReq) GetToCurrency() string {
	if x != nil {
		return x.ToCurrency
	}
	return ""
}

func (x *CreateConvertationReq) GetRate() string {
	if x != nil {
		return x.Rate
	}
	return ""
}

func (x *CreateConvertationReq) GetConversionType() string {
	if x != nil {
		return x.ConversionType
	}
	return ""
}

func (x *CreateConvertationReq) GetGoal() string {
	if x != nil {
		return x.Goal
	}
	return ""
}

type CreateConvertationResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransactionId string                 `protobuf:"bytes,1,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
	Status        string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	Reason        *RejectionReason       `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateConvertationResp) Reset() {
	*x = CreateConvertationResp{}
	mi := &file_specs_proto_foreign_activity_foreign_activity_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateConvertationResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateConvertationResp) ProtoMessage() {}

func (x *CreateConvertationResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_foreign_activity_foreign_activity_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateConvertationResp.ProtoReflect.Descriptor instead.
func (*CreateConvertationResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_foreign_activity_foreign_activity_proto_rawDescGZIP(), []int{3}
}

func (x *CreateConvertationResp) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *CreateConvertationResp) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *CreateConvertationResp) GetReason() *RejectionReason {
	if x != nil {
		return x.Reason
	}
	return nil
}

type RejectionReason struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Title         string                 `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Code          string                 `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RejectionReason) Reset() {
	*x = RejectionReason{}
	mi := &file_specs_proto_foreign_activity_foreign_activity_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RejectionReason) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RejectionReason) ProtoMessage() {}

func (x *RejectionReason) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_foreign_activity_foreign_activity_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RejectionReason.ProtoReflect.Descriptor instead.
func (*RejectionReason) Descriptor() ([]byte, []int) {
	return file_specs_proto_foreign_activity_foreign_activity_proto_rawDescGZIP(), []int{4}
}

func (x *RejectionReason) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *RejectionReason) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RejectionReason) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type ConversionSumReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ToAmount      float64                `protobuf:"fixed64,1,opt,name=to_amount,json=toAmount,proto3" json:"to_amount,omitempty"`
	ToCurrency    string                 `protobuf:"bytes,2,opt,name=to_currency,json=toCurrency,proto3" json:"to_currency,omitempty"`
	FromCurrency  string                 `protobuf:"bytes,3,opt,name=from_currency,json=fromCurrency,proto3" json:"from_currency,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConversionSumReq) Reset() {
	*x = ConversionSumReq{}
	mi := &file_specs_proto_foreign_activity_foreign_activity_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConversionSumReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConversionSumReq) ProtoMessage() {}

func (x *ConversionSumReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_foreign_activity_foreign_activity_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConversionSumReq.ProtoReflect.Descriptor instead.
func (*ConversionSumReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_foreign_activity_foreign_activity_proto_rawDescGZIP(), []int{5}
}

func (x *ConversionSumReq) GetToAmount() float64 {
	if x != nil {
		return x.ToAmount
	}
	return 0
}

func (x *ConversionSumReq) GetToCurrency() string {
	if x != nil {
		return x.ToCurrency
	}
	return ""
}

func (x *ConversionSumReq) GetFromCurrency() string {
	if x != nil {
		return x.FromCurrency
	}
	return ""
}

type ConversionSumResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ToAmount      float64                `protobuf:"fixed64,1,opt,name=to_amount,json=toAmount,proto3" json:"to_amount,omitempty"`
	ToCurrency    string                 `protobuf:"bytes,2,opt,name=to_currency,json=toCurrency,proto3" json:"to_currency,omitempty"`
	FromAmount    float64                `protobuf:"fixed64,3,opt,name=from_amount,json=fromAmount,proto3" json:"from_amount,omitempty"`
	FromCurrency  string                 `protobuf:"bytes,4,opt,name=from_currency,json=fromCurrency,proto3" json:"from_currency,omitempty"`
	Rate          string                 `protobuf:"bytes,5,opt,name=rate,proto3" json:"rate,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConversionSumResp) Reset() {
	*x = ConversionSumResp{}
	mi := &file_specs_proto_foreign_activity_foreign_activity_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConversionSumResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConversionSumResp) ProtoMessage() {}

func (x *ConversionSumResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_foreign_activity_foreign_activity_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConversionSumResp.ProtoReflect.Descriptor instead.
func (*ConversionSumResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_foreign_activity_foreign_activity_proto_rawDescGZIP(), []int{6}
}

func (x *ConversionSumResp) GetToAmount() float64 {
	if x != nil {
		return x.ToAmount
	}
	return 0
}

func (x *ConversionSumResp) GetToCurrency() string {
	if x != nil {
		return x.ToCurrency
	}
	return ""
}

func (x *ConversionSumResp) GetFromAmount() float64 {
	if x != nil {
		return x.FromAmount
	}
	return 0
}

func (x *ConversionSumResp) GetFromCurrency() string {
	if x != nil {
		return x.FromCurrency
	}
	return ""
}

func (x *ConversionSumResp) GetRate() string {
	if x != nil {
		return x.Rate
	}
	return ""
}

var File_specs_proto_foreign_activity_foreign_activity_proto protoreflect.FileDescriptor

const file_specs_proto_foreign_activity_foreign_activity_proto_rawDesc = "" +
	"\n" +
	"3specs/proto/foreign-activity/foreign-activity.proto\x12\x0fforeignactivity\"\x10\n" +
	"\x0eHealthCheckReq\")\n" +
	"\x0fHealthCheckResp\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\"\xd5\x02\n" +
	"\x15CreateConvertationReq\x12%\n" +
	"\x0eidempotency_id\x18\x01 \x01(\tR\ridempotencyId\x12!\n" +
	"\ffrom_account\x18\x02 \x01(\tR\vfromAccount\x12\x1f\n" +
	"\vfrom_amount\x18\x03 \x01(\x01R\n" +
	"fromAmount\x12#\n" +
	"\rfrom_currency\x18\x04 \x01(\tR\ffromCurrency\x12\x1d\n" +
	"\n" +
	"to_account\x18\x05 \x01(\tR\ttoAccount\x12\x1b\n" +
	"\tto_amount\x18\x06 \x01(\x01R\btoAmount\x12\x1f\n" +
	"\vto_currency\x18\a \x01(\tR\n" +
	"toCurrency\x12\x12\n" +
	"\x04rate\x18\b \x01(\tR\x04rate\x12'\n" +
	"\x0fconversion_type\x18\t \x01(\tR\x0econversionType\x12\x12\n" +
	"\x04goal\x18\n" +
	" \x01(\tR\x04goal\"\x91\x01\n" +
	"\x16CreateConvertationResp\x12%\n" +
	"\x0etransaction_id\x18\x01 \x01(\tR\rtransactionId\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x128\n" +
	"\x06reason\x18\x03 \x01(\v2 .foreignactivity.RejectionReasonR\x06reason\"U\n" +
	"\x0fRejectionReason\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x12\n" +
	"\x04code\x18\x03 \x01(\tR\x04code\"u\n" +
	"\x10ConversionSumReq\x12\x1b\n" +
	"\tto_amount\x18\x01 \x01(\x01R\btoAmount\x12\x1f\n" +
	"\vto_currency\x18\x02 \x01(\tR\n" +
	"toCurrency\x12#\n" +
	"\rfrom_currency\x18\x03 \x01(\tR\ffromCurrency\"\xab\x01\n" +
	"\x11ConversionSumResp\x12\x1b\n" +
	"\tto_amount\x18\x01 \x01(\x01R\btoAmount\x12\x1f\n" +
	"\vto_currency\x18\x02 \x01(\tR\n" +
	"toCurrency\x12\x1f\n" +
	"\vfrom_amount\x18\x03 \x01(\x01R\n" +
	"fromAmount\x12#\n" +
	"\rfrom_currency\x18\x04 \x01(\tR\ffromCurrency\x12\x12\n" +
	"\x04rate\x18\x05 \x01(\tR\x04rate2\xa2\x02\n" +
	"\x0fForeignactivity\x12P\n" +
	"\vHealthCheck\x12\x1f.foreignactivity.HealthCheckReq\x1a .foreignactivity.HealthCheckResp\x12e\n" +
	"\x12CreateConvertation\x12&.foreignactivity.CreateConvertationReq\x1a'.foreignactivity.CreateConvertationResp\x12V\n" +
	"\rConversionSum\x12!.foreignactivity.ConversionSumReq\x1a\".foreignactivity.ConversionSumRespB\x1eZ\x1cspecs/proto/foreign-activityb\x06proto3"

var (
	file_specs_proto_foreign_activity_foreign_activity_proto_rawDescOnce sync.Once
	file_specs_proto_foreign_activity_foreign_activity_proto_rawDescData []byte
)

func file_specs_proto_foreign_activity_foreign_activity_proto_rawDescGZIP() []byte {
	file_specs_proto_foreign_activity_foreign_activity_proto_rawDescOnce.Do(func() {
		file_specs_proto_foreign_activity_foreign_activity_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_specs_proto_foreign_activity_foreign_activity_proto_rawDesc), len(file_specs_proto_foreign_activity_foreign_activity_proto_rawDesc)))
	})
	return file_specs_proto_foreign_activity_foreign_activity_proto_rawDescData
}

var file_specs_proto_foreign_activity_foreign_activity_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_specs_proto_foreign_activity_foreign_activity_proto_goTypes = []any{
	(*HealthCheckReq)(nil),         // 0: foreignactivity.HealthCheckReq
	(*HealthCheckResp)(nil),        // 1: foreignactivity.HealthCheckResp
	(*CreateConvertationReq)(nil),  // 2: foreignactivity.CreateConvertationReq
	(*CreateConvertationResp)(nil), // 3: foreignactivity.CreateConvertationResp
	(*RejectionReason)(nil),        // 4: foreignactivity.RejectionReason
	(*ConversionSumReq)(nil),       // 5: foreignactivity.ConversionSumReq
	(*ConversionSumResp)(nil),      // 6: foreignactivity.ConversionSumResp
}
var file_specs_proto_foreign_activity_foreign_activity_proto_depIdxs = []int32{
	4, // 0: foreignactivity.CreateConvertationResp.reason:type_name -> foreignactivity.RejectionReason
	0, // 1: foreignactivity.Foreignactivity.HealthCheck:input_type -> foreignactivity.HealthCheckReq
	2, // 2: foreignactivity.Foreignactivity.CreateConvertation:input_type -> foreignactivity.CreateConvertationReq
	5, // 3: foreignactivity.Foreignactivity.ConversionSum:input_type -> foreignactivity.ConversionSumReq
	1, // 4: foreignactivity.Foreignactivity.HealthCheck:output_type -> foreignactivity.HealthCheckResp
	3, // 5: foreignactivity.Foreignactivity.CreateConvertation:output_type -> foreignactivity.CreateConvertationResp
	6, // 6: foreignactivity.Foreignactivity.ConversionSum:output_type -> foreignactivity.ConversionSumResp
	4, // [4:7] is the sub-list for method output_type
	1, // [1:4] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_specs_proto_foreign_activity_foreign_activity_proto_init() }
func file_specs_proto_foreign_activity_foreign_activity_proto_init() {
	if File_specs_proto_foreign_activity_foreign_activity_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_specs_proto_foreign_activity_foreign_activity_proto_rawDesc), len(file_specs_proto_foreign_activity_foreign_activity_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_specs_proto_foreign_activity_foreign_activity_proto_goTypes,
		DependencyIndexes: file_specs_proto_foreign_activity_foreign_activity_proto_depIdxs,
		MessageInfos:      file_specs_proto_foreign_activity_foreign_activity_proto_msgTypes,
	}.Build()
	File_specs_proto_foreign_activity_foreign_activity_proto = out.File
	file_specs_proto_foreign_activity_foreign_activity_proto_goTypes = nil
	file_specs_proto_foreign_activity_foreign_activity_proto_depIdxs = nil
}
