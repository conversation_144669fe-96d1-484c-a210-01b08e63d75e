// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.32.1
// source: specs/proto/cards-accounts/cards-accounts.proto

package cards_accounts

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Cardsaccounts_HealthCheck_FullMethodName                       = "/cardsAccounts.Cardsaccounts/HealthCheck"
	Cardsaccounts_GetBalance_FullMethodName                        = "/cardsAccounts.Cardsaccounts/GetBalance"
	Cardsaccounts_GetCards_FullMethodName                          = "/cardsAccounts.Cardsaccounts/GetCards"
	Cardsaccounts_GetAccounts_FullMethodName                       = "/cardsAccounts.Cardsaccounts/GetAccounts"
	Cardsaccounts_GetAccount_FullMethodName                        = "/cardsAccounts.Cardsaccounts/GetAccount"
	Cardsaccounts_CreateAccount_FullMethodName                     = "/cardsAccounts.Cardsaccounts/CreateAccount"
	Cardsaccounts_SaveAccount_FullMethodName                       = "/cardsAccounts.Cardsaccounts/SaveAccount"
	Cardsaccounts_CreateVirtualCard_FullMethodName                 = "/cardsAccounts.Cardsaccounts/CreateVirtualCard"
	Cardsaccounts_GetAccountsSME_FullMethodName                    = "/cardsAccounts.Cardsaccounts/GetAccountsSME"
	Cardsaccounts_GetApplicationForSign_FullMethodName             = "/cardsAccounts.Cardsaccounts/GetApplicationForSign"
	Cardsaccounts_VerifyClient_FullMethodName                      = "/cardsAccounts.Cardsaccounts/VerifyClient"
	Cardsaccounts_GetAvailableAccountsByCurrencySME_FullMethodName = "/cardsAccounts.Cardsaccounts/GetAvailableAccountsByCurrencySME"
	Cardsaccounts_GetDocumentsForSign_FullMethodName               = "/cardsAccounts.Cardsaccounts/GetDocumentsForSign"
	Cardsaccounts_GetRequisitesUnmasked_FullMethodName             = "/cardsAccounts.Cardsaccounts/GetRequisitesUnmasked"
)

// CardsaccountsClient is the client API for Cardsaccounts service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CardsaccountsClient interface {
	HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error)
	// Получение баланса пользователя
	GetBalance(ctx context.Context, in *GetBalanceReq, opts ...grpc.CallOption) (*GetBalanceResp, error)
	// Получение списка счетов клиента
	GetCards(ctx context.Context, in *GetCardsRequest, opts ...grpc.CallOption) (*GetCardsResponse, error)
	// Получение списка счетов
	GetAccounts(ctx context.Context, in *GetAccountsRequest, opts ...grpc.CallOption) (*GetAccountsResponse, error)
	// Получение счета по ID
	GetAccount(ctx context.Context, in *GetAccountRequest, opts ...grpc.CallOption) (*GetAccountResponse, error)
	// Создание счета в Cards-Accounts
	CreateAccount(ctx context.Context, in *CreateAccountRequest, opts ...grpc.CallOption) (*CreateAccountResponse, error)
	// Обновление информации по счету в БД Cards-Accounts
	SaveAccount(ctx context.Context, in *SaveAccountRequest, opts ...grpc.CallOption) (*SaveAccountResponse, error)
	// Создание виртуальной карты
	CreateVirtualCard(ctx context.Context, in *CreateVirtualCardRequest, opts ...grpc.CallOption) (*CreateVirtualCardResponse, error)
	// Получение списка аккаунтов SME
	GetAccountsSME(ctx context.Context, in *GetAccountsSMERequest, opts ...grpc.CallOption) (*GetAccountsSMEResponse, error)
	// GetApplicationForSign Метод для генерации заявления на подписание при открытии продуктов (карт, счетов)
	GetApplicationForSign(ctx context.Context, in *GetApplicationForSignReq, opts ...grpc.CallOption) (*GetApplicationForSignResp, error)
	// VerifyClient Метод для получения данных о проверке клиента при открытии доп.счета ИП
	VerifyClient(ctx context.Context, in *VerifyClientRequest, opts ...grpc.CallOption) (*VerifyClientResponse, error)
	// Получение доступных к открытию счетов юзера в валютах
	GetAvailableAccountsByCurrencySME(ctx context.Context, in *GetAvailableAccountsByCurrencySMERequest, opts ...grpc.CallOption) (*GetAvailableAccountsByCurrencySMEResponse, error)
	// GetDocumentsForSign получение на подпись документов необходимых для открытия счета
	GetDocumentsForSign(ctx context.Context, in *GetDocumentsForSignRequest, opts ...grpc.CallOption) (*GetDocumentsForSignResponse, error)
	// Получение полных реквизитов карты
	GetRequisitesUnmasked(ctx context.Context, in *GetRequisitesUnmaskedRequest, opts ...grpc.CallOption) (*GetRequisitesUnmaskedResponse, error)
}

type cardsaccountsClient struct {
	cc grpc.ClientConnInterface
}

func NewCardsaccountsClient(cc grpc.ClientConnInterface) CardsaccountsClient {
	return &cardsaccountsClient{cc}
}

func (c *cardsaccountsClient) HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResp)
	err := c.cc.Invoke(ctx, Cardsaccounts_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardsaccountsClient) GetBalance(ctx context.Context, in *GetBalanceReq, opts ...grpc.CallOption) (*GetBalanceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBalanceResp)
	err := c.cc.Invoke(ctx, Cardsaccounts_GetBalance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardsaccountsClient) GetCards(ctx context.Context, in *GetCardsRequest, opts ...grpc.CallOption) (*GetCardsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCardsResponse)
	err := c.cc.Invoke(ctx, Cardsaccounts_GetCards_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardsaccountsClient) GetAccounts(ctx context.Context, in *GetAccountsRequest, opts ...grpc.CallOption) (*GetAccountsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAccountsResponse)
	err := c.cc.Invoke(ctx, Cardsaccounts_GetAccounts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardsaccountsClient) GetAccount(ctx context.Context, in *GetAccountRequest, opts ...grpc.CallOption) (*GetAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAccountResponse)
	err := c.cc.Invoke(ctx, Cardsaccounts_GetAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardsaccountsClient) CreateAccount(ctx context.Context, in *CreateAccountRequest, opts ...grpc.CallOption) (*CreateAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateAccountResponse)
	err := c.cc.Invoke(ctx, Cardsaccounts_CreateAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardsaccountsClient) SaveAccount(ctx context.Context, in *SaveAccountRequest, opts ...grpc.CallOption) (*SaveAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SaveAccountResponse)
	err := c.cc.Invoke(ctx, Cardsaccounts_SaveAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardsaccountsClient) CreateVirtualCard(ctx context.Context, in *CreateVirtualCardRequest, opts ...grpc.CallOption) (*CreateVirtualCardResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateVirtualCardResponse)
	err := c.cc.Invoke(ctx, Cardsaccounts_CreateVirtualCard_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardsaccountsClient) GetAccountsSME(ctx context.Context, in *GetAccountsSMERequest, opts ...grpc.CallOption) (*GetAccountsSMEResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAccountsSMEResponse)
	err := c.cc.Invoke(ctx, Cardsaccounts_GetAccountsSME_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardsaccountsClient) GetApplicationForSign(ctx context.Context, in *GetApplicationForSignReq, opts ...grpc.CallOption) (*GetApplicationForSignResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetApplicationForSignResp)
	err := c.cc.Invoke(ctx, Cardsaccounts_GetApplicationForSign_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardsaccountsClient) VerifyClient(ctx context.Context, in *VerifyClientRequest, opts ...grpc.CallOption) (*VerifyClientResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VerifyClientResponse)
	err := c.cc.Invoke(ctx, Cardsaccounts_VerifyClient_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardsaccountsClient) GetAvailableAccountsByCurrencySME(ctx context.Context, in *GetAvailableAccountsByCurrencySMERequest, opts ...grpc.CallOption) (*GetAvailableAccountsByCurrencySMEResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAvailableAccountsByCurrencySMEResponse)
	err := c.cc.Invoke(ctx, Cardsaccounts_GetAvailableAccountsByCurrencySME_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardsaccountsClient) GetDocumentsForSign(ctx context.Context, in *GetDocumentsForSignRequest, opts ...grpc.CallOption) (*GetDocumentsForSignResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDocumentsForSignResponse)
	err := c.cc.Invoke(ctx, Cardsaccounts_GetDocumentsForSign_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardsaccountsClient) GetRequisitesUnmasked(ctx context.Context, in *GetRequisitesUnmaskedRequest, opts ...grpc.CallOption) (*GetRequisitesUnmaskedResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetRequisitesUnmaskedResponse)
	err := c.cc.Invoke(ctx, Cardsaccounts_GetRequisitesUnmasked_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CardsaccountsServer is the server API for Cardsaccounts service.
// All implementations must embed UnimplementedCardsaccountsServer
// for forward compatibility.
type CardsaccountsServer interface {
	HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error)
	// Получение баланса пользователя
	GetBalance(context.Context, *GetBalanceReq) (*GetBalanceResp, error)
	// Получение списка счетов клиента
	GetCards(context.Context, *GetCardsRequest) (*GetCardsResponse, error)
	// Получение списка счетов
	GetAccounts(context.Context, *GetAccountsRequest) (*GetAccountsResponse, error)
	// Получение счета по ID
	GetAccount(context.Context, *GetAccountRequest) (*GetAccountResponse, error)
	// Создание счета в Cards-Accounts
	CreateAccount(context.Context, *CreateAccountRequest) (*CreateAccountResponse, error)
	// Обновление информации по счету в БД Cards-Accounts
	SaveAccount(context.Context, *SaveAccountRequest) (*SaveAccountResponse, error)
	// Создание виртуальной карты
	CreateVirtualCard(context.Context, *CreateVirtualCardRequest) (*CreateVirtualCardResponse, error)
	// Получение списка аккаунтов SME
	GetAccountsSME(context.Context, *GetAccountsSMERequest) (*GetAccountsSMEResponse, error)
	// GetApplicationForSign Метод для генерации заявления на подписание при открытии продуктов (карт, счетов)
	GetApplicationForSign(context.Context, *GetApplicationForSignReq) (*GetApplicationForSignResp, error)
	// VerifyClient Метод для получения данных о проверке клиента при открытии доп.счета ИП
	VerifyClient(context.Context, *VerifyClientRequest) (*VerifyClientResponse, error)
	// Получение доступных к открытию счетов юзера в валютах
	GetAvailableAccountsByCurrencySME(context.Context, *GetAvailableAccountsByCurrencySMERequest) (*GetAvailableAccountsByCurrencySMEResponse, error)
	// GetDocumentsForSign получение на подпись документов необходимых для открытия счета
	GetDocumentsForSign(context.Context, *GetDocumentsForSignRequest) (*GetDocumentsForSignResponse, error)
	// Получение полных реквизитов карты
	GetRequisitesUnmasked(context.Context, *GetRequisitesUnmaskedRequest) (*GetRequisitesUnmaskedResponse, error)
	mustEmbedUnimplementedCardsaccountsServer()
}

// UnimplementedCardsaccountsServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCardsaccountsServer struct{}

func (UnimplementedCardsaccountsServer) HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedCardsaccountsServer) GetBalance(context.Context, *GetBalanceReq) (*GetBalanceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBalance not implemented")
}
func (UnimplementedCardsaccountsServer) GetCards(context.Context, *GetCardsRequest) (*GetCardsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCards not implemented")
}
func (UnimplementedCardsaccountsServer) GetAccounts(context.Context, *GetAccountsRequest) (*GetAccountsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccounts not implemented")
}
func (UnimplementedCardsaccountsServer) GetAccount(context.Context, *GetAccountRequest) (*GetAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccount not implemented")
}
func (UnimplementedCardsaccountsServer) CreateAccount(context.Context, *CreateAccountRequest) (*CreateAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAccount not implemented")
}
func (UnimplementedCardsaccountsServer) SaveAccount(context.Context, *SaveAccountRequest) (*SaveAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveAccount not implemented")
}
func (UnimplementedCardsaccountsServer) CreateVirtualCard(context.Context, *CreateVirtualCardRequest) (*CreateVirtualCardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateVirtualCard not implemented")
}
func (UnimplementedCardsaccountsServer) GetAccountsSME(context.Context, *GetAccountsSMERequest) (*GetAccountsSMEResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountsSME not implemented")
}
func (UnimplementedCardsaccountsServer) GetApplicationForSign(context.Context, *GetApplicationForSignReq) (*GetApplicationForSignResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetApplicationForSign not implemented")
}
func (UnimplementedCardsaccountsServer) VerifyClient(context.Context, *VerifyClientRequest) (*VerifyClientResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyClient not implemented")
}
func (UnimplementedCardsaccountsServer) GetAvailableAccountsByCurrencySME(context.Context, *GetAvailableAccountsByCurrencySMERequest) (*GetAvailableAccountsByCurrencySMEResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAvailableAccountsByCurrencySME not implemented")
}
func (UnimplementedCardsaccountsServer) GetDocumentsForSign(context.Context, *GetDocumentsForSignRequest) (*GetDocumentsForSignResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDocumentsForSign not implemented")
}
func (UnimplementedCardsaccountsServer) GetRequisitesUnmasked(context.Context, *GetRequisitesUnmaskedRequest) (*GetRequisitesUnmaskedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRequisitesUnmasked not implemented")
}
func (UnimplementedCardsaccountsServer) mustEmbedUnimplementedCardsaccountsServer() {}
func (UnimplementedCardsaccountsServer) testEmbeddedByValue()                       {}

// UnsafeCardsaccountsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CardsaccountsServer will
// result in compilation errors.
type UnsafeCardsaccountsServer interface {
	mustEmbedUnimplementedCardsaccountsServer()
}

func RegisterCardsaccountsServer(s grpc.ServiceRegistrar, srv CardsaccountsServer) {
	// If the following call pancis, it indicates UnimplementedCardsaccountsServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Cardsaccounts_ServiceDesc, srv)
}

func _Cardsaccounts_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardsaccountsServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cardsaccounts_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardsaccountsServer).HealthCheck(ctx, req.(*HealthCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cardsaccounts_GetBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBalanceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardsaccountsServer).GetBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cardsaccounts_GetBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardsaccountsServer).GetBalance(ctx, req.(*GetBalanceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cardsaccounts_GetCards_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCardsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardsaccountsServer).GetCards(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cardsaccounts_GetCards_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardsaccountsServer).GetCards(ctx, req.(*GetCardsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cardsaccounts_GetAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardsaccountsServer).GetAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cardsaccounts_GetAccounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardsaccountsServer).GetAccounts(ctx, req.(*GetAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cardsaccounts_GetAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardsaccountsServer).GetAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cardsaccounts_GetAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardsaccountsServer).GetAccount(ctx, req.(*GetAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cardsaccounts_CreateAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardsaccountsServer).CreateAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cardsaccounts_CreateAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardsaccountsServer).CreateAccount(ctx, req.(*CreateAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cardsaccounts_SaveAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardsaccountsServer).SaveAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cardsaccounts_SaveAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardsaccountsServer).SaveAccount(ctx, req.(*SaveAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cardsaccounts_CreateVirtualCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateVirtualCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardsaccountsServer).CreateVirtualCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cardsaccounts_CreateVirtualCard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardsaccountsServer).CreateVirtualCard(ctx, req.(*CreateVirtualCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cardsaccounts_GetAccountsSME_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountsSMERequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardsaccountsServer).GetAccountsSME(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cardsaccounts_GetAccountsSME_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardsaccountsServer).GetAccountsSME(ctx, req.(*GetAccountsSMERequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cardsaccounts_GetApplicationForSign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApplicationForSignReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardsaccountsServer).GetApplicationForSign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cardsaccounts_GetApplicationForSign_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardsaccountsServer).GetApplicationForSign(ctx, req.(*GetApplicationForSignReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cardsaccounts_VerifyClient_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyClientRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardsaccountsServer).VerifyClient(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cardsaccounts_VerifyClient_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardsaccountsServer).VerifyClient(ctx, req.(*VerifyClientRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cardsaccounts_GetAvailableAccountsByCurrencySME_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAvailableAccountsByCurrencySMERequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardsaccountsServer).GetAvailableAccountsByCurrencySME(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cardsaccounts_GetAvailableAccountsByCurrencySME_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardsaccountsServer).GetAvailableAccountsByCurrencySME(ctx, req.(*GetAvailableAccountsByCurrencySMERequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cardsaccounts_GetDocumentsForSign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDocumentsForSignRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardsaccountsServer).GetDocumentsForSign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cardsaccounts_GetDocumentsForSign_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardsaccountsServer).GetDocumentsForSign(ctx, req.(*GetDocumentsForSignRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Cardsaccounts_GetRequisitesUnmasked_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRequisitesUnmaskedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardsaccountsServer).GetRequisitesUnmasked(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Cardsaccounts_GetRequisitesUnmasked_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardsaccountsServer).GetRequisitesUnmasked(ctx, req.(*GetRequisitesUnmaskedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Cardsaccounts_ServiceDesc is the grpc.ServiceDesc for Cardsaccounts service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Cardsaccounts_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "cardsAccounts.Cardsaccounts",
	HandlerType: (*CardsaccountsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HealthCheck",
			Handler:    _Cardsaccounts_HealthCheck_Handler,
		},
		{
			MethodName: "GetBalance",
			Handler:    _Cardsaccounts_GetBalance_Handler,
		},
		{
			MethodName: "GetCards",
			Handler:    _Cardsaccounts_GetCards_Handler,
		},
		{
			MethodName: "GetAccounts",
			Handler:    _Cardsaccounts_GetAccounts_Handler,
		},
		{
			MethodName: "GetAccount",
			Handler:    _Cardsaccounts_GetAccount_Handler,
		},
		{
			MethodName: "CreateAccount",
			Handler:    _Cardsaccounts_CreateAccount_Handler,
		},
		{
			MethodName: "SaveAccount",
			Handler:    _Cardsaccounts_SaveAccount_Handler,
		},
		{
			MethodName: "CreateVirtualCard",
			Handler:    _Cardsaccounts_CreateVirtualCard_Handler,
		},
		{
			MethodName: "GetAccountsSME",
			Handler:    _Cardsaccounts_GetAccountsSME_Handler,
		},
		{
			MethodName: "GetApplicationForSign",
			Handler:    _Cardsaccounts_GetApplicationForSign_Handler,
		},
		{
			MethodName: "VerifyClient",
			Handler:    _Cardsaccounts_VerifyClient_Handler,
		},
		{
			MethodName: "GetAvailableAccountsByCurrencySME",
			Handler:    _Cardsaccounts_GetAvailableAccountsByCurrencySME_Handler,
		},
		{
			MethodName: "GetDocumentsForSign",
			Handler:    _Cardsaccounts_GetDocumentsForSign_Handler,
		},
		{
			MethodName: "GetRequisitesUnmasked",
			Handler:    _Cardsaccounts_GetRequisitesUnmasked_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "specs/proto/cards-accounts/cards-accounts.proto",
}
