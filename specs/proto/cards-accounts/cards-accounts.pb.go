// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v6.32.1
// source: specs/proto/cards-accounts/cards-accounts.proto

package cards_accounts

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FinContractStatus int32

const (
	FinContractStatus_FCS_UNSPECIFIED FinContractStatus = 0
	FinContractStatus_FCS_IN_OPENING  FinContractStatus = 1
	FinContractStatus_FCS_ACTIVE      FinContractStatus = 2
	FinContractStatus_FCS_BLOCKED     FinContractStatus = 3
	FinContractStatus_FCS_CLOSED      FinContractStatus = 4
)

// Enum value maps for FinContractStatus.
var (
	FinContractStatus_name = map[int32]string{
		0: "FCS_UNSPECIFIED",
		1: "FCS_IN_OPENING",
		2: "FCS_ACTIVE",
		3: "FCS_BLOCKED",
		4: "FCS_CLOSED",
	}
	FinContractStatus_value = map[string]int32{
		"FCS_UNSPECIFIED": 0,
		"FCS_IN_OPENING":  1,
		"FCS_ACTIVE":      2,
		"FCS_BLOCKED":     3,
		"FCS_CLOSED":      4,
	}
)

func (x FinContractStatus) Enum() *FinContractStatus {
	p := new(FinContractStatus)
	*p = x
	return p
}

func (x FinContractStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FinContractStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_specs_proto_cards_accounts_cards_accounts_proto_enumTypes[0].Descriptor()
}

func (FinContractStatus) Type() protoreflect.EnumType {
	return &file_specs_proto_cards_accounts_cards_accounts_proto_enumTypes[0]
}

func (x FinContractStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FinContractStatus.Descriptor instead.
func (FinContractStatus) EnumDescriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{0}
}

type ApplicationType int32

const (
	ApplicationType_cardIssueApplication                 ApplicationType = 0 // cardIssueApplication - Заявление на выпуск карты
	ApplicationType_additionalAccountsOpeningApplication ApplicationType = 1 // additionalAccountsOpeningApplication - Заявление на открытие дополнительных счетов
)

// Enum value maps for ApplicationType.
var (
	ApplicationType_name = map[int32]string{
		0: "cardIssueApplication",
		1: "additionalAccountsOpeningApplication",
	}
	ApplicationType_value = map[string]int32{
		"cardIssueApplication":                 0,
		"additionalAccountsOpeningApplication": 1,
	}
)

func (x ApplicationType) Enum() *ApplicationType {
	p := new(ApplicationType)
	*p = x
	return p
}

func (x ApplicationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ApplicationType) Descriptor() protoreflect.EnumDescriptor {
	return file_specs_proto_cards_accounts_cards_accounts_proto_enumTypes[1].Descriptor()
}

func (ApplicationType) Type() protoreflect.EnumType {
	return &file_specs_proto_cards_accounts_cards_accounts_proto_enumTypes[1]
}

func (x ApplicationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ApplicationType.Descriptor instead.
func (ApplicationType) EnumDescriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{1}
}

type HealthCheckReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckReq) Reset() {
	*x = HealthCheckReq{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckReq) ProtoMessage() {}

func (x *HealthCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckReq.ProtoReflect.Descriptor instead.
func (*HealthCheckReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{0}
}

type HealthCheckResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckResp) Reset() {
	*x = HealthCheckResp{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResp) ProtoMessage() {}

func (x *HealthCheckResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResp.ProtoReflect.Descriptor instead.
func (*HealthCheckResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{1}
}

func (x *HealthCheckResp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

// Запрос на получение баланса
type GetBalanceReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBalanceReq) Reset() {
	*x = GetBalanceReq{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBalanceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBalanceReq) ProtoMessage() {}

func (x *GetBalanceReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBalanceReq.ProtoReflect.Descriptor instead.
func (*GetBalanceReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{2}
}

// Ответ на запрос получения баланса
type GetBalanceResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Balance       float64                `protobuf:"fixed64,1,opt,name=balance,proto3" json:"balance,omitempty"`
	Currency      string                 `protobuf:"bytes,2,opt,name=currency,proto3" json:"currency,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBalanceResp) Reset() {
	*x = GetBalanceResp{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBalanceResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBalanceResp) ProtoMessage() {}

func (x *GetBalanceResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBalanceResp.ProtoReflect.Descriptor instead.
func (*GetBalanceResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{3}
}

func (x *GetBalanceResp) GetBalance() float64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

func (x *GetBalanceResp) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

// Запрос на создание счета в Cards-Accounts
type CreateAccountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserIIN       string                 `protobuf:"bytes,1,opt,name=userIIN,proto3" json:"userIIN,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAccountRequest) Reset() {
	*x = CreateAccountRequest{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAccountRequest) ProtoMessage() {}

func (x *CreateAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAccountRequest.ProtoReflect.Descriptor instead.
func (*CreateAccountRequest) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{4}
}

func (x *CreateAccountRequest) GetUserIIN() string {
	if x != nil {
		return x.UserIIN
	}
	return ""
}

// Запрос на создание виртуальной карты
type CreateVirtualCardRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserID        string                 `protobuf:"bytes,1,opt,name=userID,proto3" json:"userID,omitempty"` // Идентификатор пользователя, для которого создается виртуальная карта
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateVirtualCardRequest) Reset() {
	*x = CreateVirtualCardRequest{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateVirtualCardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateVirtualCardRequest) ProtoMessage() {}

func (x *CreateVirtualCardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateVirtualCardRequest.ProtoReflect.Descriptor instead.
func (*CreateVirtualCardRequest) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{5}
}

func (x *CreateVirtualCardRequest) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

type CreateVirtualCardResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CardID        string                 `protobuf:"bytes,1,opt,name=cardID,proto3" json:"cardID,omitempty"`        // Идентификатор созданной виртуальной карты
	AccountID     string                 `protobuf:"bytes,2,opt,name=accountID,proto3" json:"accountID,omitempty"`  // Идентификатор счета, связанного с виртуальной картой
	IsSuccess     bool                   `protobuf:"varint,3,opt,name=isSuccess,proto3" json:"isSuccess,omitempty"` // Флаг успешности операции
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateVirtualCardResponse) Reset() {
	*x = CreateVirtualCardResponse{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateVirtualCardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateVirtualCardResponse) ProtoMessage() {}

func (x *CreateVirtualCardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateVirtualCardResponse.ProtoReflect.Descriptor instead.
func (*CreateVirtualCardResponse) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{6}
}

func (x *CreateVirtualCardResponse) GetCardID() string {
	if x != nil {
		return x.CardID
	}
	return ""
}

func (x *CreateVirtualCardResponse) GetAccountID() string {
	if x != nil {
		return x.AccountID
	}
	return ""
}

func (x *CreateVirtualCardResponse) GetIsSuccess() bool {
	if x != nil {
		return x.IsSuccess
	}
	return false
}

// Ответ на запрос на создание счета в Cards-Accounts
type CreateAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAccountResponse) Reset() {
	*x = CreateAccountResponse{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAccountResponse) ProtoMessage() {}

func (x *CreateAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAccountResponse.ProtoReflect.Descriptor instead.
func (*CreateAccountResponse) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{7}
}

// Запрос на получение списка счетов клиента
type GetCardsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCardsRequest) Reset() {
	*x = GetCardsRequest{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCardsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCardsRequest) ProtoMessage() {}

func (x *GetCardsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCardsRequest.ProtoReflect.Descriptor instead.
func (*GetCardsRequest) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{8}
}

// Запрос на получение списка счетов
type GetAccountsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountsRequest) Reset() {
	*x = GetAccountsRequest{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountsRequest) ProtoMessage() {}

func (x *GetAccountsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountsRequest.ProtoReflect.Descriptor instead.
func (*GetAccountsRequest) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{9}
}

// Ответ на запрос на получение списка счетов
type GetAccountsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Accounts      []*Account             `protobuf:"bytes,1,rep,name=accounts,proto3" json:"accounts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountsResponse) Reset() {
	*x = GetAccountsResponse{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountsResponse) ProtoMessage() {}

func (x *GetAccountsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountsResponse.ProtoReflect.Descriptor instead.
func (*GetAccountsResponse) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{10}
}

func (x *GetAccountsResponse) GetAccounts() []*Account {
	if x != nil {
		return x.Accounts
	}
	return nil
}

// Ответ на запрос на получение списка счетов и карт клиента
type GetCardsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Accounts      []*Account             `protobuf:"bytes,1,rep,name=accounts,proto3" json:"accounts,omitempty"`
	Cards         []*Card                `protobuf:"bytes,2,rep,name=cards,proto3" json:"cards,omitempty"` // Список карт клиента
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCardsResponse) Reset() {
	*x = GetCardsResponse{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCardsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCardsResponse) ProtoMessage() {}

func (x *GetCardsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCardsResponse.ProtoReflect.Descriptor instead.
func (*GetCardsResponse) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{11}
}

func (x *GetCardsResponse) GetAccounts() []*Account {
	if x != nil {
		return x.Accounts
	}
	return nil
}

func (x *GetCardsResponse) GetCards() []*Card {
	if x != nil {
		return x.Cards
	}
	return nil
}

type Card struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	ID                 string                 `protobuf:"bytes,1,opt,name=ID,proto3" json:"ID,omitempty"`                                 // Идентификатор карты
	AttachedAccountID  string                 `protobuf:"bytes,2,opt,name=attachedAccountID,proto3" json:"attachedAccountID,omitempty"`   // Идентификатор счета, к которому привязана карта
	CardClass          string                 `protobuf:"bytes,3,opt,name=CardClass,proto3" json:"CardClass,omitempty"`                   // Класс карты (например, Mastercard Platinum и т.д.)
	CardType           string                 `protobuf:"bytes,4,opt,name=CardType,proto3" json:"CardType,omitempty"`                     // Тип карты (например, VIRTUAL и т.д.)
	PaymentSystem      string                 `protobuf:"bytes,5,opt,name=paymentSystem,proto3" json:"paymentSystem,omitempty"`           // Платежная система карты (например, VISA, Mastercard и т.д.)
	Status             string                 `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`                         // Статус карты (например, ACTIVE и т.д.)
	TokenizationStatus string                 `protobuf:"bytes,7,opt,name=tokenizationStatus,proto3" json:"tokenizationStatus,omitempty"` // Статус токенизации карты
	EmbossingName      string                 `protobuf:"bytes,8,opt,name=embossingName,proto3" json:"embossingName,omitempty"`           // Имя на карте
	MaskedPan          string                 `protobuf:"bytes,9,opt,name=maskedPan,proto3" json:"maskedPan,omitempty"`                   // Маскированный PAN карты
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *Card) Reset() {
	*x = Card{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Card) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Card) ProtoMessage() {}

func (x *Card) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Card.ProtoReflect.Descriptor instead.
func (*Card) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{12}
}

func (x *Card) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

func (x *Card) GetAttachedAccountID() string {
	if x != nil {
		return x.AttachedAccountID
	}
	return ""
}

func (x *Card) GetCardClass() string {
	if x != nil {
		return x.CardClass
	}
	return ""
}

func (x *Card) GetCardType() string {
	if x != nil {
		return x.CardType
	}
	return ""
}

func (x *Card) GetPaymentSystem() string {
	if x != nil {
		return x.PaymentSystem
	}
	return ""
}

func (x *Card) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Card) GetTokenizationStatus() string {
	if x != nil {
		return x.TokenizationStatus
	}
	return ""
}

func (x *Card) GetEmbossingName() string {
	if x != nil {
		return x.EmbossingName
	}
	return ""
}

func (x *Card) GetMaskedPan() string {
	if x != nil {
		return x.MaskedPan
	}
	return ""
}

// Запрос на получение счета по ID
type GetAccountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccountId     string                 `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountRequest) Reset() {
	*x = GetAccountRequest{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountRequest) ProtoMessage() {}

func (x *GetAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountRequest.ProtoReflect.Descriptor instead.
func (*GetAccountRequest) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{13}
}

func (x *GetAccountRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

// Ответ на запрос на получение счета по ID
type GetAccountResponse struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ID               string                 `protobuf:"bytes,1,opt,name=ID,proto3" json:"ID,omitempty"`
	DocumentID       string                 `protobuf:"bytes,2,opt,name=documentID,proto3" json:"documentID,omitempty"`
	DocumentNumber   string                 `protobuf:"bytes,3,opt,name=documentNumber,proto3" json:"documentNumber,omitempty"`
	Type             string                 `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	Status           string                 `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	Currency         string                 `protobuf:"bytes,6,opt,name=currency,proto3" json:"currency,omitempty"`
	Iban             string                 `protobuf:"bytes,7,opt,name=iban,proto3" json:"iban,omitempty"`
	OpenDate         string                 `protobuf:"bytes,8,opt,name=openDate,proto3" json:"openDate,omitempty"`
	CloseDate        string                 `protobuf:"bytes,9,opt,name=closeDate,proto3" json:"closeDate,omitempty"`
	Balance          float64                `protobuf:"fixed64,10,opt,name=balance,proto3" json:"balance,omitempty"`
	BalanceNatval    float64                `protobuf:"fixed64,11,opt,name=balanceNatval,proto3" json:"balanceNatval,omitempty"`
	PlanSum          float64                `protobuf:"fixed64,12,opt,name=planSum,proto3" json:"planSum,omitempty"`
	AvailableBalance float64                `protobuf:"fixed64,13,opt,name=availableBalance,proto3" json:"availableBalance,omitempty"`
	Arrest           *Arrest                `protobuf:"bytes,14,opt,name=arrest,proto3" json:"arrest,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *GetAccountResponse) Reset() {
	*x = GetAccountResponse{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountResponse) ProtoMessage() {}

func (x *GetAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountResponse.ProtoReflect.Descriptor instead.
func (*GetAccountResponse) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{14}
}

func (x *GetAccountResponse) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

func (x *GetAccountResponse) GetDocumentID() string {
	if x != nil {
		return x.DocumentID
	}
	return ""
}

func (x *GetAccountResponse) GetDocumentNumber() string {
	if x != nil {
		return x.DocumentNumber
	}
	return ""
}

func (x *GetAccountResponse) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GetAccountResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetAccountResponse) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *GetAccountResponse) GetIban() string {
	if x != nil {
		return x.Iban
	}
	return ""
}

func (x *GetAccountResponse) GetOpenDate() string {
	if x != nil {
		return x.OpenDate
	}
	return ""
}

func (x *GetAccountResponse) GetCloseDate() string {
	if x != nil {
		return x.CloseDate
	}
	return ""
}

func (x *GetAccountResponse) GetBalance() float64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

func (x *GetAccountResponse) GetBalanceNatval() float64 {
	if x != nil {
		return x.BalanceNatval
	}
	return 0
}

func (x *GetAccountResponse) GetPlanSum() float64 {
	if x != nil {
		return x.PlanSum
	}
	return 0
}

func (x *GetAccountResponse) GetAvailableBalance() float64 {
	if x != nil {
		return x.AvailableBalance
	}
	return 0
}

func (x *GetAccountResponse) GetArrest() *Arrest {
	if x != nil {
		return x.Arrest
	}
	return nil
}

// Структура счета
type Account struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	ID                  string                 `protobuf:"bytes,1,opt,name=ID,proto3" json:"ID,omitempty"`
	DocumentID          string                 `protobuf:"bytes,2,opt,name=documentID,proto3" json:"documentID,omitempty"`
	DocumentNumber      string                 `protobuf:"bytes,3,opt,name=documentNumber,proto3" json:"documentNumber,omitempty"`
	Type                string                 `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	Status              string                 `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	Currency            string                 `protobuf:"bytes,6,opt,name=currency,proto3" json:"currency,omitempty"`
	Iban                string                 `protobuf:"bytes,7,opt,name=iban,proto3" json:"iban,omitempty"`
	OpenDate            string                 `protobuf:"bytes,8,opt,name=openDate,proto3" json:"openDate,omitempty"`
	CloseDate           string                 `protobuf:"bytes,9,opt,name=closeDate,proto3" json:"closeDate,omitempty"`
	Balance             float64                `protobuf:"fixed64,10,opt,name=balance,proto3" json:"balance,omitempty"`
	BalanceNatval       float64                `protobuf:"fixed64,11,opt,name=balanceNatval,proto3" json:"balanceNatval,omitempty"`
	PlanSum             float64                `protobuf:"fixed64,12,opt,name=planSum,proto3" json:"planSum,omitempty"`
	AvailableBalance    float64                `protobuf:"fixed64,13,opt,name=availableBalance,proto3" json:"availableBalance,omitempty"`
	FilialCode          string                 `protobuf:"bytes,14,opt,name=filialCode,proto3" json:"filialCode,omitempty"`
	BranchCode          string                 `protobuf:"bytes,15,opt,name=branchCode,proto3" json:"branchCode,omitempty"`
	Arrest              *Arrest                `protobuf:"bytes,16,opt,name=arrest,proto3" json:"arrest,omitempty"`
	DeaReferenceID      string                 `protobuf:"bytes,17,opt,name=deaReferenceID,proto3" json:"deaReferenceID,omitempty"`
	ClientType          string                 `protobuf:"bytes,18,opt,name=clientType,proto3" json:"clientType,omitempty"`
	IsFinContractOpened bool                   `protobuf:"varint,19,opt,name=isFinContractOpened,proto3" json:"isFinContractOpened,omitempty"`
	FinContractID       string                 `protobuf:"bytes,20,opt,name=finContractID,proto3" json:"finContractID,omitempty"`
	FinContractStatus   FinContractStatus      `protobuf:"varint,21,opt,name=finContractStatus,proto3,enum=cardsAccounts.FinContractStatus" json:"finContractStatus,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *Account) Reset() {
	*x = Account{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Account) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Account) ProtoMessage() {}

func (x *Account) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Account.ProtoReflect.Descriptor instead.
func (*Account) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{15}
}

func (x *Account) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

func (x *Account) GetDocumentID() string {
	if x != nil {
		return x.DocumentID
	}
	return ""
}

func (x *Account) GetDocumentNumber() string {
	if x != nil {
		return x.DocumentNumber
	}
	return ""
}

func (x *Account) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Account) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Account) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *Account) GetIban() string {
	if x != nil {
		return x.Iban
	}
	return ""
}

func (x *Account) GetOpenDate() string {
	if x != nil {
		return x.OpenDate
	}
	return ""
}

func (x *Account) GetCloseDate() string {
	if x != nil {
		return x.CloseDate
	}
	return ""
}

func (x *Account) GetBalance() float64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

func (x *Account) GetBalanceNatval() float64 {
	if x != nil {
		return x.BalanceNatval
	}
	return 0
}

func (x *Account) GetPlanSum() float64 {
	if x != nil {
		return x.PlanSum
	}
	return 0
}

func (x *Account) GetAvailableBalance() float64 {
	if x != nil {
		return x.AvailableBalance
	}
	return 0
}

func (x *Account) GetFilialCode() string {
	if x != nil {
		return x.FilialCode
	}
	return ""
}

func (x *Account) GetBranchCode() string {
	if x != nil {
		return x.BranchCode
	}
	return ""
}

func (x *Account) GetArrest() *Arrest {
	if x != nil {
		return x.Arrest
	}
	return nil
}

func (x *Account) GetDeaReferenceID() string {
	if x != nil {
		return x.DeaReferenceID
	}
	return ""
}

func (x *Account) GetClientType() string {
	if x != nil {
		return x.ClientType
	}
	return ""
}

func (x *Account) GetIsFinContractOpened() bool {
	if x != nil {
		return x.IsFinContractOpened
	}
	return false
}

func (x *Account) GetFinContractID() string {
	if x != nil {
		return x.FinContractID
	}
	return ""
}

func (x *Account) GetFinContractStatus() FinContractStatus {
	if x != nil {
		return x.FinContractStatus
	}
	return FinContractStatus_FCS_UNSPECIFIED
}

// Запрос на обновление информации по счету в БД Cards-Accounts
type SaveAccountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Account       *AccountSaveToDB       `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaveAccountRequest) Reset() {
	*x = SaveAccountRequest{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAccountRequest) ProtoMessage() {}

func (x *SaveAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAccountRequest.ProtoReflect.Descriptor instead.
func (*SaveAccountRequest) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{16}
}

func (x *SaveAccountRequest) GetAccount() *AccountSaveToDB {
	if x != nil {
		return x.Account
	}
	return nil
}

// Ответ на запрос на обновление информации по счету в БД Cards-Accounts
type SaveAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsSuccess     bool                   `protobuf:"varint,1,opt,name=isSuccess,proto3" json:"isSuccess,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaveAccountResponse) Reset() {
	*x = SaveAccountResponse{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAccountResponse) ProtoMessage() {}

func (x *SaveAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAccountResponse.ProtoReflect.Descriptor instead.
func (*SaveAccountResponse) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{17}
}

func (x *SaveAccountResponse) GetIsSuccess() bool {
	if x != nil {
		return x.IsSuccess
	}
	return false
}

// Структура счета для сохранения в БД Cards-Accounts
type AccountSaveToDB struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Balance          *AccountBalance        `protobuf:"bytes,1,opt,name=balance,proto3" json:"balance,omitempty"`
	Client           *AccountClient         `protobuf:"bytes,2,opt,name=client,proto3" json:"client,omitempty"`
	Date             *AccountDate           `protobuf:"bytes,3,opt,name=date,proto3" json:"date,omitempty"`
	Document         *AccountDocument       `protobuf:"bytes,4,opt,name=document,proto3" json:"document,omitempty"`
	Arrest           *Arrest                `protobuf:"bytes,5,opt,name=arrest,proto3" json:"arrest,omitempty"`
	Currency         string                 `protobuf:"bytes,6,opt,name=currency,proto3" json:"currency,omitempty"`                   // Валюта счета
	DeaReferenceID   string                 `protobuf:"bytes,7,opt,name=deaReferenceID,proto3" json:"deaReferenceID,omitempty"`       // dea_reference_id
	Iban             string                 `protobuf:"bytes,8,opt,name=iban,proto3" json:"iban,omitempty"`                           // iban
	ID               string                 `protobuf:"bytes,9,opt,name=ID,proto3" json:"ID,omitempty"`                               // uuid account
	Origin           string                 `protobuf:"bytes,10,opt,name=origin,proto3" json:"origin,omitempty"`                      // origin
	ShortName        string                 `protobuf:"bytes,11,opt,name=shortName,proto3" json:"shortName,omitempty"`                // Краткое наименование счета
	Status           string                 `protobuf:"bytes,12,opt,name=status,proto3" json:"status,omitempty"`                      // TODO: Кажется рассинхрон по данным и ТТ https://rmrkz.atlassian.net/wiki/spaces/ZMNRET/pages/********/GET+user+cards OPENED, ARRESTED, CLOSED(?) проверить надо по фактическим данным в БД CardsAccounts
	Title            string                 `protobuf:"bytes,13,opt,name=title,proto3" json:"title,omitempty"`                        // Наименование счета
	Type             string                 `protobuf:"bytes,14,opt,name=type,proto3" json:"type,omitempty"`                          // CURR , OTHERS
	IsMain           bool                   `protobuf:"varint,15,opt,name=isMain,proto3" json:"isMain,omitempty"`                     // является ли счет основным
	AccessionAccount bool                   `protobuf:"varint,16,opt,name=accessionAccount,proto3" json:"accessionAccount,omitempty"` // является ли счет присоединенным
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *AccountSaveToDB) Reset() {
	*x = AccountSaveToDB{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountSaveToDB) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountSaveToDB) ProtoMessage() {}

func (x *AccountSaveToDB) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountSaveToDB.ProtoReflect.Descriptor instead.
func (*AccountSaveToDB) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{18}
}

func (x *AccountSaveToDB) GetBalance() *AccountBalance {
	if x != nil {
		return x.Balance
	}
	return nil
}

func (x *AccountSaveToDB) GetClient() *AccountClient {
	if x != nil {
		return x.Client
	}
	return nil
}

func (x *AccountSaveToDB) GetDate() *AccountDate {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *AccountSaveToDB) GetDocument() *AccountDocument {
	if x != nil {
		return x.Document
	}
	return nil
}

func (x *AccountSaveToDB) GetArrest() *Arrest {
	if x != nil {
		return x.Arrest
	}
	return nil
}

func (x *AccountSaveToDB) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *AccountSaveToDB) GetDeaReferenceID() string {
	if x != nil {
		return x.DeaReferenceID
	}
	return ""
}

func (x *AccountSaveToDB) GetIban() string {
	if x != nil {
		return x.Iban
	}
	return ""
}

func (x *AccountSaveToDB) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

func (x *AccountSaveToDB) GetOrigin() string {
	if x != nil {
		return x.Origin
	}
	return ""
}

func (x *AccountSaveToDB) GetShortName() string {
	if x != nil {
		return x.ShortName
	}
	return ""
}

func (x *AccountSaveToDB) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *AccountSaveToDB) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AccountSaveToDB) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *AccountSaveToDB) GetIsMain() bool {
	if x != nil {
		return x.IsMain
	}
	return false
}

func (x *AccountSaveToDB) GetAccessionAccount() bool {
	if x != nil {
		return x.AccessionAccount
	}
	return false
}

// Структура по клиенту счета
type AccountClient struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Iin           string                 `protobuf:"bytes,1,opt,name=iin,proto3" json:"iin,omitempty"`
	Code          string                 `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Type          string                 `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountClient) Reset() {
	*x = AccountClient{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountClient) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountClient) ProtoMessage() {}

func (x *AccountClient) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountClient.ProtoReflect.Descriptor instead.
func (*AccountClient) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{19}
}

func (x *AccountClient) GetIin() string {
	if x != nil {
		return x.Iin
	}
	return ""
}

func (x *AccountClient) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *AccountClient) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AccountClient) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

// Структура по документу счета - из РКО Colvir
type AccountDocument struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`         // document_id
	Number        string                 `protobuf:"bytes,2,opt,name=number,proto3" json:"number,omitempty"` // document_number
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountDocument) Reset() {
	*x = AccountDocument{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountDocument) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountDocument) ProtoMessage() {}

func (x *AccountDocument) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountDocument.ProtoReflect.Descriptor instead.
func (*AccountDocument) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{20}
}

func (x *AccountDocument) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AccountDocument) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

// Структура датам счета
type AccountDate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Opened        string                 `protobuf:"bytes,1,opt,name=opened,proto3" json:"opened,omitempty"` // date_opened
	Closed        string                 `protobuf:"bytes,2,opt,name=closed,proto3" json:"closed,omitempty"` // date_closed
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountDate) Reset() {
	*x = AccountDate{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountDate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountDate) ProtoMessage() {}

func (x *AccountDate) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountDate.ProtoReflect.Descriptor instead.
func (*AccountDate) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{21}
}

func (x *AccountDate) GetOpened() string {
	if x != nil {
		return x.Opened
	}
	return ""
}

func (x *AccountDate) GetClosed() string {
	if x != nil {
		return x.Closed
	}
	return ""
}

// Структура баланса счета
type AccountBalance struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Main          float64                `protobuf:"fixed64,1,opt,name=main,proto3" json:"main,omitempty"`           // balance
	Natival       float64                `protobuf:"fixed64,2,opt,name=natival,proto3" json:"natival,omitempty"`     // balance_natival
	Available     float64                `protobuf:"fixed64,3,opt,name=available,proto3" json:"available,omitempty"` // from rko -
	Blocked       float64                `protobuf:"fixed64,4,opt,name=blocked,proto3" json:"blocked,omitempty"`     // PlanSum - blocked_balance
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountBalance) Reset() {
	*x = AccountBalance{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountBalance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountBalance) ProtoMessage() {}

func (x *AccountBalance) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountBalance.ProtoReflect.Descriptor instead.
func (*AccountBalance) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{22}
}

func (x *AccountBalance) GetMain() float64 {
	if x != nil {
		return x.Main
	}
	return 0
}

func (x *AccountBalance) GetNatival() float64 {
	if x != nil {
		return x.Natival
	}
	return 0
}

func (x *AccountBalance) GetAvailable() float64 {
	if x != nil {
		return x.Available
	}
	return 0
}

func (x *AccountBalance) GetBlocked() float64 {
	if x != nil {
		return x.Blocked
	}
	return 0
}

// Структура ареста
type Arrest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Blocking            bool                   `protobuf:"varint,1,opt,name=blocking,proto3" json:"blocking,omitempty"`      // has_arrest / RestrictionFl
	DebtAmount          float64                `protobuf:"fixed64,2,opt,name=debtAmount,proto3" json:"debtAmount,omitempty"` // db: arrest_debt_amount
	Date                string                 `protobuf:"bytes,3,opt,name=date,proto3" json:"date,omitempty"`
	Code                string                 `protobuf:"bytes,4,opt,name=code,proto3" json:"code,omitempty"`
	BenName             string                 `protobuf:"bytes,5,opt,name=benName,proto3" json:"benName,omitempty"`
	ExecutorContactInfo string                 `protobuf:"bytes,6,opt,name=executorContactInfo,proto3" json:"executorContactInfo,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *Arrest) Reset() {
	*x = Arrest{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Arrest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Arrest) ProtoMessage() {}

func (x *Arrest) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Arrest.ProtoReflect.Descriptor instead.
func (*Arrest) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{23}
}

func (x *Arrest) GetBlocking() bool {
	if x != nil {
		return x.Blocking
	}
	return false
}

func (x *Arrest) GetDebtAmount() float64 {
	if x != nil {
		return x.DebtAmount
	}
	return 0
}

func (x *Arrest) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *Arrest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *Arrest) GetBenName() string {
	if x != nil {
		return x.BenName
	}
	return ""
}

func (x *Arrest) GetExecutorContactInfo() string {
	if x != nil {
		return x.ExecutorContactInfo
	}
	return ""
}

// Запрос на получение счетов клиента SME
type GetAccountsSMERequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountsSMERequest) Reset() {
	*x = GetAccountsSMERequest{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountsSMERequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountsSMERequest) ProtoMessage() {}

func (x *GetAccountsSMERequest) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountsSMERequest.ProtoReflect.Descriptor instead.
func (*GetAccountsSMERequest) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{24}
}

// Ответ на запрос получения счетов SME
type GetAccountsSMEResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccountsSME   []*AccountSME          `protobuf:"bytes,1,rep,name=accountsSME,proto3" json:"accountsSME,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountsSMEResponse) Reset() {
	*x = GetAccountsSMEResponse{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountsSMEResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountsSMEResponse) ProtoMessage() {}

func (x *GetAccountsSMEResponse) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountsSMEResponse.ProtoReflect.Descriptor instead.
func (*GetAccountsSMEResponse) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{25}
}

func (x *GetAccountsSMEResponse) GetAccountsSME() []*AccountSME {
	if x != nil {
		return x.AccountsSME
	}
	return nil
}

// Структура счета SME
type AccountSME struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ID               string                 `protobuf:"bytes,1,opt,name=ID,proto3" json:"ID,omitempty"`
	DocumentID       string                 `protobuf:"bytes,2,opt,name=documentID,proto3" json:"documentID,omitempty"`
	Type             string                 `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Status           string                 `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	Currency         string                 `protobuf:"bytes,5,opt,name=currency,proto3" json:"currency,omitempty"`
	Iban             string                 `protobuf:"bytes,6,opt,name=iban,proto3" json:"iban,omitempty"`
	OpenDate         string                 `protobuf:"bytes,7,opt,name=openDate,proto3" json:"openDate,omitempty"`
	CloseDate        string                 `protobuf:"bytes,8,opt,name=closeDate,proto3" json:"closeDate,omitempty"`
	Balance          float64                `protobuf:"fixed64,9,opt,name=balance,proto3" json:"balance,omitempty"`
	BalanceNatval    float64                `protobuf:"fixed64,10,opt,name=balanceNatval,proto3" json:"balanceNatval,omitempty"`
	PlanSum          float64                `protobuf:"fixed64,11,opt,name=planSum,proto3" json:"planSum,omitempty"`
	AvailableBalance float64                `protobuf:"fixed64,12,opt,name=availableBalance,proto3" json:"availableBalance,omitempty"`
	FilialCode       string                 `protobuf:"bytes,13,opt,name=filialCode,proto3" json:"filialCode,omitempty"`
	BranchCode       string                 `protobuf:"bytes,14,opt,name=branchCode,proto3" json:"branchCode,omitempty"`
	Arrest           *Arrest                `protobuf:"bytes,15,opt,name=arrest,proto3" json:"arrest,omitempty"`
	DeaReferenceID   string                 `protobuf:"bytes,16,opt,name=deaReferenceID,proto3" json:"deaReferenceID,omitempty"`
	ClientType       string                 `protobuf:"bytes,17,opt,name=clientType,proto3" json:"clientType,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *AccountSME) Reset() {
	*x = AccountSME{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountSME) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountSME) ProtoMessage() {}

func (x *AccountSME) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountSME.ProtoReflect.Descriptor instead.
func (*AccountSME) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{26}
}

func (x *AccountSME) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

func (x *AccountSME) GetDocumentID() string {
	if x != nil {
		return x.DocumentID
	}
	return ""
}

func (x *AccountSME) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *AccountSME) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *AccountSME) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *AccountSME) GetIban() string {
	if x != nil {
		return x.Iban
	}
	return ""
}

func (x *AccountSME) GetOpenDate() string {
	if x != nil {
		return x.OpenDate
	}
	return ""
}

func (x *AccountSME) GetCloseDate() string {
	if x != nil {
		return x.CloseDate
	}
	return ""
}

func (x *AccountSME) GetBalance() float64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

func (x *AccountSME) GetBalanceNatval() float64 {
	if x != nil {
		return x.BalanceNatval
	}
	return 0
}

func (x *AccountSME) GetPlanSum() float64 {
	if x != nil {
		return x.PlanSum
	}
	return 0
}

func (x *AccountSME) GetAvailableBalance() float64 {
	if x != nil {
		return x.AvailableBalance
	}
	return 0
}

func (x *AccountSME) GetFilialCode() string {
	if x != nil {
		return x.FilialCode
	}
	return ""
}

func (x *AccountSME) GetBranchCode() string {
	if x != nil {
		return x.BranchCode
	}
	return ""
}

func (x *AccountSME) GetArrest() *Arrest {
	if x != nil {
		return x.Arrest
	}
	return nil
}

func (x *AccountSME) GetDeaReferenceID() string {
	if x != nil {
		return x.DeaReferenceID
	}
	return ""
}

func (x *AccountSME) GetClientType() string {
	if x != nil {
		return x.ClientType
	}
	return ""
}

type GetApplicationForSignReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DocType       ApplicationType        `protobuf:"varint,1,opt,name=docType,proto3,enum=cardsAccounts.ApplicationType" json:"docType,omitempty"` // Тип заявления, например, cardIssueApplication или additionalAccountsOpeningApplication
	Currencies    []string               `protobuf:"bytes,2,rep,name=currencies,proto3" json:"currencies,omitempty"`                               // Список валют, запрашиваемых для открытия счетов (например, "KZT", "USD")
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetApplicationForSignReq) Reset() {
	*x = GetApplicationForSignReq{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetApplicationForSignReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApplicationForSignReq) ProtoMessage() {}

func (x *GetApplicationForSignReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApplicationForSignReq.ProtoReflect.Descriptor instead.
func (*GetApplicationForSignReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{27}
}

func (x *GetApplicationForSignReq) GetDocType() ApplicationType {
	if x != nil {
		return x.DocType
	}
	return ApplicationType_cardIssueApplication
}

func (x *GetApplicationForSignReq) GetCurrencies() []string {
	if x != nil {
		return x.Currencies
	}
	return nil
}

type GetApplicationForSignResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Documents     []*Document            `protobuf:"bytes,1,rep,name=documents,proto3" json:"documents,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetApplicationForSignResp) Reset() {
	*x = GetApplicationForSignResp{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetApplicationForSignResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApplicationForSignResp) ProtoMessage() {}

func (x *GetApplicationForSignResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApplicationForSignResp.ProtoReflect.Descriptor instead.
func (*GetApplicationForSignResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{28}
}

func (x *GetApplicationForSignResp) GetDocuments() []*Document {
	if x != nil {
		return x.Documents
	}
	return nil
}

type Document struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Type          string                 `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Version       int32                  `protobuf:"varint,4,opt,name=version,proto3" json:"version,omitempty"`
	Link          string                 `protobuf:"bytes,5,opt,name=link,proto3" json:"link,omitempty"`
	Signed        bool                   `protobuf:"varint,6,opt,name=signed,proto3" json:"signed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Document) Reset() {
	*x = Document{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Document) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Document) ProtoMessage() {}

func (x *Document) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Document.ProtoReflect.Descriptor instead.
func (*Document) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{29}
}

func (x *Document) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Document) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Document) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Document) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *Document) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

func (x *Document) GetSigned() bool {
	if x != nil {
		return x.Signed
	}
	return false
}

// Запрос на получение данных о проверке клиента при открытии доп.счета ИП
type VerifyClientRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyClientRequest) Reset() {
	*x = VerifyClientRequest{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyClientRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyClientRequest) ProtoMessage() {}

func (x *VerifyClientRequest) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyClientRequest.ProtoReflect.Descriptor instead.
func (*VerifyClientRequest) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{30}
}

// Ответ на запрос получения данных о проверке клиента при открытии доп.счета ИП
type VerifyClientResponse struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Status             string                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	VerificationResult string                 `protobuf:"bytes,2,opt,name=verificationResult,proto3" json:"verificationResult,omitempty"`
	RejectionReason    string                 `protobuf:"bytes,3,opt,name=rejectionReason,proto3" json:"rejectionReason,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *VerifyClientResponse) Reset() {
	*x = VerifyClientResponse{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyClientResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyClientResponse) ProtoMessage() {}

func (x *VerifyClientResponse) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyClientResponse.ProtoReflect.Descriptor instead.
func (*VerifyClientResponse) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{31}
}

func (x *VerifyClientResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *VerifyClientResponse) GetVerificationResult() string {
	if x != nil {
		return x.VerificationResult
	}
	return ""
}

func (x *VerifyClientResponse) GetRejectionReason() string {
	if x != nil {
		return x.RejectionReason
	}
	return ""
}

type GetAvailableAccountsByCurrencySMERequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAvailableAccountsByCurrencySMERequest) Reset() {
	*x = GetAvailableAccountsByCurrencySMERequest{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAvailableAccountsByCurrencySMERequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableAccountsByCurrencySMERequest) ProtoMessage() {}

func (x *GetAvailableAccountsByCurrencySMERequest) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableAccountsByCurrencySMERequest.ProtoReflect.Descriptor instead.
func (*GetAvailableAccountsByCurrencySMERequest) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{32}
}

type GetAvailableAccountsByCurrencySMEResponse struct {
	state                     protoimpl.MessageState `protogen:"open.v1"`
	IsAccountOpeningAvailable bool                   `protobuf:"varint,1,opt,name=isAccountOpeningAvailable,proto3" json:"isAccountOpeningAvailable,omitempty"`
	Currencies                []*CurrencyAccountSME  `protobuf:"bytes,2,rep,name=currencies,proto3" json:"currencies,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *GetAvailableAccountsByCurrencySMEResponse) Reset() {
	*x = GetAvailableAccountsByCurrencySMEResponse{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAvailableAccountsByCurrencySMEResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableAccountsByCurrencySMEResponse) ProtoMessage() {}

func (x *GetAvailableAccountsByCurrencySMEResponse) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableAccountsByCurrencySMEResponse.ProtoReflect.Descriptor instead.
func (*GetAvailableAccountsByCurrencySMEResponse) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{33}
}

func (x *GetAvailableAccountsByCurrencySMEResponse) GetIsAccountOpeningAvailable() bool {
	if x != nil {
		return x.IsAccountOpeningAvailable
	}
	return false
}

func (x *GetAvailableAccountsByCurrencySMEResponse) GetCurrencies() []*CurrencyAccountSME {
	if x != nil {
		return x.Currencies
	}
	return nil
}

type CurrencyAccountSME struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Currency      string                 `protobuf:"bytes,1,opt,name=currency,proto3" json:"currency,omitempty"`
	AccountExists bool                   `protobuf:"varint,2,opt,name=accountExists,proto3" json:"accountExists,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CurrencyAccountSME) Reset() {
	*x = CurrencyAccountSME{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CurrencyAccountSME) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CurrencyAccountSME) ProtoMessage() {}

func (x *CurrencyAccountSME) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CurrencyAccountSME.ProtoReflect.Descriptor instead.
func (*CurrencyAccountSME) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{34}
}

func (x *CurrencyAccountSME) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *CurrencyAccountSME) GetAccountExists() bool {
	if x != nil {
		return x.AccountExists
	}
	return false
}

type Currency struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Currency      string                 `protobuf:"bytes,1,opt,name=currency,proto3" json:"currency,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Currency) Reset() {
	*x = Currency{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Currency) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Currency) ProtoMessage() {}

func (x *Currency) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Currency.ProtoReflect.Descriptor instead.
func (*Currency) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{35}
}

func (x *Currency) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

// Запрос на получение документов для подписания
type GetDocumentsForSignRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Currencies    []*Currency            `protobuf:"bytes,1,rep,name=currencies,proto3" json:"currencies,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDocumentsForSignRequest) Reset() {
	*x = GetDocumentsForSignRequest{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDocumentsForSignRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDocumentsForSignRequest) ProtoMessage() {}

func (x *GetDocumentsForSignRequest) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDocumentsForSignRequest.ProtoReflect.Descriptor instead.
func (*GetDocumentsForSignRequest) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{36}
}

func (x *GetDocumentsForSignRequest) GetCurrencies() []*Currency {
	if x != nil {
		return x.Currencies
	}
	return nil
}

// Ответ на запрос получения документов для подписания
type GetDocumentsForSignResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Documents     []*Document            `protobuf:"bytes,1,rep,name=documents,proto3" json:"documents,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDocumentsForSignResponse) Reset() {
	*x = GetDocumentsForSignResponse{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDocumentsForSignResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDocumentsForSignResponse) ProtoMessage() {}

func (x *GetDocumentsForSignResponse) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDocumentsForSignResponse.ProtoReflect.Descriptor instead.
func (*GetDocumentsForSignResponse) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{37}
}

func (x *GetDocumentsForSignResponse) GetDocuments() []*Document {
	if x != nil {
		return x.Documents
	}
	return nil
}

// Запрос на получение реквизитов карты из ПЦ
type GetRequisitesUnmaskedRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CardId        string                 `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRequisitesUnmaskedRequest) Reset() {
	*x = GetRequisitesUnmaskedRequest{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRequisitesUnmaskedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRequisitesUnmaskedRequest) ProtoMessage() {}

func (x *GetRequisitesUnmaskedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRequisitesUnmaskedRequest.ProtoReflect.Descriptor instead.
func (*GetRequisitesUnmaskedRequest) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{38}
}

func (x *GetRequisitesUnmaskedRequest) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

// Ответ на запрос получения реквизитов карты из ПЦ
type GetRequisitesUnmaskedResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pan           string                 `protobuf:"bytes,1,opt,name=pan,proto3" json:"pan,omitempty"`
	EmbossName    string                 `protobuf:"bytes,2,opt,name=emboss_name,json=embossName,proto3" json:"emboss_name,omitempty"`
	ExpiryDate    string                 `protobuf:"bytes,3,opt,name=expiry_date,json=expiryDate,proto3" json:"expiry_date,omitempty"`
	Status        string                 `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	Cvv           string                 `protobuf:"bytes,5,opt,name=cvv,proto3" json:"cvv,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRequisitesUnmaskedResponse) Reset() {
	*x = GetRequisitesUnmaskedResponse{}
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRequisitesUnmaskedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRequisitesUnmaskedResponse) ProtoMessage() {}

func (x *GetRequisitesUnmaskedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRequisitesUnmaskedResponse.ProtoReflect.Descriptor instead.
func (*GetRequisitesUnmaskedResponse) Descriptor() ([]byte, []int) {
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP(), []int{39}
}

func (x *GetRequisitesUnmaskedResponse) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *GetRequisitesUnmaskedResponse) GetEmbossName() string {
	if x != nil {
		return x.EmbossName
	}
	return ""
}

func (x *GetRequisitesUnmaskedResponse) GetExpiryDate() string {
	if x != nil {
		return x.ExpiryDate
	}
	return ""
}

func (x *GetRequisitesUnmaskedResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetRequisitesUnmaskedResponse) GetCvv() string {
	if x != nil {
		return x.Cvv
	}
	return ""
}

var File_specs_proto_cards_accounts_cards_accounts_proto protoreflect.FileDescriptor

const file_specs_proto_cards_accounts_cards_accounts_proto_rawDesc = "" +
	"\n" +
	"/specs/proto/cards-accounts/cards-accounts.proto\x12\rcardsAccounts\"\x10\n" +
	"\x0eHealthCheckReq\")\n" +
	"\x0fHealthCheckResp\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\"\x0f\n" +
	"\rGetBalanceReq\"F\n" +
	"\x0eGetBalanceResp\x12\x18\n" +
	"\abalance\x18\x01 \x01(\x01R\abalance\x12\x1a\n" +
	"\bcurrency\x18\x02 \x01(\tR\bcurrency\"0\n" +
	"\x14CreateAccountRequest\x12\x18\n" +
	"\auserIIN\x18\x01 \x01(\tR\auserIIN\"2\n" +
	"\x18CreateVirtualCardRequest\x12\x16\n" +
	"\x06userID\x18\x01 \x01(\tR\x06userID\"o\n" +
	"\x19CreateVirtualCardResponse\x12\x16\n" +
	"\x06cardID\x18\x01 \x01(\tR\x06cardID\x12\x1c\n" +
	"\taccountID\x18\x02 \x01(\tR\taccountID\x12\x1c\n" +
	"\tisSuccess\x18\x03 \x01(\bR\tisSuccess\"\x17\n" +
	"\x15CreateAccountResponse\"\x11\n" +
	"\x0fGetCardsRequest\"\x14\n" +
	"\x12GetAccountsRequest\"I\n" +
	"\x13GetAccountsResponse\x122\n" +
	"\baccounts\x18\x01 \x03(\v2\x16.cardsAccounts.AccountR\baccounts\"q\n" +
	"\x10GetCardsResponse\x122\n" +
	"\baccounts\x18\x01 \x03(\v2\x16.cardsAccounts.AccountR\baccounts\x12)\n" +
	"\x05cards\x18\x02 \x03(\v2\x13.cardsAccounts.CardR\x05cards\"\xb0\x02\n" +
	"\x04Card\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\tR\x02ID\x12,\n" +
	"\x11attachedAccountID\x18\x02 \x01(\tR\x11attachedAccountID\x12\x1c\n" +
	"\tCardClass\x18\x03 \x01(\tR\tCardClass\x12\x1a\n" +
	"\bCardType\x18\x04 \x01(\tR\bCardType\x12$\n" +
	"\rpaymentSystem\x18\x05 \x01(\tR\rpaymentSystem\x12\x16\n" +
	"\x06status\x18\x06 \x01(\tR\x06status\x12.\n" +
	"\x12tokenizationStatus\x18\a \x01(\tR\x12tokenizationStatus\x12$\n" +
	"\rembossingName\x18\b \x01(\tR\rembossingName\x12\x1c\n" +
	"\tmaskedPan\x18\t \x01(\tR\tmaskedPan\"2\n" +
	"\x11GetAccountRequest\x12\x1d\n" +
	"\n" +
	"account_id\x18\x01 \x01(\tR\taccountId\"\xb7\x03\n" +
	"\x12GetAccountResponse\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\tR\x02ID\x12\x1e\n" +
	"\n" +
	"documentID\x18\x02 \x01(\tR\n" +
	"documentID\x12&\n" +
	"\x0edocumentNumber\x18\x03 \x01(\tR\x0edocumentNumber\x12\x12\n" +
	"\x04type\x18\x04 \x01(\tR\x04type\x12\x16\n" +
	"\x06status\x18\x05 \x01(\tR\x06status\x12\x1a\n" +
	"\bcurrency\x18\x06 \x01(\tR\bcurrency\x12\x12\n" +
	"\x04iban\x18\a \x01(\tR\x04iban\x12\x1a\n" +
	"\bopenDate\x18\b \x01(\tR\bopenDate\x12\x1c\n" +
	"\tcloseDate\x18\t \x01(\tR\tcloseDate\x12\x18\n" +
	"\abalance\x18\n" +
	" \x01(\x01R\abalance\x12$\n" +
	"\rbalanceNatval\x18\v \x01(\x01R\rbalanceNatval\x12\x18\n" +
	"\aplanSum\x18\f \x01(\x01R\aplanSum\x12*\n" +
	"\x10availableBalance\x18\r \x01(\x01R\x10availableBalance\x12-\n" +
	"\x06arrest\x18\x0e \x01(\v2\x15.cardsAccounts.ArrestR\x06arrest\"\xdc\x05\n" +
	"\aAccount\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\tR\x02ID\x12\x1e\n" +
	"\n" +
	"documentID\x18\x02 \x01(\tR\n" +
	"documentID\x12&\n" +
	"\x0edocumentNumber\x18\x03 \x01(\tR\x0edocumentNumber\x12\x12\n" +
	"\x04type\x18\x04 \x01(\tR\x04type\x12\x16\n" +
	"\x06status\x18\x05 \x01(\tR\x06status\x12\x1a\n" +
	"\bcurrency\x18\x06 \x01(\tR\bcurrency\x12\x12\n" +
	"\x04iban\x18\a \x01(\tR\x04iban\x12\x1a\n" +
	"\bopenDate\x18\b \x01(\tR\bopenDate\x12\x1c\n" +
	"\tcloseDate\x18\t \x01(\tR\tcloseDate\x12\x18\n" +
	"\abalance\x18\n" +
	" \x01(\x01R\abalance\x12$\n" +
	"\rbalanceNatval\x18\v \x01(\x01R\rbalanceNatval\x12\x18\n" +
	"\aplanSum\x18\f \x01(\x01R\aplanSum\x12*\n" +
	"\x10availableBalance\x18\r \x01(\x01R\x10availableBalance\x12\x1e\n" +
	"\n" +
	"filialCode\x18\x0e \x01(\tR\n" +
	"filialCode\x12\x1e\n" +
	"\n" +
	"branchCode\x18\x0f \x01(\tR\n" +
	"branchCode\x12-\n" +
	"\x06arrest\x18\x10 \x01(\v2\x15.cardsAccounts.ArrestR\x06arrest\x12&\n" +
	"\x0edeaReferenceID\x18\x11 \x01(\tR\x0edeaReferenceID\x12\x1e\n" +
	"\n" +
	"clientType\x18\x12 \x01(\tR\n" +
	"clientType\x120\n" +
	"\x13isFinContractOpened\x18\x13 \x01(\bR\x13isFinContractOpened\x12$\n" +
	"\rfinContractID\x18\x14 \x01(\tR\rfinContractID\x12N\n" +
	"\x11finContractStatus\x18\x15 \x01(\x0e2 .cardsAccounts.FinContractStatusR\x11finContractStatus\"N\n" +
	"\x12SaveAccountRequest\x128\n" +
	"\aaccount\x18\x01 \x01(\v2\x1e.cardsAccounts.AccountSaveToDBR\aaccount\"3\n" +
	"\x13SaveAccountResponse\x12\x1c\n" +
	"\tisSuccess\x18\x01 \x01(\bR\tisSuccess\"\xbf\x04\n" +
	"\x0fAccountSaveToDB\x127\n" +
	"\abalance\x18\x01 \x01(\v2\x1d.cardsAccounts.AccountBalanceR\abalance\x124\n" +
	"\x06client\x18\x02 \x01(\v2\x1c.cardsAccounts.AccountClientR\x06client\x12.\n" +
	"\x04date\x18\x03 \x01(\v2\x1a.cardsAccounts.AccountDateR\x04date\x12:\n" +
	"\bdocument\x18\x04 \x01(\v2\x1e.cardsAccounts.AccountDocumentR\bdocument\x12-\n" +
	"\x06arrest\x18\x05 \x01(\v2\x15.cardsAccounts.ArrestR\x06arrest\x12\x1a\n" +
	"\bcurrency\x18\x06 \x01(\tR\bcurrency\x12&\n" +
	"\x0edeaReferenceID\x18\a \x01(\tR\x0edeaReferenceID\x12\x12\n" +
	"\x04iban\x18\b \x01(\tR\x04iban\x12\x0e\n" +
	"\x02ID\x18\t \x01(\tR\x02ID\x12\x16\n" +
	"\x06origin\x18\n" +
	" \x01(\tR\x06origin\x12\x1c\n" +
	"\tshortName\x18\v \x01(\tR\tshortName\x12\x16\n" +
	"\x06status\x18\f \x01(\tR\x06status\x12\x14\n" +
	"\x05title\x18\r \x01(\tR\x05title\x12\x12\n" +
	"\x04type\x18\x0e \x01(\tR\x04type\x12\x16\n" +
	"\x06isMain\x18\x0f \x01(\bR\x06isMain\x12*\n" +
	"\x10accessionAccount\x18\x10 \x01(\bR\x10accessionAccount\"]\n" +
	"\rAccountClient\x12\x10\n" +
	"\x03iin\x18\x01 \x01(\tR\x03iin\x12\x12\n" +
	"\x04code\x18\x02 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x12\n" +
	"\x04type\x18\x04 \x01(\tR\x04type\"9\n" +
	"\x0fAccountDocument\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x16\n" +
	"\x06number\x18\x02 \x01(\tR\x06number\"=\n" +
	"\vAccountDate\x12\x16\n" +
	"\x06opened\x18\x01 \x01(\tR\x06opened\x12\x16\n" +
	"\x06closed\x18\x02 \x01(\tR\x06closed\"v\n" +
	"\x0eAccountBalance\x12\x12\n" +
	"\x04main\x18\x01 \x01(\x01R\x04main\x12\x18\n" +
	"\anatival\x18\x02 \x01(\x01R\anatival\x12\x1c\n" +
	"\tavailable\x18\x03 \x01(\x01R\tavailable\x12\x18\n" +
	"\ablocked\x18\x04 \x01(\x01R\ablocked\"\xb8\x01\n" +
	"\x06Arrest\x12\x1a\n" +
	"\bblocking\x18\x01 \x01(\bR\bblocking\x12\x1e\n" +
	"\n" +
	"debtAmount\x18\x02 \x01(\x01R\n" +
	"debtAmount\x12\x12\n" +
	"\x04date\x18\x03 \x01(\tR\x04date\x12\x12\n" +
	"\x04code\x18\x04 \x01(\tR\x04code\x12\x18\n" +
	"\abenName\x18\x05 \x01(\tR\abenName\x120\n" +
	"\x13executorContactInfo\x18\x06 \x01(\tR\x13executorContactInfo\"\x17\n" +
	"\x15GetAccountsSMERequest\"U\n" +
	"\x16GetAccountsSMEResponse\x12;\n" +
	"\vaccountsSME\x18\x01 \x03(\v2\x19.cardsAccounts.AccountSMER\vaccountsSME\"\x8f\x04\n" +
	"\n" +
	"AccountSME\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\tR\x02ID\x12\x1e\n" +
	"\n" +
	"documentID\x18\x02 \x01(\tR\n" +
	"documentID\x12\x12\n" +
	"\x04type\x18\x03 \x01(\tR\x04type\x12\x16\n" +
	"\x06status\x18\x04 \x01(\tR\x06status\x12\x1a\n" +
	"\bcurrency\x18\x05 \x01(\tR\bcurrency\x12\x12\n" +
	"\x04iban\x18\x06 \x01(\tR\x04iban\x12\x1a\n" +
	"\bopenDate\x18\a \x01(\tR\bopenDate\x12\x1c\n" +
	"\tcloseDate\x18\b \x01(\tR\tcloseDate\x12\x18\n" +
	"\abalance\x18\t \x01(\x01R\abalance\x12$\n" +
	"\rbalanceNatval\x18\n" +
	" \x01(\x01R\rbalanceNatval\x12\x18\n" +
	"\aplanSum\x18\v \x01(\x01R\aplanSum\x12*\n" +
	"\x10availableBalance\x18\f \x01(\x01R\x10availableBalance\x12\x1e\n" +
	"\n" +
	"filialCode\x18\r \x01(\tR\n" +
	"filialCode\x12\x1e\n" +
	"\n" +
	"branchCode\x18\x0e \x01(\tR\n" +
	"branchCode\x12-\n" +
	"\x06arrest\x18\x0f \x01(\v2\x15.cardsAccounts.ArrestR\x06arrest\x12&\n" +
	"\x0edeaReferenceID\x18\x10 \x01(\tR\x0edeaReferenceID\x12\x1e\n" +
	"\n" +
	"clientType\x18\x11 \x01(\tR\n" +
	"clientType\"t\n" +
	"\x18GetApplicationForSignReq\x128\n" +
	"\adocType\x18\x01 \x01(\x0e2\x1e.cardsAccounts.ApplicationTypeR\adocType\x12\x1e\n" +
	"\n" +
	"currencies\x18\x02 \x03(\tR\n" +
	"currencies\"R\n" +
	"\x19GetApplicationForSignResp\x125\n" +
	"\tdocuments\x18\x01 \x03(\v2\x17.cardsAccounts.DocumentR\tdocuments\"\x8a\x01\n" +
	"\bDocument\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12\x12\n" +
	"\x04type\x18\x03 \x01(\tR\x04type\x12\x18\n" +
	"\aversion\x18\x04 \x01(\x05R\aversion\x12\x12\n" +
	"\x04link\x18\x05 \x01(\tR\x04link\x12\x16\n" +
	"\x06signed\x18\x06 \x01(\bR\x06signed\"\x15\n" +
	"\x13VerifyClientRequest\"\x88\x01\n" +
	"\x14VerifyClientResponse\x12\x16\n" +
	"\x06status\x18\x01 \x01(\tR\x06status\x12.\n" +
	"\x12verificationResult\x18\x02 \x01(\tR\x12verificationResult\x12(\n" +
	"\x0frejectionReason\x18\x03 \x01(\tR\x0frejectionReason\"*\n" +
	"(GetAvailableAccountsByCurrencySMERequest\"\xac\x01\n" +
	")GetAvailableAccountsByCurrencySMEResponse\x12<\n" +
	"\x19isAccountOpeningAvailable\x18\x01 \x01(\bR\x19isAccountOpeningAvailable\x12A\n" +
	"\n" +
	"currencies\x18\x02 \x03(\v2!.cardsAccounts.CurrencyAccountSMER\n" +
	"currencies\"V\n" +
	"\x12CurrencyAccountSME\x12\x1a\n" +
	"\bcurrency\x18\x01 \x01(\tR\bcurrency\x12$\n" +
	"\raccountExists\x18\x02 \x01(\bR\raccountExists\"&\n" +
	"\bcurrency\x12\x1a\n" +
	"\bcurrency\x18\x01 \x01(\tR\bcurrency\"U\n" +
	"\x1aGetDocumentsForSignRequest\x127\n" +
	"\n" +
	"currencies\x18\x01 \x03(\v2\x17.cardsAccounts.currencyR\n" +
	"currencies\"T\n" +
	"\x1bGetDocumentsForSignResponse\x125\n" +
	"\tdocuments\x18\x01 \x03(\v2\x17.cardsAccounts.DocumentR\tdocuments\"7\n" +
	"\x1cGetRequisitesUnmaskedRequest\x12\x17\n" +
	"\acard_id\x18\x01 \x01(\tR\x06cardId\"\x9d\x01\n" +
	"\x1dGetRequisitesUnmaskedResponse\x12\x10\n" +
	"\x03pan\x18\x01 \x01(\tR\x03pan\x12\x1f\n" +
	"\vemboss_name\x18\x02 \x01(\tR\n" +
	"embossName\x12\x1f\n" +
	"\vexpiry_date\x18\x03 \x01(\tR\n" +
	"expiryDate\x12\x16\n" +
	"\x06status\x18\x04 \x01(\tR\x06status\x12\x10\n" +
	"\x03cvv\x18\x05 \x01(\tR\x03cvv*m\n" +
	"\x11FinContractStatus\x12\x13\n" +
	"\x0fFCS_UNSPECIFIED\x10\x00\x12\x12\n" +
	"\x0eFCS_IN_OPENING\x10\x01\x12\x0e\n" +
	"\n" +
	"FCS_ACTIVE\x10\x02\x12\x0f\n" +
	"\vFCS_BLOCKED\x10\x03\x12\x0e\n" +
	"\n" +
	"FCS_CLOSED\x10\x04*U\n" +
	"\x0fApplicationType\x12\x18\n" +
	"\x14cardIssueApplication\x10\x00\x12(\n" +
	"$additionalAccountsOpeningApplication\x10\x012\xd7\n" +
	"\n" +
	"\rCardsaccounts\x12L\n" +
	"\vHealthCheck\x12\x1d.cardsAccounts.HealthCheckReq\x1a\x1e.cardsAccounts.HealthCheckResp\x12I\n" +
	"\n" +
	"GetBalance\x12\x1c.cardsAccounts.GetBalanceReq\x1a\x1d.cardsAccounts.GetBalanceResp\x12K\n" +
	"\bGetCards\x12\x1e.cardsAccounts.GetCardsRequest\x1a\x1f.cardsAccounts.GetCardsResponse\x12T\n" +
	"\vGetAccounts\x12!.cardsAccounts.GetAccountsRequest\x1a\".cardsAccounts.GetAccountsResponse\x12Q\n" +
	"\n" +
	"GetAccount\x12 .cardsAccounts.GetAccountRequest\x1a!.cardsAccounts.GetAccountResponse\x12Z\n" +
	"\rCreateAccount\x12#.cardsAccounts.CreateAccountRequest\x1a$.cardsAccounts.CreateAccountResponse\x12T\n" +
	"\vSaveAccount\x12!.cardsAccounts.SaveAccountRequest\x1a\".cardsAccounts.SaveAccountResponse\x12f\n" +
	"\x11CreateVirtualCard\x12'.cardsAccounts.CreateVirtualCardRequest\x1a(.cardsAccounts.CreateVirtualCardResponse\x12]\n" +
	"\x0eGetAccountsSME\x12$.cardsAccounts.GetAccountsSMERequest\x1a%.cardsAccounts.GetAccountsSMEResponse\x12j\n" +
	"\x15GetApplicationForSign\x12'.cardsAccounts.GetApplicationForSignReq\x1a(.cardsAccounts.GetApplicationForSignResp\x12W\n" +
	"\fVerifyClient\x12\".cardsAccounts.VerifyClientRequest\x1a#.cardsAccounts.VerifyClientResponse\x12\x96\x01\n" +
	"!GetAvailableAccountsByCurrencySME\x127.cardsAccounts.GetAvailableAccountsByCurrencySMERequest\x1a8.cardsAccounts.GetAvailableAccountsByCurrencySMEResponse\x12l\n" +
	"\x13GetDocumentsForSign\x12).cardsAccounts.GetDocumentsForSignRequest\x1a*.cardsAccounts.GetDocumentsForSignResponse\x12r\n" +
	"\x15GetRequisitesUnmasked\x12+.cardsAccounts.GetRequisitesUnmaskedRequest\x1a,.cardsAccounts.GetRequisitesUnmaskedResponseB\x1cZ\x1aspecs/proto/cards-accountsb\x06proto3"

var (
	file_specs_proto_cards_accounts_cards_accounts_proto_rawDescOnce sync.Once
	file_specs_proto_cards_accounts_cards_accounts_proto_rawDescData []byte
)

func file_specs_proto_cards_accounts_cards_accounts_proto_rawDescGZIP() []byte {
	file_specs_proto_cards_accounts_cards_accounts_proto_rawDescOnce.Do(func() {
		file_specs_proto_cards_accounts_cards_accounts_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_specs_proto_cards_accounts_cards_accounts_proto_rawDesc), len(file_specs_proto_cards_accounts_cards_accounts_proto_rawDesc)))
	})
	return file_specs_proto_cards_accounts_cards_accounts_proto_rawDescData
}

var file_specs_proto_cards_accounts_cards_accounts_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes = make([]protoimpl.MessageInfo, 40)
var file_specs_proto_cards_accounts_cards_accounts_proto_goTypes = []any{
	(FinContractStatus)(0),                            // 0: cardsAccounts.FinContractStatus
	(ApplicationType)(0),                              // 1: cardsAccounts.ApplicationType
	(*HealthCheckReq)(nil),                            // 2: cardsAccounts.HealthCheckReq
	(*HealthCheckResp)(nil),                           // 3: cardsAccounts.HealthCheckResp
	(*GetBalanceReq)(nil),                             // 4: cardsAccounts.GetBalanceReq
	(*GetBalanceResp)(nil),                            // 5: cardsAccounts.GetBalanceResp
	(*CreateAccountRequest)(nil),                      // 6: cardsAccounts.CreateAccountRequest
	(*CreateVirtualCardRequest)(nil),                  // 7: cardsAccounts.CreateVirtualCardRequest
	(*CreateVirtualCardResponse)(nil),                 // 8: cardsAccounts.CreateVirtualCardResponse
	(*CreateAccountResponse)(nil),                     // 9: cardsAccounts.CreateAccountResponse
	(*GetCardsRequest)(nil),                           // 10: cardsAccounts.GetCardsRequest
	(*GetAccountsRequest)(nil),                        // 11: cardsAccounts.GetAccountsRequest
	(*GetAccountsResponse)(nil),                       // 12: cardsAccounts.GetAccountsResponse
	(*GetCardsResponse)(nil),                          // 13: cardsAccounts.GetCardsResponse
	(*Card)(nil),                                      // 14: cardsAccounts.Card
	(*GetAccountRequest)(nil),                         // 15: cardsAccounts.GetAccountRequest
	(*GetAccountResponse)(nil),                        // 16: cardsAccounts.GetAccountResponse
	(*Account)(nil),                                   // 17: cardsAccounts.Account
	(*SaveAccountRequest)(nil),                        // 18: cardsAccounts.SaveAccountRequest
	(*SaveAccountResponse)(nil),                       // 19: cardsAccounts.SaveAccountResponse
	(*AccountSaveToDB)(nil),                           // 20: cardsAccounts.AccountSaveToDB
	(*AccountClient)(nil),                             // 21: cardsAccounts.AccountClient
	(*AccountDocument)(nil),                           // 22: cardsAccounts.AccountDocument
	(*AccountDate)(nil),                               // 23: cardsAccounts.AccountDate
	(*AccountBalance)(nil),                            // 24: cardsAccounts.AccountBalance
	(*Arrest)(nil),                                    // 25: cardsAccounts.Arrest
	(*GetAccountsSMERequest)(nil),                     // 26: cardsAccounts.GetAccountsSMERequest
	(*GetAccountsSMEResponse)(nil),                    // 27: cardsAccounts.GetAccountsSMEResponse
	(*AccountSME)(nil),                                // 28: cardsAccounts.AccountSME
	(*GetApplicationForSignReq)(nil),                  // 29: cardsAccounts.GetApplicationForSignReq
	(*GetApplicationForSignResp)(nil),                 // 30: cardsAccounts.GetApplicationForSignResp
	(*Document)(nil),                                  // 31: cardsAccounts.Document
	(*VerifyClientRequest)(nil),                       // 32: cardsAccounts.VerifyClientRequest
	(*VerifyClientResponse)(nil),                      // 33: cardsAccounts.VerifyClientResponse
	(*GetAvailableAccountsByCurrencySMERequest)(nil),  // 34: cardsAccounts.GetAvailableAccountsByCurrencySMERequest
	(*GetAvailableAccountsByCurrencySMEResponse)(nil), // 35: cardsAccounts.GetAvailableAccountsByCurrencySMEResponse
	(*CurrencyAccountSME)(nil),                        // 36: cardsAccounts.CurrencyAccountSME
	(*Currency)(nil),                                  // 37: cardsAccounts.currency
	(*GetDocumentsForSignRequest)(nil),                // 38: cardsAccounts.GetDocumentsForSignRequest
	(*GetDocumentsForSignResponse)(nil),               // 39: cardsAccounts.GetDocumentsForSignResponse
	(*GetRequisitesUnmaskedRequest)(nil),              // 40: cardsAccounts.GetRequisitesUnmaskedRequest
	(*GetRequisitesUnmaskedResponse)(nil),             // 41: cardsAccounts.GetRequisitesUnmaskedResponse
}
var file_specs_proto_cards_accounts_cards_accounts_proto_depIdxs = []int32{
	17, // 0: cardsAccounts.GetAccountsResponse.accounts:type_name -> cardsAccounts.Account
	17, // 1: cardsAccounts.GetCardsResponse.accounts:type_name -> cardsAccounts.Account
	14, // 2: cardsAccounts.GetCardsResponse.cards:type_name -> cardsAccounts.Card
	25, // 3: cardsAccounts.GetAccountResponse.arrest:type_name -> cardsAccounts.Arrest
	25, // 4: cardsAccounts.Account.arrest:type_name -> cardsAccounts.Arrest
	0,  // 5: cardsAccounts.Account.finContractStatus:type_name -> cardsAccounts.FinContractStatus
	20, // 6: cardsAccounts.SaveAccountRequest.account:type_name -> cardsAccounts.AccountSaveToDB
	24, // 7: cardsAccounts.AccountSaveToDB.balance:type_name -> cardsAccounts.AccountBalance
	21, // 8: cardsAccounts.AccountSaveToDB.client:type_name -> cardsAccounts.AccountClient
	23, // 9: cardsAccounts.AccountSaveToDB.date:type_name -> cardsAccounts.AccountDate
	22, // 10: cardsAccounts.AccountSaveToDB.document:type_name -> cardsAccounts.AccountDocument
	25, // 11: cardsAccounts.AccountSaveToDB.arrest:type_name -> cardsAccounts.Arrest
	28, // 12: cardsAccounts.GetAccountsSMEResponse.accountsSME:type_name -> cardsAccounts.AccountSME
	25, // 13: cardsAccounts.AccountSME.arrest:type_name -> cardsAccounts.Arrest
	1,  // 14: cardsAccounts.GetApplicationForSignReq.docType:type_name -> cardsAccounts.ApplicationType
	31, // 15: cardsAccounts.GetApplicationForSignResp.documents:type_name -> cardsAccounts.Document
	36, // 16: cardsAccounts.GetAvailableAccountsByCurrencySMEResponse.currencies:type_name -> cardsAccounts.CurrencyAccountSME
	37, // 17: cardsAccounts.GetDocumentsForSignRequest.currencies:type_name -> cardsAccounts.currency
	31, // 18: cardsAccounts.GetDocumentsForSignResponse.documents:type_name -> cardsAccounts.Document
	2,  // 19: cardsAccounts.Cardsaccounts.HealthCheck:input_type -> cardsAccounts.HealthCheckReq
	4,  // 20: cardsAccounts.Cardsaccounts.GetBalance:input_type -> cardsAccounts.GetBalanceReq
	10, // 21: cardsAccounts.Cardsaccounts.GetCards:input_type -> cardsAccounts.GetCardsRequest
	11, // 22: cardsAccounts.Cardsaccounts.GetAccounts:input_type -> cardsAccounts.GetAccountsRequest
	15, // 23: cardsAccounts.Cardsaccounts.GetAccount:input_type -> cardsAccounts.GetAccountRequest
	6,  // 24: cardsAccounts.Cardsaccounts.CreateAccount:input_type -> cardsAccounts.CreateAccountRequest
	18, // 25: cardsAccounts.Cardsaccounts.SaveAccount:input_type -> cardsAccounts.SaveAccountRequest
	7,  // 26: cardsAccounts.Cardsaccounts.CreateVirtualCard:input_type -> cardsAccounts.CreateVirtualCardRequest
	26, // 27: cardsAccounts.Cardsaccounts.GetAccountsSME:input_type -> cardsAccounts.GetAccountsSMERequest
	29, // 28: cardsAccounts.Cardsaccounts.GetApplicationForSign:input_type -> cardsAccounts.GetApplicationForSignReq
	32, // 29: cardsAccounts.Cardsaccounts.VerifyClient:input_type -> cardsAccounts.VerifyClientRequest
	34, // 30: cardsAccounts.Cardsaccounts.GetAvailableAccountsByCurrencySME:input_type -> cardsAccounts.GetAvailableAccountsByCurrencySMERequest
	38, // 31: cardsAccounts.Cardsaccounts.GetDocumentsForSign:input_type -> cardsAccounts.GetDocumentsForSignRequest
	40, // 32: cardsAccounts.Cardsaccounts.GetRequisitesUnmasked:input_type -> cardsAccounts.GetRequisitesUnmaskedRequest
	3,  // 33: cardsAccounts.Cardsaccounts.HealthCheck:output_type -> cardsAccounts.HealthCheckResp
	5,  // 34: cardsAccounts.Cardsaccounts.GetBalance:output_type -> cardsAccounts.GetBalanceResp
	13, // 35: cardsAccounts.Cardsaccounts.GetCards:output_type -> cardsAccounts.GetCardsResponse
	12, // 36: cardsAccounts.Cardsaccounts.GetAccounts:output_type -> cardsAccounts.GetAccountsResponse
	16, // 37: cardsAccounts.Cardsaccounts.GetAccount:output_type -> cardsAccounts.GetAccountResponse
	9,  // 38: cardsAccounts.Cardsaccounts.CreateAccount:output_type -> cardsAccounts.CreateAccountResponse
	19, // 39: cardsAccounts.Cardsaccounts.SaveAccount:output_type -> cardsAccounts.SaveAccountResponse
	8,  // 40: cardsAccounts.Cardsaccounts.CreateVirtualCard:output_type -> cardsAccounts.CreateVirtualCardResponse
	27, // 41: cardsAccounts.Cardsaccounts.GetAccountsSME:output_type -> cardsAccounts.GetAccountsSMEResponse
	30, // 42: cardsAccounts.Cardsaccounts.GetApplicationForSign:output_type -> cardsAccounts.GetApplicationForSignResp
	33, // 43: cardsAccounts.Cardsaccounts.VerifyClient:output_type -> cardsAccounts.VerifyClientResponse
	35, // 44: cardsAccounts.Cardsaccounts.GetAvailableAccountsByCurrencySME:output_type -> cardsAccounts.GetAvailableAccountsByCurrencySMEResponse
	39, // 45: cardsAccounts.Cardsaccounts.GetDocumentsForSign:output_type -> cardsAccounts.GetDocumentsForSignResponse
	41, // 46: cardsAccounts.Cardsaccounts.GetRequisitesUnmasked:output_type -> cardsAccounts.GetRequisitesUnmaskedResponse
	33, // [33:47] is the sub-list for method output_type
	19, // [19:33] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_specs_proto_cards_accounts_cards_accounts_proto_init() }
func file_specs_proto_cards_accounts_cards_accounts_proto_init() {
	if File_specs_proto_cards_accounts_cards_accounts_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_specs_proto_cards_accounts_cards_accounts_proto_rawDesc), len(file_specs_proto_cards_accounts_cards_accounts_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   40,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_specs_proto_cards_accounts_cards_accounts_proto_goTypes,
		DependencyIndexes: file_specs_proto_cards_accounts_cards_accounts_proto_depIdxs,
		EnumInfos:         file_specs_proto_cards_accounts_cards_accounts_proto_enumTypes,
		MessageInfos:      file_specs_proto_cards_accounts_cards_accounts_proto_msgTypes,
	}.Build()
	File_specs_proto_cards_accounts_cards_accounts_proto = out.File
	file_specs_proto_cards_accounts_cards_accounts_proto_goTypes = nil
	file_specs_proto_cards_accounts_cards_accounts_proto_depIdxs = nil
}
