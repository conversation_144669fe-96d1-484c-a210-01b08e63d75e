// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.32.1
// source: specs/proto/spr-bridge/spr-bridge.proto

package spr_bridge

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Sprbridge_HealthCheck_FullMethodName   = "/sprBridge.Sprbridge/HealthCheck"
	Sprbridge_GetExecuteEst_FullMethodName = "/sprBridge.Sprbridge/GetExecuteEst"
)

// SprbridgeClient is the client API for Sprbridge service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SprbridgeClient interface {
	HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error)
	GetExecuteEst(ctx context.Context, in *SprRequest, opts ...grpc.CallOption) (*SprResponse, error)
}

type sprbridgeClient struct {
	cc grpc.ClientConnInterface
}

func NewSprbridgeClient(cc grpc.ClientConnInterface) SprbridgeClient {
	return &sprbridgeClient{cc}
}

func (c *sprbridgeClient) HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResp)
	err := c.cc.Invoke(ctx, Sprbridge_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sprbridgeClient) GetExecuteEst(ctx context.Context, in *SprRequest, opts ...grpc.CallOption) (*SprResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SprResponse)
	err := c.cc.Invoke(ctx, Sprbridge_GetExecuteEst_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SprbridgeServer is the server API for Sprbridge service.
// All implementations must embed UnimplementedSprbridgeServer
// for forward compatibility.
type SprbridgeServer interface {
	HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error)
	GetExecuteEst(context.Context, *SprRequest) (*SprResponse, error)
	mustEmbedUnimplementedSprbridgeServer()
}

// UnimplementedSprbridgeServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSprbridgeServer struct{}

func (UnimplementedSprbridgeServer) HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedSprbridgeServer) GetExecuteEst(context.Context, *SprRequest) (*SprResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExecuteEst not implemented")
}
func (UnimplementedSprbridgeServer) mustEmbedUnimplementedSprbridgeServer() {}
func (UnimplementedSprbridgeServer) testEmbeddedByValue()                   {}

// UnsafeSprbridgeServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SprbridgeServer will
// result in compilation errors.
type UnsafeSprbridgeServer interface {
	mustEmbedUnimplementedSprbridgeServer()
}

func RegisterSprbridgeServer(s grpc.ServiceRegistrar, srv SprbridgeServer) {
	// If the following call pancis, it indicates UnimplementedSprbridgeServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Sprbridge_ServiceDesc, srv)
}

func _Sprbridge_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SprbridgeServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Sprbridge_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SprbridgeServer).HealthCheck(ctx, req.(*HealthCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Sprbridge_GetExecuteEst_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SprRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SprbridgeServer).GetExecuteEst(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Sprbridge_GetExecuteEst_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SprbridgeServer).GetExecuteEst(ctx, req.(*SprRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Sprbridge_ServiceDesc is the grpc.ServiceDesc for Sprbridge service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Sprbridge_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sprBridge.Sprbridge",
	HandlerType: (*SprbridgeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HealthCheck",
			Handler:    _Sprbridge_HealthCheck_Handler,
		},
		{
			MethodName: "GetExecuteEst",
			Handler:    _Sprbridge_GetExecuteEst_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "specs/proto/spr-bridge/spr-bridge.proto",
}
