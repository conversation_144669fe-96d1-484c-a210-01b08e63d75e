// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v6.32.1
// source: specs/proto/balance-updater/balance-updater.proto

package balance_updater

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HealthCheckReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckReq) Reset() {
	*x = HealthCheckReq{}
	mi := &file_specs_proto_balance_updater_balance_updater_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckReq) ProtoMessage() {}

func (x *HealthCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_balance_updater_balance_updater_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckReq.ProtoReflect.Descriptor instead.
func (*HealthCheckReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_balance_updater_balance_updater_proto_rawDescGZIP(), []int{0}
}

type HealthCheckResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckResp) Reset() {
	*x = HealthCheckResp{}
	mi := &file_specs_proto_balance_updater_balance_updater_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResp) ProtoMessage() {}

func (x *HealthCheckResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_balance_updater_balance_updater_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResp.ProtoReflect.Descriptor instead.
func (*HealthCheckResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_balance_updater_balance_updater_proto_rawDescGZIP(), []int{1}
}

func (x *HealthCheckResp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

// Запрос на обработку события от Процессингового Центра
type ProcessingCenterEventRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EventJson     string                 `protobuf:"bytes,1,opt,name=event_json,json=eventJson,proto3" json:"event_json,omitempty"` // JSON события от ПЦ
	Source        string                 `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`                        // Источник события (processing)
	Version       string                 `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`                      // Версия события (1.0)
	Kind          string                 `protobuf:"bytes,4,opt,name=kind,proto3" json:"kind,omitempty"`                            // Тип операции (Payback, FeeDebit, etc.)
	Rrn           string                 `protobuf:"bytes,5,opt,name=rrn,proto3" json:"rrn,omitempty"`                              // Reference Retrieval Number
	RequestId     string                 `protobuf:"bytes,6,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"` // ID запроса для логирования
	Currency      string                 `protobuf:"bytes,7,opt,name=currency,proto3" json:"currency,omitempty"`                    // Код валюты
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProcessingCenterEventRequest) Reset() {
	*x = ProcessingCenterEventRequest{}
	mi := &file_specs_proto_balance_updater_balance_updater_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessingCenterEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessingCenterEventRequest) ProtoMessage() {}

func (x *ProcessingCenterEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_balance_updater_balance_updater_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessingCenterEventRequest.ProtoReflect.Descriptor instead.
func (*ProcessingCenterEventRequest) Descriptor() ([]byte, []int) {
	return file_specs_proto_balance_updater_balance_updater_proto_rawDescGZIP(), []int{2}
}

func (x *ProcessingCenterEventRequest) GetEventJson() string {
	if x != nil {
		return x.EventJson
	}
	return ""
}

func (x *ProcessingCenterEventRequest) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *ProcessingCenterEventRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ProcessingCenterEventRequest) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

func (x *ProcessingCenterEventRequest) GetRrn() string {
	if x != nil {
		return x.Rrn
	}
	return ""
}

func (x *ProcessingCenterEventRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *ProcessingCenterEventRequest) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

// Ответ на обработку события от ПЦ
type ProcessingCenterEventResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`                           // Успешность обработки
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`                            // Сообщение о результате
	OperationId   string                 `protobuf:"bytes,3,opt,name=operation_id,json=operationId,proto3" json:"operation_id,omitempty"` // ID операции для отслеживания
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProcessingCenterEventResponse) Reset() {
	*x = ProcessingCenterEventResponse{}
	mi := &file_specs_proto_balance_updater_balance_updater_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessingCenterEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessingCenterEventResponse) ProtoMessage() {}

func (x *ProcessingCenterEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_balance_updater_balance_updater_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessingCenterEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessingCenterEventResponse) Descriptor() ([]byte, []int) {
	return file_specs_proto_balance_updater_balance_updater_proto_rawDescGZIP(), []int{3}
}

func (x *ProcessingCenterEventResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ProcessingCenterEventResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ProcessingCenterEventResponse) GetOperationId() string {
	if x != nil {
		return x.OperationId
	}
	return ""
}

var File_specs_proto_balance_updater_balance_updater_proto protoreflect.FileDescriptor

const file_specs_proto_balance_updater_balance_updater_proto_rawDesc = "" +
	"\n" +
	"1specs/proto/balance-updater/balance-updater.proto\x12\x0ebalanceUpdater\"\x10\n" +
	"\x0eHealthCheckReq\")\n" +
	"\x0fHealthCheckResp\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\"\xd0\x01\n" +
	"\x1cProcessingCenterEventRequest\x12\x1d\n" +
	"\n" +
	"event_json\x18\x01 \x01(\tR\teventJson\x12\x16\n" +
	"\x06source\x18\x02 \x01(\tR\x06source\x12\x18\n" +
	"\aversion\x18\x03 \x01(\tR\aversion\x12\x12\n" +
	"\x04kind\x18\x04 \x01(\tR\x04kind\x12\x10\n" +
	"\x03rrn\x18\x05 \x01(\tR\x03rrn\x12\x1d\n" +
	"\n" +
	"request_id\x18\x06 \x01(\tR\trequestId\x12\x1a\n" +
	"\bcurrency\x18\a \x01(\tR\bcurrency\"v\n" +
	"\x1dProcessingCenterEventResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12!\n" +
	"\foperation_id\x18\x03 \x01(\tR\voperationId2\xdc\x01\n" +
	"\x0eBalanceupdater\x12N\n" +
	"\vHealthCheck\x12\x1e.balanceUpdater.HealthCheckReq\x1a\x1f.balanceUpdater.HealthCheckResp\x12z\n" +
	"\x1bHandleProcessingCenterEvent\x12,.balanceUpdater.ProcessingCenterEventRequest\x1a-.balanceUpdater.ProcessingCenterEventResponseB\x1dZ\x1bspecs/proto/balance-updaterb\x06proto3"

var (
	file_specs_proto_balance_updater_balance_updater_proto_rawDescOnce sync.Once
	file_specs_proto_balance_updater_balance_updater_proto_rawDescData []byte
)

func file_specs_proto_balance_updater_balance_updater_proto_rawDescGZIP() []byte {
	file_specs_proto_balance_updater_balance_updater_proto_rawDescOnce.Do(func() {
		file_specs_proto_balance_updater_balance_updater_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_specs_proto_balance_updater_balance_updater_proto_rawDesc), len(file_specs_proto_balance_updater_balance_updater_proto_rawDesc)))
	})
	return file_specs_proto_balance_updater_balance_updater_proto_rawDescData
}

var file_specs_proto_balance_updater_balance_updater_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_specs_proto_balance_updater_balance_updater_proto_goTypes = []any{
	(*HealthCheckReq)(nil),                // 0: balanceUpdater.HealthCheckReq
	(*HealthCheckResp)(nil),               // 1: balanceUpdater.HealthCheckResp
	(*ProcessingCenterEventRequest)(nil),  // 2: balanceUpdater.ProcessingCenterEventRequest
	(*ProcessingCenterEventResponse)(nil), // 3: balanceUpdater.ProcessingCenterEventResponse
}
var file_specs_proto_balance_updater_balance_updater_proto_depIdxs = []int32{
	0, // 0: balanceUpdater.Balanceupdater.HealthCheck:input_type -> balanceUpdater.HealthCheckReq
	2, // 1: balanceUpdater.Balanceupdater.HandleProcessingCenterEvent:input_type -> balanceUpdater.ProcessingCenterEventRequest
	1, // 2: balanceUpdater.Balanceupdater.HealthCheck:output_type -> balanceUpdater.HealthCheckResp
	3, // 3: balanceUpdater.Balanceupdater.HandleProcessingCenterEvent:output_type -> balanceUpdater.ProcessingCenterEventResponse
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_specs_proto_balance_updater_balance_updater_proto_init() }
func file_specs_proto_balance_updater_balance_updater_proto_init() {
	if File_specs_proto_balance_updater_balance_updater_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_specs_proto_balance_updater_balance_updater_proto_rawDesc), len(file_specs_proto_balance_updater_balance_updater_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_specs_proto_balance_updater_balance_updater_proto_goTypes,
		DependencyIndexes: file_specs_proto_balance_updater_balance_updater_proto_depIdxs,
		MessageInfos:      file_specs_proto_balance_updater_balance_updater_proto_msgTypes,
	}.Build()
	File_specs_proto_balance_updater_balance_updater_proto = out.File
	file_specs_proto_balance_updater_balance_updater_proto_goTypes = nil
	file_specs_proto_balance_updater_balance_updater_proto_depIdxs = nil
}
