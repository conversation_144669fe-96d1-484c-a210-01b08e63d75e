// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.32.1
// source: specs/proto/balance-updater/balance-updater.proto

package balance_updater

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Balanceupdater_HealthCheck_FullMethodName                 = "/balanceUpdater.Balanceupdater/HealthCheck"
	Balanceupdater_HandleProcessingCenterEvent_FullMethodName = "/balanceUpdater.Balanceupdater/HandleProcessingCenterEvent"
)

// BalanceupdaterClient is the client API for Balanceupdater service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BalanceupdaterClient interface {
	HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error)
	HandleProcessingCenterEvent(ctx context.Context, in *ProcessingCenterEventRequest, opts ...grpc.CallOption) (*ProcessingCenterEventResponse, error)
}

type balanceupdaterClient struct {
	cc grpc.ClientConnInterface
}

func NewBalanceupdaterClient(cc grpc.ClientConnInterface) BalanceupdaterClient {
	return &balanceupdaterClient{cc}
}

func (c *balanceupdaterClient) HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResp)
	err := c.cc.Invoke(ctx, Balanceupdater_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *balanceupdaterClient) HandleProcessingCenterEvent(ctx context.Context, in *ProcessingCenterEventRequest, opts ...grpc.CallOption) (*ProcessingCenterEventResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ProcessingCenterEventResponse)
	err := c.cc.Invoke(ctx, Balanceupdater_HandleProcessingCenterEvent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BalanceupdaterServer is the server API for Balanceupdater service.
// All implementations must embed UnimplementedBalanceupdaterServer
// for forward compatibility.
type BalanceupdaterServer interface {
	HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error)
	HandleProcessingCenterEvent(context.Context, *ProcessingCenterEventRequest) (*ProcessingCenterEventResponse, error)
	mustEmbedUnimplementedBalanceupdaterServer()
}

// UnimplementedBalanceupdaterServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBalanceupdaterServer struct{}

func (UnimplementedBalanceupdaterServer) HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedBalanceupdaterServer) HandleProcessingCenterEvent(context.Context, *ProcessingCenterEventRequest) (*ProcessingCenterEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleProcessingCenterEvent not implemented")
}
func (UnimplementedBalanceupdaterServer) mustEmbedUnimplementedBalanceupdaterServer() {}
func (UnimplementedBalanceupdaterServer) testEmbeddedByValue()                        {}

// UnsafeBalanceupdaterServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BalanceupdaterServer will
// result in compilation errors.
type UnsafeBalanceupdaterServer interface {
	mustEmbedUnimplementedBalanceupdaterServer()
}

func RegisterBalanceupdaterServer(s grpc.ServiceRegistrar, srv BalanceupdaterServer) {
	// If the following call pancis, it indicates UnimplementedBalanceupdaterServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Balanceupdater_ServiceDesc, srv)
}

func _Balanceupdater_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BalanceupdaterServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Balanceupdater_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BalanceupdaterServer).HealthCheck(ctx, req.(*HealthCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Balanceupdater_HandleProcessingCenterEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessingCenterEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BalanceupdaterServer).HandleProcessingCenterEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Balanceupdater_HandleProcessingCenterEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BalanceupdaterServer).HandleProcessingCenterEvent(ctx, req.(*ProcessingCenterEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Balanceupdater_ServiceDesc is the grpc.ServiceDesc for Balanceupdater service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Balanceupdater_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "balanceUpdater.Balanceupdater",
	HandlerType: (*BalanceupdaterServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HealthCheck",
			Handler:    _Balanceupdater_HealthCheck_Handler,
		},
		{
			MethodName: "HandleProcessingCenterEvent",
			Handler:    _Balanceupdater_HandleProcessingCenterEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "specs/proto/balance-updater/balance-updater.proto",
}
