// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.32.1
// source: specs/proto/bsas-bridge/bsas-bridge.proto

package bsas_bridge

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Bsasbridge_HealthCheck_FullMethodName      = "/bsasBridge.Bsasbridge/HealthCheck"
	Bsasbridge_CreateOrder_FullMethodName      = "/bsasBridge.Bsasbridge/CreateOrder"
	Bsasbridge_GetOrderResult_FullMethodName   = "/bsasBridge.Bsasbridge/GetOrderResult"
	Bsasbridge_GetSystemMapping_FullMethodName = "/bsasBridge.Bsasbridge/GetSystemMapping"
	Bsasbridge_GetSellToBMIS_FullMethodName    = "/bsasBridge.Bsasbridge/GetSellToBMIS"
	Bsasbridge_GetOtcReportInfo_FullMethodName = "/bsasBridge.Bsasbridge/GetOtcReportInfo"
)

// BsasbridgeClient is the client API for Bsasbridge service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BsasbridgeClient interface {
	HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error)
	CreateOrder(ctx context.Context, in *CreateOrderReq, opts ...grpc.CallOption) (*CreateOrderResp, error)
	GetOrderResult(ctx context.Context, in *GetOrderResultReq, opts ...grpc.CallOption) (*GetOrderResultResp, error)
	GetSystemMapping(ctx context.Context, in *GetSystemMappingReq, opts ...grpc.CallOption) (*GetSystemMappingResp, error)
	GetSellToBMIS(ctx context.Context, in *GetSellToBMISReq, opts ...grpc.CallOption) (*GetSellToBMISResp, error)
	GetOtcReportInfo(ctx context.Context, in *GetOtcReportInfoReq, opts ...grpc.CallOption) (*GetOtcReportInfoResp, error)
}

type bsasbridgeClient struct {
	cc grpc.ClientConnInterface
}

func NewBsasbridgeClient(cc grpc.ClientConnInterface) BsasbridgeClient {
	return &bsasbridgeClient{cc}
}

func (c *bsasbridgeClient) HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResp)
	err := c.cc.Invoke(ctx, Bsasbridge_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bsasbridgeClient) CreateOrder(ctx context.Context, in *CreateOrderReq, opts ...grpc.CallOption) (*CreateOrderResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateOrderResp)
	err := c.cc.Invoke(ctx, Bsasbridge_CreateOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bsasbridgeClient) GetOrderResult(ctx context.Context, in *GetOrderResultReq, opts ...grpc.CallOption) (*GetOrderResultResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetOrderResultResp)
	err := c.cc.Invoke(ctx, Bsasbridge_GetOrderResult_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bsasbridgeClient) GetSystemMapping(ctx context.Context, in *GetSystemMappingReq, opts ...grpc.CallOption) (*GetSystemMappingResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSystemMappingResp)
	err := c.cc.Invoke(ctx, Bsasbridge_GetSystemMapping_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bsasbridgeClient) GetSellToBMIS(ctx context.Context, in *GetSellToBMISReq, opts ...grpc.CallOption) (*GetSellToBMISResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSellToBMISResp)
	err := c.cc.Invoke(ctx, Bsasbridge_GetSellToBMIS_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bsasbridgeClient) GetOtcReportInfo(ctx context.Context, in *GetOtcReportInfoReq, opts ...grpc.CallOption) (*GetOtcReportInfoResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetOtcReportInfoResp)
	err := c.cc.Invoke(ctx, Bsasbridge_GetOtcReportInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BsasbridgeServer is the server API for Bsasbridge service.
// All implementations must embed UnimplementedBsasbridgeServer
// for forward compatibility.
type BsasbridgeServer interface {
	HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error)
	CreateOrder(context.Context, *CreateOrderReq) (*CreateOrderResp, error)
	GetOrderResult(context.Context, *GetOrderResultReq) (*GetOrderResultResp, error)
	GetSystemMapping(context.Context, *GetSystemMappingReq) (*GetSystemMappingResp, error)
	GetSellToBMIS(context.Context, *GetSellToBMISReq) (*GetSellToBMISResp, error)
	GetOtcReportInfo(context.Context, *GetOtcReportInfoReq) (*GetOtcReportInfoResp, error)
	mustEmbedUnimplementedBsasbridgeServer()
}

// UnimplementedBsasbridgeServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBsasbridgeServer struct{}

func (UnimplementedBsasbridgeServer) HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedBsasbridgeServer) CreateOrder(context.Context, *CreateOrderReq) (*CreateOrderResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrder not implemented")
}
func (UnimplementedBsasbridgeServer) GetOrderResult(context.Context, *GetOrderResultReq) (*GetOrderResultResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderResult not implemented")
}
func (UnimplementedBsasbridgeServer) GetSystemMapping(context.Context, *GetSystemMappingReq) (*GetSystemMappingResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSystemMapping not implemented")
}
func (UnimplementedBsasbridgeServer) GetSellToBMIS(context.Context, *GetSellToBMISReq) (*GetSellToBMISResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSellToBMIS not implemented")
}
func (UnimplementedBsasbridgeServer) GetOtcReportInfo(context.Context, *GetOtcReportInfoReq) (*GetOtcReportInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOtcReportInfo not implemented")
}
func (UnimplementedBsasbridgeServer) mustEmbedUnimplementedBsasbridgeServer() {}
func (UnimplementedBsasbridgeServer) testEmbeddedByValue()                    {}

// UnsafeBsasbridgeServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BsasbridgeServer will
// result in compilation errors.
type UnsafeBsasbridgeServer interface {
	mustEmbedUnimplementedBsasbridgeServer()
}

func RegisterBsasbridgeServer(s grpc.ServiceRegistrar, srv BsasbridgeServer) {
	// If the following call pancis, it indicates UnimplementedBsasbridgeServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Bsasbridge_ServiceDesc, srv)
}

func _Bsasbridge_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BsasbridgeServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bsasbridge_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BsasbridgeServer).HealthCheck(ctx, req.(*HealthCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bsasbridge_CreateOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BsasbridgeServer).CreateOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bsasbridge_CreateOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BsasbridgeServer).CreateOrder(ctx, req.(*CreateOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bsasbridge_GetOrderResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BsasbridgeServer).GetOrderResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bsasbridge_GetOrderResult_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BsasbridgeServer).GetOrderResult(ctx, req.(*GetOrderResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bsasbridge_GetSystemMapping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSystemMappingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BsasbridgeServer).GetSystemMapping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bsasbridge_GetSystemMapping_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BsasbridgeServer).GetSystemMapping(ctx, req.(*GetSystemMappingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bsasbridge_GetSellToBMIS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSellToBMISReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BsasbridgeServer).GetSellToBMIS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bsasbridge_GetSellToBMIS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BsasbridgeServer).GetSellToBMIS(ctx, req.(*GetSellToBMISReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bsasbridge_GetOtcReportInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOtcReportInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BsasbridgeServer).GetOtcReportInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bsasbridge_GetOtcReportInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BsasbridgeServer).GetOtcReportInfo(ctx, req.(*GetOtcReportInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Bsasbridge_ServiceDesc is the grpc.ServiceDesc for Bsasbridge service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Bsasbridge_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "bsasBridge.Bsasbridge",
	HandlerType: (*BsasbridgeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HealthCheck",
			Handler:    _Bsasbridge_HealthCheck_Handler,
		},
		{
			MethodName: "CreateOrder",
			Handler:    _Bsasbridge_CreateOrder_Handler,
		},
		{
			MethodName: "GetOrderResult",
			Handler:    _Bsasbridge_GetOrderResult_Handler,
		},
		{
			MethodName: "GetSystemMapping",
			Handler:    _Bsasbridge_GetSystemMapping_Handler,
		},
		{
			MethodName: "GetSellToBMIS",
			Handler:    _Bsasbridge_GetSellToBMIS_Handler,
		},
		{
			MethodName: "GetOtcReportInfo",
			Handler:    _Bsasbridge_GetOtcReportInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "specs/proto/bsas-bridge/bsas-bridge.proto",
}
