// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v6.32.1
// source: specs/proto/bsas-bridge/bsas-bridge.proto

package bsas_bridge

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HealthCheckReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckReq) Reset() {
	*x = HealthCheckReq{}
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckReq) ProtoMessage() {}

func (x *HealthCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckReq.ProtoReflect.Descriptor instead.
func (*HealthCheckReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP(), []int{0}
}

type HealthCheckResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckResp) Reset() {
	*x = HealthCheckResp{}
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResp) ProtoMessage() {}

func (x *HealthCheckResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResp.ProtoReflect.Descriptor instead.
func (*HealthCheckResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP(), []int{1}
}

func (x *HealthCheckResp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type CreateOrderReq struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Header        *CreateOrderHeaderRequest `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Request       []*CreateOrderRequest     `protobuf:"bytes,2,rep,name=request,proto3" json:"request,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateOrderReq) Reset() {
	*x = CreateOrderReq{}
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrderReq) ProtoMessage() {}

func (x *CreateOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrderReq.ProtoReflect.Descriptor instead.
func (*CreateOrderReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP(), []int{2}
}

func (x *CreateOrderReq) GetHeader() *CreateOrderHeaderRequest {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CreateOrderReq) GetRequest() []*CreateOrderRequest {
	if x != nil {
		return x.Request
	}
	return nil
}

type CreateOrderHeaderRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	MemberShortName string                 `protobuf:"bytes,1,opt,name=memberShortName,proto3" json:"memberShortName,omitempty"`
	Uuid            string                 `protobuf:"bytes,2,opt,name=uuid,proto3" json:"uuid,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CreateOrderHeaderRequest) Reset() {
	*x = CreateOrderHeaderRequest{}
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateOrderHeaderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrderHeaderRequest) ProtoMessage() {}

func (x *CreateOrderHeaderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrderHeaderRequest.ProtoReflect.Descriptor instead.
func (*CreateOrderHeaderRequest) Descriptor() ([]byte, []int) {
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP(), []int{3}
}

func (x *CreateOrderHeaderRequest) GetMemberShortName() string {
	if x != nil {
		return x.MemberShortName
	}
	return ""
}

func (x *CreateOrderHeaderRequest) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

type CreateOrderRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	SerialNumber     string                 `protobuf:"bytes,1,opt,name=serialNumber,proto3" json:"serialNumber,omitempty"`
	BidOption        string                 `protobuf:"bytes,2,opt,name=bidOption,proto3" json:"bidOption,omitempty"`
	OtcOption        string                 `protobuf:"bytes,3,opt,name=otcOption,proto3" json:"otcOption,omitempty"`
	StbOption        string                 `protobuf:"bytes,4,opt,name=stbOption,proto3" json:"stbOption,omitempty"`
	ProductCode      string                 `protobuf:"bytes,5,opt,name=productCode,proto3" json:"productCode,omitempty"`
	PurchaseType     string                 `protobuf:"bytes,6,opt,name=purchaseType,proto3" json:"purchaseType,omitempty"`
	ClientName       string                 `protobuf:"bytes,7,opt,name=clientName,proto3" json:"clientName,omitempty"`
	Currency         string                 `protobuf:"bytes,8,opt,name=currency,proto3" json:"currency,omitempty"`
	BidValue         string                 `protobuf:"bytes,9,opt,name=bidValue,proto3" json:"bidValue,omitempty"`
	ValueDate        string                 `protobuf:"bytes,10,opt,name=valueDate,proto3" json:"valueDate,omitempty"`
	Tenor            string                 `protobuf:"bytes,11,opt,name=tenor,proto3" json:"tenor,omitempty"`
	OtcCounterParty  string                 `protobuf:"bytes,12,opt,name=otcCounterParty,proto3" json:"otcCounterParty,omitempty"`
	OtcMurabaha      string                 `protobuf:"bytes,13,opt,name=otcMurabaha,proto3" json:"otcMurabaha,omitempty"`
	OtcMurabahaValue string                 `protobuf:"bytes,14,opt,name=otcMurabahaValue,proto3" json:"otcMurabahaValue,omitempty"`
	ECertNo          string                 `protobuf:"bytes,15,opt,name=eCertNo,proto3" json:"eCertNo,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CreateOrderRequest) Reset() {
	*x = CreateOrderRequest{}
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrderRequest) ProtoMessage() {}

func (x *CreateOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrderRequest.ProtoReflect.Descriptor instead.
func (*CreateOrderRequest) Descriptor() ([]byte, []int) {
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP(), []int{4}
}

func (x *CreateOrderRequest) GetSerialNumber() string {
	if x != nil {
		return x.SerialNumber
	}
	return ""
}

func (x *CreateOrderRequest) GetBidOption() string {
	if x != nil {
		return x.BidOption
	}
	return ""
}

func (x *CreateOrderRequest) GetOtcOption() string {
	if x != nil {
		return x.OtcOption
	}
	return ""
}

func (x *CreateOrderRequest) GetStbOption() string {
	if x != nil {
		return x.StbOption
	}
	return ""
}

func (x *CreateOrderRequest) GetProductCode() string {
	if x != nil {
		return x.ProductCode
	}
	return ""
}

func (x *CreateOrderRequest) GetPurchaseType() string {
	if x != nil {
		return x.PurchaseType
	}
	return ""
}

func (x *CreateOrderRequest) GetClientName() string {
	if x != nil {
		return x.ClientName
	}
	return ""
}

func (x *CreateOrderRequest) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *CreateOrderRequest) GetBidValue() string {
	if x != nil {
		return x.BidValue
	}
	return ""
}

func (x *CreateOrderRequest) GetValueDate() string {
	if x != nil {
		return x.ValueDate
	}
	return ""
}

func (x *CreateOrderRequest) GetTenor() string {
	if x != nil {
		return x.Tenor
	}
	return ""
}

func (x *CreateOrderRequest) GetOtcCounterParty() string {
	if x != nil {
		return x.OtcCounterParty
	}
	return ""
}

func (x *CreateOrderRequest) GetOtcMurabaha() string {
	if x != nil {
		return x.OtcMurabaha
	}
	return ""
}

func (x *CreateOrderRequest) GetOtcMurabahaValue() string {
	if x != nil {
		return x.OtcMurabahaValue
	}
	return ""
}

func (x *CreateOrderRequest) GetECertNo() string {
	if x != nil {
		return x.ECertNo
	}
	return ""
}

type CreateOrderResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *CreateOrderHeader     `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Body          []*CreateOrderBody     `protobuf:"bytes,2,rep,name=body,proto3" json:"body,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateOrderResp) Reset() {
	*x = CreateOrderResp{}
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateOrderResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrderResp) ProtoMessage() {}

func (x *CreateOrderResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrderResp.ProtoReflect.Descriptor instead.
func (*CreateOrderResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP(), []int{5}
}

func (x *CreateOrderResp) GetHeader() *CreateOrderHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CreateOrderResp) GetBody() []*CreateOrderBody {
	if x != nil {
		return x.Body
	}
	return nil
}

type CreateOrderHeader struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	MemberShortName string                 `protobuf:"bytes,1,opt,name=memberShortName,proto3" json:"memberShortName,omitempty"`
	Uuid            string                 `protobuf:"bytes,2,opt,name=uuid,proto3" json:"uuid,omitempty"`
	ErrorCode       *string                `protobuf:"bytes,3,opt,name=errorCode,proto3,oneof" json:"errorCode,omitempty"`
	ErrorMsg        *string                `protobuf:"bytes,4,opt,name=errorMsg,proto3,oneof" json:"errorMsg,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CreateOrderHeader) Reset() {
	*x = CreateOrderHeader{}
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateOrderHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrderHeader) ProtoMessage() {}

func (x *CreateOrderHeader) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrderHeader.ProtoReflect.Descriptor instead.
func (*CreateOrderHeader) Descriptor() ([]byte, []int) {
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP(), []int{6}
}

func (x *CreateOrderHeader) GetMemberShortName() string {
	if x != nil {
		return x.MemberShortName
	}
	return ""
}

func (x *CreateOrderHeader) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *CreateOrderHeader) GetErrorCode() string {
	if x != nil && x.ErrorCode != nil {
		return *x.ErrorCode
	}
	return ""
}

func (x *CreateOrderHeader) GetErrorMsg() string {
	if x != nil && x.ErrorMsg != nil {
		return *x.ErrorMsg
	}
	return ""
}

type CreateOrderBody struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SerialNumber  int32                  `protobuf:"varint,1,opt,name=serialNumber,proto3" json:"serialNumber,omitempty"`
	StatusCode    int32                  `protobuf:"varint,2,opt,name=statusCode,proto3" json:"statusCode,omitempty"`
	StatusMessage string                 `protobuf:"bytes,3,opt,name=statusMessage,proto3" json:"statusMessage,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateOrderBody) Reset() {
	*x = CreateOrderBody{}
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateOrderBody) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrderBody) ProtoMessage() {}

func (x *CreateOrderBody) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrderBody.ProtoReflect.Descriptor instead.
func (*CreateOrderBody) Descriptor() ([]byte, []int) {
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP(), []int{7}
}

func (x *CreateOrderBody) GetSerialNumber() int32 {
	if x != nil {
		return x.SerialNumber
	}
	return 0
}

func (x *CreateOrderBody) GetStatusCode() int32 {
	if x != nil {
		return x.StatusCode
	}
	return 0
}

func (x *CreateOrderBody) GetStatusMessage() string {
	if x != nil {
		return x.StatusMessage
	}
	return ""
}

type GetOrderResultReq struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Header        *CreateOrderHeaderRequest `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Request       *GetOrderResultRequest    `protobuf:"bytes,2,opt,name=request,proto3" json:"request,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrderResultReq) Reset() {
	*x = GetOrderResultReq{}
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrderResultReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderResultReq) ProtoMessage() {}

func (x *GetOrderResultReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderResultReq.ProtoReflect.Descriptor instead.
func (*GetOrderResultReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP(), []int{8}
}

func (x *GetOrderResultReq) GetHeader() *CreateOrderHeaderRequest {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetOrderResultReq) GetRequest() *GetOrderResultRequest {
	if x != nil {
		return x.Request
	}
	return nil
}

type GetOrderResultRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SerialNumbers *string                `protobuf:"bytes,1,opt,name=serialNumbers,proto3,oneof" json:"serialNumbers,omitempty"`
	ForceYN       string                 `protobuf:"bytes,2,opt,name=forceYN,proto3" json:"forceYN,omitempty"`
	MaxWaitTime   *string                `protobuf:"bytes,3,opt,name=maxWaitTime,proto3,oneof" json:"maxWaitTime,omitempty"`
	WaitAllDoneYN *string                `protobuf:"bytes,4,opt,name=waitAllDoneYN,proto3,oneof" json:"waitAllDoneYN,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrderResultRequest) Reset() {
	*x = GetOrderResultRequest{}
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrderResultRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderResultRequest) ProtoMessage() {}

func (x *GetOrderResultRequest) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderResultRequest.ProtoReflect.Descriptor instead.
func (*GetOrderResultRequest) Descriptor() ([]byte, []int) {
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP(), []int{9}
}

func (x *GetOrderResultRequest) GetSerialNumbers() string {
	if x != nil && x.SerialNumbers != nil {
		return *x.SerialNumbers
	}
	return ""
}

func (x *GetOrderResultRequest) GetForceYN() string {
	if x != nil {
		return x.ForceYN
	}
	return ""
}

func (x *GetOrderResultRequest) GetMaxWaitTime() string {
	if x != nil && x.MaxWaitTime != nil {
		return *x.MaxWaitTime
	}
	return ""
}

func (x *GetOrderResultRequest) GetWaitAllDoneYN() string {
	if x != nil && x.WaitAllDoneYN != nil {
		return *x.WaitAllDoneYN
	}
	return ""
}

type GetOrderResultResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Header        *CreateOrderHeader     `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Status        *OrderStatus           `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	Body          []*GetOrderResultBody  `protobuf:"bytes,3,rep,name=body,proto3" json:"body,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrderResultResp) Reset() {
	*x = GetOrderResultResp{}
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrderResultResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderResultResp) ProtoMessage() {}

func (x *GetOrderResultResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderResultResp.ProtoReflect.Descriptor instead.
func (*GetOrderResultResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP(), []int{10}
}

func (x *GetOrderResultResp) GetHeader() *CreateOrderHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetOrderResultResp) GetStatus() *OrderStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetOrderResultResp) GetBody() []*GetOrderResultBody {
	if x != nil {
		return x.Body
	}
	return nil
}

type OrderStatus struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	TotalOrderCount int32                  `protobuf:"varint,1,opt,name=totalOrderCount,proto3" json:"totalOrderCount,omitempty"`
	ProcessingCount int32                  `protobuf:"varint,2,opt,name=processingCount,proto3" json:"processingCount,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *OrderStatus) Reset() {
	*x = OrderStatus{}
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderStatus) ProtoMessage() {}

func (x *OrderStatus) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderStatus.ProtoReflect.Descriptor instead.
func (*OrderStatus) Descriptor() ([]byte, []int) {
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP(), []int{11}
}

func (x *OrderStatus) GetTotalOrderCount() int32 {
	if x != nil {
		return x.TotalOrderCount
	}
	return 0
}

func (x *OrderStatus) GetProcessingCount() int32 {
	if x != nil {
		return x.ProcessingCount
	}
	return 0
}

type GetOrderResultBody struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	SerialNumber     int32                  `protobuf:"varint,1,opt,name=serialNumber,proto3" json:"serialNumber,omitempty"`
	BidOption        string                 `protobuf:"bytes,2,opt,name=bidOption,proto3" json:"bidOption,omitempty"`
	OtcOption        string                 `protobuf:"bytes,3,opt,name=otcOption,proto3" json:"otcOption,omitempty"`
	StbOption        string                 `protobuf:"bytes,4,opt,name=stbOption,proto3" json:"stbOption,omitempty"`
	ProductCode      string                 `protobuf:"bytes,5,opt,name=productCode,proto3" json:"productCode,omitempty"`
	PurchaseType     string                 `protobuf:"bytes,6,opt,name=purchaseType,proto3" json:"purchaseType,omitempty"`
	ClientName       string                 `protobuf:"bytes,7,opt,name=clientName,proto3" json:"clientName,omitempty"`
	Currency         string                 `protobuf:"bytes,8,opt,name=currency,proto3" json:"currency,omitempty"`
	BidValue         string                 `protobuf:"bytes,9,opt,name=bidValue,proto3" json:"bidValue,omitempty"`
	ValueDate        string                 `protobuf:"bytes,10,opt,name=valueDate,proto3" json:"valueDate,omitempty"`
	Tenor            string                 `protobuf:"bytes,11,opt,name=tenor,proto3" json:"tenor,omitempty"`
	OtcCounterParty  string                 `protobuf:"bytes,12,opt,name=otcCounterParty,proto3" json:"otcCounterParty,omitempty"`
	OtcMurabaha      string                 `protobuf:"bytes,13,opt,name=otcMurabaha,proto3" json:"otcMurabaha,omitempty"`
	OtcMurabahaValue string                 `protobuf:"bytes,14,opt,name=otcMurabahaValue,proto3" json:"otcMurabahaValue,omitempty"`
	EcertNo          string                 `protobuf:"bytes,15,opt,name=ecertNo,proto3" json:"ecertNo,omitempty"`
	BidErrNo         string                 `protobuf:"bytes,16,opt,name=bidErrNo,proto3" json:"bidErrNo,omitempty"`
	BidMsg           string                 `protobuf:"bytes,17,opt,name=bidMsg,proto3" json:"bidMsg,omitempty"`
	OtcErrNo         string                 `protobuf:"bytes,18,opt,name=otcErrNo,proto3" json:"otcErrNo,omitempty"`
	OtcMsg           string                 `protobuf:"bytes,19,opt,name=otcMsg,proto3" json:"otcMsg,omitempty"`
	StbErrNo         string                 `protobuf:"bytes,20,opt,name=stbErrNo,proto3" json:"stbErrNo,omitempty"`
	StbMsg           string                 `protobuf:"bytes,21,opt,name=stbMsg,proto3" json:"stbMsg,omitempty"`
	RegTime          string                 `protobuf:"bytes,22,opt,name=regTime,proto3" json:"regTime,omitempty"`
	OrderTime        string                 `protobuf:"bytes,23,opt,name=orderTime,proto3" json:"orderTime,omitempty"`
	ResultTime       string                 `protobuf:"bytes,24,opt,name=resultTime,proto3" json:"resultTime,omitempty"`
	PurchaseTime     string                 `protobuf:"bytes,25,opt,name=purchaseTime,proto3" json:"purchaseTime,omitempty"`
	ReportTime       string                 `protobuf:"bytes,26,opt,name=reportTime,proto3" json:"reportTime,omitempty"`
	SellingTime      string                 `protobuf:"bytes,27,opt,name=sellingTime,proto3" json:"sellingTime,omitempty"`
	Unit             string                 `protobuf:"bytes,28,opt,name=unit,proto3" json:"unit,omitempty"`
	Price            string                 `protobuf:"bytes,29,opt,name=price,proto3" json:"price,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *GetOrderResultBody) Reset() {
	*x = GetOrderResultBody{}
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrderResultBody) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderResultBody) ProtoMessage() {}

func (x *GetOrderResultBody) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderResultBody.ProtoReflect.Descriptor instead.
func (*GetOrderResultBody) Descriptor() ([]byte, []int) {
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP(), []int{12}
}

func (x *GetOrderResultBody) GetSerialNumber() int32 {
	if x != nil {
		return x.SerialNumber
	}
	return 0
}

func (x *GetOrderResultBody) GetBidOption() string {
	if x != nil {
		return x.BidOption
	}
	return ""
}

func (x *GetOrderResultBody) GetOtcOption() string {
	if x != nil {
		return x.OtcOption
	}
	return ""
}

func (x *GetOrderResultBody) GetStbOption() string {
	if x != nil {
		return x.StbOption
	}
	return ""
}

func (x *GetOrderResultBody) GetProductCode() string {
	if x != nil {
		return x.ProductCode
	}
	return ""
}

func (x *GetOrderResultBody) GetPurchaseType() string {
	if x != nil {
		return x.PurchaseType
	}
	return ""
}

func (x *GetOrderResultBody) GetClientName() string {
	if x != nil {
		return x.ClientName
	}
	return ""
}

func (x *GetOrderResultBody) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *GetOrderResultBody) GetBidValue() string {
	if x != nil {
		return x.BidValue
	}
	return ""
}

func (x *GetOrderResultBody) GetValueDate() string {
	if x != nil {
		return x.ValueDate
	}
	return ""
}

func (x *GetOrderResultBody) GetTenor() string {
	if x != nil {
		return x.Tenor
	}
	return ""
}

func (x *GetOrderResultBody) GetOtcCounterParty() string {
	if x != nil {
		return x.OtcCounterParty
	}
	return ""
}

func (x *GetOrderResultBody) GetOtcMurabaha() string {
	if x != nil {
		return x.OtcMurabaha
	}
	return ""
}

func (x *GetOrderResultBody) GetOtcMurabahaValue() string {
	if x != nil {
		return x.OtcMurabahaValue
	}
	return ""
}

func (x *GetOrderResultBody) GetEcertNo() string {
	if x != nil {
		return x.EcertNo
	}
	return ""
}

func (x *GetOrderResultBody) GetBidErrNo() string {
	if x != nil {
		return x.BidErrNo
	}
	return ""
}

func (x *GetOrderResultBody) GetBidMsg() string {
	if x != nil {
		return x.BidMsg
	}
	return ""
}

func (x *GetOrderResultBody) GetOtcErrNo() string {
	if x != nil {
		return x.OtcErrNo
	}
	return ""
}

func (x *GetOrderResultBody) GetOtcMsg() string {
	if x != nil {
		return x.OtcMsg
	}
	return ""
}

func (x *GetOrderResultBody) GetStbErrNo() string {
	if x != nil {
		return x.StbErrNo
	}
	return ""
}

func (x *GetOrderResultBody) GetStbMsg() string {
	if x != nil {
		return x.StbMsg
	}
	return ""
}

func (x *GetOrderResultBody) GetRegTime() string {
	if x != nil {
		return x.RegTime
	}
	return ""
}

func (x *GetOrderResultBody) GetOrderTime() string {
	if x != nil {
		return x.OrderTime
	}
	return ""
}

func (x *GetOrderResultBody) GetResultTime() string {
	if x != nil {
		return x.ResultTime
	}
	return ""
}

func (x *GetOrderResultBody) GetPurchaseTime() string {
	if x != nil {
		return x.PurchaseTime
	}
	return ""
}

func (x *GetOrderResultBody) GetReportTime() string {
	if x != nil {
		return x.ReportTime
	}
	return ""
}

func (x *GetOrderResultBody) GetSellingTime() string {
	if x != nil {
		return x.SellingTime
	}
	return ""
}

func (x *GetOrderResultBody) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *GetOrderResultBody) GetPrice() string {
	if x != nil {
		return x.Price
	}
	return ""
}

type GetSystemMappingReq struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ECertNo         string                 `protobuf:"bytes,1,opt,name=eCertNo,proto3" json:"eCertNo,omitempty"`
	MemberShortName string                 `protobuf:"bytes,2,opt,name=memberShortName,proto3" json:"memberShortName,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetSystemMappingReq) Reset() {
	*x = GetSystemMappingReq{}
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSystemMappingReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSystemMappingReq) ProtoMessage() {}

func (x *GetSystemMappingReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSystemMappingReq.ProtoReflect.Descriptor instead.
func (*GetSystemMappingReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP(), []int{13}
}

func (x *GetSystemMappingReq) GetECertNo() string {
	if x != nil {
		return x.ECertNo
	}
	return ""
}

func (x *GetSystemMappingReq) GetMemberShortName() string {
	if x != nil {
		return x.MemberShortName
	}
	return ""
}

type GetSystemMappingResp struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	SuccessYn        string                 `protobuf:"bytes,1,opt,name=success_yn,json=successYn,proto3" json:"success_yn,omitempty"`                         // Успех: null или N
	Msg              string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`                                                      // Причина отклонения
	EcertNo          string                 `protobuf:"bytes,3,opt,name=ecert_no,json=ecertNo,proto3" json:"ecert_no,omitempty"`                               // Номер электронного сертификата
	Buyer            string                 `protobuf:"bytes,4,opt,name=buyer,proto3" json:"buyer,omitempty"`                                                  // Имя покупателя
	Owner            string                 `protobuf:"bytes,5,opt,name=owner,proto3" json:"owner,omitempty"`                                                  // Имя владельца
	BidNo            string                 `protobuf:"bytes,6,opt,name=bid_no,json=bidNo,proto3" json:"bid_no,omitempty"`                                     // Номер заявки заказа
	TotalValue       string                 `protobuf:"bytes,7,opt,name=total_value,json=totalValue,proto3" json:"total_value,omitempty"`                      // Итоговая сумма
	Currency         string                 `protobuf:"bytes,8,opt,name=currency,proto3" json:"currency,omitempty"`                                            // Валюта
	Price            string                 `protobuf:"bytes,9,opt,name=price,proto3" json:"price,omitempty"`                                                  // Цена
	PriceMyrEquiv    string                 `protobuf:"bytes,10,opt,name=price_myr_equiv,json=priceMyrEquiv,proto3" json:"price_myr_equiv,omitempty"`          // Цена продукта, эквивалентная MYR
	PurchaseTimeDate string                 `protobuf:"bytes,11,opt,name=purchase_time_date,json=purchaseTimeDate,proto3" json:"purchase_time_date,omitempty"` // Дата и время покупки
	ValueDate        string                 `protobuf:"bytes,12,opt,name=value_date,json=valueDate,proto3" json:"value_date,omitempty"`                        // Дата валютирования
	Pname            string                 `protobuf:"bytes,13,opt,name=pname,proto3" json:"pname,omitempty"`                                                 // Наименование продукта
	Pvolume          string                 `protobuf:"bytes,14,opt,name=pvolume,proto3" json:"pvolume,omitempty"`                                             // Объем
	Line             []*BidXMLLine          `protobuf:"bytes,15,rep,name=line,proto3" json:"line,omitempty"`                                                   // Строки поставщиков
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *GetSystemMappingResp) Reset() {
	*x = GetSystemMappingResp{}
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSystemMappingResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSystemMappingResp) ProtoMessage() {}

func (x *GetSystemMappingResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSystemMappingResp.ProtoReflect.Descriptor instead.
func (*GetSystemMappingResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP(), []int{14}
}

func (x *GetSystemMappingResp) GetSuccessYn() string {
	if x != nil {
		return x.SuccessYn
	}
	return ""
}

func (x *GetSystemMappingResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetSystemMappingResp) GetEcertNo() string {
	if x != nil {
		return x.EcertNo
	}
	return ""
}

func (x *GetSystemMappingResp) GetBuyer() string {
	if x != nil {
		return x.Buyer
	}
	return ""
}

func (x *GetSystemMappingResp) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *GetSystemMappingResp) GetBidNo() string {
	if x != nil {
		return x.BidNo
	}
	return ""
}

func (x *GetSystemMappingResp) GetTotalValue() string {
	if x != nil {
		return x.TotalValue
	}
	return ""
}

func (x *GetSystemMappingResp) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *GetSystemMappingResp) GetPrice() string {
	if x != nil {
		return x.Price
	}
	return ""
}

func (x *GetSystemMappingResp) GetPriceMyrEquiv() string {
	if x != nil {
		return x.PriceMyrEquiv
	}
	return ""
}

func (x *GetSystemMappingResp) GetPurchaseTimeDate() string {
	if x != nil {
		return x.PurchaseTimeDate
	}
	return ""
}

func (x *GetSystemMappingResp) GetValueDate() string {
	if x != nil {
		return x.ValueDate
	}
	return ""
}

func (x *GetSystemMappingResp) GetPname() string {
	if x != nil {
		return x.Pname
	}
	return ""
}

func (x *GetSystemMappingResp) GetPvolume() string {
	if x != nil {
		return x.Pvolume
	}
	return ""
}

func (x *GetSystemMappingResp) GetLine() []*BidXMLLine {
	if x != nil {
		return x.Line
	}
	return nil
}

type BidXMLLine struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Supplier      string                 `protobuf:"bytes,1,opt,name=supplier,proto3" json:"supplier,omitempty"`
	Volume        string                 `protobuf:"bytes,2,opt,name=Volume,proto3" json:"Volume,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BidXMLLine) Reset() {
	*x = BidXMLLine{}
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BidXMLLine) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BidXMLLine) ProtoMessage() {}

func (x *BidXMLLine) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BidXMLLine.ProtoReflect.Descriptor instead.
func (*BidXMLLine) Descriptor() ([]byte, []int) {
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP(), []int{15}
}

func (x *BidXMLLine) GetSupplier() string {
	if x != nil {
		return x.Supplier
	}
	return ""
}

func (x *BidXMLLine) GetVolume() string {
	if x != nil {
		return x.Volume
	}
	return ""
}

type GetSellToBMISReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Input         *GetSellToBMISInput    `protobuf:"bytes,1,opt,name=input,proto3" json:"input,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSellToBMISReq) Reset() {
	*x = GetSellToBMISReq{}
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSellToBMISReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSellToBMISReq) ProtoMessage() {}

func (x *GetSellToBMISReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSellToBMISReq.ProtoReflect.Descriptor instead.
func (*GetSellToBMISReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP(), []int{16}
}

func (x *GetSellToBMISReq) GetInput() *GetSellToBMISInput {
	if x != nil {
		return x.Input
	}
	return nil
}

type GetSellToBMISInput struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Ecertno         string                 `protobuf:"bytes,1,opt,name=ecertno,proto3" json:"ecertno,omitempty"`
	Membershortname string                 `protobuf:"bytes,2,opt,name=membershortname,proto3" json:"membershortname,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetSellToBMISInput) Reset() {
	*x = GetSellToBMISInput{}
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSellToBMISInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSellToBMISInput) ProtoMessage() {}

func (x *GetSellToBMISInput) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSellToBMISInput.ProtoReflect.Descriptor instead.
func (*GetSellToBMISInput) Descriptor() ([]byte, []int) {
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP(), []int{17}
}

func (x *GetSellToBMISInput) GetEcertno() string {
	if x != nil {
		return x.Ecertno
	}
	return ""
}

func (x *GetSellToBMISInput) GetMembershortname() string {
	if x != nil {
		return x.Membershortname
	}
	return ""
}

type GetSellToBMISResp struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Ecertno            string                 `protobuf:"bytes,1,opt,name=ecertno,proto3" json:"ecertno,omitempty"`
	Seller             string                 `protobuf:"bytes,2,opt,name=seller,proto3" json:"seller,omitempty"`
	Buyer              string                 `protobuf:"bytes,3,opt,name=buyer,proto3" json:"buyer,omitempty"`
	TotalValue         string                 `protobuf:"bytes,4,opt,name=total_value,json=totalValue,proto3" json:"total_value,omitempty"`
	Currency           string                 `protobuf:"bytes,5,opt,name=currency,proto3" json:"currency,omitempty"`
	Price              string                 `protobuf:"bytes,6,opt,name=price,proto3" json:"price,omitempty"`
	PriceMyrEquivalent string                 `protobuf:"bytes,7,opt,name=price_myr_equivalent,json=priceMyrEquivalent,proto3" json:"price_myr_equivalent,omitempty"`
	SellingTimeDate    string                 `protobuf:"bytes,8,opt,name=selling_time_date,json=sellingTimeDate,proto3" json:"selling_time_date,omitempty"`
	ValueDate          string                 `protobuf:"bytes,9,opt,name=value_date,json=valueDate,proto3" json:"value_date,omitempty"`
	Pname              string                 `protobuf:"bytes,10,opt,name=pname,proto3" json:"pname,omitempty"`
	Pvolume            string                 `protobuf:"bytes,11,opt,name=pvolume,proto3" json:"pvolume,omitempty"`
	Line               []*LineItem            `protobuf:"bytes,12,rep,name=line,proto3" json:"line,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetSellToBMISResp) Reset() {
	*x = GetSellToBMISResp{}
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSellToBMISResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSellToBMISResp) ProtoMessage() {}

func (x *GetSellToBMISResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSellToBMISResp.ProtoReflect.Descriptor instead.
func (*GetSellToBMISResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP(), []int{18}
}

func (x *GetSellToBMISResp) GetEcertno() string {
	if x != nil {
		return x.Ecertno
	}
	return ""
}

func (x *GetSellToBMISResp) GetSeller() string {
	if x != nil {
		return x.Seller
	}
	return ""
}

func (x *GetSellToBMISResp) GetBuyer() string {
	if x != nil {
		return x.Buyer
	}
	return ""
}

func (x *GetSellToBMISResp) GetTotalValue() string {
	if x != nil {
		return x.TotalValue
	}
	return ""
}

func (x *GetSellToBMISResp) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *GetSellToBMISResp) GetPrice() string {
	if x != nil {
		return x.Price
	}
	return ""
}

func (x *GetSellToBMISResp) GetPriceMyrEquivalent() string {
	if x != nil {
		return x.PriceMyrEquivalent
	}
	return ""
}

func (x *GetSellToBMISResp) GetSellingTimeDate() string {
	if x != nil {
		return x.SellingTimeDate
	}
	return ""
}

func (x *GetSellToBMISResp) GetValueDate() string {
	if x != nil {
		return x.ValueDate
	}
	return ""
}

func (x *GetSellToBMISResp) GetPname() string {
	if x != nil {
		return x.Pname
	}
	return ""
}

func (x *GetSellToBMISResp) GetPvolume() string {
	if x != nil {
		return x.Pvolume
	}
	return ""
}

func (x *GetSellToBMISResp) GetLine() []*LineItem {
	if x != nil {
		return x.Line
	}
	return nil
}

type LineItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Supplier      string                 `protobuf:"bytes,1,opt,name=supplier,proto3" json:"supplier,omitempty"`
	Volume        string                 `protobuf:"bytes,2,opt,name=volume,proto3" json:"volume,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LineItem) Reset() {
	*x = LineItem{}
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LineItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LineItem) ProtoMessage() {}

func (x *LineItem) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LineItem.ProtoReflect.Descriptor instead.
func (*LineItem) Descriptor() ([]byte, []int) {
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP(), []int{19}
}

func (x *LineItem) GetSupplier() string {
	if x != nil {
		return x.Supplier
	}
	return ""
}

func (x *LineItem) GetVolume() string {
	if x != nil {
		return x.Volume
	}
	return ""
}

type GetOtcReportInfoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Input         *GetOtcReportInfoInput `protobuf:"bytes,1,opt,name=input,proto3" json:"input,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOtcReportInfoReq) Reset() {
	*x = GetOtcReportInfoReq{}
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOtcReportInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOtcReportInfoReq) ProtoMessage() {}

func (x *GetOtcReportInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOtcReportInfoReq.ProtoReflect.Descriptor instead.
func (*GetOtcReportInfoReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP(), []int{20}
}

func (x *GetOtcReportInfoReq) GetInput() *GetOtcReportInfoInput {
	if x != nil {
		return x.Input
	}
	return nil
}

type GetOtcReportInfoInput struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Ecertno         string                 `protobuf:"bytes,1,opt,name=ecertno,proto3" json:"ecertno,omitempty"`
	Membershortname string                 `protobuf:"bytes,2,opt,name=membershortname,proto3" json:"membershortname,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetOtcReportInfoInput) Reset() {
	*x = GetOtcReportInfoInput{}
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOtcReportInfoInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOtcReportInfoInput) ProtoMessage() {}

func (x *GetOtcReportInfoInput) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOtcReportInfoInput.ProtoReflect.Descriptor instead.
func (*GetOtcReportInfoInput) Descriptor() ([]byte, []int) {
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP(), []int{21}
}

func (x *GetOtcReportInfoInput) GetEcertno() string {
	if x != nil {
		return x.Ecertno
	}
	return ""
}

func (x *GetOtcReportInfoInput) GetMembershortname() string {
	if x != nil {
		return x.Membershortname
	}
	return ""
}

type GetOtcReportInfoResp struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Ecertno            string                 `protobuf:"bytes,1,opt,name=ecertno,proto3" json:"ecertno,omitempty"`
	Seller             string                 `protobuf:"bytes,2,opt,name=seller,proto3" json:"seller,omitempty"`
	Buyer              string                 `protobuf:"bytes,3,opt,name=buyer,proto3" json:"buyer,omitempty"`
	TotalValue         string                 `protobuf:"bytes,4,opt,name=total_value,json=totalValue,proto3" json:"total_value,omitempty"`
	Currency           string                 `protobuf:"bytes,5,opt,name=currency,proto3" json:"currency,omitempty"`
	Price              string                 `protobuf:"bytes,6,opt,name=price,proto3" json:"price,omitempty"`
	PriceMyrEquivalent string                 `protobuf:"bytes,7,opt,name=price_myr_equivalent,json=priceMyrEquivalent,proto3" json:"price_myr_equivalent,omitempty"`
	MurabahaValue      string                 `protobuf:"bytes,8,opt,name=murabaha_value,json=murabahaValue,proto3" json:"murabaha_value,omitempty"`
	ReportingTimeDate  string                 `protobuf:"bytes,9,opt,name=reporting_time_date,json=reportingTimeDate,proto3" json:"reporting_time_date,omitempty"`
	ValueDate          string                 `protobuf:"bytes,10,opt,name=value_date,json=valueDate,proto3" json:"value_date,omitempty"`
	Pname              string                 `protobuf:"bytes,11,opt,name=pname,proto3" json:"pname,omitempty"`
	Pvolume            string                 `protobuf:"bytes,12,opt,name=pvolume,proto3" json:"pvolume,omitempty"`
	Line               []*LineItem            `protobuf:"bytes,13,rep,name=line,proto3" json:"line,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetOtcReportInfoResp) Reset() {
	*x = GetOtcReportInfoResp{}
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOtcReportInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOtcReportInfoResp) ProtoMessage() {}

func (x *GetOtcReportInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOtcReportInfoResp.ProtoReflect.Descriptor instead.
func (*GetOtcReportInfoResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP(), []int{22}
}

func (x *GetOtcReportInfoResp) GetEcertno() string {
	if x != nil {
		return x.Ecertno
	}
	return ""
}

func (x *GetOtcReportInfoResp) GetSeller() string {
	if x != nil {
		return x.Seller
	}
	return ""
}

func (x *GetOtcReportInfoResp) GetBuyer() string {
	if x != nil {
		return x.Buyer
	}
	return ""
}

func (x *GetOtcReportInfoResp) GetTotalValue() string {
	if x != nil {
		return x.TotalValue
	}
	return ""
}

func (x *GetOtcReportInfoResp) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *GetOtcReportInfoResp) GetPrice() string {
	if x != nil {
		return x.Price
	}
	return ""
}

func (x *GetOtcReportInfoResp) GetPriceMyrEquivalent() string {
	if x != nil {
		return x.PriceMyrEquivalent
	}
	return ""
}

func (x *GetOtcReportInfoResp) GetMurabahaValue() string {
	if x != nil {
		return x.MurabahaValue
	}
	return ""
}

func (x *GetOtcReportInfoResp) GetReportingTimeDate() string {
	if x != nil {
		return x.ReportingTimeDate
	}
	return ""
}

func (x *GetOtcReportInfoResp) GetValueDate() string {
	if x != nil {
		return x.ValueDate
	}
	return ""
}

func (x *GetOtcReportInfoResp) GetPname() string {
	if x != nil {
		return x.Pname
	}
	return ""
}

func (x *GetOtcReportInfoResp) GetPvolume() string {
	if x != nil {
		return x.Pvolume
	}
	return ""
}

func (x *GetOtcReportInfoResp) GetLine() []*LineItem {
	if x != nil {
		return x.Line
	}
	return nil
}

var File_specs_proto_bsas_bridge_bsas_bridge_proto protoreflect.FileDescriptor

const file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDesc = "" +
	"\n" +
	")specs/proto/bsas-bridge/bsas-bridge.proto\x12\n" +
	"bsasBridge\"\x10\n" +
	"\x0eHealthCheckReq\")\n" +
	"\x0fHealthCheckResp\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\"\x88\x01\n" +
	"\x0eCreateOrderReq\x12<\n" +
	"\x06header\x18\x01 \x01(\v2$.bsasBridge.CreateOrderHeaderRequestR\x06header\x128\n" +
	"\arequest\x18\x02 \x03(\v2\x1e.bsasBridge.CreateOrderRequestR\arequest\"X\n" +
	"\x18CreateOrderHeaderRequest\x12(\n" +
	"\x0fmemberShortName\x18\x01 \x01(\tR\x0fmemberShortName\x12\x12\n" +
	"\x04uuid\x18\x02 \x01(\tR\x04uuid\"\xf6\x03\n" +
	"\x12CreateOrderRequest\x12\"\n" +
	"\fserialNumber\x18\x01 \x01(\tR\fserialNumber\x12\x1c\n" +
	"\tbidOption\x18\x02 \x01(\tR\tbidOption\x12\x1c\n" +
	"\totcOption\x18\x03 \x01(\tR\totcOption\x12\x1c\n" +
	"\tstbOption\x18\x04 \x01(\tR\tstbOption\x12 \n" +
	"\vproductCode\x18\x05 \x01(\tR\vproductCode\x12\"\n" +
	"\fpurchaseType\x18\x06 \x01(\tR\fpurchaseType\x12\x1e\n" +
	"\n" +
	"clientName\x18\a \x01(\tR\n" +
	"clientName\x12\x1a\n" +
	"\bcurrency\x18\b \x01(\tR\bcurrency\x12\x1a\n" +
	"\bbidValue\x18\t \x01(\tR\bbidValue\x12\x1c\n" +
	"\tvalueDate\x18\n" +
	" \x01(\tR\tvalueDate\x12\x14\n" +
	"\x05tenor\x18\v \x01(\tR\x05tenor\x12(\n" +
	"\x0fotcCounterParty\x18\f \x01(\tR\x0fotcCounterParty\x12 \n" +
	"\votcMurabaha\x18\r \x01(\tR\votcMurabaha\x12*\n" +
	"\x10otcMurabahaValue\x18\x0e \x01(\tR\x10otcMurabahaValue\x12\x18\n" +
	"\aeCertNo\x18\x0f \x01(\tR\aeCertNo\"y\n" +
	"\x0fCreateOrderResp\x125\n" +
	"\x06header\x18\x01 \x01(\v2\x1d.bsasBridge.CreateOrderHeaderR\x06header\x12/\n" +
	"\x04body\x18\x02 \x03(\v2\x1b.bsasBridge.CreateOrderBodyR\x04body\"\xb0\x01\n" +
	"\x11CreateOrderHeader\x12(\n" +
	"\x0fmemberShortName\x18\x01 \x01(\tR\x0fmemberShortName\x12\x12\n" +
	"\x04uuid\x18\x02 \x01(\tR\x04uuid\x12!\n" +
	"\terrorCode\x18\x03 \x01(\tH\x00R\terrorCode\x88\x01\x01\x12\x1f\n" +
	"\berrorMsg\x18\x04 \x01(\tH\x01R\berrorMsg\x88\x01\x01B\f\n" +
	"\n" +
	"_errorCodeB\v\n" +
	"\t_errorMsg\"{\n" +
	"\x0fCreateOrderBody\x12\"\n" +
	"\fserialNumber\x18\x01 \x01(\x05R\fserialNumber\x12\x1e\n" +
	"\n" +
	"statusCode\x18\x02 \x01(\x05R\n" +
	"statusCode\x12$\n" +
	"\rstatusMessage\x18\x03 \x01(\tR\rstatusMessage\"\x8e\x01\n" +
	"\x11GetOrderResultReq\x12<\n" +
	"\x06header\x18\x01 \x01(\v2$.bsasBridge.CreateOrderHeaderRequestR\x06header\x12;\n" +
	"\arequest\x18\x02 \x01(\v2!.bsasBridge.GetOrderResultRequestR\arequest\"\xe2\x01\n" +
	"\x15GetOrderResultRequest\x12)\n" +
	"\rserialNumbers\x18\x01 \x01(\tH\x00R\rserialNumbers\x88\x01\x01\x12\x18\n" +
	"\aforceYN\x18\x02 \x01(\tR\aforceYN\x12%\n" +
	"\vmaxWaitTime\x18\x03 \x01(\tH\x01R\vmaxWaitTime\x88\x01\x01\x12)\n" +
	"\rwaitAllDoneYN\x18\x04 \x01(\tH\x02R\rwaitAllDoneYN\x88\x01\x01B\x10\n" +
	"\x0e_serialNumbersB\x0e\n" +
	"\f_maxWaitTimeB\x10\n" +
	"\x0e_waitAllDoneYN\"\xb0\x01\n" +
	"\x12GetOrderResultResp\x125\n" +
	"\x06header\x18\x01 \x01(\v2\x1d.bsasBridge.CreateOrderHeaderR\x06header\x12/\n" +
	"\x06status\x18\x02 \x01(\v2\x17.bsasBridge.OrderStatusR\x06status\x122\n" +
	"\x04body\x18\x03 \x03(\v2\x1e.bsasBridge.GetOrderResultBodyR\x04body\"a\n" +
	"\vOrderStatus\x12(\n" +
	"\x0ftotalOrderCount\x18\x01 \x01(\x05R\x0ftotalOrderCount\x12(\n" +
	"\x0fprocessingCount\x18\x02 \x01(\x05R\x0fprocessingCount\"\xfa\x06\n" +
	"\x12GetOrderResultBody\x12\"\n" +
	"\fserialNumber\x18\x01 \x01(\x05R\fserialNumber\x12\x1c\n" +
	"\tbidOption\x18\x02 \x01(\tR\tbidOption\x12\x1c\n" +
	"\totcOption\x18\x03 \x01(\tR\totcOption\x12\x1c\n" +
	"\tstbOption\x18\x04 \x01(\tR\tstbOption\x12 \n" +
	"\vproductCode\x18\x05 \x01(\tR\vproductCode\x12\"\n" +
	"\fpurchaseType\x18\x06 \x01(\tR\fpurchaseType\x12\x1e\n" +
	"\n" +
	"clientName\x18\a \x01(\tR\n" +
	"clientName\x12\x1a\n" +
	"\bcurrency\x18\b \x01(\tR\bcurrency\x12\x1a\n" +
	"\bbidValue\x18\t \x01(\tR\bbidValue\x12\x1c\n" +
	"\tvalueDate\x18\n" +
	" \x01(\tR\tvalueDate\x12\x14\n" +
	"\x05tenor\x18\v \x01(\tR\x05tenor\x12(\n" +
	"\x0fotcCounterParty\x18\f \x01(\tR\x0fotcCounterParty\x12 \n" +
	"\votcMurabaha\x18\r \x01(\tR\votcMurabaha\x12*\n" +
	"\x10otcMurabahaValue\x18\x0e \x01(\tR\x10otcMurabahaValue\x12\x18\n" +
	"\aecertNo\x18\x0f \x01(\tR\aecertNo\x12\x1a\n" +
	"\bbidErrNo\x18\x10 \x01(\tR\bbidErrNo\x12\x16\n" +
	"\x06bidMsg\x18\x11 \x01(\tR\x06bidMsg\x12\x1a\n" +
	"\botcErrNo\x18\x12 \x01(\tR\botcErrNo\x12\x16\n" +
	"\x06otcMsg\x18\x13 \x01(\tR\x06otcMsg\x12\x1a\n" +
	"\bstbErrNo\x18\x14 \x01(\tR\bstbErrNo\x12\x16\n" +
	"\x06stbMsg\x18\x15 \x01(\tR\x06stbMsg\x12\x18\n" +
	"\aregTime\x18\x16 \x01(\tR\aregTime\x12\x1c\n" +
	"\torderTime\x18\x17 \x01(\tR\torderTime\x12\x1e\n" +
	"\n" +
	"resultTime\x18\x18 \x01(\tR\n" +
	"resultTime\x12\"\n" +
	"\fpurchaseTime\x18\x19 \x01(\tR\fpurchaseTime\x12\x1e\n" +
	"\n" +
	"reportTime\x18\x1a \x01(\tR\n" +
	"reportTime\x12 \n" +
	"\vsellingTime\x18\x1b \x01(\tR\vsellingTime\x12\x12\n" +
	"\x04unit\x18\x1c \x01(\tR\x04unit\x12\x14\n" +
	"\x05price\x18\x1d \x01(\tR\x05price\"Y\n" +
	"\x13GetSystemMappingReq\x12\x18\n" +
	"\aeCertNo\x18\x01 \x01(\tR\aeCertNo\x12(\n" +
	"\x0fmemberShortName\x18\x02 \x01(\tR\x0fmemberShortName\"\xc9\x03\n" +
	"\x14GetSystemMappingResp\x12\x1d\n" +
	"\n" +
	"success_yn\x18\x01 \x01(\tR\tsuccessYn\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12\x19\n" +
	"\becert_no\x18\x03 \x01(\tR\aecertNo\x12\x14\n" +
	"\x05buyer\x18\x04 \x01(\tR\x05buyer\x12\x14\n" +
	"\x05owner\x18\x05 \x01(\tR\x05owner\x12\x15\n" +
	"\x06bid_no\x18\x06 \x01(\tR\x05bidNo\x12\x1f\n" +
	"\vtotal_value\x18\a \x01(\tR\n" +
	"totalValue\x12\x1a\n" +
	"\bcurrency\x18\b \x01(\tR\bcurrency\x12\x14\n" +
	"\x05price\x18\t \x01(\tR\x05price\x12&\n" +
	"\x0fprice_myr_equiv\x18\n" +
	" \x01(\tR\rpriceMyrEquiv\x12,\n" +
	"\x12purchase_time_date\x18\v \x01(\tR\x10purchaseTimeDate\x12\x1d\n" +
	"\n" +
	"value_date\x18\f \x01(\tR\tvalueDate\x12\x14\n" +
	"\x05pname\x18\r \x01(\tR\x05pname\x12\x18\n" +
	"\apvolume\x18\x0e \x01(\tR\apvolume\x12*\n" +
	"\x04line\x18\x0f \x03(\v2\x16.bsasBridge.BidXMLLineR\x04line\"@\n" +
	"\n" +
	"BidXMLLine\x12\x1a\n" +
	"\bsupplier\x18\x01 \x01(\tR\bsupplier\x12\x16\n" +
	"\x06Volume\x18\x02 \x01(\tR\x06Volume\"H\n" +
	"\x10GetSellToBMISReq\x124\n" +
	"\x05input\x18\x01 \x01(\v2\x1e.bsasBridge.GetSellToBMISInputR\x05input\"X\n" +
	"\x12GetSellToBMISInput\x12\x18\n" +
	"\aecertno\x18\x01 \x01(\tR\aecertno\x12(\n" +
	"\x0fmembershortname\x18\x02 \x01(\tR\x0fmembershortname\"\x85\x03\n" +
	"\x11GetSellToBMISResp\x12\x18\n" +
	"\aecertno\x18\x01 \x01(\tR\aecertno\x12\x16\n" +
	"\x06seller\x18\x02 \x01(\tR\x06seller\x12\x14\n" +
	"\x05buyer\x18\x03 \x01(\tR\x05buyer\x12\x1f\n" +
	"\vtotal_value\x18\x04 \x01(\tR\n" +
	"totalValue\x12\x1a\n" +
	"\bcurrency\x18\x05 \x01(\tR\bcurrency\x12\x14\n" +
	"\x05price\x18\x06 \x01(\tR\x05price\x120\n" +
	"\x14price_myr_equivalent\x18\a \x01(\tR\x12priceMyrEquivalent\x12*\n" +
	"\x11selling_time_date\x18\b \x01(\tR\x0fsellingTimeDate\x12\x1d\n" +
	"\n" +
	"value_date\x18\t \x01(\tR\tvalueDate\x12\x14\n" +
	"\x05pname\x18\n" +
	" \x01(\tR\x05pname\x12\x18\n" +
	"\apvolume\x18\v \x01(\tR\apvolume\x12(\n" +
	"\x04line\x18\f \x03(\v2\x14.bsasBridge.LineItemR\x04line\">\n" +
	"\bLineItem\x12\x1a\n" +
	"\bsupplier\x18\x01 \x01(\tR\bsupplier\x12\x16\n" +
	"\x06volume\x18\x02 \x01(\tR\x06volume\"N\n" +
	"\x13GetOtcReportInfoReq\x127\n" +
	"\x05input\x18\x01 \x01(\v2!.bsasBridge.GetOtcReportInfoInputR\x05input\"[\n" +
	"\x15GetOtcReportInfoInput\x12\x18\n" +
	"\aecertno\x18\x01 \x01(\tR\aecertno\x12(\n" +
	"\x0fmembershortname\x18\x02 \x01(\tR\x0fmembershortname\"\xb3\x03\n" +
	"\x14GetOtcReportInfoResp\x12\x18\n" +
	"\aecertno\x18\x01 \x01(\tR\aecertno\x12\x16\n" +
	"\x06seller\x18\x02 \x01(\tR\x06seller\x12\x14\n" +
	"\x05buyer\x18\x03 \x01(\tR\x05buyer\x12\x1f\n" +
	"\vtotal_value\x18\x04 \x01(\tR\n" +
	"totalValue\x12\x1a\n" +
	"\bcurrency\x18\x05 \x01(\tR\bcurrency\x12\x14\n" +
	"\x05price\x18\x06 \x01(\tR\x05price\x120\n" +
	"\x14price_myr_equivalent\x18\a \x01(\tR\x12priceMyrEquivalent\x12%\n" +
	"\x0emurabaha_value\x18\b \x01(\tR\rmurabahaValue\x12.\n" +
	"\x13reporting_time_date\x18\t \x01(\tR\x11reportingTimeDate\x12\x1d\n" +
	"\n" +
	"value_date\x18\n" +
	" \x01(\tR\tvalueDate\x12\x14\n" +
	"\x05pname\x18\v \x01(\tR\x05pname\x12\x18\n" +
	"\apvolume\x18\f \x01(\tR\apvolume\x12(\n" +
	"\x04line\x18\r \x03(\v2\x14.bsasBridge.LineItemR\x04line2\xe9\x03\n" +
	"\n" +
	"Bsasbridge\x12F\n" +
	"\vHealthCheck\x12\x1a.bsasBridge.HealthCheckReq\x1a\x1b.bsasBridge.HealthCheckResp\x12F\n" +
	"\vCreateOrder\x12\x1a.bsasBridge.CreateOrderReq\x1a\x1b.bsasBridge.CreateOrderResp\x12O\n" +
	"\x0eGetOrderResult\x12\x1d.bsasBridge.GetOrderResultReq\x1a\x1e.bsasBridge.GetOrderResultResp\x12U\n" +
	"\x10GetSystemMapping\x12\x1f.bsasBridge.GetSystemMappingReq\x1a .bsasBridge.GetSystemMappingResp\x12L\n" +
	"\rGetSellToBMIS\x12\x1c.bsasBridge.GetSellToBMISReq\x1a\x1d.bsasBridge.GetSellToBMISResp\x12U\n" +
	"\x10GetOtcReportInfo\x12\x1f.bsasBridge.GetOtcReportInfoReq\x1a .bsasBridge.GetOtcReportInfoRespB\x19Z\x17specs/proto/bsas-bridgeb\x06proto3"

var (
	file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescOnce sync.Once
	file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescData []byte
)

func file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescGZIP() []byte {
	file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescOnce.Do(func() {
		file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDesc), len(file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDesc)))
	})
	return file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDescData
}

var file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_specs_proto_bsas_bridge_bsas_bridge_proto_goTypes = []any{
	(*HealthCheckReq)(nil),           // 0: bsasBridge.HealthCheckReq
	(*HealthCheckResp)(nil),          // 1: bsasBridge.HealthCheckResp
	(*CreateOrderReq)(nil),           // 2: bsasBridge.CreateOrderReq
	(*CreateOrderHeaderRequest)(nil), // 3: bsasBridge.CreateOrderHeaderRequest
	(*CreateOrderRequest)(nil),       // 4: bsasBridge.CreateOrderRequest
	(*CreateOrderResp)(nil),          // 5: bsasBridge.CreateOrderResp
	(*CreateOrderHeader)(nil),        // 6: bsasBridge.CreateOrderHeader
	(*CreateOrderBody)(nil),          // 7: bsasBridge.CreateOrderBody
	(*GetOrderResultReq)(nil),        // 8: bsasBridge.GetOrderResultReq
	(*GetOrderResultRequest)(nil),    // 9: bsasBridge.GetOrderResultRequest
	(*GetOrderResultResp)(nil),       // 10: bsasBridge.GetOrderResultResp
	(*OrderStatus)(nil),              // 11: bsasBridge.OrderStatus
	(*GetOrderResultBody)(nil),       // 12: bsasBridge.GetOrderResultBody
	(*GetSystemMappingReq)(nil),      // 13: bsasBridge.GetSystemMappingReq
	(*GetSystemMappingResp)(nil),     // 14: bsasBridge.GetSystemMappingResp
	(*BidXMLLine)(nil),               // 15: bsasBridge.BidXMLLine
	(*GetSellToBMISReq)(nil),         // 16: bsasBridge.GetSellToBMISReq
	(*GetSellToBMISInput)(nil),       // 17: bsasBridge.GetSellToBMISInput
	(*GetSellToBMISResp)(nil),        // 18: bsasBridge.GetSellToBMISResp
	(*LineItem)(nil),                 // 19: bsasBridge.LineItem
	(*GetOtcReportInfoReq)(nil),      // 20: bsasBridge.GetOtcReportInfoReq
	(*GetOtcReportInfoInput)(nil),    // 21: bsasBridge.GetOtcReportInfoInput
	(*GetOtcReportInfoResp)(nil),     // 22: bsasBridge.GetOtcReportInfoResp
}
var file_specs_proto_bsas_bridge_bsas_bridge_proto_depIdxs = []int32{
	3,  // 0: bsasBridge.CreateOrderReq.header:type_name -> bsasBridge.CreateOrderHeaderRequest
	4,  // 1: bsasBridge.CreateOrderReq.request:type_name -> bsasBridge.CreateOrderRequest
	6,  // 2: bsasBridge.CreateOrderResp.header:type_name -> bsasBridge.CreateOrderHeader
	7,  // 3: bsasBridge.CreateOrderResp.body:type_name -> bsasBridge.CreateOrderBody
	3,  // 4: bsasBridge.GetOrderResultReq.header:type_name -> bsasBridge.CreateOrderHeaderRequest
	9,  // 5: bsasBridge.GetOrderResultReq.request:type_name -> bsasBridge.GetOrderResultRequest
	6,  // 6: bsasBridge.GetOrderResultResp.header:type_name -> bsasBridge.CreateOrderHeader
	11, // 7: bsasBridge.GetOrderResultResp.status:type_name -> bsasBridge.OrderStatus
	12, // 8: bsasBridge.GetOrderResultResp.body:type_name -> bsasBridge.GetOrderResultBody
	15, // 9: bsasBridge.GetSystemMappingResp.line:type_name -> bsasBridge.BidXMLLine
	17, // 10: bsasBridge.GetSellToBMISReq.input:type_name -> bsasBridge.GetSellToBMISInput
	19, // 11: bsasBridge.GetSellToBMISResp.line:type_name -> bsasBridge.LineItem
	21, // 12: bsasBridge.GetOtcReportInfoReq.input:type_name -> bsasBridge.GetOtcReportInfoInput
	19, // 13: bsasBridge.GetOtcReportInfoResp.line:type_name -> bsasBridge.LineItem
	0,  // 14: bsasBridge.Bsasbridge.HealthCheck:input_type -> bsasBridge.HealthCheckReq
	2,  // 15: bsasBridge.Bsasbridge.CreateOrder:input_type -> bsasBridge.CreateOrderReq
	8,  // 16: bsasBridge.Bsasbridge.GetOrderResult:input_type -> bsasBridge.GetOrderResultReq
	13, // 17: bsasBridge.Bsasbridge.GetSystemMapping:input_type -> bsasBridge.GetSystemMappingReq
	16, // 18: bsasBridge.Bsasbridge.GetSellToBMIS:input_type -> bsasBridge.GetSellToBMISReq
	20, // 19: bsasBridge.Bsasbridge.GetOtcReportInfo:input_type -> bsasBridge.GetOtcReportInfoReq
	1,  // 20: bsasBridge.Bsasbridge.HealthCheck:output_type -> bsasBridge.HealthCheckResp
	5,  // 21: bsasBridge.Bsasbridge.CreateOrder:output_type -> bsasBridge.CreateOrderResp
	10, // 22: bsasBridge.Bsasbridge.GetOrderResult:output_type -> bsasBridge.GetOrderResultResp
	14, // 23: bsasBridge.Bsasbridge.GetSystemMapping:output_type -> bsasBridge.GetSystemMappingResp
	18, // 24: bsasBridge.Bsasbridge.GetSellToBMIS:output_type -> bsasBridge.GetSellToBMISResp
	22, // 25: bsasBridge.Bsasbridge.GetOtcReportInfo:output_type -> bsasBridge.GetOtcReportInfoResp
	20, // [20:26] is the sub-list for method output_type
	14, // [14:20] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_specs_proto_bsas_bridge_bsas_bridge_proto_init() }
func file_specs_proto_bsas_bridge_bsas_bridge_proto_init() {
	if File_specs_proto_bsas_bridge_bsas_bridge_proto != nil {
		return
	}
	file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[6].OneofWrappers = []any{}
	file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes[9].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDesc), len(file_specs_proto_bsas_bridge_bsas_bridge_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_specs_proto_bsas_bridge_bsas_bridge_proto_goTypes,
		DependencyIndexes: file_specs_proto_bsas_bridge_bsas_bridge_proto_depIdxs,
		MessageInfos:      file_specs_proto_bsas_bridge_bsas_bridge_proto_msgTypes,
	}.Build()
	File_specs_proto_bsas_bridge_bsas_bridge_proto = out.File
	file_specs_proto_bsas_bridge_bsas_bridge_proto_goTypes = nil
	file_specs_proto_bsas_bridge_bsas_bridge_proto_depIdxs = nil
}
