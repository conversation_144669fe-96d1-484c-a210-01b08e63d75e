// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.32.1
// source: specs/proto/bitrix-bridge/bitrix-bridge.proto

package bitrix_bridge

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Bitrixbridge_HealthCheck_FullMethodName = "/bitrixBridge.Bitrixbridge/HealthCheck"
)

// BitrixbridgeClient is the client API for Bitrixbridge service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BitrixbridgeClient interface {
	HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error)
}

type bitrixbridgeClient struct {
	cc grpc.ClientConnInterface
}

func NewBitrixbridgeClient(cc grpc.ClientConnInterface) BitrixbridgeClient {
	return &bitrixbridgeClient{cc}
}

func (c *bitrixbridgeClient) HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResp)
	err := c.cc.Invoke(ctx, Bitrixbridge_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BitrixbridgeServer is the server API for Bitrixbridge service.
// All implementations must embed UnimplementedBitrixbridgeServer
// for forward compatibility.
type BitrixbridgeServer interface {
	HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error)
	mustEmbedUnimplementedBitrixbridgeServer()
}

// UnimplementedBitrixbridgeServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBitrixbridgeServer struct{}

func (UnimplementedBitrixbridgeServer) HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedBitrixbridgeServer) mustEmbedUnimplementedBitrixbridgeServer() {}
func (UnimplementedBitrixbridgeServer) testEmbeddedByValue()                      {}

// UnsafeBitrixbridgeServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BitrixbridgeServer will
// result in compilation errors.
type UnsafeBitrixbridgeServer interface {
	mustEmbedUnimplementedBitrixbridgeServer()
}

func RegisterBitrixbridgeServer(s grpc.ServiceRegistrar, srv BitrixbridgeServer) {
	// If the following call pancis, it indicates UnimplementedBitrixbridgeServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Bitrixbridge_ServiceDesc, srv)
}

func _Bitrixbridge_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BitrixbridgeServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bitrixbridge_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BitrixbridgeServer).HealthCheck(ctx, req.(*HealthCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Bitrixbridge_ServiceDesc is the grpc.ServiceDesc for Bitrixbridge service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Bitrixbridge_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "bitrixBridge.Bitrixbridge",
	HandlerType: (*BitrixbridgeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HealthCheck",
			Handler:    _Bitrixbridge_HealthCheck_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "specs/proto/bitrix-bridge/bitrix-bridge.proto",
}
