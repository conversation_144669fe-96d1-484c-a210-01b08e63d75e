// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.32.1
// source: specs/proto/dictionary/dictionary.proto

package dictionary

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Dictionary_HealthCheck_FullMethodName        = "/dictionary.Dictionary/HealthCheck"
	Dictionary_DictCreate_FullMethodName         = "/dictionary.Dictionary/DictCreate"
	Dictionary_DictUpdate_FullMethodName         = "/dictionary.Dictionary/DictUpdate"
	Dictionary_DictDelete_FullMethodName         = "/dictionary.Dictionary/DictDelete"
	Dictionary_DictGet_FullMethodName            = "/dictionary.Dictionary/DictGet"
	Dictionary_DictGetByName_FullMethodName      = "/dictionary.Dictionary/DictGetByName"
	Dictionary_DictGetList_FullMethodName        = "/dictionary.Dictionary/DictGetList"
	Dictionary_DocCreate_FullMethodName          = "/dictionary.Dictionary/DocCreate"
	Dictionary_DocUpdate_FullMethodName          = "/dictionary.Dictionary/DocUpdate"
	Dictionary_DocOrderUpdate_FullMethodName     = "/dictionary.Dictionary/DocOrderUpdate"
	Dictionary_DocDelete_FullMethodName          = "/dictionary.Dictionary/DocDelete"
	Dictionary_DocGet_FullMethodName             = "/dictionary.Dictionary/DocGet"
	Dictionary_DocGetList_FullMethodName         = "/dictionary.Dictionary/DocGetList"
	Dictionary_DocGetByName_FullMethodName       = "/dictionary.Dictionary/DocGetByName"
	Dictionary_DocGetListByName_FullMethodName   = "/dictionary.Dictionary/DocGetListByName"
	Dictionary_DocGetListByFilter_FullMethodName = "/dictionary.Dictionary/DocGetListByFilter"
	Dictionary_DocTreeGetLine_FullMethodName     = "/dictionary.Dictionary/DocTreeGetLine"
	Dictionary_JobRun_FullMethodName             = "/dictionary.Dictionary/JobRun"
	Dictionary_JobGetStatus_FullMethodName       = "/dictionary.Dictionary/JobGetStatus"
	Dictionary_JobStop_FullMethodName            = "/dictionary.Dictionary/JobStop"
	Dictionary_JobGetStatusAll_FullMethodName    = "/dictionary.Dictionary/JobGetStatusAll"
	Dictionary_KATOMapFromTSOID_FullMethodName   = "/dictionary.Dictionary/KATOMapFromTSOID"
	Dictionary_KATOToABIS_FullMethodName         = "/dictionary.Dictionary/KATOToABIS"
	Dictionary_GetPostalIndex_FullMethodName     = "/dictionary.Dictionary/GetPostalIndex"
)

// DictionaryClient is the client API for Dictionary service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DictionaryClient interface {
	HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error)
	DictCreate(ctx context.Context, in *DictCreateReq, opts ...grpc.CallOption) (*DictCreateResp, error)
	DictUpdate(ctx context.Context, in *DictUpdateReq, opts ...grpc.CallOption) (*DictUpdateResp, error)
	DictDelete(ctx context.Context, in *DictDeleteReq, opts ...grpc.CallOption) (*DictDeleteResp, error)
	DictGet(ctx context.Context, in *DictGetReq, opts ...grpc.CallOption) (*DictGetResp, error)
	DictGetByName(ctx context.Context, in *DictGetByNameReq, opts ...grpc.CallOption) (*DictGetByNameResp, error)
	DictGetList(ctx context.Context, in *DictGetListReq, opts ...grpc.CallOption) (*DictGetListResp, error)
	DocCreate(ctx context.Context, in *DocCreateReq, opts ...grpc.CallOption) (*DocCreateResp, error)
	DocUpdate(ctx context.Context, in *DocUpdateReq, opts ...grpc.CallOption) (*DocUpdateResp, error)
	DocOrderUpdate(ctx context.Context, in *DocOrderUpdateReq, opts ...grpc.CallOption) (*DocOrderUpdateResp, error)
	DocDelete(ctx context.Context, in *DocDeleteReq, opts ...grpc.CallOption) (*DocDeleteResp, error)
	DocGet(ctx context.Context, in *DocGetReq, opts ...grpc.CallOption) (*DocGetResp, error)
	DocGetList(ctx context.Context, in *DocGetListReq, opts ...grpc.CallOption) (*DocGetListResp, error)
	DocGetByName(ctx context.Context, in *DocGetByNameReq, opts ...grpc.CallOption) (*DocGetByNameResp, error)
	DocGetListByName(ctx context.Context, in *DocGetListByNameReq, opts ...grpc.CallOption) (*DocGetListByNameResp, error)
	DocGetListByFilter(ctx context.Context, in *DocGetListByFilterReq, opts ...grpc.CallOption) (*DocGetListByFilterResp, error)
	DocTreeGetLine(ctx context.Context, in *DocTreeGetLineReq, opts ...grpc.CallOption) (*DocTreeGetLineResp, error)
	JobRun(ctx context.Context, in *JobRunReq, opts ...grpc.CallOption) (*JobRunResp, error)
	JobGetStatus(ctx context.Context, in *JobGetStatusReq, opts ...grpc.CallOption) (*JobGetStatusResp, error)
	JobStop(ctx context.Context, in *JobStopReq, opts ...grpc.CallOption) (*JobStopResp, error)
	JobGetStatusAll(ctx context.Context, in *JobGetStatusAllReq, opts ...grpc.CallOption) (*JobGetStatusAllResp, error)
	// Методы, специфичные для отдельных словарей
	KATOMapFromTSOID(ctx context.Context, in *KATOMapFromTSOIDReq, opts ...grpc.CallOption) (*KATOMapFromTSOIDResp, error)
	KATOToABIS(ctx context.Context, in *KATOToABISReq, opts ...grpc.CallOption) (*KATOToABISResp, error)
	// Retrieves the old postal index (SPI) for a given address.
	GetPostalIndex(ctx context.Context, in *GetPostalIndexReq, opts ...grpc.CallOption) (*GetPostalIndexResp, error)
}

type dictionaryClient struct {
	cc grpc.ClientConnInterface
}

func NewDictionaryClient(cc grpc.ClientConnInterface) DictionaryClient {
	return &dictionaryClient{cc}
}

func (c *dictionaryClient) HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResp)
	err := c.cc.Invoke(ctx, Dictionary_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictionaryClient) DictCreate(ctx context.Context, in *DictCreateReq, opts ...grpc.CallOption) (*DictCreateResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DictCreateResp)
	err := c.cc.Invoke(ctx, Dictionary_DictCreate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictionaryClient) DictUpdate(ctx context.Context, in *DictUpdateReq, opts ...grpc.CallOption) (*DictUpdateResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DictUpdateResp)
	err := c.cc.Invoke(ctx, Dictionary_DictUpdate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictionaryClient) DictDelete(ctx context.Context, in *DictDeleteReq, opts ...grpc.CallOption) (*DictDeleteResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DictDeleteResp)
	err := c.cc.Invoke(ctx, Dictionary_DictDelete_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictionaryClient) DictGet(ctx context.Context, in *DictGetReq, opts ...grpc.CallOption) (*DictGetResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DictGetResp)
	err := c.cc.Invoke(ctx, Dictionary_DictGet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictionaryClient) DictGetByName(ctx context.Context, in *DictGetByNameReq, opts ...grpc.CallOption) (*DictGetByNameResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DictGetByNameResp)
	err := c.cc.Invoke(ctx, Dictionary_DictGetByName_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictionaryClient) DictGetList(ctx context.Context, in *DictGetListReq, opts ...grpc.CallOption) (*DictGetListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DictGetListResp)
	err := c.cc.Invoke(ctx, Dictionary_DictGetList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictionaryClient) DocCreate(ctx context.Context, in *DocCreateReq, opts ...grpc.CallOption) (*DocCreateResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DocCreateResp)
	err := c.cc.Invoke(ctx, Dictionary_DocCreate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictionaryClient) DocUpdate(ctx context.Context, in *DocUpdateReq, opts ...grpc.CallOption) (*DocUpdateResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DocUpdateResp)
	err := c.cc.Invoke(ctx, Dictionary_DocUpdate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictionaryClient) DocOrderUpdate(ctx context.Context, in *DocOrderUpdateReq, opts ...grpc.CallOption) (*DocOrderUpdateResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DocOrderUpdateResp)
	err := c.cc.Invoke(ctx, Dictionary_DocOrderUpdate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictionaryClient) DocDelete(ctx context.Context, in *DocDeleteReq, opts ...grpc.CallOption) (*DocDeleteResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DocDeleteResp)
	err := c.cc.Invoke(ctx, Dictionary_DocDelete_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictionaryClient) DocGet(ctx context.Context, in *DocGetReq, opts ...grpc.CallOption) (*DocGetResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DocGetResp)
	err := c.cc.Invoke(ctx, Dictionary_DocGet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictionaryClient) DocGetList(ctx context.Context, in *DocGetListReq, opts ...grpc.CallOption) (*DocGetListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DocGetListResp)
	err := c.cc.Invoke(ctx, Dictionary_DocGetList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictionaryClient) DocGetByName(ctx context.Context, in *DocGetByNameReq, opts ...grpc.CallOption) (*DocGetByNameResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DocGetByNameResp)
	err := c.cc.Invoke(ctx, Dictionary_DocGetByName_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictionaryClient) DocGetListByName(ctx context.Context, in *DocGetListByNameReq, opts ...grpc.CallOption) (*DocGetListByNameResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DocGetListByNameResp)
	err := c.cc.Invoke(ctx, Dictionary_DocGetListByName_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictionaryClient) DocGetListByFilter(ctx context.Context, in *DocGetListByFilterReq, opts ...grpc.CallOption) (*DocGetListByFilterResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DocGetListByFilterResp)
	err := c.cc.Invoke(ctx, Dictionary_DocGetListByFilter_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictionaryClient) DocTreeGetLine(ctx context.Context, in *DocTreeGetLineReq, opts ...grpc.CallOption) (*DocTreeGetLineResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DocTreeGetLineResp)
	err := c.cc.Invoke(ctx, Dictionary_DocTreeGetLine_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictionaryClient) JobRun(ctx context.Context, in *JobRunReq, opts ...grpc.CallOption) (*JobRunResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JobRunResp)
	err := c.cc.Invoke(ctx, Dictionary_JobRun_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictionaryClient) JobGetStatus(ctx context.Context, in *JobGetStatusReq, opts ...grpc.CallOption) (*JobGetStatusResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JobGetStatusResp)
	err := c.cc.Invoke(ctx, Dictionary_JobGetStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictionaryClient) JobStop(ctx context.Context, in *JobStopReq, opts ...grpc.CallOption) (*JobStopResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JobStopResp)
	err := c.cc.Invoke(ctx, Dictionary_JobStop_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictionaryClient) JobGetStatusAll(ctx context.Context, in *JobGetStatusAllReq, opts ...grpc.CallOption) (*JobGetStatusAllResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JobGetStatusAllResp)
	err := c.cc.Invoke(ctx, Dictionary_JobGetStatusAll_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictionaryClient) KATOMapFromTSOID(ctx context.Context, in *KATOMapFromTSOIDReq, opts ...grpc.CallOption) (*KATOMapFromTSOIDResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(KATOMapFromTSOIDResp)
	err := c.cc.Invoke(ctx, Dictionary_KATOMapFromTSOID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictionaryClient) KATOToABIS(ctx context.Context, in *KATOToABISReq, opts ...grpc.CallOption) (*KATOToABISResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(KATOToABISResp)
	err := c.cc.Invoke(ctx, Dictionary_KATOToABIS_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictionaryClient) GetPostalIndex(ctx context.Context, in *GetPostalIndexReq, opts ...grpc.CallOption) (*GetPostalIndexResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPostalIndexResp)
	err := c.cc.Invoke(ctx, Dictionary_GetPostalIndex_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DictionaryServer is the server API for Dictionary service.
// All implementations must embed UnimplementedDictionaryServer
// for forward compatibility.
type DictionaryServer interface {
	HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error)
	DictCreate(context.Context, *DictCreateReq) (*DictCreateResp, error)
	DictUpdate(context.Context, *DictUpdateReq) (*DictUpdateResp, error)
	DictDelete(context.Context, *DictDeleteReq) (*DictDeleteResp, error)
	DictGet(context.Context, *DictGetReq) (*DictGetResp, error)
	DictGetByName(context.Context, *DictGetByNameReq) (*DictGetByNameResp, error)
	DictGetList(context.Context, *DictGetListReq) (*DictGetListResp, error)
	DocCreate(context.Context, *DocCreateReq) (*DocCreateResp, error)
	DocUpdate(context.Context, *DocUpdateReq) (*DocUpdateResp, error)
	DocOrderUpdate(context.Context, *DocOrderUpdateReq) (*DocOrderUpdateResp, error)
	DocDelete(context.Context, *DocDeleteReq) (*DocDeleteResp, error)
	DocGet(context.Context, *DocGetReq) (*DocGetResp, error)
	DocGetList(context.Context, *DocGetListReq) (*DocGetListResp, error)
	DocGetByName(context.Context, *DocGetByNameReq) (*DocGetByNameResp, error)
	DocGetListByName(context.Context, *DocGetListByNameReq) (*DocGetListByNameResp, error)
	DocGetListByFilter(context.Context, *DocGetListByFilterReq) (*DocGetListByFilterResp, error)
	DocTreeGetLine(context.Context, *DocTreeGetLineReq) (*DocTreeGetLineResp, error)
	JobRun(context.Context, *JobRunReq) (*JobRunResp, error)
	JobGetStatus(context.Context, *JobGetStatusReq) (*JobGetStatusResp, error)
	JobStop(context.Context, *JobStopReq) (*JobStopResp, error)
	JobGetStatusAll(context.Context, *JobGetStatusAllReq) (*JobGetStatusAllResp, error)
	// Методы, специфичные для отдельных словарей
	KATOMapFromTSOID(context.Context, *KATOMapFromTSOIDReq) (*KATOMapFromTSOIDResp, error)
	KATOToABIS(context.Context, *KATOToABISReq) (*KATOToABISResp, error)
	// Retrieves the old postal index (SPI) for a given address.
	GetPostalIndex(context.Context, *GetPostalIndexReq) (*GetPostalIndexResp, error)
	mustEmbedUnimplementedDictionaryServer()
}

// UnimplementedDictionaryServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDictionaryServer struct{}

func (UnimplementedDictionaryServer) HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedDictionaryServer) DictCreate(context.Context, *DictCreateReq) (*DictCreateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DictCreate not implemented")
}
func (UnimplementedDictionaryServer) DictUpdate(context.Context, *DictUpdateReq) (*DictUpdateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DictUpdate not implemented")
}
func (UnimplementedDictionaryServer) DictDelete(context.Context, *DictDeleteReq) (*DictDeleteResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DictDelete not implemented")
}
func (UnimplementedDictionaryServer) DictGet(context.Context, *DictGetReq) (*DictGetResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DictGet not implemented")
}
func (UnimplementedDictionaryServer) DictGetByName(context.Context, *DictGetByNameReq) (*DictGetByNameResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DictGetByName not implemented")
}
func (UnimplementedDictionaryServer) DictGetList(context.Context, *DictGetListReq) (*DictGetListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DictGetList not implemented")
}
func (UnimplementedDictionaryServer) DocCreate(context.Context, *DocCreateReq) (*DocCreateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DocCreate not implemented")
}
func (UnimplementedDictionaryServer) DocUpdate(context.Context, *DocUpdateReq) (*DocUpdateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DocUpdate not implemented")
}
func (UnimplementedDictionaryServer) DocOrderUpdate(context.Context, *DocOrderUpdateReq) (*DocOrderUpdateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DocOrderUpdate not implemented")
}
func (UnimplementedDictionaryServer) DocDelete(context.Context, *DocDeleteReq) (*DocDeleteResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DocDelete not implemented")
}
func (UnimplementedDictionaryServer) DocGet(context.Context, *DocGetReq) (*DocGetResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DocGet not implemented")
}
func (UnimplementedDictionaryServer) DocGetList(context.Context, *DocGetListReq) (*DocGetListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DocGetList not implemented")
}
func (UnimplementedDictionaryServer) DocGetByName(context.Context, *DocGetByNameReq) (*DocGetByNameResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DocGetByName not implemented")
}
func (UnimplementedDictionaryServer) DocGetListByName(context.Context, *DocGetListByNameReq) (*DocGetListByNameResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DocGetListByName not implemented")
}
func (UnimplementedDictionaryServer) DocGetListByFilter(context.Context, *DocGetListByFilterReq) (*DocGetListByFilterResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DocGetListByFilter not implemented")
}
func (UnimplementedDictionaryServer) DocTreeGetLine(context.Context, *DocTreeGetLineReq) (*DocTreeGetLineResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DocTreeGetLine not implemented")
}
func (UnimplementedDictionaryServer) JobRun(context.Context, *JobRunReq) (*JobRunResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JobRun not implemented")
}
func (UnimplementedDictionaryServer) JobGetStatus(context.Context, *JobGetStatusReq) (*JobGetStatusResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JobGetStatus not implemented")
}
func (UnimplementedDictionaryServer) JobStop(context.Context, *JobStopReq) (*JobStopResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JobStop not implemented")
}
func (UnimplementedDictionaryServer) JobGetStatusAll(context.Context, *JobGetStatusAllReq) (*JobGetStatusAllResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JobGetStatusAll not implemented")
}
func (UnimplementedDictionaryServer) KATOMapFromTSOID(context.Context, *KATOMapFromTSOIDReq) (*KATOMapFromTSOIDResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KATOMapFromTSOID not implemented")
}
func (UnimplementedDictionaryServer) KATOToABIS(context.Context, *KATOToABISReq) (*KATOToABISResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KATOToABIS not implemented")
}
func (UnimplementedDictionaryServer) GetPostalIndex(context.Context, *GetPostalIndexReq) (*GetPostalIndexResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPostalIndex not implemented")
}
func (UnimplementedDictionaryServer) mustEmbedUnimplementedDictionaryServer() {}
func (UnimplementedDictionaryServer) testEmbeddedByValue()                    {}

// UnsafeDictionaryServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DictionaryServer will
// result in compilation errors.
type UnsafeDictionaryServer interface {
	mustEmbedUnimplementedDictionaryServer()
}

func RegisterDictionaryServer(s grpc.ServiceRegistrar, srv DictionaryServer) {
	// If the following call pancis, it indicates UnimplementedDictionaryServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Dictionary_ServiceDesc, srv)
}

func _Dictionary_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).HealthCheck(ctx, req.(*HealthCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dictionary_DictCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DictCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).DictCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_DictCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).DictCreate(ctx, req.(*DictCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dictionary_DictUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DictUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).DictUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_DictUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).DictUpdate(ctx, req.(*DictUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dictionary_DictDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DictDeleteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).DictDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_DictDelete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).DictDelete(ctx, req.(*DictDeleteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dictionary_DictGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DictGetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).DictGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_DictGet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).DictGet(ctx, req.(*DictGetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dictionary_DictGetByName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DictGetByNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).DictGetByName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_DictGetByName_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).DictGetByName(ctx, req.(*DictGetByNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dictionary_DictGetList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DictGetListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).DictGetList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_DictGetList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).DictGetList(ctx, req.(*DictGetListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dictionary_DocCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DocCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).DocCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_DocCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).DocCreate(ctx, req.(*DocCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dictionary_DocUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DocUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).DocUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_DocUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).DocUpdate(ctx, req.(*DocUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dictionary_DocOrderUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DocOrderUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).DocOrderUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_DocOrderUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).DocOrderUpdate(ctx, req.(*DocOrderUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dictionary_DocDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DocDeleteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).DocDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_DocDelete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).DocDelete(ctx, req.(*DocDeleteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dictionary_DocGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DocGetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).DocGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_DocGet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).DocGet(ctx, req.(*DocGetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dictionary_DocGetList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DocGetListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).DocGetList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_DocGetList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).DocGetList(ctx, req.(*DocGetListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dictionary_DocGetByName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DocGetByNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).DocGetByName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_DocGetByName_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).DocGetByName(ctx, req.(*DocGetByNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dictionary_DocGetListByName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DocGetListByNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).DocGetListByName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_DocGetListByName_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).DocGetListByName(ctx, req.(*DocGetListByNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dictionary_DocGetListByFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DocGetListByFilterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).DocGetListByFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_DocGetListByFilter_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).DocGetListByFilter(ctx, req.(*DocGetListByFilterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dictionary_DocTreeGetLine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DocTreeGetLineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).DocTreeGetLine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_DocTreeGetLine_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).DocTreeGetLine(ctx, req.(*DocTreeGetLineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dictionary_JobRun_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JobRunReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).JobRun(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_JobRun_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).JobRun(ctx, req.(*JobRunReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dictionary_JobGetStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JobGetStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).JobGetStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_JobGetStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).JobGetStatus(ctx, req.(*JobGetStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dictionary_JobStop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JobStopReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).JobStop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_JobStop_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).JobStop(ctx, req.(*JobStopReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dictionary_JobGetStatusAll_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JobGetStatusAllReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).JobGetStatusAll(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_JobGetStatusAll_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).JobGetStatusAll(ctx, req.(*JobGetStatusAllReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dictionary_KATOMapFromTSOID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KATOMapFromTSOIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).KATOMapFromTSOID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_KATOMapFromTSOID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).KATOMapFromTSOID(ctx, req.(*KATOMapFromTSOIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dictionary_KATOToABIS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KATOToABISReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).KATOToABIS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_KATOToABIS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).KATOToABIS(ctx, req.(*KATOToABISReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Dictionary_GetPostalIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostalIndexReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictionaryServer).GetPostalIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Dictionary_GetPostalIndex_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictionaryServer).GetPostalIndex(ctx, req.(*GetPostalIndexReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Dictionary_ServiceDesc is the grpc.ServiceDesc for Dictionary service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Dictionary_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "dictionary.Dictionary",
	HandlerType: (*DictionaryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HealthCheck",
			Handler:    _Dictionary_HealthCheck_Handler,
		},
		{
			MethodName: "DictCreate",
			Handler:    _Dictionary_DictCreate_Handler,
		},
		{
			MethodName: "DictUpdate",
			Handler:    _Dictionary_DictUpdate_Handler,
		},
		{
			MethodName: "DictDelete",
			Handler:    _Dictionary_DictDelete_Handler,
		},
		{
			MethodName: "DictGet",
			Handler:    _Dictionary_DictGet_Handler,
		},
		{
			MethodName: "DictGetByName",
			Handler:    _Dictionary_DictGetByName_Handler,
		},
		{
			MethodName: "DictGetList",
			Handler:    _Dictionary_DictGetList_Handler,
		},
		{
			MethodName: "DocCreate",
			Handler:    _Dictionary_DocCreate_Handler,
		},
		{
			MethodName: "DocUpdate",
			Handler:    _Dictionary_DocUpdate_Handler,
		},
		{
			MethodName: "DocOrderUpdate",
			Handler:    _Dictionary_DocOrderUpdate_Handler,
		},
		{
			MethodName: "DocDelete",
			Handler:    _Dictionary_DocDelete_Handler,
		},
		{
			MethodName: "DocGet",
			Handler:    _Dictionary_DocGet_Handler,
		},
		{
			MethodName: "DocGetList",
			Handler:    _Dictionary_DocGetList_Handler,
		},
		{
			MethodName: "DocGetByName",
			Handler:    _Dictionary_DocGetByName_Handler,
		},
		{
			MethodName: "DocGetListByName",
			Handler:    _Dictionary_DocGetListByName_Handler,
		},
		{
			MethodName: "DocGetListByFilter",
			Handler:    _Dictionary_DocGetListByFilter_Handler,
		},
		{
			MethodName: "DocTreeGetLine",
			Handler:    _Dictionary_DocTreeGetLine_Handler,
		},
		{
			MethodName: "JobRun",
			Handler:    _Dictionary_JobRun_Handler,
		},
		{
			MethodName: "JobGetStatus",
			Handler:    _Dictionary_JobGetStatus_Handler,
		},
		{
			MethodName: "JobStop",
			Handler:    _Dictionary_JobStop_Handler,
		},
		{
			MethodName: "JobGetStatusAll",
			Handler:    _Dictionary_JobGetStatusAll_Handler,
		},
		{
			MethodName: "KATOMapFromTSOID",
			Handler:    _Dictionary_KATOMapFromTSOID_Handler,
		},
		{
			MethodName: "KATOToABIS",
			Handler:    _Dictionary_KATOToABIS_Handler,
		},
		{
			MethodName: "GetPostalIndex",
			Handler:    _Dictionary_GetPostalIndex_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "specs/proto/dictionary/dictionary.proto",
}
