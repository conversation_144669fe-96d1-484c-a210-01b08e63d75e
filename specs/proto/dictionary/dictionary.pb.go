// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v6.32.1
// source: specs/proto/dictionary/dictionary.proto

package dictionary

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TSOIDAddrElement struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          string                 `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	NameKz        string                 `protobuf:"bytes,2,opt,name=nameKz,proto3" json:"nameKz,omitempty"`
	NameRu        string                 `protobuf:"bytes,3,opt,name=nameRu,proto3" json:"nameRu,omitempty"`
	ChangeDate    string                 `protobuf:"bytes,4,opt,name=changeDate,proto3" json:"changeDate,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TSOIDAddrElement) Reset() {
	*x = TSOIDAddrElement{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TSOIDAddrElement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TSOIDAddrElement) ProtoMessage() {}

func (x *TSOIDAddrElement) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TSOIDAddrElement.ProtoReflect.Descriptor instead.
func (*TSOIDAddrElement) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{0}
}

func (x *TSOIDAddrElement) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *TSOIDAddrElement) GetNameKz() string {
	if x != nil {
		return x.NameKz
	}
	return ""
}

func (x *TSOIDAddrElement) GetNameRu() string {
	if x != nil {
		return x.NameRu
	}
	return ""
}

func (x *TSOIDAddrElement) GetChangeDate() string {
	if x != nil {
		return x.ChangeDate
	}
	return ""
}

type KATOMapFromTSOIDReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Country       *TSOIDAddrElement      `protobuf:"bytes,1,opt,name=country,proto3" json:"country,omitempty"`   // Страна
	Region        *TSOIDAddrElement      `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`     // Регион или город регионального значения
	District      *TSOIDAddrElement      `protobuf:"bytes,3,opt,name=district,proto3" json:"district,omitempty"` // Район
	City          string                 `protobuf:"bytes,4,opt,name=city,proto3" json:"city,omitempty"`         // Город (населенный пункт)
	Street        string                 `protobuf:"bytes,5,opt,name=street,proto3" json:"street,omitempty"`     // Улица
	Flat          string                 `protobuf:"bytes,6,opt,name=flat,proto3" json:"flat,omitempty"`         // Квартира
	Building      string                 `protobuf:"bytes,7,opt,name=building,proto3" json:"building,omitempty"` // Строение
	Corpus        string                 `protobuf:"bytes,8,opt,name=corpus,proto3" json:"corpus,omitempty"`     // Корпус
	BeginDate     string                 `protobuf:"bytes,9,opt,name=begin_date,json=beginDate,proto3" json:"begin_date,omitempty"`
	Lang          string                 `protobuf:"bytes,10,opt,name=lang,proto3" json:"lang,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *KATOMapFromTSOIDReq) Reset() {
	*x = KATOMapFromTSOIDReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KATOMapFromTSOIDReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KATOMapFromTSOIDReq) ProtoMessage() {}

func (x *KATOMapFromTSOIDReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KATOMapFromTSOIDReq.ProtoReflect.Descriptor instead.
func (*KATOMapFromTSOIDReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{1}
}

func (x *KATOMapFromTSOIDReq) GetCountry() *TSOIDAddrElement {
	if x != nil {
		return x.Country
	}
	return nil
}

func (x *KATOMapFromTSOIDReq) GetRegion() *TSOIDAddrElement {
	if x != nil {
		return x.Region
	}
	return nil
}

func (x *KATOMapFromTSOIDReq) GetDistrict() *TSOIDAddrElement {
	if x != nil {
		return x.District
	}
	return nil
}

func (x *KATOMapFromTSOIDReq) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *KATOMapFromTSOIDReq) GetStreet() string {
	if x != nil {
		return x.Street
	}
	return ""
}

func (x *KATOMapFromTSOIDReq) GetFlat() string {
	if x != nil {
		return x.Flat
	}
	return ""
}

func (x *KATOMapFromTSOIDReq) GetBuilding() string {
	if x != nil {
		return x.Building
	}
	return ""
}

func (x *KATOMapFromTSOIDReq) GetCorpus() string {
	if x != nil {
		return x.Corpus
	}
	return ""
}

func (x *KATOMapFromTSOIDReq) GetBeginDate() string {
	if x != nil {
		return x.BeginDate
	}
	return ""
}

func (x *KATOMapFromTSOIDReq) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type KATOMapFromTSOIDResp struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Type           string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`                                            // Тип адреса (001, 002, 003)
	OwnType        string                 `protobuf:"bytes,2,opt,name=own_type,json=ownType,proto3" json:"own_type,omitempty"`                       // Вид владения (может отсутствовать)
	Country        string                 `protobuf:"bytes,3,opt,name=country,proto3" json:"country,omitempty"`                                      // Наименование страны
	RegionCity     string                 `protobuf:"bytes,4,opt,name=region_city,json=regionCity,proto3" json:"region_city,omitempty"`              // Город-регион (может отсутствовать)
	City           string                 `protobuf:"bytes,5,opt,name=city,proto3" json:"city,omitempty"`                                            // Город (может отсутствовать)
	CityTdc        string                 `protobuf:"bytes,6,opt,name=city_tdc,json=cityTdc,proto3" json:"city_tdc,omitempty"`                       // Код вида территории для города (может отсутствовать)
	Region         string                 `protobuf:"bytes,7,opt,name=region,proto3" json:"region,omitempty"`                                        // Область (может отсутствовать)
	RegionTdc      string                 `protobuf:"bytes,8,opt,name=region_tdc,json=regionTdc,proto3" json:"region_tdc,omitempty"`                 // Код вида территории для области (может отсутствовать)
	District       string                 `protobuf:"bytes,9,opt,name=district,proto3" json:"district,omitempty"`                                    // Район
	DistrictTdc    string                 `protobuf:"bytes,10,opt,name=district_tdc,json=districtTdc,proto3" json:"district_tdc,omitempty"`          // Код вида района
	Settlement     string                 `protobuf:"bytes,11,opt,name=settlement,proto3" json:"settlement,omitempty"`                               // Населенный пункт (может отсутствовать)
	SettlementTdc  string                 `protobuf:"bytes,12,opt,name=settlement_tdc,json=settlementTdc,proto3" json:"settlement_tdc,omitempty"`    // Код вида территории для пункта (может отсутствовать)
	Street         string                 `protobuf:"bytes,13,opt,name=street,proto3" json:"street,omitempty"`                                       // Улица (может отсутствовать)
	StreetTdc      string                 `protobuf:"bytes,14,opt,name=street_tdc,json=streetTdc,proto3" json:"street_tdc,omitempty"`                // Код вида территории для улицы (может отсутствовать)
	Zone           string                 `protobuf:"bytes,15,opt,name=zone,proto3" json:"zone,omitempty"`                                           // Микрорайон (может отсутствовать)
	ZoneTdc        string                 `protobuf:"bytes,16,opt,name=zone_tdc,json=zoneTdc,proto3" json:"zone_tdc,omitempty"`                      // Код вида территории для микрорайона (может отсутствовать)
	House          string                 `protobuf:"bytes,17,opt,name=house,proto3" json:"house,omitempty"`                                         // Дом (может отсутствовать)
	HouseTdc       string                 `protobuf:"bytes,18,opt,name=house_tdc,json=houseTdc,proto3" json:"house_tdc,omitempty"`                   // Код вида территории для дома (может отсутствовать)
	Body           string                 `protobuf:"bytes,19,opt,name=body,proto3" json:"body,omitempty"`                                           // Корпус (может отсутствовать)
	Building       string                 `protobuf:"bytes,20,opt,name=building,proto3" json:"building,omitempty"`                                   // Строение (может отсутствовать)
	HouseOwnership string                 `protobuf:"bytes,21,opt,name=house_ownership,json=houseOwnership,proto3" json:"house_ownership,omitempty"` // Владение (может отсутствовать)
	Flat           string                 `protobuf:"bytes,22,opt,name=flat,proto3" json:"flat,omitempty"`                                           // Квартира (может отсутствовать)
	FlatTdc        string                 `protobuf:"bytes,23,opt,name=flat_tdc,json=flatTdc,proto3" json:"flat_tdc,omitempty"`                      // Код вида территории для квартиры (может отсутствовать)
	Index          string                 `protobuf:"bytes,24,opt,name=index,proto3" json:"index,omitempty"`                                         // Почтовый индекс (может отсутствовать)
	Full           string                 `protobuf:"bytes,25,opt,name=full,proto3" json:"full,omitempty"`                                           // Адрес полной строкой (может отсутствовать)
	Kato           string                 `protobuf:"bytes,26,opt,name=kato,proto3" json:"kato,omitempty"`                                           // Код КАТО (может отсутствовать)
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *KATOMapFromTSOIDResp) Reset() {
	*x = KATOMapFromTSOIDResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KATOMapFromTSOIDResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KATOMapFromTSOIDResp) ProtoMessage() {}

func (x *KATOMapFromTSOIDResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KATOMapFromTSOIDResp.ProtoReflect.Descriptor instead.
func (*KATOMapFromTSOIDResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{2}
}

func (x *KATOMapFromTSOIDResp) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetOwnType() string {
	if x != nil {
		return x.OwnType
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetRegionCity() string {
	if x != nil {
		return x.RegionCity
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetCityTdc() string {
	if x != nil {
		return x.CityTdc
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetRegionTdc() string {
	if x != nil {
		return x.RegionTdc
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetDistrict() string {
	if x != nil {
		return x.District
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetDistrictTdc() string {
	if x != nil {
		return x.DistrictTdc
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetSettlement() string {
	if x != nil {
		return x.Settlement
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetSettlementTdc() string {
	if x != nil {
		return x.SettlementTdc
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetStreet() string {
	if x != nil {
		return x.Street
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetStreetTdc() string {
	if x != nil {
		return x.StreetTdc
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetZoneTdc() string {
	if x != nil {
		return x.ZoneTdc
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetHouse() string {
	if x != nil {
		return x.House
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetHouseTdc() string {
	if x != nil {
		return x.HouseTdc
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetBuilding() string {
	if x != nil {
		return x.Building
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetHouseOwnership() string {
	if x != nil {
		return x.HouseOwnership
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetFlat() string {
	if x != nil {
		return x.Flat
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetFlatTdc() string {
	if x != nil {
		return x.FlatTdc
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetIndex() string {
	if x != nil {
		return x.Index
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetFull() string {
	if x != nil {
		return x.Full
	}
	return ""
}

func (x *KATOMapFromTSOIDResp) GetKato() string {
	if x != nil {
		return x.Kato
	}
	return ""
}

type HealthCheckReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckReq) Reset() {
	*x = HealthCheckReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckReq) ProtoMessage() {}

func (x *HealthCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckReq.ProtoReflect.Descriptor instead.
func (*HealthCheckReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{3}
}

type HealthCheckResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckResp) Reset() {
	*x = HealthCheckResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResp) ProtoMessage() {}

func (x *HealthCheckResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResp.ProtoReflect.Descriptor instead.
func (*HealthCheckResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{4}
}

func (x *HealthCheckResp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type Dict struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Schema        string                 `protobuf:"bytes,4,opt,name=schema,proto3" json:"schema,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Dict) Reset() {
	*x = Dict{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Dict) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Dict) ProtoMessage() {}

func (x *Dict) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Dict.ProtoReflect.Descriptor instead.
func (*Dict) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{5}
}

func (x *Dict) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Dict) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Dict) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Dict) GetSchema() string {
	if x != nil {
		return x.Schema
	}
	return ""
}

type DictCreateReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Schema        string                 `protobuf:"bytes,4,opt,name=schema,proto3" json:"schema,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DictCreateReq) Reset() {
	*x = DictCreateReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DictCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DictCreateReq) ProtoMessage() {}

func (x *DictCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DictCreateReq.ProtoReflect.Descriptor instead.
func (*DictCreateReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{6}
}

func (x *DictCreateReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DictCreateReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DictCreateReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DictCreateReq) GetSchema() string {
	if x != nil {
		return x.Schema
	}
	return ""
}

type DictCreateResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Schema        string                 `protobuf:"bytes,4,opt,name=schema,proto3" json:"schema,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DictCreateResp) Reset() {
	*x = DictCreateResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DictCreateResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DictCreateResp) ProtoMessage() {}

func (x *DictCreateResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DictCreateResp.ProtoReflect.Descriptor instead.
func (*DictCreateResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{7}
}

func (x *DictCreateResp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DictCreateResp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DictCreateResp) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DictCreateResp) GetSchema() string {
	if x != nil {
		return x.Schema
	}
	return ""
}

type DictUpdateReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Schema        string                 `protobuf:"bytes,4,opt,name=schema,proto3" json:"schema,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DictUpdateReq) Reset() {
	*x = DictUpdateReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DictUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DictUpdateReq) ProtoMessage() {}

func (x *DictUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DictUpdateReq.ProtoReflect.Descriptor instead.
func (*DictUpdateReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{8}
}

func (x *DictUpdateReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DictUpdateReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DictUpdateReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DictUpdateReq) GetSchema() string {
	if x != nil {
		return x.Schema
	}
	return ""
}

type DictUpdateResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Schema        string                 `protobuf:"bytes,4,opt,name=schema,proto3" json:"schema,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DictUpdateResp) Reset() {
	*x = DictUpdateResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DictUpdateResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DictUpdateResp) ProtoMessage() {}

func (x *DictUpdateResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DictUpdateResp.ProtoReflect.Descriptor instead.
func (*DictUpdateResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{9}
}

func (x *DictUpdateResp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DictUpdateResp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DictUpdateResp) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DictUpdateResp) GetSchema() string {
	if x != nil {
		return x.Schema
	}
	return ""
}

type DictDeleteReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DictDeleteReq) Reset() {
	*x = DictDeleteReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DictDeleteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DictDeleteReq) ProtoMessage() {}

func (x *DictDeleteReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DictDeleteReq.ProtoReflect.Descriptor instead.
func (*DictDeleteReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{10}
}

func (x *DictDeleteReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DictDeleteResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DictDeleteResp) Reset() {
	*x = DictDeleteResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DictDeleteResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DictDeleteResp) ProtoMessage() {}

func (x *DictDeleteResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DictDeleteResp.ProtoReflect.Descriptor instead.
func (*DictDeleteResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{11}
}

type DictGetReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DictGetReq) Reset() {
	*x = DictGetReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DictGetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DictGetReq) ProtoMessage() {}

func (x *DictGetReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DictGetReq.ProtoReflect.Descriptor instead.
func (*DictGetReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{12}
}

func (x *DictGetReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DictGetResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Schema        string                 `protobuf:"bytes,4,opt,name=schema,proto3" json:"schema,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DictGetResp) Reset() {
	*x = DictGetResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DictGetResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DictGetResp) ProtoMessage() {}

func (x *DictGetResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DictGetResp.ProtoReflect.Descriptor instead.
func (*DictGetResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{13}
}

func (x *DictGetResp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DictGetResp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DictGetResp) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DictGetResp) GetSchema() string {
	if x != nil {
		return x.Schema
	}
	return ""
}

type Doc struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	DictId        string                 `protobuf:"bytes,2,opt,name=dict_id,json=dictId,proto3" json:"dict_id,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Data          string                 `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	Valid         bool                   `protobuf:"varint,5,opt,name=valid,proto3" json:"valid,omitempty"`
	OrderNum      int32                  `protobuf:"varint,6,opt,name=order_num,json=orderNum,proto3" json:"order_num,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Doc) Reset() {
	*x = Doc{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Doc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Doc) ProtoMessage() {}

func (x *Doc) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Doc.ProtoReflect.Descriptor instead.
func (*Doc) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{14}
}

func (x *Doc) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Doc) GetDictId() string {
	if x != nil {
		return x.DictId
	}
	return ""
}

func (x *Doc) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Doc) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *Doc) GetValid() bool {
	if x != nil {
		return x.Valid
	}
	return false
}

func (x *Doc) GetOrderNum() int32 {
	if x != nil {
		return x.OrderNum
	}
	return 0
}

type DocCreateReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Doc           *Doc                   `protobuf:"bytes,1,opt,name=doc,proto3" json:"doc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocCreateReq) Reset() {
	*x = DocCreateReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocCreateReq) ProtoMessage() {}

func (x *DocCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocCreateReq.ProtoReflect.Descriptor instead.
func (*DocCreateReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{15}
}

func (x *DocCreateReq) GetDoc() *Doc {
	if x != nil {
		return x.Doc
	}
	return nil
}

type DocCreateResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Doc           *Doc                   `protobuf:"bytes,1,opt,name=doc,proto3" json:"doc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocCreateResp) Reset() {
	*x = DocCreateResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocCreateResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocCreateResp) ProtoMessage() {}

func (x *DocCreateResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocCreateResp.ProtoReflect.Descriptor instead.
func (*DocCreateResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{16}
}

func (x *DocCreateResp) GetDoc() *Doc {
	if x != nil {
		return x.Doc
	}
	return nil
}

type DocUpdateReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Doc           *Doc                   `protobuf:"bytes,1,opt,name=doc,proto3" json:"doc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocUpdateReq) Reset() {
	*x = DocUpdateReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocUpdateReq) ProtoMessage() {}

func (x *DocUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocUpdateReq.ProtoReflect.Descriptor instead.
func (*DocUpdateReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{17}
}

func (x *DocUpdateReq) GetDoc() *Doc {
	if x != nil {
		return x.Doc
	}
	return nil
}

type DocUpdateResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Doc           *Doc                   `protobuf:"bytes,1,opt,name=doc,proto3" json:"doc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocUpdateResp) Reset() {
	*x = DocUpdateResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocUpdateResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocUpdateResp) ProtoMessage() {}

func (x *DocUpdateResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocUpdateResp.ProtoReflect.Descriptor instead.
func (*DocUpdateResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{18}
}

func (x *DocUpdateResp) GetDoc() *Doc {
	if x != nil {
		return x.Doc
	}
	return nil
}

type DocDeleteReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocDeleteReq) Reset() {
	*x = DocDeleteReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocDeleteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocDeleteReq) ProtoMessage() {}

func (x *DocDeleteReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocDeleteReq.ProtoReflect.Descriptor instead.
func (*DocDeleteReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{19}
}

func (x *DocDeleteReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DocDeleteResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocDeleteResp) Reset() {
	*x = DocDeleteResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocDeleteResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocDeleteResp) ProtoMessage() {}

func (x *DocDeleteResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocDeleteResp.ProtoReflect.Descriptor instead.
func (*DocDeleteResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{20}
}

type DocGetReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocGetReq) Reset() {
	*x = DocGetReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocGetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocGetReq) ProtoMessage() {}

func (x *DocGetReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocGetReq.ProtoReflect.Descriptor instead.
func (*DocGetReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{21}
}

func (x *DocGetReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DocGetResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Doc           *Doc                   `protobuf:"bytes,1,opt,name=doc,proto3" json:"doc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocGetResp) Reset() {
	*x = DocGetResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocGetResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocGetResp) ProtoMessage() {}

func (x *DocGetResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocGetResp.ProtoReflect.Descriptor instead.
func (*DocGetResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{22}
}

func (x *DocGetResp) GetDoc() *Doc {
	if x != nil {
		return x.Doc
	}
	return nil
}

type DocGetByNameReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DictName      string                 `protobuf:"bytes,1,opt,name=dict_name,json=dictName,proto3" json:"dict_name,omitempty"`
	DocName       string                 `protobuf:"bytes,2,opt,name=doc_name,json=docName,proto3" json:"doc_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocGetByNameReq) Reset() {
	*x = DocGetByNameReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocGetByNameReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocGetByNameReq) ProtoMessage() {}

func (x *DocGetByNameReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocGetByNameReq.ProtoReflect.Descriptor instead.
func (*DocGetByNameReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{23}
}

func (x *DocGetByNameReq) GetDictName() string {
	if x != nil {
		return x.DictName
	}
	return ""
}

func (x *DocGetByNameReq) GetDocName() string {
	if x != nil {
		return x.DocName
	}
	return ""
}

type DocGetByNameResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Doc           *Doc                   `protobuf:"bytes,1,opt,name=doc,proto3" json:"doc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocGetByNameResp) Reset() {
	*x = DocGetByNameResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocGetByNameResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocGetByNameResp) ProtoMessage() {}

func (x *DocGetByNameResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocGetByNameResp.ProtoReflect.Descriptor instead.
func (*DocGetByNameResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{24}
}

func (x *DocGetByNameResp) GetDoc() *Doc {
	if x != nil {
		return x.Doc
	}
	return nil
}

type DictGetByNameReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DictName      string                 `protobuf:"bytes,1,opt,name=dict_name,json=dictName,proto3" json:"dict_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DictGetByNameReq) Reset() {
	*x = DictGetByNameReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DictGetByNameReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DictGetByNameReq) ProtoMessage() {}

func (x *DictGetByNameReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DictGetByNameReq.ProtoReflect.Descriptor instead.
func (*DictGetByNameReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{25}
}

func (x *DictGetByNameReq) GetDictName() string {
	if x != nil {
		return x.DictName
	}
	return ""
}

type DictGetByNameResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Dict          *Dict                  `protobuf:"bytes,1,opt,name=dict,proto3" json:"dict,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DictGetByNameResp) Reset() {
	*x = DictGetByNameResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DictGetByNameResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DictGetByNameResp) ProtoMessage() {}

func (x *DictGetByNameResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DictGetByNameResp.ProtoReflect.Descriptor instead.
func (*DictGetByNameResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{26}
}

func (x *DictGetByNameResp) GetDict() *Dict {
	if x != nil {
		return x.Dict
	}
	return nil
}

type DocGetListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DictId        string                 `protobuf:"bytes,1,opt,name=dict_id,json=dictId,proto3" json:"dict_id,omitempty"`
	OnlyValid     bool                   `protobuf:"varint,2,opt,name=onlyValid,proto3" json:"onlyValid,omitempty"`
	Pagination    *Pagination            `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocGetListReq) Reset() {
	*x = DocGetListReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocGetListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocGetListReq) ProtoMessage() {}

func (x *DocGetListReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocGetListReq.ProtoReflect.Descriptor instead.
func (*DocGetListReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{27}
}

func (x *DocGetListReq) GetDictId() string {
	if x != nil {
		return x.DictId
	}
	return ""
}

func (x *DocGetListReq) GetOnlyValid() bool {
	if x != nil {
		return x.OnlyValid
	}
	return false
}

func (x *DocGetListReq) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type DocGetListResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*Doc                 `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocGetListResp) Reset() {
	*x = DocGetListResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocGetListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocGetListResp) ProtoMessage() {}

func (x *DocGetListResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocGetListResp.ProtoReflect.Descriptor instead.
func (*DocGetListResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{28}
}

func (x *DocGetListResp) GetList() []*Doc {
	if x != nil {
		return x.List
	}
	return nil
}

type DocGetListByNameReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DictName      string                 `protobuf:"bytes,1,opt,name=dict_name,json=dictName,proto3" json:"dict_name,omitempty"`
	OnlyValid     bool                   `protobuf:"varint,2,opt,name=onlyValid,proto3" json:"onlyValid,omitempty"`
	Pagination    *Pagination            `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocGetListByNameReq) Reset() {
	*x = DocGetListByNameReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocGetListByNameReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocGetListByNameReq) ProtoMessage() {}

func (x *DocGetListByNameReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocGetListByNameReq.ProtoReflect.Descriptor instead.
func (*DocGetListByNameReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{29}
}

func (x *DocGetListByNameReq) GetDictName() string {
	if x != nil {
		return x.DictName
	}
	return ""
}

func (x *DocGetListByNameReq) GetOnlyValid() bool {
	if x != nil {
		return x.OnlyValid
	}
	return false
}

func (x *DocGetListByNameReq) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type DocGetListByNameResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*Doc                 `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocGetListByNameResp) Reset() {
	*x = DocGetListByNameResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocGetListByNameResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocGetListByNameResp) ProtoMessage() {}

func (x *DocGetListByNameResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocGetListByNameResp.ProtoReflect.Descriptor instead.
func (*DocGetListByNameResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{30}
}

func (x *DocGetListByNameResp) GetList() []*Doc {
	if x != nil {
		return x.List
	}
	return nil
}

type Filter struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DataField     string                 `protobuf:"bytes,1,opt,name=data_field,json=dataField,proto3" json:"data_field,omitempty"`
	Operation     string                 `protobuf:"bytes,2,opt,name=operation,proto3" json:"operation,omitempty"`
	Value         string                 `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	Group         []*Filter              `protobuf:"bytes,4,rep,name=group,proto3" json:"group,omitempty"` // Для операций типа "OR" или "AND"
	Not           bool                   `protobuf:"varint,5,opt,name=not,proto3" json:"not,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Filter) Reset() {
	*x = Filter{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Filter) ProtoMessage() {}

func (x *Filter) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Filter.ProtoReflect.Descriptor instead.
func (*Filter) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{31}
}

func (x *Filter) GetDataField() string {
	if x != nil {
		return x.DataField
	}
	return ""
}

func (x *Filter) GetOperation() string {
	if x != nil {
		return x.Operation
	}
	return ""
}

func (x *Filter) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *Filter) GetGroup() []*Filter {
	if x != nil {
		return x.Group
	}
	return nil
}

func (x *Filter) GetNot() bool {
	if x != nil {
		return x.Not
	}
	return false
}

type DocGetListByFilterReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DictId        string                 `protobuf:"bytes,1,opt,name=dict_id,json=dictId,proto3" json:"dict_id,omitempty"`
	Filters       []*Filter              `protobuf:"bytes,2,rep,name=filters,proto3" json:"filters,omitempty"`
	Sort          []string               `protobuf:"bytes,3,rep,name=sort,proto3" json:"sort,omitempty"`
	Pagination    *Pagination            `protobuf:"bytes,4,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocGetListByFilterReq) Reset() {
	*x = DocGetListByFilterReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocGetListByFilterReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocGetListByFilterReq) ProtoMessage() {}

func (x *DocGetListByFilterReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocGetListByFilterReq.ProtoReflect.Descriptor instead.
func (*DocGetListByFilterReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{32}
}

func (x *DocGetListByFilterReq) GetDictId() string {
	if x != nil {
		return x.DictId
	}
	return ""
}

func (x *DocGetListByFilterReq) GetFilters() []*Filter {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *DocGetListByFilterReq) GetSort() []string {
	if x != nil {
		return x.Sort
	}
	return nil
}

func (x *DocGetListByFilterReq) GetPagination() *Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type DocGetListByFilterResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*Doc                 `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocGetListByFilterResp) Reset() {
	*x = DocGetListByFilterResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocGetListByFilterResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocGetListByFilterResp) ProtoMessage() {}

func (x *DocGetListByFilterResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocGetListByFilterResp.ProtoReflect.Descriptor instead.
func (*DocGetListByFilterResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{33}
}

func (x *DocGetListByFilterResp) GetList() []*Doc {
	if x != nil {
		return x.List
	}
	return nil
}

type Pagination struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Count         int32                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Pagination) Reset() {
	*x = Pagination{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Pagination) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pagination) ProtoMessage() {}

func (x *Pagination) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pagination.ProtoReflect.Descriptor instead.
func (*Pagination) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{34}
}

func (x *Pagination) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *Pagination) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type DictGetListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Count         int32                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DictGetListReq) Reset() {
	*x = DictGetListReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DictGetListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DictGetListReq) ProtoMessage() {}

func (x *DictGetListReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DictGetListReq.ProtoReflect.Descriptor instead.
func (*DictGetListReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{35}
}

func (x *DictGetListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *DictGetListReq) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type DictGetListResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*Dict                `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DictGetListResp) Reset() {
	*x = DictGetListResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DictGetListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DictGetListResp) ProtoMessage() {}

func (x *DictGetListResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DictGetListResp.ProtoReflect.Descriptor instead.
func (*DictGetListResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{36}
}

func (x *DictGetListResp) GetList() []*Dict {
	if x != nil {
		return x.List
	}
	return nil
}

type JobRunReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	JobType       string                 `protobuf:"bytes,1,opt,name=jobType,proto3" json:"jobType,omitempty"`
	Source        string                 `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	Lang          string                 `protobuf:"bytes,3,opt,name=lang,proto3" json:"lang,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobRunReq) Reset() {
	*x = JobRunReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobRunReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobRunReq) ProtoMessage() {}

func (x *JobRunReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobRunReq.ProtoReflect.Descriptor instead.
func (*JobRunReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{37}
}

func (x *JobRunReq) GetJobType() string {
	if x != nil {
		return x.JobType
	}
	return ""
}

func (x *JobRunReq) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *JobRunReq) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

type JobRunResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobRunResp) Reset() {
	*x = JobRunResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobRunResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobRunResp) ProtoMessage() {}

func (x *JobRunResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobRunResp.ProtoReflect.Descriptor instead.
func (*JobRunResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{38}
}

type JobGetStatusReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	JobType       string                 `protobuf:"bytes,1,opt,name=jobType,proto3" json:"jobType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobGetStatusReq) Reset() {
	*x = JobGetStatusReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobGetStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobGetStatusReq) ProtoMessage() {}

func (x *JobGetStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobGetStatusReq.ProtoReflect.Descriptor instead.
func (*JobGetStatusReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{39}
}

func (x *JobGetStatusReq) GetJobType() string {
	if x != nil {
		return x.JobType
	}
	return ""
}

type JobGetStatusResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StatusInfo    map[string]string      `protobuf:"bytes,1,rep,name=statusInfo,proto3" json:"statusInfo,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobGetStatusResp) Reset() {
	*x = JobGetStatusResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobGetStatusResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobGetStatusResp) ProtoMessage() {}

func (x *JobGetStatusResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobGetStatusResp.ProtoReflect.Descriptor instead.
func (*JobGetStatusResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{40}
}

func (x *JobGetStatusResp) GetStatusInfo() map[string]string {
	if x != nil {
		return x.StatusInfo
	}
	return nil
}

type JobStopReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	JobType       string                 `protobuf:"bytes,1,opt,name=jobType,proto3" json:"jobType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobStopReq) Reset() {
	*x = JobStopReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobStopReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobStopReq) ProtoMessage() {}

func (x *JobStopReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobStopReq.ProtoReflect.Descriptor instead.
func (*JobStopReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{41}
}

func (x *JobStopReq) GetJobType() string {
	if x != nil {
		return x.JobType
	}
	return ""
}

type JobStopResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobStopResp) Reset() {
	*x = JobStopResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobStopResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobStopResp) ProtoMessage() {}

func (x *JobStopResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobStopResp.ProtoReflect.Descriptor instead.
func (*JobStopResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{42}
}

type JobGetStatusAllReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobGetStatusAllReq) Reset() {
	*x = JobGetStatusAllReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobGetStatusAllReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobGetStatusAllReq) ProtoMessage() {}

func (x *JobGetStatusAllReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobGetStatusAllReq.ProtoReflect.Descriptor instead.
func (*JobGetStatusAllReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{43}
}

type JobGetStatusAllResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Statuses      []*JobGetStatusResp    `protobuf:"bytes,1,rep,name=statuses,proto3" json:"statuses,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobGetStatusAllResp) Reset() {
	*x = JobGetStatusAllResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobGetStatusAllResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobGetStatusAllResp) ProtoMessage() {}

func (x *JobGetStatusAllResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobGetStatusAllResp.ProtoReflect.Descriptor instead.
func (*JobGetStatusAllResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{44}
}

func (x *JobGetStatusAllResp) GetStatuses() []*JobGetStatusResp {
	if x != nil {
		return x.Statuses
	}
	return nil
}

type DocOrderUpdateReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DictId        string                 `protobuf:"bytes,1,opt,name=dictId,proto3" json:"dictId,omitempty"`
	OrderNum      int32                  `protobuf:"varint,2,opt,name=orderNum,proto3" json:"orderNum,omitempty"`
	DocIds        []string               `protobuf:"bytes,3,rep,name=DocIds,proto3" json:"DocIds,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocOrderUpdateReq) Reset() {
	*x = DocOrderUpdateReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocOrderUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocOrderUpdateReq) ProtoMessage() {}

func (x *DocOrderUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocOrderUpdateReq.ProtoReflect.Descriptor instead.
func (*DocOrderUpdateReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{45}
}

func (x *DocOrderUpdateReq) GetDictId() string {
	if x != nil {
		return x.DictId
	}
	return ""
}

func (x *DocOrderUpdateReq) GetOrderNum() int32 {
	if x != nil {
		return x.OrderNum
	}
	return 0
}

func (x *DocOrderUpdateReq) GetDocIds() []string {
	if x != nil {
		return x.DocIds
	}
	return nil
}

type DocOrderUpdateResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocOrderUpdateResp) Reset() {
	*x = DocOrderUpdateResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocOrderUpdateResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocOrderUpdateResp) ProtoMessage() {}

func (x *DocOrderUpdateResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocOrderUpdateResp.ProtoReflect.Descriptor instead.
func (*DocOrderUpdateResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{46}
}

type DocTreeGetLineReq struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	DictId           string                 `protobuf:"bytes,1,opt,name=dictId,proto3" json:"dictId,omitempty"`
	DocSelectField   string                 `protobuf:"bytes,2,opt,name=docSelectField,proto3" json:"docSelectField,omitempty"`
	DocSelectValue   string                 `protobuf:"bytes,3,opt,name=docSelectValue,proto3" json:"docSelectValue,omitempty"`
	DocIdField       string                 `protobuf:"bytes,4,opt,name=docIdField,proto3" json:"docIdField,omitempty"`
	DocParentIdField string                 `protobuf:"bytes,5,opt,name=docParentIdField,proto3" json:"docParentIdField,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *DocTreeGetLineReq) Reset() {
	*x = DocTreeGetLineReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocTreeGetLineReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocTreeGetLineReq) ProtoMessage() {}

func (x *DocTreeGetLineReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocTreeGetLineReq.ProtoReflect.Descriptor instead.
func (*DocTreeGetLineReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{47}
}

func (x *DocTreeGetLineReq) GetDictId() string {
	if x != nil {
		return x.DictId
	}
	return ""
}

func (x *DocTreeGetLineReq) GetDocSelectField() string {
	if x != nil {
		return x.DocSelectField
	}
	return ""
}

func (x *DocTreeGetLineReq) GetDocSelectValue() string {
	if x != nil {
		return x.DocSelectValue
	}
	return ""
}

func (x *DocTreeGetLineReq) GetDocIdField() string {
	if x != nil {
		return x.DocIdField
	}
	return ""
}

func (x *DocTreeGetLineReq) GetDocParentIdField() string {
	if x != nil {
		return x.DocParentIdField
	}
	return ""
}

type DocTreeGetLineResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*Doc                 `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocTreeGetLineResp) Reset() {
	*x = DocTreeGetLineResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocTreeGetLineResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocTreeGetLineResp) ProtoMessage() {}

func (x *DocTreeGetLineResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocTreeGetLineResp.ProtoReflect.Descriptor instead.
func (*DocTreeGetLineResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{48}
}

func (x *DocTreeGetLineResp) GetList() []*Doc {
	if x != nil {
		return x.List
	}
	return nil
}

// KATOToABISReq - запрос на преобразование KATO в ABIS
type KATOToABISReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Kato          string                 `protobuf:"bytes,1,opt,name=kato,proto3" json:"kato,omitempty"`     // Код КАТО
	Street        string                 `protobuf:"bytes,2,opt,name=street,proto3" json:"street,omitempty"` // Улица
	House         string                 `protobuf:"bytes,3,opt,name=house,proto3" json:"house,omitempty"`   // Дом
	Flat          string                 `protobuf:"bytes,4,opt,name=flat,proto3" json:"flat,omitempty"`     // Квартира
	Corpus        string                 `protobuf:"bytes,5,opt,name=corpus,proto3" json:"corpus,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *KATOToABISReq) Reset() {
	*x = KATOToABISReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KATOToABISReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KATOToABISReq) ProtoMessage() {}

func (x *KATOToABISReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KATOToABISReq.ProtoReflect.Descriptor instead.
func (*KATOToABISReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{49}
}

func (x *KATOToABISReq) GetKato() string {
	if x != nil {
		return x.Kato
	}
	return ""
}

func (x *KATOToABISReq) GetStreet() string {
	if x != nil {
		return x.Street
	}
	return ""
}

func (x *KATOToABISReq) GetHouse() string {
	if x != nil {
		return x.House
	}
	return ""
}

func (x *KATOToABISReq) GetFlat() string {
	if x != nil {
		return x.Flat
	}
	return ""
}

func (x *KATOToABISReq) GetCorpus() string {
	if x != nil {
		return x.Corpus
	}
	return ""
}

// KATOToABISResp - ответ с результатом преобразования KATO в ABIS
type KATOToABISResp struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Kato           string                 `protobuf:"bytes,1,opt,name=kato,proto3" json:"kato,omitempty"`                                            // Код КАТО
	Index          string                 `protobuf:"bytes,2,opt,name=index,proto3" json:"index,omitempty"`                                          // СПИ (старый почтовый индекс)
	RegionCity     string                 `protobuf:"bytes,3,opt,name=region_city,json=regionCity,proto3" json:"region_city,omitempty"`              // Город-регион (для Астаны, Алматы, Шымкента) в текстовом виде
	City           string                 `protobuf:"bytes,4,opt,name=city,proto3" json:"city,omitempty"`                                            // 3 уровень КАТО (может быть город, сельский округ, районная администрация и др)
	District       string                 `protobuf:"bytes,5,opt,name=district,proto3" json:"district,omitempty"`                                    // Район в текстовом виде
	Region         string                 `protobuf:"bytes,6,opt,name=region,proto3" json:"region,omitempty"`                                        // Область в текстовом виде
	Settlement     string                 `protobuf:"bytes,7,opt,name=settlement,proto3" json:"settlement,omitempty"`                                // Населённый пункт (4 и 5 уровень) в текстовом виде
	Full           string                 `protobuf:"bytes,8,opt,name=full,proto3" json:"full,omitempty"`                                            // Полный адрес
	Country        string                 `protobuf:"bytes,9,opt,name=country,proto3" json:"country,omitempty"`                                      // Наименование страны
	CityTdc        string                 `protobuf:"bytes,10,opt,name=city_tdc,json=cityTdc,proto3" json:"city_tdc,omitempty"`                      // Код вида территории для города (может отсутствовать)
	RegionTdc      string                 `protobuf:"bytes,11,opt,name=region_tdc,json=regionTdc,proto3" json:"region_tdc,omitempty"`                // Код вида территории для области (может отсутствовать)
	DistrictTdc    string                 `protobuf:"bytes,12,opt,name=district_tdc,json=districtTdc,proto3" json:"district_tdc,omitempty"`          // Код типа района
	SettlementTdc  string                 `protobuf:"bytes,13,opt,name=settlement_tdc,json=settlementTdc,proto3" json:"settlement_tdc,omitempty"`    // Код вида территории для пункта (может отсутствовать)
	Street         string                 `protobuf:"bytes,14,opt,name=street,proto3" json:"street,omitempty"`                                       // Улица (может отсутствовать)
	StreetTdc      string                 `protobuf:"bytes,15,opt,name=street_tdc,json=streetTdc,proto3" json:"street_tdc,omitempty"`                // Код вида территории для улицы (может отсутствовать)
	Zone           string                 `protobuf:"bytes,16,opt,name=zone,proto3" json:"zone,omitempty"`                                           // Микрорайон (может отсутствовать)
	ZoneTdc        string                 `protobuf:"bytes,17,opt,name=zone_tdc,json=zoneTdc,proto3" json:"zone_tdc,omitempty"`                      // Код вида территории для микрорайона (может отсутствовать)
	House          string                 `protobuf:"bytes,18,opt,name=house,proto3" json:"house,omitempty"`                                         // Дом (может отсутствовать)
	HouseTdc       string                 `protobuf:"bytes,19,opt,name=house_tdc,json=houseTdc,proto3" json:"house_tdc,omitempty"`                   // Код вида территории для дома (может отсутствовать)
	Body           string                 `protobuf:"bytes,20,opt,name=body,proto3" json:"body,omitempty"`                                           // Корпус (может отсутствовать)
	Building       string                 `protobuf:"bytes,21,opt,name=building,proto3" json:"building,omitempty"`                                   // Строение (может отсутствовать)
	HouseOwnership string                 `protobuf:"bytes,22,opt,name=house_ownership,json=houseOwnership,proto3" json:"house_ownership,omitempty"` // Владение (может отсутствовать)
	Flat           string                 `protobuf:"bytes,23,opt,name=flat,proto3" json:"flat,omitempty"`                                           // Квартира (может отсутствовать)
	FlatTdc        string                 `protobuf:"bytes,24,opt,name=flat_tdc,json=flatTdc,proto3" json:"flat_tdc,omitempty"`                      // Код вида территории для квартиры (может отсутствовать)
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *KATOToABISResp) Reset() {
	*x = KATOToABISResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KATOToABISResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KATOToABISResp) ProtoMessage() {}

func (x *KATOToABISResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KATOToABISResp.ProtoReflect.Descriptor instead.
func (*KATOToABISResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{50}
}

func (x *KATOToABISResp) GetKato() string {
	if x != nil {
		return x.Kato
	}
	return ""
}

func (x *KATOToABISResp) GetIndex() string {
	if x != nil {
		return x.Index
	}
	return ""
}

func (x *KATOToABISResp) GetRegionCity() string {
	if x != nil {
		return x.RegionCity
	}
	return ""
}

func (x *KATOToABISResp) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *KATOToABISResp) GetDistrict() string {
	if x != nil {
		return x.District
	}
	return ""
}

func (x *KATOToABISResp) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *KATOToABISResp) GetSettlement() string {
	if x != nil {
		return x.Settlement
	}
	return ""
}

func (x *KATOToABISResp) GetFull() string {
	if x != nil {
		return x.Full
	}
	return ""
}

func (x *KATOToABISResp) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *KATOToABISResp) GetCityTdc() string {
	if x != nil {
		return x.CityTdc
	}
	return ""
}

func (x *KATOToABISResp) GetRegionTdc() string {
	if x != nil {
		return x.RegionTdc
	}
	return ""
}

func (x *KATOToABISResp) GetDistrictTdc() string {
	if x != nil {
		return x.DistrictTdc
	}
	return ""
}

func (x *KATOToABISResp) GetSettlementTdc() string {
	if x != nil {
		return x.SettlementTdc
	}
	return ""
}

func (x *KATOToABISResp) GetStreet() string {
	if x != nil {
		return x.Street
	}
	return ""
}

func (x *KATOToABISResp) GetStreetTdc() string {
	if x != nil {
		return x.StreetTdc
	}
	return ""
}

func (x *KATOToABISResp) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *KATOToABISResp) GetZoneTdc() string {
	if x != nil {
		return x.ZoneTdc
	}
	return ""
}

func (x *KATOToABISResp) GetHouse() string {
	if x != nil {
		return x.House
	}
	return ""
}

func (x *KATOToABISResp) GetHouseTdc() string {
	if x != nil {
		return x.HouseTdc
	}
	return ""
}

func (x *KATOToABISResp) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *KATOToABISResp) GetBuilding() string {
	if x != nil {
		return x.Building
	}
	return ""
}

func (x *KATOToABISResp) GetHouseOwnership() string {
	if x != nil {
		return x.HouseOwnership
	}
	return ""
}

func (x *KATOToABISResp) GetFlat() string {
	if x != nil {
		return x.Flat
	}
	return ""
}

func (x *KATOToABISResp) GetFlatTdc() string {
	if x != nil {
		return x.FlatTdc
	}
	return ""
}

// Request message for GetOldPostalIndex RPC.
type GetPostalIndexReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Locality      string                 `protobuf:"bytes,1,opt,name=locality,proto3" json:"locality,omitempty"`
	Street        string                 `protobuf:"bytes,2,opt,name=street,proto3" json:"street,omitempty"`
	HouseNumber   string                 `protobuf:"bytes,3,opt,name=house_number,json=houseNumber,proto3" json:"house_number,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPostalIndexReq) Reset() {
	*x = GetPostalIndexReq{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPostalIndexReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostalIndexReq) ProtoMessage() {}

func (x *GetPostalIndexReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostalIndexReq.ProtoReflect.Descriptor instead.
func (*GetPostalIndexReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{51}
}

func (x *GetPostalIndexReq) GetLocality() string {
	if x != nil {
		return x.Locality
	}
	return ""
}

func (x *GetPostalIndexReq) GetStreet() string {
	if x != nil {
		return x.Street
	}
	return ""
}

func (x *GetPostalIndexReq) GetHouseNumber() string {
	if x != nil {
		return x.HouseNumber
	}
	return ""
}

// Response message for GetOldPostalIndex RPC.
type GetPostalIndexResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Locality      string                 `protobuf:"bytes,1,opt,name=locality,proto3" json:"locality,omitempty"`                          // Населенный пункт from the dictionary
	Street        string                 `protobuf:"bytes,2,opt,name=street,proto3" json:"street,omitempty"`                              // Улица from the dictionary
	HouseNumber   string                 `protobuf:"bytes,3,opt,name=house_number,json=houseNumber,proto3" json:"house_number,omitempty"` // Номер дома from the dictionary
	NewIndex      string                 `protobuf:"bytes,4,opt,name=new_index,json=newIndex,proto3" json:"new_index,omitempty"`          // Новый индекс from the dictionary
	OldIndex      string                 `protobuf:"bytes,5,opt,name=old_index,json=oldIndex,proto3" json:"old_index,omitempty"`          // Старый индекс (SPI) from the dictionary
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPostalIndexResp) Reset() {
	*x = GetPostalIndexResp{}
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPostalIndexResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostalIndexResp) ProtoMessage() {}

func (x *GetPostalIndexResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_dictionary_dictionary_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostalIndexResp.ProtoReflect.Descriptor instead.
func (*GetPostalIndexResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_dictionary_dictionary_proto_rawDescGZIP(), []int{52}
}

func (x *GetPostalIndexResp) GetLocality() string {
	if x != nil {
		return x.Locality
	}
	return ""
}

func (x *GetPostalIndexResp) GetStreet() string {
	if x != nil {
		return x.Street
	}
	return ""
}

func (x *GetPostalIndexResp) GetHouseNumber() string {
	if x != nil {
		return x.HouseNumber
	}
	return ""
}

func (x *GetPostalIndexResp) GetNewIndex() string {
	if x != nil {
		return x.NewIndex
	}
	return ""
}

func (x *GetPostalIndexResp) GetOldIndex() string {
	if x != nil {
		return x.OldIndex
	}
	return ""
}

var File_specs_proto_dictionary_dictionary_proto protoreflect.FileDescriptor

const file_specs_proto_dictionary_dictionary_proto_rawDesc = "" +
	"\n" +
	"'specs/proto/dictionary/dictionary.proto\x12\n" +
	"dictionary\"v\n" +
	"\x10TSOIDAddrElement\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x12\x16\n" +
	"\x06nameKz\x18\x02 \x01(\tR\x06nameKz\x12\x16\n" +
	"\x06nameRu\x18\x03 \x01(\tR\x06nameRu\x12\x1e\n" +
	"\n" +
	"changeDate\x18\x04 \x01(\tR\n" +
	"changeDate\"\xe4\x02\n" +
	"\x13KATOMapFromTSOIDReq\x126\n" +
	"\acountry\x18\x01 \x01(\v2\x1c.dictionary.TSOIDAddrElementR\acountry\x124\n" +
	"\x06region\x18\x02 \x01(\v2\x1c.dictionary.TSOIDAddrElementR\x06region\x128\n" +
	"\bdistrict\x18\x03 \x01(\v2\x1c.dictionary.TSOIDAddrElementR\bdistrict\x12\x12\n" +
	"\x04city\x18\x04 \x01(\tR\x04city\x12\x16\n" +
	"\x06street\x18\x05 \x01(\tR\x06street\x12\x12\n" +
	"\x04flat\x18\x06 \x01(\tR\x04flat\x12\x1a\n" +
	"\bbuilding\x18\a \x01(\tR\bbuilding\x12\x16\n" +
	"\x06corpus\x18\b \x01(\tR\x06corpus\x12\x1d\n" +
	"\n" +
	"begin_date\x18\t \x01(\tR\tbeginDate\x12\x12\n" +
	"\x04lang\x18\n" +
	" \x01(\tR\x04lang\"\xcb\x05\n" +
	"\x14KATOMapFromTSOIDResp\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12\x19\n" +
	"\bown_type\x18\x02 \x01(\tR\aownType\x12\x18\n" +
	"\acountry\x18\x03 \x01(\tR\acountry\x12\x1f\n" +
	"\vregion_city\x18\x04 \x01(\tR\n" +
	"regionCity\x12\x12\n" +
	"\x04city\x18\x05 \x01(\tR\x04city\x12\x19\n" +
	"\bcity_tdc\x18\x06 \x01(\tR\acityTdc\x12\x16\n" +
	"\x06region\x18\a \x01(\tR\x06region\x12\x1d\n" +
	"\n" +
	"region_tdc\x18\b \x01(\tR\tregionTdc\x12\x1a\n" +
	"\bdistrict\x18\t \x01(\tR\bdistrict\x12!\n" +
	"\fdistrict_tdc\x18\n" +
	" \x01(\tR\vdistrictTdc\x12\x1e\n" +
	"\n" +
	"settlement\x18\v \x01(\tR\n" +
	"settlement\x12%\n" +
	"\x0esettlement_tdc\x18\f \x01(\tR\rsettlementTdc\x12\x16\n" +
	"\x06street\x18\r \x01(\tR\x06street\x12\x1d\n" +
	"\n" +
	"street_tdc\x18\x0e \x01(\tR\tstreetTdc\x12\x12\n" +
	"\x04zone\x18\x0f \x01(\tR\x04zone\x12\x19\n" +
	"\bzone_tdc\x18\x10 \x01(\tR\azoneTdc\x12\x14\n" +
	"\x05house\x18\x11 \x01(\tR\x05house\x12\x1b\n" +
	"\thouse_tdc\x18\x12 \x01(\tR\bhouseTdc\x12\x12\n" +
	"\x04body\x18\x13 \x01(\tR\x04body\x12\x1a\n" +
	"\bbuilding\x18\x14 \x01(\tR\bbuilding\x12'\n" +
	"\x0fhouse_ownership\x18\x15 \x01(\tR\x0ehouseOwnership\x12\x12\n" +
	"\x04flat\x18\x16 \x01(\tR\x04flat\x12\x19\n" +
	"\bflat_tdc\x18\x17 \x01(\tR\aflatTdc\x12\x14\n" +
	"\x05index\x18\x18 \x01(\tR\x05index\x12\x12\n" +
	"\x04full\x18\x19 \x01(\tR\x04full\x12\x12\n" +
	"\x04kato\x18\x1a \x01(\tR\x04kato\"\x10\n" +
	"\x0eHealthCheckReq\")\n" +
	"\x0fHealthCheckResp\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\"d\n" +
	"\x04Dict\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x16\n" +
	"\x06schema\x18\x04 \x01(\tR\x06schema\"m\n" +
	"\rDictCreateReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x16\n" +
	"\x06schema\x18\x04 \x01(\tR\x06schema\"n\n" +
	"\x0eDictCreateResp\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x16\n" +
	"\x06schema\x18\x04 \x01(\tR\x06schema\"m\n" +
	"\rDictUpdateReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x16\n" +
	"\x06schema\x18\x04 \x01(\tR\x06schema\"n\n" +
	"\x0eDictUpdateResp\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x16\n" +
	"\x06schema\x18\x04 \x01(\tR\x06schema\"\x1f\n" +
	"\rDictDeleteReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"\x10\n" +
	"\x0eDictDeleteResp\"\x1c\n" +
	"\n" +
	"DictGetReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"k\n" +
	"\vDictGetResp\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x16\n" +
	"\x06schema\x18\x04 \x01(\tR\x06schema\"\x89\x01\n" +
	"\x03Doc\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x17\n" +
	"\adict_id\x18\x02 \x01(\tR\x06dictId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x12\n" +
	"\x04data\x18\x04 \x01(\tR\x04data\x12\x14\n" +
	"\x05valid\x18\x05 \x01(\bR\x05valid\x12\x1b\n" +
	"\torder_num\x18\x06 \x01(\x05R\borderNum\"1\n" +
	"\fDocCreateReq\x12!\n" +
	"\x03doc\x18\x01 \x01(\v2\x0f.dictionary.DocR\x03doc\"2\n" +
	"\rDocCreateResp\x12!\n" +
	"\x03doc\x18\x01 \x01(\v2\x0f.dictionary.DocR\x03doc\"1\n" +
	"\fDocUpdateReq\x12!\n" +
	"\x03doc\x18\x01 \x01(\v2\x0f.dictionary.DocR\x03doc\"2\n" +
	"\rDocUpdateResp\x12!\n" +
	"\x03doc\x18\x01 \x01(\v2\x0f.dictionary.DocR\x03doc\"\x1e\n" +
	"\fDocDeleteReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"\x0f\n" +
	"\rDocDeleteResp\"\x1b\n" +
	"\tDocGetReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"/\n" +
	"\n" +
	"DocGetResp\x12!\n" +
	"\x03doc\x18\x01 \x01(\v2\x0f.dictionary.DocR\x03doc\"I\n" +
	"\x0fDocGetByNameReq\x12\x1b\n" +
	"\tdict_name\x18\x01 \x01(\tR\bdictName\x12\x19\n" +
	"\bdoc_name\x18\x02 \x01(\tR\adocName\"5\n" +
	"\x10DocGetByNameResp\x12!\n" +
	"\x03doc\x18\x01 \x01(\v2\x0f.dictionary.DocR\x03doc\"/\n" +
	"\x10DictGetByNameReq\x12\x1b\n" +
	"\tdict_name\x18\x01 \x01(\tR\bdictName\"9\n" +
	"\x11DictGetByNameResp\x12$\n" +
	"\x04dict\x18\x01 \x01(\v2\x10.dictionary.DictR\x04dict\"~\n" +
	"\rDocGetListReq\x12\x17\n" +
	"\adict_id\x18\x01 \x01(\tR\x06dictId\x12\x1c\n" +
	"\tonlyValid\x18\x02 \x01(\bR\tonlyValid\x126\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x16.dictionary.PaginationR\n" +
	"pagination\"5\n" +
	"\x0eDocGetListResp\x12#\n" +
	"\x04list\x18\x01 \x03(\v2\x0f.dictionary.DocR\x04list\"\x88\x01\n" +
	"\x13DocGetListByNameReq\x12\x1b\n" +
	"\tdict_name\x18\x01 \x01(\tR\bdictName\x12\x1c\n" +
	"\tonlyValid\x18\x02 \x01(\bR\tonlyValid\x126\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x16.dictionary.PaginationR\n" +
	"pagination\";\n" +
	"\x14DocGetListByNameResp\x12#\n" +
	"\x04list\x18\x01 \x03(\v2\x0f.dictionary.DocR\x04list\"\x97\x01\n" +
	"\x06Filter\x12\x1d\n" +
	"\n" +
	"data_field\x18\x01 \x01(\tR\tdataField\x12\x1c\n" +
	"\toperation\x18\x02 \x01(\tR\toperation\x12\x14\n" +
	"\x05value\x18\x03 \x01(\tR\x05value\x12(\n" +
	"\x05group\x18\x04 \x03(\v2\x12.dictionary.FilterR\x05group\x12\x10\n" +
	"\x03not\x18\x05 \x01(\bR\x03not\"\xaa\x01\n" +
	"\x15DocGetListByFilterReq\x12\x17\n" +
	"\adict_id\x18\x01 \x01(\tR\x06dictId\x12,\n" +
	"\afilters\x18\x02 \x03(\v2\x12.dictionary.FilterR\afilters\x12\x12\n" +
	"\x04sort\x18\x03 \x03(\tR\x04sort\x126\n" +
	"\n" +
	"pagination\x18\x04 \x01(\v2\x16.dictionary.PaginationR\n" +
	"pagination\"=\n" +
	"\x16DocGetListByFilterResp\x12#\n" +
	"\x04list\x18\x01 \x03(\v2\x0f.dictionary.DocR\x04list\"6\n" +
	"\n" +
	"Pagination\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count\":\n" +
	"\x0eDictGetListReq\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count\"7\n" +
	"\x0fDictGetListResp\x12$\n" +
	"\x04list\x18\x01 \x03(\v2\x10.dictionary.DictR\x04list\"Q\n" +
	"\tJobRunReq\x12\x18\n" +
	"\ajobType\x18\x01 \x01(\tR\ajobType\x12\x16\n" +
	"\x06source\x18\x02 \x01(\tR\x06source\x12\x12\n" +
	"\x04lang\x18\x03 \x01(\tR\x04lang\"\f\n" +
	"\n" +
	"JobRunResp\"+\n" +
	"\x0fJobGetStatusReq\x12\x18\n" +
	"\ajobType\x18\x01 \x01(\tR\ajobType\"\x9f\x01\n" +
	"\x10JobGetStatusResp\x12L\n" +
	"\n" +
	"statusInfo\x18\x01 \x03(\v2,.dictionary.JobGetStatusResp.StatusInfoEntryR\n" +
	"statusInfo\x1a=\n" +
	"\x0fStatusInfoEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"&\n" +
	"\n" +
	"JobStopReq\x12\x18\n" +
	"\ajobType\x18\x01 \x01(\tR\ajobType\"\r\n" +
	"\vJobStopResp\"\x14\n" +
	"\x12JobGetStatusAllReq\"O\n" +
	"\x13JobGetStatusAllResp\x128\n" +
	"\bstatuses\x18\x01 \x03(\v2\x1c.dictionary.JobGetStatusRespR\bstatuses\"_\n" +
	"\x11DocOrderUpdateReq\x12\x16\n" +
	"\x06dictId\x18\x01 \x01(\tR\x06dictId\x12\x1a\n" +
	"\borderNum\x18\x02 \x01(\x05R\borderNum\x12\x16\n" +
	"\x06DocIds\x18\x03 \x03(\tR\x06DocIds\"\x14\n" +
	"\x12DocOrderUpdateResp\"\xc7\x01\n" +
	"\x11DocTreeGetLineReq\x12\x16\n" +
	"\x06dictId\x18\x01 \x01(\tR\x06dictId\x12&\n" +
	"\x0edocSelectField\x18\x02 \x01(\tR\x0edocSelectField\x12&\n" +
	"\x0edocSelectValue\x18\x03 \x01(\tR\x0edocSelectValue\x12\x1e\n" +
	"\n" +
	"docIdField\x18\x04 \x01(\tR\n" +
	"docIdField\x12*\n" +
	"\x10docParentIdField\x18\x05 \x01(\tR\x10docParentIdField\"9\n" +
	"\x12DocTreeGetLineResp\x12#\n" +
	"\x04list\x18\x01 \x03(\v2\x0f.dictionary.DocR\x04list\"}\n" +
	"\rKATOToABISReq\x12\x12\n" +
	"\x04kato\x18\x01 \x01(\tR\x04kato\x12\x16\n" +
	"\x06street\x18\x02 \x01(\tR\x06street\x12\x14\n" +
	"\x05house\x18\x03 \x01(\tR\x05house\x12\x12\n" +
	"\x04flat\x18\x04 \x01(\tR\x04flat\x12\x16\n" +
	"\x06corpus\x18\x05 \x01(\tR\x06corpus\"\x96\x05\n" +
	"\x0eKATOToABISResp\x12\x12\n" +
	"\x04kato\x18\x01 \x01(\tR\x04kato\x12\x14\n" +
	"\x05index\x18\x02 \x01(\tR\x05index\x12\x1f\n" +
	"\vregion_city\x18\x03 \x01(\tR\n" +
	"regionCity\x12\x12\n" +
	"\x04city\x18\x04 \x01(\tR\x04city\x12\x1a\n" +
	"\bdistrict\x18\x05 \x01(\tR\bdistrict\x12\x16\n" +
	"\x06region\x18\x06 \x01(\tR\x06region\x12\x1e\n" +
	"\n" +
	"settlement\x18\a \x01(\tR\n" +
	"settlement\x12\x12\n" +
	"\x04full\x18\b \x01(\tR\x04full\x12\x18\n" +
	"\acountry\x18\t \x01(\tR\acountry\x12\x19\n" +
	"\bcity_tdc\x18\n" +
	" \x01(\tR\acityTdc\x12\x1d\n" +
	"\n" +
	"region_tdc\x18\v \x01(\tR\tregionTdc\x12!\n" +
	"\fdistrict_tdc\x18\f \x01(\tR\vdistrictTdc\x12%\n" +
	"\x0esettlement_tdc\x18\r \x01(\tR\rsettlementTdc\x12\x16\n" +
	"\x06street\x18\x0e \x01(\tR\x06street\x12\x1d\n" +
	"\n" +
	"street_tdc\x18\x0f \x01(\tR\tstreetTdc\x12\x12\n" +
	"\x04zone\x18\x10 \x01(\tR\x04zone\x12\x19\n" +
	"\bzone_tdc\x18\x11 \x01(\tR\azoneTdc\x12\x14\n" +
	"\x05house\x18\x12 \x01(\tR\x05house\x12\x1b\n" +
	"\thouse_tdc\x18\x13 \x01(\tR\bhouseTdc\x12\x12\n" +
	"\x04body\x18\x14 \x01(\tR\x04body\x12\x1a\n" +
	"\bbuilding\x18\x15 \x01(\tR\bbuilding\x12'\n" +
	"\x0fhouse_ownership\x18\x16 \x01(\tR\x0ehouseOwnership\x12\x12\n" +
	"\x04flat\x18\x17 \x01(\tR\x04flat\x12\x19\n" +
	"\bflat_tdc\x18\x18 \x01(\tR\aflatTdc\"j\n" +
	"\x11GetPostalIndexReq\x12\x1a\n" +
	"\blocality\x18\x01 \x01(\tR\blocality\x12\x16\n" +
	"\x06street\x18\x02 \x01(\tR\x06street\x12!\n" +
	"\fhouse_number\x18\x03 \x01(\tR\vhouseNumber\"\xa5\x01\n" +
	"\x12GetPostalIndexResp\x12\x1a\n" +
	"\blocality\x18\x01 \x01(\tR\blocality\x12\x16\n" +
	"\x06street\x18\x02 \x01(\tR\x06street\x12!\n" +
	"\fhouse_number\x18\x03 \x01(\tR\vhouseNumber\x12\x1b\n" +
	"\tnew_index\x18\x04 \x01(\tR\bnewIndex\x12\x1b\n" +
	"\told_index\x18\x05 \x01(\tR\boldIndex2\xdb\r\n" +
	"\n" +
	"Dictionary\x12F\n" +
	"\vHealthCheck\x12\x1a.dictionary.HealthCheckReq\x1a\x1b.dictionary.HealthCheckResp\x12C\n" +
	"\n" +
	"DictCreate\x12\x19.dictionary.DictCreateReq\x1a\x1a.dictionary.DictCreateResp\x12C\n" +
	"\n" +
	"DictUpdate\x12\x19.dictionary.DictUpdateReq\x1a\x1a.dictionary.DictUpdateResp\x12C\n" +
	"\n" +
	"DictDelete\x12\x19.dictionary.DictDeleteReq\x1a\x1a.dictionary.DictDeleteResp\x12:\n" +
	"\aDictGet\x12\x16.dictionary.DictGetReq\x1a\x17.dictionary.DictGetResp\x12L\n" +
	"\rDictGetByName\x12\x1c.dictionary.DictGetByNameReq\x1a\x1d.dictionary.DictGetByNameResp\x12F\n" +
	"\vDictGetList\x12\x1a.dictionary.DictGetListReq\x1a\x1b.dictionary.DictGetListResp\x12@\n" +
	"\tDocCreate\x12\x18.dictionary.DocCreateReq\x1a\x19.dictionary.DocCreateResp\x12@\n" +
	"\tDocUpdate\x12\x18.dictionary.DocUpdateReq\x1a\x19.dictionary.DocUpdateResp\x12O\n" +
	"\x0eDocOrderUpdate\x12\x1d.dictionary.DocOrderUpdateReq\x1a\x1e.dictionary.DocOrderUpdateResp\x12@\n" +
	"\tDocDelete\x12\x18.dictionary.DocDeleteReq\x1a\x19.dictionary.DocDeleteResp\x127\n" +
	"\x06DocGet\x12\x15.dictionary.DocGetReq\x1a\x16.dictionary.DocGetResp\x12C\n" +
	"\n" +
	"DocGetList\x12\x19.dictionary.DocGetListReq\x1a\x1a.dictionary.DocGetListResp\x12I\n" +
	"\fDocGetByName\x12\x1b.dictionary.DocGetByNameReq\x1a\x1c.dictionary.DocGetByNameResp\x12U\n" +
	"\x10DocGetListByName\x12\x1f.dictionary.DocGetListByNameReq\x1a .dictionary.DocGetListByNameResp\x12[\n" +
	"\x12DocGetListByFilter\x12!.dictionary.DocGetListByFilterReq\x1a\".dictionary.DocGetListByFilterResp\x12O\n" +
	"\x0eDocTreeGetLine\x12\x1d.dictionary.DocTreeGetLineReq\x1a\x1e.dictionary.DocTreeGetLineResp\x127\n" +
	"\x06JobRun\x12\x15.dictionary.JobRunReq\x1a\x16.dictionary.JobRunResp\x12I\n" +
	"\fJobGetStatus\x12\x1b.dictionary.JobGetStatusReq\x1a\x1c.dictionary.JobGetStatusResp\x12:\n" +
	"\aJobStop\x12\x16.dictionary.JobStopReq\x1a\x17.dictionary.JobStopResp\x12R\n" +
	"\x0fJobGetStatusAll\x12\x1e.dictionary.JobGetStatusAllReq\x1a\x1f.dictionary.JobGetStatusAllResp\x12U\n" +
	"\x10KATOMapFromTSOID\x12\x1f.dictionary.KATOMapFromTSOIDReq\x1a .dictionary.KATOMapFromTSOIDResp\x12C\n" +
	"\n" +
	"KATOToABIS\x12\x19.dictionary.KATOToABISReq\x1a\x1a.dictionary.KATOToABISResp\x12O\n" +
	"\x0eGetPostalIndex\x12\x1d.dictionary.GetPostalIndexReq\x1a\x1e.dictionary.GetPostalIndexRespB\x18Z\x16specs/proto/dictionaryb\x06proto3"

var (
	file_specs_proto_dictionary_dictionary_proto_rawDescOnce sync.Once
	file_specs_proto_dictionary_dictionary_proto_rawDescData []byte
)

func file_specs_proto_dictionary_dictionary_proto_rawDescGZIP() []byte {
	file_specs_proto_dictionary_dictionary_proto_rawDescOnce.Do(func() {
		file_specs_proto_dictionary_dictionary_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_specs_proto_dictionary_dictionary_proto_rawDesc), len(file_specs_proto_dictionary_dictionary_proto_rawDesc)))
	})
	return file_specs_proto_dictionary_dictionary_proto_rawDescData
}

var file_specs_proto_dictionary_dictionary_proto_msgTypes = make([]protoimpl.MessageInfo, 54)
var file_specs_proto_dictionary_dictionary_proto_goTypes = []any{
	(*TSOIDAddrElement)(nil),       // 0: dictionary.TSOIDAddrElement
	(*KATOMapFromTSOIDReq)(nil),    // 1: dictionary.KATOMapFromTSOIDReq
	(*KATOMapFromTSOIDResp)(nil),   // 2: dictionary.KATOMapFromTSOIDResp
	(*HealthCheckReq)(nil),         // 3: dictionary.HealthCheckReq
	(*HealthCheckResp)(nil),        // 4: dictionary.HealthCheckResp
	(*Dict)(nil),                   // 5: dictionary.Dict
	(*DictCreateReq)(nil),          // 6: dictionary.DictCreateReq
	(*DictCreateResp)(nil),         // 7: dictionary.DictCreateResp
	(*DictUpdateReq)(nil),          // 8: dictionary.DictUpdateReq
	(*DictUpdateResp)(nil),         // 9: dictionary.DictUpdateResp
	(*DictDeleteReq)(nil),          // 10: dictionary.DictDeleteReq
	(*DictDeleteResp)(nil),         // 11: dictionary.DictDeleteResp
	(*DictGetReq)(nil),             // 12: dictionary.DictGetReq
	(*DictGetResp)(nil),            // 13: dictionary.DictGetResp
	(*Doc)(nil),                    // 14: dictionary.Doc
	(*DocCreateReq)(nil),           // 15: dictionary.DocCreateReq
	(*DocCreateResp)(nil),          // 16: dictionary.DocCreateResp
	(*DocUpdateReq)(nil),           // 17: dictionary.DocUpdateReq
	(*DocUpdateResp)(nil),          // 18: dictionary.DocUpdateResp
	(*DocDeleteReq)(nil),           // 19: dictionary.DocDeleteReq
	(*DocDeleteResp)(nil),          // 20: dictionary.DocDeleteResp
	(*DocGetReq)(nil),              // 21: dictionary.DocGetReq
	(*DocGetResp)(nil),             // 22: dictionary.DocGetResp
	(*DocGetByNameReq)(nil),        // 23: dictionary.DocGetByNameReq
	(*DocGetByNameResp)(nil),       // 24: dictionary.DocGetByNameResp
	(*DictGetByNameReq)(nil),       // 25: dictionary.DictGetByNameReq
	(*DictGetByNameResp)(nil),      // 26: dictionary.DictGetByNameResp
	(*DocGetListReq)(nil),          // 27: dictionary.DocGetListReq
	(*DocGetListResp)(nil),         // 28: dictionary.DocGetListResp
	(*DocGetListByNameReq)(nil),    // 29: dictionary.DocGetListByNameReq
	(*DocGetListByNameResp)(nil),   // 30: dictionary.DocGetListByNameResp
	(*Filter)(nil),                 // 31: dictionary.Filter
	(*DocGetListByFilterReq)(nil),  // 32: dictionary.DocGetListByFilterReq
	(*DocGetListByFilterResp)(nil), // 33: dictionary.DocGetListByFilterResp
	(*Pagination)(nil),             // 34: dictionary.Pagination
	(*DictGetListReq)(nil),         // 35: dictionary.DictGetListReq
	(*DictGetListResp)(nil),        // 36: dictionary.DictGetListResp
	(*JobRunReq)(nil),              // 37: dictionary.JobRunReq
	(*JobRunResp)(nil),             // 38: dictionary.JobRunResp
	(*JobGetStatusReq)(nil),        // 39: dictionary.JobGetStatusReq
	(*JobGetStatusResp)(nil),       // 40: dictionary.JobGetStatusResp
	(*JobStopReq)(nil),             // 41: dictionary.JobStopReq
	(*JobStopResp)(nil),            // 42: dictionary.JobStopResp
	(*JobGetStatusAllReq)(nil),     // 43: dictionary.JobGetStatusAllReq
	(*JobGetStatusAllResp)(nil),    // 44: dictionary.JobGetStatusAllResp
	(*DocOrderUpdateReq)(nil),      // 45: dictionary.DocOrderUpdateReq
	(*DocOrderUpdateResp)(nil),     // 46: dictionary.DocOrderUpdateResp
	(*DocTreeGetLineReq)(nil),      // 47: dictionary.DocTreeGetLineReq
	(*DocTreeGetLineResp)(nil),     // 48: dictionary.DocTreeGetLineResp
	(*KATOToABISReq)(nil),          // 49: dictionary.KATOToABISReq
	(*KATOToABISResp)(nil),         // 50: dictionary.KATOToABISResp
	(*GetPostalIndexReq)(nil),      // 51: dictionary.GetPostalIndexReq
	(*GetPostalIndexResp)(nil),     // 52: dictionary.GetPostalIndexResp
	nil,                            // 53: dictionary.JobGetStatusResp.StatusInfoEntry
}
var file_specs_proto_dictionary_dictionary_proto_depIdxs = []int32{
	0,  // 0: dictionary.KATOMapFromTSOIDReq.country:type_name -> dictionary.TSOIDAddrElement
	0,  // 1: dictionary.KATOMapFromTSOIDReq.region:type_name -> dictionary.TSOIDAddrElement
	0,  // 2: dictionary.KATOMapFromTSOIDReq.district:type_name -> dictionary.TSOIDAddrElement
	14, // 3: dictionary.DocCreateReq.doc:type_name -> dictionary.Doc
	14, // 4: dictionary.DocCreateResp.doc:type_name -> dictionary.Doc
	14, // 5: dictionary.DocUpdateReq.doc:type_name -> dictionary.Doc
	14, // 6: dictionary.DocUpdateResp.doc:type_name -> dictionary.Doc
	14, // 7: dictionary.DocGetResp.doc:type_name -> dictionary.Doc
	14, // 8: dictionary.DocGetByNameResp.doc:type_name -> dictionary.Doc
	5,  // 9: dictionary.DictGetByNameResp.dict:type_name -> dictionary.Dict
	34, // 10: dictionary.DocGetListReq.pagination:type_name -> dictionary.Pagination
	14, // 11: dictionary.DocGetListResp.list:type_name -> dictionary.Doc
	34, // 12: dictionary.DocGetListByNameReq.pagination:type_name -> dictionary.Pagination
	14, // 13: dictionary.DocGetListByNameResp.list:type_name -> dictionary.Doc
	31, // 14: dictionary.Filter.group:type_name -> dictionary.Filter
	31, // 15: dictionary.DocGetListByFilterReq.filters:type_name -> dictionary.Filter
	34, // 16: dictionary.DocGetListByFilterReq.pagination:type_name -> dictionary.Pagination
	14, // 17: dictionary.DocGetListByFilterResp.list:type_name -> dictionary.Doc
	5,  // 18: dictionary.DictGetListResp.list:type_name -> dictionary.Dict
	53, // 19: dictionary.JobGetStatusResp.statusInfo:type_name -> dictionary.JobGetStatusResp.StatusInfoEntry
	40, // 20: dictionary.JobGetStatusAllResp.statuses:type_name -> dictionary.JobGetStatusResp
	14, // 21: dictionary.DocTreeGetLineResp.list:type_name -> dictionary.Doc
	3,  // 22: dictionary.Dictionary.HealthCheck:input_type -> dictionary.HealthCheckReq
	6,  // 23: dictionary.Dictionary.DictCreate:input_type -> dictionary.DictCreateReq
	8,  // 24: dictionary.Dictionary.DictUpdate:input_type -> dictionary.DictUpdateReq
	10, // 25: dictionary.Dictionary.DictDelete:input_type -> dictionary.DictDeleteReq
	12, // 26: dictionary.Dictionary.DictGet:input_type -> dictionary.DictGetReq
	25, // 27: dictionary.Dictionary.DictGetByName:input_type -> dictionary.DictGetByNameReq
	35, // 28: dictionary.Dictionary.DictGetList:input_type -> dictionary.DictGetListReq
	15, // 29: dictionary.Dictionary.DocCreate:input_type -> dictionary.DocCreateReq
	17, // 30: dictionary.Dictionary.DocUpdate:input_type -> dictionary.DocUpdateReq
	45, // 31: dictionary.Dictionary.DocOrderUpdate:input_type -> dictionary.DocOrderUpdateReq
	19, // 32: dictionary.Dictionary.DocDelete:input_type -> dictionary.DocDeleteReq
	21, // 33: dictionary.Dictionary.DocGet:input_type -> dictionary.DocGetReq
	27, // 34: dictionary.Dictionary.DocGetList:input_type -> dictionary.DocGetListReq
	23, // 35: dictionary.Dictionary.DocGetByName:input_type -> dictionary.DocGetByNameReq
	29, // 36: dictionary.Dictionary.DocGetListByName:input_type -> dictionary.DocGetListByNameReq
	32, // 37: dictionary.Dictionary.DocGetListByFilter:input_type -> dictionary.DocGetListByFilterReq
	47, // 38: dictionary.Dictionary.DocTreeGetLine:input_type -> dictionary.DocTreeGetLineReq
	37, // 39: dictionary.Dictionary.JobRun:input_type -> dictionary.JobRunReq
	39, // 40: dictionary.Dictionary.JobGetStatus:input_type -> dictionary.JobGetStatusReq
	41, // 41: dictionary.Dictionary.JobStop:input_type -> dictionary.JobStopReq
	43, // 42: dictionary.Dictionary.JobGetStatusAll:input_type -> dictionary.JobGetStatusAllReq
	1,  // 43: dictionary.Dictionary.KATOMapFromTSOID:input_type -> dictionary.KATOMapFromTSOIDReq
	49, // 44: dictionary.Dictionary.KATOToABIS:input_type -> dictionary.KATOToABISReq
	51, // 45: dictionary.Dictionary.GetPostalIndex:input_type -> dictionary.GetPostalIndexReq
	4,  // 46: dictionary.Dictionary.HealthCheck:output_type -> dictionary.HealthCheckResp
	7,  // 47: dictionary.Dictionary.DictCreate:output_type -> dictionary.DictCreateResp
	9,  // 48: dictionary.Dictionary.DictUpdate:output_type -> dictionary.DictUpdateResp
	11, // 49: dictionary.Dictionary.DictDelete:output_type -> dictionary.DictDeleteResp
	13, // 50: dictionary.Dictionary.DictGet:output_type -> dictionary.DictGetResp
	26, // 51: dictionary.Dictionary.DictGetByName:output_type -> dictionary.DictGetByNameResp
	36, // 52: dictionary.Dictionary.DictGetList:output_type -> dictionary.DictGetListResp
	16, // 53: dictionary.Dictionary.DocCreate:output_type -> dictionary.DocCreateResp
	18, // 54: dictionary.Dictionary.DocUpdate:output_type -> dictionary.DocUpdateResp
	46, // 55: dictionary.Dictionary.DocOrderUpdate:output_type -> dictionary.DocOrderUpdateResp
	20, // 56: dictionary.Dictionary.DocDelete:output_type -> dictionary.DocDeleteResp
	22, // 57: dictionary.Dictionary.DocGet:output_type -> dictionary.DocGetResp
	28, // 58: dictionary.Dictionary.DocGetList:output_type -> dictionary.DocGetListResp
	24, // 59: dictionary.Dictionary.DocGetByName:output_type -> dictionary.DocGetByNameResp
	30, // 60: dictionary.Dictionary.DocGetListByName:output_type -> dictionary.DocGetListByNameResp
	33, // 61: dictionary.Dictionary.DocGetListByFilter:output_type -> dictionary.DocGetListByFilterResp
	48, // 62: dictionary.Dictionary.DocTreeGetLine:output_type -> dictionary.DocTreeGetLineResp
	38, // 63: dictionary.Dictionary.JobRun:output_type -> dictionary.JobRunResp
	40, // 64: dictionary.Dictionary.JobGetStatus:output_type -> dictionary.JobGetStatusResp
	42, // 65: dictionary.Dictionary.JobStop:output_type -> dictionary.JobStopResp
	44, // 66: dictionary.Dictionary.JobGetStatusAll:output_type -> dictionary.JobGetStatusAllResp
	2,  // 67: dictionary.Dictionary.KATOMapFromTSOID:output_type -> dictionary.KATOMapFromTSOIDResp
	50, // 68: dictionary.Dictionary.KATOToABIS:output_type -> dictionary.KATOToABISResp
	52, // 69: dictionary.Dictionary.GetPostalIndex:output_type -> dictionary.GetPostalIndexResp
	46, // [46:70] is the sub-list for method output_type
	22, // [22:46] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_specs_proto_dictionary_dictionary_proto_init() }
func file_specs_proto_dictionary_dictionary_proto_init() {
	if File_specs_proto_dictionary_dictionary_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_specs_proto_dictionary_dictionary_proto_rawDesc), len(file_specs_proto_dictionary_dictionary_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   54,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_specs_proto_dictionary_dictionary_proto_goTypes,
		DependencyIndexes: file_specs_proto_dictionary_dictionary_proto_depIdxs,
		MessageInfos:      file_specs_proto_dictionary_dictionary_proto_msgTypes,
	}.Build()
	File_specs_proto_dictionary_dictionary_proto = out.File
	file_specs_proto_dictionary_dictionary_proto_goTypes = nil
	file_specs_proto_dictionary_dictionary_proto_depIdxs = nil
}
