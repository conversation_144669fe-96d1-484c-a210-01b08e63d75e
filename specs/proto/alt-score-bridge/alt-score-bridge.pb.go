// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v6.32.1
// source: specs/proto/alt-score-bridge/alt-score-bridge.proto

package alt_score_bridge

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HealthCheckReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckReq) Reset() {
	*x = HealthCheckReq{}
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckReq) ProtoMessage() {}

func (x *HealthCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckReq.ProtoReflect.Descriptor instead.
func (*HealthCheckReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDescGZIP(), []int{0}
}

type HealthCheckResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckResp) Reset() {
	*x = HealthCheckResp{}
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResp) ProtoMessage() {}

func (x *HealthCheckResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResp.ProtoReflect.Descriptor instead.
func (*HealthCheckResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDescGZIP(), []int{1}
}

func (x *HealthCheckResp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type ParseBankStatementRequest struct {
	state         protoimpl.MessageState               `protogen:"open.v1"`
	BankStatement []byte                               `protobuf:"bytes,1,opt,name=bankStatement,proto3" json:"bankStatement,omitempty"`
	BankName      string                               `protobuf:"bytes,2,opt,name=bankName,proto3" json:"bankName,omitempty"`
	AltScoreUuid  string                               `protobuf:"bytes,3,opt,name=alt_score_uuid,json=altScoreUuid,proto3" json:"alt_score_uuid,omitempty"`
	ClientInfo    *ParseBankStatementRequestClientInfo `protobuf:"bytes,4,opt,name=client_info,json=clientInfo,proto3,oneof" json:"client_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParseBankStatementRequest) Reset() {
	*x = ParseBankStatementRequest{}
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParseBankStatementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseBankStatementRequest) ProtoMessage() {}

func (x *ParseBankStatementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseBankStatementRequest.ProtoReflect.Descriptor instead.
func (*ParseBankStatementRequest) Descriptor() ([]byte, []int) {
	return file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDescGZIP(), []int{2}
}

func (x *ParseBankStatementRequest) GetBankStatement() []byte {
	if x != nil {
		return x.BankStatement
	}
	return nil
}

func (x *ParseBankStatementRequest) GetBankName() string {
	if x != nil {
		return x.BankName
	}
	return ""
}

func (x *ParseBankStatementRequest) GetAltScoreUuid() string {
	if x != nil {
		return x.AltScoreUuid
	}
	return ""
}

func (x *ParseBankStatementRequest) GetClientInfo() *ParseBankStatementRequestClientInfo {
	if x != nil {
		return x.ClientInfo
	}
	return nil
}

type ParseBankStatementRequestClientInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Iin           string                 `protobuf:"bytes,1,opt,name=iin,proto3" json:"iin,omitempty"`
	FullName      string                 `protobuf:"bytes,2,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty"`
	UserType      string                 `protobuf:"bytes,3,opt,name=user_type,json=userType,proto3" json:"user_type,omitempty"`
	ApplicationId uint64                 `protobuf:"varint,4,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	ClientId      uint64                 `protobuf:"varint,5,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	TrackId       uint64                 `protobuf:"varint,6,opt,name=track_id,json=trackId,proto3" json:"track_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParseBankStatementRequestClientInfo) Reset() {
	*x = ParseBankStatementRequestClientInfo{}
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParseBankStatementRequestClientInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseBankStatementRequestClientInfo) ProtoMessage() {}

func (x *ParseBankStatementRequestClientInfo) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseBankStatementRequestClientInfo.ProtoReflect.Descriptor instead.
func (*ParseBankStatementRequestClientInfo) Descriptor() ([]byte, []int) {
	return file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDescGZIP(), []int{3}
}

func (x *ParseBankStatementRequestClientInfo) GetIin() string {
	if x != nil {
		return x.Iin
	}
	return ""
}

func (x *ParseBankStatementRequestClientInfo) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *ParseBankStatementRequestClientInfo) GetUserType() string {
	if x != nil {
		return x.UserType
	}
	return ""
}

func (x *ParseBankStatementRequestClientInfo) GetApplicationId() uint64 {
	if x != nil {
		return x.ApplicationId
	}
	return 0
}

func (x *ParseBankStatementRequestClientInfo) GetClientId() uint64 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *ParseBankStatementRequestClientInfo) GetTrackId() uint64 {
	if x != nil {
		return x.TrackId
	}
	return 0
}

type ParseBankStatementResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsDiscrepancy bool                   `protobuf:"varint,1,opt,name=is_discrepancy,json=isDiscrepancy,proto3" json:"is_discrepancy,omitempty"`
	UserDetail    *UserDetail            `protobuf:"bytes,2,opt,name=user_detail,json=userDetail,proto3" json:"user_detail,omitempty"`
	Accounts      []*Account             `protobuf:"bytes,3,rep,name=accounts,proto3" json:"accounts,omitempty"`
	AltScoreId    int64                  `protobuf:"varint,4,opt,name=altScore_id,json=altScoreId,proto3" json:"altScore_id,omitempty"`
	RequestLogId  *string                `protobuf:"bytes,5,opt,name=request_log_id,json=requestLogId,proto3,oneof" json:"request_log_id,omitempty"`
	Error         *ErrorDetail           `protobuf:"bytes,6,opt,name=error,proto3,oneof" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParseBankStatementResponse) Reset() {
	*x = ParseBankStatementResponse{}
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParseBankStatementResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseBankStatementResponse) ProtoMessage() {}

func (x *ParseBankStatementResponse) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseBankStatementResponse.ProtoReflect.Descriptor instead.
func (*ParseBankStatementResponse) Descriptor() ([]byte, []int) {
	return file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDescGZIP(), []int{4}
}

func (x *ParseBankStatementResponse) GetIsDiscrepancy() bool {
	if x != nil {
		return x.IsDiscrepancy
	}
	return false
}

func (x *ParseBankStatementResponse) GetUserDetail() *UserDetail {
	if x != nil {
		return x.UserDetail
	}
	return nil
}

func (x *ParseBankStatementResponse) GetAccounts() []*Account {
	if x != nil {
		return x.Accounts
	}
	return nil
}

func (x *ParseBankStatementResponse) GetAltScoreId() int64 {
	if x != nil {
		return x.AltScoreId
	}
	return 0
}

func (x *ParseBankStatementResponse) GetRequestLogId() string {
	if x != nil && x.RequestLogId != nil {
		return *x.RequestLogId
	}
	return ""
}

func (x *ParseBankStatementResponse) GetError() *ErrorDetail {
	if x != nil {
		return x.Error
	}
	return nil
}

type UserDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FullName      string                 `protobuf:"bytes,1,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty"`
	Iin           *string                `protobuf:"bytes,2,opt,name=iin,proto3,oneof" json:"iin,omitempty"`
	Bin           *string                `protobuf:"bytes,3,opt,name=bin,proto3,oneof" json:"bin,omitempty"`
	BankName      string                 `protobuf:"bytes,4,opt,name=bank_name,json=bankName,proto3" json:"bank_name,omitempty"`
	CardNumber    *string                `protobuf:"bytes,5,opt,name=card_number,json=cardNumber,proto3,oneof" json:"card_number,omitempty"`
	Period        *Period                `protobuf:"bytes,6,opt,name=period,proto3" json:"period,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserDetail) Reset() {
	*x = UserDetail{}
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserDetail) ProtoMessage() {}

func (x *UserDetail) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserDetail.ProtoReflect.Descriptor instead.
func (*UserDetail) Descriptor() ([]byte, []int) {
	return file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDescGZIP(), []int{5}
}

func (x *UserDetail) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *UserDetail) GetIin() string {
	if x != nil && x.Iin != nil {
		return *x.Iin
	}
	return ""
}

func (x *UserDetail) GetBin() string {
	if x != nil && x.Bin != nil {
		return *x.Bin
	}
	return ""
}

func (x *UserDetail) GetBankName() string {
	if x != nil {
		return x.BankName
	}
	return ""
}

func (x *UserDetail) GetCardNumber() string {
	if x != nil && x.CardNumber != nil {
		return *x.CardNumber
	}
	return ""
}

func (x *UserDetail) GetPeriod() *Period {
	if x != nil {
		return x.Period
	}
	return nil
}

type Account struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccountNumber string                 `protobuf:"bytes,1,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	Currency      string                 `protobuf:"bytes,2,opt,name=currency,proto3" json:"currency,omitempty"`
	Balance       *Balance               `protobuf:"bytes,3,opt,name=balance,proto3" json:"balance,omitempty"`
	Transaction   *TransactionSummary    `protobuf:"bytes,4,opt,name=transaction,proto3,oneof" json:"transaction,omitempty"`
	Total         *Total                 `protobuf:"bytes,5,opt,name=total,proto3,oneof" json:"total,omitempty"`
	Limit         *Limit                 `protobuf:"bytes,6,opt,name=limit,proto3,oneof" json:"limit,omitempty"`
	Transactions  []*Transaction         `protobuf:"bytes,7,rep,name=transactions,proto3" json:"transactions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Account) Reset() {
	*x = Account{}
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Account) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Account) ProtoMessage() {}

func (x *Account) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Account.ProtoReflect.Descriptor instead.
func (*Account) Descriptor() ([]byte, []int) {
	return file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDescGZIP(), []int{6}
}

func (x *Account) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *Account) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *Account) GetBalance() *Balance {
	if x != nil {
		return x.Balance
	}
	return nil
}

func (x *Account) GetTransaction() *TransactionSummary {
	if x != nil {
		return x.Transaction
	}
	return nil
}

func (x *Account) GetTotal() *Total {
	if x != nil {
		return x.Total
	}
	return nil
}

func (x *Account) GetLimit() *Limit {
	if x != nil {
		return x.Limit
	}
	return nil
}

func (x *Account) GetTransactions() []*Transaction {
	if x != nil {
		return x.Transactions
	}
	return nil
}

type Period struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StartDate     string                 `protobuf:"bytes,1,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	EndDate       string                 `protobuf:"bytes,2,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Period) Reset() {
	*x = Period{}
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Period) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Period) ProtoMessage() {}

func (x *Period) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Period.ProtoReflect.Descriptor instead.
func (*Period) Descriptor() ([]byte, []int) {
	return file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDescGZIP(), []int{7}
}

func (x *Period) GetStartDate() string {
	if x != nil {
		return x.StartDate
	}
	return ""
}

func (x *Period) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

type Balance struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Before        *float64               `protobuf:"fixed64,1,opt,name=before,proto3,oneof" json:"before,omitempty"`
	After         *float64               `protobuf:"fixed64,2,opt,name=after,proto3,oneof" json:"after,omitempty"`
	Processing    *float64               `protobuf:"fixed64,3,opt,name=processing,proto3,oneof" json:"processing,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Balance) Reset() {
	*x = Balance{}
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Balance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Balance) ProtoMessage() {}

func (x *Balance) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Balance.ProtoReflect.Descriptor instead.
func (*Balance) Descriptor() ([]byte, []int) {
	return file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDescGZIP(), []int{8}
}

func (x *Balance) GetBefore() float64 {
	if x != nil && x.Before != nil {
		return *x.Before
	}
	return 0
}

func (x *Balance) GetAfter() float64 {
	if x != nil && x.After != nil {
		return *x.After
	}
	return 0
}

func (x *Balance) GetProcessing() float64 {
	if x != nil && x.Processing != nil {
		return *x.Processing
	}
	return 0
}

type TransactionSummary struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Replenishment   *float64               `protobuf:"fixed64,1,opt,name=replenishment,proto3,oneof" json:"replenishment,omitempty"`
	Transfers       *float64               `protobuf:"fixed64,2,opt,name=transfers,proto3,oneof" json:"transfers,omitempty"`
	Purchases       *float64               `protobuf:"fixed64,3,opt,name=purchases,proto3,oneof" json:"purchases,omitempty"`
	Withdrawals     *float64               `protobuf:"fixed64,4,opt,name=withdrawals,proto3,oneof" json:"withdrawals,omitempty"`
	Other           *float64               `protobuf:"fixed64,5,opt,name=other,proto3,oneof" json:"other,omitempty"`
	Payment         *float64               `protobuf:"fixed64,6,opt,name=payment,proto3,oneof" json:"payment,omitempty"`
	LoanPayment     *float64               `protobuf:"fixed64,7,opt,name=loan_payment,json=loanPayment,proto3,oneof" json:"loan_payment,omitempty"`
	TotalReceipt    *float64               `protobuf:"fixed64,8,opt,name=total_receipt,json=totalReceipt,proto3,oneof" json:"total_receipt,omitempty"`
	TotalExpense    *float64               `protobuf:"fixed64,9,opt,name=total_expense,json=totalExpense,proto3,oneof" json:"total_expense,omitempty"`
	TotalCommission *float64               `protobuf:"fixed64,10,opt,name=total_commission,json=totalCommission,proto3,oneof" json:"total_commission,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *TransactionSummary) Reset() {
	*x = TransactionSummary{}
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionSummary) ProtoMessage() {}

func (x *TransactionSummary) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionSummary.ProtoReflect.Descriptor instead.
func (*TransactionSummary) Descriptor() ([]byte, []int) {
	return file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDescGZIP(), []int{9}
}

func (x *TransactionSummary) GetReplenishment() float64 {
	if x != nil && x.Replenishment != nil {
		return *x.Replenishment
	}
	return 0
}

func (x *TransactionSummary) GetTransfers() float64 {
	if x != nil && x.Transfers != nil {
		return *x.Transfers
	}
	return 0
}

func (x *TransactionSummary) GetPurchases() float64 {
	if x != nil && x.Purchases != nil {
		return *x.Purchases
	}
	return 0
}

func (x *TransactionSummary) GetWithdrawals() float64 {
	if x != nil && x.Withdrawals != nil {
		return *x.Withdrawals
	}
	return 0
}

func (x *TransactionSummary) GetOther() float64 {
	if x != nil && x.Other != nil {
		return *x.Other
	}
	return 0
}

func (x *TransactionSummary) GetPayment() float64 {
	if x != nil && x.Payment != nil {
		return *x.Payment
	}
	return 0
}

func (x *TransactionSummary) GetLoanPayment() float64 {
	if x != nil && x.LoanPayment != nil {
		return *x.LoanPayment
	}
	return 0
}

func (x *TransactionSummary) GetTotalReceipt() float64 {
	if x != nil && x.TotalReceipt != nil {
		return *x.TotalReceipt
	}
	return 0
}

func (x *TransactionSummary) GetTotalExpense() float64 {
	if x != nil && x.TotalExpense != nil {
		return *x.TotalExpense
	}
	return 0
}

func (x *TransactionSummary) GetTotalCommission() float64 {
	if x != nil && x.TotalCommission != nil {
		return *x.TotalCommission
	}
	return 0
}

type Total struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Debit         *float64               `protobuf:"fixed64,1,opt,name=debit,proto3,oneof" json:"debit,omitempty"`
	Credit        *float64               `protobuf:"fixed64,2,opt,name=credit,proto3,oneof" json:"credit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Total) Reset() {
	*x = Total{}
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Total) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Total) ProtoMessage() {}

func (x *Total) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Total.ProtoReflect.Descriptor instead.
func (*Total) Descriptor() ([]byte, []int) {
	return file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDescGZIP(), []int{10}
}

func (x *Total) GetDebit() float64 {
	if x != nil && x.Debit != nil {
		return *x.Debit
	}
	return 0
}

func (x *Total) GetCredit() float64 {
	if x != nil && x.Credit != nil {
		return *x.Credit
	}
	return 0
}

type Limit struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	RemainingSalary *float64               `protobuf:"fixed64,1,opt,name=remaining_salary,json=remainingSalary,proto3,oneof" json:"remaining_salary,omitempty"`
	Transfers       *float64               `protobuf:"fixed64,2,opt,name=transfers,proto3,oneof" json:"transfers,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Limit) Reset() {
	*x = Limit{}
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Limit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Limit) ProtoMessage() {}

func (x *Limit) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Limit.ProtoReflect.Descriptor instead.
func (*Limit) Descriptor() ([]byte, []int) {
	return file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDescGZIP(), []int{11}
}

func (x *Limit) GetRemainingSalary() float64 {
	if x != nil && x.RemainingSalary != nil {
		return *x.RemainingSalary
	}
	return 0
}

func (x *Limit) GetTransfers() float64 {
	if x != nil && x.Transfers != nil {
		return *x.Transfers
	}
	return 0
}

type Transaction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Date          string                 `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	CompletedDate *string                `protobuf:"bytes,2,opt,name=completed_date,json=completedDate,proto3,oneof" json:"completed_date,omitempty"`
	Amount        *float64               `protobuf:"fixed64,3,opt,name=amount,proto3,oneof" json:"amount,omitempty"`
	Currency      *string                `protobuf:"bytes,4,opt,name=currency,proto3,oneof" json:"currency,omitempty"`
	Type          *string                `protobuf:"bytes,5,opt,name=type,proto3,oneof" json:"type,omitempty"`
	Detail        string                 `protobuf:"bytes,6,opt,name=detail,proto3" json:"detail,omitempty"`
	Commission    *float64               `protobuf:"fixed64,7,opt,name=commission,proto3,oneof" json:"commission,omitempty"`
	IsProcessing  *bool                  `protobuf:"varint,8,opt,name=is_processing,json=isProcessing,proto3,oneof" json:"is_processing,omitempty"`
	DocNumber     *string                `protobuf:"bytes,9,opt,name=doc_number,json=docNumber,proto3,oneof" json:"doc_number,omitempty"`
	Debit         *float64               `protobuf:"fixed64,10,opt,name=debit,proto3,oneof" json:"debit,omitempty"`
	Credit        *float64               `protobuf:"fixed64,11,opt,name=credit,proto3,oneof" json:"credit,omitempty"`
	AccountNumber *string                `protobuf:"bytes,12,opt,name=account_number,json=accountNumber,proto3,oneof" json:"account_number,omitempty"`
	To            *To                    `protobuf:"bytes,13,opt,name=To,proto3,oneof" json:"To,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Transaction) Reset() {
	*x = Transaction{}
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Transaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Transaction) ProtoMessage() {}

func (x *Transaction) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Transaction.ProtoReflect.Descriptor instead.
func (*Transaction) Descriptor() ([]byte, []int) {
	return file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDescGZIP(), []int{12}
}

func (x *Transaction) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *Transaction) GetCompletedDate() string {
	if x != nil && x.CompletedDate != nil {
		return *x.CompletedDate
	}
	return ""
}

func (x *Transaction) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *Transaction) GetCurrency() string {
	if x != nil && x.Currency != nil {
		return *x.Currency
	}
	return ""
}

func (x *Transaction) GetType() string {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ""
}

func (x *Transaction) GetDetail() string {
	if x != nil {
		return x.Detail
	}
	return ""
}

func (x *Transaction) GetCommission() float64 {
	if x != nil && x.Commission != nil {
		return *x.Commission
	}
	return 0
}

func (x *Transaction) GetIsProcessing() bool {
	if x != nil && x.IsProcessing != nil {
		return *x.IsProcessing
	}
	return false
}

func (x *Transaction) GetDocNumber() string {
	if x != nil && x.DocNumber != nil {
		return *x.DocNumber
	}
	return ""
}

func (x *Transaction) GetDebit() float64 {
	if x != nil && x.Debit != nil {
		return *x.Debit
	}
	return 0
}

func (x *Transaction) GetCredit() float64 {
	if x != nil && x.Credit != nil {
		return *x.Credit
	}
	return 0
}

func (x *Transaction) GetAccountNumber() string {
	if x != nil && x.AccountNumber != nil {
		return *x.AccountNumber
	}
	return ""
}

func (x *Transaction) GetTo() *To {
	if x != nil {
		return x.To
	}
	return nil
}

type To struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Title         string                 `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Bik           *string                `protobuf:"bytes,2,opt,name=bik,proto3,oneof" json:"bik,omitempty"`
	Knp           *string                `protobuf:"bytes,3,opt,name=knp,proto3,oneof" json:"knp,omitempty"`
	Bin           string                 `protobuf:"bytes,4,opt,name=bin,proto3" json:"bin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *To) Reset() {
	*x = To{}
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *To) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*To) ProtoMessage() {}

func (x *To) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use To.ProtoReflect.Descriptor instead.
func (*To) Descriptor() ([]byte, []int) {
	return file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDescGZIP(), []int{13}
}

func (x *To) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *To) GetBik() string {
	if x != nil && x.Bik != nil {
		return *x.Bik
	}
	return ""
}

func (x *To) GetKnp() string {
	if x != nil && x.Knp != nil {
		return *x.Knp
	}
	return ""
}

func (x *To) GetBin() string {
	if x != nil {
		return x.Bin
	}
	return ""
}

type ErrorDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ErrorDetail) Reset() {
	*x = ErrorDetail{}
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ErrorDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorDetail) ProtoMessage() {}

func (x *ErrorDetail) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorDetail.ProtoReflect.Descriptor instead.
func (*ErrorDetail) Descriptor() ([]byte, []int) {
	return file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDescGZIP(), []int{14}
}

func (x *ErrorDetail) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ErrorDetail) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_specs_proto_alt_score_bridge_alt_score_bridge_proto protoreflect.FileDescriptor

const file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDesc = "" +
	"\n" +
	"3specs/proto/alt-score-bridge/alt-score-bridge.proto\x12\x0ealtScoreBridge\"\x10\n" +
	"\x0eHealthCheckReq\")\n" +
	"\x0fHealthCheckResp\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\"\xee\x01\n" +
	"\x19ParseBankStatementRequest\x12$\n" +
	"\rbankStatement\x18\x01 \x01(\fR\rbankStatement\x12\x1a\n" +
	"\bbankName\x18\x02 \x01(\tR\bbankName\x12$\n" +
	"\x0ealt_score_uuid\x18\x03 \x01(\tR\faltScoreUuid\x12Y\n" +
	"\vclient_info\x18\x04 \x01(\v23.altScoreBridge.ParseBankStatementRequestClientInfoH\x00R\n" +
	"clientInfo\x88\x01\x01B\x0e\n" +
	"\f_client_info\"\xd0\x01\n" +
	"#ParseBankStatementRequestClientInfo\x12\x10\n" +
	"\x03iin\x18\x01 \x01(\tR\x03iin\x12\x1b\n" +
	"\tfull_name\x18\x02 \x01(\tR\bfullName\x12\x1b\n" +
	"\tuser_type\x18\x03 \x01(\tR\buserType\x12%\n" +
	"\x0eapplication_id\x18\x04 \x01(\x04R\rapplicationId\x12\x1b\n" +
	"\tclient_id\x18\x05 \x01(\x04R\bclientId\x12\x19\n" +
	"\btrack_id\x18\x06 \x01(\x04R\atrackId\"\xd6\x02\n" +
	"\x1aParseBankStatementResponse\x12%\n" +
	"\x0eis_discrepancy\x18\x01 \x01(\bR\risDiscrepancy\x12;\n" +
	"\vuser_detail\x18\x02 \x01(\v2\x1a.altScoreBridge.UserDetailR\n" +
	"userDetail\x123\n" +
	"\baccounts\x18\x03 \x03(\v2\x17.altScoreBridge.AccountR\baccounts\x12\x1f\n" +
	"\valtScore_id\x18\x04 \x01(\x03R\n" +
	"altScoreId\x12)\n" +
	"\x0erequest_log_id\x18\x05 \x01(\tH\x00R\frequestLogId\x88\x01\x01\x126\n" +
	"\x05error\x18\x06 \x01(\v2\x1b.altScoreBridge.ErrorDetailH\x01R\x05error\x88\x01\x01B\x11\n" +
	"\x0f_request_log_idB\b\n" +
	"\x06_error\"\xea\x01\n" +
	"\n" +
	"UserDetail\x12\x1b\n" +
	"\tfull_name\x18\x01 \x01(\tR\bfullName\x12\x15\n" +
	"\x03iin\x18\x02 \x01(\tH\x00R\x03iin\x88\x01\x01\x12\x15\n" +
	"\x03bin\x18\x03 \x01(\tH\x01R\x03bin\x88\x01\x01\x12\x1b\n" +
	"\tbank_name\x18\x04 \x01(\tR\bbankName\x12$\n" +
	"\vcard_number\x18\x05 \x01(\tH\x02R\n" +
	"cardNumber\x88\x01\x01\x12.\n" +
	"\x06period\x18\x06 \x01(\v2\x16.altScoreBridge.PeriodR\x06periodB\x06\n" +
	"\x04_iinB\x06\n" +
	"\x04_binB\x0e\n" +
	"\f_card_number\"\x93\x03\n" +
	"\aAccount\x12%\n" +
	"\x0eaccount_number\x18\x01 \x01(\tR\raccountNumber\x12\x1a\n" +
	"\bcurrency\x18\x02 \x01(\tR\bcurrency\x121\n" +
	"\abalance\x18\x03 \x01(\v2\x17.altScoreBridge.BalanceR\abalance\x12I\n" +
	"\vtransaction\x18\x04 \x01(\v2\".altScoreBridge.TransactionSummaryH\x00R\vtransaction\x88\x01\x01\x120\n" +
	"\x05total\x18\x05 \x01(\v2\x15.altScoreBridge.TotalH\x01R\x05total\x88\x01\x01\x120\n" +
	"\x05limit\x18\x06 \x01(\v2\x15.altScoreBridge.LimitH\x02R\x05limit\x88\x01\x01\x12?\n" +
	"\ftransactions\x18\a \x03(\v2\x1b.altScoreBridge.TransactionR\ftransactionsB\x0e\n" +
	"\f_transactionB\b\n" +
	"\x06_totalB\b\n" +
	"\x06_limit\"B\n" +
	"\x06Period\x12\x1d\n" +
	"\n" +
	"start_date\x18\x01 \x01(\tR\tstartDate\x12\x19\n" +
	"\bend_date\x18\x02 \x01(\tR\aendDate\"\x8a\x01\n" +
	"\aBalance\x12\x1b\n" +
	"\x06before\x18\x01 \x01(\x01H\x00R\x06before\x88\x01\x01\x12\x19\n" +
	"\x05after\x18\x02 \x01(\x01H\x01R\x05after\x88\x01\x01\x12#\n" +
	"\n" +
	"processing\x18\x03 \x01(\x01H\x02R\n" +
	"processing\x88\x01\x01B\t\n" +
	"\a_beforeB\b\n" +
	"\x06_afterB\r\n" +
	"\v_processing\"\xb0\x04\n" +
	"\x12TransactionSummary\x12)\n" +
	"\rreplenishment\x18\x01 \x01(\x01H\x00R\rreplenishment\x88\x01\x01\x12!\n" +
	"\ttransfers\x18\x02 \x01(\x01H\x01R\ttransfers\x88\x01\x01\x12!\n" +
	"\tpurchases\x18\x03 \x01(\x01H\x02R\tpurchases\x88\x01\x01\x12%\n" +
	"\vwithdrawals\x18\x04 \x01(\x01H\x03R\vwithdrawals\x88\x01\x01\x12\x19\n" +
	"\x05other\x18\x05 \x01(\x01H\x04R\x05other\x88\x01\x01\x12\x1d\n" +
	"\apayment\x18\x06 \x01(\x01H\x05R\apayment\x88\x01\x01\x12&\n" +
	"\floan_payment\x18\a \x01(\x01H\x06R\vloanPayment\x88\x01\x01\x12(\n" +
	"\rtotal_receipt\x18\b \x01(\x01H\aR\ftotalReceipt\x88\x01\x01\x12(\n" +
	"\rtotal_expense\x18\t \x01(\x01H\bR\ftotalExpense\x88\x01\x01\x12.\n" +
	"\x10total_commission\x18\n" +
	" \x01(\x01H\tR\x0ftotalCommission\x88\x01\x01B\x10\n" +
	"\x0e_replenishmentB\f\n" +
	"\n" +
	"_transfersB\f\n" +
	"\n" +
	"_purchasesB\x0e\n" +
	"\f_withdrawalsB\b\n" +
	"\x06_otherB\n" +
	"\n" +
	"\b_paymentB\x0f\n" +
	"\r_loan_paymentB\x10\n" +
	"\x0e_total_receiptB\x10\n" +
	"\x0e_total_expenseB\x13\n" +
	"\x11_total_commission\"T\n" +
	"\x05Total\x12\x19\n" +
	"\x05debit\x18\x01 \x01(\x01H\x00R\x05debit\x88\x01\x01\x12\x1b\n" +
	"\x06credit\x18\x02 \x01(\x01H\x01R\x06credit\x88\x01\x01B\b\n" +
	"\x06_debitB\t\n" +
	"\a_credit\"}\n" +
	"\x05Limit\x12.\n" +
	"\x10remaining_salary\x18\x01 \x01(\x01H\x00R\x0fremainingSalary\x88\x01\x01\x12!\n" +
	"\ttransfers\x18\x02 \x01(\x01H\x01R\ttransfers\x88\x01\x01B\x13\n" +
	"\x11_remaining_salaryB\f\n" +
	"\n" +
	"_transfers\"\xcf\x04\n" +
	"\vTransaction\x12\x12\n" +
	"\x04date\x18\x01 \x01(\tR\x04date\x12*\n" +
	"\x0ecompleted_date\x18\x02 \x01(\tH\x00R\rcompletedDate\x88\x01\x01\x12\x1b\n" +
	"\x06amount\x18\x03 \x01(\x01H\x01R\x06amount\x88\x01\x01\x12\x1f\n" +
	"\bcurrency\x18\x04 \x01(\tH\x02R\bcurrency\x88\x01\x01\x12\x17\n" +
	"\x04type\x18\x05 \x01(\tH\x03R\x04type\x88\x01\x01\x12\x16\n" +
	"\x06detail\x18\x06 \x01(\tR\x06detail\x12#\n" +
	"\n" +
	"commission\x18\a \x01(\x01H\x04R\n" +
	"commission\x88\x01\x01\x12(\n" +
	"\ris_processing\x18\b \x01(\bH\x05R\fisProcessing\x88\x01\x01\x12\"\n" +
	"\n" +
	"doc_number\x18\t \x01(\tH\x06R\tdocNumber\x88\x01\x01\x12\x19\n" +
	"\x05debit\x18\n" +
	" \x01(\x01H\aR\x05debit\x88\x01\x01\x12\x1b\n" +
	"\x06credit\x18\v \x01(\x01H\bR\x06credit\x88\x01\x01\x12*\n" +
	"\x0eaccount_number\x18\f \x01(\tH\tR\raccountNumber\x88\x01\x01\x12'\n" +
	"\x02To\x18\r \x01(\v2\x12.altScoreBridge.ToH\n" +
	"R\x02To\x88\x01\x01B\x11\n" +
	"\x0f_completed_dateB\t\n" +
	"\a_amountB\v\n" +
	"\t_currencyB\a\n" +
	"\x05_typeB\r\n" +
	"\v_commissionB\x10\n" +
	"\x0e_is_processingB\r\n" +
	"\v_doc_numberB\b\n" +
	"\x06_debitB\t\n" +
	"\a_creditB\x11\n" +
	"\x0f_account_numberB\x05\n" +
	"\x03_To\"j\n" +
	"\x02To\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12\x15\n" +
	"\x03bik\x18\x02 \x01(\tH\x00R\x03bik\x88\x01\x01\x12\x15\n" +
	"\x03knp\x18\x03 \x01(\tH\x01R\x03knp\x88\x01\x01\x12\x10\n" +
	"\x03bin\x18\x04 \x01(\tR\x03binB\x06\n" +
	"\x04_bikB\x06\n" +
	"\x04_knp\";\n" +
	"\vErrorDetail\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage2\xcd\x01\n" +
	"\x0eAltscorebridge\x12N\n" +
	"\vHealthCheck\x12\x1e.altScoreBridge.HealthCheckReq\x1a\x1f.altScoreBridge.HealthCheckResp\x12k\n" +
	"\x12ParseBankStatement\x12).altScoreBridge.ParseBankStatementRequest\x1a*.altScoreBridge.ParseBankStatementResponseB\x1eZ\x1cspecs/proto/alt-score-bridgeb\x06proto3"

var (
	file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDescOnce sync.Once
	file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDescData []byte
)

func file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDescGZIP() []byte {
	file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDescOnce.Do(func() {
		file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDesc), len(file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDesc)))
	})
	return file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDescData
}

var file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_specs_proto_alt_score_bridge_alt_score_bridge_proto_goTypes = []any{
	(*HealthCheckReq)(nil),                      // 0: altScoreBridge.HealthCheckReq
	(*HealthCheckResp)(nil),                     // 1: altScoreBridge.HealthCheckResp
	(*ParseBankStatementRequest)(nil),           // 2: altScoreBridge.ParseBankStatementRequest
	(*ParseBankStatementRequestClientInfo)(nil), // 3: altScoreBridge.ParseBankStatementRequestClientInfo
	(*ParseBankStatementResponse)(nil),          // 4: altScoreBridge.ParseBankStatementResponse
	(*UserDetail)(nil),                          // 5: altScoreBridge.UserDetail
	(*Account)(nil),                             // 6: altScoreBridge.Account
	(*Period)(nil),                              // 7: altScoreBridge.Period
	(*Balance)(nil),                             // 8: altScoreBridge.Balance
	(*TransactionSummary)(nil),                  // 9: altScoreBridge.TransactionSummary
	(*Total)(nil),                               // 10: altScoreBridge.Total
	(*Limit)(nil),                               // 11: altScoreBridge.Limit
	(*Transaction)(nil),                         // 12: altScoreBridge.Transaction
	(*To)(nil),                                  // 13: altScoreBridge.To
	(*ErrorDetail)(nil),                         // 14: altScoreBridge.ErrorDetail
}
var file_specs_proto_alt_score_bridge_alt_score_bridge_proto_depIdxs = []int32{
	3,  // 0: altScoreBridge.ParseBankStatementRequest.client_info:type_name -> altScoreBridge.ParseBankStatementRequestClientInfo
	5,  // 1: altScoreBridge.ParseBankStatementResponse.user_detail:type_name -> altScoreBridge.UserDetail
	6,  // 2: altScoreBridge.ParseBankStatementResponse.accounts:type_name -> altScoreBridge.Account
	14, // 3: altScoreBridge.ParseBankStatementResponse.error:type_name -> altScoreBridge.ErrorDetail
	7,  // 4: altScoreBridge.UserDetail.period:type_name -> altScoreBridge.Period
	8,  // 5: altScoreBridge.Account.balance:type_name -> altScoreBridge.Balance
	9,  // 6: altScoreBridge.Account.transaction:type_name -> altScoreBridge.TransactionSummary
	10, // 7: altScoreBridge.Account.total:type_name -> altScoreBridge.Total
	11, // 8: altScoreBridge.Account.limit:type_name -> altScoreBridge.Limit
	12, // 9: altScoreBridge.Account.transactions:type_name -> altScoreBridge.Transaction
	13, // 10: altScoreBridge.Transaction.To:type_name -> altScoreBridge.To
	0,  // 11: altScoreBridge.Altscorebridge.HealthCheck:input_type -> altScoreBridge.HealthCheckReq
	2,  // 12: altScoreBridge.Altscorebridge.ParseBankStatement:input_type -> altScoreBridge.ParseBankStatementRequest
	1,  // 13: altScoreBridge.Altscorebridge.HealthCheck:output_type -> altScoreBridge.HealthCheckResp
	4,  // 14: altScoreBridge.Altscorebridge.ParseBankStatement:output_type -> altScoreBridge.ParseBankStatementResponse
	13, // [13:15] is the sub-list for method output_type
	11, // [11:13] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_specs_proto_alt_score_bridge_alt_score_bridge_proto_init() }
func file_specs_proto_alt_score_bridge_alt_score_bridge_proto_init() {
	if File_specs_proto_alt_score_bridge_alt_score_bridge_proto != nil {
		return
	}
	file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[2].OneofWrappers = []any{}
	file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[4].OneofWrappers = []any{}
	file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[5].OneofWrappers = []any{}
	file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[6].OneofWrappers = []any{}
	file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[8].OneofWrappers = []any{}
	file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[9].OneofWrappers = []any{}
	file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[10].OneofWrappers = []any{}
	file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[11].OneofWrappers = []any{}
	file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[12].OneofWrappers = []any{}
	file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes[13].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDesc), len(file_specs_proto_alt_score_bridge_alt_score_bridge_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_specs_proto_alt_score_bridge_alt_score_bridge_proto_goTypes,
		DependencyIndexes: file_specs_proto_alt_score_bridge_alt_score_bridge_proto_depIdxs,
		MessageInfos:      file_specs_proto_alt_score_bridge_alt_score_bridge_proto_msgTypes,
	}.Build()
	File_specs_proto_alt_score_bridge_alt_score_bridge_proto = out.File
	file_specs_proto_alt_score_bridge_alt_score_bridge_proto_goTypes = nil
	file_specs_proto_alt_score_bridge_alt_score_bridge_proto_depIdxs = nil
}
