// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.32.1
// source: specs/proto/alt-score-bridge/alt-score-bridge.proto

package alt_score_bridge

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Altscorebridge_HealthCheck_FullMethodName        = "/altScoreBridge.Altscorebridge/HealthCheck"
	Altscorebridge_ParseBankStatement_FullMethodName = "/altScoreBridge.Altscorebridge/ParseBankStatement"
)

// AltscorebridgeClient is the client API for Altscorebridge service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AltscorebridgeClient interface {
	HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error)
	ParseBankStatement(ctx context.Context, in *ParseBankStatementRequest, opts ...grpc.CallOption) (*ParseBankStatementResponse, error)
}

type altscorebridgeClient struct {
	cc grpc.ClientConnInterface
}

func NewAltscorebridgeClient(cc grpc.ClientConnInterface) AltscorebridgeClient {
	return &altscorebridgeClient{cc}
}

func (c *altscorebridgeClient) HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResp)
	err := c.cc.Invoke(ctx, Altscorebridge_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *altscorebridgeClient) ParseBankStatement(ctx context.Context, in *ParseBankStatementRequest, opts ...grpc.CallOption) (*ParseBankStatementResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ParseBankStatementResponse)
	err := c.cc.Invoke(ctx, Altscorebridge_ParseBankStatement_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AltscorebridgeServer is the server API for Altscorebridge service.
// All implementations must embed UnimplementedAltscorebridgeServer
// for forward compatibility.
type AltscorebridgeServer interface {
	HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error)
	ParseBankStatement(context.Context, *ParseBankStatementRequest) (*ParseBankStatementResponse, error)
	mustEmbedUnimplementedAltscorebridgeServer()
}

// UnimplementedAltscorebridgeServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAltscorebridgeServer struct{}

func (UnimplementedAltscorebridgeServer) HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedAltscorebridgeServer) ParseBankStatement(context.Context, *ParseBankStatementRequest) (*ParseBankStatementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ParseBankStatement not implemented")
}
func (UnimplementedAltscorebridgeServer) mustEmbedUnimplementedAltscorebridgeServer() {}
func (UnimplementedAltscorebridgeServer) testEmbeddedByValue()                        {}

// UnsafeAltscorebridgeServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AltscorebridgeServer will
// result in compilation errors.
type UnsafeAltscorebridgeServer interface {
	mustEmbedUnimplementedAltscorebridgeServer()
}

func RegisterAltscorebridgeServer(s grpc.ServiceRegistrar, srv AltscorebridgeServer) {
	// If the following call pancis, it indicates UnimplementedAltscorebridgeServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Altscorebridge_ServiceDesc, srv)
}

func _Altscorebridge_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AltscorebridgeServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Altscorebridge_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AltscorebridgeServer).HealthCheck(ctx, req.(*HealthCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Altscorebridge_ParseBankStatement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ParseBankStatementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AltscorebridgeServer).ParseBankStatement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Altscorebridge_ParseBankStatement_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AltscorebridgeServer).ParseBankStatement(ctx, req.(*ParseBankStatementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Altscorebridge_ServiceDesc is the grpc.ServiceDesc for Altscorebridge service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Altscorebridge_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "altScoreBridge.Altscorebridge",
	HandlerType: (*AltscorebridgeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HealthCheck",
			Handler:    _Altscorebridge_HealthCheck_Handler,
		},
		{
			MethodName: "ParseBankStatement",
			Handler:    _Altscorebridge_ParseBankStatement_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "specs/proto/alt-score-bridge/alt-score-bridge.proto",
}
