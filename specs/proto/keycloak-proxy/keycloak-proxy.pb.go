// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v6.32.1
// source: specs/proto/keycloak-proxy/keycloak-proxy.proto

package keycloak_proxy

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HealthCheckReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckReq) Reset() {
	*x = HealthCheckReq{}
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckReq) ProtoMessage() {}

func (x *HealthCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckReq.ProtoReflect.Descriptor instead.
func (*HealthCheckReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescGZIP(), []int{0}
}

type HealthCheckResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckResp) Reset() {
	*x = HealthCheckResp{}
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResp) ProtoMessage() {}

func (x *HealthCheckResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResp.ProtoReflect.Descriptor instead.
func (*HealthCheckResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescGZIP(), []int{1}
}

func (x *HealthCheckResp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type CreateUserReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Phone         string                 `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserReq) Reset() {
	*x = CreateUserReq{}
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserReq) ProtoMessage() {}

func (x *CreateUserReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserReq.ProtoReflect.Descriptor instead.
func (*CreateUserReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescGZIP(), []int{2}
}

func (x *CreateUserReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

type CreateUserResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Succeed       bool                   `protobuf:"varint,1,opt,name=succeed,proto3" json:"succeed,omitempty"`
	UserID        string                 `protobuf:"bytes,2,opt,name=userID,proto3" json:"userID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserResp) Reset() {
	*x = CreateUserResp{}
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserResp) ProtoMessage() {}

func (x *CreateUserResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserResp.ProtoReflect.Descriptor instead.
func (*CreateUserResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescGZIP(), []int{3}
}

func (x *CreateUserResp) GetSucceed() bool {
	if x != nil {
		return x.Succeed
	}
	return false
}

func (x *CreateUserResp) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

type ActivateUserReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Phone         string                 `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	UserID        string                 `protobuf:"bytes,2,opt,name=userID,proto3" json:"userID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivateUserReq) Reset() {
	*x = ActivateUserReq{}
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivateUserReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivateUserReq) ProtoMessage() {}

func (x *ActivateUserReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivateUserReq.ProtoReflect.Descriptor instead.
func (*ActivateUserReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescGZIP(), []int{4}
}

func (x *ActivateUserReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *ActivateUserReq) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

type ActivateUserResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Succeed       bool                   `protobuf:"varint,1,opt,name=succeed,proto3" json:"succeed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivateUserResp) Reset() {
	*x = ActivateUserResp{}
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivateUserResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivateUserResp) ProtoMessage() {}

func (x *ActivateUserResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivateUserResp.ProtoReflect.Descriptor instead.
func (*ActivateUserResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescGZIP(), []int{5}
}

func (x *ActivateUserResp) GetSucceed() bool {
	if x != nil {
		return x.Succeed
	}
	return false
}

type BlockUserReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Phone         string                 `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	UserID        string                 `protobuf:"bytes,2,opt,name=userID,proto3" json:"userID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockUserReq) Reset() {
	*x = BlockUserReq{}
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockUserReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockUserReq) ProtoMessage() {}

func (x *BlockUserReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockUserReq.ProtoReflect.Descriptor instead.
func (*BlockUserReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescGZIP(), []int{6}
}

func (x *BlockUserReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *BlockUserReq) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

type BlockUserResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Succeed       bool                   `protobuf:"varint,1,opt,name=succeed,proto3" json:"succeed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockUserResp) Reset() {
	*x = BlockUserResp{}
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockUserResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockUserResp) ProtoMessage() {}

func (x *BlockUserResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockUserResp.ProtoReflect.Descriptor instead.
func (*BlockUserResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescGZIP(), []int{7}
}

func (x *BlockUserResp) GetSucceed() bool {
	if x != nil {
		return x.Succeed
	}
	return false
}

type GetTokensReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Phone         string                 `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTokensReq) Reset() {
	*x = GetTokensReq{}
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTokensReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTokensReq) ProtoMessage() {}

func (x *GetTokensReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTokensReq.ProtoReflect.Descriptor instead.
func (*GetTokensReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescGZIP(), []int{8}
}

func (x *GetTokensReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

type GetTokensResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccessToken   string                 `protobuf:"bytes,1,opt,name=accessToken,proto3" json:"accessToken,omitempty"`
	RefreshToken  string                 `protobuf:"bytes,2,opt,name=refreshToken,proto3" json:"refreshToken,omitempty"`
	SessionID     string                 `protobuf:"bytes,3,opt,name=sessionID,proto3" json:"sessionID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTokensResp) Reset() {
	*x = GetTokensResp{}
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTokensResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTokensResp) ProtoMessage() {}

func (x *GetTokensResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTokensResp.ProtoReflect.Descriptor instead.
func (*GetTokensResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescGZIP(), []int{9}
}

func (x *GetTokensResp) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *GetTokensResp) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

func (x *GetTokensResp) GetSessionID() string {
	if x != nil {
		return x.SessionID
	}
	return ""
}

type RefreshTokensReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RefreshToken  string                 `protobuf:"bytes,1,opt,name=refreshToken,proto3" json:"refreshToken,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefreshTokensReq) Reset() {
	*x = RefreshTokensReq{}
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefreshTokensReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokensReq) ProtoMessage() {}

func (x *RefreshTokensReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokensReq.ProtoReflect.Descriptor instead.
func (*RefreshTokensReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescGZIP(), []int{10}
}

func (x *RefreshTokensReq) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

type RefreshTokensResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccessToken   string                 `protobuf:"bytes,1,opt,name=accessToken,proto3" json:"accessToken,omitempty"`
	RefreshToken  string                 `protobuf:"bytes,2,opt,name=refreshToken,proto3" json:"refreshToken,omitempty"`
	SessionID     string                 `protobuf:"bytes,3,opt,name=sessionID,proto3" json:"sessionID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefreshTokensResp) Reset() {
	*x = RefreshTokensResp{}
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefreshTokensResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokensResp) ProtoMessage() {}

func (x *RefreshTokensResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokensResp.ProtoReflect.Descriptor instead.
func (*RefreshTokensResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescGZIP(), []int{11}
}

func (x *RefreshTokensResp) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *RefreshTokensResp) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

func (x *RefreshTokensResp) GetSessionID() string {
	if x != nil {
		return x.SessionID
	}
	return ""
}

type DeleteSessionsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserID        string                 `protobuf:"bytes,1,opt,name=userID,proto3" json:"userID,omitempty"`
	SessionIDs    []string               `protobuf:"bytes,2,rep,name=sessionIDs,proto3" json:"sessionIDs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteSessionsReq) Reset() {
	*x = DeleteSessionsReq{}
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteSessionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSessionsReq) ProtoMessage() {}

func (x *DeleteSessionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSessionsReq.ProtoReflect.Descriptor instead.
func (*DeleteSessionsReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteSessionsReq) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

func (x *DeleteSessionsReq) GetSessionIDs() []string {
	if x != nil {
		return x.SessionIDs
	}
	return nil
}

type DeleteSessionsResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Succeed       bool                   `protobuf:"varint,1,opt,name=succeed,proto3" json:"succeed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteSessionsResp) Reset() {
	*x = DeleteSessionsResp{}
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteSessionsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSessionsResp) ProtoMessage() {}

func (x *DeleteSessionsResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSessionsResp.ProtoReflect.Descriptor instead.
func (*DeleteSessionsResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescGZIP(), []int{13}
}

func (x *DeleteSessionsResp) GetSucceed() bool {
	if x != nil {
		return x.Succeed
	}
	return false
}

type SetUserRoleReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserID        string                 `protobuf:"bytes,1,opt,name=userID,proto3" json:"userID,omitempty"`
	Role          string                 `protobuf:"bytes,2,opt,name=role,proto3" json:"role,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetUserRoleReq) Reset() {
	*x = SetUserRoleReq{}
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetUserRoleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetUserRoleReq) ProtoMessage() {}

func (x *SetUserRoleReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetUserRoleReq.ProtoReflect.Descriptor instead.
func (*SetUserRoleReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescGZIP(), []int{14}
}

func (x *SetUserRoleReq) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

func (x *SetUserRoleReq) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

type SetUserRoleResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Succeed       bool                   `protobuf:"varint,1,opt,name=succeed,proto3" json:"succeed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetUserRoleResp) Reset() {
	*x = SetUserRoleResp{}
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetUserRoleResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetUserRoleResp) ProtoMessage() {}

func (x *SetUserRoleResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetUserRoleResp.ProtoReflect.Descriptor instead.
func (*SetUserRoleResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescGZIP(), []int{15}
}

func (x *SetUserRoleResp) GetSucceed() bool {
	if x != nil {
		return x.Succeed
	}
	return false
}

type RevokeRefreshTokenReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RevokeRefreshTokenReq) Reset() {
	*x = RevokeRefreshTokenReq{}
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RevokeRefreshTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeRefreshTokenReq) ProtoMessage() {}

func (x *RevokeRefreshTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeRefreshTokenReq.ProtoReflect.Descriptor instead.
func (*RevokeRefreshTokenReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescGZIP(), []int{16}
}

func (x *RevokeRefreshTokenReq) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type RevokeRefreshTokenResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Succeed       bool                   `protobuf:"varint,1,opt,name=succeed,proto3" json:"succeed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RevokeRefreshTokenResp) Reset() {
	*x = RevokeRefreshTokenResp{}
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RevokeRefreshTokenResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeRefreshTokenResp) ProtoMessage() {}

func (x *RevokeRefreshTokenResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeRefreshTokenResp.ProtoReflect.Descriptor instead.
func (*RevokeRefreshTokenResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescGZIP(), []int{17}
}

func (x *RevokeRefreshTokenResp) GetSucceed() bool {
	if x != nil {
		return x.Succeed
	}
	return false
}

type DeleteSessionsByOriginReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserID        string                 `protobuf:"bytes,1,opt,name=userID,proto3" json:"userID,omitempty"`
	SessionIDs    []string               `protobuf:"bytes,2,rep,name=sessionIDs,proto3" json:"sessionIDs,omitempty"`
	Origin        string                 `protobuf:"bytes,3,opt,name=origin,proto3" json:"origin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteSessionsByOriginReq) Reset() {
	*x = DeleteSessionsByOriginReq{}
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteSessionsByOriginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSessionsByOriginReq) ProtoMessage() {}

func (x *DeleteSessionsByOriginReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSessionsByOriginReq.ProtoReflect.Descriptor instead.
func (*DeleteSessionsByOriginReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescGZIP(), []int{18}
}

func (x *DeleteSessionsByOriginReq) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

func (x *DeleteSessionsByOriginReq) GetSessionIDs() []string {
	if x != nil {
		return x.SessionIDs
	}
	return nil
}

func (x *DeleteSessionsByOriginReq) GetOrigin() string {
	if x != nil {
		return x.Origin
	}
	return ""
}

type DeleteSessionsByOriginResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Succeed       bool                   `protobuf:"varint,1,opt,name=succeed,proto3" json:"succeed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteSessionsByOriginResp) Reset() {
	*x = DeleteSessionsByOriginResp{}
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteSessionsByOriginResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSessionsByOriginResp) ProtoMessage() {}

func (x *DeleteSessionsByOriginResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSessionsByOriginResp.ProtoReflect.Descriptor instead.
func (*DeleteSessionsByOriginResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescGZIP(), []int{19}
}

func (x *DeleteSessionsByOriginResp) GetSucceed() bool {
	if x != nil {
		return x.Succeed
	}
	return false
}

type SetUserRoleByOriginReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserID        string                 `protobuf:"bytes,1,opt,name=userID,proto3" json:"userID,omitempty"`
	Role          string                 `protobuf:"bytes,2,opt,name=role,proto3" json:"role,omitempty"`
	Origin        string                 `protobuf:"bytes,3,opt,name=origin,proto3" json:"origin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetUserRoleByOriginReq) Reset() {
	*x = SetUserRoleByOriginReq{}
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetUserRoleByOriginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetUserRoleByOriginReq) ProtoMessage() {}

func (x *SetUserRoleByOriginReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetUserRoleByOriginReq.ProtoReflect.Descriptor instead.
func (*SetUserRoleByOriginReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescGZIP(), []int{20}
}

func (x *SetUserRoleByOriginReq) GetUserID() string {
	if x != nil {
		return x.UserID
	}
	return ""
}

func (x *SetUserRoleByOriginReq) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *SetUserRoleByOriginReq) GetOrigin() string {
	if x != nil {
		return x.Origin
	}
	return ""
}

type SetUserRoleByOriginResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Succeed       bool                   `protobuf:"varint,1,opt,name=succeed,proto3" json:"succeed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetUserRoleByOriginResp) Reset() {
	*x = SetUserRoleByOriginResp{}
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetUserRoleByOriginResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetUserRoleByOriginResp) ProtoMessage() {}

func (x *SetUserRoleByOriginResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetUserRoleByOriginResp.ProtoReflect.Descriptor instead.
func (*SetUserRoleByOriginResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescGZIP(), []int{21}
}

func (x *SetUserRoleByOriginResp) GetSucceed() bool {
	if x != nil {
		return x.Succeed
	}
	return false
}

var File_specs_proto_keycloak_proxy_keycloak_proxy_proto protoreflect.FileDescriptor

const file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDesc = "" +
	"\n" +
	"/specs/proto/keycloak-proxy/keycloak-proxy.proto\x12\rkeycloakProxy\"\x10\n" +
	"\x0eHealthCheckReq\")\n" +
	"\x0fHealthCheckResp\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\"%\n" +
	"\rCreateUserReq\x12\x14\n" +
	"\x05phone\x18\x01 \x01(\tR\x05phone\"B\n" +
	"\x0eCreateUserResp\x12\x18\n" +
	"\asucceed\x18\x01 \x01(\bR\asucceed\x12\x16\n" +
	"\x06userID\x18\x02 \x01(\tR\x06userID\"?\n" +
	"\x0fActivateUserReq\x12\x14\n" +
	"\x05phone\x18\x01 \x01(\tR\x05phone\x12\x16\n" +
	"\x06userID\x18\x02 \x01(\tR\x06userID\",\n" +
	"\x10ActivateUserResp\x12\x18\n" +
	"\asucceed\x18\x01 \x01(\bR\asucceed\"<\n" +
	"\fBlockUserReq\x12\x14\n" +
	"\x05phone\x18\x01 \x01(\tR\x05phone\x12\x16\n" +
	"\x06userID\x18\x02 \x01(\tR\x06userID\")\n" +
	"\rBlockUserResp\x12\x18\n" +
	"\asucceed\x18\x01 \x01(\bR\asucceed\"$\n" +
	"\fGetTokensReq\x12\x14\n" +
	"\x05phone\x18\x01 \x01(\tR\x05phone\"s\n" +
	"\rGetTokensResp\x12 \n" +
	"\vaccessToken\x18\x01 \x01(\tR\vaccessToken\x12\"\n" +
	"\frefreshToken\x18\x02 \x01(\tR\frefreshToken\x12\x1c\n" +
	"\tsessionID\x18\x03 \x01(\tR\tsessionID\"6\n" +
	"\x10RefreshTokensReq\x12\"\n" +
	"\frefreshToken\x18\x01 \x01(\tR\frefreshToken\"w\n" +
	"\x11RefreshTokensResp\x12 \n" +
	"\vaccessToken\x18\x01 \x01(\tR\vaccessToken\x12\"\n" +
	"\frefreshToken\x18\x02 \x01(\tR\frefreshToken\x12\x1c\n" +
	"\tsessionID\x18\x03 \x01(\tR\tsessionID\"K\n" +
	"\x11DeleteSessionsReq\x12\x16\n" +
	"\x06userID\x18\x01 \x01(\tR\x06userID\x12\x1e\n" +
	"\n" +
	"sessionIDs\x18\x02 \x03(\tR\n" +
	"sessionIDs\".\n" +
	"\x12DeleteSessionsResp\x12\x18\n" +
	"\asucceed\x18\x01 \x01(\bR\asucceed\"<\n" +
	"\x0eSetUserRoleReq\x12\x16\n" +
	"\x06userID\x18\x01 \x01(\tR\x06userID\x12\x12\n" +
	"\x04role\x18\x02 \x01(\tR\x04role\"+\n" +
	"\x0fSetUserRoleResp\x12\x18\n" +
	"\asucceed\x18\x01 \x01(\bR\asucceed\"-\n" +
	"\x15RevokeRefreshTokenReq\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\"2\n" +
	"\x16RevokeRefreshTokenResp\x12\x18\n" +
	"\asucceed\x18\x01 \x01(\bR\asucceed\"k\n" +
	"\x19DeleteSessionsByOriginReq\x12\x16\n" +
	"\x06userID\x18\x01 \x01(\tR\x06userID\x12\x1e\n" +
	"\n" +
	"sessionIDs\x18\x02 \x03(\tR\n" +
	"sessionIDs\x12\x16\n" +
	"\x06origin\x18\x03 \x01(\tR\x06origin\"6\n" +
	"\x1aDeleteSessionsByOriginResp\x12\x18\n" +
	"\asucceed\x18\x01 \x01(\bR\asucceed\"\\\n" +
	"\x16SetUserRoleByOriginReq\x12\x16\n" +
	"\x06userID\x18\x01 \x01(\tR\x06userID\x12\x12\n" +
	"\x04role\x18\x02 \x01(\tR\x04role\x12\x16\n" +
	"\x06origin\x18\x03 \x01(\tR\x06origin\"3\n" +
	"\x17SetUserRoleByOriginResp\x12\x18\n" +
	"\asucceed\x18\x01 \x01(\bR\asucceed2\xba\a\n" +
	"\rKeycloakproxy\x12L\n" +
	"\vHealthCheck\x12\x1d.keycloakProxy.HealthCheckReq\x1a\x1e.keycloakProxy.HealthCheckResp\x12I\n" +
	"\n" +
	"CreateUser\x12\x1c.keycloakProxy.CreateUserReq\x1a\x1d.keycloakProxy.CreateUserResp\x12O\n" +
	"\fActivateUser\x12\x1e.keycloakProxy.ActivateUserReq\x1a\x1f.keycloakProxy.ActivateUserResp\x12F\n" +
	"\tBlockUser\x12\x1b.keycloakProxy.BlockUserReq\x1a\x1c.keycloakProxy.BlockUserResp\x12F\n" +
	"\tGetTokens\x12\x1b.keycloakProxy.GetTokensReq\x1a\x1c.keycloakProxy.GetTokensResp\x12R\n" +
	"\rRefreshTokens\x12\x1f.keycloakProxy.RefreshTokensReq\x1a .keycloakProxy.RefreshTokensResp\x12U\n" +
	"\x0eDeleteSessions\x12 .keycloakProxy.DeleteSessionsReq\x1a!.keycloakProxy.DeleteSessionsResp\x12L\n" +
	"\vSetUserRole\x12\x1d.keycloakProxy.SetUserRoleReq\x1a\x1e.keycloakProxy.SetUserRoleResp\x12a\n" +
	"\x12RevokeRefreshToken\x12$.keycloakProxy.RevokeRefreshTokenReq\x1a%.keycloakProxy.RevokeRefreshTokenResp\x12m\n" +
	"\x16DeleteSessionsByOrigin\x12(.keycloakProxy.DeleteSessionsByOriginReq\x1a).keycloakProxy.DeleteSessionsByOriginResp\x12d\n" +
	"\x13SetUserRoleByOrigin\x12%.keycloakProxy.SetUserRoleByOriginReq\x1a&.keycloakProxy.SetUserRoleByOriginRespB\x1cZ\x1aspecs/proto/keycloak-proxyb\x06proto3"

var (
	file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescOnce sync.Once
	file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescData []byte
)

func file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescGZIP() []byte {
	file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescOnce.Do(func() {
		file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDesc), len(file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDesc)))
	})
	return file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDescData
}

var file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_specs_proto_keycloak_proxy_keycloak_proxy_proto_goTypes = []any{
	(*HealthCheckReq)(nil),             // 0: keycloakProxy.HealthCheckReq
	(*HealthCheckResp)(nil),            // 1: keycloakProxy.HealthCheckResp
	(*CreateUserReq)(nil),              // 2: keycloakProxy.CreateUserReq
	(*CreateUserResp)(nil),             // 3: keycloakProxy.CreateUserResp
	(*ActivateUserReq)(nil),            // 4: keycloakProxy.ActivateUserReq
	(*ActivateUserResp)(nil),           // 5: keycloakProxy.ActivateUserResp
	(*BlockUserReq)(nil),               // 6: keycloakProxy.BlockUserReq
	(*BlockUserResp)(nil),              // 7: keycloakProxy.BlockUserResp
	(*GetTokensReq)(nil),               // 8: keycloakProxy.GetTokensReq
	(*GetTokensResp)(nil),              // 9: keycloakProxy.GetTokensResp
	(*RefreshTokensReq)(nil),           // 10: keycloakProxy.RefreshTokensReq
	(*RefreshTokensResp)(nil),          // 11: keycloakProxy.RefreshTokensResp
	(*DeleteSessionsReq)(nil),          // 12: keycloakProxy.DeleteSessionsReq
	(*DeleteSessionsResp)(nil),         // 13: keycloakProxy.DeleteSessionsResp
	(*SetUserRoleReq)(nil),             // 14: keycloakProxy.SetUserRoleReq
	(*SetUserRoleResp)(nil),            // 15: keycloakProxy.SetUserRoleResp
	(*RevokeRefreshTokenReq)(nil),      // 16: keycloakProxy.RevokeRefreshTokenReq
	(*RevokeRefreshTokenResp)(nil),     // 17: keycloakProxy.RevokeRefreshTokenResp
	(*DeleteSessionsByOriginReq)(nil),  // 18: keycloakProxy.DeleteSessionsByOriginReq
	(*DeleteSessionsByOriginResp)(nil), // 19: keycloakProxy.DeleteSessionsByOriginResp
	(*SetUserRoleByOriginReq)(nil),     // 20: keycloakProxy.SetUserRoleByOriginReq
	(*SetUserRoleByOriginResp)(nil),    // 21: keycloakProxy.SetUserRoleByOriginResp
}
var file_specs_proto_keycloak_proxy_keycloak_proxy_proto_depIdxs = []int32{
	0,  // 0: keycloakProxy.Keycloakproxy.HealthCheck:input_type -> keycloakProxy.HealthCheckReq
	2,  // 1: keycloakProxy.Keycloakproxy.CreateUser:input_type -> keycloakProxy.CreateUserReq
	4,  // 2: keycloakProxy.Keycloakproxy.ActivateUser:input_type -> keycloakProxy.ActivateUserReq
	6,  // 3: keycloakProxy.Keycloakproxy.BlockUser:input_type -> keycloakProxy.BlockUserReq
	8,  // 4: keycloakProxy.Keycloakproxy.GetTokens:input_type -> keycloakProxy.GetTokensReq
	10, // 5: keycloakProxy.Keycloakproxy.RefreshTokens:input_type -> keycloakProxy.RefreshTokensReq
	12, // 6: keycloakProxy.Keycloakproxy.DeleteSessions:input_type -> keycloakProxy.DeleteSessionsReq
	14, // 7: keycloakProxy.Keycloakproxy.SetUserRole:input_type -> keycloakProxy.SetUserRoleReq
	16, // 8: keycloakProxy.Keycloakproxy.RevokeRefreshToken:input_type -> keycloakProxy.RevokeRefreshTokenReq
	18, // 9: keycloakProxy.Keycloakproxy.DeleteSessionsByOrigin:input_type -> keycloakProxy.DeleteSessionsByOriginReq
	20, // 10: keycloakProxy.Keycloakproxy.SetUserRoleByOrigin:input_type -> keycloakProxy.SetUserRoleByOriginReq
	1,  // 11: keycloakProxy.Keycloakproxy.HealthCheck:output_type -> keycloakProxy.HealthCheckResp
	3,  // 12: keycloakProxy.Keycloakproxy.CreateUser:output_type -> keycloakProxy.CreateUserResp
	5,  // 13: keycloakProxy.Keycloakproxy.ActivateUser:output_type -> keycloakProxy.ActivateUserResp
	7,  // 14: keycloakProxy.Keycloakproxy.BlockUser:output_type -> keycloakProxy.BlockUserResp
	9,  // 15: keycloakProxy.Keycloakproxy.GetTokens:output_type -> keycloakProxy.GetTokensResp
	11, // 16: keycloakProxy.Keycloakproxy.RefreshTokens:output_type -> keycloakProxy.RefreshTokensResp
	13, // 17: keycloakProxy.Keycloakproxy.DeleteSessions:output_type -> keycloakProxy.DeleteSessionsResp
	15, // 18: keycloakProxy.Keycloakproxy.SetUserRole:output_type -> keycloakProxy.SetUserRoleResp
	17, // 19: keycloakProxy.Keycloakproxy.RevokeRefreshToken:output_type -> keycloakProxy.RevokeRefreshTokenResp
	19, // 20: keycloakProxy.Keycloakproxy.DeleteSessionsByOrigin:output_type -> keycloakProxy.DeleteSessionsByOriginResp
	21, // 21: keycloakProxy.Keycloakproxy.SetUserRoleByOrigin:output_type -> keycloakProxy.SetUserRoleByOriginResp
	11, // [11:22] is the sub-list for method output_type
	0,  // [0:11] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_specs_proto_keycloak_proxy_keycloak_proxy_proto_init() }
func file_specs_proto_keycloak_proxy_keycloak_proxy_proto_init() {
	if File_specs_proto_keycloak_proxy_keycloak_proxy_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDesc), len(file_specs_proto_keycloak_proxy_keycloak_proxy_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_specs_proto_keycloak_proxy_keycloak_proxy_proto_goTypes,
		DependencyIndexes: file_specs_proto_keycloak_proxy_keycloak_proxy_proto_depIdxs,
		MessageInfos:      file_specs_proto_keycloak_proxy_keycloak_proxy_proto_msgTypes,
	}.Build()
	File_specs_proto_keycloak_proxy_keycloak_proxy_proto = out.File
	file_specs_proto_keycloak_proxy_keycloak_proxy_proto_goTypes = nil
	file_specs_proto_keycloak_proxy_keycloak_proxy_proto_depIdxs = nil
}
