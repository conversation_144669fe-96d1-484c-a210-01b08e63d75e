// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.32.1
// source: specs/proto/keycloak-proxy/keycloak-proxy.proto

package keycloak_proxy

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Keycloakproxy_HealthCheck_FullMethodName            = "/keycloakProxy.Keycloakproxy/HealthCheck"
	Keycloakproxy_CreateUser_FullMethodName             = "/keycloakProxy.Keycloakproxy/CreateUser"
	Keycloakproxy_ActivateUser_FullMethodName           = "/keycloakProxy.Keycloakproxy/ActivateUser"
	Keycloakproxy_BlockUser_FullMethodName              = "/keycloakProxy.Keycloakproxy/BlockUser"
	Keycloakproxy_GetTokens_FullMethodName              = "/keycloakProxy.Keycloakproxy/GetTokens"
	Keycloakproxy_RefreshTokens_FullMethodName          = "/keycloakProxy.Keycloakproxy/RefreshTokens"
	Keycloakproxy_DeleteSessions_FullMethodName         = "/keycloakProxy.Keycloakproxy/DeleteSessions"
	Keycloakproxy_SetUserRole_FullMethodName            = "/keycloakProxy.Keycloakproxy/SetUserRole"
	Keycloakproxy_RevokeRefreshToken_FullMethodName     = "/keycloakProxy.Keycloakproxy/RevokeRefreshToken"
	Keycloakproxy_DeleteSessionsByOrigin_FullMethodName = "/keycloakProxy.Keycloakproxy/DeleteSessionsByOrigin"
	Keycloakproxy_SetUserRoleByOrigin_FullMethodName    = "/keycloakProxy.Keycloakproxy/SetUserRoleByOrigin"
)

// KeycloakproxyClient is the client API for Keycloakproxy service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type KeycloakproxyClient interface {
	HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error)
	CreateUser(ctx context.Context, in *CreateUserReq, opts ...grpc.CallOption) (*CreateUserResp, error)
	ActivateUser(ctx context.Context, in *ActivateUserReq, opts ...grpc.CallOption) (*ActivateUserResp, error)
	BlockUser(ctx context.Context, in *BlockUserReq, opts ...grpc.CallOption) (*BlockUserResp, error)
	GetTokens(ctx context.Context, in *GetTokensReq, opts ...grpc.CallOption) (*GetTokensResp, error)
	RefreshTokens(ctx context.Context, in *RefreshTokensReq, opts ...grpc.CallOption) (*RefreshTokensResp, error)
	DeleteSessions(ctx context.Context, in *DeleteSessionsReq, opts ...grpc.CallOption) (*DeleteSessionsResp, error)
	SetUserRole(ctx context.Context, in *SetUserRoleReq, opts ...grpc.CallOption) (*SetUserRoleResp, error)
	RevokeRefreshToken(ctx context.Context, in *RevokeRefreshTokenReq, opts ...grpc.CallOption) (*RevokeRefreshTokenResp, error)
	DeleteSessionsByOrigin(ctx context.Context, in *DeleteSessionsByOriginReq, opts ...grpc.CallOption) (*DeleteSessionsByOriginResp, error)
	SetUserRoleByOrigin(ctx context.Context, in *SetUserRoleByOriginReq, opts ...grpc.CallOption) (*SetUserRoleByOriginResp, error)
}

type keycloakproxyClient struct {
	cc grpc.ClientConnInterface
}

func NewKeycloakproxyClient(cc grpc.ClientConnInterface) KeycloakproxyClient {
	return &keycloakproxyClient{cc}
}

func (c *keycloakproxyClient) HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResp)
	err := c.cc.Invoke(ctx, Keycloakproxy_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *keycloakproxyClient) CreateUser(ctx context.Context, in *CreateUserReq, opts ...grpc.CallOption) (*CreateUserResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateUserResp)
	err := c.cc.Invoke(ctx, Keycloakproxy_CreateUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *keycloakproxyClient) ActivateUser(ctx context.Context, in *ActivateUserReq, opts ...grpc.CallOption) (*ActivateUserResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ActivateUserResp)
	err := c.cc.Invoke(ctx, Keycloakproxy_ActivateUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *keycloakproxyClient) BlockUser(ctx context.Context, in *BlockUserReq, opts ...grpc.CallOption) (*BlockUserResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BlockUserResp)
	err := c.cc.Invoke(ctx, Keycloakproxy_BlockUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *keycloakproxyClient) GetTokens(ctx context.Context, in *GetTokensReq, opts ...grpc.CallOption) (*GetTokensResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTokensResp)
	err := c.cc.Invoke(ctx, Keycloakproxy_GetTokens_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *keycloakproxyClient) RefreshTokens(ctx context.Context, in *RefreshTokensReq, opts ...grpc.CallOption) (*RefreshTokensResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RefreshTokensResp)
	err := c.cc.Invoke(ctx, Keycloakproxy_RefreshTokens_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *keycloakproxyClient) DeleteSessions(ctx context.Context, in *DeleteSessionsReq, opts ...grpc.CallOption) (*DeleteSessionsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteSessionsResp)
	err := c.cc.Invoke(ctx, Keycloakproxy_DeleteSessions_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *keycloakproxyClient) SetUserRole(ctx context.Context, in *SetUserRoleReq, opts ...grpc.CallOption) (*SetUserRoleResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetUserRoleResp)
	err := c.cc.Invoke(ctx, Keycloakproxy_SetUserRole_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *keycloakproxyClient) RevokeRefreshToken(ctx context.Context, in *RevokeRefreshTokenReq, opts ...grpc.CallOption) (*RevokeRefreshTokenResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RevokeRefreshTokenResp)
	err := c.cc.Invoke(ctx, Keycloakproxy_RevokeRefreshToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *keycloakproxyClient) DeleteSessionsByOrigin(ctx context.Context, in *DeleteSessionsByOriginReq, opts ...grpc.CallOption) (*DeleteSessionsByOriginResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteSessionsByOriginResp)
	err := c.cc.Invoke(ctx, Keycloakproxy_DeleteSessionsByOrigin_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *keycloakproxyClient) SetUserRoleByOrigin(ctx context.Context, in *SetUserRoleByOriginReq, opts ...grpc.CallOption) (*SetUserRoleByOriginResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetUserRoleByOriginResp)
	err := c.cc.Invoke(ctx, Keycloakproxy_SetUserRoleByOrigin_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// KeycloakproxyServer is the server API for Keycloakproxy service.
// All implementations must embed UnimplementedKeycloakproxyServer
// for forward compatibility.
type KeycloakproxyServer interface {
	HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error)
	CreateUser(context.Context, *CreateUserReq) (*CreateUserResp, error)
	ActivateUser(context.Context, *ActivateUserReq) (*ActivateUserResp, error)
	BlockUser(context.Context, *BlockUserReq) (*BlockUserResp, error)
	GetTokens(context.Context, *GetTokensReq) (*GetTokensResp, error)
	RefreshTokens(context.Context, *RefreshTokensReq) (*RefreshTokensResp, error)
	DeleteSessions(context.Context, *DeleteSessionsReq) (*DeleteSessionsResp, error)
	SetUserRole(context.Context, *SetUserRoleReq) (*SetUserRoleResp, error)
	RevokeRefreshToken(context.Context, *RevokeRefreshTokenReq) (*RevokeRefreshTokenResp, error)
	DeleteSessionsByOrigin(context.Context, *DeleteSessionsByOriginReq) (*DeleteSessionsByOriginResp, error)
	SetUserRoleByOrigin(context.Context, *SetUserRoleByOriginReq) (*SetUserRoleByOriginResp, error)
	mustEmbedUnimplementedKeycloakproxyServer()
}

// UnimplementedKeycloakproxyServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedKeycloakproxyServer struct{}

func (UnimplementedKeycloakproxyServer) HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedKeycloakproxyServer) CreateUser(context.Context, *CreateUserReq) (*CreateUserResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUser not implemented")
}
func (UnimplementedKeycloakproxyServer) ActivateUser(context.Context, *ActivateUserReq) (*ActivateUserResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActivateUser not implemented")
}
func (UnimplementedKeycloakproxyServer) BlockUser(context.Context, *BlockUserReq) (*BlockUserResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BlockUser not implemented")
}
func (UnimplementedKeycloakproxyServer) GetTokens(context.Context, *GetTokensReq) (*GetTokensResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTokens not implemented")
}
func (UnimplementedKeycloakproxyServer) RefreshTokens(context.Context, *RefreshTokensReq) (*RefreshTokensResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshTokens not implemented")
}
func (UnimplementedKeycloakproxyServer) DeleteSessions(context.Context, *DeleteSessionsReq) (*DeleteSessionsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSessions not implemented")
}
func (UnimplementedKeycloakproxyServer) SetUserRole(context.Context, *SetUserRoleReq) (*SetUserRoleResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetUserRole not implemented")
}
func (UnimplementedKeycloakproxyServer) RevokeRefreshToken(context.Context, *RevokeRefreshTokenReq) (*RevokeRefreshTokenResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RevokeRefreshToken not implemented")
}
func (UnimplementedKeycloakproxyServer) DeleteSessionsByOrigin(context.Context, *DeleteSessionsByOriginReq) (*DeleteSessionsByOriginResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSessionsByOrigin not implemented")
}
func (UnimplementedKeycloakproxyServer) SetUserRoleByOrigin(context.Context, *SetUserRoleByOriginReq) (*SetUserRoleByOriginResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetUserRoleByOrigin not implemented")
}
func (UnimplementedKeycloakproxyServer) mustEmbedUnimplementedKeycloakproxyServer() {}
func (UnimplementedKeycloakproxyServer) testEmbeddedByValue()                       {}

// UnsafeKeycloakproxyServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to KeycloakproxyServer will
// result in compilation errors.
type UnsafeKeycloakproxyServer interface {
	mustEmbedUnimplementedKeycloakproxyServer()
}

func RegisterKeycloakproxyServer(s grpc.ServiceRegistrar, srv KeycloakproxyServer) {
	// If the following call pancis, it indicates UnimplementedKeycloakproxyServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Keycloakproxy_ServiceDesc, srv)
}

func _Keycloakproxy_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KeycloakproxyServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Keycloakproxy_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KeycloakproxyServer).HealthCheck(ctx, req.(*HealthCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Keycloakproxy_CreateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KeycloakproxyServer).CreateUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Keycloakproxy_CreateUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KeycloakproxyServer).CreateUser(ctx, req.(*CreateUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Keycloakproxy_ActivateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivateUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KeycloakproxyServer).ActivateUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Keycloakproxy_ActivateUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KeycloakproxyServer).ActivateUser(ctx, req.(*ActivateUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Keycloakproxy_BlockUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BlockUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KeycloakproxyServer).BlockUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Keycloakproxy_BlockUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KeycloakproxyServer).BlockUser(ctx, req.(*BlockUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Keycloakproxy_GetTokens_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTokensReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KeycloakproxyServer).GetTokens(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Keycloakproxy_GetTokens_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KeycloakproxyServer).GetTokens(ctx, req.(*GetTokensReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Keycloakproxy_RefreshTokens_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshTokensReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KeycloakproxyServer).RefreshTokens(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Keycloakproxy_RefreshTokens_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KeycloakproxyServer).RefreshTokens(ctx, req.(*RefreshTokensReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Keycloakproxy_DeleteSessions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSessionsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KeycloakproxyServer).DeleteSessions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Keycloakproxy_DeleteSessions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KeycloakproxyServer).DeleteSessions(ctx, req.(*DeleteSessionsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Keycloakproxy_SetUserRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KeycloakproxyServer).SetUserRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Keycloakproxy_SetUserRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KeycloakproxyServer).SetUserRole(ctx, req.(*SetUserRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Keycloakproxy_RevokeRefreshToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RevokeRefreshTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KeycloakproxyServer).RevokeRefreshToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Keycloakproxy_RevokeRefreshToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KeycloakproxyServer).RevokeRefreshToken(ctx, req.(*RevokeRefreshTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Keycloakproxy_DeleteSessionsByOrigin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSessionsByOriginReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KeycloakproxyServer).DeleteSessionsByOrigin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Keycloakproxy_DeleteSessionsByOrigin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KeycloakproxyServer).DeleteSessionsByOrigin(ctx, req.(*DeleteSessionsByOriginReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Keycloakproxy_SetUserRoleByOrigin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserRoleByOriginReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KeycloakproxyServer).SetUserRoleByOrigin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Keycloakproxy_SetUserRoleByOrigin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KeycloakproxyServer).SetUserRoleByOrigin(ctx, req.(*SetUserRoleByOriginReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Keycloakproxy_ServiceDesc is the grpc.ServiceDesc for Keycloakproxy service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Keycloakproxy_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "keycloakProxy.Keycloakproxy",
	HandlerType: (*KeycloakproxyServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HealthCheck",
			Handler:    _Keycloakproxy_HealthCheck_Handler,
		},
		{
			MethodName: "CreateUser",
			Handler:    _Keycloakproxy_CreateUser_Handler,
		},
		{
			MethodName: "ActivateUser",
			Handler:    _Keycloakproxy_ActivateUser_Handler,
		},
		{
			MethodName: "BlockUser",
			Handler:    _Keycloakproxy_BlockUser_Handler,
		},
		{
			MethodName: "GetTokens",
			Handler:    _Keycloakproxy_GetTokens_Handler,
		},
		{
			MethodName: "RefreshTokens",
			Handler:    _Keycloakproxy_RefreshTokens_Handler,
		},
		{
			MethodName: "DeleteSessions",
			Handler:    _Keycloakproxy_DeleteSessions_Handler,
		},
		{
			MethodName: "SetUserRole",
			Handler:    _Keycloakproxy_SetUserRole_Handler,
		},
		{
			MethodName: "RevokeRefreshToken",
			Handler:    _Keycloakproxy_RevokeRefreshToken_Handler,
		},
		{
			MethodName: "DeleteSessionsByOrigin",
			Handler:    _Keycloakproxy_DeleteSessionsByOrigin_Handler,
		},
		{
			MethodName: "SetUserRoleByOrigin",
			Handler:    _Keycloakproxy_SetUserRoleByOrigin_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "specs/proto/keycloak-proxy/keycloak-proxy.proto",
}
