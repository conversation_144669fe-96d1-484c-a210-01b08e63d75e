// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v6.32.1
// source: specs/proto/bts-bridge/bts-bridge.proto

package bts_bridge

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetLastBtsVerifyResultByIinReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Iin           string                 `protobuf:"bytes,1,opt,name=iin,proto3" json:"iin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLastBtsVerifyResultByIinReq) Reset() {
	*x = GetLastBtsVerifyResultByIinReq{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLastBtsVerifyResultByIinReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLastBtsVerifyResultByIinReq) ProtoMessage() {}

func (x *GetLastBtsVerifyResultByIinReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLastBtsVerifyResultByIinReq.ProtoReflect.Descriptor instead.
func (*GetLastBtsVerifyResultByIinReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{0}
}

func (x *GetLastBtsVerifyResultByIinReq) GetIin() string {
	if x != nil {
		return x.Iin
	}
	return ""
}

type GetLastBtsVerifyResultByIinResp struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ConfidenceLevel string                 `protobuf:"bytes,1,opt,name=confidence_level,json=confidenceLevel,proto3" json:"confidence_level,omitempty"`
	RequestId       string                 `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetLastBtsVerifyResultByIinResp) Reset() {
	*x = GetLastBtsVerifyResultByIinResp{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLastBtsVerifyResultByIinResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLastBtsVerifyResultByIinResp) ProtoMessage() {}

func (x *GetLastBtsVerifyResultByIinResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLastBtsVerifyResultByIinResp.ProtoReflect.Descriptor instead.
func (*GetLastBtsVerifyResultByIinResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{1}
}

func (x *GetLastBtsVerifyResultByIinResp) GetConfidenceLevel() string {
	if x != nil {
		return x.ConfidenceLevel
	}
	return ""
}

func (x *GetLastBtsVerifyResultByIinResp) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

type HealthCheckReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckReq) Reset() {
	*x = HealthCheckReq{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckReq) ProtoMessage() {}

func (x *HealthCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckReq.ProtoReflect.Descriptor instead.
func (*HealthCheckReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{2}
}

type HealthCheckResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckResp) Reset() {
	*x = HealthCheckResp{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResp) ProtoMessage() {}

func (x *HealthCheckResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResp.ProtoReflect.Descriptor instead.
func (*HealthCheckResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{3}
}

func (x *HealthCheckResp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type RequestVerifyReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          string                 `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	RedirectUri   string                 `protobuf:"bytes,2,opt,name=redirect_uri,json=redirectUri,proto3" json:"redirect_uri,omitempty"`
	Iin           string                 `protobuf:"bytes,3,opt,name=iin,proto3" json:"iin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RequestVerifyReq) Reset() {
	*x = RequestVerifyReq{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestVerifyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestVerifyReq) ProtoMessage() {}

func (x *RequestVerifyReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestVerifyReq.ProtoReflect.Descriptor instead.
func (*RequestVerifyReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{4}
}

func (x *RequestVerifyReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *RequestVerifyReq) GetRedirectUri() string {
	if x != nil {
		return x.RedirectUri
	}
	return ""
}

func (x *RequestVerifyReq) GetIin() string {
	if x != nil {
		return x.Iin
	}
	return ""
}

type RequestVerifyResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RequestId     string                 `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	Verified      bool                   `protobuf:"varint,2,opt,name=verified,proto3" json:"verified,omitempty"`
	BiometryQr    bool                   `protobuf:"varint,3,opt,name=biometry_qr,json=biometryQr,proto3" json:"biometry_qr,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RequestVerifyResp) Reset() {
	*x = RequestVerifyResp{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestVerifyResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestVerifyResp) ProtoMessage() {}

func (x *RequestVerifyResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestVerifyResp.ProtoReflect.Descriptor instead.
func (*RequestVerifyResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{5}
}

func (x *RequestVerifyResp) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *RequestVerifyResp) GetVerified() bool {
	if x != nil {
		return x.Verified
	}
	return false
}

func (x *RequestVerifyResp) GetBiometryQr() bool {
	if x != nil {
		return x.BiometryQr
	}
	return false
}

type RequestPersonalDataReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Iin           string                 `protobuf:"bytes,1,opt,name=iin,proto3" json:"iin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RequestPersonalDataReq) Reset() {
	*x = RequestPersonalDataReq{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestPersonalDataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestPersonalDataReq) ProtoMessage() {}

func (x *RequestPersonalDataReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestPersonalDataReq.ProtoReflect.Descriptor instead.
func (*RequestPersonalDataReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{6}
}

func (x *RequestPersonalDataReq) GetIin() string {
	if x != nil {
		return x.Iin
	}
	return ""
}

type RequestPersonalDataResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Citizenship   *Citizenship           `protobuf:"bytes,1,opt,name=citizenship,proto3" json:"citizenship,omitempty"`
	Gender        *Gender                `protobuf:"bytes,2,opt,name=gender,proto3" json:"gender,omitempty"`
	Documents     []*Document            `protobuf:"bytes,3,rep,name=documents,proto3" json:"documents,omitempty"`
	RegAddress    *RegAddress            `protobuf:"bytes,4,opt,name=reg_address,json=regAddress,proto3" json:"reg_address,omitempty"`
	Name          string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Patronymic    string                 `protobuf:"bytes,6,opt,name=patronymic,proto3" json:"patronymic,omitempty"`
	Surname       string                 `protobuf:"bytes,7,opt,name=surname,proto3" json:"surname,omitempty"`
	Iin           string                 `protobuf:"bytes,8,opt,name=iin,proto3" json:"iin,omitempty"`
	BirthDate     string                 `protobuf:"bytes,9,opt,name=birth_date,json=birthDate,proto3" json:"birth_date,omitempty"`
	BirthPlace    *BirthPlace            `protobuf:"bytes,10,opt,name=birth_place,json=birthPlace,proto3" json:"birth_place,omitempty"`
	LifeStatus    *LifeStatus            `protobuf:"bytes,11,opt,name=life_status,json=lifeStatus,proto3" json:"life_status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RequestPersonalDataResp) Reset() {
	*x = RequestPersonalDataResp{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestPersonalDataResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestPersonalDataResp) ProtoMessage() {}

func (x *RequestPersonalDataResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestPersonalDataResp.ProtoReflect.Descriptor instead.
func (*RequestPersonalDataResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{7}
}

func (x *RequestPersonalDataResp) GetCitizenship() *Citizenship {
	if x != nil {
		return x.Citizenship
	}
	return nil
}

func (x *RequestPersonalDataResp) GetGender() *Gender {
	if x != nil {
		return x.Gender
	}
	return nil
}

func (x *RequestPersonalDataResp) GetDocuments() []*Document {
	if x != nil {
		return x.Documents
	}
	return nil
}

func (x *RequestPersonalDataResp) GetRegAddress() *RegAddress {
	if x != nil {
		return x.RegAddress
	}
	return nil
}

func (x *RequestPersonalDataResp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RequestPersonalDataResp) GetPatronymic() string {
	if x != nil {
		return x.Patronymic
	}
	return ""
}

func (x *RequestPersonalDataResp) GetSurname() string {
	if x != nil {
		return x.Surname
	}
	return ""
}

func (x *RequestPersonalDataResp) GetIin() string {
	if x != nil {
		return x.Iin
	}
	return ""
}

func (x *RequestPersonalDataResp) GetBirthDate() string {
	if x != nil {
		return x.BirthDate
	}
	return ""
}

func (x *RequestPersonalDataResp) GetBirthPlace() *BirthPlace {
	if x != nil {
		return x.BirthPlace
	}
	return nil
}

func (x *RequestPersonalDataResp) GetLifeStatus() *LifeStatus {
	if x != nil {
		return x.LifeStatus
	}
	return nil
}

type Gender struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NameRu        string                 `protobuf:"bytes,1,opt,name=name_ru,json=nameRu,proto3" json:"name_ru,omitempty"`
	Code          int64                  `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	NameKz        string                 `protobuf:"bytes,3,opt,name=name_kz,json=nameKz,proto3" json:"name_kz,omitempty"`
	ChangeDate    string                 `protobuf:"bytes,4,opt,name=change_date,json=changeDate,proto3" json:"change_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Gender) Reset() {
	*x = Gender{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Gender) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Gender) ProtoMessage() {}

func (x *Gender) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Gender.ProtoReflect.Descriptor instead.
func (*Gender) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{8}
}

func (x *Gender) GetNameRu() string {
	if x != nil {
		return x.NameRu
	}
	return ""
}

func (x *Gender) GetCode() int64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *Gender) GetNameKz() string {
	if x != nil {
		return x.NameKz
	}
	return ""
}

func (x *Gender) GetChangeDate() string {
	if x != nil {
		return x.ChangeDate
	}
	return ""
}

type GetLiveness3DPhotoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Iin           string                 `protobuf:"bytes,1,opt,name=iin,proto3" json:"iin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLiveness3DPhotoReq) Reset() {
	*x = GetLiveness3DPhotoReq{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLiveness3DPhotoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLiveness3DPhotoReq) ProtoMessage() {}

func (x *GetLiveness3DPhotoReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLiveness3DPhotoReq.ProtoReflect.Descriptor instead.
func (*GetLiveness3DPhotoReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{9}
}

func (x *GetLiveness3DPhotoReq) GetIin() string {
	if x != nil {
		return x.Iin
	}
	return ""
}

type GetLiveness3DPhotoResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	File          []byte                 `protobuf:"bytes,2,opt,name=file,proto3" json:"file,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLiveness3DPhotoResp) Reset() {
	*x = GetLiveness3DPhotoResp{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLiveness3DPhotoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLiveness3DPhotoResp) ProtoMessage() {}

func (x *GetLiveness3DPhotoResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLiveness3DPhotoResp.ProtoReflect.Descriptor instead.
func (*GetLiveness3DPhotoResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{10}
}

func (x *GetLiveness3DPhotoResp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *GetLiveness3DPhotoResp) GetFile() []byte {
	if x != nil {
		return x.File
	}
	return nil
}

type GetLiveness3DVideoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Iin           string                 `protobuf:"bytes,1,opt,name=iin,proto3" json:"iin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLiveness3DVideoReq) Reset() {
	*x = GetLiveness3DVideoReq{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLiveness3DVideoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLiveness3DVideoReq) ProtoMessage() {}

func (x *GetLiveness3DVideoReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLiveness3DVideoReq.ProtoReflect.Descriptor instead.
func (*GetLiveness3DVideoReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{11}
}

func (x *GetLiveness3DVideoReq) GetIin() string {
	if x != nil {
		return x.Iin
	}
	return ""
}

type GetLiveness3DVideoResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	File          []byte                 `protobuf:"bytes,2,opt,name=file,proto3" json:"file,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLiveness3DVideoResp) Reset() {
	*x = GetLiveness3DVideoResp{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLiveness3DVideoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLiveness3DVideoResp) ProtoMessage() {}

func (x *GetLiveness3DVideoResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLiveness3DVideoResp.ProtoReflect.Descriptor instead.
func (*GetLiveness3DVideoResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{12}
}

func (x *GetLiveness3DVideoResp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *GetLiveness3DVideoResp) GetFile() []byte {
	if x != nil {
		return x.File
	}
	return nil
}

type GetPersonalDataReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Iin           string                 `protobuf:"bytes,1,opt,name=iin,proto3" json:"iin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPersonalDataReq) Reset() {
	*x = GetPersonalDataReq{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPersonalDataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPersonalDataReq) ProtoMessage() {}

func (x *GetPersonalDataReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPersonalDataReq.ProtoReflect.Descriptor instead.
func (*GetPersonalDataReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{13}
}

func (x *GetPersonalDataReq) GetIin() string {
	if x != nil {
		return x.Iin
	}
	return ""
}

type GetPersonalDataResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Citizenship   *Citizenship           `protobuf:"bytes,1,opt,name=citizenship,proto3" json:"citizenship,omitempty"`
	Gender        *Gender                `protobuf:"bytes,2,opt,name=gender,proto3" json:"gender,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Patronymic    string                 `protobuf:"bytes,4,opt,name=patronymic,proto3" json:"patronymic,omitempty"`
	Surname       string                 `protobuf:"bytes,5,opt,name=surname,proto3" json:"surname,omitempty"`
	Iin           string                 `protobuf:"bytes,6,opt,name=iin,proto3" json:"iin,omitempty"`
	BirthDate     string                 `protobuf:"bytes,7,opt,name=birth_date,json=birthDate,proto3" json:"birth_date,omitempty"`
	LifeStatus    *LifeStatus            `protobuf:"bytes,8,opt,name=life_status,json=lifeStatus,proto3" json:"life_status,omitempty"`
	Documents     []*Document            `protobuf:"bytes,9,rep,name=documents,proto3" json:"documents,omitempty"`
	RegAddress    *RegAddress            `protobuf:"bytes,10,opt,name=reg_address,json=regAddress,proto3" json:"reg_address,omitempty"`
	BirthPlace    *BirthPlace            `protobuf:"bytes,11,opt,name=birth_place,json=birthPlace,proto3" json:"birth_place,omitempty"`
	Phone         string                 `protobuf:"bytes,12,opt,name=phone,proto3" json:"phone,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPersonalDataResp) Reset() {
	*x = GetPersonalDataResp{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPersonalDataResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPersonalDataResp) ProtoMessage() {}

func (x *GetPersonalDataResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPersonalDataResp.ProtoReflect.Descriptor instead.
func (*GetPersonalDataResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{14}
}

func (x *GetPersonalDataResp) GetCitizenship() *Citizenship {
	if x != nil {
		return x.Citizenship
	}
	return nil
}

func (x *GetPersonalDataResp) GetGender() *Gender {
	if x != nil {
		return x.Gender
	}
	return nil
}

func (x *GetPersonalDataResp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetPersonalDataResp) GetPatronymic() string {
	if x != nil {
		return x.Patronymic
	}
	return ""
}

func (x *GetPersonalDataResp) GetSurname() string {
	if x != nil {
		return x.Surname
	}
	return ""
}

func (x *GetPersonalDataResp) GetIin() string {
	if x != nil {
		return x.Iin
	}
	return ""
}

func (x *GetPersonalDataResp) GetBirthDate() string {
	if x != nil {
		return x.BirthDate
	}
	return ""
}

func (x *GetPersonalDataResp) GetLifeStatus() *LifeStatus {
	if x != nil {
		return x.LifeStatus
	}
	return nil
}

func (x *GetPersonalDataResp) GetDocuments() []*Document {
	if x != nil {
		return x.Documents
	}
	return nil
}

func (x *GetPersonalDataResp) GetRegAddress() *RegAddress {
	if x != nil {
		return x.RegAddress
	}
	return nil
}

func (x *GetPersonalDataResp) GetBirthPlace() *BirthPlace {
	if x != nil {
		return x.BirthPlace
	}
	return nil
}

func (x *GetPersonalDataResp) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

type Country struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NameRu        string                 `protobuf:"bytes,1,opt,name=name_ru,json=nameRu,proto3" json:"name_ru,omitempty"`
	Code          int64                  `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	NameKz        string                 `protobuf:"bytes,3,opt,name=name_kz,json=nameKz,proto3" json:"name_kz,omitempty"`
	ChangeDate    string                 `protobuf:"bytes,4,opt,name=change_date,json=changeDate,proto3" json:"change_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Country) Reset() {
	*x = Country{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Country) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Country) ProtoMessage() {}

func (x *Country) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Country.ProtoReflect.Descriptor instead.
func (*Country) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{15}
}

func (x *Country) GetNameRu() string {
	if x != nil {
		return x.NameRu
	}
	return ""
}

func (x *Country) GetCode() int64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *Country) GetNameKz() string {
	if x != nil {
		return x.NameKz
	}
	return ""
}

func (x *Country) GetChangeDate() string {
	if x != nil {
		return x.ChangeDate
	}
	return ""
}

type District struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NameRu        string                 `protobuf:"bytes,1,opt,name=name_ru,json=nameRu,proto3" json:"name_ru,omitempty"`
	Code          int64                  `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	NameKz        string                 `protobuf:"bytes,3,opt,name=name_kz,json=nameKz,proto3" json:"name_kz,omitempty"`
	ChangeDate    string                 `protobuf:"bytes,4,opt,name=change_date,json=changeDate,proto3" json:"change_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *District) Reset() {
	*x = District{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *District) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*District) ProtoMessage() {}

func (x *District) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use District.ProtoReflect.Descriptor instead.
func (*District) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{16}
}

func (x *District) GetNameRu() string {
	if x != nil {
		return x.NameRu
	}
	return ""
}

func (x *District) GetCode() int64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *District) GetNameKz() string {
	if x != nil {
		return x.NameKz
	}
	return ""
}

func (x *District) GetChangeDate() string {
	if x != nil {
		return x.ChangeDate
	}
	return ""
}

type Region struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NameRu        string                 `protobuf:"bytes,1,opt,name=name_ru,json=nameRu,proto3" json:"name_ru,omitempty"`
	Code          int64                  `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	NameKz        string                 `protobuf:"bytes,3,opt,name=name_kz,json=nameKz,proto3" json:"name_kz,omitempty"`
	ChangeDate    string                 `protobuf:"bytes,4,opt,name=change_date,json=changeDate,proto3" json:"change_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Region) Reset() {
	*x = Region{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Region) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Region) ProtoMessage() {}

func (x *Region) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Region.ProtoReflect.Descriptor instead.
func (*Region) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{17}
}

func (x *Region) GetNameRu() string {
	if x != nil {
		return x.NameRu
	}
	return ""
}

func (x *Region) GetCode() int64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *Region) GetNameKz() string {
	if x != nil {
		return x.NameKz
	}
	return ""
}

func (x *Region) GetChangeDate() string {
	if x != nil {
		return x.ChangeDate
	}
	return ""
}

type BirthPlace struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Country       *Country               `protobuf:"bytes,1,opt,name=country,proto3" json:"country,omitempty"`
	City          string                 `protobuf:"bytes,2,opt,name=city,proto3" json:"city,omitempty"`
	District      *District              `protobuf:"bytes,3,opt,name=district,proto3" json:"district,omitempty"`
	Region        *Region                `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BirthPlace) Reset() {
	*x = BirthPlace{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BirthPlace) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BirthPlace) ProtoMessage() {}

func (x *BirthPlace) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BirthPlace.ProtoReflect.Descriptor instead.
func (*BirthPlace) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{18}
}

func (x *BirthPlace) GetCountry() *Country {
	if x != nil {
		return x.Country
	}
	return nil
}

func (x *BirthPlace) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *BirthPlace) GetDistrict() *District {
	if x != nil {
		return x.District
	}
	return nil
}

func (x *BirthPlace) GetRegion() *Region {
	if x != nil {
		return x.Region
	}
	return nil
}

type RegAddress struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Country       *Country               `protobuf:"bytes,1,opt,name=country,proto3" json:"country,omitempty"`
	BeginDate     string                 `protobuf:"bytes,2,opt,name=begin_date,json=beginDate,proto3" json:"begin_date,omitempty"`
	Street        string                 `protobuf:"bytes,3,opt,name=street,proto3" json:"street,omitempty"`
	Flat          string                 `protobuf:"bytes,4,opt,name=flat,proto3" json:"flat,omitempty"`
	District      *District              `protobuf:"bytes,5,opt,name=district,proto3" json:"district,omitempty"`
	Region        *Region                `protobuf:"bytes,6,opt,name=region,proto3" json:"region,omitempty"`
	Building      string                 `protobuf:"bytes,7,opt,name=building,proto3" json:"building,omitempty"`
	City          string                 `protobuf:"bytes,8,opt,name=city,proto3" json:"city,omitempty"`
	Corpus        string                 `protobuf:"bytes,9,opt,name=corpus,proto3" json:"corpus,omitempty"`
	Status        *RegAddressStatus      `protobuf:"bytes,10,opt,name=status,proto3" json:"status,omitempty"`
	Invalidity    *Invalidity            `protobuf:"bytes,11,opt,name=invalidity,proto3" json:"invalidity,omitempty"`
	ArCode        int64                  `protobuf:"varint,12,opt,name=ar_code,json=arCode,proto3" json:"ar_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegAddress) Reset() {
	*x = RegAddress{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegAddress) ProtoMessage() {}

func (x *RegAddress) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegAddress.ProtoReflect.Descriptor instead.
func (*RegAddress) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{19}
}

func (x *RegAddress) GetCountry() *Country {
	if x != nil {
		return x.Country
	}
	return nil
}

func (x *RegAddress) GetBeginDate() string {
	if x != nil {
		return x.BeginDate
	}
	return ""
}

func (x *RegAddress) GetStreet() string {
	if x != nil {
		return x.Street
	}
	return ""
}

func (x *RegAddress) GetFlat() string {
	if x != nil {
		return x.Flat
	}
	return ""
}

func (x *RegAddress) GetDistrict() *District {
	if x != nil {
		return x.District
	}
	return nil
}

func (x *RegAddress) GetRegion() *Region {
	if x != nil {
		return x.Region
	}
	return nil
}

func (x *RegAddress) GetBuilding() string {
	if x != nil {
		return x.Building
	}
	return ""
}

func (x *RegAddress) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *RegAddress) GetCorpus() string {
	if x != nil {
		return x.Corpus
	}
	return ""
}

func (x *RegAddress) GetStatus() *RegAddressStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *RegAddress) GetInvalidity() *Invalidity {
	if x != nil {
		return x.Invalidity
	}
	return nil
}

func (x *RegAddress) GetArCode() int64 {
	if x != nil {
		return x.ArCode
	}
	return 0
}

type Invalidity struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NameRu        string                 `protobuf:"bytes,1,opt,name=name_ru,json=nameRu,proto3" json:"name_ru,omitempty"`
	Code          int64                  `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	NameKz        string                 `protobuf:"bytes,3,opt,name=name_kz,json=nameKz,proto3" json:"name_kz,omitempty"`
	ChangeDate    string                 `protobuf:"bytes,4,opt,name=change_date,json=changeDate,proto3" json:"change_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Invalidity) Reset() {
	*x = Invalidity{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Invalidity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Invalidity) ProtoMessage() {}

func (x *Invalidity) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Invalidity.ProtoReflect.Descriptor instead.
func (*Invalidity) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{20}
}

func (x *Invalidity) GetNameRu() string {
	if x != nil {
		return x.NameRu
	}
	return ""
}

func (x *Invalidity) GetCode() int64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *Invalidity) GetNameKz() string {
	if x != nil {
		return x.NameKz
	}
	return ""
}

func (x *Invalidity) GetChangeDate() string {
	if x != nil {
		return x.ChangeDate
	}
	return ""
}

type RegAddressStatus struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NameRu        string                 `protobuf:"bytes,1,opt,name=name_ru,json=nameRu,proto3" json:"name_ru,omitempty"`
	Code          int64                  `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	NameKz        string                 `protobuf:"bytes,3,opt,name=name_kz,json=nameKz,proto3" json:"name_kz,omitempty"`
	ChangeDate    string                 `protobuf:"bytes,4,opt,name=change_date,json=changeDate,proto3" json:"change_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegAddressStatus) Reset() {
	*x = RegAddressStatus{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegAddressStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegAddressStatus) ProtoMessage() {}

func (x *RegAddressStatus) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegAddressStatus.ProtoReflect.Descriptor instead.
func (*RegAddressStatus) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{21}
}

func (x *RegAddressStatus) GetNameRu() string {
	if x != nil {
		return x.NameRu
	}
	return ""
}

func (x *RegAddressStatus) GetCode() int64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RegAddressStatus) GetNameKz() string {
	if x != nil {
		return x.NameKz
	}
	return ""
}

func (x *RegAddressStatus) GetChangeDate() string {
	if x != nil {
		return x.ChangeDate
	}
	return ""
}

type DocumentType struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NameRu        string                 `protobuf:"bytes,1,opt,name=name_ru,json=nameRu,proto3" json:"name_ru,omitempty"`
	Code          string                 `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	NameKz        string                 `protobuf:"bytes,3,opt,name=name_kz,json=nameKz,proto3" json:"name_kz,omitempty"`
	ChangeDate    string                 `protobuf:"bytes,4,opt,name=change_date,json=changeDate,proto3" json:"change_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocumentType) Reset() {
	*x = DocumentType{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocumentType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocumentType) ProtoMessage() {}

func (x *DocumentType) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocumentType.ProtoReflect.Descriptor instead.
func (*DocumentType) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{22}
}

func (x *DocumentType) GetNameRu() string {
	if x != nil {
		return x.NameRu
	}
	return ""
}

func (x *DocumentType) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *DocumentType) GetNameKz() string {
	if x != nil {
		return x.NameKz
	}
	return ""
}

func (x *DocumentType) GetChangeDate() string {
	if x != nil {
		return x.ChangeDate
	}
	return ""
}

type DocumentIssueOrganization struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NameRu        string                 `protobuf:"bytes,1,opt,name=name_ru,json=nameRu,proto3" json:"name_ru,omitempty"`
	Code          string                 `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	NameKz        string                 `protobuf:"bytes,3,opt,name=name_kz,json=nameKz,proto3" json:"name_kz,omitempty"`
	ChangeDate    string                 `protobuf:"bytes,4,opt,name=change_date,json=changeDate,proto3" json:"change_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocumentIssueOrganization) Reset() {
	*x = DocumentIssueOrganization{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocumentIssueOrganization) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocumentIssueOrganization) ProtoMessage() {}

func (x *DocumentIssueOrganization) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocumentIssueOrganization.ProtoReflect.Descriptor instead.
func (*DocumentIssueOrganization) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{23}
}

func (x *DocumentIssueOrganization) GetNameRu() string {
	if x != nil {
		return x.NameRu
	}
	return ""
}

func (x *DocumentIssueOrganization) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *DocumentIssueOrganization) GetNameKz() string {
	if x != nil {
		return x.NameKz
	}
	return ""
}

func (x *DocumentIssueOrganization) GetChangeDate() string {
	if x != nil {
		return x.ChangeDate
	}
	return ""
}

type DocumentStatus struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NameRu        string                 `protobuf:"bytes,1,opt,name=name_ru,json=nameRu,proto3" json:"name_ru,omitempty"`
	Code          string                 `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	NameKz        string                 `protobuf:"bytes,3,opt,name=name_kz,json=nameKz,proto3" json:"name_kz,omitempty"`
	ChangeDate    string                 `protobuf:"bytes,4,opt,name=change_date,json=changeDate,proto3" json:"change_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DocumentStatus) Reset() {
	*x = DocumentStatus{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DocumentStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DocumentStatus) ProtoMessage() {}

func (x *DocumentStatus) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DocumentStatus.ProtoReflect.Descriptor instead.
func (*DocumentStatus) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{24}
}

func (x *DocumentStatus) GetNameRu() string {
	if x != nil {
		return x.NameRu
	}
	return ""
}

func (x *DocumentStatus) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *DocumentStatus) GetNameKz() string {
	if x != nil {
		return x.NameKz
	}
	return ""
}

func (x *DocumentStatus) GetChangeDate() string {
	if x != nil {
		return x.ChangeDate
	}
	return ""
}

type Document struct {
	state             protoimpl.MessageState     `protogen:"open.v1"`
	Number            string                     `protobuf:"bytes,1,opt,name=number,proto3" json:"number,omitempty"`
	BeginDate         string                     `protobuf:"bytes,2,opt,name=begin_date,json=beginDate,proto3" json:"begin_date,omitempty"`
	Patronymic        string                     `protobuf:"bytes,3,opt,name=patronymic,proto3" json:"patronymic,omitempty"`
	EndDate           string                     `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	Surname           string                     `protobuf:"bytes,5,opt,name=surname,proto3" json:"surname,omitempty"`
	Name              string                     `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	Type              *DocumentType              `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty"`
	BirthDate         string                     `protobuf:"bytes,8,opt,name=birth_date,json=birthDate,proto3" json:"birth_date,omitempty"`
	IssueOrganization *DocumentIssueOrganization `protobuf:"bytes,9,opt,name=issue_organization,json=issueOrganization,proto3" json:"issue_organization,omitempty"`
	Status            *DocumentStatus            `protobuf:"bytes,10,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *Document) Reset() {
	*x = Document{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Document) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Document) ProtoMessage() {}

func (x *Document) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Document.ProtoReflect.Descriptor instead.
func (*Document) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{25}
}

func (x *Document) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

func (x *Document) GetBeginDate() string {
	if x != nil {
		return x.BeginDate
	}
	return ""
}

func (x *Document) GetPatronymic() string {
	if x != nil {
		return x.Patronymic
	}
	return ""
}

func (x *Document) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *Document) GetSurname() string {
	if x != nil {
		return x.Surname
	}
	return ""
}

func (x *Document) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Document) GetType() *DocumentType {
	if x != nil {
		return x.Type
	}
	return nil
}

func (x *Document) GetBirthDate() string {
	if x != nil {
		return x.BirthDate
	}
	return ""
}

func (x *Document) GetIssueOrganization() *DocumentIssueOrganization {
	if x != nil {
		return x.IssueOrganization
	}
	return nil
}

func (x *Document) GetStatus() *DocumentStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

type Documents struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Document      []*Document            `protobuf:"bytes,1,rep,name=document,proto3" json:"document,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Documents) Reset() {
	*x = Documents{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Documents) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Documents) ProtoMessage() {}

func (x *Documents) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Documents.ProtoReflect.Descriptor instead.
func (*Documents) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{26}
}

func (x *Documents) GetDocument() []*Document {
	if x != nil {
		return x.Document
	}
	return nil
}

type Citizenship struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NameRu        string                 `protobuf:"bytes,1,opt,name=name_ru,json=nameRu,proto3" json:"name_ru,omitempty"`
	Code          int64                  `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	NameKz        string                 `protobuf:"bytes,3,opt,name=name_kz,json=nameKz,proto3" json:"name_kz,omitempty"`
	ChangeDate    string                 `protobuf:"bytes,4,opt,name=change_date,json=changeDate,proto3" json:"change_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Citizenship) Reset() {
	*x = Citizenship{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Citizenship) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Citizenship) ProtoMessage() {}

func (x *Citizenship) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Citizenship.ProtoReflect.Descriptor instead.
func (*Citizenship) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{27}
}

func (x *Citizenship) GetNameRu() string {
	if x != nil {
		return x.NameRu
	}
	return ""
}

func (x *Citizenship) GetCode() int64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *Citizenship) GetNameKz() string {
	if x != nil {
		return x.NameKz
	}
	return ""
}

func (x *Citizenship) GetChangeDate() string {
	if x != nil {
		return x.ChangeDate
	}
	return ""
}

type LifeStatus struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NameRu        string                 `protobuf:"bytes,1,opt,name=name_ru,json=nameRu,proto3" json:"name_ru,omitempty"`
	Code          int64                  `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`
	NameKz        string                 `protobuf:"bytes,3,opt,name=name_kz,json=nameKz,proto3" json:"name_kz,omitempty"`
	ChangeDate    string                 `protobuf:"bytes,4,opt,name=change_date,json=changeDate,proto3" json:"change_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LifeStatus) Reset() {
	*x = LifeStatus{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LifeStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LifeStatus) ProtoMessage() {}

func (x *LifeStatus) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LifeStatus.ProtoReflect.Descriptor instead.
func (*LifeStatus) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{28}
}

func (x *LifeStatus) GetNameRu() string {
	if x != nil {
		return x.NameRu
	}
	return ""
}

func (x *LifeStatus) GetCode() int64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *LifeStatus) GetNameKz() string {
	if x != nil {
		return x.NameKz
	}
	return ""
}

func (x *LifeStatus) GetChangeDate() string {
	if x != nil {
		return x.ChangeDate
	}
	return ""
}

type Nationality struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NameRu        string                 `protobuf:"bytes,1,opt,name=name_ru,json=nameRu,proto3" json:"name_ru,omitempty"`
	Code          string                 `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	NameKz        string                 `protobuf:"bytes,3,opt,name=name_kz,json=nameKz,proto3" json:"name_kz,omitempty"`
	ChangeDate    string                 `protobuf:"bytes,4,opt,name=change_date,json=changeDate,proto3" json:"change_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Nationality) Reset() {
	*x = Nationality{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Nationality) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Nationality) ProtoMessage() {}

func (x *Nationality) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Nationality.ProtoReflect.Descriptor instead.
func (*Nationality) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{29}
}

func (x *Nationality) GetNameRu() string {
	if x != nil {
		return x.NameRu
	}
	return ""
}

func (x *Nationality) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *Nationality) GetNameKz() string {
	if x != nil {
		return x.NameKz
	}
	return ""
}

func (x *Nationality) GetChangeDate() string {
	if x != nil {
		return x.ChangeDate
	}
	return ""
}

type Person struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Patronymic    string                 `protobuf:"bytes,2,opt,name=patronymic,proto3" json:"patronymic,omitempty"`
	Surname       string                 `protobuf:"bytes,3,opt,name=surname,proto3" json:"surname,omitempty"`
	Iin           string                 `protobuf:"bytes,4,opt,name=iin,proto3" json:"iin,omitempty"`
	BirthDate     string                 `protobuf:"bytes,5,opt,name=birth_date,json=birthDate,proto3" json:"birth_date,omitempty"`
	BirthPlace    *BirthPlace            `protobuf:"bytes,6,opt,name=birth_place,json=birthPlace,proto3" json:"birth_place,omitempty"`
	RegAddress    *RegAddress            `protobuf:"bytes,7,opt,name=reg_address,json=regAddress,proto3" json:"reg_address,omitempty"`
	Documents     *Documents             `protobuf:"bytes,8,opt,name=documents,proto3" json:"documents,omitempty"`
	Citizenship   *Citizenship           `protobuf:"bytes,9,opt,name=citizenship,proto3" json:"citizenship,omitempty"`
	LifeStatus    *LifeStatus            `protobuf:"bytes,10,opt,name=life_status,json=lifeStatus,proto3" json:"life_status,omitempty"`
	Gender        *Gender                `protobuf:"bytes,11,opt,name=gender,proto3" json:"gender,omitempty"`
	Nationality   *Nationality           `protobuf:"bytes,12,opt,name=nationality,proto3" json:"nationality,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Person) Reset() {
	*x = Person{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Person) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Person) ProtoMessage() {}

func (x *Person) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Person.ProtoReflect.Descriptor instead.
func (*Person) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{30}
}

func (x *Person) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Person) GetPatronymic() string {
	if x != nil {
		return x.Patronymic
	}
	return ""
}

func (x *Person) GetSurname() string {
	if x != nil {
		return x.Surname
	}
	return ""
}

func (x *Person) GetIin() string {
	if x != nil {
		return x.Iin
	}
	return ""
}

func (x *Person) GetBirthDate() string {
	if x != nil {
		return x.BirthDate
	}
	return ""
}

func (x *Person) GetBirthPlace() *BirthPlace {
	if x != nil {
		return x.BirthPlace
	}
	return nil
}

func (x *Person) GetRegAddress() *RegAddress {
	if x != nil {
		return x.RegAddress
	}
	return nil
}

func (x *Person) GetDocuments() *Documents {
	if x != nil {
		return x.Documents
	}
	return nil
}

func (x *Person) GetCitizenship() *Citizenship {
	if x != nil {
		return x.Citizenship
	}
	return nil
}

func (x *Person) GetLifeStatus() *LifeStatus {
	if x != nil {
		return x.LifeStatus
	}
	return nil
}

func (x *Person) GetGender() *Gender {
	if x != nil {
		return x.Gender
	}
	return nil
}

func (x *Person) GetNationality() *Nationality {
	if x != nil {
		return x.Nationality
	}
	return nil
}

type CreateTrustedPhoneReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Phone         string                 `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTrustedPhoneReq) Reset() {
	*x = CreateTrustedPhoneReq{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTrustedPhoneReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTrustedPhoneReq) ProtoMessage() {}

func (x *CreateTrustedPhoneReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTrustedPhoneReq.ProtoReflect.Descriptor instead.
func (*CreateTrustedPhoneReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{31}
}

func (x *CreateTrustedPhoneReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

type CreateTrustedPhoneResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RequestId     string                 `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	Secret        string                 `protobuf:"bytes,2,opt,name=secret,proto3" json:"secret,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTrustedPhoneResp) Reset() {
	*x = CreateTrustedPhoneResp{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTrustedPhoneResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTrustedPhoneResp) ProtoMessage() {}

func (x *CreateTrustedPhoneResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTrustedPhoneResp.ProtoReflect.Descriptor instead.
func (*CreateTrustedPhoneResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{32}
}

func (x *CreateTrustedPhoneResp) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *CreateTrustedPhoneResp) GetSecret() string {
	if x != nil {
		return x.Secret
	}
	return ""
}

type UploadFileForSignReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DocumentID    string                 `protobuf:"bytes,1,opt,name=documentID,proto3" json:"documentID,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Bytes         []byte                 `protobuf:"bytes,3,opt,name=bytes,proto3" json:"bytes,omitempty"`
	Link          *string                `protobuf:"bytes,4,opt,name=link,proto3,oneof" json:"link,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadFileForSignReq) Reset() {
	*x = UploadFileForSignReq{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadFileForSignReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadFileForSignReq) ProtoMessage() {}

func (x *UploadFileForSignReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadFileForSignReq.ProtoReflect.Descriptor instead.
func (*UploadFileForSignReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{33}
}

func (x *UploadFileForSignReq) GetDocumentID() string {
	if x != nil {
		return x.DocumentID
	}
	return ""
}

func (x *UploadFileForSignReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UploadFileForSignReq) GetBytes() []byte {
	if x != nil {
		return x.Bytes
	}
	return nil
}

func (x *UploadFileForSignReq) GetLink() string {
	if x != nil && x.Link != nil {
		return *x.Link
	}
	return ""
}

type UploadFileForSignResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DocumentID    string                 `protobuf:"bytes,1,opt,name=documentID,proto3" json:"documentID,omitempty"`
	SignableID    string                 `protobuf:"bytes,2,opt,name=signableID,proto3" json:"signableID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadFileForSignResp) Reset() {
	*x = UploadFileForSignResp{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadFileForSignResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadFileForSignResp) ProtoMessage() {}

func (x *UploadFileForSignResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadFileForSignResp.ProtoReflect.Descriptor instead.
func (*UploadFileForSignResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{34}
}

func (x *UploadFileForSignResp) GetDocumentID() string {
	if x != nil {
		return x.DocumentID
	}
	return ""
}

func (x *UploadFileForSignResp) GetSignableID() string {
	if x != nil {
		return x.SignableID
	}
	return ""
}

type GetSignedFilesReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Iin           string                 `protobuf:"bytes,1,opt,name=iin,proto3" json:"iin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSignedFilesReq) Reset() {
	*x = GetSignedFilesReq{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSignedFilesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSignedFilesReq) ProtoMessage() {}

func (x *GetSignedFilesReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSignedFilesReq.ProtoReflect.Descriptor instead.
func (*GetSignedFilesReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{35}
}

func (x *GetSignedFilesReq) GetIin() string {
	if x != nil {
		return x.Iin
	}
	return ""
}

type SignedDocument struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DocumentID    string                 `protobuf:"bytes,1,opt,name=documentID,proto3" json:"documentID,omitempty"`
	DocType       string                 `protobuf:"bytes,2,opt,name=docType,proto3" json:"docType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignedDocument) Reset() {
	*x = SignedDocument{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignedDocument) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignedDocument) ProtoMessage() {}

func (x *SignedDocument) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignedDocument.ProtoReflect.Descriptor instead.
func (*SignedDocument) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{36}
}

func (x *SignedDocument) GetDocumentID() string {
	if x != nil {
		return x.DocumentID
	}
	return ""
}

func (x *SignedDocument) GetDocType() string {
	if x != nil {
		return x.DocType
	}
	return ""
}

type GetSignedFilesResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Documents     []*SignedDocument      `protobuf:"bytes,1,rep,name=documents,proto3" json:"documents,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSignedFilesResp) Reset() {
	*x = GetSignedFilesResp{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSignedFilesResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSignedFilesResp) ProtoMessage() {}

func (x *GetSignedFilesResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSignedFilesResp.ProtoReflect.Descriptor instead.
func (*GetSignedFilesResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{37}
}

func (x *GetSignedFilesResp) GetDocuments() []*SignedDocument {
	if x != nil {
		return x.Documents
	}
	return nil
}

type GetIdentificationSessionReportReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Iin           string                 `protobuf:"bytes,1,opt,name=iin,proto3" json:"iin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIdentificationSessionReportReq) Reset() {
	*x = GetIdentificationSessionReportReq{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIdentificationSessionReportReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIdentificationSessionReportReq) ProtoMessage() {}

func (x *GetIdentificationSessionReportReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIdentificationSessionReportReq.ProtoReflect.Descriptor instead.
func (*GetIdentificationSessionReportReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{38}
}

func (x *GetIdentificationSessionReportReq) GetIin() string {
	if x != nil {
		return x.Iin
	}
	return ""
}

type GetIdentificationSessionReportResp struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	SessionDocumentID string                 `protobuf:"bytes,1,opt,name=sessionDocumentID,proto3" json:"sessionDocumentID,omitempty"`
	File              []byte                 `protobuf:"bytes,2,opt,name=file,proto3" json:"file,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetIdentificationSessionReportResp) Reset() {
	*x = GetIdentificationSessionReportResp{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIdentificationSessionReportResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIdentificationSessionReportResp) ProtoMessage() {}

func (x *GetIdentificationSessionReportResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIdentificationSessionReportResp.ProtoReflect.Descriptor instead.
func (*GetIdentificationSessionReportResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{39}
}

func (x *GetIdentificationSessionReportResp) GetSessionDocumentID() string {
	if x != nil {
		return x.SessionDocumentID
	}
	return ""
}

func (x *GetIdentificationSessionReportResp) GetFile() []byte {
	if x != nil {
		return x.File
	}
	return nil
}

type GetIdentificationSessionListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DataFrom      string                 `protobuf:"bytes,1,opt,name=dataFrom,proto3" json:"dataFrom,omitempty"`
	DataTo        string                 `protobuf:"bytes,2,opt,name=dataTo,proto3" json:"dataTo,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIdentificationSessionListReq) Reset() {
	*x = GetIdentificationSessionListReq{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIdentificationSessionListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIdentificationSessionListReq) ProtoMessage() {}

func (x *GetIdentificationSessionListReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIdentificationSessionListReq.ProtoReflect.Descriptor instead.
func (*GetIdentificationSessionListReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{40}
}

func (x *GetIdentificationSessionListReq) GetDataFrom() string {
	if x != nil {
		return x.DataFrom
	}
	return ""
}

func (x *GetIdentificationSessionListReq) GetDataTo() string {
	if x != nil {
		return x.DataTo
	}
	return ""
}

type GetIdentificationSessionListResp struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	List          []*GetIdentificationSession `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIdentificationSessionListResp) Reset() {
	*x = GetIdentificationSessionListResp{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIdentificationSessionListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIdentificationSessionListResp) ProtoMessage() {}

func (x *GetIdentificationSessionListResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIdentificationSessionListResp.ProtoReflect.Descriptor instead.
func (*GetIdentificationSessionListResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{41}
}

func (x *GetIdentificationSessionListResp) GetList() []*GetIdentificationSession {
	if x != nil {
		return x.List
	}
	return nil
}

type GetIdentificationSession struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	SessionDocumentID string                 `protobuf:"bytes,1,opt,name=sessionDocumentID,proto3" json:"sessionDocumentID,omitempty"`
	Iin               string                 `protobuf:"bytes,2,opt,name=iin,proto3" json:"iin,omitempty"`
	Phone             string                 `protobuf:"bytes,3,opt,name=phone,proto3" json:"phone,omitempty"`
	CreatedAt         string                 `protobuf:"bytes,4,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetIdentificationSession) Reset() {
	*x = GetIdentificationSession{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIdentificationSession) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIdentificationSession) ProtoMessage() {}

func (x *GetIdentificationSession) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIdentificationSession.ProtoReflect.Descriptor instead.
func (*GetIdentificationSession) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{42}
}

func (x *GetIdentificationSession) GetSessionDocumentID() string {
	if x != nil {
		return x.SessionDocumentID
	}
	return ""
}

func (x *GetIdentificationSession) GetIin() string {
	if x != nil {
		return x.Iin
	}
	return ""
}

func (x *GetIdentificationSession) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *GetIdentificationSession) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

type GetIdentificationSessionReportSessionIDReq struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	SessionDocumentID string                 `protobuf:"bytes,1,opt,name=sessionDocumentID,proto3" json:"sessionDocumentID,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetIdentificationSessionReportSessionIDReq) Reset() {
	*x = GetIdentificationSessionReportSessionIDReq{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIdentificationSessionReportSessionIDReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIdentificationSessionReportSessionIDReq) ProtoMessage() {}

func (x *GetIdentificationSessionReportSessionIDReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIdentificationSessionReportSessionIDReq.ProtoReflect.Descriptor instead.
func (*GetIdentificationSessionReportSessionIDReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{43}
}

func (x *GetIdentificationSessionReportSessionIDReq) GetSessionDocumentID() string {
	if x != nil {
		return x.SessionDocumentID
	}
	return ""
}

type GetIdentificationSessionReportSessionIDResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	File          []byte                 `protobuf:"bytes,1,opt,name=file,proto3" json:"file,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIdentificationSessionReportSessionIDResp) Reset() {
	*x = GetIdentificationSessionReportSessionIDResp{}
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIdentificationSessionReportSessionIDResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIdentificationSessionReportSessionIDResp) ProtoMessage() {}

func (x *GetIdentificationSessionReportSessionIDResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIdentificationSessionReportSessionIDResp.ProtoReflect.Descriptor instead.
func (*GetIdentificationSessionReportSessionIDResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP(), []int{44}
}

func (x *GetIdentificationSessionReportSessionIDResp) GetFile() []byte {
	if x != nil {
		return x.File
	}
	return nil
}

var File_specs_proto_bts_bridge_bts_bridge_proto protoreflect.FileDescriptor

const file_specs_proto_bts_bridge_bts_bridge_proto_rawDesc = "" +
	"\n" +
	"'specs/proto/bts-bridge/bts-bridge.proto\x12\tbtsBridge\"2\n" +
	"\x1eGetLastBtsVerifyResultByIinReq\x12\x10\n" +
	"\x03iin\x18\x01 \x01(\tR\x03iin\"k\n" +
	"\x1fGetLastBtsVerifyResultByIinResp\x12)\n" +
	"\x10confidence_level\x18\x01 \x01(\tR\x0fconfidenceLevel\x12\x1d\n" +
	"\n" +
	"request_id\x18\x02 \x01(\tR\trequestId\"\x10\n" +
	"\x0eHealthCheckReq\")\n" +
	"\x0fHealthCheckResp\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\"[\n" +
	"\x10RequestVerifyReq\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x12!\n" +
	"\fredirect_uri\x18\x02 \x01(\tR\vredirectUri\x12\x10\n" +
	"\x03iin\x18\x03 \x01(\tR\x03iin\"o\n" +
	"\x11RequestVerifyResp\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\tR\trequestId\x12\x1a\n" +
	"\bverified\x18\x02 \x01(\bR\bverified\x12\x1f\n" +
	"\vbiometry_qr\x18\x03 \x01(\bR\n" +
	"biometryQr\"*\n" +
	"\x16RequestPersonalDataReq\x12\x10\n" +
	"\x03iin\x18\x01 \x01(\tR\x03iin\"\xd8\x03\n" +
	"\x17RequestPersonalDataResp\x128\n" +
	"\vcitizenship\x18\x01 \x01(\v2\x16.btsBridge.CitizenshipR\vcitizenship\x12)\n" +
	"\x06gender\x18\x02 \x01(\v2\x11.btsBridge.GenderR\x06gender\x121\n" +
	"\tdocuments\x18\x03 \x03(\v2\x13.btsBridge.DocumentR\tdocuments\x126\n" +
	"\vreg_address\x18\x04 \x01(\v2\x15.btsBridge.RegAddressR\n" +
	"regAddress\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\x12\x1e\n" +
	"\n" +
	"patronymic\x18\x06 \x01(\tR\n" +
	"patronymic\x12\x18\n" +
	"\asurname\x18\a \x01(\tR\asurname\x12\x10\n" +
	"\x03iin\x18\b \x01(\tR\x03iin\x12\x1d\n" +
	"\n" +
	"birth_date\x18\t \x01(\tR\tbirthDate\x126\n" +
	"\vbirth_place\x18\n" +
	" \x01(\v2\x15.btsBridge.BirthPlaceR\n" +
	"birthPlace\x126\n" +
	"\vlife_status\x18\v \x01(\v2\x15.btsBridge.LifeStatusR\n" +
	"lifeStatus\"o\n" +
	"\x06Gender\x12\x17\n" +
	"\aname_ru\x18\x01 \x01(\tR\x06nameRu\x12\x12\n" +
	"\x04code\x18\x02 \x01(\x03R\x04code\x12\x17\n" +
	"\aname_kz\x18\x03 \x01(\tR\x06nameKz\x12\x1f\n" +
	"\vchange_date\x18\x04 \x01(\tR\n" +
	"changeDate\")\n" +
	"\x15GetLiveness3DPhotoReq\x12\x10\n" +
	"\x03iin\x18\x01 \x01(\tR\x03iin\"D\n" +
	"\x16GetLiveness3DPhotoResp\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\x12\x12\n" +
	"\x04file\x18\x02 \x01(\fR\x04file\")\n" +
	"\x15GetLiveness3DVideoReq\x12\x10\n" +
	"\x03iin\x18\x01 \x01(\tR\x03iin\"D\n" +
	"\x16GetLiveness3DVideoResp\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\x12\x12\n" +
	"\x04file\x18\x02 \x01(\fR\x04file\"&\n" +
	"\x12GetPersonalDataReq\x12\x10\n" +
	"\x03iin\x18\x01 \x01(\tR\x03iin\"\xea\x03\n" +
	"\x13GetPersonalDataResp\x128\n" +
	"\vcitizenship\x18\x01 \x01(\v2\x16.btsBridge.CitizenshipR\vcitizenship\x12)\n" +
	"\x06gender\x18\x02 \x01(\v2\x11.btsBridge.GenderR\x06gender\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x1e\n" +
	"\n" +
	"patronymic\x18\x04 \x01(\tR\n" +
	"patronymic\x12\x18\n" +
	"\asurname\x18\x05 \x01(\tR\asurname\x12\x10\n" +
	"\x03iin\x18\x06 \x01(\tR\x03iin\x12\x1d\n" +
	"\n" +
	"birth_date\x18\a \x01(\tR\tbirthDate\x126\n" +
	"\vlife_status\x18\b \x01(\v2\x15.btsBridge.LifeStatusR\n" +
	"lifeStatus\x121\n" +
	"\tdocuments\x18\t \x03(\v2\x13.btsBridge.DocumentR\tdocuments\x126\n" +
	"\vreg_address\x18\n" +
	" \x01(\v2\x15.btsBridge.RegAddressR\n" +
	"regAddress\x126\n" +
	"\vbirth_place\x18\v \x01(\v2\x15.btsBridge.BirthPlaceR\n" +
	"birthPlace\x12\x14\n" +
	"\x05phone\x18\f \x01(\tR\x05phone\"p\n" +
	"\aCountry\x12\x17\n" +
	"\aname_ru\x18\x01 \x01(\tR\x06nameRu\x12\x12\n" +
	"\x04code\x18\x02 \x01(\x03R\x04code\x12\x17\n" +
	"\aname_kz\x18\x03 \x01(\tR\x06nameKz\x12\x1f\n" +
	"\vchange_date\x18\x04 \x01(\tR\n" +
	"changeDate\"q\n" +
	"\bDistrict\x12\x17\n" +
	"\aname_ru\x18\x01 \x01(\tR\x06nameRu\x12\x12\n" +
	"\x04code\x18\x02 \x01(\x03R\x04code\x12\x17\n" +
	"\aname_kz\x18\x03 \x01(\tR\x06nameKz\x12\x1f\n" +
	"\vchange_date\x18\x04 \x01(\tR\n" +
	"changeDate\"o\n" +
	"\x06Region\x12\x17\n" +
	"\aname_ru\x18\x01 \x01(\tR\x06nameRu\x12\x12\n" +
	"\x04code\x18\x02 \x01(\x03R\x04code\x12\x17\n" +
	"\aname_kz\x18\x03 \x01(\tR\x06nameKz\x12\x1f\n" +
	"\vchange_date\x18\x04 \x01(\tR\n" +
	"changeDate\"\xaa\x01\n" +
	"\n" +
	"BirthPlace\x12,\n" +
	"\acountry\x18\x01 \x01(\v2\x12.btsBridge.CountryR\acountry\x12\x12\n" +
	"\x04city\x18\x02 \x01(\tR\x04city\x12/\n" +
	"\bdistrict\x18\x03 \x01(\v2\x13.btsBridge.DistrictR\bdistrict\x12)\n" +
	"\x06region\x18\x04 \x01(\v2\x11.btsBridge.RegionR\x06region\"\xae\x03\n" +
	"\n" +
	"RegAddress\x12,\n" +
	"\acountry\x18\x01 \x01(\v2\x12.btsBridge.CountryR\acountry\x12\x1d\n" +
	"\n" +
	"begin_date\x18\x02 \x01(\tR\tbeginDate\x12\x16\n" +
	"\x06street\x18\x03 \x01(\tR\x06street\x12\x12\n" +
	"\x04flat\x18\x04 \x01(\tR\x04flat\x12/\n" +
	"\bdistrict\x18\x05 \x01(\v2\x13.btsBridge.DistrictR\bdistrict\x12)\n" +
	"\x06region\x18\x06 \x01(\v2\x11.btsBridge.RegionR\x06region\x12\x1a\n" +
	"\bbuilding\x18\a \x01(\tR\bbuilding\x12\x12\n" +
	"\x04city\x18\b \x01(\tR\x04city\x12\x16\n" +
	"\x06corpus\x18\t \x01(\tR\x06corpus\x123\n" +
	"\x06status\x18\n" +
	" \x01(\v2\x1b.btsBridge.RegAddressStatusR\x06status\x125\n" +
	"\n" +
	"invalidity\x18\v \x01(\v2\x15.btsBridge.InvalidityR\n" +
	"invalidity\x12\x17\n" +
	"\aar_code\x18\f \x01(\x03R\x06arCode\"s\n" +
	"\n" +
	"Invalidity\x12\x17\n" +
	"\aname_ru\x18\x01 \x01(\tR\x06nameRu\x12\x12\n" +
	"\x04code\x18\x02 \x01(\x03R\x04code\x12\x17\n" +
	"\aname_kz\x18\x03 \x01(\tR\x06nameKz\x12\x1f\n" +
	"\vchange_date\x18\x04 \x01(\tR\n" +
	"changeDate\"y\n" +
	"\x10RegAddressStatus\x12\x17\n" +
	"\aname_ru\x18\x01 \x01(\tR\x06nameRu\x12\x12\n" +
	"\x04code\x18\x02 \x01(\x03R\x04code\x12\x17\n" +
	"\aname_kz\x18\x03 \x01(\tR\x06nameKz\x12\x1f\n" +
	"\vchange_date\x18\x04 \x01(\tR\n" +
	"changeDate\"u\n" +
	"\fDocumentType\x12\x17\n" +
	"\aname_ru\x18\x01 \x01(\tR\x06nameRu\x12\x12\n" +
	"\x04code\x18\x02 \x01(\tR\x04code\x12\x17\n" +
	"\aname_kz\x18\x03 \x01(\tR\x06nameKz\x12\x1f\n" +
	"\vchange_date\x18\x04 \x01(\tR\n" +
	"changeDate\"\x82\x01\n" +
	"\x19DocumentIssueOrganization\x12\x17\n" +
	"\aname_ru\x18\x01 \x01(\tR\x06nameRu\x12\x12\n" +
	"\x04code\x18\x02 \x01(\tR\x04code\x12\x17\n" +
	"\aname_kz\x18\x03 \x01(\tR\x06nameKz\x12\x1f\n" +
	"\vchange_date\x18\x04 \x01(\tR\n" +
	"changeDate\"w\n" +
	"\x0eDocumentStatus\x12\x17\n" +
	"\aname_ru\x18\x01 \x01(\tR\x06nameRu\x12\x12\n" +
	"\x04code\x18\x02 \x01(\tR\x04code\x12\x17\n" +
	"\aname_kz\x18\x03 \x01(\tR\x06nameKz\x12\x1f\n" +
	"\vchange_date\x18\x04 \x01(\tR\n" +
	"changeDate\"\xfe\x02\n" +
	"\bDocument\x12\x16\n" +
	"\x06number\x18\x01 \x01(\tR\x06number\x12\x1d\n" +
	"\n" +
	"begin_date\x18\x02 \x01(\tR\tbeginDate\x12\x1e\n" +
	"\n" +
	"patronymic\x18\x03 \x01(\tR\n" +
	"patronymic\x12\x19\n" +
	"\bend_date\x18\x04 \x01(\tR\aendDate\x12\x18\n" +
	"\asurname\x18\x05 \x01(\tR\asurname\x12\x12\n" +
	"\x04name\x18\x06 \x01(\tR\x04name\x12+\n" +
	"\x04type\x18\a \x01(\v2\x17.btsBridge.DocumentTypeR\x04type\x12\x1d\n" +
	"\n" +
	"birth_date\x18\b \x01(\tR\tbirthDate\x12S\n" +
	"\x12issue_organization\x18\t \x01(\v2$.btsBridge.DocumentIssueOrganizationR\x11issueOrganization\x121\n" +
	"\x06status\x18\n" +
	" \x01(\v2\x19.btsBridge.DocumentStatusR\x06status\"<\n" +
	"\tDocuments\x12/\n" +
	"\bdocument\x18\x01 \x03(\v2\x13.btsBridge.DocumentR\bdocument\"t\n" +
	"\vCitizenship\x12\x17\n" +
	"\aname_ru\x18\x01 \x01(\tR\x06nameRu\x12\x12\n" +
	"\x04code\x18\x02 \x01(\x03R\x04code\x12\x17\n" +
	"\aname_kz\x18\x03 \x01(\tR\x06nameKz\x12\x1f\n" +
	"\vchange_date\x18\x04 \x01(\tR\n" +
	"changeDate\"s\n" +
	"\n" +
	"LifeStatus\x12\x17\n" +
	"\aname_ru\x18\x01 \x01(\tR\x06nameRu\x12\x12\n" +
	"\x04code\x18\x02 \x01(\x03R\x04code\x12\x17\n" +
	"\aname_kz\x18\x03 \x01(\tR\x06nameKz\x12\x1f\n" +
	"\vchange_date\x18\x04 \x01(\tR\n" +
	"changeDate\"t\n" +
	"\vNationality\x12\x17\n" +
	"\aname_ru\x18\x01 \x01(\tR\x06nameRu\x12\x12\n" +
	"\x04code\x18\x02 \x01(\tR\x04code\x12\x17\n" +
	"\aname_kz\x18\x03 \x01(\tR\x06nameKz\x12\x1f\n" +
	"\vchange_date\x18\x04 \x01(\tR\n" +
	"changeDate\"\x82\x04\n" +
	"\x06Person\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x1e\n" +
	"\n" +
	"patronymic\x18\x02 \x01(\tR\n" +
	"patronymic\x12\x18\n" +
	"\asurname\x18\x03 \x01(\tR\asurname\x12\x10\n" +
	"\x03iin\x18\x04 \x01(\tR\x03iin\x12\x1d\n" +
	"\n" +
	"birth_date\x18\x05 \x01(\tR\tbirthDate\x126\n" +
	"\vbirth_place\x18\x06 \x01(\v2\x15.btsBridge.BirthPlaceR\n" +
	"birthPlace\x126\n" +
	"\vreg_address\x18\a \x01(\v2\x15.btsBridge.RegAddressR\n" +
	"regAddress\x122\n" +
	"\tdocuments\x18\b \x01(\v2\x14.btsBridge.DocumentsR\tdocuments\x128\n" +
	"\vcitizenship\x18\t \x01(\v2\x16.btsBridge.CitizenshipR\vcitizenship\x126\n" +
	"\vlife_status\x18\n" +
	" \x01(\v2\x15.btsBridge.LifeStatusR\n" +
	"lifeStatus\x12)\n" +
	"\x06gender\x18\v \x01(\v2\x11.btsBridge.GenderR\x06gender\x128\n" +
	"\vnationality\x18\f \x01(\v2\x16.btsBridge.NationalityR\vnationality\"-\n" +
	"\x15CreateTrustedPhoneReq\x12\x14\n" +
	"\x05phone\x18\x01 \x01(\tR\x05phone\"O\n" +
	"\x16CreateTrustedPhoneResp\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\tR\trequestId\x12\x16\n" +
	"\x06secret\x18\x02 \x01(\tR\x06secret\"\x82\x01\n" +
	"\x14UploadFileForSignReq\x12\x1e\n" +
	"\n" +
	"documentID\x18\x01 \x01(\tR\n" +
	"documentID\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x14\n" +
	"\x05bytes\x18\x03 \x01(\fR\x05bytes\x12\x17\n" +
	"\x04link\x18\x04 \x01(\tH\x00R\x04link\x88\x01\x01B\a\n" +
	"\x05_link\"W\n" +
	"\x15UploadFileForSignResp\x12\x1e\n" +
	"\n" +
	"documentID\x18\x01 \x01(\tR\n" +
	"documentID\x12\x1e\n" +
	"\n" +
	"signableID\x18\x02 \x01(\tR\n" +
	"signableID\"%\n" +
	"\x11GetSignedFilesReq\x12\x10\n" +
	"\x03iin\x18\x01 \x01(\tR\x03iin\"J\n" +
	"\x0eSignedDocument\x12\x1e\n" +
	"\n" +
	"documentID\x18\x01 \x01(\tR\n" +
	"documentID\x12\x18\n" +
	"\adocType\x18\x02 \x01(\tR\adocType\"M\n" +
	"\x12GetSignedFilesResp\x127\n" +
	"\tdocuments\x18\x01 \x03(\v2\x19.btsBridge.SignedDocumentR\tdocuments\"5\n" +
	"!GetIdentificationSessionReportReq\x12\x10\n" +
	"\x03iin\x18\x01 \x01(\tR\x03iin\"f\n" +
	"\"GetIdentificationSessionReportResp\x12,\n" +
	"\x11sessionDocumentID\x18\x01 \x01(\tR\x11sessionDocumentID\x12\x12\n" +
	"\x04file\x18\x02 \x01(\fR\x04file\"U\n" +
	"\x1fGetIdentificationSessionListReq\x12\x1a\n" +
	"\bdataFrom\x18\x01 \x01(\tR\bdataFrom\x12\x16\n" +
	"\x06dataTo\x18\x02 \x01(\tR\x06dataTo\"[\n" +
	" GetIdentificationSessionListResp\x127\n" +
	"\x04list\x18\x01 \x03(\v2#.btsBridge.GetIdentificationSessionR\x04list\"\x8e\x01\n" +
	"\x18GetIdentificationSession\x12,\n" +
	"\x11sessionDocumentID\x18\x01 \x01(\tR\x11sessionDocumentID\x12\x10\n" +
	"\x03iin\x18\x02 \x01(\tR\x03iin\x12\x14\n" +
	"\x05phone\x18\x03 \x01(\tR\x05phone\x12\x1c\n" +
	"\tcreatedAt\x18\x04 \x01(\tR\tcreatedAt\"Z\n" +
	"*GetIdentificationSessionReportSessionIDReq\x12,\n" +
	"\x11sessionDocumentID\x18\x01 \x01(\tR\x11sessionDocumentID\"A\n" +
	"+GetIdentificationSessionReportSessionIDResp\x12\x12\n" +
	"\x04file\x18\x01 \x01(\fR\x04file2\x90\n" +
	"\n" +
	"\tBtsbridge\x12D\n" +
	"\vHealthCheck\x12\x19.btsBridge.HealthCheckReq\x1a\x1a.btsBridge.HealthCheckResp\x12J\n" +
	"\rRequestVerify\x12\x1b.btsBridge.RequestVerifyReq\x1a\x1c.btsBridge.RequestVerifyResp\x12Y\n" +
	"\x12GetLiveness3DPhoto\x12 .btsBridge.GetLiveness3DPhotoReq\x1a!.btsBridge.GetLiveness3DPhotoResp\x12Y\n" +
	"\x12GetLiveness3DVideo\x12 .btsBridge.GetLiveness3DVideoReq\x1a!.btsBridge.GetLiveness3DVideoResp\x12P\n" +
	"\x0fGetPersonalData\x12\x1d.btsBridge.GetPersonalDataReq\x1a\x1e.btsBridge.GetPersonalDataResp\x12t\n" +
	"\x1bGetLastBtsVerifyResultByIin\x12).btsBridge.GetLastBtsVerifyResultByIinReq\x1a*.btsBridge.GetLastBtsVerifyResultByIinResp\x12\\\n" +
	"\x13RequestPersonalData\x12!.btsBridge.RequestPersonalDataReq\x1a\".btsBridge.RequestPersonalDataResp\x12Y\n" +
	"\x12CreateTrustedPhone\x12 .btsBridge.CreateTrustedPhoneReq\x1a!.btsBridge.CreateTrustedPhoneResp\x12V\n" +
	"\x11UploadFileForSign\x12\x1f.btsBridge.UploadFileForSignReq\x1a .btsBridge.UploadFileForSignResp\x12M\n" +
	"\x0eGetSignedFiles\x12\x1c.btsBridge.GetSignedFilesReq\x1a\x1d.btsBridge.GetSignedFilesResp\x12}\n" +
	"\x1eGetIdentificationSessionReport\x12,.btsBridge.GetIdentificationSessionReportReq\x1a-.btsBridge.GetIdentificationSessionReportResp\x12w\n" +
	"\x1cGetIdentificationSessionList\x12*.btsBridge.GetIdentificationSessionListReq\x1a+.btsBridge.GetIdentificationSessionListResp\x12\x9a\x01\n" +
	")GetIdentificationSessionReportBySessionID\x125.btsBridge.GetIdentificationSessionReportSessionIDReq\x1a6.btsBridge.GetIdentificationSessionReportSessionIDRespB\x18Z\x16specs/proto/bts-bridgeb\x06proto3"

var (
	file_specs_proto_bts_bridge_bts_bridge_proto_rawDescOnce sync.Once
	file_specs_proto_bts_bridge_bts_bridge_proto_rawDescData []byte
)

func file_specs_proto_bts_bridge_bts_bridge_proto_rawDescGZIP() []byte {
	file_specs_proto_bts_bridge_bts_bridge_proto_rawDescOnce.Do(func() {
		file_specs_proto_bts_bridge_bts_bridge_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_specs_proto_bts_bridge_bts_bridge_proto_rawDesc), len(file_specs_proto_bts_bridge_bts_bridge_proto_rawDesc)))
	})
	return file_specs_proto_bts_bridge_bts_bridge_proto_rawDescData
}

var file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes = make([]protoimpl.MessageInfo, 45)
var file_specs_proto_bts_bridge_bts_bridge_proto_goTypes = []any{
	(*GetLastBtsVerifyResultByIinReq)(nil),              // 0: btsBridge.GetLastBtsVerifyResultByIinReq
	(*GetLastBtsVerifyResultByIinResp)(nil),             // 1: btsBridge.GetLastBtsVerifyResultByIinResp
	(*HealthCheckReq)(nil),                              // 2: btsBridge.HealthCheckReq
	(*HealthCheckResp)(nil),                             // 3: btsBridge.HealthCheckResp
	(*RequestVerifyReq)(nil),                            // 4: btsBridge.RequestVerifyReq
	(*RequestVerifyResp)(nil),                           // 5: btsBridge.RequestVerifyResp
	(*RequestPersonalDataReq)(nil),                      // 6: btsBridge.RequestPersonalDataReq
	(*RequestPersonalDataResp)(nil),                     // 7: btsBridge.RequestPersonalDataResp
	(*Gender)(nil),                                      // 8: btsBridge.Gender
	(*GetLiveness3DPhotoReq)(nil),                       // 9: btsBridge.GetLiveness3DPhotoReq
	(*GetLiveness3DPhotoResp)(nil),                      // 10: btsBridge.GetLiveness3DPhotoResp
	(*GetLiveness3DVideoReq)(nil),                       // 11: btsBridge.GetLiveness3DVideoReq
	(*GetLiveness3DVideoResp)(nil),                      // 12: btsBridge.GetLiveness3DVideoResp
	(*GetPersonalDataReq)(nil),                          // 13: btsBridge.GetPersonalDataReq
	(*GetPersonalDataResp)(nil),                         // 14: btsBridge.GetPersonalDataResp
	(*Country)(nil),                                     // 15: btsBridge.Country
	(*District)(nil),                                    // 16: btsBridge.District
	(*Region)(nil),                                      // 17: btsBridge.Region
	(*BirthPlace)(nil),                                  // 18: btsBridge.BirthPlace
	(*RegAddress)(nil),                                  // 19: btsBridge.RegAddress
	(*Invalidity)(nil),                                  // 20: btsBridge.Invalidity
	(*RegAddressStatus)(nil),                            // 21: btsBridge.RegAddressStatus
	(*DocumentType)(nil),                                // 22: btsBridge.DocumentType
	(*DocumentIssueOrganization)(nil),                   // 23: btsBridge.DocumentIssueOrganization
	(*DocumentStatus)(nil),                              // 24: btsBridge.DocumentStatus
	(*Document)(nil),                                    // 25: btsBridge.Document
	(*Documents)(nil),                                   // 26: btsBridge.Documents
	(*Citizenship)(nil),                                 // 27: btsBridge.Citizenship
	(*LifeStatus)(nil),                                  // 28: btsBridge.LifeStatus
	(*Nationality)(nil),                                 // 29: btsBridge.Nationality
	(*Person)(nil),                                      // 30: btsBridge.Person
	(*CreateTrustedPhoneReq)(nil),                       // 31: btsBridge.CreateTrustedPhoneReq
	(*CreateTrustedPhoneResp)(nil),                      // 32: btsBridge.CreateTrustedPhoneResp
	(*UploadFileForSignReq)(nil),                        // 33: btsBridge.UploadFileForSignReq
	(*UploadFileForSignResp)(nil),                       // 34: btsBridge.UploadFileForSignResp
	(*GetSignedFilesReq)(nil),                           // 35: btsBridge.GetSignedFilesReq
	(*SignedDocument)(nil),                              // 36: btsBridge.SignedDocument
	(*GetSignedFilesResp)(nil),                          // 37: btsBridge.GetSignedFilesResp
	(*GetIdentificationSessionReportReq)(nil),           // 38: btsBridge.GetIdentificationSessionReportReq
	(*GetIdentificationSessionReportResp)(nil),          // 39: btsBridge.GetIdentificationSessionReportResp
	(*GetIdentificationSessionListReq)(nil),             // 40: btsBridge.GetIdentificationSessionListReq
	(*GetIdentificationSessionListResp)(nil),            // 41: btsBridge.GetIdentificationSessionListResp
	(*GetIdentificationSession)(nil),                    // 42: btsBridge.GetIdentificationSession
	(*GetIdentificationSessionReportSessionIDReq)(nil),  // 43: btsBridge.GetIdentificationSessionReportSessionIDReq
	(*GetIdentificationSessionReportSessionIDResp)(nil), // 44: btsBridge.GetIdentificationSessionReportSessionIDResp
}
var file_specs_proto_bts_bridge_bts_bridge_proto_depIdxs = []int32{
	27, // 0: btsBridge.RequestPersonalDataResp.citizenship:type_name -> btsBridge.Citizenship
	8,  // 1: btsBridge.RequestPersonalDataResp.gender:type_name -> btsBridge.Gender
	25, // 2: btsBridge.RequestPersonalDataResp.documents:type_name -> btsBridge.Document
	19, // 3: btsBridge.RequestPersonalDataResp.reg_address:type_name -> btsBridge.RegAddress
	18, // 4: btsBridge.RequestPersonalDataResp.birth_place:type_name -> btsBridge.BirthPlace
	28, // 5: btsBridge.RequestPersonalDataResp.life_status:type_name -> btsBridge.LifeStatus
	27, // 6: btsBridge.GetPersonalDataResp.citizenship:type_name -> btsBridge.Citizenship
	8,  // 7: btsBridge.GetPersonalDataResp.gender:type_name -> btsBridge.Gender
	28, // 8: btsBridge.GetPersonalDataResp.life_status:type_name -> btsBridge.LifeStatus
	25, // 9: btsBridge.GetPersonalDataResp.documents:type_name -> btsBridge.Document
	19, // 10: btsBridge.GetPersonalDataResp.reg_address:type_name -> btsBridge.RegAddress
	18, // 11: btsBridge.GetPersonalDataResp.birth_place:type_name -> btsBridge.BirthPlace
	15, // 12: btsBridge.BirthPlace.country:type_name -> btsBridge.Country
	16, // 13: btsBridge.BirthPlace.district:type_name -> btsBridge.District
	17, // 14: btsBridge.BirthPlace.region:type_name -> btsBridge.Region
	15, // 15: btsBridge.RegAddress.country:type_name -> btsBridge.Country
	16, // 16: btsBridge.RegAddress.district:type_name -> btsBridge.District
	17, // 17: btsBridge.RegAddress.region:type_name -> btsBridge.Region
	21, // 18: btsBridge.RegAddress.status:type_name -> btsBridge.RegAddressStatus
	20, // 19: btsBridge.RegAddress.invalidity:type_name -> btsBridge.Invalidity
	22, // 20: btsBridge.Document.type:type_name -> btsBridge.DocumentType
	23, // 21: btsBridge.Document.issue_organization:type_name -> btsBridge.DocumentIssueOrganization
	24, // 22: btsBridge.Document.status:type_name -> btsBridge.DocumentStatus
	25, // 23: btsBridge.Documents.document:type_name -> btsBridge.Document
	18, // 24: btsBridge.Person.birth_place:type_name -> btsBridge.BirthPlace
	19, // 25: btsBridge.Person.reg_address:type_name -> btsBridge.RegAddress
	26, // 26: btsBridge.Person.documents:type_name -> btsBridge.Documents
	27, // 27: btsBridge.Person.citizenship:type_name -> btsBridge.Citizenship
	28, // 28: btsBridge.Person.life_status:type_name -> btsBridge.LifeStatus
	8,  // 29: btsBridge.Person.gender:type_name -> btsBridge.Gender
	29, // 30: btsBridge.Person.nationality:type_name -> btsBridge.Nationality
	36, // 31: btsBridge.GetSignedFilesResp.documents:type_name -> btsBridge.SignedDocument
	42, // 32: btsBridge.GetIdentificationSessionListResp.list:type_name -> btsBridge.GetIdentificationSession
	2,  // 33: btsBridge.Btsbridge.HealthCheck:input_type -> btsBridge.HealthCheckReq
	4,  // 34: btsBridge.Btsbridge.RequestVerify:input_type -> btsBridge.RequestVerifyReq
	9,  // 35: btsBridge.Btsbridge.GetLiveness3DPhoto:input_type -> btsBridge.GetLiveness3DPhotoReq
	11, // 36: btsBridge.Btsbridge.GetLiveness3DVideo:input_type -> btsBridge.GetLiveness3DVideoReq
	13, // 37: btsBridge.Btsbridge.GetPersonalData:input_type -> btsBridge.GetPersonalDataReq
	0,  // 38: btsBridge.Btsbridge.GetLastBtsVerifyResultByIin:input_type -> btsBridge.GetLastBtsVerifyResultByIinReq
	6,  // 39: btsBridge.Btsbridge.RequestPersonalData:input_type -> btsBridge.RequestPersonalDataReq
	31, // 40: btsBridge.Btsbridge.CreateTrustedPhone:input_type -> btsBridge.CreateTrustedPhoneReq
	33, // 41: btsBridge.Btsbridge.UploadFileForSign:input_type -> btsBridge.UploadFileForSignReq
	35, // 42: btsBridge.Btsbridge.GetSignedFiles:input_type -> btsBridge.GetSignedFilesReq
	38, // 43: btsBridge.Btsbridge.GetIdentificationSessionReport:input_type -> btsBridge.GetIdentificationSessionReportReq
	40, // 44: btsBridge.Btsbridge.GetIdentificationSessionList:input_type -> btsBridge.GetIdentificationSessionListReq
	43, // 45: btsBridge.Btsbridge.GetIdentificationSessionReportBySessionID:input_type -> btsBridge.GetIdentificationSessionReportSessionIDReq
	3,  // 46: btsBridge.Btsbridge.HealthCheck:output_type -> btsBridge.HealthCheckResp
	5,  // 47: btsBridge.Btsbridge.RequestVerify:output_type -> btsBridge.RequestVerifyResp
	10, // 48: btsBridge.Btsbridge.GetLiveness3DPhoto:output_type -> btsBridge.GetLiveness3DPhotoResp
	12, // 49: btsBridge.Btsbridge.GetLiveness3DVideo:output_type -> btsBridge.GetLiveness3DVideoResp
	14, // 50: btsBridge.Btsbridge.GetPersonalData:output_type -> btsBridge.GetPersonalDataResp
	1,  // 51: btsBridge.Btsbridge.GetLastBtsVerifyResultByIin:output_type -> btsBridge.GetLastBtsVerifyResultByIinResp
	7,  // 52: btsBridge.Btsbridge.RequestPersonalData:output_type -> btsBridge.RequestPersonalDataResp
	32, // 53: btsBridge.Btsbridge.CreateTrustedPhone:output_type -> btsBridge.CreateTrustedPhoneResp
	34, // 54: btsBridge.Btsbridge.UploadFileForSign:output_type -> btsBridge.UploadFileForSignResp
	37, // 55: btsBridge.Btsbridge.GetSignedFiles:output_type -> btsBridge.GetSignedFilesResp
	39, // 56: btsBridge.Btsbridge.GetIdentificationSessionReport:output_type -> btsBridge.GetIdentificationSessionReportResp
	41, // 57: btsBridge.Btsbridge.GetIdentificationSessionList:output_type -> btsBridge.GetIdentificationSessionListResp
	44, // 58: btsBridge.Btsbridge.GetIdentificationSessionReportBySessionID:output_type -> btsBridge.GetIdentificationSessionReportSessionIDResp
	46, // [46:59] is the sub-list for method output_type
	33, // [33:46] is the sub-list for method input_type
	33, // [33:33] is the sub-list for extension type_name
	33, // [33:33] is the sub-list for extension extendee
	0,  // [0:33] is the sub-list for field type_name
}

func init() { file_specs_proto_bts_bridge_bts_bridge_proto_init() }
func file_specs_proto_bts_bridge_bts_bridge_proto_init() {
	if File_specs_proto_bts_bridge_bts_bridge_proto != nil {
		return
	}
	file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes[33].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_specs_proto_bts_bridge_bts_bridge_proto_rawDesc), len(file_specs_proto_bts_bridge_bts_bridge_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   45,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_specs_proto_bts_bridge_bts_bridge_proto_goTypes,
		DependencyIndexes: file_specs_proto_bts_bridge_bts_bridge_proto_depIdxs,
		MessageInfos:      file_specs_proto_bts_bridge_bts_bridge_proto_msgTypes,
	}.Build()
	File_specs_proto_bts_bridge_bts_bridge_proto = out.File
	file_specs_proto_bts_bridge_bts_bridge_proto_goTypes = nil
	file_specs_proto_bts_bridge_bts_bridge_proto_depIdxs = nil
}
