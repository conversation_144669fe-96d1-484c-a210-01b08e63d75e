// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.32.1
// source: specs/proto/bts-bridge/bts-bridge.proto

package bts_bridge

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Btsbridge_HealthCheck_FullMethodName                               = "/btsBridge.Btsbridge/HealthCheck"
	Btsbridge_RequestVerify_FullMethodName                             = "/btsBridge.Btsbridge/RequestVerify"
	Btsbridge_GetLiveness3DPhoto_FullMethodName                        = "/btsBridge.Btsbridge/GetLiveness3DPhoto"
	Btsbridge_GetLiveness3DVideo_FullMethodName                        = "/btsBridge.Btsbridge/GetLiveness3DVideo"
	Btsbridge_GetPersonalData_FullMethodName                           = "/btsBridge.Btsbridge/GetPersonalData"
	Btsbridge_GetLastBtsVerifyResultByIin_FullMethodName               = "/btsBridge.Btsbridge/GetLastBtsVerifyResultByIin"
	Btsbridge_RequestPersonalData_FullMethodName                       = "/btsBridge.Btsbridge/RequestPersonalData"
	Btsbridge_CreateTrustedPhone_FullMethodName                        = "/btsBridge.Btsbridge/CreateTrustedPhone"
	Btsbridge_UploadFileForSign_FullMethodName                         = "/btsBridge.Btsbridge/UploadFileForSign"
	Btsbridge_GetSignedFiles_FullMethodName                            = "/btsBridge.Btsbridge/GetSignedFiles"
	Btsbridge_GetIdentificationSessionReport_FullMethodName            = "/btsBridge.Btsbridge/GetIdentificationSessionReport"
	Btsbridge_GetIdentificationSessionList_FullMethodName              = "/btsBridge.Btsbridge/GetIdentificationSessionList"
	Btsbridge_GetIdentificationSessionReportBySessionID_FullMethodName = "/btsBridge.Btsbridge/GetIdentificationSessionReportBySessionID"
)

// BtsbridgeClient is the client API for Btsbridge service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BtsbridgeClient interface {
	HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error)
	RequestVerify(ctx context.Context, in *RequestVerifyReq, opts ...grpc.CallOption) (*RequestVerifyResp, error)
	GetLiveness3DPhoto(ctx context.Context, in *GetLiveness3DPhotoReq, opts ...grpc.CallOption) (*GetLiveness3DPhotoResp, error)
	GetLiveness3DVideo(ctx context.Context, in *GetLiveness3DVideoReq, opts ...grpc.CallOption) (*GetLiveness3DVideoResp, error)
	GetPersonalData(ctx context.Context, in *GetPersonalDataReq, opts ...grpc.CallOption) (*GetPersonalDataResp, error)
	GetLastBtsVerifyResultByIin(ctx context.Context, in *GetLastBtsVerifyResultByIinReq, opts ...grpc.CallOption) (*GetLastBtsVerifyResultByIinResp, error)
	RequestPersonalData(ctx context.Context, in *RequestPersonalDataReq, opts ...grpc.CallOption) (*RequestPersonalDataResp, error)
	CreateTrustedPhone(ctx context.Context, in *CreateTrustedPhoneReq, opts ...grpc.CallOption) (*CreateTrustedPhoneResp, error)
	UploadFileForSign(ctx context.Context, in *UploadFileForSignReq, opts ...grpc.CallOption) (*UploadFileForSignResp, error)
	GetSignedFiles(ctx context.Context, in *GetSignedFilesReq, opts ...grpc.CallOption) (*GetSignedFilesResp, error)
	GetIdentificationSessionReport(ctx context.Context, in *GetIdentificationSessionReportReq, opts ...grpc.CallOption) (*GetIdentificationSessionReportResp, error)
	GetIdentificationSessionList(ctx context.Context, in *GetIdentificationSessionListReq, opts ...grpc.CallOption) (*GetIdentificationSessionListResp, error)
	GetIdentificationSessionReportBySessionID(ctx context.Context, in *GetIdentificationSessionReportSessionIDReq, opts ...grpc.CallOption) (*GetIdentificationSessionReportSessionIDResp, error)
}

type btsbridgeClient struct {
	cc grpc.ClientConnInterface
}

func NewBtsbridgeClient(cc grpc.ClientConnInterface) BtsbridgeClient {
	return &btsbridgeClient{cc}
}

func (c *btsbridgeClient) HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResp)
	err := c.cc.Invoke(ctx, Btsbridge_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *btsbridgeClient) RequestVerify(ctx context.Context, in *RequestVerifyReq, opts ...grpc.CallOption) (*RequestVerifyResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RequestVerifyResp)
	err := c.cc.Invoke(ctx, Btsbridge_RequestVerify_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *btsbridgeClient) GetLiveness3DPhoto(ctx context.Context, in *GetLiveness3DPhotoReq, opts ...grpc.CallOption) (*GetLiveness3DPhotoResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLiveness3DPhotoResp)
	err := c.cc.Invoke(ctx, Btsbridge_GetLiveness3DPhoto_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *btsbridgeClient) GetLiveness3DVideo(ctx context.Context, in *GetLiveness3DVideoReq, opts ...grpc.CallOption) (*GetLiveness3DVideoResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLiveness3DVideoResp)
	err := c.cc.Invoke(ctx, Btsbridge_GetLiveness3DVideo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *btsbridgeClient) GetPersonalData(ctx context.Context, in *GetPersonalDataReq, opts ...grpc.CallOption) (*GetPersonalDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPersonalDataResp)
	err := c.cc.Invoke(ctx, Btsbridge_GetPersonalData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *btsbridgeClient) GetLastBtsVerifyResultByIin(ctx context.Context, in *GetLastBtsVerifyResultByIinReq, opts ...grpc.CallOption) (*GetLastBtsVerifyResultByIinResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLastBtsVerifyResultByIinResp)
	err := c.cc.Invoke(ctx, Btsbridge_GetLastBtsVerifyResultByIin_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *btsbridgeClient) RequestPersonalData(ctx context.Context, in *RequestPersonalDataReq, opts ...grpc.CallOption) (*RequestPersonalDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RequestPersonalDataResp)
	err := c.cc.Invoke(ctx, Btsbridge_RequestPersonalData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *btsbridgeClient) CreateTrustedPhone(ctx context.Context, in *CreateTrustedPhoneReq, opts ...grpc.CallOption) (*CreateTrustedPhoneResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateTrustedPhoneResp)
	err := c.cc.Invoke(ctx, Btsbridge_CreateTrustedPhone_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *btsbridgeClient) UploadFileForSign(ctx context.Context, in *UploadFileForSignReq, opts ...grpc.CallOption) (*UploadFileForSignResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UploadFileForSignResp)
	err := c.cc.Invoke(ctx, Btsbridge_UploadFileForSign_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *btsbridgeClient) GetSignedFiles(ctx context.Context, in *GetSignedFilesReq, opts ...grpc.CallOption) (*GetSignedFilesResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSignedFilesResp)
	err := c.cc.Invoke(ctx, Btsbridge_GetSignedFiles_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *btsbridgeClient) GetIdentificationSessionReport(ctx context.Context, in *GetIdentificationSessionReportReq, opts ...grpc.CallOption) (*GetIdentificationSessionReportResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetIdentificationSessionReportResp)
	err := c.cc.Invoke(ctx, Btsbridge_GetIdentificationSessionReport_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *btsbridgeClient) GetIdentificationSessionList(ctx context.Context, in *GetIdentificationSessionListReq, opts ...grpc.CallOption) (*GetIdentificationSessionListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetIdentificationSessionListResp)
	err := c.cc.Invoke(ctx, Btsbridge_GetIdentificationSessionList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *btsbridgeClient) GetIdentificationSessionReportBySessionID(ctx context.Context, in *GetIdentificationSessionReportSessionIDReq, opts ...grpc.CallOption) (*GetIdentificationSessionReportSessionIDResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetIdentificationSessionReportSessionIDResp)
	err := c.cc.Invoke(ctx, Btsbridge_GetIdentificationSessionReportBySessionID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BtsbridgeServer is the server API for Btsbridge service.
// All implementations must embed UnimplementedBtsbridgeServer
// for forward compatibility.
type BtsbridgeServer interface {
	HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error)
	RequestVerify(context.Context, *RequestVerifyReq) (*RequestVerifyResp, error)
	GetLiveness3DPhoto(context.Context, *GetLiveness3DPhotoReq) (*GetLiveness3DPhotoResp, error)
	GetLiveness3DVideo(context.Context, *GetLiveness3DVideoReq) (*GetLiveness3DVideoResp, error)
	GetPersonalData(context.Context, *GetPersonalDataReq) (*GetPersonalDataResp, error)
	GetLastBtsVerifyResultByIin(context.Context, *GetLastBtsVerifyResultByIinReq) (*GetLastBtsVerifyResultByIinResp, error)
	RequestPersonalData(context.Context, *RequestPersonalDataReq) (*RequestPersonalDataResp, error)
	CreateTrustedPhone(context.Context, *CreateTrustedPhoneReq) (*CreateTrustedPhoneResp, error)
	UploadFileForSign(context.Context, *UploadFileForSignReq) (*UploadFileForSignResp, error)
	GetSignedFiles(context.Context, *GetSignedFilesReq) (*GetSignedFilesResp, error)
	GetIdentificationSessionReport(context.Context, *GetIdentificationSessionReportReq) (*GetIdentificationSessionReportResp, error)
	GetIdentificationSessionList(context.Context, *GetIdentificationSessionListReq) (*GetIdentificationSessionListResp, error)
	GetIdentificationSessionReportBySessionID(context.Context, *GetIdentificationSessionReportSessionIDReq) (*GetIdentificationSessionReportSessionIDResp, error)
	mustEmbedUnimplementedBtsbridgeServer()
}

// UnimplementedBtsbridgeServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBtsbridgeServer struct{}

func (UnimplementedBtsbridgeServer) HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedBtsbridgeServer) RequestVerify(context.Context, *RequestVerifyReq) (*RequestVerifyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RequestVerify not implemented")
}
func (UnimplementedBtsbridgeServer) GetLiveness3DPhoto(context.Context, *GetLiveness3DPhotoReq) (*GetLiveness3DPhotoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLiveness3DPhoto not implemented")
}
func (UnimplementedBtsbridgeServer) GetLiveness3DVideo(context.Context, *GetLiveness3DVideoReq) (*GetLiveness3DVideoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLiveness3DVideo not implemented")
}
func (UnimplementedBtsbridgeServer) GetPersonalData(context.Context, *GetPersonalDataReq) (*GetPersonalDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPersonalData not implemented")
}
func (UnimplementedBtsbridgeServer) GetLastBtsVerifyResultByIin(context.Context, *GetLastBtsVerifyResultByIinReq) (*GetLastBtsVerifyResultByIinResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLastBtsVerifyResultByIin not implemented")
}
func (UnimplementedBtsbridgeServer) RequestPersonalData(context.Context, *RequestPersonalDataReq) (*RequestPersonalDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RequestPersonalData not implemented")
}
func (UnimplementedBtsbridgeServer) CreateTrustedPhone(context.Context, *CreateTrustedPhoneReq) (*CreateTrustedPhoneResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTrustedPhone not implemented")
}
func (UnimplementedBtsbridgeServer) UploadFileForSign(context.Context, *UploadFileForSignReq) (*UploadFileForSignResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadFileForSign not implemented")
}
func (UnimplementedBtsbridgeServer) GetSignedFiles(context.Context, *GetSignedFilesReq) (*GetSignedFilesResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSignedFiles not implemented")
}
func (UnimplementedBtsbridgeServer) GetIdentificationSessionReport(context.Context, *GetIdentificationSessionReportReq) (*GetIdentificationSessionReportResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIdentificationSessionReport not implemented")
}
func (UnimplementedBtsbridgeServer) GetIdentificationSessionList(context.Context, *GetIdentificationSessionListReq) (*GetIdentificationSessionListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIdentificationSessionList not implemented")
}
func (UnimplementedBtsbridgeServer) GetIdentificationSessionReportBySessionID(context.Context, *GetIdentificationSessionReportSessionIDReq) (*GetIdentificationSessionReportSessionIDResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIdentificationSessionReportBySessionID not implemented")
}
func (UnimplementedBtsbridgeServer) mustEmbedUnimplementedBtsbridgeServer() {}
func (UnimplementedBtsbridgeServer) testEmbeddedByValue()                   {}

// UnsafeBtsbridgeServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BtsbridgeServer will
// result in compilation errors.
type UnsafeBtsbridgeServer interface {
	mustEmbedUnimplementedBtsbridgeServer()
}

func RegisterBtsbridgeServer(s grpc.ServiceRegistrar, srv BtsbridgeServer) {
	// If the following call pancis, it indicates UnimplementedBtsbridgeServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Btsbridge_ServiceDesc, srv)
}

func _Btsbridge_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BtsbridgeServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Btsbridge_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BtsbridgeServer).HealthCheck(ctx, req.(*HealthCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Btsbridge_RequestVerify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RequestVerifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BtsbridgeServer).RequestVerify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Btsbridge_RequestVerify_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BtsbridgeServer).RequestVerify(ctx, req.(*RequestVerifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Btsbridge_GetLiveness3DPhoto_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveness3DPhotoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BtsbridgeServer).GetLiveness3DPhoto(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Btsbridge_GetLiveness3DPhoto_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BtsbridgeServer).GetLiveness3DPhoto(ctx, req.(*GetLiveness3DPhotoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Btsbridge_GetLiveness3DVideo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveness3DVideoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BtsbridgeServer).GetLiveness3DVideo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Btsbridge_GetLiveness3DVideo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BtsbridgeServer).GetLiveness3DVideo(ctx, req.(*GetLiveness3DVideoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Btsbridge_GetPersonalData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPersonalDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BtsbridgeServer).GetPersonalData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Btsbridge_GetPersonalData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BtsbridgeServer).GetPersonalData(ctx, req.(*GetPersonalDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Btsbridge_GetLastBtsVerifyResultByIin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLastBtsVerifyResultByIinReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BtsbridgeServer).GetLastBtsVerifyResultByIin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Btsbridge_GetLastBtsVerifyResultByIin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BtsbridgeServer).GetLastBtsVerifyResultByIin(ctx, req.(*GetLastBtsVerifyResultByIinReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Btsbridge_RequestPersonalData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RequestPersonalDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BtsbridgeServer).RequestPersonalData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Btsbridge_RequestPersonalData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BtsbridgeServer).RequestPersonalData(ctx, req.(*RequestPersonalDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Btsbridge_CreateTrustedPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTrustedPhoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BtsbridgeServer).CreateTrustedPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Btsbridge_CreateTrustedPhone_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BtsbridgeServer).CreateTrustedPhone(ctx, req.(*CreateTrustedPhoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Btsbridge_UploadFileForSign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadFileForSignReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BtsbridgeServer).UploadFileForSign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Btsbridge_UploadFileForSign_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BtsbridgeServer).UploadFileForSign(ctx, req.(*UploadFileForSignReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Btsbridge_GetSignedFiles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSignedFilesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BtsbridgeServer).GetSignedFiles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Btsbridge_GetSignedFiles_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BtsbridgeServer).GetSignedFiles(ctx, req.(*GetSignedFilesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Btsbridge_GetIdentificationSessionReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIdentificationSessionReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BtsbridgeServer).GetIdentificationSessionReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Btsbridge_GetIdentificationSessionReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BtsbridgeServer).GetIdentificationSessionReport(ctx, req.(*GetIdentificationSessionReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Btsbridge_GetIdentificationSessionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIdentificationSessionListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BtsbridgeServer).GetIdentificationSessionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Btsbridge_GetIdentificationSessionList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BtsbridgeServer).GetIdentificationSessionList(ctx, req.(*GetIdentificationSessionListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Btsbridge_GetIdentificationSessionReportBySessionID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIdentificationSessionReportSessionIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BtsbridgeServer).GetIdentificationSessionReportBySessionID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Btsbridge_GetIdentificationSessionReportBySessionID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BtsbridgeServer).GetIdentificationSessionReportBySessionID(ctx, req.(*GetIdentificationSessionReportSessionIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Btsbridge_ServiceDesc is the grpc.ServiceDesc for Btsbridge service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Btsbridge_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "btsBridge.Btsbridge",
	HandlerType: (*BtsbridgeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HealthCheck",
			Handler:    _Btsbridge_HealthCheck_Handler,
		},
		{
			MethodName: "RequestVerify",
			Handler:    _Btsbridge_RequestVerify_Handler,
		},
		{
			MethodName: "GetLiveness3DPhoto",
			Handler:    _Btsbridge_GetLiveness3DPhoto_Handler,
		},
		{
			MethodName: "GetLiveness3DVideo",
			Handler:    _Btsbridge_GetLiveness3DVideo_Handler,
		},
		{
			MethodName: "GetPersonalData",
			Handler:    _Btsbridge_GetPersonalData_Handler,
		},
		{
			MethodName: "GetLastBtsVerifyResultByIin",
			Handler:    _Btsbridge_GetLastBtsVerifyResultByIin_Handler,
		},
		{
			MethodName: "RequestPersonalData",
			Handler:    _Btsbridge_RequestPersonalData_Handler,
		},
		{
			MethodName: "CreateTrustedPhone",
			Handler:    _Btsbridge_CreateTrustedPhone_Handler,
		},
		{
			MethodName: "UploadFileForSign",
			Handler:    _Btsbridge_UploadFileForSign_Handler,
		},
		{
			MethodName: "GetSignedFiles",
			Handler:    _Btsbridge_GetSignedFiles_Handler,
		},
		{
			MethodName: "GetIdentificationSessionReport",
			Handler:    _Btsbridge_GetIdentificationSessionReport_Handler,
		},
		{
			MethodName: "GetIdentificationSessionList",
			Handler:    _Btsbridge_GetIdentificationSessionList_Handler,
		},
		{
			MethodName: "GetIdentificationSessionReportBySessionID",
			Handler:    _Btsbridge_GetIdentificationSessionReportBySessionID_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "specs/proto/bts-bridge/bts-bridge.proto",
}
