// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v6.32.1
// source: specs/proto/sms-bridge/sms-bridge.proto

package sms_bridge

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HealthCheckReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckReq) Reset() {
	*x = HealthCheckReq{}
	mi := &file_specs_proto_sms_bridge_sms_bridge_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckReq) ProtoMessage() {}

func (x *HealthCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_sms_bridge_sms_bridge_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckReq.ProtoReflect.Descriptor instead.
func (*HealthCheckReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_sms_bridge_sms_bridge_proto_rawDescGZIP(), []int{0}
}

type HealthCheckResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckResp) Reset() {
	*x = HealthCheckResp{}
	mi := &file_specs_proto_sms_bridge_sms_bridge_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResp) ProtoMessage() {}

func (x *HealthCheckResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_sms_bridge_sms_bridge_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResp.ProtoReflect.Descriptor instead.
func (*HealthCheckResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_sms_bridge_sms_bridge_proto_rawDescGZIP(), []int{1}
}

func (x *HealthCheckResp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type SendSmsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PhoneNumber   string                 `protobuf:"bytes,1,opt,name=phoneNumber,proto3" json:"phoneNumber,omitempty"`
	Text          string                 `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	DateBeg       *string                `protobuf:"bytes,3,opt,name=dateBeg,proto3,oneof" json:"dateBeg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendSmsReq) Reset() {
	*x = SendSmsReq{}
	mi := &file_specs_proto_sms_bridge_sms_bridge_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendSmsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSmsReq) ProtoMessage() {}

func (x *SendSmsReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_sms_bridge_sms_bridge_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSmsReq.ProtoReflect.Descriptor instead.
func (*SendSmsReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_sms_bridge_sms_bridge_proto_rawDescGZIP(), []int{2}
}

func (x *SendSmsReq) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *SendSmsReq) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *SendSmsReq) GetDateBeg() string {
	if x != nil && x.DateBeg != nil {
		return *x.DateBeg
	}
	return ""
}

type SendSmsResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	SmsID         string                 `protobuf:"bytes,2,opt,name=SmsID,proto3" json:"SmsID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendSmsResp) Reset() {
	*x = SendSmsResp{}
	mi := &file_specs_proto_sms_bridge_sms_bridge_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendSmsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSmsResp) ProtoMessage() {}

func (x *SendSmsResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_sms_bridge_sms_bridge_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSmsResp.ProtoReflect.Descriptor instead.
func (*SendSmsResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_sms_bridge_sms_bridge_proto_rawDescGZIP(), []int{3}
}

func (x *SendSmsResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *SendSmsResp) GetSmsID() string {
	if x != nil {
		return x.SmsID
	}
	return ""
}

type SmsStatusReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SmsID         string                 `protobuf:"bytes,1,opt,name=smsID,proto3" json:"smsID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SmsStatusReq) Reset() {
	*x = SmsStatusReq{}
	mi := &file_specs_proto_sms_bridge_sms_bridge_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SmsStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmsStatusReq) ProtoMessage() {}

func (x *SmsStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_sms_bridge_sms_bridge_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmsStatusReq.ProtoReflect.Descriptor instead.
func (*SmsStatusReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_sms_bridge_sms_bridge_proto_rawDescGZIP(), []int{4}
}

func (x *SmsStatusReq) GetSmsID() string {
	if x != nil {
		return x.SmsID
	}
	return ""
}

type SmsStatusResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        string                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SmsStatusResp) Reset() {
	*x = SmsStatusResp{}
	mi := &file_specs_proto_sms_bridge_sms_bridge_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SmsStatusResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmsStatusResp) ProtoMessage() {}

func (x *SmsStatusResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_sms_bridge_sms_bridge_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmsStatusResp.ProtoReflect.Descriptor instead.
func (*SmsStatusResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_sms_bridge_sms_bridge_proto_rawDescGZIP(), []int{5}
}

func (x *SmsStatusResp) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

var File_specs_proto_sms_bridge_sms_bridge_proto protoreflect.FileDescriptor

const file_specs_proto_sms_bridge_sms_bridge_proto_rawDesc = "" +
	"\n" +
	"'specs/proto/sms-bridge/sms-bridge.proto\x12\tsmsBridge\"\x10\n" +
	"\x0eHealthCheckReq\")\n" +
	"\x0fHealthCheckResp\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\"m\n" +
	"\n" +
	"SendSmsReq\x12 \n" +
	"\vphoneNumber\x18\x01 \x01(\tR\vphoneNumber\x12\x12\n" +
	"\x04text\x18\x02 \x01(\tR\x04text\x12\x1d\n" +
	"\adateBeg\x18\x03 \x01(\tH\x00R\adateBeg\x88\x01\x01B\n" +
	"\n" +
	"\b_dateBeg\"=\n" +
	"\vSendSmsResp\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x14\n" +
	"\x05SmsID\x18\x02 \x01(\tR\x05SmsID\"$\n" +
	"\fSmsStatusReq\x12\x14\n" +
	"\x05smsID\x18\x01 \x01(\tR\x05smsID\"'\n" +
	"\rSmsStatusResp\x12\x16\n" +
	"\x06status\x18\x01 \x01(\tR\x06status2\xcb\x01\n" +
	"\tSmsbridge\x12D\n" +
	"\vHealthCheck\x12\x19.smsBridge.HealthCheckReq\x1a\x1a.smsBridge.HealthCheckResp\x128\n" +
	"\aSendSMS\x12\x15.smsBridge.SendSmsReq\x1a\x16.smsBridge.SendSmsResp\x12>\n" +
	"\tSmsStatus\x12\x17.smsBridge.SmsStatusReq\x1a\x18.smsBridge.SmsStatusRespB\x18Z\x16specs/proto/sms-bridgeb\x06proto3"

var (
	file_specs_proto_sms_bridge_sms_bridge_proto_rawDescOnce sync.Once
	file_specs_proto_sms_bridge_sms_bridge_proto_rawDescData []byte
)

func file_specs_proto_sms_bridge_sms_bridge_proto_rawDescGZIP() []byte {
	file_specs_proto_sms_bridge_sms_bridge_proto_rawDescOnce.Do(func() {
		file_specs_proto_sms_bridge_sms_bridge_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_specs_proto_sms_bridge_sms_bridge_proto_rawDesc), len(file_specs_proto_sms_bridge_sms_bridge_proto_rawDesc)))
	})
	return file_specs_proto_sms_bridge_sms_bridge_proto_rawDescData
}

var file_specs_proto_sms_bridge_sms_bridge_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_specs_proto_sms_bridge_sms_bridge_proto_goTypes = []any{
	(*HealthCheckReq)(nil),  // 0: smsBridge.HealthCheckReq
	(*HealthCheckResp)(nil), // 1: smsBridge.HealthCheckResp
	(*SendSmsReq)(nil),      // 2: smsBridge.SendSmsReq
	(*SendSmsResp)(nil),     // 3: smsBridge.SendSmsResp
	(*SmsStatusReq)(nil),    // 4: smsBridge.SmsStatusReq
	(*SmsStatusResp)(nil),   // 5: smsBridge.SmsStatusResp
}
var file_specs_proto_sms_bridge_sms_bridge_proto_depIdxs = []int32{
	0, // 0: smsBridge.Smsbridge.HealthCheck:input_type -> smsBridge.HealthCheckReq
	2, // 1: smsBridge.Smsbridge.SendSMS:input_type -> smsBridge.SendSmsReq
	4, // 2: smsBridge.Smsbridge.SmsStatus:input_type -> smsBridge.SmsStatusReq
	1, // 3: smsBridge.Smsbridge.HealthCheck:output_type -> smsBridge.HealthCheckResp
	3, // 4: smsBridge.Smsbridge.SendSMS:output_type -> smsBridge.SendSmsResp
	5, // 5: smsBridge.Smsbridge.SmsStatus:output_type -> smsBridge.SmsStatusResp
	3, // [3:6] is the sub-list for method output_type
	0, // [0:3] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_specs_proto_sms_bridge_sms_bridge_proto_init() }
func file_specs_proto_sms_bridge_sms_bridge_proto_init() {
	if File_specs_proto_sms_bridge_sms_bridge_proto != nil {
		return
	}
	file_specs_proto_sms_bridge_sms_bridge_proto_msgTypes[2].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_specs_proto_sms_bridge_sms_bridge_proto_rawDesc), len(file_specs_proto_sms_bridge_sms_bridge_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_specs_proto_sms_bridge_sms_bridge_proto_goTypes,
		DependencyIndexes: file_specs_proto_sms_bridge_sms_bridge_proto_depIdxs,
		MessageInfos:      file_specs_proto_sms_bridge_sms_bridge_proto_msgTypes,
	}.Build()
	File_specs_proto_sms_bridge_sms_bridge_proto = out.File
	file_specs_proto_sms_bridge_sms_bridge_proto_goTypes = nil
	file_specs_proto_sms_bridge_sms_bridge_proto_depIdxs = nil
}
