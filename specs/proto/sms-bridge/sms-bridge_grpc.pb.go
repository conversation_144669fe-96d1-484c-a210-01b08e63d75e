// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.32.1
// source: specs/proto/sms-bridge/sms-bridge.proto

package sms_bridge

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Smsbridge_HealthCheck_FullMethodName = "/smsBridge.Smsbridge/HealthCheck"
	Smsbridge_SendSMS_FullMethodName     = "/smsBridge.Smsbridge/SendSMS"
	Smsbridge_SmsStatus_FullMethodName   = "/smsBridge.Smsbridge/SmsStatus"
)

// SmsbridgeClient is the client API for Smsbridge service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SmsbridgeClient interface {
	HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error)
	SendSMS(ctx context.Context, in *SendSmsReq, opts ...grpc.CallOption) (*SendSmsResp, error)
	SmsStatus(ctx context.Context, in *SmsStatusReq, opts ...grpc.CallOption) (*SmsStatusResp, error)
}

type smsbridgeClient struct {
	cc grpc.ClientConnInterface
}

func NewSmsbridgeClient(cc grpc.ClientConnInterface) SmsbridgeClient {
	return &smsbridgeClient{cc}
}

func (c *smsbridgeClient) HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResp)
	err := c.cc.Invoke(ctx, Smsbridge_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsbridgeClient) SendSMS(ctx context.Context, in *SendSmsReq, opts ...grpc.CallOption) (*SendSmsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendSmsResp)
	err := c.cc.Invoke(ctx, Smsbridge_SendSMS_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *smsbridgeClient) SmsStatus(ctx context.Context, in *SmsStatusReq, opts ...grpc.CallOption) (*SmsStatusResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SmsStatusResp)
	err := c.cc.Invoke(ctx, Smsbridge_SmsStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SmsbridgeServer is the server API for Smsbridge service.
// All implementations must embed UnimplementedSmsbridgeServer
// for forward compatibility.
type SmsbridgeServer interface {
	HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error)
	SendSMS(context.Context, *SendSmsReq) (*SendSmsResp, error)
	SmsStatus(context.Context, *SmsStatusReq) (*SmsStatusResp, error)
	mustEmbedUnimplementedSmsbridgeServer()
}

// UnimplementedSmsbridgeServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSmsbridgeServer struct{}

func (UnimplementedSmsbridgeServer) HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedSmsbridgeServer) SendSMS(context.Context, *SendSmsReq) (*SendSmsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSMS not implemented")
}
func (UnimplementedSmsbridgeServer) SmsStatus(context.Context, *SmsStatusReq) (*SmsStatusResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SmsStatus not implemented")
}
func (UnimplementedSmsbridgeServer) mustEmbedUnimplementedSmsbridgeServer() {}
func (UnimplementedSmsbridgeServer) testEmbeddedByValue()                   {}

// UnsafeSmsbridgeServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SmsbridgeServer will
// result in compilation errors.
type UnsafeSmsbridgeServer interface {
	mustEmbedUnimplementedSmsbridgeServer()
}

func RegisterSmsbridgeServer(s grpc.ServiceRegistrar, srv SmsbridgeServer) {
	// If the following call pancis, it indicates UnimplementedSmsbridgeServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Smsbridge_ServiceDesc, srv)
}

func _Smsbridge_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsbridgeServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Smsbridge_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsbridgeServer).HealthCheck(ctx, req.(*HealthCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Smsbridge_SendSMS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendSmsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsbridgeServer).SendSMS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Smsbridge_SendSMS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsbridgeServer).SendSMS(ctx, req.(*SendSmsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Smsbridge_SmsStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SmsStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SmsbridgeServer).SmsStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Smsbridge_SmsStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SmsbridgeServer).SmsStatus(ctx, req.(*SmsStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Smsbridge_ServiceDesc is the grpc.ServiceDesc for Smsbridge service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Smsbridge_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "smsBridge.Smsbridge",
	HandlerType: (*SmsbridgeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HealthCheck",
			Handler:    _Smsbridge_HealthCheck_Handler,
		},
		{
			MethodName: "SendSMS",
			Handler:    _Smsbridge_SendSMS_Handler,
		},
		{
			MethodName: "SmsStatus",
			Handler:    _Smsbridge_SmsStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "specs/proto/sms-bridge/sms-bridge.proto",
}
