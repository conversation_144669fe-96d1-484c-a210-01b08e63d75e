// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v6.32.1
// source: specs/proto/antifraud/antifraud.proto

package antifraud_folder

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HealthCheckReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckReq) Reset() {
	*x = HealthCheckReq{}
	mi := &file_specs_proto_antifraud_antifraud_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckReq) ProtoMessage() {}

func (x *HealthCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_antifraud_antifraud_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckReq.ProtoReflect.Descriptor instead.
func (*HealthCheckReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_antifraud_antifraud_proto_rawDescGZIP(), []int{0}
}

type HealthCheckResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckResp) Reset() {
	*x = HealthCheckResp{}
	mi := &file_specs_proto_antifraud_antifraud_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResp) ProtoMessage() {}

func (x *HealthCheckResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_antifraud_antifraud_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResp.ProtoReflect.Descriptor instead.
func (*HealthCheckResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_antifraud_antifraud_proto_rawDescGZIP(), []int{1}
}

func (x *HealthCheckResp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type FraudCheckClientReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ClientType    string                 `protobuf:"bytes,1,opt,name=clientType,proto3" json:"clientType,omitempty"`
	Iin           *string                `protobuf:"bytes,2,opt,name=iin,proto3,oneof" json:"iin,omitempty"`
	Bin           *string                `protobuf:"bytes,3,opt,name=bin,proto3,oneof" json:"bin,omitempty"`
	Firstname     *string                `protobuf:"bytes,4,opt,name=firstname,proto3,oneof" json:"firstname,omitempty"`
	Lastname      *string                `protobuf:"bytes,5,opt,name=lastname,proto3,oneof" json:"lastname,omitempty"`
	Patronymic    *string                `protobuf:"bytes,6,opt,name=patronymic,proto3,oneof" json:"patronymic,omitempty"`
	Title         *string                `protobuf:"bytes,7,opt,name=title,proto3,oneof" json:"title,omitempty"`
	Ibans         *string                `protobuf:"bytes,8,opt,name=ibans,proto3,oneof" json:"ibans,omitempty"`
	Phone         *string                `protobuf:"bytes,9,opt,name=phone,proto3,oneof" json:"phone,omitempty"`
	BirthDate     *string                `protobuf:"bytes,10,opt,name=birthDate,proto3,oneof" json:"birthDate,omitempty"`
	RegisterDate  *string                `protobuf:"bytes,11,opt,name=registerDate,proto3,oneof" json:"registerDate,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FraudCheckClientReq) Reset() {
	*x = FraudCheckClientReq{}
	mi := &file_specs_proto_antifraud_antifraud_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FraudCheckClientReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FraudCheckClientReq) ProtoMessage() {}

func (x *FraudCheckClientReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_antifraud_antifraud_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FraudCheckClientReq.ProtoReflect.Descriptor instead.
func (*FraudCheckClientReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_antifraud_antifraud_proto_rawDescGZIP(), []int{2}
}

func (x *FraudCheckClientReq) GetClientType() string {
	if x != nil {
		return x.ClientType
	}
	return ""
}

func (x *FraudCheckClientReq) GetIin() string {
	if x != nil && x.Iin != nil {
		return *x.Iin
	}
	return ""
}

func (x *FraudCheckClientReq) GetBin() string {
	if x != nil && x.Bin != nil {
		return *x.Bin
	}
	return ""
}

func (x *FraudCheckClientReq) GetFirstname() string {
	if x != nil && x.Firstname != nil {
		return *x.Firstname
	}
	return ""
}

func (x *FraudCheckClientReq) GetLastname() string {
	if x != nil && x.Lastname != nil {
		return *x.Lastname
	}
	return ""
}

func (x *FraudCheckClientReq) GetPatronymic() string {
	if x != nil && x.Patronymic != nil {
		return *x.Patronymic
	}
	return ""
}

func (x *FraudCheckClientReq) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *FraudCheckClientReq) GetIbans() string {
	if x != nil && x.Ibans != nil {
		return *x.Ibans
	}
	return ""
}

func (x *FraudCheckClientReq) GetPhone() string {
	if x != nil && x.Phone != nil {
		return *x.Phone
	}
	return ""
}

func (x *FraudCheckClientReq) GetBirthDate() string {
	if x != nil && x.BirthDate != nil {
		return *x.BirthDate
	}
	return ""
}

func (x *FraudCheckClientReq) GetRegisterDate() string {
	if x != nil && x.RegisterDate != nil {
		return *x.RegisterDate
	}
	return ""
}

type FraudCheckClientResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int64                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Status        string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	MatchedFields []string               `protobuf:"bytes,4,rep,name=matchedFields,proto3" json:"matchedFields,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FraudCheckClientResp) Reset() {
	*x = FraudCheckClientResp{}
	mi := &file_specs_proto_antifraud_antifraud_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FraudCheckClientResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FraudCheckClientResp) ProtoMessage() {}

func (x *FraudCheckClientResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_antifraud_antifraud_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FraudCheckClientResp.ProtoReflect.Descriptor instead.
func (*FraudCheckClientResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_antifraud_antifraud_proto_rawDescGZIP(), []int{3}
}

func (x *FraudCheckClientResp) GetCode() int64 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *FraudCheckClientResp) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *FraudCheckClientResp) GetMatchedFields() []string {
	if x != nil {
		return x.MatchedFields
	}
	return nil
}

var File_specs_proto_antifraud_antifraud_proto protoreflect.FileDescriptor

const file_specs_proto_antifraud_antifraud_proto_rawDesc = "" +
	"\n" +
	"%specs/proto/antifraud/antifraud.proto\x12\tantifraud\"\x10\n" +
	"\x0eHealthCheckReq\")\n" +
	"\x0fHealthCheckResp\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\"\xe0\x03\n" +
	"\x13FraudCheckClientReq\x12\x1e\n" +
	"\n" +
	"clientType\x18\x01 \x01(\tR\n" +
	"clientType\x12\x15\n" +
	"\x03iin\x18\x02 \x01(\tH\x00R\x03iin\x88\x01\x01\x12\x15\n" +
	"\x03bin\x18\x03 \x01(\tH\x01R\x03bin\x88\x01\x01\x12!\n" +
	"\tfirstname\x18\x04 \x01(\tH\x02R\tfirstname\x88\x01\x01\x12\x1f\n" +
	"\blastname\x18\x05 \x01(\tH\x03R\blastname\x88\x01\x01\x12#\n" +
	"\n" +
	"patronymic\x18\x06 \x01(\tH\x04R\n" +
	"patronymic\x88\x01\x01\x12\x19\n" +
	"\x05title\x18\a \x01(\tH\x05R\x05title\x88\x01\x01\x12\x19\n" +
	"\x05ibans\x18\b \x01(\tH\x06R\x05ibans\x88\x01\x01\x12\x19\n" +
	"\x05phone\x18\t \x01(\tH\aR\x05phone\x88\x01\x01\x12!\n" +
	"\tbirthDate\x18\n" +
	" \x01(\tH\bR\tbirthDate\x88\x01\x01\x12'\n" +
	"\fregisterDate\x18\v \x01(\tH\tR\fregisterDate\x88\x01\x01B\x06\n" +
	"\x04_iinB\x06\n" +
	"\x04_binB\f\n" +
	"\n" +
	"_firstnameB\v\n" +
	"\t_lastnameB\r\n" +
	"\v_patronymicB\b\n" +
	"\x06_titleB\b\n" +
	"\x06_ibansB\b\n" +
	"\x06_phoneB\f\n" +
	"\n" +
	"_birthDateB\x0f\n" +
	"\r_registerDate\"h\n" +
	"\x14FraudCheckClientResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x03R\x04code\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12$\n" +
	"\rmatchedFields\x18\x04 \x03(\tR\rmatchedFields2\xa6\x01\n" +
	"\tAntifraud\x12D\n" +
	"\vHealthCheck\x12\x19.antifraud.HealthCheckReq\x1a\x1a.antifraud.HealthCheckResp\x12S\n" +
	"\x10FraudCheckClient\x12\x1e.antifraud.FraudCheckClientReq\x1a\x1f.antifraud.FraudCheckClientRespB\x1eZ\x1cspecs/proto/antifraud_folderb\x06proto3"

var (
	file_specs_proto_antifraud_antifraud_proto_rawDescOnce sync.Once
	file_specs_proto_antifraud_antifraud_proto_rawDescData []byte
)

func file_specs_proto_antifraud_antifraud_proto_rawDescGZIP() []byte {
	file_specs_proto_antifraud_antifraud_proto_rawDescOnce.Do(func() {
		file_specs_proto_antifraud_antifraud_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_specs_proto_antifraud_antifraud_proto_rawDesc), len(file_specs_proto_antifraud_antifraud_proto_rawDesc)))
	})
	return file_specs_proto_antifraud_antifraud_proto_rawDescData
}

var file_specs_proto_antifraud_antifraud_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_specs_proto_antifraud_antifraud_proto_goTypes = []any{
	(*HealthCheckReq)(nil),       // 0: antifraud.HealthCheckReq
	(*HealthCheckResp)(nil),      // 1: antifraud.HealthCheckResp
	(*FraudCheckClientReq)(nil),  // 2: antifraud.FraudCheckClientReq
	(*FraudCheckClientResp)(nil), // 3: antifraud.FraudCheckClientResp
}
var file_specs_proto_antifraud_antifraud_proto_depIdxs = []int32{
	0, // 0: antifraud.Antifraud.HealthCheck:input_type -> antifraud.HealthCheckReq
	2, // 1: antifraud.Antifraud.FraudCheckClient:input_type -> antifraud.FraudCheckClientReq
	1, // 2: antifraud.Antifraud.HealthCheck:output_type -> antifraud.HealthCheckResp
	3, // 3: antifraud.Antifraud.FraudCheckClient:output_type -> antifraud.FraudCheckClientResp
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_specs_proto_antifraud_antifraud_proto_init() }
func file_specs_proto_antifraud_antifraud_proto_init() {
	if File_specs_proto_antifraud_antifraud_proto != nil {
		return
	}
	file_specs_proto_antifraud_antifraud_proto_msgTypes[2].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_specs_proto_antifraud_antifraud_proto_rawDesc), len(file_specs_proto_antifraud_antifraud_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_specs_proto_antifraud_antifraud_proto_goTypes,
		DependencyIndexes: file_specs_proto_antifraud_antifraud_proto_depIdxs,
		MessageInfos:      file_specs_proto_antifraud_antifraud_proto_msgTypes,
	}.Build()
	File_specs_proto_antifraud_antifraud_proto = out.File
	file_specs_proto_antifraud_antifraud_proto_goTypes = nil
	file_specs_proto_antifraud_antifraud_proto_depIdxs = nil
}
