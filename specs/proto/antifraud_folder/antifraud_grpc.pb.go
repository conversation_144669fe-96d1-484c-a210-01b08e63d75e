// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.32.1
// source: specs/proto/antifraud/antifraud.proto

package antifraud_folder

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Antifraud_HealthCheck_FullMethodName      = "/antifraud.Antifraud/HealthCheck"
	Antifraud_FraudCheckClient_FullMethodName = "/antifraud.Antifraud/FraudCheckClient"
)

// AntifraudClient is the client API for Antifraud service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AntifraudClient interface {
	HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error)
	FraudCheckClient(ctx context.Context, in *FraudCheckClientReq, opts ...grpc.CallOption) (*FraudCheckClientResp, error)
}

type antifraudClient struct {
	cc grpc.ClientConnInterface
}

func NewAntifraudClient(cc grpc.ClientConnInterface) AntifraudClient {
	return &antifraudClient{cc}
}

func (c *antifraudClient) HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResp)
	err := c.cc.Invoke(ctx, Antifraud_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *antifraudClient) FraudCheckClient(ctx context.Context, in *FraudCheckClientReq, opts ...grpc.CallOption) (*FraudCheckClientResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FraudCheckClientResp)
	err := c.cc.Invoke(ctx, Antifraud_FraudCheckClient_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AntifraudServer is the server API for Antifraud service.
// All implementations must embed UnimplementedAntifraudServer
// for forward compatibility.
type AntifraudServer interface {
	HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error)
	FraudCheckClient(context.Context, *FraudCheckClientReq) (*FraudCheckClientResp, error)
	mustEmbedUnimplementedAntifraudServer()
}

// UnimplementedAntifraudServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAntifraudServer struct{}

func (UnimplementedAntifraudServer) HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedAntifraudServer) FraudCheckClient(context.Context, *FraudCheckClientReq) (*FraudCheckClientResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FraudCheckClient not implemented")
}
func (UnimplementedAntifraudServer) mustEmbedUnimplementedAntifraudServer() {}
func (UnimplementedAntifraudServer) testEmbeddedByValue()                   {}

// UnsafeAntifraudServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AntifraudServer will
// result in compilation errors.
type UnsafeAntifraudServer interface {
	mustEmbedUnimplementedAntifraudServer()
}

func RegisterAntifraudServer(s grpc.ServiceRegistrar, srv AntifraudServer) {
	// If the following call pancis, it indicates UnimplementedAntifraudServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Antifraud_ServiceDesc, srv)
}

func _Antifraud_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntifraudServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Antifraud_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntifraudServer).HealthCheck(ctx, req.(*HealthCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Antifraud_FraudCheckClient_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FraudCheckClientReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AntifraudServer).FraudCheckClient(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Antifraud_FraudCheckClient_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AntifraudServer).FraudCheckClient(ctx, req.(*FraudCheckClientReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Antifraud_ServiceDesc is the grpc.ServiceDesc for Antifraud service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Antifraud_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "antifraud.Antifraud",
	HandlerType: (*AntifraudServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HealthCheck",
			Handler:    _Antifraud_HealthCheck_Handler,
		},
		{
			MethodName: "FraudCheckClient",
			Handler:    _Antifraud_FraudCheckClient_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "specs/proto/antifraud/antifraud.proto",
}
