// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.32.1
// source: specs/proto/tokenize/tokenize.proto

package tokenize

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Tokenize_HealthCheck_FullMethodName      = "/tokenize.Tokenize/HealthCheck"
	Tokenize_GetCardInfo_FullMethodName      = "/tokenize.Tokenize/GetCardInfo"
	Tokenize_GetInitData_FullMethodName      = "/tokenize.Tokenize/GetInitData"
	Tokenize_SetTokenizeState_FullMethodName = "/tokenize.Tokenize/SetTokenizeState"
)

// TokenizeClient is the client API for Tokenize service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TokenizeClient interface {
	HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error)
	GetCardInfo(ctx context.Context, in *GetCardInfoReq, opts ...grpc.CallOption) (*GetCardInfoResp, error)
	GetInitData(ctx context.Context, in *GetInitDataReq, opts ...grpc.CallOption) (*GetInitDataResp, error)
	SetTokenizeState(ctx context.Context, in *SetTokenizeStateReq, opts ...grpc.CallOption) (*SetTokenizeStateResp, error)
}

type tokenizeClient struct {
	cc grpc.ClientConnInterface
}

func NewTokenizeClient(cc grpc.ClientConnInterface) TokenizeClient {
	return &tokenizeClient{cc}
}

func (c *tokenizeClient) HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResp)
	err := c.cc.Invoke(ctx, Tokenize_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tokenizeClient) GetCardInfo(ctx context.Context, in *GetCardInfoReq, opts ...grpc.CallOption) (*GetCardInfoResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCardInfoResp)
	err := c.cc.Invoke(ctx, Tokenize_GetCardInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tokenizeClient) GetInitData(ctx context.Context, in *GetInitDataReq, opts ...grpc.CallOption) (*GetInitDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetInitDataResp)
	err := c.cc.Invoke(ctx, Tokenize_GetInitData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tokenizeClient) SetTokenizeState(ctx context.Context, in *SetTokenizeStateReq, opts ...grpc.CallOption) (*SetTokenizeStateResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetTokenizeStateResp)
	err := c.cc.Invoke(ctx, Tokenize_SetTokenizeState_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TokenizeServer is the server API for Tokenize service.
// All implementations must embed UnimplementedTokenizeServer
// for forward compatibility.
type TokenizeServer interface {
	HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error)
	GetCardInfo(context.Context, *GetCardInfoReq) (*GetCardInfoResp, error)
	GetInitData(context.Context, *GetInitDataReq) (*GetInitDataResp, error)
	SetTokenizeState(context.Context, *SetTokenizeStateReq) (*SetTokenizeStateResp, error)
	mustEmbedUnimplementedTokenizeServer()
}

// UnimplementedTokenizeServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTokenizeServer struct{}

func (UnimplementedTokenizeServer) HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedTokenizeServer) GetCardInfo(context.Context, *GetCardInfoReq) (*GetCardInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCardInfo not implemented")
}
func (UnimplementedTokenizeServer) GetInitData(context.Context, *GetInitDataReq) (*GetInitDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInitData not implemented")
}
func (UnimplementedTokenizeServer) SetTokenizeState(context.Context, *SetTokenizeStateReq) (*SetTokenizeStateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetTokenizeState not implemented")
}
func (UnimplementedTokenizeServer) mustEmbedUnimplementedTokenizeServer() {}
func (UnimplementedTokenizeServer) testEmbeddedByValue()                  {}

// UnsafeTokenizeServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TokenizeServer will
// result in compilation errors.
type UnsafeTokenizeServer interface {
	mustEmbedUnimplementedTokenizeServer()
}

func RegisterTokenizeServer(s grpc.ServiceRegistrar, srv TokenizeServer) {
	// If the following call pancis, it indicates UnimplementedTokenizeServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Tokenize_ServiceDesc, srv)
}

func _Tokenize_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TokenizeServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tokenize_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TokenizeServer).HealthCheck(ctx, req.(*HealthCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tokenize_GetCardInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCardInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TokenizeServer).GetCardInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tokenize_GetCardInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TokenizeServer).GetCardInfo(ctx, req.(*GetCardInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tokenize_GetInitData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInitDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TokenizeServer).GetInitData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tokenize_GetInitData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TokenizeServer).GetInitData(ctx, req.(*GetInitDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tokenize_SetTokenizeState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetTokenizeStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TokenizeServer).SetTokenizeState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tokenize_SetTokenizeState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TokenizeServer).SetTokenizeState(ctx, req.(*SetTokenizeStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Tokenize_ServiceDesc is the grpc.ServiceDesc for Tokenize service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Tokenize_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "tokenize.Tokenize",
	HandlerType: (*TokenizeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HealthCheck",
			Handler:    _Tokenize_HealthCheck_Handler,
		},
		{
			MethodName: "GetCardInfo",
			Handler:    _Tokenize_GetCardInfo_Handler,
		},
		{
			MethodName: "GetInitData",
			Handler:    _Tokenize_GetInitData_Handler,
		},
		{
			MethodName: "SetTokenizeState",
			Handler:    _Tokenize_SetTokenizeState_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "specs/proto/tokenize/tokenize.proto",
}
