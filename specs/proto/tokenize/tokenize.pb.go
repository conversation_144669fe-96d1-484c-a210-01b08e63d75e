// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v6.32.1
// source: specs/proto/tokenize/tokenize.proto

package tokenize

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HealthCheckReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckReq) Reset() {
	*x = HealthCheckReq{}
	mi := &file_specs_proto_tokenize_tokenize_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckReq) ProtoMessage() {}

func (x *HealthCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_tokenize_tokenize_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckReq.ProtoReflect.Descriptor instead.
func (*HealthCheckReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_tokenize_tokenize_proto_rawDescGZIP(), []int{0}
}

type HealthCheckResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckResp) Reset() {
	*x = HealthCheckResp{}
	mi := &file_specs_proto_tokenize_tokenize_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResp) ProtoMessage() {}

func (x *HealthCheckResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_tokenize_tokenize_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResp.ProtoReflect.Descriptor instead.
func (*HealthCheckResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_tokenize_tokenize_proto_rawDescGZIP(), []int{1}
}

func (x *HealthCheckResp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

// GetCardInfoReq - запрос информации о токенизации карты
type GetCardInfoReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// issuer_card_ref_id карты у эмитента (cardId из API)
	CardId        string `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCardInfoReq) Reset() {
	*x = GetCardInfoReq{}
	mi := &file_specs_proto_tokenize_tokenize_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCardInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCardInfoReq) ProtoMessage() {}

func (x *GetCardInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_tokenize_tokenize_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCardInfoReq.ProtoReflect.Descriptor instead.
func (*GetCardInfoReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_tokenize_tokenize_proto_rawDescGZIP(), []int{2}
}

func (x *GetCardInfoReq) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

// GetCardInfoResp - ответ с нормализованным JSON о токенизации
type GetCardInfoResp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Нормализованный JSON (issuerCardRefId, virtualCardId, wallet*, status)
	PayloadJson   []byte `protobuf:"bytes,1,opt,name=payload_json,json=payloadJson,proto3" json:"payload_json,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCardInfoResp) Reset() {
	*x = GetCardInfoResp{}
	mi := &file_specs_proto_tokenize_tokenize_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCardInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCardInfoResp) ProtoMessage() {}

func (x *GetCardInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_tokenize_tokenize_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCardInfoResp.ProtoReflect.Descriptor instead.
func (*GetCardInfoResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_tokenize_tokenize_proto_rawDescGZIP(), []int{3}
}

func (x *GetCardInfoResp) GetPayloadJson() []byte {
	if x != nil {
		return x.PayloadJson
	}
	return nil
}

// GetInitDataReq - запрос данных для старта токенизации
type GetInitDataReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID карты (issuer_card_ref_id / cardId)
	CardId string `protobuf:"bytes,1,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// Целевой кошелёк (GooglePay | ApplePay)
	Wallet        string `protobuf:"bytes,2,opt,name=wallet,proto3" json:"wallet,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetInitDataReq) Reset() {
	*x = GetInitDataReq{}
	mi := &file_specs_proto_tokenize_tokenize_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetInitDataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInitDataReq) ProtoMessage() {}

func (x *GetInitDataReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_tokenize_tokenize_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInitDataReq.ProtoReflect.Descriptor instead.
func (*GetInitDataReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_tokenize_tokenize_proto_rawDescGZIP(), []int{4}
}

func (x *GetInitDataReq) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *GetInitDataReq) GetWallet() string {
	if x != nil {
		return x.Wallet
	}
	return ""
}

// GetInitDataResp - ответ с данными для старта токенизации
type GetInitDataResp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Идентификатор публичного ключа
	PublicKeyId string `protobuf:"bytes,1,opt,name=public_key_id,json=publicKeyId,proto3" json:"public_key_id,omitempty"`
	// One-time Provisioning Code (HEX)
	Opc string `protobuf:"bytes,2,opt,name=opc,proto3" json:"opc,omitempty"`
	// Зашифрованный payload (HEX)
	EncryptedPayload string `protobuf:"bytes,3,opt,name=encrypted_payload,json=encryptedPayload,proto3" json:"encrypted_payload,omitempty"`
	// Идентификатор приложения эмитента
	IssuerAppId string `protobuf:"bytes,4,opt,name=issuer_app_id,json=issuerAppId,proto3" json:"issuer_app_id,omitempty"`
	// Идентификатор продукта
	ProductId string `protobuf:"bytes,5,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	// Адрес пользователя (если есть)
	UserAddress string `protobuf:"bytes,6,opt,name=user_address,json=userAddress,proto3" json:"user_address,omitempty"`
	// Платёжная схема (например, MASTERCARD)
	Scheme string `protobuf:"bytes,7,opt,name=scheme,proto3" json:"scheme,omitempty"`
	// Базовый URL TSH/Thales API для SDK
	Url           string `protobuf:"bytes,8,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetInitDataResp) Reset() {
	*x = GetInitDataResp{}
	mi := &file_specs_proto_tokenize_tokenize_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetInitDataResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInitDataResp) ProtoMessage() {}

func (x *GetInitDataResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_tokenize_tokenize_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInitDataResp.ProtoReflect.Descriptor instead.
func (*GetInitDataResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_tokenize_tokenize_proto_rawDescGZIP(), []int{5}
}

func (x *GetInitDataResp) GetPublicKeyId() string {
	if x != nil {
		return x.PublicKeyId
	}
	return ""
}

func (x *GetInitDataResp) GetOpc() string {
	if x != nil {
		return x.Opc
	}
	return ""
}

func (x *GetInitDataResp) GetEncryptedPayload() string {
	if x != nil {
		return x.EncryptedPayload
	}
	return ""
}

func (x *GetInitDataResp) GetIssuerAppId() string {
	if x != nil {
		return x.IssuerAppId
	}
	return ""
}

func (x *GetInitDataResp) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *GetInitDataResp) GetUserAddress() string {
	if x != nil {
		return x.UserAddress
	}
	return ""
}

func (x *GetInitDataResp) GetScheme() string {
	if x != nil {
		return x.Scheme
	}
	return ""
}

func (x *GetInitDataResp) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

// SetTokenizeStateReq - запрос для установки/сохранения статуса токенизации (callback /tokenize/state)
type SetTokenizeStateReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// идентификатор карты эмитента (уникальный)
	IssuerCardRefId string `protobuf:"bytes,1,opt,name=issuer_card_ref_id,json=issuerCardRefId,proto3" json:"issuer_card_ref_id,omitempty"`
	// ID виртуальной карты в TSP
	VirtualCardId string `protobuf:"bytes,2,opt,name=virtual_card_id,json=virtualCardId,proto3" json:"virtual_card_id,omitempty"`
	// 'tokenized' | 'need_verify' | 'not_tokenized'
	TokenizeStatus string `protobuf:"bytes,3,opt,name=tokenize_status,json=tokenizeStatus,proto3" json:"tokenize_status,omitempty"`
	// опционально, чтобы сохранить исходный JSON от TSH
	RawPayload    []byte `protobuf:"bytes,4,opt,name=raw_payload,json=rawPayload,proto3" json:"raw_payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetTokenizeStateReq) Reset() {
	*x = SetTokenizeStateReq{}
	mi := &file_specs_proto_tokenize_tokenize_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetTokenizeStateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetTokenizeStateReq) ProtoMessage() {}

func (x *SetTokenizeStateReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_tokenize_tokenize_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetTokenizeStateReq.ProtoReflect.Descriptor instead.
func (*SetTokenizeStateReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_tokenize_tokenize_proto_rawDescGZIP(), []int{6}
}

func (x *SetTokenizeStateReq) GetIssuerCardRefId() string {
	if x != nil {
		return x.IssuerCardRefId
	}
	return ""
}

func (x *SetTokenizeStateReq) GetVirtualCardId() string {
	if x != nil {
		return x.VirtualCardId
	}
	return ""
}

func (x *SetTokenizeStateReq) GetTokenizeStatus() string {
	if x != nil {
		return x.TokenizeStatus
	}
	return ""
}

func (x *SetTokenizeStateReq) GetRawPayload() []byte {
	if x != nil {
		return x.RawPayload
	}
	return nil
}

// SetTokenizeStateResp - ответ по результату установки статуса токенизации
type SetTokenizeStateResp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// error_code — код результата:
	//
	//	"0" — Ok (успешно обработано или callback уже был обработан ранее),
	//	"1" — неверный формат JSON в raw_payload,
	//	"2" — issuer_card_ref_id (cardId) не найден/не передан,
	//	"921" — внутренняя ошибка при сохранении состояния.
	ErrorCode string `protobuf:"bytes,1,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	// error_message — текстовое описание результата (например, "Ok" при успехе
	// или пояснение причины ошибки при кодах "1"/"2"/"921").
	ErrorMessage  string `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetTokenizeStateResp) Reset() {
	*x = SetTokenizeStateResp{}
	mi := &file_specs_proto_tokenize_tokenize_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetTokenizeStateResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetTokenizeStateResp) ProtoMessage() {}

func (x *SetTokenizeStateResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_tokenize_tokenize_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetTokenizeStateResp.ProtoReflect.Descriptor instead.
func (*SetTokenizeStateResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_tokenize_tokenize_proto_rawDescGZIP(), []int{7}
}

func (x *SetTokenizeStateResp) GetErrorCode() string {
	if x != nil {
		return x.ErrorCode
	}
	return ""
}

func (x *SetTokenizeStateResp) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

var File_specs_proto_tokenize_tokenize_proto protoreflect.FileDescriptor

const file_specs_proto_tokenize_tokenize_proto_rawDesc = "" +
	"\n" +
	"#specs/proto/tokenize/tokenize.proto\x12\btokenize\"\x10\n" +
	"\x0eHealthCheckReq\")\n" +
	"\x0fHealthCheckResp\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\")\n" +
	"\x0eGetCardInfoReq\x12\x17\n" +
	"\acard_id\x18\x01 \x01(\tR\x06cardId\"4\n" +
	"\x0fGetCardInfoResp\x12!\n" +
	"\fpayload_json\x18\x01 \x01(\fR\vpayloadJson\"A\n" +
	"\x0eGetInitDataReq\x12\x17\n" +
	"\acard_id\x18\x01 \x01(\tR\x06cardId\x12\x16\n" +
	"\x06wallet\x18\x02 \x01(\tR\x06wallet\"\x84\x02\n" +
	"\x0fGetInitDataResp\x12\"\n" +
	"\rpublic_key_id\x18\x01 \x01(\tR\vpublicKeyId\x12\x10\n" +
	"\x03opc\x18\x02 \x01(\tR\x03opc\x12+\n" +
	"\x11encrypted_payload\x18\x03 \x01(\tR\x10encryptedPayload\x12\"\n" +
	"\rissuer_app_id\x18\x04 \x01(\tR\vissuerAppId\x12\x1d\n" +
	"\n" +
	"product_id\x18\x05 \x01(\tR\tproductId\x12!\n" +
	"\fuser_address\x18\x06 \x01(\tR\vuserAddress\x12\x16\n" +
	"\x06scheme\x18\a \x01(\tR\x06scheme\x12\x10\n" +
	"\x03url\x18\b \x01(\tR\x03url\"\xb4\x01\n" +
	"\x13SetTokenizeStateReq\x12+\n" +
	"\x12issuer_card_ref_id\x18\x01 \x01(\tR\x0fissuerCardRefId\x12&\n" +
	"\x0fvirtual_card_id\x18\x02 \x01(\tR\rvirtualCardId\x12'\n" +
	"\x0ftokenize_status\x18\x03 \x01(\tR\x0etokenizeStatus\x12\x1f\n" +
	"\vraw_payload\x18\x04 \x01(\fR\n" +
	"rawPayload\"Z\n" +
	"\x14SetTokenizeStateResp\x12\x1d\n" +
	"\n" +
	"error_code\x18\x01 \x01(\tR\terrorCode\x12#\n" +
	"\rerror_message\x18\x02 \x01(\tR\ferrorMessage2\xa9\x02\n" +
	"\bTokenize\x12B\n" +
	"\vHealthCheck\x12\x18.tokenize.HealthCheckReq\x1a\x19.tokenize.HealthCheckResp\x12B\n" +
	"\vGetCardInfo\x12\x18.tokenize.GetCardInfoReq\x1a\x19.tokenize.GetCardInfoResp\x12B\n" +
	"\vGetInitData\x12\x18.tokenize.GetInitDataReq\x1a\x19.tokenize.GetInitDataResp\x12Q\n" +
	"\x10SetTokenizeState\x12\x1d.tokenize.SetTokenizeStateReq\x1a\x1e.tokenize.SetTokenizeStateRespB\x16Z\x14specs/proto/tokenizeb\x06proto3"

var (
	file_specs_proto_tokenize_tokenize_proto_rawDescOnce sync.Once
	file_specs_proto_tokenize_tokenize_proto_rawDescData []byte
)

func file_specs_proto_tokenize_tokenize_proto_rawDescGZIP() []byte {
	file_specs_proto_tokenize_tokenize_proto_rawDescOnce.Do(func() {
		file_specs_proto_tokenize_tokenize_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_specs_proto_tokenize_tokenize_proto_rawDesc), len(file_specs_proto_tokenize_tokenize_proto_rawDesc)))
	})
	return file_specs_proto_tokenize_tokenize_proto_rawDescData
}

var file_specs_proto_tokenize_tokenize_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_specs_proto_tokenize_tokenize_proto_goTypes = []any{
	(*HealthCheckReq)(nil),       // 0: tokenize.HealthCheckReq
	(*HealthCheckResp)(nil),      // 1: tokenize.HealthCheckResp
	(*GetCardInfoReq)(nil),       // 2: tokenize.GetCardInfoReq
	(*GetCardInfoResp)(nil),      // 3: tokenize.GetCardInfoResp
	(*GetInitDataReq)(nil),       // 4: tokenize.GetInitDataReq
	(*GetInitDataResp)(nil),      // 5: tokenize.GetInitDataResp
	(*SetTokenizeStateReq)(nil),  // 6: tokenize.SetTokenizeStateReq
	(*SetTokenizeStateResp)(nil), // 7: tokenize.SetTokenizeStateResp
}
var file_specs_proto_tokenize_tokenize_proto_depIdxs = []int32{
	0, // 0: tokenize.Tokenize.HealthCheck:input_type -> tokenize.HealthCheckReq
	2, // 1: tokenize.Tokenize.GetCardInfo:input_type -> tokenize.GetCardInfoReq
	4, // 2: tokenize.Tokenize.GetInitData:input_type -> tokenize.GetInitDataReq
	6, // 3: tokenize.Tokenize.SetTokenizeState:input_type -> tokenize.SetTokenizeStateReq
	1, // 4: tokenize.Tokenize.HealthCheck:output_type -> tokenize.HealthCheckResp
	3, // 5: tokenize.Tokenize.GetCardInfo:output_type -> tokenize.GetCardInfoResp
	5, // 6: tokenize.Tokenize.GetInitData:output_type -> tokenize.GetInitDataResp
	7, // 7: tokenize.Tokenize.SetTokenizeState:output_type -> tokenize.SetTokenizeStateResp
	4, // [4:8] is the sub-list for method output_type
	0, // [0:4] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_specs_proto_tokenize_tokenize_proto_init() }
func file_specs_proto_tokenize_tokenize_proto_init() {
	if File_specs_proto_tokenize_tokenize_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_specs_proto_tokenize_tokenize_proto_rawDesc), len(file_specs_proto_tokenize_tokenize_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_specs_proto_tokenize_tokenize_proto_goTypes,
		DependencyIndexes: file_specs_proto_tokenize_tokenize_proto_depIdxs,
		MessageInfos:      file_specs_proto_tokenize_tokenize_proto_msgTypes,
	}.Build()
	File_specs_proto_tokenize_tokenize_proto = out.File
	file_specs_proto_tokenize_tokenize_proto_goTypes = nil
	file_specs_proto_tokenize_tokenize_proto_depIdxs = nil
}
