syntax = "proto3";

import "google/protobuf/timestamp.proto";

package documents;

option go_package = "specs/proto/documents";

service Documents {
  rpc HealthCheck (HealthCheckReq) returns (HealthCheckResp); // Сгенерированный метод, не изменяйте его.

  rpc GenerateDocumentByType (GenerateDocumentReq) returns (DocumentResp);
  rpc AccountDocument (GenerateAccountDocumentReq) returns (GenerateAccountDocumentResp);
  rpc GetDocumentByID (GetDocumentByIDReq) returns (DocumentResp);
  rpc DownloadDocument (DownloadDocumentReq) returns (DownloadDocumentResp);
  rpc GetDocumentByType (GetDocumentByTypeReq) returns (DocumentResp);
  rpc SignDocumentByID (SignDocumentReq) returns (SignDocumentResp);
  rpc ConfirmSignDocument (ConfirmSignDocumentReq) returns (ConfirmSignDocumentResp);
  rpc ConfirmSignDocuments (ConfirmSignDocumentsReq) returns (ConfirmSignDocumentsResp);
  rpc ConfirmSignDocumentsBatch (ConfirmSignDocumentsBatchReq) returns (ConfirmSignDocumentsBatchResp);
  rpc SignDocumentForUser (SignDocumentForUserReq) returns (SignDocumentForUserResp);
  rpc SaveLiveness3DFile(SaveLiveness3DFileReq) returns (SaveLiveness3DFileResp);
  rpc GenerateComplexConsentDocument (GenerateComplexConsentDocumentReq) returns (DocumentResp);
  rpc GenerateCreditContractWithRepaymentSchedule(GenerateCreditContractWithRepaymentScheduleReq) returns (DocumentResp);
  rpc GenerateDocumentForOpeningAccount (GenerateDocumentForOpeningAccountReq) returns (DocumentResp);
  rpc GenerateDocumentForOpeningAccountSME (GenerateDocumentForOpeningAccountSMEReq) returns (DocumentResp);
  rpc GenerateDocumentForOpeningAdditionalAccountSME (GenerateDocumentForOpeningAdditionalAccountSMEReq) returns (DocumentResp);
  rpc GenerateAppSaleOfGoodsDocument (GenerateAppSaleOfGoodsDocumentReq) returns (DocumentResp);
  rpc GenerateLoansFundsTransferRequestRefinance (GenerateLoansFundsTransferRequestRefinanceRequest) returns (DocumentResp);
  rpc GenerateAppSaleOfGoodsDocumentSMEIP (GenerateAppSaleOfGoodsDocumentSMEIPReq) returns (DocumentResp);
  rpc GenerateBankServiceApplicationSMEIP (GenerateBankServiceApplicationSMEIPReq) returns (DocumentResp);
  rpc GetUserDocuments (GetUserDocumentsReq) returns (GetUserDocumentsResp);
  rpc SaveSignedBtsDocuments (SaveSignedBtsDocumentsReq) returns (SaveSignedBtsDocumentsResp);
  rpc GetDocumentContent (GetDocumentContentReq) returns (GetDocumentContentResp);
  rpc GeneratePaymentByAccountReceipt (GeneratePaymentByAccountReceiptReq) returns (DocumentResp);
  rpc GeneratePaymentForMobileReceipt (GeneratePaymentForMobileReceiptReq) returns (DocumentResp);
  rpc GenerateInternalPaymentByPhoneNumberReceipt (GenerateInternalPaymentByPhoneNumberReceiptReq) returns (DocumentResp);
  rpc GeneratePaymentByAccountSmeReceipt (GeneratePaymentByAccountReceiptSmeReq) returns (DocumentResp);
  rpc GeneratePersonalDataAgreement (GeneratePersonalDataAgreementReq) returns (DocumentResp);
  rpc GeneratePersonalDataAgreementSME (GeneratePersonalDataAgreementSMEReq) returns (DocumentResp);
  rpc GenerateBankingServiceApplicationDocument (GenerateBankingServiceApplicationDocumentReq) returns (DocumentResp);
  rpc GetDocumentSign (GetDocumentSignReq) returns (GetDocumentSignResp);
  rpc GenerateCreditContractWithRepaymentScheduleSMEIP(GenerateCreditContractWithRepaymentScheduleSMEIPReq) returns (DocumentResp);
  rpc GenerateComplexConsentDocumentSMEIP (GenerateComplexConsentDocumentSMEIPReq) returns (DocumentResp);
  rpc GenerateSprProtocol (GenerateSprProtocolReq) returns (DocumentResp);
  rpc CreateOrderBsas (CreateOrderBsasReq) returns (CreateOrderBsasResp);
  rpc GetOrderResultBsas (GetOrderResultBsasReq) returns (GetOrderResultBsasResp);
  rpc GetSystemMapping (GetSystemMappingReq) returns (GetSystemMappingResp);
  rpc GetOtcReportInfo (GetOtcReportInfoReq) returns (GetOtcReportInfoResp);
  rpc GenerateOtcTemplateCertificate (GenerateOtcTemplateCertificateReq) returns (DocumentResp);
  rpc GenerateSTBTransactionTemplateCertificate (GenerateSTBTransactionTemplateCertificateReq) returns (DocumentResp);
  rpc GetSellToBMIS (GetSellToBMISReq) returns (GetSellToBMISResp);
  rpc GenerateDepositApplicationDocument(GenerateDepositApplicationDocumentReq) returns (DocumentResp);
  rpc GenerateDocumentBID (GenerateDocumentBIDReq) returns (DocumentResp);
  rpc GenerateCollectionIncome (GenerateCollectionIncomeReq) returns (DocumentResp);
  rpc GeneratePaymentOrderSMEIP (GeneratePaymentOrderSMEIPReq) returns (DocumentResp);
  rpc GenerateMassPaymentOrderSMEIP (GenerateMassPaymentOrderSMEIPReq) returns (DocumentResp);
  rpc SignDocumentByIDWeb (SignDocumentWebReq) returns (SignDocumentWebResp);
  rpc ConfirmSignDocumentWeb (ConfirmSignDocumentWebReq) returns (ConfirmSignDocumentWebResp);
  rpc GenerateDepositCloseApplicationDocument(GenerateDepositCloseApplicationDocumentReq) returns (DocumentResp);
}

enum Locale {
  ru = 0;
  kk = 1;
}

message GetUserDocumentsReq {
  string userID = 1;
}

message GetUserDocumentsResp {
  repeated DocumentResp documents = 1;
}

message HealthCheckReq {}

message HealthCheckResp {
  bool status = 1;
}

message SaveLiveness3DFileReq {
  string type = 1;
  bytes file = 2;
  string userID = 3;
}

message SaveLiveness3DFileResp {
  string id = 1;
  string url = 2;
}

message GenerateDocumentReq {
  string type = 1;
}

message SignDocumentReq {
  repeated string docIDs = 1;
}

message SignDocumentResp {
  string attemptID = 1;
  int32 retryTime = 2;
}

message SignDocumentForUserReq {
  string userID = 1;
  string code = 2;
  string docType = 3;
}

message SignDocumentForUserResp {
  string documentID = 1;
  int32 version = 2;
  bool signed = 3;
}

message ConfirmSignDocumentReq {
  string id = 1;
  string attemptID = 2;
  string code = 3;
}

message ConfirmSignDocumentsReq {
  string attemptID = 1;
  string code = 2;
}

message ConfirmSignDocumentsResp {
  repeated DocumentResp documents = 1;
}

message ConfirmSignDocumentResp {
  string id = 1;
  string type = 2;
  int32 version = 3;
  string fileLink = 4;
  bool signed = 5;
}

message ConfirmSignDocumentsBatchReq {
  repeated string docIDs = 1;
  string attemptID = 2;
  string code = 3;
}

message ConfirmSignDocumentsBatchResp {
  repeated DocumentResp documents = 1;
}

message DocumentResp {
  string id = 1;
  string title = 2;
  string type = 3;
  int32 version = 4;
  string link = 5;
  bool signed = 6;
}

message GetDocumentByIDReq {
  string ID = 1;
}

message GetDocumentByTypeReq {
  string docType = 1;
  string userID = 2;
}

message GenerateAccountDocumentReq {
  string docType = 1;
  string accountID = 2;
  string language = 3;
  string periodFrom = 4;
  string periodTo = 5;
}

message GenerateAccountDocumentResp {
  DocumentResp document = 1;
}

message GenerateComplexConsentDocumentSMEIPReq {
  string personIIN = 1;
  string personFullName = 2;
  string documentDate = 3;
  string productName = 4;
  uint32 interest = 5;
  uint32 amount = 6;
  ComplexConsentBankData bankData = 7;
}

message GenerateComplexConsentDocumentReq {
  string personIIN = 1;
  string personFullName = 2;
  string documentDate = 3;
  string productName = 4;
  uint32 interest = 5;
  uint32 amount = 6;
  ComplexConsentBankData bankData = 7;
}

message GenerateLoansFundsTransferRequestRefinanceRequest {
  repeated Application Applications = 1;
}

message Application {
  string OrderNum = 1;
  string OrderDate = 2;
  string OrderSumAmount = 3;
  string SenderFullName = 4;
  string SenderIin = 5;
  string SenderIban = 6;
  uint32 SenderCode = 7;
  string SenderBankNameRu = 8;
  uint32 SenderBankCode = 9;
  string SenderResidence = 10;
  string BeneficiaryFullName = 11;
  string BeneficiaryIin = 12;
  string BeneficiaryBankName = 13;
  string BeneficiaryIban = 14;
  uint32 BeneficiaryCode = 15;
  string BeneficiaryResidence = 16;
  uint32 BeneficiaryBankCode = 17;
  string OrderSumAmountTextKz = 18;
  string OrderSumAmountTextRu = 19;
  string DescriptionKz = 20;
  string DescriptionRu = 21;
  uint32 PaymentCode = 22;
  string ValueDate = 23;
  string ExecutionDateKz = 24;
  string ExecutionDateRu = 25;
  string SenderNameShort = 26;
}

message ComplexConsentBankData {
  LocalizationData bankName = 1;
  LocalizationData bankCity = 2;
  LocalizationData reprPosition = 3;
  string bankSiteDocsUrl = 4;
  string reprFullName = 5;
}

message GenerateCreditContractWithRepaymentScheduleReqContract {
  string number = 1;
  string contractDate = 2;
  string contractTime = 3;
}

message GenerateCreditContractWithRepaymentScheduleReqBankRepresentative {
  string fullName = 1;
  GenerateCreditContractWithRepaymentScheduleReqLocalizedValue jobPosition = 2;
  GenerateCreditContractWithRepaymentScheduleReqLocalizedValue documentNameGenitive = 3;
}

message GenerateCreditContractWithRepaymentScheduleReqLocalizedValue {
  string RU = 1;
  string KZ = 2;
}

message GenerateCreditContractWithRepaymentScheduleReqBankCity {
  GenerateCreditContractWithRepaymentScheduleReqLocalizedValue name = 1;
}

message GenerateCreditContractWithRepaymentScheduleReqBank {
  GenerateCreditContractWithRepaymentScheduleReqLocalizedValue Name = 1;
  string bin = 2;
  GenerateCreditContractWithRepaymentScheduleReqLocalizedValue address = 3;
  string phone = 4;
  string email = 5;
  string website = 6;
  string iik = 7;
  string bic = 8;
  GenerateCreditContractWithRepaymentScheduleReqBankRepresentative representative = 9;
  GenerateCreditContractWithRepaymentScheduleReqBankCity City = 10;
}

message GenerateCreditContractWithRepaymentScheduleReqUser {
  string fullName = 1;
  string iin = 2;
  GenerateCreditContractWithRepaymentScheduleReqLocalizedValue legalAddress = 3;
  GenerateCreditContractWithRepaymentScheduleReqLocalizedValue actualAddress = 4;
  string mobilePhone = 5;
  string landlinePhone = 6;
  string email = 7;
  string organizationName = 8;
}

message GenerateCreditContractWithRepaymentScheduleReqPayment {
  string date = 1;
  string amount = 2;
  string markup = 3;
  string baseCost = 4;
  string remainingDebt = 5;
}

message GenerateCreditContractWithRepaymentScheduleReqPaymentsSummary {
  string amount = 1;
  string markup = 2;
  string baseCost = 3;
  string remainingDebt = 4;
}

message GenerateCreditContractWithRepaymentScheduleReqProduct {
  string cost = 1;
}

message GenerateCreditContractWithRepaymentScheduleReq {
  GenerateCreditContractWithRepaymentScheduleReqContract contract = 1;
  GenerateCreditContractWithRepaymentScheduleReqBank bank = 3;
  GenerateCreditContractWithRepaymentScheduleReqUser user = 4;
  GenerateCreditContractWithRepaymentScheduleReqProduct product = 5;
  string markupAmountSum = 6;
  string paymentScheduleDate = 7;
  string financingTerm = 8;
  repeated GenerateCreditContractWithRepaymentScheduleReqPayment payments = 9;
  GenerateCreditContractWithRepaymentScheduleReqPaymentsSummary paymentsSummary = 10;
  GenerateCreditContractWithRepaymentScheduleReqLocalizedValue currency = 11;
}

message GenerateDocumentForOpeningAccountReq {
  string docType = 1;
  DataForGenerateDocumentReq data = 2;
}

// Protobuf структура данных для генерации документа
message DataForGenerateDocumentReq {
		string ActivityType = 1;
		string AttorneyPowerExpDate = 2;
		string AttorneyPowerIssueDate = 3;
		string AttorneyPowerIssueOrg = 4;
		string AttorneyPowerIssuePlace = 5;
		string AttorneyPowerNumber = 6;
		string AttorneyPowerType = 7;
		string BeneficialOwner = 8;
		string BirthCountry = 9;
		string BirthDay = 10;
		string BirthMonth = 11;
		string BirthPlace = 12;
		string BirthYear = 13;
		string Building = 14;
		string City = 15;
		string CreateDay = 16;
		string CreateMonth = 17;
		string CreateYear = 18;
		string District = 19;
		string DocExpDay = 20;
		string DocExpMonth = 21;
		string DocExpYear = 22;
		string DocIssueDay = 23;
		string DocIssueMonth = 24;
		string DocIssueYear = 25;
		string DocSeries = 26;
		string DocumentNumber = 27;
		string Education = 28;
		string Email = 29;
		string Flat = 30;
		string IIN = 31;
		bool IncomeBankFunding = 32;
		bool IncomeBusiness = 33;
		bool IncomeDividends = 34;
		bool IncomeInheritance = 35;
		bool IncomePension = 36;
		bool IncomeRealEstateSale = 37;
		bool IncomeSalary = 38;
		bool IncomeShares = 39;
		bool IsAbove20Million = 40;
		bool IsAddressRegistrationInKZ = 41;
		bool IsBelow20Million = 42;
		bool IsCitizenCertificate = 43;
		bool IsCitizenPassport = 44;
		bool IsCurrentAccount = 45;
		bool IsCurrentAccountEUR = 46;
		bool IsCurrentAccountKZT = 47;
		bool IsCurrentAccountUSD = 48;
		bool IsEnableMobileApp = 49;
		bool IsForeignPassport = 50;
		bool IsForeignResidencePermit = 51;
		bool IsMastercardPlatinum = 52;
		bool IsNoBelongPDL = 53;
		bool IsNoBeneficialOwner = 54;
		bool IsNoCitizenshipCertificate = 55;
		bool IsNoOtherCountryTaxResident = 56;
		bool IsNoPersonPDL = 57;
		bool IsNoRelatedPDL = 58;
		bool IsNotFiveCardHolder = 59;
		bool IsNotResident = 60;
		bool IsNotThreeCardHolder = 61;
		bool IsNoUsaBorn = 62;
		bool IsNoUsaBusinessActivity = 63;
		bool IsNoUsaCitizen = 64;
		bool IsNoUsaCurrentAddress = 65;
		bool IsNoUsaCurrentMore31Days = 66;
		bool IsNoUsaEmergencyAddress = 67;
		bool IsNoUsaPaymentAccountOurBank = 68;
		bool IsNoUsaPhoneNumber = 69;
		bool IsNoUsaRegistrationAddress = 70;
		bool IsNoUsaTaxResident = 71;
		bool IsNoUsaTrustOrSignaturePowerOfAttorney = 72;
		bool IsOpenBankAccount = 73;
		bool IsOpenPaymentCard = 74;
		bool IsOtherCurrency = 75;
		bool IsResident = 76;
		string IssueOrganization = 77;
		bool IsYesAccounting = 78;
		bool IsYesBankFinancing = 79;
		bool IsYesBelongPDL = 80;
		bool IsYesBeneficialOwner = 81;
		bool IsYesBusinessIncome = 82;
		bool IsYesCardOperations = 83;
		bool IsYesDeposit = 84;
		bool IsYesForeignPassport = 85;
		bool IsYesInheritance = 86;
		bool IsYesInterest = 87;
		bool IsYesIslamicFinance = 88;
		bool IsYesKZPassport = 89;
		bool IsYesNoCitizenship = 90;
		bool IsYesOtherBusinessPurpose = 91;
		bool IsYesOtherCountryTaxResident = 92;
		bool IsYesOtherIncome = 93;
		bool IsYesPersonalIdentityDocument = 94;
		bool IsYesPersonPDL = 95;
		bool IsYesRealEstate = 96;
		bool IsYesRegistrationCountry = 97;
		bool IsYesRelatedPDL = 98;
		bool IsYesSafeRental = 99;
		bool IsYesSalary = 100;
		bool IsYesScholarship = 101;
		bool IsYesShares = 102;
		bool IsYesUsaBorn = 103;
		bool IsYesUsaBusinessActivity = 104;
		bool IsYesUsaCitizen = 105;
		bool IsYesUsaCurrentAddress = 106;
		bool IsYesUsaCurrentMore31Days = 107;
		bool IsYesUsaEmergencyAddress = 108;
		bool IsYesUsaPaymentAccountOurBank = 109;
		bool IsYesUsaPhoneNumber = 110;
		bool IsYesUsaRegistrationAddress = 111;
		bool IsYesUsaTaxResident = 112;
		bool IsYesUsaTrustOrSignaturePowerOfAttorney = 113;
		string JobTitle = 114;
		string Name = 115;
		string OtherBusinessPurpose = 116;
		string OtherCountry = 117;
		string OtherCurrency = 118;
		string OtherIncome = 119;
		string Patronymic = 120;
		string Phone = 121;
		bool PurposeCardOperations = 122;
		bool PurposeDepositPlacement = 123;
		bool PurposeIslamicFinance = 124;
		bool PurposeSafeRental = 125;
		bool PurposeSettlementService = 126;
		string Region = 127;
		string RepresentativeAttorneyPowerExpDate = 128;
		string RepresentativeAttorneyPowerIssueDate = 129;
		string RepresentativeAttorneyPowerIssueOrg = 130;
		string RepresentativeAttorneyPowerIssuePlace = 131;
		string RepresentativeAttorneyPowerNumber = 132;
		string RepresentativeBirthCountry = 133;
		string RepresentativeBirthDay = 134;
		string RepresentativeBirthMonth = 135;
		string RepresentativeBirthPlace = 136;
		string RepresentativeBirthYear = 137;
		string RepresentativeBuilding = 138;
		string RepresentativeCitizenship = 139;
		string RepresentativeCity = 140;
		string RepresentativeCreateDay = 141;
		string RepresentativeCreateMonth = 142;
		string RepresentativeCreateYear = 143;
		string RepresentativeDistrict = 144;
		string RepresentativeDocExpDay = 145;
		string RepresentativeDocExpMonth = 146;
		string RepresentativeDocExpYear = 147;
		string RepresentativeDocIssueDay = 148;
		string RepresentativeDocIssueMonth = 149;
		string RepresentativeDocIssueYear = 150;
		string RepresentativeDocNumber = 151;
		string RepresentativeDocSeries = 152;
		string RepresentativeDocumentNumber = 153;
		string RepresentativeEmail = 154;
		string RepresentativeFlat = 155;
		string RepresentativeIIN = 156;
		string RepresentativeIssueOrganization = 157;
		string RepresentativeName = 158;
		string RepresentativeOtherCountry = 159;
		string RepresentativePatronymic = 160;
		string RepresentativePhone = 161;
		string RepresentativeRegion = 162;
		string RepresentativeResCountry = 163;
		string RepresentativeSignDate = 164;
		string RepresentativeSignTime = 165;
		string RepresentativeStreet = 166;
		string RepresentativeSurname = 167;
		string RepresentativeTaxResCountry = 168;
		string RepresentativeTempResDocExpDate = 169;
		string RepresentativeTempResDocIssDate = 170;
		string RepresentativeTempResDocIssOrg = 171;
		string RepresentativeTempResDocIssOrganization = 172;
		string RepresentativeTempResDocNumber = 173;
		string ResidentCountry = 174;
		string SignDate = 175;
		string SignTime = 176;
		string Sitizenship = 177;
		string Street = 178;
		string Surname = 179;
		string TaxForeignCountry = 180;
		string TaxNumberForeignCountry = 181;
		string TaxResidentCountry = 182;
		string TempResDocExpDate = 183;
		string TempResDocIssDate = 184;
		string TempResDocIssOrganization = 185;
		string TempResDocNumber = 186;
		string UsaBornPlace = 187;
		string UsaCurrentAddress = 188;
		string UsaEmergencyAddress = 189;
		string UsaPhoneNumber = 190;
		string UsaRegistrationAddress = 191;
		string WorkPlace = 192;
}

message GenerateDocumentForOpeningAccountSMEReq{
  string docType = 1;
  string personIIN = 2;
}

message GenerateDocumentForOpeningAdditionalAccountSMEReq{
  string docType = 1;
  string personIIN = 2;
  repeated currency currencies = 3;
}

message currency {
  string currency = 1;
}

message GenerateAppSaleOfGoodsDocumentSMEIPReq {
  string personIIN = 1;
  string personFullName = 2;
  string organizationName = 3;
  SaleOfGoodsContractData contractData = 4;
  SaleOfGoodsProductData productData = 5;
  SaleOfGoodsBankData bankData = 6;
}

message GenerateBankServiceApplicationSMEIPReq{
  string ipname = 1;
  string personFullName = 2;
  string personIIN = 3;
  string phone = 4;
  string email = 5;
  string okedName = 6;
  string okedCode = 7;
  BankServiceApplicationSMEIPResidencyInfo residencyInfo = 8;
  BankServiceApplicationSMEIPResidentialAddress residentialAddress = 9;
  BankServiceApplicationSMEIPBirthLocation birthLocation = 10;
  google.protobuf.Timestamp birthDate = 11;
  BankServiceApplicationSMEIPIdentityDocument identityDocument = 12;
  BankServiceApplicationSMEIPRegistrationAddress registrationAddress = 13;
  bool isPDL = 14;
  bool isBeneficiary = 15;
  string beneficiaryOwner = 16;
  bool upToLimit = 17;
  BankServiceApplicationSMEIPSourcesOfIncome sourcesOfIncome = 18;
  BankServiceApplicationSMEIPTemporaryResidencyDocument temporaryResidencyDocument = 19;
  Director director = 20;
  bool isLeader = 21;
  google.protobuf.Timestamp createDate = 22;
  BankServiceApplicationSMEIPSign sign = 23;
  string bin = 24;
  BankServiceApplicationSMEIPRegistration registration = 25;
  BankServiceApplicationSMEIPLicense license = 26;
  BankServiceApplicationSMEIPPDLInfo pdlInfo = 27;
}

message Director{
  string inn = 1;
  string directorFullName = 2;
  string post = 3;
  google.protobuf.Timestamp BirthDay = 4;
  BankServiceApplicationSMEIPBirthLocation birthLocation = 5;
  BankServiceApplicationSMEIPIdentityDocument identityDocument = 6;
}

message BankServiceApplicationSMEIPSign{
  string date = 1;
  string time = 2;
}

message BankServiceApplicationSMEIPRegistration{
  string registrationCert = 1;
  google.protobuf.Timestamp registrationDate = 2;
}
message BankServiceApplicationSMEIPLicense{
  bool checkbox = 1;
  string LicenseNumber = 2;
  string Licensiar = 3;
  google.protobuf.Timestamp ValidityDate =4;
  google.protobuf.Timestamp  IssueDate =5 ;
}

message BankServiceApplicationSMEIPPDLInfo{
  bool relatedToPDL = 1;
  bool isBeneficialOwner = 2;
  string beneficialOwnerFullName = 3;
  bool plannedMonthlyOperations = 4;
  bool relatedToPDLFamilyRelation = 5;
}

message BankServiceApplicationSMEIPResidentialAddress {
  string residentialCountry = 1;
  string residentialOtherCountry = 2;
  string residentialDistrict = 3;
  string residentialCity = 4;
  string residentialRegion = 5;
  string residentialStreet = 6;
  string residentialBuilding = 7;
  string residentialFlat = 8;
}

message BankServiceApplicationSMEIPResidencyInfo{
  string residenceCountry = 1;
  string taxResidenceCountry = 2;
  string residenceCountryCode = 3;
  string taxResidenceCountryCode = 4;
}

message BankServiceApplicationSMEIPTemporaryResidencyDocument{
  string docNumber = 1;
  string docSeries = 2;
  google.protobuf.Timestamp docIssueDate = 3;
  string issueOrganization = 4;
  google.protobuf.Timestamp docExpDate = 5;
}

message BankServiceApplicationSMEIPSourcesOfIncome{
  bool wages = 1; // Заработная плата
  bool socialPayments = 2; // Стипендия, пособие, пенсия
  bool inheritance = 3; // Наследство, дарение, безвозмездная помощь
  bool bankFinancing = 5; // Финансирование Банка
  bool returnOnInvestment = 6; // Прибыль от инвестиций
  bool companyShare = 7; // Акции, доли капитала
  bool sale = 8; // Продажа недвижимости или иного имущества
  bool businessActivities = 9; // Доход от предпринимательской деятельности
  optional string otherIncome = 10; //Иные доходы
}

message BankServiceApplicationSMEIPLicenseCert {
  string number = 1;
  string licensiar = 2;
  google.protobuf.Timestamp validityEndDate = 3;
  google.protobuf.Timestamp issueDate = 4;
}

message BankServiceApplicationSMEIPRegistrationCert {
  string number = 1;
  google.protobuf.Timestamp regDate = 2;
  optional BankServiceApplicationSMEIPLicenseCert cert = 3;
}

message BankServiceApplicationSMEIPBirthLocation {
  string birthCountry = 1;
  string birthPlace = 2;
  string citizenship = 3;
}

message BankServiceApplicationSMEIPIdentityDocument {
  string docNumber = 1;
  string docSeries = 2;
  string docCode = 3;
  google.protobuf.Timestamp docIssueDate = 4;
  string issueOrganization = 5;
  google.protobuf.Timestamp docExpDate = 6;
}

message BankServiceApplicationSMEIPRegistrationAddress {
  string otherCountry = 1;
  string district = 2;
  string city = 3;
  string region = 4;
  string street = 5;
  string building = 6;
  string flat = 7;
}

message GenerateAppSaleOfGoodsDocumentReq {
  string personIIN = 1;
  string personFullName = 2;
  SaleOfGoodsContractData contractData = 3;
  SaleOfGoodsProductData productData = 4;
  SaleOfGoodsBankData bankData = 5;
  SaleOfGoodsRefinancingData refData = 6;
}

message SaleOfGoodsRefinancingData {
  string bankName = 1;
  string bankBin = 2;
  string loanContractNumber = 3;
  string principalAmount = 4;
  string accountDetails = 5;
}

message SaleOfGoodsContractData {
  string contractNumber = 1;
  string contractDate = 2;
}

message SaleOfGoodsProductData {
  string productName = 1;
  double productQty = 2;
  string productMarkupAmount = 3;
  string productTotalCost = 4;
  string productExcludeCost = 5;
  string paymentDate = 6;
}

message SaleOfGoodsBankData {
  string bankAccountPayment = 1;
  string buyerName = 2;
  string buyerID = 3;
  string buyerAddress = 4;
  string userBankAccount = 5;
  string bankBIN = 6;
  LocalizationData reprPosition = 7;
  string reprFullName = 8;
  LocalizationData bankName = 9;
}

message LocalizationData {
  string kk = 1;
  string ru = 2;
}

message SaveSignedBtsDocumentsReq {
  repeated SignedBtsDocumentData documents = 1;
}

message SignedBtsDocumentData {
  string originDocID = 1;
  bytes bytes = 2;
}

message SaveSignedBtsDocumentsResp {
  repeated DocumentResp documents = 1;
}

message GetDocumentContentReq {
  string id = 1;
}

message GetDocumentContentResp {
  string id = 1;
  string docType = 2;
  string docTitle = 3;
  bytes bytes = 4;
}


enum LK {
  LK_RU = 0;
  LK_KZ = 1;
}

message GeneratePaymentByAccountReceiptReq {
  LK lang = 1;
  string paymentNumber = 2;
  string date = 3;
  string status = 4;
  string payerBankName = 5;
  string payerFullName = 6;
  string payerAccount = 7;
  optional GeneratePaymentByAccountReceiptReqRealSide payerRealSide = 8;
  string beneficiaryBankName = 9;
  string beneficiaryIin = 10;
  string beneficiaryFullName = 11;
  string beneficiaryAccount = 12;
  string purposeCode = 13;
  string beneficiaryCode = 14;
  string paymentDetails = 15;
  optional GeneratePaymentByAccountReceiptReqRealSide beneficiaryRealSide = 16;
  string amount = 17;
  string commission = 18;
  string totalAmount = 19;
}

message GeneratePaymentByAccountReceiptReqRealSide {
  string fullName = 1;
  string iin = 2;
  string countryName = 3;
}

message GeneratePaymentForMobileReceiptReq {
  LK lang = 1;
  string paymentNumber = 2;
  string date = 3;
  string status = 4;
  string payerAccount = 5;
  string payerFullName = 6;
  string operatorName = 7;
  string phoneNumber = 8;
  string amount = 9;
  string commission = 10;
  string totalAmount = 11;
}

message GenerateInternalPaymentByPhoneNumberReceiptReq {
  LK lang = 1;
  string paymentNumber = 2;
  string date = 3;
  string status = 4;
  string payerAccount = 5;
  string payerShortName = 6;
  string beneficiaryShortName = 7;
  string beneficiaryPhoneNumber = 8;
  string amount = 9;
  string commission = 10;
  string totalAmount = 11;
}

message GeneratePaymentByAccountReceiptSmeReq {
  LK lang = 1;
  string paymentNumber = 2;
  string date = 3;
  optional string time = 4;
  string status = 5;
  string totalAmount = 6;
  string totalAmountInWords = 7;
  string paymentDetails = 8;
  string purposeCode = 9;
  string valueDate = 10;

  // Данные отправителя
  string payerFullName = 11;
  string payerIin = 12;
  string payerAccount = 13;
  string payerCode = 14;
  string payerBankName = 15;
  string payerBankBic = 16;

  // Данные получателя
  string beneficiaryFullName = 17;
  string beneficiaryIin = 18;
  string beneficiaryAccount = 19;
  string beneficiaryCode = 20;
  string beneficiaryBankName = 21;
  string beneficiaryBankBic = 22;

  // Детали фактических сторон
  optional GeneratePaymentByAccountReceiptReqRealSide payerRealSide = 23;
  optional GeneratePaymentByAccountReceiptReqRealSide beneficiaryRealSide = 24;
}

message GenerateBankingServiceApplicationDocumentReq {
  BankingServiceApplicationClient client = 1;
  BankingServiceApplicationResidencyInfo residencyInfo = 2;
  BankingServiceApplicationBirthDate birthDate = 3;
  BankingServiceApplicationBirthLocation birthLocation = 4;
  BankingServiceApplicationIdentityDocument identityDocument = 5;
  BankingServiceApplicationRegistrationAddress registrationAddress = 6;
  BankingServiceApplicationResidentialAddress residentialAddress = 7;
  BankingServiceApplicationClientContacts clientContacts = 8;
  //BankingServiceApplicationClientAdditionalInfo ClientAdditionalInfo = 9;
  //BankingServiceApplicationNonResidentInfo nonResidentInfo = 10;
  //BankingServiceApplicationIncomeSources incomeSources = 11;
  //BankingServiceApplicationBusinessTypes businessTypes = 12;
  //BankingServiceApplicationResidencyOECDQuestionnaire residencyOECDQuestionnaire = 13;
  //BankingServiceApplicationClientRepresentative clientRepresentative = 14;
  //BankingServiceApplicationRepresentativeAddress representativeAddress = 15;
  //BankingServiceApplicationFiduciaryPowerDocument fiduciaryPowerDocument = 16;
  //BankingServiceApplicationTemporaryResidencyDocument temporaryResidencyDocument = 17;
  BankingServiceApplicationApplicationForm applicationForm = 18;
  BankingServiceApplicationRemoteBankingDetails remoteBankingDetails = 19;
  string product = 20;
}

message BankingServiceApplicationClient {
  string fio = 1;
  string iin = 2;
}

message BankingServiceApplicationResidencyInfo {
  string residenceCountry = 1;
  string taxResidenceCountry = 2;
  string residenceCountryCode = 3;
  string taxResidenceCountryCode = 4;
}

message BankingServiceApplicationBirthDate {
  google.protobuf.Timestamp BirthDate = 1;
}

message BankingServiceApplicationBirthLocation {
  string birthCountry = 1;
  string birthPlace = 2;
  string citizenship = 3;
}

message BankingServiceApplicationIdentityDocument {
  string docNumber = 1;
  //string docSeries = 2;
  google.protobuf.Timestamp docIssueDate = 3;
  string issueOrganization = 4;
  google.protobuf.Timestamp docExpDate = 5;
}

message BankingServiceApplicationRegistrationAddress {
  //string otherCountry = 1;
  string district = 2;
  string city = 3;
  string region = 4;
  string street = 5;
  string building = 6;
  string flat = 7;
}

message BankingServiceApplicationResidentialAddress {
  string residentialCountry = 1;
  //string residentialOtherCountry = 2;
  string residentialDistrict = 3;
  string residentialCity = 4;
  string residentialRegion = 5;
  string residentialStreet = 6;
  string residentialBuilding = 7;
  string residentialFlat = 8;
}

message BankingServiceApplicationClientContacts {
  string phone = 1;
  string email = 2;
}

message BankingServiceApplicationClientAdditionalInfo {
  string beneficialOwnerName = 1;
}

message BankingServiceApplicationNonResidentInfo {
  string tempResDocNumber = 1;
  string tempResDocIssOrganization = 2;
  string workPlace = 3;
  string education = 4;
  google.protobuf.Timestamp tempResDocIssDate = 5;
  google.protobuf.Timestamp tempResDocExpDate = 6;
  string jobTitle = 7;
  string activityType = 8;
}

message BankingServiceApplicationIncomeSources {
  string otherIncome = 1;
}

message BankingServiceApplicationBusinessTypes {
  string otherBusinessType = 1;
}

message  BankingServiceApplicationResidencyOECDQuestionnaire {
  string taxForeignCountry = 1;
  string taxNumberForeignCountry = 2;
}


message BankingServiceApplicationClientRepresentative {
  string representativeSurname = 1;
  string representativeName = 2;
  string representativePatronymic = 3;
  string representativeIin = 4;
  string representativeResCountry = 5;
  string representativeTaxResCountry = 6;
  google.protobuf.Timestamp representativeBirthDate = 7;
  string representativeBirthCountry = 8;
  string representativeBirthPlace = 9;

  string representativePhone = 10;
  string representativeEMail = 11;
  string representativeDocNumber = 12;
  string representativeDocSeries = 13;
  google.protobuf.Timestamp representativeDocIssueDate = 14;
  string representativeIssueOrganization = 15;
  google.protobuf.Timestamp representativeDocExpDate = 16;
}

message BankingServiceApplicationRepresentativeAddress {
  string representativeOtherCountry = 1;
  string representativeDistrict = 2;
  string representativeCity = 3;
  string representativeRegion = 4;
  string representativeStreet = 5;
  string representativeBuilding = 6;
  string representativeFlat = 7;
}

message BankingServiceApplicationFiduciaryPowerDocument{
  string attorneyPowerType = 1;
  google.protobuf.Timestamp attorneyPowerIssueDate = 2;
  string attorneyPowerIssueOrg = 3;
  string attorneyPowerNumber = 4;
  google.protobuf.Timestamp attorneyPowerExpDate = 5;
  string attorneyPowerIssuePlace = 6;
}

message BankingServiceApplicationTemporaryResidencyDocument{
  string reprTempResDocNumber = 1;
  string reprTempResDocIssOrg = 2;
  google.protobuf.Timestamp reprTempResDocIssDate = 3;
  google.protobuf.Timestamp reprTempResDocExpDate = 4;
}

message BankingServiceApplicationApplicationForm {
  google.protobuf.Timestamp createDate = 1;
}

message BankingServiceApplicationRemoteBankingDetails {
  string signDate = 1;
  string signTime = 2;
}

message GeneratePersonalDataAgreementReq {
  string fullName = 1;
  string iin = 2;
}

message GeneratePersonalDataAgreementSMEReq {
  string city = 1;
  string fullName = 2;
  string iin = 3;
  string bank_name_kz = 4;      // Bank name in Kazakh
  string bank_name_ru = 5;      // Bank name in Russian
  string bank_site_documents = 6; // Bank website URL with documents
}

message GetDocumentSignReq {
  optional string ID = 1;
  optional string documentType = 2;
  optional string documentID = 3;
  optional string userID = 4;
}

message GetDocumentSignResp {
  string ID = 1;
  string documentType = 2;
  string documentID = 3;
  string userID = 4;
  google.protobuf.Timestamp signedAt = 5;
}

message GenerateCreditContractWithRepaymentScheduleSMEIPReq {
  GenerateCreditContractWithRepaymentScheduleReqContract contract = 1;
  GenerateCreditContractWithRepaymentScheduleReqBank bank = 3;
  GenerateCreditContractWithRepaymentScheduleReqUser user = 4;
  GenerateCreditContractWithRepaymentScheduleReqProduct product = 5;
  string markupAmountSum = 6;
  string paymentScheduleDate = 7;
  string financingTerm = 8;
  repeated GenerateCreditContractWithRepaymentScheduleReqPayment payments = 9;
  GenerateCreditContractWithRepaymentScheduleReqPaymentsSummary paymentsSummary = 10;
  GenerateCreditContractWithRepaymentScheduleReqLocalizedValue currency = 11;
}

message GenerateSprProtocolReq {
  LocalizationData bankName = 1;
  string sprID = 2;
  string date = 3;
  string applicationID = 4;
  string personFullName = 5;
  string personIIN = 6;
  string sprKDN = 7;
  string sprResult = 8;
  SprApprovedConditions approvedConditions = 9;
  string sprKDD = 10;
}

message SprApprovedConditions {
  string approvedAmount = 1;
  string approvedTerm = 2;
  string approvedInterest = 3;
  string approvedInterestAmount = 4;
  string repaymentDate = 5;
}

message CreateOrderBsasReq {
  CreateOrderHeaderRequest header = 1;
  repeated CreateOrderRequest request = 2;
}

message CreateOrderHeaderRequest {
  string memberShortName = 1;
  string uuid = 2;
}

message CreateOrderRequest {
  string serialNumber = 1;
  string bidOption = 2;
  string otcOption = 3;
  string stbOption = 4;
  string productCode = 5;
  string purchaseType = 6;
  string clientName = 7;
  string currency = 8;
  string bidValue = 9;
  string valueDate = 10;
  string tenor = 11;
  string otcCounterParty = 12;
  string otcMurabaha = 13;
  string otcMurabahaValue = 14;
  string eCertNo = 15;
}

message CreateOrderBsasResp {
  CreateOrderHeader header = 1;
  repeated CreateOrderBody body = 2;
}

message CreateOrderHeader {
  string memberShortName = 1;
  string uuid = 2;
  optional  string errorCode = 3;
  optional string errorMsg = 4;
}

message CreateOrderBody {
  int32 serialNumber = 1;
  int32 statusCode = 2;
  string statusMessage = 3;
}

message GetOrderResultBsasReq{
  CreateOrderHeaderRequest header = 1;
  GetOrderResultRequest request = 2;
}

message GetOrderResultRequest{
  optional string serialNumbers =1;
  string forceYN =2;
  optional string maxWaitTime =3;
  optional string waitAllDoneYN =4;
}

message GetOrderResultBsasResp {
  CreateOrderHeader header = 1;
  OrderStatus status =2;
  repeated GetOrderResultBody body = 3;
}

message OrderStatus {
  int32 totalOrderCount = 1;
  int32 processingCount=  2;
}

message GetOrderResultBody {
  int32 serialNumber = 1;
  string bidOption = 2;
  string otcOption = 3;
  string stbOption = 4;
  string productCode = 5;
  string purchaseType = 6;
  string clientName = 7;
  string currency = 8;
  string bidValue = 9;
  string valueDate = 10;
  string tenor = 11;
  string otcCounterParty = 12;
  string otcMurabaha = 13;
  string otcMurabahaValue = 14;
  string ecertNo = 15;
  string bidErrNo = 16;
  string bidMsg = 17;
  string otcErrNo = 18;
  string otcMsg = 19;
  string stbErrNo = 20;
  string stbMsg = 21;
  string regTime = 22;
  string orderTime = 23;
  string resultTime = 24;
  string purchaseTime = 25;
  string reportTime = 26;
  string sellingTime = 27;
  string unit = 28;
  string price = 29;
}


message  GetOtcReportInfoReq {
  GetOtcReportInfoInput input =1;
}

message  GetOtcReportInfoInput {
  string ecertno = 1;
  string membershortname = 2;
}

message GetOtcReportInfoResp {
  string ecertno = 1;
  string seller = 2;
  string buyer = 3;
  string total_value = 4;
  string currency = 5;
  string price = 6;
  string price_myr_equivalent = 7;
  string murabaha_value =8;
  string reporting_time_date = 9;
  string value_date = 10;
  string pname = 11;
  string pvolume = 12;
  repeated LineItem line = 13;
}

message LineItem {
  string supplier = 1;
  string volume = 2;
}

message GetSellToBMISReq {
  GetSellToBMISInput input = 1;
}

message GetSellToBMISInput{
  string ecertno = 1;
  string membershortname = 2;
}

message GetSellToBMISResp {
  string ecertno = 1;
  string seller = 2;
  string buyer = 3;
  string total_value = 4;
  string currency = 5;
  string price = 6;
  string price_myr_equivalent = 7;
  string selling_time_date = 8;
  string value_date = 9;
  string pname = 10;
  string pvolume = 11;
  repeated LineItem line = 12;
}

message GetSystemMappingReq {
  string eCertNo = 1;
  string memberShortName = 2;
}

message GetSystemMappingResp{
  string success_yn = 1;            // Успех: null или N
  string msg = 2;                   // Причина отклонения
  string ecert_no = 3;              // Номер электронного сертификата
  string buyer = 4;                 // Имя покупателя
  string owner = 5;                 // Имя владельца
  string bid_no = 6;                // Номер заявки заказа
  string total_value = 7;           // Итоговая сумма
  string currency = 8;              // Валюта
  string price = 9;                 // Цена
  string price_myr_equiv = 10;      // Цена продукта, эквивалентная MYR
  string purchase_time_date = 11;   // Дата и время покупки
  string value_date = 12;           // Дата валютирования
  string pname = 13;                // Наименование продукта
  string pvolume = 14;              // Объем
  repeated BidXMLLine line = 15;    // Строки поставщиков
}

message BidXMLLine {
  string supplier = 1;
  string Volume = 2;
}

message GenerateOtcTemplateCertificateReq {
  string certificateNumber = 1;
  string bidNo = 2;
  string reportingTime = 3;
  string valueDate = 4;
  string productCode = 5;
  string unit = 6;
  string volume = 7;
  string currency = 8;
  string price = 9;
  string priceMyrEquivalent = 10;
  string value = 11;
  string murabahaValue = 12;
  string seller = 13;
  string buyer = 14;
}

message GenerateSTBTransactionTemplateCertificateReq {
  string certificateNumber = 1;
  string bidNo = 2;
  string sellingTime = 3;
  string valueDate = 4;
  string productCode = 5;
  string unit = 6;
  string volume = 7;
  string currency = 8;
  string price = 9;
  string priceMyrEquivalent = 10;
  string value = 11;
  string seller = 12;
}


message GenerateDepositApplicationDocumentReq {
    Locale locale = 1;
    string userFullName = 2;
    string userIin = 3;
    string depositProductCode = 4;
    string depositProductName = 5;
    string depositCurrency = 6;
    // срок действия депозита
    uint32 depositMonthTerm = 7;
    // 0.156 = 15.6%
    double depositProfitRate = 8;
    // сумма депозита
    double depositAmount = 9;
    // сумма профита депозита
    double depositProfitAmount = 10;
    // метод выплаты CARD, DEPOSIT
    string payoutMethod = 11;
    // сумма первоначального взноса
    double depositStartAmount = 12;
    // годовая эффективная ожидаемая доходность
    double depositYearProfitRate = 13;
    // срок депозита начало
    string beginDate = 14;
    // срок депозита конец
    string endDate = 15;
    // размер агентского вознаграждения %
    double agentProfitRate = 17;
    // размер агентского вознаграждения T
    double agentProfitAmount = 18;
    // номер аккаунта
    string accountNumber = 19;

}

message GenerateDocumentBIDReq {
  string ecertno = 1;
  string buyer = 2;
  string owner = 3;
  string bidNo = 4;
  string purchaseTimeDate = 5;
  string valueDate = 6;
  string productName = 7;
  string unit = 8;
  string volume = 9;
  string currency = 10;
  string price = 11;
  string priceMyrEquivalent = 12;
  string value = 13;
}

message GenerateCollectionIncomeReq {
  string iin = 3;
  repeated IncomeData incomeData = 4;
}

message IncomeData {
  string Date = 1;
  string paymentPeriod = 2;
  string paymentSum = 3;
  string totalPaymentSum = 4;
  string bin = 5;
  string orgName = 6;
}

message GeneratePaymentOrderSMEIPReq {
  string transactionNumber = 1;     // Номер транзакции
  string transactionStatus = 2;     // Статус транзакции (COMPLETED - Исполнено/IN_PROGRESS - В обработке/REJECTED - Отменен)
  string payerName = 3;             // Наименование плательщика
  string payerBINIIN = 4;           // ИИН/БИН плательщика
  string payerAccount = 5;          // ИИК плательщика
  string payerKod = 6;              // Код плательщика
  string payerCountry = 7;          // Страна резидентства плательщика
  string payerBankName = 8;         // Наименование банка плательщика
  string payerBankBIC = 9;          // БИК банка плательщика
  string beneficiaryName = 10;      // Наименование получателя
  string beneficiaryBINIIN = 11;    // ИИН/БИН получателя
  string beneficiaryAccount = 12;   // ИИК получателя
  string beneficiaryKbe = 13;       // КБЕ получателя
  string beneficiaryCountry = 14;   // Страна резидентства получателя
  string beneficiaryBankName = 15;  // Наименование банка получателя
  string beneficiaryBankBIC = 16;   // БИК банка получателя
  string purposeDetails = 17;       // Назначение платежа
  string purposeCode = 18;          // КНП (код назначения платежа)
  string kbk = 19;                  // КБК (код бюджетной классификации)
  string paymentPeriod = 20;        // Период платежа
  string valueDate = 21;            // Дата валютирования
  string referenceDate = 22;        // Дата проведения
  string writtenAmount = 23;        // Сумма прописью
  string amount = 24;               // Сумма платежа
  string signatoryA = 25;           // Руководитель
  string signatoryB = 26;           // Главный бухгалтер
  string realBeneficiaryName = 27; // ФИО фактического получателя
  string realBeneficiaryBINIIN = 28; // ИИН фактического получателя
  string realBeneficiaryCountry = 29; // Страна резидентства фактического получателя
  string employeeOrderNumber = 30; // Порядковый номер платежного поручения для сотрудника (1, 2, 3, ...)
  LK lang = 31; // Язык документа
}

// Первая страница платежного поручения с реестром
message GenerateMassPaymentOrderSMEIPReq {
  string transactionNumber = 1; // Номер транзакции (Заголовок)
  string transactionStatus = 2; // Статус транзакции
  string payerBankName = 3; // Банк отправителя
  string payerBankBIC = 4; // БИК банка
  string beneficiaryBankName = 5; // Банк бенефициара
  string beneficiaryBankBIC = 6; // БИК банка
  string beneficiaryAccount = 7; // ИИК
  uint64 countEmployeeList = 8; // количество платежных поручений
  string valueDate = 9; // Дата валютирования
  string referenceDate = 10; // Дата проведения
  string writtenAmount = 11; // Сумма прописью
  string amount = 12; // Сумма
  string signatoryA = 13; // Руководитель
  string signatoryB = 14; // Главный бухгалтер
  repeated GeneratePaymentOrderSMEIPReq paymentOrder = 15; // список платежных поручений
  LK lang = 16; // Язык документа
}

message SignDocumentWebReq {
  string docID = 1;
  string phoneNumber = 2;
}

message SignDocumentWebResp {
  string attemptID = 1;
  int32 retryTime = 2;
}
message ConfirmSignDocumentWebReq {
  string attemptID = 1;
  string code = 2;
  string phoneNumber = 3;
  string docID = 4;
}

message ConfirmSignDocumentWebResp {
  string id = 1;
  string title = 2;
  string type = 3;
  int32 version = 4;
  string fileLink = 5;
  bool signed = 6;
}

message DownloadDocumentReq {
  string docName = 1;
  string bucketName = 2;
}

message DownloadDocumentResp {
  string fileExt = 1;
  bytes file = 2;
  string fileName = 3;
}

message GenerateDepositCloseApplicationDocumentReq {
  Locale locale = 1;
  string userFullName = 2;
  string userIin = 3;
  string depositProductCode = 4;
  string depositProductName = 5;
  // срок депозита начало
  string beginDate = 6;
  // дата заполнения
  string createDate = 7;
}
