// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v6.32.1
// source: specs/proto/kgd-bridge/kgd-bridge.proto

package kgd_bridge

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HealthCheckReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckReq) Reset() {
	*x = HealthCheckReq{}
	mi := &file_specs_proto_kgd_bridge_kgd_bridge_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckReq) ProtoMessage() {}

func (x *HealthCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kgd_bridge_kgd_bridge_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckReq.ProtoReflect.Descriptor instead.
func (*HealthCheckReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_kgd_bridge_kgd_bridge_proto_rawDescGZIP(), []int{0}
}

type HealthCheckResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckResp) Reset() {
	*x = HealthCheckResp{}
	mi := &file_specs_proto_kgd_bridge_kgd_bridge_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResp) ProtoMessage() {}

func (x *HealthCheckResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kgd_bridge_kgd_bridge_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResp.ProtoReflect.Descriptor instead.
func (*HealthCheckResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_kgd_bridge_kgd_bridge_proto_rawDescGZIP(), []int{1}
}

func (x *HealthCheckResp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type GetDebtsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PersonIIN     string                 `protobuf:"bytes,1,opt,name=personIIN,proto3" json:"personIIN,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDebtsReq) Reset() {
	*x = GetDebtsReq{}
	mi := &file_specs_proto_kgd_bridge_kgd_bridge_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDebtsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDebtsReq) ProtoMessage() {}

func (x *GetDebtsReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kgd_bridge_kgd_bridge_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDebtsReq.ProtoReflect.Descriptor instead.
func (*GetDebtsReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_kgd_bridge_kgd_bridge_proto_rawDescGZIP(), []int{2}
}

func (x *GetDebtsReq) GetPersonIIN() string {
	if x != nil {
		return x.PersonIIN
	}
	return ""
}

type GetPersonDataReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PersonIIN     string                 `protobuf:"bytes,1,opt,name=personIIN,proto3" json:"personIIN,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPersonDataReq) Reset() {
	*x = GetPersonDataReq{}
	mi := &file_specs_proto_kgd_bridge_kgd_bridge_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPersonDataReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPersonDataReq) ProtoMessage() {}

func (x *GetPersonDataReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kgd_bridge_kgd_bridge_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPersonDataReq.ProtoReflect.Descriptor instead.
func (*GetPersonDataReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_kgd_bridge_kgd_bridge_proto_rawDescGZIP(), []int{3}
}

func (x *GetPersonDataReq) GetPersonIIN() string {
	if x != nil {
		return x.PersonIIN
	}
	return ""
}

type GetPersonDataResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FullNameRu    string                 `protobuf:"bytes,1,opt,name=fullNameRu,proto3" json:"fullNameRu,omitempty"`
	FullNameKz    string                 `protobuf:"bytes,2,opt,name=fullNameKz,proto3" json:"fullNameKz,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPersonDataResp) Reset() {
	*x = GetPersonDataResp{}
	mi := &file_specs_proto_kgd_bridge_kgd_bridge_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPersonDataResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPersonDataResp) ProtoMessage() {}

func (x *GetPersonDataResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kgd_bridge_kgd_bridge_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPersonDataResp.ProtoReflect.Descriptor instead.
func (*GetPersonDataResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_kgd_bridge_kgd_bridge_proto_rawDescGZIP(), []int{4}
}

func (x *GetPersonDataResp) GetFullNameRu() string {
	if x != nil {
		return x.FullNameRu
	}
	return ""
}

func (x *GetPersonDataResp) GetFullNameKz() string {
	if x != nil {
		return x.FullNameKz
	}
	return ""
}

type GetDebtsResp struct {
	state                       protoimpl.MessageState `protogen:"open.v1"`
	TotalArrear                 float64                `protobuf:"fixed64,1,opt,name=totalArrear,proto3" json:"totalArrear,omitempty"`
	TotalTaxArrear              float64                `protobuf:"fixed64,2,opt,name=totalTaxArrear,proto3" json:"totalTaxArrear,omitempty"`
	PensionContributionArrear   float64                `protobuf:"fixed64,3,opt,name=pensionContributionArrear,proto3" json:"pensionContributionArrear,omitempty"`
	SocialContributionArrear    float64                `protobuf:"fixed64,4,opt,name=socialContributionArrear,proto3" json:"socialContributionArrear,omitempty"`
	SocialHealthInsuranceArrear float64                `protobuf:"fixed64,5,opt,name=socialHealthInsuranceArrear,proto3" json:"socialHealthInsuranceArrear,omitempty"`
	TaxOrgInfo                  []*TaxOrgInfo          `protobuf:"bytes,6,rep,name=taxOrgInfo,proto3" json:"taxOrgInfo,omitempty"`
	unknownFields               protoimpl.UnknownFields
	sizeCache                   protoimpl.SizeCache
}

func (x *GetDebtsResp) Reset() {
	*x = GetDebtsResp{}
	mi := &file_specs_proto_kgd_bridge_kgd_bridge_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDebtsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDebtsResp) ProtoMessage() {}

func (x *GetDebtsResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kgd_bridge_kgd_bridge_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDebtsResp.ProtoReflect.Descriptor instead.
func (*GetDebtsResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_kgd_bridge_kgd_bridge_proto_rawDescGZIP(), []int{5}
}

func (x *GetDebtsResp) GetTotalArrear() float64 {
	if x != nil {
		return x.TotalArrear
	}
	return 0
}

func (x *GetDebtsResp) GetTotalTaxArrear() float64 {
	if x != nil {
		return x.TotalTaxArrear
	}
	return 0
}

func (x *GetDebtsResp) GetPensionContributionArrear() float64 {
	if x != nil {
		return x.PensionContributionArrear
	}
	return 0
}

func (x *GetDebtsResp) GetSocialContributionArrear() float64 {
	if x != nil {
		return x.SocialContributionArrear
	}
	return 0
}

func (x *GetDebtsResp) GetSocialHealthInsuranceArrear() float64 {
	if x != nil {
		return x.SocialHealthInsuranceArrear
	}
	return 0
}

func (x *GetDebtsResp) GetTaxOrgInfo() []*TaxOrgInfo {
	if x != nil {
		return x.TaxOrgInfo
	}
	return nil
}

type TaxOrgInfo struct {
	state                       protoimpl.MessageState `protogen:"open.v1"`
	TotalArrear                 float64                `protobuf:"fixed64,1,opt,name=totalArrear,proto3" json:"totalArrear,omitempty"`
	TotalTaxArrear              float64                `protobuf:"fixed64,2,opt,name=totalTaxArrear,proto3" json:"totalTaxArrear,omitempty"`
	PensionContributionArrear   float64                `protobuf:"fixed64,3,opt,name=pensionContributionArrear,proto3" json:"pensionContributionArrear,omitempty"`
	SocialContributionArrear    float64                `protobuf:"fixed64,4,opt,name=socialContributionArrear,proto3" json:"socialContributionArrear,omitempty"`
	SocialHealthInsuranceArrear float64                `protobuf:"fixed64,5,opt,name=socialHealthInsuranceArrear,proto3" json:"socialHealthInsuranceArrear,omitempty"`
	TaxPayerInfo                *TaxPayerInfo          `protobuf:"bytes,6,opt,name=taxPayerInfo,proto3" json:"taxPayerInfo,omitempty"`
	unknownFields               protoimpl.UnknownFields
	sizeCache                   protoimpl.SizeCache
}

func (x *TaxOrgInfo) Reset() {
	*x = TaxOrgInfo{}
	mi := &file_specs_proto_kgd_bridge_kgd_bridge_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaxOrgInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaxOrgInfo) ProtoMessage() {}

func (x *TaxOrgInfo) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kgd_bridge_kgd_bridge_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaxOrgInfo.ProtoReflect.Descriptor instead.
func (*TaxOrgInfo) Descriptor() ([]byte, []int) {
	return file_specs_proto_kgd_bridge_kgd_bridge_proto_rawDescGZIP(), []int{6}
}

func (x *TaxOrgInfo) GetTotalArrear() float64 {
	if x != nil {
		return x.TotalArrear
	}
	return 0
}

func (x *TaxOrgInfo) GetTotalTaxArrear() float64 {
	if x != nil {
		return x.TotalTaxArrear
	}
	return 0
}

func (x *TaxOrgInfo) GetPensionContributionArrear() float64 {
	if x != nil {
		return x.PensionContributionArrear
	}
	return 0
}

func (x *TaxOrgInfo) GetSocialContributionArrear() float64 {
	if x != nil {
		return x.SocialContributionArrear
	}
	return 0
}

func (x *TaxOrgInfo) GetSocialHealthInsuranceArrear() float64 {
	if x != nil {
		return x.SocialHealthInsuranceArrear
	}
	return 0
}

func (x *TaxOrgInfo) GetTaxPayerInfo() *TaxPayerInfo {
	if x != nil {
		return x.TaxPayerInfo
	}
	return nil
}

type TaxPayerInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IinBin         string                 `protobuf:"bytes,1,opt,name=iinBin,proto3" json:"iinBin,omitempty"`
	NameRu         string                 `protobuf:"bytes,3,opt,name=nameRu,proto3" json:"nameRu,omitempty"`
	NameKk         string                 `protobuf:"bytes,4,opt,name=nameKk,proto3" json:"nameKk,omitempty"`
	NameQq         string                 `protobuf:"bytes,5,opt,name=nameQq,proto3" json:"nameQq,omitempty"`
	BccArrearsInfo []*BccArrearsInfo      `protobuf:"bytes,6,rep,name=bccArrearsInfo,proto3" json:"bccArrearsInfo,omitempty"`
	TaxArrear      float64                `protobuf:"fixed64,7,opt,name=taxArrear,proto3" json:"taxArrear,omitempty"`
	PoenaArrear    float64                `protobuf:"fixed64,8,opt,name=poenaArrear,proto3" json:"poenaArrear,omitempty"`
	PercentArrear  float64                `protobuf:"fixed64,9,opt,name=percentArrear,proto3" json:"percentArrear,omitempty"`
	FineArrear     float64                `protobuf:"fixed64,10,opt,name=fineArrear,proto3" json:"fineArrear,omitempty"`
	TotalArrear    float64                `protobuf:"fixed64,11,opt,name=totalArrear,proto3" json:"totalArrear,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *TaxPayerInfo) Reset() {
	*x = TaxPayerInfo{}
	mi := &file_specs_proto_kgd_bridge_kgd_bridge_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaxPayerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaxPayerInfo) ProtoMessage() {}

func (x *TaxPayerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kgd_bridge_kgd_bridge_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaxPayerInfo.ProtoReflect.Descriptor instead.
func (*TaxPayerInfo) Descriptor() ([]byte, []int) {
	return file_specs_proto_kgd_bridge_kgd_bridge_proto_rawDescGZIP(), []int{7}
}

func (x *TaxPayerInfo) GetIinBin() string {
	if x != nil {
		return x.IinBin
	}
	return ""
}

func (x *TaxPayerInfo) GetNameRu() string {
	if x != nil {
		return x.NameRu
	}
	return ""
}

func (x *TaxPayerInfo) GetNameKk() string {
	if x != nil {
		return x.NameKk
	}
	return ""
}

func (x *TaxPayerInfo) GetNameQq() string {
	if x != nil {
		return x.NameQq
	}
	return ""
}

func (x *TaxPayerInfo) GetBccArrearsInfo() []*BccArrearsInfo {
	if x != nil {
		return x.BccArrearsInfo
	}
	return nil
}

func (x *TaxPayerInfo) GetTaxArrear() float64 {
	if x != nil {
		return x.TaxArrear
	}
	return 0
}

func (x *TaxPayerInfo) GetPoenaArrear() float64 {
	if x != nil {
		return x.PoenaArrear
	}
	return 0
}

func (x *TaxPayerInfo) GetPercentArrear() float64 {
	if x != nil {
		return x.PercentArrear
	}
	return 0
}

func (x *TaxPayerInfo) GetFineArrear() float64 {
	if x != nil {
		return x.FineArrear
	}
	return 0
}

func (x *TaxPayerInfo) GetTotalArrear() float64 {
	if x != nil {
		return x.TotalArrear
	}
	return 0
}

type BccArrearsInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TaxArrear     float64                `protobuf:"fixed64,1,opt,name=taxArrear,proto3" json:"taxArrear,omitempty"`
	PoenaArrear   float64                `protobuf:"fixed64,2,opt,name=poenaArrear,proto3" json:"poenaArrear,omitempty"`
	PercentArrear float64                `protobuf:"fixed64,3,opt,name=percentArrear,proto3" json:"percentArrear,omitempty"`
	FineArrear    float64                `protobuf:"fixed64,4,opt,name=fineArrear,proto3" json:"fineArrear,omitempty"`
	TotalArrear   float64                `protobuf:"fixed64,5,opt,name=totalArrear,proto3" json:"totalArrear,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BccArrearsInfo) Reset() {
	*x = BccArrearsInfo{}
	mi := &file_specs_proto_kgd_bridge_kgd_bridge_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BccArrearsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BccArrearsInfo) ProtoMessage() {}

func (x *BccArrearsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kgd_bridge_kgd_bridge_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BccArrearsInfo.ProtoReflect.Descriptor instead.
func (*BccArrearsInfo) Descriptor() ([]byte, []int) {
	return file_specs_proto_kgd_bridge_kgd_bridge_proto_rawDescGZIP(), []int{8}
}

func (x *BccArrearsInfo) GetTaxArrear() float64 {
	if x != nil {
		return x.TaxArrear
	}
	return 0
}

func (x *BccArrearsInfo) GetPoenaArrear() float64 {
	if x != nil {
		return x.PoenaArrear
	}
	return 0
}

func (x *BccArrearsInfo) GetPercentArrear() float64 {
	if x != nil {
		return x.PercentArrear
	}
	return 0
}

func (x *BccArrearsInfo) GetFineArrear() float64 {
	if x != nil {
		return x.FineArrear
	}
	return 0
}

func (x *BccArrearsInfo) GetTotalArrear() float64 {
	if x != nil {
		return x.TotalArrear
	}
	return 0
}

var File_specs_proto_kgd_bridge_kgd_bridge_proto protoreflect.FileDescriptor

const file_specs_proto_kgd_bridge_kgd_bridge_proto_rawDesc = "" +
	"\n" +
	"'specs/proto/kgd-bridge/kgd-bridge.proto\x12\tkgdBridge\"\x10\n" +
	"\x0eHealthCheckReq\")\n" +
	"\x0fHealthCheckResp\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\"+\n" +
	"\vGetDebtsReq\x12\x1c\n" +
	"\tpersonIIN\x18\x01 \x01(\tR\tpersonIIN\"0\n" +
	"\x10GetPersonDataReq\x12\x1c\n" +
	"\tpersonIIN\x18\x01 \x01(\tR\tpersonIIN\"S\n" +
	"\x11GetPersonDataResp\x12\x1e\n" +
	"\n" +
	"fullNameRu\x18\x01 \x01(\tR\n" +
	"fullNameRu\x12\x1e\n" +
	"\n" +
	"fullNameKz\x18\x02 \x01(\tR\n" +
	"fullNameKz\"\xcb\x02\n" +
	"\fGetDebtsResp\x12 \n" +
	"\vtotalArrear\x18\x01 \x01(\x01R\vtotalArrear\x12&\n" +
	"\x0etotalTaxArrear\x18\x02 \x01(\x01R\x0etotalTaxArrear\x12<\n" +
	"\x19pensionContributionArrear\x18\x03 \x01(\x01R\x19pensionContributionArrear\x12:\n" +
	"\x18socialContributionArrear\x18\x04 \x01(\x01R\x18socialContributionArrear\x12@\n" +
	"\x1bsocialHealthInsuranceArrear\x18\x05 \x01(\x01R\x1bsocialHealthInsuranceArrear\x125\n" +
	"\n" +
	"taxOrgInfo\x18\x06 \x03(\v2\x15.kgdBridge.TaxOrgInfoR\n" +
	"taxOrgInfo\"\xcf\x02\n" +
	"\n" +
	"TaxOrgInfo\x12 \n" +
	"\vtotalArrear\x18\x01 \x01(\x01R\vtotalArrear\x12&\n" +
	"\x0etotalTaxArrear\x18\x02 \x01(\x01R\x0etotalTaxArrear\x12<\n" +
	"\x19pensionContributionArrear\x18\x03 \x01(\x01R\x19pensionContributionArrear\x12:\n" +
	"\x18socialContributionArrear\x18\x04 \x01(\x01R\x18socialContributionArrear\x12@\n" +
	"\x1bsocialHealthInsuranceArrear\x18\x05 \x01(\x01R\x1bsocialHealthInsuranceArrear\x12;\n" +
	"\ftaxPayerInfo\x18\x06 \x01(\v2\x17.kgdBridge.TaxPayerInfoR\ftaxPayerInfo\"\xd9\x02\n" +
	"\fTaxPayerInfo\x12\x16\n" +
	"\x06iinBin\x18\x01 \x01(\tR\x06iinBin\x12\x16\n" +
	"\x06nameRu\x18\x03 \x01(\tR\x06nameRu\x12\x16\n" +
	"\x06nameKk\x18\x04 \x01(\tR\x06nameKk\x12\x16\n" +
	"\x06nameQq\x18\x05 \x01(\tR\x06nameQq\x12A\n" +
	"\x0ebccArrearsInfo\x18\x06 \x03(\v2\x19.kgdBridge.BccArrearsInfoR\x0ebccArrearsInfo\x12\x1c\n" +
	"\ttaxArrear\x18\a \x01(\x01R\ttaxArrear\x12 \n" +
	"\vpoenaArrear\x18\b \x01(\x01R\vpoenaArrear\x12$\n" +
	"\rpercentArrear\x18\t \x01(\x01R\rpercentArrear\x12\x1e\n" +
	"\n" +
	"fineArrear\x18\n" +
	" \x01(\x01R\n" +
	"fineArrear\x12 \n" +
	"\vtotalArrear\x18\v \x01(\x01R\vtotalArrear\"\xb8\x01\n" +
	"\x0eBccArrearsInfo\x12\x1c\n" +
	"\ttaxArrear\x18\x01 \x01(\x01R\ttaxArrear\x12 \n" +
	"\vpoenaArrear\x18\x02 \x01(\x01R\vpoenaArrear\x12$\n" +
	"\rpercentArrear\x18\x03 \x01(\x01R\rpercentArrear\x12\x1e\n" +
	"\n" +
	"fineArrear\x18\x04 \x01(\x01R\n" +
	"fineArrear\x12 \n" +
	"\vtotalArrear\x18\x05 \x01(\x01R\vtotalArrear2\xda\x01\n" +
	"\tKgdbridge\x12D\n" +
	"\vHealthCheck\x12\x19.kgdBridge.HealthCheckReq\x1a\x1a.kgdBridge.HealthCheckResp\x12;\n" +
	"\bGetDebts\x12\x16.kgdBridge.GetDebtsReq\x1a\x17.kgdBridge.GetDebtsResp\x12J\n" +
	"\rGetPersonData\x12\x1b.kgdBridge.GetPersonDataReq\x1a\x1c.kgdBridge.GetPersonDataRespB\x18Z\x16specs/proto/kgd-bridgeb\x06proto3"

var (
	file_specs_proto_kgd_bridge_kgd_bridge_proto_rawDescOnce sync.Once
	file_specs_proto_kgd_bridge_kgd_bridge_proto_rawDescData []byte
)

func file_specs_proto_kgd_bridge_kgd_bridge_proto_rawDescGZIP() []byte {
	file_specs_proto_kgd_bridge_kgd_bridge_proto_rawDescOnce.Do(func() {
		file_specs_proto_kgd_bridge_kgd_bridge_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_specs_proto_kgd_bridge_kgd_bridge_proto_rawDesc), len(file_specs_proto_kgd_bridge_kgd_bridge_proto_rawDesc)))
	})
	return file_specs_proto_kgd_bridge_kgd_bridge_proto_rawDescData
}

var file_specs_proto_kgd_bridge_kgd_bridge_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_specs_proto_kgd_bridge_kgd_bridge_proto_goTypes = []any{
	(*HealthCheckReq)(nil),    // 0: kgdBridge.HealthCheckReq
	(*HealthCheckResp)(nil),   // 1: kgdBridge.HealthCheckResp
	(*GetDebtsReq)(nil),       // 2: kgdBridge.GetDebtsReq
	(*GetPersonDataReq)(nil),  // 3: kgdBridge.GetPersonDataReq
	(*GetPersonDataResp)(nil), // 4: kgdBridge.GetPersonDataResp
	(*GetDebtsResp)(nil),      // 5: kgdBridge.GetDebtsResp
	(*TaxOrgInfo)(nil),        // 6: kgdBridge.TaxOrgInfo
	(*TaxPayerInfo)(nil),      // 7: kgdBridge.TaxPayerInfo
	(*BccArrearsInfo)(nil),    // 8: kgdBridge.BccArrearsInfo
}
var file_specs_proto_kgd_bridge_kgd_bridge_proto_depIdxs = []int32{
	6, // 0: kgdBridge.GetDebtsResp.taxOrgInfo:type_name -> kgdBridge.TaxOrgInfo
	7, // 1: kgdBridge.TaxOrgInfo.taxPayerInfo:type_name -> kgdBridge.TaxPayerInfo
	8, // 2: kgdBridge.TaxPayerInfo.bccArrearsInfo:type_name -> kgdBridge.BccArrearsInfo
	0, // 3: kgdBridge.Kgdbridge.HealthCheck:input_type -> kgdBridge.HealthCheckReq
	2, // 4: kgdBridge.Kgdbridge.GetDebts:input_type -> kgdBridge.GetDebtsReq
	3, // 5: kgdBridge.Kgdbridge.GetPersonData:input_type -> kgdBridge.GetPersonDataReq
	1, // 6: kgdBridge.Kgdbridge.HealthCheck:output_type -> kgdBridge.HealthCheckResp
	5, // 7: kgdBridge.Kgdbridge.GetDebts:output_type -> kgdBridge.GetDebtsResp
	4, // 8: kgdBridge.Kgdbridge.GetPersonData:output_type -> kgdBridge.GetPersonDataResp
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_specs_proto_kgd_bridge_kgd_bridge_proto_init() }
func file_specs_proto_kgd_bridge_kgd_bridge_proto_init() {
	if File_specs_proto_kgd_bridge_kgd_bridge_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_specs_proto_kgd_bridge_kgd_bridge_proto_rawDesc), len(file_specs_proto_kgd_bridge_kgd_bridge_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_specs_proto_kgd_bridge_kgd_bridge_proto_goTypes,
		DependencyIndexes: file_specs_proto_kgd_bridge_kgd_bridge_proto_depIdxs,
		MessageInfos:      file_specs_proto_kgd_bridge_kgd_bridge_proto_msgTypes,
	}.Build()
	File_specs_proto_kgd_bridge_kgd_bridge_proto = out.File
	file_specs_proto_kgd_bridge_kgd_bridge_proto_goTypes = nil
	file_specs_proto_kgd_bridge_kgd_bridge_proto_depIdxs = nil
}
