// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.32.1
// source: specs/proto/kgd-bridge/kgd-bridge.proto

package kgd_bridge

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Kgdbridge_HealthCheck_FullMethodName   = "/kgdBridge.Kgdbridge/HealthCheck"
	Kgdbridge_GetDebts_FullMethodName      = "/kgdBridge.Kgdbridge/GetDebts"
	Kgdbridge_GetPersonData_FullMethodName = "/kgdBridge.Kgdbridge/GetPersonData"
)

// KgdbridgeClient is the client API for Kgdbridge service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type KgdbridgeClient interface {
	HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error)
	GetDebts(ctx context.Context, in *GetDebtsReq, opts ...grpc.CallOption) (*GetDebtsResp, error)
	GetPersonData(ctx context.Context, in *GetPersonDataReq, opts ...grpc.CallOption) (*GetPersonDataResp, error)
}

type kgdbridgeClient struct {
	cc grpc.ClientConnInterface
}

func NewKgdbridgeClient(cc grpc.ClientConnInterface) KgdbridgeClient {
	return &kgdbridgeClient{cc}
}

func (c *kgdbridgeClient) HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResp)
	err := c.cc.Invoke(ctx, Kgdbridge_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *kgdbridgeClient) GetDebts(ctx context.Context, in *GetDebtsReq, opts ...grpc.CallOption) (*GetDebtsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDebtsResp)
	err := c.cc.Invoke(ctx, Kgdbridge_GetDebts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *kgdbridgeClient) GetPersonData(ctx context.Context, in *GetPersonDataReq, opts ...grpc.CallOption) (*GetPersonDataResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPersonDataResp)
	err := c.cc.Invoke(ctx, Kgdbridge_GetPersonData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// KgdbridgeServer is the server API for Kgdbridge service.
// All implementations must embed UnimplementedKgdbridgeServer
// for forward compatibility.
type KgdbridgeServer interface {
	HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error)
	GetDebts(context.Context, *GetDebtsReq) (*GetDebtsResp, error)
	GetPersonData(context.Context, *GetPersonDataReq) (*GetPersonDataResp, error)
	mustEmbedUnimplementedKgdbridgeServer()
}

// UnimplementedKgdbridgeServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedKgdbridgeServer struct{}

func (UnimplementedKgdbridgeServer) HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedKgdbridgeServer) GetDebts(context.Context, *GetDebtsReq) (*GetDebtsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDebts not implemented")
}
func (UnimplementedKgdbridgeServer) GetPersonData(context.Context, *GetPersonDataReq) (*GetPersonDataResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPersonData not implemented")
}
func (UnimplementedKgdbridgeServer) mustEmbedUnimplementedKgdbridgeServer() {}
func (UnimplementedKgdbridgeServer) testEmbeddedByValue()                   {}

// UnsafeKgdbridgeServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to KgdbridgeServer will
// result in compilation errors.
type UnsafeKgdbridgeServer interface {
	mustEmbedUnimplementedKgdbridgeServer()
}

func RegisterKgdbridgeServer(s grpc.ServiceRegistrar, srv KgdbridgeServer) {
	// If the following call pancis, it indicates UnimplementedKgdbridgeServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Kgdbridge_ServiceDesc, srv)
}

func _Kgdbridge_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KgdbridgeServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kgdbridge_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KgdbridgeServer).HealthCheck(ctx, req.(*HealthCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Kgdbridge_GetDebts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDebtsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KgdbridgeServer).GetDebts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kgdbridge_GetDebts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KgdbridgeServer).GetDebts(ctx, req.(*GetDebtsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Kgdbridge_GetPersonData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPersonDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KgdbridgeServer).GetPersonData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kgdbridge_GetPersonData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KgdbridgeServer).GetPersonData(ctx, req.(*GetPersonDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Kgdbridge_ServiceDesc is the grpc.ServiceDesc for Kgdbridge service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Kgdbridge_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "kgdBridge.Kgdbridge",
	HandlerType: (*KgdbridgeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HealthCheck",
			Handler:    _Kgdbridge_HealthCheck_Handler,
		},
		{
			MethodName: "GetDebts",
			Handler:    _Kgdbridge_GetDebts_Handler,
		},
		{
			MethodName: "GetPersonData",
			Handler:    _Kgdbridge_GetPersonData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "specs/proto/kgd-bridge/kgd-bridge.proto",
}
