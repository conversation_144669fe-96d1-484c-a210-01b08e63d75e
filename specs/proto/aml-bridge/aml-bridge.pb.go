// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v6.32.1
// source: specs/proto/aml-bridge/aml-bridge.proto

package aml_bridge

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HealthCheckReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckReq) Reset() {
	*x = HealthCheckReq{}
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckReq) ProtoMessage() {}

func (x *HealthCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckReq.ProtoReflect.Descriptor instead.
func (*HealthCheckReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_aml_bridge_aml_bridge_proto_rawDescGZIP(), []int{0}
}

type HealthCheckResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckResp) Reset() {
	*x = HealthCheckResp{}
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResp) ProtoMessage() {}

func (x *HealthCheckResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResp.ProtoReflect.Descriptor instead.
func (*HealthCheckResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_aml_bridge_aml_bridge_proto_rawDescGZIP(), []int{1}
}

func (x *HealthCheckResp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type CheckOnlineClientCardRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	IssuedBid        string                 `protobuf:"bytes,1,opt,name=issuedBid,proto3" json:"issuedBid,omitempty"`
	Uid              string                 `protobuf:"bytes,3,opt,name=uid,proto3" json:"uid,omitempty"`
	BankClientCode   *string                `protobuf:"bytes,4,opt,name=bankClientCode,proto3,oneof" json:"bankClientCode,omitempty"`
	OperationDate    string                 `protobuf:"bytes,5,opt,name=operationDate,proto3" json:"operationDate,omitempty"`
	ClientID         *string                `protobuf:"bytes,6,opt,name=clientID,proto3,oneof" json:"clientID,omitempty"`
	BsClientID       string                 `protobuf:"bytes,7,opt,name=bsClientID,proto3" json:"bsClientID,omitempty"`
	IsNew            string                 `protobuf:"bytes,8,opt,name=isNew,proto3" json:"isNew,omitempty"`
	UrName           *string                `protobuf:"bytes,9,opt,name=urName,proto3,oneof" json:"urName,omitempty"`
	Type             *string                `protobuf:"bytes,10,opt,name=type,proto3,oneof" json:"type,omitempty"`
	BankClient       *string                `protobuf:"bytes,11,opt,name=bankClient,proto3,oneof" json:"bankClient,omitempty"`
	ResCountryCode   *string                `protobuf:"bytes,12,opt,name=resCountryCode,proto3,oneof" json:"resCountryCode,omitempty"`
	ForeignExtra     *string                `protobuf:"bytes,13,opt,name=foreignExtra,proto3,oneof" json:"foreignExtra,omitempty"`
	AcBirthDate      *string                `protobuf:"bytes,14,opt,name=acBirthDate,proto3,oneof" json:"acBirthDate,omitempty"`
	AcBirthPlace     *string                `protobuf:"bytes,15,opt,name=acBirthPlace,proto3,oneof" json:"acBirthPlace,omitempty"`
	AcRegCountry     *string                `protobuf:"bytes,16,opt,name=acRegCountry,proto3,oneof" json:"acRegCountry,omitempty"`
	AcDocTypeCode    *string                `protobuf:"bytes,17,opt,name=acDocTypeCode,proto3,oneof" json:"acDocTypeCode,omitempty"`
	AcDocSeries      *string                `protobuf:"bytes,18,opt,name=acDocSeries,proto3,oneof" json:"acDocSeries,omitempty"`
	AcDocNumber      *string                `protobuf:"bytes,19,opt,name=acDocNumber,proto3,oneof" json:"acDocNumber,omitempty"`
	AcDocWhom        *string                `protobuf:"bytes,20,opt,name=acDocWhom,proto3,oneof" json:"acDocWhom,omitempty"`
	AcDocIssueDate   *string                `protobuf:"bytes,21,opt,name=acDocIssueDate,proto3,oneof" json:"acDocIssueDate,omitempty"`
	RegCountryCode   *string                `protobuf:"bytes,22,opt,name=regCountryCode,proto3,oneof" json:"regCountryCode,omitempty"`
	RegCity          *string                `protobuf:"bytes,23,opt,name=regCity,proto3,oneof" json:"regCity,omitempty"`
	SeatCountryCode  *string                `protobuf:"bytes,24,opt,name=seatCountryCode,proto3,oneof" json:"seatCountryCode,omitempty"`
	SeatCity         *string                `protobuf:"bytes,25,opt,name=seatCity,proto3,oneof" json:"seatCity,omitempty"`
	Branch           *string                `protobuf:"bytes,26,opt,name=branch,proto3,oneof" json:"branch,omitempty"`
	ClientCardClosed *string                `protobuf:"bytes,27,opt,name=clientCardClosed,proto3,oneof" json:"clientCardClosed,omitempty"`
	Oked             *string                `protobuf:"bytes,28,opt,name=oked,proto3,oneof" json:"oked,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CheckOnlineClientCardRequest) Reset() {
	*x = CheckOnlineClientCardRequest{}
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckOnlineClientCardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckOnlineClientCardRequest) ProtoMessage() {}

func (x *CheckOnlineClientCardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckOnlineClientCardRequest.ProtoReflect.Descriptor instead.
func (*CheckOnlineClientCardRequest) Descriptor() ([]byte, []int) {
	return file_specs_proto_aml_bridge_aml_bridge_proto_rawDescGZIP(), []int{2}
}

func (x *CheckOnlineClientCardRequest) GetIssuedBid() string {
	if x != nil {
		return x.IssuedBid
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetBankClientCode() string {
	if x != nil && x.BankClientCode != nil {
		return *x.BankClientCode
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetOperationDate() string {
	if x != nil {
		return x.OperationDate
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetClientID() string {
	if x != nil && x.ClientID != nil {
		return *x.ClientID
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetBsClientID() string {
	if x != nil {
		return x.BsClientID
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetIsNew() string {
	if x != nil {
		return x.IsNew
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetUrName() string {
	if x != nil && x.UrName != nil {
		return *x.UrName
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetType() string {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetBankClient() string {
	if x != nil && x.BankClient != nil {
		return *x.BankClient
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetResCountryCode() string {
	if x != nil && x.ResCountryCode != nil {
		return *x.ResCountryCode
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetForeignExtra() string {
	if x != nil && x.ForeignExtra != nil {
		return *x.ForeignExtra
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetAcBirthDate() string {
	if x != nil && x.AcBirthDate != nil {
		return *x.AcBirthDate
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetAcBirthPlace() string {
	if x != nil && x.AcBirthPlace != nil {
		return *x.AcBirthPlace
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetAcRegCountry() string {
	if x != nil && x.AcRegCountry != nil {
		return *x.AcRegCountry
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetAcDocTypeCode() string {
	if x != nil && x.AcDocTypeCode != nil {
		return *x.AcDocTypeCode
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetAcDocSeries() string {
	if x != nil && x.AcDocSeries != nil {
		return *x.AcDocSeries
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetAcDocNumber() string {
	if x != nil && x.AcDocNumber != nil {
		return *x.AcDocNumber
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetAcDocWhom() string {
	if x != nil && x.AcDocWhom != nil {
		return *x.AcDocWhom
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetAcDocIssueDate() string {
	if x != nil && x.AcDocIssueDate != nil {
		return *x.AcDocIssueDate
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetRegCountryCode() string {
	if x != nil && x.RegCountryCode != nil {
		return *x.RegCountryCode
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetRegCity() string {
	if x != nil && x.RegCity != nil {
		return *x.RegCity
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetSeatCountryCode() string {
	if x != nil && x.SeatCountryCode != nil {
		return *x.SeatCountryCode
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetSeatCity() string {
	if x != nil && x.SeatCity != nil {
		return *x.SeatCity
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetBranch() string {
	if x != nil && x.Branch != nil {
		return *x.Branch
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetClientCardClosed() string {
	if x != nil && x.ClientCardClosed != nil {
		return *x.ClientCardClosed
	}
	return ""
}

func (x *CheckOnlineClientCardRequest) GetOked() string {
	if x != nil && x.Oked != nil {
		return *x.Oked
	}
	return ""
}

type NewCheckOnlineClientCardRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Text             string                 `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	TypeList         string                 `protobuf:"bytes,2,opt,name=type_list,json=typeList,proto3" json:"type_list,omitempty"`
	CheckType        string                 `protobuf:"bytes,3,opt,name=check_type,json=checkType,proto3" json:"check_type,omitempty"`
	Instance         string                 `protobuf:"bytes,4,opt,name=instance,proto3" json:"instance,omitempty"`
	IssuedBid        string                 `protobuf:"bytes,5,opt,name=issued_bid,json=issuedBid,proto3" json:"issued_bid,omitempty"`
	User             string                 `protobuf:"bytes,6,opt,name=user,proto3" json:"user,omitempty"`
	Uid              string                 `protobuf:"bytes,7,opt,name=uid,proto3" json:"uid,omitempty"`
	Product          string                 `protobuf:"bytes,8,opt,name=product,proto3" json:"product,omitempty"`
	Roles            string                 `protobuf:"bytes,9,opt,name=roles,proto3" json:"roles,omitempty"`
	BankClientCode   *string                `protobuf:"bytes,10,opt,name=bank_client_code,json=bankClientCode,proto3,oneof" json:"bank_client_code,omitempty"`
	OperationDate    string                 `protobuf:"bytes,11,opt,name=operation_date,json=operationDate,proto3" json:"operation_date,omitempty"`
	BsClientId       *string                `protobuf:"bytes,12,opt,name=bs_client_id,json=bsClientId,proto3,oneof" json:"bs_client_id,omitempty"`
	IsNew            *string                `protobuf:"bytes,13,opt,name=is_new,json=isNew,proto3,oneof" json:"is_new,omitempty"`
	UrName           string                 `protobuf:"bytes,14,opt,name=ur_name,json=urName,proto3" json:"ur_name,omitempty"`
	UrSname          string                 `protobuf:"bytes,15,opt,name=ur_sname,json=urSname,proto3" json:"ur_sname,omitempty"`
	Lastname         string                 `protobuf:"bytes,16,opt,name=lastname,proto3" json:"lastname,omitempty"`
	Middlename       string                 `protobuf:"bytes,17,opt,name=middlename,proto3" json:"middlename,omitempty"`
	Type             string                 `protobuf:"bytes,18,opt,name=type,proto3" json:"type,omitempty"`
	ClientId         string                 `protobuf:"bytes,19,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	BankClient       *string                `protobuf:"bytes,20,opt,name=bank_client,json=bankClient,proto3,oneof" json:"bank_client,omitempty"`
	ResCountryCode   string                 `protobuf:"bytes,21,opt,name=res_country_code,json=resCountryCode,proto3" json:"res_country_code,omitempty"`
	ForeignExtra     *string                `protobuf:"bytes,22,opt,name=foreign_extra,json=foreignExtra,proto3,oneof" json:"foreign_extra,omitempty"`
	UrFirstHeadName  *string                `protobuf:"bytes,23,opt,name=ur_first_head_name,json=urFirstHeadName,proto3,oneof" json:"ur_first_head_name,omitempty"`
	Oked             *string                `protobuf:"bytes,24,opt,name=oked,proto3,oneof" json:"oked,omitempty"`
	Klvd             *string                `protobuf:"bytes,25,opt,name=klvd,proto3,oneof" json:"klvd,omitempty"`
	AcBirthdate      string                 `protobuf:"bytes,26,opt,name=ac_birthdate,json=acBirthdate,proto3" json:"ac_birthdate,omitempty"`
	AcBirthplace     string                 `protobuf:"bytes,27,opt,name=ac_birthplace,json=acBirthplace,proto3" json:"ac_birthplace,omitempty"`
	AcRegCountry     string                 `protobuf:"bytes,28,opt,name=ac_reg_country,json=acRegCountry,proto3" json:"ac_reg_country,omitempty"`
	AcDocTypeCode    *string                `protobuf:"bytes,29,opt,name=ac_doc_type_code,json=acDocTypeCode,proto3,oneof" json:"ac_doc_type_code,omitempty"`
	AcDocSeries      *string                `protobuf:"bytes,30,opt,name=ac_doc_series,json=acDocSeries,proto3,oneof" json:"ac_doc_series,omitempty"`
	AcDocNumber      *string                `protobuf:"bytes,31,opt,name=ac_doc_number,json=acDocNumber,proto3,oneof" json:"ac_doc_number,omitempty"`
	AcDocWhom        *string                `protobuf:"bytes,32,opt,name=ac_doc_whom,json=acDocWhom,proto3,oneof" json:"ac_doc_whom,omitempty"`
	AcDocIssueDate   *string                `protobuf:"bytes,33,opt,name=ac_doc_issue_date,json=acDocIssueDate,proto3,oneof" json:"ac_doc_issue_date,omitempty"`
	AcDocExpireDate  *string                `protobuf:"bytes,34,opt,name=ac_doc_expire_date,json=acDocExpireDate,proto3,oneof" json:"ac_doc_expire_date,omitempty"`
	RegCountryCode   string                 `protobuf:"bytes,35,opt,name=reg_country_code,json=regCountryCode,proto3" json:"reg_country_code,omitempty"`
	RegCity          string                 `protobuf:"bytes,36,opt,name=reg_city,json=regCity,proto3" json:"reg_city,omitempty"`
	SeatCountryCode  string                 `protobuf:"bytes,37,opt,name=seat_country_code,json=seatCountryCode,proto3" json:"seat_country_code,omitempty"`
	SeatCity         string                 `protobuf:"bytes,38,opt,name=seat_city,json=seatCity,proto3" json:"seat_city,omitempty"`
	Branch           *string                `protobuf:"bytes,39,opt,name=branch,proto3,oneof" json:"branch,omitempty"`
	ClientCardClosed string                 `protobuf:"bytes,40,opt,name=client_card_closed,json=clientCardClosed,proto3" json:"client_card_closed,omitempty"`
	FirstName        string                 `protobuf:"bytes,41,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	Founders         []*Founder             `protobuf:"bytes,42,rep,name=founders,proto3" json:"founders,omitempty"`
	BeneficialOwners []*BeneficialOwner     `protobuf:"bytes,43,rep,name=beneficial_owners,json=beneficialOwners,proto3" json:"beneficial_owners,omitempty"`
	Supervisors      []*Supervisor          `protobuf:"bytes,44,rep,name=supervisors,proto3" json:"supervisors,omitempty"`
	RelatedList      []*Related             `protobuf:"bytes,45,rep,name=related_list,json=relatedList,proto3" json:"related_list,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *NewCheckOnlineClientCardRequest) Reset() {
	*x = NewCheckOnlineClientCardRequest{}
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewCheckOnlineClientCardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewCheckOnlineClientCardRequest) ProtoMessage() {}

func (x *NewCheckOnlineClientCardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewCheckOnlineClientCardRequest.ProtoReflect.Descriptor instead.
func (*NewCheckOnlineClientCardRequest) Descriptor() ([]byte, []int) {
	return file_specs_proto_aml_bridge_aml_bridge_proto_rawDescGZIP(), []int{3}
}

func (x *NewCheckOnlineClientCardRequest) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetTypeList() string {
	if x != nil {
		return x.TypeList
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetCheckType() string {
	if x != nil {
		return x.CheckType
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetInstance() string {
	if x != nil {
		return x.Instance
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetIssuedBid() string {
	if x != nil {
		return x.IssuedBid
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetProduct() string {
	if x != nil {
		return x.Product
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetRoles() string {
	if x != nil {
		return x.Roles
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetBankClientCode() string {
	if x != nil && x.BankClientCode != nil {
		return *x.BankClientCode
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetOperationDate() string {
	if x != nil {
		return x.OperationDate
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetBsClientId() string {
	if x != nil && x.BsClientId != nil {
		return *x.BsClientId
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetIsNew() string {
	if x != nil && x.IsNew != nil {
		return *x.IsNew
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetUrName() string {
	if x != nil {
		return x.UrName
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetUrSname() string {
	if x != nil {
		return x.UrSname
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetLastname() string {
	if x != nil {
		return x.Lastname
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetMiddlename() string {
	if x != nil {
		return x.Middlename
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetBankClient() string {
	if x != nil && x.BankClient != nil {
		return *x.BankClient
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetResCountryCode() string {
	if x != nil {
		return x.ResCountryCode
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetForeignExtra() string {
	if x != nil && x.ForeignExtra != nil {
		return *x.ForeignExtra
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetUrFirstHeadName() string {
	if x != nil && x.UrFirstHeadName != nil {
		return *x.UrFirstHeadName
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetOked() string {
	if x != nil && x.Oked != nil {
		return *x.Oked
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetKlvd() string {
	if x != nil && x.Klvd != nil {
		return *x.Klvd
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetAcBirthdate() string {
	if x != nil {
		return x.AcBirthdate
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetAcBirthplace() string {
	if x != nil {
		return x.AcBirthplace
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetAcRegCountry() string {
	if x != nil {
		return x.AcRegCountry
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetAcDocTypeCode() string {
	if x != nil && x.AcDocTypeCode != nil {
		return *x.AcDocTypeCode
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetAcDocSeries() string {
	if x != nil && x.AcDocSeries != nil {
		return *x.AcDocSeries
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetAcDocNumber() string {
	if x != nil && x.AcDocNumber != nil {
		return *x.AcDocNumber
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetAcDocWhom() string {
	if x != nil && x.AcDocWhom != nil {
		return *x.AcDocWhom
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetAcDocIssueDate() string {
	if x != nil && x.AcDocIssueDate != nil {
		return *x.AcDocIssueDate
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetAcDocExpireDate() string {
	if x != nil && x.AcDocExpireDate != nil {
		return *x.AcDocExpireDate
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetRegCountryCode() string {
	if x != nil {
		return x.RegCountryCode
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetRegCity() string {
	if x != nil {
		return x.RegCity
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetSeatCountryCode() string {
	if x != nil {
		return x.SeatCountryCode
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetSeatCity() string {
	if x != nil {
		return x.SeatCity
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetBranch() string {
	if x != nil && x.Branch != nil {
		return *x.Branch
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetClientCardClosed() string {
	if x != nil {
		return x.ClientCardClosed
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *NewCheckOnlineClientCardRequest) GetFounders() []*Founder {
	if x != nil {
		return x.Founders
	}
	return nil
}

func (x *NewCheckOnlineClientCardRequest) GetBeneficialOwners() []*BeneficialOwner {
	if x != nil {
		return x.BeneficialOwners
	}
	return nil
}

func (x *NewCheckOnlineClientCardRequest) GetSupervisors() []*Supervisor {
	if x != nil {
		return x.Supervisors
	}
	return nil
}

func (x *NewCheckOnlineClientCardRequest) GetRelatedList() []*Related {
	if x != nil {
		return x.RelatedList
	}
	return nil
}

type Founder struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BsClientId    string                 `protobuf:"bytes,1,opt,name=bs_client_id,json=bsClientId,proto3" json:"bs_client_id,omitempty"`
	Role          string                 `protobuf:"bytes,2,opt,name=role,proto3" json:"role,omitempty"`
	ClientId      string                 `protobuf:"bytes,3,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	SubCountry    string                 `protobuf:"bytes,5,opt,name=sub_country,json=subCountry,proto3" json:"sub_country,omitempty"`
	ResCountry    string                 `protobuf:"bytes,6,opt,name=res_country,json=resCountry,proto3" json:"res_country,omitempty"`
	Type          string                 `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty"`
	Urname        string                 `protobuf:"bytes,8,opt,name=urname,proto3" json:"urname,omitempty"`
	Ursname       string                 `protobuf:"bytes,9,opt,name=ursname,proto3" json:"ursname,omitempty"`
	Lastname      string                 `protobuf:"bytes,10,opt,name=lastname,proto3" json:"lastname,omitempty"`
	Firstname     string                 `protobuf:"bytes,11,opt,name=firstname,proto3" json:"firstname,omitempty"`
	Middlename    string                 `protobuf:"bytes,12,opt,name=middlename,proto3" json:"middlename,omitempty"`
	ForeignExtra  string                 `protobuf:"bytes,13,opt,name=foreign_extra,json=foreignExtra,proto3" json:"foreign_extra,omitempty"`
	AcRegCountry  string                 `protobuf:"bytes,14,opt,name=ac_reg_country,json=acRegCountry,proto3" json:"ac_reg_country,omitempty"`
	AcBirthDate   string                 `protobuf:"bytes,15,opt,name=ac_birth_date,json=acBirthDate,proto3" json:"ac_birth_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Founder) Reset() {
	*x = Founder{}
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Founder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Founder) ProtoMessage() {}

func (x *Founder) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Founder.ProtoReflect.Descriptor instead.
func (*Founder) Descriptor() ([]byte, []int) {
	return file_specs_proto_aml_bridge_aml_bridge_proto_rawDescGZIP(), []int{4}
}

func (x *Founder) GetBsClientId() string {
	if x != nil {
		return x.BsClientId
	}
	return ""
}

func (x *Founder) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *Founder) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *Founder) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Founder) GetSubCountry() string {
	if x != nil {
		return x.SubCountry
	}
	return ""
}

func (x *Founder) GetResCountry() string {
	if x != nil {
		return x.ResCountry
	}
	return ""
}

func (x *Founder) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Founder) GetUrname() string {
	if x != nil {
		return x.Urname
	}
	return ""
}

func (x *Founder) GetUrsname() string {
	if x != nil {
		return x.Ursname
	}
	return ""
}

func (x *Founder) GetLastname() string {
	if x != nil {
		return x.Lastname
	}
	return ""
}

func (x *Founder) GetFirstname() string {
	if x != nil {
		return x.Firstname
	}
	return ""
}

func (x *Founder) GetMiddlename() string {
	if x != nil {
		return x.Middlename
	}
	return ""
}

func (x *Founder) GetForeignExtra() string {
	if x != nil {
		return x.ForeignExtra
	}
	return ""
}

func (x *Founder) GetAcRegCountry() string {
	if x != nil {
		return x.AcRegCountry
	}
	return ""
}

func (x *Founder) GetAcBirthDate() string {
	if x != nil {
		return x.AcBirthDate
	}
	return ""
}

type BeneficialOwner struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BsClientId    string                 `protobuf:"bytes,1,opt,name=bs_client_id,json=bsClientId,proto3" json:"bs_client_id,omitempty"`
	Role          string                 `protobuf:"bytes,2,opt,name=role,proto3" json:"role,omitempty"`
	ClientId      string                 `protobuf:"bytes,3,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	SubCountry    string                 `protobuf:"bytes,5,opt,name=sub_country,json=subCountry,proto3" json:"sub_country,omitempty"`
	ResCountry    string                 `protobuf:"bytes,6,opt,name=res_country,json=resCountry,proto3" json:"res_country,omitempty"`
	Type          string                 `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty"`
	Urname        string                 `protobuf:"bytes,8,opt,name=urname,proto3" json:"urname,omitempty"`
	Lastname      string                 `protobuf:"bytes,9,opt,name=lastname,proto3" json:"lastname,omitempty"`
	Firstname     string                 `protobuf:"bytes,10,opt,name=firstname,proto3" json:"firstname,omitempty"`
	Middlename    string                 `protobuf:"bytes,11,opt,name=middlename,proto3" json:"middlename,omitempty"`
	ForeignExtra  string                 `protobuf:"bytes,12,opt,name=foreign_extra,json=foreignExtra,proto3" json:"foreign_extra,omitempty"`
	AcRegCountry  string                 `protobuf:"bytes,13,opt,name=ac_reg_country,json=acRegCountry,proto3" json:"ac_reg_country,omitempty"`
	AcBirthDate   string                 `protobuf:"bytes,14,opt,name=ac_birth_date,json=acBirthDate,proto3" json:"ac_birth_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BeneficialOwner) Reset() {
	*x = BeneficialOwner{}
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BeneficialOwner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BeneficialOwner) ProtoMessage() {}

func (x *BeneficialOwner) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BeneficialOwner.ProtoReflect.Descriptor instead.
func (*BeneficialOwner) Descriptor() ([]byte, []int) {
	return file_specs_proto_aml_bridge_aml_bridge_proto_rawDescGZIP(), []int{5}
}

func (x *BeneficialOwner) GetBsClientId() string {
	if x != nil {
		return x.BsClientId
	}
	return ""
}

func (x *BeneficialOwner) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *BeneficialOwner) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *BeneficialOwner) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BeneficialOwner) GetSubCountry() string {
	if x != nil {
		return x.SubCountry
	}
	return ""
}

func (x *BeneficialOwner) GetResCountry() string {
	if x != nil {
		return x.ResCountry
	}
	return ""
}

func (x *BeneficialOwner) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *BeneficialOwner) GetUrname() string {
	if x != nil {
		return x.Urname
	}
	return ""
}

func (x *BeneficialOwner) GetLastname() string {
	if x != nil {
		return x.Lastname
	}
	return ""
}

func (x *BeneficialOwner) GetFirstname() string {
	if x != nil {
		return x.Firstname
	}
	return ""
}

func (x *BeneficialOwner) GetMiddlename() string {
	if x != nil {
		return x.Middlename
	}
	return ""
}

func (x *BeneficialOwner) GetForeignExtra() string {
	if x != nil {
		return x.ForeignExtra
	}
	return ""
}

func (x *BeneficialOwner) GetAcRegCountry() string {
	if x != nil {
		return x.AcRegCountry
	}
	return ""
}

func (x *BeneficialOwner) GetAcBirthDate() string {
	if x != nil {
		return x.AcBirthDate
	}
	return ""
}

type Supervisor struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BsClientId    string                 `protobuf:"bytes,1,opt,name=bs_client_id,json=bsClientId,proto3" json:"bs_client_id,omitempty"`
	Role          string                 `protobuf:"bytes,2,opt,name=role,proto3" json:"role,omitempty"`
	ClientId      string                 `protobuf:"bytes,3,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	SubCountry    string                 `protobuf:"bytes,5,opt,name=sub_country,json=subCountry,proto3" json:"sub_country,omitempty"`
	ResCountry    string                 `protobuf:"bytes,6,opt,name=res_country,json=resCountry,proto3" json:"res_country,omitempty"`
	Type          string                 `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty"`
	Urname        string                 `protobuf:"bytes,8,opt,name=urname,proto3" json:"urname,omitempty"`
	Lastname      string                 `protobuf:"bytes,9,opt,name=lastname,proto3" json:"lastname,omitempty"`
	Firstname     string                 `protobuf:"bytes,10,opt,name=firstname,proto3" json:"firstname,omitempty"`
	Middlename    string                 `protobuf:"bytes,11,opt,name=middlename,proto3" json:"middlename,omitempty"`
	ForeignExtra  string                 `protobuf:"bytes,12,opt,name=foreign_extra,json=foreignExtra,proto3" json:"foreign_extra,omitempty"`
	AcRegCountry  string                 `protobuf:"bytes,13,opt,name=ac_reg_country,json=acRegCountry,proto3" json:"ac_reg_country,omitempty"`
	AcBirthDate   string                 `protobuf:"bytes,14,opt,name=ac_birth_date,json=acBirthDate,proto3" json:"ac_birth_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Supervisor) Reset() {
	*x = Supervisor{}
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Supervisor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Supervisor) ProtoMessage() {}

func (x *Supervisor) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Supervisor.ProtoReflect.Descriptor instead.
func (*Supervisor) Descriptor() ([]byte, []int) {
	return file_specs_proto_aml_bridge_aml_bridge_proto_rawDescGZIP(), []int{6}
}

func (x *Supervisor) GetBsClientId() string {
	if x != nil {
		return x.BsClientId
	}
	return ""
}

func (x *Supervisor) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *Supervisor) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *Supervisor) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Supervisor) GetSubCountry() string {
	if x != nil {
		return x.SubCountry
	}
	return ""
}

func (x *Supervisor) GetResCountry() string {
	if x != nil {
		return x.ResCountry
	}
	return ""
}

func (x *Supervisor) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Supervisor) GetUrname() string {
	if x != nil {
		return x.Urname
	}
	return ""
}

func (x *Supervisor) GetLastname() string {
	if x != nil {
		return x.Lastname
	}
	return ""
}

func (x *Supervisor) GetFirstname() string {
	if x != nil {
		return x.Firstname
	}
	return ""
}

func (x *Supervisor) GetMiddlename() string {
	if x != nil {
		return x.Middlename
	}
	return ""
}

func (x *Supervisor) GetForeignExtra() string {
	if x != nil {
		return x.ForeignExtra
	}
	return ""
}

func (x *Supervisor) GetAcRegCountry() string {
	if x != nil {
		return x.AcRegCountry
	}
	return ""
}

func (x *Supervisor) GetAcBirthDate() string {
	if x != nil {
		return x.AcBirthDate
	}
	return ""
}

type Related struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BsClientId    string                 `protobuf:"bytes,1,opt,name=bs_client_id,json=bsClientId,proto3" json:"bs_client_id,omitempty"`
	ClientId      string                 `protobuf:"bytes,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	SubCountry    string                 `protobuf:"bytes,4,opt,name=sub_country,json=subCountry,proto3" json:"sub_country,omitempty"`
	ResCountry    string                 `protobuf:"bytes,5,opt,name=res_country,json=resCountry,proto3" json:"res_country,omitempty"`
	Role          string                 `protobuf:"bytes,6,opt,name=role,proto3" json:"role,omitempty"`
	Type          string                 `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty"`
	Urname        string                 `protobuf:"bytes,8,opt,name=urname,proto3" json:"urname,omitempty"`
	Ursname       string                 `protobuf:"bytes,9,opt,name=ursname,proto3" json:"ursname,omitempty"`
	Lastname      string                 `protobuf:"bytes,10,opt,name=lastname,proto3" json:"lastname,omitempty"`
	Firstname     string                 `protobuf:"bytes,11,opt,name=firstname,proto3" json:"firstname,omitempty"`
	Middlename    string                 `protobuf:"bytes,12,opt,name=middlename,proto3" json:"middlename,omitempty"`
	ForeignExtra  string                 `protobuf:"bytes,13,opt,name=foreign_extra,json=foreignExtra,proto3" json:"foreign_extra,omitempty"`
	AcRegCountry  string                 `protobuf:"bytes,14,opt,name=ac_reg_country,json=acRegCountry,proto3" json:"ac_reg_country,omitempty"`
	AcBirthDate   string                 `protobuf:"bytes,15,opt,name=ac_birth_date,json=acBirthDate,proto3" json:"ac_birth_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Related) Reset() {
	*x = Related{}
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Related) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Related) ProtoMessage() {}

func (x *Related) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Related.ProtoReflect.Descriptor instead.
func (*Related) Descriptor() ([]byte, []int) {
	return file_specs_proto_aml_bridge_aml_bridge_proto_rawDescGZIP(), []int{7}
}

func (x *Related) GetBsClientId() string {
	if x != nil {
		return x.BsClientId
	}
	return ""
}

func (x *Related) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *Related) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Related) GetSubCountry() string {
	if x != nil {
		return x.SubCountry
	}
	return ""
}

func (x *Related) GetResCountry() string {
	if x != nil {
		return x.ResCountry
	}
	return ""
}

func (x *Related) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *Related) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Related) GetUrname() string {
	if x != nil {
		return x.Urname
	}
	return ""
}

func (x *Related) GetUrsname() string {
	if x != nil {
		return x.Ursname
	}
	return ""
}

func (x *Related) GetLastname() string {
	if x != nil {
		return x.Lastname
	}
	return ""
}

func (x *Related) GetFirstname() string {
	if x != nil {
		return x.Firstname
	}
	return ""
}

func (x *Related) GetMiddlename() string {
	if x != nil {
		return x.Middlename
	}
	return ""
}

func (x *Related) GetForeignExtra() string {
	if x != nil {
		return x.ForeignExtra
	}
	return ""
}

func (x *Related) GetAcRegCountry() string {
	if x != nil {
		return x.AcRegCountry
	}
	return ""
}

func (x *Related) GetAcBirthDate() string {
	if x != nil {
		return x.AcBirthDate
	}
	return ""
}

type NewCheckOnlineClientCardResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Text           string                 `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	Comment        *string                `protobuf:"bytes,2,opt,name=comment,proto3,oneof" json:"comment,omitempty"`
	Status         int32                  `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	Uid            string                 `protobuf:"bytes,4,opt,name=uid,proto3" json:"uid,omitempty"`
	User           string                 `protobuf:"bytes,5,opt,name=user,proto3" json:"user,omitempty"`
	BsClientId     string                 `protobuf:"bytes,6,opt,name=bs_client_id,json=bsClientId,proto3" json:"bs_client_id,omitempty"`
	CheckType      string                 `protobuf:"bytes,7,opt,name=check_type,json=checkType,proto3" json:"check_type,omitempty"`
	Instance       string                 `protobuf:"bytes,8,opt,name=instance,proto3" json:"instance,omitempty"`
	AmlPriznak     string                 `protobuf:"bytes,9,opt,name=aml_priznak,json=amlPriznak,proto3" json:"aml_priznak,omitempty"`
	CommentPriznak string                 `protobuf:"bytes,10,opt,name=comment_priznak,json=commentPriznak,proto3" json:"comment_priznak,omitempty"`
	IssuedBid      string                 `protobuf:"bytes,11,opt,name=issued_bid,json=issuedBid,proto3" json:"issued_bid,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *NewCheckOnlineClientCardResponse) Reset() {
	*x = NewCheckOnlineClientCardResponse{}
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewCheckOnlineClientCardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewCheckOnlineClientCardResponse) ProtoMessage() {}

func (x *NewCheckOnlineClientCardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewCheckOnlineClientCardResponse.ProtoReflect.Descriptor instead.
func (*NewCheckOnlineClientCardResponse) Descriptor() ([]byte, []int) {
	return file_specs_proto_aml_bridge_aml_bridge_proto_rawDescGZIP(), []int{8}
}

func (x *NewCheckOnlineClientCardResponse) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *NewCheckOnlineClientCardResponse) GetComment() string {
	if x != nil && x.Comment != nil {
		return *x.Comment
	}
	return ""
}

func (x *NewCheckOnlineClientCardResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *NewCheckOnlineClientCardResponse) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *NewCheckOnlineClientCardResponse) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *NewCheckOnlineClientCardResponse) GetBsClientId() string {
	if x != nil {
		return x.BsClientId
	}
	return ""
}

func (x *NewCheckOnlineClientCardResponse) GetCheckType() string {
	if x != nil {
		return x.CheckType
	}
	return ""
}

func (x *NewCheckOnlineClientCardResponse) GetInstance() string {
	if x != nil {
		return x.Instance
	}
	return ""
}

func (x *NewCheckOnlineClientCardResponse) GetAmlPriznak() string {
	if x != nil {
		return x.AmlPriznak
	}
	return ""
}

func (x *NewCheckOnlineClientCardResponse) GetCommentPriznak() string {
	if x != nil {
		return x.CommentPriznak
	}
	return ""
}

func (x *NewCheckOnlineClientCardResponse) GetIssuedBid() string {
	if x != nil {
		return x.IssuedBid
	}
	return ""
}

type CheckOnlineClientCardResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        int32                  `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Uid           string                 `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,omitempty"`
	BsClientID    string                 `protobuf:"bytes,3,opt,name=bsClientID,proto3" json:"bsClientID,omitempty"`
	Comment       *string                `protobuf:"bytes,4,opt,name=comment,proto3,oneof" json:"comment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckOnlineClientCardResponse) Reset() {
	*x = CheckOnlineClientCardResponse{}
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckOnlineClientCardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckOnlineClientCardResponse) ProtoMessage() {}

func (x *CheckOnlineClientCardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckOnlineClientCardResponse.ProtoReflect.Descriptor instead.
func (*CheckOnlineClientCardResponse) Descriptor() ([]byte, []int) {
	return file_specs_proto_aml_bridge_aml_bridge_proto_rawDescGZIP(), []int{9}
}

func (x *CheckOnlineClientCardResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CheckOnlineClientCardResponse) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *CheckOnlineClientCardResponse) GetBsClientID() string {
	if x != nil {
		return x.BsClientID
	}
	return ""
}

func (x *CheckOnlineClientCardResponse) GetComment() string {
	if x != nil && x.Comment != nil {
		return *x.Comment
	}
	return ""
}

type NewCheckOnlineOperationRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Uid             string                 `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BankOperationId int64                  `protobuf:"varint,2,opt,name=bank_operation_id,json=bankOperationId,proto3" json:"bank_operation_id,omitempty"`
	IssuedBid       int64                  `protobuf:"varint,3,opt,name=issued_bid,json=issuedBid,proto3" json:"issued_bid,omitempty"`
	Instance        int64                  `protobuf:"varint,4,opt,name=instance,proto3" json:"instance,omitempty"`
	Username        string                 `protobuf:"bytes,5,opt,name=username,proto3" json:"username,omitempty"`
	Branch          string                 `protobuf:"bytes,6,opt,name=branch,proto3" json:"branch,omitempty"`
	CheckType       string                 `protobuf:"bytes,7,opt,name=check_type,json=checkType,proto3" json:"check_type,omitempty"`
	TypeList        string                 `protobuf:"bytes,8,opt,name=type_list,json=typeList,proto3" json:"type_list,omitempty"`
	OperationDate   string                 `protobuf:"bytes,9,opt,name=operation_date,json=operationDate,proto3" json:"operation_date,omitempty"`
	OperReason      string                 `protobuf:"bytes,10,opt,name=oper_reason,json=operReason,proto3" json:"oper_reason,omitempty"`
	Amount          float64                `protobuf:"fixed64,11,opt,name=amount,proto3" json:"amount,omitempty"`
	Currency        string                 `protobuf:"bytes,12,opt,name=currency,proto3" json:"currency,omitempty"`
	AmountInKzt     float64                `protobuf:"fixed64,13,opt,name=amount_in_kzt,json=amountInKzt,proto3" json:"amount_in_kzt,omitempty"`
	KnpCode         string                 `protobuf:"bytes,14,opt,name=knp_code,json=knpCode,proto3" json:"knp_code,omitempty"`
	AbisTaskCode    string                 `protobuf:"bytes,15,opt,name=abis_task_code,json=abisTaskCode,proto3" json:"abis_task_code,omitempty"`
	Product         string                 `protobuf:"bytes,16,opt,name=product,proto3" json:"product,omitempty"`
	DocCategory     string                 `protobuf:"bytes,17,opt,name=doc_category,json=docCategory,proto3" json:"doc_category,omitempty"`
	Persons         []*Person              `protobuf:"bytes,18,rep,name=persons,proto3" json:"persons,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *NewCheckOnlineOperationRequest) Reset() {
	*x = NewCheckOnlineOperationRequest{}
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewCheckOnlineOperationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewCheckOnlineOperationRequest) ProtoMessage() {}

func (x *NewCheckOnlineOperationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewCheckOnlineOperationRequest.ProtoReflect.Descriptor instead.
func (*NewCheckOnlineOperationRequest) Descriptor() ([]byte, []int) {
	return file_specs_proto_aml_bridge_aml_bridge_proto_rawDescGZIP(), []int{10}
}

func (x *NewCheckOnlineOperationRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *NewCheckOnlineOperationRequest) GetBankOperationId() int64 {
	if x != nil {
		return x.BankOperationId
	}
	return 0
}

func (x *NewCheckOnlineOperationRequest) GetIssuedBid() int64 {
	if x != nil {
		return x.IssuedBid
	}
	return 0
}

func (x *NewCheckOnlineOperationRequest) GetInstance() int64 {
	if x != nil {
		return x.Instance
	}
	return 0
}

func (x *NewCheckOnlineOperationRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *NewCheckOnlineOperationRequest) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

func (x *NewCheckOnlineOperationRequest) GetCheckType() string {
	if x != nil {
		return x.CheckType
	}
	return ""
}

func (x *NewCheckOnlineOperationRequest) GetTypeList() string {
	if x != nil {
		return x.TypeList
	}
	return ""
}

func (x *NewCheckOnlineOperationRequest) GetOperationDate() string {
	if x != nil {
		return x.OperationDate
	}
	return ""
}

func (x *NewCheckOnlineOperationRequest) GetOperReason() string {
	if x != nil {
		return x.OperReason
	}
	return ""
}

func (x *NewCheckOnlineOperationRequest) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *NewCheckOnlineOperationRequest) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *NewCheckOnlineOperationRequest) GetAmountInKzt() float64 {
	if x != nil {
		return x.AmountInKzt
	}
	return 0
}

func (x *NewCheckOnlineOperationRequest) GetKnpCode() string {
	if x != nil {
		return x.KnpCode
	}
	return ""
}

func (x *NewCheckOnlineOperationRequest) GetAbisTaskCode() string {
	if x != nil {
		return x.AbisTaskCode
	}
	return ""
}

func (x *NewCheckOnlineOperationRequest) GetProduct() string {
	if x != nil {
		return x.Product
	}
	return ""
}

func (x *NewCheckOnlineOperationRequest) GetDocCategory() string {
	if x != nil {
		return x.DocCategory
	}
	return ""
}

func (x *NewCheckOnlineOperationRequest) GetPersons() []*Person {
	if x != nil {
		return x.Persons
	}
	return nil
}

type Person struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrderNumber   int64                  `protobuf:"varint,1,opt,name=order_number,json=orderNumber,proto3" json:"order_number,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	FirstName     string                 `protobuf:"bytes,3,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName      string                 `protobuf:"bytes,4,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	MiddleName    string                 `protobuf:"bytes,5,opt,name=middle_name,json=middleName,proto3" json:"middle_name,omitempty"`
	SubDate       string                 `protobuf:"bytes,6,opt,name=sub_date,json=subDate,proto3" json:"sub_date,omitempty"`
	SubCountry    string                 `protobuf:"bytes,7,opt,name=sub_country,json=subCountry,proto3" json:"sub_country,omitempty"`
	City          string                 `protobuf:"bytes,8,opt,name=city,proto3" json:"city,omitempty"`
	ResCountry    string                 `protobuf:"bytes,9,opt,name=res_country,json=resCountry,proto3" json:"res_country,omitempty"`
	ClientId      string                 `protobuf:"bytes,10,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	DocNumber     string                 `protobuf:"bytes,11,opt,name=doc_number,json=docNumber,proto3" json:"doc_number,omitempty"`
	ClientType    string                 `protobuf:"bytes,12,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	ClientRole    string                 `protobuf:"bytes,13,opt,name=client_role,json=clientRole,proto3" json:"client_role,omitempty"`
	ClientKind    string                 `protobuf:"bytes,14,opt,name=client_kind,json=clientKind,proto3" json:"client_kind,omitempty"`
	BankClient    string                 `protobuf:"bytes,15,opt,name=bank_client,json=bankClient,proto3" json:"bank_client,omitempty"`
	Banks         []*Bank                `protobuf:"bytes,16,rep,name=banks,proto3" json:"banks,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Person) Reset() {
	*x = Person{}
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Person) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Person) ProtoMessage() {}

func (x *Person) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Person.ProtoReflect.Descriptor instead.
func (*Person) Descriptor() ([]byte, []int) {
	return file_specs_proto_aml_bridge_aml_bridge_proto_rawDescGZIP(), []int{11}
}

func (x *Person) GetOrderNumber() int64 {
	if x != nil {
		return x.OrderNumber
	}
	return 0
}

func (x *Person) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Person) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *Person) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *Person) GetMiddleName() string {
	if x != nil {
		return x.MiddleName
	}
	return ""
}

func (x *Person) GetSubDate() string {
	if x != nil {
		return x.SubDate
	}
	return ""
}

func (x *Person) GetSubCountry() string {
	if x != nil {
		return x.SubCountry
	}
	return ""
}

func (x *Person) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *Person) GetResCountry() string {
	if x != nil {
		return x.ResCountry
	}
	return ""
}

func (x *Person) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *Person) GetDocNumber() string {
	if x != nil {
		return x.DocNumber
	}
	return ""
}

func (x *Person) GetClientType() string {
	if x != nil {
		return x.ClientType
	}
	return ""
}

func (x *Person) GetClientRole() string {
	if x != nil {
		return x.ClientRole
	}
	return ""
}

func (x *Person) GetClientKind() string {
	if x != nil {
		return x.ClientKind
	}
	return ""
}

func (x *Person) GetBankClient() string {
	if x != nil {
		return x.BankClient
	}
	return ""
}

func (x *Person) GetBanks() []*Bank {
	if x != nil {
		return x.Banks
	}
	return nil
}

type Bank struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Account       string                 `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	BsAccount     string                 `protobuf:"bytes,2,opt,name=bs_account,json=bsAccount,proto3" json:"bs_account,omitempty"`
	Swift         string                 `protobuf:"bytes,3,opt,name=swift,proto3" json:"swift,omitempty"`
	Bic           string                 `protobuf:"bytes,4,opt,name=bic,proto3" json:"bic,omitempty"`
	Name          string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Country       string                 `protobuf:"bytes,6,opt,name=country,proto3" json:"country,omitempty"`
	City          string                 `protobuf:"bytes,7,opt,name=city,proto3" json:"city,omitempty"`
	Sdp           string                 `protobuf:"bytes,8,opt,name=sdp,proto3" json:"sdp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Bank) Reset() {
	*x = Bank{}
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Bank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bank) ProtoMessage() {}

func (x *Bank) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bank.ProtoReflect.Descriptor instead.
func (*Bank) Descriptor() ([]byte, []int) {
	return file_specs_proto_aml_bridge_aml_bridge_proto_rawDescGZIP(), []int{12}
}

func (x *Bank) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *Bank) GetBsAccount() string {
	if x != nil {
		return x.BsAccount
	}
	return ""
}

func (x *Bank) GetSwift() string {
	if x != nil {
		return x.Swift
	}
	return ""
}

func (x *Bank) GetBic() string {
	if x != nil {
		return x.Bic
	}
	return ""
}

func (x *Bank) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Bank) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *Bank) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *Bank) GetSdp() string {
	if x != nil {
		return x.Sdp
	}
	return ""
}

type NewCheckOnlineOperationResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Uid             string                 `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	User            string                 `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	BankOperationId int64                  `protobuf:"varint,3,opt,name=bank_operation_id,json=bankOperationId,proto3" json:"bank_operation_id,omitempty"`
	Comment         string                 `protobuf:"bytes,4,opt,name=comment,proto3" json:"comment,omitempty"`
	Status          int64                  `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	AmlPriznak      string                 `protobuf:"bytes,6,opt,name=aml_priznak,json=amlPriznak,proto3" json:"aml_priznak,omitempty"`
	CommentPriznak  string                 `protobuf:"bytes,7,opt,name=comment_priznak,json=commentPriznak,proto3" json:"comment_priznak,omitempty"`
	IssuedBid       int64                  `protobuf:"varint,8,opt,name=issued_bid,json=issuedBid,proto3" json:"issued_bid,omitempty"`
	Instance        int64                  `protobuf:"varint,9,opt,name=instance,proto3" json:"instance,omitempty"`
	CheckType       string                 `protobuf:"bytes,10,opt,name=check_type,json=checkType,proto3" json:"check_type,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *NewCheckOnlineOperationResponse) Reset() {
	*x = NewCheckOnlineOperationResponse{}
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewCheckOnlineOperationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewCheckOnlineOperationResponse) ProtoMessage() {}

func (x *NewCheckOnlineOperationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewCheckOnlineOperationResponse.ProtoReflect.Descriptor instead.
func (*NewCheckOnlineOperationResponse) Descriptor() ([]byte, []int) {
	return file_specs_proto_aml_bridge_aml_bridge_proto_rawDescGZIP(), []int{13}
}

func (x *NewCheckOnlineOperationResponse) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *NewCheckOnlineOperationResponse) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *NewCheckOnlineOperationResponse) GetBankOperationId() int64 {
	if x != nil {
		return x.BankOperationId
	}
	return 0
}

func (x *NewCheckOnlineOperationResponse) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *NewCheckOnlineOperationResponse) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *NewCheckOnlineOperationResponse) GetAmlPriznak() string {
	if x != nil {
		return x.AmlPriznak
	}
	return ""
}

func (x *NewCheckOnlineOperationResponse) GetCommentPriznak() string {
	if x != nil {
		return x.CommentPriznak
	}
	return ""
}

func (x *NewCheckOnlineOperationResponse) GetIssuedBid() int64 {
	if x != nil {
		return x.IssuedBid
	}
	return 0
}

func (x *NewCheckOnlineOperationResponse) GetInstance() int64 {
	if x != nil {
		return x.Instance
	}
	return 0
}

func (x *NewCheckOnlineOperationResponse) GetCheckType() string {
	if x != nil {
		return x.CheckType
	}
	return ""
}

var File_specs_proto_aml_bridge_aml_bridge_proto protoreflect.FileDescriptor

const file_specs_proto_aml_bridge_aml_bridge_proto_rawDesc = "" +
	"\n" +
	"'specs/proto/aml-bridge/aml-bridge.proto\x12\tamlBridge\"\x10\n" +
	"\x0eHealthCheckReq\")\n" +
	"\x0fHealthCheckResp\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\"\xc3\n" +
	"\n" +
	"\x1cCheckOnlineClientCardRequest\x12\x1c\n" +
	"\tissuedBid\x18\x01 \x01(\tR\tissuedBid\x12\x10\n" +
	"\x03uid\x18\x03 \x01(\tR\x03uid\x12+\n" +
	"\x0ebankClientCode\x18\x04 \x01(\tH\x00R\x0ebankClientCode\x88\x01\x01\x12$\n" +
	"\roperationDate\x18\x05 \x01(\tR\roperationDate\x12\x1f\n" +
	"\bclientID\x18\x06 \x01(\tH\x01R\bclientID\x88\x01\x01\x12\x1e\n" +
	"\n" +
	"bsClientID\x18\a \x01(\tR\n" +
	"bsClientID\x12\x14\n" +
	"\x05isNew\x18\b \x01(\tR\x05isNew\x12\x1b\n" +
	"\x06urName\x18\t \x01(\tH\x02R\x06urName\x88\x01\x01\x12\x17\n" +
	"\x04type\x18\n" +
	" \x01(\tH\x03R\x04type\x88\x01\x01\x12#\n" +
	"\n" +
	"bankClient\x18\v \x01(\tH\x04R\n" +
	"bankClient\x88\x01\x01\x12+\n" +
	"\x0eresCountryCode\x18\f \x01(\tH\x05R\x0eresCountryCode\x88\x01\x01\x12'\n" +
	"\fforeignExtra\x18\r \x01(\tH\x06R\fforeignExtra\x88\x01\x01\x12%\n" +
	"\vacBirthDate\x18\x0e \x01(\tH\aR\vacBirthDate\x88\x01\x01\x12'\n" +
	"\facBirthPlace\x18\x0f \x01(\tH\bR\facBirthPlace\x88\x01\x01\x12'\n" +
	"\facRegCountry\x18\x10 \x01(\tH\tR\facRegCountry\x88\x01\x01\x12)\n" +
	"\racDocTypeCode\x18\x11 \x01(\tH\n" +
	"R\racDocTypeCode\x88\x01\x01\x12%\n" +
	"\vacDocSeries\x18\x12 \x01(\tH\vR\vacDocSeries\x88\x01\x01\x12%\n" +
	"\vacDocNumber\x18\x13 \x01(\tH\fR\vacDocNumber\x88\x01\x01\x12!\n" +
	"\tacDocWhom\x18\x14 \x01(\tH\rR\tacDocWhom\x88\x01\x01\x12+\n" +
	"\x0eacDocIssueDate\x18\x15 \x01(\tH\x0eR\x0eacDocIssueDate\x88\x01\x01\x12+\n" +
	"\x0eregCountryCode\x18\x16 \x01(\tH\x0fR\x0eregCountryCode\x88\x01\x01\x12\x1d\n" +
	"\aregCity\x18\x17 \x01(\tH\x10R\aregCity\x88\x01\x01\x12-\n" +
	"\x0fseatCountryCode\x18\x18 \x01(\tH\x11R\x0fseatCountryCode\x88\x01\x01\x12\x1f\n" +
	"\bseatCity\x18\x19 \x01(\tH\x12R\bseatCity\x88\x01\x01\x12\x1b\n" +
	"\x06branch\x18\x1a \x01(\tH\x13R\x06branch\x88\x01\x01\x12/\n" +
	"\x10clientCardClosed\x18\x1b \x01(\tH\x14R\x10clientCardClosed\x88\x01\x01\x12\x17\n" +
	"\x04oked\x18\x1c \x01(\tH\x15R\x04oked\x88\x01\x01B\x11\n" +
	"\x0f_bankClientCodeB\v\n" +
	"\t_clientIDB\t\n" +
	"\a_urNameB\a\n" +
	"\x05_typeB\r\n" +
	"\v_bankClientB\x11\n" +
	"\x0f_resCountryCodeB\x0f\n" +
	"\r_foreignExtraB\x0e\n" +
	"\f_acBirthDateB\x0f\n" +
	"\r_acBirthPlaceB\x0f\n" +
	"\r_acRegCountryB\x10\n" +
	"\x0e_acDocTypeCodeB\x0e\n" +
	"\f_acDocSeriesB\x0e\n" +
	"\f_acDocNumberB\f\n" +
	"\n" +
	"_acDocWhomB\x11\n" +
	"\x0f_acDocIssueDateB\x11\n" +
	"\x0f_regCountryCodeB\n" +
	"\n" +
	"\b_regCityB\x12\n" +
	"\x10_seatCountryCodeB\v\n" +
	"\t_seatCityB\t\n" +
	"\a_branchB\x13\n" +
	"\x11_clientCardClosedB\a\n" +
	"\x05_oked\"\xed\x0e\n" +
	"\x1fNewCheckOnlineClientCardRequest\x12\x12\n" +
	"\x04text\x18\x01 \x01(\tR\x04text\x12\x1b\n" +
	"\ttype_list\x18\x02 \x01(\tR\btypeList\x12\x1d\n" +
	"\n" +
	"check_type\x18\x03 \x01(\tR\tcheckType\x12\x1a\n" +
	"\binstance\x18\x04 \x01(\tR\binstance\x12\x1d\n" +
	"\n" +
	"issued_bid\x18\x05 \x01(\tR\tissuedBid\x12\x12\n" +
	"\x04user\x18\x06 \x01(\tR\x04user\x12\x10\n" +
	"\x03uid\x18\a \x01(\tR\x03uid\x12\x18\n" +
	"\aproduct\x18\b \x01(\tR\aproduct\x12\x14\n" +
	"\x05roles\x18\t \x01(\tR\x05roles\x12-\n" +
	"\x10bank_client_code\x18\n" +
	" \x01(\tH\x00R\x0ebankClientCode\x88\x01\x01\x12%\n" +
	"\x0eoperation_date\x18\v \x01(\tR\roperationDate\x12%\n" +
	"\fbs_client_id\x18\f \x01(\tH\x01R\n" +
	"bsClientId\x88\x01\x01\x12\x1a\n" +
	"\x06is_new\x18\r \x01(\tH\x02R\x05isNew\x88\x01\x01\x12\x17\n" +
	"\aur_name\x18\x0e \x01(\tR\x06urName\x12\x19\n" +
	"\bur_sname\x18\x0f \x01(\tR\aurSname\x12\x1a\n" +
	"\blastname\x18\x10 \x01(\tR\blastname\x12\x1e\n" +
	"\n" +
	"middlename\x18\x11 \x01(\tR\n" +
	"middlename\x12\x12\n" +
	"\x04type\x18\x12 \x01(\tR\x04type\x12\x1b\n" +
	"\tclient_id\x18\x13 \x01(\tR\bclientId\x12$\n" +
	"\vbank_client\x18\x14 \x01(\tH\x03R\n" +
	"bankClient\x88\x01\x01\x12(\n" +
	"\x10res_country_code\x18\x15 \x01(\tR\x0eresCountryCode\x12(\n" +
	"\rforeign_extra\x18\x16 \x01(\tH\x04R\fforeignExtra\x88\x01\x01\x120\n" +
	"\x12ur_first_head_name\x18\x17 \x01(\tH\x05R\x0furFirstHeadName\x88\x01\x01\x12\x17\n" +
	"\x04oked\x18\x18 \x01(\tH\x06R\x04oked\x88\x01\x01\x12\x17\n" +
	"\x04klvd\x18\x19 \x01(\tH\aR\x04klvd\x88\x01\x01\x12!\n" +
	"\fac_birthdate\x18\x1a \x01(\tR\vacBirthdate\x12#\n" +
	"\rac_birthplace\x18\x1b \x01(\tR\facBirthplace\x12$\n" +
	"\x0eac_reg_country\x18\x1c \x01(\tR\facRegCountry\x12,\n" +
	"\x10ac_doc_type_code\x18\x1d \x01(\tH\bR\racDocTypeCode\x88\x01\x01\x12'\n" +
	"\rac_doc_series\x18\x1e \x01(\tH\tR\vacDocSeries\x88\x01\x01\x12'\n" +
	"\rac_doc_number\x18\x1f \x01(\tH\n" +
	"R\vacDocNumber\x88\x01\x01\x12#\n" +
	"\vac_doc_whom\x18  \x01(\tH\vR\tacDocWhom\x88\x01\x01\x12.\n" +
	"\x11ac_doc_issue_date\x18! \x01(\tH\fR\x0eacDocIssueDate\x88\x01\x01\x120\n" +
	"\x12ac_doc_expire_date\x18\" \x01(\tH\rR\x0facDocExpireDate\x88\x01\x01\x12(\n" +
	"\x10reg_country_code\x18# \x01(\tR\x0eregCountryCode\x12\x19\n" +
	"\breg_city\x18$ \x01(\tR\aregCity\x12*\n" +
	"\x11seat_country_code\x18% \x01(\tR\x0fseatCountryCode\x12\x1b\n" +
	"\tseat_city\x18& \x01(\tR\bseatCity\x12\x1b\n" +
	"\x06branch\x18' \x01(\tH\x0eR\x06branch\x88\x01\x01\x12,\n" +
	"\x12client_card_closed\x18( \x01(\tR\x10clientCardClosed\x12\x1d\n" +
	"\n" +
	"first_name\x18) \x01(\tR\tfirstName\x12.\n" +
	"\bfounders\x18* \x03(\v2\x12.amlBridge.FounderR\bfounders\x12G\n" +
	"\x11beneficial_owners\x18+ \x03(\v2\x1a.amlBridge.BeneficialOwnerR\x10beneficialOwners\x127\n" +
	"\vsupervisors\x18, \x03(\v2\x15.amlBridge.SupervisorR\vsupervisors\x125\n" +
	"\frelated_list\x18- \x03(\v2\x12.amlBridge.RelatedR\vrelatedListB\x13\n" +
	"\x11_bank_client_codeB\x0f\n" +
	"\r_bs_client_idB\t\n" +
	"\a_is_newB\x0e\n" +
	"\f_bank_clientB\x10\n" +
	"\x0e_foreign_extraB\x15\n" +
	"\x13_ur_first_head_nameB\a\n" +
	"\x05_okedB\a\n" +
	"\x05_klvdB\x13\n" +
	"\x11_ac_doc_type_codeB\x10\n" +
	"\x0e_ac_doc_seriesB\x10\n" +
	"\x0e_ac_doc_numberB\x0e\n" +
	"\f_ac_doc_whomB\x14\n" +
	"\x12_ac_doc_issue_dateB\x15\n" +
	"\x13_ac_doc_expire_dateB\t\n" +
	"\a_branch\"\xc1\x03\n" +
	"\aFounder\x12 \n" +
	"\fbs_client_id\x18\x01 \x01(\tR\n" +
	"bsClientId\x12\x12\n" +
	"\x04role\x18\x02 \x01(\tR\x04role\x12\x1b\n" +
	"\tclient_id\x18\x03 \x01(\tR\bclientId\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x1f\n" +
	"\vsub_country\x18\x05 \x01(\tR\n" +
	"subCountry\x12\x1f\n" +
	"\vres_country\x18\x06 \x01(\tR\n" +
	"resCountry\x12\x12\n" +
	"\x04type\x18\a \x01(\tR\x04type\x12\x16\n" +
	"\x06urname\x18\b \x01(\tR\x06urname\x12\x18\n" +
	"\aursname\x18\t \x01(\tR\aursname\x12\x1a\n" +
	"\blastname\x18\n" +
	" \x01(\tR\blastname\x12\x1c\n" +
	"\tfirstname\x18\v \x01(\tR\tfirstname\x12\x1e\n" +
	"\n" +
	"middlename\x18\f \x01(\tR\n" +
	"middlename\x12#\n" +
	"\rforeign_extra\x18\r \x01(\tR\fforeignExtra\x12$\n" +
	"\x0eac_reg_country\x18\x0e \x01(\tR\facRegCountry\x12\"\n" +
	"\rac_birth_date\x18\x0f \x01(\tR\vacBirthDate\"\xaf\x03\n" +
	"\x0fBeneficialOwner\x12 \n" +
	"\fbs_client_id\x18\x01 \x01(\tR\n" +
	"bsClientId\x12\x12\n" +
	"\x04role\x18\x02 \x01(\tR\x04role\x12\x1b\n" +
	"\tclient_id\x18\x03 \x01(\tR\bclientId\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x1f\n" +
	"\vsub_country\x18\x05 \x01(\tR\n" +
	"subCountry\x12\x1f\n" +
	"\vres_country\x18\x06 \x01(\tR\n" +
	"resCountry\x12\x12\n" +
	"\x04type\x18\a \x01(\tR\x04type\x12\x16\n" +
	"\x06urname\x18\b \x01(\tR\x06urname\x12\x1a\n" +
	"\blastname\x18\t \x01(\tR\blastname\x12\x1c\n" +
	"\tfirstname\x18\n" +
	" \x01(\tR\tfirstname\x12\x1e\n" +
	"\n" +
	"middlename\x18\v \x01(\tR\n" +
	"middlename\x12#\n" +
	"\rforeign_extra\x18\f \x01(\tR\fforeignExtra\x12$\n" +
	"\x0eac_reg_country\x18\r \x01(\tR\facRegCountry\x12\"\n" +
	"\rac_birth_date\x18\x0e \x01(\tR\vacBirthDate\"\xaa\x03\n" +
	"\n" +
	"Supervisor\x12 \n" +
	"\fbs_client_id\x18\x01 \x01(\tR\n" +
	"bsClientId\x12\x12\n" +
	"\x04role\x18\x02 \x01(\tR\x04role\x12\x1b\n" +
	"\tclient_id\x18\x03 \x01(\tR\bclientId\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x1f\n" +
	"\vsub_country\x18\x05 \x01(\tR\n" +
	"subCountry\x12\x1f\n" +
	"\vres_country\x18\x06 \x01(\tR\n" +
	"resCountry\x12\x12\n" +
	"\x04type\x18\a \x01(\tR\x04type\x12\x16\n" +
	"\x06urname\x18\b \x01(\tR\x06urname\x12\x1a\n" +
	"\blastname\x18\t \x01(\tR\blastname\x12\x1c\n" +
	"\tfirstname\x18\n" +
	" \x01(\tR\tfirstname\x12\x1e\n" +
	"\n" +
	"middlename\x18\v \x01(\tR\n" +
	"middlename\x12#\n" +
	"\rforeign_extra\x18\f \x01(\tR\fforeignExtra\x12$\n" +
	"\x0eac_reg_country\x18\r \x01(\tR\facRegCountry\x12\"\n" +
	"\rac_birth_date\x18\x0e \x01(\tR\vacBirthDate\"\xc1\x03\n" +
	"\aRelated\x12 \n" +
	"\fbs_client_id\x18\x01 \x01(\tR\n" +
	"bsClientId\x12\x1b\n" +
	"\tclient_id\x18\x02 \x01(\tR\bclientId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x1f\n" +
	"\vsub_country\x18\x04 \x01(\tR\n" +
	"subCountry\x12\x1f\n" +
	"\vres_country\x18\x05 \x01(\tR\n" +
	"resCountry\x12\x12\n" +
	"\x04role\x18\x06 \x01(\tR\x04role\x12\x12\n" +
	"\x04type\x18\a \x01(\tR\x04type\x12\x16\n" +
	"\x06urname\x18\b \x01(\tR\x06urname\x12\x18\n" +
	"\aursname\x18\t \x01(\tR\aursname\x12\x1a\n" +
	"\blastname\x18\n" +
	" \x01(\tR\blastname\x12\x1c\n" +
	"\tfirstname\x18\v \x01(\tR\tfirstname\x12\x1e\n" +
	"\n" +
	"middlename\x18\f \x01(\tR\n" +
	"middlename\x12#\n" +
	"\rforeign_extra\x18\r \x01(\tR\fforeignExtra\x12$\n" +
	"\x0eac_reg_country\x18\x0e \x01(\tR\facRegCountry\x12\"\n" +
	"\rac_birth_date\x18\x0f \x01(\tR\vacBirthDate\"\xe5\x02\n" +
	" NewCheckOnlineClientCardResponse\x12\x12\n" +
	"\x04text\x18\x01 \x01(\tR\x04text\x12\x1d\n" +
	"\acomment\x18\x02 \x01(\tH\x00R\acomment\x88\x01\x01\x12\x16\n" +
	"\x06status\x18\x03 \x01(\x05R\x06status\x12\x10\n" +
	"\x03uid\x18\x04 \x01(\tR\x03uid\x12\x12\n" +
	"\x04user\x18\x05 \x01(\tR\x04user\x12 \n" +
	"\fbs_client_id\x18\x06 \x01(\tR\n" +
	"bsClientId\x12\x1d\n" +
	"\n" +
	"check_type\x18\a \x01(\tR\tcheckType\x12\x1a\n" +
	"\binstance\x18\b \x01(\tR\binstance\x12\x1f\n" +
	"\vaml_priznak\x18\t \x01(\tR\n" +
	"amlPriznak\x12'\n" +
	"\x0fcomment_priznak\x18\n" +
	" \x01(\tR\x0ecommentPriznak\x12\x1d\n" +
	"\n" +
	"issued_bid\x18\v \x01(\tR\tissuedBidB\n" +
	"\n" +
	"\b_comment\"\x94\x01\n" +
	"\x1dCheckOnlineClientCardResponse\x12\x16\n" +
	"\x06status\x18\x01 \x01(\x05R\x06status\x12\x10\n" +
	"\x03uid\x18\x02 \x01(\tR\x03uid\x12\x1e\n" +
	"\n" +
	"bsClientID\x18\x03 \x01(\tR\n" +
	"bsClientID\x12\x1d\n" +
	"\acomment\x18\x04 \x01(\tH\x00R\acomment\x88\x01\x01B\n" +
	"\n" +
	"\b_comment\"\xd4\x04\n" +
	"\x1eNewCheckOnlineOperationRequest\x12\x10\n" +
	"\x03uid\x18\x01 \x01(\tR\x03uid\x12*\n" +
	"\x11bank_operation_id\x18\x02 \x01(\x03R\x0fbankOperationId\x12\x1d\n" +
	"\n" +
	"issued_bid\x18\x03 \x01(\x03R\tissuedBid\x12\x1a\n" +
	"\binstance\x18\x04 \x01(\x03R\binstance\x12\x1a\n" +
	"\busername\x18\x05 \x01(\tR\busername\x12\x16\n" +
	"\x06branch\x18\x06 \x01(\tR\x06branch\x12\x1d\n" +
	"\n" +
	"check_type\x18\a \x01(\tR\tcheckType\x12\x1b\n" +
	"\ttype_list\x18\b \x01(\tR\btypeList\x12%\n" +
	"\x0eoperation_date\x18\t \x01(\tR\roperationDate\x12\x1f\n" +
	"\voper_reason\x18\n" +
	" \x01(\tR\n" +
	"operReason\x12\x16\n" +
	"\x06amount\x18\v \x01(\x01R\x06amount\x12\x1a\n" +
	"\bcurrency\x18\f \x01(\tR\bcurrency\x12\"\n" +
	"\ramount_in_kzt\x18\r \x01(\x01R\vamountInKzt\x12\x19\n" +
	"\bknp_code\x18\x0e \x01(\tR\aknpCode\x12$\n" +
	"\x0eabis_task_code\x18\x0f \x01(\tR\fabisTaskCode\x12\x18\n" +
	"\aproduct\x18\x10 \x01(\tR\aproduct\x12!\n" +
	"\fdoc_category\x18\x11 \x01(\tR\vdocCategory\x12+\n" +
	"\apersons\x18\x12 \x03(\v2\x11.amlBridge.PersonR\apersons\"\xf4\x03\n" +
	"\x06Person\x12!\n" +
	"\forder_number\x18\x01 \x01(\x03R\vorderNumber\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1d\n" +
	"\n" +
	"first_name\x18\x03 \x01(\tR\tfirstName\x12\x1b\n" +
	"\tlast_name\x18\x04 \x01(\tR\blastName\x12\x1f\n" +
	"\vmiddle_name\x18\x05 \x01(\tR\n" +
	"middleName\x12\x19\n" +
	"\bsub_date\x18\x06 \x01(\tR\asubDate\x12\x1f\n" +
	"\vsub_country\x18\a \x01(\tR\n" +
	"subCountry\x12\x12\n" +
	"\x04city\x18\b \x01(\tR\x04city\x12\x1f\n" +
	"\vres_country\x18\t \x01(\tR\n" +
	"resCountry\x12\x1b\n" +
	"\tclient_id\x18\n" +
	" \x01(\tR\bclientId\x12\x1d\n" +
	"\n" +
	"doc_number\x18\v \x01(\tR\tdocNumber\x12\x1f\n" +
	"\vclient_type\x18\f \x01(\tR\n" +
	"clientType\x12\x1f\n" +
	"\vclient_role\x18\r \x01(\tR\n" +
	"clientRole\x12\x1f\n" +
	"\vclient_kind\x18\x0e \x01(\tR\n" +
	"clientKind\x12\x1f\n" +
	"\vbank_client\x18\x0f \x01(\tR\n" +
	"bankClient\x12%\n" +
	"\x05banks\x18\x10 \x03(\v2\x0f.amlBridge.BankR\x05banks\"\xbb\x01\n" +
	"\x04Bank\x12\x18\n" +
	"\aaccount\x18\x01 \x01(\tR\aaccount\x12\x1d\n" +
	"\n" +
	"bs_account\x18\x02 \x01(\tR\tbsAccount\x12\x14\n" +
	"\x05swift\x18\x03 \x01(\tR\x05swift\x12\x10\n" +
	"\x03bic\x18\x04 \x01(\tR\x03bic\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\x12\x18\n" +
	"\acountry\x18\x06 \x01(\tR\acountry\x12\x12\n" +
	"\x04city\x18\a \x01(\tR\x04city\x12\x10\n" +
	"\x03sdp\x18\b \x01(\tR\x03sdp\"\xc9\x02\n" +
	"\x1fNewCheckOnlineOperationResponse\x12\x10\n" +
	"\x03uid\x18\x01 \x01(\tR\x03uid\x12\x12\n" +
	"\x04user\x18\x02 \x01(\tR\x04user\x12*\n" +
	"\x11bank_operation_id\x18\x03 \x01(\x03R\x0fbankOperationId\x12\x18\n" +
	"\acomment\x18\x04 \x01(\tR\acomment\x12\x16\n" +
	"\x06status\x18\x05 \x01(\x03R\x06status\x12\x1f\n" +
	"\vaml_priznak\x18\x06 \x01(\tR\n" +
	"amlPriznak\x12'\n" +
	"\x0fcomment_priznak\x18\a \x01(\tR\x0ecommentPriznak\x12\x1d\n" +
	"\n" +
	"issued_bid\x18\b \x01(\x03R\tissuedBid\x12\x1a\n" +
	"\binstance\x18\t \x01(\x03R\binstance\x12\x1d\n" +
	"\n" +
	"check_type\x18\n" +
	" \x01(\tR\tcheckType2\xa4\x03\n" +
	"\tAmlbridge\x12D\n" +
	"\vHealthCheck\x12\x19.amlBridge.HealthCheckReq\x1a\x1a.amlBridge.HealthCheckResp\x12j\n" +
	"\x15CheckOnlineClientCard\x12'.amlBridge.CheckOnlineClientCardRequest\x1a(.amlBridge.CheckOnlineClientCardResponse\x12s\n" +
	"\x18NewCheckOnlineClientCard\x12*.amlBridge.NewCheckOnlineClientCardRequest\x1a+.amlBridge.NewCheckOnlineClientCardResponse\x12p\n" +
	"\x17NewCheckOnlineOperation\x12).amlBridge.NewCheckOnlineOperationRequest\x1a*.amlBridge.NewCheckOnlineOperationResponseB\x18Z\x16specs/proto/aml-bridgeb\x06proto3"

var (
	file_specs_proto_aml_bridge_aml_bridge_proto_rawDescOnce sync.Once
	file_specs_proto_aml_bridge_aml_bridge_proto_rawDescData []byte
)

func file_specs_proto_aml_bridge_aml_bridge_proto_rawDescGZIP() []byte {
	file_specs_proto_aml_bridge_aml_bridge_proto_rawDescOnce.Do(func() {
		file_specs_proto_aml_bridge_aml_bridge_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_specs_proto_aml_bridge_aml_bridge_proto_rawDesc), len(file_specs_proto_aml_bridge_aml_bridge_proto_rawDesc)))
	})
	return file_specs_proto_aml_bridge_aml_bridge_proto_rawDescData
}

var file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_specs_proto_aml_bridge_aml_bridge_proto_goTypes = []any{
	(*HealthCheckReq)(nil),                   // 0: amlBridge.HealthCheckReq
	(*HealthCheckResp)(nil),                  // 1: amlBridge.HealthCheckResp
	(*CheckOnlineClientCardRequest)(nil),     // 2: amlBridge.CheckOnlineClientCardRequest
	(*NewCheckOnlineClientCardRequest)(nil),  // 3: amlBridge.NewCheckOnlineClientCardRequest
	(*Founder)(nil),                          // 4: amlBridge.Founder
	(*BeneficialOwner)(nil),                  // 5: amlBridge.BeneficialOwner
	(*Supervisor)(nil),                       // 6: amlBridge.Supervisor
	(*Related)(nil),                          // 7: amlBridge.Related
	(*NewCheckOnlineClientCardResponse)(nil), // 8: amlBridge.NewCheckOnlineClientCardResponse
	(*CheckOnlineClientCardResponse)(nil),    // 9: amlBridge.CheckOnlineClientCardResponse
	(*NewCheckOnlineOperationRequest)(nil),   // 10: amlBridge.NewCheckOnlineOperationRequest
	(*Person)(nil),                           // 11: amlBridge.Person
	(*Bank)(nil),                             // 12: amlBridge.Bank
	(*NewCheckOnlineOperationResponse)(nil),  // 13: amlBridge.NewCheckOnlineOperationResponse
}
var file_specs_proto_aml_bridge_aml_bridge_proto_depIdxs = []int32{
	4,  // 0: amlBridge.NewCheckOnlineClientCardRequest.founders:type_name -> amlBridge.Founder
	5,  // 1: amlBridge.NewCheckOnlineClientCardRequest.beneficial_owners:type_name -> amlBridge.BeneficialOwner
	6,  // 2: amlBridge.NewCheckOnlineClientCardRequest.supervisors:type_name -> amlBridge.Supervisor
	7,  // 3: amlBridge.NewCheckOnlineClientCardRequest.related_list:type_name -> amlBridge.Related
	11, // 4: amlBridge.NewCheckOnlineOperationRequest.persons:type_name -> amlBridge.Person
	12, // 5: amlBridge.Person.banks:type_name -> amlBridge.Bank
	0,  // 6: amlBridge.Amlbridge.HealthCheck:input_type -> amlBridge.HealthCheckReq
	2,  // 7: amlBridge.Amlbridge.CheckOnlineClientCard:input_type -> amlBridge.CheckOnlineClientCardRequest
	3,  // 8: amlBridge.Amlbridge.NewCheckOnlineClientCard:input_type -> amlBridge.NewCheckOnlineClientCardRequest
	10, // 9: amlBridge.Amlbridge.NewCheckOnlineOperation:input_type -> amlBridge.NewCheckOnlineOperationRequest
	1,  // 10: amlBridge.Amlbridge.HealthCheck:output_type -> amlBridge.HealthCheckResp
	9,  // 11: amlBridge.Amlbridge.CheckOnlineClientCard:output_type -> amlBridge.CheckOnlineClientCardResponse
	8,  // 12: amlBridge.Amlbridge.NewCheckOnlineClientCard:output_type -> amlBridge.NewCheckOnlineClientCardResponse
	13, // 13: amlBridge.Amlbridge.NewCheckOnlineOperation:output_type -> amlBridge.NewCheckOnlineOperationResponse
	10, // [10:14] is the sub-list for method output_type
	6,  // [6:10] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_specs_proto_aml_bridge_aml_bridge_proto_init() }
func file_specs_proto_aml_bridge_aml_bridge_proto_init() {
	if File_specs_proto_aml_bridge_aml_bridge_proto != nil {
		return
	}
	file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[2].OneofWrappers = []any{}
	file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[3].OneofWrappers = []any{}
	file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[8].OneofWrappers = []any{}
	file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes[9].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_specs_proto_aml_bridge_aml_bridge_proto_rawDesc), len(file_specs_proto_aml_bridge_aml_bridge_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_specs_proto_aml_bridge_aml_bridge_proto_goTypes,
		DependencyIndexes: file_specs_proto_aml_bridge_aml_bridge_proto_depIdxs,
		MessageInfos:      file_specs_proto_aml_bridge_aml_bridge_proto_msgTypes,
	}.Build()
	File_specs_proto_aml_bridge_aml_bridge_proto = out.File
	file_specs_proto_aml_bridge_aml_bridge_proto_goTypes = nil
	file_specs_proto_aml_bridge_aml_bridge_proto_depIdxs = nil
}
