// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.32.1
// source: specs/proto/aml-bridge/aml-bridge.proto

package aml_bridge

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Amlbridge_HealthCheck_FullMethodName              = "/amlBridge.Amlbridge/HealthCheck"
	Amlbridge_CheckOnlineClientCard_FullMethodName    = "/amlBridge.Amlbridge/CheckOnlineClientCard"
	Amlbridge_NewCheckOnlineClientCard_FullMethodName = "/amlBridge.Amlbridge/NewCheckOnlineClientCard"
	Amlbridge_NewCheckOnlineOperation_FullMethodName  = "/amlBridge.Amlbridge/NewCheckOnlineOperation"
)

// AmlbridgeClient is the client API for Amlbridge service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AmlbridgeClient interface {
	HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error)
	CheckOnlineClientCard(ctx context.Context, in *CheckOnlineClientCardRequest, opts ...grpc.CallOption) (*CheckOnlineClientCardResponse, error)
	NewCheckOnlineClientCard(ctx context.Context, in *NewCheckOnlineClientCardRequest, opts ...grpc.CallOption) (*NewCheckOnlineClientCardResponse, error)
	NewCheckOnlineOperation(ctx context.Context, in *NewCheckOnlineOperationRequest, opts ...grpc.CallOption) (*NewCheckOnlineOperationResponse, error)
}

type amlbridgeClient struct {
	cc grpc.ClientConnInterface
}

func NewAmlbridgeClient(cc grpc.ClientConnInterface) AmlbridgeClient {
	return &amlbridgeClient{cc}
}

func (c *amlbridgeClient) HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResp)
	err := c.cc.Invoke(ctx, Amlbridge_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *amlbridgeClient) CheckOnlineClientCard(ctx context.Context, in *CheckOnlineClientCardRequest, opts ...grpc.CallOption) (*CheckOnlineClientCardResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckOnlineClientCardResponse)
	err := c.cc.Invoke(ctx, Amlbridge_CheckOnlineClientCard_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *amlbridgeClient) NewCheckOnlineClientCard(ctx context.Context, in *NewCheckOnlineClientCardRequest, opts ...grpc.CallOption) (*NewCheckOnlineClientCardResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NewCheckOnlineClientCardResponse)
	err := c.cc.Invoke(ctx, Amlbridge_NewCheckOnlineClientCard_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *amlbridgeClient) NewCheckOnlineOperation(ctx context.Context, in *NewCheckOnlineOperationRequest, opts ...grpc.CallOption) (*NewCheckOnlineOperationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NewCheckOnlineOperationResponse)
	err := c.cc.Invoke(ctx, Amlbridge_NewCheckOnlineOperation_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AmlbridgeServer is the server API for Amlbridge service.
// All implementations must embed UnimplementedAmlbridgeServer
// for forward compatibility.
type AmlbridgeServer interface {
	HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error)
	CheckOnlineClientCard(context.Context, *CheckOnlineClientCardRequest) (*CheckOnlineClientCardResponse, error)
	NewCheckOnlineClientCard(context.Context, *NewCheckOnlineClientCardRequest) (*NewCheckOnlineClientCardResponse, error)
	NewCheckOnlineOperation(context.Context, *NewCheckOnlineOperationRequest) (*NewCheckOnlineOperationResponse, error)
	mustEmbedUnimplementedAmlbridgeServer()
}

// UnimplementedAmlbridgeServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAmlbridgeServer struct{}

func (UnimplementedAmlbridgeServer) HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedAmlbridgeServer) CheckOnlineClientCard(context.Context, *CheckOnlineClientCardRequest) (*CheckOnlineClientCardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckOnlineClientCard not implemented")
}
func (UnimplementedAmlbridgeServer) NewCheckOnlineClientCard(context.Context, *NewCheckOnlineClientCardRequest) (*NewCheckOnlineClientCardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewCheckOnlineClientCard not implemented")
}
func (UnimplementedAmlbridgeServer) NewCheckOnlineOperation(context.Context, *NewCheckOnlineOperationRequest) (*NewCheckOnlineOperationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewCheckOnlineOperation not implemented")
}
func (UnimplementedAmlbridgeServer) mustEmbedUnimplementedAmlbridgeServer() {}
func (UnimplementedAmlbridgeServer) testEmbeddedByValue()                   {}

// UnsafeAmlbridgeServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AmlbridgeServer will
// result in compilation errors.
type UnsafeAmlbridgeServer interface {
	mustEmbedUnimplementedAmlbridgeServer()
}

func RegisterAmlbridgeServer(s grpc.ServiceRegistrar, srv AmlbridgeServer) {
	// If the following call pancis, it indicates UnimplementedAmlbridgeServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Amlbridge_ServiceDesc, srv)
}

func _Amlbridge_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AmlbridgeServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Amlbridge_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AmlbridgeServer).HealthCheck(ctx, req.(*HealthCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Amlbridge_CheckOnlineClientCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckOnlineClientCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AmlbridgeServer).CheckOnlineClientCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Amlbridge_CheckOnlineClientCard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AmlbridgeServer).CheckOnlineClientCard(ctx, req.(*CheckOnlineClientCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Amlbridge_NewCheckOnlineClientCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewCheckOnlineClientCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AmlbridgeServer).NewCheckOnlineClientCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Amlbridge_NewCheckOnlineClientCard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AmlbridgeServer).NewCheckOnlineClientCard(ctx, req.(*NewCheckOnlineClientCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Amlbridge_NewCheckOnlineOperation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewCheckOnlineOperationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AmlbridgeServer).NewCheckOnlineOperation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Amlbridge_NewCheckOnlineOperation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AmlbridgeServer).NewCheckOnlineOperation(ctx, req.(*NewCheckOnlineOperationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Amlbridge_ServiceDesc is the grpc.ServiceDesc for Amlbridge service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Amlbridge_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "amlBridge.Amlbridge",
	HandlerType: (*AmlbridgeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HealthCheck",
			Handler:    _Amlbridge_HealthCheck_Handler,
		},
		{
			MethodName: "CheckOnlineClientCard",
			Handler:    _Amlbridge_CheckOnlineClientCard_Handler,
		},
		{
			MethodName: "NewCheckOnlineClientCard",
			Handler:    _Amlbridge_NewCheckOnlineClientCard_Handler,
		},
		{
			MethodName: "NewCheckOnlineOperation",
			Handler:    _Amlbridge_NewCheckOnlineOperation_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "specs/proto/aml-bridge/aml-bridge.proto",
}
