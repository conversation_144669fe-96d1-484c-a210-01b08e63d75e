// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v5.29.3
// source: specs/proto/payments-sme/payments-sme.proto

package payments_sme

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HealthCheckReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckReq) Reset() {
	*x = HealthCheckReq{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckReq) ProtoMessage() {}

func (x *HealthCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckReq.ProtoReflect.Descriptor instead.
func (*HealthCheckReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{0}
}

type HealthCheckResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckResp) Reset() {
	*x = HealthCheckResp{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResp) ProtoMessage() {}

func (x *HealthCheckResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResp.ProtoReflect.Descriptor instead.
func (*HealthCheckResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{1}
}

func (x *HealthCheckResp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type SmePaymentsClientReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SmePaymentsClientReq) Reset() {
	*x = SmePaymentsClientReq{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SmePaymentsClientReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmePaymentsClientReq) ProtoMessage() {}

func (x *SmePaymentsClientReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmePaymentsClientReq.ProtoReflect.Descriptor instead.
func (*SmePaymentsClientReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{2}
}

type SmePaymentsClientResp struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Iin                string                 `protobuf:"bytes,1,opt,name=iin,proto3" json:"iin,omitempty"`
	Name               string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Surname            string                 `protobuf:"bytes,3,opt,name=surname,proto3" json:"surname,omitempty"`
	Patronymic         string                 `protobuf:"bytes,4,opt,name=patronymic,proto3" json:"patronymic,omitempty"`
	Birthdate          string                 `protobuf:"bytes,5,opt,name=birthdate,proto3" json:"birthdate,omitempty"`
	EnterpriseName     string                 `protobuf:"bytes,6,opt,name=enterpriseName,proto3" json:"enterpriseName,omitempty"`
	EnterpriseKATOCode string                 `protobuf:"bytes,7,opt,name=enterpriseKATOCode,proto3" json:"enterpriseKATOCode,omitempty"`
	EnterpriseKATOId   string                 `protobuf:"bytes,8,opt,name=enterpriseKATOId,proto3" json:"enterpriseKATOId,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *SmePaymentsClientResp) Reset() {
	*x = SmePaymentsClientResp{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SmePaymentsClientResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmePaymentsClientResp) ProtoMessage() {}

func (x *SmePaymentsClientResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmePaymentsClientResp.ProtoReflect.Descriptor instead.
func (*SmePaymentsClientResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{3}
}

func (x *SmePaymentsClientResp) GetIin() string {
	if x != nil {
		return x.Iin
	}
	return ""
}

func (x *SmePaymentsClientResp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SmePaymentsClientResp) GetSurname() string {
	if x != nil {
		return x.Surname
	}
	return ""
}

func (x *SmePaymentsClientResp) GetPatronymic() string {
	if x != nil {
		return x.Patronymic
	}
	return ""
}

func (x *SmePaymentsClientResp) GetBirthdate() string {
	if x != nil {
		return x.Birthdate
	}
	return ""
}

func (x *SmePaymentsClientResp) GetEnterpriseName() string {
	if x != nil {
		return x.EnterpriseName
	}
	return ""
}

func (x *SmePaymentsClientResp) GetEnterpriseKATOCode() string {
	if x != nil {
		return x.EnterpriseKATOCode
	}
	return ""
}

func (x *SmePaymentsClientResp) GetEnterpriseKATOId() string {
	if x != nil {
		return x.EnterpriseKATOId
	}
	return ""
}

type ConfirmPaymentSmeReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransactionID string                 `protobuf:"bytes,1,opt,name=transactionID,proto3" json:"transactionID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConfirmPaymentSmeReq) Reset() {
	*x = ConfirmPaymentSmeReq{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConfirmPaymentSmeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmPaymentSmeReq) ProtoMessage() {}

func (x *ConfirmPaymentSmeReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmPaymentSmeReq.ProtoReflect.Descriptor instead.
func (*ConfirmPaymentSmeReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{4}
}

func (x *ConfirmPaymentSmeReq) GetTransactionID() string {
	if x != nil {
		return x.TransactionID
	}
	return ""
}

type ConfirmPaymentSmeResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        string                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ReasonCode    string                 `protobuf:"bytes,2,opt,name=reasonCode,proto3" json:"reasonCode,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	OtpNeeded     bool                   `protobuf:"varint,4,opt,name=otpNeeded,proto3" json:"otpNeeded,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConfirmPaymentSmeResp) Reset() {
	*x = ConfirmPaymentSmeResp{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConfirmPaymentSmeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmPaymentSmeResp) ProtoMessage() {}

func (x *ConfirmPaymentSmeResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmPaymentSmeResp.ProtoReflect.Descriptor instead.
func (*ConfirmPaymentSmeResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{5}
}

func (x *ConfirmPaymentSmeResp) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ConfirmPaymentSmeResp) GetReasonCode() string {
	if x != nil {
		return x.ReasonCode
	}
	return ""
}

func (x *ConfirmPaymentSmeResp) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *ConfirmPaymentSmeResp) GetOtpNeeded() bool {
	if x != nil {
		return x.OtpNeeded
	}
	return false
}

type SmePaymentsCreateOtpReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceInfo    *DeviceInfo            `protobuf:"bytes,1,opt,name=deviceInfo,proto3" json:"deviceInfo,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SmePaymentsCreateOtpReq) Reset() {
	*x = SmePaymentsCreateOtpReq{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SmePaymentsCreateOtpReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmePaymentsCreateOtpReq) ProtoMessage() {}

func (x *SmePaymentsCreateOtpReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmePaymentsCreateOtpReq.ProtoReflect.Descriptor instead.
func (*SmePaymentsCreateOtpReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{6}
}

func (x *SmePaymentsCreateOtpReq) GetDeviceInfo() *DeviceInfo {
	if x != nil {
		return x.DeviceInfo
	}
	return nil
}

type SmePaymentsCreateOtpResp struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	AttemptId       string                 `protobuf:"bytes,1,opt,name=attemptId,proto3" json:"attemptId,omitempty"`
	CodeTtl         string                 `protobuf:"bytes,2,opt,name=codeTtl,proto3" json:"codeTtl,omitempty"`
	CodeChecksLeft  string                 `protobuf:"bytes,3,opt,name=codeChecksLeft,proto3" json:"codeChecksLeft,omitempty"`
	AttemptsLeft    string                 `protobuf:"bytes,4,opt,name=attemptsLeft,proto3" json:"attemptsLeft,omitempty"`
	AttemptsTimeout string                 `protobuf:"bytes,5,opt,name=attemptsTimeout,proto3" json:"attemptsTimeout,omitempty"`
	NewAttemptDelay string                 `protobuf:"bytes,6,opt,name=newAttemptDelay,proto3" json:"newAttemptDelay,omitempty"`
	Error           string                 `protobuf:"bytes,7,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *SmePaymentsCreateOtpResp) Reset() {
	*x = SmePaymentsCreateOtpResp{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SmePaymentsCreateOtpResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmePaymentsCreateOtpResp) ProtoMessage() {}

func (x *SmePaymentsCreateOtpResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmePaymentsCreateOtpResp.ProtoReflect.Descriptor instead.
func (*SmePaymentsCreateOtpResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{7}
}

func (x *SmePaymentsCreateOtpResp) GetAttemptId() string {
	if x != nil {
		return x.AttemptId
	}
	return ""
}

func (x *SmePaymentsCreateOtpResp) GetCodeTtl() string {
	if x != nil {
		return x.CodeTtl
	}
	return ""
}

func (x *SmePaymentsCreateOtpResp) GetCodeChecksLeft() string {
	if x != nil {
		return x.CodeChecksLeft
	}
	return ""
}

func (x *SmePaymentsCreateOtpResp) GetAttemptsLeft() string {
	if x != nil {
		return x.AttemptsLeft
	}
	return ""
}

func (x *SmePaymentsCreateOtpResp) GetAttemptsTimeout() string {
	if x != nil {
		return x.AttemptsTimeout
	}
	return ""
}

func (x *SmePaymentsCreateOtpResp) GetNewAttemptDelay() string {
	if x != nil {
		return x.NewAttemptDelay
	}
	return ""
}

func (x *SmePaymentsCreateOtpResp) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

type DeviceInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	AppVersion     string                 `protobuf:"bytes,1,opt,name=appVersion,proto3" json:"appVersion,omitempty"`
	DeviceModel    string                 `protobuf:"bytes,2,opt,name=deviceModel,proto3" json:"deviceModel,omitempty"`
	InstallationId string                 `protobuf:"bytes,3,opt,name=installationId,proto3" json:"installationId,omitempty"`
	SystemType     string                 `protobuf:"bytes,4,opt,name=systemType,proto3" json:"systemType,omitempty"`
	SystemVersion  string                 `protobuf:"bytes,5,opt,name=systemVersion,proto3" json:"systemVersion,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *DeviceInfo) Reset() {
	*x = DeviceInfo{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceInfo) ProtoMessage() {}

func (x *DeviceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceInfo.ProtoReflect.Descriptor instead.
func (*DeviceInfo) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{8}
}

func (x *DeviceInfo) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *DeviceInfo) GetDeviceModel() string {
	if x != nil {
		return x.DeviceModel
	}
	return ""
}

func (x *DeviceInfo) GetInstallationId() string {
	if x != nil {
		return x.InstallationId
	}
	return ""
}

func (x *DeviceInfo) GetSystemType() string {
	if x != nil {
		return x.SystemType
	}
	return ""
}

func (x *DeviceInfo) GetSystemVersion() string {
	if x != nil {
		return x.SystemVersion
	}
	return ""
}

type SmePaymentsOtpResendReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AttemptId     string                 `protobuf:"bytes,1,opt,name=attemptId,proto3" json:"attemptId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SmePaymentsOtpResendReq) Reset() {
	*x = SmePaymentsOtpResendReq{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SmePaymentsOtpResendReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmePaymentsOtpResendReq) ProtoMessage() {}

func (x *SmePaymentsOtpResendReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmePaymentsOtpResendReq.ProtoReflect.Descriptor instead.
func (*SmePaymentsOtpResendReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{9}
}

func (x *SmePaymentsOtpResendReq) GetAttemptId() string {
	if x != nil {
		return x.AttemptId
	}
	return ""
}

type SmePaymentsOtpResendResp struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	AttemptId       string                 `protobuf:"bytes,1,opt,name=attemptId,proto3" json:"attemptId,omitempty"`
	CodeTtl         string                 `protobuf:"bytes,2,opt,name=codeTtl,proto3" json:"codeTtl,omitempty"`
	CodeChecksLeft  string                 `protobuf:"bytes,3,opt,name=codeChecksLeft,proto3" json:"codeChecksLeft,omitempty"`
	AttemptsLeft    string                 `protobuf:"bytes,4,opt,name=attemptsLeft,proto3" json:"attemptsLeft,omitempty"`
	AttemptsTimeout string                 `protobuf:"bytes,5,opt,name=attemptsTimeout,proto3" json:"attemptsTimeout,omitempty"`
	NewAttemptDelay string                 `protobuf:"bytes,6,opt,name=newAttemptDelay,proto3" json:"newAttemptDelay,omitempty"`
	Error           string                 `protobuf:"bytes,7,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *SmePaymentsOtpResendResp) Reset() {
	*x = SmePaymentsOtpResendResp{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SmePaymentsOtpResendResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmePaymentsOtpResendResp) ProtoMessage() {}

func (x *SmePaymentsOtpResendResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmePaymentsOtpResendResp.ProtoReflect.Descriptor instead.
func (*SmePaymentsOtpResendResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{10}
}

func (x *SmePaymentsOtpResendResp) GetAttemptId() string {
	if x != nil {
		return x.AttemptId
	}
	return ""
}

func (x *SmePaymentsOtpResendResp) GetCodeTtl() string {
	if x != nil {
		return x.CodeTtl
	}
	return ""
}

func (x *SmePaymentsOtpResendResp) GetCodeChecksLeft() string {
	if x != nil {
		return x.CodeChecksLeft
	}
	return ""
}

func (x *SmePaymentsOtpResendResp) GetAttemptsLeft() string {
	if x != nil {
		return x.AttemptsLeft
	}
	return ""
}

func (x *SmePaymentsOtpResendResp) GetAttemptsTimeout() string {
	if x != nil {
		return x.AttemptsTimeout
	}
	return ""
}

func (x *SmePaymentsOtpResendResp) GetNewAttemptDelay() string {
	if x != nil {
		return x.NewAttemptDelay
	}
	return ""
}

func (x *SmePaymentsOtpResendResp) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

type SmePaymentsOtpValidateReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AttemptId     string                 `protobuf:"bytes,1,opt,name=attemptId,proto3" json:"attemptId,omitempty"`
	Code          string                 `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	TransactionID string                 `protobuf:"bytes,3,opt,name=transactionID,proto3" json:"transactionID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SmePaymentsOtpValidateReq) Reset() {
	*x = SmePaymentsOtpValidateReq{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SmePaymentsOtpValidateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmePaymentsOtpValidateReq) ProtoMessage() {}

func (x *SmePaymentsOtpValidateReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmePaymentsOtpValidateReq.ProtoReflect.Descriptor instead.
func (*SmePaymentsOtpValidateReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{11}
}

func (x *SmePaymentsOtpValidateReq) GetAttemptId() string {
	if x != nil {
		return x.AttemptId
	}
	return ""
}

func (x *SmePaymentsOtpValidateReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *SmePaymentsOtpValidateReq) GetTransactionID() string {
	if x != nil {
		return x.TransactionID
	}
	return ""
}

type SmePaymentsOtpValidateResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Error         string                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SmePaymentsOtpValidateResp) Reset() {
	*x = SmePaymentsOtpValidateResp{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SmePaymentsOtpValidateResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmePaymentsOtpValidateResp) ProtoMessage() {}

func (x *SmePaymentsOtpValidateResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmePaymentsOtpValidateResp.ProtoReflect.Descriptor instead.
func (*SmePaymentsOtpValidateResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{12}
}

func (x *SmePaymentsOtpValidateResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *SmePaymentsOtpValidateResp) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

// KbeKodFilter фильтр для получения списка Кбе/КОд кодов
type KbeKodFilter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// codes список кодов Кбе/КОд
	Codes []string `protobuf:"bytes,1,rep,name=codes,proto3" json:"codes,omitempty"`
	// residency признак резидента
	Residency     *bool `protobuf:"varint,2,opt,name=residency,proto3,oneof" json:"residency,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *KbeKodFilter) Reset() {
	*x = KbeKodFilter{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KbeKodFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KbeKodFilter) ProtoMessage() {}

func (x *KbeKodFilter) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KbeKodFilter.ProtoReflect.Descriptor instead.
func (*KbeKodFilter) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{13}
}

func (x *KbeKodFilter) GetCodes() []string {
	if x != nil {
		return x.Codes
	}
	return nil
}

func (x *KbeKodFilter) GetResidency() bool {
	if x != nil && x.Residency != nil {
		return *x.Residency
	}
	return false
}

// GetKbeKodListReq запрос на получение списка Кбе/КОд кодов
type GetKbeKodListReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// filter фильтр для получения списка Кбе/КОд кодов
	Filter        *KbeKodFilter `protobuf:"bytes,1,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetKbeKodListReq) Reset() {
	*x = GetKbeKodListReq{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetKbeKodListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKbeKodListReq) ProtoMessage() {}

func (x *GetKbeKodListReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKbeKodListReq.ProtoReflect.Descriptor instead.
func (*GetKbeKodListReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{14}
}

func (x *GetKbeKodListReq) GetFilter() *KbeKodFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// KbeKod тело Кбе/КОд кода
type KbeKod struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// value код Кбе/КОд
	Value string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	// name наименование Кбе/КОд на языке локализации
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// residency признак резидента
	Residency     bool `protobuf:"varint,3,opt,name=residency,proto3" json:"residency,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *KbeKod) Reset() {
	*x = KbeKod{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KbeKod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KbeKod) ProtoMessage() {}

func (x *KbeKod) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KbeKod.ProtoReflect.Descriptor instead.
func (*KbeKod) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{15}
}

func (x *KbeKod) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *KbeKod) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *KbeKod) GetResidency() bool {
	if x != nil {
		return x.Residency
	}
	return false
}

// GetKbeKodItem элемент списка Кбе/КОд кодов
type GetKbeKodItem struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// code Кбе/КОд код
	Code          *KbeKod `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetKbeKodItem) Reset() {
	*x = GetKbeKodItem{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetKbeKodItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKbeKodItem) ProtoMessage() {}

func (x *GetKbeKodItem) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKbeKodItem.ProtoReflect.Descriptor instead.
func (*GetKbeKodItem) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{16}
}

func (x *GetKbeKodItem) GetCode() *KbeKod {
	if x != nil {
		return x.Code
	}
	return nil
}

// GetKbeKodListResp ответ на запрос на получение списка Кбе/КОд кодов
type GetKbeKodListResp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// codes список Кбе/КОд кодов
	Codes         []*GetKbeKodItem `protobuf:"bytes,1,rep,name=codes,proto3" json:"codes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetKbeKodListResp) Reset() {
	*x = GetKbeKodListResp{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetKbeKodListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKbeKodListResp) ProtoMessage() {}

func (x *GetKbeKodListResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKbeKodListResp.ProtoReflect.Descriptor instead.
func (*GetKbeKodListResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{17}
}

func (x *GetKbeKodListResp) GetCodes() []*GetKbeKodItem {
	if x != nil {
		return x.Codes
	}
	return nil
}

// KnpFilter фильтр для получения списка КНП
type KnpFilter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// codes список кодов КНП
	Codes []string `protobuf:"bytes,1,rep,name=codes,proto3" json:"codes,omitempty"`
	// groups список групп КНП
	Groups        []string `protobuf:"bytes,2,rep,name=groups,proto3" json:"groups,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *KnpFilter) Reset() {
	*x = KnpFilter{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KnpFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnpFilter) ProtoMessage() {}

func (x *KnpFilter) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnpFilter.ProtoReflect.Descriptor instead.
func (*KnpFilter) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{18}
}

func (x *KnpFilter) GetCodes() []string {
	if x != nil {
		return x.Codes
	}
	return nil
}

func (x *KnpFilter) GetGroups() []string {
	if x != nil {
		return x.Groups
	}
	return nil
}

// GetKnpListReq запрос на получение списка КНП
type GetKnpListReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// filter фильтр для получения списка КНП
	Filter        *KnpFilter `protobuf:"bytes,1,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetKnpListReq) Reset() {
	*x = GetKnpListReq{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetKnpListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKnpListReq) ProtoMessage() {}

func (x *GetKnpListReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKnpListReq.ProtoReflect.Descriptor instead.
func (*GetKnpListReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{19}
}

func (x *GetKnpListReq) GetFilter() *KnpFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// Knp тело кода КНП
type Knp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// value код КНП
	Value string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	// name наименование КНП на языке локализации
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Knp) Reset() {
	*x = Knp{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Knp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Knp) ProtoMessage() {}

func (x *Knp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Knp.ProtoReflect.Descriptor instead.
func (*Knp) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{20}
}

func (x *Knp) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *Knp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// GetKnpItem элемент списка КНП
type GetKnpItem struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// code код КНП
	Code          *Knp `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetKnpItem) Reset() {
	*x = GetKnpItem{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetKnpItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKnpItem) ProtoMessage() {}

func (x *GetKnpItem) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKnpItem.ProtoReflect.Descriptor instead.
func (*GetKnpItem) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{21}
}

func (x *GetKnpItem) GetCode() *Knp {
	if x != nil {
		return x.Code
	}
	return nil
}

// GetKnpListResp ответ на запрос на получение списка КНП
type GetKnpListResp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// codes список КНП
	Codes         []*GetKnpItem `protobuf:"bytes,1,rep,name=codes,proto3" json:"codes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetKnpListResp) Reset() {
	*x = GetKnpListResp{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetKnpListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKnpListResp) ProtoMessage() {}

func (x *GetKnpListResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKnpListResp.ProtoReflect.Descriptor instead.
func (*GetKnpListResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{22}
}

func (x *GetKnpListResp) GetCodes() []*GetKnpItem {
	if x != nil {
		return x.Codes
	}
	return nil
}

// BankFilter фильтр для получения списка банков
type BankFilter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// bics список БИКов банков
	Bics          []string `protobuf:"bytes,1,rep,name=bics,proto3" json:"bics,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BankFilter) Reset() {
	*x = BankFilter{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BankFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankFilter) ProtoMessage() {}

func (x *BankFilter) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankFilter.ProtoReflect.Descriptor instead.
func (*BankFilter) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{23}
}

func (x *BankFilter) GetBics() []string {
	if x != nil {
		return x.Bics
	}
	return nil
}

// GetBankListReq запрос на получение списка банков
type GetBankListReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// filter фильтр для получения списка банков
	Filter        *BankFilter `protobuf:"bytes,1,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBankListReq) Reset() {
	*x = GetBankListReq{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBankListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBankListReq) ProtoMessage() {}

func (x *GetBankListReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBankListReq.ProtoReflect.Descriptor instead.
func (*GetBankListReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{24}
}

func (x *GetBankListReq) GetFilter() *BankFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// Bank тело банка
type Bank struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// code код банка
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	// bic БИК банка
	Bic string `protobuf:"bytes,2,opt,name=bic,proto3" json:"bic,omitempty"`
	// name наименование банка на языке локализации
	Name          string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Bank) Reset() {
	*x = Bank{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Bank) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bank) ProtoMessage() {}

func (x *Bank) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bank.ProtoReflect.Descriptor instead.
func (*Bank) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{25}
}

func (x *Bank) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *Bank) GetBic() string {
	if x != nil {
		return x.Bic
	}
	return ""
}

func (x *Bank) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// GetBankItem элемент списка банков
type GetBankItem struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// bank банк
	Bank          *Bank `protobuf:"bytes,1,opt,name=bank,proto3" json:"bank,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBankItem) Reset() {
	*x = GetBankItem{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBankItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBankItem) ProtoMessage() {}

func (x *GetBankItem) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBankItem.ProtoReflect.Descriptor instead.
func (*GetBankItem) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{26}
}

func (x *GetBankItem) GetBank() *Bank {
	if x != nil {
		return x.Bank
	}
	return nil
}

// GetBankListResp ответ на запрос на получение списка банков
type GetBankListResp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// banks список банков
	Banks         []*GetBankItem `protobuf:"bytes,1,rep,name=banks,proto3" json:"banks,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBankListResp) Reset() {
	*x = GetBankListResp{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBankListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBankListResp) ProtoMessage() {}

func (x *GetBankListResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBankListResp.ProtoReflect.Descriptor instead.
func (*GetBankListResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{27}
}

func (x *GetBankListResp) GetBanks() []*GetBankItem {
	if x != nil {
		return x.Banks
	}
	return nil
}

// GetCountryListReq запрос на получение списка стран
type GetCountryListReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// filter фильтр для получения списка стран
	Filter        *CountryFilter `protobuf:"bytes,1,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCountryListReq) Reset() {
	*x = GetCountryListReq{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCountryListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCountryListReq) ProtoMessage() {}

func (x *GetCountryListReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCountryListReq.ProtoReflect.Descriptor instead.
func (*GetCountryListReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{28}
}

func (x *GetCountryListReq) GetFilter() *CountryFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// CountryFilter фильтр для получения списка стран
type CountryFilter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// codes список кодов стран
	Codes         []string `protobuf:"bytes,1,rep,name=codes,proto3" json:"codes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CountryFilter) Reset() {
	*x = CountryFilter{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountryFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountryFilter) ProtoMessage() {}

func (x *CountryFilter) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountryFilter.ProtoReflect.Descriptor instead.
func (*CountryFilter) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{29}
}

func (x *CountryFilter) GetCodes() []string {
	if x != nil {
		return x.Codes
	}
	return nil
}

// Country тело страны
type Country struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// code код страны
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	// name наименование страны на языке локализации
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// description описание страны
	Description   string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Country) Reset() {
	*x = Country{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Country) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Country) ProtoMessage() {}

func (x *Country) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Country.ProtoReflect.Descriptor instead.
func (*Country) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{30}
}

func (x *Country) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *Country) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Country) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// GetCountryListResp ответ на запрос на получение списка стран
type GetCountryListResp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// countries список стран
	Countries     []*Country `protobuf:"bytes,1,rep,name=countries,proto3" json:"countries,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCountryListResp) Reset() {
	*x = GetCountryListResp{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCountryListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCountryListResp) ProtoMessage() {}

func (x *GetCountryListResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCountryListResp.ProtoReflect.Descriptor instead.
func (*GetCountryListResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{31}
}

func (x *GetCountryListResp) GetCountries() []*Country {
	if x != nil {
		return x.Countries
	}
	return nil
}

// GetKbkListReq запрос на получение списка КБК
type GetKbkListReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// filter фильтр для получения списка КБК
	Filter        *KbkFilter `protobuf:"bytes,1,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetKbkListReq) Reset() {
	*x = GetKbkListReq{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetKbkListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKbkListReq) ProtoMessage() {}

func (x *GetKbkListReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKbkListReq.ProtoReflect.Descriptor instead.
func (*GetKbkListReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{32}
}

func (x *GetKbkListReq) GetFilter() *KbkFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// KbkFilter фильтр для получения списка КБК
type KbkFilter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// codes список кодов КБК
	Codes         []string `protobuf:"bytes,1,rep,name=codes,proto3" json:"codes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *KbkFilter) Reset() {
	*x = KbkFilter{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KbkFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KbkFilter) ProtoMessage() {}

func (x *KbkFilter) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KbkFilter.ProtoReflect.Descriptor instead.
func (*KbkFilter) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{33}
}

func (x *KbkFilter) GetCodes() []string {
	if x != nil {
		return x.Codes
	}
	return nil
}

// GetKbkListResp ответ на запрос на получение списка КБК
type GetKbkListResp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// codes список КБК
	Codes         []*GetKbkItem `protobuf:"bytes,1,rep,name=codes,proto3" json:"codes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetKbkListResp) Reset() {
	*x = GetKbkListResp{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetKbkListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKbkListResp) ProtoMessage() {}

func (x *GetKbkListResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKbkListResp.ProtoReflect.Descriptor instead.
func (*GetKbkListResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{34}
}

func (x *GetKbkListResp) GetCodes() []*GetKbkItem {
	if x != nil {
		return x.Codes
	}
	return nil
}

// GetKbkItem элемент списка КБК
type GetKbkItem struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// code код КБК
	Code          *Kbk `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetKbkItem) Reset() {
	*x = GetKbkItem{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetKbkItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKbkItem) ProtoMessage() {}

func (x *GetKbkItem) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKbkItem.ProtoReflect.Descriptor instead.
func (*GetKbkItem) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{35}
}

func (x *GetKbkItem) GetCode() *Kbk {
	if x != nil {
		return x.Code
	}
	return nil
}

// Kbk тело КБК
type Kbk struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// value значение кода КБК
	Value string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	// name описание кода КБК на языке локализации
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Kbk) Reset() {
	*x = Kbk{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Kbk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Kbk) ProtoMessage() {}

func (x *Kbk) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Kbk.ProtoReflect.Descriptor instead.
func (*Kbk) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{36}
}

func (x *Kbk) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *Kbk) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// GetTaxAuthorityListReq запрос на получение списка УГД
type GetTaxAuthorityListReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// filter фильтр для получения списка УГД
	Filter        *TaxAuthorityFilter `protobuf:"bytes,1,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTaxAuthorityListReq) Reset() {
	*x = GetTaxAuthorityListReq{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTaxAuthorityListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaxAuthorityListReq) ProtoMessage() {}

func (x *GetTaxAuthorityListReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaxAuthorityListReq.ProtoReflect.Descriptor instead.
func (*GetTaxAuthorityListReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{37}
}

func (x *GetTaxAuthorityListReq) GetFilter() *TaxAuthorityFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// TaxAuthorityFilter фильтр для получения списка УГД
type TaxAuthorityFilter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// codes список кодов УГД
	Regions       []string `protobuf:"bytes,1,rep,name=regions,proto3" json:"regions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaxAuthorityFilter) Reset() {
	*x = TaxAuthorityFilter{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaxAuthorityFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaxAuthorityFilter) ProtoMessage() {}

func (x *TaxAuthorityFilter) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaxAuthorityFilter.ProtoReflect.Descriptor instead.
func (*TaxAuthorityFilter) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{38}
}

func (x *TaxAuthorityFilter) GetRegions() []string {
	if x != nil {
		return x.Regions
	}
	return nil
}

// GetTaxAuthorityListResp ответ на запрос на получение списка УГД
type GetTaxAuthorityListResp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// codes список УГД
	Ugds          []*GetTaxAuthorityItem `protobuf:"bytes,1,rep,name=ugds,proto3" json:"ugds,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTaxAuthorityListResp) Reset() {
	*x = GetTaxAuthorityListResp{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTaxAuthorityListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaxAuthorityListResp) ProtoMessage() {}

func (x *GetTaxAuthorityListResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaxAuthorityListResp.ProtoReflect.Descriptor instead.
func (*GetTaxAuthorityListResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{39}
}

func (x *GetTaxAuthorityListResp) GetUgds() []*GetTaxAuthorityItem {
	if x != nil {
		return x.Ugds
	}
	return nil
}

// GetTaxAuthorityItem элемент списка УГД
type GetTaxAuthorityItem struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// code код УГД
	Ugd           *TaxAuthority `protobuf:"bytes,1,opt,name=ugd,proto3" json:"ugd,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTaxAuthorityItem) Reset() {
	*x = GetTaxAuthorityItem{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTaxAuthorityItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaxAuthorityItem) ProtoMessage() {}

func (x *GetTaxAuthorityItem) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaxAuthorityItem.ProtoReflect.Descriptor instead.
func (*GetTaxAuthorityItem) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{40}
}

func (x *GetTaxAuthorityItem) GetUgd() *TaxAuthority {
	if x != nil {
		return x.Ugd
	}
	return nil
}

// TaxAuthority тело УГД
type TaxAuthority struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// name наименование УГД на языке локализации
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// bin БИН УГД
	Bin string `protobuf:"bytes,2,opt,name=bin,proto3" json:"bin,omitempty"`
	// regionCode код региона
	RegionCode string `protobuf:"bytes,3,opt,name=regionCode,proto3" json:"regionCode,omitempty"`
	// Название региона
	RegionName string `protobuf:"bytes,4,opt,name=regionName,proto3" json:"regionName,omitempty"`
	// КНО
	Code          string `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaxAuthority) Reset() {
	*x = TaxAuthority{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaxAuthority) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaxAuthority) ProtoMessage() {}

func (x *TaxAuthority) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaxAuthority.ProtoReflect.Descriptor instead.
func (*TaxAuthority) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{41}
}

func (x *TaxAuthority) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TaxAuthority) GetBin() string {
	if x != nil {
		return x.Bin
	}
	return ""
}

func (x *TaxAuthority) GetRegionCode() string {
	if x != nil {
		return x.RegionCode
	}
	return ""
}

func (x *TaxAuthority) GetRegionName() string {
	if x != nil {
		return x.RegionName
	}
	return ""
}

func (x *TaxAuthority) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type SmePaymentsWorktimeReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SmePaymentsWorktimeReq) Reset() {
	*x = SmePaymentsWorktimeReq{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SmePaymentsWorktimeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmePaymentsWorktimeReq) ProtoMessage() {}

func (x *SmePaymentsWorktimeReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmePaymentsWorktimeReq.ProtoReflect.Descriptor instead.
func (*SmePaymentsWorktimeReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{42}
}

type SmePaymentsWorktimeResp struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Date                string                 `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	IsDateOperational   bool                   `protobuf:"varint,2,opt,name=isDateOperational,proto3" json:"isDateOperational,omitempty"`
	NextOperationalDate string                 `protobuf:"bytes,3,opt,name=nextOperationalDate,proto3" json:"nextOperationalDate,omitempty"`
	IsEndOfWorkTime     bool                   `protobuf:"varint,4,opt,name=isEndOfWorkTime,proto3" json:"isEndOfWorkTime,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *SmePaymentsWorktimeResp) Reset() {
	*x = SmePaymentsWorktimeResp{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SmePaymentsWorktimeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmePaymentsWorktimeResp) ProtoMessage() {}

func (x *SmePaymentsWorktimeResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmePaymentsWorktimeResp.ProtoReflect.Descriptor instead.
func (*SmePaymentsWorktimeResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{43}
}

func (x *SmePaymentsWorktimeResp) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *SmePaymentsWorktimeResp) GetIsDateOperational() bool {
	if x != nil {
		return x.IsDateOperational
	}
	return false
}

func (x *SmePaymentsWorktimeResp) GetNextOperationalDate() string {
	if x != nil {
		return x.NextOperationalDate
	}
	return ""
}

func (x *SmePaymentsWorktimeResp) GetIsEndOfWorkTime() bool {
	if x != nil {
		return x.IsEndOfWorkTime
	}
	return false
}

// CreatePaymentReq запрос на создание платежа
type CreatePaymentReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// idempotencyKey уникальный идентификатор запроса для избежания дублирования платежа
	IdempotencyKey string `protobuf:"bytes,1,opt,name=idempotencyKey,proto3" json:"idempotencyKey,omitempty"`
	// paymentCode тип платежа
	PaymentCode string `protobuf:"bytes,2,opt,name=paymentCode,proto3" json:"paymentCode,omitempty"`
	// paymentData данные платежа
	PaymentData   *PaymentData `protobuf:"bytes,3,opt,name=paymentData,proto3" json:"paymentData,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePaymentReq) Reset() {
	*x = CreatePaymentReq{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePaymentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePaymentReq) ProtoMessage() {}

func (x *CreatePaymentReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePaymentReq.ProtoReflect.Descriptor instead.
func (*CreatePaymentReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{44}
}

func (x *CreatePaymentReq) GetIdempotencyKey() string {
	if x != nil {
		return x.IdempotencyKey
	}
	return ""
}

func (x *CreatePaymentReq) GetPaymentCode() string {
	if x != nil {
		return x.PaymentCode
	}
	return ""
}

func (x *CreatePaymentReq) GetPaymentData() *PaymentData {
	if x != nil {
		return x.PaymentData
	}
	return nil
}

// CreatePaymentResp ответ на запрос на создание платежа
type CreatePaymentResp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// transactionID уникальный идентификатор созданной транзакции в БД Платежей и Переводов. Нужен для дальнейшей работы с транзакцией. Опциональное поле - может быть пустым при ошибках валидации.
	TransactionID string `protobuf:"bytes,1,opt,name=transactionID,proto3" json:"transactionID,omitempty"`
	// status статус платежа
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	// reasonCode код состояния платежа
	ReasonCode string `protobuf:"bytes,3,opt,name=reasonCode,proto3" json:"reasonCode,omitempty"`
	// reason описание причины состояния платежа
	Reason string `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
	// otpNeeded необходимость подтвердить платёж OTP
	OtpNeeded     bool `protobuf:"varint,5,opt,name=otpNeeded,proto3" json:"otpNeeded,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePaymentResp) Reset() {
	*x = CreatePaymentResp{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePaymentResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePaymentResp) ProtoMessage() {}

func (x *CreatePaymentResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePaymentResp.ProtoReflect.Descriptor instead.
func (*CreatePaymentResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{45}
}

func (x *CreatePaymentResp) GetTransactionID() string {
	if x != nil {
		return x.TransactionID
	}
	return ""
}

func (x *CreatePaymentResp) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *CreatePaymentResp) GetReasonCode() string {
	if x != nil {
		return x.ReasonCode
	}
	return ""
}

func (x *CreatePaymentResp) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *CreatePaymentResp) GetOtpNeeded() bool {
	if x != nil {
		return x.OtpNeeded
	}
	return false
}

// OPVPaymentData структура данных для платежа ОПВ(Обязательные Платежи Взносы)
type PaymentData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// paymentPeriod период платежа
	PaymentPeriod string `protobuf:"bytes,1,opt,name=paymentPeriod,proto3" json:"paymentPeriod,omitempty"`
	// purposeCode код назначения платежа (код КНП)
	PurposeCode string `protobuf:"bytes,2,opt,name=purposeCode,proto3" json:"purposeCode,omitempty"`
	// purposeDetails детали назначения платежа
	PurposeDetails string `protobuf:"bytes,3,opt,name=purposeDetails,proto3" json:"purposeDetails,omitempty"`
	// amount сумма платежа
	Amount string `protobuf:"bytes,4,opt,name=amount,proto3" json:"amount,omitempty"`
	// payerAccount номер счета плательщика (Например: **********************)
	PayerAccount string `protobuf:"bytes,5,opt,name=payerAccount,proto3" json:"payerAccount,omitempty"`
	// employees список сотрудников
	Employees []*EmployeeItem `protobuf:"bytes,6,rep,name=employees,proto3" json:"employees,omitempty"`
	// signatoryA Подпись ответственного лица (ФИО)
	SignatoryA string `protobuf:"bytes,7,opt,name=signatoryA,proto3" json:"signatoryA,omitempty"`
	// beneficiaryBINIIN БИН/ИИН получателя платежа
	BeneficiaryBINIIN string `protobuf:"bytes,8,opt,name=beneficiaryBINIIN,proto3" json:"beneficiaryBINIIN,omitempty"`
	// beneficiaryName Наименование получателя платежа
	BeneficiaryName string `protobuf:"bytes,9,opt,name=beneficiaryName,proto3" json:"beneficiaryName,omitempty"`
	// realBeneficiaryName ФИО фактического получателя
	RealBeneficiaryName string `protobuf:"bytes,10,opt,name=realBeneficiaryName,proto3" json:"realBeneficiaryName,omitempty"`
	// realBeneficiaryBINIIN ИИН фактического получателя
	RealBeneficiaryBINIIN string `protobuf:"bytes,11,opt,name=realBeneficiaryBINIIN,proto3" json:"realBeneficiaryBINIIN,omitempty"`
	// realBeneficiaryCountry Страна резидентства фактического получателя
	RealBeneficiaryCountry string `protobuf:"bytes,12,opt,name=realBeneficiaryCountry,proto3" json:"realBeneficiaryCountry,omitempty"`
	// kbk Код Бюджетной Классификации
	Kbk           string `protobuf:"bytes,13,opt,name=kbk,proto3" json:"kbk,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PaymentData) Reset() {
	*x = PaymentData{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaymentData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentData) ProtoMessage() {}

func (x *PaymentData) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentData.ProtoReflect.Descriptor instead.
func (*PaymentData) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{46}
}

func (x *PaymentData) GetPaymentPeriod() string {
	if x != nil {
		return x.PaymentPeriod
	}
	return ""
}

func (x *PaymentData) GetPurposeCode() string {
	if x != nil {
		return x.PurposeCode
	}
	return ""
}

func (x *PaymentData) GetPurposeDetails() string {
	if x != nil {
		return x.PurposeDetails
	}
	return ""
}

func (x *PaymentData) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *PaymentData) GetPayerAccount() string {
	if x != nil {
		return x.PayerAccount
	}
	return ""
}

func (x *PaymentData) GetEmployees() []*EmployeeItem {
	if x != nil {
		return x.Employees
	}
	return nil
}

func (x *PaymentData) GetSignatoryA() string {
	if x != nil {
		return x.SignatoryA
	}
	return ""
}

func (x *PaymentData) GetBeneficiaryBINIIN() string {
	if x != nil {
		return x.BeneficiaryBINIIN
	}
	return ""
}

func (x *PaymentData) GetBeneficiaryName() string {
	if x != nil {
		return x.BeneficiaryName
	}
	return ""
}

func (x *PaymentData) GetRealBeneficiaryName() string {
	if x != nil {
		return x.RealBeneficiaryName
	}
	return ""
}

func (x *PaymentData) GetRealBeneficiaryBINIIN() string {
	if x != nil {
		return x.RealBeneficiaryBINIIN
	}
	return ""
}

func (x *PaymentData) GetRealBeneficiaryCountry() string {
	if x != nil {
		return x.RealBeneficiaryCountry
	}
	return ""
}

func (x *PaymentData) GetKbk() string {
	if x != nil {
		return x.Kbk
	}
	return ""
}

// EmployeeItem структура данных для сотрудника
type EmployeeItem struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// name имя сотрудника
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// middleName отчество сотрудника
	MiddleName *string `protobuf:"bytes,2,opt,name=middleName,proto3,oneof" json:"middleName,omitempty"`
	// lastName фамилия сотрудника
	LastName string `protobuf:"bytes,3,opt,name=lastName,proto3" json:"lastName,omitempty"`
	// iin ИИН сотрудника
	Iin string `protobuf:"bytes,4,opt,name=iin,proto3" json:"iin,omitempty"`
	// birthday дата рождения сотрудника
	Birthday string `protobuf:"bytes,5,opt,name=birthday,proto3" json:"birthday,omitempty"`
	// country страна сотрудника
	Country string `protobuf:"bytes,6,opt,name=country,proto3" json:"country,omitempty"`
	// amount сумма платежа для сотрудника
	Amount string `protobuf:"bytes,7,opt,name=amount,proto3" json:"amount,omitempty"`
	// valuePeriod период платежа для сотрудника
	ValuePeriod string `protobuf:"bytes,8,opt,name=valuePeriod,proto3" json:"valuePeriod,omitempty"`
	// id идентификатор записи о сотруднике
	Id string `protobuf:"bytes,9,opt,name=id,proto3" json:"id,omitempty"`
	// displayOrder Порядок отображения в списке
	DisplayOrder int32 `protobuf:"varint,10,opt,name=displayOrder,proto3" json:"displayOrder,omitempty"`
	// employer_iin БИН/ИИН нанимателя
	EmployerIin   string `protobuf:"bytes,11,opt,name=employer_iin,json=employerIin,proto3" json:"employer_iin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmployeeItem) Reset() {
	*x = EmployeeItem{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmployeeItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmployeeItem) ProtoMessage() {}

func (x *EmployeeItem) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmployeeItem.ProtoReflect.Descriptor instead.
func (*EmployeeItem) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{47}
}

func (x *EmployeeItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EmployeeItem) GetMiddleName() string {
	if x != nil && x.MiddleName != nil {
		return *x.MiddleName
	}
	return ""
}

func (x *EmployeeItem) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *EmployeeItem) GetIin() string {
	if x != nil {
		return x.Iin
	}
	return ""
}

func (x *EmployeeItem) GetBirthday() string {
	if x != nil {
		return x.Birthday
	}
	return ""
}

func (x *EmployeeItem) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *EmployeeItem) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *EmployeeItem) GetValuePeriod() string {
	if x != nil {
		return x.ValuePeriod
	}
	return ""
}

func (x *EmployeeItem) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EmployeeItem) GetDisplayOrder() int32 {
	if x != nil {
		return x.DisplayOrder
	}
	return 0
}

func (x *EmployeeItem) GetEmployerIin() string {
	if x != nil {
		return x.EmployerIin
	}
	return ""
}

type SmePaymentsGetPaymentOrderReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransactionID string                 `protobuf:"bytes,1,opt,name=transactionID,proto3" json:"transactionID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SmePaymentsGetPaymentOrderReq) Reset() {
	*x = SmePaymentsGetPaymentOrderReq{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SmePaymentsGetPaymentOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmePaymentsGetPaymentOrderReq) ProtoMessage() {}

func (x *SmePaymentsGetPaymentOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmePaymentsGetPaymentOrderReq.ProtoReflect.Descriptor instead.
func (*SmePaymentsGetPaymentOrderReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{48}
}

func (x *SmePaymentsGetPaymentOrderReq) GetTransactionID() string {
	if x != nil {
		return x.TransactionID
	}
	return ""
}

type SmePaymentsGetPaymentOrderByTrNumberReq struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	TransactionNumber string                 `protobuf:"bytes,1,opt,name=transactionNumber,proto3" json:"transactionNumber,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *SmePaymentsGetPaymentOrderByTrNumberReq) Reset() {
	*x = SmePaymentsGetPaymentOrderByTrNumberReq{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SmePaymentsGetPaymentOrderByTrNumberReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmePaymentsGetPaymentOrderByTrNumberReq) ProtoMessage() {}

func (x *SmePaymentsGetPaymentOrderByTrNumberReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmePaymentsGetPaymentOrderByTrNumberReq.ProtoReflect.Descriptor instead.
func (*SmePaymentsGetPaymentOrderByTrNumberReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{49}
}

func (x *SmePaymentsGetPaymentOrderByTrNumberReq) GetTransactionNumber() string {
	if x != nil {
		return x.TransactionNumber
	}
	return ""
}

type SmePaymentsGetPaymentOrderByTrNumberResp struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title             string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Link              string                 `protobuf:"bytes,3,opt,name=link,proto3" json:"link,omitempty"`
	Version           int32                  `protobuf:"varint,4,opt,name=version,proto3" json:"version,omitempty"`
	TransactionStatus string                 `protobuf:"bytes,5,opt,name=transactionStatus,proto3" json:"transactionStatus,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *SmePaymentsGetPaymentOrderByTrNumberResp) Reset() {
	*x = SmePaymentsGetPaymentOrderByTrNumberResp{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SmePaymentsGetPaymentOrderByTrNumberResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmePaymentsGetPaymentOrderByTrNumberResp) ProtoMessage() {}

func (x *SmePaymentsGetPaymentOrderByTrNumberResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmePaymentsGetPaymentOrderByTrNumberResp.ProtoReflect.Descriptor instead.
func (*SmePaymentsGetPaymentOrderByTrNumberResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{50}
}

func (x *SmePaymentsGetPaymentOrderByTrNumberResp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SmePaymentsGetPaymentOrderByTrNumberResp) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *SmePaymentsGetPaymentOrderByTrNumberResp) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

func (x *SmePaymentsGetPaymentOrderByTrNumberResp) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *SmePaymentsGetPaymentOrderByTrNumberResp) GetTransactionStatus() string {
	if x != nil {
		return x.TransactionStatus
	}
	return ""
}

type SmePaymentsGetPaymentOrderResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Title         string                 `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Link          string                 `protobuf:"bytes,2,opt,name=link,proto3" json:"link,omitempty"`
	Version       string                 `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SmePaymentsGetPaymentOrderResp) Reset() {
	*x = SmePaymentsGetPaymentOrderResp{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SmePaymentsGetPaymentOrderResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SmePaymentsGetPaymentOrderResp) ProtoMessage() {}

func (x *SmePaymentsGetPaymentOrderResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SmePaymentsGetPaymentOrderResp.ProtoReflect.Descriptor instead.
func (*SmePaymentsGetPaymentOrderResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{51}
}

func (x *SmePaymentsGetPaymentOrderResp) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *SmePaymentsGetPaymentOrderResp) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

func (x *SmePaymentsGetPaymentOrderResp) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type GetEmployeeListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEmployeeListReq) Reset() {
	*x = GetEmployeeListReq{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEmployeeListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEmployeeListReq) ProtoMessage() {}

func (x *GetEmployeeListReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEmployeeListReq.ProtoReflect.Descriptor instead.
func (*GetEmployeeListReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{52}
}

type GetEmployeeListResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Employees     []*EmployeeInfo        `protobuf:"bytes,1,rep,name=employees,proto3" json:"employees,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEmployeeListResp) Reset() {
	*x = GetEmployeeListResp{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEmployeeListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEmployeeListResp) ProtoMessage() {}

func (x *GetEmployeeListResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEmployeeListResp.ProtoReflect.Descriptor instead.
func (*GetEmployeeListResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{53}
}

func (x *GetEmployeeListResp) GetEmployees() []*EmployeeInfo {
	if x != nil {
		return x.Employees
	}
	return nil
}

type DeleteEmployeeReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteEmployeeReq) Reset() {
	*x = DeleteEmployeeReq{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteEmployeeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEmployeeReq) ProtoMessage() {}

func (x *DeleteEmployeeReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEmployeeReq.ProtoReflect.Descriptor instead.
func (*DeleteEmployeeReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{54}
}

func (x *DeleteEmployeeReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DeleteEmployeeResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Employees     []*EmployeeInfo        `protobuf:"bytes,1,rep,name=employees,proto3" json:"employees,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteEmployeeResp) Reset() {
	*x = DeleteEmployeeResp{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteEmployeeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEmployeeResp) ProtoMessage() {}

func (x *DeleteEmployeeResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEmployeeResp.ProtoReflect.Descriptor instead.
func (*DeleteEmployeeResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{55}
}

func (x *DeleteEmployeeResp) GetEmployees() []*EmployeeInfo {
	if x != nil {
		return x.Employees
	}
	return nil
}

type CreateEmployeeReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// name имя сотрудника
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// middleName отчество сотрудника
	MiddleName *string `protobuf:"bytes,2,opt,name=middleName,proto3,oneof" json:"middleName,omitempty"`
	// lastName фамилия сотрудника
	LastName string `protobuf:"bytes,3,opt,name=lastName,proto3" json:"lastName,omitempty"`
	// iin ИИН сотрудника
	Iin string `protobuf:"bytes,4,opt,name=iin,proto3" json:"iin,omitempty"`
	// birthday дата рождения сотрудника
	Birthday string `protobuf:"bytes,5,opt,name=birthday,proto3" json:"birthday,omitempty"`
	// country страна сотрудника
	Country string `protobuf:"bytes,6,opt,name=country,proto3" json:"country,omitempty"`
	// displayOrder Порядок отображения в списке
	DisplayOrder int32 `protobuf:"varint,7,opt,name=displayOrder,proto3" json:"displayOrder,omitempty"`
	// employer_iin БИН/ИИН нанимателя
	EmployerIin   string `protobuf:"bytes,8,opt,name=employer_iin,json=employerIin,proto3" json:"employer_iin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateEmployeeReq) Reset() {
	*x = CreateEmployeeReq{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateEmployeeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEmployeeReq) ProtoMessage() {}

func (x *CreateEmployeeReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEmployeeReq.ProtoReflect.Descriptor instead.
func (*CreateEmployeeReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{56}
}

func (x *CreateEmployeeReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateEmployeeReq) GetMiddleName() string {
	if x != nil && x.MiddleName != nil {
		return *x.MiddleName
	}
	return ""
}

func (x *CreateEmployeeReq) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *CreateEmployeeReq) GetIin() string {
	if x != nil {
		return x.Iin
	}
	return ""
}

func (x *CreateEmployeeReq) GetBirthday() string {
	if x != nil {
		return x.Birthday
	}
	return ""
}

func (x *CreateEmployeeReq) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *CreateEmployeeReq) GetDisplayOrder() int32 {
	if x != nil {
		return x.DisplayOrder
	}
	return 0
}

func (x *CreateEmployeeReq) GetEmployerIin() string {
	if x != nil {
		return x.EmployerIin
	}
	return ""
}

type UpdateEmployeeReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id идентификатор записи о сотруднике
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// name имя сотрудника
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// middleName отчество сотрудника
	MiddleName *string `protobuf:"bytes,3,opt,name=middleName,proto3,oneof" json:"middleName,omitempty"`
	// lastName фамилия сотрудника
	LastName string `protobuf:"bytes,4,opt,name=lastName,proto3" json:"lastName,omitempty"`
	// iin ИИН сотрудника
	Iin string `protobuf:"bytes,5,opt,name=iin,proto3" json:"iin,omitempty"`
	// employer_iin БИН/ИИН нанимателя
	EmployerIin string `protobuf:"bytes,6,opt,name=employer_iin,json=employerIin,proto3" json:"employer_iin,omitempty"`
	// birthday дата рождения сотрудника
	Birthday string `protobuf:"bytes,7,opt,name=birthday,proto3" json:"birthday,omitempty"`
	// country страна сотрудника
	Country string `protobuf:"bytes,8,opt,name=country,proto3" json:"country,omitempty"`
	// displayOrder Порядок отображения в списке
	DisplayOrder  int32 `protobuf:"varint,9,opt,name=displayOrder,proto3" json:"displayOrder,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateEmployeeReq) Reset() {
	*x = UpdateEmployeeReq{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateEmployeeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEmployeeReq) ProtoMessage() {}

func (x *UpdateEmployeeReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEmployeeReq.ProtoReflect.Descriptor instead.
func (*UpdateEmployeeReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{57}
}

func (x *UpdateEmployeeReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateEmployeeReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateEmployeeReq) GetMiddleName() string {
	if x != nil && x.MiddleName != nil {
		return *x.MiddleName
	}
	return ""
}

func (x *UpdateEmployeeReq) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *UpdateEmployeeReq) GetIin() string {
	if x != nil {
		return x.Iin
	}
	return ""
}

func (x *UpdateEmployeeReq) GetEmployerIin() string {
	if x != nil {
		return x.EmployerIin
	}
	return ""
}

func (x *UpdateEmployeeReq) GetBirthday() string {
	if x != nil {
		return x.Birthday
	}
	return ""
}

func (x *UpdateEmployeeReq) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *UpdateEmployeeReq) GetDisplayOrder() int32 {
	if x != nil {
		return x.DisplayOrder
	}
	return 0
}

type EmployeeInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id идентификатор записи о сотруднике
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// name имя сотрудника
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// middleName отчество сотрудника
	MiddleName *string `protobuf:"bytes,3,opt,name=middleName,proto3,oneof" json:"middleName,omitempty"`
	// lastName фамилия сотрудника
	LastName string `protobuf:"bytes,4,opt,name=lastName,proto3" json:"lastName,omitempty"`
	// iin ИИН сотрудника
	Iin string `protobuf:"bytes,5,opt,name=iin,proto3" json:"iin,omitempty"`
	// employer_iin БИН/ИИН нанимателя
	EmployerIin string `protobuf:"bytes,6,opt,name=employer_iin,json=employerIin,proto3" json:"employer_iin,omitempty"`
	// birthday дата рождения сотрудника
	Birthday string `protobuf:"bytes,7,opt,name=birthday,proto3" json:"birthday,omitempty"`
	// country страна сотрудника
	Country string `protobuf:"bytes,8,opt,name=country,proto3" json:"country,omitempty"`
	// displayOrder Порядок отображения в списке
	DisplayOrder  int32 `protobuf:"varint,9,opt,name=displayOrder,proto3" json:"displayOrder,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmployeeInfo) Reset() {
	*x = EmployeeInfo{}
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmployeeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmployeeInfo) ProtoMessage() {}

func (x *EmployeeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_payments_sme_payments_sme_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmployeeInfo.ProtoReflect.Descriptor instead.
func (*EmployeeInfo) Descriptor() ([]byte, []int) {
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP(), []int{58}
}

func (x *EmployeeInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EmployeeInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EmployeeInfo) GetMiddleName() string {
	if x != nil && x.MiddleName != nil {
		return *x.MiddleName
	}
	return ""
}

func (x *EmployeeInfo) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *EmployeeInfo) GetIin() string {
	if x != nil {
		return x.Iin
	}
	return ""
}

func (x *EmployeeInfo) GetEmployerIin() string {
	if x != nil {
		return x.EmployerIin
	}
	return ""
}

func (x *EmployeeInfo) GetBirthday() string {
	if x != nil {
		return x.Birthday
	}
	return ""
}

func (x *EmployeeInfo) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *EmployeeInfo) GetDisplayOrder() int32 {
	if x != nil {
		return x.DisplayOrder
	}
	return 0
}

var File_specs_proto_payments_sme_payments_sme_proto protoreflect.FileDescriptor

const file_specs_proto_payments_sme_payments_sme_proto_rawDesc = "" +
	"\n" +
	"+specs/proto/payments-sme/payments-sme.proto\x12\vpaymentsSme\"\x10\n" +
	"\x0eHealthCheckReq\")\n" +
	"\x0fHealthCheckResp\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\"\x16\n" +
	"\x14SmePaymentsClientReq\"\x99\x02\n" +
	"\x15SmePaymentsClientResp\x12\x10\n" +
	"\x03iin\x18\x01 \x01(\tR\x03iin\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x18\n" +
	"\asurname\x18\x03 \x01(\tR\asurname\x12\x1e\n" +
	"\n" +
	"patronymic\x18\x04 \x01(\tR\n" +
	"patronymic\x12\x1c\n" +
	"\tbirthdate\x18\x05 \x01(\tR\tbirthdate\x12&\n" +
	"\x0eenterpriseName\x18\x06 \x01(\tR\x0eenterpriseName\x12.\n" +
	"\x12enterpriseKATOCode\x18\a \x01(\tR\x12enterpriseKATOCode\x12*\n" +
	"\x10enterpriseKATOId\x18\b \x01(\tR\x10enterpriseKATOId\"<\n" +
	"\x14ConfirmPaymentSmeReq\x12$\n" +
	"\rtransactionID\x18\x01 \x01(\tR\rtransactionID\"\x85\x01\n" +
	"\x15ConfirmPaymentSmeResp\x12\x16\n" +
	"\x06status\x18\x01 \x01(\tR\x06status\x12\x1e\n" +
	"\n" +
	"reasonCode\x18\x02 \x01(\tR\n" +
	"reasonCode\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\x12\x1c\n" +
	"\totpNeeded\x18\x04 \x01(\bR\totpNeeded\"R\n" +
	"\x17SmePaymentsCreateOtpReq\x127\n" +
	"\n" +
	"deviceInfo\x18\x01 \x01(\v2\x17.paymentsSme.DeviceInfoR\n" +
	"deviceInfo\"\x88\x02\n" +
	"\x18SmePaymentsCreateOtpResp\x12\x1c\n" +
	"\tattemptId\x18\x01 \x01(\tR\tattemptId\x12\x18\n" +
	"\acodeTtl\x18\x02 \x01(\tR\acodeTtl\x12&\n" +
	"\x0ecodeChecksLeft\x18\x03 \x01(\tR\x0ecodeChecksLeft\x12\"\n" +
	"\fattemptsLeft\x18\x04 \x01(\tR\fattemptsLeft\x12(\n" +
	"\x0fattemptsTimeout\x18\x05 \x01(\tR\x0fattemptsTimeout\x12(\n" +
	"\x0fnewAttemptDelay\x18\x06 \x01(\tR\x0fnewAttemptDelay\x12\x14\n" +
	"\x05error\x18\a \x01(\tR\x05error\"\xbc\x01\n" +
	"\n" +
	"DeviceInfo\x12\x1e\n" +
	"\n" +
	"appVersion\x18\x01 \x01(\tR\n" +
	"appVersion\x12 \n" +
	"\vdeviceModel\x18\x02 \x01(\tR\vdeviceModel\x12&\n" +
	"\x0einstallationId\x18\x03 \x01(\tR\x0einstallationId\x12\x1e\n" +
	"\n" +
	"systemType\x18\x04 \x01(\tR\n" +
	"systemType\x12$\n" +
	"\rsystemVersion\x18\x05 \x01(\tR\rsystemVersion\"7\n" +
	"\x17SmePaymentsOtpResendReq\x12\x1c\n" +
	"\tattemptId\x18\x01 \x01(\tR\tattemptId\"\x88\x02\n" +
	"\x18SmePaymentsOtpResendResp\x12\x1c\n" +
	"\tattemptId\x18\x01 \x01(\tR\tattemptId\x12\x18\n" +
	"\acodeTtl\x18\x02 \x01(\tR\acodeTtl\x12&\n" +
	"\x0ecodeChecksLeft\x18\x03 \x01(\tR\x0ecodeChecksLeft\x12\"\n" +
	"\fattemptsLeft\x18\x04 \x01(\tR\fattemptsLeft\x12(\n" +
	"\x0fattemptsTimeout\x18\x05 \x01(\tR\x0fattemptsTimeout\x12(\n" +
	"\x0fnewAttemptDelay\x18\x06 \x01(\tR\x0fnewAttemptDelay\x12\x14\n" +
	"\x05error\x18\a \x01(\tR\x05error\"s\n" +
	"\x19SmePaymentsOtpValidateReq\x12\x1c\n" +
	"\tattemptId\x18\x01 \x01(\tR\tattemptId\x12\x12\n" +
	"\x04code\x18\x02 \x01(\tR\x04code\x12$\n" +
	"\rtransactionID\x18\x03 \x01(\tR\rtransactionID\"L\n" +
	"\x1aSmePaymentsOtpValidateResp\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x14\n" +
	"\x05error\x18\x02 \x01(\tR\x05error\"U\n" +
	"\fKbeKodFilter\x12\x14\n" +
	"\x05codes\x18\x01 \x03(\tR\x05codes\x12!\n" +
	"\tresidency\x18\x02 \x01(\bH\x00R\tresidency\x88\x01\x01B\f\n" +
	"\n" +
	"_residency\"U\n" +
	"\x10GetKbeKodListReq\x126\n" +
	"\x06filter\x18\x01 \x01(\v2\x19.paymentsSme.KbeKodFilterH\x00R\x06filter\x88\x01\x01B\t\n" +
	"\a_filter\"P\n" +
	"\x06KbeKod\x12\x14\n" +
	"\x05value\x18\x01 \x01(\tR\x05value\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1c\n" +
	"\tresidency\x18\x03 \x01(\bR\tresidency\"8\n" +
	"\rGetKbeKodItem\x12'\n" +
	"\x04code\x18\x01 \x01(\v2\x13.paymentsSme.KbeKodR\x04code\"E\n" +
	"\x11GetKbeKodListResp\x120\n" +
	"\x05codes\x18\x01 \x03(\v2\x1a.paymentsSme.GetKbeKodItemR\x05codes\"9\n" +
	"\tKnpFilter\x12\x14\n" +
	"\x05codes\x18\x01 \x03(\tR\x05codes\x12\x16\n" +
	"\x06groups\x18\x02 \x03(\tR\x06groups\"O\n" +
	"\rGetKnpListReq\x123\n" +
	"\x06filter\x18\x01 \x01(\v2\x16.paymentsSme.KnpFilterH\x00R\x06filter\x88\x01\x01B\t\n" +
	"\a_filter\"/\n" +
	"\x03Knp\x12\x14\n" +
	"\x05value\x18\x01 \x01(\tR\x05value\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"2\n" +
	"\n" +
	"GetKnpItem\x12$\n" +
	"\x04code\x18\x01 \x01(\v2\x10.paymentsSme.KnpR\x04code\"?\n" +
	"\x0eGetKnpListResp\x12-\n" +
	"\x05codes\x18\x01 \x03(\v2\x17.paymentsSme.GetKnpItemR\x05codes\" \n" +
	"\n" +
	"BankFilter\x12\x12\n" +
	"\x04bics\x18\x01 \x03(\tR\x04bics\"Q\n" +
	"\x0eGetBankListReq\x124\n" +
	"\x06filter\x18\x01 \x01(\v2\x17.paymentsSme.BankFilterH\x00R\x06filter\x88\x01\x01B\t\n" +
	"\a_filter\"@\n" +
	"\x04Bank\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x12\x10\n" +
	"\x03bic\x18\x02 \x01(\tR\x03bic\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\"4\n" +
	"\vGetBankItem\x12%\n" +
	"\x04bank\x18\x01 \x01(\v2\x11.paymentsSme.BankR\x04bank\"A\n" +
	"\x0fGetBankListResp\x12.\n" +
	"\x05banks\x18\x01 \x03(\v2\x18.paymentsSme.GetBankItemR\x05banks\"W\n" +
	"\x11GetCountryListReq\x127\n" +
	"\x06filter\x18\x01 \x01(\v2\x1a.paymentsSme.CountryFilterH\x00R\x06filter\x88\x01\x01B\t\n" +
	"\a_filter\"%\n" +
	"\rCountryFilter\x12\x14\n" +
	"\x05codes\x18\x01 \x03(\tR\x05codes\"S\n" +
	"\aCountry\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\"H\n" +
	"\x12GetCountryListResp\x122\n" +
	"\tcountries\x18\x01 \x03(\v2\x14.paymentsSme.CountryR\tcountries\"O\n" +
	"\rGetKbkListReq\x123\n" +
	"\x06filter\x18\x01 \x01(\v2\x16.paymentsSme.KbkFilterH\x00R\x06filter\x88\x01\x01B\t\n" +
	"\a_filter\"!\n" +
	"\tKbkFilter\x12\x14\n" +
	"\x05codes\x18\x01 \x03(\tR\x05codes\"?\n" +
	"\x0eGetKbkListResp\x12-\n" +
	"\x05codes\x18\x01 \x03(\v2\x17.paymentsSme.GetKbkItemR\x05codes\"2\n" +
	"\n" +
	"GetKbkItem\x12$\n" +
	"\x04code\x18\x01 \x01(\v2\x10.paymentsSme.KbkR\x04code\"/\n" +
	"\x03Kbk\x12\x14\n" +
	"\x05value\x18\x01 \x01(\tR\x05value\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"a\n" +
	"\x16GetTaxAuthorityListReq\x12<\n" +
	"\x06filter\x18\x01 \x01(\v2\x1f.paymentsSme.TaxAuthorityFilterH\x00R\x06filter\x88\x01\x01B\t\n" +
	"\a_filter\".\n" +
	"\x12TaxAuthorityFilter\x12\x18\n" +
	"\aregions\x18\x01 \x03(\tR\aregions\"O\n" +
	"\x17GetTaxAuthorityListResp\x124\n" +
	"\x04ugds\x18\x01 \x03(\v2 .paymentsSme.GetTaxAuthorityItemR\x04ugds\"B\n" +
	"\x13GetTaxAuthorityItem\x12+\n" +
	"\x03ugd\x18\x01 \x01(\v2\x19.paymentsSme.TaxAuthorityR\x03ugd\"\x88\x01\n" +
	"\fTaxAuthority\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x10\n" +
	"\x03bin\x18\x02 \x01(\tR\x03bin\x12\x1e\n" +
	"\n" +
	"regionCode\x18\x03 \x01(\tR\n" +
	"regionCode\x12\x1e\n" +
	"\n" +
	"regionName\x18\x04 \x01(\tR\n" +
	"regionName\x12\x12\n" +
	"\x04code\x18\x05 \x01(\tR\x04code\"\x18\n" +
	"\x16SmePaymentsWorktimeReq\"\xb7\x01\n" +
	"\x17SmePaymentsWorktimeResp\x12\x12\n" +
	"\x04date\x18\x01 \x01(\tR\x04date\x12,\n" +
	"\x11isDateOperational\x18\x02 \x01(\bR\x11isDateOperational\x120\n" +
	"\x13nextOperationalDate\x18\x03 \x01(\tR\x13nextOperationalDate\x12(\n" +
	"\x0fisEndOfWorkTime\x18\x04 \x01(\bR\x0fisEndOfWorkTime\"\x98\x01\n" +
	"\x10CreatePaymentReq\x12&\n" +
	"\x0eidempotencyKey\x18\x01 \x01(\tR\x0eidempotencyKey\x12 \n" +
	"\vpaymentCode\x18\x02 \x01(\tR\vpaymentCode\x12:\n" +
	"\vpaymentData\x18\x03 \x01(\v2\x18.paymentsSme.PaymentDataR\vpaymentData\"\xa7\x01\n" +
	"\x11CreatePaymentResp\x12$\n" +
	"\rtransactionID\x18\x01 \x01(\tR\rtransactionID\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12\x1e\n" +
	"\n" +
	"reasonCode\x18\x03 \x01(\tR\n" +
	"reasonCode\x12\x16\n" +
	"\x06reason\x18\x04 \x01(\tR\x06reason\x12\x1c\n" +
	"\totpNeeded\x18\x05 \x01(\bR\totpNeeded\"\x9c\x04\n" +
	"\vPaymentData\x12$\n" +
	"\rpaymentPeriod\x18\x01 \x01(\tR\rpaymentPeriod\x12 \n" +
	"\vpurposeCode\x18\x02 \x01(\tR\vpurposeCode\x12&\n" +
	"\x0epurposeDetails\x18\x03 \x01(\tR\x0epurposeDetails\x12\x16\n" +
	"\x06amount\x18\x04 \x01(\tR\x06amount\x12\"\n" +
	"\fpayerAccount\x18\x05 \x01(\tR\fpayerAccount\x127\n" +
	"\temployees\x18\x06 \x03(\v2\x19.paymentsSme.EmployeeItemR\temployees\x12\x1e\n" +
	"\n" +
	"signatoryA\x18\a \x01(\tR\n" +
	"signatoryA\x12,\n" +
	"\x11beneficiaryBINIIN\x18\b \x01(\tR\x11beneficiaryBINIIN\x12(\n" +
	"\x0fbeneficiaryName\x18\t \x01(\tR\x0fbeneficiaryName\x120\n" +
	"\x13realBeneficiaryName\x18\n" +
	" \x01(\tR\x13realBeneficiaryName\x124\n" +
	"\x15realBeneficiaryBINIIN\x18\v \x01(\tR\x15realBeneficiaryBINIIN\x126\n" +
	"\x16realBeneficiaryCountry\x18\f \x01(\tR\x16realBeneficiaryCountry\x12\x10\n" +
	"\x03kbk\x18\r \x01(\tR\x03kbk\"\xcb\x02\n" +
	"\fEmployeeItem\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12#\n" +
	"\n" +
	"middleName\x18\x02 \x01(\tH\x00R\n" +
	"middleName\x88\x01\x01\x12\x1a\n" +
	"\blastName\x18\x03 \x01(\tR\blastName\x12\x10\n" +
	"\x03iin\x18\x04 \x01(\tR\x03iin\x12\x1a\n" +
	"\bbirthday\x18\x05 \x01(\tR\bbirthday\x12\x18\n" +
	"\acountry\x18\x06 \x01(\tR\acountry\x12\x16\n" +
	"\x06amount\x18\a \x01(\tR\x06amount\x12 \n" +
	"\vvaluePeriod\x18\b \x01(\tR\vvaluePeriod\x12\x0e\n" +
	"\x02id\x18\t \x01(\tR\x02id\x12\"\n" +
	"\fdisplayOrder\x18\n" +
	" \x01(\x05R\fdisplayOrder\x12!\n" +
	"\femployer_iin\x18\v \x01(\tR\vemployerIinB\r\n" +
	"\v_middleName\"E\n" +
	"\x1dSmePaymentsGetPaymentOrderReq\x12$\n" +
	"\rtransactionID\x18\x01 \x01(\tR\rtransactionID\"W\n" +
	"'SmePaymentsGetPaymentOrderByTrNumberReq\x12,\n" +
	"\x11transactionNumber\x18\x01 \x01(\tR\x11transactionNumber\"\xac\x01\n" +
	"(SmePaymentsGetPaymentOrderByTrNumberResp\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12\x12\n" +
	"\x04link\x18\x03 \x01(\tR\x04link\x12\x18\n" +
	"\aversion\x18\x04 \x01(\x05R\aversion\x12,\n" +
	"\x11transactionStatus\x18\x05 \x01(\tR\x11transactionStatus\"d\n" +
	"\x1eSmePaymentsGetPaymentOrderResp\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12\x12\n" +
	"\x04link\x18\x02 \x01(\tR\x04link\x12\x18\n" +
	"\aversion\x18\x03 \x01(\tR\aversion\"\x14\n" +
	"\x12GetEmployeeListReq\"N\n" +
	"\x13GetEmployeeListResp\x127\n" +
	"\temployees\x18\x01 \x03(\v2\x19.paymentsSme.EmployeeInfoR\temployees\"#\n" +
	"\x11DeleteEmployeeReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"M\n" +
	"\x12DeleteEmployeeResp\x127\n" +
	"\temployees\x18\x01 \x03(\v2\x19.paymentsSme.EmployeeInfoR\temployees\"\x86\x02\n" +
	"\x11CreateEmployeeReq\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12#\n" +
	"\n" +
	"middleName\x18\x02 \x01(\tH\x00R\n" +
	"middleName\x88\x01\x01\x12\x1a\n" +
	"\blastName\x18\x03 \x01(\tR\blastName\x12\x10\n" +
	"\x03iin\x18\x04 \x01(\tR\x03iin\x12\x1a\n" +
	"\bbirthday\x18\x05 \x01(\tR\bbirthday\x12\x18\n" +
	"\acountry\x18\x06 \x01(\tR\acountry\x12\"\n" +
	"\fdisplayOrder\x18\a \x01(\x05R\fdisplayOrder\x12!\n" +
	"\femployer_iin\x18\b \x01(\tR\vemployerIinB\r\n" +
	"\v_middleName\"\x96\x02\n" +
	"\x11UpdateEmployeeReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12#\n" +
	"\n" +
	"middleName\x18\x03 \x01(\tH\x00R\n" +
	"middleName\x88\x01\x01\x12\x1a\n" +
	"\blastName\x18\x04 \x01(\tR\blastName\x12\x10\n" +
	"\x03iin\x18\x05 \x01(\tR\x03iin\x12!\n" +
	"\femployer_iin\x18\x06 \x01(\tR\vemployerIin\x12\x1a\n" +
	"\bbirthday\x18\a \x01(\tR\bbirthday\x12\x18\n" +
	"\acountry\x18\b \x01(\tR\acountry\x12\"\n" +
	"\fdisplayOrder\x18\t \x01(\x05R\fdisplayOrderB\r\n" +
	"\v_middleName\"\x91\x02\n" +
	"\fEmployeeInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12#\n" +
	"\n" +
	"middleName\x18\x03 \x01(\tH\x00R\n" +
	"middleName\x88\x01\x01\x12\x1a\n" +
	"\blastName\x18\x04 \x01(\tR\blastName\x12\x10\n" +
	"\x03iin\x18\x05 \x01(\tR\x03iin\x12!\n" +
	"\femployer_iin\x18\x06 \x01(\tR\vemployerIin\x12\x1a\n" +
	"\bbirthday\x18\a \x01(\tR\bbirthday\x12\x18\n" +
	"\acountry\x18\b \x01(\tR\acountry\x12\"\n" +
	"\fdisplayOrder\x18\t \x01(\x05R\fdisplayOrderB\r\n" +
	"\v_middleName2\xa3\x0e\n" +
	"\vPaymentssme\x12H\n" +
	"\vHealthCheck\x12\x1b.paymentsSme.HealthCheckReq\x1a\x1c.paymentsSme.HealthCheckResp\x12Z\n" +
	"\x11SmePaymentsClient\x12!.paymentsSme.SmePaymentsClientReq\x1a\".paymentsSme.SmePaymentsClientResp\x12Z\n" +
	"\x11ConfirmPaymentSme\x12!.paymentsSme.ConfirmPaymentSmeReq\x1a\".paymentsSme.ConfirmPaymentSmeResp\x12c\n" +
	"\x14SmePaymentsCreateOtp\x12$.paymentsSme.SmePaymentsCreateOtpReq\x1a%.paymentsSme.SmePaymentsCreateOtpResp\x12c\n" +
	"\x14SmePaymentsOtpResend\x12$.paymentsSme.SmePaymentsOtpResendReq\x1a%.paymentsSme.SmePaymentsOtpResendResp\x12i\n" +
	"\x16SmePaymentsOtpValidate\x12&.paymentsSme.SmePaymentsOtpValidateReq\x1a'.paymentsSme.SmePaymentsOtpValidateResp\x12N\n" +
	"\rGetKbeKodList\x12\x1d.paymentsSme.GetKbeKodListReq\x1a\x1e.paymentsSme.GetKbeKodListResp\x12E\n" +
	"\n" +
	"GetKnpList\x12\x1a.paymentsSme.GetKnpListReq\x1a\x1b.paymentsSme.GetKnpListResp\x12H\n" +
	"\vGetBankList\x12\x1b.paymentsSme.GetBankListReq\x1a\x1c.paymentsSme.GetBankListResp\x12Q\n" +
	"\x0eGetCountryList\x12\x1e.paymentsSme.GetCountryListReq\x1a\x1f.paymentsSme.GetCountryListResp\x12E\n" +
	"\n" +
	"GetKbkList\x12\x1a.paymentsSme.GetKbkListReq\x1a\x1b.paymentsSme.GetKbkListResp\x12`\n" +
	"\x13GetTaxAuthorityList\x12#.paymentsSme.GetTaxAuthorityListReq\x1a$.paymentsSme.GetTaxAuthorityListResp\x12`\n" +
	"\x13SmePaymentsWorktime\x12#.paymentsSme.SmePaymentsWorktimeReq\x1a$.paymentsSme.SmePaymentsWorktimeResp\x12N\n" +
	"\rCreatePayment\x12\x1d.paymentsSme.CreatePaymentReq\x1a\x1e.paymentsSme.CreatePaymentResp\x12u\n" +
	"\x1aSmePaymentsGetPaymentOrder\x12*.paymentsSme.SmePaymentsGetPaymentOrderReq\x1a+.paymentsSme.SmePaymentsGetPaymentOrderResp\x12\x93\x01\n" +
	"$SmePaymentsGetPaymentOrderByTrNumber\x124.paymentsSme.SmePaymentsGetPaymentOrderByTrNumberReq\x1a5.paymentsSme.SmePaymentsGetPaymentOrderByTrNumberResp\x12T\n" +
	"\x0fGetEmployeeList\x12\x1f.paymentsSme.GetEmployeeListReq\x1a .paymentsSme.GetEmployeeListResp\x12Q\n" +
	"\x0eDeleteEmployee\x12\x1e.paymentsSme.DeleteEmployeeReq\x1a\x1f.paymentsSme.DeleteEmployeeResp\x12K\n" +
	"\x0eCreateEmployee\x12\x1e.paymentsSme.CreateEmployeeReq\x1a\x19.paymentsSme.EmployeeInfo\x12K\n" +
	"\x0eUpdateEmployee\x12\x1e.paymentsSme.UpdateEmployeeReq\x1a\x19.paymentsSme.EmployeeInfoB\x1aZ\x18specs/proto/payments-smeb\x06proto3"

var (
	file_specs_proto_payments_sme_payments_sme_proto_rawDescOnce sync.Once
	file_specs_proto_payments_sme_payments_sme_proto_rawDescData []byte
)

func file_specs_proto_payments_sme_payments_sme_proto_rawDescGZIP() []byte {
	file_specs_proto_payments_sme_payments_sme_proto_rawDescOnce.Do(func() {
		file_specs_proto_payments_sme_payments_sme_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_specs_proto_payments_sme_payments_sme_proto_rawDesc), len(file_specs_proto_payments_sme_payments_sme_proto_rawDesc)))
	})
	return file_specs_proto_payments_sme_payments_sme_proto_rawDescData
}

var file_specs_proto_payments_sme_payments_sme_proto_msgTypes = make([]protoimpl.MessageInfo, 59)
var file_specs_proto_payments_sme_payments_sme_proto_goTypes = []any{
	(*HealthCheckReq)(nil),                           // 0: paymentsSme.HealthCheckReq
	(*HealthCheckResp)(nil),                          // 1: paymentsSme.HealthCheckResp
	(*SmePaymentsClientReq)(nil),                     // 2: paymentsSme.SmePaymentsClientReq
	(*SmePaymentsClientResp)(nil),                    // 3: paymentsSme.SmePaymentsClientResp
	(*ConfirmPaymentSmeReq)(nil),                     // 4: paymentsSme.ConfirmPaymentSmeReq
	(*ConfirmPaymentSmeResp)(nil),                    // 5: paymentsSme.ConfirmPaymentSmeResp
	(*SmePaymentsCreateOtpReq)(nil),                  // 6: paymentsSme.SmePaymentsCreateOtpReq
	(*SmePaymentsCreateOtpResp)(nil),                 // 7: paymentsSme.SmePaymentsCreateOtpResp
	(*DeviceInfo)(nil),                               // 8: paymentsSme.DeviceInfo
	(*SmePaymentsOtpResendReq)(nil),                  // 9: paymentsSme.SmePaymentsOtpResendReq
	(*SmePaymentsOtpResendResp)(nil),                 // 10: paymentsSme.SmePaymentsOtpResendResp
	(*SmePaymentsOtpValidateReq)(nil),                // 11: paymentsSme.SmePaymentsOtpValidateReq
	(*SmePaymentsOtpValidateResp)(nil),               // 12: paymentsSme.SmePaymentsOtpValidateResp
	(*KbeKodFilter)(nil),                             // 13: paymentsSme.KbeKodFilter
	(*GetKbeKodListReq)(nil),                         // 14: paymentsSme.GetKbeKodListReq
	(*KbeKod)(nil),                                   // 15: paymentsSme.KbeKod
	(*GetKbeKodItem)(nil),                            // 16: paymentsSme.GetKbeKodItem
	(*GetKbeKodListResp)(nil),                        // 17: paymentsSme.GetKbeKodListResp
	(*KnpFilter)(nil),                                // 18: paymentsSme.KnpFilter
	(*GetKnpListReq)(nil),                            // 19: paymentsSme.GetKnpListReq
	(*Knp)(nil),                                      // 20: paymentsSme.Knp
	(*GetKnpItem)(nil),                               // 21: paymentsSme.GetKnpItem
	(*GetKnpListResp)(nil),                           // 22: paymentsSme.GetKnpListResp
	(*BankFilter)(nil),                               // 23: paymentsSme.BankFilter
	(*GetBankListReq)(nil),                           // 24: paymentsSme.GetBankListReq
	(*Bank)(nil),                                     // 25: paymentsSme.Bank
	(*GetBankItem)(nil),                              // 26: paymentsSme.GetBankItem
	(*GetBankListResp)(nil),                          // 27: paymentsSme.GetBankListResp
	(*GetCountryListReq)(nil),                        // 28: paymentsSme.GetCountryListReq
	(*CountryFilter)(nil),                            // 29: paymentsSme.CountryFilter
	(*Country)(nil),                                  // 30: paymentsSme.Country
	(*GetCountryListResp)(nil),                       // 31: paymentsSme.GetCountryListResp
	(*GetKbkListReq)(nil),                            // 32: paymentsSme.GetKbkListReq
	(*KbkFilter)(nil),                                // 33: paymentsSme.KbkFilter
	(*GetKbkListResp)(nil),                           // 34: paymentsSme.GetKbkListResp
	(*GetKbkItem)(nil),                               // 35: paymentsSme.GetKbkItem
	(*Kbk)(nil),                                      // 36: paymentsSme.Kbk
	(*GetTaxAuthorityListReq)(nil),                   // 37: paymentsSme.GetTaxAuthorityListReq
	(*TaxAuthorityFilter)(nil),                       // 38: paymentsSme.TaxAuthorityFilter
	(*GetTaxAuthorityListResp)(nil),                  // 39: paymentsSme.GetTaxAuthorityListResp
	(*GetTaxAuthorityItem)(nil),                      // 40: paymentsSme.GetTaxAuthorityItem
	(*TaxAuthority)(nil),                             // 41: paymentsSme.TaxAuthority
	(*SmePaymentsWorktimeReq)(nil),                   // 42: paymentsSme.SmePaymentsWorktimeReq
	(*SmePaymentsWorktimeResp)(nil),                  // 43: paymentsSme.SmePaymentsWorktimeResp
	(*CreatePaymentReq)(nil),                         // 44: paymentsSme.CreatePaymentReq
	(*CreatePaymentResp)(nil),                        // 45: paymentsSme.CreatePaymentResp
	(*PaymentData)(nil),                              // 46: paymentsSme.PaymentData
	(*EmployeeItem)(nil),                             // 47: paymentsSme.EmployeeItem
	(*SmePaymentsGetPaymentOrderReq)(nil),            // 48: paymentsSme.SmePaymentsGetPaymentOrderReq
	(*SmePaymentsGetPaymentOrderByTrNumberReq)(nil),  // 49: paymentsSme.SmePaymentsGetPaymentOrderByTrNumberReq
	(*SmePaymentsGetPaymentOrderByTrNumberResp)(nil), // 50: paymentsSme.SmePaymentsGetPaymentOrderByTrNumberResp
	(*SmePaymentsGetPaymentOrderResp)(nil),           // 51: paymentsSme.SmePaymentsGetPaymentOrderResp
	(*GetEmployeeListReq)(nil),                       // 52: paymentsSme.GetEmployeeListReq
	(*GetEmployeeListResp)(nil),                      // 53: paymentsSme.GetEmployeeListResp
	(*DeleteEmployeeReq)(nil),                        // 54: paymentsSme.DeleteEmployeeReq
	(*DeleteEmployeeResp)(nil),                       // 55: paymentsSme.DeleteEmployeeResp
	(*CreateEmployeeReq)(nil),                        // 56: paymentsSme.CreateEmployeeReq
	(*UpdateEmployeeReq)(nil),                        // 57: paymentsSme.UpdateEmployeeReq
	(*EmployeeInfo)(nil),                             // 58: paymentsSme.EmployeeInfo
}
var file_specs_proto_payments_sme_payments_sme_proto_depIdxs = []int32{
	8,  // 0: paymentsSme.SmePaymentsCreateOtpReq.deviceInfo:type_name -> paymentsSme.DeviceInfo
	13, // 1: paymentsSme.GetKbeKodListReq.filter:type_name -> paymentsSme.KbeKodFilter
	15, // 2: paymentsSme.GetKbeKodItem.code:type_name -> paymentsSme.KbeKod
	16, // 3: paymentsSme.GetKbeKodListResp.codes:type_name -> paymentsSme.GetKbeKodItem
	18, // 4: paymentsSme.GetKnpListReq.filter:type_name -> paymentsSme.KnpFilter
	20, // 5: paymentsSme.GetKnpItem.code:type_name -> paymentsSme.Knp
	21, // 6: paymentsSme.GetKnpListResp.codes:type_name -> paymentsSme.GetKnpItem
	23, // 7: paymentsSme.GetBankListReq.filter:type_name -> paymentsSme.BankFilter
	25, // 8: paymentsSme.GetBankItem.bank:type_name -> paymentsSme.Bank
	26, // 9: paymentsSme.GetBankListResp.banks:type_name -> paymentsSme.GetBankItem
	29, // 10: paymentsSme.GetCountryListReq.filter:type_name -> paymentsSme.CountryFilter
	30, // 11: paymentsSme.GetCountryListResp.countries:type_name -> paymentsSme.Country
	33, // 12: paymentsSme.GetKbkListReq.filter:type_name -> paymentsSme.KbkFilter
	35, // 13: paymentsSme.GetKbkListResp.codes:type_name -> paymentsSme.GetKbkItem
	36, // 14: paymentsSme.GetKbkItem.code:type_name -> paymentsSme.Kbk
	38, // 15: paymentsSme.GetTaxAuthorityListReq.filter:type_name -> paymentsSme.TaxAuthorityFilter
	40, // 16: paymentsSme.GetTaxAuthorityListResp.ugds:type_name -> paymentsSme.GetTaxAuthorityItem
	41, // 17: paymentsSme.GetTaxAuthorityItem.ugd:type_name -> paymentsSme.TaxAuthority
	46, // 18: paymentsSme.CreatePaymentReq.paymentData:type_name -> paymentsSme.PaymentData
	47, // 19: paymentsSme.PaymentData.employees:type_name -> paymentsSme.EmployeeItem
	58, // 20: paymentsSme.GetEmployeeListResp.employees:type_name -> paymentsSme.EmployeeInfo
	58, // 21: paymentsSme.DeleteEmployeeResp.employees:type_name -> paymentsSme.EmployeeInfo
	0,  // 22: paymentsSme.Paymentssme.HealthCheck:input_type -> paymentsSme.HealthCheckReq
	2,  // 23: paymentsSme.Paymentssme.SmePaymentsClient:input_type -> paymentsSme.SmePaymentsClientReq
	4,  // 24: paymentsSme.Paymentssme.ConfirmPaymentSme:input_type -> paymentsSme.ConfirmPaymentSmeReq
	6,  // 25: paymentsSme.Paymentssme.SmePaymentsCreateOtp:input_type -> paymentsSme.SmePaymentsCreateOtpReq
	9,  // 26: paymentsSme.Paymentssme.SmePaymentsOtpResend:input_type -> paymentsSme.SmePaymentsOtpResendReq
	11, // 27: paymentsSme.Paymentssme.SmePaymentsOtpValidate:input_type -> paymentsSme.SmePaymentsOtpValidateReq
	14, // 28: paymentsSme.Paymentssme.GetKbeKodList:input_type -> paymentsSme.GetKbeKodListReq
	19, // 29: paymentsSme.Paymentssme.GetKnpList:input_type -> paymentsSme.GetKnpListReq
	24, // 30: paymentsSme.Paymentssme.GetBankList:input_type -> paymentsSme.GetBankListReq
	28, // 31: paymentsSme.Paymentssme.GetCountryList:input_type -> paymentsSme.GetCountryListReq
	32, // 32: paymentsSme.Paymentssme.GetKbkList:input_type -> paymentsSme.GetKbkListReq
	37, // 33: paymentsSme.Paymentssme.GetTaxAuthorityList:input_type -> paymentsSme.GetTaxAuthorityListReq
	42, // 34: paymentsSme.Paymentssme.SmePaymentsWorktime:input_type -> paymentsSme.SmePaymentsWorktimeReq
	44, // 35: paymentsSme.Paymentssme.CreatePayment:input_type -> paymentsSme.CreatePaymentReq
	48, // 36: paymentsSme.Paymentssme.SmePaymentsGetPaymentOrder:input_type -> paymentsSme.SmePaymentsGetPaymentOrderReq
	49, // 37: paymentsSme.Paymentssme.SmePaymentsGetPaymentOrderByTrNumber:input_type -> paymentsSme.SmePaymentsGetPaymentOrderByTrNumberReq
	52, // 38: paymentsSme.Paymentssme.GetEmployeeList:input_type -> paymentsSme.GetEmployeeListReq
	54, // 39: paymentsSme.Paymentssme.DeleteEmployee:input_type -> paymentsSme.DeleteEmployeeReq
	56, // 40: paymentsSme.Paymentssme.CreateEmployee:input_type -> paymentsSme.CreateEmployeeReq
	57, // 41: paymentsSme.Paymentssme.UpdateEmployee:input_type -> paymentsSme.UpdateEmployeeReq
	1,  // 42: paymentsSme.Paymentssme.HealthCheck:output_type -> paymentsSme.HealthCheckResp
	3,  // 43: paymentsSme.Paymentssme.SmePaymentsClient:output_type -> paymentsSme.SmePaymentsClientResp
	5,  // 44: paymentsSme.Paymentssme.ConfirmPaymentSme:output_type -> paymentsSme.ConfirmPaymentSmeResp
	7,  // 45: paymentsSme.Paymentssme.SmePaymentsCreateOtp:output_type -> paymentsSme.SmePaymentsCreateOtpResp
	10, // 46: paymentsSme.Paymentssme.SmePaymentsOtpResend:output_type -> paymentsSme.SmePaymentsOtpResendResp
	12, // 47: paymentsSme.Paymentssme.SmePaymentsOtpValidate:output_type -> paymentsSme.SmePaymentsOtpValidateResp
	17, // 48: paymentsSme.Paymentssme.GetKbeKodList:output_type -> paymentsSme.GetKbeKodListResp
	22, // 49: paymentsSme.Paymentssme.GetKnpList:output_type -> paymentsSme.GetKnpListResp
	27, // 50: paymentsSme.Paymentssme.GetBankList:output_type -> paymentsSme.GetBankListResp
	31, // 51: paymentsSme.Paymentssme.GetCountryList:output_type -> paymentsSme.GetCountryListResp
	34, // 52: paymentsSme.Paymentssme.GetKbkList:output_type -> paymentsSme.GetKbkListResp
	39, // 53: paymentsSme.Paymentssme.GetTaxAuthorityList:output_type -> paymentsSme.GetTaxAuthorityListResp
	43, // 54: paymentsSme.Paymentssme.SmePaymentsWorktime:output_type -> paymentsSme.SmePaymentsWorktimeResp
	45, // 55: paymentsSme.Paymentssme.CreatePayment:output_type -> paymentsSme.CreatePaymentResp
	51, // 56: paymentsSme.Paymentssme.SmePaymentsGetPaymentOrder:output_type -> paymentsSme.SmePaymentsGetPaymentOrderResp
	50, // 57: paymentsSme.Paymentssme.SmePaymentsGetPaymentOrderByTrNumber:output_type -> paymentsSme.SmePaymentsGetPaymentOrderByTrNumberResp
	53, // 58: paymentsSme.Paymentssme.GetEmployeeList:output_type -> paymentsSme.GetEmployeeListResp
	55, // 59: paymentsSme.Paymentssme.DeleteEmployee:output_type -> paymentsSme.DeleteEmployeeResp
	58, // 60: paymentsSme.Paymentssme.CreateEmployee:output_type -> paymentsSme.EmployeeInfo
	58, // 61: paymentsSme.Paymentssme.UpdateEmployee:output_type -> paymentsSme.EmployeeInfo
	42, // [42:62] is the sub-list for method output_type
	22, // [22:42] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_specs_proto_payments_sme_payments_sme_proto_init() }
func file_specs_proto_payments_sme_payments_sme_proto_init() {
	if File_specs_proto_payments_sme_payments_sme_proto != nil {
		return
	}
	file_specs_proto_payments_sme_payments_sme_proto_msgTypes[13].OneofWrappers = []any{}
	file_specs_proto_payments_sme_payments_sme_proto_msgTypes[14].OneofWrappers = []any{}
	file_specs_proto_payments_sme_payments_sme_proto_msgTypes[19].OneofWrappers = []any{}
	file_specs_proto_payments_sme_payments_sme_proto_msgTypes[24].OneofWrappers = []any{}
	file_specs_proto_payments_sme_payments_sme_proto_msgTypes[28].OneofWrappers = []any{}
	file_specs_proto_payments_sme_payments_sme_proto_msgTypes[32].OneofWrappers = []any{}
	file_specs_proto_payments_sme_payments_sme_proto_msgTypes[37].OneofWrappers = []any{}
	file_specs_proto_payments_sme_payments_sme_proto_msgTypes[47].OneofWrappers = []any{}
	file_specs_proto_payments_sme_payments_sme_proto_msgTypes[56].OneofWrappers = []any{}
	file_specs_proto_payments_sme_payments_sme_proto_msgTypes[57].OneofWrappers = []any{}
	file_specs_proto_payments_sme_payments_sme_proto_msgTypes[58].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_specs_proto_payments_sme_payments_sme_proto_rawDesc), len(file_specs_proto_payments_sme_payments_sme_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   59,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_specs_proto_payments_sme_payments_sme_proto_goTypes,
		DependencyIndexes: file_specs_proto_payments_sme_payments_sme_proto_depIdxs,
		MessageInfos:      file_specs_proto_payments_sme_payments_sme_proto_msgTypes,
	}.Build()
	File_specs_proto_payments_sme_payments_sme_proto = out.File
	file_specs_proto_payments_sme_payments_sme_proto_goTypes = nil
	file_specs_proto_payments_sme_payments_sme_proto_depIdxs = nil
}
