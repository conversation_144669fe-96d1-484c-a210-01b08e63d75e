// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: specs/proto/payments-sme/payments-sme.proto

package payments_sme

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Paymentssme_HealthCheck_FullMethodName                          = "/paymentsSme.Paymentssme/HealthCheck"
	Paymentssme_SmePaymentsClient_FullMethodName                    = "/paymentsSme.Paymentssme/SmePaymentsClient"
	Paymentssme_ConfirmPaymentSme_FullMethodName                    = "/paymentsSme.Paymentssme/ConfirmPaymentSme"
	Paymentssme_SmePaymentsCreateOtp_FullMethodName                 = "/paymentsSme.Paymentssme/SmePaymentsCreateOtp"
	Paymentssme_SmePaymentsOtpResend_FullMethodName                 = "/paymentsSme.Paymentssme/SmePaymentsOtpResend"
	Paymentssme_SmePaymentsOtpValidate_FullMethodName               = "/paymentsSme.Paymentssme/SmePaymentsOtpValidate"
	Paymentssme_GetKbeKodList_FullMethodName                        = "/paymentsSme.Paymentssme/GetKbeKodList"
	Paymentssme_GetKnpList_FullMethodName                           = "/paymentsSme.Paymentssme/GetKnpList"
	Paymentssme_GetBankList_FullMethodName                          = "/paymentsSme.Paymentssme/GetBankList"
	Paymentssme_GetCountryList_FullMethodName                       = "/paymentsSme.Paymentssme/GetCountryList"
	Paymentssme_GetKbkList_FullMethodName                           = "/paymentsSme.Paymentssme/GetKbkList"
	Paymentssme_GetTaxAuthorityList_FullMethodName                  = "/paymentsSme.Paymentssme/GetTaxAuthorityList"
	Paymentssme_SmePaymentsWorktime_FullMethodName                  = "/paymentsSme.Paymentssme/SmePaymentsWorktime"
	Paymentssme_CreatePayment_FullMethodName                        = "/paymentsSme.Paymentssme/CreatePayment"
	Paymentssme_SmePaymentsGetPaymentOrder_FullMethodName           = "/paymentsSme.Paymentssme/SmePaymentsGetPaymentOrder"
	Paymentssme_SmePaymentsGetPaymentOrderByTrNumber_FullMethodName = "/paymentsSme.Paymentssme/SmePaymentsGetPaymentOrderByTrNumber"
	Paymentssme_GetEmployeeList_FullMethodName                      = "/paymentsSme.Paymentssme/GetEmployeeList"
	Paymentssme_DeleteEmployee_FullMethodName                       = "/paymentsSme.Paymentssme/DeleteEmployee"
	Paymentssme_CreateEmployee_FullMethodName                       = "/paymentsSme.Paymentssme/CreateEmployee"
	Paymentssme_UpdateEmployee_FullMethodName                       = "/paymentsSme.Paymentssme/UpdateEmployee"
)

// PaymentssmeClient is the client API for Paymentssme service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PaymentssmeClient interface {
	HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error)
	SmePaymentsClient(ctx context.Context, in *SmePaymentsClientReq, opts ...grpc.CallOption) (*SmePaymentsClientResp, error)
	ConfirmPaymentSme(ctx context.Context, in *ConfirmPaymentSmeReq, opts ...grpc.CallOption) (*ConfirmPaymentSmeResp, error)
	SmePaymentsCreateOtp(ctx context.Context, in *SmePaymentsCreateOtpReq, opts ...grpc.CallOption) (*SmePaymentsCreateOtpResp, error)
	SmePaymentsOtpResend(ctx context.Context, in *SmePaymentsOtpResendReq, opts ...grpc.CallOption) (*SmePaymentsOtpResendResp, error)
	SmePaymentsOtpValidate(ctx context.Context, in *SmePaymentsOtpValidateReq, opts ...grpc.CallOption) (*SmePaymentsOtpValidateResp, error)
	// GetKbeKodList получение списка Кбе/КОд кодов
	GetKbeKodList(ctx context.Context, in *GetKbeKodListReq, opts ...grpc.CallOption) (*GetKbeKodListResp, error)
	// GetKnpList получение списка КНП
	GetKnpList(ctx context.Context, in *GetKnpListReq, opts ...grpc.CallOption) (*GetKnpListResp, error)
	// GetBankList получение списка БВУ
	GetBankList(ctx context.Context, in *GetBankListReq, opts ...grpc.CallOption) (*GetBankListResp, error)
	// GetCountryList получение списка стран
	GetCountryList(ctx context.Context, in *GetCountryListReq, opts ...grpc.CallOption) (*GetCountryListResp, error)
	// GetKbkList получение списка КБК
	GetKbkList(ctx context.Context, in *GetKbkListReq, opts ...grpc.CallOption) (*GetKbkListResp, error)
	// GetTaxAuthorityList получение списка УГД
	GetTaxAuthorityList(ctx context.Context, in *GetTaxAuthorityListReq, opts ...grpc.CallOption) (*GetTaxAuthorityListResp, error)
	SmePaymentsWorktime(ctx context.Context, in *SmePaymentsWorktimeReq, opts ...grpc.CallOption) (*SmePaymentsWorktimeResp, error)
	// CreatePayment создание платежа
	CreatePayment(ctx context.Context, in *CreatePaymentReq, opts ...grpc.CallOption) (*CreatePaymentResp, error)
	// SmePaymentsGetPaymentOrder получение ссылки на платежное поручение
	SmePaymentsGetPaymentOrder(ctx context.Context, in *SmePaymentsGetPaymentOrderReq, opts ...grpc.CallOption) (*SmePaymentsGetPaymentOrderResp, error)
	// SmePaymentsGetPaymentOrderByTrNumber получение ссылки на платежное поручение через номер транзакции
	SmePaymentsGetPaymentOrderByTrNumber(ctx context.Context, in *SmePaymentsGetPaymentOrderByTrNumberReq, opts ...grpc.CallOption) (*SmePaymentsGetPaymentOrderByTrNumberResp, error)
	// GetEmployeeList получение списка сотрудников ИП
	GetEmployeeList(ctx context.Context, in *GetEmployeeListReq, opts ...grpc.CallOption) (*GetEmployeeListResp, error)
	// DeleteEmployee удаление сотрудника из списка сотрудников ИП
	DeleteEmployee(ctx context.Context, in *DeleteEmployeeReq, opts ...grpc.CallOption) (*DeleteEmployeeResp, error)
	// CreateEmployee создание нового сотрудника из списка сотрудников ИП
	CreateEmployee(ctx context.Context, in *CreateEmployeeReq, opts ...grpc.CallOption) (*EmployeeInfo, error)
	// UpdateEmployee редактирование существующего сотрудника из списка сотрудников ИП
	UpdateEmployee(ctx context.Context, in *UpdateEmployeeReq, opts ...grpc.CallOption) (*EmployeeInfo, error)
}

type paymentssmeClient struct {
	cc grpc.ClientConnInterface
}

func NewPaymentssmeClient(cc grpc.ClientConnInterface) PaymentssmeClient {
	return &paymentssmeClient{cc}
}

func (c *paymentssmeClient) HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResp)
	err := c.cc.Invoke(ctx, Paymentssme_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentssmeClient) SmePaymentsClient(ctx context.Context, in *SmePaymentsClientReq, opts ...grpc.CallOption) (*SmePaymentsClientResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SmePaymentsClientResp)
	err := c.cc.Invoke(ctx, Paymentssme_SmePaymentsClient_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentssmeClient) ConfirmPaymentSme(ctx context.Context, in *ConfirmPaymentSmeReq, opts ...grpc.CallOption) (*ConfirmPaymentSmeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConfirmPaymentSmeResp)
	err := c.cc.Invoke(ctx, Paymentssme_ConfirmPaymentSme_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentssmeClient) SmePaymentsCreateOtp(ctx context.Context, in *SmePaymentsCreateOtpReq, opts ...grpc.CallOption) (*SmePaymentsCreateOtpResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SmePaymentsCreateOtpResp)
	err := c.cc.Invoke(ctx, Paymentssme_SmePaymentsCreateOtp_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentssmeClient) SmePaymentsOtpResend(ctx context.Context, in *SmePaymentsOtpResendReq, opts ...grpc.CallOption) (*SmePaymentsOtpResendResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SmePaymentsOtpResendResp)
	err := c.cc.Invoke(ctx, Paymentssme_SmePaymentsOtpResend_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentssmeClient) SmePaymentsOtpValidate(ctx context.Context, in *SmePaymentsOtpValidateReq, opts ...grpc.CallOption) (*SmePaymentsOtpValidateResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SmePaymentsOtpValidateResp)
	err := c.cc.Invoke(ctx, Paymentssme_SmePaymentsOtpValidate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentssmeClient) GetKbeKodList(ctx context.Context, in *GetKbeKodListReq, opts ...grpc.CallOption) (*GetKbeKodListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetKbeKodListResp)
	err := c.cc.Invoke(ctx, Paymentssme_GetKbeKodList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentssmeClient) GetKnpList(ctx context.Context, in *GetKnpListReq, opts ...grpc.CallOption) (*GetKnpListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetKnpListResp)
	err := c.cc.Invoke(ctx, Paymentssme_GetKnpList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentssmeClient) GetBankList(ctx context.Context, in *GetBankListReq, opts ...grpc.CallOption) (*GetBankListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBankListResp)
	err := c.cc.Invoke(ctx, Paymentssme_GetBankList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentssmeClient) GetCountryList(ctx context.Context, in *GetCountryListReq, opts ...grpc.CallOption) (*GetCountryListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCountryListResp)
	err := c.cc.Invoke(ctx, Paymentssme_GetCountryList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentssmeClient) GetKbkList(ctx context.Context, in *GetKbkListReq, opts ...grpc.CallOption) (*GetKbkListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetKbkListResp)
	err := c.cc.Invoke(ctx, Paymentssme_GetKbkList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentssmeClient) GetTaxAuthorityList(ctx context.Context, in *GetTaxAuthorityListReq, opts ...grpc.CallOption) (*GetTaxAuthorityListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTaxAuthorityListResp)
	err := c.cc.Invoke(ctx, Paymentssme_GetTaxAuthorityList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentssmeClient) SmePaymentsWorktime(ctx context.Context, in *SmePaymentsWorktimeReq, opts ...grpc.CallOption) (*SmePaymentsWorktimeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SmePaymentsWorktimeResp)
	err := c.cc.Invoke(ctx, Paymentssme_SmePaymentsWorktime_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentssmeClient) CreatePayment(ctx context.Context, in *CreatePaymentReq, opts ...grpc.CallOption) (*CreatePaymentResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreatePaymentResp)
	err := c.cc.Invoke(ctx, Paymentssme_CreatePayment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentssmeClient) SmePaymentsGetPaymentOrder(ctx context.Context, in *SmePaymentsGetPaymentOrderReq, opts ...grpc.CallOption) (*SmePaymentsGetPaymentOrderResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SmePaymentsGetPaymentOrderResp)
	err := c.cc.Invoke(ctx, Paymentssme_SmePaymentsGetPaymentOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentssmeClient) SmePaymentsGetPaymentOrderByTrNumber(ctx context.Context, in *SmePaymentsGetPaymentOrderByTrNumberReq, opts ...grpc.CallOption) (*SmePaymentsGetPaymentOrderByTrNumberResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SmePaymentsGetPaymentOrderByTrNumberResp)
	err := c.cc.Invoke(ctx, Paymentssme_SmePaymentsGetPaymentOrderByTrNumber_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentssmeClient) GetEmployeeList(ctx context.Context, in *GetEmployeeListReq, opts ...grpc.CallOption) (*GetEmployeeListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetEmployeeListResp)
	err := c.cc.Invoke(ctx, Paymentssme_GetEmployeeList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentssmeClient) DeleteEmployee(ctx context.Context, in *DeleteEmployeeReq, opts ...grpc.CallOption) (*DeleteEmployeeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteEmployeeResp)
	err := c.cc.Invoke(ctx, Paymentssme_DeleteEmployee_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentssmeClient) CreateEmployee(ctx context.Context, in *CreateEmployeeReq, opts ...grpc.CallOption) (*EmployeeInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EmployeeInfo)
	err := c.cc.Invoke(ctx, Paymentssme_CreateEmployee_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentssmeClient) UpdateEmployee(ctx context.Context, in *UpdateEmployeeReq, opts ...grpc.CallOption) (*EmployeeInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EmployeeInfo)
	err := c.cc.Invoke(ctx, Paymentssme_UpdateEmployee_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PaymentssmeServer is the server API for Paymentssme service.
// All implementations must embed UnimplementedPaymentssmeServer
// for forward compatibility.
type PaymentssmeServer interface {
	HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error)
	SmePaymentsClient(context.Context, *SmePaymentsClientReq) (*SmePaymentsClientResp, error)
	ConfirmPaymentSme(context.Context, *ConfirmPaymentSmeReq) (*ConfirmPaymentSmeResp, error)
	SmePaymentsCreateOtp(context.Context, *SmePaymentsCreateOtpReq) (*SmePaymentsCreateOtpResp, error)
	SmePaymentsOtpResend(context.Context, *SmePaymentsOtpResendReq) (*SmePaymentsOtpResendResp, error)
	SmePaymentsOtpValidate(context.Context, *SmePaymentsOtpValidateReq) (*SmePaymentsOtpValidateResp, error)
	// GetKbeKodList получение списка Кбе/КОд кодов
	GetKbeKodList(context.Context, *GetKbeKodListReq) (*GetKbeKodListResp, error)
	// GetKnpList получение списка КНП
	GetKnpList(context.Context, *GetKnpListReq) (*GetKnpListResp, error)
	// GetBankList получение списка БВУ
	GetBankList(context.Context, *GetBankListReq) (*GetBankListResp, error)
	// GetCountryList получение списка стран
	GetCountryList(context.Context, *GetCountryListReq) (*GetCountryListResp, error)
	// GetKbkList получение списка КБК
	GetKbkList(context.Context, *GetKbkListReq) (*GetKbkListResp, error)
	// GetTaxAuthorityList получение списка УГД
	GetTaxAuthorityList(context.Context, *GetTaxAuthorityListReq) (*GetTaxAuthorityListResp, error)
	SmePaymentsWorktime(context.Context, *SmePaymentsWorktimeReq) (*SmePaymentsWorktimeResp, error)
	// CreatePayment создание платежа
	CreatePayment(context.Context, *CreatePaymentReq) (*CreatePaymentResp, error)
	// SmePaymentsGetPaymentOrder получение ссылки на платежное поручение
	SmePaymentsGetPaymentOrder(context.Context, *SmePaymentsGetPaymentOrderReq) (*SmePaymentsGetPaymentOrderResp, error)
	// SmePaymentsGetPaymentOrderByTrNumber получение ссылки на платежное поручение через номер транзакции
	SmePaymentsGetPaymentOrderByTrNumber(context.Context, *SmePaymentsGetPaymentOrderByTrNumberReq) (*SmePaymentsGetPaymentOrderByTrNumberResp, error)
	// GetEmployeeList получение списка сотрудников ИП
	GetEmployeeList(context.Context, *GetEmployeeListReq) (*GetEmployeeListResp, error)
	// DeleteEmployee удаление сотрудника из списка сотрудников ИП
	DeleteEmployee(context.Context, *DeleteEmployeeReq) (*DeleteEmployeeResp, error)
	// CreateEmployee создание нового сотрудника из списка сотрудников ИП
	CreateEmployee(context.Context, *CreateEmployeeReq) (*EmployeeInfo, error)
	// UpdateEmployee редактирование существующего сотрудника из списка сотрудников ИП
	UpdateEmployee(context.Context, *UpdateEmployeeReq) (*EmployeeInfo, error)
	mustEmbedUnimplementedPaymentssmeServer()
}

// UnimplementedPaymentssmeServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPaymentssmeServer struct{}

func (UnimplementedPaymentssmeServer) HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedPaymentssmeServer) SmePaymentsClient(context.Context, *SmePaymentsClientReq) (*SmePaymentsClientResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SmePaymentsClient not implemented")
}
func (UnimplementedPaymentssmeServer) ConfirmPaymentSme(context.Context, *ConfirmPaymentSmeReq) (*ConfirmPaymentSmeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConfirmPaymentSme not implemented")
}
func (UnimplementedPaymentssmeServer) SmePaymentsCreateOtp(context.Context, *SmePaymentsCreateOtpReq) (*SmePaymentsCreateOtpResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SmePaymentsCreateOtp not implemented")
}
func (UnimplementedPaymentssmeServer) SmePaymentsOtpResend(context.Context, *SmePaymentsOtpResendReq) (*SmePaymentsOtpResendResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SmePaymentsOtpResend not implemented")
}
func (UnimplementedPaymentssmeServer) SmePaymentsOtpValidate(context.Context, *SmePaymentsOtpValidateReq) (*SmePaymentsOtpValidateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SmePaymentsOtpValidate not implemented")
}
func (UnimplementedPaymentssmeServer) GetKbeKodList(context.Context, *GetKbeKodListReq) (*GetKbeKodListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKbeKodList not implemented")
}
func (UnimplementedPaymentssmeServer) GetKnpList(context.Context, *GetKnpListReq) (*GetKnpListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKnpList not implemented")
}
func (UnimplementedPaymentssmeServer) GetBankList(context.Context, *GetBankListReq) (*GetBankListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBankList not implemented")
}
func (UnimplementedPaymentssmeServer) GetCountryList(context.Context, *GetCountryListReq) (*GetCountryListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCountryList not implemented")
}
func (UnimplementedPaymentssmeServer) GetKbkList(context.Context, *GetKbkListReq) (*GetKbkListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKbkList not implemented")
}
func (UnimplementedPaymentssmeServer) GetTaxAuthorityList(context.Context, *GetTaxAuthorityListReq) (*GetTaxAuthorityListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTaxAuthorityList not implemented")
}
func (UnimplementedPaymentssmeServer) SmePaymentsWorktime(context.Context, *SmePaymentsWorktimeReq) (*SmePaymentsWorktimeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SmePaymentsWorktime not implemented")
}
func (UnimplementedPaymentssmeServer) CreatePayment(context.Context, *CreatePaymentReq) (*CreatePaymentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePayment not implemented")
}
func (UnimplementedPaymentssmeServer) SmePaymentsGetPaymentOrder(context.Context, *SmePaymentsGetPaymentOrderReq) (*SmePaymentsGetPaymentOrderResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SmePaymentsGetPaymentOrder not implemented")
}
func (UnimplementedPaymentssmeServer) SmePaymentsGetPaymentOrderByTrNumber(context.Context, *SmePaymentsGetPaymentOrderByTrNumberReq) (*SmePaymentsGetPaymentOrderByTrNumberResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SmePaymentsGetPaymentOrderByTrNumber not implemented")
}
func (UnimplementedPaymentssmeServer) GetEmployeeList(context.Context, *GetEmployeeListReq) (*GetEmployeeListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEmployeeList not implemented")
}
func (UnimplementedPaymentssmeServer) DeleteEmployee(context.Context, *DeleteEmployeeReq) (*DeleteEmployeeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteEmployee not implemented")
}
func (UnimplementedPaymentssmeServer) CreateEmployee(context.Context, *CreateEmployeeReq) (*EmployeeInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateEmployee not implemented")
}
func (UnimplementedPaymentssmeServer) UpdateEmployee(context.Context, *UpdateEmployeeReq) (*EmployeeInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEmployee not implemented")
}
func (UnimplementedPaymentssmeServer) mustEmbedUnimplementedPaymentssmeServer() {}
func (UnimplementedPaymentssmeServer) testEmbeddedByValue()                     {}

// UnsafePaymentssmeServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PaymentssmeServer will
// result in compilation errors.
type UnsafePaymentssmeServer interface {
	mustEmbedUnimplementedPaymentssmeServer()
}

func RegisterPaymentssmeServer(s grpc.ServiceRegistrar, srv PaymentssmeServer) {
	// If the following call pancis, it indicates UnimplementedPaymentssmeServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Paymentssme_ServiceDesc, srv)
}

func _Paymentssme_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentssmeServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Paymentssme_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentssmeServer).HealthCheck(ctx, req.(*HealthCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Paymentssme_SmePaymentsClient_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SmePaymentsClientReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentssmeServer).SmePaymentsClient(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Paymentssme_SmePaymentsClient_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentssmeServer).SmePaymentsClient(ctx, req.(*SmePaymentsClientReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Paymentssme_ConfirmPaymentSme_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmPaymentSmeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentssmeServer).ConfirmPaymentSme(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Paymentssme_ConfirmPaymentSme_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentssmeServer).ConfirmPaymentSme(ctx, req.(*ConfirmPaymentSmeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Paymentssme_SmePaymentsCreateOtp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SmePaymentsCreateOtpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentssmeServer).SmePaymentsCreateOtp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Paymentssme_SmePaymentsCreateOtp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentssmeServer).SmePaymentsCreateOtp(ctx, req.(*SmePaymentsCreateOtpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Paymentssme_SmePaymentsOtpResend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SmePaymentsOtpResendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentssmeServer).SmePaymentsOtpResend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Paymentssme_SmePaymentsOtpResend_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentssmeServer).SmePaymentsOtpResend(ctx, req.(*SmePaymentsOtpResendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Paymentssme_SmePaymentsOtpValidate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SmePaymentsOtpValidateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentssmeServer).SmePaymentsOtpValidate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Paymentssme_SmePaymentsOtpValidate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentssmeServer).SmePaymentsOtpValidate(ctx, req.(*SmePaymentsOtpValidateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Paymentssme_GetKbeKodList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKbeKodListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentssmeServer).GetKbeKodList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Paymentssme_GetKbeKodList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentssmeServer).GetKbeKodList(ctx, req.(*GetKbeKodListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Paymentssme_GetKnpList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKnpListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentssmeServer).GetKnpList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Paymentssme_GetKnpList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentssmeServer).GetKnpList(ctx, req.(*GetKnpListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Paymentssme_GetBankList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBankListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentssmeServer).GetBankList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Paymentssme_GetBankList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentssmeServer).GetBankList(ctx, req.(*GetBankListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Paymentssme_GetCountryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCountryListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentssmeServer).GetCountryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Paymentssme_GetCountryList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentssmeServer).GetCountryList(ctx, req.(*GetCountryListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Paymentssme_GetKbkList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKbkListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentssmeServer).GetKbkList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Paymentssme_GetKbkList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentssmeServer).GetKbkList(ctx, req.(*GetKbkListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Paymentssme_GetTaxAuthorityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTaxAuthorityListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentssmeServer).GetTaxAuthorityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Paymentssme_GetTaxAuthorityList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentssmeServer).GetTaxAuthorityList(ctx, req.(*GetTaxAuthorityListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Paymentssme_SmePaymentsWorktime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SmePaymentsWorktimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentssmeServer).SmePaymentsWorktime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Paymentssme_SmePaymentsWorktime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentssmeServer).SmePaymentsWorktime(ctx, req.(*SmePaymentsWorktimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Paymentssme_CreatePayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePaymentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentssmeServer).CreatePayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Paymentssme_CreatePayment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentssmeServer).CreatePayment(ctx, req.(*CreatePaymentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Paymentssme_SmePaymentsGetPaymentOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SmePaymentsGetPaymentOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentssmeServer).SmePaymentsGetPaymentOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Paymentssme_SmePaymentsGetPaymentOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentssmeServer).SmePaymentsGetPaymentOrder(ctx, req.(*SmePaymentsGetPaymentOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Paymentssme_SmePaymentsGetPaymentOrderByTrNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SmePaymentsGetPaymentOrderByTrNumberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentssmeServer).SmePaymentsGetPaymentOrderByTrNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Paymentssme_SmePaymentsGetPaymentOrderByTrNumber_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentssmeServer).SmePaymentsGetPaymentOrderByTrNumber(ctx, req.(*SmePaymentsGetPaymentOrderByTrNumberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Paymentssme_GetEmployeeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEmployeeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentssmeServer).GetEmployeeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Paymentssme_GetEmployeeList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentssmeServer).GetEmployeeList(ctx, req.(*GetEmployeeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Paymentssme_DeleteEmployee_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteEmployeeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentssmeServer).DeleteEmployee(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Paymentssme_DeleteEmployee_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentssmeServer).DeleteEmployee(ctx, req.(*DeleteEmployeeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Paymentssme_CreateEmployee_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateEmployeeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentssmeServer).CreateEmployee(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Paymentssme_CreateEmployee_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentssmeServer).CreateEmployee(ctx, req.(*CreateEmployeeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Paymentssme_UpdateEmployee_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEmployeeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentssmeServer).UpdateEmployee(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Paymentssme_UpdateEmployee_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentssmeServer).UpdateEmployee(ctx, req.(*UpdateEmployeeReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Paymentssme_ServiceDesc is the grpc.ServiceDesc for Paymentssme service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Paymentssme_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "paymentsSme.Paymentssme",
	HandlerType: (*PaymentssmeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HealthCheck",
			Handler:    _Paymentssme_HealthCheck_Handler,
		},
		{
			MethodName: "SmePaymentsClient",
			Handler:    _Paymentssme_SmePaymentsClient_Handler,
		},
		{
			MethodName: "ConfirmPaymentSme",
			Handler:    _Paymentssme_ConfirmPaymentSme_Handler,
		},
		{
			MethodName: "SmePaymentsCreateOtp",
			Handler:    _Paymentssme_SmePaymentsCreateOtp_Handler,
		},
		{
			MethodName: "SmePaymentsOtpResend",
			Handler:    _Paymentssme_SmePaymentsOtpResend_Handler,
		},
		{
			MethodName: "SmePaymentsOtpValidate",
			Handler:    _Paymentssme_SmePaymentsOtpValidate_Handler,
		},
		{
			MethodName: "GetKbeKodList",
			Handler:    _Paymentssme_GetKbeKodList_Handler,
		},
		{
			MethodName: "GetKnpList",
			Handler:    _Paymentssme_GetKnpList_Handler,
		},
		{
			MethodName: "GetBankList",
			Handler:    _Paymentssme_GetBankList_Handler,
		},
		{
			MethodName: "GetCountryList",
			Handler:    _Paymentssme_GetCountryList_Handler,
		},
		{
			MethodName: "GetKbkList",
			Handler:    _Paymentssme_GetKbkList_Handler,
		},
		{
			MethodName: "GetTaxAuthorityList",
			Handler:    _Paymentssme_GetTaxAuthorityList_Handler,
		},
		{
			MethodName: "SmePaymentsWorktime",
			Handler:    _Paymentssme_SmePaymentsWorktime_Handler,
		},
		{
			MethodName: "CreatePayment",
			Handler:    _Paymentssme_CreatePayment_Handler,
		},
		{
			MethodName: "SmePaymentsGetPaymentOrder",
			Handler:    _Paymentssme_SmePaymentsGetPaymentOrder_Handler,
		},
		{
			MethodName: "SmePaymentsGetPaymentOrderByTrNumber",
			Handler:    _Paymentssme_SmePaymentsGetPaymentOrderByTrNumber_Handler,
		},
		{
			MethodName: "GetEmployeeList",
			Handler:    _Paymentssme_GetEmployeeList_Handler,
		},
		{
			MethodName: "DeleteEmployee",
			Handler:    _Paymentssme_DeleteEmployee_Handler,
		},
		{
			MethodName: "CreateEmployee",
			Handler:    _Paymentssme_CreateEmployee_Handler,
		},
		{
			MethodName: "UpdateEmployee",
			Handler:    _Paymentssme_UpdateEmployee_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "specs/proto/payments-sme/payments-sme.proto",
}
