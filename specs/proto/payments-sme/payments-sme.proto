syntax = "proto3";

package paymentsSme;

option go_package = "specs/proto/payments-sme";

service Paymentssme {
  rpc HealthCheck (HealthCheckReq) returns (HealthCheckResp); // Сгенерированный метод, не изменяйте его.
  rpc SmePaymentsClient(SmePaymentsClientReq) returns (SmePaymentsClientResp);
  rpc ConfirmPaymentSme(ConfirmPaymentSmeReq) returns (ConfirmPaymentSmeResp);
  rpc SmePaymentsCreateOtp (SmePaymentsCreateOtpReq) returns (SmePaymentsCreateOtpResp);
  rpc SmePaymentsOtpResend (SmePaymentsOtpResendReq) returns (SmePaymentsOtpResendResp);
  rpc SmePaymentsOtpValidate (SmePaymentsOtpValidateReq) returns (SmePaymentsOtpValidateResp);
  // GetKbeKodList получение списка Кбе/КОд кодов
  rpc GetKbeKodList(GetKbeKodListReq) returns (GetKbeKodListResp);
  // GetKnpList получение списка КНП
  rpc GetKnpList(GetKnpListReq) returns (GetKnpListResp);
  // GetBankList получение списка БВУ
  rpc GetBankList(GetBankListReq) returns (GetBankListResp);
  // GetCountryList получение списка стран
  rpc GetCountryList(GetCountryListReq) returns (GetCountryListResp);
  // GetKbkList получение списка КБК
  rpc GetKbkList(GetKbkListReq) returns (GetKbkListResp);
  // GetTaxAuthorityList получение списка УГД
  rpc GetTaxAuthorityList(GetTaxAuthorityListReq) returns (GetTaxAuthorityListResp);
  rpc SmePaymentsWorktime(SmePaymentsWorktimeReq) returns (SmePaymentsWorktimeResp);
  // CreatePayment создание платежа
  rpc CreatePayment(CreatePaymentReq) returns (CreatePaymentResp);
  // SmePaymentsGetPaymentOrder получение ссылки на платежное поручение
  rpc SmePaymentsGetPaymentOrder(SmePaymentsGetPaymentOrderReq) returns (SmePaymentsGetPaymentOrderResp);
  // SmePaymentsGetPaymentOrderByTrNumber получение ссылки на платежное поручение через номер транзакции
  rpc SmePaymentsGetPaymentOrderByTrNumber(SmePaymentsGetPaymentOrderByTrNumberReq) returns (SmePaymentsGetPaymentOrderByTrNumberResp);
  // GetEmployeeList получение списка сотрудников ИП
  rpc GetEmployeeList(GetEmployeeListReq) returns (GetEmployeeListResp);
  // DeleteEmployee удаление сотрудника из списка сотрудников ИП
  rpc DeleteEmployee(DeleteEmployeeReq) returns (DeleteEmployeeResp);
  // CreateEmployee создание нового сотрудника из списка сотрудников ИП
  rpc CreateEmployee(CreateEmployeeReq) returns (EmployeeInfo);
  // UpdateEmployee редактирование существующего сотрудника из списка сотрудников ИП
  rpc UpdateEmployee(UpdateEmployeeReq) returns (EmployeeInfo);

}

message HealthCheckReq {}

message HealthCheckResp {
  bool status = 1;
}

message SmePaymentsClientReq {}

message SmePaymentsClientResp {
  string iin = 1;
  string name = 2;
  string surname = 3;
  string patronymic = 4;
  string birthdate = 5;
  string enterpriseName = 6;
  string enterpriseKATOCode = 7;
  string enterpriseKATOId = 8;
}

message ConfirmPaymentSmeReq{
  string transactionID = 1;
}

message ConfirmPaymentSmeResp{
  string status = 1;
  string reasonCode = 2;
  string reason = 3;
  bool otpNeeded = 4;
}


message SmePaymentsCreateOtpReq {
  DeviceInfo deviceInfo = 1;
}

message SmePaymentsCreateOtpResp {
  string attemptId = 1;
  string codeTtl = 2;
  string codeChecksLeft = 3;
  string attemptsLeft = 4;
  string attemptsTimeout = 5;
  string newAttemptDelay = 6;
  string error = 7;
}

message DeviceInfo {
  string appVersion = 1;
  string deviceModel = 2;
  string installationId = 3;
  string systemType = 4;
  string systemVersion = 5;
}

message SmePaymentsOtpResendReq {
  string attemptId = 1;
}

message SmePaymentsOtpResendResp {
  string attemptId = 1;
  string codeTtl = 2;
  string codeChecksLeft = 3;
  string attemptsLeft = 4;
  string attemptsTimeout = 5;
  string newAttemptDelay = 6;
  string error = 7;
}

message SmePaymentsOtpValidateReq {
  string attemptId = 1;
  string code = 2;
  string transactionID = 3;
}

message SmePaymentsOtpValidateResp {
  bool success = 1;
  string error = 2;
}


// KbeKodFilter фильтр для получения списка Кбе/КОд кодов
message KbeKodFilter {
  // codes список кодов Кбе/КОд
  repeated string codes = 1;
  // residency признак резидента
  optional bool residency = 2;
}

// GetKbeKodListReq запрос на получение списка Кбе/КОд кодов
message GetKbeKodListReq {
  // filter фильтр для получения списка Кбе/КОд кодов
  optional KbeKodFilter filter = 1;
}

// KbeKod тело Кбе/КОд кода
message KbeKod {
  // value код Кбе/КОд
  string value = 1;
  // name наименование Кбе/КОд на языке локализации
  string name = 2;
  // residency признак резидента
  bool residency = 3;
}

// GetKbeKodItem элемент списка Кбе/КОд кодов
message GetKbeKodItem {
  // code Кбе/КОд код
  KbeKod code = 1;
}

// GetKbeKodListResp ответ на запрос на получение списка Кбе/КОд кодов
message GetKbeKodListResp {
  // codes список Кбе/КОд кодов
  repeated GetKbeKodItem codes = 1;
}

// KnpFilter фильтр для получения списка КНП
message KnpFilter {
  // codes список кодов КНП
  repeated string codes = 1;
  // groups список групп КНП
  repeated string groups = 2;
}

// GetKnpListReq запрос на получение списка КНП
message GetKnpListReq {
  // filter фильтр для получения списка КНП
  optional KnpFilter filter = 1;
}

// Knp тело кода КНП
message Knp {
  // value код КНП
  string value = 1;
  // name наименование КНП на языке локализации
  string name = 2;
}

// GetKnpItem элемент списка КНП
message GetKnpItem {
  // code код КНП
  Knp code = 1;
}

// GetKnpListResp ответ на запрос на получение списка КНП
message GetKnpListResp {
  // codes список КНП
  repeated GetKnpItem codes = 1;
}

// BankFilter фильтр для получения списка банков
message BankFilter {
  // bics список БИКов банков
  repeated string bics = 1;
}

// GetBankListReq запрос на получение списка банков
message GetBankListReq {
  // filter фильтр для получения списка банков
  optional BankFilter filter = 1;
}

// Bank тело банка
message Bank {
  // code код банка
  string code = 1;
  // bic БИК банка
  string bic = 2;
  // name наименование банка на языке локализации
  string name = 3;
}

// GetBankItem элемент списка банков
message GetBankItem {
  // bank банк
  Bank bank = 1;
}

// GetBankListResp ответ на запрос на получение списка банков
message GetBankListResp {
  // banks список банков
  repeated GetBankItem banks = 1;
}

// GetCountryListReq запрос на получение списка стран
message GetCountryListReq {
  // filter фильтр для получения списка стран
  optional CountryFilter filter = 1;
}

// CountryFilter фильтр для получения списка стран
message CountryFilter {
  // codes список кодов стран
  repeated string codes = 1;
}

// Country тело страны
message Country {
  // code код страны
  string code = 1;
  // name наименование страны на языке локализации
  string name = 2;
  // description описание страны
  string description = 3;
}

// GetCountryListResp ответ на запрос на получение списка стран
message GetCountryListResp {
  // countries список стран
  repeated Country countries = 1;
}

// GetKbkListReq запрос на получение списка КБК
message GetKbkListReq {
  // filter фильтр для получения списка КБК
  optional KbkFilter filter = 1;
}

// KbkFilter фильтр для получения списка КБК
message KbkFilter {
  // codes список кодов КБК
  repeated string codes = 1;
}

// GetKbkListResp ответ на запрос на получение списка КБК
message GetKbkListResp {
  // codes список КБК
  repeated GetKbkItem codes = 1;
}

// GetKbkItem элемент списка КБК
message GetKbkItem {
  // code код КБК
  Kbk code = 1;
}

// Kbk тело КБК
message Kbk {
  // value значение кода КБК
  string value = 1;
  // name описание кода КБК на языке локализации
  string name = 2;
}

// GetTaxAuthorityListReq запрос на получение списка УГД
message GetTaxAuthorityListReq {
  // filter фильтр для получения списка УГД
  optional TaxAuthorityFilter filter = 1;
}

// TaxAuthorityFilter фильтр для получения списка УГД
message TaxAuthorityFilter {
  // codes список кодов УГД
  repeated string regions = 1;
}

// GetTaxAuthorityListResp ответ на запрос на получение списка УГД
message GetTaxAuthorityListResp {
  // codes список УГД
  repeated GetTaxAuthorityItem ugds = 1;
}

// GetTaxAuthorityItem элемент списка УГД
message GetTaxAuthorityItem {
  // code код УГД
  TaxAuthority ugd = 1;
}

// TaxAuthority тело УГД
message TaxAuthority {
  // name наименование УГД на языке локализации
  string name = 1;
  // bin БИН УГД
  string bin = 2;
  // regionCode код региона
  string regionCode = 3;
  // Название региона
  string regionName = 4;
  // КНО
  string code = 5;
}

message SmePaymentsWorktimeReq {}

message SmePaymentsWorktimeResp {
  string date = 1;
  bool isDateOperational = 2;
  string nextOperationalDate = 3;
  bool isEndOfWorkTime = 4;
}

// CreatePaymentReq запрос на создание платежа
message CreatePaymentReq {
  // idempotencyKey уникальный идентификатор запроса для избежания дублирования платежа
  string idempotencyKey = 1;
  // paymentCode тип платежа
  string paymentCode = 2;
  // paymentData данные платежа
  PaymentData paymentData = 3;
}

// CreatePaymentResp ответ на запрос на создание платежа
message CreatePaymentResp {
  // transactionID уникальный идентификатор созданной транзакции в БД Платежей и Переводов. Нужен для дальнейшей работы с транзакцией. Опциональное поле - может быть пустым при ошибках валидации.
  string transactionID = 1;
  // status статус платежа
  string status = 2;
  // reasonCode код состояния платежа
  string reasonCode = 3;
  // reason описание причины состояния платежа
  string reason = 4;
  // otpNeeded необходимость подтвердить платёж OTP
  bool otpNeeded = 5;
}

// OPVPaymentData структура данных для платежа ОПВ(Обязательные Платежи Взносы)
message PaymentData {
  // paymentPeriod период платежа
  string paymentPeriod = 1;
  // purposeCode код назначения платежа (код КНП)
  string purposeCode = 2;
  // purposeDetails детали назначения платежа
  string purposeDetails = 3;
  // amount сумма платежа
  string amount = 4;
  // payerAccount номер счета плательщика (Например: **********************)
  string payerAccount = 5;
  // employees список сотрудников
  repeated EmployeeItem employees = 6;
  // signatoryA Подпись ответственного лица (ФИО)
  string signatoryA = 7;
  // beneficiaryBINIIN БИН/ИИН получателя платежа
  string beneficiaryBINIIN = 8;
  // beneficiaryName Наименование получателя платежа
  string beneficiaryName = 9;
  // realBeneficiaryName ФИО фактического получателя
  string realBeneficiaryName = 10;
  // realBeneficiaryBINIIN ИИН фактического получателя
  string realBeneficiaryBINIIN = 11;
  // realBeneficiaryCountry Страна резидентства фактического получателя
  string realBeneficiaryCountry = 12;
  // kbk Код Бюджетной Классификации
  string kbk = 13;
}

// EmployeeItem структура данных для сотрудника
message EmployeeItem {
  // name имя сотрудника
  string name = 1;
  // middleName отчество сотрудника
  optional string middleName = 2;
  // lastName фамилия сотрудника
  string lastName = 3;
  // iin ИИН сотрудника
  string iin = 4;
  // birthday дата рождения сотрудника
  string birthday = 5;
  // country страна сотрудника
  string country = 6;
  // amount сумма платежа для сотрудника
  string amount = 7;
  // valuePeriod период платежа для сотрудника
  string valuePeriod = 8;
  // id идентификатор записи о сотруднике
  string id = 9;
  // displayOrder Порядок отображения в списке
  int32 displayOrder = 10;
  // employer_iin БИН/ИИН нанимателя
  string employer_iin = 11;
}

message SmePaymentsGetPaymentOrderReq {
  string transactionID = 1;
}

message SmePaymentsGetPaymentOrderByTrNumberReq {
  string transactionNumber = 1;
}

message SmePaymentsGetPaymentOrderByTrNumberResp {
  string id = 1;
  string title = 2;
  string link = 3;
  int32 version = 4;
  string transactionStatus = 5;
}

message SmePaymentsGetPaymentOrderResp {
  string title = 1;
  string link = 2;
  string version = 3;
}

message GetEmployeeListReq {
}

message GetEmployeeListResp {
  repeated EmployeeInfo employees = 1;
}

message DeleteEmployeeReq {
  string id = 1;
}

message DeleteEmployeeResp {
  repeated EmployeeInfo employees = 1;
}

message CreateEmployeeReq {
  // name имя сотрудника
  string name = 1;
  // middleName отчество сотрудника
  optional string middleName = 2;
  // lastName фамилия сотрудника
  string lastName = 3;
  // iin ИИН сотрудника
  string iin = 4;
  // birthday дата рождения сотрудника
  string birthday = 5;
  // country страна сотрудника
  string country = 6;
  // displayOrder Порядок отображения в списке
  int32 displayOrder = 7;
  // employer_iin БИН/ИИН нанимателя
  string employer_iin = 8;
}

message UpdateEmployeeReq {
  // id идентификатор записи о сотруднике
  string id = 1;
  // name имя сотрудника
  string name = 2;
  // middleName отчество сотрудника
  optional string middleName = 3;
  // lastName фамилия сотрудника
  string lastName = 4;
  // iin ИИН сотрудника
  string iin = 5;
  // employer_iin БИН/ИИН нанимателя
  string employer_iin = 6;
  // birthday дата рождения сотрудника
  string birthday = 7;
  // country страна сотрудника
  string country = 8;
  // displayOrder Порядок отображения в списке
  int32 displayOrder = 9;
}

message EmployeeInfo {
  // id идентификатор записи о сотруднике
  string id = 1;
  // name имя сотрудника
  string name = 2;
  // middleName отчество сотрудника
  optional string middleName = 3;
  // lastName фамилия сотрудника
  string lastName = 4;
  // iin ИИН сотрудника
  string iin = 5;
  // employer_iin БИН/ИИН нанимателя
  string employer_iin = 6;
  // birthday дата рождения сотрудника
  string birthday = 7;
  // country страна сотрудника
  string country = 8;
  // displayOrder Порядок отображения в списке
  int32 displayOrder = 9;
}

