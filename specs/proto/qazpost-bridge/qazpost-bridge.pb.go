// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v6.32.1
// source: specs/proto/qazpost-bridge/qazpost-bridge.proto

package qazpost_bridge

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// --- Health Check ---
type HealthCheckReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckReq) Reset() {
	*x = HealthCheckReq{}
	mi := &file_specs_proto_qazpost_bridge_qazpost_bridge_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckReq) ProtoMessage() {}

func (x *HealthCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_qazpost_bridge_qazpost_bridge_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckReq.ProtoReflect.Descriptor instead.
func (*HealthCheckReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_qazpost_bridge_qazpost_bridge_proto_rawDescGZIP(), []int{0}
}

type HealthCheckResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckResp) Reset() {
	*x = HealthCheckResp{}
	mi := &file_specs_proto_qazpost_bridge_qazpost_bridge_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResp) ProtoMessage() {}

func (x *HealthCheckResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_qazpost_bridge_qazpost_bridge_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResp.ProtoReflect.Descriptor instead.
func (*HealthCheckResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_qazpost_bridge_qazpost_bridge_proto_rawDescGZIP(), []int{1}
}

func (x *HealthCheckResp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type GetOldPostalIndexByAddressRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The address string to search for (e.g., "Астана Орнек 1/1", "область Жамбылская, город Тараз, улица Байкент, 80").
	AddressQuery  string `protobuf:"bytes,1,opt,name=address_query,json=addressQuery,proto3" json:"address_query,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOldPostalIndexByAddressRequest) Reset() {
	*x = GetOldPostalIndexByAddressRequest{}
	mi := &file_specs_proto_qazpost_bridge_qazpost_bridge_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOldPostalIndexByAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOldPostalIndexByAddressRequest) ProtoMessage() {}

func (x *GetOldPostalIndexByAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_qazpost_bridge_qazpost_bridge_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOldPostalIndexByAddressRequest.ProtoReflect.Descriptor instead.
func (*GetOldPostalIndexByAddressRequest) Descriptor() ([]byte, []int) {
	return file_specs_proto_qazpost_bridge_qazpost_bridge_proto_rawDescGZIP(), []int{2}
}

func (x *GetOldPostalIndexByAddressRequest) GetAddressQuery() string {
	if x != nil {
		return x.AddressQuery
	}
	return ""
}

type GetOldPostalIndexByAddressResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The old postal index (SPI) found for the address, if any.
	OldPostalIndex string `protobuf:"bytes,1,opt,name=old_postal_index,json=oldPostalIndex,proto3" json:"old_postal_index,omitempty"`
	// The new postal index found by the initial search (optional, for context).
	NewPostalIndex string `protobuf:"bytes,2,opt,name=new_postal_index,json=newPostalIndex,proto3" json:"new_postal_index,omitempty"`
	// The full address string returned by Qazpost (optional, for context).
	QazpostAddress string `protobuf:"bytes,3,opt,name=qazpost_address,json=qazpostAddress,proto3" json:"qazpost_address,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetOldPostalIndexByAddressResponse) Reset() {
	*x = GetOldPostalIndexByAddressResponse{}
	mi := &file_specs_proto_qazpost_bridge_qazpost_bridge_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOldPostalIndexByAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOldPostalIndexByAddressResponse) ProtoMessage() {}

func (x *GetOldPostalIndexByAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_qazpost_bridge_qazpost_bridge_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOldPostalIndexByAddressResponse.ProtoReflect.Descriptor instead.
func (*GetOldPostalIndexByAddressResponse) Descriptor() ([]byte, []int) {
	return file_specs_proto_qazpost_bridge_qazpost_bridge_proto_rawDescGZIP(), []int{3}
}

func (x *GetOldPostalIndexByAddressResponse) GetOldPostalIndex() string {
	if x != nil {
		return x.OldPostalIndex
	}
	return ""
}

func (x *GetOldPostalIndexByAddressResponse) GetNewPostalIndex() string {
	if x != nil {
		return x.NewPostalIndex
	}
	return ""
}

func (x *GetOldPostalIndexByAddressResponse) GetQazpostAddress() string {
	if x != nil {
		return x.QazpostAddress
	}
	return ""
}

var File_specs_proto_qazpost_bridge_qazpost_bridge_proto protoreflect.FileDescriptor

const file_specs_proto_qazpost_bridge_qazpost_bridge_proto_rawDesc = "" +
	"\n" +
	"/specs/proto/qazpost-bridge/qazpost-bridge.proto\x12\rqazpostBridge\"\x10\n" +
	"\x0eHealthCheckReq\")\n" +
	"\x0fHealthCheckResp\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\"H\n" +
	"!GetOldPostalIndexByAddressRequest\x12#\n" +
	"\raddress_query\x18\x01 \x01(\tR\faddressQuery\"\xa1\x01\n" +
	"\"GetOldPostalIndexByAddressResponse\x12(\n" +
	"\x10old_postal_index\x18\x01 \x01(\tR\x0eoldPostalIndex\x12(\n" +
	"\x10new_postal_index\x18\x02 \x01(\tR\x0enewPostalIndex\x12'\n" +
	"\x0fqazpost_address\x18\x03 \x01(\tR\x0eqazpostAddress2\xe1\x01\n" +
	"\rQazpostbridge\x12L\n" +
	"\vHealthCheck\x12\x1d.qazpostBridge.HealthCheckReq\x1a\x1e.qazpostBridge.HealthCheckResp\x12\x81\x01\n" +
	"\x1aGetOldPostalIndexByAddress\x120.qazpostBridge.GetOldPostalIndexByAddressRequest\x1a1.qazpostBridge.GetOldPostalIndexByAddressResponseB\x1cZ\x1aspecs/proto/qazpost-bridgeb\x06proto3"

var (
	file_specs_proto_qazpost_bridge_qazpost_bridge_proto_rawDescOnce sync.Once
	file_specs_proto_qazpost_bridge_qazpost_bridge_proto_rawDescData []byte
)

func file_specs_proto_qazpost_bridge_qazpost_bridge_proto_rawDescGZIP() []byte {
	file_specs_proto_qazpost_bridge_qazpost_bridge_proto_rawDescOnce.Do(func() {
		file_specs_proto_qazpost_bridge_qazpost_bridge_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_specs_proto_qazpost_bridge_qazpost_bridge_proto_rawDesc), len(file_specs_proto_qazpost_bridge_qazpost_bridge_proto_rawDesc)))
	})
	return file_specs_proto_qazpost_bridge_qazpost_bridge_proto_rawDescData
}

var file_specs_proto_qazpost_bridge_qazpost_bridge_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_specs_proto_qazpost_bridge_qazpost_bridge_proto_goTypes = []any{
	(*HealthCheckReq)(nil),                     // 0: qazpostBridge.HealthCheckReq
	(*HealthCheckResp)(nil),                    // 1: qazpostBridge.HealthCheckResp
	(*GetOldPostalIndexByAddressRequest)(nil),  // 2: qazpostBridge.GetOldPostalIndexByAddressRequest
	(*GetOldPostalIndexByAddressResponse)(nil), // 3: qazpostBridge.GetOldPostalIndexByAddressResponse
}
var file_specs_proto_qazpost_bridge_qazpost_bridge_proto_depIdxs = []int32{
	0, // 0: qazpostBridge.Qazpostbridge.HealthCheck:input_type -> qazpostBridge.HealthCheckReq
	2, // 1: qazpostBridge.Qazpostbridge.GetOldPostalIndexByAddress:input_type -> qazpostBridge.GetOldPostalIndexByAddressRequest
	1, // 2: qazpostBridge.Qazpostbridge.HealthCheck:output_type -> qazpostBridge.HealthCheckResp
	3, // 3: qazpostBridge.Qazpostbridge.GetOldPostalIndexByAddress:output_type -> qazpostBridge.GetOldPostalIndexByAddressResponse
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_specs_proto_qazpost_bridge_qazpost_bridge_proto_init() }
func file_specs_proto_qazpost_bridge_qazpost_bridge_proto_init() {
	if File_specs_proto_qazpost_bridge_qazpost_bridge_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_specs_proto_qazpost_bridge_qazpost_bridge_proto_rawDesc), len(file_specs_proto_qazpost_bridge_qazpost_bridge_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_specs_proto_qazpost_bridge_qazpost_bridge_proto_goTypes,
		DependencyIndexes: file_specs_proto_qazpost_bridge_qazpost_bridge_proto_depIdxs,
		MessageInfos:      file_specs_proto_qazpost_bridge_qazpost_bridge_proto_msgTypes,
	}.Build()
	File_specs_proto_qazpost_bridge_qazpost_bridge_proto = out.File
	file_specs_proto_qazpost_bridge_qazpost_bridge_proto_goTypes = nil
	file_specs_proto_qazpost_bridge_qazpost_bridge_proto_depIdxs = nil
}
