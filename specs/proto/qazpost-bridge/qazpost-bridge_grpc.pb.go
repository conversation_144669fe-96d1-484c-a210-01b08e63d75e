// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.32.1
// source: specs/proto/qazpost-bridge/qazpost-bridge.proto

package qazpost_bridge

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Qazpostbridge_HealthCheck_FullMethodName                = "/qazpostBridge.Qazpostbridge/HealthCheck"
	Qazpostbridge_GetOldPostalIndexByAddress_FullMethodName = "/qazpostBridge.Qazpostbridge/GetOldPostalIndexByAddress"
)

// QazpostbridgeClient is the client API for Qazpostbridge service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Service definition for the Qazpost Bridge
type QazpostbridgeClient interface {
	// Standard health check
	HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error)
	// Retrieves the old postal index (SPI) for a given address string
	// by querying the Qazpost NPI API.
	GetOldPostalIndexByAddress(ctx context.Context, in *GetOldPostalIndexByAddressRequest, opts ...grpc.CallOption) (*GetOldPostalIndexByAddressResponse, error)
}

type qazpostbridgeClient struct {
	cc grpc.ClientConnInterface
}

func NewQazpostbridgeClient(cc grpc.ClientConnInterface) QazpostbridgeClient {
	return &qazpostbridgeClient{cc}
}

func (c *qazpostbridgeClient) HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResp)
	err := c.cc.Invoke(ctx, Qazpostbridge_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *qazpostbridgeClient) GetOldPostalIndexByAddress(ctx context.Context, in *GetOldPostalIndexByAddressRequest, opts ...grpc.CallOption) (*GetOldPostalIndexByAddressResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetOldPostalIndexByAddressResponse)
	err := c.cc.Invoke(ctx, Qazpostbridge_GetOldPostalIndexByAddress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// QazpostbridgeServer is the server API for Qazpostbridge service.
// All implementations must embed UnimplementedQazpostbridgeServer
// for forward compatibility.
//
// Service definition for the Qazpost Bridge
type QazpostbridgeServer interface {
	// Standard health check
	HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error)
	// Retrieves the old postal index (SPI) for a given address string
	// by querying the Qazpost NPI API.
	GetOldPostalIndexByAddress(context.Context, *GetOldPostalIndexByAddressRequest) (*GetOldPostalIndexByAddressResponse, error)
	mustEmbedUnimplementedQazpostbridgeServer()
}

// UnimplementedQazpostbridgeServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedQazpostbridgeServer struct{}

func (UnimplementedQazpostbridgeServer) HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedQazpostbridgeServer) GetOldPostalIndexByAddress(context.Context, *GetOldPostalIndexByAddressRequest) (*GetOldPostalIndexByAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOldPostalIndexByAddress not implemented")
}
func (UnimplementedQazpostbridgeServer) mustEmbedUnimplementedQazpostbridgeServer() {}
func (UnimplementedQazpostbridgeServer) testEmbeddedByValue()                       {}

// UnsafeQazpostbridgeServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to QazpostbridgeServer will
// result in compilation errors.
type UnsafeQazpostbridgeServer interface {
	mustEmbedUnimplementedQazpostbridgeServer()
}

func RegisterQazpostbridgeServer(s grpc.ServiceRegistrar, srv QazpostbridgeServer) {
	// If the following call pancis, it indicates UnimplementedQazpostbridgeServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Qazpostbridge_ServiceDesc, srv)
}

func _Qazpostbridge_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QazpostbridgeServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Qazpostbridge_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QazpostbridgeServer).HealthCheck(ctx, req.(*HealthCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Qazpostbridge_GetOldPostalIndexByAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOldPostalIndexByAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QazpostbridgeServer).GetOldPostalIndexByAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Qazpostbridge_GetOldPostalIndexByAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QazpostbridgeServer).GetOldPostalIndexByAddress(ctx, req.(*GetOldPostalIndexByAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Qazpostbridge_ServiceDesc is the grpc.ServiceDesc for Qazpostbridge service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Qazpostbridge_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "qazpostBridge.Qazpostbridge",
	HandlerType: (*QazpostbridgeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HealthCheck",
			Handler:    _Qazpostbridge_HealthCheck_Handler,
		},
		{
			MethodName: "GetOldPostalIndexByAddress",
			Handler:    _Qazpostbridge_GetOldPostalIndexByAddress_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "specs/proto/qazpost-bridge/qazpost-bridge.proto",
}
