// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v6.32.1
// source: specs/proto/jira-bridge/jira-bridge.proto

package jira_bridge

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HealthCheckReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckReq) Reset() {
	*x = HealthCheckReq{}
	mi := &file_specs_proto_jira_bridge_jira_bridge_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckReq) ProtoMessage() {}

func (x *HealthCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_jira_bridge_jira_bridge_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckReq.ProtoReflect.Descriptor instead.
func (*HealthCheckReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_jira_bridge_jira_bridge_proto_rawDescGZIP(), []int{0}
}

type HealthCheckResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckResp) Reset() {
	*x = HealthCheckResp{}
	mi := &file_specs_proto_jira_bridge_jira_bridge_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResp) ProtoMessage() {}

func (x *HealthCheckResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_jira_bridge_jira_bridge_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResp.ProtoReflect.Descriptor instead.
func (*HealthCheckResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_jira_bridge_jira_bridge_proto_rawDescGZIP(), []int{1}
}

func (x *HealthCheckResp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type CreateIssueReq struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Summary        string                 `protobuf:"bytes,1,opt,name=summary,proto3" json:"summary,omitempty"`
	ContractNumber string                 `protobuf:"bytes,2,opt,name=contractNumber,proto3" json:"contractNumber,omitempty"`
	FullName       string                 `protobuf:"bytes,3,opt,name=fullName,proto3" json:"fullName,omitempty"`
	Term           string                 `protobuf:"bytes,4,opt,name=term,proto3" json:"term,omitempty"`
	Amount         uint32                 `protobuf:"varint,5,opt,name=amount,proto3" json:"amount,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CreateIssueReq) Reset() {
	*x = CreateIssueReq{}
	mi := &file_specs_proto_jira_bridge_jira_bridge_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateIssueReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateIssueReq) ProtoMessage() {}

func (x *CreateIssueReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_jira_bridge_jira_bridge_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateIssueReq.ProtoReflect.Descriptor instead.
func (*CreateIssueReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_jira_bridge_jira_bridge_proto_rawDescGZIP(), []int{2}
}

func (x *CreateIssueReq) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *CreateIssueReq) GetContractNumber() string {
	if x != nil {
		return x.ContractNumber
	}
	return ""
}

func (x *CreateIssueReq) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *CreateIssueReq) GetTerm() string {
	if x != nil {
		return x.Term
	}
	return ""
}

func (x *CreateIssueReq) GetAmount() uint32 {
	if x != nil {
		return x.Amount
	}
	return 0
}

type CreateIssueResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ID            string                 `protobuf:"bytes,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Key           string                 `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Self          string                 `protobuf:"bytes,3,opt,name=self,proto3" json:"self,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateIssueResp) Reset() {
	*x = CreateIssueResp{}
	mi := &file_specs_proto_jira_bridge_jira_bridge_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateIssueResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateIssueResp) ProtoMessage() {}

func (x *CreateIssueResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_jira_bridge_jira_bridge_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateIssueResp.ProtoReflect.Descriptor instead.
func (*CreateIssueResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_jira_bridge_jira_bridge_proto_rawDescGZIP(), []int{3}
}

func (x *CreateIssueResp) GetID() string {
	if x != nil {
		return x.ID
	}
	return ""
}

func (x *CreateIssueResp) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *CreateIssueResp) GetSelf() string {
	if x != nil {
		return x.Self
	}
	return ""
}

var File_specs_proto_jira_bridge_jira_bridge_proto protoreflect.FileDescriptor

const file_specs_proto_jira_bridge_jira_bridge_proto_rawDesc = "" +
	"\n" +
	")specs/proto/jira-bridge/jira-bridge.proto\x12\n" +
	"jiraBridge\"\x10\n" +
	"\x0eHealthCheckReq\")\n" +
	"\x0fHealthCheckResp\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\"\x9a\x01\n" +
	"\x0eCreateIssueReq\x12\x18\n" +
	"\asummary\x18\x01 \x01(\tR\asummary\x12&\n" +
	"\x0econtractNumber\x18\x02 \x01(\tR\x0econtractNumber\x12\x1a\n" +
	"\bfullName\x18\x03 \x01(\tR\bfullName\x12\x12\n" +
	"\x04term\x18\x04 \x01(\tR\x04term\x12\x16\n" +
	"\x06amount\x18\x05 \x01(\rR\x06amount\"G\n" +
	"\x0fCreateIssueResp\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\tR\x02ID\x12\x10\n" +
	"\x03key\x18\x02 \x01(\tR\x03key\x12\x12\n" +
	"\x04self\x18\x03 \x01(\tR\x04self2\x9c\x01\n" +
	"\n" +
	"Jirabridge\x12F\n" +
	"\vHealthCheck\x12\x1a.jiraBridge.HealthCheckReq\x1a\x1b.jiraBridge.HealthCheckResp\x12F\n" +
	"\vCreateIssue\x12\x1a.jiraBridge.CreateIssueReq\x1a\x1b.jiraBridge.CreateIssueRespB\x19Z\x17specs/proto/jira-bridgeb\x06proto3"

var (
	file_specs_proto_jira_bridge_jira_bridge_proto_rawDescOnce sync.Once
	file_specs_proto_jira_bridge_jira_bridge_proto_rawDescData []byte
)

func file_specs_proto_jira_bridge_jira_bridge_proto_rawDescGZIP() []byte {
	file_specs_proto_jira_bridge_jira_bridge_proto_rawDescOnce.Do(func() {
		file_specs_proto_jira_bridge_jira_bridge_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_specs_proto_jira_bridge_jira_bridge_proto_rawDesc), len(file_specs_proto_jira_bridge_jira_bridge_proto_rawDesc)))
	})
	return file_specs_proto_jira_bridge_jira_bridge_proto_rawDescData
}

var file_specs_proto_jira_bridge_jira_bridge_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_specs_proto_jira_bridge_jira_bridge_proto_goTypes = []any{
	(*HealthCheckReq)(nil),  // 0: jiraBridge.HealthCheckReq
	(*HealthCheckResp)(nil), // 1: jiraBridge.HealthCheckResp
	(*CreateIssueReq)(nil),  // 2: jiraBridge.CreateIssueReq
	(*CreateIssueResp)(nil), // 3: jiraBridge.CreateIssueResp
}
var file_specs_proto_jira_bridge_jira_bridge_proto_depIdxs = []int32{
	0, // 0: jiraBridge.Jirabridge.HealthCheck:input_type -> jiraBridge.HealthCheckReq
	2, // 1: jiraBridge.Jirabridge.CreateIssue:input_type -> jiraBridge.CreateIssueReq
	1, // 2: jiraBridge.Jirabridge.HealthCheck:output_type -> jiraBridge.HealthCheckResp
	3, // 3: jiraBridge.Jirabridge.CreateIssue:output_type -> jiraBridge.CreateIssueResp
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_specs_proto_jira_bridge_jira_bridge_proto_init() }
func file_specs_proto_jira_bridge_jira_bridge_proto_init() {
	if File_specs_proto_jira_bridge_jira_bridge_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_specs_proto_jira_bridge_jira_bridge_proto_rawDesc), len(file_specs_proto_jira_bridge_jira_bridge_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_specs_proto_jira_bridge_jira_bridge_proto_goTypes,
		DependencyIndexes: file_specs_proto_jira_bridge_jira_bridge_proto_depIdxs,
		MessageInfos:      file_specs_proto_jira_bridge_jira_bridge_proto_msgTypes,
	}.Build()
	File_specs_proto_jira_bridge_jira_bridge_proto = out.File
	file_specs_proto_jira_bridge_jira_bridge_proto_goTypes = nil
	file_specs_proto_jira_bridge_jira_bridge_proto_depIdxs = nil
}
