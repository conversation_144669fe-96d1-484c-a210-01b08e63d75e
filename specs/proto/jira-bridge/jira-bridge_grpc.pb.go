// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.32.1
// source: specs/proto/jira-bridge/jira-bridge.proto

package jira_bridge

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Jirabridge_HealthCheck_FullMethodName = "/jiraBridge.Jirabridge/HealthCheck"
	Jirabridge_CreateIssue_FullMethodName = "/jiraBridge.Jirabridge/CreateIssue"
)

// JirabridgeClient is the client API for Jirabridge service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type JirabridgeClient interface {
	HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error)
	CreateIssue(ctx context.Context, in *CreateIssueReq, opts ...grpc.CallOption) (*CreateIssueResp, error)
}

type jirabridgeClient struct {
	cc grpc.ClientConnInterface
}

func NewJirabridgeClient(cc grpc.ClientConnInterface) JirabridgeClient {
	return &jirabridgeClient{cc}
}

func (c *jirabridgeClient) HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResp)
	err := c.cc.Invoke(ctx, Jirabridge_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jirabridgeClient) CreateIssue(ctx context.Context, in *CreateIssueReq, opts ...grpc.CallOption) (*CreateIssueResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateIssueResp)
	err := c.cc.Invoke(ctx, Jirabridge_CreateIssue_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// JirabridgeServer is the server API for Jirabridge service.
// All implementations must embed UnimplementedJirabridgeServer
// for forward compatibility.
type JirabridgeServer interface {
	HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error)
	CreateIssue(context.Context, *CreateIssueReq) (*CreateIssueResp, error)
	mustEmbedUnimplementedJirabridgeServer()
}

// UnimplementedJirabridgeServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedJirabridgeServer struct{}

func (UnimplementedJirabridgeServer) HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedJirabridgeServer) CreateIssue(context.Context, *CreateIssueReq) (*CreateIssueResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateIssue not implemented")
}
func (UnimplementedJirabridgeServer) mustEmbedUnimplementedJirabridgeServer() {}
func (UnimplementedJirabridgeServer) testEmbeddedByValue()                    {}

// UnsafeJirabridgeServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to JirabridgeServer will
// result in compilation errors.
type UnsafeJirabridgeServer interface {
	mustEmbedUnimplementedJirabridgeServer()
}

func RegisterJirabridgeServer(s grpc.ServiceRegistrar, srv JirabridgeServer) {
	// If the following call pancis, it indicates UnimplementedJirabridgeServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Jirabridge_ServiceDesc, srv)
}

func _Jirabridge_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JirabridgeServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jirabridge_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JirabridgeServer).HealthCheck(ctx, req.(*HealthCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jirabridge_CreateIssue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateIssueReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JirabridgeServer).CreateIssue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jirabridge_CreateIssue_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JirabridgeServer).CreateIssue(ctx, req.(*CreateIssueReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Jirabridge_ServiceDesc is the grpc.ServiceDesc for Jirabridge service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Jirabridge_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "jiraBridge.Jirabridge",
	HandlerType: (*JirabridgeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HealthCheck",
			Handler:    _Jirabridge_HealthCheck_Handler,
		},
		{
			MethodName: "CreateIssue",
			Handler:    _Jirabridge_CreateIssue_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "specs/proto/jira-bridge/jira-bridge.proto",
}
