// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.32.1
// source: specs/proto/otp/otp.proto

package otp

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Otp_HealthCheck_FullMethodName       = "/otp.Otp/HealthCheck"
	Otp_GenerateCode_FullMethodName      = "/otp.Otp/GenerateCode"
	Otp_GenerateRetryCode_FullMethodName = "/otp.Otp/GenerateRetryCode"
	Otp_ValidateCode_FullMethodName      = "/otp.Otp/ValidateCode"
)

// OtpClient is the client API for Otp service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OtpClient interface {
	HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error)
	GenerateCode(ctx context.Context, in *GenerateCodeReq, opts ...grpc.CallOption) (*GenerateCodeResp, error)
	GenerateRetryCode(ctx context.Context, in *GenerateRetryCodeReq, opts ...grpc.CallOption) (*GenerateCodeResp, error)
	ValidateCode(ctx context.Context, in *ValidateCodeReq, opts ...grpc.CallOption) (*ValidateCodeResp, error)
}

type otpClient struct {
	cc grpc.ClientConnInterface
}

func NewOtpClient(cc grpc.ClientConnInterface) OtpClient {
	return &otpClient{cc}
}

func (c *otpClient) HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResp)
	err := c.cc.Invoke(ctx, Otp_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *otpClient) GenerateCode(ctx context.Context, in *GenerateCodeReq, opts ...grpc.CallOption) (*GenerateCodeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GenerateCodeResp)
	err := c.cc.Invoke(ctx, Otp_GenerateCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *otpClient) GenerateRetryCode(ctx context.Context, in *GenerateRetryCodeReq, opts ...grpc.CallOption) (*GenerateCodeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GenerateCodeResp)
	err := c.cc.Invoke(ctx, Otp_GenerateRetryCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *otpClient) ValidateCode(ctx context.Context, in *ValidateCodeReq, opts ...grpc.CallOption) (*ValidateCodeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ValidateCodeResp)
	err := c.cc.Invoke(ctx, Otp_ValidateCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OtpServer is the server API for Otp service.
// All implementations must embed UnimplementedOtpServer
// for forward compatibility.
type OtpServer interface {
	HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error)
	GenerateCode(context.Context, *GenerateCodeReq) (*GenerateCodeResp, error)
	GenerateRetryCode(context.Context, *GenerateRetryCodeReq) (*GenerateCodeResp, error)
	ValidateCode(context.Context, *ValidateCodeReq) (*ValidateCodeResp, error)
	mustEmbedUnimplementedOtpServer()
}

// UnimplementedOtpServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedOtpServer struct{}

func (UnimplementedOtpServer) HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedOtpServer) GenerateCode(context.Context, *GenerateCodeReq) (*GenerateCodeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateCode not implemented")
}
func (UnimplementedOtpServer) GenerateRetryCode(context.Context, *GenerateRetryCodeReq) (*GenerateCodeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateRetryCode not implemented")
}
func (UnimplementedOtpServer) ValidateCode(context.Context, *ValidateCodeReq) (*ValidateCodeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateCode not implemented")
}
func (UnimplementedOtpServer) mustEmbedUnimplementedOtpServer() {}
func (UnimplementedOtpServer) testEmbeddedByValue()             {}

// UnsafeOtpServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OtpServer will
// result in compilation errors.
type UnsafeOtpServer interface {
	mustEmbedUnimplementedOtpServer()
}

func RegisterOtpServer(s grpc.ServiceRegistrar, srv OtpServer) {
	// If the following call pancis, it indicates UnimplementedOtpServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Otp_ServiceDesc, srv)
}

func _Otp_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OtpServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Otp_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OtpServer).HealthCheck(ctx, req.(*HealthCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Otp_GenerateCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OtpServer).GenerateCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Otp_GenerateCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OtpServer).GenerateCode(ctx, req.(*GenerateCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Otp_GenerateRetryCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateRetryCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OtpServer).GenerateRetryCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Otp_GenerateRetryCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OtpServer).GenerateRetryCode(ctx, req.(*GenerateRetryCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Otp_ValidateCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OtpServer).ValidateCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Otp_ValidateCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OtpServer).ValidateCode(ctx, req.(*ValidateCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Otp_ServiceDesc is the grpc.ServiceDesc for Otp service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Otp_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "otp.Otp",
	HandlerType: (*OtpServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HealthCheck",
			Handler:    _Otp_HealthCheck_Handler,
		},
		{
			MethodName: "GenerateCode",
			Handler:    _Otp_GenerateCode_Handler,
		},
		{
			MethodName: "GenerateRetryCode",
			Handler:    _Otp_GenerateRetryCode_Handler,
		},
		{
			MethodName: "ValidateCode",
			Handler:    _Otp_ValidateCode_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "specs/proto/otp/otp.proto",
}
