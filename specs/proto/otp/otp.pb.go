// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v6.32.1
// source: specs/proto/otp/otp.proto

package otp

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HealthCheckReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckReq) Reset() {
	*x = HealthCheckReq{}
	mi := &file_specs_proto_otp_otp_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckReq) ProtoMessage() {}

func (x *HealthCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_otp_otp_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckReq.ProtoReflect.Descriptor instead.
func (*HealthCheckReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_otp_otp_proto_rawDescGZIP(), []int{0}
}

type HealthCheckResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckResp) Reset() {
	*x = HealthCheckResp{}
	mi := &file_specs_proto_otp_otp_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResp) ProtoMessage() {}

func (x *HealthCheckResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_otp_otp_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResp.ProtoReflect.Descriptor instead.
func (*HealthCheckResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_otp_otp_proto_rawDescGZIP(), []int{1}
}

func (x *HealthCheckResp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type GenerateCodeReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Action        string                 `protobuf:"bytes,1,opt,name=action,proto3" json:"action,omitempty"` // Действие, для которого запрашивается код
	Phone         string                 `protobuf:"bytes,2,opt,name=phone,proto3" json:"phone,omitempty"`
	Payload       []byte                 `protobuf:"bytes,10,opt,name=payload,proto3,oneof" json:"payload,omitempty"` // Полезная нагрузка для обработки в вызывающем сервисе
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerateCodeReq) Reset() {
	*x = GenerateCodeReq{}
	mi := &file_specs_proto_otp_otp_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateCodeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateCodeReq) ProtoMessage() {}

func (x *GenerateCodeReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_otp_otp_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateCodeReq.ProtoReflect.Descriptor instead.
func (*GenerateCodeReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_otp_otp_proto_rawDescGZIP(), []int{2}
}

func (x *GenerateCodeReq) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *GenerateCodeReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *GenerateCodeReq) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

type GenerateCodeResp struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	AttemptId       string                 `protobuf:"bytes,1,opt,name=attempt_id,json=attemptId,proto3" json:"attempt_id,omitempty"`
	Code            string                 `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	CodeTtl         int32                  `protobuf:"varint,3,opt,name=code_ttl,json=codeTtl,proto3" json:"code_ttl,omitempty"`                           // Время жизни кода в секундах
	CodeChecksLeft  int32                  `protobuf:"varint,4,opt,name=code_checks_left,json=codeChecksLeft,proto3" json:"code_checks_left,omitempty"`    // Количество возможных вводов кода для одной попытки
	AttemptsLeft    int32                  `protobuf:"varint,5,opt,name=attempts_left,json=attemptsLeft,proto3" json:"attempts_left,omitempty"`            // Количество возможных запросов нового кода до таймаута (не считая текущий)
	AttemptsTimeout int32                  `protobuf:"varint,6,opt,name=attempts_timeout,json=attemptsTimeout,proto3" json:"attempts_timeout,omitempty"`   // Время ожидания в секундах, после которого можно запросить новый код
	NewAttemptDelay int32                  `protobuf:"varint,7,opt,name=new_attempt_delay,json=newAttemptDelay,proto3" json:"new_attempt_delay,omitempty"` // Время до возможности запроса следующего запроса кода в секундах
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GenerateCodeResp) Reset() {
	*x = GenerateCodeResp{}
	mi := &file_specs_proto_otp_otp_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateCodeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateCodeResp) ProtoMessage() {}

func (x *GenerateCodeResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_otp_otp_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateCodeResp.ProtoReflect.Descriptor instead.
func (*GenerateCodeResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_otp_otp_proto_rawDescGZIP(), []int{3}
}

func (x *GenerateCodeResp) GetAttemptId() string {
	if x != nil {
		return x.AttemptId
	}
	return ""
}

func (x *GenerateCodeResp) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *GenerateCodeResp) GetCodeTtl() int32 {
	if x != nil {
		return x.CodeTtl
	}
	return 0
}

func (x *GenerateCodeResp) GetCodeChecksLeft() int32 {
	if x != nil {
		return x.CodeChecksLeft
	}
	return 0
}

func (x *GenerateCodeResp) GetAttemptsLeft() int32 {
	if x != nil {
		return x.AttemptsLeft
	}
	return 0
}

func (x *GenerateCodeResp) GetAttemptsTimeout() int32 {
	if x != nil {
		return x.AttemptsTimeout
	}
	return 0
}

func (x *GenerateCodeResp) GetNewAttemptDelay() int32 {
	if x != nil {
		return x.NewAttemptDelay
	}
	return 0
}

type GenerateRetryCodeReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AttemptId     string                 `protobuf:"bytes,2,opt,name=attempt_id,json=attemptId,proto3" json:"attempt_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerateRetryCodeReq) Reset() {
	*x = GenerateRetryCodeReq{}
	mi := &file_specs_proto_otp_otp_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateRetryCodeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateRetryCodeReq) ProtoMessage() {}

func (x *GenerateRetryCodeReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_otp_otp_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateRetryCodeReq.ProtoReflect.Descriptor instead.
func (*GenerateRetryCodeReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_otp_otp_proto_rawDescGZIP(), []int{4}
}

func (x *GenerateRetryCodeReq) GetAttemptId() string {
	if x != nil {
		return x.AttemptId
	}
	return ""
}

type ValidateCodeReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AttemptId     string                 `protobuf:"bytes,1,opt,name=attempt_id,json=attemptId,proto3" json:"attempt_id,omitempty"`
	Code          string                 `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateCodeReq) Reset() {
	*x = ValidateCodeReq{}
	mi := &file_specs_proto_otp_otp_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateCodeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateCodeReq) ProtoMessage() {}

func (x *ValidateCodeReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_otp_otp_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateCodeReq.ProtoReflect.Descriptor instead.
func (*ValidateCodeReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_otp_otp_proto_rawDescGZIP(), []int{5}
}

func (x *ValidateCodeReq) GetAttemptId() string {
	if x != nil {
		return x.AttemptId
	}
	return ""
}

func (x *ValidateCodeReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type ValidateCodeResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	RetriesLeft   int32                  `protobuf:"varint,2,opt,name=retries_left,json=retriesLeft,proto3" json:"retries_left,omitempty"`
	Initiator     string                 `protobuf:"bytes,3,opt,name=initiator,proto3" json:"initiator,omitempty"`
	Payload       []byte                 `protobuf:"bytes,10,opt,name=payload,proto3,oneof" json:"payload,omitempty"` // Полезная нагрузка, переданная при запросе кода
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateCodeResp) Reset() {
	*x = ValidateCodeResp{}
	mi := &file_specs_proto_otp_otp_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateCodeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateCodeResp) ProtoMessage() {}

func (x *ValidateCodeResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_otp_otp_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateCodeResp.ProtoReflect.Descriptor instead.
func (*ValidateCodeResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_otp_otp_proto_rawDescGZIP(), []int{6}
}

func (x *ValidateCodeResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ValidateCodeResp) GetRetriesLeft() int32 {
	if x != nil {
		return x.RetriesLeft
	}
	return 0
}

func (x *ValidateCodeResp) GetInitiator() string {
	if x != nil {
		return x.Initiator
	}
	return ""
}

func (x *ValidateCodeResp) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

var File_specs_proto_otp_otp_proto protoreflect.FileDescriptor

const file_specs_proto_otp_otp_proto_rawDesc = "" +
	"\n" +
	"\x19specs/proto/otp/otp.proto\x12\x03otp\"\x10\n" +
	"\x0eHealthCheckReq\")\n" +
	"\x0fHealthCheckResp\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\"j\n" +
	"\x0fGenerateCodeReq\x12\x16\n" +
	"\x06action\x18\x01 \x01(\tR\x06action\x12\x14\n" +
	"\x05phone\x18\x02 \x01(\tR\x05phone\x12\x1d\n" +
	"\apayload\x18\n" +
	" \x01(\fH\x00R\apayload\x88\x01\x01B\n" +
	"\n" +
	"\b_payload\"\x86\x02\n" +
	"\x10GenerateCodeResp\x12\x1d\n" +
	"\n" +
	"attempt_id\x18\x01 \x01(\tR\tattemptId\x12\x12\n" +
	"\x04code\x18\x02 \x01(\tR\x04code\x12\x19\n" +
	"\bcode_ttl\x18\x03 \x01(\x05R\acodeTtl\x12(\n" +
	"\x10code_checks_left\x18\x04 \x01(\x05R\x0ecodeChecksLeft\x12#\n" +
	"\rattempts_left\x18\x05 \x01(\x05R\fattemptsLeft\x12)\n" +
	"\x10attempts_timeout\x18\x06 \x01(\x05R\x0fattemptsTimeout\x12*\n" +
	"\x11new_attempt_delay\x18\a \x01(\x05R\x0fnewAttemptDelay\"5\n" +
	"\x14GenerateRetryCodeReq\x12\x1d\n" +
	"\n" +
	"attempt_id\x18\x02 \x01(\tR\tattemptId\"D\n" +
	"\x0fValidateCodeReq\x12\x1d\n" +
	"\n" +
	"attempt_id\x18\x01 \x01(\tR\tattemptId\x12\x12\n" +
	"\x04code\x18\x02 \x01(\tR\x04code\"\x98\x01\n" +
	"\x10ValidateCodeResp\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12!\n" +
	"\fretries_left\x18\x02 \x01(\x05R\vretriesLeft\x12\x1c\n" +
	"\tinitiator\x18\x03 \x01(\tR\tinitiator\x12\x1d\n" +
	"\apayload\x18\n" +
	" \x01(\fH\x00R\apayload\x88\x01\x01B\n" +
	"\n" +
	"\b_payload2\x80\x02\n" +
	"\x03Otp\x128\n" +
	"\vHealthCheck\x12\x13.otp.HealthCheckReq\x1a\x14.otp.HealthCheckResp\x12;\n" +
	"\fGenerateCode\x12\x14.otp.GenerateCodeReq\x1a\x15.otp.GenerateCodeResp\x12E\n" +
	"\x11GenerateRetryCode\x12\x19.otp.GenerateRetryCodeReq\x1a\x15.otp.GenerateCodeResp\x12;\n" +
	"\fValidateCode\x12\x14.otp.ValidateCodeReq\x1a\x15.otp.ValidateCodeRespB\x11Z\x0fspecs/proto/otpb\x06proto3"

var (
	file_specs_proto_otp_otp_proto_rawDescOnce sync.Once
	file_specs_proto_otp_otp_proto_rawDescData []byte
)

func file_specs_proto_otp_otp_proto_rawDescGZIP() []byte {
	file_specs_proto_otp_otp_proto_rawDescOnce.Do(func() {
		file_specs_proto_otp_otp_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_specs_proto_otp_otp_proto_rawDesc), len(file_specs_proto_otp_otp_proto_rawDesc)))
	})
	return file_specs_proto_otp_otp_proto_rawDescData
}

var file_specs_proto_otp_otp_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_specs_proto_otp_otp_proto_goTypes = []any{
	(*HealthCheckReq)(nil),       // 0: otp.HealthCheckReq
	(*HealthCheckResp)(nil),      // 1: otp.HealthCheckResp
	(*GenerateCodeReq)(nil),      // 2: otp.GenerateCodeReq
	(*GenerateCodeResp)(nil),     // 3: otp.GenerateCodeResp
	(*GenerateRetryCodeReq)(nil), // 4: otp.GenerateRetryCodeReq
	(*ValidateCodeReq)(nil),      // 5: otp.ValidateCodeReq
	(*ValidateCodeResp)(nil),     // 6: otp.ValidateCodeResp
}
var file_specs_proto_otp_otp_proto_depIdxs = []int32{
	0, // 0: otp.Otp.HealthCheck:input_type -> otp.HealthCheckReq
	2, // 1: otp.Otp.GenerateCode:input_type -> otp.GenerateCodeReq
	4, // 2: otp.Otp.GenerateRetryCode:input_type -> otp.GenerateRetryCodeReq
	5, // 3: otp.Otp.ValidateCode:input_type -> otp.ValidateCodeReq
	1, // 4: otp.Otp.HealthCheck:output_type -> otp.HealthCheckResp
	3, // 5: otp.Otp.GenerateCode:output_type -> otp.GenerateCodeResp
	3, // 6: otp.Otp.GenerateRetryCode:output_type -> otp.GenerateCodeResp
	6, // 7: otp.Otp.ValidateCode:output_type -> otp.ValidateCodeResp
	4, // [4:8] is the sub-list for method output_type
	0, // [0:4] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_specs_proto_otp_otp_proto_init() }
func file_specs_proto_otp_otp_proto_init() {
	if File_specs_proto_otp_otp_proto != nil {
		return
	}
	file_specs_proto_otp_otp_proto_msgTypes[2].OneofWrappers = []any{}
	file_specs_proto_otp_otp_proto_msgTypes[6].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_specs_proto_otp_otp_proto_rawDesc), len(file_specs_proto_otp_otp_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_specs_proto_otp_otp_proto_goTypes,
		DependencyIndexes: file_specs_proto_otp_otp_proto_depIdxs,
		MessageInfos:      file_specs_proto_otp_otp_proto_msgTypes,
	}.Build()
	File_specs_proto_otp_otp_proto = out.File
	file_specs_proto_otp_otp_proto_goTypes = nil
	file_specs_proto_otp_otp_proto_depIdxs = nil
}
