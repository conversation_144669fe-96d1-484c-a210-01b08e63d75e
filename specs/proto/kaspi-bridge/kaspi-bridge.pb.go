// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v6.32.1
// source: specs/proto/kaspi-bridge/kaspi-bridge.proto

package kaspi_bridge

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HealthCheckReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckReq) Reset() {
	*x = HealthCheckReq{}
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckReq) ProtoMessage() {}

func (x *HealthCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckReq.ProtoReflect.Descriptor instead.
func (*HealthCheckReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDescGZIP(), []int{0}
}

type HealthCheckResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckResp) Reset() {
	*x = HealthCheckResp{}
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResp) ProtoMessage() {}

func (x *HealthCheckResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResp.ProtoReflect.Descriptor instead.
func (*HealthCheckResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDescGZIP(), []int{1}
}

func (x *HealthCheckResp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type Result struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResultCode    string                 `protobuf:"bytes,1,opt,name=resultCode,proto3" json:"resultCode,omitempty"`
	ResultMessage string                 `protobuf:"bytes,2,opt,name=resultMessage,proto3" json:"resultMessage,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Result) Reset() {
	*x = Result{}
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Result) ProtoMessage() {}

func (x *Result) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Result.ProtoReflect.Descriptor instead.
func (*Result) Descriptor() ([]byte, []int) {
	return file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDescGZIP(), []int{2}
}

func (x *Result) GetResultCode() string {
	if x != nil {
		return x.ResultCode
	}
	return ""
}

func (x *Result) GetResultMessage() string {
	if x != nil {
		return x.ResultMessage
	}
	return ""
}

type Merchant struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MerchantId    *string                `protobuf:"bytes,1,opt,name=merchantId,proto3,oneof" json:"merchantId,omitempty"`
	MerchantName  *string                `protobuf:"bytes,2,opt,name=merchantName,proto3,oneof" json:"merchantName,omitempty"`
	MerchantMCC   *string                `protobuf:"bytes,3,opt,name=merchantMCC,proto3,oneof" json:"merchantMCC,omitempty"`
	AuthorType    *string                `protobuf:"bytes,4,opt,name=authorType,proto3,oneof" json:"authorType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Merchant) Reset() {
	*x = Merchant{}
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Merchant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Merchant) ProtoMessage() {}

func (x *Merchant) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Merchant.ProtoReflect.Descriptor instead.
func (*Merchant) Descriptor() ([]byte, []int) {
	return file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDescGZIP(), []int{3}
}

func (x *Merchant) GetMerchantId() string {
	if x != nil && x.MerchantId != nil {
		return *x.MerchantId
	}
	return ""
}

func (x *Merchant) GetMerchantName() string {
	if x != nil && x.MerchantName != nil {
		return *x.MerchantName
	}
	return ""
}

func (x *Merchant) GetMerchantMCC() string {
	if x != nil && x.MerchantMCC != nil {
		return *x.MerchantMCC
	}
	return ""
}

func (x *Merchant) GetAuthorType() string {
	if x != nil && x.AuthorType != nil {
		return *x.AuthorType
	}
	return ""
}

type QrScanReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	QrCode        string                 `protobuf:"bytes,1,opt,name=qrCode,proto3" json:"qrCode,omitempty"`
	CustomerId    string                 `protobuf:"bytes,2,opt,name=customerId,proto3" json:"customerId,omitempty"`
	Locale        string                 `protobuf:"bytes,3,opt,name=locale,proto3" json:"locale,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QrScanReq) Reset() {
	*x = QrScanReq{}
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QrScanReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QrScanReq) ProtoMessage() {}

func (x *QrScanReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QrScanReq.ProtoReflect.Descriptor instead.
func (*QrScanReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDescGZIP(), []int{4}
}

func (x *QrScanReq) GetQrCode() string {
	if x != nil {
		return x.QrCode
	}
	return ""
}

func (x *QrScanReq) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *QrScanReq) GetLocale() string {
	if x != nil {
		return x.Locale
	}
	return ""
}

type QrScanResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        *Result                `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	QrCodeType    *string                `protobuf:"bytes,2,opt,name=qrCodeType,proto3,oneof" json:"qrCodeType,omitempty"`
	Merchant      *Merchant              `protobuf:"bytes,3,opt,name=merchant,proto3,oneof" json:"merchant,omitempty"`
	Payment       *ScanPayment           `protobuf:"bytes,4,opt,name=payment,proto3,oneof" json:"payment,omitempty"`
	Refund        *ScanRefund            `protobuf:"bytes,5,opt,name=refund,proto3,oneof" json:"refund,omitempty"`
	PaymentData   *ScanPaymentData       `protobuf:"bytes,6,opt,name=paymentData,proto3,oneof" json:"paymentData,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QrScanResp) Reset() {
	*x = QrScanResp{}
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QrScanResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QrScanResp) ProtoMessage() {}

func (x *QrScanResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QrScanResp.ProtoReflect.Descriptor instead.
func (*QrScanResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDescGZIP(), []int{5}
}

func (x *QrScanResp) GetResult() *Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *QrScanResp) GetQrCodeType() string {
	if x != nil && x.QrCodeType != nil {
		return *x.QrCodeType
	}
	return ""
}

func (x *QrScanResp) GetMerchant() *Merchant {
	if x != nil {
		return x.Merchant
	}
	return nil
}

func (x *QrScanResp) GetPayment() *ScanPayment {
	if x != nil {
		return x.Payment
	}
	return nil
}

func (x *QrScanResp) GetRefund() *ScanRefund {
	if x != nil {
		return x.Refund
	}
	return nil
}

func (x *QrScanResp) GetPaymentData() *ScanPaymentData {
	if x != nil {
		return x.PaymentData
	}
	return nil
}

type ScanPayment struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	PaymentId       *string                `protobuf:"bytes,1,opt,name=paymentId,proto3,oneof" json:"paymentId,omitempty"`
	PaymentAmount   *string                `protobuf:"bytes,2,opt,name=paymentAmount,proto3,oneof" json:"paymentAmount,omitempty"`
	CanConfirmUntil *string                `protobuf:"bytes,3,opt,name=canConfirmUntil,proto3,oneof" json:"canConfirmUntil,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ScanPayment) Reset() {
	*x = ScanPayment{}
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScanPayment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScanPayment) ProtoMessage() {}

func (x *ScanPayment) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScanPayment.ProtoReflect.Descriptor instead.
func (*ScanPayment) Descriptor() ([]byte, []int) {
	return file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDescGZIP(), []int{6}
}

func (x *ScanPayment) GetPaymentId() string {
	if x != nil && x.PaymentId != nil {
		return *x.PaymentId
	}
	return ""
}

func (x *ScanPayment) GetPaymentAmount() string {
	if x != nil && x.PaymentAmount != nil {
		return *x.PaymentAmount
	}
	return ""
}

func (x *ScanPayment) GetCanConfirmUntil() string {
	if x != nil && x.CanConfirmUntil != nil {
		return *x.CanConfirmUntil
	}
	return ""
}

type ScanRefund struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RefundId      *string                `protobuf:"bytes,1,opt,name=refundId,proto3,oneof" json:"refundId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ScanRefund) Reset() {
	*x = ScanRefund{}
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScanRefund) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScanRefund) ProtoMessage() {}

func (x *ScanRefund) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScanRefund.ProtoReflect.Descriptor instead.
func (*ScanRefund) Descriptor() ([]byte, []int) {
	return file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDescGZIP(), []int{7}
}

func (x *ScanRefund) GetRefundId() string {
	if x != nil && x.RefundId != nil {
		return *x.RefundId
	}
	return ""
}

type ScanPaymentData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ServiceId     *int64                 `protobuf:"varint,1,opt,name=ServiceId,proto3,oneof" json:"ServiceId,omitempty"`
	ServiceName   *string                `protobuf:"bytes,2,opt,name=ServiceName,proto3,oneof" json:"ServiceName,omitempty"`
	Parameters    []*Parameters          `protobuf:"bytes,3,rep,name=parameters,proto3" json:"parameters,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ScanPaymentData) Reset() {
	*x = ScanPaymentData{}
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScanPaymentData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScanPaymentData) ProtoMessage() {}

func (x *ScanPaymentData) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScanPaymentData.ProtoReflect.Descriptor instead.
func (*ScanPaymentData) Descriptor() ([]byte, []int) {
	return file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDescGZIP(), []int{8}
}

func (x *ScanPaymentData) GetServiceId() int64 {
	if x != nil && x.ServiceId != nil {
		return *x.ServiceId
	}
	return 0
}

func (x *ScanPaymentData) GetServiceName() string {
	if x != nil && x.ServiceName != nil {
		return *x.ServiceName
	}
	return ""
}

func (x *ScanPaymentData) GetParameters() []*Parameters {
	if x != nil {
		return x.Parameters
	}
	return nil
}

type Parameters struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            *int64                 `protobuf:"varint,1,opt,name=Id,proto3,oneof" json:"Id,omitempty"`
	Regex         *string                `protobuf:"bytes,2,opt,name=regex,proto3,oneof" json:"regex,omitempty"`
	Value         *string                `protobuf:"bytes,3,opt,name=value,proto3,oneof" json:"value,omitempty"`
	Name          *string                `protobuf:"bytes,4,opt,name=name,proto3,oneof" json:"name,omitempty"`
	Options       []*Options             `protobuf:"bytes,5,rep,name=options,proto3" json:"options,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Parameters) Reset() {
	*x = Parameters{}
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Parameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Parameters) ProtoMessage() {}

func (x *Parameters) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Parameters.ProtoReflect.Descriptor instead.
func (*Parameters) Descriptor() ([]byte, []int) {
	return file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDescGZIP(), []int{9}
}

func (x *Parameters) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *Parameters) GetRegex() string {
	if x != nil && x.Regex != nil {
		return *x.Regex
	}
	return ""
}

func (x *Parameters) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

func (x *Parameters) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *Parameters) GetOptions() []*Options {
	if x != nil {
		return x.Options
	}
	return nil
}

type Options struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           *string                `protobuf:"bytes,1,opt,name=key,proto3,oneof" json:"key,omitempty"`
	Label         *string                `protobuf:"bytes,2,opt,name=label,proto3,oneof" json:"label,omitempty"`
	Description   *string                `protobuf:"bytes,3,opt,name=description,proto3,oneof" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Options) Reset() {
	*x = Options{}
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Options) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Options) ProtoMessage() {}

func (x *Options) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Options.ProtoReflect.Descriptor instead.
func (*Options) Descriptor() ([]byte, []int) {
	return file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDescGZIP(), []int{10}
}

func (x *Options) GetKey() string {
	if x != nil && x.Key != nil {
		return *x.Key
	}
	return ""
}

func (x *Options) GetLabel() string {
	if x != nil && x.Label != nil {
		return *x.Label
	}
	return ""
}

func (x *Options) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

type QrNotifyPaymentReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        *Result                `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	Payment       *QrNotifyPayment       `protobuf:"bytes,2,opt,name=payment,proto3" json:"payment,omitempty"`
	CustomerId    string                 `protobuf:"bytes,3,opt,name=customerId,proto3" json:"customerId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QrNotifyPaymentReq) Reset() {
	*x = QrNotifyPaymentReq{}
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QrNotifyPaymentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QrNotifyPaymentReq) ProtoMessage() {}

func (x *QrNotifyPaymentReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QrNotifyPaymentReq.ProtoReflect.Descriptor instead.
func (*QrNotifyPaymentReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDescGZIP(), []int{11}
}

func (x *QrNotifyPaymentReq) GetResult() *Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *QrNotifyPaymentReq) GetPayment() *QrNotifyPayment {
	if x != nil {
		return x.Payment
	}
	return nil
}

func (x *QrNotifyPaymentReq) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

type QrNotifyPayment struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PaymentId     string                 `protobuf:"bytes,1,opt,name=paymentId,proto3" json:"paymentId,omitempty"`
	MppPaymentId  string                 `protobuf:"bytes,2,opt,name=mppPaymentId,proto3" json:"mppPaymentId,omitempty"`
	PaymentAmount *string                `protobuf:"bytes,3,opt,name=paymentAmount,proto3,oneof" json:"paymentAmount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QrNotifyPayment) Reset() {
	*x = QrNotifyPayment{}
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QrNotifyPayment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QrNotifyPayment) ProtoMessage() {}

func (x *QrNotifyPayment) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QrNotifyPayment.ProtoReflect.Descriptor instead.
func (*QrNotifyPayment) Descriptor() ([]byte, []int) {
	return file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDescGZIP(), []int{12}
}

func (x *QrNotifyPayment) GetPaymentId() string {
	if x != nil {
		return x.PaymentId
	}
	return ""
}

func (x *QrNotifyPayment) GetMppPaymentId() string {
	if x != nil {
		return x.MppPaymentId
	}
	return ""
}

func (x *QrNotifyPayment) GetPaymentAmount() string {
	if x != nil && x.PaymentAmount != nil {
		return *x.PaymentAmount
	}
	return ""
}

type QrNotifyPaymentResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        *Result                `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	LinkReceipt   *string                `protobuf:"bytes,2,opt,name=linkReceipt,proto3,oneof" json:"linkReceipt,omitempty"`
	PdfReceipt    *string                `protobuf:"bytes,3,opt,name=pdfReceipt,proto3,oneof" json:"pdfReceipt,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QrNotifyPaymentResp) Reset() {
	*x = QrNotifyPaymentResp{}
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QrNotifyPaymentResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QrNotifyPaymentResp) ProtoMessage() {}

func (x *QrNotifyPaymentResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QrNotifyPaymentResp.ProtoReflect.Descriptor instead.
func (*QrNotifyPaymentResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDescGZIP(), []int{13}
}

func (x *QrNotifyPaymentResp) GetResult() *Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *QrNotifyPaymentResp) GetLinkReceipt() string {
	if x != nil && x.LinkReceipt != nil {
		return *x.LinkReceipt
	}
	return ""
}

func (x *QrNotifyPaymentResp) GetPdfReceipt() string {
	if x != nil && x.PdfReceipt != nil {
		return *x.PdfReceipt
	}
	return ""
}

type QrCheckoutReq struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	PaymentId     string                  `protobuf:"bytes,1,opt,name=paymentId,proto3" json:"paymentId,omitempty"`
	ServiceId     string                  `protobuf:"bytes,2,opt,name=serviceId,proto3" json:"serviceId,omitempty"`
	Parameters    []*QrCheckoutParameters `protobuf:"bytes,3,rep,name=parameters,proto3" json:"parameters,omitempty"`
	Locale        string                  `protobuf:"bytes,4,opt,name=locale,proto3" json:"locale,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QrCheckoutReq) Reset() {
	*x = QrCheckoutReq{}
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QrCheckoutReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QrCheckoutReq) ProtoMessage() {}

func (x *QrCheckoutReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QrCheckoutReq.ProtoReflect.Descriptor instead.
func (*QrCheckoutReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDescGZIP(), []int{14}
}

func (x *QrCheckoutReq) GetPaymentId() string {
	if x != nil {
		return x.PaymentId
	}
	return ""
}

func (x *QrCheckoutReq) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *QrCheckoutReq) GetParameters() []*QrCheckoutParameters {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *QrCheckoutReq) GetLocale() string {
	if x != nil {
		return x.Locale
	}
	return ""
}

type QrCheckoutParameters struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Value         *string                `protobuf:"bytes,2,opt,name=value,proto3,oneof" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QrCheckoutParameters) Reset() {
	*x = QrCheckoutParameters{}
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QrCheckoutParameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QrCheckoutParameters) ProtoMessage() {}

func (x *QrCheckoutParameters) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QrCheckoutParameters.ProtoReflect.Descriptor instead.
func (*QrCheckoutParameters) Descriptor() ([]byte, []int) {
	return file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDescGZIP(), []int{15}
}

func (x *QrCheckoutParameters) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *QrCheckoutParameters) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

type QrCheckoutResp struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Result          *Result                `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	PaymentAmount   *string                `protobuf:"bytes,2,opt,name=paymentAmount,proto3,oneof" json:"paymentAmount,omitempty"`
	CanConfirmUntil *string                `protobuf:"bytes,3,opt,name=canConfirmUntil,proto3,oneof" json:"canConfirmUntil,omitempty"`
	Merchant        *Merchant              `protobuf:"bytes,4,opt,name=merchant,proto3,oneof" json:"merchant,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *QrCheckoutResp) Reset() {
	*x = QrCheckoutResp{}
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QrCheckoutResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QrCheckoutResp) ProtoMessage() {}

func (x *QrCheckoutResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QrCheckoutResp.ProtoReflect.Descriptor instead.
func (*QrCheckoutResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDescGZIP(), []int{16}
}

func (x *QrCheckoutResp) GetResult() *Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *QrCheckoutResp) GetPaymentAmount() string {
	if x != nil && x.PaymentAmount != nil {
		return *x.PaymentAmount
	}
	return ""
}

func (x *QrCheckoutResp) GetCanConfirmUntil() string {
	if x != nil && x.CanConfirmUntil != nil {
		return *x.CanConfirmUntil
	}
	return ""
}

func (x *QrCheckoutResp) GetMerchant() *Merchant {
	if x != nil {
		return x.Merchant
	}
	return nil
}

var File_specs_proto_kaspi_bridge_kaspi_bridge_proto protoreflect.FileDescriptor

const file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDesc = "" +
	"\n" +
	"+specs/proto/kaspi-bridge/kaspi-bridge.proto\x12\vkaspiBridge\"\x10\n" +
	"\x0eHealthCheckReq\")\n" +
	"\x0fHealthCheckResp\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\"N\n" +
	"\x06Result\x12\x1e\n" +
	"\n" +
	"resultCode\x18\x01 \x01(\tR\n" +
	"resultCode\x12$\n" +
	"\rresultMessage\x18\x02 \x01(\tR\rresultMessage\"\xe3\x01\n" +
	"\bMerchant\x12#\n" +
	"\n" +
	"merchantId\x18\x01 \x01(\tH\x00R\n" +
	"merchantId\x88\x01\x01\x12'\n" +
	"\fmerchantName\x18\x02 \x01(\tH\x01R\fmerchantName\x88\x01\x01\x12%\n" +
	"\vmerchantMCC\x18\x03 \x01(\tH\x02R\vmerchantMCC\x88\x01\x01\x12#\n" +
	"\n" +
	"authorType\x18\x04 \x01(\tH\x03R\n" +
	"authorType\x88\x01\x01B\r\n" +
	"\v_merchantIdB\x0f\n" +
	"\r_merchantNameB\x0e\n" +
	"\f_merchantMCCB\r\n" +
	"\v_authorType\"[\n" +
	"\tQrScanReq\x12\x16\n" +
	"\x06qrCode\x18\x01 \x01(\tR\x06qrCode\x12\x1e\n" +
	"\n" +
	"customerId\x18\x02 \x01(\tR\n" +
	"customerId\x12\x16\n" +
	"\x06locale\x18\x03 \x01(\tR\x06locale\"\x8d\x03\n" +
	"\n" +
	"QrScanResp\x12+\n" +
	"\x06result\x18\x01 \x01(\v2\x13.kaspiBridge.ResultR\x06result\x12#\n" +
	"\n" +
	"qrCodeType\x18\x02 \x01(\tH\x00R\n" +
	"qrCodeType\x88\x01\x01\x126\n" +
	"\bmerchant\x18\x03 \x01(\v2\x15.kaspiBridge.MerchantH\x01R\bmerchant\x88\x01\x01\x127\n" +
	"\apayment\x18\x04 \x01(\v2\x18.kaspiBridge.ScanPaymentH\x02R\apayment\x88\x01\x01\x124\n" +
	"\x06refund\x18\x05 \x01(\v2\x17.kaspiBridge.ScanRefundH\x03R\x06refund\x88\x01\x01\x12C\n" +
	"\vpaymentData\x18\x06 \x01(\v2\x1c.kaspiBridge.ScanPaymentDataH\x04R\vpaymentData\x88\x01\x01B\r\n" +
	"\v_qrCodeTypeB\v\n" +
	"\t_merchantB\n" +
	"\n" +
	"\b_paymentB\t\n" +
	"\a_refundB\x0e\n" +
	"\f_paymentData\"\xbe\x01\n" +
	"\vScanPayment\x12!\n" +
	"\tpaymentId\x18\x01 \x01(\tH\x00R\tpaymentId\x88\x01\x01\x12)\n" +
	"\rpaymentAmount\x18\x02 \x01(\tH\x01R\rpaymentAmount\x88\x01\x01\x12-\n" +
	"\x0fcanConfirmUntil\x18\x03 \x01(\tH\x02R\x0fcanConfirmUntil\x88\x01\x01B\f\n" +
	"\n" +
	"_paymentIdB\x10\n" +
	"\x0e_paymentAmountB\x12\n" +
	"\x10_canConfirmUntil\":\n" +
	"\n" +
	"ScanRefund\x12\x1f\n" +
	"\brefundId\x18\x01 \x01(\tH\x00R\brefundId\x88\x01\x01B\v\n" +
	"\t_refundId\"\xb2\x01\n" +
	"\x0fScanPaymentData\x12!\n" +
	"\tServiceId\x18\x01 \x01(\x03H\x00R\tServiceId\x88\x01\x01\x12%\n" +
	"\vServiceName\x18\x02 \x01(\tH\x01R\vServiceName\x88\x01\x01\x127\n" +
	"\n" +
	"parameters\x18\x03 \x03(\v2\x17.kaspiBridge.ParametersR\n" +
	"parametersB\f\n" +
	"\n" +
	"_ServiceIdB\x0e\n" +
	"\f_ServiceName\"\xc4\x01\n" +
	"\n" +
	"Parameters\x12\x13\n" +
	"\x02Id\x18\x01 \x01(\x03H\x00R\x02Id\x88\x01\x01\x12\x19\n" +
	"\x05regex\x18\x02 \x01(\tH\x01R\x05regex\x88\x01\x01\x12\x19\n" +
	"\x05value\x18\x03 \x01(\tH\x02R\x05value\x88\x01\x01\x12\x17\n" +
	"\x04name\x18\x04 \x01(\tH\x03R\x04name\x88\x01\x01\x12.\n" +
	"\aoptions\x18\x05 \x03(\v2\x14.kaspiBridge.OptionsR\aoptionsB\x05\n" +
	"\x03_IdB\b\n" +
	"\x06_regexB\b\n" +
	"\x06_valueB\a\n" +
	"\x05_name\"\x84\x01\n" +
	"\aOptions\x12\x15\n" +
	"\x03key\x18\x01 \x01(\tH\x00R\x03key\x88\x01\x01\x12\x19\n" +
	"\x05label\x18\x02 \x01(\tH\x01R\x05label\x88\x01\x01\x12%\n" +
	"\vdescription\x18\x03 \x01(\tH\x02R\vdescription\x88\x01\x01B\x06\n" +
	"\x04_keyB\b\n" +
	"\x06_labelB\x0e\n" +
	"\f_description\"\x99\x01\n" +
	"\x12QrNotifyPaymentReq\x12+\n" +
	"\x06result\x18\x01 \x01(\v2\x13.kaspiBridge.ResultR\x06result\x126\n" +
	"\apayment\x18\x02 \x01(\v2\x1c.kaspiBridge.QrNotifyPaymentR\apayment\x12\x1e\n" +
	"\n" +
	"customerId\x18\x03 \x01(\tR\n" +
	"customerId\"\x90\x01\n" +
	"\x0fQrNotifyPayment\x12\x1c\n" +
	"\tpaymentId\x18\x01 \x01(\tR\tpaymentId\x12\"\n" +
	"\fmppPaymentId\x18\x02 \x01(\tR\fmppPaymentId\x12)\n" +
	"\rpaymentAmount\x18\x03 \x01(\tH\x00R\rpaymentAmount\x88\x01\x01B\x10\n" +
	"\x0e_paymentAmount\"\xad\x01\n" +
	"\x13QrNotifyPaymentResp\x12+\n" +
	"\x06result\x18\x01 \x01(\v2\x13.kaspiBridge.ResultR\x06result\x12%\n" +
	"\vlinkReceipt\x18\x02 \x01(\tH\x00R\vlinkReceipt\x88\x01\x01\x12#\n" +
	"\n" +
	"pdfReceipt\x18\x03 \x01(\tH\x01R\n" +
	"pdfReceipt\x88\x01\x01B\x0e\n" +
	"\f_linkReceiptB\r\n" +
	"\v_pdfReceipt\"\xa6\x01\n" +
	"\rQrCheckoutReq\x12\x1c\n" +
	"\tpaymentId\x18\x01 \x01(\tR\tpaymentId\x12\x1c\n" +
	"\tserviceId\x18\x02 \x01(\tR\tserviceId\x12A\n" +
	"\n" +
	"parameters\x18\x03 \x03(\v2!.kaspiBridge.QrCheckoutParametersR\n" +
	"parameters\x12\x16\n" +
	"\x06locale\x18\x04 \x01(\tR\x06locale\"K\n" +
	"\x14QrCheckoutParameters\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x19\n" +
	"\x05value\x18\x02 \x01(\tH\x00R\x05value\x88\x01\x01B\b\n" +
	"\x06_value\"\x82\x02\n" +
	"\x0eQrCheckoutResp\x12+\n" +
	"\x06result\x18\x01 \x01(\v2\x13.kaspiBridge.ResultR\x06result\x12)\n" +
	"\rpaymentAmount\x18\x02 \x01(\tH\x00R\rpaymentAmount\x88\x01\x01\x12-\n" +
	"\x0fcanConfirmUntil\x18\x03 \x01(\tH\x01R\x0fcanConfirmUntil\x88\x01\x01\x126\n" +
	"\bmerchant\x18\x04 \x01(\v2\x15.kaspiBridge.MerchantH\x02R\bmerchant\x88\x01\x01B\x10\n" +
	"\x0e_paymentAmountB\x12\n" +
	"\x10_canConfirmUntilB\v\n" +
	"\t_merchant2\xaf\x02\n" +
	"\vKaspibridge\x12H\n" +
	"\vHealthCheck\x12\x1b.kaspiBridge.HealthCheckReq\x1a\x1c.kaspiBridge.HealthCheckResp\x12E\n" +
	"\n" +
	"QrCheckout\x12\x1a.kaspiBridge.QrCheckoutReq\x1a\x1b.kaspiBridge.QrCheckoutResp\x129\n" +
	"\x06QrScan\x12\x16.kaspiBridge.QrScanReq\x1a\x17.kaspiBridge.QrScanResp\x12T\n" +
	"\x0fQrNotifyPayment\x12\x1f.kaspiBridge.QrNotifyPaymentReq\x1a .kaspiBridge.QrNotifyPaymentRespB\x1aZ\x18specs/proto/kaspi-bridgeb\x06proto3"

var (
	file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDescOnce sync.Once
	file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDescData []byte
)

func file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDescGZIP() []byte {
	file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDescOnce.Do(func() {
		file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDesc), len(file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDesc)))
	})
	return file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDescData
}

var file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_specs_proto_kaspi_bridge_kaspi_bridge_proto_goTypes = []any{
	(*HealthCheckReq)(nil),       // 0: kaspiBridge.HealthCheckReq
	(*HealthCheckResp)(nil),      // 1: kaspiBridge.HealthCheckResp
	(*Result)(nil),               // 2: kaspiBridge.Result
	(*Merchant)(nil),             // 3: kaspiBridge.Merchant
	(*QrScanReq)(nil),            // 4: kaspiBridge.QrScanReq
	(*QrScanResp)(nil),           // 5: kaspiBridge.QrScanResp
	(*ScanPayment)(nil),          // 6: kaspiBridge.ScanPayment
	(*ScanRefund)(nil),           // 7: kaspiBridge.ScanRefund
	(*ScanPaymentData)(nil),      // 8: kaspiBridge.ScanPaymentData
	(*Parameters)(nil),           // 9: kaspiBridge.Parameters
	(*Options)(nil),              // 10: kaspiBridge.Options
	(*QrNotifyPaymentReq)(nil),   // 11: kaspiBridge.QrNotifyPaymentReq
	(*QrNotifyPayment)(nil),      // 12: kaspiBridge.QrNotifyPayment
	(*QrNotifyPaymentResp)(nil),  // 13: kaspiBridge.QrNotifyPaymentResp
	(*QrCheckoutReq)(nil),        // 14: kaspiBridge.QrCheckoutReq
	(*QrCheckoutParameters)(nil), // 15: kaspiBridge.QrCheckoutParameters
	(*QrCheckoutResp)(nil),       // 16: kaspiBridge.QrCheckoutResp
}
var file_specs_proto_kaspi_bridge_kaspi_bridge_proto_depIdxs = []int32{
	2,  // 0: kaspiBridge.QrScanResp.result:type_name -> kaspiBridge.Result
	3,  // 1: kaspiBridge.QrScanResp.merchant:type_name -> kaspiBridge.Merchant
	6,  // 2: kaspiBridge.QrScanResp.payment:type_name -> kaspiBridge.ScanPayment
	7,  // 3: kaspiBridge.QrScanResp.refund:type_name -> kaspiBridge.ScanRefund
	8,  // 4: kaspiBridge.QrScanResp.paymentData:type_name -> kaspiBridge.ScanPaymentData
	9,  // 5: kaspiBridge.ScanPaymentData.parameters:type_name -> kaspiBridge.Parameters
	10, // 6: kaspiBridge.Parameters.options:type_name -> kaspiBridge.Options
	2,  // 7: kaspiBridge.QrNotifyPaymentReq.result:type_name -> kaspiBridge.Result
	12, // 8: kaspiBridge.QrNotifyPaymentReq.payment:type_name -> kaspiBridge.QrNotifyPayment
	2,  // 9: kaspiBridge.QrNotifyPaymentResp.result:type_name -> kaspiBridge.Result
	15, // 10: kaspiBridge.QrCheckoutReq.parameters:type_name -> kaspiBridge.QrCheckoutParameters
	2,  // 11: kaspiBridge.QrCheckoutResp.result:type_name -> kaspiBridge.Result
	3,  // 12: kaspiBridge.QrCheckoutResp.merchant:type_name -> kaspiBridge.Merchant
	0,  // 13: kaspiBridge.Kaspibridge.HealthCheck:input_type -> kaspiBridge.HealthCheckReq
	14, // 14: kaspiBridge.Kaspibridge.QrCheckout:input_type -> kaspiBridge.QrCheckoutReq
	4,  // 15: kaspiBridge.Kaspibridge.QrScan:input_type -> kaspiBridge.QrScanReq
	11, // 16: kaspiBridge.Kaspibridge.QrNotifyPayment:input_type -> kaspiBridge.QrNotifyPaymentReq
	1,  // 17: kaspiBridge.Kaspibridge.HealthCheck:output_type -> kaspiBridge.HealthCheckResp
	16, // 18: kaspiBridge.Kaspibridge.QrCheckout:output_type -> kaspiBridge.QrCheckoutResp
	5,  // 19: kaspiBridge.Kaspibridge.QrScan:output_type -> kaspiBridge.QrScanResp
	13, // 20: kaspiBridge.Kaspibridge.QrNotifyPayment:output_type -> kaspiBridge.QrNotifyPaymentResp
	17, // [17:21] is the sub-list for method output_type
	13, // [13:17] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_specs_proto_kaspi_bridge_kaspi_bridge_proto_init() }
func file_specs_proto_kaspi_bridge_kaspi_bridge_proto_init() {
	if File_specs_proto_kaspi_bridge_kaspi_bridge_proto != nil {
		return
	}
	file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[3].OneofWrappers = []any{}
	file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[5].OneofWrappers = []any{}
	file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[6].OneofWrappers = []any{}
	file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[7].OneofWrappers = []any{}
	file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[8].OneofWrappers = []any{}
	file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[9].OneofWrappers = []any{}
	file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[10].OneofWrappers = []any{}
	file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[12].OneofWrappers = []any{}
	file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[13].OneofWrappers = []any{}
	file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[15].OneofWrappers = []any{}
	file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes[16].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDesc), len(file_specs_proto_kaspi_bridge_kaspi_bridge_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_specs_proto_kaspi_bridge_kaspi_bridge_proto_goTypes,
		DependencyIndexes: file_specs_proto_kaspi_bridge_kaspi_bridge_proto_depIdxs,
		MessageInfos:      file_specs_proto_kaspi_bridge_kaspi_bridge_proto_msgTypes,
	}.Build()
	File_specs_proto_kaspi_bridge_kaspi_bridge_proto = out.File
	file_specs_proto_kaspi_bridge_kaspi_bridge_proto_goTypes = nil
	file_specs_proto_kaspi_bridge_kaspi_bridge_proto_depIdxs = nil
}
