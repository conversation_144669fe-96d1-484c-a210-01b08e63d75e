// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.32.1
// source: specs/proto/kaspi-bridge/kaspi-bridge.proto

package kaspi_bridge

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Kaspibridge_HealthCheck_FullMethodName     = "/kaspiBridge.Kaspibridge/HealthCheck"
	Kaspibridge_QrCheckout_FullMethodName      = "/kaspiBridge.Kaspibridge/QrCheckout"
	Kaspibridge_QrScan_FullMethodName          = "/kaspiBridge.Kaspibridge/QrScan"
	Kaspibridge_QrNotifyPayment_FullMethodName = "/kaspiBridge.Kaspibridge/QrNotifyPayment"
)

// KaspibridgeClient is the client API for Kaspibridge service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type KaspibridgeClient interface {
	HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error)
	QrCheckout(ctx context.Context, in *QrCheckoutReq, opts ...grpc.CallOption) (*QrCheckoutResp, error)
	QrScan(ctx context.Context, in *QrScanReq, opts ...grpc.CallOption) (*QrScanResp, error)
	QrNotifyPayment(ctx context.Context, in *QrNotifyPaymentReq, opts ...grpc.CallOption) (*QrNotifyPaymentResp, error)
}

type kaspibridgeClient struct {
	cc grpc.ClientConnInterface
}

func NewKaspibridgeClient(cc grpc.ClientConnInterface) KaspibridgeClient {
	return &kaspibridgeClient{cc}
}

func (c *kaspibridgeClient) HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResp)
	err := c.cc.Invoke(ctx, Kaspibridge_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *kaspibridgeClient) QrCheckout(ctx context.Context, in *QrCheckoutReq, opts ...grpc.CallOption) (*QrCheckoutResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QrCheckoutResp)
	err := c.cc.Invoke(ctx, Kaspibridge_QrCheckout_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *kaspibridgeClient) QrScan(ctx context.Context, in *QrScanReq, opts ...grpc.CallOption) (*QrScanResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QrScanResp)
	err := c.cc.Invoke(ctx, Kaspibridge_QrScan_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *kaspibridgeClient) QrNotifyPayment(ctx context.Context, in *QrNotifyPaymentReq, opts ...grpc.CallOption) (*QrNotifyPaymentResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QrNotifyPaymentResp)
	err := c.cc.Invoke(ctx, Kaspibridge_QrNotifyPayment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// KaspibridgeServer is the server API for Kaspibridge service.
// All implementations must embed UnimplementedKaspibridgeServer
// for forward compatibility.
type KaspibridgeServer interface {
	HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error)
	QrCheckout(context.Context, *QrCheckoutReq) (*QrCheckoutResp, error)
	QrScan(context.Context, *QrScanReq) (*QrScanResp, error)
	QrNotifyPayment(context.Context, *QrNotifyPaymentReq) (*QrNotifyPaymentResp, error)
	mustEmbedUnimplementedKaspibridgeServer()
}

// UnimplementedKaspibridgeServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedKaspibridgeServer struct{}

func (UnimplementedKaspibridgeServer) HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedKaspibridgeServer) QrCheckout(context.Context, *QrCheckoutReq) (*QrCheckoutResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QrCheckout not implemented")
}
func (UnimplementedKaspibridgeServer) QrScan(context.Context, *QrScanReq) (*QrScanResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QrScan not implemented")
}
func (UnimplementedKaspibridgeServer) QrNotifyPayment(context.Context, *QrNotifyPaymentReq) (*QrNotifyPaymentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QrNotifyPayment not implemented")
}
func (UnimplementedKaspibridgeServer) mustEmbedUnimplementedKaspibridgeServer() {}
func (UnimplementedKaspibridgeServer) testEmbeddedByValue()                     {}

// UnsafeKaspibridgeServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to KaspibridgeServer will
// result in compilation errors.
type UnsafeKaspibridgeServer interface {
	mustEmbedUnimplementedKaspibridgeServer()
}

func RegisterKaspibridgeServer(s grpc.ServiceRegistrar, srv KaspibridgeServer) {
	// If the following call pancis, it indicates UnimplementedKaspibridgeServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Kaspibridge_ServiceDesc, srv)
}

func _Kaspibridge_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KaspibridgeServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kaspibridge_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KaspibridgeServer).HealthCheck(ctx, req.(*HealthCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Kaspibridge_QrCheckout_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QrCheckoutReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KaspibridgeServer).QrCheckout(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kaspibridge_QrCheckout_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KaspibridgeServer).QrCheckout(ctx, req.(*QrCheckoutReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Kaspibridge_QrScan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QrScanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KaspibridgeServer).QrScan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kaspibridge_QrScan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KaspibridgeServer).QrScan(ctx, req.(*QrScanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Kaspibridge_QrNotifyPayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QrNotifyPaymentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KaspibridgeServer).QrNotifyPayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Kaspibridge_QrNotifyPayment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KaspibridgeServer).QrNotifyPayment(ctx, req.(*QrNotifyPaymentReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Kaspibridge_ServiceDesc is the grpc.ServiceDesc for Kaspibridge service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Kaspibridge_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "kaspiBridge.Kaspibridge",
	HandlerType: (*KaspibridgeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HealthCheck",
			Handler:    _Kaspibridge_HealthCheck_Handler,
		},
		{
			MethodName: "QrCheckout",
			Handler:    _Kaspibridge_QrCheckout_Handler,
		},
		{
			MethodName: "QrScan",
			Handler:    _Kaspibridge_QrScan_Handler,
		},
		{
			MethodName: "QrNotifyPayment",
			Handler:    _Kaspibridge_QrNotifyPayment_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "specs/proto/kaspi-bridge/kaspi-bridge.proto",
}
