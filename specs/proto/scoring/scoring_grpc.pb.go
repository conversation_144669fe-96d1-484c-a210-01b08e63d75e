// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.32.1
// source: specs/proto/scoring/scoring.proto

package scoring

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Scoring_HealthCheck_FullMethodName = "/scoring.Scoring/HealthCheck"
)

// ScoringClient is the client API for Scoring service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ScoringClient interface {
	HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error)
}

type scoringClient struct {
	cc grpc.ClientConnInterface
}

func NewScoringClient(cc grpc.ClientConnInterface) ScoringClient {
	return &scoringClient{cc}
}

func (c *scoringClient) HealthCheck(ctx context.Context, in *HealthCheckReq, opts ...grpc.CallOption) (*HealthCheckResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HealthCheckResp)
	err := c.cc.Invoke(ctx, Scoring_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ScoringServer is the server API for Scoring service.
// All implementations must embed UnimplementedScoringServer
// for forward compatibility.
type ScoringServer interface {
	HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error)
	mustEmbedUnimplementedScoringServer()
}

// UnimplementedScoringServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedScoringServer struct{}

func (UnimplementedScoringServer) HealthCheck(context.Context, *HealthCheckReq) (*HealthCheckResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedScoringServer) mustEmbedUnimplementedScoringServer() {}
func (UnimplementedScoringServer) testEmbeddedByValue()                 {}

// UnsafeScoringServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ScoringServer will
// result in compilation errors.
type UnsafeScoringServer interface {
	mustEmbedUnimplementedScoringServer()
}

func RegisterScoringServer(s grpc.ServiceRegistrar, srv ScoringServer) {
	// If the following call pancis, it indicates UnimplementedScoringServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Scoring_ServiceDesc, srv)
}

func _Scoring_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HealthCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScoringServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Scoring_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScoringServer).HealthCheck(ctx, req.(*HealthCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Scoring_ServiceDesc is the grpc.ServiceDesc for Scoring service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Scoring_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "scoring.Scoring",
	HandlerType: (*ScoringServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HealthCheck",
			Handler:    _Scoring_HealthCheck_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "specs/proto/scoring/scoring.proto",
}
