// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v6.32.1
// source: specs/proto/scoring/scoring.proto

package scoring

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HealthCheckReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckReq) Reset() {
	*x = HealthCheckReq{}
	mi := &file_specs_proto_scoring_scoring_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckReq) ProtoMessage() {}

func (x *HealthCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_scoring_scoring_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckReq.ProtoReflect.Descriptor instead.
func (*HealthCheckReq) Descriptor() ([]byte, []int) {
	return file_specs_proto_scoring_scoring_proto_rawDescGZIP(), []int{0}
}

type HealthCheckResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckResp) Reset() {
	*x = HealthCheckResp{}
	mi := &file_specs_proto_scoring_scoring_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResp) ProtoMessage() {}

func (x *HealthCheckResp) ProtoReflect() protoreflect.Message {
	mi := &file_specs_proto_scoring_scoring_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResp.ProtoReflect.Descriptor instead.
func (*HealthCheckResp) Descriptor() ([]byte, []int) {
	return file_specs_proto_scoring_scoring_proto_rawDescGZIP(), []int{1}
}

func (x *HealthCheckResp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

var File_specs_proto_scoring_scoring_proto protoreflect.FileDescriptor

const file_specs_proto_scoring_scoring_proto_rawDesc = "" +
	"\n" +
	"!specs/proto/scoring/scoring.proto\x12\ascoring\"\x10\n" +
	"\x0eHealthCheckReq\")\n" +
	"\x0fHealthCheckResp\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status2K\n" +
	"\aScoring\x12@\n" +
	"\vHealthCheck\x12\x17.scoring.HealthCheckReq\x1a\x18.scoring.HealthCheckRespB\x15Z\x13specs/proto/scoringb\x06proto3"

var (
	file_specs_proto_scoring_scoring_proto_rawDescOnce sync.Once
	file_specs_proto_scoring_scoring_proto_rawDescData []byte
)

func file_specs_proto_scoring_scoring_proto_rawDescGZIP() []byte {
	file_specs_proto_scoring_scoring_proto_rawDescOnce.Do(func() {
		file_specs_proto_scoring_scoring_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_specs_proto_scoring_scoring_proto_rawDesc), len(file_specs_proto_scoring_scoring_proto_rawDesc)))
	})
	return file_specs_proto_scoring_scoring_proto_rawDescData
}

var file_specs_proto_scoring_scoring_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_specs_proto_scoring_scoring_proto_goTypes = []any{
	(*HealthCheckReq)(nil),  // 0: scoring.HealthCheckReq
	(*HealthCheckResp)(nil), // 1: scoring.HealthCheckResp
}
var file_specs_proto_scoring_scoring_proto_depIdxs = []int32{
	0, // 0: scoring.Scoring.HealthCheck:input_type -> scoring.HealthCheckReq
	1, // 1: scoring.Scoring.HealthCheck:output_type -> scoring.HealthCheckResp
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_specs_proto_scoring_scoring_proto_init() }
func file_specs_proto_scoring_scoring_proto_init() {
	if File_specs_proto_scoring_scoring_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_specs_proto_scoring_scoring_proto_rawDesc), len(file_specs_proto_scoring_scoring_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_specs_proto_scoring_scoring_proto_goTypes,
		DependencyIndexes: file_specs_proto_scoring_scoring_proto_depIdxs,
		MessageInfos:      file_specs_proto_scoring_scoring_proto_msgTypes,
	}.Build()
	File_specs_proto_scoring_scoring_proto = out.File
	file_specs_proto_scoring_scoring_proto_goTypes = nil
	file_specs_proto_scoring_scoring_proto_depIdxs = nil
}
