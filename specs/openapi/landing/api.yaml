openapi: 3.0.1

servers:
  - description: Dev server
    url: https://landing-dev.zaman.redmadrobot.com/api/v1
  - description: Stage server
    url: https://landing-stage.zaman.redmadrobot.com/api/v1

info:
  title: Zaman Landing API
  version: dev-0916-08335e49
  description: |
    API для бэкэнда мобильного приложения  Zaman Landing

    Спецификация в формате [swagger](../swagger) или [redoc](../redoc).

    [Changelog](../history/changelog_main.html)

    [Release Changelog](../history/changelog_feature.html)
  termsOfService: https://zaman/terms-of-service
  x-logo:
    url: ../static/logo.png
tags:
  - name: "Системные"
  - name: "Документы"

paths:
  /health:
    $ref: "health.gen.yaml#/health"

  /documents/{docID}/sign-web:
    $ref: "docs.yaml#/docs-by-docID-sign-web"
  /documents/{docID}/sign-confirm-web:
    $ref: "docs.yaml#/docs-by-docID-sign-confirm-web"

components:
  securitySchemes:
    BearerTokenAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    APIError:
        type: object
        description: Общий формат ошибки API. Возвращает цифровой код
        properties:
          error:
            type: string
          fields:
            type: object
            description: Объект с описанием деталей ошибок
            additionalProperties:
              type: string
        required:
          - error
        example:
          error: "X.0"
  parameters:
    AcceptLanguage:
        name: Accept-Language
        in: header
        description: Язык запроса
        required: false
        schema:
          type: string
          enum:
            - kk
            - en
            - ru
          example: ru
          default: kk
