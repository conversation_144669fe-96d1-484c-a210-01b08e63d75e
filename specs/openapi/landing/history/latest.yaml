openapi: 3.0.1
info:
  title: Zaman Landing API
  version: dev-0916-08335e49
  description: |
    API для бэкэнда мобильного приложения  Zaman Landing

    Спецификация в формате [swagger](../swagger) или [redoc](../redoc).

    [Changelog](../history/changelog_main.html)

    [Release Changelog](../history/changelog_feature.html)
  termsOfService: https://zaman/terms-of-service
  x-logo:
    url: ../static/logo.png
servers:
  - description: Dev server
    url: https://landing-dev.zaman.redmadrobot.com/api/v1
  - description: Stage server
    url: https://landing-stage.zaman.redmadrobot.com/api/v1
tags:
  - name: Системные
  - name: Документы
paths:
  /health:
    get:
      tags:
        - Системные
      summary: Проверка на работоспособность
      description: Проверка всех модулей системы на работоспособность
      operationId: health
      security:
        - BearerTokenAuth: []
      responses:
        '200':
          description: Информация по запрошенному статусу микросервисов
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Health'
  /documents/{docID}/sign-web:
    post:
      deprecated: true
      tags:
        - Документы
      summary: Подпись сгенерированного ранее документа
      description: Подпись персонализированного документа из шаблона
      operationId: signDocumentByIDWeb
      security: []
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
        - $ref: '#/components/parameters/DocumentIDPathParam'
      requestBody:
        $ref: '#/components/requestBodies/SignWebBody'
      responses:
        '200':
          description: Код подтверждения для подписи документа отправлен
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OtpResponse'
        '400':
          description: |
            Ошибка инициации процесса подписи
            <br><b>1.0 ValidationError</b> - Ошибка валидации данных в параметрах запроса
            <br><b>3.3 MaxCodeAttemptsExceeded</b> - Превышен лимит количества попыток за период
            <br><b>3.4 NewAttemptTimeNotExceeded</b> - Необходимо подождать, прежде чем запрашивать новый код
            <br><b>3.5 AttemptNotFoundInCache</b> - Попытка не найдена в кэше
            <br><b>4.3 InvalidDocID</b> - Неверный ID документа
            <br><b>4.9 DocumentAlreadySigned</b> - Документ уже подписан
            <br><b>4.12 FailedToSignDocument</b> - Не удалось подписать документ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /documents/{docID}/sign-confirm-web:
    post:
      deprecated: true
      tags:
        - Документы
      summary: Подтверждение подписи документа через ОТП
      description: Подтверждение подписи персонализированного документа из шаблона через ОТП
      operationId: confirmSignDocumentByIDWeb
      security: []
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
        - $ref: '#/components/parameters/DocumentIDPathParam'
      requestBody:
        $ref: '#/components/requestBodies/SignConfirmWebBody'
      responses:
        '200':
          description: Документ подписан, обновленная информация по документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
        '400':
          description: |
            Ошибка подписи документа
            <br><b>3.1 InvalidCode</b> - Неверный код
            <br><b>3.2 MaxCodeChecksExceeded</b> - Превышено количество попыток проверки кода
            <br><b>4.9 DocumentAlreadySigned</b> - Документ уже подписан
            <br><b>4.12 FailedToSignDocument</b> - Не удалось подписать документ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
components:
  securitySchemes:
    BearerTokenAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    APIError:
      type: object
      description: Общий формат ошибки API. Возвращает цифровой код
      properties:
        error:
          type: string
        fields:
          type: object
          description: Объект с описанием деталей ошибок
          additionalProperties:
            type: string
      required:
        - error
      example:
        error: X.0
    Health:
      required:
        - users
        - otp
        - documents
        - notifications
        - keycloakProxy
        - kgdBridge
        - btsBridge
        - smsBridge
        - loans
        - colvirBridge
        - payments
        - cardsAccounts
        - dictionary
        - pkbBridge
        - amlBridge
        - liveness
        - taskManager
        - juicyscoreBridge
        - jiraBridge
        - fileGuard
        - scoring
        - seonBridge
        - sprBridge
        - altScoreBridge
        - qazpostBridge
        - deposits
        - bsasBridge
        - apBridge
        - processingBridge
        - collection
        - paymentsSme
        - referral
        - antifraud
        - crm
        - tokenize
        - balanceUpdater
        - kaspiBridge
        - bitrixBridge
        - foreignActivity
      type: object
      properties:
        users:
          type: boolean
          description: Статус сервиса users
        otp:
          type: boolean
          description: Статус сервиса otp
        documents:
          type: boolean
          description: Статус сервиса documents
        notifications:
          type: boolean
          description: Статус сервиса notifications
        keycloakProxy:
          type: boolean
          description: Статус сервиса keycloakProxy
        kgdBridge:
          type: boolean
          description: Статус сервиса kgdBridge
        btsBridge:
          type: boolean
          description: Статус сервиса btsBridge
        smsBridge:
          type: boolean
          description: Статус сервиса smsBridge
        loans:
          type: boolean
          description: Статус сервиса loans
        colvirBridge:
          type: boolean
          description: Статус сервиса colvirBridge
        payments:
          type: boolean
          description: Статус сервиса payments
        cardsAccounts:
          type: boolean
          description: Статус сервиса cardsAccounts
        dictionary:
          type: boolean
          description: Статус сервиса dictionary
        pkbBridge:
          type: boolean
          description: Статус сервиса pkbBridge
        amlBridge:
          type: boolean
          description: Статус сервиса amlBridge
        liveness:
          type: boolean
          description: Статус сервиса liveness
        taskManager:
          type: boolean
          description: Статус сервиса taskManager
        juicyscoreBridge:
          type: boolean
          description: Статус сервиса juicyscoreBridge
        jiraBridge:
          type: boolean
          description: Статус сервиса jiraBridge
        fileGuard:
          type: boolean
          description: Статус сервиса fileGuard
        scoring:
          type: boolean
          description: Статус сервиса scoring
        seonBridge:
          type: boolean
          description: Статус сервиса seonBridge
        sprBridge:
          type: boolean
          description: Статус сервиса sprBridge
        altScoreBridge:
          type: boolean
          description: Статус сервиса altScoreBridge
        qazpostBridge:
          type: boolean
          description: Статус сервиса qazpostBridge
        deposits:
          type: boolean
          description: Статус сервиса deposits
        bsasBridge:
          type: boolean
          description: Статус сервиса bsasBridge
        apBridge:
          type: boolean
          description: Статус сервиса apBridge
        processingBridge:
          type: boolean
          description: Статус сервиса processingBridge
        collection:
          type: boolean
          description: Статус сервиса collection
        paymentsSme:
          type: boolean
          description: Статус сервиса paymentsSme
        referral:
          type: boolean
          description: Статус сервиса referral
        antifraud:
          type: boolean
          description: Статус сервиса antifraud
        crm:
          type: boolean
          description: Статус сервиса crm
        tokenize:
          type: boolean
          description: Статус сервиса tokenize
        balanceUpdater:
          type: boolean
          description: Статус сервиса balanceUpdater
        kaspiBridge:
          type: boolean
          description: Статус сервиса kaspiBridge
        bitrixBridge:
          type: boolean
          description: Статус сервиса bitrixBridge
        foreignActivity:
          type: boolean
          description: Статус сервиса foreignActivity
    OtpResponse:
      type: object
      required:
        - attemptID
        - retryTime
      properties:
        attemptID:
          type: string
          format: uuid
          description: Идентификатор попытки для проверки кода
        retryTime:
          type: integer
          description: Количество секунд до следующей отправки
          example: 60
    DocumentType:
      type: string
      enum:
        - personalDataAgreementSMEIP
      description: Тип (шаблон) документа
    Document:
      required:
        - ID
        - title
        - type
        - version
        - fileLink
        - signed
      type: object
      properties:
        ID:
          type: string
          format: uuid
          description: Идентификатор документа
        title:
          type: string
          description: Название документа
        type:
          $ref: '#/components/schemas/DocumentType'
        version:
          type: integer
          description: Версия документа, которая соотносится с типом документа (версия шаблона)
        fileLink:
          type: string
          description: Ссылка на физический файл
        signed:
          type: boolean
          description: Был ли документ подписан пользователем
  parameters:
    AcceptLanguage:
      name: Accept-Language
      in: header
      description: Язык запроса
      required: false
      schema:
        type: string
        enum:
          - kk
          - en
          - ru
        example: ru
        default: kk
    DocumentIDPathParam:
      name: docID
      in: path
      description: Идентификатор запрашиваемого документа
      required: true
      schema:
        type: string
        format: uuid
        pattern: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
        maxLength: 36
  requestBodies:
    SignWebBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              phoneNumber:
                type: string
                description: Номер телефона
                pattern: ^\+?[1-9]\d{10,10}$
                example: '+78889991100'
            required:
              - phoneNumber
    SignConfirmWebBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              phoneNumber:
                type: string
                description: Номер телефона
                pattern: ^\+?[1-9]\d{10,10}$
                example: '+78889991100'
              code:
                type: string
                description: Код подтверждения
                example: '3223'
              attemptID:
                type: string
                description: Идентификатор попытки для ввода кода
                format: uuid
            required:
              - phoneNumber
              - code
              - attemptID
