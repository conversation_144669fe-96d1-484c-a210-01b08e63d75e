<html>

<head>
    <style>

        @import url(//fonts.googleapis.com/css?family=Nunito);

        * {
            font-family: 'Nunito','Helvetica Neue',Helvetica,Arial,sans-serif;
        }

        .title {
            margin: 1em 0 0.5em 0;
            font-size: 36px;
        }

        .path {
            color: #016BF8;
            font-size: 18px;
            font-weight: 600;
        }

        .endpoint {
            color: #21313c;
            line-height: 24px;
            margin: 22px 0;
        }

        .endpoint-header {
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .change-type {
            box-sizing: border-box;
            font-weight: 700;
            font-size: 12px;
            line-height: 16px;
            border-radius: 5px;
            height: 18px;
            padding-left: 6px;
            padding-right: 6px;
            text-transform: uppercase;
            border: 1px solid;
            letter-spacing: 1px;
            background-color: #E3FCF7;
            border-color: #C0FAE6;
            color: #00684A;
            margin-top: 2px;
        }

        .change {
        }

        .breaking {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            margin-right: 5px;
        }

        .breaking-icon {
            color: #DB3030;
        }

        .endpoint-changes {
        }

        .tooltip {
            position:relative;
        }

        .tooltip:before {
            content: attr(data-text);
            position:absolute;


            top:50%;
            transform:translateY(-50%);


            left:100%;
            margin-left:15px;


            width:200px;
            padding:10px;
            border-radius:10px;
            background:#000;
            color: #fff;
            text-align:center;

            display:none;
        }

        .tooltip:hover:before {
            display:block;
        }
    </style>
</head>

<body>
	<div class="title">API Changelog dev-0915-34314d22 vs. dev-0916-08335e49</div>

	<div class="title">API Changelog dev-0912-2bc22638 vs. dev-0915-34314d22</div>

    <div class="endpoint">
        <div class="endpoint-header">
            <span class="path">
                <div class="">POST /account/application-for-sign</div>
            </span>
            <div class="change-type">Updated</div>
        </div>
        <ul class="endpoint-changes">

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            API path удалён без процедуры deprecation
            </li>

        </ul>
    </div>

    <div class="endpoint">
        <div class="endpoint-header">
            <span class="path">
                <div class="">POST /accounts/application-for-sign</div>
            </span>
            <div class="change-type">Updated</div>
        </div>
        <ul class="endpoint-changes">

            <li class="change">

            эндпоинт добавлен
            </li>

        </ul>
    </div>

    <div class="endpoint">
        <div class="endpoint-header">
            <span class="path">
                <div class="">GET /user/deposits/{depositID}</div>
            </span>
            <div class="change-type">Updated</div>
        </div>
        <ul class="endpoint-changes">

            <li class="change">

            добавлено необязательное свойство &#39;accDepRet&#39; в ответе со статусом &#39;200&#39;
            </li>

        </ul>
    </div>

	<div class="title">API Changelog dev-0911-404023ab vs. dev-0912-2bc22638</div>

	<div class="title">API Changelog dev-0909-3279d8c0 vs. dev-0911-404023ab</div>

    <div class="endpoint">
        <div class="endpoint-header">
            <span class="path">
                <div class="">PUT /loans/{applicationID}/application</div>
            </span>
            <div class="change-type">Updated</div>
        </div>
        <ul class="endpoint-changes">

            <li class="change">

            эндпоинт добавлен
            </li>

        </ul>
    </div>

	<div class="title">API Changelog dev-0908-41fb7393 vs. dev-0909-3279d8c0</div>

    <div class="endpoint">
        <div class="endpoint-header">
            <span class="path">
                <div class="">GET /health</div>
            </span>
            <div class="change-type">Updated</div>
        </div>
        <ul class="endpoint-changes">

            <li class="change">

            добавил требуемое свойство &#39;foreignActivity&#39; в ответ со статусом &#39;200&#39;
            </li>

        </ul>
    </div>

    <div class="endpoint">
        <div class="endpoint-header">
            <span class="path">
                <div class="">POST /tokenize</div>
            </span>
            <div class="change-type">Updated</div>
        </div>
        <ul class="endpoint-changes">

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;OPC&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;encryptedPayload&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;issuerAppId&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;productId&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;publicKeyId&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            добавил требуемое свойство &#39;issuerId&#39; в ответ со статусом &#39;200&#39;
            </li>

        </ul>
    </div>

	<div class="title">API Changelog dev-0905-318daeb6 vs. dev-0908-41fb7393</div>

    <div class="endpoint">
        <div class="endpoint-header">
            <span class="path">
                <div class="">POST /loans/{applicationID}/eds</div>
            </span>
            <div class="change-type">Updated</div>
        </div>
        <ul class="endpoint-changes">

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            добавлено новое enum значение &#39;CNY&#39; в поле ответа &#39;amount/currencyCode&#39; для ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            добавлено новое enum значение &#39;EUR&#39; в поле ответа &#39;amount/currencyCode&#39; для ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            добавлено новое enum значение &#39;KZT&#39; в поле ответа &#39;amount/currencyCode&#39; для ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            добавлено новое enum значение &#39;RUB&#39; в поле ответа &#39;amount/currencyCode&#39; для ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            добавлено новое enum значение &#39;USD&#39; в поле ответа &#39;amount/currencyCode&#39; для ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            добавлено необязательное свойство &#39;refinancing&#39; в ответе со статусом &#39;200&#39;
            </li>

            <li class="change">

            добавил требуемое свойство &#39;productType&#39; в ответ со статусом &#39;200&#39;
            </li>

        </ul>
    </div>

    <div class="endpoint">
        <div class="endpoint-header">
            <span class="path">
                <div class="">GET /loans/{applicationID}/scoring-result</div>
            </span>
            <div class="change-type">Updated</div>
        </div>
        <ul class="endpoint-changes">

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;refinancing/conditions/ID&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;refinancing/externalBankLoansInfo/items/items/ID&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            добавил требуемое свойство &#39;refinancing/externalBankLoansInfo/totalOutstandingAmount&#39; в ответ со статусом &#39;200&#39;
            </li>

        </ul>
    </div>

	<div class="title">API Changelog dev-0904-7c34a26c vs. dev-0905-318daeb6</div>

    <div class="endpoint">
        <div class="endpoint-header">
            <span class="path">
                <div class="">GET /cards/{cardId}/requisites</div>
            </span>
            <div class="change-type">Updated</div>
        </div>
        <ul class="endpoint-changes">

            <li class="change">

            эндпоинт добавлен
            </li>

        </ul>
    </div>

	<div class="title">API Changelog dev-0903-15a15c85 vs. dev-0904-7c34a26c</div>

    <div class="endpoint">
        <div class="endpoint-header">
            <span class="path">
                <div class="">POST /card/{cardId}</div>
            </span>
            <div class="change-type">Updated</div>
        </div>
        <ul class="endpoint-changes">

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            API path удалён без процедуры deprecation
            </li>

        </ul>
    </div>

    <div class="endpoint">
        <div class="endpoint-header">
            <span class="path">
                <div class="">GET /card/{cardId}/tokenize/state</div>
            </span>
            <div class="change-type">Updated</div>
        </div>
        <ul class="endpoint-changes">

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            API path удалён без процедуры deprecation
            </li>

        </ul>
    </div>

    <div class="endpoint">
        <div class="endpoint-header">
            <span class="path">
                <div class="">GET /deposits/utils/calculate-profit</div>
            </span>
            <div class="change-type">Updated</div>
        </div>
        <ul class="endpoint-changes">

            <li class="change">

            эндпоинт добавлен
            </li>

        </ul>
    </div>

    <div class="endpoint">
        <div class="endpoint-header">
            <span class="path">
                <div class="">POST /tokenize</div>
            </span>
            <div class="change-type">Updated</div>
        </div>
        <ul class="endpoint-changes">

            <li class="change">

            эндпоинт добавлен
            </li>

        </ul>
    </div>

    <div class="endpoint">
        <div class="endpoint-header">
            <span class="path">
                <div class="">POST /tokenize/state</div>
            </span>
            <div class="change-type">Updated</div>
        </div>
        <ul class="endpoint-changes">

            <li class="change">

            эндпоинт добавлен
            </li>

        </ul>
    </div>

    <div class="endpoint">
        <div class="endpoint-header">
            <span class="path">
                <div class="">GET /tokenize/{cardId}/info</div>
            </span>
            <div class="change-type">Updated</div>
        </div>
        <ul class="endpoint-changes">

            <li class="change">

            эндпоинт добавлен
            </li>

        </ul>
    </div>

    <div class="endpoint">
        <div class="endpoint-header">
            <span class="path">
                <div class="">GET /tokenize/{cardId}/state/update</div>
            </span>
            <div class="change-type">Updated</div>
        </div>
        <ul class="endpoint-changes">

            <li class="change">

            эндпоинт добавлен
            </li>

        </ul>
    </div>

	<div class="title">API Changelog dev-0902-55d4e52e vs. dev-0903-15a15c85</div>

    <div class="endpoint">
        <div class="endpoint-header">
            <span class="path">
                <div class="">POST /payments/create-payment-kaspiqr</div>
            </span>
            <div class="change-type">Updated</div>
        </div>
        <ul class="endpoint-changes">

            <li class="change">

            эндпоинт добавлен
            </li>

        </ul>
    </div>

    <div class="endpoint">
        <div class="endpoint-header">
            <span class="path">
                <div class="">POST /payments/qr-session-termination</div>
            </span>
            <div class="change-type">Updated</div>
        </div>
        <ul class="endpoint-changes">

            <li class="change">

            эндпоинт добавлен
            </li>

        </ul>
    </div>

    <div class="endpoint">
        <div class="endpoint-header">
            <span class="path">
                <div class="">POST /payments/qr-token</div>
            </span>
            <div class="change-type">Updated</div>
        </div>
        <ul class="endpoint-changes">

            <li class="change">

            эндпоинт добавлен
            </li>

        </ul>
    </div>

	<div class="title">API Changelog dev-0901-9908578d vs. dev-0902-55d4e52e</div>

	<div class="title">API Changelog dev-0829-f9371efa vs. dev-0901-9908578d</div>

	<div class="title">API Changelog 1.1.0 vs. dev-0829-f9371efa</div>

    <div class="endpoint">
        <div class="endpoint-header">
            <span class="path">
                <div class="">GET /health</div>
            </span>
            <div class="change-type">Updated</div>
        </div>
        <ul class="endpoint-changes">

            <li class="change">

            добавил требуемое свойство &#39;bitrixBridge&#39; в ответ со статусом &#39;200&#39;
            </li>

        </ul>
    </div>

	</body>

</html>

