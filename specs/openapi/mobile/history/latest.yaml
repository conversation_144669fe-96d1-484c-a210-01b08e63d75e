openapi: 3.0.1
info:
  title: Zaman Mobile API
  version: dev-0916-08335e49
  description: |
    API для бэкэнда мобильного приложения Zaman

    Спецификация в формате [swagger](../swagger) или [redoc](../redoc).

    Любой ответ сервера может содержать коды 1.X (где Х - цифровой код ошибки) и X.0 (где X - цифровой ID сервиса).
    [Справочник ошибок](../errors)

    Хэдер accept-language используется для переключения языка ответа сервера.
    Подробнее см. [Accept-Language](#component-parameters-AcceptLanguage).

    [Запрос выборки данных из словаря](../dictionary)

    [Changelog](../history/changelog_main.html)

    [Release Changelog](../history/changelog_feature.html)
  termsOfService: https://www.zamanbank.kz/useful-info/
  x-logo:
    url: ../static/logo.png
servers:
  - description: Dev server
    url: https://mobile-dev.zaman.redmadrobot.com/api/v1
  - description: Stage server
    url: https://mobile.zaman.redmadrobot.com/api/v1
tags:
  - name: Системные
  - name: Авторизация
  - name: Профиль
  - name: Документы
  - name: Карты и счета
  - name: Общее
  - name: Кредиты
  - name: Справочники
  - name: Платежи
  - name: Таск менеджер
  - name: Токенизация карт
  - name: Депозиты
  - name: File Guard
  - name: Реферальная программа
paths:
  /health:
    get:
      tags:
        - Системные
      summary: Проверка на работоспособность
      description: Проверка всех модулей системы на работоспособность
      operationId: health
      security:
        - BearerTokenAuth: []
      responses:
        '200':
          description: Информация по запрошенному статусу микросервисов
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Health'
  /auth/login:
    post:
      tags:
        - Авторизация
      summary: Авторизация пользователя по телефону
      description: Проверка существования пользователя по телефону, доп проверки, авторизация
      operationId: authLogin
      security: []
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
        - $ref: '#/components/parameters/EntryPointHeader'
      requestBody:
        $ref: '#/components/requestBodies/LogInBody'
      responses:
        '200':
          description: Пользователь найден, отправлен код подтверждения для входа
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OtpResponse'
        '400':
          description: |
            <br>Ошибка запроса авторизации
            <br><b>1.0 ValidationError</b> - Ошибка валидации данных в теле запроса
            <br><b>2.1 UserBlocked</b> - Пользователь заблокирован
            <br><b>2.2 UserPhoneIinMismatch</b> - Не совпадение номера ИИН и номера телефона пользователя
            <br><b>3.3 OtpMaxCodeAttemptsExceeded</b> - Превышен лимит количества попыток за период
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/APIError'
                  - $ref: '#/components/schemas/ValidationError'
  /auth/logout:
    post:
      tags:
        - Авторизация
      summary: Выход из системы
      description: Выход пользователя из системы, удаление токенов. Используется в том числе при сценарии "забыл пин"
      operationId: authLogout
      security: []
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
      requestBody:
        $ref: '#/components/requestBodies/LogoutBody'
      responses:
        '200':
          $ref: '#/components/responses/RespEmpty'
        '400':
          description: |
            <br>Ошибка запроса выхода пользователя
            <br><b>1.0 ValidationError</b> - Ошибка валидации данных в теле запроса
  /auth/confirm:
    post:
      tags:
        - Авторизация
      operationId: authConfirm
      summary: Подтверждение авторизации
      description: Ввод кода из СМС/... , создание токенов доступа
      security: []
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
      requestBody:
        $ref: '#/components/requestBodies/ConfirmRequestBody'
      responses:
        '200':
          description: Номер подтвержден, сгенерированы токены доступа
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthConfirmResponse'
        '400':
          description: |
            Ошибка авторизации
            <br><b>1.0 ValidationError</b> - Ошибка в теле запроса
            <br><b>2.15 UserDebtsCheckError</b> - Проверка задолженностей пользователя не пройдена - в ответе json объект "fields": {"debts_amount": "some num"}
            <br><b>2.16 TaxPayerInactive</b> - Проверка действующего налогоплательщика не пройдена
            <br><b>2.29 AntiFraudCheckFailed</b> - пользователь не прошел проверку антифрода.
            <br><b>3.1 InvalidOTP</b> - Ошибка ввода кода ОТП
            <br><b>3.2 OtpMaxCodeChecksExceeded</b> - Превышено количество попыток проверки кода
            <br><b>3.5 OtpAttemptNotFound</b> - Сценарий проверки ОТП кода не найден или истёк
            <br><b>8.1 CitsDebtsEmptyResponse</b> - Пустой ответ от PKB по запросу о задолженностях
            <br><b>8.2 CitsDebtsCodeNotCompleted</b> - Код ответа от PKB по запросу о задолженностях не валиден
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/APIError'
                  - $ref: '#/components/schemas/ValidationError'
  /auth/refresh:
    post:
      tags:
        - Авторизация
      description: Обновление токенов пользователя
      summary: Обновление токенов пользователя
      operationId: authRefresh
      security: []
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
      requestBody:
        $ref: '#/components/requestBodies/AuthRefreshBody'
      responses:
        '200':
          description: Токены доступа обновлены
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthRefreshResponse'
        '400':
          description: |
            Ошибка авторизации
            <br><b>1.0 ValidationError</b> - Ошибка в теле запроса
            <br><b>1.1 ValidationError</b> - Требуется аутентификация
            <br><b>2.1 UserBlocked</b> - Пользователь заблокирован
            <br><b>2.3 TokenRefreshForbidden</b> - Обновление токена запрещено
            <br><b>6.1 InvalidRefreshToken</b> - Недействительный refresh token
  /auth/bts-data:
    get:
      tags:
        - Авторизация
      summary: Метод для получения ссылки liveness
      description: |
        Метод для получения ссылки liveness для прохождения флоу авторизации
      operationId: getBtsDataForAuth
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
      responses:
        '200':
          description: Данные со сгенерированной ссылкой и ссылкой для редиректа
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BtsDataForAuthResp'
        '400':
          description: |
            Ошибка авторизации
            <br><b>1.0 ValidationError</b> - Ошибка в теле запроса
            <br><b>1.1 ValidationError</b> - Требуется аутентификация
  /auth/identify:
    post:
      tags:
        - Авторизация
      summary: Идентификация пользователя
      description: Идентификация пользователя по коду BTS
      operationId: authIdentify
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
      requestBody:
        $ref: '#/components/requestBodies/IdentifyBody'
      responses:
        '200':
          description: Пользователь идентифицирован
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthRefreshResponse'
        '400':
          description: |
            Ошибка идентификации
            <br><b>1.0 ValidationError</b> - Ошибка в теле запроса
            <br><b>1.3 Forbidden</b> - Доступ запрещен
            <br><b>2.10 CitizenshipCheckFailed</b> - Проверка гражданства не пройдена
            <br><b>2.11 AgeCheckFailed</b> - Проверка возраста не пройдена
            <br><b>2.12 UserVerifyFailed</b> - Верификация пользователя не пройдена
            <br><b>2.13 PersonalDocumentInvalidStatus</b> - Документ удостоверяющий личность не действителен
            <br><b>2.14 ValidPersonalDocumentNotFound</b> - Необходимый документ удостоверяющий личность не найден
            <br><b>2.21 AmlCheckFailed</b> - Проверка AML не пройдена, пользователь заблокирован
            <br><b>2.26 LivenessPassedByQRLink</b> - Верификация пользователя пройдена по QR ссылке на другом устройстве
            <br><b>2.30 RegAddressInvalidityCheckFailed</b> - Проверка валидности адреса регистрации не пройдена
            <br><b>8.4 IINMismatch</b> - ИНН в запросе не совпадает с ИНН в полученном ID токене
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/APIError'
                  - $ref: '#/components/schemas/ValidationError'
  /auth/document-for-sign:
    post:
      tags:
        - Авторизация
      summary: Метод для генерации и получения документа для подписи
      description: |
        Метод для генерации и получения документа для подписи. Определяет тип документа в зависимости от статуса пользователя.
      operationId: documentForSign
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      responses:
        '200':
          description: Информация по запрошенному документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentsForSignResponse'
        '400':
          description: |
            Ошибка генерации документа
            <br><b>4.1 TemplateNotFound</b> - Шаблон не найден
            <br><b>4.10 FailedToGenerateFromTemplate</b> - Не удалось сгенерировать документ из шаблона
            <br><b>4.11 FailedToUploadFile</b> - Не удалось загрузить файл
            <br><b>2.19 ClientCardDuplicate</b> - Найден дубль карточки клиента
            <br><b>2.20 AccountArrested</b> - Ошибка ограничения на действующем счете
  /documents/public:
    post:
      tags:
        - Документы
      summary: Метод для получения документов, доступных для просмотра всем
      description: |
        Метод для получения документов, доступных для просмотра всем
        personalDataAgreement - документ персональных данных
      operationId: requestDocumentPublic
      security: []
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
        - name: docType
          in: query
          description: Тип запрашиваемого документа
          required: true
          schema:
            $ref: '#/components/schemas/PublicDocumentType'
      responses:
        '200':
          description: Информация по запрошенному документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PublicDocument'
        '400':
          description: |
            Ошибка инициализации процесса подписи
            <br><b>4.1 InvalidTemplateType</b> - Неверный тип шаблона
            <br><b>4.8 UserInfoMismatch</b> - Информация о пользователя не корректна
            <br><b>4.10 FailedToGenerateFromTemplate</b> - Не удалось сгенерировать документ из шаблона
  /documents/{docID}:
    get:
      tags:
        - Документы
      summary: Просмотр сгенерированного ранее документа
      description: |
        Просмотр сгенерированного ранее из шаблона документа.
        Метод приватный, т.к. требует авторизации.
        Для просмотра публичных документов используется метод /documents/public
      operationId: getDocumentByID
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
        - $ref: '#/components/parameters/DocumentIDPathParam'
      responses:
        '200':
          description: Информация по запрошенному документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
        '400':
          description: |
            Ошибка инициации процесса подписи
            <br><b>4.3 InvalidDocID</b> - Неверный ID документа
            <br><b>4.8 UserInfoMismatch</b> - Информация о пользователя не корректна
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /documents/{docID}/sign:
    post:
      deprecated: true
      tags:
        - Документы
      summary: Подпись сгенерированного ранее документа
      description: Подпись персонализированного документа из шаблона
      operationId: signDocumentByID
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
        - $ref: '#/components/parameters/DocumentIDPathParam'
      responses:
        '200':
          description: Код подтверждения для подписи документа отправлен
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OtpResponse'
        '400':
          description: |
            Ошибка инициации процесса подписи
            <br><b>1.0 ValidationError</b> - Ошибка валидации данных в параметрах запроса
            <br><b>3.3 MaxCodeAttemptsExceeded</b> - Превышен лимит количества попыток за период
            <br><b>3.4 NewAttemptTimeNotExceeded</b> - Необходимо подождать, прежде чем запрашивать новый код
            <br><b>3.5 AttemptNotFoundInCache</b> - Попытка не найдена в кэше
            <br><b>4.3 InvalidDocID</b> - Неверный ID документа
            <br><b>4.9 DocumentAlreadySigned</b> - Документ уже подписан
            <br><b>4.12 FailedToSignDocument</b> - Не удалось подписать документ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /documents/sign:
    post:
      tags:
        - Документы
      summary: Отправка ОТП для подтверждения подписания
      description: Метод используется для отправки ОТП для подтверждения подписания переданных документов
      operationId: signDocumentByIDs
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
      requestBody:
        $ref: '#/components/requestBodies/SignDocumentsByIDReqBody'
      responses:
        '200':
          description: Код подтверждения для подписи документа отправлен
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OtpResponse'
        '400':
          description: |
            Ошибка подписи документа
            <br><b>3.2 MaxCodeChecksExceeded</b> - Превышено количество попыток проверки кода
            <br><b>3.3 MaxCodeAttemptsExceeded</b> - Превышен лимит количества попыток за период
            <br><b>4.3 InvalidDocID</b> - Неверный ID документа
            <br><b>4.9 DocumentAlreadySigned</b> - Документ уже подписан
            <br><b>4.12 FailedToSignDocument</b> - Не удалось подписать документ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /documents/{docID}/sign-confirm:
    post:
      deprecated: true
      tags:
        - Документы
      summary: Подтверждение подписи документа через ОТП
      description: Подтверждение подписи персонализированного документа из шаблона через ОТП
      operationId: confirmSignDocumentByID
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
        - $ref: '#/components/parameters/DocumentIDPathParam'
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      requestBody:
        $ref: '#/components/requestBodies/OtpBody'
      responses:
        '200':
          description: Документ подписан, обновленная информация по документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
        '400':
          description: |
            Ошибка подписи документа
            <br><b>3.1 InvalidCode</b> - Неверный код
            <br><b>3.2 MaxCodeChecksExceeded</b> - Превышено количество попыток проверки кода
            <br><b>3.5 AttemptNotFound</b> - Сценарий проверки ОТП кода не найден или истёк
            <br><b>4.9 DocumentAlreadySigned</b> - Документ уже подписан
            <br><b>4.12 FailedToSignDocument</b> - Не удалось подписать документ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /documents/sign-confirm:
    post:
      tags:
        - Документы
      summary: Принятие кода ОТП по подписанию переданных документов
      description: Принятие кода ОТП по подписанию переданных документов и при успешной проверке ОТП сохраняет документ в БД
      operationId: confirmSignDocuments
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      requestBody:
        $ref: '#/components/requestBodies/ConfirmSignDocumentsReqBody'
      responses:
        '200':
          description: Документ подписан, обновленная информация по документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfirmSignDocumentsResponse'
        '400':
          description: |
            Ошибка подписи документа
            <br><b>3.1 InvalidCode</b> - Неверный код
            <br><b>3.2 MaxCodeChecksExceeded</b> - Превышено количество попыток проверки кода
            <br><b>3.5 AttemptNotFound</b> - Сценарий проверки ОТП кода не найден или истёк
            <br><b>4.9 DocumentAlreadySigned</b> - Документ уже подписан
            <br><b>4.12 FailedToSignDocument</b> - Не удалось подписать документ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /documents/batch-sign-confirm:
    post:
      deprecated: true
      tags:
        - Документы
      summary: Подтверждение подписи нескольких документов через ОТП
      description: Подтверждение подписи нескольких документов через ОТП
      operationId: confirmSignDocumentsBatch
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      requestBody:
        $ref: '#/components/requestBodies/BatchDocsOtpBody'
      responses:
        '200':
          description: Документы подписаны, обновленная информация по документам
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SignedDocumentsBatchResponse'
        '400':
          description: |
            Ошибка подписи документа
            <br><b>3.1 InvalidCode</b> - Неверный код
            <br><b>3.2 MaxCodeChecksExceeded</b> - Превышено количество попыток проверки кода
            <br><b>4.9 DocumentAlreadySigned</b> - Документ уже подписан
            <br><b>4.12 FailedToSignDocument</b> - Не удалось подписать документ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /accounts/documents:
    post:
      tags:
        - Карты и счета
      summary: Метод для получения документов из счетов
      description: Метод для получения документов из счетов
      operationId: createAccountsDocument
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      parameters:
        - name: docType
          in: query
          description: Тип запрашиваемого документа
          required: true
          schema:
            $ref: '#/components/schemas/AccountDocumentType'
      requestBody:
        $ref: '#/components/requestBodies/AccountDocumentRequest'
      responses:
        '200':
          description: Информация по запрошенному документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountDocumentResponse'
        '400':
          description: |
            Ошибка инициации процесса подписи
            <br><b>11.1 TemplateNotFound</b> - Шаблон не найден
            <br><b>11.4 InvalidTemplateType</b> - Неверный тип шаблона
  /otp/retry:
    post:
      tags:
        - Общее
      summary: Перезапрос кода подтверждения
      description: Перезапрос кода подтверждения
      operationId: otpRetry
      security:
        - BearerTokenAuth: []
      requestBody:
        $ref: '#/components/requestBodies/OtpRetryBody'
      responses:
        '200':
          description: Код подтверждения успешно отправлен
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OtpResponse'
        '400':
          description: |
            Ошибка перезапроса кода подтверждения
            <br><b>1.0 ValidationError</b> - Ошибка валидации данных в теле запроса
            <br><b>3.3 MaxCodeAttemptsExceeded</b> - Превышен лимит количества попыток ОТП за период
            <br><b>3.5 NotFound</b> - Объект не найден
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/APIError'
                  - $ref: '#/components/schemas/ValidationError'
  /user/cards:
    get:
      tags:
        - Карты и счета
      summary: Список текущих карт и счетов пользователя
      description: Список текущих карт и счетов пользователя
      operationId: userCards
      security:
        - BearerTokenAuth:
            - active
      responses:
        '200':
          description: Данные о продуктах
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserCardsResponse'
        '400':
          description: |
            Если ошибка с самой картой, это будет отражено в теле 200ки, специфичных ошибок пока не ожидаем.
            Глубже не прорабатывалось пока, соряшки :(
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /user/accounts/{accountID}:
    get:
      tags:
        - Карты и счета
      summary: Получение счета пользователя
      description: Получение счета пользователя
      operationId: getUserAccounts
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/AccountIDPathParam'
      responses:
        '200':
          description: Счета пользователя
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserAccountResponse'
        '400':
          description: |
            Если ошибка с самой картой, это будет отражено в теле 200ки, специфичных ошибок пока не ожидаем.
            Глубже не прорабаты
  /user/loans:
    get:
      tags:
        - Кредиты
      summary: Получение информации о кредитах пользователя
      description: Получение информации о кредитах пользователя
      operationId: getLoans
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - in: header
          name: Accept-Language
          description: Язык запроса
          required: true
          schema:
            type: string
            enum:
              - kk
              - en
              - ru
            example: ru
            default: kk
      responses:
        '200':
          description: Список кредитов, соответствующих фильтрам
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoansResponse'
        '400':
          description: |
            Ошибка получения данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /user/locale:
    post:
      tags:
        - Профиль
      summary: Метод для обновления локализации пользователя
      description: |
        Метод для обновления локализации пользователя
      operationId: usersUpdateUserLocale
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      parameters:
        - name: Accept-Language
          in: header
          required: true
          schema:
            type: string
            enum:
              - ru
              - kk
              - en
            default: kk
          description: Язык запроса
      responses:
        '200':
          description: Успех обновления локализации пользователя
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateUserLocaleResp'
        '400':
          description: |
            Ошибка получения данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /user/profile:
    post:
      tags:
        - Профиль
      summary: Удаление профиля из системы
      description: Выход пользователя из системы и удаление профиля
      operationId: profileDelete
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/ProfileDeleteBody'
      responses:
        '200':
          $ref: '#/components/responses/RespEmpty'
        '400':
          description: |
            <br>Ошибка запроса удаления профиля
            <br><b>1.0 ValidationError</b> - Ошибка валидации данных в теле запроса
  /accounts/application-for-sign:
    post:
      tags:
        - Карты и счета
      summary: Генерация документа для выпуска виртуальной карты
      description: |
        Метод для проверки пользователя и генерации документа для подписи.
      operationId: applicationForSign
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      requestBody:
        $ref: '#/components/requestBodies/ApplicationForSignRequest'
      responses:
        '200':
          description: Информация по запрошенному документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApplicationForSignResponse'
        '400':
          description: |
            Ошибка генерации документа
            <br><b>13.1 TemplateNotFound</b> - Шаблон не найден
            <br><b>13.2 InvalidTemplateType</b> - Неверный тип шаблона
            <br><b>13.3 ExternalServiceNotAvailable</b> - Внешние сервисы не отвечают
            <br><b>13.4 PersonalDataUpdateError</b> - Ошибка при получении данных клиента
            <br><b>13.5 PersonalDataUpdateError</b> - Ошибка при обновлении карточки клиента
            <br><b>13.6 AmlCheckFailed</b> - Отказано по результатам проверки AML (на фронт не выводятся причины, только код)
            <br><b>13.7 AntiFraudCheckFailed</b> - Отказано по результатам проверки антифрод (на фронт не выводятся причины, только код)
  /cards/{cardId}/requisites:
    get:
      tags:
        - Карты и счета
      summary: Получение полных реквизитов карты
      description: Получение полных реквизитов карты
      operationId: getRequisites
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/CardIDPathParam'
      responses:
        '200':
          description: Запрос выполнен успешно
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CardRequisitesResponse'
        '400':
          description: Данные карты не получены
        '500':
          description: Internal Server Error
  /referral-program/status:
    get:
      tags:
        - Реферальная программа
      summary: Проверка активности реферальной программы
      description: Проверка доступности реферальной программы
      operationId: referralProgramStatus
      security:
        - BearerTokenAuth:
            - active
      responses:
        '200':
          description: Статус реферальной программы успешно получен
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CheckReferralStatusResponse'
        '400':
          description: |
            Ошибка проверки статуса реферальной программы
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /referral-program/profile:
    get:
      tags:
        - Реферальная программа
      summary: Получение профиля реферальной программы
      description: Получение подробной информации о профиле пользователя в реферальной программе
      operationId: referralProgramProfile
      security:
        - BearerTokenAuth:
            - active
      responses:
        '200':
          description: Профиль реферальной программы успешно получен
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReferralProfileResponse'
        '400':
          description: Ошибка валидации данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
        '401':
          description: Ошибка авторизации
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
        '404':
          description: Пользователь не найден
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /referral-program/onboarding:
    post:
      tags:
        - Реферальная программа
      summary: Онбординг реферальной программы
      description: Запуск процесса онбординга для пользователя в реферальной программе
      operationId: referralProgramOnboarding
      security:
        - BearerTokenAuth:
            - active
      responses:
        '200':
          description: Онбординг успешно запущен
        '400':
          description: Ошибка валидации данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
        '401':
          description: Ошибка авторизации
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
        '404':
          description: Пользователь не найден
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /referral-program/attribution:
    post:
      tags:
        - Реферальная программа
      summary: Атрибуция реферальной программы
      description: Отправка данных об атрибуции реферала
      operationId: referralProgramAttribution
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/ReferralAttributionRequest'
      responses:
        '200':
          description: Данные атрибуции успешно обработаны
        '400':
          description: Ошибка валидации данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
        '401':
          description: Ошибка авторизации
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
        '404':
          description: Пользователь не найден
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /dict/create:
    post:
      tags:
        - Справочники
      summary: Метод для создания нового справочника
      description: |
        Метод для создания нового справочника в сервисе dictionary
      operationId: dictCreate
      security:
        - BearerTokenAuth: []
      requestBody:
        $ref: '#/components/requestBodies/DictCreateReq'
      responses:
        '200':
          description: Информация по созданному справочнику
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Dictionary'
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /dict/update:
    post:
      tags:
        - Справочники
      summary: Метод для редактирования справочника
      description: |
        Метод для изменения справочника в сервисе dictionary
      operationId: dictUpdate
      security:
        - BearerTokenAuth: []
      requestBody:
        $ref: '#/components/requestBodies/DictUpdateReq'
      responses:
        '200':
          description: Информация по измененному справочнику
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Dictionary'
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /dict/delete:
    post:
      tags:
        - Справочники
      summary: Метод для удаления справочника
      description: |
        Метод для удаления справочника в сервисе dictionary
      operationId: dictDelete
      security:
        - BearerTokenAuth: []
      requestBody:
        $ref: '#/components/requestBodies/DictDeleteReq'
      responses:
        '200':
          description: Операция выполнена успешно
          content:
            application/json:
              schema:
                type: object
                properties: {}
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /dict/get/{dict_id}:
    get:
      tags:
        - Справочники
      summary: Просмотр данных справочника по его ID
      description: |
        Просмотр данных справочника
      operationId: dictGet
      security:
        - BearerTokenAuth: []
      parameters:
        - $ref: '#/components/parameters/DictionaryIDPathParam'
      responses:
        '200':
          description: Информация по запрошенному справочнику
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Dictionary'
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /dict/list:
    get:
      tags:
        - Справочники
      summary: Просмотр списка справочников
      description: |
        Просмотр списка справочника
      operationId: dictGetList
      security:
        - BearerTokenAuth: []
      parameters:
        - $ref: '#/components/parameters/PaginationPageQueryParam'
        - $ref: '#/components/parameters/PaginationCountQueryParam'
      responses:
        '200':
          description: Информация по запрошенному списку
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DictionaryList'
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /dict/doc/create:
    post:
      tags:
        - Справочники
      summary: Метод для создания нового документа в справочнике
      description: |
        Метод для создания нового документа в справочнике в сервисе dictionary
      operationId: dictDocCreate
      security:
        - BearerTokenAuth: []
      requestBody:
        $ref: '#/components/requestBodies/DictDocCreateReq'
      responses:
        '200':
          description: Информация по созданному документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DictDocument'
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /dict/doc/update:
    post:
      tags:
        - Справочники
      summary: Метод для изменения документа в справочнике
      description: |
        Метод для изменения документа в справочнике в сервисе dictionary
      operationId: dictDocUpdate
      security:
        - BearerTokenAuth: []
      requestBody:
        $ref: '#/components/requestBodies/DictDocUpdateReq'
      responses:
        '200':
          description: Информация по измененному документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DictDocument'
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /dict/doc/delete:
    post:
      tags:
        - Справочники
      summary: Метод для удаления документа в справочнике
      description: |
        Метод для удаления документа в справочнике в сервисе dictionary
      operationId: dictDocDelete
      security:
        - BearerTokenAuth: []
      requestBody:
        $ref: '#/components/requestBodies/DictDocDeleteReq'
      responses:
        '200':
          description: Операция выполнена успешно
          content:
            application/json:
              schema:
                type: object
                properties: {}
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /dict/doc/get/{doc_id}:
    get:
      tags:
        - Справочники
      summary: Просмотр документа справочника по ID документа
      description: |
        Просмотр данных документа
      operationId: dictDocGet
      security:
        - BearerTokenAuth: []
      parameters:
        - $ref: '#/components/parameters/DictDocumentIDPathParam'
      responses:
        '200':
          description: Информация по запрошенному документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DictDocument'
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /dict/doc/by-name/{dict_name}/{doc_name}:
    get:
      tags:
        - Справочники
      summary: Просмотр документа по именам справочника и документа
      description: |
        Просмотр данных документа
      operationId: dictDocGetByName
      security:
        - BearerTokenAuth: []
      parameters:
        - $ref: '#/components/parameters/DictionaryNamePathParam'
        - $ref: '#/components/parameters/DictDocumentNamePathParam'
      responses:
        '200':
          description: Информация по запрошенному документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DictDocument'
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /dict/doc/by-filters:
    post:
      tags:
        - Справочники
      summary: Просмотр списка документов справочника по фильтрам данных
      description: |
        Просмотр списка документов
      operationId: dictDocGetListByFilters
      security:
        - BearerTokenAuth: []
      requestBody:
        $ref: '#/components/requestBodies/DictDocGetListByFiltersReq'
      responses:
        '200':
          description: Информация по списку документов
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DictDocumentList'
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /dict/doc/tree/path:
    post:
      tags:
        - Справочники
      summary: Возвращает путь для древовидной структуры справочника
      description: |
        Просмотр списка документов в иерархии дерева
      operationId: dictDocGetTreeLine
      security:
        - BearerTokenAuth: []
      requestBody:
        $ref: '#/components/requestBodies/DictDocGetTreeLineReq'
      responses:
        '200':
          description: Информация по списку документов
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DictDocumentList'
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /dict/doc/order-update:
    post:
      tags:
        - Справочники
      summary: Метод для установки индекса сортировки
      description: |
        Метод для установки индекса сортировки order_num в документах
      operationId: dictDocOrderUpdate
      security:
        - BearerTokenAuth: []
      requestBody:
        $ref: '#/components/requestBodies/DictDocOrderUpdateReq'
      responses:
        '200':
          description: Операция выполнена успешно
          content:
            application/json:
              schema:
                type: object
                properties: {}
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /dict/job/run:
    post:
      tags:
        - Справочники
      summary: Запуск задачи синхронизации справочника
      description: |
        Запуск задачи синхронизации справочника в сервисе dictionary
      operationId: dictJobRun
      security:
        - BearerTokenAuth: []
      requestBody:
        $ref: '#/components/requestBodies/JobRunReq'
      responses:
        '200':
          description: Ответ
          content:
            application/json:
              schema:
                type: object
                properties: {}
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /dict/job/stop:
    post:
      tags:
        - Справочники
      summary: Принудительная остановка задачи синхронизации справочника
      description: |
        Принудительная остановка задачи синхронизации справочника в сервисе dictionary
      operationId: dictJobStop
      security:
        - BearerTokenAuth: []
      requestBody:
        $ref: '#/components/requestBodies/JobStopReq'
      responses:
        '200':
          description: Ответ
          content:
            application/json:
              schema:
                type: object
                properties: {}
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /dict/job/status:
    post:
      tags:
        - Справочники
      summary: Получение информации о статусе задачи
      description: |
        Получение информации о статусе задачи синхронизации справочников в сервисе dictionary
      operationId: dictJobGetStatus
      security:
        - BearerTokenAuth: []
      requestBody:
        $ref: '#/components/requestBodies/JobGetStatusReq'
      responses:
        '200':
          description: Ответ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobStatus'
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /dict/job/statusall:
    post:
      tags:
        - Справочники
      summary: Получение информации о статусе всех задач
      description: |
        Получение информации о статусе всех задач синхронизации справочников в сервисе dictionary
      operationId: dictJobGetStatusAll
      security:
        - BearerTokenAuth: []
      responses:
        '200':
          description: Ответ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobStatusAll'
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /dict/kato/map/tsoid:
    post:
      tags:
        - Справочники
      summary: Спец.метод преобразования адреса ЦОИД->КАТО
      description: |
        Конвертация адреса ЦОИД в КАТО
      operationId: dictKATOMapFromTSOID
      security:
        - BearerTokenAuth: []
      requestBody:
        $ref: '#/components/requestBodies/DictKATOMapFromTSOIDReq'
      responses:
        '200':
          description: Информация по списку документов
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DictKATOMapFromTSOIDResp'
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /dictionaries:
    get:
      tags:
        - Справочники
      summary: Просмотр списка документов справочника по имени
      description: |
        Просмотр списка документов
      operationId: getDictionaries
      security:
        - BearerTokenAuth: []
      parameters:
        - name: name
          in: query
          required: true
          description: Имя справочника
          schema:
            type: string
        - name: sort
          in: query
          required: false
          style: form
          explode: false
          description: Список полей сортировки
          schema:
            type: array
            items:
              type: string
        - name: page
          in: query
          required: false
          description: Номер страницы начиная с 0
          schema:
            type: integer
            format: int32
            default: 0
        - name: count
          in: query
          required: false
          description: Кол-во записей на странице
          schema:
            type: integer
            format: int32
            default: 1000000
      responses:
        '200':
          description: Информация по запрошенному документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DictDocumentList'
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /dictionaries/locations:
    get:
      tags:
        - Справочники
      summary: Получение локаций по родительским локациям
      description: |
        Получение локаций по родительским локациям
      operationId: dictGetLocations
      security:
        - BearerTokenAuth: []
      parameters:
        - name: parentID
          in: query
          description: Идентификатор родительской локации. 0 - для получения корневых локаций. Не требуется если указан параметр parentIds
          example: 0
          required: false
          schema:
            type: integer
            format: int32
            minimum: 0
        - name: parentIds
          in: query
          description: Идентификаторы родительских локаций. Не требуется если указан параметр parentID
          required: false
          schema:
            type: array
            minItems: 1
            items:
              type: integer
              format: int32
              minimum: 0
          explode: false
        - $ref: '#/components/parameters/AcceptLanguage'
      x-parameters-constraints:
        oneOf:
          - required:
              - parentID
          - required:
              - parentIds
      responses:
        '200':
          description: Локации
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetLocationsResp'
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /dictionaries/by-filter:
    post:
      tags:
        - Справочники
      summary: Просмотр списка документов справочника по фильтрам данных
      description: |
        Просмотр списка документов
      operationId: getDictionariesByFilter
      security:
        - BearerTokenAuth: []
      requestBody:
        $ref: '#/components/requestBodies/DictDocGetListByFiltersReq'
      responses:
        '200':
          description: Информация по списку документов
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DictDocumentList'
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/onboarding-texts:
    get:
      tags:
        - Кредиты
      summary: Получение onboarding текстовок для кредита
      description: Получение onboarding текстовок для кредита
      operationId: getLoansOnboardingTexts
      security:
        - BearerTokenAuth: []
      parameters:
        - $ref: '#/components/parameters/OnboardingSourceQueryParam'
      responses:
        '200':
          description: Onboarding текста для кредита
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnboardingTextsResponse'
        '400':
          description: |
            Ошибка получения данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/calc-data:
    get:
      tags:
        - Кредиты
      summary: Получение условий выдачи кредита
      description: Получение условий выдачи кредита для кредитного калькулятора
      operationId: getLoansCalcData
      security:
        - BearerTokenAuth: []
      responses:
        '200':
          description: Данные из справочников для кредитного калькулятора
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalcDataResponse'
        '400':
          description: |
            Ошибка получения данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/calculation:
    post:
      tags:
        - Кредиты
      summary: Расчет параметров платежей для всех сроков кредитов
      description: Расчет параметров платежей для всех сроков кредитов
      operationId: calculateLoanTerms
      security:
        - BearerTokenAuth: []
      requestBody:
        $ref: '#/components/requestBodies/CalculationBody'
      responses:
        '200':
          description: Данные сумм к погашению по кредиту для всех сроков
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalculationResultResponse'
        '400':
          description: |
            Ошибка получения данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/survey:
    get:
      tags:
        - Кредиты
      summary: Получение анкеты пользователя
      description: Получение анкеты пользователя
      operationId: getLoanSurvey
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
        - $ref: '#/components/parameters/UserAgent'
      responses:
        '200':
          description: Успешное получение анкеты пользователя
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSurveyResult'
        '400':
          description: |
            Ошибка получения анкеты пользователя.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
    post:
      tags:
        - Кредиты
      summary: Сохранение анкеты пользователя.
      description: Сохранение анкеты пользователя.
      operationId: saveLoanSurvey
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
      requestBody:
        $ref: '#/components/requestBodies/SaveSurveyBody'
      responses:
        '200':
          description: Анкета пользователя успешно сохранена.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SaveSurveyResp'
        '400':
          description: |
            Ошибка получения анкеты пользователя.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/application:
    post:
      tags:
        - Кредиты
      summary: Метод для создания кредитной заявки.
      description: Метод для создания кредитной заявки.
      operationId: createLoanApplication
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/CreateLoanApplicationBody'
      responses:
        '200':
          description: Кредитная заявка успешно создана
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateLoanApplicationResp'
        '400':
          description: |
            Ошибка создания кредитной заявки.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/application:
    put:
      tags:
        - Кредиты
      summary: Метод для дозаполенения/обновление кредитной заявки.
      description: Метод для дозаполенения/обновление кредитной заявки.
      operationId: updateLoanApplication
      parameters:
        - $ref: '#/components/parameters/ApplicationIDPathParam'
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/UpdateLoanApplicationBody'
      responses:
        '200':
          description: Кредитная заявка успешно обновлена
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateLoanApplicationResp'
        '400':
          description: |
            Ошибка обновление кредитной заявки.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/internal-checks-result:
    get:
      tags:
        - Кредиты
      summary: Получение  результата внутренних проверок кредитной заявки
      description: Получение результата внутренних проверок кредитной заявки
      operationId: loansGetInternalChecksResult
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/ApplicationIDPathParam'
      responses:
        '200':
          description: Результат внутренних проверок кредитной заявки
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetInternalChecksResultResp'
        '400':
          description: |
            Ошибка получения результата внутренних проверок кредитной заявки
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/education-types:
    get:
      tags:
        - Кредиты
      summary: Получение справочника по типам образования
      description: Получение справочника по типам образования
      operationId: loansGetEducationTypes
      security:
        - BearerTokenAuth: []
      responses:
        '200':
          description: Справочник по типам образования
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetEducationTypesResp'
        '400':
          description: |
            Ошибка получения справочника по типам образования
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/employment-types:
    get:
      tags:
        - Кредиты
      summary: Получение справочника по типам занятости
      description: Получение справочника по типам занятости
      operationId: loansGetEmploymentTypes
      security:
        - BearerTokenAuth: []
      responses:
        '200':
          description: Справочник по типам занятости
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetEmploymentTypesResp'
        '400':
          description: |
            Ошибка получения справочника по типам занятости
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/relation-types:
    get:
      tags:
        - Кредиты
      summary: Получение справочника по видам отношений к контактным лицам
      description: Получение справочника по видам отношений к контактным лицам
      operationId: loansGetRelationTypes
      security:
        - BearerTokenAuth: []
      responses:
        '200':
          description: Справочник по видам отношений к контактным лицам
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetRelationTypesResp'
        '400':
          description: |
            Ошибка получения справочника по видам отношений к контактным лицам
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/cancel:
    post:
      tags:
        - Кредиты
      summary: Отмена заявки на кредит
      description: Отмена заявки на кредит по идентификатору
      operationId: loansCancelLoanApplication
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/ApplicationIDPathParam'
      responses:
        '200':
          description: Заявка на кредит успешно отменена
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CancelLoanApplicationResp'
        '400':
          description: Ошибка отмены заявки на займ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/active-application-check:
    get:
      tags:
        - Кредиты
      summary: Проверка на наличие активной кредитной заявки
      description: Проверка на наличие активной кредитной заявки
      operationId: loansCheckActiveLoanAppExists
      security:
        - BearerTokenAuth:
            - active
      responses:
        '200':
          description: Результат проверки на наличие активной кредитной заявки
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CheckActiveLoanAppExistsResp'
        '400':
          description: |
            Ошибка получения результата проверки на наличие активной кредитной заявки
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/documents:
    post:
      tags:
        - Кредиты
      summary: Метод для генерации и получения документа для подписи
      description: |
        Метод для генерации и получения документа для кредитной заявки
      operationId: documentsForLoanApp
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
      requestBody:
        $ref: '#/components/requestBodies/LoanApplicationDocumentsBody'
      responses:
        '200':
          description: Информация по запрошенному документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoanApplicationDocumentsResp'
        '400':
          description: |
            Ошибка генерации документа
            <br><b>1.0 ValidationError</b> - Ошибка в теле запроса
            <br><b>10.1 InvalidInput</b> - Некорректный ввод
            <br><b>10.2 InvalidInput</b> - Кредитная заявка не найдена
            <br><b>10.3 DocumentNotFound</b> - Документ не найден
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/documents-for-sign:
    post:
      tags:
        - Кредиты
      summary: Метод для получения документа для подписи
      description: Подготовить для просмотра (и подписания) все необходимые документы для кредитного процесса
      operationId: loanDocumentForSign
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/ApplicationIDPathParam'
        - $ref: '#/components/parameters/AcceptLanguage'
      responses:
        '200':
          description: Успешное получение документа для подписи
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentForSignResp'
        '400':
          description: |
            Ошибка получения документа для подписи.
            <br><b>4.1 InvalidTemplateType</b> - Неверный тип шаблона
            <br><b>4.10 FailedToGenerateFromTemplate</b> - Не удалось сгенерировать документ из шаблона
            <br><b>4.11 FailedToUploadFile</b> - Не удалось загрузить файл
            <br><b>2.19 ClientCardDuplicate</b> - Найден дубль карточки клиента
            <br><b>2.20 AccountArrested</b> - Ошибка ограничения на действующем счете
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/status:
    get:
      tags:
        - Кредиты
      summary: Получение статуса подтвержденной заявки на кредит
      description: Получение статуса подтвержденной заявки на кредит
      operationId: loansGetApprovedLoanAppStatus
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/ApplicationIDPathParam'
      responses:
        '200':
          description: Статус заявки на кредит
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetApprovedLoanAppStatusResp'
        '400':
          description: |
            Ошибка получения статуса заявки на кредит
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/publish:
    post:
      tags:
        - Кредиты
      summary: Метод для публикации заявки с отправкой в СПР
      description: |
        Метод для публикации заявки вместе с файлами выписок с последующей отправкой в СПР
      operationId: publishLoanAppData
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/ApplicationIDPathParam'
      requestBody:
        $ref: '#/components/requestBodies/PublishLoanAppDataRequestBody'
      responses:
        '200':
          description: Данные по статусу кредита
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PublishLoanAppResp'
        '400':
          description: Ошибка публикации заявки
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/scoring-result:
    get:
      tags:
        - Кредиты
      summary: Метод для получения условий одобренного предложения от СПР
      description: |
        Метод для получения условий одобренного предложения после ожидания ответа СПР
      operationId: getScoringResult
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - in: header
          name: Accept-Language
          description: Язык запроса
          required: true
          schema:
            type: string
            enum:
              - kk
              - en
              - ru
            example: ru
            default: kk
        - $ref: '#/components/parameters/ApplicationIDPathParam'
      responses:
        '200':
          description: Данные с условиями одобренного предложения после ожидания ответа СПР
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScoringResultResp'
        '400':
          description: Ошибка генерации ссылки
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/bts-data:
    get:
      tags:
        - Кредиты
      summary: Метод для получения ссылки (liveness, eds)
      description: |
        Метод для получения ссылки (liveness, eds)
      operationId: getBtsDataForLoanApp
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - in: header
          name: Accept-Language
          description: Язык запроса
          required: true
          schema:
            type: string
            enum:
              - kk
              - en
              - ru
            example: ru
            default: kk
        - $ref: '#/components/parameters/ApplicationIDPathParam'
        - in: query
          name: type
          required: true
          schema:
            type: string
            description: Тип генерируемой ссылки
      responses:
        '200':
          description: Данные со сгенерированной ссылкой и ссылкой для редиректа
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BtsDataResp'
        '400':
          description: Ошибка генерации ссылки
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/identify:
    post:
      tags:
        - Кредиты
      summary: Метод для идентификации после liveness
      description: |
        Метод для идентификации после liveness
      operationId: loansPostIdentifyBtsData
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/ApplicationIDPathParam'
        - $ref: '#/components/parameters/AcceptLanguage'
      requestBody:
        $ref: '#/components/requestBodies/PostIdentifyBtsDataRequestBody'
      responses:
        '200':
          description: Результат идентификации
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostIdentifyBtsDataResp'
        '400':
          description: |
            Ошибка идентификации
            <br><b>10.2 LoanApplicationNotFound</b> - Кредитная заявка не найдена
            <br><b>10.6 LoansAppHasNotSuitableStatus</b> - Кредитная заявка находится в не соответствующем статусе
            <br><b>17.1 LivenessVerifyFailed</b> - Верификация пользователя не пройдена
            <br><b>17.2 LivenessPassedByQRLink</b> - Верификация пользователя пройдена по QR ссылке на другом устройстве
  /loans/{applicationID}/eds:
    post:
      tags:
        - Кредиты
      summary: Метод для идентификации после ЭЦП
      description: |
        Метод для идентификации после ЭЦП
      operationId: loansPostEdsBtsData
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/ApplicationIDPathParam'
        - $ref: '#/components/parameters/AcceptLanguage'
      requestBody:
        $ref: '#/components/requestBodies/PostEdsBtsDataRequestBody'
      responses:
        '200':
          description: Результат идентификации
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostEdsBtsDataResp'
        '400':
          description: Ошибка идентификации
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/details:
    get:
      tags:
        - Кредиты
      summary: Получение детальной информации по выданному займу
      description: Получение детальной информации по выданному займу
      operationId: getLoansDetails
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/ApplicationIDPathParam'
        - $ref: '#/components/parameters/AcceptLanguage'
      responses:
        '200':
          description: Запрос выполнен успешно, данные о кредитах переданы
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetLoansDetailsResponse'
        '400':
          description: |
            Ошибка получения данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/early-repay:
    post:
      tags:
        - Кредиты
      summary: Метод для проведения досрочного погашения (ЧДП/ПДП)
      description: |
        Метод для проведения досрочного погашения (ЧДП/ПДП)
      operationId: loansPostEarlyRepay
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/ApplicationIDPathParam'
      requestBody:
        $ref: '#/components/requestBodies/PostEarlyRepayRequestBody'
      responses:
        '200':
          description: Результат успешного досрочного погашения
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostEarlyRepayResp'
        '400':
          description: Ошибка досрочного погашения | <br><b>10.1 CodeLoansInvalidInputError</b> - неверный формат запроса <br><b>10.2 LoanApplicationNotFound</b> - Кредитная заявка не найдена <br><b>10.6 LoansAppHasNotSuitableStatus</b> - Кредитная заявка находится в не соответствующем статусе <br><b>10.7 CodeLoansEarlyRepayInvalidSum</b> - Сумма досрочного погашения не соответствует условиям <br><b>10.8 CodeLoansBankWorkingDayCheckFailed</b> - Операция доступна только в рабочее время (09:00-21:00 по КЗ)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/sign-confirm:
    post:
      tags:
        - Документы
      summary: Подтверждение подписи документов через ОТП
      description: Подтверждение подписи персонализированного документов из шаблона через ОТП
      operationId: loansConfirmSignDocuments
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
        - $ref: '#/components/parameters/ApplicationIDPathParam'
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/DocsOtpBody'
      responses:
        '200':
          description: Документ подписан, обновленная информация по документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoansConfirmSignDocumentsResponse'
        '400':
          description: |
            Ошибка подписи документа
            <br><b>3.1 InvalidCode</b> - Неверный код
            <br><b>3.2 MaxCodeChecksExceeded</b> - Превышено количество попыток проверки кода
            <br><b>3.5 AttemptNotFound</b> - Сценарий проверки ОТП кода не найден или истёк
            <br><b>4.9 DocumentAlreadySigned</b> - Документ уже подписан
            <br><b>4.12 FailedToSignDocument</b> - Не удалось подписать документ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/{docID}/sign-confirm:
    post:
      tags:
        - Документы
      summary: Подтверждение подписи документа через ОТП
      description: Подтверждение подписи персонализированного документа из шаблона через ОТП
      operationId: loansConfirmSignDocumentByID
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
        - $ref: '#/components/parameters/ApplicationIDPathParam'
        - $ref: '#/components/parameters/DocumentIDPathParam'
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/OtpBody'
      responses:
        '200':
          description: Документ подписан, обновленная информация по документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
        '400':
          description: |
            Ошибка подписи документа
            <br><b>3.1 InvalidCode</b> - Неверный код
            <br><b>3.2 MaxCodeChecksExceeded</b> - Превышено количество попыток проверки кода
            <br><b>3.5 AttemptNotFound</b> - Сценарий проверки ОТП кода не найден или истёк
            <br><b>4.9 DocumentAlreadySigned</b> - Документ уже подписан
            <br><b>4.12 FailedToSignDocument</b> - Не удалось подписать документ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/bank-statement:
    get:
      tags:
        - Кредиты
      summary: Получение справочных значений банков для загрузки выписки
      deprecated: true
      description: |
        Deprecated(Устарело)⚠️: Этот эндпоинт заменён на  `/loans/{applicationID}/bank-statement`
        Предоставляет информацию о банках
      operationId: getBankStatement
      security:
        - BearerTokenAuth:
            - active
      responses:
        '200':
          description: Успешный ответ с данными по банкам
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BankStatements'
        '400':
          description: Ошибка получения данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/bank-statement:
    get:
      tags:
        - Кредиты
      summary: Получение справочных значений банков для загрузки выписки
      description: Предоставляет информацию о банках
      operationId: getBankStatementV2
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/ApplicationIDPathParam'
      responses:
        '200':
          description: Успешный ответ с данными по банкам
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetBankStatementV2Response'
        '400':
          description: Ошибка получения данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/refinancing-info:
    get:
      tags:
        - Кредиты
      summary: Получение справочной информации о рефинансировании
      description: Получение справочной информации о рефинансировании по кредитной заявке
      operationId: getRefinancingInfo
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/ApplicationIDPathParam'
        - $ref: '#/components/parameters/AcceptLanguage'
        - $ref: '#/components/parameters/RefinancingInfoReqStep'
      responses:
        '200':
          description: Запрос выполнен успешно, данные о рефинансировании переданы
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetRefinancingInfoResp'
        '400':
          description: |
            Ошибка получения данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/external-bank-loans:
    post:
      tags:
        - Кредиты
      summary: Метод для сохранения кредитах в других банках
      description: |
        Метод для сохранения кредитах в других банках
      operationId: saveUserExternalBankLoans
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/ApplicationIDPathParam'
      requestBody:
        $ref: '#/components/requestBodies/SaveUserExternalBankLoansBody'
      responses:
        '200':
          description: Данные по статусу кредита
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SaveUserExternalBankLoansResp'
        '400':
          description: |
            Ошибка сохранения введённых данных о кредитах в внешних банках
            <br><b>10.9 CodeLoansInvalidUserExternalBankLoanIban</b> - IBAN не валидный
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /payments/transactions:
    get:
      tags:
        - Платежи
      summary: История операций
      description: История операций
      operationId: getTransactions
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/PaymentsGetTransactionsStartDate'
        - $ref: '#/components/parameters/PaymentsGetTransactionsEndDate'
        - $ref: '#/components/parameters/PaymentsGetTransactionsAccounts'
        - $ref: '#/components/parameters/PaymentsGetTransactionsCards'
        - $ref: '#/components/parameters/PaymentsGetTransactionsOperationType'
        - $ref: '#/components/parameters/PaymentsGetTransactionsCounterparty'
        - $ref: '#/components/parameters/PaymentsGetTransactionsMinAmount'
        - $ref: '#/components/parameters/PaymentsGetTransactionsMaxAmount'
        - $ref: '#/components/parameters/PaymentsGetTransactionsLimit'
        - $ref: '#/components/parameters/PaymentsGetTransactionsOffset'
      responses:
        '200':
          description: История операций
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentsGetTransactionsResponse'
  /payments/transactions/{transactionID}:
    get:
      tags:
        - Платежи
      summary: Получение транзакции по индентификатору
      description: Получение транзакции по индентификатору
      operationId: getTransactionByID
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/PaymentsGetTransactionByIDTransactionID'
      responses:
        '200':
          description: Транзакция
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentsGetTransactionByIDResponse'
  /payments/transactions/{transactionID}/receipt:
    get:
      tags:
        - Платежи
      summary: Получение квитанции по платежу
      description: Получение квитанции по платежу
      operationId: getTransactionReceipt
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/PaymentsGetTransactionByIDTransactionID'
      responses:
        '200':
          description: Квитанция транзакции
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentsGetTransactionReceiptResponse'
  /payments/history:
    get:
      tags:
        - Платежи
      summary: История платежей
      description: История платежей
      operationId: getPaymentHistory
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/PaymentsGetHistoryClientIinBin'
        - $ref: '#/components/parameters/PaymentsGetTransactionsLimit'
        - $ref: '#/components/parameters/PaymentsGetTransactionsOffset'
      responses:
        '200':
          description: История платежей
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentsGetHistoryResponse'
  /payments/check-account-iin:
    post:
      tags:
        - Платежи
      summary: Проверка ИИН/БИН и номера счёта получателя
      description: Проверка ИИН/БИН и номера счёта бенефициара
      operationId: paymentsCheckAccountIin
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/PaymentsCheckAccountIinRequest'
      responses:
        '200':
          description: |
            Результат проверки номера счёта и ИИН/БИН бенефициара.
            <br>Если получатель ЮЛ то приходят 4 параметра информация о банке бенефициара, тип что это ЮЛ, наименование ЮЛ
            <br>Если получатель ФЛ то приходит так же информация о банке бенефициара, наименование ФЛ
            <br>Если у ФЛ есть дополнительный тип деятельности то появляется объект additionalIndividualType в котором указана доп деятельность
            <br>В поле additionalIndividualType.type могут быть следующие значения: 32 - ИП, 64 — адвокат, 128 — частный нотариус, 256 — частный судебный исполнитель
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentsCheckAccountIinResponse'
        '400':
          description: |
            Ошибки проверки пользователя
            <br><b>12.19 InvalidAccountNumber</b> - Неверный номер счёта
            <br><b>12.26 BankByIbanCodeNotFound</b> - Банк по ibanCode не найден
            <br><b>12.27 CodeTaxPayerNotFound</b> - Не найден налогоплатильщик по ИИН/БИН
  /payments/create-payment-by-account:
    post:
      tags:
        - Платежи
      summary: Создание платежа для внутренних переводов
      description: Создание платежа для внутренних переводов
      operationId: createPaymentByAccount
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/CreatePaymentByAccount'
      responses:
        '200':
          description: Платеж создан успешно
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatePaymentByAccountResponse'
        '400':
          description: |
            Ошибки создания перевода
            <br><b>12.5 InvalidPayerBinIINError</b> - ИИН / БИН пользователя введен не верно
            <br><b>12.6 InvalidBeneficiaryBinIINError</b> - ИИН / БИН бенефициара введен не верно
            <br><b>12.7 InvalidPayerAccountError</b> - Счет пользователя введен не верно
            <br><b>12.8 InvalidBeneficiaryAccountError</b> - Счет бенефициара введен не верно
            <br><b>12.9 AmountIsNotDecimalError</b> - Сумма введена не верно
            <br><b>12.11 PaymentDetailsTooLongError</b> - Детали платежа больше 250 символов
            <br><b>12.21 DublicatePayment</b> - Дубликат платежа
            <br><b>12.28 InvalidPurposeCode</b> - Неверный код назначения платежа
            <br><b>12.29 InvalidBeneficiaryCode</b> - Неверный код бенефициара
            <br><b>12.30 InvalidBeneficiaryBankBic</b> - Неверный бик банка бенефициара
            <br><b>12.31 GrossErrorsWhenExecutePayment</b> - Обнаружены грубые ошибки при выполнении платежа
  /payments/confirm-payment-by-account:
    post:
      tags:
        - Платежи
      summary: Проверка отп и выполнение платежа для внутренних переводов
      description: Проверка отп и выполнение платежа для внутренних переводов
      operationId: confirmPaymentByAccount
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/ConfirmPaymentByAccount'
      responses:
        '200':
          description: Платеж выполнен успешно
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfirmPaymentByAccountResponse'
        '400':
          description: |
            Ошибки выполнения платежа
            <br><b>12.3 CodePaymentsInvalidOTP</b> - Неверный одноразовый пароль
            <br><b>12.16 TransactionNotFoundError</b> - Транзакция платежа не найдена
            <br><b>12.23 OTPNumberAttemptsExceeded</b> - Превышено количество попыток ввода отп
  /payments/check-phone-number:
    post:
      tags:
        - Платежи
      summary: Проверка номера телефона
      description: Создание оплаты сотовой связи
      operationId: checkPhoneNumber
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/CheckPhoneNumberRequest'
      responses:
        '200':
          description: Проверка номера телефона прошла успешно
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CheckPhoneNumberResponse'
        '400':
          description: |
            Ошибки проверки номера телефона
            <br><b>12.33 InvalidPhoneNumber</b> - Неверный номер телефона
            <br><b>12.34 PhoneOperatorNotFound</b> - Оператор по номеру телефона не найден
  /payments/create-payment-for-mobile:
    post:
      tags:
        - Платежи
      summary: Создание оплаты сотовой связи
      description: Создание оплаты сотовой связи
      operationId: createPaymentForMobile
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/CreatePaymentForMobileRequest'
      responses:
        '200':
          description: Создание оплаты сотовой связи выполнено успешно
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatePaymentForMobileResponse'
        '400':
          description: |
            Ошибки создания оплаты мобильной связи
            <br><b>12.21 DublicatePayment</b> - Дубликат платежа
            <br><b>12.38 NotEnoughMoneyInAccounts</b> - Недостаточно средств на счёте для выполнения операции
            <br><b>12.39 AntifraudСheckFailed</b> - Проверка антифрода не пройдена
            <br><b>12.43 PaymentErrorPC</b> - Ошибка от процессинга
            <br><b>12.44 PaymentErrorColvir</b> - Ошибка проведения платежа в Colvir
            <br><b>12.45 CodeRejectedByAP</b> - Ошибка обращения к ПС Астана Плат
  /payments/confirm-payment-for-mobile:
    post:
      tags:
        - Платежи
      summary: Проверка отп и выполнение оплаты сотовой связи
      description: Создание оплаты сотовой связи
      operationId: confirmPaymentForMobile
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/ConfirmPaymentForMobileRequest'
      responses:
        '200':
          description: Оплата сотовой связи подтверждена
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfirmPaymentForMobileResponse'
        '400':
          description: |
            Ошибки выполнения платежа
            <br><b>12.3 CodePaymentsInvalidOTP</b> - Неверный одноразовый пароль
            <br><b>12.16 TransactionNotFoundError</b> - Транзакция платежа не найдена
            <br><b>12.23 OTPNumberAttemptsExceeded</b> - Превышено количество попыток ввода отп
            <br><b>12.43 PaymentErrorPC</b> - Ошибка от процессинга
            <br><b>12.44 PaymentErrorColvir</b> - Ошибка проведения платежа в Colvir
            <br><b>12.45 CodeRejectedByAP</b> - Ошибка обращения к ПС Астана Плат
  /payments/check-client-by-phone-number:
    post:
      tags:
        - Платежи
      summary: Проверка клиента банка по номеру телефона
      description: Проверка клиента банка по номеру телефона
      operationId: checkClientByPhoneNumber
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/CheckClientByPhoneNumberRequest'
      responses:
        '200':
          description: Проверка клиента банка по номеру телефона прошла успешно
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CheckClientByPhoneNumberResponse'
        '400':
          description: |
            Ошибки проверки номера телефона
            <br><b>12.33 InvalidPhoneNumber</b> - Неверный номер телефона
            <br><b>12.35 ClientNotFoundByPhoneNumber</b> - Клиент банка не найден по номеру телефона
  /payments/create-internal-payment-by-phone-number:
    post:
      tags:
        - Платежи
      summary: Создание внутрибанковского перевода по номеру телефона
      description: Создание внутрибанковского перевода по номеру телефона
      operationId: createInternalPaymentByPhoneNumber
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/CreateInternalPaymentByPhoneNumberRequest'
      responses:
        '200':
          description: Создание внутрибанковского перевода по номеру телефона успешно
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateInternalPaymentByPhoneNumberResponse'
        '400':
          description: |
            Ошибки создания внутрибанковского перевода по номеру телефона
            <br><b>12.21 DublicatePayment</b> - Дубликат платежа
            <br><b>12.33 InvalidPhoneNumber</b> - Неверный номер телефона бенефициара
            <br><b>12.35 ClientNotFoundByPhoneNumber</b> - Клиент банка не найден по номеру телефона
            <br><b>12.36 MonthlyPaymentLimitExceeded</b> - Превышен месячный лимит на переводы
            <br><b>12.37 OneTimePaymentLimitExceeded</b> - Превышен разовый лимит на перевод
            <br><b>12.38 NotEnoughMoneyInAccounts</b> - Недостаточно средств на счёте для выполнения операции
            <br><b>12.39 AntifraudСheckFailed</b> - Проверка антифрода не пройдена
            <br><b>12.40 AMLСheckFailed</b> - Проверка AML не пройдена
  /payments/confirm-internal-payment-by-phone-number:
    post:
      tags:
        - Платежи
      summary: Проверка отп и выполнение перевода по номеру телефона
      description: Проверка отп и выполнение внутрибанковского перевода по номеру телефона
      operationId: confirmInternalPaymentByPhoneNumber
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/ConfirmInternalPaymentByPhoneNumberRequest'
      responses:
        '200':
          description: Внутрибанковский перевод по номеру телефона подтвердён
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfirmInternalPaymentByPhoneNumberResponse'
        '400':
          description: |
            Ошибки подтверждения внутрибанковского перевода по номеру телефона
            <br><b>12.3 CodePaymentsInvalidOTP</b> - Неверный одноразовый пароль
            <br><b>12.16 TransactionNotFoundError</b> - Транзакция платежа не найдена
            <br><b>12.23 OTPNumberAttemptsExceeded</b> - Превышено количество попыток ввода отп
  /payments/create-self-transfer:
    post:
      tags:
        - Платежи
      summary: Перевод между своими счетами
      description: Перевод между своими счетами
      operationId: createSelfTransfer
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/CreateSelfTransferRequest'
      responses:
        '200':
          description: Перевод между своими счетами
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateSelfTransferResponse'
        '400':
          description: |
            Ошибки подтверждения внутрибанковского перевода между своими счетами
            <br><b>12.7 InvalidPayerAccountError</b> - Счет пользователя введен не верно
            <br><b>12.21 DublicatePayment</b> - Дубликат платежа
            <br><b>12.22 UserBlocked</b> - Пользователь заблокирован
            <br><b>12.38 NotEnoughMoneyInAccounts</b> - Недостаточно средств на счёте для выполнения операции
            <br><b>12.40 AMLСheckFailed</b> - Проверка AML не пройдена
            <br><b>12.42 InvalidOperDate</b> - Операционная дата не определена
  /payments/create-payment-kaspiqr:
    post:
      tags:
        - Платежи
      summary: Создание платежа для оплаты с помощью QR
      description: Создание платежа для оплаты с помощью QR
      operationId: CreatePaymentKaspiQR
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/CreatePaymentKaspiQRRequest'
      responses:
        '200':
          description: Перевод между своими счетами
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatePaymentKaspiQRResponse'
        '400':
          description: |
            Ошибки создание платежа для оплаты с помощью QR
            <br><b>PaymentTimeout</b> - Сработал таймаут указанный в передаваемом параметре CanConfirmUntil от ПС
            <br><b>PaymentErrorPC</b> - При получении ошибки холдирования от ПЦ или его недоступности
            <br><b>PaymentErrorColvir</b> - При получении ошибки установки лимита в Colvir или его недоступности
            <br><b>RejectedByPS</b> - При получении кода ошибки с ПС или его недоступности
            <br><b>AccountArrest</b> - Счет аретован
            <br><b>12.38 NotEnoughMoneyInAccounts</b> - Недостаточно средств на счёте для выполнения операции
            <br><b>12.39 AntifraudСheckFailed</b> - Проверка антифрода не пройдена
            <br><b>12.40 AMLСheckFailed</b> - Проверка AML не пройдена
  /payments/qr-token:
    post:
      tags:
        - Платежи
      summary: Получить данные по платежу по данным QR Токена
      description: Получить данные по платежу по данным QR Токена
      operationId: QRToken
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/QRTokenRequest'
      responses:
        '200':
          description: Перевод между своими счетами
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QRTokenResponse'
        '400':
          description: |
            Ошибка получения данных по платежу по данным QR Токена
            <br><b>InvalidMCC</b> - МСС входит в список запрещенных к проведению
            <br><b>ErrorNoAccount</b> - Счет арестован или остаток < 0
            <br><b>ErrorNoCard</b> - У клиента нет счета в процессинге
            <br><b>KaspiSuspended</b> - Получена ошибка при запросе на расшифровку QR-токена либо метод недоступен
            <br><b>OverLimit</b> - Платеж выше установленного лимита
  /payments/qr-session-termination:
    post:
      tags:
        - Платежи
      summary: Оповещения бэка о закрытии экрана проведения платежа
      description: Оповещения бэка о закрытии экрана проведения платежа
      operationId: QRSessionTermination
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/QRSessionTerminationRequest'
      responses:
        '200':
          description: Успешная обработка запроса
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QRSessionTerminationResponse'
        '400':
          description: |
            Ошибки оповещения бэка о закрытии экрана проведения платежа
  /tasks:
    get:
      tags:
        - Таск менеджер
      summary: Получение списка задач
      description: Получение списка задач с поддержкой фильтров и пагинации
      operationId: getTasks
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/TaskStatusQueryParam'
        - $ref: '#/components/parameters/TaskTypeQueryParam'
        - $ref: '#/components/parameters/TaskCreatedAfterQueryParam'
        - $ref: '#/components/parameters/TaskCreatedBeforeQueryParam'
        - $ref: '#/components/parameters/TaskPageQueryParam'
        - $ref: '#/components/parameters/TaskPageSizeQueryParam'
      responses:
        '200':
          description: Список задач
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTasksListResponse'
        '400':
          $ref: '#/components/responses/RespEmpty'
  /tasks/{taskID}:
    get:
      tags:
        - Таск менеджер
      summary: Получение детальной информации о задаче
      description: Получение детальной информации о задаче по ID
      operationId: getTaskByID
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/TaskIDPathParam'
      responses:
        '200':
          description: Детальная информация о задаче
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTaskDetailsResponse'
        '400':
          $ref: '#/components/responses/RespEmpty'
  /tokenize:
    post:
      tags:
        - Токенизация карт
      summary: Подготовка данных для вызова SDK Thales
      description: |
        Возвращает OPC, publicKey id и прочие данные, необходимые
        фронту для вызова `addCard()` в SDK Thales.
      operationId: startTokenization
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TokenizeStartReq'
      responses:
        '200':
          description: Данные готовы, можно вызывать SDK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenizeStartResp'
        '400':
          description: Ошибка запроса
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /tokenize/{cardId}/state/update:
    get:
      tags:
        - Токенизация карт
      summary: Получить данные по обновлению токенизации карты
      description: Метод для получения данных по обновлению токенизации
      operationId: getTokenizeState
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/parameters-CardIDPathParam'
      responses:
        '200':
          description: Текущий статус токена
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenizeStateResp'
        '400':
          description: Ошибка запроса
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /tokenize/state:
    post:
      tags:
        - Токенизация карт
      summary: Уведомление об изменении виртуальной карты (Thales → Issuer)
      security:
        - BearerTokenAuth:
            - active
      description: |
        Эндпоинт для приёма уведомлений от TSH о любых изменениях виртуальной карты (токена):
        смена состояния (activated/suspended/resumed/deleted), обновление карты-источника,
        обновление токена (DPAN/expiry), а также события COF для VISA (device bind/unbind, step-up).
      operationId: notifyVirtualCardChange
      parameters:
        - name: x-correlation-id
          in: header
          required: true
          description: Идентификатор сессии в TSH (связывает запросы одного сценария)
          schema:
            type: string
            minLength: 1
            maxLength: 64
        - name: x-issuer-id
          in: header
          required: true
          description: Идентификатор эмитента (10 символов)
          schema:
            type: string
            minLength: 10
            maxLength: 10
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NotifyVirtualCardChangeReq'
      responses:
        '200':
          description: Уведомление принято (0 — ок, либо callback уже обработан)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotifyVirtualCardChangeResp'
              examples:
                ok:
                  summary: Успешная обработка
                  value:
                    errorCode: '0'
                    errorMessage: Ok
        '400':
          description: Ошибка запроса (1 — неверный JSON; 2 — issuerCardRefId не найден)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotifyVirtualCardChangeResp'
              examples:
                invalidJson:
                  summary: Неправильно заполнен JSON
                  value:
                    errorCode: '1'
                    errorMessage: invalid JSON
                notFound:
                  summary: IssuerCardRefId не найден
                  value:
                    errorCode: '2'
                    errorMessage: issuer_card_ref_id not found
        '500':
          description: Внутренняя ошибка сервера
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /tokenize/{cardId}/info:
    get:
      tags:
        - Токенизация карт
      summary: Метод получения информации по токенизации
      description: |
        Проксирует вызов Thales `getCardInfo` через processing-bridge / ПЦ (Silk).
        Если данных ещё нет в БД, выполняется внешний запрос, результат кэшируется.
      operationId: getCardInfo
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/parameters-CardIDPathParam'
      responses:
        '200':
          description: Подробные сведения о токенизации карты
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CardTokenInfo'
        '400':
          description: Ошибка запроса
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
        '401':
          description: Пользователь не авторизован или карта не принадлежит ему
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /fileguard:
    post:
      tags:
        - File Guard
      summary: Загрузка PDF файла
      description: Загрузка PDF файла
      operationId: postUnsecurePdfFile
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/FileUploadRequestBody'
      responses:
        '200':
          description: Данные с идентификатором загруженного документа
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileUploadResponse'
        '400':
          description: |
            Ошибка загрузки файла
            <br><b>21.1 IncorrectInputFileFormat</b> - Некорректный формат загружаемого файла
            <br><b>21.2 IncorrectInputFileSize</b> - Некорректный размер загружаемого файла
  /deposits/product-info/{productCode}:
    get:
      tags:
        - Депозиты
      summary: Получение информации о продукте
      description: Возвращает информацию о продукте депозита, включая максимальную процентную ставку
      operationId: getProductInfo
      security:
        - BearerTokenAuth: []
      parameters:
        - $ref: '#/components/parameters/GetProductInfo'
      responses:
        '200':
          description: Успешное получение информации о продукте
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProductInfoResp'
        '400':
          description: Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /deposits/utils/calculate-profit:
    get:
      tags:
        - Депозиты
      summary: Расчет доходности для указанной суммы/срока/ставки
      description: Возвращает суммарную доходность по календарным месяцам для выбранного метода выплаты (CARD/DEPOSIT) без обращения к условиям продукта.
      operationId: calculateProfit
      security:
        - BearerTokenAuth: []
      parameters:
        - $ref: '#/components/parameters/CalculateProfitAmount'
        - $ref: '#/components/parameters/CalculateProfitTermMonths'
        - $ref: '#/components/parameters/CalculateProfitProfitRate'
        - $ref: '#/components/parameters/CalculateProfitPayoutMethod'
      responses:
        '200':
          description: Успешный расчет доходности
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalculateProfitResp'
        '400':
          description: Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /deposits/condition-profit:
    post:
      tags:
        - Депозиты
      summary: Расчет доходности депозита
      description: Возвращает информацию о доходности депозита по выбранному продукту, сумме и валюте
      operationId: depositConditionProfit
      security:
        - BearerTokenAuth: []
      requestBody:
        $ref: '#/components/requestBodies/DepositConditionProfitReqBody'
      responses:
        '200':
          description: Успешный расчет доходности депозита
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DepositConditionProfitResp'
        '400':
          description: Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /deposits/get-available-accounts:
    get:
      tags:
        - Депозиты
      summary: Получение доступных счетов пользователя
      description: Возвращает доступных счетов пользователя
      operationId: getAvailableAccounts
      security:
        - BearerTokenAuth:
            - active
      responses:
        '200':
          description: Успешное получение доступных счетов
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAvailableAccountsResp'
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /deposits/create-offer:
    post:
      tags:
        - Депозиты
      summary: Создание офера депозита
      description: Создает офер депозита на подпись
      operationId: createDepositOffer
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/CreateDepositOfferReqBody'
      responses:
        '200':
          description: Успешное создание офера депозита
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateDepositOfferResp'
        '400':
          description: Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /user/deposits:
    get:
      tags:
        - Депозиты
      summary: Получение депозитов пользователя
      description: Возвращает список депозитных счетов пользователя из Колвира и базы данных
      operationId: getUserDeposits
      security:
        - BearerTokenAuth:
            - active
      responses:
        '200':
          description: Успешное получение депозитов пользователя
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserDepositsResponse'
        '400':
          description: Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /user/deposits/{depositID}:
    get:
      tags:
        - Депозиты
      summary: Получение детализации депозита
      description: Возвращает детальную информацию о депозите по его ID
      operationId: getDepositDetail
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/DepositDetailsParams'
      responses:
        '200':
          description: Успешное получение детализации депозита
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetDepositDetailResp'
        '400':
          description: Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
components:
  securitySchemes:
    BearerTokenAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  responses:
    RespEmpty:
      description: Операция выполнена успешно
      content:
        application/json:
          schema:
            type: object
            properties: {}
  schemas:
    PhoneNumber:
      type: string
      description: Номер телефона
      pattern: ^\+?[1-9]\d{10,10}$
      example: '+78889991100'
    Email:
      type: string
      pattern: ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$
      description: Электронная почта
    APIError:
      type: object
      description: Общий формат ошибки API. Возвращает цифровой код
      properties:
        error:
          type: string
        fields:
          type: object
          description: Объект с описанием деталей ошибок
          additionalProperties:
            type: string
      required:
        - error
      example:
        error: X.0
    ValidationError:
      type: object
      description: Ошибка валидации данных в теле запроса
      properties:
        error:
          type: string
          description: Код ошибки
          example: '1.0'
        fields:
          type: object
          description: Объект с описанием ошибок валидации полей
          additionalProperties:
            type: string
          example:
            iin: value must be a string
      required:
        - error
        - fields
    Money:
      type: object
      description: Сумма
      required:
        - value
        - currencyCode
      properties:
        value:
          type: string
          description: Сумма
        currencyCode:
          $ref: '#/components/schemas/CurrencyCode'
    CurrencyCode:
      type: string
      description: Код валюты
      enum:
        - KZT
        - RUB
        - EUR
        - USD
        - CNY
    Health:
      required:
        - users
        - otp
        - documents
        - notifications
        - keycloakProxy
        - kgdBridge
        - btsBridge
        - smsBridge
        - loans
        - colvirBridge
        - payments
        - cardsAccounts
        - dictionary
        - pkbBridge
        - amlBridge
        - liveness
        - taskManager
        - juicyscoreBridge
        - jiraBridge
        - fileGuard
        - scoring
        - seonBridge
        - sprBridge
        - altScoreBridge
        - qazpostBridge
        - deposits
        - bsasBridge
        - apBridge
        - processingBridge
        - collection
        - paymentsSme
        - referral
        - antifraud
        - crm
        - tokenize
        - balanceUpdater
        - kaspiBridge
        - bitrixBridge
        - foreignActivity
      type: object
      properties:
        users:
          type: boolean
          description: Статус сервиса users
        otp:
          type: boolean
          description: Статус сервиса otp
        documents:
          type: boolean
          description: Статус сервиса documents
        notifications:
          type: boolean
          description: Статус сервиса notifications
        keycloakProxy:
          type: boolean
          description: Статус сервиса keycloakProxy
        kgdBridge:
          type: boolean
          description: Статус сервиса kgdBridge
        btsBridge:
          type: boolean
          description: Статус сервиса btsBridge
        smsBridge:
          type: boolean
          description: Статус сервиса smsBridge
        loans:
          type: boolean
          description: Статус сервиса loans
        colvirBridge:
          type: boolean
          description: Статус сервиса colvirBridge
        payments:
          type: boolean
          description: Статус сервиса payments
        cardsAccounts:
          type: boolean
          description: Статус сервиса cardsAccounts
        dictionary:
          type: boolean
          description: Статус сервиса dictionary
        pkbBridge:
          type: boolean
          description: Статус сервиса pkbBridge
        amlBridge:
          type: boolean
          description: Статус сервиса amlBridge
        liveness:
          type: boolean
          description: Статус сервиса liveness
        taskManager:
          type: boolean
          description: Статус сервиса taskManager
        juicyscoreBridge:
          type: boolean
          description: Статус сервиса juicyscoreBridge
        jiraBridge:
          type: boolean
          description: Статус сервиса jiraBridge
        fileGuard:
          type: boolean
          description: Статус сервиса fileGuard
        scoring:
          type: boolean
          description: Статус сервиса scoring
        seonBridge:
          type: boolean
          description: Статус сервиса seonBridge
        sprBridge:
          type: boolean
          description: Статус сервиса sprBridge
        altScoreBridge:
          type: boolean
          description: Статус сервиса altScoreBridge
        qazpostBridge:
          type: boolean
          description: Статус сервиса qazpostBridge
        deposits:
          type: boolean
          description: Статус сервиса deposits
        bsasBridge:
          type: boolean
          description: Статус сервиса bsasBridge
        apBridge:
          type: boolean
          description: Статус сервиса apBridge
        processingBridge:
          type: boolean
          description: Статус сервиса processingBridge
        collection:
          type: boolean
          description: Статус сервиса collection
        paymentsSme:
          type: boolean
          description: Статус сервиса paymentsSme
        referral:
          type: boolean
          description: Статус сервиса referral
        antifraud:
          type: boolean
          description: Статус сервиса antifraud
        crm:
          type: boolean
          description: Статус сервиса crm
        tokenize:
          type: boolean
          description: Статус сервиса tokenize
        balanceUpdater:
          type: boolean
          description: Статус сервиса balanceUpdater
        kaspiBridge:
          type: boolean
          description: Статус сервиса kaspiBridge
        bitrixBridge:
          type: boolean
          description: Статус сервиса bitrixBridge
        foreignActivity:
          type: boolean
          description: Статус сервиса foreignActivity
    DeviceInfo:
      type: object
      properties:
        appVersion:
          type: string
          default: '101'
          description: Версия приложения
        deviceModel:
          type: string
          description: Модель устройства
          example: iPhone 7
        installationID:
          type: string
          format: uuid
          description: Идентификатор установки приложения
          example: 4d41e509-1d1e-4530-a1d6-97d2d599d3f8
        systemType:
          type: string
          description: Тип операционной системы
          enum:
            - Android
            - iOS
            - Web
          example: iOS
        systemVersion:
          type: string
          description: Версия операционной системы
          example: '20.2'
      required:
        - appVersion
        - deviceModel
        - installationID
        - systemType
        - systemVersion
    OtpResponse:
      type: object
      required:
        - attemptID
        - retryTime
      properties:
        attemptID:
          type: string
          format: uuid
          description: Идентификатор попытки для проверки кода
        retryTime:
          type: integer
          description: Количество секунд до следующей отправки
          example: 60
    AuthTokens:
      required:
        - access
        - refresh
      type: object
      properties:
        access:
          type: string
          description: Токен авторизации
        refresh:
          type: string
          description: Токен обновления авторизации
    NextStep:
      type: string
      example: identificationRequired
      enum:
        - identificationRequired
        - reidentificationRequired
        - documentSignRequired
    AuthConfirmResponse:
      type: object
      required:
        - tokens
        - userID
      properties:
        userID:
          type: string
          description: Идентификатор пользователя
          example: bc759511-de96-469f-a23c-fc7e2c406c0f
        tokens:
          $ref: '#/components/schemas/AuthTokens'
        nextStep:
          $ref: '#/components/schemas/NextStep'
    AuthRefreshResponse:
      required:
        - tokens
        - nextStep
        - firstName
        - lastName
      type: object
      properties:
        tokens:
          $ref: '#/components/schemas/AuthTokens'
        nextStep:
          $ref: '#/components/schemas/NextStep'
        firstName:
          type: string
        lastName:
          type: string
        middleName:
          type: string
    BtsDataForAuthResp:
      type: object
      required:
        - link
        - redirectURI
      properties:
        link:
          type: string
          description: Ссылка для прохождения идентификации
        redirectURI:
          type: string
          description: Ссылка для редиректа
    DocumentType:
      type: string
      enum:
        - accountOpeningApplication
        - bankServiceAgreement
        - personalDataAgreement
        - personalFilledDataAgreement
        - accessionAgreement
        - complexConsentClient
        - financingTermsInfo
      description: Тип (шаблон) документа
    Document:
      required:
        - ID
        - type
        - version
        - fileLink
        - title
        - signed
      type: object
      properties:
        ID:
          type: string
          format: uuid
          description: Идентификатор документа
        title:
          type: string
          description: Название документа
        type:
          $ref: '#/components/schemas/DocumentType'
        version:
          type: integer
          description: Версия документа, которая соотносится с типом документа (версия шаблона)
        fileLink:
          type: string
          description: Ссылка на физический файл
        signed:
          type: boolean
          description: Был ли документ подписан пользователем
    DocumentsForSignResponse:
      type: object
      required:
        - documents
      properties:
        documents:
          type: array
          description: Документы на подпись
          items:
            $ref: '#/components/schemas/Document'
    PublicDocumentType:
      type: string
      enum:
        - personalDataAgreement
        - financingTermsInfo
      description: Тип (шаблон) публичного документа
    PublicDocument:
      required:
        - ID
        - type
        - version
        - fileLink
        - title
        - signed
      type: object
      properties:
        ID:
          type: string
          format: uuid
          description: Идентификатор документа
        title:
          type: string
          description: Название документа
        type:
          $ref: '#/components/schemas/PublicDocumentType'
        version:
          type: integer
          description: Версия документа, которая соотносится с типом документа (версия шаблона)
        fileLink:
          type: string
          description: Ссылка на физический файл
        signed:
          type: boolean
          description: Был ли документ подписан пользователем
    ConfirmSignDocumentsResponse:
      type: object
      required:
        - documents
      properties:
        documents:
          type: array
          description: Подписанные документы
          items:
            $ref: '#/components/schemas/Document'
    SignedDocumentsBatchResponse:
      type: object
      required:
        - documents
      properties:
        documents:
          type: array
          description: Подписанные документы
          items:
            $ref: '#/components/schemas/Document'
    AccountDocumentType:
      type: string
      enum:
        - accountStatement
        - accountAvailability
        - availableBalance
      description: Тип (шаблон) документа для счета
    AccountDocumentResponse:
      type: object
      properties:
        document:
          $ref: '#/components/schemas/Document'
    UserCard:
      type: object
      required:
        - ID
        - attachedAccountID
        - embossingName
        - maskedPAN
        - cardType
        - paymentSystem
        - cardClass
        - status
        - tokenizationStatus
        - wallet
      properties:
        ID:
          type: string
          format: uuid
        attachedAccountID:
          type: string
          format: uuid
        embossingName:
          type: string
        maskedPAN:
          type: string
        cardType:
          type: string
          enum:
            - VIRTUAL
            - PHYSICAL
        paymentSystem:
          type: string
          enum:
            - MASTERCARD
        cardClass:
          type: string
        status:
          type: string
          enum:
            - ACTIVE
            - IN_OPENING
            - ERROR
            - BLOCKED
        tokenizationStatus:
          type: string
          enum:
            - tokenized
            - need_verify
            - not_tokenized
        wallet:
          type: string
          enum:
            - GooglePay
            - ApplePay
    UserAccountArrest:
      type: object
      properties:
        blocking:
          type: boolean
          description: Признак полной или частичной блокировки, накладывает ограничения на определенную сумму - сумму ареста
    UserAccount:
      type: object
      required:
        - ID
        - type
        - status
        - currency
        - openDate
        - iban
        - arrest
        - balance
        - balanceNatval
        - availableBalance
        - planSum
        - isFinContractOpened
      properties:
        ID:
          type: string
          description: Идентификатор счёта
        type:
          type: string
          enum:
            - CURR
            - BUFB
            - BUCO
            - TU
            - LOAN
            - OTHERS
          description: Тип счёта
        status:
          type: string
          enum:
            - ACTIVE
            - IN_OPENING
            - ERROR
            - CLOSED
          description: Статус счёта
        currency:
          type: string
          enum:
            - KZT
            - RUB
            - EUR
            - USD
            - CNY
          description: Валюта счёта
        iban:
          type: string
          description: Номер счета iban
        openDate:
          type: string
          format: date
          description: Дата открытия счёта
        closeDate:
          type: string
          format: date
          description: Дата закрытия счёта
        balance:
          type: number
          format: double
          description: Баланс (остаток счёта)
        balanceNatval:
          type: number
          format: double
          description: Баланс счета в нац. валюте (KZT)
        planSum:
          type: number
          format: double
          description: Плановые суммы
        availableBalance:
          type: number
          format: double
          description: Доступный баланс (balance-plansum-partiallyDebtAmount)
        isFinContractOpened:
          type: boolean
          description: Признак открытого фин. контракта в процессинге для данного счета
        finContractID:
          type: string
          format: uuid
          description: Идентификатор фин. контракта в процессинге
        finContractStatus:
          type: string
          enum:
            - ACTIVE
            - IN_OPENING
            - BLOCKED
            - CLOSED
          description: Статус фин. контракта в процессинге
        arrest:
          $ref: '#/components/schemas/UserAccountArrest'
    UserCardsResponse:
      type: object
      required:
        - accounts
      properties:
        cards:
          type: array
          items:
            $ref: '#/components/schemas/UserCard'
        accounts:
          type: array
          description: Счета пользователя
          items:
            $ref: '#/components/schemas/UserAccount'
    UserAccountResponse:
      type: object
      required:
        - account
      properties:
        account:
          $ref: '#/components/schemas/UserAccount'
    Offer:
      type: object
      required:
        - title
        - text
        - subText
      properties:
        title:
          type: string
          description: Заголовок для баннера (ограничение по символам - 20)
          example: Средства на ваши цели
        text:
          type: string
          description: Текст с предложением оформить кредит (для баннера) (ограничение по символам - 20)
          example: Быстрое оформление онлайн по исламским принципам
        subText:
          type: string
          description: Подтекст с информацией по сумме и сроку кредита (ограничение по символам - 20)
          example: до 3 млн ₸, до 5 лет
    LoansNextPayment:
      type: object
      description: Объект “Следующий платеж”
      required:
        - date
      properties:
        date:
          type: string
          format: date
          description: Дата ближайшего платежа
          example: '2025-01-02'
        amount:
          $ref: '#/components/schemas/Money'
    ErrorReason:
      type: object
      required:
        - title
        - code
      properties:
        title:
          type: string
          description: Заголовок причины отказа
        message:
          type: string
          description: Сообщение о причине отказа
        code:
          type: string
          description: Код ошибки
    Loan:
      type: object
      required:
        - ID
        - productType
        - hasDelay
      properties:
        ID:
          type: string
          description: Уникальный идентификатор кредита
        productType:
          type: string
          description: Тип кредитного продукта
          example: LOAN
        status:
          type: string
          description: Статус кредита
          example: COMPLETED
        amount:
          $ref: '#/components/schemas/Money'
        applicationDueDate:
          type: string
          format: date
          description: Срок окончания одобренной заявки на кредит (5 дней)
          example: '2024-12-02'
        percentPaid:
          type: integer
          description: Поле для заполнения виджета оставшейся выплате по кредиту
          example: 67
        nextPayment:
          $ref: '#/components/schemas/LoansNextPayment'
        hasDelay:
          type: boolean
          description: Флаг наличия просроченных платежей по кредиту
          example: false
        statusReason:
          $ref: '#/components/schemas/ErrorReason'
    LoansResponse:
      type: object
      required:
        - loans
      properties:
        offer:
          $ref: '#/components/schemas/Offer'
        loans:
          type: array
          description: Список кредитов
          items:
            $ref: '#/components/schemas/Loan'
    UpdateUserLocaleResp:
      type: object
      properties: {}
    Documents:
      required:
        - ID
        - type
        - version
        - fileLink
        - title
        - signed
      type: object
      properties:
        ID:
          type: string
          format: uuid
          description: Идентификатор документа
        title:
          type: string
          description: Название документа
        type:
          type: string
          description: Тип документа
        version:
          type: integer
          description: Версия документа, которая соотносится с типом документа (версия шаблона)
        fileLink:
          type: string
          description: Ссылка на физический файл
        signed:
          type: boolean
          description: Был ли документ подписан пользователем
    ApplicationForSignResponse:
      type: object
      required:
        - documents
      properties:
        documents:
          type: array
          description: Документы на подпись
          items:
            $ref: '#/components/schemas/Documents'
    CardRequisitesResponse:
      type: object
      required:
        - pan
        - embossName
        - expiryDate
        - status
        - cvv
      properties:
        pan:
          type: string
          description: Полный номер карты (PAN)
        embossName:
          type: string
          description: Имя, вытесненное на карте
        expiryDate:
          type: string
          description: Срок действия карты в формате MM/YY
          example: 06/28
        status:
          type: string
          description: Статус карты
          example: Active
        cvv:
          type: string
          description: CVV код карты
          example: '018'
    CheckReferralStatusResponse:
      type: object
      description: Ответ с результатом проверки статуса реферальной программы
      required:
        - isAvailable
        - isReferral
        - onboardingDisplayStatus
      properties:
        isAvailable:
          type: boolean
          description: |
            Доступна ли реферальная программа для пользователя
          example: true
        isReferral:
          type: boolean
          description: |
            Признак реферала
          example: true
        onboardingDisplayStatus:
          type: boolean
          description: |
            Статус отображения экранов онбординга
          example: true
    ActionType:
      type: string
      description: |
        Тип целевого действия.
      example: payment_purchase
    ActionStatus:
      type: string
      description: |
        Статус выполнения целевого действия. Возможные значения:
        * **done** - целевое действие выполнено
        * **waiting** - целевое действие ожидает выполнения
      enum:
        - done
        - waiting
      example: done
    ActionDetail:
      type: object
      description: Детальная информация о целевом действии
      required:
        - actionType
        - actionStatus
        - rewardForAction
      properties:
        actionType:
          $ref: '#/components/schemas/ActionType'
        actionStatus:
          $ref: '#/components/schemas/ActionStatus'
        rewardForAction:
          type: number
          format: double
          description: Сумма вознаграждения за целевое действие
          example: 7000
    ReferralActions:
      type: object
      description: Общая информация по целевым действиям. Обязателен, если isReferral = true
      required:
        - completedActionCount
        - totalActionCount
        - leftActionCount
        - daysLeft
        - rewardSum
        - actionDetails
      properties:
        completedActionCount:
          type: integer
          description: Кол-во выполненных целевых действий
          example: 2
        totalActionCount:
          type: integer
          description: Общее кол-во целевых действий
          example: 4
        leftActionCount:
          type: integer
          description: Кол-во оставшихся целевых действий
          example: 0
        daysLeft:
          type: integer
          description: Кол-во дней до завершения периода выполнения целевых действий
          example: 15
        rewardSum:
          type: number
          format: double
          description: Сумма вознаграждения за выполненные целевые действия
          example: 2500
        rewardDueDate:
          type: string
          description: Срок выплаты вознаграждения в формате ГГГГ-ММ-ДД. Если срок истек, то будет пустым
          example: '2025-07-25'
        actionDetails:
          type: array
          description: |
            Подробная информация о **всех** целевых действиях Реферала.
            По ожидающим целевым действиям (action_status = waiting) сортировка производится в следующем порядке:
            1. payment_purchase
            2. replenishment
            3. murabaha
            4. deposit
          items:
            $ref: '#/components/schemas/ActionDetail'
    ReferralStatus:
      type: string
      description: |
        Статус Реферала. Возможные значения:
        * **active** - срок выполнения целевых действий ещё не истек
        * **completed** - срок выполнения целевых действий истек
      enum:
        - active
        - completed
      example: active
    InvitedReferral:
      type: object
      description: Информация о приглашенном реферале
      required:
        - name
        - referralStatus
        - completedActionCount
        - totalActionCount
        - leftActionCount
        - daysLeftForReferral
        - rewardSum
      properties:
        name:
          type: string
          description: Имя и первая буква фамилии Реферала
          example: Алихан К.
        referralStatus:
          $ref: '#/components/schemas/ReferralStatus'
        completedActionCount:
          type: integer
          description: Кол-во выполненных целевых действий
          example: 1
        totalActionCount:
          type: integer
          description: Общее кол-во целевых действий
          example: 4
        leftActionCount:
          type: integer
          description: Кол-во оставшихся целевых действий
          example: 2
        daysLeftForReferral:
          type: integer
          description: Кол-во дней до завершения периода выполнения целевых действий
          example: 0
        rewardSum:
          type: number
          format: double
          description: Сумма вознаграждения за выполненные целевые действия Реферала
          example: 28000
        rewardDueDate:
          type: string
          description: Срок выплаты вознаграждения в формате ГГГГ-ММ-ДД. Если срок истек, то будет пустым
          example: '2025-07-25'
    ReferralProfileResponse:
      type: object
      description: Профиль реферальной программы пользователя
      required:
        - referralLink
        - isReferral
        - shortReferralLink
        - invitedReferralCount
        - paidRewardSum
        - waitingRewardSum
        - referralActions
        - invitedReferrals
      properties:
        referralLink:
          type: string
          description: Уникальная реферальная ссылка пользователя
          example: https://zaman.onelink.me/OAIU?af_xp=custom&pid=referral&deep_link_sub1=referrer_id&af_channel=referal
        isReferral:
          type: boolean
          description: |
            Признак Реферала. Возможные значения:
            * **true** - пользователь является рефералом
            * **false** - пользователь **НЕ** является рефералом
          example: true
        shortReferralLink:
          type: string
          description: Короткая уникальная реферальная ссылка клиента
          example: https://zaman.onelink.me/OAIU/njs5xdja
        invitedReferralCount:
          type: integer
          description: Кол-во приглашенных Рефералов
          example: 0
        paidRewardSum:
          type: number
          format: double
          description: Фактически выплаченная сумма вознаграждений
          example: 0
        waitingRewardSum:
          type: number
          format: double
          description: |
            Общая сумма вознаграждений, ожидающая выплаты за:
            * действия приглашенных Рефералов
            * собственные целевые действия Клиента, если он сам является Рефералом
          example: 15000
        referralActions:
          $ref: '#/components/schemas/ReferralActions'
        invitedReferrals:
          type: array
          description: |
            Общая информация по целевым действиям приглашенных Рефералов. Обязателен, если invited_referral_count > 0.
            Для Рефералов, у которых срок выполнения целевых действий ещё не истёк (referral_status = active), сортировка списка производится по следующему принципу:
            * в начале списка отображаются Рефералы с наименьшим количеством выполненных действий (completed_action_count)
          items:
            $ref: '#/components/schemas/InvitedReferral'
    Dictionary:
      required:
        - name
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Идентификатор справочника
        name:
          type: string
          description: Имя справочника
        description:
          type: string
          description: Описание справочника
        schema:
          type: string
          description: Описание структуры документов справочника в формате JSON schema (если не нужно, используем пустой JSON "{}")
    DictionaryID:
      required:
        - dict_id
      type: object
      properties:
        dict_id:
          type: string
          format: uuid
          description: ID справочника
    DictionaryList:
      required:
        - list
      type: object
      properties:
        list:
          type: array
          description: Список справочников
          items:
            $ref: '#/components/schemas/Dictionary'
    DictDocument:
      required:
        - name
        - dict_id
        - data
        - order_num
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Идентификатор документа справочника
        dict_id:
          type: string
          format: uuid
          description: Идентификатор справочника к которому относится документ
        name:
          type: string
          description: Имя документа справочника (не обязательно)
        data:
          type: object
          description: Данные документа справочника в виде объекта JSON (зависит от структуры справочника)
        valid:
          type: boolean
          description: Соответствует-ли документ настройкам схемы для данного словаря
        order_num:
          type: integer
          default: 0
          description: Поле для настройки ручной сортировки при необходимости
    DictDocumentID:
      required:
        - doc_id
      type: object
      properties:
        doc_id:
          type: string
          format: uuid
          description: ID документа
    Filter:
      required:
        - operation
      type: object
      properties:
        field:
          type: string
          description: Поле данных
          example: name, data.fieldName, data.fieldName1.fieldName2
        operation:
          type: string
          description: Операция
          example: '=, ==, >, >=, <, <=, like, ilike, in'
        value:
          type: string
          description: Значение
          example: string
        not:
          type: boolean
          description: Флаг использования обратного условия фильтра
          example: false
        group:
          type: array
          items:
            $ref: '#/components/schemas/Filter'
    DictDocumentFilters:
      required:
        - dict_id
        - filters
      type: object
      properties:
        dict_id:
          type: string
          description: ID или имя словаря
        filters:
          type: array
          items:
            $ref: '#/components/schemas/Filter'
        sort:
          type: array
          items:
            type: string
        pagination:
          required:
            - page
            - count
          type: object
          properties:
            page:
              type: integer
              format: int32
              default: 0
              description: Номер страницы начиная с 0
            count:
              type: integer
              format: int32
              default: 1000000
              description: Размер страницы
    DictDocumentList:
      required:
        - list
      type: object
      properties:
        list:
          type: array
          description: Список документов справочника
          items:
            $ref: '#/components/schemas/DictDocument'
    DictGetTreeLine:
      required:
        - dict_id
        - selectFieldName
        - selectFieldValue
        - idFieldName
        - parentIDFieldName
      type: object
      properties:
        dict_id:
          type: string
          description: ID или имя словаря
          example: kato
        selectFieldName:
          type: string
          description: Имя поля по значению которого мы выбираем первый элемент
          example: data.code
        selectFieldValue:
          type: string
          description: Значение поля по которому мы выбираем первый элемент
          example: '103243134'
        idFieldName:
          type: string
          description: Имя поля в древовидной структуре, соответствующее id узла дерева
          example: data.id
        parentIDFieldName:
          type: string
          description: Имя поля в древовидной структуре, соответствующее id узла-владельца для текущего
          example: data.parent_id
    DictDocumentOrderUpdate:
      required:
        - dict_id
        - order_num
        - doc_ids
      type: object
      properties:
        dict_id:
          type: string
          description: ID или имя словаря
        order_num:
          type: integer
          format: int32
          description: Индекс для сортировки
        doc_ids:
          type: array
          items:
            type: string
            description: ID или имена документов, которым нужно установить индекс сортировки
    JobRun:
      required:
        - name
        - source
        - lang
      type: object
      properties:
        name:
          type: string
          description: Имя задачи синхронизации справочника
        source:
          type: string
          description: URI исходного файла с данными справочника
        lang:
          type: string
          description: Язык исходного файла
          example: ru
    JobStop:
      required:
        - name
      type: object
      properties:
        name:
          type: string
          description: Имя задачи синхронизации справочника
    JobGetStatus:
      required:
        - name
      type: object
      properties:
        name:
          type: string
          description: Имя задачи синхронизации справочника
    JobStatus:
      required:
        - info
      type: object
      properties:
        info:
          type: object
          additionalProperties:
            type: string
    JobStatusAll:
      type: array
      items:
        type: object
        additionalProperties:
          type: string
    DictTSOIDAddr:
      required:
        - code
        - nameKz
        - nameRu
      type: object
      properties:
        code:
          type: integer
          format: int64
          description: КАТО код элемента адреса
        nameKz:
          type: string
          description: Наименование элемента адреса на казахском языке
        nameRu:
          type: string
          description: Наименование элемента адреса на русском языке
    DictKATOMapFromTSOIDArgs:
      type: object
      required:
        - lang
      properties:
        country:
          $ref: '#/components/schemas/DictTSOIDAddr'
        region:
          $ref: '#/components/schemas/DictTSOIDAddr'
        district:
          $ref: '#/components/schemas/DictTSOIDAddr'
        city:
          type: string
          description: Город (населенный пункт)
        street:
          type: string
          description: Улица
        building:
          type: string
          description: Здание (дом)
        corpus:
          type: string
          description: Корпус
        flat:
          type: string
          description: Квартира
        lang:
          type: string
          enum:
            - kz
            - ru
            - en
          example: kz
          description: Язык данных запроса
    DictKATOMapFromTSOIDResp:
      type: object
      properties:
        addr_type:
          type: string
          description: Тип адреса (001 - адрес рождения, 002 - адрес регистрации, 003 - адрес фактического проживания)
          default: '000'
          example: '000'
        own_type:
          type: string
          description: Тип владения (по умолчанию - пусто)
          default: ''
        country:
          type: string
          description: Страна
        region_city:
          type: string
          description: Город-регион
        city:
          type: string
          description: Город
        city_tdc:
          type: string
          description: Код вида территории для города
        region:
          type: string
          description: Область
        region_tdc:
          type: string
          description: Код вида территории для области
        district:
          type: string
          description: Район
        district_tdc:
          type: string
          description: Код вида территории для района
        settlement:
          type: string
          description: Населенный пункт
        settlement_tdc:
          type: string
          description: Код вида территории для населенного пункта
        street:
          type: string
          description: Улица
        street_tdc:
          type: string
          description: Код вида территории для улицы
        zone:
          type: string
          description: Микрорайон
        zone_tdc:
          type: string
          description: Код вида территории для микрорайона
        house:
          type: string
          description: Дом
        house_tdc:
          type: string
          description: Код вида территории для дома
        body:
          type: string
          description: Корпус
        building:
          type: string
          description: Строение
        house_ownership:
          type: string
          description: Владение
        flat:
          type: string
          description: Квартира
        flat_tdc:
          type: string
          description: Код вида территории для квартиры
        index:
          type: string
          description: Почтовый индекс
        full:
          type: string
          description: Полный адрес
        kato:
          type: string
          description: КАТО код для данного объекта
    Location:
      required:
        - ID
        - name
        - parentID
        - code
      type: object
      properties:
        ID:
          type: integer
          format: int32
          description: Идентификатор локации
        name:
          type: string
          description: Название локации
        parentID:
          type: integer
          format: int32
          description: Идентификатор родительской локации
        code:
          type: string
          description: Код локации
    GetLocationsResp:
      required:
        - locations
      type: object
      properties:
        locations:
          type: array
          description: Список локаций
          items:
            $ref: '#/components/schemas/Location'
    OnboardingTextData:
      type: object
      required:
        - code
        - image
        - description
      description: Холдер текстовок
      properties:
        code:
          type: string
          description: Уникальный код текста
          example: '1'
        image:
          type: string
          description: Ссылка в S3 на изображение
          example: https://s3.somelink.com/img.png
        description:
          type: string
          description: Текст для отображения
          example: Низкий ежемесячный платеж
    OnboardingTextsResponse:
      type: object
      required:
        - texts
      properties:
        texts:
          type: array
          description: Массив текстов и изображений для onboarding экрана
          items:
            $ref: '#/components/schemas/OnboardingTextData'
    Term:
      type: integer
      minimum: 0
      example: 3
      description: Срок кредита (в месяцах)
    LoanTermInterestData:
      type: object
      required:
        - ID
        - term
        - interest
        - defaultInterest
      description: Холдер условий кредита
      properties:
        ID:
          type: string
          description: Идентификатор объекта условий кредита
          example: '1'
        term:
          $ref: '#/components/schemas/Term'
        interest:
          type: integer
          minimum: 0
          example: 30
          description: Процентная ставка кредита
        defaultInterest:
          type: boolean
          description: Признак срока кредита по подсветки по умолчанию
    LoanPurposeData:
      type: object
      required:
        - ID
        - description
        - defaultPurpose
      properties:
        ID:
          type: string
          description: Идентификатор объекта условий кредита
          example: '1'
        description:
          type: string
          description: Описание цели кредита
          example: На покупку товара
        defaultPurpose:
          type: boolean
          description: Является ли цель дефолтной
    LoanAmount:
      type: object
      required:
        - minAmount
        - maxAmount
      description: Холдер лимитов по сумме кредита
      properties:
        minAmount:
          type: integer
          example: 10000
          description: Минимальный лимит кредита
        maxAmount:
          type: integer
          example: 50000
          description: Максимальный лимит кредита
    CalcDataResponse:
      type: object
      required:
        - termInterest
        - purpose
        - amount
      properties:
        termInterest:
          type: array
          description: Массив доступных условий кредита
          items:
            $ref: '#/components/schemas/LoanTermInterestData'
        purpose:
          type: array
          description: Массив доступных целей кредита
          items:
            $ref: '#/components/schemas/LoanPurposeData'
        amount:
          $ref: '#/components/schemas/LoanAmount'
    CalculationTermData:
      type: object
      required:
        - termInterest
        - monthlyPayment
        - overpayAmount
        - totalAmount
      properties:
        termInterest:
          $ref: '#/components/schemas/LoanTermInterestData'
        monthlyPayment:
          type: integer
          minimum: 0
          example: 363333
          description: Предварительный ежемесячный платеж
        overpayAmount:
          type: integer
          minimum: 0
          example: 89999
          description: Сумма наценки
        totalAmount:
          type: integer
          minimum: 0
          example: 1089999
          description: Общая сумма (сумма кредита + сумма наценки)
    CalculationResultResponse:
      type: object
      required:
        - calculationResult
      properties:
        calculationResult:
          type: array
          description: Массив результатов расчетов платежей
          items:
            $ref: '#/components/schemas/CalculationTermData'
    KatoData:
      type: object
      required:
        - code
        - name
        - ID
        - parentID
      properties:
        code:
          type: string
          description: Код КАТО
        name:
          type: string
          description: Название КАТО
        ID:
          type: integer
          format: int32
          description: Идентификатор КАТО
        parentID:
          type: integer
          format: int32
          description: Идентификатор родительской КАТО
    GetSurveyAddress:
      type: object
      required:
        - isFull
      properties:
        region:
          $ref: '#/components/schemas/KatoData'
        district:
          $ref: '#/components/schemas/KatoData'
        settlementArea:
          $ref: '#/components/schemas/KatoData'
        settlement:
          $ref: '#/components/schemas/KatoData'
        locality:
          $ref: '#/components/schemas/KatoData'
        street:
          type: string
          description: Улица адреса
        building:
          type: string
          description: Номер или название здания
        flat:
          type: string
          nullable: true
          description: Номер квартиры или офиса
        isFull:
          type: boolean
          description: Флаг полностью заполненного адреса
    EducationType:
      type: object
      required:
        - ID
        - name
        - code
      properties:
        ID:
          type: string
          description: Идентификатор уровня образования
        name:
          type: string
          description: Название уровня образования
        code:
          type: string
          description: Код уровня образования
    EmploymentType:
      type: object
      required:
        - ID
        - name
        - code
      properties:
        ID:
          type: string
          description: Идентификатор типа занятости
        name:
          type: string
          description: Название типа занятости
        code:
          type: string
          description: Код типа занятости
    RelationType:
      type: object
      required:
        - ID
        - name
        - code
      properties:
        ID:
          type: string
          description: Идентификатор родства контактного лица
        name:
          type: string
          description: Название родства контактного лица
        code:
          type: string
          description: Код родства контактного лица
    GetSurveyContactPerson:
      type: object
      required:
        - relType
        - firstName
        - lastName
        - phone
      properties:
        relType:
          $ref: '#/components/schemas/RelationType'
        firstName:
          type: string
          description: Имя контактного лица
        lastName:
          type: string
          description: Фамилия контактного лица
        phone:
          $ref: '#/components/schemas/PhoneNumber'
    GetSurveyResult:
      type: object
      properties:
        applicationID:
          type: string
          description: Идентификатор заявки
        children:
          type: integer
          format: int32
          description: Количество детей
        email:
          $ref: '#/components/schemas/Email'
        address:
          $ref: '#/components/schemas/GetSurveyAddress'
        educationType:
          $ref: '#/components/schemas/EducationType'
        empType:
          $ref: '#/components/schemas/EmploymentType'
        contactPersons:
          type: array
          items:
            $ref: '#/components/schemas/GetSurveyContactPerson'
          description: Список контактных лиц
    SaveSurveyAddress:
      type: object
      required:
        - katoCodes
        - street
        - building
      properties:
        katoCodes:
          type: array
          items:
            type: string
            minLength: 1
          minItems: 2
          description: Коды КАТО
        street:
          type: string
          minLength: 1
          description: Улица адреса.
        building:
          type: string
          minLength: 1
          description: Номер или название здания.
        flat:
          type: string
          description: Номер квартиры или офиса.
    SaveSurveyContactPerson:
      type: object
      required:
        - relID
        - firstName
        - lastName
        - phone
      properties:
        relID:
          type: string
          minLength: 1
          description: Идентификатор родства контактного лица.
        firstName:
          type: string
          minLength: 1
          description: Имя контактного лица.
        lastName:
          type: string
          minLength: 1
          description: Фамилия контактного лица.
        phone:
          $ref: '#/components/schemas/PhoneNumber'
    SaveSurveyResp:
      type: object
      required:
        - surveyID
      properties:
        surveyID:
          type: string
          description: Идентификатор анкеты пользователя.
    CreateLoanApplicationResp:
      type: object
      required:
        - applicationID
        - applicationStatus
      description: Объект ответа для успешного создания заявки на кредит.
      properties:
        applicationID:
          type: string
          description: Уникальный идентификатор, присвоенный созданной заявке на кредит.
        applicationStatus:
          type: string
          description: Статус заявки на кредит.
    UpdateLoanApplicationResp:
      type: object
      required:
        - applicationID
        - applicationStatus
      description: Объект ответа для успешного обновления заявки на кредит.
      properties:
        applicationID:
          type: string
          description: Уникальный идентификатор, присвоенный созданной заявке на кредит.
        applicationStatus:
          type: string
          description: Статус заявки на кредит.
    GetInternalChecksResultResp:
      type: object
      required:
        - applicationStatus
        - isInProgress
      properties:
        applicationStatus:
          type: string
          description: Статус заявки на кредит.
        isInProgress:
          type: boolean
          description: Признак наличия в процессе выполнения внутренних проверок
        reason:
          $ref: '#/components/schemas/ErrorReason'
    GetEducationTypesResp:
      type: object
      required:
        - educationTypes
      properties:
        educationTypes:
          type: array
          description: Список справочников по типам образования
          items:
            $ref: '#/components/schemas/EducationType'
    GetEmploymentTypesResp:
      type: object
      required:
        - employmentTypes
      properties:
        employmentTypes:
          type: array
          description: Список справочников по типам занятости
          items:
            $ref: '#/components/schemas/EmploymentType'
    GetRelationTypesResp:
      type: object
      required:
        - relationTypes
      properties:
        relationTypes:
          type: array
          description: Список справочников по видам отношений к контактным лицам
          items:
            $ref: '#/components/schemas/RelationType'
    CancelLoanApplicationResp:
      type: object
      required:
        - applicationID
      properties:
        applicationID:
          type: string
          description: Уникальный идентификатор заявки на кредит
    CheckActiveLoanAppExistsResp:
      type: object
      required:
        - hasActive
      properties:
        hasActive:
          type: boolean
          description: Признак наличия активной кредитной заявки
        reason:
          $ref: '#/components/schemas/ErrorReason'
    LoanAppDocumentType:
      type: string
      enum:
        - complexConsentClient
      description: Тип (шаблон) документа
    LoanApplicationDocument:
      type: object
      required:
        - ID
        - type
        - title
        - version
        - fileLink
        - signed
      properties:
        ID:
          type: string
          format: uuid
          description: Идентификатор документа
        title:
          type: string
          description: Название документа
        type:
          $ref: '#/components/schemas/LoanAppDocumentType'
        version:
          type: integer
          description: Версия документа, которая соотносится с типом документа (версия шаблона)
        fileLink:
          type: string
          description: Ссылка на физический файл
        signed:
          type: boolean
          description: Был ли документ подписан пользователем
    LoanApplicationDocumentsResp:
      type: object
      required:
        - documents
      properties:
        documents:
          type: array
          description: Документы сгенерированные по запросу
          items:
            $ref: '#/components/schemas/LoanApplicationDocument'
    DocumentForSign:
      type: object
      required:
        - ID
        - title
        - type
        - version
        - fileLink
        - signed
      description: Документ для подписи
      properties:
        ID:
          type: string
          description: Уникальный идентификатор документа
          example: 3fa85f64-5717-4562-b3fc-2c963f66afa6
        title:
          type: string
          description: Название документа
          example: Заявление на открытие счета
        type:
          type: string
          description: Тип (шаблон) документа
          example: accountOpeningApplication
        fileLink:
          type: string
          description: Ссылка на физический файл
          example: https://s3.somelink.com/document.pdf
        version:
          type: integer
          description: Версия документа, которая соотносится с типом документа (версия шаблона)
          example: 0
        signed:
          type: boolean
          description: Был ли документ подписан пользователем
          example: false
    DocumentForSignResp:
      type: object
      required:
        - documents
      properties:
        documents:
          type: array
          description: Список документов для подписи
          items:
            $ref: '#/components/schemas/DocumentForSign'
    GetApprovedLoanAppStatusResp:
      type: object
      required:
        - applicationStatus
        - isInProgress
      properties:
        applicationStatus:
          type: string
          description: Статус заявки на кредит.
        nextStep:
          type: string
          description: Следующий шаг.
        isInProgress:
          type: boolean
          description: Признак наличия в процессе выполнения внутренних проверок
        reason:
          $ref: '#/components/schemas/ErrorReason'
    AttachedDocData:
      type: object
      required:
        - bankID
        - docID
      properties:
        bankID:
          type: string
          description: Идентификатор банка, согласно справочнику
        docID:
          type: string
          description: Идентификатор документа из временного хранилища
    PublishLoanAppResp:
      type: object
      required:
        - applicationStatus
      properties:
        applicationStatus:
          type: string
          description: Статус заявки на кредит
    LoanAppConditionsAmountData:
      type: object
      description: Сумма финансирования, одобренная СПР
      required:
        - value
        - currencyCode
      properties:
        value:
          type: string
          example: '100000'
          description: Сумма финансирования
        currencyCode:
          type: string
          description: Валюта
          example: KZT
    NextPaymentAmountData:
      type: object
      description: Ежемесячный платеж
      required:
        - value
        - currencyCode
      properties:
        value:
          type: string
          example: '100000'
          description: Сумма ежемесячного платежа
        currencyCode:
          type: string
          description: Валюта
          example: KZT
    InterestAmountData:
      type: object
      description: Наценка
      required:
        - value
        - currencyCode
      properties:
        value:
          type: string
          example: '100000'
          description: Сумма наценки
        currencyCode:
          type: string
          description: Валюта
          example: KZT
    ScoringLoanConditions:
      type: object
      required:
        - title
        - hint
        - amount
        - term
        - nextPaymentAmount
        - interestAmount
      properties:
        title:
          type: string
          description: Заголовок экрана
        hint:
          type: string
          description: Текст под условиями предложения
        amount:
          $ref: '#/components/schemas/LoanAppConditionsAmountData'
        term:
          $ref: '#/components/schemas/Term'
        nextPaymentAmount:
          $ref: '#/components/schemas/NextPaymentAmountData'
        interestAmount:
          $ref: '#/components/schemas/InterestAmountData'
    RefinancingConditions:
      type: object
      properties:
        title:
          type: string
        totalAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Общая сумма
        refAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма на погашение кредитов в других банках
        creditAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма зачисления (полученная клиентом на руки)
        paymentAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Ежемесячный платеж
        interestAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Наценка
        term:
          $ref: '#/components/schemas/Term'
      required:
        - title
        - totalAmount
        - refAmount
        - creditAmount
        - paymentAmount
        - interestAmount
        - term
    ExternalBankLoan:
      type: object
      properties:
        bankName:
          type: string
          description: Наименование банка
        bankBIN:
          type: string
          description: БИН банка
        outstandingAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма задолженности по одному банку
        paymentAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Ежемесячный платеж по одному банку
        contractNumber:
          type: string
          description: Номер договора
        contractDate:
          type: string
          format: date-time
          description: Дата договора
        image:
          type: string
          description: Ссылка на изображение
      required:
        - bankName
        - bankBIN
        - outstandingAmount
        - paymentAmount
        - contractNumber
        - contractDate
        - image
    GetScoringResultRespExternalBankLoansInfo:
      type: object
      required:
        - title
        - totalOutstandingAmount
        - totalPaymentAmount
        - items
      properties:
        title:
          type: string
        totalOutstandingAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Общая сумма задолженности по всем займам в других банках
        totalPaymentAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Общая сумма ежемесячного платежа при рефинансировании
        items:
          type: array
          description: Блок с информацией по каждому займу
          items:
            $ref: '#/components/schemas/ExternalBankLoan'
    GetScoringResultRespRefinancingInfo:
      type: object
      properties:
        conditions:
          $ref: '#/components/schemas/RefinancingConditions'
        externalBankLoansInfo:
          $ref: '#/components/schemas/GetScoringResultRespExternalBankLoansInfo'
    ScoringResultResp:
      type: object
      required:
        - applicationStatus
      properties:
        applicationStatus:
          type: string
          description: Статус заявки на кредит
        productType:
          type: string
          description: Тип продукта (LOAN/REFINANCING)
          example: LOAN
        reason:
          $ref: '#/components/schemas/ErrorReason'
        loanConditions:
          $ref: '#/components/schemas/ScoringLoanConditions'
        refinancing:
          $ref: '#/components/schemas/GetScoringResultRespRefinancingInfo'
    BtsDataResp:
      type: object
      required:
        - link
        - redirectURI
      properties:
        link:
          type: string
          description: Ссылка для прохождения идентификации
        redirectURI:
          type: string
          description: Ссылка для редиректа
    PostIdentifyBtsDataResp:
      type: object
      required:
        - applicationStatus
      properties:
        applicationStatus:
          type: string
          description: Статус заявки на кредит
        reason:
          $ref: '#/components/schemas/ErrorReason'
    CreditData:
      type: object
      description: Данные о кредите
      required:
        - amount
        - label
      properties:
        amount:
          $ref: '#/components/schemas/Money'
        label:
          type: string
          description: Текст о кредите
    RefinancingData:
      type: object
      description: Данные о рефинансировании
      required:
        - ref
        - credit
        - hint
      properties:
        ref:
          $ref: '#/components/schemas/CreditData'
        credit:
          $ref: '#/components/schemas/CreditData'
        hint:
          type: string
          description: Подсказка для пользователя
    PostEdsBtsDataResp:
      type: object
      required:
        - productType
        - title
        - amount
        - term
        - subtitle
      properties:
        productType:
          type: string
          description: Тип продукта (обычное финансирование, рефинансирование)
        title:
          type: string
          description: Заголовок сообщения пользователю
        amount:
          $ref: '#/components/schemas/Money'
        term:
          type: string
          description: Текст по сроку кредита
        subtitle:
          type: string
          description: Подзаголовок сообщения пользователю
        refinancing:
          $ref: '#/components/schemas/RefinancingData'
    PaymentHintData:
      type: object
      description: Подсказка в карточке кредита
      required:
        - title
        - text
        - infoUrl
      properties:
        title:
          type: string
          description: Заголовок подсказки
          example: string
        text:
          type: string
          description: Текст подсказки
          example: string
        infoUrl:
          type: string
          description: URL сайта банка с информацией по способам внесения платежей
          example: string
    PaidInfo:
      type: object
      description: Информация для виджета
      required:
        - percentPaid
        - remainingAmount
        - text
      properties:
        percentPaid:
          type: string
          description: Поле для заполнения виджета оставшейся суммы выплаты по кредиту
        remainingAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Оставшаяся сумма выплаты по кредиту
        text:
          type: string
          description: Текст под виджетом оставшейся суммы выплаты по кредиту
    NextPayment:
      type: object
      description: Информация о ближайшем платеже
      required:
        - date
        - amount
      properties:
        date:
          type: string
          format: date
          description: Дата ближайшего платежа
        amount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма ближайшего платежа
    Account:
      type: object
      description: Информация по счету
      required:
        - ibanLastDigits
        - amount
        - iban
        - iin
        - fullName
        - bankName
        - bankBin
        - bankBic
      properties:
        ibanLastDigits:
          type: string
          description: Замаскированный номер счета списания (отображать только 4 последние символа)
        amount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Баланс счета
        iban:
          type: string
          description: Счет для погашения (IBAN)
        iin:
          type: string
          description: ИИН клиента
        fullName:
          type: string
          description: ФИО клиента
        bankName:
          type: string
          description: Наименование банка
        bankBin:
          type: string
          description: БИН банка
        bankBic:
          type: string
          description: БИК банка
    LoanDetailsContract:
      type: object
      description: Подписанный договор
      required:
        - number
        - title
        - docID
        - fileLink
      properties:
        number:
          type: string
          description: Номер кредитного договора
        title:
          type: string
          description: Заголовок для документа
        docID:
          type: string
          description: ID подписанного договора
        fileLink:
          type: string
          description: Ссылка на подписанный договор, хранящийся в S3 в общей папке
    LoanDetailsObject:
      type: object
      description: Детали финансирования
      required:
        - contract
        - loanAmount
        - startDate
        - endDate
        - term
        - interestAmount
      properties:
        contract:
          $ref: '#/components/schemas/LoanDetailsContract'
        loanAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма финансирования (на весь срок)
        startDate:
          type: string
          format: date
          description: Дата начала договора
        endDate:
          type: string
          format: date
          description: Дата окончания договора
        term:
          $ref: '#/components/schemas/Term'
        interestAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма наценки на весь срок финансирования
    Overdue:
      type: object
      description: Просроченный платеж
      required:
        - hint
        - days
        - fineDebt
      properties:
        hint:
          type: string
          description: Подсказка по просроченному платежу
        days:
          type: integer
          description: Количество дней просрочки
        fineDebt:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма пени
    PaymentDetails:
      type: object
      description: Детали платежа
      required:
        - date
        - baseAmount
        - interestAmount
      properties:
        date:
          type: string
          format: date
          description: Дата платежа
        baseAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма финансирования (за 1 месяц)
        interestAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма наценки (заполняется в зависимости от наличия просрочки)
        overdue:
          $ref: '#/components/schemas/Overdue'
    ScheduleItem:
      type: object
      description: График погашения
      required:
        - paymentAmount
        - date
        - status
        - details
      properties:
        paymentAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма платежа (Сумма финансирования (baseAmount) + Наценка (interestAmount))
        date:
          type: string
          format: date
          description: Дата платежа
        status:
          type: string
          description: Статус платежа PLAN - К оплате END - Погашен OVERDUE - Просрочен
          enum:
            - PLAN
            - END
            - OVERDUE
        details:
          $ref: '#/components/schemas/PaymentDetails'
    LoanDetails:
      type: object
      description: Информация по кредиту
      required:
        - hasOverdue
        - productType
        - paidInfo
        - nextPayment
        - account
        - details
        - schedule
      properties:
        hasOverdue:
          type: boolean
        productType:
          type: string
          description: Тип продукта LOAN - Кредит REFINANCE - Рефинансирование
          enum:
            - LOAN
            - REFINANCE
        paidInfo:
          $ref: '#/components/schemas/PaidInfo'
        nextPayment:
          $ref: '#/components/schemas/NextPayment'
        account:
          $ref: '#/components/schemas/Account'
        details:
          $ref: '#/components/schemas/LoanDetailsObject'
        schedule:
          type: array
          description: График погашения
          items:
            $ref: '#/components/schemas/ScheduleItem'
    LinkObject:
      type: object
      required:
        - title
        - url
      properties:
        title:
          type: string
          description: Титры
        url:
          type: string
          description: Url
    Links:
      type: object
      description: Блок с URL
      required:
        - earlyRepayment
        - inquiry
        - faq
      properties:
        earlyRepayment:
          allOf:
            - $ref: '#/components/schemas/LinkObject'
          description: URL сайта банка с информацией по досрочному погашению
        inquiry:
          allOf:
            - $ref: '#/components/schemas/LinkObject'
          description: URL сайта банка с информацией по справке
        faq:
          allOf:
            - $ref: '#/components/schemas/LinkObject'
          description: URL сайта банка с информацией по вопросам и ответам
    GetLoansDetailsEarlyRepaymentAllDebtToPay:
      type: object
      required:
        - label
        - amount
      properties:
        label:
          type: string
        amount:
          $ref: '#/components/schemas/Money'
    GetLoansDetailsEarlyRepaymentPartialRepaymentLimit:
      type: object
      required:
        - hint
        - minAmount
        - maxAmount
      properties:
        hint:
          type: string
        minAmount:
          $ref: '#/components/schemas/Money'
        maxAmount:
          $ref: '#/components/schemas/Money'
    GetLoansDetailsEarlyRepaymentPartialRepaymentNextPayment:
      type: object
      required:
        - hint
      properties:
        hint:
          type: string
    GetLoansDetailsEarlyRepaymentPartialRepayment:
      type: object
      required:
        - limit
        - nextPayment
      properties:
        limit:
          $ref: '#/components/schemas/GetLoansDetailsEarlyRepaymentPartialRepaymentLimit'
        nextPayment:
          $ref: '#/components/schemas/GetLoansDetailsEarlyRepaymentPartialRepaymentNextPayment'
    GetLoansDetailsEarlyRepayment:
      type: object
      required:
        - isAvailable
      properties:
        isAvailable:
          type: boolean
        reason:
          $ref: '#/components/schemas/ErrorReason'
        allDebtToPay:
          $ref: '#/components/schemas/GetLoansDetailsEarlyRepaymentAllDebtToPay'
        partialRepayment:
          $ref: '#/components/schemas/GetLoansDetailsEarlyRepaymentPartialRepayment'
    GetLoansDetailsResponse:
      type: object
      required:
        - paymentHint
        - loan
        - links
        - earlyRepayment
      properties:
        paymentHint:
          $ref: '#/components/schemas/PaymentHintData'
        loan:
          $ref: '#/components/schemas/LoanDetails'
        links:
          $ref: '#/components/schemas/Links'
        earlyRepayment:
          $ref: '#/components/schemas/GetLoansDetailsEarlyRepayment'
    EarlyRepayAmount:
      type: object
      description: Сумма ЧДП/ПДП
      required:
        - value
        - currencyCode
      properties:
        value:
          type: string
          description: Сумма досрочного погашения
          example: '10000'
        currencyCode:
          type: string
          description: Валюта
          example: KZT
    EarlyRepayContract:
      type: object
      description: Оплата финансирования
      required:
        - number
        - title
      properties:
        number:
          type: string
          description: Номер кредитного договора
        title:
          type: string
          description: Заголовок для документа
          example: Договор финансирования
    PostEarlyRepayResp:
      type: object
      required:
        - repayAmount
      properties:
        repayAmount:
          $ref: '#/components/schemas/EarlyRepayAmount'
        contract:
          $ref: '#/components/schemas/EarlyRepayContract'
    LoansConfirmSignDocumentsResponse:
      type: object
      required:
        - documents
      properties:
        documents:
          type: array
          description: Подписанные документы
          items:
            $ref: '#/components/schemas/Document'
    ExternalBank:
      type: object
      required:
        - ID
        - name
        - instructions
        - logoUrl
      properties:
        ID:
          type: string
          description: Уникальный идентификатор
        name:
          type: string
          description: Наименование банка для отображения на UI клиенту
          example: Kaspi
        instructions:
          type: string
          description: Инструкция для выгрузки выписки из банка в виде списка шагов, т.к. так проще отображать на UI
        logoUrl:
          type: string
          description: Логотип банка (ссылка на изображение)
    BankStatements:
      type: object
      required:
        - banks
        - formats
        - maxFileSize
        - months
      properties:
        banks:
          type: array
          description: Банки для загрузки выписок
          items:
            $ref: '#/components/schemas/ExternalBank'
        formats:
          type: string
          example: PDF
          description: Разрешенные форматы для выписок
        maxFileSize:
          type: number
          example: 10
          description: Максимальное кол-во мегабайт для выписок
        months:
          type: number
          example: 6
          description: Кол-во месяцев, за которые нужна выписка
    GetBankStatementV2Bank:
      type: object
      required:
        - ID
        - name
        - logoUrl
      properties:
        ID:
          type: string
          description: БИК банка
        name:
          type: string
          description: Наименование банка
        instructions:
          type: string
          description: Инструкция для выгрузки выписки из банка
        logoUrl:
          type: string
          description: Логотип банка (ссылка на изображение)
        statementName:
          type: string
          description: Наименование файла выписки (возвращается только при наличии некорректного файла)
        isStatementValid:
          type: boolean
          description: |
            Флаг корректного файла выписки
    GetBankStatementV2Instruction:
      type: object
      description: Инструкция
      required:
        - title
        - description
      properties:
        title:
          type: string
          description: Заголовок инструкции
          example: “Как скачать выписку?”
        description:
          type: string
          description: |
            Описание инструкции
          example: “В приложении любого банка перейдите на страницу с деталями карты, затем выберите «Выписка»”
    GetBankStatementV2StatementHint:
      type: object
      description: Баннер с подсказкой для клиента о преимуществе выписки
      required:
        - title
        - description
        - instruction
      properties:
        title:
          type: string
          description: |
            Заголовок
          example: “Увеличьте вероятность одобрения в 2 раза”
        description:
          type: string
          description: |
            Описание
          example: “Чем больше выписок по карте вы приложите из любых банков, тем выше вероятность одобрения”
        instruction:
          $ref: '#/components/schemas/GetBankStatementV2Instruction'
    GetBankStatementV2Response:
      type: object
      required:
        - banks
        - maxFileSizeMb
        - periodMonths
        - statementHint
      properties:
        banks:
          type: array
          description: Информация по банкам (Справочник банков для загрузки выписки)
          items:
            $ref: '#/components/schemas/GetBankStatementV2Bank'
        maxFileSizeMb:
          type: number
          description: Максимальное количество мегабайт для выписки (из конфига, напр. "10")
        periodMonths:
          type: number
          description: Период (количество месяцев), за который нужна выписка (из конфига, напр. "6")
        statementHint:
          $ref: '#/components/schemas/GetBankStatementV2StatementHint'
    RefinancingFeature:
      type: object
      required:
        - image
        - description
      properties:
        image:
          type: string
          description: Ссылка на изображение
        description:
          type: string
          description: Описание преимущества
    RefinancingStep:
      type: object
      required:
        - description
      properties:
        description:
          type: string
          description: Описание шага
    RefinancingSteps:
      type: object
      required:
        - title
        - items
      properties:
        title:
          type: string
          description: Заголовок блока шагов
        items:
          type: array
          description: Список шагов
          items:
            $ref: '#/components/schemas/RefinancingStep'
    GetRefinancingInfoResp:
      type: object
      required:
        - title
        - description
        - features
        - steps
      properties:
        title:
          type: string
          description: Заголовок страницы
        description:
          type: string
          description: Основное описание
        subtitle:
          type: string
          description: Подзаголовок
        features:
          type: array
          description: Преимущества рефинансирования
          items:
            $ref: '#/components/schemas/RefinancingFeature'
        steps:
          $ref: '#/components/schemas/RefinancingSteps'
    UserExternalBankLoan:
      type: object
      required:
        - iban
        - contractNumber
      properties:
        iban:
          type: string
          description: IBAN банка
          minLength: 20
          maxLength: 20
          pattern: ^KZ\d{2}\d{3}[A-Z0-9]{13}$
        contractNumber:
          type: string
          description: Номер контракта
    SaveUserExternalBankLoansResp:
      type: object
      properties: {}
    PaymentsOperationType:
      type: string
      enum:
        - CREDIT
        - DEBIT
      description: Тип операции
    PaymentsTransactionStatus:
      type: string
      enum:
        - IN_PROGRESS
        - COMPLETED
        - REJECTED
      description: Статус транзакции
    TransactionType:
      type: string
      enum:
        - OTHER
        - PAYMENT_BY_ACCOUNT
        - PAYMENT_MOBILE
        - PAYMENT_TERMINAL
        - PAYMENT_BY_PHONE_NUMBER
        - BETWEEN_OWN_ACCKZT
      description: Внутренний тип перевода
    PaymentsTransaction:
      type: object
      required:
        - transactionID
        - accountNumber
        - amount
        - currency
        - direction
        - status
        - counterparty
        - transactionDate
        - transactionType
      properties:
        transactionID:
          type: string
          description: Уникальный идентификатор транзакции
        accountNumber:
          type: string
          description: Номер cчёта
        amount:
          type: number
          format: double
          description: Сумма транзакции
          example: 99.95
        currency:
          type: string
          description: Валюта транзакции
        direction:
          $ref: '#/components/schemas/PaymentsOperationType'
          description: Тип операции (кредит/дебет)
        status:
          $ref: '#/components/schemas/PaymentsTransactionStatus'
          description: Статус транзакции
        counterparty:
          type: string
          description: Контрагент
        transactionDate:
          type: string
          format: date
          description: Дата операции
        transactionType:
          $ref: '#/components/schemas/TransactionType'
          description: Тип транзакции
    PaymentsGetTransactionsResponse:
      type: object
      required:
        - transactions
        - totalCount
        - limit
        - offset
        - startDate
      properties:
        transactions:
          type: array
          items:
            $ref: '#/components/schemas/PaymentsTransaction'
          description: Список транзакций
        totalCount:
          type: integer
          format: int64
          description: Общее количество транзакций
        limit:
          type: integer
          format: int64
          description: Лимит количества возвращаемых транзакций
        offset:
          type: integer
          format: int64
          description: Смещение для пагинации
        startDate:
          type: string
          format: date-time
          description: Начальная дата для фильтрации транзакций
    PaymentsGetTransactionByIDResponse:
      type: object
      properties:
        transactionID:
          type: string
          description: Идентификатор транзакции
        transactionNumber:
          type: string
          description: Пользовательский номер транзакции
        transactionDate:
          type: string
          format: date-time
          description: Дата операции
        valueDate:
          type: string
          format: date-time
          description: Дата исполнения операции
        transactionType:
          $ref: '#/components/schemas/TransactionType'
          description: Тип транзакции
        direction:
          $ref: '#/components/schemas/PaymentsOperationType'
          description: Тип операции (кредит/дебет)
        status:
          $ref: '#/components/schemas/PaymentsTransactionStatus'
          description: Статус транзакции
        amount:
          type: number
          format: double
          description: Сумма операции
          example: 99.95
        commission:
          type: number
          format: double
          description: Коммиссия
          example: 1.95
        currency:
          type: string
          description: Валюта операции
          example: KZT
        payerIinBin:
          type: string
          description: ИИН/БИН отправителя
        payerName:
          type: string
          description: Наименование отправителя
        payerAccount:
          type: string
          description: Номер счёта отправителя
        beneficiaryIinBin:
          type: string
          description: ИИН/БИН получателя
        beneficiaryName:
          type: string
          description: Наименование получателя
        beneficiaryAccount:
          type: string
          description: Номер счёта получателя
        kbe:
          type: string
          description: Код бенефициара
        phoneNumber:
          type: string
          description: Номер телефона
          example: '+***********'
        phoneOperatorName:
          type: string
          description: Наименование мобильного оператора
        transactionDetails:
          type: string
          description: Детали транзакции
        knp:
          type: string
          description: Код назначения платежа (КНП)
      required:
        - transactionID
        - transactionNumber
        - transactionDate
        - transactionType
        - direction
        - status
        - amount
        - commission
        - currency
        - transactionDetails
        - knp
    PaymentsGetTransactionReceiptResponse:
      type: object
      properties:
        title:
          type: string
          description: Наименование документа
        fileLink:
          type: string
          description: Ссылка на файл
      required:
        - title
        - fileLink
    PaymentsGetHistoryItem:
      type: object
      required:
        - clientIinBin
        - clientName
        - clientAccount
        - clientBankName
        - clientBankBic
        - counterpartyIinBin
        - counterpartyName
        - counterpartyAccount
        - counterpartyBankName
        - counterpartyBankBic
        - counterpartyKbe
        - amount
        - currency
        - knp
        - paymentDetails
        - date
      properties:
        clientIinBin:
          type: string
          description: ИИН/БИН клиента
        clientName:
          type: string
          description: Наименование клиента
        clientAccount:
          type: string
          description: Номер счёта клиента
        clientBankName:
          type: string
          description: Наименование банка клиента
        clientBankBic:
          type: string
          description: БИК банка
        counterpartyIinBin:
          type: string
          description: ИИН/БИН контрагента
        counterpartyName:
          type: string
          description: Название контрагента
        counterpartyAccount:
          type: string
          description: Счёт контрагента
        counterpartyBankName:
          type: string
          description: Название банка контрагента
        counterpartyBankBic:
          type: string
          description: БИК банка контрагента
        counterpartyKbe:
          type: string
          description: КБЕ контрагента
        amount:
          type: number
          format: double
          description: Сумма платежа
        currency:
          type: string
          description: Валюта платежа
        knp:
          type: string
          description: Код назначения платежа (КНП)
        paymentDetails:
          type: string
          description: Детали платежа
        date:
          type: string
          format: date
          description: Дата платежа
        valueDate:
          type: string
          format: date
          description: Дата исполнения операции
        actualClientIinBin:
          type: string
          description: ИИН/БИН фактического клиента (опционально)
        actualClientName:
          type: string
          description: Имя фактического клиента (опционально)
        actualClientCountry:
          type: string
          description: Страна фактического клиента (опционально)
        actualCounterpartyIinBin:
          type: string
          description: ИИН/БИН фактического контрагента (опционально)
        actualCounterpartyName:
          type: string
          description: Имя фактического контрагента (опционально)
        actualCounterpartyCountry:
          type: string
          description: Страна фактического контрагента (опционально)
    PaymentsGetHistoryResponse:
      type: object
      properties:
        payments:
          type: array
          items:
            $ref: '#/components/schemas/PaymentsGetHistoryItem'
          description: Список элементов истории платежей
    TaxPayerType:
      type: string
      enum:
        - INDIVIDUAL
        - LEGAL_ENTITY
      description: Тип налогоплательщика
    AdditionalIndividualType:
      type: object
      description: Дополнительный тип для ФЛ если есть
      required:
        - type
        - name
      properties:
        type:
          type: integer
          description: Список кодов 32 - ИП, 64 — адвокат, 128 — частный нотариус, 256 — частный судебный исполнитель
        name:
          type: string
          description: Наименование дополнительного бенефициара для данного доп.  типа ФЛ
    PaymentsCheckAccountIinResponse:
      type: object
      required:
        - name
        - taxPayerType
        - bankBic
        - bankName
      properties:
        name:
          type: string
          description: Наименование ЮЛ/ФЛ
        taxPayerType:
          $ref: '#/components/schemas/TaxPayerType'
          description: Тип физ лица ЮЛ/ФЛ
        additionalIndividualType:
          $ref: '#/components/schemas/AdditionalIndividualType'
        bankBic:
          type: string
          description: БИК банка
        bankName:
          type: string
          description: Название банка
    OtpFullResponse:
      type: object
      properties:
        attemptID:
          type: string
          format: uuid
          description: Идентификатор для валидации отп кода
          example: 2100e82d-e288-4882-8fac-3f1403449051
        retryTime:
          type: integer
          description: Количество секунд до следующей отправки
          example: 60
        codeChecksLeft:
          type: integer
          description: Количество оставшихся попыток проверки кода отп
        attemptsLeft:
          type: integer
          description: Количество оставшихся попыток
        attemptsTimeout:
          type: integer
          description: Количество секунд жизни попытки отп валидации
      required:
        - attemptID
        - retryTime
        - codeChecksLeft
        - attemptsLeft
        - attemptsTimeout
    CreatePaymentByAccountResponse:
      type: object
      required:
        - status
        - message
        - transactionID
        - otpRequired
      properties:
        status:
          type: string
          description: Статус операции
          example: success
        message:
          type: string
          description: Информация из бэк по статусу
          example: Платеж в обработке
        otpRequired:
          type: boolean
          description: Признак нужно ли проводить проверки по отп
          example: true
        transactionID:
          type: string
          format: uuid
          description: Идентификатор транзакции в БД
          example: 41c3f02f-436d-4f9b-8657-694f4c8890f8
        otpResponse:
          $ref: '#/components/schemas/OtpFullResponse'
    ConfirmPaymentByAccountResponse:
      type: object
      required:
        - status
        - message
      properties:
        status:
          type: string
          description: Статус операции
        message:
          type: string
          description: Информация из бэк по статусу
    CheckPhoneNumberResponse:
      type: object
      properties:
        phoneOperator:
          type: string
      required:
        - phoneOperator
    CreatePaymentForMobileResponse:
      type: object
      properties:
        transactionID:
          type: string
          format: uuid
          description: Идентификатор транзакции в БД
          example: 41c3f02f-436d-4f9b-8657-694f4c8890f8
        status:
          $ref: '#/components/schemas/PaymentsTransactionStatus'
          description: Статус транзакции
        otpRequired:
          type: boolean
          description: Признак нужно ли проводить проверки по отп
          example: true
        otpResponse:
          $ref: '#/components/schemas/OtpFullResponse'
      required:
        - transactionID
        - status
        - otpRequired
    ConfirmPaymentForMobileResponse:
      type: object
      required:
        - status
      properties:
        status:
          $ref: '#/components/schemas/PaymentsTransactionStatus'
          description: Статус транзакции
    CheckClientByPhoneNumberResponse:
      type: object
      required:
        - clientName
      properties:
        clientName:
          type: string
          description: Наименование клиента банка в сокращённом варианте
          example: Имя Ф.
    CreateInternalPaymentByPhoneNumberResponse:
      type: object
      properties:
        transactionID:
          type: string
          format: uuid
          description: Идентификатор транзакции в БД
          example: 41c3f02f-436d-4f9b-8657-694f4c8890f8
        status:
          $ref: '#/components/schemas/PaymentsTransactionStatus'
          description: Статус транзакции
        otpRequired:
          type: boolean
          description: Признак нужно ли проводить проверки по отп
          example: true
        otpResponse:
          $ref: '#/components/schemas/OtpFullResponse'
      required:
        - transactionID
        - status
        - otpRequired
    ConfirmInternalPaymentByPhoneNumberResponse:
      type: object
      required:
        - status
      properties:
        status:
          $ref: '#/components/schemas/PaymentsTransactionStatus'
          description: Статус транзакции
    CreateSelfTransferResponse:
      type: object
      required:
        - status
      properties:
        transactionID:
          type: string
          format: uuid
          description: Идентификатор транзакции в БД
          example: 41c3f02f-436d-4f9b-8657-694f4c8890f8
        status:
          $ref: '#/components/schemas/PaymentsTransactionStatus'
          description: Статус транзакции
    CreatePaymentKaspiQRResponse:
      type: object
      required:
        - status
        - transactionID
      properties:
        transactionID:
          type: string
          format: uuid
          description: Идентификатор транзакции в БД
          example: 41c3f02f-436d-4f9b-8657-694f4c8890f8
        status:
          $ref: '#/components/schemas/PaymentsTransactionStatus'
          description: Статус транзакции
    QRTokenResponse:
      type: object
      required:
        - qrCodeType
        - paymentID
      properties:
        qrCodeType:
          type: string
          description: Тип QR токена
        merchantName:
          type: string
          description: Наименование продавца
        paymentID:
          type: string
          description: Идентификатор платежа в ПС
        paymentAmount:
          type: string
          description: Сумма платежа
        paymentData:
          type: object
          description: Объект содержащий описание всех дополнительных полей
          properties:
            serviceId:
              type: integer
              format: int32
              description: Идентификатор сервиса
            serviceName:
              type: string
              description: Наименование сервиса
            parameters:
              type: array
              description: Массив параметров
              items:
                type: object
                properties:
                  regex:
                    type: string
                    description: Параметры для валидации поля
                    example: ^7\d{9}$
                  value:
                    type: string
                    description: Значение параметра. Пустое — значит, клиент должен заполнить значение
                  name:
                    type: string
                    description: Описание value на языке локализации
                  options:
                    type: array
                    description: Элементы выпадающего списка
                    items:
                      type: object
                      properties:
                        key:
                          type: string
                          description: Идентификатор элемента
                        label:
                          type: string
                          description: Отображаемое название
                        description:
                          type: string
                          description: Дополнительное описание
    QRSessionTerminationResponse:
      type: object
      required:
        - result
      properties:
        result:
          type: string
    Task:
      type: object
      properties:
        taskID:
          type: string
          format: uuid
          pattern: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
          maxLength: 36
        type:
          type: string
        status:
          type: string
        payload:
          type: object
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    GetTasksListResponse:
      type: object
      required:
        - total_count
        - page
        - page_size
        - tasks
      properties:
        total_count:
          type: integer
          description: Общее количество задач
        page:
          type: integer
          description: Номер страницы
        page_size:
          description: Размер страницы
          type: integer
        tasks:
          type: array
          description: Список задач
          items:
            $ref: '#/components/schemas/Task'
    GetTaskDetailsResponse:
      type: object
      required:
        - taskID
        - task_type
        - status
        - payload
        - created_at
        - updated_at
      properties:
        taskID:
          type: string
          description: Идентификатор задачи
          format: uuid
          pattern: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
          maxLength: 36
        task_type:
          type: string
          description: Тип задачи
        status:
          type: string
          description: Статус задачи
        payload:
          type: object
          description: Полезная нагрузка задачи
        created_at:
          type: string
          description: Дата создания задачи
          format: date-time
        updated_at:
          type: string
          description: Дата обновления задачи
          format: date-time
    Wallet:
      type: string
      description: Целевой кошелёк
      enum:
        - GooglePay
        - ApplePay
    TokenizeStartReq:
      type: object
      required:
        - cardId
        - wallet
      properties:
        cardId:
          type: string
          format: uuid
          description: ID карты, которую нужно токенизировать
        wallet:
          $ref: '#/components/schemas/Wallet'
    HexString:
      type: string
      description: HEX-строка (0-9a-fA-F)
      pattern: ^[0-9a-fA-F]+$
    TokenizeStartResp:
      type: object
      required:
        - issuerId
        - scheme
        - encryptedCardInfo
        - authCode
      properties:
        encryptedCardInfo:
          $ref: '#/components/schemas/HexString'
          description: |
            Зашифрованные данные карты (HEX).
        authCode:
          type: string
          description: |
            Шестнадцатеричная строка зашифрованной полезной нагрузки с открытым ключом TSH / PKCS7.
            Контент: AES-256/CBC/PKCS7Padding; ключ: RSA-OAEP-SHA-256 (MGF1 c SHA-256).
            Соответствует полю OPC в ответе backend.
        issuerId:
          type: string
          description: Идентификатор эмитента
        scheme:
          type: string
          description: Платёжная схема токенизации
          example: MASTERCARD
        userAddress:
          type: string
          description: Адрес пользователя
          nullable: true
    TokenizeStatus:
      type: string
      description: Минимизированный статус токена для UI-иконки.
      enum:
        - tokenized
        - need_verify
        - not_tokenized
    TokenizeStateResp:
      type: object
      required:
        - isTokenized
      properties:
        isTokenized:
          $ref: '#/components/schemas/TokenizeStatus'
        wallet:
          $ref: '#/components/schemas/Wallet'
          nullable: true
    NotifyVirtualCardChangeReq:
      type: object
      required:
        - issuerCardRefId
        - virtualCardId
        - walletProviderId
        - isPrimary
        - tokenizeStatus
      properties:
        issuerCardRefId:
          type: string
          maxLength: 48
          description: Уникальный идентификатор карты-источника у эмитента
        virtualCardId:
          type: string
          minLength: 1
          maxLength: 64
          description: Уникальный идентификатор виртуальной карты (токена)
        walletCardRefId:
          type: string
          maxLength: 128
          description: Уникальный ID карты в кошельке (если предоставлен провайдером)
        walletVirtualCardId:
          type: string
          maxLength: 128
          description: Идентификатор виртуальной карты в кошельке (для ApplePay — DPANID)
        walletProviderId:
          type: string
          minLength: 1
          maxLength: 128
          description: Идентификатор провайдера кошелька (Thales)
        tokenRequestor:
          type: object
          properties:
            id:
              type: string
              maxLength: 11
            originalTokenRequestorId:
              type: string
              maxLength: 11
            walletId:
              type: string
              maxLength: 100
            merchantId:
              type: string
              maxLength: 32
            name:
              type: string
              maxLength: 256
            tspId:
              type: string
              maxLength: 11
        tokenStorageId:
          type: string
          minLength: 1
          maxLength: 128
        isPrimary:
          type: boolean
          description: Признак основного токена
        action:
          type: string
          maxLength: 128
          description: Действие над токеном (например, DEVICE_BOUND/UNBOUND и т.п.)
        deviceBindingReference:
          type: string
          maxLength: 64
        tokenInfo:
          type: string
          minLength: 1
          maxLength: 8196
          description: Доп. информация о токене (может быть зашифрована)
        publicKeyIdentifier:
          type: string
          minLength: 1
          maxLength: 32
        errorCode:
          type: integer
          format: int32
          description: Код ошибки при неуспехе операции у эмитента/кошелька
          example: 0
        source:
          type: string
          maxLength: 32
          description: Источник изменения состояния
        tokenType:
          type: string
          maxLength: 16
          description: Тип токена (SE, HCE, COF, ECOM, QRC)
        tokenAssuranceLevel:
          type: string
          maxLength: 2
          description: Уровень достоверности токена
        tokenizeStatus:
          $ref: '#/components/schemas/TokenizeStatus'
          description: Минимизированный статус токена для upsert в БД (tokenized | need_verify | not_tokenized)
    NotifyVirtualCardChangeResp:
      type: object
      required:
        - errorCode
        - errorMessage
      properties:
        errorCode:
          type: string
          description: Код ошибки
          example: '0'
        errorMessage:
          type: string
          description: Сообщение об ошибке
          example: Ok
    TokenRequestor:
      type: object
      properties:
        id:
          type: string
        originalTokenRequestorId:
          type: string
        walletId:
          type: string
        name:
          type: string
        tspId:
          type: string
    DeviceInformation:
      type: object
      properties:
        tokenStorageId:
          type: string
        tokenStorageType:
          type: string
        manufacturer:
          type: string
        brand:
          type: string
        model:
          type: string
        osVersion:
          type: string
        firmwareVersion:
          type: string
        phoneNumber:
          type: string
        fourLastDigitPhoneNumber:
          type: string
        deviceName:
          type: string
        deviceId:
          type: string
        deviceParentId:
          type: string
        language:
          type: string
        serialNumber:
          type: string
        timeZone:
          type: string
        timeZoneSetting:
          type: string
          enum:
            - NETWORK_SET
            - MANUAL
        simSerialNumber:
          type: string
        IMEI:
          type: string
        networkOperator:
          type: string
        networkType:
          type: string
          enum:
            - CELLULAR
            - WIFI
    DeviceBinding:
      type: object
      properties:
        deviceBindingReference:
          type: string
    CardTokenInfo:
      type: object
      required:
        - issuerCardRefId
        - status
      properties:
        issuerCardRefId:
          type: string
        virtualCardId:
          type: string
        walletCardRefId:
          type: string
        walletProviderId:
          type: string
        walletVirtualCardId:
          type: string
        tokenRequestor:
          $ref: '#/components/schemas/TokenRequestor'
        deviceInformation:
          $ref: '#/components/schemas/DeviceInformation'
        deviceBindingList:
          type: array
          items:
            $ref: '#/components/schemas/DeviceBinding'
        tokenSuffix:
          type: string
        tokenDetails:
          type: string
        tokenType:
          type: string
        publicKeyIdentifier:
          type: string
        provisioningTime:
          type: string
          format: date-time
        lastReplenishTime:
          type: string
          format: date-time
        lastStatusChangeTime:
          type: string
          format: date-time
        status:
          type: string
          description: Статус по справочнику Thales (`DEPLOYMENT_ONGOING`, …)
        tokenAssuranceLevel:
          type: string
        isPrimary:
          type: boolean
    FileUploadRequestBody:
      type: object
      required:
        - file
        - filename
      properties:
        file:
          description: Контент документ для загрузки
          type: string
          format: binary
        filename:
          description: Имя файла вместе с расширением
          example: test.pdf
          type: string
    FileUploadResponse:
      type: object
      required:
        - ID
      properties:
        ID:
          description: ID загруженного файла
          type: string
    ProductInfoCurrencies:
      type: object
      required:
        - currency
        - minAmount
        - maxAmount
      properties:
        currency:
          type: string
          description: 'Код валюты (например: KZT, USD, EUR)'
          example: KZT
        minAmount:
          type: number
          format: double
          description: Минимальная сумма на пополнение
          example: 50000
        maxAmount:
          type: number
          format: double
          description: Максимальная сумма на пополнение
          example: 50000
    GetProductInfoResp:
      type: object
      required:
        - productCode
        - maxProfitRate
        - productName
        - isReplenishable
        - finalReplenishableDate
        - currencies
        - isWithdrawable
      properties:
        productCode:
          type: string
          description: Идентификатор продукта (colvir id)
          example: WAKALA
        maxProfitRate:
          type: string
          description: Максимальная процентная ставка (готовый процент)
          example: '12.5'
        productName:
          type: string
          description: Название продукта
          example: Стандартный депозит
        isReplenishable:
          type: boolean
          description: Доступно ли пополнение
          example: false
        finalReplenishableDate:
          type: string
          format: date
          nullable: true
          description: Дата, до которой будет доступно пополнение
          example: '2025-01-02'
        currencies:
          type: array
          description: Массив с информацией о доходности по выбранному продукту/сроку/валюте
          items:
            $ref: '#/components/schemas/ProductInfoCurrencies'
        isWithdrawable:
          type: boolean
          description: Доступно ли снятие (его всегда передаем false)
    DepositPayoutMethod:
      type: string
      description: Метод выплаты доходности
      enum:
        - CARD
        - DEPOSIT
      example: CARD
    CalculateProfitResp:
      type: object
      required:
        - calculatedProfit
      properties:
        calculatedProfit:
          type: number
          format: double
          description: Рассчитанная суммарная доходность за весь срок
          example: 170000
    DepositCondition:
      type: object
      required:
        - UID
        - termMonths
        - currencyCode
        - profitRate
        - effectiveProfitRate
        - isDefault
        - calculatedProfit
        - payoutMethod
      properties:
        UID:
          type: string
          format: uuid
          description: ID условия
          example: 123e4567-e89b-12d3-a456-************
        termMonths:
          type: integer
          format: uint32
          description: 'Срок продукта в месяцах (например: 3, 6, 9, 12, 24, 36)'
          example: 12
        currencyCode:
          type: string
          description: 'Короткий код валюты (например: KZT, USD, EUR)'
          example: KZT
        profitRate:
          type: number
          format: double
          description: Ставка доходности
          example: 0.14
        effectiveProfitRate:
          type: number
          format: double
          description: Ставка эффективной доходности
          example: 0.15
        isDefault:
          type: boolean
          description: Если true, то данное условие используется по-умолчанию
          example: false
        calculatedProfit:
          type: number
          format: double
          description: Рассчитанная доходность в указанной валюте
          example: 178000
        payoutMethod:
          $ref: '#/components/schemas/DepositPayoutMethod'
    DepositConditionProfitResp:
      type: object
      required:
        - depositConditions
      properties:
        depositConditions:
          type: array
          description: Массив с информацией о доходности по выбранному продукту/сроку/валюте
          items:
            $ref: '#/components/schemas/DepositCondition'
    AvailableAccount:
      type: object
      required:
        - ID
        - iban
        - type
        - status
        - currency
        - availableBalance
      properties:
        ID:
          type: string
          description: Идентификатор счёта
        iban:
          type: string
          description: Номер счета iban
        type:
          type: string
          enum:
            - CURR
            - BUFB
            - BUCO
            - TU
            - LOAN
            - OTHERS
          description: Тип счёта
        status:
          type: string
          enum:
            - ACTIVE
            - BLOCKED
            - CLOSED
            - ARRESTED
            - MISTAKEN
            - ARCHIVED
            - REOPENED
          description: Статус счёта
        currency:
          type: string
          enum:
            - KZT
            - RUB
            - EUR
            - USD
            - CNY
          description: Валюта счёта
        availableBalance:
          type: number
          format: double
          description: Доступный баланс
    GetAvailableAccountsResp:
      type: object
      required:
        - availableAccount
      properties:
        availableAccount:
          type: array
          description: Массив с информацией по активным счетам пользователя
          items:
            $ref: '#/components/schemas/AvailableAccount'
    DepositDocument:
      type: object
      required:
        - id
        - title
        - type
        - link
      properties:
        id:
          type: string
          description: ID документа из БД (documents)
          example: doc123
        title:
          type: string
          description: Название документа для фронта
          example: Договор депозита
        type:
          type: string
          description: Тип документа. Для подписания нужны типы из примера.
          example: depositApplicationRu, depositApplicationKz
        link:
          type: string
          description: Ссылка S3
          example: https://s3.example.com/document.pdf
        isSignable:
          type: boolean
          description: Необходимость подписать документ.
    CreateDepositOfferResp:
      type: object
      required:
        - currencyCode
        - profitRate
        - effectiveProfitRate
        - calculatedProfit
        - documents
      properties:
        currencyCode:
          type: string
          description: 'Короткий код валюты (например: KZT, USD, EUR)'
          example: KZT
        profitRate:
          type: number
          format: double
          description: Ставка доходности
          example: 0.15
        effectiveProfitRate:
          type: number
          format: double
          description: Ставка эффективной доходности
          example: 0.15
        calculatedProfit:
          type: number
          format: double
          description: Рассчитанная доходность в указанной валюте
          example: 178000
        documents:
          type: array
          description: Список документов
          items:
            $ref: '#/components/schemas/DepositDocument'
    DepositAccount:
      type: object
      required:
        - ID
        - accountNum
        - currency
        - agreementCode
        - balance
        - isReplenishable
        - isWithdrawal
        - payedAmount
        - profitRate
        - nearestPayDate
        - depositEndDate
      properties:
        ID:
          type: string
          description: UUID идентификатор депозита в базе данных
          example: 123e4567-e89b-12d3-a456-************
        accountNum:
          type: string
          description: Номер депозитного счета (accountCode в Колвире)
          example: ********************
        currency:
          type: string
          description: Валюта депозитного счета (currency в Колвире)
          enum:
            - KZT
            - USD
            - EUR
            - RUB
            - CNY
          example: KZT
        agreementCode:
          type: string
          description: Номер депозитного договора (code в Колвире)
          example: DEP-2024-001
        balance:
          type: number
          format: double
          description: Актуальный баланс в валюте депозитного счета (balance в Колвире)
          example: 1500000.5
        isReplenishable:
          type: boolean
          description: Доступно ли пополнение
          example: true
        isWithdrawal:
          type: boolean
          description: Доступно ли снятие
          example: false
        payedAmount:
          type: number
          format: double
          description: Общая сумма выплаченных процентов
          example: 45000
        profitRate:
          type: number
          format: double
          description: 'Ставка доходности (например: 0.15 = 15%)'
          example: 0.15
        nearestPayDate:
          type: string
          format: date
          nullable: true
          description: Следующая дата выплаты процентов (пока что, если даты нет то пишем 1970-01-01)
          example: '2024-12-31'
        depositEndDate:
          type: string
          format: date
          description: Дата окончания действия договора
          example: '2025-12-31'
    UserDepositsResponse:
      type: object
      required:
        - deposits
      properties:
        deposits:
          type: array
          description: Список депозитных счетов пользователя
          items:
            $ref: '#/components/schemas/DepositAccount'
    GetDepositDetailResp:
      type: object
      required:
        - id
        - productName
        - productCode
        - beginDate
        - endDate
        - depositAmount
        - accountNum
        - balance
        - currency
        - agreementCode
        - isReplenishable
        - replenishableDays
        - isWithdrawal
        - payedAmount
        - rate
        - effectiveRate
        - payoutMethod
        - termMonth
        - nearestPayDate
        - documents
      properties:
        id:
          type: string
          description: ID депозита
          example: 123e4567-e89b-12d3-a456-************
        productName:
          type: string
          description: Название продукта (name из раздела product)
          example: Вакала Заман
        productCode:
          type: string
          description: Код продукта (code из раздела product)
          example: WAKALA_ZAMAN
        beginDate:
          type: string
          format: date
          description: Дата открытия договора (fromDate в Колвире)
          example: '2024-01-15'
        endDate:
          type: string
          format: date
          description: Дата окончания действия договора (toDate в Колвире)
          example: '2025-01-15'
        depositAmount:
          type: number
          format: double
          description: Первоначальная сумма депозита (amount в Колвире)
          example: 1000000
        accountNum:
          type: string
          description: Номер депозитного счета (accountCode в Колвире)
          example: ********************
        balance:
          type: number
          format: double
          description: Актуальный баланс в валюте депозитного счета (balance в Колвире)
          example: 1500000.5
        currency:
          type: string
          description: Валюта депозитного счета (currency в Колвире)
          enum:
            - KZT
            - USD
            - EUR
            - RUB
            - CNY
          example: KZT
        agreementCode:
          type: string
          description: Номер депозитного договора (code в Колвире)
          example: DEP-2024-001
        isReplenishable:
          type: boolean
          description: Доступно ли пополнение
          example: true
        replenishableDays:
          type: integer
          format: int32
          description: Сколько дней еще доступно пополнение
          example: 45
        isWithdrawal:
          type: boolean
          description: Доступно ли снятие (всегда false)
          example: false
        payedAmount:
          type: number
          format: double
          description: Общая сумма выплаченной доходности (payedAmount в Колвире)
          example: 45000
        rate:
          type: number
          format: double
          description: Доходность (rate в Колвире, конвертировать xx.x в 0.xxx)
          example: 0.162
        effectiveRate:
          type: number
          format: double
          description: Эффективная ставка доходности (рассчитать от rate)
          example: 0.165
        payoutMethod:
          $ref: '#/components/schemas/DepositPayoutMethod'
        termMonth:
          type: integer
          format: int32
          description: Срок депозита в месяцах (term в Колвире)
          example: 12
        nearestPayDate:
          type: string
          nullable: true
          format: date
          description: Следующая дата выплаты процентов (пока что, если даты нет то пишем 1970-01-01)
          example: '2024-12-31'
        accDepRet:
          type: string
          nullable: true
          description: Счет для возврата вклада
          example: ''
        documents:
          type: array
          description: Список документов
          items:
            $ref: '#/components/schemas/DepositDocument'
  parameters:
    AcceptLanguage:
      name: Accept-Language
      in: header
      description: Язык запроса
      required: false
      schema:
        type: string
        enum:
          - kk
          - en
          - ru
        example: ru
        default: kk
    EntryPointHeader:
      name: entryPoint
      in: header
      description: Ресурс, откуда пользователь пришел в МП (например - с лендинга)
      schema:
        type: string
        example: from_credit_landing
    UserAgent:
      name: User-Agent
      in: header
      description: Юзер агент
      required: true
      schema:
        type: string
    DocumentIDPathParam:
      name: docID
      in: path
      description: Идентификатор запрашиваемого документа
      required: true
      schema:
        type: string
        format: uuid
        pattern: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
        maxLength: 36
    AccountIDPathParam:
      name: accountID
      in: path
      description: Идентификатор запрашиваемого счета
      required: true
      schema:
        type: string
        format: uuid
        pattern: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
        maxLength: 36
    CardIDPathParam:
      name: cardId
      in: path
      required: true
      description: UUID виртуальной карты
      schema:
        type: string
        format: uuid
    DictionaryIDPathParam:
      name: dict_id
      in: path
      description: ID справочника
      required: true
      schema:
        type: string
        format: uuid
        pattern: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
        maxLength: 36
    PaginationPageQueryParam:
      name: page
      in: query
      description: Страница
      required: false
      schema:
        type: integer
        format: int32
        default: 0
    PaginationCountQueryParam:
      name: count
      in: query
      description: Количество строк на странице
      required: false
      schema:
        type: integer
        format: int32
        default: 10000
    DictDocumentIDPathParam:
      name: doc_id
      in: path
      description: ID документа
      required: true
      schema:
        type: string
        format: uuid
        pattern: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
        maxLength: 36
    DictionaryNamePathParam:
      name: dict_name
      in: path
      description: Имя справочника
      required: true
      schema:
        type: string
        maxLength: 64
    DictDocumentNamePathParam:
      name: doc_name
      in: path
      description: Имя документа
      required: true
      schema:
        type: string
        maxLength: 64
    OnboardingSourceQueryParam:
      name: source
      in: query
      description: Источник запуска онбординга
      required: false
      schema:
        type: string
        enum:
          - common
          - landing
    ApplicationIDPathParam:
      name: applicationID
      in: path
      description: Идентификатор заявки на кредит
      required: true
      schema:
        type: string
        format: uuid
        pattern: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
        maxLength: 36
    RefinancingInfoReqStep:
      name: step
      in: query
      description: Вид шага
      required: true
      schema:
        type: string
        enum:
          - REF_CONDITIONS
          - INPUT_IBAN
    PaymentsGetTransactionsStartDate:
      name: startDate
      in: query
      description: Начальная дата для фильтрации транзакций
      schema:
        type: string
      required: false
    PaymentsGetTransactionsEndDate:
      name: endDate
      in: query
      description: Конечная дата для фильтрации транзакций
      schema:
        type: string
      required: false
    PaymentsGetTransactionsAccounts:
      name: accounts
      in: query
      description: Список номеров счетов для фильтрации транзакций
      schema:
        type: array
        items:
          type: string
      required: true
    PaymentsGetTransactionsCards:
      name: cards
      in: query
      description: Список карт для фильтрации транзакций
      schema:
        type: array
        items:
          type: string
    PaymentsGetTransactionsOperationType:
      name: direction
      in: query
      description: Тип операции (например, CREDIT или DEBIT)
      schema:
        type: string
        enum:
          - CREDIT
          - DEBIT
      required: false
    PaymentsGetTransactionsCounterparty:
      name: counterparty
      in: query
      description: Контрагент для фильтрации (получатель или отправитель средств)
      schema:
        type: string
      required: false
    PaymentsGetTransactionsMinAmount:
      name: minAmount
      in: query
      description: Минимальная сумма транзакции
      schema:
        type: string
      required: false
    PaymentsGetTransactionsMaxAmount:
      name: maxAmount
      in: query
      description: Максимальная сумма транзакции
      schema:
        type: string
      required: false
    PaymentsGetTransactionsLimit:
      name: limit
      in: query
      description: Лимит количества возвращаемых транзакций
      schema:
        type: integer
        format: int64
      required: false
    PaymentsGetTransactionsOffset:
      name: offset
      in: query
      description: Смещение для пагинации
      schema:
        type: integer
        format: int64
      required: false
    PaymentsGetTransactionByIDTransactionID:
      name: transactionID
      in: path
      description: Индектификатор транзакции
      schema:
        type: string
        format: uuid
      required: true
    PaymentsGetHistoryClientIinBin:
      name: clientIinBin
      in: query
      description: ИИН/БИН клиента
      schema:
        type: string
      required: true
    TaskStatusQueryParam:
      name: status
      in: query
      description: Статус задачи
      required: false
      schema:
        type: string
        enum:
          - in-progress
          - completed
          - pending
    TaskTypeQueryParam:
      name: type
      in: query
      description: Тип задачи
      required: false
      schema:
        type: string
    TaskCreatedAfterQueryParam:
      name: created_after
      in: query
      description: Дата создания после (timestamp)
      required: false
      schema:
        type: integer
        format: int64
    TaskCreatedBeforeQueryParam:
      name: created_before
      in: query
      description: Дата создания до (timestamp)
      required: false
      schema:
        type: integer
        format: int64
    TaskPageQueryParam:
      name: page
      in: query
      description: Номер страницы
      required: false
      schema:
        type: integer
        format: int64
        default: 1
    TaskPageSizeQueryParam:
      name: page_size
      in: query
      description: Размер страницы
      required: false
      schema:
        type: integer
        format: int64
        default: 20
    TaskIDPathParam:
      name: taskID
      in: path
      description: Идентификатор запрашиваемого счета
      required: true
      schema:
        type: string
        format: uuid
        pattern: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
        maxLength: 36
    parameters-CardIDPathParam:
      name: cardId
      in: path
      required: true
      description: UUID карты эмитента (issuer_card_ref_id)
      schema:
        type: string
        format: uuid
    GetProductInfo:
      name: productCode
      in: path
      description: Код депозитного продукта
      required: true
      schema:
        type: string
    CalculateProfitAmount:
      name: amount
      in: query
      required: true
      description: Сумма депозита (> 0)
      schema:
        type: number
        format: double
        example: 100000
    CalculateProfitTermMonths:
      name: termMonths
      in: query
      required: true
      description: Срок депозита в месяцах (> 0)
      schema:
        type: integer
        format: uint32
        example: 12
    CalculateProfitProfitRate:
      name: profitRate
      in: query
      required: true
      description: 'Номинальная годовая ставка (например: 0.12 = 12%)'
      schema:
        type: number
        format: double
        example: 0.12
    CalculateProfitPayoutMethod:
      name: payoutMethod
      in: query
      required: true
      description: Метод выплаты доходности
      schema:
        $ref: '#/components/schemas/DepositPayoutMethod'
    DepositDetailsParams:
      name: depositID
      in: path
      description: ID депозита
      required: true
      schema:
        type: string
  requestBodies:
    LogInBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              phone:
                $ref: '#/components/schemas/PhoneNumber'
              iin:
                type: string
                description: ИИН
                pattern: ^[0-9]+$
                example: '123456789012'
              deviceInfo:
                $ref: '#/components/schemas/DeviceInfo'
            required:
              - phone
              - iin
              - deviceInfo
    LogoutBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              installationID:
                type: string
                format: uuid
                description: Идентификатор установки приложения
                example: 4d41e509-1d1e-4530-a1d6-97d2d599d3f8
            required:
              - installationID
    ConfirmRequestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: string
                description: Код подтверждения
                minLength: 1
                example: '3223'
              attemptID:
                type: string
                description: Идентификатор попытки для ввода кода
                format: uuid
                example: bae9d327-d578-4a92-9cbc-664bb7e007f9
            required:
              - attemptID
              - code
    AuthRefreshBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              refreshToken:
                type: string
                description: Токен обновления авторизации
              phone:
                $ref: '#/components/schemas/PhoneNumber'
              deviceInfo:
                $ref: '#/components/schemas/DeviceInfo'
            required:
              - refreshToken
              - phone
              - deviceInfo
    IdentifyBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: string
                description: Код BTS
                example: '123456789012'
            required:
              - code
    SignDocumentsByIDReqBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - docIDs
            properties:
              docIDs:
                type: array
                items:
                  type: string
                  format: uuid
                  pattern: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
                  maxLength: 36
    OtpBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: string
                description: Код подтверждения
                minLength: 1
                example: '3223'
              attemptID:
                type: string
                description: Идентификатор попытки для ввода кода
                format: uuid
            required:
              - attemptID
    ConfirmSignDocumentsReqBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: string
                description: Код подтверждения
                minLength: 1
                example: '3223'
              attemptID:
                type: string
                description: Идентификатор попытки для ввода кода
                format: uuid
            required:
              - attemptID
              - code
    BatchDocsOtpBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - docIDs
              - attemptID
              - code
            properties:
              code:
                type: string
                description: Код подтверждения
                minLength: 1
                example: '3223'
              attemptID:
                type: string
                description: Идентификатор попытки для ввода кода
                format: uuid
              docIDs:
                description: Идентификаторы подписываемых документов
                type: array
                items:
                  type: string
                  format: uuid
    AccountDocumentRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              accountID:
                type: string
                description: Идентификатор счета
                format: uuid
                pattern: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
                maxLength: 36
              language:
                type: string
                description: Язык документа
                example: ru
              periodFrom:
                type: string
                format: date
                nullable: true
                description: Начало периода (только для выписки по счету)
                example: '2025-01-01'
              periodTo:
                type: string
                format: date
                nullable: true
                description: Конец периода (только для выписки по счету)
                example: '2025-01-30'
    OtpRetryBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              attemptID:
                type: string
                description: Идентификатор попытки для ввода кода
                format: uuid
            required:
              - attemptID
    ProfileDeleteBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              installationID:
                type: string
                format: uuid
                description: Идентификатор установки приложения
                example: 4d41e509-1d1e-4530-a1d6-97d2d599d3f8
            required:
              - installationID
    ApplicationForSignRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              docType:
                type: string
                description: Тип документа, который будет сгенерирован
              currencies:
                type: array
                description: Список валют, для которых будет сгенерирован документ
                items:
                  type: string
            required:
              - docType
    ReferralAttributionRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - timestamp
              - deepLinkSub1
            properties:
              timestamp:
                type: string
                format: date-time
                description: Дата и время перехода Реферала по ссылке
              deepLinkSub1:
                type: string
                description: Уникальный код Клиента, который пригласил Реферала
              deepLinkValue:
                type: string
                description: Параметр в ссылках AppsFlyer, который указывает, на какой конкретный экран, продукт или контент нужно направить пользователя внутри мобильного приложения после клика по ссылке
    DictCreateReq:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Dictionary'
    DictUpdateReq:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Dictionary'
    DictDeleteReq:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/DictionaryID'
    DictDocCreateReq:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/DictDocument'
    DictDocUpdateReq:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/DictDocument'
    DictDocDeleteReq:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/DictDocumentID'
    DictDocGetListByFiltersReq:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/DictDocumentFilters'
    DictDocGetTreeLineReq:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/DictGetTreeLine'
    DictDocOrderUpdateReq:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/DictDocumentOrderUpdate'
    JobRunReq:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/JobRun'
    JobStopReq:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/JobStop'
    JobGetStatusReq:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/JobGetStatus'
    DictKATOMapFromTSOIDReq:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/DictKATOMapFromTSOIDArgs'
    CalculationBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              purposeID:
                type: string
                description: ID выбранной клиентом цели
                example: '1'
              amount:
                type: integer
                example: 10000
                description: Значение, внесенное клиентом по сумме кредита
            required:
              - purposeID
              - amount
    SaveSurveyBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - applicationID
              - email
              - educationID
              - address
              - empID
              - contactPersons
              - children
            properties:
              applicationID:
                type: string
                description: Идентификатор заявки.
                minLength: 1
              email:
                $ref: '#/components/schemas/Email'
              address:
                $ref: '#/components/schemas/SaveSurveyAddress'
              educationID:
                type: string
                description: Идентификатор уровня образования.
                minLength: 1
              empID:
                type: string
                description: Идентификатор типа занятости.
                minLength: 1
              contactPersons:
                type: array
                items:
                  $ref: '#/components/schemas/SaveSurveyContactPerson'
                description: Список контактных лиц.
                minItems: 1
              children:
                type: integer
                format: int32
                description: Количество детей.
                minimum: 0
    CreateLoanApplicationBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - amount
            description: Объект запроса для создания новой заявки на кредит.
            properties:
              amount:
                type: integer
                minimum: 0
                description: Запрашиваемая сумма кредита.
              termInterestID:
                type: string
                description: Уникальный идентификатор, представляющий условия кредита (из справочника).
                minLength: 1
              purposeID:
                type: string
                description: Уникальный идентификатор, представляющий цель кредита (из справочника).
                minLength: 1
              juicySessionID:
                type: string
                description: ID сессии в системе JUICYSCORE
                example: 4d41e509-1d1e-4530-a1d6
                minLength: 1
    UpdateLoanApplicationBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - amount
              - termInterestID
              - purposeID
              - juicySessionID
            description: Объект запроса для создания новой заявки на кредит.
            properties:
              amount:
                type: integer
                minimum: 0
                description: Запрашиваемая сумма кредита.
              termInterestID:
                type: string
                description: Уникальный идентификатор, представляющий условия кредита (из справочника).
                minLength: 1
              purposeID:
                type: string
                description: Уникальный идентификатор, представляющий цель кредита (из справочника).
                minLength: 1
              juicySessionID:
                type: string
                description: ID сессии в системе JUICYSCORE
                example: 4d41e509-1d1e-4530-a1d6
                minLength: 1
    LoanApplicationDocumentsBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - applicationID
              - docTypes
            properties:
              applicationID:
                type: string
                description: Идентификатор заявки.
                format: uuid
              docTypes:
                type: array
                description: Тип (шаблон) документа
                uniqueItems: true
                items:
                  $ref: '#/components/schemas/LoanAppDocumentType'
    PublishLoanAppDataRequestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              bankStatements:
                type: array
                description: Данные прикрепленных документов
                items:
                  $ref: '#/components/schemas/AttachedDocData'
    PostIdentifyBtsDataRequestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - code
            properties:
              code:
                type: string
                description: Код
    PostEdsBtsDataRequestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - code
            properties:
              code:
                type: string
                description: Код
    PostEarlyRepayRequestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - repayType
              - repayAmount
            properties:
              repayType:
                type: string
                description: |
                  ODONLY - ЧДП
                  WSHD_FULL - ПДП
              repayAmount:
                $ref: '#/components/schemas/EarlyRepayAmount'
    DocsOtpBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - attemptID
              - code
            properties:
              code:
                type: string
                description: Код подтверждения
                minLength: 1
                example: '3223'
              attemptID:
                type: string
                description: Идентификатор попытки для ввода кода
                format: uuid
    SaveUserExternalBankLoansBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - externalLoans
            properties:
              externalLoans:
                type: array
                items:
                  $ref: '#/components/schemas/UserExternalBankLoan'
    PaymentsCheckAccountIinRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - clientIinBin
              - account
            properties:
              clientIinBin:
                type: string
                description: ИИН/БИН клиента
              account:
                type: string
                description: Номер счёта
    CreatePaymentByAccount:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              idempotencyID:
                type: string
                description: Ключ идемпотентности
                example: 59acde04-ef16-4a3d-9a95-4728d42e20cd
              payerBinIIN:
                type: string
                description: ИИН/БИН отправителя
                maxLength: 12
                minLength: 12
              payerName:
                type: string
                description: ФИО/Наименование
                maxLength: 250
                minLength: 3
              payerAccount:
                type: string
                description: Счет клиента
                maxLength: 20
                minLength: 20
              amount:
                type: number
                format: double
                description: Сумма операции
                example: 99.95
              currency:
                type: string
                description: Валюта операции
                example: KZT
              beneficiaryBinIIN:
                type: string
                description: ИИН/БИН получателя
                pattern: ^[0-9]{12}$
              beneficiaryName:
                type: string
                description: ФИО/Наименование получателя
                maxLength: 250
                minLength: 3
              beneficiaryType:
                type: integer
                format: int32
                description: Тип получателя
                example: 32
              beneficiaryTaxPayerType:
                $ref: '#/components/schemas/TaxPayerType'
                description: Тип налогоплательщика
              beneficiaryBank:
                type: string
                description: БИК банка получателя
              beneficiaryAccount:
                type: string
                description: IBAN cчета  получателя
              beneficiaryBankName:
                type: string
                description: Наименование банка получателя
              beneficiaryCountry:
                type: string
                description: Страна резидентство
              date:
                type: string
                format: date-time
                description: Дата операции
              valueDate:
                type: string
                format: date-time
                description: Дата валютирования
              kod:
                type: integer
                format: int32
                description: Код отправителя
              kbe:
                type: string
                description: Код бенефициара
              knp:
                type: string
                description: КНП, purposeCode
              paymentDetails:
                type: string
                description: Назначение платежа
                maxLength: 250
              actualSender:
                type: string
                description: Фактический отправитель (ФИО/Наименование)
              actualSenderBinIIN:
                type: string
                description: ИИН/БИН фактического отправителя
                pattern: ^[0-9]{12}$
              actualSenderIsLegal:
                type: boolean
                description: Флаг юр лица фактического отправителя
              actualSenderCountry:
                type: string
                description: Страна резидентство фактического отправителя
              actualBeneficiary:
                type: string
                description: Фактический получатель (ФИО/Наименование)
              actualBeneficiaryBinIIN:
                type: string
                description: ИИН/БИН фактического получателя
                pattern: ^[0-9]{12}$
              actualBeneficiaryIsLegal:
                type: boolean
                description: Флаг юр лица фактического получателя
              actualBeneficiaryCountry:
                type: string
                description: Страна резидентство фактического получателя
            required:
              - payerBinIIN
              - payerName
              - payerAccount
              - amount
              - currency
              - beneficiaryBinIIN
              - beneficiaryName
              - beneficiaryType
              - beneficiaryBank
              - beneficiaryAccount
              - beneficiaryBankName
              - kbe
              - knp
              - paymentDetails
              - idempotencyID
    ConfirmPaymentByAccount:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: string
                description: Код валидации отп
              attemptID:
                type: string
                format: uuid
                description: Идентификатор для валидации отп кода
                example: 2100e82d-e288-4882-8fac-3f1403449051
            required:
              - attemptID
              - code
    CheckPhoneNumberRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              phoneNumber:
                type: string
                description: Номер телефона
                example: '+***********'
            required:
              - phoneNumber
    CreatePaymentForMobileRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              idempotencyID:
                type: string
                description: Ключ идемпотентности
                example: 59acde04-ef16-4a3d-9a95-4728d42e20cd
              accountNumber:
                type: string
                description: Номер счёта с которого списываются деньги
              phoneNumber:
                type: string
                description: Номер телефона на который поступят деньги
                example: '+***********'
              amount:
                type: string
                pattern: ^\d+\.\d{2}$
                example: '1234.56'
            required:
              - idempotencyID
              - accountNumber
              - phoneNumber
              - amount
    ConfirmPaymentForMobileRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: string
                description: Код валидации отп
              attemptID:
                type: string
                format: uuid
                description: Идентификатор для валидации отп кода
                example: 2100e82d-e288-4882-8fac-3f1403449051
            required:
              - attemptID
              - code
    CheckClientByPhoneNumberRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              phoneNumber:
                type: string
                description: Номер телефона
                example: '+***********'
            required:
              - phoneNumber
    CreateInternalPaymentByPhoneNumberRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              idempotencyID:
                type: string
                description: Ключ идемпотентности
                example: 59acde04-ef16-4a3d-9a95-4728d42e20cd
              payerAccountNumber:
                type: string
                description: Номер счёта с которого списываются деньги
              beneficiaryPhoneNumber:
                type: string
                description: Номер телефона клиента банка на счёт которого происходит перевод
                example: '+***********'
              amount:
                type: string
                pattern: ^\d+\.\d{2}$
                example: '1234.56'
              currency:
                type: string
                description: Валюта перевода
                example: KZT
            required:
              - idempotencyID
              - payerAccountNumber
              - beneficiaryPhoneNumber
              - amount
              - currency
    ConfirmInternalPaymentByPhoneNumberRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: string
                description: Код валидации отп
              attemptID:
                type: string
                format: uuid
                description: Идентификатор для валидации отп кода
                example: 2100e82d-e288-4882-8fac-3f1403449051
            required:
              - attemptID
              - code
    CreateSelfTransferRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              idempotencyID:
                type: string
                description: Ключ идемпотентности
                example: 59acde04-ef16-4a3d-9a95-4728d42e20cd
              fromAccount:
                type: string
                description: Номер счёта с которого списываются деньги
              fromAccountСurrency:
                type: string
                description: Валюта перевода
                example: KZT
              toAccount:
                type: string
                description: Номер счёта на который поступят деньги
              toAccountСurrency:
                type: string
                description: Валюта перевода
                example: KZT
              amount:
                type: string
                pattern: ^\d+\.\d{2}$
                example: '1234.56'
    CreatePaymentKaspiQRRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - idempotencyID
              - paymentID
              - payerAccount
              - paymentAmount
              - customerID
            properties:
              idempotencyID:
                type: string
                description: Ключ идемпотентности
                example: 59acde04-ef16-4a3d-9a95-4728d42e20cd
              paymentID:
                type: string
                description: Идентификатор платежа от ПС
                maxLength: 64
              payerAccount:
                type: string
                description: IBAN счета отправителя (маскированный)
                example: KZ123456**5678
              paymentAmount:
                type: string
                description: Идентификатор возврата в ПС
                maxLength: 7
              customerID:
                type: string
                description: ИИН пользователя
                maxLength: 12
    QRTokenRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - qrToken
              - customerID
            properties:
              qrToken:
                type: string
                description: Содержимое отсканированного QR-кода
              customerID:
                type: string
                description: ИИН пользователя
                maxLength: 12
    QRSessionTerminationRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - paymentID
              - customerID
            properties:
              paymentID:
                type: string
                description: Идентификатор платежа платежной системы Kaspi Pay
                example: 59acde04-ef16-4a3d-9a95-4728d42e20cd
              customerID:
                type: string
                description: ИИН пользователя
                maxLength: 12
    DepositConditionProfitReqBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - productCode
              - amount
              - currencyCode
            properties:
              productCode:
                type: string
                description: 'Код продукта (например: WAKALA)'
                example: WAKALA
              amount:
                type: number
                format: double
                description: Сумма для расчета доходности депозита
                example: 1000000
              currencyCode:
                type: string
                description: 'Код валюты (например: KZT, USD, EUR)'
                example: KZT
    CreateDepositOfferReqBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - rateId
              - accountId
              - payoutMethod
              - amount
            properties:
              rateId:
                type: string
                description: ID условия/рейта
                example: 123e4567-e89b-12d3-a456-************
              accountId:
                type: string
                description: ID счет списания в пользу депозита
                example: account123
              amount:
                type: number
                format: double
                description: Сумма депозита
                example: 90001.95
              payoutMethod:
                $ref: '#/components/schemas/DepositPayoutMethod'
