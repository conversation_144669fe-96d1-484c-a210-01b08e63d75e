openapi: 3.0.1

servers:
  - description: Dev server
    url: https://mobile-dev.zaman.redmadrobot.com/api/v1
  - description: Stage server
    url: https://mobile.zaman.redmadrobot.com/api/v1

info:
  title: Zaman Mobile API
  version: dev-0916-08335e49
  description: |
    API для бэкэнда мобильного приложения Zaman

    Спецификация в формате [swagger](../swagger) или [redoc](../redoc).

    Любой ответ сервера может содержать коды 1.X (где Х - цифровой код ошибки) и X.0 (где X - цифровой ID сервиса).
    [Справочник ошибок](../errors)

    Хэдер accept-language используется для переключения языка ответа сервера.
    Подробнее см. [Accept-Language](#component-parameters-AcceptLanguage).

    [Запрос выборки данных из словаря](../dictionary)

    [Changelog](../history/changelog_main.html)

    [Release Changelog](../history/changelog_feature.html)
  termsOfService: https://www.zamanbank.kz/useful-info/
  x-logo:
    url: ../static/logo.png

tags:
  - name: "Системные"
  - name: "Авторизация"
  - name: "Профиль"
  - name: "Документы"
  - name: "Карты и счета"
  - name: "Общее"
  - name: "Кредиты"
  - name: "Справочники"
  - name: "Платежи"
  - name: "Таск менеджер"
  - name: "Токенизация карт"
  - name: "Депозиты"
  - name: "File Guard"
  - name: "Реферальная программа"

paths:
  /health:
    $ref: "health.gen.yaml#/health"

  /auth/login:
    $ref: "auth.yaml#/login"
  /auth/logout:
    $ref: "auth.yaml#/logout"
  /auth/confirm:
    $ref: "auth.yaml#/confirm"
  /auth/refresh:
    $ref: "auth.yaml#/refresh"
  /auth/bts-data:
    $ref: "auth.yaml#/get-bts-data"
  /auth/identify:
    $ref: "auth.yaml#/identify"
  /auth/document-for-sign:
    $ref: "docs.yaml#/documents-for-sign"

  /documents/public:
    $ref: "docs.yaml#/docs-request-public"
  /documents/{docID}:
    $ref: "docs.yaml#/docs-by-docID"
  /documents/{docID}/sign:
    $ref: "docs.yaml#/docs-by-docID-sign"
  /documents/sign:
    $ref: "docs.yaml#/docs-sign"
  /documents/{docID}/sign-confirm:
    $ref: "docs.yaml#/docs-by-docID-sign-confirm"
  /documents/sign-confirm:
    $ref: "docs.yaml#/docs-sign-confirm"
  /documents/batch-sign-confirm:
    $ref: "docs.yaml#/docs-batch-sign-confirm"

  /accounts/documents:
    $ref: "docs.yaml#/accounts-documents"

  /otp/retry:
    $ref: "otp.yaml#/retry"

  /user/cards:
    $ref: "cards.yaml#/user-cards"
  /user/accounts/{accountID}:
    $ref: "cards.yaml#/user-account"
  /user/loans:
    $ref: "loans.yaml#/loans"
  /user/locale:
    $ref: "users.yaml#/user-locale"
  /user/profile:
    $ref: "users.yaml#/profile-delete"
  /accounts/application-for-sign:
    $ref: "cards.yaml#/application-for-sign"
  /cards/{cardId}/requisites:
    $ref: "cards.yaml#/requisites"


  /referral-program/status:
    $ref: "referral.yaml#/referral-program-status"
  /referral-program/profile:
    $ref: "referral.yaml#/referral-program-profile"
  /referral-program/onboarding:
    $ref: "referral.yaml#/referral-program-onboarding"
  /referral-program/attribution:
    $ref: "referral.yaml#/referral-program-attribution"

  /dict/create: # TODO: только для тестирования
    $ref: "dictionary.yaml#/dict-create"
  /dict/update: # TODO: только для тестирования
    $ref: "dictionary.yaml#/dict-update"
  /dict/delete: # TODO: только для тестирования
    $ref: "dictionary.yaml#/dict-delete"
  /dict/get/{dict_id}: # TODO: только для тестирования
    $ref: "dictionary.yaml#/dict-get-public"
  /dict/list: # TODO: только для тестирования
    $ref: "dictionary.yaml#/dict-get-list-public"
  /dict/doc/create: # TODO: только для тестирования
    $ref: "dictionary.yaml#/dict-doc-create"
  /dict/doc/update: # TODO: только для тестирования
    $ref: "dictionary.yaml#/dict-doc-update"
  /dict/doc/delete: # TODO: только для тестирования
    $ref: "dictionary.yaml#/dict-doc-delete"
  /dict/doc/get/{doc_id}:
    $ref: "dictionary.yaml#/dict-doc-get-public"
  /dict/doc/by-name/{dict_name}/{doc_name}:
    $ref: "dictionary.yaml#/dict-doc-get-by-name-public"
  /dict/doc/by-filters:
    $ref: "dictionary.yaml#/dict-doc-get-list-by-filters-public"
  /dict/doc/tree/path:
    $ref: "dictionary.yaml#/dict-doc-get-tree-line-public"
  /dict/doc/order-update:
    $ref: "dictionary.yaml#/dict-doc-order-update"
  /dict/job/run:
    $ref: "dictionary_jobs.yaml#/dict-job-run"
  /dict/job/stop:
    $ref: "dictionary_jobs.yaml#/dict-job-stop"
  /dict/job/status:
    $ref: "dictionary_jobs.yaml#/dict-job-status"
  /dict/job/statusall:
    $ref: "dictionary_jobs.yaml#/dict-job-status-all"
  /dict/kato/map/tsoid:
    $ref: "dictionary.yaml#/dict-kato-map-from-tsoid"

  /dictionaries:
    $ref: "dictionary.yaml#/dictionaries"
  /dictionaries/locations:
    $ref: "dictionary.yaml#/dictionaries-locations"
  /dictionaries/by-filter:
    $ref: "dictionary.yaml#/dictionaries-by-filter"

  /loans/onboarding-texts:
    $ref: "loans.yaml#/loans-onboarding-texts"
  /loans/calc-data:
    $ref: "loans.yaml#/loans-calc-data"
  /loans/calculation:
    $ref: "loans.yaml#/loans-calculate"
  /loans/survey:
    $ref: "loans.yaml#/survey"
  /loans/application:
    $ref: "loans.yaml#/loan-application"
  /loans/{applicationID}/application:
    $ref: "loans.yaml#/update-loan-application"
  /loans/{applicationID}/internal-checks-result:
    $ref: "loans.yaml#/internal-checks-result"
  /loans/education-types:
    $ref: "loans.yaml#/education-types"
  /loans/employment-types:
    $ref: "loans.yaml#/employment-types"
  /loans/relation-types:
    $ref: "loans.yaml#/relation-types"
  /loans/{applicationID}/cancel:
    $ref: "loans.yaml#/cancel-application"
  /loans/active-application-check:
    $ref: "loans.yaml#/check-active-loan-app-exists"
  /loans/documents:
    $ref: "loans.yaml#/documents-for-application"
  /loans/{applicationID}/documents-for-sign:
    $ref: "loans.yaml#/loan-document-for-sign"
  /loans/{applicationID}/status:
    $ref: "loans.yaml#/approved-loan-app-status"
  /loans/{applicationID}/publish:
    $ref: "loans.yaml#/publish-loan-app-data"
  /loans/{applicationID}/scoring-result:
    $ref: "loans.yaml#/get-scoring-result"
  /loans/{applicationID}/bts-data:
    $ref: "loans.yaml#/get-bts-data"
  /loans/{applicationID}/identify:
    $ref: "loans.yaml#/post-identify-bts-data"
  /loans/{applicationID}/eds:
    $ref: "loans.yaml#/post-eds-bts-data"
  /loans/{applicationID}/details:
    $ref: "loans.yaml#/loans-details"
  /loans/{applicationID}/early-repay:
    $ref: "loans.yaml#/post-early-repay"
  /loans/{applicationID}/sign-confirm:
    $ref: "loans.yaml#/docs-sign-confirm"
  /loans/{applicationID}/{docID}/sign-confirm:
    $ref: "loans.yaml#/doc-sign-confirm"
  /loans/bank-statement:
    $ref: "loans.yaml#/bank-statement"
  /loans/{applicationID}/bank-statement:
    $ref: "loans.yaml#/bank-statement-v2"
  /loans/{applicationID}/refinancing-info:
    $ref: "loans.yaml#/refinancing-info"
  /loans/{applicationID}/external-bank-loans:
    $ref: "loans.yaml#/save-external-bank-loans"

  /payments/transactions:
    $ref: "payments.yaml#/transactions"
  /payments/transactions/{transactionID}:
    $ref: "payments.yaml#/get-transaction-by-id"
  /payments/transactions/{transactionID}/receipt:
    $ref: "payments.yaml#/get-transaction-receipt"
  /payments/history:
    $ref: "payments.yaml#/history"
  /payments/check-account-iin:
    $ref: "payments.yaml#/check-account-iin"
  /payments/create-payment-by-account:
    $ref: "payments.yaml#/create-payment-by-account"
  /payments/confirm-payment-by-account:
    $ref: "payments.yaml#/confirm-payment-by-account"
  /payments/check-phone-number:
    $ref: "payments.yaml#/check-phone-number"
  /payments/create-payment-for-mobile:
    $ref: "payments.yaml#/create-payment-for-mobile"
  /payments/confirm-payment-for-mobile:
    $ref: "payments.yaml#/confirm-payment-for-mobile"
  /payments/check-client-by-phone-number:
    $ref: "payments.yaml#/check-client-by-phone-number"
  /payments/create-internal-payment-by-phone-number:
    $ref: "payments.yaml#/create-internal-payment-by-phone-number"
  /payments/confirm-internal-payment-by-phone-number:
    $ref: "payments.yaml#/confirm-internal-payment-by-phone-number"
  /payments/create-self-transfer:
    $ref: "payments.yaml#/create-self-transfer"
  /payments/create-payment-kaspiqr:
    $ref: "payments.yaml#/create-payment-kaspiqr"
  /payments/qr-token:
    $ref: "payments.yaml#/qr-token"
  /payments/qr-session-termination:
    $ref: "payments.yaml#/qr-session-termination"

  /tasks:
    $ref: "taskmanager.yaml#/tasks"
  /tasks/{taskID}:
    $ref: "taskmanager.yaml#/task-details"

  /tokenize:
    $ref: "tokenization.yaml#/tokenization-card"
  /tokenize/{cardId}/state/update:
    $ref: "tokenization.yaml#/tokenization-state"
  /tokenize/state:
    $ref: "tokenization.yaml#/tokenization-state-notify"
  /tokenize/{cardId}/info:
    $ref: "tokenization.yaml#/tokenization-info"

  /fileguard:
    $ref: "file_guard.yaml#/unsecure-file-upload"

  /deposits/product-info/{productCode}:
    $ref: "deposits.yaml#/get-product-info"
  /deposits/utils/calculate-profit:
    $ref: "deposits.yaml#/calculate-profit"
  /deposits/condition-profit:
    $ref: "deposits.yaml#/deposit-condition-profit"
  /deposits/get-available-accounts:
    $ref: "deposits.yaml#/get-available-accounts"
  /deposits/create-offer:
    $ref: "deposits.yaml#/create-deposit-offer"
  /user/deposits:
    $ref: "deposits.yaml#/get-user-deposits"
  /user/deposits/{depositID}:
    $ref: "deposits.yaml#/get-deposit-detail"

components:
  securitySchemes:
    BearerTokenAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  # Reusable responses, such as 401 Unauthorized or 400 Bad Request
  responses:
    RespEmpty:
      description: Операция выполнена успешно
      content:
        application/json:
          schema:
            type: object
            properties: {}

  schemas:
    PhoneNumber:
      type: string
      description: Номер телефона
      pattern: '^\+?[1-9]\d{10,10}$'
      example: "+***********"
    Email:
      type: string
      #      TODO: разобраться почему с этим форматом возникает ошибка. can't serialize to JSON: json: error calling MarshalJSON for type *types.Email: email: failed to pass regex validation
      #      format: email
      pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
      description: Электронная почта
    APIError:
      type: object
      description: Общий формат ошибки API. Возвращает цифровой код
      properties:
        error:
          type: string
        fields:
          type: object
          description: Объект с описанием деталей ошибок
          additionalProperties:
            type: string
      required:
        - error
      example:
        error: "X.0"
    ValidationError:
      type: object
      description: Ошибка валидации данных в теле запроса
      properties:
        error:
          type: string
          description: Код ошибки
          example: "1.0"
        fields:
          type: object
          description: Объект с описанием ошибок валидации полей
          additionalProperties:
            type: string
          example:
            iin: "value must be a string"
      required:
        - error
        - fields

    Money:
      type: object
      description: Сумма
      required:
        - value
        - currencyCode
      properties:
        value:
          type: string
          description: Сумма
        currencyCode:
          $ref: "#/components/schemas/CurrencyCode"

    CurrencyCode:
      type: string
      description: Код валюты
      enum:
        - KZT
        - RUB
        - EUR
        - USD
        - CNY

  parameters:
    AcceptLanguage:
      name: Accept-Language
      in: header
      description: Язык запроса
      required: false
      schema:
        type: string
        enum:
          - kk
          - en
          - ru
        example: ru
        default: kk
    EntryPointHeader:
      name: entryPoint
      in: header
      description: Ресурс, откуда пользователь пришел в МП (например - с лендинга)
      schema:
        type: string
        example: from_credit_landing
    UserAgent:
      name: User-Agent
      in: header
      description: Юзер агент
      required: true
      schema:
        type: string
