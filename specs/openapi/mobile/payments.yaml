transactions:
  get:
    tags:
      - Платежи
    summary: История операций
    description: История операций
    operationId: getTransactions
    security:
      - BearerTokenAuth: [ "active" ]
    parameters:
      - $ref: "#/components/parameters/PaymentsGetTransactionsStartDate"
      - $ref: "#/components/parameters/PaymentsGetTransactionsEndDate"
      - $ref: "#/components/parameters/PaymentsGetTransactionsAccounts"
      - $ref: "#/components/parameters/PaymentsGetTransactionsCards"
      - $ref: "#/components/parameters/PaymentsGetTransactionsOperationType"
      - $ref: "#/components/parameters/PaymentsGetTransactionsCounterparty"
      - $ref: "#/components/parameters/PaymentsGetTransactionsMinAmount"
      - $ref: "#/components/parameters/PaymentsGetTransactionsMaxAmount"
      - $ref: "#/components/parameters/PaymentsGetTransactionsLimit"
      - $ref: "#/components/parameters/PaymentsGetTransactionsOffset"
    responses:
      200:
        description: "История операций"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PaymentsGetTransactionsResponse"

get-transaction-by-id:
  get:
    tags:
      - Платежи
    summary: Получение транзакции по индентификатору
    description: Получение транзакции по индентификатору
    operationId: getTransactionByID
    security:
      - BearerTokenAuth: [ "active" ]
    parameters:
      - $ref: "#/components/parameters/PaymentsGetTransactionByIDTransactionID"
    responses:
      200:
        description: "Транзакция"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PaymentsGetTransactionByIDResponse"

get-transaction-receipt:
  get:
    tags:
      - Платежи
    summary: Получение квитанции по платежу
    description: Получение квитанции по платежу
    operationId: getTransactionReceipt
    security:
      - BearerTokenAuth: [ "active" ]
    parameters:
      - $ref: "#/components/parameters/PaymentsGetTransactionByIDTransactionID"
    responses:
      200:
        description: "Квитанция транзакции"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PaymentsGetTransactionReceiptResponse"

history:
  get:
    tags:
      - Платежи
    summary: История платежей
    description: История платежей
    operationId: getPaymentHistory
    security:
      - BearerTokenAuth: [ "active" ]
    parameters:
      - $ref: "#/components/parameters/PaymentsGetHistoryClientIinBin"
      - $ref: "#/components/parameters/PaymentsGetTransactionsLimit"
      - $ref: "#/components/parameters/PaymentsGetTransactionsOffset"
    responses:
      200:
        description: "История платежей"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PaymentsGetHistoryResponse"

check-account-iin:
  post:
    tags:
      - Платежи
    summary: Проверка ИИН/БИН и номера счёта получателя
    description: Проверка ИИН/БИН и номера счёта бенефициара
    operationId: paymentsCheckAccountIin
    security:
      - BearerTokenAuth: [ "active" ]
    requestBody:
      $ref: "#/components/schemas/PaymentsCheckAccountIinRequest"
    responses:
      200:
        description: |
          Результат проверки номера счёта и ИИН/БИН бенефициара.
          <br>Если получатель ЮЛ то приходят 4 параметра информация о банке бенефициара, тип что это ЮЛ, наименование ЮЛ
          <br>Если получатель ФЛ то приходит так же информация о банке бенефициара, наименование ФЛ
          <br>Если у ФЛ есть дополнительный тип деятельности то появляется объект additionalIndividualType в котором указана доп деятельность
          <br>В поле additionalIndividualType.type могут быть следующие значения: 32 - ИП, 64 — адвокат, 128 — частный нотариус, 256 — частный судебный исполнитель
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PaymentsCheckAccountIinResponse"
      400:
        description: |
          Ошибки проверки пользователя
          <br><b>12.19 InvalidAccountNumber</b> - Неверный номер счёта
          <br><b>12.26 BankByIbanCodeNotFound</b> - Банк по ibanCode не найден
          <br><b>12.27 CodeTaxPayerNotFound</b> - Не найден налогоплатильщик по ИИН/БИН

create-payment-by-account:
  post:
    tags:
      - Платежи
    summary: Создание платежа для внутренних переводов
    description: Создание платежа для внутренних переводов
    operationId: createPaymentByAccount
    security:
      - BearerTokenAuth: [ "active" ]
    requestBody:
      $ref: "#/components/requestBodies/CreatePaymentByAccount"
    responses:
      200:
        description: "Платеж создан успешно"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreatePaymentByAccountResponse"
      400:
        description: |
          Ошибки создания перевода
          <br><b>12.5 InvalidPayerBinIINError</b> - ИИН / БИН пользователя введен не верно
          <br><b>12.6 InvalidBeneficiaryBinIINError</b> - ИИН / БИН бенефициара введен не верно
          <br><b>12.7 InvalidPayerAccountError</b> - Счет пользователя введен не верно
          <br><b>12.8 InvalidBeneficiaryAccountError</b> - Счет бенефициара введен не верно
          <br><b>12.9 AmountIsNotDecimalError</b> - Сумма введена не верно
          <br><b>12.11 PaymentDetailsTooLongError</b> - Детали платежа больше 250 символов
          <br><b>12.21 DublicatePayment</b> - Дубликат платежа
          <br><b>12.28 InvalidPurposeCode</b> - Неверный код назначения платежа
          <br><b>12.29 InvalidBeneficiaryCode</b> - Неверный код бенефициара
          <br><b>12.30 InvalidBeneficiaryBankBic</b> - Неверный бик банка бенефициара
          <br><b>12.31 GrossErrorsWhenExecutePayment</b> - Обнаружены грубые ошибки при выполнении платежа

confirm-payment-by-account:
  post:
    tags:
      - Платежи
    summary: Проверка отп и выполнение платежа для внутренних переводов
    description: Проверка отп и выполнение платежа для внутренних переводов
    operationId: confirmPaymentByAccount
    security:
      - BearerTokenAuth: [ "active" ]
    requestBody:
      $ref: "#/components/requestBodies/ConfirmPaymentByAccount"
    responses:
      200:
        description: "Платеж выполнен успешно"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ConfirmPaymentByAccountResponse"
      400:
        description: |
          Ошибки выполнения платежа
          <br><b>12.3 CodePaymentsInvalidOTP</b> - Неверный одноразовый пароль
          <br><b>12.16 TransactionNotFoundError</b> - Транзакция платежа не найдена
          <br><b>12.23 OTPNumberAttemptsExceeded</b> - Превышено количество попыток ввода отп

check-phone-number:
  post:
    tags:
      - Платежи
    summary: Проверка номера телефона
    description: Создание оплаты сотовой связи
    operationId: checkPhoneNumber
    security:
      - BearerTokenAuth: [ "active" ]
    requestBody:
      $ref: "#/components/requestBodies/CheckPhoneNumberRequest"
    responses:
      200:
        description: Проверка номера телефона прошла успешно
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CheckPhoneNumberResponse"
      400:
        description: |
          Ошибки проверки номера телефона
          <br><b>12.33 InvalidPhoneNumber</b> - Неверный номер телефона
          <br><b>12.34 PhoneOperatorNotFound</b> - Оператор по номеру телефона не найден

create-payment-for-mobile:
  post:
    tags:
      - Платежи
    summary: Создание оплаты сотовой связи
    description: Создание оплаты сотовой связи
    operationId: createPaymentForMobile
    security:
      - BearerTokenAuth: [ "active" ]
    requestBody:
      $ref: "#/components/requestBodies/CreatePaymentForMobileRequest"
    responses:
      200:
        description: "Создание оплаты сотовой связи выполнено успешно"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreatePaymentForMobileResponse"
      400:
        description: |
          Ошибки создания оплаты мобильной связи
          <br><b>12.21 DublicatePayment</b> - Дубликат платежа
          <br><b>12.38 NotEnoughMoneyInAccounts</b> - Недостаточно средств на счёте для выполнения операции
          <br><b>12.39 AntifraudСheckFailed</b> - Проверка антифрода не пройдена
          <br><b>12.43 PaymentErrorPC</b> - Ошибка от процессинга
          <br><b>12.44 PaymentErrorColvir</b> - Ошибка проведения платежа в Colvir
          <br><b>12.45 CodeRejectedByAP</b> - Ошибка обращения к ПС Астана Плат

confirm-payment-for-mobile:
  post:
    tags:
      - Платежи
    summary: Проверка отп и выполнение оплаты сотовой связи
    description: Создание оплаты сотовой связи
    operationId: confirmPaymentForMobile
    security:
      - BearerTokenAuth: [ "active" ]
    requestBody:
      $ref: "#/components/requestBodies/ConfirmPaymentForMobileRequest"
    responses:
      200:
        description: "Оплата сотовой связи подтверждена"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ConfirmPaymentForMobileResponse"
      400:
        description: |
          Ошибки выполнения платежа
          <br><b>12.3 CodePaymentsInvalidOTP</b> - Неверный одноразовый пароль
          <br><b>12.16 TransactionNotFoundError</b> - Транзакция платежа не найдена
          <br><b>12.23 OTPNumberAttemptsExceeded</b> - Превышено количество попыток ввода отп
          <br><b>12.43 PaymentErrorPC</b> - Ошибка от процессинга
          <br><b>12.44 PaymentErrorColvir</b> - Ошибка проведения платежа в Colvir
          <br><b>12.45 CodeRejectedByAP</b> - Ошибка обращения к ПС Астана Плат

check-client-by-phone-number:
  post:
    tags:
      - Платежи
    summary: Проверка клиента банка по номеру телефона
    description: Проверка клиента банка по номеру телефона
    operationId: checkClientByPhoneNumber
    security:
      - BearerTokenAuth: [ "active" ]
    requestBody:
      $ref: "#/components/requestBodies/CheckClientByPhoneNumberRequest"
    responses:
      200:
        description: Проверка клиента банка по номеру телефона прошла успешно
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CheckClientByPhoneNumberResponse"
      400:
        description: |
          Ошибки проверки номера телефона
          <br><b>12.33 InvalidPhoneNumber</b> - Неверный номер телефона
          <br><b>12.35 ClientNotFoundByPhoneNumber</b> - Клиент банка не найден по номеру телефона

create-internal-payment-by-phone-number:
  post:
    tags:
      - Платежи
    summary: Создание внутрибанковского перевода по номеру телефона
    description: Создание внутрибанковского перевода по номеру телефона
    operationId: createInternalPaymentByPhoneNumber
    security:
      - BearerTokenAuth: [ "active" ]
    requestBody:
      $ref: "#/components/requestBodies/CreateInternalPaymentByPhoneNumberRequest"
    responses:
      200:
        description: "Создание внутрибанковского перевода по номеру телефона успешно"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateInternalPaymentByPhoneNumberResponse"
      400:
        description: |
          Ошибки создания внутрибанковского перевода по номеру телефона
          <br><b>12.21 DublicatePayment</b> - Дубликат платежа
          <br><b>12.33 InvalidPhoneNumber</b> - Неверный номер телефона бенефициара
          <br><b>12.35 ClientNotFoundByPhoneNumber</b> - Клиент банка не найден по номеру телефона
          <br><b>12.36 MonthlyPaymentLimitExceeded</b> - Превышен месячный лимит на переводы
          <br><b>12.37 OneTimePaymentLimitExceeded</b> - Превышен разовый лимит на перевод
          <br><b>12.38 NotEnoughMoneyInAccounts</b> - Недостаточно средств на счёте для выполнения операции
          <br><b>12.39 AntifraudСheckFailed</b> - Проверка антифрода не пройдена
          <br><b>12.40 AMLСheckFailed</b> - Проверка AML не пройдена

confirm-internal-payment-by-phone-number:
  post:
    tags:
      - Платежи
    summary: Проверка отп и выполнение перевода по номеру телефона
    description: Проверка отп и выполнение внутрибанковского перевода по номеру телефона
    operationId: confirmInternalPaymentByPhoneNumber
    security:
      - BearerTokenAuth: [ "active" ]
    requestBody:
      $ref: "#/components/requestBodies/ConfirmInternalPaymentByPhoneNumberRequest"
    responses:
      200:
        description: "Внутрибанковский перевод по номеру телефона подтвердён"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ConfirmInternalPaymentByPhoneNumberResponse"
      400:
        description: |
          Ошибки подтверждения внутрибанковского перевода по номеру телефона
          <br><b>12.3 CodePaymentsInvalidOTP</b> - Неверный одноразовый пароль
          <br><b>12.16 TransactionNotFoundError</b> - Транзакция платежа не найдена
          <br><b>12.23 OTPNumberAttemptsExceeded</b> - Превышено количество попыток ввода отп

create-self-transfer:
  post:
    tags:
      - Платежи
    summary: Перевод между своими счетами
    description: Перевод между своими счетами
    operationId: createSelfTransfer
    security:
      - BearerTokenAuth: [ "active" ]
    requestBody:
      $ref: "#/components/requestBodies/CreateSelfTransferRequest"
    responses:
      200:
        description: "Перевод между своими счетами"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateSelfTransferResponse"
      400:
        description: |
          Ошибки подтверждения внутрибанковского перевода между своими счетами
          <br><b>12.7 InvalidPayerAccountError</b> - Счет пользователя введен не верно
          <br><b>12.21 DublicatePayment</b> - Дубликат платежа
          <br><b>12.22 UserBlocked</b> - Пользователь заблокирован
          <br><b>12.38 NotEnoughMoneyInAccounts</b> - Недостаточно средств на счёте для выполнения операции
          <br><b>12.40 AMLСheckFailed</b> - Проверка AML не пройдена
          <br><b>12.42 InvalidOperDate</b> - Операционная дата не определена

create-payment-kaspiqr:
  post:
    tags:
      - Платежи
    summary: Создание платежа для оплаты с помощью QR
    description: Создание платежа для оплаты с помощью QR
    operationId: CreatePaymentKaspiQR
    security:
      - BearerTokenAuth: [ "active" ]
    requestBody:
      $ref: "#/components/requestBodies/CreatePaymentKaspiQRRequest"
    responses:
      200:
        description: "Перевод между своими счетами"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreatePaymentKaspiQRResponse"
      400:
        description: |
          Ошибки создание платежа для оплаты с помощью QR
          <br><b>PaymentTimeout</b> - Сработал таймаут указанный в передаваемом параметре CanConfirmUntil от ПС
          <br><b>PaymentErrorPC</b> - При получении ошибки холдирования от ПЦ или его недоступности
          <br><b>PaymentErrorColvir</b> - При получении ошибки установки лимита в Colvir или его недоступности
          <br><b>RejectedByPS</b> - При получении кода ошибки с ПС или его недоступности
          <br><b>AccountArrest</b> - Счет аретован
          <br><b>12.38 NotEnoughMoneyInAccounts</b> - Недостаточно средств на счёте для выполнения операции
          <br><b>12.39 AntifraudСheckFailed</b> - Проверка антифрода не пройдена
          <br><b>12.40 AMLСheckFailed</b> - Проверка AML не пройдена

qr-token:
  post:
    tags:
      - Платежи
    summary: Получить данные по платежу по данным QR Токена
    description: Получить данные по платежу по данным QR Токена
    operationId: QRToken
    security:
      - BearerTokenAuth: [ "active" ]
    requestBody:
      $ref: "#/components/requestBodies/QRTokenRequest"
    responses:
      200:
        description: "Перевод между своими счетами"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/QRTokenResponse"
      400:
        description: |
          Ошибка получения данных по платежу по данным QR Токена
          <br><b>12.46 InvalidMCC</b> - МСС входит в список запрещенных к проведению
          <br><b>12.47 ErrorNoAccount</b> - Счет арестован или остаток < 0
          <br><b>12.48 ErrorNoCard</b> - У клиента нет счета в процессинге
          <br><b>12.49 KaspiSuspended</b> - Получена ошибка при запросе на расшифровку QR-токена либо метод недоступен
          <br><b>12.50 OverLimit</b> - Платеж выше установленного лимита

qr-session-termination:
  post:
    tags:
      - Платежи
    summary: Оповещения бэка о закрытии экрана проведения платежа
    description: Оповещения бэка о закрытии экрана проведения платежа
    operationId: QRSessionTermination
    security:
      - BearerTokenAuth: [ "active" ]
    requestBody:
      $ref: "#/components/requestBodies/QRSessionTerminationRequest"
    responses:
      200:
        description: "Успешная обработка запроса"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/QRSessionTerminationResponse"
      400:
        description: |
          Ошибки оповещения бэка о закрытии экрана проведения платежа

components:
  requestBodies:
    CreatePaymentByAccount:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              idempotencyID:
                type: string
                description: Ключ идемпотентности
                example: "59acde04-ef16-4a3d-9a95-4728d42e20cd"
              payerBinIIN:
                type: string
                description: ИИН/БИН отправителя
                maxLength: 12
                minLength: 12
              payerName:
                type: string
                description: ФИО/Наименование
                maxLength: 250
                minLength: 3
              payerAccount:
                type: string
                description: Счет клиента
                maxLength: 20
                minLength: 20
              amount:
                type: number
                format: double
                description: Сумма операции
                example: 99.95
              currency:
                type: string
                description: Валюта операции
                example: "KZT"
              beneficiaryBinIIN:
                type: string
                description: ИИН/БИН получателя
                pattern: "^[0-9]{12}$"
              beneficiaryName:
                type: string
                description: ФИО/Наименование получателя
                maxLength: 250
                minLength: 3
              beneficiaryType:
                type: integer
                format: int32
                description: Тип получателя
                example: 32
              beneficiaryTaxPayerType:
                $ref: '#/components/schemas/TaxPayerType'
                description: Тип налогоплательщика
              beneficiaryBank:
                type: string
                description: БИК банка получателя
              beneficiaryAccount:
                type: string
                description: IBAN cчета  получателя
              beneficiaryBankName:
                type: string
                description: Наименование банка получателя
              beneficiaryCountry:
                type: string
                description: Страна резидентство
              date:
                type: string
                format: date-time
                description: Дата операции
              valueDate:
                type: string
                format: date-time
                description: Дата валютирования
              kod:
                type: integer
                format: int32
                description: Код отправителя
              kbe:
                type: string
                description: Код бенефициара
              knp:
                type: string
                description: КНП, purposeCode
              paymentDetails:
                type: string
                description: Назначение платежа
                maxLength: 250
              actualSender:
                type: string
                description: Фактический отправитель (ФИО/Наименование)
              actualSenderBinIIN:
                type: string
                description: ИИН/БИН фактического отправителя
                pattern: "^[0-9]{12}$"
              actualSenderIsLegal:
                type: boolean
                description: Флаг юр лица фактического отправителя
              actualSenderCountry:
                type: string
                description: Страна резидентство фактического отправителя
              actualBeneficiary:
                type: string
                description: Фактический получатель (ФИО/Наименование)
              actualBeneficiaryBinIIN:
                type: string
                description: ИИН/БИН фактического получателя
                pattern: "^[0-9]{12}$"
              actualBeneficiaryIsLegal:
                type: boolean
                description: Флаг юр лица фактического получателя
              actualBeneficiaryCountry:
                type: string
                description: Страна резидентство фактического получателя
            required:
              - payerBinIIN
              - payerName
              - payerAccount
              - amount
              - currency
              - beneficiaryBinIIN
              - beneficiaryName
              - beneficiaryType
              - beneficiaryBank
              - beneficiaryAccount
              - beneficiaryBankName
              - kbe
              - knp
              - paymentDetails
              - idempotencyID

    ConfirmPaymentByAccount:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: string
                description: Код валидации отп
              attemptID:
                type: string
                format: uuid
                description: Идентификатор для валидации отп кода
                example: "2100e82d-e288-4882-8fac-3f1403449051"
            required:
              - attemptID
              - code

    CheckPhoneNumberRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              phoneNumber:
                type: string
                description: Номер телефона
                example: "+***********"
            required:
              - phoneNumber

    CreatePaymentForMobileRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              idempotencyID:
                type: string
                description: Ключ идемпотентности
                example: "59acde04-ef16-4a3d-9a95-4728d42e20cd"
              accountNumber:
                type: string
                description: Номер счёта с которого списываются деньги
              phoneNumber:
                type: string
                description: Номер телефона на который поступят деньги
                example: "+***********"
              amount:
                type: string
                pattern: '^\d+\.\d{2}$'
                example: "1234.56"
            required:
              - idempotencyID
              - accountNumber
              - phoneNumber
              - amount

    ConfirmPaymentForMobileRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: string
                description: Код валидации отп
              attemptID:
                type: string
                format: uuid
                description: Идентификатор для валидации отп кода
                example: "2100e82d-e288-4882-8fac-3f1403449051"
            required:
              - attemptID
              - code

    CheckClientByPhoneNumberRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              phoneNumber:
                type: string
                description: Номер телефона
                example: "+***********"
            required:
              - phoneNumber

    CreateInternalPaymentByPhoneNumberRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              idempotencyID:
                type: string
                description: Ключ идемпотентности
                example: "59acde04-ef16-4a3d-9a95-4728d42e20cd"
              payerAccountNumber:
                type: string
                description: Номер счёта с которого списываются деньги
              beneficiaryPhoneNumber:
                type: string
                description: Номер телефона клиента банка на счёт которого происходит перевод
                example: "+***********"
              amount:
                type: string
                pattern: '^\d+\.\d{2}$'
                example: "1234.56"
              currency:
                type: string
                description: Валюта перевода
                example: KZT
            required:
              - idempotencyID
              - payerAccountNumber
              - beneficiaryPhoneNumber
              - amount
              - currency

    ConfirmInternalPaymentByPhoneNumberRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: string
                description: Код валидации отп
              attemptID:
                type: string
                format: uuid
                description: Идентификатор для валидации отп кода
                example: "2100e82d-e288-4882-8fac-3f1403449051"
            required:
              - attemptID
              - code

    CreateSelfTransferRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              idempotencyID:
                type: string
                description: Ключ идемпотентности
                example: "59acde04-ef16-4a3d-9a95-4728d42e20cd"
              fromAccount:
                type: string
                description: Номер счёта с которого списываются деньги
              fromAccountСurrency:
                type: string
                description: Валюта перевода
                example: KZT
              toAccount:
                type: string
                description: Номер счёта на который поступят деньги
              toAccountСurrency:
                type: string
                description: Валюта перевода
                example: KZT
              amount:
                type: string
                pattern: '^\d+\.\d{2}$'
                example: "1234.56"

    CreatePaymentKaspiQRRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - idempotencyID
              - paymentID
              - payerAccount
              - paymentAmount
              - customerID
            properties:
              idempotencyID:
                type: string
                description: Ключ идемпотентности
                example: "59acde04-ef16-4a3d-9a95-4728d42e20cd"
              paymentID:
                type: string
                description: Идентификатор платежа от ПС
                maxLength: 64
              payerAccount:
                type: string
                description: IBAN счета отправителя (маскированный)
                example: "KZ123456**5678"
              paymentAmount:
                type: string
                description: Идентификатор возврата в ПС
                maxLength: 7
              customerID:
                type: string
                description: ИИН пользователя
                maxLength: 12

    QRTokenRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - qrToken
              - customerID
            properties:
              qrToken:
                type: string
                description: Содержимое отсканированного QR-кода
              customerID:
                type: string
                description: ИИН пользователя
                maxLength: 12

    QRSessionTerminationRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - paymentID
              - customerID
            properties:
              paymentID:
                type: string
                description: Идентификатор платежа платежной системы Kaspi Pay
                example: "59acde04-ef16-4a3d-9a95-4728d42e20cd"
              customerID:
                type: string
                description: ИИН пользователя
                maxLength: 12

  parameters:
    PaymentsGetTransactionByIDTransactionID:
      name: transactionID
      in: path
      description: Индектификатор транзакции
      schema:
        type: string
        format: uuid
      required: true
    PaymentsGetTransactionsStartDate:
      name: startDate
      in: query
      description: Начальная дата для фильтрации транзакций
      schema:
        type: string
      required: false
    PaymentsGetTransactionsEndDate:
      name: endDate
      in: query
      description: Конечная дата для фильтрации транзакций
      schema:
        type: string
      required: false
    PaymentsGetTransactionsAccounts:
      name: accounts
      in: query
      description: Список номеров счетов для фильтрации транзакций
      schema:
        type: array
        items:
          type: string
      required: true
    PaymentsGetTransactionsCards:
      name: cards
      in: query
      description: Список карт для фильтрации транзакций
      schema:
        type: array
        items:
          type: string
    PaymentsGetTransactionsOperationType:
      name: direction
      in: query
      description: Тип операции (например, CREDIT или DEBIT)
      schema:
        type: string
        enum: [CREDIT, DEBIT]
      required: false
    PaymentsGetTransactionsCounterparty:
      name: counterparty
      in: query
      description: Контрагент для фильтрации (получатель или отправитель средств)
      schema:
        type: string
      required: false
    PaymentsGetTransactionsMinAmount:
      name: minAmount
      in: query
      description: Минимальная сумма транзакции
      schema:
        type: string
      required: false
    PaymentsGetTransactionsMaxAmount:
      name: maxAmount
      in: query
      description: Максимальная сумма транзакции
      schema:
        type: string
      required: false
    PaymentsGetTransactionsLimit:
      name: limit
      in: query
      description: Лимит количества возвращаемых транзакций
      schema:
        type: integer
        format: int64
      required: false
    PaymentsGetTransactionsOffset:
      name: offset
      in: query
      description: Смещение для пагинации
      schema:
        type: integer
        format: int64
      required: false
    PaymentsGetHistoryClientIinBin:
      name: clientIinBin
      in: query
      description: ИИН/БИН клиента
      schema:
        type: string
      required: true


  schemas:
    CreatePaymentByAccountResponse:
      type: object
      required:
        - status
        - message
        - transactionID
        - otpRequired
      properties:
        status:
          type: string
          description: Статус операции
          example: "success"
        message:
          type: string
          description: Информация из бэк по статусу
          example: "Платеж в обработке"
        otpRequired:
          type: boolean
          description: Признак нужно ли проводить проверки по отп
          example: true
        transactionID:
          type: string
          format: uuid
          description: Идентификатор транзакции в БД
          example: "41c3f02f-436d-4f9b-8657-694f4c8890f8"
        otpResponse:
          $ref: "#/components/schemas/OtpFullResponse"

    ConfirmPaymentByAccountResponse:
      type: object
      required:
        - status
        - message
      properties:
        status:
          type: string
          description: Статус операции
        message:
          type: string
          description: Информация из бэк по статусу

    PaymentsGetTransactionsResponse:
      type: object
      required:
        - transactions
        - totalCount
        - limit
        - offset
        - startDate
      properties:
        transactions:
          type: array
          items:
            $ref: '#/components/schemas/PaymentsTransaction'
          description: Список транзакций
        totalCount:
          type: integer
          format: int64
          description: Общее количество транзакций
        limit:
          type: integer
          format: int64
          description: Лимит количества возвращаемых транзакций
        offset:
          type: integer
          format: int64
          description: Смещение для пагинации
        startDate:
          type: string
          format: date-time
          description: Начальная дата для фильтрации транзакций

    PaymentsTransaction:
      type: object
      required:
        - transactionID
        - accountNumber
        - amount
        - currency
        - direction
        - status
        - counterparty
        - transactionDate
        - transactionType
      properties:
        transactionID:
          type: string
          description: Уникальный идентификатор транзакции
        accountNumber:
          type: string
          description: Номер cчёта
        amount:
          type: number
          format: double
          description: Сумма транзакции
          example: 99.95
        currency:
          type: string
          description: Валюта транзакции
        direction:
          $ref: '#/components/schemas/PaymentsOperationType'
          description: Тип операции (кредит/дебет)
        status:
          $ref: '#/components/schemas/PaymentsTransactionStatus'
          description: Статус транзакции
        counterparty:
          type: string
          description: Контрагент
        transactionDate:
          type: string
          format: date
          description: Дата операции
        transactionType:
          $ref: '#/components/schemas/TransactionType'
          description: Тип транзакции

    PaymentsOperationType:
      type: string
      enum:
        - CREDIT
        - DEBIT
      description: Тип операции

    PaymentsTransactionStatus:
      type: string
      enum:
        - IN_PROGRESS
        - COMPLETED
        - REJECTED
      description: Статус транзакции

    PaymentsGetHistoryResponse:
      type: object
      properties:
        payments:
          type: array
          items:
            $ref: '#/components/schemas/PaymentsGetHistoryItem'
          description: Список элементов истории платежей

    PaymentsGetHistoryItem:
      type: object
      required:
        - clientIinBin
        - clientName
        - clientAccount
        - clientBankName
        - clientBankBic
        - counterpartyIinBin
        - counterpartyName
        - counterpartyAccount
        - counterpartyBankName
        - counterpartyBankBic
        - counterpartyKbe
        - amount
        - currency
        - knp
        - paymentDetails
        - date
      properties:
        clientIinBin:
          type: string
          description: ИИН/БИН клиента
        clientName:
          type: string
          description: Наименование клиента
        clientAccount:
          type: string
          description: Номер счёта клиента
        clientBankName:
          type: string
          description: Наименование банка клиента
        clientBankBic:
          type: string
          description: БИК банка
        counterpartyIinBin:
          type: string
          description: ИИН/БИН контрагента
        counterpartyName:
          type: string
          description: Название контрагента
        counterpartyAccount:
          type: string
          description: Счёт контрагента
        counterpartyBankName:
          type: string
          description: Название банка контрагента
        counterpartyBankBic:
          type: string
          description: БИК банка контрагента
        counterpartyKbe:
          type: string
          description: КБЕ контрагента
        amount:
          type: number
          format: double
          description: Сумма платежа
        currency:
          type: string
          description: Валюта платежа
        knp:
          type: string
          description: Код назначения платежа (КНП)
        paymentDetails:
          type: string
          description: Детали платежа
        date:
          type: string
          format: date
          description: Дата платежа
        valueDate:
          type: string
          format: date
          description: Дата исполнения операции
        actualClientIinBin:
          type: string
          description: ИИН/БИН фактического клиента (опционально)
        actualClientName:
          type: string
          description: Имя фактического клиента (опционально)
        actualClientCountry:
          type: string
          description: Страна фактического клиента (опционально)
        actualCounterpartyIinBin:
          type: string
          description: ИИН/БИН фактического контрагента (опционально)
        actualCounterpartyName:
          type: string
          description: Имя фактического контрагента (опционально)
        actualCounterpartyCountry:
          type: string
          description: Страна фактического контрагента (опционально)

    PaymentsCheckAccountIinRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - clientIinBin
              - account
            properties:
              clientIinBin:
                type: string
                description: ИИН/БИН клиента
              account:
                type: string
                description: Номер счёта

    PaymentsCheckAccountIinResponse:
      type: object
      required:
        - name
        - taxPayerType
        - bankBic
        - bankName
      properties:
        name:
          type: string
          description: Наименование ЮЛ/ФЛ
        taxPayerType:
          $ref: '#/components/schemas/TaxPayerType'
          description: Тип физ лица ЮЛ/ФЛ
        additionalIndividualType:
          $ref: '#/components/schemas/AdditionalIndividualType'
        bankBic:
          type: string
          description: БИК банка
        bankName:
          type: string
          description: Название банка

    AdditionalIndividualType:
      type: object
      description: Дополнительный тип для ФЛ если есть
      required:
        - type
        - name
      properties:
        type:
          type: integer
          description: Список кодов 32 - ИП, 64 — адвокат, 128 — частный нотариус, 256 — частный судебный исполнитель
        name:
          type: string
          description: Наименование дополнительного бенефициара для данного доп.  типа ФЛ

    TaxPayerType:
      type: string
      enum:
        - INDIVIDUAL
        - LEGAL_ENTITY
      description: Тип налогоплательщика

    TransactionType:
      type: string
      enum:
        - OTHER
        - PAYMENT_BY_ACCOUNT
        - PAYMENT_MOBILE
        - PAYMENT_TERMINAL
        - PAYMENT_BY_PHONE_NUMBER
        - BETWEEN_OWN_ACCKZT
      description: Внутренний тип перевода

    OtpFullResponse:
      type: object
      properties:
        attemptID:
          type: string
          format: uuid
          description: Идентификатор для валидации отп кода
          example: "2100e82d-e288-4882-8fac-3f1403449051"
        retryTime:
          type: integer
          description: Количество секунд до следующей отправки
          example: 60
        codeChecksLeft:
          type: integer
          description: Количество оставшихся попыток проверки кода отп
        attemptsLeft:
          type: integer
          description: Количество оставшихся попыток
        attemptsTimeout:
          type: integer
          description: Количество секунд жизни попытки отп валидации
      required:
        - attemptID
        - retryTime
        - codeChecksLeft
        - attemptsLeft
        - attemptsTimeout

    PaymentsGetTransactionByIDResponse:
      type: object
      properties:
        transactionID:
          type: string
          description: Идентификатор транзакции
        transactionNumber:
          type: string
          description: Пользовательский номер транзакции
        transactionDate:
          type: string
          format: date-time
          description: Дата операции
        valueDate:
          type: string
          format: date-time
          description: Дата исполнения операции
        transactionType:
          $ref: '#/components/schemas/TransactionType'
          description: Тип транзакции
        direction:
          $ref: '#/components/schemas/PaymentsOperationType'
          description: Тип операции (кредит/дебет)
        status:
          $ref: '#/components/schemas/PaymentsTransactionStatus'
          description: Статус транзакции
        amount:
          type: number
          format: double
          description: Сумма операции
          example: 99.95
        commission:
          type: number
          format: double
          description: Коммиссия
          example: 1.95
        currency:
          type: string
          description: Валюта операции
          example: "KZT"
        payerIinBin:
          type: string
          description: ИИН/БИН отправителя
        payerName:
          type: string
          description: Наименование отправителя
        payerAccount:
          type: string
          description: Номер счёта отправителя
        beneficiaryIinBin:
          type: string
          description: ИИН/БИН получателя
        beneficiaryName:
          type: string
          description: Наименование получателя
        beneficiaryAccount:
          type: string
          description: Номер счёта получателя
        kbe:
          type: string
          description: Код бенефициара
        phoneNumber:
          type: string
          description: Номер телефона
          example: "+***********"
        phoneOperatorName:
          type: string
          description: Наименование мобильного оператора
        transactionDetails:
          type: string
          description: Детали транзакции
        knp:
          type: string
          description: Код назначения платежа (КНП)
      required:
        - transactionID
        - transactionNumber
        - transactionDate
        - transactionType
        - direction
        - status
        - amount
        - commission
        - currency
        - transactionDetails
        - knp

    PaymentsGetTransactionReceiptResponse:
      type: object
      properties:
        title:
          type: string
          description: Наименование документа
        fileLink:
          type: string
          description: Ссылка на файл
      required:
        - title
        - fileLink

    CheckPhoneNumberResponse:
      type: object
      properties:
        phoneOperator:
          type: string
      required:
        - phoneOperator

    CreatePaymentForMobileResponse:
      type: object
      properties:
        transactionID:
          type: string
          format: uuid
          description: Идентификатор транзакции в БД
          example: "41c3f02f-436d-4f9b-8657-694f4c8890f8"
        status:
          $ref: '#/components/schemas/PaymentsTransactionStatus'
          description: Статус транзакции
        otpRequired:
          type: boolean
          description: Признак нужно ли проводить проверки по отп
          example: true
        otpResponse:
          $ref: "#/components/schemas/OtpFullResponse"
      required:
        - transactionID
        - status
        - otpRequired

    ConfirmPaymentForMobileResponse:
      type: object
      required:
        - status
      properties:
        status:
          $ref: '#/components/schemas/PaymentsTransactionStatus'
          description: Статус транзакции


    CheckClientByPhoneNumberResponse:
      type: object
      required:
        - clientName
      properties:
        clientName:
          type: string
          description: Наименование клиента банка в сокращённом варианте
          example: Имя Ф.

    CreateInternalPaymentByPhoneNumberResponse:
      type: object
      properties:
        transactionID:
          type: string
          format: uuid
          description: Идентификатор транзакции в БД
          example: "41c3f02f-436d-4f9b-8657-694f4c8890f8"
        status:
          $ref: '#/components/schemas/PaymentsTransactionStatus'
          description: Статус транзакции
        otpRequired:
          type: boolean
          description: Признак нужно ли проводить проверки по отп
          example: true
        otpResponse:
          $ref: "#/components/schemas/OtpFullResponse"
      required:
        - transactionID
        - status
        - otpRequired

    ConfirmInternalPaymentByPhoneNumberResponse:
      type: object
      required:
        - status
      properties:
        status:
          $ref: '#/components/schemas/PaymentsTransactionStatus'
          description: Статус транзакции

    CreateSelfTransferResponse:
      type: object
      required:
        - status
      properties:
        transactionID:
          type: string
          format: uuid
          description: Идентификатор транзакции в БД
          example: "41c3f02f-436d-4f9b-8657-694f4c8890f8"
        status:
          $ref: '#/components/schemas/PaymentsTransactionStatus'
          description: Статус транзакции

    CreatePaymentKaspiQRResponse:
      type: object
      required:
        - status
        - transactionID
      properties:
        transactionID:
          type: string
          format: uuid
          description: Идентификатор транзакции в БД
          example: "41c3f02f-436d-4f9b-8657-694f4c8890f8"
        status:
          $ref: '#/components/schemas/PaymentsTransactionStatus'
          description: Статус транзакции

    QRTokenResponse:
      type: object
      required:
        - qrCodeType
        - paymentID
      properties:
        qrCodeType:
          type: string
          description: "Тип QR токена"
        merchantName:
          type: string
          description: "Наименование продавца"
        paymentID:
          type: string
          description: "Идентификатор платежа в ПС"
        paymentAmount:
          type: string
          description: "Сумма платежа"
        paymentData:
          type: object
          description: "Объект содержащий описание всех дополнительных полей"
          properties:
            serviceId:
              type: integer
              format: int32
              description: "Идентификатор сервиса"
            serviceName:
              type: string
              description: "Наименование сервиса"
            parameters:
              type: array
              description: "Массив параметров"
              items:
                type: object
                properties:
                  regex:
                    type: string
                    description: "Параметры для валидации поля"
                    example: "^7\\d{9}$"
                  value:
                    type: string
                    description: "Значение параметра. Пустое — значит, клиент должен заполнить значение"
                  name:
                    type: string
                    description: "Описание value на языке локализации"
                  options:
                    type: array
                    description: "Элементы выпадающего списка"
                    items:
                      type: object
                      properties:
                        key:
                          type: string
                          description: "Идентификатор элемента"
                        label:
                          type: string
                          description: "Отображаемое название"
                        description:
                          type: string
                          description: "Дополнительное описание"

    QRSessionTerminationResponse:
      type: object
      required:
        - result
      properties:
        result:
          type: string
