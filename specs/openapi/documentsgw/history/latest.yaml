openapi: 3.0.1
info:
  title: Zaman Documents API
  version: dev-0916-08335e49
  description: |
    API для бэкэнда gateway Documents

    Спецификация в формате [swagger](../swagger) или [redoc](../redoc).

    [Changelog](../history/changelog_main.html)

    [Release Changelog](../history/changelog_feature.html)
  termsOfService: https://zaman/terms-of-service
  x-logo:
    url: ../static/logo.png
servers:
  - description: Dev server
    url: some-url
  - description: Pre-prod server
    url: some-url
tags:
  - name: Документы
  - name: Системные
paths:
  /health:
    get:
      tags:
        - Системные
      summary: Проверка на работоспособность
      description: Проверка всех модулей системы на работоспособность
      operationId: health
      security:
        - BearerTokenAuth: []
      responses:
        '200':
          description: Информация по запрошенному статусу микросервисов
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Health'
  /{bucketName}/{docName}:
    get:
      tags:
        - Документы
      summary: Скачивание приватного документа
      description: |
        Скачивание приватного документа с учетом userID и параметров, переданных в токене
      operationId: downloadPrivateDoc
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
        - $ref: '#/components/parameters/BucketNamePathParam'
        - $ref: '#/components/parameters/DocumentNamePathParam'
      responses:
        '200':
          $ref: '#/components/responses/FileDownloadResponse'
        '400':
          description: |
            Ошибка запроса
            <br><b>4.3 InvalidDocID</b> - Неверный ID документа
            <br><b>4.8 UserInfoMismatch</b> - Информация о пользователе не корректна
            <br><b>4.17 FailedToReadFile</b> - Не удалось прочитать файл
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /public/{docName}:
    get:
      tags:
        - Документы
      summary: Скачивание публичного документа
      description: |
        Скачивание публичного документа
      operationId: downloadPublicDoc
      security: []
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
        - $ref: '#/components/parameters/DocumentNamePathParam'
      responses:
        '200':
          $ref: '#/components/responses/FileDownloadResponse'
        '400':
          description: |
            Ошибка запроса
            <br><b>4.3 InvalidDocID</b> - Неверный ID документа
            <br><b>4.17 FailedToReadFile</b> - Не удалось прочитать файл
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
components:
  schemas:
    APIError:
      type: object
      description: Общий формат ошибки API. Возвращает цифровой код
      properties:
        error:
          type: string
        fields:
          type: object
          description: Объект с описанием деталей ошибок
          additionalProperties:
            type: string
      required:
        - error
      example:
        error: X.0
    Health:
      required:
        - documents
      type: object
      properties:
        documents:
          type: boolean
          description: Статус сервиса documents
  securitySchemes:
    BearerTokenAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  parameters:
    AcceptLanguage:
      name: Accept-Language
      in: header
      description: Язык запроса
      required: false
      schema:
        type: string
        enum:
          - kk
          - en
          - ru
        example: ru
        default: kk
    BucketNamePathParam:
      name: bucketName
      in: path
      description: Имя бакета
      required: true
      schema:
        type: string
        pattern: ^[a-zA-Z0-9-_]+$
        example: 95e33586-a2d8-47e0-9a1d-ca938d1f2f3c
    DocumentNamePathParam:
      name: docName
      in: path
      description: |
        Имя файла в формате file_name.extension
      required: true
      schema:
        type: string
        pattern: ^[a-zA-Z0-9-_]+\.[a-zA-Z0-9]+$
        example: a28ffbf6-c69d-437c-a674-00847c150fdc_v1.pdf
  responses:
    FileDownloadResponse:
      description: Файл успешно скачан
      content:
        application/octet-stream:
          schema:
            type: string
            format: binary
      headers:
        Content-Disposition:
          description: Заголовок для указания имени файла
          schema:
            type: string
            example: attachment; filename="example.txt"
