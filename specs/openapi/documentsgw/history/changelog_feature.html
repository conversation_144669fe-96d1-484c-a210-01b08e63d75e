<html>
<body>
	<div class="title">API Changelog dev-0915-34314d22 vs. dev-0916-08335e49</div>

	<div class="title">API Changelog dev-0912-2bc22638 vs. dev-0915-34314d22</div>

	<div class="title">API Changelog dev-0911-404023ab vs. dev-0912-2bc22638</div>

	<div class="title">API Changelog dev-0909-3279d8c0 vs. dev-0911-404023ab</div>

	<div class="title">API Changelog dev-0908-41fb7393 vs. dev-0909-3279d8c0</div>

	<div class="title">API Changelog dev-0905-318daeb6 vs. dev-0908-41fb7393</div>

	<div class="title">API Changelog dev-0904-7c34a26c vs. dev-0905-318daeb6</div>

    <div class="endpoint">
        <div class="endpoint-header">
            <span class="path">
                <div class="">GET /health</div>
            </span>
            <div class="change-type">Updated</div>
        </div>
        <ul class="endpoint-changes">

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;altScoreBridge&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;amlBridge&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;antifraud&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;apBridge&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;balanceUpdater&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;bitrixBridge&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;bsasBridge&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;btsBridge&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;cardsAccounts&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;collection&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;colvirBridge&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;crm&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;deposits&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;dictionary&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;fileGuard&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;jiraBridge&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;juicyscoreBridge&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;kaspiBridge&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;keycloakProxy&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;kgdBridge&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;liveness&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;loans&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;notifications&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;otp&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;payments&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;paymentsSme&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;pkbBridge&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;processingBridge&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;qazpostBridge&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;referral&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;scoring&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;seonBridge&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;smsBridge&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;sprBridge&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;taskManager&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;tokenize&#39; из ответа со статусом &#39;200&#39;
            </li>

            <li class="change">

            <div class="breaking tooltip" data-text="Breaking Change">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16" class="breaking-icon" role="img" aria-label="Important With Circle Icon"><path fill="currentColor" fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14ZM7 4.5a1 1 0 0 1 2 0v4a1 1 0 0 1-2 0v-4Zm2 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"></path></svg>
            </div>

            удалено обязательное поле ответа &#39;users&#39; из ответа со статусом &#39;200&#39;
            </li>

        </ul>
    </div>

	<div class="title">API Changelog dev-0903-15a15c85 vs. dev-0904-7c34a26c</div>

	<div class="title">API Changelog dev-0902-55d4e52e vs. dev-0903-15a15c85</div>

	<div class="title">API Changelog dev-0901-9908578d vs. dev-0902-55d4e52e</div>

	<div class="title">API Changelog dev-0829-f9371efa vs. dev-0901-9908578d</div>

    <div class="endpoint">
        <div class="endpoint-header">
            <span class="path">
                <div class="">GET /health</div>
            </span>
            <div class="change-type">Updated</div>
        </div>
        <ul class="endpoint-changes">

            <li class="change">

            добавил требуемое свойство &#39;bitrixBridge&#39; в ответ со статусом &#39;200&#39;
            </li>

        </ul>
    </div>

    <div class="endpoint">
        <div class="endpoint-header">
            <span class="path">
                <div class="">GET /public/{docName}</div>
            </span>
            <div class="change-type">Updated</div>
        </div>
        <ul class="endpoint-changes">

            <li class="change">

            схема безопасности точки доступа &#39;BearerTokenAuth&#39; была удалена из API
            </li>

        </ul>
    </div>

	<div class="title">API Changelog 1.1.0 vs. dev-0829-f9371efa</div>

	</body>
</html>

