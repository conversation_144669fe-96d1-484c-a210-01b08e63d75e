openapi: 3.0.1

servers:
  - description: Dev server
    url: some-url

  - description: Pre-prod server
    url: some-url

info:
  title: Zaman Documents API
  version: dev-0916-08335e49
  description: |
    API для бэкэнда gateway Documents

    Спецификация в формате [swagger](../swagger) или [redoc](../redoc).

    [Changelog](../history/changelog_main.html)

    [Release Changelog](../history/changelog_feature.html)
  termsOfService: https://zaman/terms-of-service
  x-logo:
    url: ../static/logo.png
tags:
  - name: "Документы"
  - name: "Системные"

paths:
  /health:
    $ref: "health.gen.yaml#/health"

  ## docs
  /{bucketName}/{docName}:
    $ref: "docs.yaml#/private-doc-download"
  /public/{docName}:
    $ref: "docs.yaml#/public-doc-download"

components:
  schemas:
    APIError:
      type: object
      description: Общий формат ошибки API. Возвращает цифровой код
      properties:
        error:
          type: string
        fields:
          type: object
          description: Объект с описанием деталей ошибок
          additionalProperties:
            type: string
      required:
        - error
      example:
        error: "X.0"

  securitySchemes:
    BearerTokenAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  parameters:
    AcceptLanguage:
      name: Accept-Language
      in: header
      description: Язык запроса
      required: false
      schema:
        type: string
        enum:
          - kk
          - en
          - ru
        example: ru
        default: kk
