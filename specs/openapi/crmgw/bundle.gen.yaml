openapi: 3.0.1
info:
  title: Zaman Crmgw API
  version: dev-0916-08335e49
  description: |
    API для бэкэнда CRM

    Спецификация в формате [swagger](../swagger) или [redoc](../redoc).

    [Changelog](../history/changelog_main.html)

    [Release Changelog](../history/changelog_feature.html)
  termsOfService: https://zaman/terms-of-service
  x-logo:
    url: ../static/logo.png
servers:
  - description: Dev server
    url: https://crmgw-dev.zaman.redmadrobot.com/api/v1
  - description: Stage server
    url: https://crmgw-stage.zaman.redmadrobot.com/api/v1
tags:
  - name: Системные
  - name: Профиль
  - name: Авторизация
  - name: Кредиты
  - name: Клиентские счета
  - name: Платежи
  - name: Депозиты
paths:
  /health:
    get:
      tags:
        - Системные
      summary: Проверка на работоспособность
      description: Проверка всех модулей системы на работоспособность
      operationId: health
      security:
        - BearerTokenAuth: []
      responses:
        '200':
          description: Информация по запрошенному статусу микросервисов
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Health'
  /users:
    get:
      tags:
        - Профиль
      summary: Персональные данные клиента
      description: Используется в CRM для поиска клиента по номеру телефона или ИИН
      operationId: getClientInfo
      security:
        - ApiKeyAuth: []
      parameters:
        - $ref: '#/components/parameters/PhoneQueryParam'
        - $ref: '#/components/parameters/IinQueryParam'
        - $ref: '#/components/parameters/OriginQueryParam'
      x-oas-docs:
        description: |
          Должен быть указан только один из параметров: phone или iin. Origin - обязателен.  
      x-parameter-validation:
        allOf:
          - oneOf:
              - required:
                  - phone
              - required:
                  - iin
          - required:
              - origin
      responses:
        '200':
          description: Данные о клиенте
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
        '400':
          description: |
            <br>Ошибки поиска клиента
            <br><b>1.0 ValidationError</b> - Ошибка валидации данных в теле запроса
            <br><b>2.0 UserNotFound</b> - Пользователь не найден
            <br><b>35.1 CodeCrmServiceUserNotFound</b> - Пользователь не найден (ошибка валидации пользователя)
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/APIError'
  /users/logout:
    post:
      tags:
        - Авторизация
      summary: Разлогин пользователя
      description: Используется для завершения сессии пользователя оператором из CRM
      operationId: logoutUser
      security:
        - ApiKeyAuth: []
      requestBody:
        $ref: '#/components/requestBodies/LogoutUserRequest'
      responses:
        '200':
          $ref: '#/components/responses/RespEmpty'
        '400':
          description: |
            <br>Ошибка выхода пользователя
            <br><b>1.0 ValidationError</b> - Ошибка валидации данных в теле запроса
            <br><b>2.0 UserNotFound</b> - Пользователь не найден
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/APIError'
                  - $ref: '#/components/schemas/ValidationError'
  /loans:
    get:
      tags:
        - Кредиты
      summary: Получение детальной информации по выданному займу
      description: Получение детальной информации по выданному займу
      operationId: getLoanDetails
      security:
        - ApiKeyAuth: []
      parameters:
        - $ref: '#/components/parameters/ApplicationIDQueryParam'
        - $ref: '#/components/parameters/UserIDQueryParam'
        - $ref: '#/components/parameters/StatusQueryParam'
        - $ref: '#/components/parameters/OriginHeaderParam'
      responses:
        '200':
          description: Запрос выполнен успешно, данные о кредитах переданы
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetLoansDetailsResponse'
        '400':
          description: |
            <br>Ошибки получения данных о кредите
            <br><b>1.0 ValidationError</b> - Ошибка валидации данных в теле запроса
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /accounts:
    get:
      tags:
        - Клиентские счета
      summary: Получение счета клиента
      description: Получение счета клиента
      operationId: getAccountDetails
      security:
        - ApiKeyAuth: []
      parameters:
        - $ref: '#/components/parameters/AccountIDPathParam'
        - $ref: '#/components/parameters/UserIDQueryParam'
      responses:
        '200':
          description: Счет клиента
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientAccountResponse'
        '400':
          description: |
            <br>Ошибки получения данных о счете
            <br><b>1.0 ValidationError</b> - Ошибка валидации данных в теле запроса
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /transactions:
    get:
      tags:
        - Платежи
      summary: История операций по счету
      description: История операций
      operationId: getAccountTransactions
      security:
        - ApiKeyAuth: []
      parameters:
        - $ref: '#/components/parameters/TransactionsAccountParam'
        - $ref: '#/components/parameters/UserIDQueryParam'
        - $ref: '#/components/parameters/TransactionsStartDateParam'
        - $ref: '#/components/parameters/TransactionsEndDateParam'
        - $ref: '#/components/parameters/TransactionsOperationTypeParam'
        - $ref: '#/components/parameters/TransactionsMinAmountParam'
        - $ref: '#/components/parameters/TransactionsMaxAmountParam'
        - $ref: '#/components/parameters/TransactionsLimitParam'
        - $ref: '#/components/parameters/TransactionsOffsetParam'
      responses:
        '200':
          description: История операций по счету
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionsResponse'
        '400':
          description: |
            <br>Ошибки получения данных о транзакциях
            <br><b>1.0 ValidationError</b> - Ошибка валидации данных в теле запроса
            <br><b>35.3 ServiceFailedToConvertError</b> - Ошибка при конвертации данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /deposits:
    get:
      tags:
        - Депозиты
      summary: Получение детализации депозита
      description: Возвращает детальную информацию о депозите по его ID
      operationId: getDepositDetails
      security:
        - ApiKeyAuth: []
      parameters:
        - $ref: '#/components/parameters/UserIDQueryParam'
        - $ref: '#/components/parameters/DepositIDQueryParam'
      responses:
        '200':
          description: Успешное получение детализации депозита
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetDepositDetailResp'
        '400':
          description: |
            <br>Ошибки получения данных о депозите
            <br><b>1.0 ValidationError</b> - Ошибка валидации данных в теле запроса
            <br><b>35.27.1 DepositDetailsNotFound</b> - Депозит не найден
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
components:
  securitySchemes:
    BearerTokenAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: API Key для доступа к CRM API
  responses:
    RespEmpty:
      description: Операция выполнена успешно
      content:
        application/json:
          schema:
            type: object
            properties: {}
  parameters:
    UserIDQueryParam:
      name: userID
      in: query
      description: Идентификатор пользователя (uuid)
      required: true
      schema:
        type: string
        pattern: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
        maxLength: 36
    AccountIDPathParam:
      name: accountID
      in: query
      description: Идентификатор запрашиваемого счета (uuid)
      required: true
      schema:
        type: string
        pattern: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
        maxLength: 36
    PhoneQueryParam:
      name: phone
      in: query
      schema:
        type: string
        pattern: ^\+7[0-9]{10}$
      required: false
      description: Номер телефона в формате +7XXXXXXXXXX. Нельзя использовать одновременно с iin.
      example: '+***********'
    IinQueryParam:
      name: iin
      in: query
      schema:
        type: string
        pattern: ^\d{12}$
      required: false
      description: ИИН клиента. Нельзя использовать одновременно с phone.
    OriginQueryParam:
      name: origin
      in: query
      schema:
        type: string
        enum:
          - FIZ
          - IP
      required: true
      description: Флаг типа клиента ФЛ (FIZ) или ИП (IP)
      example: FIZ
    ApplicationIDQueryParam:
      name: applicationID
      in: query
      description: Идентификатор заявки на кредит (uuid)
      required: true
      schema:
        type: string
        pattern: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
        maxLength: 36
    StatusQueryParam:
      name: status
      in: query
      description: Статус заявки на кредит
      required: true
      schema:
        type: string
    OriginHeaderParam:
      name: origin
      in: header
      schema:
        type: string
        enum:
          - FIZ
          - IP
      required: true
      description: Флаг типа клиента ФЛ (FIZ) или ИП (IP)
      example: FIZ
    TransactionsAccountParam:
      name: account
      in: query
      description: Номер счета
      schema:
        type: string
      required: true
    TransactionsStartDateParam:
      name: startDate
      in: query
      description: Начальная дата для фильтрации транзакций
      schema:
        type: string
        format: date-time
      required: false
    TransactionsEndDateParam:
      name: endDate
      in: query
      description: Конечная дата для фильтрации транзакций
      schema:
        type: string
        format: date-time
      required: false
    TransactionsOperationTypeParam:
      name: direction
      in: query
      description: Тип операции (например, CREDIT или DEBIT)
      schema:
        type: string
        enum:
          - CREDIT
          - DEBIT
      required: false
    TransactionsMinAmountParam:
      name: minAmount
      in: query
      description: Минимальная сумма транзакции
      schema:
        type: string
      required: false
    TransactionsMaxAmountParam:
      name: maxAmount
      in: query
      description: Максимальная сумма транзакции
      schema:
        type: string
      required: false
    TransactionsLimitParam:
      name: limit
      in: query
      description: Лимит количества возвращаемых транзакций
      schema:
        type: integer
        format: int64
      required: false
    TransactionsOffsetParam:
      name: offset
      in: query
      description: Смещение для пагинации
      schema:
        type: integer
        format: int64
      required: false
    DepositIDQueryParam:
      name: depositID
      in: query
      description: Идентификатор депозита
      required: true
      schema:
        type: string
  schemas:
    APIError:
      type: object
      description: Ошибка для CRM
      properties:
        code:
          type: integer
          description: Код ошибки
        message:
          type: string
          description: Текст ошибки
    ValidationError:
      type: object
      description: Ошибка валидации данных в теле запроса
      properties:
        error:
          type: string
          description: Код ошибки
          example: '1.0'
        fields:
          type: object
          description: Объект с описанием ошибок валидации полей
          additionalProperties:
            type: string
          example:
            iin: value must be a string
      required:
        - error
    Money:
      type: object
      description: Сумма
      required:
        - value
        - currencyCode
      properties:
        value:
          type: string
          description: Сумма
        currencyCode:
          $ref: '#/components/schemas/CurrencyCode'
    CurrencyCode:
      type: string
      description: Код валюты
      enum:
        - KZT
        - RUB
        - EUR
        - USD
        - CNY
    Health:
      required:
        - users
        - otp
        - documents
        - notifications
        - keycloakProxy
        - kgdBridge
        - btsBridge
        - smsBridge
        - loans
        - colvirBridge
        - payments
        - cardsAccounts
        - dictionary
        - pkbBridge
        - amlBridge
        - liveness
        - taskManager
        - juicyscoreBridge
        - jiraBridge
        - fileGuard
        - scoring
        - seonBridge
        - sprBridge
        - altScoreBridge
        - qazpostBridge
        - deposits
        - bsasBridge
        - apBridge
        - processingBridge
        - collection
        - paymentsSme
        - referral
        - antifraud
        - crm
        - tokenize
        - balanceUpdater
        - kaspiBridge
        - bitrixBridge
        - foreignActivity
      type: object
      properties:
        users:
          type: boolean
          description: Статус сервиса users
        otp:
          type: boolean
          description: Статус сервиса otp
        documents:
          type: boolean
          description: Статус сервиса documents
        notifications:
          type: boolean
          description: Статус сервиса notifications
        keycloakProxy:
          type: boolean
          description: Статус сервиса keycloakProxy
        kgdBridge:
          type: boolean
          description: Статус сервиса kgdBridge
        btsBridge:
          type: boolean
          description: Статус сервиса btsBridge
        smsBridge:
          type: boolean
          description: Статус сервиса smsBridge
        loans:
          type: boolean
          description: Статус сервиса loans
        colvirBridge:
          type: boolean
          description: Статус сервиса colvirBridge
        payments:
          type: boolean
          description: Статус сервиса payments
        cardsAccounts:
          type: boolean
          description: Статус сервиса cardsAccounts
        dictionary:
          type: boolean
          description: Статус сервиса dictionary
        pkbBridge:
          type: boolean
          description: Статус сервиса pkbBridge
        amlBridge:
          type: boolean
          description: Статус сервиса amlBridge
        liveness:
          type: boolean
          description: Статус сервиса liveness
        taskManager:
          type: boolean
          description: Статус сервиса taskManager
        juicyscoreBridge:
          type: boolean
          description: Статус сервиса juicyscoreBridge
        jiraBridge:
          type: boolean
          description: Статус сервиса jiraBridge
        fileGuard:
          type: boolean
          description: Статус сервиса fileGuard
        scoring:
          type: boolean
          description: Статус сервиса scoring
        seonBridge:
          type: boolean
          description: Статус сервиса seonBridge
        sprBridge:
          type: boolean
          description: Статус сервиса sprBridge
        altScoreBridge:
          type: boolean
          description: Статус сервиса altScoreBridge
        qazpostBridge:
          type: boolean
          description: Статус сервиса qazpostBridge
        deposits:
          type: boolean
          description: Статус сервиса deposits
        bsasBridge:
          type: boolean
          description: Статус сервиса bsasBridge
        apBridge:
          type: boolean
          description: Статус сервиса apBridge
        processingBridge:
          type: boolean
          description: Статус сервиса processingBridge
        collection:
          type: boolean
          description: Статус сервиса collection
        paymentsSme:
          type: boolean
          description: Статус сервиса paymentsSme
        referral:
          type: boolean
          description: Статус сервиса referral
        antifraud:
          type: boolean
          description: Статус сервиса antifraud
        crm:
          type: boolean
          description: Статус сервиса crm
        tokenize:
          type: boolean
          description: Статус сервиса tokenize
        balanceUpdater:
          type: boolean
          description: Статус сервиса balanceUpdater
        kaspiBridge:
          type: boolean
          description: Статус сервиса kaspiBridge
        bitrixBridge:
          type: boolean
          description: Статус сервиса bitrixBridge
        foreignActivity:
          type: boolean
          description: Статус сервиса foreignActivity
    Address:
      type: object
      properties:
        country:
          type: string
          description: Страна
          example: KZ
        district:
          type: string
          description: Район
          example: Астана
        region:
          type: string
          description: Область/Регион
          example: Есильский
        city:
          type: string
          description: Город
          example: Астана
        street:
          type: string
          description: Улица
          example: Абая
        building:
          type: string
          description: Дом
          example: '1'
        corpus:
          type: string
          description: Корпус
          example: '1'
        flat:
          type: string
          description: Квартира
          example: '1'
        beginDate:
          type: string
          format: date
          description: Дата начала регистрации
          example: '2022-05-10'
    Account:
      type: object
      required:
        - accountID
        - iban
        - status
        - openDate
        - currency
      properties:
        accountID:
          type: string
          description: Идентификатор счета
          example: 90b67b89-4659-421b-9499-c195cfc5d034
        iban:
          type: string
          description: IBAN счета
          example: ********************
        status:
          type: string
          description: Статус счета
          example: Active
        openDate:
          type: string
          format: date
          description: Дата открытия счета
          example: '2022-05-10'
        currency:
          type: string
          description: Валюта счета
          example: KZT
    LoanAmount:
      type: object
      description: Сумма займа
      required:
        - currencyCode
        - value
      properties:
        currencyCode:
          type: string
          description: Код валюты
          example: KZT
        value:
          type: string
          description: Сумма займа
          example: '5000000'
    Loan:
      type: object
      required:
        - applicationID
        - loanAmount
        - productType
      properties:
        applicationID:
          type: string
          description: Идентификатор заявки
          example: a12f4b89-1234-5678-9876-bbcdfe45698a
        status:
          type: string
          description: Статус кредитной заявки
          example: COMPLETED
        loanAmount:
          $ref: '#/components/schemas/LoanAmount'
        applicationDueDate:
          type: string
          format: date
          description: Дата подачи заявки
          example: '2023-07-01'
        productType:
          type: string
          description: Тип продукта
          example: consumer
        purpose:
          type: string
          description: Цель финансирования
          example: Оборотные средства
    DepositShort:
      type: object
      required:
        - id
        - currency
        - productCode
        - depositBalance
        - payedAmount
        - rate
        - endDate
      properties:
        id:
          type: string
          description: Идентификатор депозита
          example: '735_2632945'
        currency:
          type: string
          description: Валюта депозита
          example: KZT
        productCode:
          type: string
          description: Код шаблона продукта
          example: ZB.310.01
        depositBalance:
          type: number
          format: double
          description: Баланс депозита
          example: 1000000
        payedAmount:
          type: string
          description: Общая сумма процентов
          example: '1000000'
        rate:
          type: string
          description: Доходность в процентах
          example: '15'
        endDate:
          type: string
          format: date
          description: Дата окончания действия договора
          example: '2023-07-01'
    UserResponse:
      type: object
      required:
        - userID
        - phone
        - iin
        - status
        - origin
      properties:
        userID:
          type: string
          description: Уникальный идентификатор клиента
          example: 550e8400-e29b-41d4-a716-446655440000
        phone:
          type: string
          pattern: ^\+7[0-9]{10}$
          description: Номер телефона клиента
          example: '+***********'
        iin:
          type: string
          pattern: ^\d{12}$
          description: ИИН клиента
          example: '900101300455'
        status:
          type: string
          description: Статус клиента
          example: Active
        origin:
          type: string
          description: Тип клиента
          example: IP
        firstname:
          type: string
          description: Имя клиента
          example: Болат
        lastname:
          type: string
          description: Фамилия клиента
          example: Алибаев
        patronymic:
          type: string
          description: Отчество клиента
          example: Аскарович
        birthDate:
          type: string
          format: date
          description: Дата рождения
        gender:
          type: string
          description: Пол клиента
        citizenship:
          type: string
          description: Гражданство
          example: KZ
        regAddress:
          $ref: '#/components/schemas/Address'
        birthPlace:
          $ref: '#/components/schemas/Address'
        registrationCert:
          type: string
          description: Номер сертификата
          example: '**********'
        registrationDate:
          type: string
          format: date
          description: Дата регистрации
        issuingAuthority:
          type: string
          description: Орган выдачи
          example: Астана
        ipName:
          type: string
          description: Наименование ИП
          example: ИП Алибаев
        okedValueName:
          type: string
          description: ОКЭД
          example: '**********'
        accounts:
          type: array
          items:
            $ref: '#/components/schemas/Account'
        loans:
          type: array
          items:
            $ref: '#/components/schemas/Loan'
        deposits:
          type: array
          items:
            $ref: '#/components/schemas/DepositShort'
    Reason:
      type: object
      description: Причина отказа
      required:
        - code
        - messageText
        - title
      properties:
        code:
          type: string
          description: Код ошибки
        messageText:
          type: string
          description: Текст ошибки
        title:
          type: string
          description: Заголовок ошибки
    PaidInfo:
      type: object
      description: Информация о выплате по кредиту
      required:
        - remainingAmount
      properties:
        remainingAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Оставшаяся сумма выплаты по кредиту
    NextPayment:
      type: object
      description: Информация о ближайшем платеже
      required:
        - date
        - amount
      properties:
        date:
          type: string
          format: date
          description: Дата ближайшего платежа
        amount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма ближайшего платежа
    AccountInfo:
      type: object
      description: Информация по счету
      required:
        - ibanLastDigits
        - amount
        - iban
        - iin
        - fullName
        - bankName
        - bankBin
        - bankBic
      properties:
        ibanLastDigits:
          type: string
          description: Замаскированный номер счета списания
        amount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Баланс счета
        iban:
          type: string
          description: Счет для погашения (IBAN)
        iin:
          type: string
          description: ИИН клиента
        fullName:
          type: string
          description: ФИО клиента
        bankName:
          type: string
          description: Наименование банка
        bankBin:
          type: string
          description: БИН банка
        bankBic:
          type: string
          description: БИК банка
    LoanDetailsObject:
      type: object
      description: Детали финансирования
      required:
        - contractNumber
        - loanAmount
        - startDate
        - endDate
        - term
        - interestAmount
      properties:
        contractNumber:
          type: string
          description: Номер договора
        loanAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма финансирования (на весь срок)
        startDate:
          type: string
          format: date
          description: Дата начала договора
        endDate:
          type: string
          format: date
          description: Дата окончания договора
        term:
          type: integer
          description: Срок финасирования
        interestAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма наценки на весь срок финансирования
    Overdue:
      type: object
      description: Просроченный платеж
      required:
        - hint
      properties:
        hint:
          type: string
          description: Подсказка по просроченному платежу
        days:
          type: integer
          description: Количество дней просрочки
        fineDebt:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма пени
    PaymentDetails:
      type: object
      description: Детали платежа
      required:
        - date
        - baseAmount
        - interestAmount
      properties:
        date:
          type: string
          format: date
          description: Дата платежа
        baseAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма финансирования (за 1 месяц)
        interestAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма наценки (заполняется в зависимости от наличия просрочки)
        overdue:
          $ref: '#/components/schemas/Overdue'
    ScheduleItem:
      type: object
      description: График погашения
      required:
        - paymentAmount
        - date
        - status
        - details
      properties:
        paymentAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма платежа (Сумма финансирования (baseAmount) + Наценка (interestAmount))
        date:
          type: string
          format: date
          description: Дата платежа
        status:
          type: string
          description: Статус платежа PLAN - К оплате END - Погашен OVERDUE - Просрочен
          enum:
            - PLAN
            - END
            - OVERDUE
        details:
          $ref: '#/components/schemas/PaymentDetails'
    LoanDetails:
      type: object
      description: Информация по кредиту
      required:
        - hasOverdue
        - productType
        - paidInfo
        - nextPayment
        - account
        - details
        - schedule
      properties:
        hasOverdue:
          type: boolean
        productType:
          type: string
          description: Тип продукта LOAN - Кредит REFINANCE - Рефинансирование
          enum:
            - LOAN
            - REFINANCE
        reason:
          $ref: '#/components/schemas/Reason'
        paidInfo:
          $ref: '#/components/schemas/PaidInfo'
        nextPayment:
          $ref: '#/components/schemas/NextPayment'
        account:
          $ref: '#/components/schemas/AccountInfo'
        details:
          $ref: '#/components/schemas/LoanDetailsObject'
        schedule:
          type: array
          description: График погашения
          items:
            $ref: '#/components/schemas/ScheduleItem'
    GetLoansDetailsResponse:
      type: object
      required:
        - loan
      properties:
        loan:
          $ref: '#/components/schemas/LoanDetails'
    AccountArrest:
      type: object
      required:
        - blocking
      properties:
        blocking:
          type: boolean
          description: Признак полной или частичной блокировки, накладывает ограничения на определенную сумму - сумму ареста
    ClientAccountResponse:
      type: object
      required:
        - accountID
        - type
        - status
        - currency
        - openDate
        - iban
        - arrest
      properties:
        accountID:
          type: string
          description: Идентификатор счёта
        documentNumber:
          type: string
          description: Номер договора
        type:
          type: string
          enum:
            - CURR
            - BUFB
            - BUCO
            - TU
            - LOAN
            - OTHERS
          description: Тип счёта
        status:
          type: string
          enum:
            - ACTIVE
            - BLOCKED
            - CLOSED
            - ARRESTED
            - MISTAKEN
            - ARCHIVED
            - REOPENED
          description: Статус счёта
        currency:
          type: string
          enum:
            - KZT
            - RUB
            - EUR
            - USD
            - CNY
          description: Валюта счёта
        iban:
          type: string
          description: Номер счета iban
        openDate:
          type: string
          format: date
          description: Дата открытия счёта
        closeDate:
          type: string
          format: date
          description: Дата закрытия счёта
        balance:
          type: number
          format: double
          description: Баланс (остаток счёта)
        balanceNatval:
          type: number
          format: double
          description: Баланс счета в нац. валюте (KZT)
        planSum:
          type: number
          format: double
          description: Плановые суммы
        availableBalance:
          type: number
          format: double
          description: Доступный баланс (balance-plansum-partiallyDebtAmount)
        arrest:
          $ref: '#/components/schemas/AccountArrest'
    TransactionsOperationType:
      type: string
      enum:
        - CREDIT
        - DEBIT
      description: Тип операции
    TransactionsTransactionStatus:
      type: string
      enum:
        - INITIALIZED
        - IN_PROGRESS
        - COMPLETED
        - REJECTED
      description: Статус транзакции
    TransactionType:
      type: string
      enum:
        - OTHER
        - PAYMENT_BY_ACCOUNT
        - PAYMENT_MOBILE
        - PAYMENT_TERMINAL
      description: Внутренний тип перевода
    Transaction:
      type: object
      required:
        - transactionID
        - accountNumber
        - amount
        - currency
        - direction
        - status
        - transactionDate
        - transactionType
      properties:
        transactionID:
          type: string
          description: Уникальный идентификатор транзакции
        accountNumber:
          type: string
          description: Номер cчёта
        amount:
          type: number
          format: double
          description: Сумма транзакции
          example: 99.96
        currency:
          type: string
          description: Валюта транзакции
        direction:
          $ref: '#/components/schemas/TransactionsOperationType'
          description: Тип операции (кредит/дебет)
        status:
          $ref: '#/components/schemas/TransactionsTransactionStatus'
          description: Статус транзакции
        transactionDate:
          type: string
          format: date-time
          description: Дата операции
        transactionType:
          $ref: '#/components/schemas/TransactionType'
          description: Тип транзакции
        category:
          type: string
          description: Категория операции
    TransactionsResponse:
      type: object
      required:
        - transactions
        - totalCount
        - limit
        - offset
      properties:
        transactions:
          type: array
          items:
            $ref: '#/components/schemas/Transaction'
          description: Список транзакций
        totalCount:
          type: integer
          format: int64
          description: Общее количество транзакций
        limit:
          type: integer
          format: int64
          description: Лимит количества возвращаемых транзакций
        offset:
          type: integer
          format: int64
          description: Смещение для пагинации
        startDate:
          type: string
          format: date-time
          description: Начальная дата для фильтрации транзакций
    DepositProduct:
      type: object
      required:
        - name
        - code
        - beginDate
        - endDate
        - term
        - agreementCode
        - state
      properties:
        name:
          type: string
          description: Название продукта
          example: Вакала
        code:
          type: string
          description: Код продукта
          example: FL.МС
        beginDate:
          type: string
          format: date
          description: Дата открытия договора
          example: '2024-01-15T00:00:00Z'
        endDate:
          type: string
          format: date
          description: Дата окончания действия договора
          example: '2025-01-15T00:00:00Z'
        term:
          type: integer
          format: int32
          description: Срок депозита в месяцах
          example: 12
        agreementCode:
          type: string
          description: Номер депозитного договора
          example: FL.00-7
        state:
          type: string
          description: Статус депозита
          example: Актуален
    DepositBalance:
      type: object
      required:
        - depositAmount
        - depositBalance
        - currency
      properties:
        depositAmount:
          type: number
          format: double
          description: Первоначальная сумма депозита
          example: 1000000
        depositBalance:
          type: number
          format: double
          description: Актуальный баланс в валюте депозитного счета
          example: 1500000.5
        currency:
          type: string
          description: Валюта депозитного счета
          example: KZT
    DepositInterest:
      type: object
      required:
        - rate
        - effectiveRate
        - payedAmount
        - nearestPayDate
      properties:
        rate:
          type: string
          description: Доходность в процентах
          example: '15.1'
        effectiveRate:
          type: string
          description: Эффективная ставка доходности в процентах
          example: '14.3'
        payedAmount:
          type: number
          format: double
          description: Общая сумма выплаченной доходности
          example: 33000
        nearestPayDate:
          type: string
          format: date
          description: Следующая дата выплаты процентов
          example: '2021-04-01T00:00:00Z'
    DepositDetails:
      type: object
      required:
        - isReplenishable
        - isReplenishableDays
        - isWithdrawal
        - minBal
        - payoutMethod
      properties:
        isReplenishable:
          type: boolean
          description: Доступно ли пополнение
          example: true
        isReplenishableDays:
          type: integer
          format: int32
          description: Сколько дней еще доступно пополнение
          example: 21
        isWithdrawal:
          type: boolean
          description: Доступно ли снятие
          example: false
        minBal:
          type: number
          format: double
          description: Минимальный баланс
          example: 1000
        payoutMethod:
          type: string
          description: Способ выплаты
          example: CARD
    GetDepositDetailResp:
      type: object
      required:
        - product
        - balance
        - interest
        - details
      properties:
        product:
          $ref: '#/components/schemas/DepositProduct'
        balance:
          $ref: '#/components/schemas/DepositBalance'
        interest:
          $ref: '#/components/schemas/DepositInterest'
        details:
          $ref: '#/components/schemas/DepositDetails'
  requestBodies:
    LogoutUserRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - phone
            properties:
              phone:
                type: string
                description: Номер телефона в формате +7XXXXXXXXXX
                example: '+***********'
                pattern: ^\+7[0-9]{10}$
