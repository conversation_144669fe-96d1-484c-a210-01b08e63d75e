openapi: 3.0.1

servers:
  - description: Dev server
    url: https://crmgw-dev.zaman.redmadrobot.com/api/v1
  - description: Stage server
    url: https://crmgw-stage.zaman.redmadrobot.com/api/v1

info:
  title: Zaman Crmgw API
  version: dev-0916-08335e49
  description: |
    API для бэкэнда CRM

    Спецификация в формате [swagger](../swagger) или [redoc](../redoc).

    [Changelog](../history/changelog_main.html)

    [Release Changelog](../history/changelog_feature.html)
  termsOfService: https://zaman/terms-of-service
  x-logo:
    url: ../static/logo.png
tags:
  - name: "Системные"
  - name: "Профиль"
  - name: "Авторизация"
  - name: "Кредиты"
  - name: "Клиентские счета"
  - name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"
  - name: "Депозиты"

paths:
  /health:
    $ref: "health.gen.yaml#/health"

  /users:
    $ref: "users.yaml#/users"
  /users/logout:
    $ref: "users.yaml#/users-logout"

  /loans:
    $ref: "loans.yaml#/loans-details"

  /accounts:
    $ref: "accounts.yaml#/client-account"

  /transactions:
    $ref: "transactions.yaml#/account-transactions"

  /deposits:
    $ref: "deposits.yaml#/deposit-details"


components:
  securitySchemes:
    BearerTokenAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: API Key для доступа к CRM API

  responses:
    RespEmpty:
      description: Операция выполнена успешно
      content:
        application/json:
          schema:
            type: object
            properties: { }
  parameters:
   UserIDQueryParam:
      name: userID
      in: query
      description: Идентификатор пользователя (uuid)
      required: true
      schema:
        type: string
        pattern: "[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"
        maxLength: 36
   AccountIDPathParam:
      name: accountID
      in: query
      description: Идентификатор запрашиваемого счета (uuid)
      required: true
      schema:
        type: string
        pattern: "[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"
        maxLength: 36
  schemas:
    APIError:
      type: object
      description: Ошибка для CRM
      properties:
        code:
          type: integer
          description: Код ошибки
        message:
          type: string
          description: Текст ошибки
    ValidationError:
      type: object
      description: Ошибка валидации данных в теле запроса
      properties:
        error:
          type: string
          description: Код ошибки
          example: "1.0"
        fields:
          type: object
          description: Объект с описанием ошибок валидации полей
          additionalProperties:
            type: string
          example:
            iin: "value must be a string"
      required:
        - error

    Money:
      type: object
      description: Сумма
      required:
        - value
        - currencyCode
      properties:
        value:
          type: string
          description: Сумма
        currencyCode:
          $ref: "#/components/schemas/CurrencyCode"

    CurrencyCode:
      type: string
      description: Код валюты
      enum:
        - KZT
        - RUB
        - EUR
        - USD
        - CNY
