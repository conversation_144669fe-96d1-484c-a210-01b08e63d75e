openapi: 3.0.1
info:
  title: Zaman Collection API
  version: dev-0916-08335e49
  description: |
    API для бэкэнда Collection

    Любой ответ сервера может содержать коды 1.X (где Х - цифровой код ошибки) и X.0 (где X - цифровой ID сервиса).
    [Справочник ошибок](../errors)

    Хэдер accept-language используется для переключения языка ответа сервера.
    Подробнее см. [Accept-Language](#component-parameters-AcceptLanguage).

    Спецификация в формате [swagger](../swagger) или [redoc](../redoc).

    [Changelog](../history/changelog_main.html)

    [Release Changelog](../history/changelog_feature.html)
  termsOfService: https://zaman/terms-of-service
  x-logo:
    url: ../static/logo.png
servers:
  - description: Dev server
    url: https://collectiongw-dev.zaman.redmadrobot.com/api/v1
  - description: Stage server
    url: https://collectiongw-stage.zaman.redmadrobot.com/api/v1
tags:
  - name: Системные
  - name: Документы
paths:
  /health:
    get:
      tags:
        - Системные
      summary: Проверка на работоспособность
      description: Проверка всех модулей системы на работоспособность
      operationId: health
      security:
        - BearerTokenAuth: []
      responses:
        '200':
          description: Информация по запрошенному статусу микросервисов
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Health'
  /collection/client-file-income:
    post:
      tags:
        - Документы
      summary: Получение документа - ПКБ Доходы
      description: |
        Получение документа - ПКБ Доходы
      operationId: GetClientFileIncome
      security:
        - BearerTokenAuth:
            - not_identified
            - active
            - offline_access
            - default-roles-bank
            - uma_authorization
      requestBody:
        $ref: '#/components/requestBodies/ClientFileIncomeReq'
      responses:
        '200':
          description: |
            Документ с ссылкой получен успешно
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetDocumentResp'
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /collection/client-credit-reports:
    post:
      tags:
        - Документы
      summary: Получение документа - ПКБ Кредитный отчет
      description: |
        Получение документа - ПКБ Кредитный отчет
      operationId: GetClientCreditReports
      security:
        - BearerTokenAuth:
            - not_identified
            - active
            - offline_access
            - default-roles-bank
            - uma_authorization
      requestBody:
        $ref: '#/components/requestBodies/ClientCreditReportsReq'
      responses:
        '200':
          description: |
            Документ с ссылкой получен успешно
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientCreditReportsResp'
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
components:
  securitySchemes:
    BearerTokenAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    APIError:
      type: object
      description: Общий формат ошибки API. Возвращает цифровой код
      properties:
        error:
          type: string
        fields:
          type: object
          description: Объект с описанием деталей ошибок
          additionalProperties:
            type: string
      required:
        - error
      example:
        error: X.0
    Health:
      required:
        - users
        - otp
        - documents
        - notifications
        - keycloakProxy
        - kgdBridge
        - btsBridge
        - smsBridge
        - loans
        - colvirBridge
        - payments
        - cardsAccounts
        - dictionary
        - pkbBridge
        - amlBridge
        - liveness
        - taskManager
        - juicyscoreBridge
        - jiraBridge
        - fileGuard
        - scoring
        - seonBridge
        - sprBridge
        - altScoreBridge
        - qazpostBridge
        - deposits
        - bsasBridge
        - apBridge
        - processingBridge
        - collection
        - paymentsSme
        - referral
        - antifraud
        - crm
        - tokenize
        - balanceUpdater
        - kaspiBridge
        - bitrixBridge
        - foreignActivity
      type: object
      properties:
        users:
          type: boolean
          description: Статус сервиса users
        otp:
          type: boolean
          description: Статус сервиса otp
        documents:
          type: boolean
          description: Статус сервиса documents
        notifications:
          type: boolean
          description: Статус сервиса notifications
        keycloakProxy:
          type: boolean
          description: Статус сервиса keycloakProxy
        kgdBridge:
          type: boolean
          description: Статус сервиса kgdBridge
        btsBridge:
          type: boolean
          description: Статус сервиса btsBridge
        smsBridge:
          type: boolean
          description: Статус сервиса smsBridge
        loans:
          type: boolean
          description: Статус сервиса loans
        colvirBridge:
          type: boolean
          description: Статус сервиса colvirBridge
        payments:
          type: boolean
          description: Статус сервиса payments
        cardsAccounts:
          type: boolean
          description: Статус сервиса cardsAccounts
        dictionary:
          type: boolean
          description: Статус сервиса dictionary
        pkbBridge:
          type: boolean
          description: Статус сервиса pkbBridge
        amlBridge:
          type: boolean
          description: Статус сервиса amlBridge
        liveness:
          type: boolean
          description: Статус сервиса liveness
        taskManager:
          type: boolean
          description: Статус сервиса taskManager
        juicyscoreBridge:
          type: boolean
          description: Статус сервиса juicyscoreBridge
        jiraBridge:
          type: boolean
          description: Статус сервиса jiraBridge
        fileGuard:
          type: boolean
          description: Статус сервиса fileGuard
        scoring:
          type: boolean
          description: Статус сервиса scoring
        seonBridge:
          type: boolean
          description: Статус сервиса seonBridge
        sprBridge:
          type: boolean
          description: Статус сервиса sprBridge
        altScoreBridge:
          type: boolean
          description: Статус сервиса altScoreBridge
        qazpostBridge:
          type: boolean
          description: Статус сервиса qazpostBridge
        deposits:
          type: boolean
          description: Статус сервиса deposits
        bsasBridge:
          type: boolean
          description: Статус сервиса bsasBridge
        apBridge:
          type: boolean
          description: Статус сервиса apBridge
        processingBridge:
          type: boolean
          description: Статус сервиса processingBridge
        collection:
          type: boolean
          description: Статус сервиса collection
        paymentsSme:
          type: boolean
          description: Статус сервиса paymentsSme
        referral:
          type: boolean
          description: Статус сервиса referral
        antifraud:
          type: boolean
          description: Статус сервиса antifraud
        crm:
          type: boolean
          description: Статус сервиса crm
        tokenize:
          type: boolean
          description: Статус сервиса tokenize
        balanceUpdater:
          type: boolean
          description: Статус сервиса balanceUpdater
        kaspiBridge:
          type: boolean
          description: Статус сервиса kaspiBridge
        bitrixBridge:
          type: boolean
          description: Статус сервиса bitrixBridge
        foreignActivity:
          type: boolean
          description: Статус сервиса foreignActivity
    ClientFileIncomeReq:
      type: object
      required:
        - iin
      properties:
        iin:
          type: string
          description: ИИН клиента
          pattern: ^[0-9]+$
          example: '123456789012'
        maxDateReport:
          type: string
          format: date-time
          description: |
            Документ формируется на основе данных за период
      example:
        iin: '************'
        maxDateReport: '2025-07-31T00:00:00Z'
    Document:
      required:
        - ID
        - type
        - fileLink
      type: object
      properties:
        ID:
          type: string
          description: Идентификатор документа (UUID)
        type:
          type: string
          description: Тип документа
        fileLink:
          type: string
          description: Ссылка S3
    GetDocumentResp:
      required:
        - document
      type: object
      properties:
        document:
          $ref: '#/components/schemas/Document'
    ClientCreditReportsReq:
      type: object
      required:
        - iin
      properties:
        iin:
          type: string
          description: ИИН клиента
          pattern: ^[0-9]+$
          example: '123456789012'
        reportType:
          type: integer
          description: Тип отчета
          example: 6
        maxDateReport:
          type: string
          format: date-time
          description: |
            Документ формируется на основе данных за период
      example:
        iin: '************'
        maxDateReport: '2025-07-31T00:00:00Z'
    ClientCreditReportsResp:
      required:
        - fileLink
      type: object
      properties:
        fileLink:
          type: string
          description: Ссылка S3
  requestBodies:
    ClientFileIncomeReq:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ClientFileIncomeReq'
    ClientCreditReportsReq:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ClientCreditReportsReq'
