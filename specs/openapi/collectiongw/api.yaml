openapi: 3.0.1

servers:
  - description: Dev server
    url: https://collectiongw-dev.zaman.redmadrobot.com/api/v1
  - description: Stage server
    url: https://collectiongw-stage.zaman.redmadrobot.com/api/v1

info:
  title: Zaman Collection API
  version: dev-0916-08335e49
  description: |
    API для бэкэнда Collection

    Любой ответ сервера может содержать коды 1.X (где Х - цифровой код ошибки) и X.0 (где X - цифровой ID сервиса).
    [Справочник ошибок](../errors)

    Хэдер accept-language используется для переключения языка ответа сервера.
    Подробнее см. [Accept-Language](#component-parameters-AcceptLanguage).

    Спецификация в формате [swagger](../swagger) или [redoc](../redoc).

    [Changelog](../history/changelog_main.html)

    [Release Changelog](../history/changelog_feature.html)
  termsOfService: https://zaman/terms-of-service
  x-logo:
    url: ../static/logo.png

tags:
  - name: "Системные"
  - name: "Документы"

paths:
  /health:
    $ref: "health.gen.yaml#/health"

  /collection/client-file-income:
    $ref: "collection.yaml#/client-file-income"
  /collection/client-credit-reports:
    $ref: "collection.yaml#/client-credit-reports"

components:
  securitySchemes:
    BearerTokenAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    APIError:
      type: object
      description: Общий формат ошибки API. Возвращает цифровой код
      properties:
        error:
          type: string
        fields:
          type: object
          description: Объект с описанием деталей ошибок
          additionalProperties:
            type: string
      required:
        - error
      example:
        error: "X.0"

