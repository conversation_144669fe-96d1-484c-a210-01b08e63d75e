client:
  get:
    tags:
      - Платежи
    summary: Получение данных по клиенту
    description: Получение данных по клиенту
    operationId: smePaymentsClient
    security:
      - BearerTokenAuth: [ "active" ]
    responses:
      200:
        description: "Данные по клиенту успешно получены"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ClientResponse"
      400:
        description: "Ошибка получения данных по клиенту"
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"
      403:
        description: "Некорректный контекст запроса (origin is not sme)"
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"
      503:
        description: "Сервис недоступен"
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"

confirm-payment-sme:
  post:
    tags:
      - Платежи
    summary: Подтверждение платежа в бюджет (SME)
    description: Подтверждение платежа в бюджет (SME)
    operationId: confirmPaymentSme
    security:
      - BearerTokenAuth: [ "active" ]
    parameters:
      - $ref: "#/components/parameters/PaymentsTransactionID"
    responses:
      200:
        description: "Платеж подтвержден успешно"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ConfirmPaymentSmeResponse"
      400:
        description: |
          Ошибки подтверждения платежа
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"

sme-payments-create-otp:
  post:
    tags:
      - Платежи
    summary: Создание OTP для подписи транзакции
    description: Создание OTP для подписи транзакции
    operationId: smePaymentsCreateOtp
    security:
      - BearerTokenAuth: [ "active" ]
    requestBody:
      $ref: "#/components/requestBodies/SmePaymentsCreateOtpRequestBody"
    responses:
      200:
        description: OTP успешно сгенерирован
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SmePaymentsCreateOtpResponse"
      400:
        description: Ошибка создания OTP
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"

sme-payments-otp-resend:
  post:
    tags:
      - Платежи
    summary: Перегенерация OTP кода
    description: Перегенерация OTP кода
    operationId: smePaymentsOtpResend
    security:
      - BearerTokenAuth: [ "active" ]
    parameters:
      - $ref: "#/components/parameters/SmePaymentsOtpAttemptID"
    responses:
      200:
        description: OTP успешно перегенерирован
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SmePaymentsCreateOtpResponse"
      400:
        description: Ошибка перегенерации OTP
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"

sme-payments-otp-validate:
  post:
    tags:
      - Платежи
    summary: Валидация OTP кода
    description: Валидация OTP кода
    operationId: smePaymentsOtpValidate
    security:
      - BearerTokenAuth: [ "active" ]
    parameters:
      - $ref: "#/components/parameters/SmePaymentsOtpAttemptID"
    requestBody:
      $ref: "#/components/requestBodies/SmePaymentsOtpValidateRequestBody"
    responses:
      200:
        description: OTP успешно валидирован
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SmePaymentsOtpValidateResponse"
      400:
        description: Ошибка валидации OTP
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"

get-kbe-kod-list:
  get:
    tags:
      - Платежи
    summary: Получить справочник Кбе/Код (SME)
    description: Справочник Кбе/Код (SME)
    operationId: getKbeKodList
    security:
      - BearerTokenAuth: [ "active" ]
    parameters:
      - $ref: "#/components/parameters/KbeKodFilterCodes"
      - $ref: "#/components/parameters/KbeKodFilterResidency"
    responses:
      200:
        description: "Справочник Кбе/Код (SME)"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetKbeKodListResp"
      400:
        description: Ошибка валидации
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/ValidationError"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"
      500:
        description: Внутренняя ошибка сервера
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"

get-knp-list:
  get:
    tags:
      - Платежи
    summary: Получить справочник КНП (SME)
    description: Справочник КНП (SME)
    operationId: getKnpList
    security:
      - BearerTokenAuth: [ "active" ]
    parameters:
      - $ref: "#/components/parameters/KnpFilterCodes"
      - $ref: "#/components/parameters/KnpFilterGroups"
    responses:
      200:
        description: "Справочник КНП (SME)"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetKnpListResp"
      400:
        description: Ошибка валидации
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/ValidationError"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"
      500:
        description: Внутренняя ошибка сервера
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"

get-bank-list:
  get:
    tags:
      - Платежи
    summary: Получить справочник банков (SME)
    description: Справочник банков (SME)
    operationId: getBankList
    security:
      - BearerTokenAuth: [ "active" ]
    parameters:
      - $ref: "#/components/parameters/BankFilterBics"
    responses:
      200:
        description: "Справочник банков (SME)"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetBankListResp"
      400:
        description: Ошибка валидации
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/ValidationError"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"
      500:
        description: Внутренняя ошибка сервера
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"

get-tax-authority-list:
  get:
    tags:
      - Платежи
    summary: Получить справочник УГД (SME)
    description: Справочник УГД (SME)
    operationId: getTaxAuthorityList
    security:
      - BearerTokenAuth: [ "active" ]
    parameters:
      - $ref: "#/components/parameters/TaxAuthorityFilterRegions"
    responses:
      200:
        description: "Справочник УГД (SME)"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetTaxAuthorityListResp"
      400:
        description: Ошибка валидации
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/ValidationError"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"
      500:
        description: Внутренняя ошибка сервера
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"

get-kbk-list:
  get:
    tags:
      - Платежи
    summary: Получить справочник КБК (SME)
    description: Справочник КБК (SME)
    operationId: getKbkList
    security:
      - BearerTokenAuth: [ "active" ]
    parameters:
      - $ref: "#/components/parameters/KbkFilterCodes"
    responses:
      200:
        description: "Справочник КБК (SME)"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetKbkListResp"
      400:
        description: Ошибка валидации
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/ValidationError"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"
      500:
        description: Внутренняя ошибка сервера
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"

sme-payments-worktime:
  get:
    tags:
      - Платежи
    summary: Проверка операционного дня банка
    description: Проверка операционного дня банка
    operationId: smePaymentsWorktime
    security:
      - BearerTokenAuth: [ "active" ]
    responses:
      200:
        description: "Платеж подтвержден успешно"
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SmePaymentsWorktimeResponse"
      400:
        description: Ошибка получения операционного дня банка
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"
      500:
        description: Внутренняя ошибка сервера
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"

create-payment:
  post:
    tags:
      - Платежи
    summary: Создание платежа (SME)
    description: Создание различных типов платежей (бюджетных и небюджетных)
    operationId: createPayment
    security:
      - BearerTokenAuth: [ "active" ]
    requestBody:
      $ref: "#/components/requestBodies/CreatePaymentRequest"
    responses:
      200:
        description: Платеж успешно создан
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreatePaymentResponse"
      400:
        description: Ошибка создания платежа
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/ValidationError"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"
      500:
        description: Внутренняя ошибка сервера
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"

sme-payments-get-payment-order:
  get:
    tags:
      - Платежи
    summary: Получение ссылки на печатную форму платежного поручения
    description: Получение ссылки на печатную форму платежного поручения
    operationId: smePaymentsGetPaymentOrder
    security:
      - BearerTokenAuth: [ "active" ]
    parameters:
      - $ref: "#/components/parameters/PaymentsTransactionID"
    responses:
      200:
        description: Ссылка на печатную форму платежного поручения
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SmePaymentsGetPaymentOrderResponse"
      404:
        description: Транзакция не найдена
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"
      500:
        description: Что-то пошло не так
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"

sme-payments-get-payment-order-by-tr-number:
  get:
    tags:
      - Платежи
    summary: Получение ссылки на печатную форму платежного поручения
    description: Получение ссылки на печатную форму платежного поручения
    operationId: smePaymentsGetPaymentOrderByTrNumber
    security:
      - BearerTokenAuth: [ "active" ]
    parameters:
      - $ref: "#/components/parameters/PaymentsTransactionNumber"
    responses:
      200:
        description: Ссылка на печатную форму платежного поручения
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SmePaymentsGetPaymentOrderByTrNumberResponse"
      404:
        description: Транзакция не найдена
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"
      500:
        description: Что-то пошло не так
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"

sme-payments-get-employee-list:
  get:
    tags:
      - Платежи
    summary: Получение списка сотрудников ИП
    description: Получение списка сотрудников ИП
    operationId: smePaymentsGetEmployeeList
    security:
      - BearerTokenAuth: [ "active" ]
    responses:
      200:
        description: Список сотрудников ИП
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SmePaymentsGetEmployeeListResponse"
      404:
        description: Сотрудник не найден
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"
      500:
        description: Что-то пошло не так
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"

sme-payments-create-employee:
  post:
    tags:
      - Платежи
    summary: Добавление нового сотрудника в список сотрудников ИП
    description: Добавление нового сотрудника в список сотрудников ИП
    operationId: smePaymentsCreateEmployee
    security:
      - BearerTokenAuth: [ "active" ]
    requestBody:
      $ref: "#/components/requestBodies/SmePaymentsAddEmployeeRequestBody"
    responses:
      200:
        description: Данные сотрудника ИП
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EmployeeInfo"
      403:
        description: Сотрудник с таким ИИН уже существует
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"
      500:
        description: Что-то пошло не так
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"

sme-payments-update-employee-by-id:
  put:
    tags:
      - Платежи
    summary: Редактирование сотрудника из списка сотрудников ИП
    description: Редактирование сотрудника из списка сотрудников ИП
    operationId: smePaymentsUpdateEmployeeByID
    security:
      - BearerTokenAuth: [ "active" ]
    parameters:
      - $ref: "#/components/parameters/EmployeeID"
    requestBody:
      $ref: "#/components/requestBodies/SmePaymentsUpdateEmployeeRequestBody"
    responses:
      200:
        description: Список сотрудников ИП
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EmployeeInfo"
      404:
        description: Сотрудник не найден
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"
      500:
        description: Что-то пошло не так
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"

sme-payments-delete-employee-by-id:
  delete:
    tags:
      - Платежи
    summary: Удаление сотрудника из списка сотрудников ИП
    description: Удаление сотрудника из списка сотрудников ИП
    operationId: smePaymentsDeleteEmployeeByID
    security:
      - BearerTokenAuth: [ "active" ]
    parameters:
      - $ref: "#/components/parameters/EmployeeID"
    responses:
      200:
        description: Список сотрудников ИП
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SmePaymentsDeleteEmployeeListResponse"
      404:
        description: Сотрудник не найден
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"
      500:
        description: Что-то пошло не так
        content:
          application/json:
            schema:
              $ref: "api.yaml#/components/schemas/APIError"

components:
  requestBodies:
    SmePaymentsCreateOtpRequestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              deviceInfo:
                $ref: "#/components/schemas/SmePaymentsDeviceInfo"
            required:
              - deviceInfo

    SmePaymentsOtpValidateRequestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: string
                description: Введенный код
              transactionID:
                type: string
                description: Идентификатор транзакции, которую подтверждаем
            required:
              - code
              - transactionID

    CreatePaymentRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - paymentCode
              - idempotencyKey
              - paymentData
            properties:
              paymentCode:
                type: string
                description: Код платежа
              idempotencyKey:
                type: string
                description: Ключ идемпотентности
              paymentData:
                $ref: "#/components/schemas/PaymentData"

    SmePaymentsAddEmployeeRequestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            description: Информация о сотруднике для добавления в ИП
            required:
              - name
              - lastName
              - iin
              - employerIinBin
              - birthday
              - country
              - displayOrder
            properties:
              name:
                type: string
                description: Имя сотрудника
                example: "Айдар"
              middleName:
                type: string
                description: Отчество сотрудника
                example: "Асылбекович"
              lastName:
                type: string
                description: Фамилия сотрудника
                example: "Нурманов"
              iin:
                type: string
                description: ИИН сотрудника
                pattern: '^[0-9]{12}$'
                example: "************"
              employerIinBin:
                type: string
                description: ИИН/БИН организации
                pattern: '^[0-9]{12}$'
                example: "************"
              birthday:
                type: string
                format: date
                description: Дата рождения сотрудника
                example: "1990-01-15"
              country:
                type: string
                description: Страна
                example: "KZ"
              displayOrder:
                type: integer
                description: Порядок отображения в списке
    SmePaymentsUpdateEmployeeRequestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            description: Информация о сотруднике для добавления в ИП
            required:
              - name
              - lastName
              - iin
              - employerIinBin
              - birthday
              - country
              - displayOrder
            properties:
              name:
                type: string
                description: Имя сотрудника
                example: "Айдар"
              middleName:
                type: string
                description: Отчество сотрудника
                example: "Асылбекович"
              lastName:
                type: string
                description: Фамилия сотрудника
                example: "Нурманов"
              iin:
                type: string
                description: ИИН сотрудника
                pattern: '^[0-9]{12}$'
                example: "************"
              employerIinBin:
                type: string
                description: ИИН/БИН организации
                pattern: '^[0-9]{12}$'
                example: "************"
              birthday:
                type: string
                format: date
                description: Дата рождения сотрудника
                example: "1990-01-15"
              country:
                type: string
                description: Страна
                example: "KZ"
              displayOrder:
                type: integer
                description: Порядок отображения в списке

  parameters:
    EmployeeID:
      name: employeeID
      in: path
      description: Идентификатор сотрудника ИП
      schema:
        type: string
        format: uuid
      required: true

    PaymentsTransactionID:
      name: transactionID
      in: path
      description: Идентификатор транзакции
      schema:
        type: string
        format: uuid
      required: true

    PaymentsTransactionNumber:
      name: transactionNumber
      in: path
      description: Номер транзакции
      schema:
        type: string
      required: true

    SmePaymentsOtpAttemptID:
      name: attemptID
      in: path
      description: Идентификатор попытки подписи
      schema:
        type: string
        format: uuid
      required: true

    KbeKodFilterCodes:
      name: filter.codes
      in: query
      description: Список кодов для фильтрации
      required: false
      schema:
        type: array
        items:
          type: string
        example: ["011,012", "010"]

    KbeKodFilterResidency:
      name: filter.residency
      in: query
      description: Признак резидента для фильтрации
      required: false
      schema:
        type: boolean
        example: true

    KnpFilterCodes:
      name: filter.codes
      in: query
      description: Список кодов КНП для фильтрации
      required: false
      schema:
        type: array
        items:
          type: string
        example: ["011,012", "010"]

    KnpFilterGroups:
      name: filter.groups
      in: query
      description: Список кодов групп КНП для фильтрации
      required: false
      schema:
        type: array
        items:
          type: string
        example: ["011,012", "010"]

    BankFilterBics:
      name: filter.bics
      in: query
      description: Список БИК кодов банков для фильтрации
      required: false
      schema:
        type: array
        items:
          type: string
        example: ["KZKOKZKX", "TSESKZKA"]

    TaxAuthorityFilterRegions:
      name: filter.regions
      in: query
      description: Список кодов регионов УГД для фильтрации
      required: false
      schema:
        type: array
        items:
          type: string
        example: ["60","62","38"]

    KbkFilterCodes:
      name: filter.codes
      in: query
      description: Список кодов КБК для фильтрации
      required: false
      schema:
        type: array
        items:
          type: string
        example: ["105274", "105276", "105276"]

  schemas:
    ClientResponse:
      type: object
      properties:
        iin:
          type: string
          description: ИИН клиента
        name:
          type: string
          description: Имя клиента
        surname:
          type: string
          description: Фамилия клиента
        patronymic:
          type: string
          description: Отчество клиента
        birthdate:
          type: string
          description: Дата рождения клиента
        enterpriseName:
          type: string
          description: Название ИП клиента
        enterpriseAddressKATOCode:
          type: string
          description: Код КАТО адреса регистрации ИП
        enterpriseAddressKATOId:
          type: string
          description: Номер КАТО адреса регистрации ИП
      required:
        - iin
        - name
        - surname
        - birthdate

    ConfirmPaymentSmeResponse:
      type: object
      properties:
        status:
          type: string
          description: Статус платежа
        reasonCode:
          type: string
          description: Код состояния платежа
        reason:
          type: string
          description: Описание причины состояния платежа
        otpNeeded:
          type: boolean
          description: Необходимость подтвердить платёж OTP
      required:
        - status

    SmePaymentsCreateOtpResponse:
      type: object
      properties:
        attemptId:
          type: string
          description: Идентификатор попытки подписи
        codeTtl:
          type: string
          description: Время жизни OTP
        codeChecksLeft:
          type: string
          description: Количество попыток ввода кода
        attemptsLeft:
          type: string
          description: Количество попыток перегенерации кода
        attemptsTimeout:
          type: string
          description: Время между генерациями кода
        newAttemptDelay:
          type: string
          description: Задержка перед следующей перегенерацией
        error:
          type: string
          description: Описание ошибки

    SmePaymentsDeviceInfo:
      type: object
      properties:
        appVersion:
          type: string
          description: Версия МП
        deviceModel:
          type: string
          description: Модель устройства
        installationId:
          type: string
          description: Идентификатор установки
        systemType:
          type: string
          description: ОС устройства
        systemVersion:
          type: string
          description: Версия ОС

    SmePaymentsOtpValidateResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Успешность проверки кода
        error:
          type: string
          description: Описание ошибки

    GetKbeKodListResp:
      type: object
      properties:
        codes:
          type: array
          description: Список Кбе/КОд кодов
          items:
            $ref: "#/components/schemas/KbeKodItem"

    KbeKodItem:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/KbeKodCode'

    KbeKodCode:
      type: object
      properties:
        value:
          type: string
          description: Код Кбе/КОд
        name:
          type: string
          description: Наименование Кбе/КОд на языке локализации
        residency:
          type: boolean
          description: Признак резидента

    GetKnpListResp:
      type: object
      properties:
        codes:
          type: array
          description: Список КНП
          items:
            $ref: '#/components/schemas/KnpItem'

    KnpItem:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/KnpCode'

    KnpCode:
      type: object
      properties:
        value:
          type: string
          description: Код КНП
        name:
          type: string
          description: Наименование КНП на языке локализации

    GetBankListResp:
      type: object
      properties:
        banks:
          type: array
          description: Список банков
          items:
            $ref: '#/components/schemas/BankItem'

    BankItem:
      type: object
      properties:
        bank:
          $ref: '#/components/schemas/Bank'

    Bank:
      type: object
      properties:
        code:
          type: string
          description: Код банка
        bic:
          type: string
          description: БИК банка
        name:
          type: string
          description: Наименование банка на языке локализации

    GetTaxAuthorityListResp:
      type: object
      properties:
        ugds:
          type: array
          description: Список УГД
          items:
            $ref: '#/components/schemas/TaxAuthorityItem'

    TaxAuthorityItem:
      type: object
      properties:
        ugd:
          $ref: '#/components/schemas/TaxAuthorityUgd'

    TaxAuthorityUgd:
      type: object
      properties:
        code:
          type: string
          description: Код УГД
        bin:
          type: string
          description: БИН УГД
        regionCode:
          type: string
          description: Код региона
        regionName:
          type: string
          description: Название региона
        name:
          type: string
          description: Наименование УГД на языке локализации

    GetKbkListResp:
      type: object
      properties:
        codes:
          type: array
          description: Список КБК
          items:
            $ref: '#/components/schemas/KbkItem'

    KbkItem:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/KbkCode'

    KbkCode:
      type: object
      properties:
        value:
          type: string
          description: Код КБК
        name:
          type: string
          description: Наименование КБК на языке локализации

    SmePaymentsWorktimeResponse:
      type: object
      properties:
        date:
          type: string
          description: Текущая дата в системах банка (GMT+5 Astana)
        isDateOperational:
          type: boolean
          description: Открыт опердень или нет
        nextOperationalDate:
          type: string
          description: Следующий рабочий день
        isEndOfWorkTime:
          type: boolean
          description: Проверка на опер день после рабочего времени
      required:
        - date
        - isDateOperational
        - isEndOfWorkTime

    PaymentData:
      type: object
      description: Данные для платежа ОПВ (Обязательные пенсионные взносы)
      required:
        - paymentPeriod
        - purposeCode
        - purposeDetails
        - amount
        - payerAccount
        - employees
        - signatoryA
      properties:
        paymentPeriod:
          type: string
          description: Период платежа (MMYYYY)
          pattern: '^[0-9]{6}$'
          example: "122024"
        purposeCode:
          type: string
          description: Код назначения платежа
          example: "001"
        purposeDetails:
          type: string
          description: Детали назначения платежа
          example: "Обязательные пенсионные взносы за декабрь 2024"
        amount:
          type: string
          description: Общая сумма платежа
          pattern: '^\d+\.\d{2}$'
          example: "15000.00"
        payerAccount:
          type: string
          description: Счет плательщика
          pattern: '^KZ[A-Z0-9]{18}$'
          example: "********************"
        employees:
          type: array
          description: Список сотрудников
          items:
            $ref: "#/components/schemas/EmployeeItem"
        signatoryA:
            type: string
            description: Подпись ответственного лица (ФИО)
            example: "Токаев Касым-Жомарт Кемельевич"
        beneficiaryBINIIN:
            type: string
            description: БИН/ИИН получателя платежа
            pattern: '^[0-9]{12}$'
            example: "************"
        beneficiaryName:
            type: string
            description: Наименование получателя платежа
            example: "АО 'Единый накопительный пенсионный фонд'"
        realBeneficiaryName:
            type: string
            description: ФИО фактического получателя
            example: "АО 'ЕНПФ'"
        realBeneficiaryBINIIN:
            type: string
            description: ИИН фактического получателя
            pattern: '^[0-9]{12}$'
            example: "************"
        realBeneficiaryCountry:
            type: string
            description: Страна резидентства фактического получателя
            example: "KZ"
        kbk:
          type: string
          description: Код Бюджетной Классификации
          example: "101202"

    EmployeeItem:
      type: object
      description: Информация о сотруднике для платежа ОПВ
      required:
        - name
        - lastName
        - iin
        - birthday
        - country
        - amount
        - valuePeriod
      properties:
        name:
          type: string
          description: Имя сотрудника
          example: "Айдар"
        middleName:
          type: string
          description: Отчество сотрудника
          example: "Асылбекович"
        lastName:
          type: string
          description: Фамилия сотрудника
          example: "Нурманов"
        iin:
          type: string
          description: ИИН сотрудника
          pattern: '^[0-9]{12}$'
          example: "************"
        birthday:
          type: string
          format: date
          description: Дата рождения сотрудника
          example: "1990-01-15"
        country:
          type: string
          description: Страна
          example: "KZ"
        amount:
          type: string
          description: Сумма взноса для данного сотрудника
          pattern: '^\d+\.\d{2}$'
          example: "5000.00"
        valuePeriod:
          type: string
          description: Период взноса (YYYYMM)
          pattern: '^[0-9]{6}$'
          example: "202412"

    CreatePaymentResponse:
      type: object
      required:
        - status
        - otpNeeded
      properties:
        transactionID:
          type: string
          format: uuid
          description: Идентификатор созданной транзакции (опциональное поле - может отсутствовать при ошибках валидации)
          example: "550e8400-e29b-41d4-a716-************"
        status:
          type: string
          description: Статус создания платежа
          enum:
            - INITIALIZED # Платёж пришёл на бэкэнд, прошёл валидацию и был принят в обработку. Может придти вместе с атрибутом otpNeeded = true. Это значит что требуется авторизовать платёж по OTP.
            - REJECTED    # Платёж пришел на бэкэнд, но не прошёл валидацию. Например, не хватает средств на счёте или неактивный статус счёта.
            - IN_PROGRESS # Платёж проведён в платформе Платежи и Переводы, но ещё не обработан в Колвир, но будет обработан позже.
            - COMPLETED   # Платёж проведён везде по системам банка, деньги переведены. Это финальный статус.
          example: "INITIALIZED"
        reasonCode:
            type: string
            description: Код состояния платежа
            enum:
              - "1005" # Validation error
              - "1013" # Worktime exceeded
              - "1041" # Insufficient funds
              - "1042" # No active account
              - "1040" # Unable to process payment
            example: "1005"
        reason:
            type: string
            description: Описание причины состояния платежа
            enum:
              - Validation error          # Платёж пришёл на бэкэнд, но что-то пошло не так на шаге валидации. Надо разбираться, потому что правила должны быть едины для фронта и бэка
              - Worktime exceeded         # Платёж требует инициализации и проведения одним оперднём. Этот опердень кончился, поэтому нужно провести данный платёж в другой день.
              - Insufficient funds        # Платёж создан, но при попытке провести его в Колвир столкнулись с нехваткой средств для его проведения.
              - No active account         # Платёж создан, но при попытке провести его в Колвир столкнулись с неактивным статусом счёта по которому должны были провести дебет.
              - Unable to process payment # Платёж создан, но при попытке провести его в Колвир столкнулись с ошибками: например с блокировкой счёта от Антифрода
            example: "Worktime exceeded"
        otpNeeded:
          type: boolean
          description: Необходимость подтвердить платёж OTP
          example: false

    SmePaymentsGetPaymentOrderResponse:
      type: object
      required:
        - title
        - link
        - version
      properties:
        title:
          type: string
          description: Название документа
        link:
          type: string
          description: Ссылка на файл документа
        version:
          type: string
          description: Версия документа

    SmePaymentsGetPaymentOrderByTrNumberResponse:
      type: object
      required:
        - id
        - title
        - link
        - version
        - transactionStatus
      properties:
        id:
          type: string
          description: Идентификатор транзакции
        title:
          type: string
          description: Название документа
        link:
          type: string
          description: Ссылка на файл документа
        version:
          type: integer
          description: Версия документа
        transactionStatus:
          type: string
          description: Статус транзакции
          enum:
            - INITIALIZED # Платёж пришёл на бэкэнд, прошёл валидацию и был принят в обработку. Может придти вместе с атрибутом otpNeeded = true. Это значит что требуется авторизовать платёж по OTP.
            - REJECTED    # Платёж пришел на бэкэнд, но не прошёл валидацию. Например, не хватает средств на счёте или неактивный статус счёта.
            - IN_PROGRESS # Платёж проведён в платформе Платежи и Переводы, но ещё не обработан в Колвир, но будет обработан позже.
            - COMPLETED   # Платёж проведён везде по системам банка, деньги переведены. Это финальный статус.

    SmePaymentsGetEmployeeListResponse:
      type: object
      required:
        - employees
      properties:
        employees:
          type: array
          description: Список сотрудников
          items:
            $ref: "#/components/schemas/EmployeeInfo"

    SmePaymentsDeleteEmployeeListResponse:
      type: object
      required:
        - employees
      properties:
        employees:
          type: array
          description: Список сотрудников
          items:
            $ref: "#/components/schemas/EmployeeInfo"

    EmployeeInfo:
      type: object
      description: Информация о сотруднике ИП
      required:
        - id
        - name
        - lastName
        - iin
        - birthday
        - country
        - displayOrder
      properties:
        id:
          type: string
          description: Идентификатор записи о сотруднике
        name:
          type: string
          description: Имя сотрудника
          example: "Айдар"
        middleName:
          type: string
          description: Отчество сотрудника
          example: "Асылбекович"
        lastName:
          type: string
          description: Фамилия сотрудника
          example: "Нурманов"
        iin:
          type: string
          description: ИИН сотрудника
          pattern: '^[0-9]{12}$'
          example: "************"
        employerIinBin:
          type: string
          description: ИИН/БИН организации
          pattern: '^[0-9]{12}$'
          example: "************"
        birthday:
          type: string
          format: date
          description: Дата рождения сотрудника
          example: "1990-01-15"
        country:
          type: string
          description: Страна
          example: "KZ"
        displayOrder:
          type: integer
          description: Порядок отображения в списке

