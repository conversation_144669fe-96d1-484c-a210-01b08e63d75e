openapi: 3.0.1

servers:
  - description: Dev server
    url: https://sme-dev.zaman.redmadrobot.com/api/v1
  - description: Stage server
    url: https://sme.zaman.redmadrobot.com/api/v1

info:
  title: Zaman Sme API
  version: dev-0916-08335e49
  description: |
    API для бэкэнда мобильного приложения  Zaman Sme

    Любой ответ сервера может содержать коды 1.X (где Х - цифровой код ошибки) и X.0 (где X - цифровой ID сервиса).
    [Справочник ошибок](../errors)

    Хэдер accept-language используется для переключения языка ответа сервера.
    Подробнее см. [Accept-Language](#component-parameters-AcceptLanguage).


    Спецификация в формате [swagger](../swagger) или [redoc](../redoc).


    [Changelog](../history/changelog_main.html)

    [Release Changelog](../history/changelog_feature.html)
  termsOfService: https://zaman/terms-of-service
  x-logo:
    url: ../static/logo.png

tags:
  - name: "Системные"
  - name: "Авторизация"
  - name: "Документы"
  - name: "Кредиты"
  - name: "Общее"
  - name: "Справочники"
  - name: "Платежи"
  - name: "Карты и счета"
  - name: "Таск менеджер"
  - name: "File Guard"
  - name: "Профиль"
  - name: "ВЭД"

paths:
  /health:
    $ref: "health.gen.yaml#/health"

  /auth/login:
    $ref: "auth.yaml#/login"
  /auth/logout:
    $ref: "auth.yaml#/logout"
  /auth/confirm:
    $ref: "auth.yaml#/confirm"
  /auth/refresh:
    $ref: "auth.yaml#/refresh"
  /auth/bts-data:
    $ref: "auth.yaml#/get-bts-data"
  /auth/identify:
    $ref: "auth.yaml#/identify"
  /auth/document-for-sign:
    $ref: "docs.yaml#/documents-for-sign"

  /accounts/open/currency-check:
    $ref: "accounts.yaml#/currency-check"

  /documents/public:
    $ref: "docs.yaml#/docs-request-public"
  /documents/{docID}:
    $ref: "docs.yaml#/docs-by-docID"
  /documents/{docID}/sign:
    $ref: "docs.yaml#/docs-by-docID-sign"
  /documents/{docID}/sign-confirm:
    $ref: "docs.yaml#/docs-by-docID-sign-confirm"
  /documents/sign:
    $ref: "docs.yaml#/docs-sign"
  /documents/sign-confirm:
    $ref: "docs.yaml#/docs-sign-confirm"
  /documents/batch-sign-confirm:
    $ref: "docs.yaml#/docs-batch-sign-confirm"
  /documents/bsas-create-order:
    $ref: "docs.yaml#/docs-request-bsas-create-order"
  /documents/bsas-get-order:
    $ref: "docs.yaml#/docs-request-bsas-get-order"
  /documents/bsas-get-system:
    $ref: "docs.yaml#/docs-request-bsas-get-system"
  /documents/bsas-get-otc:
    $ref: "docs.yaml#/docs-request-bsas-get-otc"
  /documents/bsas-sell:
    $ref: "docs.yaml#/docs-request-bsas-sell"

  /accounts/documents:
    $ref: "docs.yaml#/accounts-documents"
  /accounts/open/client-verification:
    $ref: "accounts.yaml#/accounts-open-client-verification"
  /accounts/open/documents-for-sign:
    $ref: "accounts.yaml#/accounts-open-documents-for-sign"

  /otp/retry:
    $ref: "otp.yaml#/retry"


  /loans/onboarding-texts:
    $ref: "loans.yaml#/loans-onboarding-texts"
  /loans/calc-data:
    $ref: "loans.yaml#/loans-calc-data"
  /loans/calculation:
    $ref: "loans.yaml#/loans-calculate"
  /loans/survey:
    $ref: "loans.yaml#/survey"
  /loans/client-application/check:
    $ref: "loans.yaml#/loans-client-application-check"
  /loans/application:
    $ref: "loans.yaml#/loans-application"
  /loans/bank-statement:
    $ref: "loans.yaml#/bank-statement"
  /loans/{applicationID}/bank-statement:
    $ref: "loans.yaml#/bank-statement-v2"
  /loans/{applicationID}/internal-checks-result:
    $ref: "loans.yaml#/internal-checks-result"
  /loans/education-types:
    $ref: "loans.yaml#/education-types"
  /loans/employment-types:
    $ref: "loans.yaml#/employment-types"
  /loans/relation-types:
    $ref: "loans.yaml#/relation-types"
  /loans/{applicationID}/cancel:
    $ref: "loans.yaml#/cancel-application"
  /loans/documents:
    $ref: "loans.yaml#/documents-for-application"
  /loans/{applicationID}/status:
    $ref: "loans.yaml#/approved-loan-app-status"
  /loans/{applicationID}/publish:
    $ref: "loans.yaml#/publish-loan-app-data"
  /loans/{applicationID}/scoring-result:
    $ref: "loans.yaml#/get-scoring-result"
  /loans/{applicationID}/bts-data:
    $ref: "loans.yaml#/get-bts-data"
  /loans/{applicationID}/identify:
    $ref: "loans.yaml#/post-identify-bts-data-sme"
  /loans/{applicationID}/eds:
    $ref: "loans.yaml#/post-eds-bts-data"
  /loans/{applicationID}/details:
    $ref: "loans.yaml#/loans-details"
  /loans/{applicationID}/early-repay:
    $ref: "loans.yaml#/post-early-repay"
  /loans/{applicationID}/{docID}/sign-confirm:
    $ref: "loans.yaml#/doc-sign-confirm"
  /loans/change-disbursement-mode:
    $ref: "loans.yaml#/change-disbursement-control"

  /dict/doc/by-filters:
    $ref: "dictionary.yaml#/dict-doc-get-list-by-filters-public"
  /dictionaries/locations:
    $ref: "dictionary.yaml#/locations"

  /payments/transactions:
    $ref: "payments.yaml#/transactions"
  /payments/transactions/{transactionID}:
    $ref: "payments.yaml#/get-transaction-by-id"
  /payments/transactions/{transactionID}/receipt:
    $ref: "payments.yaml#/get-transaction-receipt"
  /payments/history:
    $ref: "payments.yaml#/history"
  /payments/check-account-iin:
    $ref: "payments.yaml#/check-account-iin"
  /payments/create-payment-by-account:
    $ref: "payments.yaml#/create-payment-by-account"
  /payments/confirm-payment-by-account:
    $ref: "payments.yaml#/confirm-payment-by-account"

  /smepayments/client:
    $ref: "smepayments.yaml#/client"
  /smepayments/transactions:
    $ref: "smepayments.yaml#/create-payment"
  /smepayments/transactions/{transactionID}/confirm:
    $ref: "smepayments.yaml#/confirm-payment-sme"
  /smepayments/transactions/{transactionID}/payment-order:
    $ref: "smepayments.yaml#/sme-payments-get-payment-order"
  /smepayments/transactions/{transactionNumber}/payment-order-by-tr-number:
    $ref: "smepayments.yaml#/sme-payments-get-payment-order-by-tr-number"
  /smepayments/dictionaries/code:
    $ref: "smepayments.yaml#/get-kbe-kod-list"
  /smepayments/dictionaries/knp:
    $ref: "smepayments.yaml#/get-knp-list"
  /smepayments/dictionaries/banks:
    $ref: "smepayments.yaml#/get-bank-list"
  /smepayments/dictionaries/tax-authorities:
    $ref: "smepayments.yaml#/get-tax-authority-list"
  /smepayments/dictionaries/kbk:
    $ref: "smepayments.yaml#/get-kbk-list"
  /smepayments/otp:
    $ref: "smepayments.yaml#/sme-payments-create-otp"
  /smepayments/otp/{attemptID}/resend:
    $ref: "smepayments.yaml#/sme-payments-otp-resend"
  /smepayments/otp/{attemptID}/validate:
    $ref: "smepayments.yaml#/sme-payments-otp-validate"
  /smepayments/worktime:
    $ref: "smepayments.yaml#/sme-payments-worktime"
  /smepayments/employee-list:
    $ref: "smepayments.yaml#/sme-payments-get-employee-list"
  /smepayments/employee-list/new:
    $ref: "smepayments.yaml#/sme-payments-create-employee"
  /smepayments/employee-list/{employeeID}/edit:
    $ref: "smepayments.yaml#/sme-payments-update-employee-by-id"
  /smepayments/employee-list/{employeeID}/delete:
    $ref: "smepayments.yaml#/sme-payments-delete-employee-by-id"

  /user/cards:
    $ref: "cards.yaml#/user-cards"
  /user/accounts/{accountID}:
    $ref: "cards.yaml#/user-account"
  /user/loans:
    $ref: "loans.yaml#/loans"
  /user/locale:
    $ref: "users.yaml#/user-locale"
  /user/profile:
    $ref: "users.yaml#/profile-delete"
  /user/accounts:
    $ref: "cards.yaml#/user-accounts-sme"

  /tasks:
    $ref: "taskmanager.yaml#/tasks"
  /tasks/{taskID}:
    $ref: "taskmanager.yaml#/task-details"

  /fileguard:
    $ref: "file_guard.yaml#/unsecure-file-upload"

  /foreign-activity/create-convertation:
    $ref: "foreign-activity.yaml#/create-convertation"
  /foreign-activity/conversion-sum:
    $ref: "foreign-activity.yaml#/conversion-sum"

components:
  securitySchemes:
    BearerTokenAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  # Reusable responses, such as 401 Unauthorized or 400 Bad Request
  responses:
    RespEmpty:
      description: Операция выполнена успешно
      content:
        application/json:
          schema:
            type: object
            properties: { }

  schemas:
    Money:
      type: object
      description: Сумма
      required:
        - value
        - currencyCode
      properties:
        value:
          type: string
          description: Сумма
        currencyCode:
          $ref: "#/components/schemas/CurrencyCode"

    CurrencyCode:
      type: string
      description: Код валюты
      enum:
        - KZT
        - RUB
        - EUR
        - USD
        - CNY

    PhoneNumber:
      type: string
      description: Номер телефона
      pattern: '^\+?[1-9]\d{10,10}$'
      example: '+78889991100'
    Email:
      type: string
      #      TODO: разобраться почему с этим форматом возникает ошибка. can't serialize to JSON: json: error calling MarshalJSON for type *types.Email: email: failed to pass regex validation
      #      format: email
      pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
      description: Электронная почта
    APIError:
      type: object
      description: Общий формат ошибки API. Возвращает цифровой код
      properties:
        error:
          type: string
        fields:
          type: object
          description: Объект с описанием деталей ошибок
          additionalProperties:
            type: string
      required:
        - error
      example:
        error: "X.0"
    ValidationError:
      type: object
      description: Ошибка валидации данных в теле запроса
      properties:
        error:
          type: string
          description: Код ошибки
          example: "1.0"
        fields:
          type: object
          description: Объект с описанием ошибок валидации полей
          additionalProperties:
            type: string
          example:
            iin: "value must be a string"
      required:
        - error
        - fields
  parameters:
    AcceptLanguage:
      name: Accept-Language
      in: header
      description: Язык запроса
      required: false
      schema:
        type: string
        enum:
          - kk
          - en
          - ru
        example: ru
        default: kk
    UserAgent:
      name: User-Agent
      in: header
      description: Юзер агент
      required: true
      schema:
        type: string
