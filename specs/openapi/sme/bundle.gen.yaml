openapi: 3.0.1
info:
  title: Zaman Sme API
  version: dev-0916-08335e49
  description: |
    API для бэкэнда мобильного приложения  Zaman Sme

    Любой ответ сервера может содержать коды 1.X (где Х - цифровой код ошибки) и X.0 (где X - цифровой ID сервиса).
    [Справочник ошибок](../errors)

    Хэдер accept-language используется для переключения языка ответа сервера.
    Подробнее см. [Accept-Language](#component-parameters-AcceptLanguage).


    Спецификация в формате [swagger](../swagger) или [redoc](../redoc).


    [Changelog](../history/changelog_main.html)

    [Release Changelog](../history/changelog_feature.html)
  termsOfService: https://zaman/terms-of-service
  x-logo:
    url: ../static/logo.png
servers:
  - description: Dev server
    url: https://sme-dev.zaman.redmadrobot.com/api/v1
  - description: Stage server
    url: https://sme.zaman.redmadrobot.com/api/v1
tags:
  - name: Системные
  - name: Авторизация
  - name: Документы
  - name: Кредиты
  - name: Общее
  - name: Справочники
  - name: Платежи
  - name: Карты и счета
  - name: Таск менеджер
  - name: File Guard
  - name: Профиль
  - name: ВЭД
paths:
  /health:
    get:
      tags:
        - Системные
      summary: Проверка на работоспособность
      description: Проверка всех модулей системы на работоспособность
      operationId: health
      security:
        - BearerTokenAuth: []
      responses:
        '200':
          description: Информация по запрошенному статусу микросервисов
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Health'
  /auth/login:
    post:
      tags:
        - Авторизация
      summary: Авторизация пользователя по телефону
      description: Проверка существования пользователя по телефону, доп проверки, авторизация
      operationId: authLogin
      security: []
      requestBody:
        $ref: '#/components/requestBodies/LogInBody'
      responses:
        '200':
          description: Пользователь найден, отправлен код подтверждения для входа
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OtpResponse'
        '400':
          description: |
            <br>Ошибка запроса авторизации
            <br><b>1.0 ValidationError</b> - Ошибка валидации данных в теле запроса
            <br><b>2.1 UserBlocked</b> - Пользователь заблокирован
            <br><b>2.2 UserPhoneIinMismatch</b> - Не совпадение номера ИИН и номера телефона пользователя
            <br><b>3.3 OtpMaxCodeAttemptsExceeded</b> - Превышен лимит количества попыток за период
            <br><b>3.4 OtpNewAttemptTimeNotExceeded</b> - Необходимо подождать, прежде чем запрашивать новый код
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/APIError'
                  - $ref: '#/components/schemas/ValidationError'
  /auth/logout:
    post:
      tags:
        - Авторизация
      summary: Выход из системы
      description: Выход пользователя из системы, удаление токенов. Используется в том числе при сценарии "забыл пин"
      operationId: authLogout
      security: []
      requestBody:
        $ref: '#/components/requestBodies/LogoutBody'
      responses:
        '200':
          $ref: '#/components/responses/RespEmpty'
        '400':
          description: |
            <br>Ошибка запроса выхода пользователя
            <br><b>1.0 ValidationError</b> - Ошибка валидации данных в теле запроса
  /auth/confirm:
    post:
      tags:
        - Авторизация
      operationId: authConfirm
      summary: Подтверждение авторизации
      description: Ввод кода из СМС/... , создание токенов доступа
      security: []
      requestBody:
        $ref: '#/components/requestBodies/OtpBody'
      responses:
        '200':
          description: Номер подтвержден, сгенерированы токены доступа
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthConfirmResponse'
        '400':
          description: |
            Ошибка авторизации
            <br><b>1.0 ValidationError</b> - Ошибка в теле запроса
            <br><b>2.15 UserDebtsCheckError</b> - Проверка задолженностей пользователя не пройдена - в ответе json объект "fields": {"debts_amount": "some num"}
            <br><b>2.16 TaxPayerInactive</b> - Проверка действующего налогоплательщика не пройдена
            <br><b>2.21 AmlCheckFailed</b> - Проверка AML не пройдена, пользователь заблокирован
            <br><b>2.22 JurSearchNotEligible</b> - Юр лицо не подходит по окэд коду
            <br><b>2.23 NotPermitedActivityType</b> - Не подходит по типу деятельности
            <br><b>2.29 AntiFraudCheckFailed</b> - пользователь не прошел проверку антифрода.
            <br><b>3.1 InvalidOTP</b> - Ошибка ввода кода ОТП
            <br><b>3.2 OtpMaxCodeChecksExceeded</b> - Превышено количество попыток проверки кода
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/APIError'
                  - $ref: '#/components/schemas/ValidationError'
  /auth/refresh:
    post:
      tags:
        - Авторизация
      description: Обновление токенов пользователя
      summary: Обновление токенов пользователя
      operationId: authRefresh
      security: []
      requestBody:
        $ref: '#/components/requestBodies/AuthRefreshBody'
      responses:
        '200':
          description: Токены доступа обновлены
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthRefreshResponse'
        '400':
          description: |
            Ошибка авторизации
            <br><b>1.0 ValidationError</b> - Ошибка в теле запроса
            <br><b>1.1 ValidationError</b> - Требуется аутентификация
            <br><b>2.1 UserBlocked</b> - Пользователь заблокирован
            <br><b>2.3 TokenRefreshForbidden</b> - Обновление токена запрещено
  /auth/bts-data:
    post:
      tags:
        - Авторизация
      summary: Метод для получения ссылки liveness
      description: |
        Метод для получения ссылки liveness для прохождения флоу авторизации
      operationId: getBtsDataForAuth
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
      responses:
        '200':
          description: Данные со сгенерированной ссылкой и ссылкой для редиректа
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BtsDataForAuthResp'
        '400':
          description: |
            Ошибка авторизации
            <br><b>1.0 ValidationError</b> - Ошибка в теле запроса
            <br><b>1.1 ValidationError</b> - Требуется аутентификация
  /auth/identify:
    post:
      tags:
        - Авторизация
      summary: Идентификация пользователя
      description: Идентификация пользователя по коду BTS
      operationId: authIdentify
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      requestBody:
        $ref: '#/components/requestBodies/IdentifyBody'
      responses:
        '200':
          description: Пользователь идентифицирован
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthRefreshResponse'
        '400':
          description: |
            Ошибка идентификации
            <br><b>1.0 ValidationError</b> - Ошибка в теле запроса
            <br><b>1.3 Forbidden</b> - Доступ запрещен
            <br><b>2.10 CitizenshipCheckFailed</b> - Проверка гражданства не пройдена
            <br><b>2.11 AgeCheckFailed</b> - Проверка возраста не пройдена
            <br><b>2.12 UserVerifyFailed</b> - Верификация пользователя не пройдена
            <br><b>2.13 PersonalDocumentInvalidStatus</b> - Документ удостоверяющий личность не действителен
            <br><b>2.14 ValidPersonalDocumentNotFound</b> - Необходимый документ удостоверяющий личность не найден
            <br><b>2.21 AmlCheckFailed</b> - Проверка AML не пройдена, пользователь заблокирован
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/APIError'
                  - $ref: '#/components/schemas/ValidationError'
  /auth/document-for-sign:
    post:
      tags:
        - Авторизация
      summary: Метод для генерации и получения документа для подписи
      description: |
        Метод для генерации и получения документа для подписи. Определяет тип документа в зависимости от статуса пользователя.
      operationId: documentForSign
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      responses:
        '200':
          description: Информация по запрошенному документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentsForSignResponse'
        '400':
          description: |
            Ошибка генерации документа
            <br><b>4.1 TemplateNotFound</b> - Шаблон не найден
            <br><b>4.10 FailedToGenerateFromTemplate</b> - Не удалось сгенерировать документ из шаблона
            <br><b>4.11 FailedToUploadFile</b> - Не удалось загрузить файл
            <br><b>2.19 ClientCardDuplicate</b> - Найден дубль карточки клиента
            <br><b>2.20 AccountArrested</b> - Ошибка ограничения на действующем счете
  /accounts/open/currency-check:
    get:
      tags:
        - Карты и счета
      summary: Доступные к открытию счета пользователя в валютах
      description: Доступные к открытию счета пользователя в валютах
      operationId: currencyCheck
      security:
        - BearerTokenAuth:
            - active
      responses:
        '200':
          description: Счета пользователя
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CurrencyCheckResponse'
  /documents/public:
    post:
      tags:
        - Документы
      summary: Метод для получения документов, доступных для просмотра всем
      description: |
        Метод для получения документов, доступных для просмотра всем
        personalDataAgreementSMEIP,financingTermsInfoSMEIP - документ персональных данных
      operationId: requestDocumentPublic
      security: []
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
        - name: docType
          in: query
          description: Тип запрашиваемого документа
          required: true
          schema:
            $ref: '#/components/schemas/PublicDocumentType'
      responses:
        '200':
          description: Информация по запрошенному документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
        '400':
          description: |
            Ошибка инициации процесса подписи
            <br><b>4.1 InvalidTemplateType</b> - Неверный тип шаблона
            <br><b>4.8 UserInfoMismatch</b> - Информация о пользователя не корректна
            <br><b>4.10 FailedToGenerateFromTemplate</b> - Не удалось сгенерировать документ из шаблона
  /documents/{docID}:
    get:
      tags:
        - Документы
      summary: Просмотр сгенерированного ранее документа
      description: |
        Просмотр сгенерированного ранее из шаблона документа.
        Метод приватный, т.к. требует авторизации.
        Для просмотра публичных документов используется метод /documents/public
      operationId: getDocumentByID
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
        - $ref: '#/components/parameters/DocumentIDPathParam'
      responses:
        '200':
          description: Информация по запрошенному документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
        '400':
          description: |
            Ошибка инициации процесса подписи
            <br><b>4.3 InvalidDocID</b> - Неверный ID документа
            <br><b>4.8 UserInfoMismatch</b> - Информация о пользователя не корректна
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /documents/{docID}/sign:
    post:
      deprecated: true
      tags:
        - Документы
      summary: Подпись сгенерированного ранее документа
      description: Подпись персонализированного документа из шаблона
      operationId: signDocumentByID
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
        - $ref: '#/components/parameters/DocumentIDPathParam'
      responses:
        '200':
          description: Код подтверждения для подписи документа отправлен
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OtpResponse'
        '400':
          description: |
            Ошибка инициации процесса подписи
            <br><b>1.0 ValidationError</b> - Ошибка валидации данных в параметрах запроса
            <br><b>3.3 MaxCodeAttemptsExceeded</b> - Превышен лимит количества попыток за период
            <br><b>3.4 NewAttemptTimeNotExceeded</b> - Необходимо подождать, прежде чем запрашивать новый код
            <br><b>3.5 AttemptNotFoundInCache</b> - Попытка не найдена в кэше
            <br><b>4.3 InvalidDocID</b> - Неверный ID документа
            <br><b>4.9 DocumentAlreadySigned</b> - Документ уже подписан
            <br><b>4.12 FailedToSignDocument</b> - Не удалось подписать документ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /documents/{docID}/sign-confirm:
    post:
      deprecated: true
      tags:
        - Документы
      summary: Подтверждение подписи документа через ОТП
      description: Подтверждение подписи персонализированного документа из шаблона через ОТП
      operationId: confirmSignDocumentByID
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
        - $ref: '#/components/parameters/DocumentIDPathParam'
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      requestBody:
        $ref: '#/components/requestBodies/OtpBody'
      responses:
        '200':
          description: Документ подписан, обновленная информация по документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
        '400':
          description: |
            Ошибка подписи документа
            <br><b>3.1 InvalidCode</b> - Неверный код
            <br><b>3.2 MaxCodeChecksExceeded</b> - Превышено количество попыток проверки кода
            <br><b>4.9 DocumentAlreadySigned</b> - Документ уже подписан
            <br><b>4.12 FailedToSignDocument</b> - Не удалось подписать документ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /documents/sign:
    post:
      tags:
        - Документы
      summary: Отправка ОТП для подтверждения подписания
      description: Метод используется для отправки ОТП для подтверждения подписания переданных документов
      operationId: signDocumentByIDs
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
      requestBody:
        $ref: '#/components/requestBodies/SignDocumentsByIDReqBody'
      responses:
        '200':
          description: Код подтверждения для подписи документа отправлен
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OtpResponse'
        '400':
          description: |
            Ошибка подписи документа
            <br><b>3.2 MaxCodeChecksExceeded</b> - Превышено количество попыток проверки кода
            <br><b>3.3 MaxCodeAttemptsExceeded</b> - Превышен лимит количества попыток за период
            <br><b>4.3 InvalidDocID</b> - Неверный ID документа
            <br><b>4.9 DocumentAlreadySigned</b> - Документ уже подписан
            <br><b>4.12 FailedToSignDocument</b> - Не удалось подписать документ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /documents/sign-confirm:
    post:
      tags:
        - Документы
      summary: Принятие кода ОТП по подписанию переданных документов
      description: Принятие кода ОТП по подписанию переданных документов и при успешной проверке ОТП сохраняет документ в БД
      operationId: confirmSignDocuments
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      requestBody:
        $ref: '#/components/requestBodies/ConfirmSignDocumentsReqBody'
      responses:
        '200':
          description: Документ подписан, обновленная информация по документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfirmSignDocumentsResponse'
        '400':
          description: |
            Ошибка подписи документа
            <br><b>3.1 InvalidCode</b> - Неверный код
            <br><b>3.2 MaxCodeChecksExceeded</b> - Превышено количество попыток проверки кода
            <br><b>3.5 AttemptNotFound</b> - Сценарий проверки ОТП кода не найден или истёк
            <br><b>4.9 DocumentAlreadySigned</b> - Документ уже подписан
            <br><b>4.12 FailedToSignDocument</b> - Не удалось подписать документ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /documents/batch-sign-confirm:
    post:
      deprecated: true
      tags:
        - Документы
      summary: Подтверждение подписи нескольких документов через ОТП
      description: Подтверждение подписи нескольких документов через ОТП
      operationId: confirmSignDocumentsBatch
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      requestBody:
        $ref: '#/components/requestBodies/BatchDocsOtpBody'
      responses:
        '200':
          description: Документы подписаны, обновленная информация по документам
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SignedDocumentsBatchResponse'
        '400':
          description: |
            Ошибка подписи документа
            <br><b>3.1 InvalidCode</b> - Неверный код
            <br><b>3.2 MaxCodeChecksExceeded</b> - Превышено количество попыток проверки кода
            <br><b>4.9 DocumentAlreadySigned</b> - Документ уже подписан
            <br><b>4.12 FailedToSignDocument</b> - Не удалось подписать документ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /documents/bsas-create-order:
    post:
      tags:
        - Документы
      summary: Метод ввода и обработки новых заказов в BSAS
      description: |
        Метод ввода и обработки новых заказов в BSAS (Это временная ручка для Максата Валиханова)
      operationId: createOrderBsas
      security: []
      requestBody:
        $ref: '#/components/requestBodies/CreateOrderBody'
      responses:
        '200':
          description: Информация по созданному заказу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateOrderResp'
        '400':
          description: |
            Описание ошибки будет возвращена в теле ответа
  /documents/bsas-get-order:
    post:
      tags:
        - Документы
      summary: Метод получения результата обработки заказа в BSAS
      description: |
        Метод получения результата обработки заказа в BSAS (Это временная ручка для Максата Валиханова)
      operationId: getOrderBsas
      security: []
      requestBody:
        $ref: '#/components/requestBodies/GetOrderBody'
      responses:
        '200':
          description: Информация по полученному заказу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetOrderResp'
        '400':
          description: |
            Описание ошибки будет возвращена в теле ответа
  /documents/bsas-get-system:
    post:
      tags:
        - Документы
      summary: Получение сведений о сопоставлении из BSAS
      description: |
        Метод по извлечению сведений о сопоставлении информации из системы API BSAS (Временная ручка)
      operationId: getSystemBsas
      security: []
      requestBody:
        $ref: '#/components/requestBodies/GetSystemBody'
      responses:
        '200':
          description: Информация по извлечению сведений о сопоставлении информации
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSystemResp'
        '400':
          description: |
            Описание ошибки будет возвращена в теле ответа
  /documents/bsas-get-otc:
    post:
      tags:
        - Документы
      summary: Метод получения сведений об ОТС информации из системы
      description: |
        Метод получения сведений об ОТС информации из системы (Это временная ручка для Максата Валиханова)
      operationId: getOtcBsas
      security: []
      requestBody:
        $ref: '#/components/requestBodies/GetOtcBody'
      responses:
        '200':
          description: Информация по полученному заказу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetOtcResp'
        '400':
          description: |
            Описание ошибки будет возвращена в теле ответа
  /documents/bsas-sell:
    post:
      tags:
        - Документы
      summary: Метод получения сведений о продажах из системы
      description: |
        Метод получения сведений о продажах из системы (Это временная ручка для Максата Валиханова)
      operationId: getSellBsas
      security: []
      requestBody:
        $ref: '#/components/requestBodies/SellBody'
      responses:
        '200':
          description: Информация по полученному заказу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SellResp'
        '400':
          description: |
            Описание ошибки будет возвращена в теле ответа
  /accounts/documents:
    post:
      tags:
        - Карты и счета
      summary: Метод для получения документов из счетов
      description: Метод для получения документов из счетов
      operationId: createAccountsDocument
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      parameters:
        - name: docType
          in: query
          description: Тип запрашиваемого документа
          required: true
          schema:
            $ref: '#/components/schemas/AccountDocumentType'
      requestBody:
        $ref: '#/components/requestBodies/AccountDocumentRequest'
      responses:
        '200':
          description: Информация по запрошенному документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountDocumentResponse'
        '400':
          description: |
            Ошибка инициации процесса подписи
            <br><b>11.1 TemplateNotFound</b> - Шаблон не найден
            <br><b>11.4 InvalidTemplateType</b> - Неверный тип шаблона
  /accounts/open/client-verification:
    post:
      tags:
        - Карты и счета
      summary: Проверка клиента при открытии доп.счета ИП
      description: Используется фронтом для получения данных о проверке клиента при открытии доп.счета ИП
      operationId: clientVerification
      security:
        - BearerTokenAuth:
            - active
      responses:
        '200':
          description: Результат проверки клиента
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientVerificationResponse'
        '400':
          description: |
            Ошибка при получении данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /accounts/open/documents-for-sign:
    post:
      tags:
        - Карты и счета
      operationId: documentsForSign
      summary: Получение документов на подписание при открытии счета
      description: Получение документов на подписание при открытии счета
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/DocumentsForSignRequest'
      responses:
        '200':
          description: Список документов на подписание при открытие доп. счета ИП
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentsForSignForAccountOpeningResponse'
        '400':
          description: |
            Ошибка при получении данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /otp/retry:
    post:
      tags:
        - Общее
      summary: Перезапрос кода подтверждения
      description: Перезапрос кода подтверждения
      operationId: otpRetry
      security:
        - BearerTokenAuth: []
      requestBody:
        $ref: '#/components/requestBodies/OtpBody'
      responses:
        '200':
          description: Код подтверждения успешно отправлен
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OtpResponse'
        '400':
          description: |
            Ошибка перезапроса кода подтверждения
            <br><b>1.0 ValidationError</b> - Ошибка валидации данных в теле запроса
            <br><b>3.3 MaxCodeAttemptsExceeded</b> - Превышен лимит количества попыток ОТП за период
            <br><b>3.4 NewAttemptTimeNotExceeded</b> - Необходимо подождать, прежде чем запрашивать новый код
            <br><b>3.5 NotFound</b> - Объект не найден
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/APIError'
                  - $ref: '#/components/schemas/ValidationError'
  /loans/onboarding-texts:
    get:
      tags:
        - Кредиты
      summary: Получение onboarding текстовок для кредита SME
      description: Получение onboarding текстовок для кредита SME
      operationId: getLoansOnboardingTexts
      security:
        - BearerTokenAuth: []
      responses:
        '200':
          description: Onboarding текста для кредита
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnboardingTextsResponse'
        '400':
          description: |
            Ошибка получения данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/calc-data:
    get:
      tags:
        - Кредиты
      summary: Получение данных для расчета кредита
      description: Предоставляет информацию о возможных целях кредита, доступных суммах и процентных ставках
      operationId: getLoansCalcData
      security:
        - BearerTokenAuth: []
      responses:
        '200':
          description: Данные из справочников для кредитного калькулятора
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoanCalcDataResponse'
        '400':
          description: Ошибка получения данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/calculation:
    post:
      tags:
        - Кредиты
      summary: Расчет параметров платежей для всех сроков кредитов
      description: Расчет параметров платежей для всех сроков кредитов
      operationId: calculateLoanTerms
      security:
        - BearerTokenAuth: []
      requestBody:
        $ref: '#/components/requestBodies/CalculationBody'
      responses:
        '200':
          description: Данные сумм к погашению по кредиту для всех сроков
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalculationResultResponse'
        '400':
          description: |
            Ошибка получения данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/survey:
    get:
      tags:
        - Кредиты
      summary: Получение анкеты пользователя
      description: Получение анкеты пользователя
      operationId: getLoanSurvey
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
      responses:
        '200':
          description: Успешное получение анкеты пользователя
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSurveyResult'
        '400':
          description: |
            Ошибка получения анкеты пользователя.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
    post:
      tags:
        - Кредиты
      summary: Сохранение анкеты пользователя.
      description: Сохранение анкеты пользователя.
      operationId: saveLoanSurvey
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
        - $ref: '#/components/parameters/UserAgent'
      requestBody:
        $ref: '#/components/requestBodies/SaveSurveyBody'
      responses:
        '200':
          description: Анкета пользователя успешно сохранена.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SaveSurveyResp'
        '400':
          description: |
            Ошибка получения анкеты пользователя.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/client-application/check:
    post:
      tags:
        - Кредиты
      summary: Проверка клиента на активные заявки на кредит
      description: Проверка клиента на активные заявки на кредит
      operationId: loansClientApplicationCheck
      security:
        - BearerTokenAuth:
            - active
      responses:
        '200':
          description: Список кредитов, соответствующих фильтрам
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoanApplicationCheckResponse'
        '400':
          description: |
            Ошибка получения данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/application:
    post:
      tags:
        - Кредиты
      summary: Создание кредитной заявки
      description: Используется FE для создания и передачи на BE данных о кредитной заявке
      operationId: createLoanApplication
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/LoansApplicationBody'
      responses:
        '200':
          description: Запрос успешно отработан, заявка создана
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoanApplicationResponse'
        '400':
          description: |
            Ошибка получения данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/bank-statement:
    get:
      tags:
        - Кредиты
      summary: Получение справочных значений банков для загрузки выписки
      description: Предоставляет информацию о банках
      operationId: getBankStatement
      security:
        - BearerTokenAuth:
            - active
      responses:
        '200':
          description: Успешный ответ с данными по банкам
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BankStatements'
        '400':
          description: Ошибка получения данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/bank-statement:
    get:
      tags:
        - Кредиты
      summary: Получение справочных значений банков для загрузки выписки
      description: Предоставляет информацию о банках
      operationId: getBankStatementV2
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/ApplicationIDPathParam'
      responses:
        '200':
          description: Успешный ответ с данными по банкам
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetBankStatementV2Response'
        '400':
          description: Ошибка получения данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/internal-checks-result:
    get:
      tags:
        - Кредиты
      summary: Получение  результата внутренних проверок кредитной заявки
      description: Получение результата внутренних проверок кредитной заявки
      operationId: loansGetInternalChecksResult
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/ApplicationIDPathParam'
      responses:
        '200':
          description: Результат внутренних проверок кредитной заявки
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetInternalChecksResultResp'
        '400':
          description: |
            Ошибка получения результата внутренних проверок кредитной заявки
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/education-types:
    get:
      tags:
        - Кредиты
      summary: Получение справочника по типам образования
      description: Получение справочника по типам образования
      operationId: loansGetEducationTypes
      security:
        - BearerTokenAuth: []
      responses:
        '200':
          description: Справочник по типам образования
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetEducationTypesResp'
        '400':
          description: |
            Ошибка получения справочника по типам образования
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/employment-types:
    get:
      tags:
        - Кредиты
      summary: Получение справочника по типам занятости
      description: Получение справочника по типам занятости
      operationId: loansGetEmploymentTypes
      security:
        - BearerTokenAuth: []
      responses:
        '200':
          description: Справочник по типам занятости
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetEmploymentTypesResp'
        '400':
          description: |
            Ошибка получения справочника по типам занятости
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/relation-types:
    get:
      tags:
        - Кредиты
      summary: Получение справочника по видам отношений к контактным лицам
      description: Получение справочника по видам отношений к контактным лицам
      operationId: loansGetRelationTypes
      security:
        - BearerTokenAuth: []
      responses:
        '200':
          description: Справочник по видам отношений к контактным лицам
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetRelationTypesResp'
        '400':
          description: |
            Ошибка получения справочника по видам отношений к контактным лицам
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/cancel:
    post:
      tags:
        - Кредиты
      summary: Отмена заявки на кредит
      description: Отмена заявки на кредит по идентификатору
      operationId: loansCancelLoanApplication
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/ApplicationIDPathParam'
      responses:
        '200':
          description: Заявка на кредит успешно отменена
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CancelLoanApplicationResp'
        '400':
          description: Ошибка отмены заявки на займ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/documents:
    post:
      tags:
        - Кредиты
      summary: Метод для генерации и получения документа для подписи
      description: |
        Метод для генерации и получения документа для кредитной заявки
      operationId: documentsForLoanApp
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
      requestBody:
        $ref: '#/components/requestBodies/LoanApplicationDocumentsBody'
      responses:
        '200':
          description: Информация по запрошенному документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoanApplicationDocumentsResp'
        '400':
          description: Ошибка генерации документа
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/status:
    get:
      tags:
        - Кредиты
      summary: Получение статуса подтвержденной заявки на кредит
      description: Получение статуса подтвержденной заявки на кредит
      operationId: loansGetApprovedLoanAppStatus
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/ApplicationIDPathParam'
      responses:
        '200':
          description: Статус заявки на кредит
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetApprovedLoanAppStatusResp'
        '400':
          description: |
            Ошибка получения статуса заявки на кредит
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/publish:
    post:
      tags:
        - Кредиты
      summary: Метод для публикации заявки с отправкой в СПР
      description: |
        Метод для публикации заявки вместе с файлами выписок с последующей отправкой в СПР
      operationId: publishLoanAppData
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/ApplicationIDPathParam'
      requestBody:
        $ref: '#/components/requestBodies/PublishLoanAppDataRequestBody'
      responses:
        '200':
          description: Данные со сгенерированной ссылкой и ссылкой для редиректа
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PublishLoanAppResp'
        '400':
          description: Ошибка генерации ссылки
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/scoring-result:
    get:
      tags:
        - Кредиты
      summary: Метод для получения условий одобренного предложения от СПР
      description: |
        Метод для получения условий одобренного предложения после ожидания ответа СПР
      operationId: getScoringResult
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - in: header
          name: Accept-Language
          description: Язык запроса
          required: true
          schema:
            type: string
            enum:
              - kk
              - en
              - ru
            example: ru
            default: kk
        - $ref: '#/components/parameters/ApplicationIDPathParam'
      responses:
        '200':
          description: Данные с условиями одобренного предложения после ожидания ответа СПР
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScoringResultResp'
        '400':
          description: Ошибка генерации ссылки
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/bts-data:
    get:
      tags:
        - Кредиты
      summary: Метод для получения ссылки (liveness, eds)
      description: |
        Метод для получения ссылки (liveness, eds)
      operationId: getBtsDataForLoanApp
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - in: header
          name: Accept-Language
          description: Язык запроса
          required: true
          schema:
            type: string
            enum:
              - kk
              - en
              - ru
            example: ru
            default: kk
        - $ref: '#/components/parameters/ApplicationIDPathParam'
        - in: query
          name: type
          required: true
          schema:
            type: string
            description: Тип генерируемой ссылки
      responses:
        '200':
          description: Данные со сгенерированной ссылкой и ссылкой для редиректа
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BtsDataResp'
        '400':
          description: Ошибка генерации ссылки
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/identify:
    post:
      tags:
        - Кредиты
      summary: Метод для идентификации после liveness
      description: |
        Метод для идентификации после liveness
      operationId: loansPostIdentifyBtsDataSme
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/ApplicationIDPathParam'
        - $ref: '#/components/parameters/AcceptLanguage'
      requestBody:
        $ref: '#/components/requestBodies/PostIdentifyBtsDataSmeRequestBody'
      responses:
        '200':
          description: Результат идентификации
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostIdentifyBtsDataSmeResp'
        '400':
          description: |
            Ошибка идентификации
            <br><b>10.2 LoanApplicationNotFound</b> - Кредитная заявка не найдена
            <br><b>10.6 LoansAppHasNotSuitableStatus</b> - Кредитная заявка находится в не соответствующем статусе
            <br><b>17.1 LivenessVerifyFailed</b> - Верификация пользователя не пройдена
            <br><b>17.2 LivenessPassedByQRLink</b> - Верификация пользователя пройдена по QR ссылке на другом устройстве
  /loans/{applicationID}/eds:
    post:
      tags:
        - Кредиты
      summary: Метод для идентификации после ЭЦП
      description: |
        Метод для идентификации после ЭЦП
      operationId: loansPostEdsBtsData
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/ApplicationIDPathParam'
        - $ref: '#/components/parameters/AcceptLanguage'
      requestBody:
        $ref: '#/components/requestBodies/PostEdsBtsDataRequestBody'
      responses:
        '200':
          description: Результат идентификации
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostEdsBtsDataResp'
        '400':
          description: Ошибка идентификации
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/details:
    get:
      tags:
        - Кредиты
      summary: Получение детальной информации по выданному займу
      description: Получение детальной информации по выданному займу
      operationId: getLoansDetails
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/ApplicationIDPathParam'
        - $ref: '#/components/parameters/AcceptLanguage'
      responses:
        '200':
          description: Запрос выполнен успешно, данные о кредитах переданы
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetLoansDetailsResponse'
        '400':
          description: |
            Ошибка получения данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/early-repay:
    post:
      tags:
        - Кредиты
      summary: Метод для проведения досрочного погашения (ЧДП/ПДП)
      description: |
        Метод для проведения досрочного погашения (ЧДП/ПДП)
      operationId: loansPostEarlyRepay
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/ApplicationIDPathParam'
      requestBody:
        $ref: '#/components/requestBodies/PostEarlyRepayRequestBody'
      responses:
        '200':
          description: Результат успешного досрочного погашения
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostEarlyRepayResp'
        '400':
          description: Ошибка досрочного погашения | <br><b>10.1 CodeLoansInvalidInputError</b> - неверный формат запроса <br><b>10.2 LoanApplicationNotFound</b> - Кредитная заявка не найдена <br><b>10.6 LoansAppHasNotSuitableStatus</b> - Кредитная заявка находится в не соответствующем статусе <br><b>10.7 CodeLoansEarlyRepayInvalidSum</b> - Сумма досрочного погашения не соответствует условиям <br><b>10.8 CodeLoansBankWorkingDayCheckFailed</b> - Операция доступна только в рабочее время (09:00-21:00 по КЗ)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/{applicationID}/{docID}/sign-confirm:
    post:
      tags:
        - Документы
      summary: Подтверждение подписи документа через ОТП
      description: Подтверждение подписи персонализированного документа из шаблона через ОТП
      operationId: loansConfirmSignDocumentByID
      parameters:
        - $ref: '#/components/parameters/AcceptLanguage'
        - $ref: '#/components/parameters/ApplicationIDPathParam'
        - $ref: '#/components/parameters/DocumentIDPathParam'
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/OtpBody'
      responses:
        '200':
          description: Документ подписан, обновленная информация по документу
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
        '400':
          description: |
            Ошибка подписи документа
            <br><b>3.1 InvalidCode</b> - Неверный код
            <br><b>3.2 MaxCodeChecksExceeded</b> - Превышено количество попыток проверки кода
            <br><b>3.3 MaxCodeAttemptsExceeded</b> - Превышен лимит количества попыток за период
            <br><b>3.5 AttemptNotFound</b> - Сценарий проверки ОТП кода не найден или истёк
            <br><b>4.9 DocumentAlreadySigned</b> - Документ уже подписан
            <br><b>4.12 FailedToSignDocument</b> - Не удалось подписать документ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /loans/change-disbursement-mode:
    post:
      tags:
        - Кредиты
      summary: Метод для изменения флага режима работы биржи
      description: |
        Метод для изменения флага режима работы биржи (автоматический/ручной)
      operationId: changeDisbursementControl
      security:
        - BearerTokenAuth: []
      requestBody:
        $ref: '#/components/requestBodies/PostChangeDisbursementControlRequestBody'
      responses:
        '200':
          description: Результат успешного изменения режима работы биржи
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostChangeDisbursementControlResponse'
        '400':
          description: Ошибка изменения режима работы биржи
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /dict/doc/by-filters:
    post:
      tags:
        - Справочники
      summary: Просмотр списка документов справочника по фильтрам данных
      description: |
        Просмотр списка документов
      operationId: dictDocGetListByFilters
      security:
        - BearerTokenAuth: []
      requestBody:
        $ref: '#/components/requestBodies/DictDocGetListByFiltersReq'
      responses:
        '200':
          description: Информация по списку документов
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DictDocumentList'
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /dictionaries/locations:
    get:
      tags:
        - Справочники
      summary: Получение локаций по родительским локациям
      description: |
        Получение локаций по родительским локациям
      operationId: dictGetLocations
      security:
        - BearerTokenAuth: []
      parameters:
        - name: parentID
          in: query
          description: Идентификатор родительской локации. 0 - для получения корневых локаций. Не требуется если указан параметр parentIds
          example: 0
          required: false
          schema:
            type: integer
            format: int32
            minimum: 0
        - name: parentIds
          in: query
          description: Идентификаторы родительских локаций. Не требуется если указан параметр parentID
          required: false
          schema:
            type: array
            minItems: 1
            items:
              type: integer
              format: int32
              minimum: 0
          explode: false
        - $ref: '#/components/parameters/AcceptLanguage'
      x-parameters-constraints:
        oneOf:
          - required:
              - parentID
          - required:
              - parentIds
      responses:
        '200':
          description: Локации
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetLocationsResp'
        '400':
          description: |
            Ошибка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /payments/transactions:
    get:
      tags:
        - Платежи
      summary: История операций
      description: История операций
      operationId: getTransactions
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/PaymentsGetTransactionsStartDate'
        - $ref: '#/components/parameters/PaymentsGetTransactionsEndDate'
        - $ref: '#/components/parameters/PaymentsGetTransactionsAccounts'
        - $ref: '#/components/parameters/PaymentsGetTransactionsCards'
        - $ref: '#/components/parameters/PaymentsGetTransactionsOperationType'
        - $ref: '#/components/parameters/PaymentsGetTransactionsCounterparty'
        - $ref: '#/components/parameters/PaymentsGetTransactionsMinAmount'
        - $ref: '#/components/parameters/PaymentsGetTransactionsMaxAmount'
        - $ref: '#/components/parameters/PaymentsGetTransactionsLimit'
        - $ref: '#/components/parameters/PaymentsGetTransactionsOffset'
      responses:
        '200':
          description: История операций
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentsGetTransactionsResponse'
  /payments/transactions/{transactionID}:
    get:
      tags:
        - Платежи
      summary: Получение транзакции по индентификатору
      description: Получение транзакции по индентификатору
      operationId: getTransactionByID
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/PaymentsGetTransactionByIDTransactionID'
      responses:
        '200':
          description: Транзакция
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentsGetTransactionByIDResponse'
  /payments/transactions/{transactionID}/receipt:
    get:
      tags:
        - Платежи
      summary: Получение квитанции по платежу
      description: Получение квитанции по платежу
      operationId: getTransactionReceipt
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/PaymentsGetTransactionByIDTransactionID'
      responses:
        '200':
          description: Квитанция транзакции
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentsGetTransactionReceiptResponse'
  /payments/history:
    get:
      tags:
        - Платежи
      summary: История платежей
      description: История платежей
      operationId: getPaymentHistory
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/PaymentsGetHistoryClientIinBin'
        - $ref: '#/components/parameters/PaymentsGetTransactionsLimit'
        - $ref: '#/components/parameters/PaymentsGetTransactionsOffset'
      responses:
        '200':
          description: История платежей
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentsGetHistoryResponse'
  /payments/check-account-iin:
    post:
      tags:
        - Платежи
      summary: Проверка ИИН/БИН и номера счёта получателя
      description: Проверка ИИН/БИН и номера счёта бенефициара
      operationId: paymentsCheckAccountIin
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/PaymentsCheckAccountIinRequest'
      responses:
        '200':
          description: |
            Результат проверки номера счёта и ИИН/БИН бенефициара.
            <br>Если получатель ЮЛ то приходят 4 параметра информация о банке бенефициара, тип что это ЮЛ, наименование ЮЛ
            <br>Если получатель ФЛ то приходит так же информация о банке бенефициара, наименование ФЛ
            <br>Если у ФЛ есть дополнительный тип деятельности то появляется объект additionalIndividualType в котором указана доп деятельность
            <br>В поле additionalIndividualType.type могут быть следующие значения: 32 - ИП, 64 — адвокат, 128 — частный нотариус, 256 — частный судебный исполнитель
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentsCheckAccountIinResponse'
        '400':
          description: |
            Ошибки проверки пользователя
            <br><b>12.19 InvalidAccountNumber</b> - Неверный номер счёта
            <br><b>12.26 BankByIbanCodeNotFound</b> - Банк по ibanCode не найден
            <br><b>12.27 CodeTaxPayerNotFound</b> - Не найден налогоплатильщик по ИИН/БИН
  /payments/create-payment-by-account:
    post:
      tags:
        - Платежи
      summary: Создание платежа для внутренних переводов
      description: Создание платежа для внутренних переводов
      operationId: createPaymentByAccount
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/CreatePaymentByAccount'
      responses:
        '200':
          description: Платеж создан успешно
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatePaymentByAccountResponse'
        '400':
          description: |
            Ошибки создания перевода
            <br><b>12.5 InvalidPayerBinIINError</b> - ИИН / БИН пользователя введен не верно
            <br><b>12.6 InvalidBeneficiaryBinIINError</b> - ИИН / БИН бенефициара введен не верно
            <br><b>12.7 InvalidPayerAccountError</b> - Счет пользователя введен не верно
            <br><b>12.8 InvalidBeneficiaryAccountError</b> - Счет бенефициара введен не верно
            <br><b>12.9 AmountIsNotDecimalError</b> - Сумма введена не верно
            <br><b>12.11 PaymentDetailsTooLongError</b> - Детали платежа больше 250 символов
            <br><b>12.21 DublicatePayment</b> - Дубликат платежа
            <br><b>12.28 InvalidPurposeCode</b> - Неверный код назначения платежа
            <br><b>12.29 InvalidBeneficiaryCode</b> - Неверный код бенефициара
            <br><b>12.30 InvalidBeneficiaryBankBic</b> - Неверный бик банка бенефициара
            <br><b>12.31 GrossErrorsWhenExecutePayment</b> - Обнаружены грубые ошибки при выполнении платежа
  /payments/confirm-payment-by-account:
    post:
      tags:
        - Платежи
      summary: Проверка отп и выполнение платежа для внутренних переводов
      description: Проверка отп и выполнение платежа для внутренних переводов
      operationId: confirmPaymentByAccount
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/ConfirmPaymentByAccount'
      responses:
        '200':
          description: Платеж выполнен успешно
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfirmPaymentByAccountResponse'
        '400':
          description: |
            Ошибки выполнения платежа
            <br><b>12.3 CodePaymentsInvalidOTP</b> - Неверный одноразовый пароль
            <br><b>12.16 TransactionNotFoundError</b> - Транзакция платежа не найдена
            <br><b>12.23 OTPNumberAttemptsExceeded</b> - Превышено количество попыток ввода отп
  /smepayments/client:
    get:
      tags:
        - Платежи
      summary: Получение данных по клиенту
      description: Получение данных по клиенту
      operationId: smePaymentsClient
      security:
        - BearerTokenAuth:
            - active
      responses:
        '200':
          description: Данные по клиенту успешно получены
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientResponse'
        '400':
          description: Ошибка получения данных по клиенту
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
        '403':
          description: Некорректный контекст запроса (origin is not sme)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
        '503':
          description: Сервис недоступен
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /smepayments/transactions:
    post:
      tags:
        - Платежи
      summary: Создание платежа (SME)
      description: Создание различных типов платежей (бюджетных и небюджетных)
      operationId: createPayment
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/CreatePaymentRequest'
      responses:
        '200':
          description: Платеж успешно создан
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatePaymentResponse'
        '400':
          description: Ошибка создания платежа
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
        '500':
          description: Внутренняя ошибка сервера
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /smepayments/transactions/{transactionID}/confirm:
    post:
      tags:
        - Платежи
      summary: Подтверждение платежа в бюджет (SME)
      description: Подтверждение платежа в бюджет (SME)
      operationId: confirmPaymentSme
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/PaymentsTransactionID'
      responses:
        '200':
          description: Платеж подтвержден успешно
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConfirmPaymentSmeResponse'
        '400':
          description: |
            Ошибки подтверждения платежа
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /smepayments/transactions/{transactionID}/payment-order:
    get:
      tags:
        - Платежи
      summary: Получение ссылки на печатную форму платежного поручения
      description: Получение ссылки на печатную форму платежного поручения
      operationId: smePaymentsGetPaymentOrder
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/PaymentsTransactionID'
      responses:
        '200':
          description: Ссылка на печатную форму платежного поручения
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SmePaymentsGetPaymentOrderResponse'
        '404':
          description: Транзакция не найдена
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
        '500':
          description: Что-то пошло не так
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /smepayments/transactions/{transactionNumber}/payment-order-by-tr-number:
    get:
      tags:
        - Платежи
      summary: Получение ссылки на печатную форму платежного поручения
      description: Получение ссылки на печатную форму платежного поручения
      operationId: smePaymentsGetPaymentOrderByTrNumber
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/PaymentsTransactionNumber'
      responses:
        '200':
          description: Ссылка на печатную форму платежного поручения
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SmePaymentsGetPaymentOrderByTrNumberResponse'
        '404':
          description: Транзакция не найдена
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
        '500':
          description: Что-то пошло не так
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /smepayments/dictionaries/code:
    get:
      tags:
        - Платежи
      summary: Получить справочник Кбе/Код (SME)
      description: Справочник Кбе/Код (SME)
      operationId: getKbeKodList
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/KbeKodFilterCodes'
        - $ref: '#/components/parameters/KbeKodFilterResidency'
      responses:
        '200':
          description: Справочник Кбе/Код (SME)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetKbeKodListResp'
        '400':
          description: Ошибка валидации
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
        '500':
          description: Внутренняя ошибка сервера
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /smepayments/dictionaries/knp:
    get:
      tags:
        - Платежи
      summary: Получить справочник КНП (SME)
      description: Справочник КНП (SME)
      operationId: getKnpList
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/KnpFilterCodes'
        - $ref: '#/components/parameters/KnpFilterGroups'
      responses:
        '200':
          description: Справочник КНП (SME)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetKnpListResp'
        '400':
          description: Ошибка валидации
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
        '500':
          description: Внутренняя ошибка сервера
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /smepayments/dictionaries/banks:
    get:
      tags:
        - Платежи
      summary: Получить справочник банков (SME)
      description: Справочник банков (SME)
      operationId: getBankList
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/BankFilterBics'
      responses:
        '200':
          description: Справочник банков (SME)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetBankListResp'
        '400':
          description: Ошибка валидации
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
        '500':
          description: Внутренняя ошибка сервера
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /smepayments/dictionaries/tax-authorities:
    get:
      tags:
        - Платежи
      summary: Получить справочник УГД (SME)
      description: Справочник УГД (SME)
      operationId: getTaxAuthorityList
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/TaxAuthorityFilterRegions'
      responses:
        '200':
          description: Справочник УГД (SME)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTaxAuthorityListResp'
        '400':
          description: Ошибка валидации
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
        '500':
          description: Внутренняя ошибка сервера
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /smepayments/dictionaries/kbk:
    get:
      tags:
        - Платежи
      summary: Получить справочник КБК (SME)
      description: Справочник КБК (SME)
      operationId: getKbkList
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/KbkFilterCodes'
      responses:
        '200':
          description: Справочник КБК (SME)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetKbkListResp'
        '400':
          description: Ошибка валидации
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
        '500':
          description: Внутренняя ошибка сервера
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /smepayments/otp:
    post:
      tags:
        - Платежи
      summary: Создание OTP для подписи транзакции
      description: Создание OTP для подписи транзакции
      operationId: smePaymentsCreateOtp
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/SmePaymentsCreateOtpRequestBody'
      responses:
        '200':
          description: OTP успешно сгенерирован
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SmePaymentsCreateOtpResponse'
        '400':
          description: Ошибка создания OTP
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /smepayments/otp/{attemptID}/resend:
    post:
      tags:
        - Платежи
      summary: Перегенерация OTP кода
      description: Перегенерация OTP кода
      operationId: smePaymentsOtpResend
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/SmePaymentsOtpAttemptID'
      responses:
        '200':
          description: OTP успешно перегенерирован
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SmePaymentsCreateOtpResponse'
        '400':
          description: Ошибка перегенерации OTP
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /smepayments/otp/{attemptID}/validate:
    post:
      tags:
        - Платежи
      summary: Валидация OTP кода
      description: Валидация OTP кода
      operationId: smePaymentsOtpValidate
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/SmePaymentsOtpAttemptID'
      requestBody:
        $ref: '#/components/requestBodies/SmePaymentsOtpValidateRequestBody'
      responses:
        '200':
          description: OTP успешно валидирован
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SmePaymentsOtpValidateResponse'
        '400':
          description: Ошибка валидации OTP
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /smepayments/worktime:
    get:
      tags:
        - Платежи
      summary: Проверка операционного дня банка
      description: Проверка операционного дня банка
      operationId: smePaymentsWorktime
      security:
        - BearerTokenAuth:
            - active
      responses:
        '200':
          description: Платеж подтвержден успешно
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SmePaymentsWorktimeResponse'
        '400':
          description: Ошибка получения операционного дня банка
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
        '500':
          description: Внутренняя ошибка сервера
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /smepayments/employee-list:
    get:
      tags:
        - Платежи
      summary: Получение списка сотрудников ИП
      description: Получение списка сотрудников ИП
      operationId: smePaymentsGetEmployeeList
      security:
        - BearerTokenAuth:
            - active
      responses:
        '200':
          description: Список сотрудников ИП
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SmePaymentsGetEmployeeListResponse'
        '404':
          description: Сотрудник не найден
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
        '500':
          description: Что-то пошло не так
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /smepayments/employee-list/new:
    post:
      tags:
        - Платежи
      summary: Добавление нового сотрудника в список сотрудников ИП
      description: Добавление нового сотрудника в список сотрудников ИП
      operationId: smePaymentsCreateEmployee
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/SmePaymentsAddEmployeeRequestBody'
      responses:
        '200':
          description: Данные сотрудника ИП
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmployeeInfo'
        '403':
          description: Сотрудник с таким ИИН уже существует
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
        '500':
          description: Что-то пошло не так
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /smepayments/employee-list/{employeeID}/edit:
    put:
      tags:
        - Платежи
      summary: Редактирование сотрудника из списка сотрудников ИП
      description: Редактирование сотрудника из списка сотрудников ИП
      operationId: smePaymentsUpdateEmployeeByID
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/EmployeeID'
      requestBody:
        $ref: '#/components/requestBodies/SmePaymentsUpdateEmployeeRequestBody'
      responses:
        '200':
          description: Список сотрудников ИП
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmployeeInfo'
        '404':
          description: Сотрудник не найден
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
        '500':
          description: Что-то пошло не так
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /smepayments/employee-list/{employeeID}/delete:
    delete:
      tags:
        - Платежи
      summary: Удаление сотрудника из списка сотрудников ИП
      description: Удаление сотрудника из списка сотрудников ИП
      operationId: smePaymentsDeleteEmployeeByID
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/EmployeeID'
      responses:
        '200':
          description: Список сотрудников ИП
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SmePaymentsDeleteEmployeeListResponse'
        '404':
          description: Сотрудник не найден
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
        '500':
          description: Что-то пошло не так
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /user/cards:
    get:
      tags:
        - Карты и счета
      summary: Список текущих карт и счетов пользователя
      description: Список текущих карт и счетов пользователя
      operationId: userCards
      security:
        - BearerTokenAuth:
            - active
      responses:
        '200':
          description: Данные о продуктах
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserCardsResponse'
        '400':
          description: |
            Если ошибка с самой картой, это будет отражено в теле 200ки, специфичных ошибок пока не ожидаем.
            Глубже не прорабатывалось пока, соряшки :(
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /user/accounts/{accountID}:
    get:
      tags:
        - Карты и счета
      summary: Получение счета пользователя
      description: Получение счета пользователя
      operationId: getUserAccounts
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/AccountIDPathParam'
      responses:
        '200':
          description: Счета пользователя
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserAccountResponse'
        '400':
          description: |
            Если ошибка с самой картой, это будет отражено в теле 200ки, специфичных ошибок пока не ожидаем.
            Глубже не прорабаты
  /user/loans:
    get:
      tags:
        - Кредиты
      summary: Получение информации о кредитах пользователя
      description: Получение информации о кредитах пользователя
      operationId: getLoans
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - in: header
          name: Accept-Language
          description: Язык запроса
          required: true
          schema:
            type: string
            enum:
              - kk
              - en
              - ru
            example: ru
            default: kk
      responses:
        '200':
          description: Список кредитов, соответствующих фильтрам
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoansResponse'
        '400':
          description: |
            Ошибка получения данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /user/locale:
    post:
      tags:
        - Профиль
      summary: Метод для обновления локализации пользователя
      description: |
        Метод для обновления локализации пользователя
      operationId: usersUpdateUserLocale
      security:
        - BearerTokenAuth:
            - not_identified
            - active
      parameters:
        - name: Accept-Language
          in: header
          required: true
          schema:
            type: string
            enum:
              - ru
              - kk
              - en
            default: kk
          description: Язык запроса
      responses:
        '200':
          description: Успех обновления локализации пользователя
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateUserLocaleResp'
        '400':
          description: |
            Ошибка получения данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /user/profile:
    post:
      tags:
        - Профиль
      summary: Удаление профиля из системы
      description: Выход пользователя из системы и удаление профиля
      operationId: profileDelete
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/ProfileDeleteBody'
      responses:
        '200':
          $ref: '#/components/responses/RespEmpty'
        '400':
          description: |
            <br>Ошибка запроса удаления профиля
            <br><b>1.0 ValidationError</b> - Ошибка валидации данных в теле запроса
  /user/accounts:
    get:
      tags:
        - Карты и счета
      summary: Список текущих счетов пользователя
      description: Список текущих счетов пользователя
      operationId: getUserAccountsSME
      security:
        - BearerTokenAuth:
            - active
      responses:
        '200':
          description: Данные о продуктах
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserAccountsSMEResponse'
        '400':
          description: |
            Если ошибка с самой картой, это будет отражено в теле 200ки, специфичных ошибок пока не ожидаем.
            Глубже не прорабатывалось пока, соряшки :(
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /tasks:
    get:
      tags:
        - Таск менеджер
      summary: Получение списка задач
      description: Получение списка задач с поддержкой фильтров и пагинации
      operationId: getTasks
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/TaskStatusQueryParam'
        - $ref: '#/components/parameters/TaskTypeQueryParam'
        - $ref: '#/components/parameters/TaskCreatedAfterQueryParam'
        - $ref: '#/components/parameters/TaskCreatedBeforeQueryParam'
        - $ref: '#/components/parameters/TaskPageQueryParam'
        - $ref: '#/components/parameters/TaskPageSizeQueryParam'
      responses:
        '200':
          description: Список задач
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTasksListResponse'
        '400':
          $ref: '#/components/responses/RespEmpty'
  /tasks/{taskID}:
    get:
      tags:
        - Таск менеджер
      summary: Получение детальной информации о задаче
      description: Получение детальной информации о задаче по ID
      operationId: getTaskByID
      security:
        - BearerTokenAuth:
            - active
      parameters:
        - $ref: '#/components/parameters/TaskIDPathParam'
      responses:
        '200':
          description: Детальная информация о задаче
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTaskDetailsResponse'
        '400':
          $ref: '#/components/responses/RespEmpty'
  /fileguard:
    post:
      tags:
        - File Guard
      summary: Загрузка PDF файла
      description: Загрузка PDF файла
      operationId: postUnsecurePdfFile
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/FileUploadRequestBody'
      responses:
        '200':
          description: Данные с идентификатором загруженного документа
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileUploadResponse'
        '400':
          description: |
            Ошибка загрузки файла
            <br><b>21.1 IncorrectInputFileFormat</b> - Некорректный формат загружаемого файла
            <br><b>21.2 IncorrectInputFileSize</b> - Некорректный размер загружаемого файла
  /foreign-activity/create-convertation:
    post:
      tags:
        - ВЭД
      summary: Создать конвертацию
      description: Создает новую конвертацию для внешнеэкономической деятельности
      operationId: createConvertation
      security:
        - BearerTokenAuth:
            - active
      requestBody:
        $ref: '#/components/requestBodies/CreateConvertationRequest'
      responses:
        '200':
          description: Успешное создание конвертации
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateConvertationResponse'
        '400':
          description: Ошибка запроса
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /foreign-activity/conversion-sum:
    post:
      tags:
        - ВЭД
      summary: Расчёт сумм конвертации
      description: |
        Данный метод позволяет рассчитать суммы конвертации для валютного контракта.
      operationId: faConversionSum
      security:
        - BearerTokenAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConversionSumRequestBody'
      responses:
        '200':
          description: Данные по расчёту сумм конвертации
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConversionSumResponse'
        '400':
          description: |
            Ошибка в запросе
            <br><b>1.1 InvalidParameter</b> - Некорректное значение параметра запроса
            <br><b>1.2 MissingRequiredParameter</b> - Отсутствует обязательный параметр запроса
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/APIError'
                  - $ref: '#/components/schemas/ValidationError'
components:
  securitySchemes:
    BearerTokenAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  responses:
    RespEmpty:
      description: Операция выполнена успешно
      content:
        application/json:
          schema:
            type: object
            properties: {}
  schemas:
    Money:
      type: object
      description: Сумма
      required:
        - value
        - currencyCode
      properties:
        value:
          type: string
          description: Сумма
        currencyCode:
          $ref: '#/components/schemas/CurrencyCode'
    CurrencyCode:
      type: string
      description: Код валюты
      enum:
        - KZT
        - RUB
        - EUR
        - USD
        - CNY
    PhoneNumber:
      type: string
      description: Номер телефона
      pattern: ^\+?[1-9]\d{10,10}$
      example: '+78889991100'
    Email:
      type: string
      pattern: ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$
      description: Электронная почта
    APIError:
      type: object
      description: Общий формат ошибки API. Возвращает цифровой код
      properties:
        error:
          type: string
        fields:
          type: object
          description: Объект с описанием деталей ошибок
          additionalProperties:
            type: string
      required:
        - error
      example:
        error: X.0
    ValidationError:
      type: object
      description: Ошибка валидации данных в теле запроса
      properties:
        error:
          type: string
          description: Код ошибки
          example: '1.0'
        fields:
          type: object
          description: Объект с описанием ошибок валидации полей
          additionalProperties:
            type: string
          example:
            iin: value must be a string
      required:
        - error
        - fields
    Health:
      required:
        - users
        - otp
        - documents
        - notifications
        - keycloakProxy
        - kgdBridge
        - btsBridge
        - smsBridge
        - loans
        - colvirBridge
        - payments
        - cardsAccounts
        - dictionary
        - pkbBridge
        - amlBridge
        - liveness
        - taskManager
        - juicyscoreBridge
        - jiraBridge
        - fileGuard
        - scoring
        - seonBridge
        - sprBridge
        - altScoreBridge
        - qazpostBridge
        - deposits
        - bsasBridge
        - apBridge
        - processingBridge
        - collection
        - paymentsSme
        - referral
        - antifraud
        - crm
        - tokenize
        - balanceUpdater
        - kaspiBridge
        - bitrixBridge
        - foreignActivity
      type: object
      properties:
        users:
          type: boolean
          description: Статус сервиса users
        otp:
          type: boolean
          description: Статус сервиса otp
        documents:
          type: boolean
          description: Статус сервиса documents
        notifications:
          type: boolean
          description: Статус сервиса notifications
        keycloakProxy:
          type: boolean
          description: Статус сервиса keycloakProxy
        kgdBridge:
          type: boolean
          description: Статус сервиса kgdBridge
        btsBridge:
          type: boolean
          description: Статус сервиса btsBridge
        smsBridge:
          type: boolean
          description: Статус сервиса smsBridge
        loans:
          type: boolean
          description: Статус сервиса loans
        colvirBridge:
          type: boolean
          description: Статус сервиса colvirBridge
        payments:
          type: boolean
          description: Статус сервиса payments
        cardsAccounts:
          type: boolean
          description: Статус сервиса cardsAccounts
        dictionary:
          type: boolean
          description: Статус сервиса dictionary
        pkbBridge:
          type: boolean
          description: Статус сервиса pkbBridge
        amlBridge:
          type: boolean
          description: Статус сервиса amlBridge
        liveness:
          type: boolean
          description: Статус сервиса liveness
        taskManager:
          type: boolean
          description: Статус сервиса taskManager
        juicyscoreBridge:
          type: boolean
          description: Статус сервиса juicyscoreBridge
        jiraBridge:
          type: boolean
          description: Статус сервиса jiraBridge
        fileGuard:
          type: boolean
          description: Статус сервиса fileGuard
        scoring:
          type: boolean
          description: Статус сервиса scoring
        seonBridge:
          type: boolean
          description: Статус сервиса seonBridge
        sprBridge:
          type: boolean
          description: Статус сервиса sprBridge
        altScoreBridge:
          type: boolean
          description: Статус сервиса altScoreBridge
        qazpostBridge:
          type: boolean
          description: Статус сервиса qazpostBridge
        deposits:
          type: boolean
          description: Статус сервиса deposits
        bsasBridge:
          type: boolean
          description: Статус сервиса bsasBridge
        apBridge:
          type: boolean
          description: Статус сервиса apBridge
        processingBridge:
          type: boolean
          description: Статус сервиса processingBridge
        collection:
          type: boolean
          description: Статус сервиса collection
        paymentsSme:
          type: boolean
          description: Статус сервиса paymentsSme
        referral:
          type: boolean
          description: Статус сервиса referral
        antifraud:
          type: boolean
          description: Статус сервиса antifraud
        crm:
          type: boolean
          description: Статус сервиса crm
        tokenize:
          type: boolean
          description: Статус сервиса tokenize
        balanceUpdater:
          type: boolean
          description: Статус сервиса balanceUpdater
        kaspiBridge:
          type: boolean
          description: Статус сервиса kaspiBridge
        bitrixBridge:
          type: boolean
          description: Статус сервиса bitrixBridge
        foreignActivity:
          type: boolean
          description: Статус сервиса foreignActivity
    DeviceInfo:
      type: object
      properties:
        appVersion:
          type: string
          default: '101'
          description: Версия приложения
        deviceModel:
          type: string
          description: Модель устройства
          example: iPhone 7
        installationID:
          type: string
          format: uuid
          description: Идентификатор установки приложения
          example: 4d41e509-1d1e-4530-a1d6-97d2d599d3f8
        systemType:
          type: string
          description: Тип операционной системы
          enum:
            - Android
            - iOS
            - Web
          example: iOS
        systemVersion:
          type: string
          description: Версия операционной системы
          example: '20.2'
      required:
        - appVersion
        - deviceModel
        - installationID
        - systemType
        - systemVersion
    IpInfo:
      type: object
      properties:
        registrationDate:
          type: string
          description: Дата регистрации ИП "yyyy-mm-dd"
          example: '2023-05-17'
        registrationCert:
          type: string
          description: Номер свидетельства о регистрации ИП
          example: '123456789'
        issuingAuthority:
          type: string
          description: Организация, выдавшая документ
          example: Налоговая инспекция №5
      required:
        - registrationDate
        - registrationCert
        - issuingAuthority
    OtpResponse:
      type: object
      required:
        - attemptID
        - retryTime
      properties:
        attemptID:
          type: string
          format: uuid
          description: Идентификатор попытки для проверки кода
        retryTime:
          type: integer
          description: Количество секунд до следующей отправки
          example: 60
    AuthTokens:
      required:
        - access
        - refresh
      type: object
      properties:
        access:
          type: string
          description: Токен авторизации
        refresh:
          type: string
          description: Токен обновления авторизации
    NextStep:
      type: string
      example: identificationRequired
      enum:
        - identificationRequired
        - reidentificationRequired
        - dboSignRequired
    AuthConfirmResponse:
      type: object
      required:
        - tokens
        - userID
      properties:
        userID:
          type: string
          description: Идентификатор пользователя
          example: bc759511-de96-469f-a23c-fc7e2c406c0f
        tokens:
          $ref: '#/components/schemas/AuthTokens'
        nextStep:
          $ref: '#/components/schemas/NextStep'
    AuthRefreshResponse:
      required:
        - tokens
        - nextStep
        - firstName
        - lastName
        - IPName
      type: object
      properties:
        tokens:
          $ref: '#/components/schemas/AuthTokens'
        nextStep:
          $ref: '#/components/schemas/NextStep'
        firstName:
          type: string
        lastName:
          type: string
        middleName:
          type: string
        IPName:
          type: string
    BtsDataForAuthResp:
      type: object
      required:
        - link
        - redirectURI
      properties:
        link:
          type: string
          description: Ссылка для прохождения идентификации
        redirectURI:
          type: string
          description: Ссылка для редиректа
    DocumentType:
      type: string
      enum:
        - accountOpeningApplicationSMEIP
        - bankServiceAgreementSMEIP
        - personalDataAgreementSMEIP
        - accessionAgreementSMEIP
        - complexConsentClientSMEIP
        - financingTermsInfoSMEIP
      description: Тип (шаблон) документа
    Document:
      required:
        - ID
        - type
        - version
        - fileLink
        - title
        - signed
      type: object
      properties:
        ID:
          type: string
          format: uuid
          description: Идентификатор документа
        title:
          type: string
          description: Название документа
        type:
          $ref: '#/components/schemas/DocumentType'
        version:
          type: integer
          description: Версия документа, которая соотносится с типом документа (версия шаблона)
        fileLink:
          type: string
          description: Ссылка на физический файл
        signed:
          type: boolean
          description: Был ли документ подписан пользователем
    DocumentsForSignResponse:
      type: object
      required:
        - documents
      properties:
        documents:
          type: array
          description: Документы на подпись
          items:
            $ref: '#/components/schemas/Document'
    CurrencyAccount:
      type: object
      required:
        - currency
        - accountExists
      properties:
        currency:
          $ref: '#/components/schemas/CurrencyCode'
        accountExists:
          type: boolean
          description: Наличие счета ИП в данной валюте
    CurrencyCheckResponse:
      type: object
      required:
        - isAccountOpeningAvailable
        - currencies
      properties:
        isAccountOpeningAvailable:
          type: boolean
          description: Доступность открытия счета
        currencies:
          type: array
          description: Массив объектов с доступными валютами для открытия нового счета
          items:
            $ref: '#/components/schemas/CurrencyAccount'
    PublicDocumentType:
      type: string
      enum:
        - personalDataAgreementSMEIP
        - financingTermsInfoSMEIP
      description: Тип (шаблон) публичного документа
    ConfirmSignDocumentsResponse:
      type: object
      required:
        - documents
      properties:
        documents:
          type: array
          description: Подписанные документы
          items:
            $ref: '#/components/schemas/Document'
    SignedDocumentsBatchResponse:
      type: object
      required:
        - documents
      properties:
        documents:
          type: array
          description: Подписанные документы
          items:
            $ref: '#/components/schemas/Document'
    HeaderRequest:
      type: object
      required:
        - memberShortName
        - uuid
      properties:
        memberShortName:
          type: string
        uuid:
          type: string
    RequestItem:
      type: object
      required:
        - serialNumber
        - bidOption
        - otcOption
        - stbOption
        - productCode
        - purchaseType
        - clientName
        - currency
        - bidValue
        - valueDate
        - tenor
        - otcCounterParty
        - otcMurabaha
        - otcMurabahaValue
        - eCertNo
      properties:
        serialNumber:
          type: string
        bidOption:
          type: string
        otcOption:
          type: string
        stbOption:
          type: string
        productCode:
          type: string
        purchaseType:
          type: string
        clientName:
          type: string
        currency:
          type: string
        bidValue:
          type: string
        valueDate:
          type: string
        tenor:
          type: string
        otcCounterParty:
          type: string
        otcMurabaha:
          type: string
        otcMurabahaValue:
          type: string
        eCertNo:
          type: string
    HeaderResp:
      type: object
      required:
        - memberShortName
        - uuid
      properties:
        memberShortName:
          type: string
        uuid:
          type: string
        errorCode:
          type: string
          nullable: true
        errorMsg:
          type: string
          nullable: true
    BodyItem:
      type: object
      required:
        - serialNumber
        - statusCode
        - statusMessage
      properties:
        serialNumber:
          type: integer
        statusCode:
          type: integer
        statusMessage:
          type: string
    CreateOrderResp:
      type: object
      required:
        - header
        - body
      properties:
        header:
          $ref: '#/components/schemas/HeaderResp'
        body:
          type: array
          items:
            $ref: '#/components/schemas/BodyItem'
    OrderResultItemRequest:
      type: object
      required:
        - forceYN
      properties:
        serialNumber:
          type: string
        forceYN:
          type: string
        maxWaitTime:
          type: string
        waitAllDoneYN:
          type: string
    StatusResp:
      type: object
      required:
        - totalOrderCount
        - processingCount
      properties:
        totalOrderCount:
          type: integer
        processingCount:
          type: integer
    OrderResultBodyResp:
      type: object
      properties:
        serialNumber:
          type: integer
          description: Запись количества заказов
        bidOption:
          type: string
          description: BID заказ – купить товар
        otcOption:
          type: string
          description: Внебиржевой заказ – передача права собственности на товар клиенту
        stbOption:
          type: string
          description: Заказ STB – Продать товар клиенту
        productCode:
          type: string
          description: Код продукта
        purchaseType:
          type: string
          description: Тип покупки
        clientName:
          type: string
          description: ФИО клиента на латинском
        currency:
          type: string
          description: Валюта
        bidValue:
          type: string
          description: Сумма покупки/продажи
        valueDate:
          type: string
          description: Дата запроса покупки/продажи Передает дату по Малазии
        tenor:
          type: string
          description: Значение тенора
        otcCounterParty:
          type: string
        otcMurabaha:
          type: string
        otcMurabahaValue:
          type: string
        ecertNo:
          type: string
        bidErrNo:
          type: string
          description: Номер кода ошибки для BID
        bidMsg:
          type: string
          description: Сообщение для BID
        otcErrNo:
          type: string
          description: Номер кода ошибки для OTC
        otcMsg:
          type: string
          description: Сообщение для OTC
        stbErrNo:
          type: string
          description: Номер кода ошибки для STB
        stbMsg:
          type: string
          description: Сообщение для СТБ
        regTime:
          type: string
          description: Время регистрации (hhmmssmss)
        orderTime:
          type: string
          description: Время заказа (hhmmssmss)
        resultTime:
          type: string
          description: Время генерации данных результата (hhmmssmss)
        purchaseTime:
          type: string
          description: Время покупки (hhmmssmss)
        reportTime:
          type: string
          description: Время внебиржевой отчетности (hhmmssmss)
        sellingTime:
          type: string
          description: Время СТБ (hhmmssmss)
        unit:
          type: string
          description: Единица
        price:
          type: string
          description: Цена
      required:
        - serialNumber
        - bidOption
        - otcOption
        - stbOption
        - productCode
        - purchaseType
        - clientName
        - currency
        - bidValue
        - valueDate
        - tenor
        - otcCounterParty
        - otcMurabaha
        - otcMurabahaValue
        - ecertNo
    GetOrderResp:
      type: object
      required:
        - header
        - status
        - body
      properties:
        header:
          $ref: '#/components/schemas/HeaderResp'
        status:
          $ref: '#/components/schemas/StatusResp'
        body:
          type: array
          items:
            $ref: '#/components/schemas/OrderResultBodyResp'
    BidXMLRequestInput:
      type: object
      required:
        - ecertNo
        - memberShortName
      properties:
        ecertNo:
          type: string
        memberShortName:
          type: string
    SystemResp:
      type: object
      required:
        - successYN
        - msg
        - eCertNo
        - buyer
        - owner
        - bidNo
        - totalValue
        - currency
        - price
        - priceMYREquiv
        - purchaseTimeDate
        - valueDate
        - pName
        - pVolume
      properties:
        successYN:
          type: string
        msg:
          type: string
        eCertNo:
          type: string
          description: Номер электронного сертификата
        buyer:
          type: string
          description: Имя покупателя
        owner:
          type: string
          description: Имя владельца
        bidNo:
          type: string
          description: Номер заявки заказа
        totalValue:
          type: string
          description: Итоговая сумма
        currency:
          type: string
          description: Валюта
        price:
          type: string
          description: Цена
        priceMYREquiv:
          type: string
          description: Цена продукта, эквивалентная MYR
        purchaseTimeDate:
          type: string
          description: Дата и время покупки
        valueDate:
          type: string
          description: Дата валютирования
        pName:
          type: string
          description: Наименование продукта
        pVolume:
          type: string
          description: Соответствующий объем
    BidXMLLine:
      type: object
      required:
        - supplier
        - volume
      properties:
        supplier:
          type: string
        volume:
          type: string
    GetSystemResp:
      type: object
      required:
        - systemResponse
        - line
      properties:
        systemResponse:
          $ref: '#/components/schemas/SystemResp'
        line:
          type: array
          items:
            $ref: '#/components/schemas/BidXMLLine'
    InputRequest:
      type: object
      required:
        - ecertno
        - membershortname
      properties:
        ecertno:
          type: string
        membershortname:
          type: string
    OtcLineItem:
      type: object
      properties:
        SUPPLIER:
          type: string
          example: SPTEST301
        VOLUME:
          type: string
          example: '3.********'
    GetOtcResp:
      type: object
      required:
        - ECERTNO
        - SELLER
        - BUYER
        - TOTALVALUE
        - CURRENCY
        - PRICE
        - PRICE_MYR_EQUIVALENT
        - MURABAHAVALUE
        - REPORTINGTIMEDATE
        - VALUEDATE
        - PNAME
        - PVOLUME
        - LINE
      properties:
        ECERTNO:
          type: string
          example: ACF17FEB21-0000004-000
        SELLER:
          type: string
          example: AGENT
        BUYER:
          type: string
          example: ClientName Test
        TOTALVALUE:
          type: string
          example: 99,999.00
        CURRENCY:
          type: string
          example: MYR
        PRICE:
          type: string
          example: 58,600.29
        PRICE_MYR_EQUIVALENT:
          type: string
          example: 58,600.29
        MURABAHAVALUE:
          type: string
          example: 199,999.00
        REPORTINGTIMEDATE:
          type: string
          example: 09:31:06.761 17 Feb 2021
        VALUEDATE:
          type: string
          example: 17 Feb 2021
        PNAME:
          type: string
          example: ACOFFEE-AAA
        PVOLUME:
          type: string
          example: '3.********'
        LINE:
          type: array
          items:
            $ref: '#/components/schemas/OtcLineItem'
    SellResp:
      type: object
      required:
        - ECERTNO
        - SELLER
        - BUYER
        - TOTALVALUE
        - CURRENCY
        - PRICE
        - PRICE_MYR_EQUIVALENT
        - SELLINGTIMEDATE
        - VALUEDATE
        - PNAME
        - PVOLUME
        - LINE
      properties:
        ECERTNO:
          type: string
          description: Unique certificate number
          example: ACF17FEB21-0000004-000
        SELLER:
          type: string
          description: Name of the seller
          example: ClientName Test
        BUYER:
          type: string
          description: Name of the buyer
          example: BURSA MALAYSIA ISLAMIC SERVICES
        TOTALVALUE:
          type: string
          description: Total value of the trade (formatted with commas)
          example: 99,999.00
        CURRENCY:
          type: string
          description: Currency code
          example: MYR
        PRICE:
          type: string
          description: Price per unit (formatted with commas)
          example: 29,300.00
        PRICE_MYR_EQUIVALENT:
          type: string
          description: Price equivalent in MYR (formatted with commas)
          example: 29,300.00
        SELLINGTIMEDATE:
          type: string
          description: Timestamp of the trade execution
          example: 09:31:06.772 17 Feb 2021
        VALUEDATE:
          type: string
          description: Value date of the trade
          example: 17 Feb 2021
        PNAME:
          type: string
          description: Product name
          example: ACOFFEE-AAA
        PVOLUME:
          type: string
          description: Product volume/quantity
          example: '3.********'
        LINE:
          type: array
          description: List of suppliers and volumes
          items:
            $ref: '#/components/schemas/OtcLineItem'
    AccountDocumentType:
      type: string
      enum:
        - accountStatementSMEIP
        - accountStatementSMEIPKz
        - accountAvailabilitySMEIP
        - availableBalanceSMEIP
      description: Тип (шаблон) документа для счета SME
    AccountDocumentResponse:
      type: object
      properties:
        document:
          $ref: '#/components/schemas/Document'
    ClientVerificationResponse:
      type: object
      required:
        - status
        - verificationResult
      properties:
        status:
          type: string
          enum:
            - IN_PROGRESS
            - DONE
            - ERROR
          description: |
            Статус прохождения проверки:
              * `IN_PROGRESS` – проверка в процессе
              * `DONE` – проверка завершена
              * `ERROR` – ошибка (какой-то из сервисов недоступен или ответил с ошибкой)
          example: DONE
        verificationResult:
          type: string
          enum:
            - APPROVED
            - REJECTED
            - PENDING
          description: |
            Результат проверки (Доступность открытия счёта):
              * `APPROVED` – проверки по клиенту прошли успешно
              * `REJECTED` – проверки не пройдены
              * `PENDING` – проверка в статусе InProgress или Error
          example: APPROVED
        rejectionReason:
          type: string
          nullable: true
          description: |
            Причина отказа (если результат `Rejected`):
              * `IpNotFound`
              * `InactiveTaxpayer`
              * `TaxDebtPresent`
              * `AmlRejection`
              * `null` – если причина отсутствует
          example: TaxDebtPresent
    Currency:
      type: object
      required:
        - currency
      properties:
        currency:
          $ref: '#/components/schemas/CurrencyCode'
    DocumentForAccountOpening:
      type: object
      required:
        - ID
        - type
        - version
        - fileLink
        - title
        - signed
      properties:
        ID:
          type: string
          format: uuid
          description: Идентификатор документа
        title:
          type: string
          example: string
          description: Название документа
        type:
          type: string
          enum:
            - AdditionalAccountOpeningApplicationSMEIP
        version:
          type: integer
          example: 0
          description: Версия документа, которая соотносится с типом документа (версия шаблона)
        fileLink:
          type: string
          example: string
          description: Ссылка на физический файл
        signed:
          type: boolean
          description: Был ли документ подписан пользователем
    DocumentsForSignForAccountOpeningResponse:
      type: object
      required:
        - documents
      properties:
        documents:
          type: array
          description: Список документов
          items:
            $ref: '#/components/schemas/DocumentForAccountOpening'
    OnboardingTextData:
      type: object
      required:
        - code
        - image
        - description
      description: Холдер текстовок
      properties:
        code:
          type: string
          description: Уникальный код текста
          example: '1'
        image:
          type: string
          description: Ссылка в S3 на изображение
          example: https://s3.somelink.com/img.png
        description:
          type: string
          description: Текст для отображения
          example: Лимит до 10 млн Т
    OnboardingTextsResponse:
      type: object
      required:
        - texts
      properties:
        texts:
          type: array
          description: Массив текстов и изображений для onboarding экрана
          items:
            $ref: '#/components/schemas/OnboardingTextData'
    LoanAmount:
      type: object
      required:
        - min
        - max
      description: Сумма кредита
      properties:
        min:
          type: integer
          description: Минимальная сумма кредита
          example: 10000
        max:
          type: integer
          description: Максимальная сумма кредита
          example: 50000000
    Term:
      type: integer
      minimum: 0
      example: 3
      description: Срок кредита (в месяцах)
    LoanTermInterestData:
      type: object
      required:
        - ID
        - term
        - interest
        - defaultInterest
      description: Холдер условий кредита
      properties:
        ID:
          type: string
          description: Идентификатор объекта условий кредита
          example: '1'
        term:
          $ref: '#/components/schemas/Term'
        interest:
          type: integer
          minimum: 0
          example: 30
          description: Процентная ставка кредита
        defaultInterest:
          type: boolean
          description: Признак срока кредита по подсветки по умолчанию
    LoanPurposeData:
      type: object
      required:
        - ID
        - description
        - amount
        - termInterest
      properties:
        ID:
          type: string
          description: Идентификатор цели кредита
          example: '1'
        description:
          type: string
          example: На покупку товара
        amount:
          $ref: '#/components/schemas/LoanAmount'
        termInterest:
          type: array
          description: Массив доступных целей кредита
          items:
            $ref: '#/components/schemas/LoanTermInterestData'
    LoanCalcDataResponse:
      type: object
      properties:
        purpose:
          type: array
          description: Массив доступных условий кредита
          items:
            $ref: '#/components/schemas/LoanPurposeData'
      required:
        - purpose
    CalculationTermData:
      type: object
      required:
        - termInterest
        - monthlyPayment
        - overpayAmount
        - totalAmount
      properties:
        termInterest:
          $ref: '#/components/schemas/LoanTermInterestData'
        monthlyPayment:
          type: integer
          minimum: 0
          example: 363333
          description: Предварительный ежемесячный платеж
        overpayAmount:
          type: integer
          minimum: 0
          example: 89999
          description: Сумма наценки
        totalAmount:
          type: integer
          minimum: 0
          example: 1089999
          description: Общая сумма (сумма кредита + сумма наценки)
    CalculationResultResponse:
      type: object
      required:
        - calculationResult
      properties:
        calculationResult:
          type: array
          description: Массив результатов расчетов платежей
          items:
            $ref: '#/components/schemas/CalculationTermData'
    KatoData:
      type: object
      required:
        - code
        - name
        - ID
        - parentID
      properties:
        code:
          type: string
          description: Код КАТО
        name:
          type: string
          description: Название КАТО
        ID:
          type: integer
          format: int32
          description: Идентификатор КАТО
        parentID:
          type: integer
          format: int32
          description: Идентификатор родительской КАТО
    GetSurveyAddress:
      type: object
      required:
        - isFull
      properties:
        region:
          $ref: '#/components/schemas/KatoData'
        district:
          $ref: '#/components/schemas/KatoData'
        settlementArea:
          $ref: '#/components/schemas/KatoData'
        settlement:
          $ref: '#/components/schemas/KatoData'
        locality:
          $ref: '#/components/schemas/KatoData'
        street:
          type: string
          description: Улица адреса
        building:
          type: string
          description: Номер или название здания
        flat:
          type: string
          nullable: true
          description: Номер квартиры или офиса
        isFull:
          type: boolean
          description: Флаг полностью заполненного адреса
    EducationType:
      type: object
      required:
        - ID
        - name
        - code
      properties:
        ID:
          type: string
          description: Идентификатор уровня образования
        name:
          type: string
          description: Название уровня образования
        code:
          type: string
          description: Код уровня образования
    EmploymentType:
      type: object
      required:
        - ID
        - name
        - code
      properties:
        ID:
          type: string
          description: Идентификатор типа занятости
        name:
          type: string
          description: Название типа занятости
        code:
          type: string
          description: Код типа занятости
    RelationType:
      type: object
      required:
        - ID
        - name
        - code
      properties:
        ID:
          type: string
          description: Идентификатор родства контактного лица
        name:
          type: string
          description: Название родства контактного лица
        code:
          type: string
          description: Код родства контактного лица
    GetSurveyContactPerson:
      type: object
      required:
        - relType
        - firstName
        - lastName
        - phone
      properties:
        relType:
          $ref: '#/components/schemas/RelationType'
        firstName:
          type: string
          description: Имя контактного лица
        lastName:
          type: string
          description: Фамилия контактного лица
        phone:
          $ref: '#/components/schemas/PhoneNumber'
    GetSurveyResult:
      type: object
      properties:
        applicationID:
          type: string
          description: Идентификатор заявки
        children:
          type: integer
          format: int32
          description: Количество детей
        email:
          $ref: '#/components/schemas/Email'
        address:
          $ref: '#/components/schemas/GetSurveyAddress'
        educationType:
          $ref: '#/components/schemas/EducationType'
        empType:
          $ref: '#/components/schemas/EmploymentType'
        contactPersons:
          type: array
          items:
            $ref: '#/components/schemas/GetSurveyContactPerson'
          description: Список контактных лиц
        additionalAmount:
          type: string
          description: Сумма дополнительного источника дохода за последние 6 месяцев
          minLength: 1
    SaveSurveyAddress:
      type: object
      required:
        - katoCodes
        - street
        - building
      properties:
        katoCodes:
          type: array
          items:
            type: string
            minLength: 1
          minItems: 2
          description: Коды КАТО
        street:
          type: string
          minLength: 1
          description: Улица адреса.
        building:
          type: string
          minLength: 1
          description: Номер или название здания.
        flat:
          type: string
          description: Номер квартиры или офиса.
    SaveSurveyContactPerson:
      type: object
      required:
        - relID
        - firstName
        - lastName
        - phone
      properties:
        relID:
          type: string
          minLength: 1
          description: Идентификатор родства контактного лица.
        firstName:
          type: string
          minLength: 1
          description: Имя контактного лица.
        lastName:
          type: string
          minLength: 1
          description: Фамилия контактного лица.
        phone:
          $ref: '#/components/schemas/PhoneNumber'
    SaveSurveyResp:
      type: object
      required:
        - surveyID
      properties:
        surveyID:
          type: string
          description: Идентификатор анкеты пользователя.
    ErrorReason:
      type: object
      required:
        - code
        - title
      properties:
        code:
          type: string
          description: Код ошибки
        message:
          type: string
          description: Описание ошибки
        title:
          type: string
          description: Заголовок ошибки
    LoanApplicationCheckResponse:
      type: object
      required:
        - hasActive
      properties:
        hasActive:
          type: boolean
          description: Признак наличия активной кредитной заявки
        reason:
          $ref: '#/components/schemas/ErrorReason'
    LoanApplicationResponse:
      type: object
      required:
        - applicationID
        - applicationStatus
      properties:
        applicationID:
          type: string
          description: ID заявки
          example: '532'
        applicationStatus:
          type: string
          description: Присвоенный статус заявки
          example: DRAFT
    ExternalBank:
      type: object
      required:
        - ID
        - name
        - instructions
        - logoUrl
      properties:
        ID:
          type: string
          description: Уникальный идентификатор
        name:
          type: string
          description: Наименование банка для отображения на UI клиенту
          example: Kaspi
        instructions:
          type: string
          description: Инструкция для выгрузки выписки из банка в виде списка шагов, т.к. так проще отображать на UI
        logoUrl:
          type: string
          description: Логотип банка (ссылка на изображение)
    BankStatements:
      type: object
      required:
        - banks
        - formats
        - maxFileSize
        - months
      properties:
        banks:
          type: array
          description: Банки для загрузки выписок
          items:
            $ref: '#/components/schemas/ExternalBank'
        formats:
          type: string
          example: PDF
          description: Разрешенные форматы для выписок
        maxFileSize:
          type: number
          example: 10
          description: Максимальное кол-во мегабайт для выписок
        months:
          type: number
          example: 6
          description: Кол-во месяцев, за которые нужна выписка
    GetBankStatementV2Bank:
      type: object
      required:
        - ID
        - name
        - logoUrl
      properties:
        ID:
          type: string
          description: БИК банка
        name:
          type: string
          description: Наименование банка
        instructions:
          type: string
          description: Инструкция для выгрузки выписки из банка
        logoUrl:
          type: string
          description: Логотип банка (ссылка на изображение)
        statementName:
          type: string
          description: Наименование файла выписки (возвращается только при наличии некорректного файла)
        isStatementValid:
          type: boolean
          description: |
            Флаг корректного файла выписки
    GetBankStatementV2Instruction:
      type: object
      description: Инструкция
      required:
        - title
        - description
      properties:
        title:
          type: string
          description: Заголовок инструкции
          example: “Как скачать выписку?”
        description:
          type: string
          description: |
            Описание инструкции
          example: “В приложении любого банка перейдите на страницу с деталями карты, затем выберите «Выписка»”
    GetBankStatementV2StatementHint:
      type: object
      description: Баннер с подсказкой для клиента о преимуществе выписки
      required:
        - title
        - description
        - instruction
      properties:
        title:
          type: string
          description: |
            Заголовок
          example: “Увеличьте вероятность одобрения в 2 раза”
        description:
          type: string
          description: |
            Описание
          example: “Чем больше выписок по карте вы приложите из любых банков, тем выше вероятность одобрения”
        instruction:
          $ref: '#/components/schemas/GetBankStatementV2Instruction'
    GetBankStatementV2Response:
      type: object
      required:
        - banks
        - maxFileSizeMb
        - periodMonths
        - statementHint
      properties:
        banks:
          type: array
          description: Информация по банкам (Справочник банков для загрузки выписки)
          items:
            $ref: '#/components/schemas/GetBankStatementV2Bank'
        maxFileSizeMb:
          type: number
          description: Максимальное количество мегабайт для выписки (из конфига, напр. "10")
        periodMonths:
          type: number
          description: Период (количество месяцев), за который нужна выписка (из конфига, напр. "6")
        statementHint:
          $ref: '#/components/schemas/GetBankStatementV2StatementHint'
    GetInternalChecksResultResp:
      type: object
      required:
        - applicationStatus
        - isInProgress
      properties:
        applicationStatus:
          type: string
          description: Статус заявки на кредит.
        isInProgress:
          type: boolean
          description: Признак наличия в процессе выполнения внутренних проверок
        reason:
          $ref: '#/components/schemas/ErrorReason'
    GetEducationTypesResp:
      type: object
      required:
        - educationTypes
      properties:
        educationTypes:
          type: array
          description: Список справочников по типам образования
          items:
            $ref: '#/components/schemas/EducationType'
    GetEmploymentTypesResp:
      type: object
      required:
        - employmentTypes
      properties:
        employmentTypes:
          type: array
          description: Список справочников по типам занятости
          items:
            $ref: '#/components/schemas/EmploymentType'
    GetRelationTypesResp:
      type: object
      required:
        - relationTypes
      properties:
        relationTypes:
          type: array
          description: Список справочников по видам отношений к контактным лицам
          items:
            $ref: '#/components/schemas/RelationType'
    CancelLoanApplicationResp:
      type: object
      required:
        - applicationID
      properties:
        applicationID:
          type: string
          description: Уникальный идентификатор заявки на кредит
    LoanAppDocumentType:
      type: string
      enum:
        - complexConsentClientSMEIP
      description: Тип (шаблон) документа
    LoanApplicationDocument:
      type: object
      required:
        - ID
        - type
        - title
        - version
        - fileLink
        - signed
      properties:
        ID:
          type: string
          format: uuid
          description: Идентификатор документа
        title:
          type: string
          description: Название документа
        type:
          $ref: '#/components/schemas/LoanAppDocumentType'
        version:
          type: integer
          description: Версия документа, которая соотносится с типом документа (версия шаблона)
        fileLink:
          type: string
          description: Ссылка на физический файл
        signed:
          type: boolean
          description: Был ли документ подписан пользователем
    LoanApplicationDocumentsResp:
      type: object
      required:
        - documents
      properties:
        documents:
          type: array
          description: Документы сгенерированные по запросу
          items:
            $ref: '#/components/schemas/LoanApplicationDocument'
    GetApprovedLoanAppStatusResp:
      type: object
      required:
        - applicationStatus
        - isInProgress
      properties:
        applicationStatus:
          type: string
          description: Статус заявки на кредит.
        nextStep:
          type: string
          description: Следующий шаг.
        isInProgress:
          type: boolean
          description: Признак наличия в процессе выполнения внутренних проверок
        reason:
          $ref: '#/components/schemas/ErrorReason'
    AttachedDocData:
      type: object
      required:
        - bankID
        - docID
      properties:
        bankID:
          type: string
          description: Идентификатор банка, согласно справочнику
        docID:
          type: string
          description: Идентификатор документа из временного хранилища
    PublishLoanAppResp:
      type: object
      required:
        - applicationStatus
      properties:
        applicationStatus:
          type: string
          description: Статус заявки на кредит
    LoanAppConditionsAmountData:
      type: object
      description: Сумма финансирования, одобренная СПР
      required:
        - value
        - currencyCode
      properties:
        value:
          type: string
          example: '100000'
          description: Сумма финансирования
        currencyCode:
          type: string
          description: Валюта
          example: KZT
    NextPaymentAmountData:
      type: object
      description: Ежемесячный платеж
      required:
        - value
        - currencyCode
      properties:
        value:
          type: string
          example: '100000'
          description: Сумма ежемесячного платежа
        currencyCode:
          type: string
          description: Валюта
          example: KZT
    InterestAmountData:
      type: object
      description: Наценка
      required:
        - value
        - currencyCode
      properties:
        value:
          type: string
          example: '100000'
          description: Сумма наценки
        currencyCode:
          type: string
          description: Валюта
          example: KZT
    ScoringLoanConditions:
      type: object
      required:
        - title
        - hint
        - amount
        - term
        - nextPaymentAmount
        - interestAmount
      properties:
        title:
          type: string
          description: Заголовок экрана
        hint:
          type: string
          description: Текст под условиями предложения
        amount:
          $ref: '#/components/schemas/LoanAppConditionsAmountData'
        term:
          $ref: '#/components/schemas/Term'
        nextPaymentAmount:
          $ref: '#/components/schemas/NextPaymentAmountData'
        interestAmount:
          $ref: '#/components/schemas/InterestAmountData'
    RefinancingConditions:
      type: object
      properties:
        ID:
          type: string
        title:
          type: string
        totalAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Общая сумма
        refAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма на погашение кредитов в других банках
        creditAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма зачисления (полученная клиентом на руки)
        paymentAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Ежемесячный платеж
        interestAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Наценка
        term:
          $ref: '#/components/schemas/Term'
      required:
        - ID
        - title
        - totalAmount
        - refAmount
        - creditAmount
        - paymentAmount
        - interestAmount
        - term
    ExternalBankLoan:
      type: object
      properties:
        ID:
          type: string
        bankName:
          type: string
          description: Наименование банка
        bankBIN:
          type: string
          description: БИН банка
        outstandingAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма задолженности по одному банку
        paymentAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Ежемесячный платеж по одному банку
        contractNumber:
          type: string
          description: Номер договора
        contractDate:
          type: string
          format: date-time
          description: Дата договора
        image:
          type: string
          description: Ссылка на изображение
      required:
        - ID
        - bankName
        - bankBIN
        - outstandingAmount
        - paymentAmount
        - contractNumber
        - contractDate
        - image
    GetScoringResultRespExternalBankLoansInfo:
      type: object
      required:
        - title
        - totalPaymentAmount
        - items
      properties:
        title:
          type: string
        totalPaymentAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Общая сумма ежемесячного платежа при рефинансировании
        items:
          type: array
          description: Блок с информацией по каждому займу
          items:
            $ref: '#/components/schemas/ExternalBankLoan'
    GetScoringResultRespRefinancingInfo:
      type: object
      properties:
        conditions:
          $ref: '#/components/schemas/RefinancingConditions'
        externalBankLoansInfo:
          $ref: '#/components/schemas/GetScoringResultRespExternalBankLoansInfo'
    ScoringResultResp:
      type: object
      required:
        - applicationStatus
      properties:
        applicationStatus:
          type: string
          description: Статус заявки на кредит
        productType:
          type: string
          description: Тип продукта (LOAN/REFINANCING)
          example: LOAN
        reason:
          $ref: '#/components/schemas/ErrorReason'
        loanConditions:
          $ref: '#/components/schemas/ScoringLoanConditions'
        refinancing:
          $ref: '#/components/schemas/GetScoringResultRespRefinancingInfo'
    BtsDataResp:
      type: object
      required:
        - link
        - redirectURI
      properties:
        link:
          type: string
          description: Ссылка для прохождения идентификации
        redirectURI:
          type: string
          description: Ссылка для редиректа
    PostIdentifyBtsDataSmeResp:
      type: object
      required:
        - applicationStatus
      properties:
        applicationStatus:
          type: string
          description: Статус заявки на кредит
        reason:
          $ref: '#/components/schemas/ErrorReason'
    LoanAppAmountData:
      type: object
      description: Объект суммы кредита
      required:
        - value
        - currencyCode
      properties:
        value:
          type: string
          example: '100000'
          description: Сумма кредита
        currencyCode:
          type: string
          description: Валюта кредита
          example: KZT
    PostEdsBtsDataResp:
      type: object
      required:
        - title
        - amount
        - term
        - subtitle
      properties:
        title:
          type: string
          description: Заголовок сообщения пользователю
        amount:
          $ref: '#/components/schemas/LoanAppAmountData'
        term:
          type: string
          description: Текст по сроку кредита
        subtitle:
          type: string
          description: Подзаголовок сообщения пользователю
    PaymentHintData:
      type: object
      description: Подсказка в карточке кредита
      required:
        - title
        - text
        - infoUrl
      properties:
        title:
          type: string
          description: Заголовок подсказки
          example: string
        text:
          type: string
          description: Текст подсказки
          example: string
        infoUrl:
          type: string
          description: URL сайта банка с информацией по способам внесения платежей
          example: string
    PaidInfo:
      type: object
      description: Информация для виджета
      required:
        - percentPaid
        - remainingAmount
        - text
      properties:
        percentPaid:
          type: string
          description: Поле для заполнения виджета оставшейся суммы выплаты по кредиту
        remainingAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Оставшаяся сумма выплаты по кредиту
        text:
          type: string
          description: Текст под виджетом оставшейся суммы выплаты по кредиту
    NextPayment:
      type: object
      description: Информация о ближайшем платеже
      required:
        - date
        - amount
      properties:
        date:
          type: string
          format: date
          description: Дата ближайшего платежа
        amount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма ближайшего платежа
    Account:
      type: object
      description: Информация по счету
      required:
        - ibanLastDigits
        - amount
        - iban
        - iin
        - fullName
        - bankName
        - bankBin
        - bankBic
      properties:
        ibanLastDigits:
          type: string
          description: Замаскированный номер счета списания (отображать только 4 последние символа)
        amount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Баланс счета
        iban:
          type: string
          description: Счет для погашения (IBAN)
        iin:
          type: string
          description: ИИН клиента
        fullName:
          type: string
          description: ФИО клиента
        bankName:
          type: string
          description: Наименование банка
        bankBin:
          type: string
          description: БИН банка
        bankBic:
          type: string
          description: БИК банка
    LoanDetailsContract:
      type: object
      description: Подписанный договор
      required:
        - number
        - title
        - docID
        - fileLink
      properties:
        number:
          type: string
          description: Номер кредитного договора
        title:
          type: string
          description: Заголовок для документа
        docID:
          type: string
          description: ID подписанного договора
        fileLink:
          type: string
          description: Ссылка на подписанный договор, хранящийся в S3 в общей папке
    LoanDetailsObject:
      type: object
      description: Детали финансирования
      required:
        - contract
        - loanAmount
        - startDate
        - endDate
        - term
        - interestAmount
      properties:
        contract:
          $ref: '#/components/schemas/LoanDetailsContract'
        loanAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма финансирования (на весь срок)
        startDate:
          type: string
          format: date
          description: Дата начала договора
        endDate:
          type: string
          format: date
          description: Дата окончания договора
        term:
          $ref: '#/components/schemas/Term'
        interestAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма наценки на весь срок финансирования
    Overdue:
      type: object
      description: Просроченный платеж
      required:
        - hint
      properties:
        hint:
          type: string
          description: Подсказка по просроченному платежу
        days:
          type: integer
          description: Количество дней просрочки
        fineDebt:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма пени
    PaymentDetails:
      type: object
      description: Детали платежа
      required:
        - date
        - baseAmount
        - interestAmount
      properties:
        date:
          type: string
          format: date
          description: Дата платежа
        baseAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма финансирования (за 1 месяц)
        interestAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма наценки (заполняется в зависимости от наличия просрочки)
        overdue:
          $ref: '#/components/schemas/Overdue'
    ScheduleItem:
      type: object
      description: График погашения
      required:
        - paymentAmount
        - date
        - status
        - details
      properties:
        paymentAmount:
          allOf:
            - $ref: '#/components/schemas/Money'
          description: Сумма платежа (Сумма финансирования (baseAmount) + Наценка (interestAmount))
        date:
          type: string
          format: date
          description: Дата платежа
        status:
          type: string
          description: Статус платежа PLAN - К оплате END - Погашен OVERDUE - Просрочен
          enum:
            - PLAN
            - END
            - OVERDUE
        details:
          $ref: '#/components/schemas/PaymentDetails'
    LoanDetails:
      type: object
      description: Информация по кредиту
      required:
        - hasOverdue
        - productType
        - paidInfo
        - nextPayment
        - account
        - details
        - schedule
      properties:
        hasOverdue:
          type: boolean
        productType:
          type: string
          description: Тип продукта LOAN - Кредит REFINANCE - Рефинансирование
          enum:
            - LOAN
            - REFINANCE
        paidInfo:
          $ref: '#/components/schemas/PaidInfo'
        nextPayment:
          $ref: '#/components/schemas/NextPayment'
        account:
          $ref: '#/components/schemas/Account'
        details:
          $ref: '#/components/schemas/LoanDetailsObject'
        schedule:
          type: array
          description: График погашения
          items:
            $ref: '#/components/schemas/ScheduleItem'
    LinkObject:
      type: object
      required:
        - title
        - url
      properties:
        title:
          type: string
          description: Титры
        url:
          type: string
          description: Url
    Links:
      type: object
      description: Блок с URL
      required:
        - earlyRepayment
        - inquiry
        - faq
      properties:
        earlyRepayment:
          allOf:
            - $ref: '#/components/schemas/LinkObject'
          description: URL сайта банка с информацией по досрочному погашению
        inquiry:
          allOf:
            - $ref: '#/components/schemas/LinkObject'
          description: URL сайта банка с информацией по справке
        faq:
          allOf:
            - $ref: '#/components/schemas/LinkObject'
          description: URL сайта банка с информацией по вопросам и ответам
    GetLoansDetailsEarlyRepaymentAllDebtToPay:
      type: object
      required:
        - label
        - amount
      properties:
        label:
          type: string
        amount:
          $ref: '#/components/schemas/Money'
    GetLoansDetailsEarlyRepaymentPartialRepaymentLimit:
      type: object
      required:
        - hint
        - minAmount
        - maxAmount
      properties:
        hint:
          type: string
        minAmount:
          $ref: '#/components/schemas/Money'
        maxAmount:
          $ref: '#/components/schemas/Money'
    GetLoansDetailsEarlyRepaymentPartialRepaymentNextPayment:
      type: object
      required:
        - hint
      properties:
        hint:
          type: string
    GetLoansDetailsEarlyRepaymentPartialRepayment:
      type: object
      required:
        - limit
        - nextPayment
      properties:
        limit:
          $ref: '#/components/schemas/GetLoansDetailsEarlyRepaymentPartialRepaymentLimit'
        nextPayment:
          $ref: '#/components/schemas/GetLoansDetailsEarlyRepaymentPartialRepaymentNextPayment'
    GetLoansDetailsEarlyRepayment:
      type: object
      required:
        - isAvailable
      properties:
        isAvailable:
          type: boolean
        reason:
          $ref: '#/components/schemas/ErrorReason'
        allDebtToPay:
          $ref: '#/components/schemas/GetLoansDetailsEarlyRepaymentAllDebtToPay'
        partialRepayment:
          $ref: '#/components/schemas/GetLoansDetailsEarlyRepaymentPartialRepayment'
    GetLoansDetailsResponse:
      type: object
      required:
        - paymentHint
        - loan
        - links
        - earlyRepayment
      properties:
        paymentHint:
          $ref: '#/components/schemas/PaymentHintData'
        loan:
          $ref: '#/components/schemas/LoanDetails'
        links:
          $ref: '#/components/schemas/Links'
        earlyRepayment:
          $ref: '#/components/schemas/GetLoansDetailsEarlyRepayment'
    EarlyRepayAmount:
      type: object
      description: Сумма ЧДП/ПДП
      required:
        - value
        - currencyCode
      properties:
        value:
          type: string
          description: Сумма досрочного погашения
          example: '10000'
        currencyCode:
          type: string
          description: Валюта
          example: KZT
    EarlyRepayContract:
      type: object
      description: Оплата финансирования
      required:
        - number
        - title
      properties:
        number:
          type: string
          description: Номер кредитного договора
        title:
          type: string
          description: Заголовок для документа
          example: Договор финансирования
    PostEarlyRepayResp:
      type: object
      required:
        - repayAmount
      properties:
        repayAmount:
          $ref: '#/components/schemas/EarlyRepayAmount'
        contract:
          $ref: '#/components/schemas/EarlyRepayContract'
    DisbursementMode:
      type: string
      enum:
        - loanDisbursementAutoMode
        - loanDisbursementManualMode
        - loanDisbursementAutoModeV2
      description: Режим работы биржи
    PostChangeDisbursementControlResponse:
      type: object
      required:
        - mode
      properties:
        mode:
          type: string
          description: Активированные режим работы биржи
    Filter:
      required:
        - operation
      type: object
      properties:
        field:
          type: string
          description: Поле данных
          example: name, data.fieldName, data.fieldName1.fieldName2
        operation:
          type: string
          description: Операция
          example: '=, ==, >, >=, <, <=, like, ilike, in'
        value:
          type: string
          description: Значение
          example: string
        not:
          type: boolean
          description: Флаг использования обратного условия фильтра
          example: false
        group:
          type: array
          items:
            $ref: '#/components/schemas/Filter'
    DictDocumentFilters:
      required:
        - dict_id
        - filters
      type: object
      properties:
        dict_id:
          type: string
          description: ID или имя словаря
        filters:
          type: array
          items:
            $ref: '#/components/schemas/Filter'
        sort:
          type: array
          items:
            type: string
        pagination:
          required:
            - page
            - count
          type: object
          properties:
            page:
              type: integer
              format: int32
              default: 0
              description: Номер страницы начиная с 0
            count:
              type: integer
              format: int32
              default: 1000000
              description: Размер страницы
    DictDocument:
      required:
        - name
        - dict_id
        - data
        - order_num
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Идентификатор документа справочника
        dict_id:
          type: string
          format: uuid
          description: Идентификатор справочника к которому относится документ
        name:
          type: string
          description: Имя документа справочника (не обязательно)
        data:
          type: object
          description: Данные документа справочника в виде объекта JSON (зависит от структуры справочника)
        valid:
          type: boolean
          description: Соответствует-ли документ настройкам схемы для данного словаря
        order_num:
          type: integer
          default: 0
          description: Поле для настройки ручной сортировки при необходимости
    DictDocumentList:
      required:
        - list
      type: object
      properties:
        list:
          type: array
          description: Список документов справочника
          items:
            $ref: '#/components/schemas/DictDocument'
    Location:
      required:
        - ID
        - name
        - parentID
        - code
      type: object
      properties:
        ID:
          type: integer
          format: int32
          description: Идентификатор локации
        name:
          type: string
          description: Название локации
        parentID:
          type: integer
          format: int32
          description: Идентификатор родительской локации
        code:
          type: string
          description: Код локации
    GetLocationsResp:
      required:
        - locations
      type: object
      properties:
        locations:
          type: array
          description: Список локаций
          items:
            $ref: '#/components/schemas/Location'
    PaymentsOperationType:
      type: string
      enum:
        - CREDIT
        - DEBIT
      description: Тип операции
    PaymentsTransactionStatus:
      type: string
      enum:
        - INITIALIZED
        - IN_PROGRESS
        - COMPLETED
        - REJECTED
      description: Статус транзакции
    TransactionType:
      type: string
      enum:
        - OTHER
        - PAYMENT_BY_ACCOUNT
        - PAYMENT_MOBILE
        - PAYMENT_TERMINAL
      description: Внутренний тип перевода
    PaymentsTransaction:
      type: object
      required:
        - transactionID
        - accountNumber
        - amount
        - currency
        - direction
        - status
        - transactionDate
        - transactionType
      properties:
        transactionID:
          type: string
          description: Уникальный идентификатор транзакции
        accountNumber:
          type: string
          description: Номер cчёта
        amount:
          type: number
          format: double
          description: Сумма транзакции
          example: 99.95
        currency:
          type: string
          description: Валюта транзакции
        direction:
          $ref: '#/components/schemas/PaymentsOperationType'
          description: Тип операции (кредит/дебет)
        status:
          $ref: '#/components/schemas/PaymentsTransactionStatus'
          description: Статус транзакции
        transactionDate:
          type: string
          format: date
          description: Дата операции
        transactionType:
          $ref: '#/components/schemas/TransactionType'
          description: Тип транзакции
        category:
          type: string
          description: Категория операции
        logoUrl:
          type: string
          description: URL изображения логотипа контрагента
    PaymentsGetTransactionsResponse:
      type: object
      required:
        - transactions
        - totalCount
        - limit
        - offset
        - startDate
      properties:
        transactions:
          type: array
          items:
            $ref: '#/components/schemas/PaymentsTransaction'
          description: Список транзакций
        totalCount:
          type: integer
          format: int64
          description: Общее количество транзакций
        limit:
          type: integer
          format: int64
          description: Лимит количества возвращаемых транзакций
        offset:
          type: integer
          format: int64
          description: Смещение для пагинации
        startDate:
          type: string
          format: date-time
          description: Начальная дата для фильтрации транзакций
    PaymentsGetTransactionByIDResponse:
      type: object
      properties:
        transactionID:
          type: string
          description: Идентификатор транзакции
        transactionNumber:
          type: string
          description: Пользовательский номер транзакции
        transactionDate:
          type: string
          format: date-time
          description: Дата операции
        valueDate:
          type: string
          format: date-time
          description: Дата исполнения операции
        transactionType:
          $ref: '#/components/schemas/TransactionType'
          description: Тип транзакции
        direction:
          $ref: '#/components/schemas/PaymentsOperationType'
          description: Тип операции (кредит/дебет)
        status:
          $ref: '#/components/schemas/PaymentsTransactionStatus'
          description: Статус транзакции
        amount:
          type: number
          format: double
          description: Сумма операции
          example: 99.95
        commission:
          type: number
          format: double
          description: Коммиссия
          example: 1.95
        currency:
          type: string
          description: Валюта операции
          example: KZT
        payerIinBin:
          type: string
          description: ИИН/БИН отправителя
        payerName:
          type: string
          description: Наименование отправителя
        payerAccount:
          type: string
          description: Номер счёта отправителя
        beneficiaryIinBin:
          type: string
          description: ИИН/БИН получателя
        beneficiaryName:
          type: string
          description: Наименование получателя
        beneficiaryAccount:
          type: string
          description: Номер счёта получателя
        kbe:
          type: string
          description: Код бенефициара
        phoneNumber:
          type: string
          description: Номер телефона
        mobileOperator:
          type: string
          description: Мобильный оператор
        transactionDetails:
          type: string
          description: Детали транзакции
        knp:
          type: string
          description: Код назначения платежа (КНП)
      required:
        - transactionID
        - transactionNumber
        - transactionDate
        - transactionType
        - direction
        - status
        - amount
        - commission
        - currency
        - transactionDetails
        - knp
    PaymentsGetTransactionReceiptResponse:
      type: object
      properties:
        title:
          type: string
          description: Наименование документа
        fileLink:
          type: string
          description: Ссылка на файл
      required:
        - title
        - fileLink
    PaymentsGetHistoryItem:
      type: object
      required:
        - clientIinBin
        - clientName
        - clientAccount
        - clientBankName
        - clientBankBic
        - counterpartyIinBin
        - counterpartyName
        - counterpartyAccount
        - counterpartyBankName
        - counterpartyBankBic
        - counterpartyKbe
        - amount
        - currency
        - knp
        - paymentDetails
        - date
      properties:
        clientIinBin:
          type: string
          description: ИИН/БИН клиента
        clientName:
          type: string
          description: Наименование клиента
        clientAccount:
          type: string
          description: Номер счёта клиента
        clientBankName:
          type: string
          description: Наименование банка клиента
        clientBankBic:
          type: string
          description: БИК банка
        counterpartyIinBin:
          type: string
          description: ИИН/БИН контрагента
        counterpartyName:
          type: string
          description: Название контрагента
        counterpartyAccount:
          type: string
          description: Счёт контрагента
        counterpartyBankName:
          type: string
          description: Название банка контрагента
        counterpartyBankBic:
          type: string
          description: БИК банка контрагента
        counterpartyKbe:
          type: string
          description: КБЕ контрагента
        amount:
          type: number
          format: double
          description: Сумма платежа
        currency:
          type: string
          description: Валюта платежа
        knp:
          type: string
          description: Код назначения платежа (КНП)
        paymentDetails:
          type: string
          description: Детали платежа
        date:
          type: string
          format: date
          description: Дата платежа
        valueDate:
          type: string
          format: date
          description: Дата исполнения операции
        actualClientIinBin:
          type: string
          description: ИИН/БИН фактического клиента (опционально)
        actualClientName:
          type: string
          description: Имя фактического клиента (опционально)
        actualClientCountry:
          type: string
          description: Страна фактического клиента (опционально)
        actualCounterpartyIinBin:
          type: string
          description: ИИН/БИН фактического контрагента (опционально)
        actualCounterpartyName:
          type: string
          description: Имя фактического контрагента (опционально)
        actualCounterpartyCountry:
          type: string
          description: Страна фактического контрагента (опционально)
    PaymentsGetHistoryResponse:
      type: object
      properties:
        payments:
          type: array
          items:
            $ref: '#/components/schemas/PaymentsGetHistoryItem'
          description: Список элементов истории платежей
    TaxPayerType:
      type: string
      enum:
        - INDIVIDUAL
        - LEGAL_ENTITY
      description: Тип налогоплательщика
    AdditionalIndividualType:
      type: object
      description: Дополнительный тип для ФЛ если есть
      required:
        - type
        - name
      properties:
        type:
          type: integer
          description: Список кодов 32 - ИП, 64 — адвокат, 128 — частный нотариус, 256 — частный судебный исполнитель
        name:
          type: string
          description: Наименование дополнительного бенефициара для данного доп.  типа ФЛ
    PaymentsCheckAccountIinResponse:
      type: object
      required:
        - name
        - taxPayerType
        - bankBic
        - bankName
      properties:
        name:
          type: string
          description: Наименование ЮЛ/ФЛ
        taxPayerType:
          $ref: '#/components/schemas/TaxPayerType'
          description: Тип физ лица ЮЛ/ФЛ
        additionalIndividualType:
          $ref: '#/components/schemas/AdditionalIndividualType'
        bankBic:
          type: string
          description: БИК банка
        bankName:
          type: string
          description: Название банка
    OtpFullResponse:
      type: object
      properties:
        attemptID:
          type: string
          format: uuid
          description: Идентификатор для валидации отп кода
          example: 2100e82d-e288-4882-8fac-3f1403449051
        retryTime:
          type: integer
          description: Количество секунд до следующей отправки
          example: 60
        codeChecksLeft:
          type: integer
          description: Количество оставшихся попыток проверки кода отп
        attemptsLeft:
          type: integer
          description: Количество оставшихся попыток
        attemptsTimeout:
          type: integer
          description: Количество секунд жизни попытки отп валидации
      required:
        - attemptID
        - retryTime
        - codeChecksLeft
        - attemptsLeft
        - attemptsTimeout
    CreatePaymentByAccountResponse:
      type: object
      required:
        - status
        - message
        - transactionID
        - otpRequired
      properties:
        status:
          type: string
          description: Статус операции
          example: success
        message:
          type: string
          description: Информация из бэк по статусу
          example: Платеж в обработке
        otpRequired:
          type: boolean
          description: Признак нужно ли проводить проверки по отп
          example: true
        transactionID:
          type: string
          format: uuid
          description: Идентификатор транзакции в БД
          example: 41c3f02f-436d-4f9b-8657-694f4c8890f8
        otpResponse:
          $ref: '#/components/schemas/OtpFullResponse'
    ConfirmPaymentByAccountResponse:
      type: object
      required:
        - status
        - message
      properties:
        status:
          type: string
          description: Статус операции
        message:
          type: string
          description: Информация из бэк по статусу
    ClientResponse:
      type: object
      properties:
        iin:
          type: string
          description: ИИН клиента
        name:
          type: string
          description: Имя клиента
        surname:
          type: string
          description: Фамилия клиента
        patronymic:
          type: string
          description: Отчество клиента
        birthdate:
          type: string
          description: Дата рождения клиента
        enterpriseName:
          type: string
          description: Название ИП клиента
        enterpriseAddressKATOCode:
          type: string
          description: Код КАТО адреса регистрации ИП
        enterpriseAddressKATOId:
          type: string
          description: Номер КАТО адреса регистрации ИП
      required:
        - iin
        - name
        - surname
        - birthdate
    EmployeeItem:
      type: object
      description: Информация о сотруднике для платежа ОПВ
      required:
        - name
        - lastName
        - iin
        - birthday
        - country
        - amount
        - valuePeriod
      properties:
        name:
          type: string
          description: Имя сотрудника
          example: Айдар
        middleName:
          type: string
          description: Отчество сотрудника
          example: Асылбекович
        lastName:
          type: string
          description: Фамилия сотрудника
          example: Нурманов
        iin:
          type: string
          description: ИИН сотрудника
          pattern: ^[0-9]{12}$
          example: '************'
        birthday:
          type: string
          format: date
          description: Дата рождения сотрудника
          example: '1990-01-15'
        country:
          type: string
          description: Страна
          example: KZ
        amount:
          type: string
          description: Сумма взноса для данного сотрудника
          pattern: ^\d+\.\d{2}$
          example: '5000.00'
        valuePeriod:
          type: string
          description: Период взноса (YYYYMM)
          pattern: ^[0-9]{6}$
          example: '202412'
    PaymentData:
      type: object
      description: Данные для платежа ОПВ (Обязательные пенсионные взносы)
      required:
        - paymentPeriod
        - purposeCode
        - purposeDetails
        - amount
        - payerAccount
        - employees
        - signatoryA
      properties:
        paymentPeriod:
          type: string
          description: Период платежа (MMYYYY)
          pattern: ^[0-9]{6}$
          example: '122024'
        purposeCode:
          type: string
          description: Код назначения платежа
          example: '001'
        purposeDetails:
          type: string
          description: Детали назначения платежа
          example: Обязательные пенсионные взносы за декабрь 2024
        amount:
          type: string
          description: Общая сумма платежа
          pattern: ^\d+\.\d{2}$
          example: '15000.00'
        payerAccount:
          type: string
          description: Счет плательщика
          pattern: ^KZ[A-Z0-9]{18}$
          example: ********************
        employees:
          type: array
          description: Список сотрудников
          items:
            $ref: '#/components/schemas/EmployeeItem'
        signatoryA:
          type: string
          description: Подпись ответственного лица (ФИО)
          example: Токаев Касым-Жомарт Кемельевич
        beneficiaryBINIIN:
          type: string
          description: БИН/ИИН получателя платежа
          pattern: ^[0-9]{12}$
          example: '************'
        beneficiaryName:
          type: string
          description: Наименование получателя платежа
          example: АО 'Единый накопительный пенсионный фонд'
        realBeneficiaryName:
          type: string
          description: ФИО фактического получателя
          example: АО 'ЕНПФ'
        realBeneficiaryBINIIN:
          type: string
          description: ИИН фактического получателя
          pattern: ^[0-9]{12}$
          example: '************'
        realBeneficiaryCountry:
          type: string
          description: Страна резидентства фактического получателя
          example: KZ
        kbk:
          type: string
          description: Код Бюджетной Классификации
          example: '101202'
    CreatePaymentResponse:
      type: object
      required:
        - status
        - otpNeeded
      properties:
        transactionID:
          type: string
          format: uuid
          description: Идентификатор созданной транзакции (опциональное поле - может отсутствовать при ошибках валидации)
          example: 550e8400-e29b-41d4-a716-************
        status:
          type: string
          description: Статус создания платежа
          enum:
            - INITIALIZED
            - REJECTED
            - IN_PROGRESS
            - COMPLETED
          example: INITIALIZED
        reasonCode:
          type: string
          description: Код состояния платежа
          enum:
            - '1005'
            - '1013'
            - '1041'
            - '1042'
            - '1040'
          example: '1005'
        reason:
          type: string
          description: Описание причины состояния платежа
          enum:
            - Validation error
            - Worktime exceeded
            - Insufficient funds
            - No active account
            - Unable to process payment
          example: Worktime exceeded
        otpNeeded:
          type: boolean
          description: Необходимость подтвердить платёж OTP
          example: false
    ConfirmPaymentSmeResponse:
      type: object
      properties:
        status:
          type: string
          description: Статус платежа
        reasonCode:
          type: string
          description: Код состояния платежа
        reason:
          type: string
          description: Описание причины состояния платежа
        otpNeeded:
          type: boolean
          description: Необходимость подтвердить платёж OTP
      required:
        - status
    SmePaymentsGetPaymentOrderResponse:
      type: object
      required:
        - title
        - link
        - version
      properties:
        title:
          type: string
          description: Название документа
        link:
          type: string
          description: Ссылка на файл документа
        version:
          type: string
          description: Версия документа
    SmePaymentsGetPaymentOrderByTrNumberResponse:
      type: object
      required:
        - id
        - title
        - link
        - version
        - transactionStatus
      properties:
        id:
          type: string
          description: Идентификатор транзакции
        title:
          type: string
          description: Название документа
        link:
          type: string
          description: Ссылка на файл документа
        version:
          type: integer
          description: Версия документа
        transactionStatus:
          type: string
          description: Статус транзакции
          enum:
            - INITIALIZED
            - REJECTED
            - IN_PROGRESS
            - COMPLETED
    KbeKodCode:
      type: object
      properties:
        value:
          type: string
          description: Код Кбе/КОд
        name:
          type: string
          description: Наименование Кбе/КОд на языке локализации
        residency:
          type: boolean
          description: Признак резидента
    KbeKodItem:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/KbeKodCode'
    GetKbeKodListResp:
      type: object
      properties:
        codes:
          type: array
          description: Список Кбе/КОд кодов
          items:
            $ref: '#/components/schemas/KbeKodItem'
    KnpCode:
      type: object
      properties:
        value:
          type: string
          description: Код КНП
        name:
          type: string
          description: Наименование КНП на языке локализации
    KnpItem:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/KnpCode'
    GetKnpListResp:
      type: object
      properties:
        codes:
          type: array
          description: Список КНП
          items:
            $ref: '#/components/schemas/KnpItem'
    Bank:
      type: object
      properties:
        code:
          type: string
          description: Код банка
        bic:
          type: string
          description: БИК банка
        name:
          type: string
          description: Наименование банка на языке локализации
    BankItem:
      type: object
      properties:
        bank:
          $ref: '#/components/schemas/Bank'
    GetBankListResp:
      type: object
      properties:
        banks:
          type: array
          description: Список банков
          items:
            $ref: '#/components/schemas/BankItem'
    TaxAuthorityUgd:
      type: object
      properties:
        code:
          type: string
          description: Код УГД
        bin:
          type: string
          description: БИН УГД
        regionCode:
          type: string
          description: Код региона
        regionName:
          type: string
          description: Название региона
        name:
          type: string
          description: Наименование УГД на языке локализации
    TaxAuthorityItem:
      type: object
      properties:
        ugd:
          $ref: '#/components/schemas/TaxAuthorityUgd'
    GetTaxAuthorityListResp:
      type: object
      properties:
        ugds:
          type: array
          description: Список УГД
          items:
            $ref: '#/components/schemas/TaxAuthorityItem'
    KbkCode:
      type: object
      properties:
        value:
          type: string
          description: Код КБК
        name:
          type: string
          description: Наименование КБК на языке локализации
    KbkItem:
      type: object
      properties:
        code:
          $ref: '#/components/schemas/KbkCode'
    GetKbkListResp:
      type: object
      properties:
        codes:
          type: array
          description: Список КБК
          items:
            $ref: '#/components/schemas/KbkItem'
    SmePaymentsDeviceInfo:
      type: object
      properties:
        appVersion:
          type: string
          description: Версия МП
        deviceModel:
          type: string
          description: Модель устройства
        installationId:
          type: string
          description: Идентификатор установки
        systemType:
          type: string
          description: ОС устройства
        systemVersion:
          type: string
          description: Версия ОС
    SmePaymentsCreateOtpResponse:
      type: object
      properties:
        attemptId:
          type: string
          description: Идентификатор попытки подписи
        codeTtl:
          type: string
          description: Время жизни OTP
        codeChecksLeft:
          type: string
          description: Количество попыток ввода кода
        attemptsLeft:
          type: string
          description: Количество попыток перегенерации кода
        attemptsTimeout:
          type: string
          description: Время между генерациями кода
        newAttemptDelay:
          type: string
          description: Задержка перед следующей перегенерацией
        error:
          type: string
          description: Описание ошибки
    SmePaymentsOtpValidateResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Успешность проверки кода
        error:
          type: string
          description: Описание ошибки
    SmePaymentsWorktimeResponse:
      type: object
      properties:
        date:
          type: string
          description: Текущая дата в системах банка (GMT+5 Astana)
        isDateOperational:
          type: boolean
          description: Открыт опердень или нет
        nextOperationalDate:
          type: string
          description: Следующий рабочий день
        isEndOfWorkTime:
          type: boolean
          description: Проверка на опер день после рабочего времени
      required:
        - date
        - isDateOperational
        - isEndOfWorkTime
    EmployeeInfo:
      type: object
      description: Информация о сотруднике ИП
      required:
        - id
        - name
        - lastName
        - iin
        - birthday
        - country
        - displayOrder
      properties:
        id:
          type: string
          description: Идентификатор записи о сотруднике
        name:
          type: string
          description: Имя сотрудника
          example: Айдар
        middleName:
          type: string
          description: Отчество сотрудника
          example: Асылбекович
        lastName:
          type: string
          description: Фамилия сотрудника
          example: Нурманов
        iin:
          type: string
          description: ИИН сотрудника
          pattern: ^[0-9]{12}$
          example: '************'
        employerIinBin:
          type: string
          description: ИИН/БИН организации
          pattern: ^[0-9]{12}$
          example: '************'
        birthday:
          type: string
          format: date
          description: Дата рождения сотрудника
          example: '1990-01-15'
        country:
          type: string
          description: Страна
          example: KZ
        displayOrder:
          type: integer
          description: Порядок отображения в списке
    SmePaymentsGetEmployeeListResponse:
      type: object
      required:
        - employees
      properties:
        employees:
          type: array
          description: Список сотрудников
          items:
            $ref: '#/components/schemas/EmployeeInfo'
    SmePaymentsDeleteEmployeeListResponse:
      type: object
      required:
        - employees
      properties:
        employees:
          type: array
          description: Список сотрудников
          items:
            $ref: '#/components/schemas/EmployeeInfo'
    UserAccountArrest:
      type: object
      properties:
        blocking:
          type: boolean
          description: Признак полной или частичной блокировки, накладывает ограничения на определенную сумму - сумму ареста
    UserAccount:
      type: object
      required:
        - ID
        - type
        - status
        - currency
        - openDate
        - iban
        - arrest
        - clientType
      properties:
        ID:
          type: string
          description: Идентификатор счёта
        documentNumber:
          type: string
          description: Номер договора
        type:
          type: string
          enum:
            - CURR
            - BUFB
            - BUCO
            - TU
            - LOAN
            - OTHERS
          description: Тип счёта
        status:
          type: string
          enum:
            - ACTIVE
            - BLOCKED
            - CLOSED
            - ARRESTED
            - MISTAKEN
            - ARCHIVED
            - REOPENED
          description: Статус счёта
        currency:
          type: string
          enum:
            - KZT
            - RUB
            - EUR
            - USD
            - CNY
          description: Валюта счёта
        iban:
          type: string
          description: Номер счета iban
        openDate:
          type: string
          format: date
          description: Дата открытия счёта
        closeDate:
          type: string
          format: date
          description: Дата закрытия счёта
        balance:
          type: number
          format: double
          description: Баланс (остаток счёта)
        balanceNatval:
          type: number
          format: double
          description: Баланс счета в нац. валюте (KZT)
        planSum:
          type: number
          format: double
          description: Плановые суммы
        availableBalance:
          type: number
          format: double
          description: Доступный баланс (balance-plansum-partiallyDebtAmount)
        arrest:
          $ref: '#/components/schemas/UserAccountArrest'
        clientType:
          type: string
          enum:
            - IP
          description: Тип клиента
    UserCardsResponse:
      type: object
      required:
        - accounts
      properties:
        accounts:
          type: array
          description: Счета пользователя
          items:
            $ref: '#/components/schemas/UserAccount'
    UserAccountResponse:
      type: object
      required:
        - account
      properties:
        account:
          $ref: '#/components/schemas/UserAccount'
    Offer:
      type: object
      required:
        - title
        - text
        - subText
      properties:
        title:
          type: string
          description: Заголовок для баннера (ограничение по символам - 20)
          example: Средства на ваши цели
        text:
          type: string
          description: Текст с предложением оформить кредит (для баннера) (ограничение по символам - 20)
          example: Быстрое оформление онлайн по исламским принципам
        subText:
          type: string
          description: Подтекст с информацией по сумме и сроку кредита (ограничение по символам - 20)
          example: до 3 млн ₸, до 5 лет
    LoansNextPayment:
      type: object
      description: Объект “Следующий платеж”
      required:
        - date
      properties:
        date:
          type: string
          format: date
          description: Дата ближайшего платежа
          example: '2025-01-02'
        amount:
          $ref: '#/components/schemas/Money'
    Loan:
      type: object
      required:
        - ID
        - productType
        - hasDelay
      properties:
        ID:
          type: string
          description: Уникальный идентификатор кредита
        productType:
          type: string
          description: Тип кредитного продукта
          example: LOAN
        status:
          type: string
          description: Статус кредита
          example: COMPLETED
        amount:
          $ref: '#/components/schemas/Money'
        applicationDueDate:
          type: string
          format: date
          description: Срок окончания одобренной заявки на кредит (5 дней)
          example: '2024-12-02'
        percentPaid:
          type: integer
          description: Поле для заполнения виджета оставшейся выплате по кредиту
          example: 67
        nextPayment:
          $ref: '#/components/schemas/LoansNextPayment'
        hasDelay:
          type: boolean
          description: Флаг наличия просроченных платежей по кредиту
          example: false
        statusReason:
          $ref: '#/components/schemas/ErrorReason'
    LoansResponse:
      type: object
      required:
        - loans
      properties:
        offer:
          $ref: '#/components/schemas/Offer'
        loans:
          type: array
          description: Список кредитов
          items:
            $ref: '#/components/schemas/Loan'
    UpdateUserLocaleResp:
      type: object
      properties: {}
    UserAccountsSMEResponse:
      type: object
      required:
        - accounts
      properties:
        accounts:
          type: array
          description: Счета пользователя
          items:
            $ref: '#/components/schemas/UserAccount'
    Task:
      type: object
      properties:
        taskID:
          type: string
          format: uuid
          pattern: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
          maxLength: 36
        type:
          type: string
        status:
          type: string
        payload:
          type: object
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    GetTasksListResponse:
      type: object
      required:
        - total_count
        - page
        - page_size
        - tasks
      properties:
        total_count:
          type: integer
          description: Общее количество задач
        page:
          type: integer
          description: Номер страницы
        page_size:
          description: Размер страницы
          type: integer
        tasks:
          type: array
          description: Список задач
          items:
            $ref: '#/components/schemas/Task'
    GetTaskDetailsResponse:
      type: object
      required:
        - taskID
        - task_type
        - status
        - payload
        - created_at
        - updated_at
      properties:
        taskID:
          type: string
          description: Идентификатор задачи
          format: uuid
          pattern: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
          maxLength: 36
        task_type:
          type: string
          description: Тип задачи
        status:
          type: string
          description: Статус задачи
        payload:
          type: object
          description: Полезная нагрузка задачи
        created_at:
          type: string
          description: Дата создания задачи
          format: date-time
        updated_at:
          type: string
          description: Дата обновления задачи
          format: date-time
    FileUploadRequestBody:
      type: object
      required:
        - file
        - filename
      properties:
        file:
          description: Контент документ для загрузки
          type: string
          format: binary
        filename:
          description: Имя файла вместе с расширением
          example: test.pdf
          type: string
    FileUploadResponse:
      type: object
      required:
        - ID
      properties:
        ID:
          description: ID загруженного файла
          type: string
    CreateConvertationRequest:
      type: object
      required:
        - idempotencyID
        - fromAccount
        - fromAmount
        - fromCurrency
        - toAccount
        - toAmount
        - toCurrency
        - rate
        - conversionType
      properties:
        idempotencyID:
          type: string
          description: Ключ идемпотентности
          example: abc1234567890
        fromAccount:
          type: string
          description: Номер счета списания
          example: *****************
        fromAmount:
          type: number
          format: float
          description: Сумма списания
          example: 6400.01
        fromCurrency:
          type: string
          maxLength: 20
          description: Валюта суммы списания
          example: KZT
        toAccount:
          type: string
          description: Номер счета зачисления
          example: *******************
        toAmount:
          type: number
          format: float
          description: Сумма зачисления
          example: 1000
        toCurrency:
          type: string
          maxLength: 20
          description: Валюта суммы зачисления
          example: RUB
        rate:
          type: string
          description: Курс валют, по которому проводился расчёт
          example: '0.640001'
        conversionType:
          type: string
          enum:
            - BUY_CURRENCY
            - SELL_CURRENCY
          description: Тип конвертации
          example: BUY_CURRENCY
        goal:
          type: string
          description: Цель покупки валюты
          example: '1'
    RejectionReason:
      type: object
      required:
        - title
        - message
        - code
      properties:
        title:
          type: string
          description: Заголовок причины отказа
          example: Конвертация недоступна
        message:
          type: string
          description: Сообщение о причине отказа
          example: Обратитесь в поддержку для получения подробной информации
        code:
          type: string
          description: Код ошибки
          example: CONVERSION_UNAVAILABLE
    CreateConvertationResponse:
      type: object
      required:
        - transactionID
        - status
      properties:
        transactionID:
          type: string
          description: ID транзакции
          example: 41c3f02f-436d-4f9b-8657-694f4c8890f8
        status:
          type: string
          enum:
            - COMPLETED
            - REJECTED
          description: Статус операции
          example: COMPLETED
        reason:
          $ref: '#/components/schemas/RejectionReason'
    ConversionSumRequestBody:
      description: |
        Данный объект содержит данные для расчёта сумм конвертации.
      required:
        - toAmount
        - toCurrency
        - fromCurrency
      properties:
        toAmount:
          type: number
          format: float
          description: Сумма зачисления
        toCurrency:
          type: string
          description: Код валюты (например, CHY, RUB)
        fromCurrency:
          type: string
          description: Код валюты (например, KZT)
    ConversionSumResponse:
      description: |
        Данный объект содержит результаты расчёта сумм конвертации.
      properties:
        toAmount:
          type: number
          format: float
          description: Сумма зачисления
        toCurrency:
          type: string
          description: Валюта суммы зачисления (например, CHY, RUB)
        fromAmount:
          type: number
          format: float
          description: Сумма списания
        fromCurrency:
          type: string
          description: Валюта суммы списания (например, KZT)
        rate:
          type: string
          description: Курс валют, по которому проводился расчёт
  parameters:
    AcceptLanguage:
      name: Accept-Language
      in: header
      description: Язык запроса
      required: false
      schema:
        type: string
        enum:
          - kk
          - en
          - ru
        example: ru
        default: kk
    UserAgent:
      name: User-Agent
      in: header
      description: Юзер агент
      required: true
      schema:
        type: string
    DocumentIDPathParam:
      name: docID
      in: path
      description: Идентификатор запрашиваемого документа
      required: true
      schema:
        type: string
        format: uuid
        pattern: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
        maxLength: 36
    ApplicationIDPathParam:
      name: applicationID
      in: path
      description: Идентификатор заявки на кредит
      required: true
      schema:
        type: string
        format: uuid
        pattern: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
        maxLength: 36
    PaymentsGetTransactionsStartDate:
      name: startDate
      in: query
      description: Начальная дата для фильтрации транзакций
      schema:
        type: string
        format: date-time
      required: false
    PaymentsGetTransactionsEndDate:
      name: endDate
      in: query
      description: Конечная дата для фильтрации транзакций
      schema:
        type: string
        format: date-time
      required: false
    PaymentsGetTransactionsAccounts:
      name: accounts
      in: query
      description: Список номеров счетов для фильтрации транзакций
      schema:
        type: array
        items:
          type: string
      required: true
    PaymentsGetTransactionsCards:
      name: cards
      in: query
      description: Список карт для фильтрации транзакций
      schema:
        type: array
        items:
          type: string
    PaymentsGetTransactionsOperationType:
      name: direction
      in: query
      description: Тип операции (например, CREDIT или DEBIT)
      schema:
        type: string
        enum:
          - CREDIT
          - DEBIT
      required: false
    PaymentsGetTransactionsCounterparty:
      name: counterparty
      in: query
      description: Контрагент для фильтрации (получатель или отправитель средств)
      schema:
        type: string
      required: false
    PaymentsGetTransactionsMinAmount:
      name: minAmount
      in: query
      description: Минимальная сумма транзакции
      schema:
        type: string
      required: false
    PaymentsGetTransactionsMaxAmount:
      name: maxAmount
      in: query
      description: Максимальная сумма транзакции
      schema:
        type: string
      required: false
    PaymentsGetTransactionsLimit:
      name: limit
      in: query
      description: Лимит количества возвращаемых транзакций
      schema:
        type: integer
        format: int64
      required: false
    PaymentsGetTransactionsOffset:
      name: offset
      in: query
      description: Смещение для пагинации
      schema:
        type: integer
        format: int64
      required: false
    PaymentsGetTransactionByIDTransactionID:
      name: transactionID
      in: path
      description: Индектификатор транзакции
      schema:
        type: string
        format: uuid
      required: true
    PaymentsGetHistoryClientIinBin:
      name: clientIinBin
      in: query
      description: ИИН/БИН клиента
      schema:
        type: string
      required: true
    PaymentsTransactionID:
      name: transactionID
      in: path
      description: Идентификатор транзакции
      schema:
        type: string
        format: uuid
      required: true
    PaymentsTransactionNumber:
      name: transactionNumber
      in: path
      description: Номер транзакции
      schema:
        type: string
      required: true
    KbeKodFilterCodes:
      name: filter.codes
      in: query
      description: Список кодов для фильтрации
      required: false
      schema:
        type: array
        items:
          type: string
        example:
          - 011,012
          - '010'
    KbeKodFilterResidency:
      name: filter.residency
      in: query
      description: Признак резидента для фильтрации
      required: false
      schema:
        type: boolean
        example: true
    KnpFilterCodes:
      name: filter.codes
      in: query
      description: Список кодов КНП для фильтрации
      required: false
      schema:
        type: array
        items:
          type: string
        example:
          - 011,012
          - '010'
    KnpFilterGroups:
      name: filter.groups
      in: query
      description: Список кодов групп КНП для фильтрации
      required: false
      schema:
        type: array
        items:
          type: string
        example:
          - 011,012
          - '010'
    BankFilterBics:
      name: filter.bics
      in: query
      description: Список БИК кодов банков для фильтрации
      required: false
      schema:
        type: array
        items:
          type: string
        example:
          - KZKOKZKX
          - TSESKZKA
    TaxAuthorityFilterRegions:
      name: filter.regions
      in: query
      description: Список кодов регионов УГД для фильтрации
      required: false
      schema:
        type: array
        items:
          type: string
        example:
          - '60'
          - '62'
          - '38'
    KbkFilterCodes:
      name: filter.codes
      in: query
      description: Список кодов КБК для фильтрации
      required: false
      schema:
        type: array
        items:
          type: string
        example:
          - '105274'
          - '105276'
          - '105276'
    SmePaymentsOtpAttemptID:
      name: attemptID
      in: path
      description: Идентификатор попытки подписи
      schema:
        type: string
        format: uuid
      required: true
    EmployeeID:
      name: employeeID
      in: path
      description: Идентификатор сотрудника ИП
      schema:
        type: string
        format: uuid
      required: true
    AccountIDPathParam:
      name: accountID
      in: path
      description: Идентификатор запрашиваемого счета
      required: true
      schema:
        type: string
        format: uuid
        pattern: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
        maxLength: 36
    TaskStatusQueryParam:
      name: status
      in: query
      description: Статус задачи
      required: false
      schema:
        type: string
        enum:
          - in-progress
          - completed
          - pending
    TaskTypeQueryParam:
      name: type
      in: query
      description: Тип задачи
      required: false
      schema:
        type: string
    TaskCreatedAfterQueryParam:
      name: created_after
      in: query
      description: Дата создания после (timestamp)
      required: false
      schema:
        type: integer
        format: int64
    TaskCreatedBeforeQueryParam:
      name: created_before
      in: query
      description: Дата создания до (timestamp)
      required: false
      schema:
        type: integer
        format: int64
    TaskPageQueryParam:
      name: page
      in: query
      description: Номер страницы
      required: false
      schema:
        type: integer
        format: int64
        default: 1
    TaskPageSizeQueryParam:
      name: page_size
      in: query
      description: Размер страницы
      required: false
      schema:
        type: integer
        format: int64
        default: 20
    TaskIDPathParam:
      name: taskID
      in: path
      description: Идентификатор запрашиваемого счета
      required: true
      schema:
        type: string
        format: uuid
        pattern: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
        maxLength: 36
  requestBodies:
    LogInBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              phone:
                $ref: '#/components/schemas/PhoneNumber'
              iin:
                type: string
                description: ИИН
                pattern: ^[0-9]+$
                example: '************'
              deviceInfo:
                $ref: '#/components/schemas/DeviceInfo'
              ipinfo:
                $ref: '#/components/schemas/IpInfo'
            required:
              - phone
              - iin
              - deviceInfo
    LogoutBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              installationID:
                type: string
                format: uuid
                description: Идентификатор установки приложения
                example: 4d41e509-1d1e-4530-a1d6-97d2d599d3f8
            required:
              - installationID
    OtpBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: string
                description: Код подтверждения
                example: '3223'
              attemptID:
                type: string
                description: Идентификатор попытки для ввода кода
                format: uuid
            required:
              - attemptID
    AuthRefreshBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              refreshToken:
                type: string
                description: Токен обновления авторизации
              phone:
                $ref: '#/components/schemas/PhoneNumber'
              deviceInfo:
                $ref: '#/components/schemas/DeviceInfo'
              ipinfo:
                $ref: '#/components/schemas/IpInfo'
            required:
              - refreshToken
              - phone
              - deviceInfo
    IdentifyBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: string
                description: Код BTS
                example: '************'
            required:
              - code
    SignDocumentsByIDReqBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - docIDs
            properties:
              docIDs:
                type: array
                items:
                  type: string
                  format: uuid
                  pattern: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
                  maxLength: 36
    ConfirmSignDocumentsReqBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: string
                description: Код подтверждения
                minLength: 1
                example: '3223'
              attemptID:
                type: string
                description: Идентификатор попытки для ввода кода
                format: uuid
            required:
              - attemptID
              - code
    BatchDocsOtpBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - docIDs
              - attemptID
              - code
            properties:
              code:
                type: string
                description: Код подтверждения
                minLength: 1
                example: '3223'
              attemptID:
                type: string
                description: Идентификатор попытки для ввода кода
                format: uuid
              docIDs:
                description: Идентификаторы подписываемых документов
                type: array
                items:
                  type: string
                  format: uuid
    CreateOrderBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - header
              - request
            properties:
              header:
                $ref: '#/components/schemas/HeaderRequest'
              request:
                type: array
                items:
                  $ref: '#/components/schemas/RequestItem'
    GetOrderBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - header
              - request
            properties:
              header:
                $ref: '#/components/schemas/HeaderRequest'
              request:
                $ref: '#/components/schemas/OrderResultItemRequest'
    GetSystemBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - header
            properties:
              header:
                $ref: '#/components/schemas/BidXMLRequestInput'
    GetOtcBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - input
            properties:
              input:
                $ref: '#/components/schemas/InputRequest'
    SellBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - input
            properties:
              input:
                $ref: '#/components/schemas/InputRequest'
    AccountDocumentRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              accountID:
                type: string
                description: Идентификатор счета
                format: uuid
                pattern: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'
                maxLength: 36
              language:
                type: string
                description: Язык документа
                example: ru
              periodFrom:
                type: string
                format: date
                nullable: true
                description: Начало периода (только для выписки по счету)
                example: '2025-01-01'
              periodTo:
                type: string
                format: date
                nullable: true
                description: Конец периода (только для выписки по счету)
                example: '2025-01-30'
    DocumentsForSignRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              currencies:
                type: array
                items:
                  $ref: '#/components/schemas/Currency'
    CalculationBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              purposeID:
                type: string
                description: ID выбранной клиентом цели
                example: '1'
              amount:
                type: integer
                example: 10000
                description: Значение, внесенное клиентом по сумме кредита
            required:
              - purposeID
              - amount
    SaveSurveyBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - applicationID
              - email
              - educationID
              - address
              - empID
              - contactPersons
            properties:
              applicationID:
                type: string
                description: Идентификатор заявки.
                minLength: 1
              email:
                $ref: '#/components/schemas/Email'
              address:
                $ref: '#/components/schemas/SaveSurveyAddress'
              educationID:
                type: string
                description: Идентификатор уровня образования.
                minLength: 1
              empID:
                type: string
                description: Идентификатор типа занятости.
                minLength: 1
              additionalAmount:
                type: string
                description: Сумма дополнительного источника дохода за последние 6 месяцев
                minLength: 1
              contactPersons:
                type: array
                items:
                  $ref: '#/components/schemas/SaveSurveyContactPerson'
                description: Список контактных лиц.
                minItems: 1
    LoansApplicationBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              amount:
                type: number
                description: Сумма кредита, указанная клиентом на калькуляторе
                example: 1000000
              termInterestID:
                type: string
                description: ID срока (и наценки) по кредиту из справочника termInterest
                example: '2'
                minLength: 1
              purposeID:
                type: string
                description: ID цели кредита, выбранная клиентом при расчете сумм
                example: '1'
                minLength: 1
              juicySessionID:
                type: string
                description: ID сессии в системе JUICYSCORE
                example: 4d41e509-1d1e-4530-a1d6
                minLength: 1
            required:
              - amount
              - termInterestID
              - purposeID
              - juicySessionID
    LoanApplicationDocumentsBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - applicationID
              - docTypes
            properties:
              applicationID:
                type: string
                description: Идентификатор заявки.
                format: uuid
              docTypes:
                type: array
                description: Тип (шаблон) документа
                uniqueItems: true
                items:
                  $ref: '#/components/schemas/LoanAppDocumentType'
    PublishLoanAppDataRequestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              bankStatements:
                type: array
                description: Данные прикрепленных документов
                items:
                  $ref: '#/components/schemas/AttachedDocData'
    PostIdentifyBtsDataSmeRequestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - code
            properties:
              code:
                type: string
                description: Код
    PostEdsBtsDataRequestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - code
            properties:
              code:
                type: string
                description: Код
    PostEarlyRepayRequestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - repayType
              - repayAmount
            properties:
              repayType:
                type: string
                description: |
                  ODONLY - ЧДП
                  WSHD_FULL - ПДП
              repayAmount:
                $ref: '#/components/schemas/EarlyRepayAmount'
    PostChangeDisbursementControlRequestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - mode
              - token
            properties:
              token:
                type: string
                description: Авторизационный токен
              mode:
                $ref: '#/components/schemas/DisbursementMode'
    DictDocGetListByFiltersReq:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/DictDocumentFilters'
    PaymentsCheckAccountIinRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - clientIinBin
              - account
            properties:
              clientIinBin:
                type: string
                description: ИИН/БИН клиента
              account:
                type: string
                description: Номер счёта
    CreatePaymentByAccount:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              idempotencyID:
                type: string
                description: Ключ идемпотентности
                example: 59acde04-ef16-4a3d-9a95-4728d42e20cd
              payerBinIIN:
                type: string
                description: ИИН/БИН отправителя
                maxLength: 12
                minLength: 12
              payerName:
                type: string
                description: ФИО/Наименование
                maxLength: 250
                minLength: 3
              payerAccount:
                type: string
                description: Счет клиента
                maxLength: 20
                minLength: 20
              amount:
                type: number
                format: double
                description: Сумма операции
                example: 99.95
              currency:
                type: string
                description: Валюта операции
                example: KZT
              beneficiaryBinIIN:
                type: string
                description: ИИН/БИН получателя
                pattern: ^[0-9]{12}$
              beneficiaryName:
                type: string
                description: ФИО/Наименование получателя
                maxLength: 250
                minLength: 3
              beneficiaryType:
                type: integer
                format: int32
                description: Тип получателя
                example: 32
              beneficiaryTaxPayerType:
                $ref: '#/components/schemas/TaxPayerType'
                description: Тип налогоплательщика
              beneficiaryBank:
                type: string
                description: БИК банка получателя
              beneficiaryAccount:
                type: string
                description: IBAN cчета  получателя
              beneficiaryBankName:
                type: string
                description: Наименование банка получателя
              beneficiaryCountry:
                type: string
                description: Страна резидентство
              date:
                type: string
                format: date-time
                description: Дата операции
              valueDate:
                type: string
                format: date-time
                description: Дата валютирования
              kod:
                type: integer
                format: int32
                description: Код отправителя
              kbe:
                type: string
                description: Код бенефициара
              knp:
                type: string
                description: КНП, purposeCode
              paymentDetails:
                type: string
                description: Назначение платежа
                maxLength: 250
              actualSender:
                type: string
                description: Фактический отправитель (ФИО/Наименование)
              actualSenderBinIIN:
                type: string
                description: ИИН/БИН фактического отправителя
                pattern: ^[0-9]{12}$
              actualSenderIsLegal:
                type: boolean
                description: Флаг юр лица фактического отправителя
              actualSenderCountry:
                type: string
                description: Страна резидентство фактического отправителя
              actualBeneficiary:
                type: string
                description: Фактический получатель (ФИО/Наименование)
              actualBeneficiaryBinIIN:
                type: string
                description: ИИН/БИН фактического получателя
                pattern: ^[0-9]{12}$
              actualBeneficiaryIsLegal:
                type: boolean
                description: Флаг юр лица фактического получателя
              actualBeneficiaryCountry:
                type: string
                description: Страна резидентство фактического получателя
            required:
              - payerBinIIN
              - payerName
              - payerAccount
              - amount
              - currency
              - beneficiaryBinIIN
              - beneficiaryName
              - beneficiaryType
              - beneficiaryBank
              - beneficiaryAccount
              - beneficiaryBankName
              - kbe
              - knp
              - paymentDetails
              - idempotencyID
    ConfirmPaymentByAccount:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: string
                description: Код валидации отп
              attemptID:
                type: string
                format: uuid
                description: Идентификатор для валидации отп кода
                example: 2100e82d-e288-4882-8fac-3f1403449051
            required:
              - attemptID
              - code
    CreatePaymentRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - paymentCode
              - idempotencyKey
              - paymentData
            properties:
              paymentCode:
                type: string
                description: Код платежа
              idempotencyKey:
                type: string
                description: Ключ идемпотентности
              paymentData:
                $ref: '#/components/schemas/PaymentData'
    SmePaymentsCreateOtpRequestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              deviceInfo:
                $ref: '#/components/schemas/SmePaymentsDeviceInfo'
            required:
              - deviceInfo
    SmePaymentsOtpValidateRequestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: string
                description: Введенный код
              transactionID:
                type: string
                description: Идентификатор транзакции, которую подтверждаем
            required:
              - code
              - transactionID
    SmePaymentsAddEmployeeRequestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            description: Информация о сотруднике для добавления в ИП
            required:
              - name
              - lastName
              - iin
              - employerIinBin
              - birthday
              - country
              - displayOrder
            properties:
              name:
                type: string
                description: Имя сотрудника
                example: Айдар
              middleName:
                type: string
                description: Отчество сотрудника
                example: Асылбекович
              lastName:
                type: string
                description: Фамилия сотрудника
                example: Нурманов
              iin:
                type: string
                description: ИИН сотрудника
                pattern: ^[0-9]{12}$
                example: '************'
              employerIinBin:
                type: string
                description: ИИН/БИН организации
                pattern: ^[0-9]{12}$
                example: '************'
              birthday:
                type: string
                format: date
                description: Дата рождения сотрудника
                example: '1990-01-15'
              country:
                type: string
                description: Страна
                example: KZ
              displayOrder:
                type: integer
                description: Порядок отображения в списке
    SmePaymentsUpdateEmployeeRequestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            description: Информация о сотруднике для добавления в ИП
            required:
              - name
              - lastName
              - iin
              - employerIinBin
              - birthday
              - country
              - displayOrder
            properties:
              name:
                type: string
                description: Имя сотрудника
                example: Айдар
              middleName:
                type: string
                description: Отчество сотрудника
                example: Асылбекович
              lastName:
                type: string
                description: Фамилия сотрудника
                example: Нурманов
              iin:
                type: string
                description: ИИН сотрудника
                pattern: ^[0-9]{12}$
                example: '************'
              employerIinBin:
                type: string
                description: ИИН/БИН организации
                pattern: ^[0-9]{12}$
                example: '************'
              birthday:
                type: string
                format: date
                description: Дата рождения сотрудника
                example: '1990-01-15'
              country:
                type: string
                description: Страна
                example: KZ
              displayOrder:
                type: integer
                description: Порядок отображения в списке
    ProfileDeleteBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              installationID:
                type: string
                format: uuid
                description: Идентификатор установки приложения
                example: 4d41e509-1d1e-4530-a1d6-97d2d599d3f8
            required:
              - installationID
    CreateConvertationRequest:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/CreateConvertationRequest'
