openapi: 3.0.3
info:
  title: Balance Gateway API
  description: API для работы с балансами
  version: dev-0916-08335e49
  contact:
    name: Zaman Backend Team
    email: <EMAIL>
servers:
  - url: '{protocol}://{host}/api/v1'
    variables:
      protocol:
        enum:
          - http
          - https
        default: https
      host:
        default: localhost:8082
paths:
  /health:
    get:
      summary: Проверка работоспособности
      description: Проверка состояния всех микросервисов балансового gateway
      operationId: health
      security: []
      responses:
        '200':
          description: Информация о статусе микросервисов
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Health'
  /health/balanceupdater:
    get:
      summary: Проверка доступности balanceupdater
      description: Проверяет связь с микросервисом balanceupdater через gRPC HealthCheck
      operationId: healthBalanceUpdater
      security: []
      responses:
        '200':
          description: Статус доступности balanceupdater
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BalanceUpdaterHealth'
        '503':
          description: Сервис balanceupdater недоступен
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /balance:
    get:
      summary: Получение баланса пользователя
      description: Возвращает баланс текущего авторизованного пользователя
      operationId: getBalance
      security:
        - BearerTokenAuth: []
      responses:
        '200':
          description: Успешный ответ с балансом
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetBalanceResponse'
        '401':
          description: Ошибка авторизации
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/APIError'
  /rtp/transaction:
    post:
      summary: Обработка RTP транзакций
      description: Endpoint для обработки SOAP запросов от процессингового центра
      operationId: processRtpTransaction
      security: []
      requestBody:
        required: true
        content:
          application/soap+xml:
            schema:
              type: string
              description: SOAP XML с транзакцией
          text/xml:
            schema:
              type: string
              description: XML с транзакцией
          application/xml:
            schema:
              type: string
              description: XML с транзакцией
      responses:
        '200':
          description: Успешная обработка транзакции
          content:
            application/soap+xml:
              schema:
                type: string
                description: SOAP ответ с результатом
        '400':
          description: Ошибка валидации RRN
          content:
            application/soap+xml:
              schema:
                type: string
                description: SOAP Fault с описанием ошибки
components:
  securitySchemes:
    BearerTokenAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    Health:
      type: object
      description: Статус микросервисов balance gateway
      required:
        - users
      properties:
        users:
          type: boolean
          description: Статус сервиса users
    BalanceUpdaterHealth:
      type: object
      description: Статус доступности balanceupdater
      required:
        - balanceupdater
      properties:
        balanceupdater:
          type: boolean
          description: Статус сервиса balanceupdater (true - доступен, false - недоступен)
    GetBalanceResponse:
      type: object
      description: Ответ с балансом пользователя
      required:
        - balance
        - currency
      properties:
        balance:
          type: number
          format: double
          description: Баланс пользователя
        currency:
          type: string
          description: Валюта баланса
          example: KZT
    APIError:
      type: object
      description: Общий формат ошибки API
      properties:
        error:
          type: string
        fields:
          type: object
          description: Объект с описанием деталей ошибок
          additionalProperties:
            type: string
      required:
        - error
      example:
        error: X.0
