openapi: 3.0.1
info:
  title: Zaman Payments API
  version: dev-0916-08335e49
  description: |
    API для бэкэнда мобильного приложения  Zaman Payments

    Спецификация в формате [swagger](../swagger) или [redoc](../redoc).

    [Changelog](../history/changelog_main.html)

    [Release Changelog](../history/changelog_feature.html)
  termsOfService: https://zaman/terms-of-service
  x-logo:
    url: ../static/logo.png
servers:
  - description: Dev server
    url: https://paymentsgw.zaman.redmadrobot.com/api/v1
  - description: Pre-prod server
    url: https://paymentsgw-predprod.zamanbank.kz/api/v1/payments/astanaplat
tags:
  - name: Системные
  - name: Платежи
paths:
  /health:
    get:
      tags:
        - Системные
      summary: Проверка на работоспособность
      description: Проверка всех модулей системы на работоспособность
      operationId: health
      security:
        - BearerTokenAuth: []
      responses:
        '200':
          description: Информация по запрошенному статусу микросервисов
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Health'
  /payments/astanaplat:
    get:
      tags:
        - Платежи
      summary: Проверка пользователя по ИИН
      description: Проверка существования пользователя по ИИН, и проверка заполнения профиля пользователя ФИО
      operationId: AstanaPlat
      security: []
      parameters:
        - in: query
          name: action
          schema:
            type: string
          required: true
          description: Предопределенная строка. Возможные значения -> check, payment
        - name: number
          in: query
          schema:
            type: string
          required: true
          description: ИИН пользователя
        - name: account
          in: query
          schema:
            type: string
          required: true
          description: Часть номера текущего счета, последние 6 цифр текущего счета
        - name: type
          in: query
          schema:
            type: integer
            format: uint32
          required: true
          description: Идентификатор 1 - пополнение на счет ФЛ, 2- пополнение на счет ИП
        - name: amount
          in: query
          schema:
            type: string
          required: false
          description: Сумма платежа в тенге с тиынами. Разделитель ‘.’ (точка)
        - name: receipt
          in: query
          schema:
            type: string
          required: false
          description: Уникальный идентификатор платежа из внешней платежной системы (Терминала). Может содержать только цифры
        - name: date
          in: query
          schema:
            type: string
          required: false
          description: Дата и время операции по часовому поясу внешней платежной системы в формате YYYY-MM-DDThh:mm:ss
      responses:
        '200':
          description: |
            Обработка платежа прошло успешно
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AstanaPlatResponse'
        '400':
          description: |
            Ошибка получения данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AstanaPlatError'
components:
  securitySchemes:
    BearerTokenAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    AstanaPlatError:
      type: object
      description: Ошибка для API Astana Plat
      properties:
        code:
          type: integer
          description: Код ошибки
        message:
          type: string
          description: Текст ошибки
    Health:
      required:
        - users
        - otp
        - documents
        - notifications
        - keycloakProxy
        - kgdBridge
        - btsBridge
        - smsBridge
        - loans
        - colvirBridge
        - payments
        - cardsAccounts
        - dictionary
        - pkbBridge
        - amlBridge
        - liveness
        - taskManager
        - juicyscoreBridge
        - jiraBridge
        - fileGuard
        - scoring
        - seonBridge
        - sprBridge
        - altScoreBridge
        - qazpostBridge
        - deposits
        - bsasBridge
        - apBridge
        - processingBridge
        - collection
        - paymentsSme
        - referral
        - antifraud
        - crm
        - tokenize
        - balanceUpdater
        - kaspiBridge
        - bitrixBridge
        - foreignActivity
      type: object
      properties:
        users:
          type: boolean
          description: Статус сервиса users
        otp:
          type: boolean
          description: Статус сервиса otp
        documents:
          type: boolean
          description: Статус сервиса documents
        notifications:
          type: boolean
          description: Статус сервиса notifications
        keycloakProxy:
          type: boolean
          description: Статус сервиса keycloakProxy
        kgdBridge:
          type: boolean
          description: Статус сервиса kgdBridge
        btsBridge:
          type: boolean
          description: Статус сервиса btsBridge
        smsBridge:
          type: boolean
          description: Статус сервиса smsBridge
        loans:
          type: boolean
          description: Статус сервиса loans
        colvirBridge:
          type: boolean
          description: Статус сервиса colvirBridge
        payments:
          type: boolean
          description: Статус сервиса payments
        cardsAccounts:
          type: boolean
          description: Статус сервиса cardsAccounts
        dictionary:
          type: boolean
          description: Статус сервиса dictionary
        pkbBridge:
          type: boolean
          description: Статус сервиса pkbBridge
        amlBridge:
          type: boolean
          description: Статус сервиса amlBridge
        liveness:
          type: boolean
          description: Статус сервиса liveness
        taskManager:
          type: boolean
          description: Статус сервиса taskManager
        juicyscoreBridge:
          type: boolean
          description: Статус сервиса juicyscoreBridge
        jiraBridge:
          type: boolean
          description: Статус сервиса jiraBridge
        fileGuard:
          type: boolean
          description: Статус сервиса fileGuard
        scoring:
          type: boolean
          description: Статус сервиса scoring
        seonBridge:
          type: boolean
          description: Статус сервиса seonBridge
        sprBridge:
          type: boolean
          description: Статус сервиса sprBridge
        altScoreBridge:
          type: boolean
          description: Статус сервиса altScoreBridge
        qazpostBridge:
          type: boolean
          description: Статус сервиса qazpostBridge
        deposits:
          type: boolean
          description: Статус сервиса deposits
        bsasBridge:
          type: boolean
          description: Статус сервиса bsasBridge
        apBridge:
          type: boolean
          description: Статус сервиса apBridge
        processingBridge:
          type: boolean
          description: Статус сервиса processingBridge
        collection:
          type: boolean
          description: Статус сервиса collection
        paymentsSme:
          type: boolean
          description: Статус сервиса paymentsSme
        referral:
          type: boolean
          description: Статус сервиса referral
        antifraud:
          type: boolean
          description: Статус сервиса antifraud
        crm:
          type: boolean
          description: Статус сервиса crm
        tokenize:
          type: boolean
          description: Статус сервиса tokenize
        balanceUpdater:
          type: boolean
          description: Статус сервиса balanceUpdater
        kaspiBridge:
          type: boolean
          description: Статус сервиса kaspiBridge
        bitrixBridge:
          type: boolean
          description: Статус сервиса bitrixBridge
        foreignActivity:
          type: boolean
          description: Статус сервиса foreignActivity
    AstanaPlatResponse:
      type: object
      properties:
        code:
          type: integer
        message:
          type: string
        name:
          type: string
        receipt:
          type: string
        authCode:
          type: string
        date:
          type: string
