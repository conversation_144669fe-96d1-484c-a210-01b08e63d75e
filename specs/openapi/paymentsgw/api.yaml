openapi: 3.0.1

servers:
  - description: Dev server
    url: https://paymentsgw.zaman.redmadrobot.com/api/v1

  - description: Pre-prod server
    url: https://paymentsgw-predprod.zamanbank.kz/api/v1/payments/astanaplat

info:
  title: Zaman Payments API
  version: dev-0916-08335e49
  description: |
    API для бэкэнда мобильного приложения  Zaman Payments

    Спецификация в формате [swagger](../swagger) или [redoc](../redoc).

    [Changelog](../history/changelog_main.html)

    [Release Changelog](../history/changelog_feature.html)
  termsOfService: https://zaman/terms-of-service
  x-logo:
    url: ../static/logo.png
tags:
  - name: "Системные"
  - name: "Платежи"

paths:
  /health:
    $ref: "health.gen.yaml#/health"

  /payments/astanaplat:
    $ref: "payments.yaml#/astana-plat"

components:
  securitySchemes:
    BearerTokenAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    AstanaPlatError:
      type: object
      description: Ошибка для API Astana Plat
      properties:
        code:
          type: integer
          description: Код ошибки
        message:
          type: string
          description: Текст ошибки

