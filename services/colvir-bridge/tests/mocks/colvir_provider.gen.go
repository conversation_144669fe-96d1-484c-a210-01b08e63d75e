// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/pkg/colvir (interfaces: ColvirProvider)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"

	entity "git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

// MockColvirProvider is a mock of ColvirProvider interface.
type MockColvirProvider struct {
	ctrl     *gomock.Controller
	recorder *MockColvirProviderMockRecorder
}

// MockColvirProviderMockRecorder is the mock recorder for MockColvirProvider.
type MockColvirProviderMockRecorder struct {
	mock *MockColvirProvider
}

// NewMockColvirProvider creates a new mock instance.
func NewMockColvirProvider(ctrl *gomock.Controller) *MockColvirProvider {
	mock := &MockColvirProvider{ctrl: ctrl}
	mock.recorder = &MockColvirProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockColvirProvider) EXPECT() *MockColvirProviderMockRecorder {
	return m.recorder
}

// GetAllLoans mocks base method.
func (m *MockColvirProvider) GetAllLoans(arg0 context.Context, arg1 *entity.GetAllLoansReq) (*entity.GetAllLoansRespWrapper, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllLoans", arg0, arg1)
	ret0, _ := ret[0].(*entity.GetAllLoansRespWrapper)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllLoans indicates an expected call of GetAllLoans.
func (mr *MockColvirProviderMockRecorder) GetAllLoans(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllLoans", reflect.TypeOf((*MockColvirProvider)(nil).GetAllLoans), arg0, arg1)
}

// GetClients mocks base method.
func (m *MockColvirProvider) GetClients(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClients", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetClients indicates an expected call of GetClients.
func (mr *MockColvirProviderMockRecorder) GetClients(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClients", reflect.TypeOf((*MockColvirProvider)(nil).GetClients), arg0)
}

// GetCreditContractDetails mocks base method.
func (m *MockColvirProvider) GetCreditContractDetails(arg0 context.Context, arg1 *entity.GetCreditContractDetailsReqBodyLoadLoanDetailsRequest) (*entity.GetCreditContractDetailsRespBodyLoadLoanDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCreditContractDetails", arg0, arg1)
	ret0, _ := ret[0].(*entity.GetCreditContractDetailsRespBodyLoadLoanDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCreditContractDetails indicates an expected call of GetCreditContractDetails.
func (mr *MockColvirProviderMockRecorder) GetCreditContractDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCreditContractDetails", reflect.TypeOf((*MockColvirProvider)(nil).GetCreditContractDetails), arg0, arg1)
}

// GetLoanAmounts mocks base method.
func (m *MockColvirProvider) GetLoanAmounts(arg0 context.Context, arg1 *entity.GetLoanAmountsReq) (*entity.GetLoanAmountsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLoanAmounts", arg0, arg1)
	ret0, _ := ret[0].(*entity.GetLoanAmountsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanAmounts indicates an expected call of GetLoanAmounts.
func (mr *MockColvirProviderMockRecorder) GetLoanAmounts(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanAmounts", reflect.TypeOf((*MockColvirProvider)(nil).GetLoanAmounts), arg0, arg1)
}

// GetLoanDetails mocks base method.
func (m *MockColvirProvider) GetLoanDetails(arg0 context.Context, arg1 *entity.GetLoanDetailsReq) (*entity.GetLoanDetailsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLoanDetails", arg0, arg1)
	ret0, _ := ret[0].(*entity.GetLoanDetailsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanDetails indicates an expected call of GetLoanDetails.
func (mr *MockColvirProviderMockRecorder) GetLoanDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanDetails", reflect.TypeOf((*MockColvirProvider)(nil).GetLoanDetails), arg0, arg1)
}

// GetLoanSchedule mocks base method.
func (m *MockColvirProvider) GetLoanSchedule(arg0 context.Context, arg1 *entity.GetLoanScheduleReq) (*entity.GetLoanScheduleRespWrapper, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLoanSchedule", arg0, arg1)
	ret0, _ := ret[0].(*entity.GetLoanScheduleRespWrapper)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanSchedule indicates an expected call of GetLoanSchedule.
func (mr *MockColvirProviderMockRecorder) GetLoanSchedule(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanSchedule", reflect.TypeOf((*MockColvirProvider)(nil).GetLoanSchedule), arg0, arg1)
}

// GetMissedPayments mocks base method.
func (m *MockColvirProvider) GetMissedPayments(arg0 context.Context, arg1 *entity.GetMissedPaymentsReq) (*entity.GetMissedPaymentsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMissedPayments", arg0, arg1)
	ret0, _ := ret[0].(*entity.GetMissedPaymentsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMissedPayments indicates an expected call of GetMissedPayments.
func (mr *MockColvirProviderMockRecorder) GetMissedPayments(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMissedPayments", reflect.TypeOf((*MockColvirProvider)(nil).GetMissedPayments), arg0, arg1)
}

// GetStatusOperDate mocks base method.
func (m *MockColvirProvider) GetStatusOperDate(arg0 context.Context, arg1 string) (*entity.RespStatusOperDate, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStatusOperDate", arg0, arg1)
	ret0, _ := ret[0].(*entity.RespStatusOperDate)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStatusOperDate indicates an expected call of GetStatusOperDate.
func (mr *MockColvirProviderMockRecorder) GetStatusOperDate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStatusOperDate", reflect.TypeOf((*MockColvirProvider)(nil).GetStatusOperDate), arg0, arg1)
}

// LoadClientBankRelationLink mocks base method.
func (m *MockColvirProvider) LoadClientBankRelationLink(arg0 context.Context, arg1 string) (*entity.LoadClientBankRelationLinkResponseEnvelope, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LoadClientBankRelationLink", arg0, arg1)
	ret0, _ := ret[0].(*entity.LoadClientBankRelationLinkResponseEnvelope)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadClientBankRelationLink indicates an expected call of LoadClientBankRelationLink.
func (mr *MockColvirProviderMockRecorder) LoadClientBankRelationLink(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadClientBankRelationLink", reflect.TypeOf((*MockColvirProvider)(nil).LoadClientBankRelationLink), arg0, arg1)
}

// LoadLoanAgreementDetails mocks base method.
func (m *MockColvirProvider) LoadLoanAgreementDetails(arg0 context.Context, arg1 *entity.LoadLoanAgreementDetailsReq) (*entity.LoadLoanAgreementDetailsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LoadLoanAgreementDetails", arg0, arg1)
	ret0, _ := ret[0].(*entity.LoadLoanAgreementDetailsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadLoanAgreementDetails indicates an expected call of LoadLoanAgreementDetails.
func (mr *MockColvirProviderMockRecorder) LoadLoanAgreementDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadLoanAgreementDetails", reflect.TypeOf((*MockColvirProvider)(nil).LoadLoanAgreementDetails), arg0, arg1)
}

// LoadOverdueLoanPaymentReport mocks base method.
func (m *MockColvirProvider) LoadOverdueLoanPaymentReport(arg0 context.Context, arg1 *entity.LoadOverdueLoanPaymentReport) (*entity.LoadOverdueLoanPaymentReportResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LoadOverdueLoanPaymentReport", arg0, arg1)
	ret0, _ := ret[0].(*entity.LoadOverdueLoanPaymentReportResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadOverdueLoanPaymentReport indicates an expected call of LoadOverdueLoanPaymentReport.
func (mr *MockColvirProviderMockRecorder) LoadOverdueLoanPaymentReport(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadOverdueLoanPaymentReport", reflect.TypeOf((*MockColvirProvider)(nil).LoadOverdueLoanPaymentReport), arg0, arg1)
}

// LoadUGDDictionary mocks base method.
func (m *MockColvirProvider) LoadUGDDictionary(arg0 context.Context, arg1 *entity.LoadColvirReportDataElemRequest) (*entity.LoadColvirReportDataElemResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LoadUGDDictionary", arg0, arg1)
	ret0, _ := ret[0].(*entity.LoadColvirReportDataElemResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadUGDDictionary indicates an expected call of LoadUGDDictionary.
func (mr *MockColvirProviderMockRecorder) LoadUGDDictionary(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadUGDDictionary", reflect.TypeOf((*MockColvirProvider)(nil).LoadUGDDictionary), arg0, arg1)
}

// LoanCalcLoadPreSchedule mocks base method.
func (m *MockColvirProvider) LoanCalcLoadPreSchedule(arg0 context.Context, arg1 *entity.LoanCalcLoadPreScheduleReq) (*entity.LoanCalcLoadPreScheduleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LoanCalcLoadPreSchedule", arg0, arg1)
	ret0, _ := ret[0].(*entity.LoanCalcLoadPreScheduleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoanCalcLoadPreSchedule indicates an expected call of LoanCalcLoadPreSchedule.
func (mr *MockColvirProviderMockRecorder) LoanCalcLoadPreSchedule(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoanCalcLoadPreSchedule", reflect.TypeOf((*MockColvirProvider)(nil).LoanCalcLoadPreSchedule), arg0, arg1)
}

// RepayLoanEarly mocks base method.
func (m *MockColvirProvider) RepayLoanEarly(arg0 context.Context, arg1 *entity.RepayLoanEarlyReq) (*entity.RepayLoanEarlyResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RepayLoanEarly", arg0, arg1)
	ret0, _ := ret[0].(*entity.RepayLoanEarlyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RepayLoanEarly indicates an expected call of RepayLoanEarly.
func (mr *MockColvirProviderMockRecorder) RepayLoanEarly(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RepayLoanEarly", reflect.TypeOf((*MockColvirProvider)(nil).RepayLoanEarly), arg0, arg1)
}

// RequestCheckClientAgreement mocks base method.
func (m *MockColvirProvider) RequestCheckClientAgreement(arg0 context.Context, arg1 entity.CheckClientAgreementRequest) (*entity.CheckClientAgreementResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestCheckClientAgreement", arg0, arg1)
	ret0, _ := ret[0].(*entity.CheckClientAgreementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestCheckClientAgreement indicates an expected call of RequestCheckClientAgreement.
func (mr *MockColvirProviderMockRecorder) RequestCheckClientAgreement(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestCheckClientAgreement", reflect.TypeOf((*MockColvirProvider)(nil).RequestCheckClientAgreement), arg0, arg1)
}

// RequestCheckDomesticMassPayment mocks base method.
func (m *MockColvirProvider) RequestCheckDomesticMassPayment(arg0 context.Context, arg1 *entity.CheckDomesticMassPaymentRequest) (*entity.CheckDomesticMassPaymentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestCheckDomesticMassPayment", arg0, arg1)
	ret0, _ := ret[0].(*entity.CheckDomesticMassPaymentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestCheckDomesticMassPayment indicates an expected call of RequestCheckDomesticMassPayment.
func (mr *MockColvirProviderMockRecorder) RequestCheckDomesticMassPayment(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestCheckDomesticMassPayment", reflect.TypeOf((*MockColvirProvider)(nil).RequestCheckDomesticMassPayment), arg0, arg1)
}

// RequestCheckDomesticPayment mocks base method.
func (m *MockColvirProvider) RequestCheckDomesticPayment(arg0 context.Context, arg1 *entity.CheckDomesticPaymentRequest) (*entity.CheckDomesticPaymentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestCheckDomesticPayment", arg0, arg1)
	ret0, _ := ret[0].(*entity.CheckDomesticPaymentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestCheckDomesticPayment indicates an expected call of RequestCheckDomesticPayment.
func (mr *MockColvirProviderMockRecorder) RequestCheckDomesticPayment(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestCheckDomesticPayment", reflect.TypeOf((*MockColvirProvider)(nil).RequestCheckDomesticPayment), arg0, arg1)
}

// RequestCloseLimit mocks base method.
func (m *MockColvirProvider) RequestCloseLimit(arg0 context.Context, arg1 *entity.CloseLimitReq) (*entity.CloseLimitResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestCloseLimit", arg0, arg1)
	ret0, _ := ret[0].(*entity.CloseLimitResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestCloseLimit indicates an expected call of RequestCloseLimit.
func (mr *MockColvirProviderMockRecorder) RequestCloseLimit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestCloseLimit", reflect.TypeOf((*MockColvirProvider)(nil).RequestCloseLimit), arg0, arg1)
}

// RequestCreateClient mocks base method.
func (m *MockColvirProvider) RequestCreateClient(arg0 context.Context, arg1 *entity.CreateClientRequest) (*entity.CreateClientResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestCreateClient", arg0, arg1)
	ret0, _ := ret[0].(*entity.CreateClientResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestCreateClient indicates an expected call of RequestCreateClient.
func (mr *MockColvirProviderMockRecorder) RequestCreateClient(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestCreateClient", reflect.TypeOf((*MockColvirProvider)(nil).RequestCreateClient), arg0, arg1)
}

// RequestCreateClientAgreement mocks base method.
func (m *MockColvirProvider) RequestCreateClientAgreement(arg0 context.Context, arg1 entity.CreateClientAgreementRequest) (*entity.CreateClientAgreementResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestCreateClientAgreement", arg0, arg1)
	ret0, _ := ret[0].(*entity.CreateClientAgreementResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestCreateClientAgreement indicates an expected call of RequestCreateClientAgreement.
func (mr *MockColvirProviderMockRecorder) RequestCreateClientAgreement(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestCreateClientAgreement", reflect.TypeOf((*MockColvirProvider)(nil).RequestCreateClientAgreement), arg0, arg1)
}

// RequestCreateClientSMEIP mocks base method.
func (m *MockColvirProvider) RequestCreateClientSMEIP(arg0 context.Context, arg1 *entity.CreateClientIPRequest) (*entity.CreateClientIPResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestCreateClientSMEIP", arg0, arg1)
	ret0, _ := ret[0].(*entity.CreateClientIPResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestCreateClientSMEIP indicates an expected call of RequestCreateClientSMEIP.
func (mr *MockColvirProviderMockRecorder) RequestCreateClientSMEIP(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestCreateClientSMEIP", reflect.TypeOf((*MockColvirProvider)(nil).RequestCreateClientSMEIP), arg0, arg1)
}

// RequestCreateLimit mocks base method.
func (m *MockColvirProvider) RequestCreateLimit(arg0 context.Context, arg1 *entity.CreateLimitReq) (*entity.CreateLimitResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestCreateLimit", arg0, arg1)
	ret0, _ := ret[0].(*entity.CreateLimitResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestCreateLimit indicates an expected call of RequestCreateLimit.
func (mr *MockColvirProviderMockRecorder) RequestCreateLimit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestCreateLimit", reflect.TypeOf((*MockColvirProvider)(nil).RequestCreateLimit), arg0, arg1)
}

// RequestDeleteLimit mocks base method.
func (m *MockColvirProvider) RequestDeleteLimit(arg0 context.Context, arg1 *entity.DeleteLimitReq) (*entity.DeleteLimitResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestDeleteLimit", arg0, arg1)
	ret0, _ := ret[0].(*entity.DeleteLimitResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestDeleteLimit indicates an expected call of RequestDeleteLimit.
func (mr *MockColvirProviderMockRecorder) RequestDeleteLimit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestDeleteLimit", reflect.TypeOf((*MockColvirProvider)(nil).RequestDeleteLimit), arg0, arg1)
}

// RequestDepositOperation mocks base method.
func (m *MockColvirProvider) RequestDepositOperation(arg0 context.Context, arg1 *entity.DepositOperationRequest) (*entity.DepositOperationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestDepositOperation", arg0, arg1)
	ret0, _ := ret[0].(*entity.DepositOperationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestDepositOperation indicates an expected call of RequestDepositOperation.
func (mr *MockColvirProviderMockRecorder) RequestDepositOperation(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestDepositOperation", reflect.TypeOf((*MockColvirProvider)(nil).RequestDepositOperation), arg0, arg1)
}

// RequestExecuteDomesticMassPayment mocks base method.
func (m *MockColvirProvider) RequestExecuteDomesticMassPayment(arg0 context.Context, arg1 *entity.ExecuteDomesticMassPaymentRequest) (*entity.ExecuteDomesticMassPaymentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestExecuteDomesticMassPayment", arg0, arg1)
	ret0, _ := ret[0].(*entity.ExecuteDomesticMassPaymentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestExecuteDomesticMassPayment indicates an expected call of RequestExecuteDomesticMassPayment.
func (mr *MockColvirProviderMockRecorder) RequestExecuteDomesticMassPayment(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestExecuteDomesticMassPayment", reflect.TypeOf((*MockColvirProvider)(nil).RequestExecuteDomesticMassPayment), arg0, arg1)
}

// RequestExecuteDomesticPayment mocks base method.
func (m *MockColvirProvider) RequestExecuteDomesticPayment(arg0 context.Context, arg1 *entity.ExecuteDomesticPaymentRequest) (*entity.ExecuteDomesticPaymentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestExecuteDomesticPayment", arg0, arg1)
	ret0, _ := ret[0].(*entity.ExecuteDomesticPaymentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestExecuteDomesticPayment indicates an expected call of RequestExecuteDomesticPayment.
func (mr *MockColvirProviderMockRecorder) RequestExecuteDomesticPayment(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestExecuteDomesticPayment", reflect.TypeOf((*MockColvirProvider)(nil).RequestExecuteDomesticPayment), arg0, arg1)
}

// RequestFindClient mocks base method.
func (m *MockColvirProvider) RequestFindClient(arg0 context.Context, arg1 entity.FindClientReq) (*entity.FindClientResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestFindClient", arg0, arg1)
	ret0, _ := ret[0].(*entity.FindClientResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestFindClient indicates an expected call of RequestFindClient.
func (mr *MockColvirProviderMockRecorder) RequestFindClient(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestFindClient", reflect.TypeOf((*MockColvirProvider)(nil).RequestFindClient), arg0, arg1)
}

// RequestFindClientAccountsList mocks base method.
func (m *MockColvirProvider) RequestFindClientAccountsList(arg0 context.Context, arg1 entity.RequestFindClientAccountsListReq) (*entity.FindClientAccountsListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestFindClientAccountsList", arg0, arg1)
	ret0, _ := ret[0].(*entity.FindClientAccountsListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestFindClientAccountsList indicates an expected call of RequestFindClientAccountsList.
func (mr *MockColvirProviderMockRecorder) RequestFindClientAccountsList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestFindClientAccountsList", reflect.TypeOf((*MockColvirProvider)(nil).RequestFindClientAccountsList), arg0, arg1)
}

// RequestGetClient mocks base method.
func (m *MockColvirProvider) RequestGetClient(arg0 context.Context, arg1 *entity.GetClientRequest) (*entity.GetClientResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestGetClient", arg0, arg1)
	ret0, _ := ret[0].(*entity.GetClientResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestGetClient indicates an expected call of RequestGetClient.
func (mr *MockColvirProviderMockRecorder) RequestGetClient(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestGetClient", reflect.TypeOf((*MockColvirProvider)(nil).RequestGetClient), arg0, arg1)
}

// RequestGetClientCard mocks base method.
func (m *MockColvirProvider) RequestGetClientCard(arg0 context.Context, arg1 *entity.GetClientCardRequest) (*entity.GetClientCardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestGetClientCard", arg0, arg1)
	ret0, _ := ret[0].(*entity.GetClientCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestGetClientCard indicates an expected call of RequestGetClientCard.
func (mr *MockColvirProviderMockRecorder) RequestGetClientCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestGetClientCard", reflect.TypeOf((*MockColvirProvider)(nil).RequestGetClientCard), arg0, arg1)
}

// RequestGetClientDeposits mocks base method.
func (m *MockColvirProvider) RequestGetClientDeposits(arg0 context.Context, arg1 *entity.GetClientDepositsRequest) (*entity.GetClientDepositsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestGetClientDeposits", arg0, arg1)
	ret0, _ := ret[0].(*entity.GetClientDepositsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestGetClientDeposits indicates an expected call of RequestGetClientDeposits.
func (mr *MockColvirProviderMockRecorder) RequestGetClientDeposits(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestGetClientDeposits", reflect.TypeOf((*MockColvirProvider)(nil).RequestGetClientDeposits), arg0, arg1)
}

// RequestGetClientIP mocks base method.
func (m *MockColvirProvider) RequestGetClientIP(arg0 context.Context, arg1 *entity.GetClientIPRequest) (*entity.GetClientIPResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestGetClientIP", arg0, arg1)
	ret0, _ := ret[0].(*entity.GetClientIPResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestGetClientIP indicates an expected call of RequestGetClientIP.
func (mr *MockColvirProviderMockRecorder) RequestGetClientIP(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestGetClientIP", reflect.TypeOf((*MockColvirProvider)(nil).RequestGetClientIP), arg0, arg1)
}

// RequestGetClientIPCard mocks base method.
func (m *MockColvirProvider) RequestGetClientIPCard(arg0 context.Context, arg1 *entity.GetClientIPCardRequest) (*entity.GetClientIPCardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestGetClientIPCard", arg0, arg1)
	ret0, _ := ret[0].(*entity.GetClientIPCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestGetClientIPCard indicates an expected call of RequestGetClientIPCard.
func (mr *MockColvirProviderMockRecorder) RequestGetClientIPCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestGetClientIPCard", reflect.TypeOf((*MockColvirProvider)(nil).RequestGetClientIPCard), arg0, arg1)
}

// RequestGetClientLoans mocks base method.
func (m *MockColvirProvider) RequestGetClientLoans(arg0 context.Context, arg1 *entity.GetClientLoansRequest) (*entity.GetClientLoansResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestGetClientLoans", arg0, arg1)
	ret0, _ := ret[0].(*entity.GetClientLoansResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestGetClientLoans indicates an expected call of RequestGetClientLoans.
func (mr *MockColvirProviderMockRecorder) RequestGetClientLoans(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestGetClientLoans", reflect.TypeOf((*MockColvirProvider)(nil).RequestGetClientLoans), arg0, arg1)
}

// RequestGetLimitInfo mocks base method.
func (m *MockColvirProvider) RequestGetLimitInfo(arg0 context.Context, arg1 *entity.GetLimitInfoReq) (*entity.GetLimitInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestGetLimitInfo", arg0, arg1)
	ret0, _ := ret[0].(*entity.GetLimitInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestGetLimitInfo indicates an expected call of RequestGetLimitInfo.
func (mr *MockColvirProviderMockRecorder) RequestGetLimitInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestGetLimitInfo", reflect.TypeOf((*MockColvirProvider)(nil).RequestGetLimitInfo), arg0, arg1)
}

// RequestLoadAccountTransactions mocks base method.
func (m *MockColvirProvider) RequestLoadAccountTransactions(arg0 context.Context, arg1 *entity.LoadAccountTransactionsRequest) (*entity.LoadAccountTransactionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestLoadAccountTransactions", arg0, arg1)
	ret0, _ := ret[0].(*entity.LoadAccountTransactionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestLoadAccountTransactions indicates an expected call of RequestLoadAccountTransactions.
func (mr *MockColvirProviderMockRecorder) RequestLoadAccountTransactions(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestLoadAccountTransactions", reflect.TypeOf((*MockColvirProvider)(nil).RequestLoadAccountTransactions), arg0, arg1)
}

// RequestLoadBankHolidays mocks base method.
func (m *MockColvirProvider) RequestLoadBankHolidays(arg0 context.Context, arg1, arg2 time.Time) (*entity.LoadBankHolidaysResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestLoadBankHolidays", arg0, arg1, arg2)
	ret0, _ := ret[0].(*entity.LoadBankHolidaysResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestLoadBankHolidays indicates an expected call of RequestLoadBankHolidays.
func (mr *MockColvirProviderMockRecorder) RequestLoadBankHolidays(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestLoadBankHolidays", reflect.TypeOf((*MockColvirProvider)(nil).RequestLoadBankHolidays), arg0, arg1, arg2)
}

// RequestLoadBankList mocks base method.
func (m *MockColvirProvider) RequestLoadBankList(arg0 context.Context) (*entity.LoadBankListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestLoadBankList", arg0)
	ret0, _ := ret[0].(*entity.LoadBankListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestLoadBankList indicates an expected call of RequestLoadBankList.
func (mr *MockColvirProviderMockRecorder) RequestLoadBankList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestLoadBankList", reflect.TypeOf((*MockColvirProvider)(nil).RequestLoadBankList), arg0)
}

// RequestLoadDomainHierarchyValues mocks base method.
func (m *MockColvirProvider) RequestLoadDomainHierarchyValues(arg0 context.Context, arg1 string) (*entity.LoadDomainHierarchyValuesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestLoadDomainHierarchyValues", arg0, arg1)
	ret0, _ := ret[0].(*entity.LoadDomainHierarchyValuesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestLoadDomainHierarchyValues indicates an expected call of RequestLoadDomainHierarchyValues.
func (mr *MockColvirProviderMockRecorder) RequestLoadDomainHierarchyValues(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestLoadDomainHierarchyValues", reflect.TypeOf((*MockColvirProvider)(nil).RequestLoadDomainHierarchyValues), arg0, arg1)
}

// RequestLoadDomainValues mocks base method.
func (m *MockColvirProvider) RequestLoadDomainValues(arg0 context.Context, arg1 string) (*entity.LoadDomainValuesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestLoadDomainValues", arg0, arg1)
	ret0, _ := ret[0].(*entity.LoadDomainValuesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestLoadDomainValues indicates an expected call of RequestLoadDomainValues.
func (mr *MockColvirProviderMockRecorder) RequestLoadDomainValues(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestLoadDomainValues", reflect.TypeOf((*MockColvirProvider)(nil).RequestLoadDomainValues), arg0, arg1)
}

// RequestLoadDomesticPaymentStatus mocks base method.
func (m *MockColvirProvider) RequestLoadDomesticPaymentStatus(arg0 context.Context, arg1 []string, arg2 string) (*entity.LoadDomesticPaymentStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestLoadDomesticPaymentStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(*entity.LoadDomesticPaymentStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestLoadDomesticPaymentStatus indicates an expected call of RequestLoadDomesticPaymentStatus.
func (mr *MockColvirProviderMockRecorder) RequestLoadDomesticPaymentStatus(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestLoadDomesticPaymentStatus", reflect.TypeOf((*MockColvirProvider)(nil).RequestLoadDomesticPaymentStatus), arg0, arg1, arg2)
}

// RequestLoanRegisterShd mocks base method.
func (m *MockColvirProvider) RequestLoanRegisterShd(arg0 context.Context, arg1 *entity.LoanRegisterShdReq) (*entity.LoanRegisterShdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestLoanRegisterShd", arg0, arg1)
	ret0, _ := ret[0].(*entity.LoanRegisterShdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestLoanRegisterShd indicates an expected call of RequestLoanRegisterShd.
func (mr *MockColvirProviderMockRecorder) RequestLoanRegisterShd(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestLoanRegisterShd", reflect.TypeOf((*MockColvirProvider)(nil).RequestLoanRegisterShd), arg0, arg1)
}

// RequestLoansCalculateSchedule mocks base method.
func (m *MockColvirProvider) RequestLoansCalculateSchedule(arg0 context.Context, arg1 *entity.LoansCalculateScheduleReq) (*entity.LoansCalculateScheduleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestLoansCalculateSchedule", arg0, arg1)
	ret0, _ := ret[0].(*entity.LoansCalculateScheduleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestLoansCalculateSchedule indicates an expected call of RequestLoansCalculateSchedule.
func (mr *MockColvirProviderMockRecorder) RequestLoansCalculateSchedule(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestLoansCalculateSchedule", reflect.TypeOf((*MockColvirProvider)(nil).RequestLoansCalculateSchedule), arg0, arg1)
}

// RequestLoansCalculateTotalCost mocks base method.
func (m *MockColvirProvider) RequestLoansCalculateTotalCost(arg0 context.Context, arg1 *entity.LoansCalculateTotalCostReq) (*entity.LoansCalculateTotalCostResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestLoansCalculateTotalCost", arg0, arg1)
	ret0, _ := ret[0].(*entity.LoansCalculateTotalCostResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestLoansCalculateTotalCost indicates an expected call of RequestLoansCalculateTotalCost.
func (mr *MockColvirProviderMockRecorder) RequestLoansCalculateTotalCost(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestLoansCalculateTotalCost", reflect.TypeOf((*MockColvirProvider)(nil).RequestLoansCalculateTotalCost), arg0, arg1)
}

// RequestOpenClientCard mocks base method.
func (m *MockColvirProvider) RequestOpenClientCard(arg0 context.Context, arg1 *entity.OpenClientCardRequest) (*entity.OpenClientCardResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestOpenClientCard", arg0, arg1)
	ret0, _ := ret[0].(*entity.OpenClientCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestOpenClientCard indicates an expected call of RequestOpenClientCard.
func (mr *MockColvirProviderMockRecorder) RequestOpenClientCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestOpenClientCard", reflect.TypeOf((*MockColvirProvider)(nil).RequestOpenClientCard), arg0, arg1)
}

// RequestOpenDeposit mocks base method.
func (m *MockColvirProvider) RequestOpenDeposit(arg0 context.Context, arg1 *entity.OpenDepositRequest) (*entity.OpenDepositResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestOpenDeposit", arg0, arg1)
	ret0, _ := ret[0].(*entity.OpenDepositResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestOpenDeposit indicates an expected call of RequestOpenDeposit.
func (mr *MockColvirProviderMockRecorder) RequestOpenDeposit(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestOpenDeposit", reflect.TypeOf((*MockColvirProvider)(nil).RequestOpenDeposit), arg0, arg1)
}

// RequestProvidingLoan mocks base method.
func (m *MockColvirProvider) RequestProvidingLoan(arg0 context.Context, arg1 *entity.ProvidingLoanReq) (*entity.ProvidingLoanResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestProvidingLoan", arg0, arg1)
	ret0, _ := ret[0].(*entity.ProvidingLoanResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestProvidingLoan indicates an expected call of RequestProvidingLoan.
func (mr *MockColvirProviderMockRecorder) RequestProvidingLoan(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestProvidingLoan", reflect.TypeOf((*MockColvirProvider)(nil).RequestProvidingLoan), arg0, arg1)
}

// RequestSaveLoan mocks base method.
func (m *MockColvirProvider) RequestSaveLoan(arg0 context.Context, arg1 *entity.SaveLoanRequest) (*entity.SaveLoanResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestSaveLoan", arg0, arg1)
	ret0, _ := ret[0].(*entity.SaveLoanResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestSaveLoan indicates an expected call of RequestSaveLoan.
func (mr *MockColvirProviderMockRecorder) RequestSaveLoan(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestSaveLoan", reflect.TypeOf((*MockColvirProvider)(nil).RequestSaveLoan), arg0, arg1)
}

// RequestTaxPayer mocks base method.
func (m *MockColvirProvider) RequestTaxPayer(arg0 context.Context, arg1 string) (*entity.FindTaxPayerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestTaxPayer", arg0, arg1)
	ret0, _ := ret[0].(*entity.FindTaxPayerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestTaxPayer indicates an expected call of RequestTaxPayer.
func (mr *MockColvirProviderMockRecorder) RequestTaxPayer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestTaxPayer", reflect.TypeOf((*MockColvirProvider)(nil).RequestTaxPayer), arg0, arg1)
}

// RequestUpdateClient mocks base method.
func (m *MockColvirProvider) RequestUpdateClient(arg0 context.Context, arg1 *entity.UpdateClientRequest) (*entity.UpdateClientResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestUpdateClient", arg0, arg1)
	ret0, _ := ret[0].(*entity.UpdateClientResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestUpdateClient indicates an expected call of RequestUpdateClient.
func (mr *MockColvirProviderMockRecorder) RequestUpdateClient(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestUpdateClient", reflect.TypeOf((*MockColvirProvider)(nil).RequestUpdateClient), arg0, arg1)
}

// RequestUpdateClientSMEIP mocks base method.
func (m *MockColvirProvider) RequestUpdateClientSMEIP(arg0 context.Context, arg1 *entity.UpdateClientIPRequest) (*entity.UpdateClientIPResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestUpdateClientSMEIP", arg0, arg1)
	ret0, _ := ret[0].(*entity.UpdateClientIPResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestUpdateClientSMEIP indicates an expected call of RequestUpdateClientSMEIP.
func (mr *MockColvirProviderMockRecorder) RequestUpdateClientSMEIP(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestUpdateClientSMEIP", reflect.TypeOf((*MockColvirProvider)(nil).RequestUpdateClientSMEIP), arg0, arg1)
}
