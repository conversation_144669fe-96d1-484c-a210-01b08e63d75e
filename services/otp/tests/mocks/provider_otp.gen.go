// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/pkg/otp (interfaces: ProviderOtp)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"

	otp "git.redmadrobot.com/zaman/backend/zaman/pkg/otp"
)

// MockProviderOtp is a mock of ProviderOtp interface.
type MockProviderOtp struct {
	ctrl     *gomock.Controller
	recorder *MockProviderOtpMockRecorder
}

// MockProviderOtpMockRecorder is the mock recorder for MockProviderOtp.
type MockProviderOtpMockRecorder struct {
	mock *MockProviderOtp
}

// NewMockProviderOtp creates a new mock instance.
func NewMockProviderOtp(ctrl *gomock.Controller) *MockProviderOtp {
	mock := &MockProviderOtp{ctrl: ctrl}
	mock.recorder = &MockProviderOtpMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProviderOtp) EXPECT() *MockProviderOtpMockRecorder {
	return m.recorder
}

// CreateNewAttempt mocks base method.
func (m *MockProviderOtp) CreateNewAttempt(arg0 context.Context, arg1 *otp.Request) (*otp.Request, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateNewAttempt", arg0, arg1)
	ret0, _ := ret[0].(*otp.Request)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateNewAttempt indicates an expected call of CreateNewAttempt.
func (mr *MockProviderOtpMockRecorder) CreateNewAttempt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNewAttempt", reflect.TypeOf((*MockProviderOtp)(nil).CreateNewAttempt), arg0, arg1)
}

// CreateNewOtp mocks base method.
func (m *MockProviderOtp) CreateNewOtp(arg0 context.Context, arg1, arg2 string, arg3 []byte) (*otp.Request, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateNewOtp", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*otp.Request)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateNewOtp indicates an expected call of CreateNewOtp.
func (mr *MockProviderOtpMockRecorder) CreateNewOtp(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNewOtp", reflect.TypeOf((*MockProviderOtp)(nil).CreateNewOtp), arg0, arg1, arg2, arg3)
}

// GetOtpRequestByAction mocks base method.
func (m *MockProviderOtp) GetOtpRequestByAction(arg0 context.Context, arg1, arg2 string) (*otp.Request, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOtpRequestByAction", arg0, arg1, arg2)
	ret0, _ := ret[0].(*otp.Request)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOtpRequestByAction indicates an expected call of GetOtpRequestByAction.
func (mr *MockProviderOtpMockRecorder) GetOtpRequestByAction(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOtpRequestByAction", reflect.TypeOf((*MockProviderOtp)(nil).GetOtpRequestByAction), arg0, arg1, arg2)
}

// GetOtpRequestByAttemptID mocks base method.
func (m *MockProviderOtp) GetOtpRequestByAttemptID(arg0 context.Context, arg1 string) (*otp.Request, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOtpRequestByAttemptID", arg0, arg1)
	ret0, _ := ret[0].(*otp.Request)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOtpRequestByAttemptID indicates an expected call of GetOtpRequestByAttemptID.
func (mr *MockProviderOtpMockRecorder) GetOtpRequestByAttemptID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOtpRequestByAttemptID", reflect.TypeOf((*MockProviderOtp)(nil).GetOtpRequestByAttemptID), arg0, arg1)
}

// ValidateCode mocks base method.
func (m *MockProviderOtp) ValidateCode(arg0 context.Context, arg1 *otp.Request, arg2 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateCode", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateCode indicates an expected call of ValidateCode.
func (mr *MockProviderOtpMockRecorder) ValidateCode(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateCode", reflect.TypeOf((*MockProviderOtp)(nil).ValidateCode), arg0, arg1, arg2)
}
