// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package tests

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"reflect"
)

// LoadRedisFixture loads data for Redis from a JSON file located in the fixtures directory.
func (s *Suite) LoadRedisFixture() {
	s.T().Helper()

	// Получаем имя текущего теста, чтобы определить путь к файлу фикстуры
	testName := s.getCurrentTestName()

	// Определяем путь к файлу фикстуры Redis
	filePath := filepath.Join("./fixtures", testName, "redis.json")

	// Читаем JSON файл
	data, err := os.ReadFile(filePath)
	if err != nil {
		s.T().Fatalf("Error reading Redis fixture file: %v", err)
	}

	// Парсим JSON данные в map[string]interface{}, где значение может быть объектом, строкой и т.д.
	var fixtureData map[string]interface{}
	if err := json.Unmarshal(data, &fixtureData); err != nil {
		s.T().Fatalf("Error parsing Redis fixture JSON data: %v", err)
	}

	// Загружаем данные в Redis
	for key, value := range fixtureData {
		var redisValue string

		// Проверяем тип значения
		switch v := value.(type) {
		case string:
			// Если это строка, сохраняем её напрямую
			redisValue = v
		case float64:
			// Если это число, преобразуем его в строку
			redisValue = fmt.Sprintf("%v", v)
		case bool:
			// Если это булево значение, сохраняем его как "true" или "false"
			redisValue = fmt.Sprintf("%v", v)
		default:
			// Если это объект или любой другой сложный тип, сериализуем его в JSON
			jsonValue, err := json.Marshal(v)
			if err != nil {
				s.T().Fatalf("Error marshaling Redis value for key %s: %v", key, err)
			}
			redisValue = string(jsonValue)
		}

		// Устанавливаем ключ и значение в Redis
		err = s.redis.Set(s.ctx, key, redisValue, 0)
		if err != nil {
			s.T().Fatalf("Error setting Redis key %s: %v", key, err)
		}
	}
}

// LoadJSONFixture loads a fixture from a JSON file, using the struct's name in lowercase as the filename.
func (s *Suite) LoadJSONFixture(structType interface{}) interface{} {
	s.T().Helper()

	// Get the name of the struct type using reflection
	typeName := reflect.TypeOf(structType).Name()

	// Convert the struct name and add the .json extension
	filename := typeName + ".json"

	// Get the current test name to construct the path to the fixture
	testName := s.getCurrentTestName()

	// Construct the path to the fixture file
	filePath := filepath.Join("./fixtures", testName, filename)

	// Open the file
	file, err := os.Open(filePath)
	if err != nil {
		s.T().Fatalf("Failed to open file %s: %v", filePath, err)
	}
	defer file.Close()

	// Create a new instance of the passed type using reflection
	fixture := reflect.New(reflect.TypeOf(structType)).Interface()

	// Read and decode the JSON into the created struct
	decoder := json.NewDecoder(file)
	if err := decoder.Decode(fixture); err != nil {
		s.T().Fatalf("Failed to decode JSON from file %s: %v", filePath, err)
	}

	// Return the decoded struct
	return fixture
}
