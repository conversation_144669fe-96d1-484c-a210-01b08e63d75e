// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package usecase

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/otp/usecase -i Otp -t ../../../etc/templates/hook.tmpl -o usecase_hook.gen.go -l ""

import (
	"context"

	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"

	"git.redmadrobot.com/zaman/backend/zaman/services/otp/entity"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"
)

var _ Otp = (*OtpHook)(nil)

// OtpHook implements Otp interface wrapper
type OtpHook struct {
	Otp
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// GenerateCode implements Otp
func (_w *OtpHook) GenerateCode(ctx context.Context, req *entity.GenerateCodeReq) (gp1 *entity.GenerateCodeResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.Otp, "GenerateCode", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Otp, "GenerateCode", _params)

	gp1, err = _w.Otp.GenerateCode(_ctx, req)
	_w._postCall.Hook(_ctx, _w.Otp, "GenerateCode", []any{gp1, err})
	return gp1, err
}

// GenerateRetryCode implements Otp
func (_w *OtpHook) GenerateRetryCode(ctx context.Context, req *entity.GenerateRetryCodeReq) (gp1 *entity.GenerateRetryCodeResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.Otp, "GenerateRetryCode", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Otp, "GenerateRetryCode", _params)

	gp1, err = _w.Otp.GenerateRetryCode(_ctx, req)
	_w._postCall.Hook(_ctx, _w.Otp, "GenerateRetryCode", []any{gp1, err})
	return gp1, err
}

// HealthCheck implements Otp
func (_w *OtpHook) HealthCheck(ctx context.Context) (hp1 *entity.Health, err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.Otp, "HealthCheck", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Otp, "HealthCheck", _params)

	hp1, err = _w.Otp.HealthCheck(_ctx)
	_w._postCall.Hook(_ctx, _w.Otp, "HealthCheck", []any{hp1, err})
	return hp1, err
}

// HealthEvent implements Otp
func (_w *OtpHook) HealthEvent(ctx context.Context, message *kafka.Message) {
	_params := []any{ctx, message}
	defer _w._onPanic.Hook(_w.Otp, "HealthEvent", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Otp, "HealthEvent", _params)

	_w.Otp.HealthEvent(_ctx, message)
	_w._postCall.Hook(_ctx, _w.Otp, "HealthEvent", []any{})
	return
}

// InitConsumer implements Otp
func (_w *OtpHook) InitConsumer(ctx context.Context) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.Otp, "InitConsumer", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Otp, "InitConsumer", _params)

	_w.Otp.InitConsumer(_ctx)
	_w._postCall.Hook(_ctx, _w.Otp, "InitConsumer", []any{})
	return
}

// ValidateCode implements Otp
func (_w *OtpHook) ValidateCode(ctx context.Context, req *entity.ValidateCodeReq) (vp1 *entity.ValidateCodeResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.Otp, "ValidateCode", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Otp, "ValidateCode", _params)

	vp1, err = _w.Otp.ValidateCode(_ctx, req)
	_w._postCall.Hook(_ctx, _w.Otp, "ValidateCode", []any{vp1, err})
	return vp1, err
}

// NewOtpHook returns OtpHook
func NewOtpHook(object Otp, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *OtpHook {
	return &OtpHook{
		Otp:         object,
		_beforeCall: beforeCall,
		_postCall:   postCall,
		_onPanic:    onPanic,
	}
}
