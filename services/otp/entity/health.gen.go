// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package entity

import (
	"google.golang.org/grpc/health/grpc_health_v1"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/otp"
)

type (
	Health struct {
		Status bool
	}
)

func MakeHealth(status bool) *Health {
	return &Health{
		Status: status,
	}
}

func MakeHealthEntityToPb(req *Health) *pb.HealthCheckResp {
	return &pb.HealthCheckResp{
		Status: req.Status,
	}
}

func (h Health) toServingStatus() grpc_health_v1.HealthCheckResponse_ServingStatus {
	if h.Status {
		return grpc_health_v1.HealthCheckResponse_SERVING
	}
	return grpc_health_v1.HealthCheckResponse_NOT_SERVING
}

// MakeCheckEntityToPb создает объект из Health в grpc_health_v1.HealthCheckResponse для возврата ответа из сервиса
func MakeCheckEntityToPb(res *Health) *grpc_health_v1.HealthCheckResponse {
	if res == nil {
		return &grpc_health_v1.HealthCheckResponse{
			Status: grpc_health_v1.HealthCheckResponse_UNKNOWN,
		}
	}

	return &grpc_health_v1.HealthCheckResponse{Status: res.toServingStatus()}
}

// MakeListEntityToPb создает объект из Health в pb.HealthCheckResponse для возврата ответа из сервиса
func MakeListEntityToPb(res *Health) *grpc_health_v1.HealthListResponse {
	serviceRes := MakeCheckEntityToPb(res)

	resp := &grpc_health_v1.HealthListResponse{
		Statuses: map[string]*grpc_health_v1.HealthCheckResponse{"otp": serviceRes},
	}

	return resp
}
