package usecase

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"slices"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/conversion"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"

	"git.redmadrobot.com/zaman/backend/zaman/services/liveness/consts"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/common"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"

	cfg "git.redmadrobot.com/zaman/backend/zaman/config/services/users"
	usersErrs "git.redmadrobot.com/zaman/backend/zaman/errs/users"
	entity2 "git.redmadrobot.com/zaman/backend/zaman/pkg/pkb/entity"
	usersConsts "git.redmadrobot.com/zaman/backend/zaman/services/users/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/users/entity"
	amlBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/aml-bridge"
	"git.redmadrobot.com/zaman/backend/zaman/specs/proto/antifraud"
	colvirBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
	"git.redmadrobot.com/zaman/backend/zaman/specs/proto/dictionary"
	pkbBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/pkb-bridge"
)

const (
	xDataFoundStatus            = 1
	xDataNotFoundStatus         = 2
	xDataUnknownStatus          = 3
	xDataInsufficientDataStatus = 4
	docNotFoundStr              = "Документ со статусом 00 (ДЕЙСТВИТЕЛЕН) не найден"
)

func (u *useCasesImpl) doJurSearch(ctx context.Context, iin string) (*pkbBridge.SendJurSearchByIinResp, bool, bool, error) {
	l := logs.FromContext(ctx)

	l.Info().Msg("SmeConfirmLoginVerification: start")

	l.Info().Msg("SmeConfirmLoginVerification: starting verification process")
	l.Info().Msg("Verification step 1: Starting juristic person information check")
	// получение информации о юр лице
	jurSearchResp, err := u.Providers.Pkbbridge.SendJurSearchByIin(ctx, &pkbBridge.SendJurSearchByIinReq{
		Iin: iin,
	})
	if err != nil {
		l.Error().Err(err).Msg("Check jur search error")
		return nil, false, true, err
	}
	l.Info().Msg("Verification step 1: Juristic person information check completed")

	// перенесено сюда, т.к в дальнейшем мы опираемся на результаты поиска jurSearch(его кэш).
	// И при отключении хождения в jurSearch в кэше пусто и валимся на identify.
	l.Info().Msgf("Skip verify sme is %t", u.cfg.App.FeatureToggles.IsEnabled(cfg.SkipVerifySMEFTN))
	if u.cfg.App.FeatureToggles.IsEnabled(cfg.SkipVerifySMEFTN) {
		l.Info().Msg("SmeConfirmLoginVerification: skipping all verification steps")
		return nil, true, true, nil
	}

	l.Info().Msg("Verification step 2: Starting restricted OKED codes check")
	// Сервис по проверке запрещенных ОКЭД
	err = u.EligibleJurSearch(ctx, jurSearchResp)
	if err != nil {
		l.Error().Err(err).Msg("Check jur search error")
		return nil, false, true, err
	}
	l.Info().Msg("Verification step 2: Restricted OKED codes check completed")

	return jurSearchResp, false, false, nil
}

func (u *useCasesImpl) doPersonalInfoCheck(ctx context.Context, iin string) (*pkbBridge.GetPersonalInfoByIinResp, error) {
	logger := logs.FromContext(ctx)

	logger.Info().Msg("Verification step 3: Starting personal information check")
	// получение информации о пользователе
	userInfo, err := u.Providers.Pkbbridge.GetPersonalInfoByIin(ctx, &pkbBridge.GetPersonalInfoByIinReq{
		Iin: iin,
	})
	if err != nil {
		logger.Error().Err(err).Msg("Check personal info error")
		return nil, err
	}
	logger.Info().Msg("Verification step 3: Personal information check completed")

	return userInfo, nil
}

func (u *useCasesImpl) doTaxpayerInfo(ctx context.Context, iin string) (*colvirBridge.FindTaxPayerResp, error) {
	l := logs.FromContext(ctx)

	l.Info().Msg("Verification step 4: Starting taxpayer status check")
	// Пропуск проверки на ип налогоплательщика для дев среды SME
	if u.cfg.App.FeatureToggles.IsEnabled(cfg.SMESkipTaxPayerCheckFTN) {
		l.Info().Msg("Verification step 4: Skipping taxpayer status check")
		return nil, nil
	}

	// проверка на действующего налогоплательщика
	taxPayer, err := u.Providers.Colvirbridge.FindTaxPayer(ctx, &colvirBridge.FindTaxPayerReq{
		PersonIIN: iin,
	})
	if err != nil {
		l.Error().Err(err).Msg("find taxpayer error")
		return nil, err
	}
	if taxPayer.ErrorStatus == colvirBridge.ErrorStatus_Error {
		l.Error().Msg("find taxpayer error")
		return nil, errors.New("colvir internal error")
	}
	if taxPayer.TaxPayerStatus == colvirBridge.TaxPayerStatus_INACTIVE {
		l.Error().Msg("tax payer is inactive")
		return nil, usersErrs.UsersErrs().TaxPayerInactiveError()
	}
	if taxPayer.IndividualType != colvirBridge.IndividualType_INDIVIDUAL_ENTREPRENEUR {
		l.Error().Msg("tax payer is not individual entrepreneur")
		return nil, usersErrs.UsersErrs().TaxPayerInactiveError()
	}
	l.Info().Msg("Verification step 4: Taxpayer status check completed")

	return taxPayer, nil
}

func (u *useCasesImpl) doPermittedDocuments(ctx context.Context, iin string) (*pkbBridge.GetPermitDocumentsByIinResp, bool, bool, error) {
	l := logs.FromContext(ctx)

	l.Info().Msg("Verification step 5: Starting restricted licenses check")
	// Сервис по проверке запрещенных лицензии
	permitedDocs, err := u.Providers.Pkbbridge.GetPermitDocumentsByIin(ctx, &pkbBridge.GetPermitDocumentsByIinReq{
		Iin: iin,
	})
	if err != nil {
		logs.FromContext(ctx).Err(err).Msg("get permit documents error")
		return nil, false, true, err
	}

	err = u.EligibleActivityByIin(ctx, permitedDocs)
	if err != nil {
		log.Error().Err(err).Msg("Check license error")
		return nil, false, true, err
	}
	l.Info().Msg("Verification step 5: Restricted licenses check completed")

	return permitedDocs, true, false, nil
}

func (u *useCasesImpl) doAML(ctx context.Context,
	user *entity.User,
	colvirData *colvirBridge.FindClientResp,
	userInfo *pkbBridge.GetPersonalInfoByIinResp,
	userSmeInfo *entity.GetUserSmeIPInfoResult,
	jurSearchResp *pkbBridge.SendJurSearchByIinResp,
	permitedDocs *pkbBridge.GetPermitDocumentsByIinResp,
) (bool, error) {
	l := logs.FromContext(ctx)
	l.Info().Msg("Verification step 7: Starting AML check")
	var err error
	var amlCheck bool

	isWhitelisted := slices.Contains(u.cfg.App.NewAMLCheckWhitelist, user.Iin)

	l.Info().Msgf("Verification step 7: iin is whitelisted: %v and toggle enabled: %v", isWhitelisted,
		u.cfg.App.FeatureToggles.IsEnabled(cfg.SMENewAMLCheckForAllFTN))

	if isWhitelisted || u.cfg.App.FeatureToggles.IsEnabled(cfg.SMENewAMLCheckForAllFTN) {
		l.Info().Msg("Verification step 7: New AML check selected")
		// Проверка клиента через сервис AML(новый метод)
		amlCheck, err = u.CheckAMLForIPNew(ctx, user, colvirData, userInfo, userSmeInfo, jurSearchResp, permitedDocs,
			usersConsts.DefaultAmlTypeSMEIP)
	} else {
		l.Info().Msg("Verification step 7: Old AML check selected")
		// Проверка клиента через сервис AML(новый метод)
		amlCheck, err = u.CheckAMLForIPOld(ctx, user, userInfo, jurSearchResp, usersConsts.DefaultAmlTypeSMEIP)
	}

	if err != nil {
		l.Error().Err(err).Msg("Check AML error")
		return false, err
	}

	if !amlCheck {
		l.Error().Msg("AML check failed")
		return false, usersErrs.UsersErrs().CodeAmlCheckFailedError()
	}

	l.Info().Msg("Verification step 7: AML check completed")

	return true, nil
}

func (u *useCasesImpl) doXData(ctx context.Context, iin, name, surname, patronymic string) (bool, error) {
	l := logs.FromContext(ctx)

	l.Info().Msg("Verification step 6: Starting XData information check")
	fullName := fmt.Sprintf("%s %s %s", surname, name, patronymic)

	xdata, err := u.EligibleByXdataInfo(ctx, iin, fullName, entity2.PresetSme)
	if err != nil {
		l.Error().Err(err).Msg("Check XData error")
		return false, err
	}
	if err = u.isXDataUserInfoCheckPassed(ctx, xdata, iin); err != nil {
		l.Error().Err(err).Msg("Check XData error")
		return false, err
	}
	l.Info().Msg("Verification step 6: XData/ADATA information check completed")

	return true, nil
}

func (u *useCasesImpl) doAntifraud(ctx context.Context, user *entity.User,
	userInfo *pkbBridge.GetPersonalInfoByIinResp,
	jurSearchResp *pkbBridge.SendJurSearchByIinResp,
) (bool, error) {
	l := logs.FromContext(ctx)

	l.Info().Msg("Verification step 8: Starting Antifraud check")
	// Проверка клиента через сервис антифрода
	fraudCheck, err := u.CheckAntifraudForIP(ctx, user, userInfo, jurSearchResp)
	if err != nil {
		l.Error().Err(err).Msg("Check Antifraud error")
		return false, err
	}
	if !fraudCheck {
		l.Error().Msg("Antifraud check failed")
		return false, usersErrs.UsersErrs().CodeAntiFraudCheckFailedError()
	}

	l.Info().Msg("Verification step 8: Antifraud check completed")

	return true, nil
}

func (u *useCasesImpl) getIPInfo(ctx context.Context, iin string) (*entity.GetUserSmeIPInfoResult, error) {
	return u.GetUserSmeIPInfo(ctx, &entity.GetUserSmeIPInfoReq{
		Iin: iin,
	})
}

func (u *useCasesImpl) SmeConfirmLoginVerification(ctx context.Context, user *entity.User) (bool, error) {
	l := logs.FromContext(ctx)

	jurSearchResp, res, finished, err := u.doJurSearch(ctx, user.Iin)
	if err != nil {
		return false, err
	}

	if finished {
		return res, nil
	}

	userInfo, err := u.doPersonalInfoCheck(ctx, user.Iin)
	if err != nil {
		return false, err
	}

	_, err = u.doTaxpayerInfo(ctx, user.Iin)
	if err != nil {
		return false, err
	}

	permitedDocs, res, finished, err := u.doPermittedDocuments(ctx, user.Iin)
	if err != nil {
		return false, err
	}

	if finished {
		return res, nil
	}

	_, err = u.doXData(ctx, user.Iin, userInfo.GetName(), userInfo.GetSurname(), userInfo.GetPatronymic())
	if err != nil {
		return false, err
	}

	ipInfo, err := u.getIPInfo(ctx, user.Iin)
	if err != nil {
		return false, err
	}

	userColvirInfo, ClientErr := u.Providers.Colvirbridge.FindClient(ctx, &colvirBridge.FindClientReq{Iin: user.Iin})
	if ClientErr != nil {
		l.Info().Msgf("Error while getting user colvir info: %s", ClientErr.Error())
	}

	_, err = u.doAML(ctx, user, userColvirInfo, userInfo, ipInfo, jurSearchResp, permitedDocs)
	if err != nil {
		return false, err
	}

	_, err = u.doAntifraud(ctx, user, userInfo, jurSearchResp)
	if err != nil {
		return false, err
	}

	l.Info().Msg("SmeConfirmLoginVerification: all verification steps completed successfully")
	return true, nil
}

func (u *useCasesImpl) EligibleJurSearch(ctx context.Context, jurSearchResp *pkbBridge.SendJurSearchByIinResp) error {
	// проверка на не подходит ли юр лицо по окэд коду
	restrictedCodes, err := getRestrictedOkedCodes(ctx, u)
	if err != nil {
		return err
	}
	if slices.Contains(restrictedCodes, jurSearchResp.OkedCode) {
		return usersErrs.UsersErrs().CodeJurSearchNotEligibleError()
	}
	return nil
}

func (u *useCasesImpl) EligibleActivityByIin(ctx context.Context, permitedDocs *pkbBridge.GetPermitDocumentsByIinResp) error {
	// Коды запрещенных лицензий
	restrictedCodes, err := getRestrictedActivityTypeCodes(ctx, u)
	if err != nil {
		return err
	}
	restrictedCodesSet := make(map[string]struct{}, len(restrictedCodes))
	for _, code := range restrictedCodes {
		restrictedCodesSet[code] = struct{}{}
	}

	// Если есть хоть одно совпадение то, отправляем ошибку о несоответствий
	for _, license := range permitedDocs.TaxPayerLicenses {
		if _, found := restrictedCodesSet[license.ActivityType.Code]; found {
			return usersErrs.UsersErrs().CodeNotPermitedActivityTypeError()
		}
	}

	return nil
}

func (u *useCasesImpl) EligibleByXdataInfo(ctx context.Context, iin, fullName, preset string) (*pkbBridge.GetReportResp, error) {
	l := logs.FromContext(ctx)

	if deadline, ok := ctx.Deadline(); ok {
		l.Info().
			Time("deadline", deadline).
			Dur("timeout", time.Until(deadline)).
			Msg("EligibleByXdataInfo: context deadline info")
	} else {
		l.Info().Msg("EligibleByXdataInfo: context has no deadline")
	}

	createReportResp, err := u.Providers.Pkbbridge.CreateReport(ctx, &pkbBridge.CreateReportReq{
		Callback: "",
		Preset:   preset,
		Fields: &pkbBridge.Fields{
			Iin:  iin,
			Name: fullName,
		},
	})
	if err != nil {
		return nil, err
	}

	if createReportResp.ReportID == "" {
		return nil, errors.New("report id is empty")
	}

	l.Info().
		Int("ready_percent", int(createReportResp.ReadyPercentage)).
		Str("status", createReportResp.Status).
		Msg("EligibleByXdataInfo: report created")

	getReportStatusResp := &pkbBridge.GetReportStatusResp{
		ReportID:        createReportResp.ReportID,
		ReadyPercentage: createReportResp.ReadyPercentage,
		Status:          createReportResp.Status,
	}

	startAll := time.Now()
	if getReportStatusResp.ReadyPercentage < 100 {
		ticker := time.NewTicker(time.Duration(u.cfg.App.XdataInfoCheckMillisecond) * time.Millisecond)
		defer ticker.Stop()

		l.Info().
			Str("report_id", getReportStatusResp.ReportID).
			Msg("EligibleByXdataInfo: waiting for report to be ready")

		for getReportStatusResp.ReadyPercentage < 100 {
			select {
			case <-ctx.Done():
				l.Error().Msg("EligibleByXdataInfo: context canceled while waiting for report")
				return nil, ctx.Err()

			case <-ticker.C:
				l.Debug().
					Str("report_id", getReportStatusResp.ReportID).
					Msg("EligibleByXdataInfo: polling GetReportStatus")

				getReportStatusResp, err = u.Providers.Pkbbridge.GetReportStatus(ctx, &pkbBridge.GetReportStatusReq{
					ReportID: createReportResp.ReportID,
				})
				if err != nil {
					l.Error().Err(err).Msg("EligibleByXdataInfo: GetReportStatus failed")
					return nil, err
				}
			}
		}
	}

	l.Info().Msgf("EligibleByXdataInfo: report ready in %v", time.Since(startAll))

	xdataUserInfo, err := u.Providers.Pkbbridge.GetReport(ctx, &pkbBridge.GetReportReq{
		ReportId: getReportStatusResp.ReportID,
	})
	if err != nil {
		l.Error().Err(err).Msg("EligibleByXdataInfo: failed to get report")
		return nil, err
	}

	return xdataUserInfo, nil
}

func processDebtsReportInfo(_ context.Context, info *pkbBridge.Info) error {
	for _, detail := range info.DetailsKk {
		if detail.Title == usersConsts.TotalDebtsTitleKK {
			if detail.Value != "0" {
				return usersErrs.UsersErrs().
					DebtsCheckFailedDetailedError(map[string]string{"debts_amount": detail.Value})
			}
		}
	}
	for _, detail := range info.DetailsRu {
		if detail.Title == usersConsts.TotalDebtsTitleRU {
			if detail.Value != "0" {
				return usersErrs.UsersErrs().
					DebtsCheckFailedDetailedError(map[string]string{"debts_amount": detail.Value})
			}
		}
	}

	return nil
}

// если нам пришел статус, что данных нет или не хватает для поиска - выдаем соотвтествющую ошибку. По факту - просто селектор оишбки
func insufficientDataCheck(ctx context.Context, code string) (bool, error) {
	l := logs.FromContext(ctx)

	switch code {
	case usersConsts.DebtsReportCode: // Сведения об отсутствии (наличии) задолженности, учет по которым ведется в органах государственных доходов
		return true, usersErrs.UsersErrs().DebtsCheckFailedError()
	case usersConsts.FakeCompaniesReportCode: // Список налогоплательщиков, признанных лжепредприятиями
		return false, usersErrs.UsersErrs().FakeCompaniesCheckFailedError()
	case usersConsts.BankruptBusinessReportCode: // Список налогоплательщиков, признанных бездействующими
		return false, usersErrs.UsersErrs().BankruptTaxPayerCheckFailedError()
	default:
		// мы не знаем, как реагировать на этот код
		l.Warn().Msgf("xdata insufficientDataCheck: unknown source code %s", code)
		return false, nil
	}
}

func notFoundDataCheck(_ context.Context, code string) bool {
	return code == usersConsts.DebtsReportCode
}

func sufficientDataCheck(ctx context.Context, xdata *pkbBridge.Source, iin string) (bool, error) {
	l := logs.FromContext(ctx)

	switch xdata.Code {
	case usersConsts.DebtsReportCode: // Сведения об отсутствии (наличии) задолженности, учет по которым ведется в органах государственных доходов
		l.Info().Msgf("%s: found for iin %s. Processing", usersConsts.DebtsReportCode, iin)

		for _, info := range xdata.Infos {
			err := processDebtsReportInfo(ctx, info)
			if err != nil {
				return true, err
			}
		}

		return true, nil
	case usersConsts.FakeCompaniesReportCode: // Список налогоплательщиков, признанных лжепредприятиями
		return false, usersErrs.UsersErrs().FakeCompaniesCheckFailedError()
	case usersConsts.BankruptBusinessReportCode: // Список налогоплательщиков, признанных бездействующими
		return false, usersErrs.UsersErrs().BankruptTaxPayerCheckFailedError()
	default:
		// мы не знаем, как реагировать на этот код
		l.Warn().Msgf("xdata sufficientDataCheck: unknown source code %s", xdata.Code)
		return false, nil
	}
}

func (u *useCasesImpl) processSource(ctx context.Context, xdata *pkbBridge.Source, iin string) (bool, error) {
	l := logs.FromContext(ctx)

	var err error
	debtsSourceFound := false

	switch xdata.Status {
	case xDataUnknownStatus, xDataInsufficientDataStatus:
		l.Info().Msgf("Got insufficient data(code %d) for source %s", xdata.Status, xdata.Code)
		debtsSourceFound, err = insufficientDataCheck(ctx, xdata.Code)
	case xDataNotFoundStatus:
		l.Info().Msgf("Not found subject data(code %d) for source %s", xdata.Status, xdata.Code)
		// пропускаем, 2й код, отмечаем что блок с долгами нам явно пришел
		debtsSourceFound = notFoundDataCheck(ctx, xdata.Code)
	case xDataFoundStatus:
		l.Info().Msgf("Found subject data(code %d) for source %s", xdata.Status, xdata.Code)
		debtsSourceFound, err = sufficientDataCheck(ctx, xdata, iin)
	}

	return debtsSourceFound, err
}

func (u *useCasesImpl) isXDataUserInfoCheckPassed(ctx context.Context, xdataUserInfo *pkbBridge.GetReportResp, iin string) error {
	l := logs.FromContext(ctx)

	if len(xdataUserInfo.Sources) == 0 {
		l.Error().Msgf("isXDataUserInfoCheckPassed: sources is empty, check failed for iin %s", iin) // на secure iin logging
		return usersErrs.UsersErrs().DebtsCheckFailedError()
	}

	var debtsSourceFound bool
	for _, xdata := range xdataUserInfo.Sources {
		isDebtsBlock, err := u.processSource(ctx, xdata, iin)
		if err != nil {
			return err
		}

		debtsSourceFound = debtsSourceFound || isDebtsBlock
	}

	// нам может не прийти источник KGD22
	if !debtsSourceFound {
		l.Error().Msgf("isXDataUserInfoCheckPassed: debts source not found, check failed for iin %s", iin) // на secure iin logging
		return usersErrs.UsersErrs().DebtsCheckFailedError()
	}

	return nil
}

func amlPrefixedUID(uid string, status entity.UserStatus) string {
	if status == consts.UserStatusActive {
		return usersConsts.AmlExistingUserPrefix + uid
	}
	return usersConsts.AmlNewUserPrefix + uid
}

func chooseRole(status entity.UserStatus) string {
	if status == consts.UserStatusActive {
		return usersConsts.AmlNewCheckClientCardRoleExistingUserSmeIP
	}
	return usersConsts.AmlNewCheckClientCardRoleNewUserSmeIP
}

func (u *useCasesImpl) getCountryCode(ctx context.Context, numCountryCode string) (string, error) {
	// Запрос к словарю стран SME по числовому коду
	dict, err := u.Providers.Dictionary.DictGetByName(ctx, &dictionary.DictGetByNameReq{
		DictName: usersConsts.DictNameCountry,
	})
	if err != nil {
		return "", fmt.Errorf("failed to query country dictionary: %w", err)
	}

	resp, err := u.Providers.Dictionary.DocGetListByFilter(ctx, &dictionary.DocGetListByFilterReq{
		DictId: dict.Dict.Id,
		Filters: []*dictionary.Filter{
			{
				DataField: "data.iso.numeric", // Фильтр по числовому ISO коду
				Operation: "=",                // Точное совпадение
				Value:     numCountryCode,     // например, "398" для Казахстана
			},
		},
	})
	if err != nil {
		return "", fmt.Errorf("failed to query country dictionary for numeric code %s: %w", numCountryCode, err)
	}

	// Проверяем, найдены ли результаты
	if len(resp.List) == 0 {
		return "", fmt.Errorf("country not found for numeric code: %s", numCountryCode)
	}

	// Парсим первый результат (должен быть уникальным для числовых кодов)
	var countryData struct {
		ISO struct {
			Alpha2  string `json:"alpha2"`
			Numeric string `json:"numeric"`
		} `json:"iso"`
		Code string `json:"code"`
	}
	err = json.Unmarshal([]byte(resp.List[0].Data), &countryData)
	if err != nil {
		return "", fmt.Errorf("failed to unmarshal country data for numeric code %s: %w", numCountryCode, err)
	}
	if countryData.ISO.Numeric != numCountryCode {
		return "", fmt.Errorf("numeric code %s does not match numeric code from dict %s", numCountryCode, countryData.ISO.Numeric)
	}

	return countryData.Code, nil
}

func getIпfoAsRole(
	bsClientID string,
	user *entity.User,
	userInfo *pkbBridge.GetPersonalInfoByIinResp,
	jurSearchResp *pkbBridge.SendJurSearchByIinResp,
) (*amlBridge.Founder, *amlBridge.BeneficialOwner, *amlBridge.Supervisor) {
	role := chooseRole(user.Status)
	fullname := fmt.Sprintf("%s %s %s", userInfo.GetSurname(), userInfo.GetName(), userInfo.GetPatronymic())

	// Format birth date for AML
	birthdate, err := utils.ReformatDateStr(userInfo.GetDob(), utils.RegDateFormat, utils.DotDocsDateFormat)
	if err != nil {
		// Fallback to empty string if date formatting fails
		birthdate = ""
	}

	founder := amlBridge.Founder{
		BsClientId:   bsClientID,
		Role:         role,
		ClientId:     userInfo.Iin,
		Name:         fullname,
		SubCountry:   userInfo.Address.Region.Code,
		ResCountry:   usersConsts.AmlNewCheckClientCardResCountryCodeSmeIP,
		Type:         usersConsts.AmlNewCheckClientCardTypeSmeIP,
		Urname:       jurSearchResp.Name,
		Ursname:      jurSearchResp.Name,
		Lastname:     userInfo.GetSurname(),
		Firstname:    userInfo.GetName(),
		Middlename:   userInfo.GetPatronymic(),
		ForeignExtra: "",
		AcRegCountry: userInfo.Citizenship.Code,
		AcBirthDate:  birthdate,
	}

	beneficialowner := amlBridge.BeneficialOwner{
		BsClientId:   bsClientID,
		Role:         role,
		ClientId:     userInfo.Iin,
		Name:         fullname,
		SubCountry:   userInfo.Address.Region.Code,
		ResCountry:   usersConsts.AmlNewCheckClientCardResCountryCodeSmeIP,
		Type:         usersConsts.AmlNewCheckClientCardTypeSmeIP,
		Urname:       jurSearchResp.Name,
		Lastname:     userInfo.GetSurname(),
		Firstname:    userInfo.GetName(),
		Middlename:   userInfo.GetPatronymic(),
		ForeignExtra: "",
		AcRegCountry: userInfo.Citizenship.Code,
		AcBirthDate:  birthdate,
	}

	supervisor := amlBridge.Supervisor{
		BsClientId:   bsClientID,
		Role:         role,
		ClientId:     userInfo.Iin,
		Name:         fullname,
		SubCountry:   userInfo.Address.Region.Code,
		ResCountry:   usersConsts.AmlNewCheckClientCardResCountryCodeSmeIP,
		Type:         usersConsts.AmlNewCheckClientCardTypeSmeIP,
		Urname:       jurSearchResp.Name,
		Lastname:     userInfo.GetSurname(),
		Firstname:    userInfo.GetName(),
		Middlename:   userInfo.GetPatronymic(),
		ForeignExtra: "",
		AcRegCountry: userInfo.Citizenship.Code,
		AcBirthDate:  birthdate,
	}

	return &founder, &beneficialowner, &supervisor
}

func chooseBankClient(colvirData *colvirBridge.FindClientResp, userID string) string {
	if colvirData == nil || len(colvirData.Clients) == 0 {
		return userID
	}

	for _, client := range colvirData.Clients {
		if client.Type == usersConsts.ClientIP.Str() {
			return client.ID
		}
	}

	return userID
}

func trimDocNum(num string) string {
	if len(num) < 3 {
		return num
	}
	return num[3:]
}

func (u *useCasesImpl) prepareRequest(ctx context.Context,
	user *entity.User,
	colvirData *colvirBridge.FindClientResp,
	userInfo *pkbBridge.GetPersonalInfoByIinResp,
	userSmeInfo *entity.GetUserSmeIPInfoResult,
	jurSearchResp *pkbBridge.SendJurSearchByIinResp,
	_ *pkbBridge.GetPermitDocumentsByIinResp,
) (*amlBridge.NewCheckOnlineClientCardRequest, error) {
	logger := logs.FromContext(ctx)

	operationDate, err := utils.GetCurrentKzTime()
	if err != nil {
		return nil, err
	}

	// костыль что бы закрыть костыль хранения в виде строки
	regdate, err := utils.ReformatDateStr(userSmeInfo.RegistrationDate, utils.RegDateFormat, utils.DotDocsDateFormat)
	if err != nil {
		logger.Error().Err(err).Msgf("Wrong SME IP registration date format %s", userSmeInfo.RegistrationDate)
		return nil, err
	}
	// Получаем город из KATO маппинга или fallback к региону
	regCity := u.getRegCityWithKATO(ctx, userInfo)

	bsClientID := fmt.Sprintf("000-%s", user.ID)
	fullname := fmt.Sprintf("%s %s %s", userInfo.GetSurname(), userInfo.GetName(), userInfo.GetPatronymic())
	founder, beneficialowner, supervisor := getIпfoAsRole(bsClientID, user, userInfo, jurSearchResp)
	isNew := usersConsts.AmlNewCheckIsNewSmeIP
	// ursName := formatUrsName(personalInfo) // уточнить правила
	requestID := utils.ExtractRequestID(ctx)
	if requestID == "" {
		requestID = uuid.New().String()
	}
	// Определяем тип документа чтобы в дальнейшем искать инфорамцию по нему
	doc := u.findActiveDocumentByPriority(ctx, userInfo)
	docseries, docnum := splitNumbertSeriesByType(doc.Type.Code, doc.Number)
	logger.Info().Msgf("PrepareAMLForIPNew: found active document for aml check %s issuer %s num starts with %s",
		doc.Type.NameRU, doc.IssueOrg.NameRU, trimDocNum(doc.Number))

	bankClient := chooseBankClient(colvirData, user.ID.String())
	klvd := "" // conversion.Ptr(getKlvdFromGetPermitDocumentsByIinResp(permitedDocs)),

	countryCode, err := u.getCountryCode(ctx, userInfo.Address.Country.Code)
	if err != nil {
		return nil, err
	}

	issuer := doc.IssueOrg.NameKZ
	if issuer == "" {
		issuer = doc.IssueOrg.NameRU
	}

	// Отправка запроса в сервис AML
	request := amlBridge.NewCheckOnlineClientCardRequest{
		BsClientId:      &bsClientID,
		ClientId:        userInfo.Iin,
		RegCountryCode:  countryCode,
		RegCity:         regCity,
		CheckType:       u.cfg.App.AmlSmeCheckType,
		TypeList:        u.cfg.App.AmlSmeTypeList,
		IssuedBid:       usersConsts.AmlNewCheckClientCardIssuedBidSmeIP,
		Instance:        usersConsts.AmlNewCheckClientCardInstanceSmeIP,
		User:            usersConsts.AmlNewCheckClientCardUserSmeIP,
		Uid:             amlPrefixedUID(requestID, user.Status),
		Product:         usersConsts.AmlNewCheckClientCardProductSmeIP,
		Roles:           chooseRole(user.Status),
		OperationDate:   operationDate.Format("2006-01-02T15:04:05"),
		UrName:          jurSearchResp.Name,
		UrSname:         jurSearchResp.Name,
		UrFirstHeadName: &fullname,
		Lastname:        userInfo.GetSurname(),
		FirstName:       userInfo.GetName(),
		Middlename:      userInfo.GetPatronymic(),
		Type:            usersConsts.AmlNewCheckClientCardTypeSmeIP,
		BankClient:      conversion.Ptr(usersConsts.AmlNewCheckClientCardBankClientSmeIP),
		ResCountryCode:  usersConsts.AmlNewCheckClientCardResCountryCodeSmeIP,
		ForeignExtra:    conversion.Ptr(""),
		Oked:            &jurSearchResp.OkedCode,
		Klvd:            &klvd,
		AcBirthdate:     regdate,
		AcDocTypeCode:   &doc.Type.NameRU,
		AcDocSeries:     &docseries,
		AcDocNumber:     &docnum,
		AcDocWhom:       &issuer,
		AcDocIssueDate:  &doc.BeginDate,
		AcDocExpireDate: &doc.EndDate,
		AcBirthplace: fmt.Sprintf("%s,%s,%s", userInfo.BirthPlace.GetCountry().NameRU,
			userInfo.BirthPlace.GetDistrict().NameRU, userInfo.BirthPlace.GetRegion().NameRU),
		AcRegCountry:     userInfo.Citizenship.Code,
		SeatCountryCode:  countryCode,
		SeatCity:         regCity,
		Branch:           conversion.Ptr(usersConsts.DefaultAmlBranchCode),
		ClientCardClosed: usersConsts.AmlNewCheckClientCardClientCardClosedSmeIP,
		IsNew:            &isNew,
		BankClientCode:   &bankClient,
		Founders: []*amlBridge.Founder{
			founder,
		},
		BeneficialOwners: []*amlBridge.BeneficialOwner{
			beneficialowner,
		},
		Supervisors: []*amlBridge.Supervisor{
			supervisor,
		},
	}

	return &request, nil
}

func (u *useCasesImpl) CheckAMLForIPNew(
	ctx context.Context,
	user *entity.User,
	personalData *colvirBridge.FindClientResp,
	userInfo *pkbBridge.GetPersonalInfoByIinResp,
	userSmeInfo *entity.GetUserSmeIPInfoResult,
	jurSearchResp *pkbBridge.SendJurSearchByIinResp,
	permitedDocs *pkbBridge.GetPermitDocumentsByIinResp,
	typeSME string,
) (bool, error) {
	logger := logs.FromContext(ctx)
	err := u.validateCheckAMLInput(user, userInfo, jurSearchResp, typeSME)
	if err != nil {
		return false, err
	}

	request, _ := u.prepareRequest(ctx, user, personalData, userInfo, userSmeInfo, jurSearchResp, permitedDocs)
	// Отправка запроса в сервис AML
	amlResponse, err := u.Providers.Amlbridge.NewCheckOnlineClientCard(ctx, request)
	if err != nil {
		// Если флаг SkipAMLErrorVerification включен, то пропускаем ошибку проверки AML
		if u.cfg.App.FeatureToggles.IsEnabled(cfg.SkipAMLErrVerifyFTN) {
			logs.FromContext(ctx).Err(err).
				Str("user_id", user.ID.String()).
				Str("iin", user.Iin).
				Msg("AML check error was skipped due to SkipAMLErrorVerification flag")
			return true, nil
		}
		return false, err
	}

	logger.Info().Msgf("AML response: %+v", amlResponse)
	logger.Info().Msgf("AML status: %d", amlResponse.Status)
	logger.Info().Msgf("User ID: %s", user.ID.String())

	// Проверка статуса ответа
	if amlResponse.Status == 1 || amlResponse.Status == 2 {
		// Если флаг SkipAMLErrorVerification включен, то пропускаем ошибку проверки AML
		if u.cfg.App.FeatureToggles.IsEnabled(cfg.SkipAMLErrVerifyFTN) {
			logs.FromContext(ctx).Info().
				Str("user_id", user.ID.String()).
				Str("iin", user.Iin).
				Int32("aml_status", amlResponse.Status).
				Msg("AML check failed status was skipped due to SkipAMLErrorVerification flag")
			return true, nil
		}
		return false, nil // Клиент найден в списках AML не прошел проверку
	}

	return true, nil // Клиент НЕ найден в списках AML прошел проверку
}

func (u *useCasesImpl) CheckAMLForIPOld(ctx context.Context, user *entity.User, userInfo *pkbBridge.GetPersonalInfoByIinResp, jurSearchResp *pkbBridge.SendJurSearchByIinResp, typeSME string) (bool, error) {
	logger := logs.FromContext(ctx)
	err := u.validateCheckAMLInput(user, userInfo, jurSearchResp, typeSME)
	if err != nil {
		return false, err
	}
	uid := uuid.New().String()
	operationDate, err := utils.GetCurrentKzTime()
	if err != nil {
		return false, err
	}

	doc := u.findActiveDocumentByPriority(ctx, userInfo)

	fullName := utils.JoinStringsWithSpaces(userInfo.GetSurname(), userInfo.GetName(), userInfo.GetPatronymic())
	// Отправка запроса в сервис AML
	amlResponse, err := u.Providers.Amlbridge.CheckOnlineClientCard(ctx, &amlBridge.CheckOnlineClientCardRequest{
		IssuedBid:       usersConsts.DefaultAmlBid,
		Uid:             uid,
		IsNew:           "0",
		OperationDate:   operationDate.Format("2006-01-02T15:04:05"),
		ClientID:        conversion.Ptr(user.Iin),
		BsClientID:      usersConsts.DefaultAmlBranchCode + user.ID.String(),
		UrName:          conversion.Ptr(fullName),
		Type:            conversion.Ptr(typeSME),
		ResCountryCode:  conversion.Ptr(userInfo.Citizenship.NameKZ),
		Oked:            conversion.Ptr(jurSearchResp.OkedCode),
		ForeignExtra:    conversion.Ptr(jurSearchResp.Name),
		AcBirthDate:     conversion.Ptr(userInfo.Dob),
		AcBirthPlace:    conversion.Ptr(userInfo.BirthPlace.City),
		AcRegCountry:    conversion.Ptr(userInfo.Citizenship.Code),
		AcDocTypeCode:   &doc.Type.NameRU,
		AcDocSeries:     &doc.Number,
		AcDocNumber:     &doc.Number,
		AcDocWhom:       &doc.IssueOrg.NameKZ,
		AcDocIssueDate:  &doc.BeginDate,
		RegCountryCode:  conversion.Ptr(userInfo.Address.Country.NameKZ),
		RegCity:         conversion.Ptr(userInfo.Address.Region.NameKZ),
		SeatCountryCode: conversion.Ptr(u.findCountryAddressTemp(userInfo)), // Ищем адрес временной прописки у которого не истек срок
		SeatCity:        conversion.Ptr(u.findCityAddressTemp(userInfo)),    // Ищем адрес временной прописки у которого не истек срок
		Branch:          conversion.Ptr(usersConsts.DefaultAmlBranchCode),
		// TODO тут не хватает поля FOUNDERS/FOUNDER/BSCLIENTID - это для БИН
		// TODO тут не хватает поля FOUNDERS/FOUNDER/ROLE - это для БИН
	})
	if err != nil {
		// Если флаг SkipAMLErrorVerification включен, то пропускаем ошибку проверки AML
		if u.cfg.App.FeatureToggles.IsEnabled(cfg.SkipAMLErrVerifyFTN) {
			logs.FromContext(ctx).Err(err).
				Str("user_id", user.ID.String()).
				Str("iin", user.Iin).
				Msg("AML check error was skipped due to SkipAMLErrorVerification flag")
			return true, nil
		}
		return false, err
	}

	logger.Info().Msgf("AML response: %+v", amlResponse)
	logger.Info().Msgf("AML status: %d", amlResponse.Status)
	logger.Info().Msgf("User ID: %s", user.ID.String())

	// Проверка статуса ответа
	if amlResponse.Status == 1 || amlResponse.Status == 2 {
		// Если флаг SkipAMLErrorVerification включен, то пропускаем ошибку проверки AML
		if u.cfg.App.FeatureToggles.IsEnabled(cfg.SkipAMLErrVerifyFTN) {
			logs.FromContext(ctx).Info().
				Str("user_id", user.ID.String()).
				Str("iin", user.Iin).
				Int32("aml_status", amlResponse.Status).
				Msg("AML check failed status was skipped due to SkipAMLErrorVerification flag")
			return true, nil
		}
		return false, nil // Клиент найден в списках AML не прошел проверку
	}

	return true, nil // Клиент НЕ найден в списках AML прошел проверку
}

func (u *useCasesImpl) CheckAntifraudForIP(
	ctx context.Context,
	user *entity.User,
	userInfo *pkbBridge.GetPersonalInfoByIinResp,
	jurSearchResp *pkbBridge.SendJurSearchByIinResp,
) (bool, error) {
	const fraudCheckSuccessStatus = "allowed"
	logger := logs.FromContext(ctx)

	fraudCheckResponse, err := u.Providers.Antifraud.FraudCheckClient(
		ctx, makeFraudCheckClientRequestForIP(ctx, user, userInfo, jurSearchResp),
	)
	if err != nil {
		// Если флаг SkipAntifraudErrorVerification включен, то пропускаем ошибку проверки Антифрода
		if u.cfg.App.FeatureToggles.IsEnabled(cfg.SkipAntifraudErrorVerificationFTN) {
			logger.Err(err).
				Str("user_id", user.ID.String()).
				Str("iin", user.Iin).
				Msg("Antifroud check error was skipped due to SkipAntifraudErrorVerification flag")
			return true, nil
		}

		return false, err
	}

	logger.Info().Msgf("Antifroud response: %+v", fraudCheckResponse)
	logger.Info().Msgf("Antifroud status: %s", fraudCheckResponse.Status)
	logger.Info().Msgf("User ID: %s", user.ID.String())

	if fraudCheckResponse.Status != fraudCheckSuccessStatus {
		// Если флаг SkipAMLErrorVerification включен, то пропускаем ошибку проверки AML
		if u.cfg.App.FeatureToggles.IsEnabled(cfg.SkipAntifraudErrorVerificationFTN) {
			logger.Error().
				Str("user_id", user.ID.String()).
				Str("iin", user.Iin).
				Str("antifroud_status", fraudCheckResponse.Status).
				Msg("Antifroud check failed status was skipped due to SkipAntifraudErrorVerification flag")
			return true, nil
		}

		return false, err
	}

	return true, nil
}

func makeFraudCheckClientRequestForIP(
	ctx context.Context,
	user *entity.User,
	userInfo *pkbBridge.GetPersonalInfoByIinResp,
	jurSearchResp *pkbBridge.SendJurSearchByIinResp,
) *antifraud.FraudCheckClientReq {
	birthDate := utils.FormatDateToStr(ctx, userInfo.GetDob(), common.DFAccountReference)

	return &antifraud.FraudCheckClientReq{
		ClientType:   "IP",
		Iin:          conversion.Ptr(user.Iin),
		Bin:          conversion.Ptr(user.Iin),
		Firstname:    conversion.Ptr(userInfo.GetName()),
		Lastname:     conversion.Ptr(userInfo.GetSurname()),
		Patronymic:   conversion.Ptr(userInfo.GetPatronymic()),
		Title:        conversion.Ptr(jurSearchResp.Name),
		Phone:        conversion.Ptr(user.Phone),
		BirthDate:    conversion.Ptr(birthDate),
		RegisterDate: conversion.Ptr(jurSearchResp.RegisterDate),
	}
}

func (u *useCasesImpl) findActiveDocumentByPriority(ctx context.Context, req *pkbBridge.GetPersonalInfoByIinResp) *pkbBridge.Document {
	l := logs.FromContext(ctx)

	priority := map[string]int{
		"002": 1, // Удостоверение личности гражданина РК (самый приоритетный)
		"001": 2, // Паспорт гражданина РК
		"003": 3, // Вид на жительство иностранного гражданина
		"004": 4, // Удостоверение лица без гражданства
		"005": 5, // Регистрационное свидетельство
		"015": 6, // Паспорт гражданина иностранного государства
		"100": 7, // Свидетельство о смерти
	}

	currentPriority := 10

	var res *pkbBridge.Document

	for _, doc := range req.Documents {
		l.Info().Msgf("Got document with code: %s", doc.Type.Code)
		if p, ok := priority[doc.Type.Code]; ok && p < currentPriority && doc.Status.Code == "00" {
			l.Info().Msgf("Document with code: %s more important than before. Assigning", doc.Type.Code)
			res = doc
			currentPriority = p
		}
	}

	if res != nil {
		return res
	}

	if len(req.Documents) == 0 {
		l.Warn().Msg("No documents found from GetPersonalInfoByIinResp")
	}

	l.Info().Msgf("Document not found^ returning default 'Документ не найден в ГБДФЛ' doc")

	return &pkbBridge.Document{
		Type: &pkbBridge.NameCode{
			NameRU: "Документ не найден в ГБДФЛ",
			NameKZ: "Документ не найден в ГБДФЛ",
		},
		Number: docNotFoundStr,
		IssueOrg: &pkbBridge.NameCode{
			NameRU: docNotFoundStr,
			NameKZ: docNotFoundStr,
		},
		BeginDate: docNotFoundStr,
		EndDate:   docNotFoundStr,
	}
}

func (u *useCasesImpl) findCountryAddressTemp(req *pkbBridge.GetPersonalInfoByIinResp) string {
	now := time.Now()
	endDateTempAddress := "Срок временной прописки истек"
	for _, address := range req.AddressTemp {
		endDate, err := time.Parse("2006-01-02", address.EndDate) // Парсим дату
		if err != nil {
			continue // Пропускаем, если формат даты некорректный
		}
		if endDate.After(now) { // Если срок еще не истек — сразу возвращаем страну
			return address.Country.NameKZ
		}
	}

	return endDateTempAddress
}

func (u *useCasesImpl) findCityAddressTemp(req *pkbBridge.GetPersonalInfoByIinResp) string {
	now := time.Now()
	endDateTempAddress := "Срок временной прописки истек"
	for _, address := range req.AddressTemp {
		endDate, err := time.Parse("2006-01-02", address.EndDate) // Парсим дату
		if err != nil {
			continue // Пропускаем, если формат даты некорректный
		}
		if endDate.After(now) { // Если срок еще не истек — сразу возвращаем город
			return address.Region.NameKZ
		}
	}

	return endDateTempAddress
}

func (u *useCasesImpl) validateCheckAMLInput(user *entity.User, userInfo *pkbBridge.GetPersonalInfoByIinResp, jurSearchResp *pkbBridge.SendJurSearchByIinResp, typeSME string) error {
	if user == nil {
		return errors.New("user is nil")
	}
	if jurSearchResp == nil {
		return errors.New("jurSearchResp is nil")
	}
	if userInfo == nil {
		return errors.New("userInfo is nil")
	}
	if typeSME == "" {
		return errors.New("typeSME is nil")
	}
	return nil
}

func getRestrictedOkedCodes(ctx context.Context, u *useCasesImpl) ([]string, error) {
	docResp, err := u.Providers.Dictionary.DocGetByName(ctx, &dictionary.DocGetByNameReq{
		DictName: usersConsts.DictNameRestrictedOkedCodes,
		DocName:  usersConsts.DictNameRestrictedOkedCodes,
	})
	if err != nil {
		return nil, err
	}

	var codes struct {
		Codes []string `json:"codes"`
	}
	err = json.Unmarshal([]byte(docResp.Doc.GetData()), &codes)
	if err != nil {
		return nil, err
	}
	return codes.Codes, nil
}

func getRestrictedActivityTypeCodes(ctx context.Context, u *useCasesImpl) ([]string, error) {
	docResp, err := u.Providers.Dictionary.DocGetByName(ctx, &dictionary.DocGetByNameReq{
		DictName: usersConsts.DictNameRestrictedActivityTypeCodes,
		DocName:  usersConsts.DictNameRestrictedActivityTypeCodes,
	})
	if err != nil {
		return nil, err
	}

	var codes struct {
		Codes []string `json:"codes"`
	}
	err = json.Unmarshal([]byte(docResp.Doc.GetData()), &codes)
	if err != nil {
		return nil, err
	}
	return codes.Codes, nil
}

// func getKlvdFromGetPermitDocumentsByIinResp(permitedDocs *pkbBridge.GetPermitDocumentsByIinResp) string {
//	if permitedDocs == nil {
//		return ""
//	}
//	klvds := make([]string, 0, len(permitedDocs.TaxPayerLicenses))
//	for _, licence := range permitedDocs.TaxPayerLicenses {
//		klvds = append(klvds, licence.ActivityType.Code)
//	}
//
//	return strings.Join(klvds, ",")
// }

// getKATOFromPersonalInfo получает KATO информацию из TSOID используя данные из GetPersonalInfoByIinResp
func (u *useCasesImpl) getKATOFromPersonalInfo(ctx context.Context, personalInfo *pkbBridge.GetPersonalInfoByIinResp) (*dictionary.KATOMapFromTSOIDResp, error) {
	logger := logs.FromContext(ctx)

	if personalInfo == nil || personalInfo.Address == nil {
		return nil, fmt.Errorf("personalInfo or address is nil")
	}

	address := personalInfo.Address

	// Подготавливаем запрос для KATO маппинга
	katoReq := &dictionary.KATOMapFromTSOIDReq{
		Country: &dictionary.TSOIDAddrElement{
			Code:       address.GetCountry().GetCode(),
			NameKz:     address.GetCountry().GetNameKZ(),
			NameRu:     address.GetCountry().GetNameRU(),
			ChangeDate: address.GetCountry().GetChangeDate(),
		},
		Region: &dictionary.TSOIDAddrElement{
			Code:       address.GetRegion().GetCode(),
			NameKz:     address.GetRegion().GetNameKZ(),
			NameRu:     address.GetRegion().GetNameRU(),
			ChangeDate: address.GetRegion().GetChangeDate(),
		},
		District: &dictionary.TSOIDAddrElement{
			Code:       address.GetDistrict().GetCode(),
			NameKz:     address.GetDistrict().GetNameKZ(),
			NameRu:     address.GetDistrict().GetNameRU(),
			ChangeDate: address.GetDistrict().GetChangeDate(),
		},
		City:      "", // City не передается в PKB Address
		Street:    address.GetStreet(),
		Building:  address.GetBuilding(),
		Corpus:    "", // Corpus не передается в PKB Address
		Flat:      address.GetFlat(),
		BeginDate: address.GetBeginDate(),
		Lang:      "ru",
	}

	// Вызываем сервис KATO маппинга
	katoResp, err := u.Providers.Dictionary.KATOMapFromTSOID(ctx, katoReq)
	if err != nil {
		logger.Error().Err(err).Msg("Error calling KATOMapFromTSOID service")
		return nil, fmt.Errorf("failed to get KATO mapping: %w", err)
	}

	return katoResp, nil
}

// extractRegCityFromKATO извлекает город из KATO маппинга
// Порядок приоритета: regionCity -> settlement -> city -> district -> region
func extractRegCityFromKATO(katoResp *dictionary.KATOMapFromTSOIDResp) string {
	if katoResp == nil {
		return ""
	}

	// 1. regionCity - первый приоритет
	if regionCity := katoResp.GetRegionCity(); regionCity != "" {
		return regionCity
	}

	// 2. settlement - если нет regionCity
	if settlement := katoResp.GetSettlement(); settlement != "" {
		return settlement
	}

	// 3. city - если нет regionCity и settlement
	if city := katoResp.GetCity(); city != "" {
		return city
	}

	// 4. district - если нет regionCity, settlement, city
	if district := katoResp.GetDistrict(); district != "" {
		return district
	}

	// 5. region - если нет regionCity, settlement, city, district
	if region := katoResp.GetRegion(); region != "" {
		return region
	}

	return ""
}

// getRegCityWithKATO получает город используя KATO маппинг или fallback к региону
func (u *useCasesImpl) getRegCityWithKATO(ctx context.Context, userInfo *pkbBridge.GetPersonalInfoByIinResp) string {
	logger := logs.FromContext(ctx)

	// Пытаемся получить KATO маппинг
	katoResp, err := u.getKATOFromPersonalInfo(ctx, userInfo)
	if err != nil {
		logger.Warn().Err(err).Msg("Failed to get KATO mapping, using fallback")
		// Fallback к старому способу
		if userInfo.Address != nil && userInfo.Address.Region != nil {
			return userInfo.Address.Region.Code
		}
		return ""
	}

	// Извлекаем город из KATO маппинга
	regCity := extractRegCityFromKATO(katoResp)
	if regCity != "" {
		logger.Info().Str("regCity", regCity).Msg("Successfully got city from KATO mapping")
		return regCity
	}

	// Fallback если KATO не вернул город
	logger.Warn().Msg("KATO mapping returned empty city, using fallback")
	if userInfo.Address != nil && userInfo.Address.Region != nil {
		return userInfo.Address.Region.Code
	}

	return ""
}
