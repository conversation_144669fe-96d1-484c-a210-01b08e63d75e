// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/documents/storage/postgres/ent/document"
	"git.redmadrobot.com/zaman/backend/zaman/services/documents/storage/postgres/ent/documentsign"
	"git.redmadrobot.com/zaman/backend/zaman/services/documents/storage/postgres/ent/documenttemplate"
	"git.redmadrobot.com/zaman/backend/zaman/services/documents/storage/postgres/ent/predicate"
)

// DocumentUpdate is the builder for updating Document entities.
type DocumentUpdate struct {
	config
	hooks     []Hook
	mutation  *DocumentMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the DocumentUpdate builder.
func (_u *DocumentUpdate) Where(ps ...predicate.Document) *DocumentUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetType sets the "type" field.
func (_u *DocumentUpdate) SetType(v document.Type) *DocumentUpdate {
	_u.mutation.SetType(v)
	return _u
}

// SetNillableType sets the "type" field if the given value is not nil.
func (_u *DocumentUpdate) SetNillableType(v *document.Type) *DocumentUpdate {
	if v != nil {
		_u.SetType(*v)
	}
	return _u
}

// SetTemplateID sets the "template_id" field.
func (_u *DocumentUpdate) SetTemplateID(v uuid.UUID) *DocumentUpdate {
	_u.mutation.SetTemplateID(v)
	return _u
}

// SetNillableTemplateID sets the "template_id" field if the given value is not nil.
func (_u *DocumentUpdate) SetNillableTemplateID(v *uuid.UUID) *DocumentUpdate {
	if v != nil {
		_u.SetTemplateID(*v)
	}
	return _u
}

// SetUserID sets the "user_id" field.
func (_u *DocumentUpdate) SetUserID(v uuid.UUID) *DocumentUpdate {
	_u.mutation.SetUserID(v)
	return _u
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (_u *DocumentUpdate) SetNillableUserID(v *uuid.UUID) *DocumentUpdate {
	if v != nil {
		_u.SetUserID(*v)
	}
	return _u
}

// ClearUserID clears the value of the "user_id" field.
func (_u *DocumentUpdate) ClearUserID() *DocumentUpdate {
	_u.mutation.ClearUserID()
	return _u
}

// SetFileURL sets the "file_url" field.
func (_u *DocumentUpdate) SetFileURL(v string) *DocumentUpdate {
	_u.mutation.SetFileURL(v)
	return _u
}

// SetNillableFileURL sets the "file_url" field if the given value is not nil.
func (_u *DocumentUpdate) SetNillableFileURL(v *string) *DocumentUpdate {
	if v != nil {
		_u.SetFileURL(*v)
	}
	return _u
}

// AddSignIDs adds the "signs" edge to the DocumentSign entity by IDs.
func (_u *DocumentUpdate) AddSignIDs(ids ...uuid.UUID) *DocumentUpdate {
	_u.mutation.AddSignIDs(ids...)
	return _u
}

// AddSigns adds the "signs" edges to the DocumentSign entity.
func (_u *DocumentUpdate) AddSigns(v ...*DocumentSign) *DocumentUpdate {
	ids := make([]uuid.UUID, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _u.AddSignIDs(ids...)
}

// SetTemplate sets the "template" edge to the DocumentTemplate entity.
func (_u *DocumentUpdate) SetTemplate(v *DocumentTemplate) *DocumentUpdate {
	return _u.SetTemplateID(v.ID)
}

// Mutation returns the DocumentMutation object of the builder.
func (_u *DocumentUpdate) Mutation() *DocumentMutation {
	return _u.mutation
}

// ClearSigns clears all "signs" edges to the DocumentSign entity.
func (_u *DocumentUpdate) ClearSigns() *DocumentUpdate {
	_u.mutation.ClearSigns()
	return _u
}

// RemoveSignIDs removes the "signs" edge to DocumentSign entities by IDs.
func (_u *DocumentUpdate) RemoveSignIDs(ids ...uuid.UUID) *DocumentUpdate {
	_u.mutation.RemoveSignIDs(ids...)
	return _u
}

// RemoveSigns removes "signs" edges to DocumentSign entities.
func (_u *DocumentUpdate) RemoveSigns(v ...*DocumentSign) *DocumentUpdate {
	ids := make([]uuid.UUID, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _u.RemoveSignIDs(ids...)
}

// ClearTemplate clears the "template" edge to the DocumentTemplate entity.
func (_u *DocumentUpdate) ClearTemplate() *DocumentUpdate {
	_u.mutation.ClearTemplate()
	return _u
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *DocumentUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *DocumentUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *DocumentUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *DocumentUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *DocumentUpdate) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := document.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *DocumentUpdate) check() error {
	if v, ok := _u.mutation.GetType(); ok {
		if err := document.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Document.type": %w`, err)}
		}
	}
	if _u.mutation.TemplateCleared() && len(_u.mutation.TemplateIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "Document.template"`)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *DocumentUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DocumentUpdate {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *DocumentUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(document.Table, document.Columns, sqlgraph.NewFieldSpec(document.FieldID, field.TypeUUID))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(document.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(document.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(document.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.GetType(); ok {
		_spec.SetField(document.FieldType, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.UserID(); ok {
		_spec.SetField(document.FieldUserID, field.TypeUUID, value)
	}
	if _u.mutation.UserIDCleared() {
		_spec.ClearField(document.FieldUserID, field.TypeUUID)
	}
	if value, ok := _u.mutation.FileURL(); ok {
		_spec.SetField(document.FieldFileURL, field.TypeString, value)
	}
	if _u.mutation.SignsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   document.SignsTable,
			Columns: []string{document.SignsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(documentsign.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.RemovedSignsIDs(); len(nodes) > 0 && !_u.mutation.SignsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   document.SignsTable,
			Columns: []string{document.SignsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(documentsign.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.SignsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   document.SignsTable,
			Columns: []string{document.SignsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(documentsign.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if _u.mutation.TemplateCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   document.TemplateTable,
			Columns: []string{document.TemplateColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(documenttemplate.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.TemplateIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   document.TemplateTable,
			Columns: []string{document.TemplateColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(documenttemplate.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(_u.modifiers...)
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{document.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// DocumentUpdateOne is the builder for updating a single Document entity.
type DocumentUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *DocumentMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetType sets the "type" field.
func (_u *DocumentUpdateOne) SetType(v document.Type) *DocumentUpdateOne {
	_u.mutation.SetType(v)
	return _u
}

// SetNillableType sets the "type" field if the given value is not nil.
func (_u *DocumentUpdateOne) SetNillableType(v *document.Type) *DocumentUpdateOne {
	if v != nil {
		_u.SetType(*v)
	}
	return _u
}

// SetTemplateID sets the "template_id" field.
func (_u *DocumentUpdateOne) SetTemplateID(v uuid.UUID) *DocumentUpdateOne {
	_u.mutation.SetTemplateID(v)
	return _u
}

// SetNillableTemplateID sets the "template_id" field if the given value is not nil.
func (_u *DocumentUpdateOne) SetNillableTemplateID(v *uuid.UUID) *DocumentUpdateOne {
	if v != nil {
		_u.SetTemplateID(*v)
	}
	return _u
}

// SetUserID sets the "user_id" field.
func (_u *DocumentUpdateOne) SetUserID(v uuid.UUID) *DocumentUpdateOne {
	_u.mutation.SetUserID(v)
	return _u
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (_u *DocumentUpdateOne) SetNillableUserID(v *uuid.UUID) *DocumentUpdateOne {
	if v != nil {
		_u.SetUserID(*v)
	}
	return _u
}

// ClearUserID clears the value of the "user_id" field.
func (_u *DocumentUpdateOne) ClearUserID() *DocumentUpdateOne {
	_u.mutation.ClearUserID()
	return _u
}

// SetFileURL sets the "file_url" field.
func (_u *DocumentUpdateOne) SetFileURL(v string) *DocumentUpdateOne {
	_u.mutation.SetFileURL(v)
	return _u
}

// SetNillableFileURL sets the "file_url" field if the given value is not nil.
func (_u *DocumentUpdateOne) SetNillableFileURL(v *string) *DocumentUpdateOne {
	if v != nil {
		_u.SetFileURL(*v)
	}
	return _u
}

// AddSignIDs adds the "signs" edge to the DocumentSign entity by IDs.
func (_u *DocumentUpdateOne) AddSignIDs(ids ...uuid.UUID) *DocumentUpdateOne {
	_u.mutation.AddSignIDs(ids...)
	return _u
}

// AddSigns adds the "signs" edges to the DocumentSign entity.
func (_u *DocumentUpdateOne) AddSigns(v ...*DocumentSign) *DocumentUpdateOne {
	ids := make([]uuid.UUID, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _u.AddSignIDs(ids...)
}

// SetTemplate sets the "template" edge to the DocumentTemplate entity.
func (_u *DocumentUpdateOne) SetTemplate(v *DocumentTemplate) *DocumentUpdateOne {
	return _u.SetTemplateID(v.ID)
}

// Mutation returns the DocumentMutation object of the builder.
func (_u *DocumentUpdateOne) Mutation() *DocumentMutation {
	return _u.mutation
}

// ClearSigns clears all "signs" edges to the DocumentSign entity.
func (_u *DocumentUpdateOne) ClearSigns() *DocumentUpdateOne {
	_u.mutation.ClearSigns()
	return _u
}

// RemoveSignIDs removes the "signs" edge to DocumentSign entities by IDs.
func (_u *DocumentUpdateOne) RemoveSignIDs(ids ...uuid.UUID) *DocumentUpdateOne {
	_u.mutation.RemoveSignIDs(ids...)
	return _u
}

// RemoveSigns removes "signs" edges to DocumentSign entities.
func (_u *DocumentUpdateOne) RemoveSigns(v ...*DocumentSign) *DocumentUpdateOne {
	ids := make([]uuid.UUID, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _u.RemoveSignIDs(ids...)
}

// ClearTemplate clears the "template" edge to the DocumentTemplate entity.
func (_u *DocumentUpdateOne) ClearTemplate() *DocumentUpdateOne {
	_u.mutation.ClearTemplate()
	return _u
}

// Where appends a list predicates to the DocumentUpdate builder.
func (_u *DocumentUpdateOne) Where(ps ...predicate.Document) *DocumentUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *DocumentUpdateOne) Select(field string, fields ...string) *DocumentUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated Document entity.
func (_u *DocumentUpdateOne) Save(ctx context.Context) (*Document, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *DocumentUpdateOne) SaveX(ctx context.Context) *Document {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *DocumentUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *DocumentUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *DocumentUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := document.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *DocumentUpdateOne) check() error {
	if v, ok := _u.mutation.GetType(); ok {
		if err := document.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Document.type": %w`, err)}
		}
	}
	if _u.mutation.TemplateCleared() && len(_u.mutation.TemplateIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "Document.template"`)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *DocumentUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DocumentUpdateOne {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *DocumentUpdateOne) sqlSave(ctx context.Context) (_node *Document, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(document.Table, document.Columns, sqlgraph.NewFieldSpec(document.FieldID, field.TypeUUID))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Document.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, document.FieldID)
		for _, f := range fields {
			if !document.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != document.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(document.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(document.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(document.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.GetType(); ok {
		_spec.SetField(document.FieldType, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.UserID(); ok {
		_spec.SetField(document.FieldUserID, field.TypeUUID, value)
	}
	if _u.mutation.UserIDCleared() {
		_spec.ClearField(document.FieldUserID, field.TypeUUID)
	}
	if value, ok := _u.mutation.FileURL(); ok {
		_spec.SetField(document.FieldFileURL, field.TypeString, value)
	}
	if _u.mutation.SignsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   document.SignsTable,
			Columns: []string{document.SignsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(documentsign.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.RemovedSignsIDs(); len(nodes) > 0 && !_u.mutation.SignsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   document.SignsTable,
			Columns: []string{document.SignsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(documentsign.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.SignsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   document.SignsTable,
			Columns: []string{document.SignsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(documentsign.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if _u.mutation.TemplateCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   document.TemplateTable,
			Columns: []string{document.TemplateColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(documenttemplate.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.TemplateIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   document.TemplateTable,
			Columns: []string{document.TemplateColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(documenttemplate.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(_u.modifiers...)
	_node = &Document{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{document.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
