// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/specs/proto/bsas-bridge (interfaces: BsasbridgeClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"

	bsas_bridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/bsas-bridge"
)

// MockBsasbridgeClient is a mock of BsasbridgeClient interface.
type MockBsasbridgeClient struct {
	ctrl     *gomock.Controller
	recorder *MockBsasbridgeClientMockRecorder
}

// MockBsasbridgeClientMockRecorder is the mock recorder for MockBsasbridgeClient.
type MockBsasbridgeClientMockRecorder struct {
	mock *MockBsasbridgeClient
}

// NewMockBsasbridgeClient creates a new mock instance.
func NewMockBsasbridgeClient(ctrl *gomock.Controller) *MockBsasbridgeClient {
	mock := &MockBsasbridgeClient{ctrl: ctrl}
	mock.recorder = &MockBsasbridgeClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBsasbridgeClient) EXPECT() *MockBsasbridgeClientMockRecorder {
	return m.recorder
}

// CreateOrder mocks base method.
func (m *MockBsasbridgeClient) CreateOrder(arg0 context.Context, arg1 *bsas_bridge.CreateOrderReq, arg2 ...grpc.CallOption) (*bsas_bridge.CreateOrderResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateOrder", varargs...)
	ret0, _ := ret[0].(*bsas_bridge.CreateOrderResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOrder indicates an expected call of CreateOrder.
func (mr *MockBsasbridgeClientMockRecorder) CreateOrder(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrder", reflect.TypeOf((*MockBsasbridgeClient)(nil).CreateOrder), varargs...)
}

// GetOrderResult mocks base method.
func (m *MockBsasbridgeClient) GetOrderResult(arg0 context.Context, arg1 *bsas_bridge.GetOrderResultReq, arg2 ...grpc.CallOption) (*bsas_bridge.GetOrderResultResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOrderResult", varargs...)
	ret0, _ := ret[0].(*bsas_bridge.GetOrderResultResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderResult indicates an expected call of GetOrderResult.
func (mr *MockBsasbridgeClientMockRecorder) GetOrderResult(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderResult", reflect.TypeOf((*MockBsasbridgeClient)(nil).GetOrderResult), varargs...)
}

// GetOtcReportInfo mocks base method.
func (m *MockBsasbridgeClient) GetOtcReportInfo(arg0 context.Context, arg1 *bsas_bridge.GetOtcReportInfoReq, arg2 ...grpc.CallOption) (*bsas_bridge.GetOtcReportInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOtcReportInfo", varargs...)
	ret0, _ := ret[0].(*bsas_bridge.GetOtcReportInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOtcReportInfo indicates an expected call of GetOtcReportInfo.
func (mr *MockBsasbridgeClientMockRecorder) GetOtcReportInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOtcReportInfo", reflect.TypeOf((*MockBsasbridgeClient)(nil).GetOtcReportInfo), varargs...)
}

// GetSellToBMIS mocks base method.
func (m *MockBsasbridgeClient) GetSellToBMIS(arg0 context.Context, arg1 *bsas_bridge.GetSellToBMISReq, arg2 ...grpc.CallOption) (*bsas_bridge.GetSellToBMISResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSellToBMIS", varargs...)
	ret0, _ := ret[0].(*bsas_bridge.GetSellToBMISResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSellToBMIS indicates an expected call of GetSellToBMIS.
func (mr *MockBsasbridgeClientMockRecorder) GetSellToBMIS(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSellToBMIS", reflect.TypeOf((*MockBsasbridgeClient)(nil).GetSellToBMIS), varargs...)
}

// GetSystemMapping mocks base method.
func (m *MockBsasbridgeClient) GetSystemMapping(arg0 context.Context, arg1 *bsas_bridge.GetSystemMappingReq, arg2 ...grpc.CallOption) (*bsas_bridge.GetSystemMappingResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSystemMapping", varargs...)
	ret0, _ := ret[0].(*bsas_bridge.GetSystemMappingResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSystemMapping indicates an expected call of GetSystemMapping.
func (mr *MockBsasbridgeClientMockRecorder) GetSystemMapping(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSystemMapping", reflect.TypeOf((*MockBsasbridgeClient)(nil).GetSystemMapping), varargs...)
}

// HealthCheck mocks base method.
func (m *MockBsasbridgeClient) HealthCheck(arg0 context.Context, arg1 *bsas_bridge.HealthCheckReq, arg2 ...grpc.CallOption) (*bsas_bridge.HealthCheckResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HealthCheck", varargs...)
	ret0, _ := ret[0].(*bsas_bridge.HealthCheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockBsasbridgeClientMockRecorder) HealthCheck(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockBsasbridgeClient)(nil).HealthCheck), varargs...)
}
