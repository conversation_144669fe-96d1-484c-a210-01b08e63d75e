// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package mocks

import (
	"testing"

	"github.com/golang/mock/gomock"
)

type Mocks struct {
	GRPC      GRPC
	Providers Providers
}

type GRPC struct {
	Notifications *MockNotificationsClient
	Otp           *MockOtpClient
	Users         *MockUsersClient
	Colvirbridge  *MockColvirbridgeClient
	Payments      *MockPaymentsClient
	Taskmanager   *MockTaskmanagerClient
	Cardsaccounts *MockCardsaccountsClient
	Pkbbridge     *MockPkbbridgeClient
	Bsasbridge    *MockBsasbridgeClient
	Dictionary    *MockDictionaryClient
}

type Providers struct {
}

func NewMocks(t *testing.T) *Mocks {
	return &Mocks{
		GRPC: GRPC{
			Notifications: NewMockNotificationsClient(gomock.NewController(t)),
			Otp:           NewMockOtpClient(gomock.NewController(t)),
			Users:         NewMockUsersClient(gomock.NewController(t)),
			Colvirbridge:  NewMockColvirbridgeClient(gomock.NewController(t)),
			Payments:      NewMockPaymentsClient(gomock.NewController(t)),
			Taskmanager:   NewMockTaskmanagerClient(gomock.NewController(t)),
			Cardsaccounts: NewMockCardsaccountsClient(gomock.NewController(t)),
			Pkbbridge:     NewMockPkbbridgeClient(gomock.NewController(t)),
			Bsasbridge:    NewMockBsasbridgeClient(gomock.NewController(t)),
			Dictionary:    NewMockDictionaryClient(gomock.NewController(t)),
		},
		Providers: Providers{},
	}
}
