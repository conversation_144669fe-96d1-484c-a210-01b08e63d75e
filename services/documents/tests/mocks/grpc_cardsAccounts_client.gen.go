// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts (interfaces: CardsaccountsClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"

	cards_accounts "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"
)

// MockCardsaccountsClient is a mock of CardsaccountsClient interface.
type MockCardsaccountsClient struct {
	ctrl     *gomock.Controller
	recorder *MockCardsaccountsClientMockRecorder
}

// MockCardsaccountsClientMockRecorder is the mock recorder for MockCardsaccountsClient.
type MockCardsaccountsClientMockRecorder struct {
	mock *MockCardsaccountsClient
}

// NewMockCardsaccountsClient creates a new mock instance.
func NewMockCardsaccountsClient(ctrl *gomock.Controller) *MockCardsaccountsClient {
	mock := &MockCardsaccountsClient{ctrl: ctrl}
	mock.recorder = &MockCardsaccountsClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCardsaccountsClient) EXPECT() *MockCardsaccountsClientMockRecorder {
	return m.recorder
}

// CreateAccount mocks base method.
func (m *MockCardsaccountsClient) CreateAccount(arg0 context.Context, arg1 *cards_accounts.CreateAccountRequest, arg2 ...grpc.CallOption) (*cards_accounts.CreateAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateAccount", varargs...)
	ret0, _ := ret[0].(*cards_accounts.CreateAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAccount indicates an expected call of CreateAccount.
func (mr *MockCardsaccountsClientMockRecorder) CreateAccount(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAccount", reflect.TypeOf((*MockCardsaccountsClient)(nil).CreateAccount), varargs...)
}

// CreateVirtualCard mocks base method.
func (m *MockCardsaccountsClient) CreateVirtualCard(arg0 context.Context, arg1 *cards_accounts.CreateVirtualCardRequest, arg2 ...grpc.CallOption) (*cards_accounts.CreateVirtualCardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateVirtualCard", varargs...)
	ret0, _ := ret[0].(*cards_accounts.CreateVirtualCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateVirtualCard indicates an expected call of CreateVirtualCard.
func (mr *MockCardsaccountsClientMockRecorder) CreateVirtualCard(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateVirtualCard", reflect.TypeOf((*MockCardsaccountsClient)(nil).CreateVirtualCard), varargs...)
}

// GetAccount mocks base method.
func (m *MockCardsaccountsClient) GetAccount(arg0 context.Context, arg1 *cards_accounts.GetAccountRequest, arg2 ...grpc.CallOption) (*cards_accounts.GetAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAccount", varargs...)
	ret0, _ := ret[0].(*cards_accounts.GetAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccount indicates an expected call of GetAccount.
func (mr *MockCardsaccountsClientMockRecorder) GetAccount(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccount", reflect.TypeOf((*MockCardsaccountsClient)(nil).GetAccount), varargs...)
}

// GetAccounts mocks base method.
func (m *MockCardsaccountsClient) GetAccounts(arg0 context.Context, arg1 *cards_accounts.GetAccountsRequest, arg2 ...grpc.CallOption) (*cards_accounts.GetAccountsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAccounts", varargs...)
	ret0, _ := ret[0].(*cards_accounts.GetAccountsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccounts indicates an expected call of GetAccounts.
func (mr *MockCardsaccountsClientMockRecorder) GetAccounts(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccounts", reflect.TypeOf((*MockCardsaccountsClient)(nil).GetAccounts), varargs...)
}

// GetAccountsSME mocks base method.
func (m *MockCardsaccountsClient) GetAccountsSME(arg0 context.Context, arg1 *cards_accounts.GetAccountsSMERequest, arg2 ...grpc.CallOption) (*cards_accounts.GetAccountsSMEResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAccountsSME", varargs...)
	ret0, _ := ret[0].(*cards_accounts.GetAccountsSMEResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountsSME indicates an expected call of GetAccountsSME.
func (mr *MockCardsaccountsClientMockRecorder) GetAccountsSME(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountsSME", reflect.TypeOf((*MockCardsaccountsClient)(nil).GetAccountsSME), varargs...)
}

// GetApplicationForSign mocks base method.
func (m *MockCardsaccountsClient) GetApplicationForSign(arg0 context.Context, arg1 *cards_accounts.GetApplicationForSignReq, arg2 ...grpc.CallOption) (*cards_accounts.GetApplicationForSignResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetApplicationForSign", varargs...)
	ret0, _ := ret[0].(*cards_accounts.GetApplicationForSignResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApplicationForSign indicates an expected call of GetApplicationForSign.
func (mr *MockCardsaccountsClientMockRecorder) GetApplicationForSign(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplicationForSign", reflect.TypeOf((*MockCardsaccountsClient)(nil).GetApplicationForSign), varargs...)
}

// GetAvailableAccountsByCurrencySME mocks base method.
func (m *MockCardsaccountsClient) GetAvailableAccountsByCurrencySME(arg0 context.Context, arg1 *cards_accounts.GetAvailableAccountsByCurrencySMERequest, arg2 ...grpc.CallOption) (*cards_accounts.GetAvailableAccountsByCurrencySMEResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAvailableAccountsByCurrencySME", varargs...)
	ret0, _ := ret[0].(*cards_accounts.GetAvailableAccountsByCurrencySMEResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAvailableAccountsByCurrencySME indicates an expected call of GetAvailableAccountsByCurrencySME.
func (mr *MockCardsaccountsClientMockRecorder) GetAvailableAccountsByCurrencySME(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvailableAccountsByCurrencySME", reflect.TypeOf((*MockCardsaccountsClient)(nil).GetAvailableAccountsByCurrencySME), varargs...)
}

// GetBalance mocks base method.
func (m *MockCardsaccountsClient) GetBalance(arg0 context.Context, arg1 *cards_accounts.GetBalanceReq, arg2 ...grpc.CallOption) (*cards_accounts.GetBalanceResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBalance", varargs...)
	ret0, _ := ret[0].(*cards_accounts.GetBalanceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBalance indicates an expected call of GetBalance.
func (mr *MockCardsaccountsClientMockRecorder) GetBalance(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBalance", reflect.TypeOf((*MockCardsaccountsClient)(nil).GetBalance), varargs...)
}

// GetCards mocks base method.
func (m *MockCardsaccountsClient) GetCards(arg0 context.Context, arg1 *cards_accounts.GetCardsRequest, arg2 ...grpc.CallOption) (*cards_accounts.GetCardsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCards", varargs...)
	ret0, _ := ret[0].(*cards_accounts.GetCardsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCards indicates an expected call of GetCards.
func (mr *MockCardsaccountsClientMockRecorder) GetCards(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCards", reflect.TypeOf((*MockCardsaccountsClient)(nil).GetCards), varargs...)
}

// GetDocumentsForSign mocks base method.
func (m *MockCardsaccountsClient) GetDocumentsForSign(arg0 context.Context, arg1 *cards_accounts.GetDocumentsForSignRequest, arg2 ...grpc.CallOption) (*cards_accounts.GetDocumentsForSignResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDocumentsForSign", varargs...)
	ret0, _ := ret[0].(*cards_accounts.GetDocumentsForSignResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDocumentsForSign indicates an expected call of GetDocumentsForSign.
func (mr *MockCardsaccountsClientMockRecorder) GetDocumentsForSign(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDocumentsForSign", reflect.TypeOf((*MockCardsaccountsClient)(nil).GetDocumentsForSign), varargs...)
}

// GetRequisitesUnmasked mocks base method.
func (m *MockCardsaccountsClient) GetRequisitesUnmasked(arg0 context.Context, arg1 *cards_accounts.GetRequisitesUnmaskedRequest, arg2 ...grpc.CallOption) (*cards_accounts.GetRequisitesUnmaskedResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRequisitesUnmasked", varargs...)
	ret0, _ := ret[0].(*cards_accounts.GetRequisitesUnmaskedResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRequisitesUnmasked indicates an expected call of GetRequisitesUnmasked.
func (mr *MockCardsaccountsClientMockRecorder) GetRequisitesUnmasked(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRequisitesUnmasked", reflect.TypeOf((*MockCardsaccountsClient)(nil).GetRequisitesUnmasked), varargs...)
}

// HealthCheck mocks base method.
func (m *MockCardsaccountsClient) HealthCheck(arg0 context.Context, arg1 *cards_accounts.HealthCheckReq, arg2 ...grpc.CallOption) (*cards_accounts.HealthCheckResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HealthCheck", varargs...)
	ret0, _ := ret[0].(*cards_accounts.HealthCheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockCardsaccountsClientMockRecorder) HealthCheck(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockCardsaccountsClient)(nil).HealthCheck), varargs...)
}

// SaveAccount mocks base method.
func (m *MockCardsaccountsClient) SaveAccount(arg0 context.Context, arg1 *cards_accounts.SaveAccountRequest, arg2 ...grpc.CallOption) (*cards_accounts.SaveAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SaveAccount", varargs...)
	ret0, _ := ret[0].(*cards_accounts.SaveAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveAccount indicates an expected call of SaveAccount.
func (mr *MockCardsaccountsClientMockRecorder) SaveAccount(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveAccount", reflect.TypeOf((*MockCardsaccountsClient)(nil).SaveAccount), varargs...)
}

// VerifyClient mocks base method.
func (m *MockCardsaccountsClient) VerifyClient(arg0 context.Context, arg1 *cards_accounts.VerifyClientRequest, arg2 ...grpc.CallOption) (*cards_accounts.VerifyClientResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyClient", varargs...)
	ret0, _ := ret[0].(*cards_accounts.VerifyClientResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyClient indicates an expected call of VerifyClient.
func (mr *MockCardsaccountsClientMockRecorder) VerifyClient(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyClient", reflect.TypeOf((*MockCardsaccountsClient)(nil).VerifyClient), varargs...)
}
