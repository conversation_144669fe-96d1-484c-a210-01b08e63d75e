// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/specs/proto/pkb-bridge (interfaces: PkbbridgeClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"

	pkb_bridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/pkb-bridge"
)

// MockPkbbridgeClient is a mock of PkbbridgeClient interface.
type MockPkbbridgeClient struct {
	ctrl     *gomock.Controller
	recorder *MockPkbbridgeClientMockRecorder
}

// MockPkbbridgeClientMockRecorder is the mock recorder for MockPkbbridgeClient.
type MockPkbbridgeClientMockRecorder struct {
	mock *MockPkbbridgeClient
}

// NewMockPkbbridgeClient creates a new mock instance.
func NewMockPkbbridgeClient(ctrl *gomock.Controller) *MockPkbbridgeClient {
	mock := &MockPkbbridgeClient{ctrl: ctrl}
	mock.recorder = &MockPkbbridgeClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPkbbridgeClient) EXPECT() *MockPkbbridgeClientMockRecorder {
	return m.recorder
}

// CheckIinPhoneMatch mocks base method.
func (m *MockPkbbridgeClient) CheckIinPhoneMatch(arg0 context.Context, arg1 *pkb_bridge.CheckIinPhoneMatchReq, arg2 ...grpc.CallOption) (*pkb_bridge.CheckIinPhoneMatchResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckIinPhoneMatch", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.CheckIinPhoneMatchResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIinPhoneMatch indicates an expected call of CheckIinPhoneMatch.
func (mr *MockPkbbridgeClientMockRecorder) CheckIinPhoneMatch(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIinPhoneMatch", reflect.TypeOf((*MockPkbbridgeClient)(nil).CheckIinPhoneMatch), varargs...)
}

// CreateReport mocks base method.
func (m *MockPkbbridgeClient) CreateReport(arg0 context.Context, arg1 *pkb_bridge.CreateReportReq, arg2 ...grpc.CallOption) (*pkb_bridge.CreateReportResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateReport", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.CreateReportResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateReport indicates an expected call of CreateReport.
func (mr *MockPkbbridgeClientMockRecorder) CreateReport(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateReport", reflect.TypeOf((*MockPkbbridgeClient)(nil).CreateReport), varargs...)
}

// GetArmyStatus mocks base method.
func (m *MockPkbbridgeClient) GetArmyStatus(arg0 context.Context, arg1 *pkb_bridge.ArmyStatusReq, arg2 ...grpc.CallOption) (*pkb_bridge.ArmyStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetArmyStatus", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.ArmyStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetArmyStatus indicates an expected call of GetArmyStatus.
func (mr *MockPkbbridgeClientMockRecorder) GetArmyStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetArmyStatus", reflect.TypeOf((*MockPkbbridgeClient)(nil).GetArmyStatus), varargs...)
}

// GetAspStatus mocks base method.
func (m *MockPkbbridgeClient) GetAspStatus(arg0 context.Context, arg1 *pkb_bridge.GetAspStatusReq, arg2 ...grpc.CallOption) (*pkb_bridge.GetAspStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAspStatus", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.GetAspStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAspStatus indicates an expected call of GetAspStatus.
func (mr *MockPkbbridgeClientMockRecorder) GetAspStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAspStatus", reflect.TypeOf((*MockPkbbridgeClient)(nil).GetAspStatus), varargs...)
}

// GetAvailableReports mocks base method.
func (m *MockPkbbridgeClient) GetAvailableReports(arg0 context.Context, arg1 *pkb_bridge.GetAvailableReportsReq, arg2 ...grpc.CallOption) (*pkb_bridge.GetAvailableReportsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAvailableReports", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.GetAvailableReportsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAvailableReports indicates an expected call of GetAvailableReports.
func (mr *MockPkbbridgeClientMockRecorder) GetAvailableReports(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvailableReports", reflect.TypeOf((*MockPkbbridgeClient)(nil).GetAvailableReports), varargs...)
}

// GetBeScoreData mocks base method.
func (m *MockPkbbridgeClient) GetBeScoreData(arg0 context.Context, arg1 *pkb_bridge.BeScoreRequest, arg2 ...grpc.CallOption) (*pkb_bridge.BeScoreEnvelopeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBeScoreData", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.BeScoreEnvelopeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBeScoreData indicates an expected call of GetBeScoreData.
func (mr *MockPkbbridgeClientMockRecorder) GetBeScoreData(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBeScoreData", reflect.TypeOf((*MockPkbbridgeClient)(nil).GetBeScoreData), varargs...)
}

// GetCitsDebts mocks base method.
func (m *MockPkbbridgeClient) GetCitsDebts(arg0 context.Context, arg1 *pkb_bridge.GetCitsDebtsReq, arg2 ...grpc.CallOption) (*pkb_bridge.GetCitsDebtsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCitsDebts", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.GetCitsDebtsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCitsDebts indicates an expected call of GetCitsDebts.
func (mr *MockPkbbridgeClientMockRecorder) GetCitsDebts(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCitsDebts", reflect.TypeOf((*MockPkbbridgeClient)(nil).GetCitsDebts), varargs...)
}

// GetDispensaryPatients mocks base method.
func (m *MockPkbbridgeClient) GetDispensaryPatients(arg0 context.Context, arg1 *pkb_bridge.GetDispensaryPatientsReq, arg2 ...grpc.CallOption) (*pkb_bridge.GetDispensaryPatientsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDispensaryPatients", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.GetDispensaryPatientsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDispensaryPatients indicates an expected call of GetDispensaryPatients.
func (mr *MockPkbbridgeClientMockRecorder) GetDispensaryPatients(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDispensaryPatients", reflect.TypeOf((*MockPkbbridgeClient)(nil).GetDispensaryPatients), varargs...)
}

// GetFamilyInfoByIin mocks base method.
func (m *MockPkbbridgeClient) GetFamilyInfoByIin(arg0 context.Context, arg1 *pkb_bridge.GetFamilyInfoByIinReq, arg2 ...grpc.CallOption) (*pkb_bridge.GetFamilyInfoByIinResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFamilyInfoByIin", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.GetFamilyInfoByIinResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFamilyInfoByIin indicates an expected call of GetFamilyInfoByIin.
func (mr *MockPkbbridgeClientMockRecorder) GetFamilyInfoByIin(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFamilyInfoByIin", reflect.TypeOf((*MockPkbbridgeClient)(nil).GetFamilyInfoByIin), varargs...)
}

// GetGbdulbybin mocks base method.
func (m *MockPkbbridgeClient) GetGbdulbybin(arg0 context.Context, arg1 *pkb_bridge.GetGbdulbybinReq, arg2 ...grpc.CallOption) (*pkb_bridge.GetGbdulbybinResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGbdulbybin", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.GetGbdulbybinResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGbdulbybin indicates an expected call of GetGbdulbybin.
func (mr *MockPkbbridgeClientMockRecorder) GetGbdulbybin(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGbdulbybin", reflect.TypeOf((*MockPkbbridgeClient)(nil).GetGbdulbybin), varargs...)
}

// GetIncomeCompany mocks base method.
func (m *MockPkbbridgeClient) GetIncomeCompany(arg0 context.Context, arg1 *pkb_bridge.GetIncomeCompanyReq, arg2 ...grpc.CallOption) (*pkb_bridge.GetIncomeCompanyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetIncomeCompany", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.GetIncomeCompanyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIncomeCompany indicates an expected call of GetIncomeCompany.
func (mr *MockPkbbridgeClientMockRecorder) GetIncomeCompany(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIncomeCompany", reflect.TypeOf((*MockPkbbridgeClient)(nil).GetIncomeCompany), varargs...)
}

// GetIncomeIndividual mocks base method.
func (m *MockPkbbridgeClient) GetIncomeIndividual(arg0 context.Context, arg1 *pkb_bridge.GetIncomeIndividualReq, arg2 ...grpc.CallOption) (*pkb_bridge.GetIncomeIndividualResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetIncomeIndividual", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.GetIncomeIndividualResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIncomeIndividual indicates an expected call of GetIncomeIndividual.
func (mr *MockPkbbridgeClientMockRecorder) GetIncomeIndividual(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIncomeIndividual", reflect.TypeOf((*MockPkbbridgeClient)(nil).GetIncomeIndividual), varargs...)
}

// GetJurSearchByIinCache mocks base method.
func (m *MockPkbbridgeClient) GetJurSearchByIinCache(arg0 context.Context, arg1 *pkb_bridge.SendJurSearchByIinReq, arg2 ...grpc.CallOption) (*pkb_bridge.SendJurSearchByIinResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetJurSearchByIinCache", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.SendJurSearchByIinResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetJurSearchByIinCache indicates an expected call of GetJurSearchByIinCache.
func (mr *MockPkbbridgeClientMockRecorder) GetJurSearchByIinCache(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetJurSearchByIinCache", reflect.TypeOf((*MockPkbbridgeClient)(nil).GetJurSearchByIinCache), varargs...)
}

// GetLatestIncomeIndividualData mocks base method.
func (m *MockPkbbridgeClient) GetLatestIncomeIndividualData(arg0 context.Context, arg1 *pkb_bridge.GetLatestIncomeIndividualReq, arg2 ...grpc.CallOption) (*pkb_bridge.GetIncomeIndividualResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLatestIncomeIndividualData", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.GetIncomeIndividualResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestIncomeIndividualData indicates an expected call of GetLatestIncomeIndividualData.
func (mr *MockPkbbridgeClientMockRecorder) GetLatestIncomeIndividualData(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestIncomeIndividualData", reflect.TypeOf((*MockPkbbridgeClient)(nil).GetLatestIncomeIndividualData), varargs...)
}

// GetPermitDocumentsByIin mocks base method.
func (m *MockPkbbridgeClient) GetPermitDocumentsByIin(arg0 context.Context, arg1 *pkb_bridge.GetPermitDocumentsByIinReq, arg2 ...grpc.CallOption) (*pkb_bridge.GetPermitDocumentsByIinResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPermitDocumentsByIin", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.GetPermitDocumentsByIinResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPermitDocumentsByIin indicates an expected call of GetPermitDocumentsByIin.
func (mr *MockPkbbridgeClientMockRecorder) GetPermitDocumentsByIin(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPermitDocumentsByIin", reflect.TypeOf((*MockPkbbridgeClient)(nil).GetPermitDocumentsByIin), varargs...)
}

// GetPersonalInfoByIin mocks base method.
func (m *MockPkbbridgeClient) GetPersonalInfoByIin(arg0 context.Context, arg1 *pkb_bridge.GetPersonalInfoByIinReq, arg2 ...grpc.CallOption) (*pkb_bridge.GetPersonalInfoByIinResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPersonalInfoByIin", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.GetPersonalInfoByIinResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPersonalInfoByIin indicates an expected call of GetPersonalInfoByIin.
func (mr *MockPkbbridgeClientMockRecorder) GetPersonalInfoByIin(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersonalInfoByIin", reflect.TypeOf((*MockPkbbridgeClient)(nil).GetPersonalInfoByIin), varargs...)
}

// GetReport mocks base method.
func (m *MockPkbbridgeClient) GetReport(arg0 context.Context, arg1 *pkb_bridge.GetReportReq, arg2 ...grpc.CallOption) (*pkb_bridge.GetReportResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetReport", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.GetReportResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReport indicates an expected call of GetReport.
func (mr *MockPkbbridgeClientMockRecorder) GetReport(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReport", reflect.TypeOf((*MockPkbbridgeClient)(nil).GetReport), varargs...)
}

// GetReportPdfFileContent mocks base method.
func (m *MockPkbbridgeClient) GetReportPdfFileContent(arg0 context.Context, arg1 *pkb_bridge.GetReportPdfFileContentReq, arg2 ...grpc.CallOption) (*pkb_bridge.GetReportPdfFileContentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetReportPdfFileContent", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.GetReportPdfFileContentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReportPdfFileContent indicates an expected call of GetReportPdfFileContent.
func (mr *MockPkbbridgeClientMockRecorder) GetReportPdfFileContent(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReportPdfFileContent", reflect.TypeOf((*MockPkbbridgeClient)(nil).GetReportPdfFileContent), varargs...)
}

// GetReportPdfService mocks base method.
func (m *MockPkbbridgeClient) GetReportPdfService(arg0 context.Context, arg1 *pkb_bridge.GetReportPdfServiceReq, arg2 ...grpc.CallOption) (*pkb_bridge.GetReportPdfServiceResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetReportPdfService", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.GetReportPdfServiceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReportPdfService indicates an expected call of GetReportPdfService.
func (mr *MockPkbbridgeClientMockRecorder) GetReportPdfService(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReportPdfService", reflect.TypeOf((*MockPkbbridgeClient)(nil).GetReportPdfService), varargs...)
}

// GetReportStatus mocks base method.
func (m *MockPkbbridgeClient) GetReportStatus(arg0 context.Context, arg1 *pkb_bridge.GetReportStatusReq, arg2 ...grpc.CallOption) (*pkb_bridge.GetReportStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetReportStatus", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.GetReportStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReportStatus indicates an expected call of GetReportStatus.
func (mr *MockPkbbridgeClientMockRecorder) GetReportStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReportStatus", reflect.TypeOf((*MockPkbbridgeClient)(nil).GetReportStatus), varargs...)
}

// GetSoHoEntrepreneur mocks base method.
func (m *MockPkbbridgeClient) GetSoHoEntrepreneur(arg0 context.Context, arg1 *pkb_bridge.GetSoHoEntrepreneurReq, arg2 ...grpc.CallOption) (*pkb_bridge.GetSoHoEntrepreneursResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSoHoEntrepreneur", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.GetSoHoEntrepreneursResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSoHoEntrepreneur indicates an expected call of GetSoHoEntrepreneur.
func (mr *MockPkbbridgeClientMockRecorder) GetSoHoEntrepreneur(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSoHoEntrepreneur", reflect.TypeOf((*MockPkbbridgeClient)(nil).GetSoHoEntrepreneur), varargs...)
}

// GetStopCreditStatusByIin mocks base method.
func (m *MockPkbbridgeClient) GetStopCreditStatusByIin(arg0 context.Context, arg1 *pkb_bridge.GetStopCreditStatusByIinReq, arg2 ...grpc.CallOption) (*pkb_bridge.GetStopCreditStatusByIinResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStopCreditStatusByIin", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.GetStopCreditStatusByIinResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStopCreditStatusByIin indicates an expected call of GetStopCreditStatusByIin.
func (mr *MockPkbbridgeClientMockRecorder) GetStopCreditStatusByIin(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStopCreditStatusByIin", reflect.TypeOf((*MockPkbbridgeClient)(nil).GetStopCreditStatusByIin), varargs...)
}

// GetSusnStatus mocks base method.
func (m *MockPkbbridgeClient) GetSusnStatus(arg0 context.Context, arg1 *pkb_bridge.GetSusnStatusReq, arg2 ...grpc.CallOption) (*pkb_bridge.GetSusnStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSusnStatus", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.GetSusnStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSusnStatus indicates an expected call of GetSusnStatus.
func (mr *MockPkbbridgeClientMockRecorder) GetSusnStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSusnStatus", reflect.TypeOf((*MockPkbbridgeClient)(nil).GetSusnStatus), varargs...)
}

// HealthCheck mocks base method.
func (m *MockPkbbridgeClient) HealthCheck(arg0 context.Context, arg1 *pkb_bridge.HealthCheckReq, arg2 ...grpc.CallOption) (*pkb_bridge.HealthCheckResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HealthCheck", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.HealthCheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockPkbbridgeClientMockRecorder) HealthCheck(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockPkbbridgeClient)(nil).HealthCheck), varargs...)
}

// SendGKBLoanApplicationInfo mocks base method.
func (m *MockPkbbridgeClient) SendGKBLoanApplicationInfo(arg0 context.Context, arg1 *pkb_bridge.SendGKBLoanApplicationInfoReq, arg2 ...grpc.CallOption) (*pkb_bridge.SendGKBLoanApplicationInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendGKBLoanApplicationInfo", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.SendGKBLoanApplicationInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendGKBLoanApplicationInfo indicates an expected call of SendGKBLoanApplicationInfo.
func (mr *MockPkbbridgeClientMockRecorder) SendGKBLoanApplicationInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendGKBLoanApplicationInfo", reflect.TypeOf((*MockPkbbridgeClient)(nil).SendGKBLoanApplicationInfo), varargs...)
}

// SendJurSearchByIin mocks base method.
func (m *MockPkbbridgeClient) SendJurSearchByIin(arg0 context.Context, arg1 *pkb_bridge.SendJurSearchByIinReq, arg2 ...grpc.CallOption) (*pkb_bridge.SendJurSearchByIinResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendJurSearchByIin", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.SendJurSearchByIinResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendJurSearchByIin indicates an expected call of SendJurSearchByIin.
func (mr *MockPkbbridgeClientMockRecorder) SendJurSearchByIin(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendJurSearchByIin", reflect.TypeOf((*MockPkbbridgeClient)(nil).SendJurSearchByIin), varargs...)
}

// SendPKBLoanApplicationInfo mocks base method.
func (m *MockPkbbridgeClient) SendPKBLoanApplicationInfo(arg0 context.Context, arg1 *pkb_bridge.SendPKBLoanApplicationInfoReq, arg2 ...grpc.CallOption) (*pkb_bridge.SendPKBLoanApplicationInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendPKBLoanApplicationInfo", varargs...)
	ret0, _ := ret[0].(*pkb_bridge.SendPKBLoanApplicationInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendPKBLoanApplicationInfo indicates an expected call of SendPKBLoanApplicationInfo.
func (mr *MockPkbbridgeClientMockRecorder) SendPKBLoanApplicationInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPKBLoanApplicationInfo", reflect.TypeOf((*MockPkbbridgeClient)(nil).SendPKBLoanApplicationInfo), varargs...)
}
