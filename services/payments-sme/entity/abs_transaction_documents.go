package entity

import (
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent"
)

type AbsTransactionDocument struct {
	ID              uuid.UUID
	ReferenceID     string
	DocumentType    *string
	ReferenceDate   *string
	DocumentStatus  *string
	RejectionReason *string
}

func EntAbsTransactionDocumentToEntity(doc *ent.AbsTransactionDocuments) *AbsTransactionDocument {
	var docStatus *string
	if doc.DocumentStatus != nil {
		status := doc.DocumentStatus.String()
		docStatus = &status
	}

	var rejectionReason *string
	if doc.RejectionReason != nil {
		reasonString := doc.RejectionReason.String()
		rejectionReason = &reasonString
	}

	return &AbsTransactionDocument{
		ID:              doc.ID,
		ReferenceID:     doc.ReferenceID,
		DocumentType:    doc.DocumentType,
		ReferenceDate:   doc.ReferenceDate,
		DocumentStatus:  docStatus,
		RejectionReason: rejectionReason,
	}
}
