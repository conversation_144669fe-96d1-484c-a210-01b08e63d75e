package entity

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/consts/configs"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent"
	colvirBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
)

type Transaction struct {
	// Основные идентификаторы
	ID                uuid.UUID `json:"id"`
	TransactionNumber string    `json:"transaction_number"`
	InitiatorID       string    `json:"initiator_id"`
	IdempotencyKey    string    `json:"idempotency_key"`

	// Даты
	TransactionDate time.Time `json:"transaction_date"`
	ValueDate       *string   `json:"value_date,omitempty"`

	// Типы и статусы
	TransactionType      consts.TransactionType       `json:"transaction_type"`
	TransactionStatus    consts.TransactionStatus     `json:"transaction_status"`
	TransactionDirection *consts.TransactionDirection `json:"transaction_direction,omitempty"`

	// Финансовые данные
	TransactionAmount      string          `json:"transaction_amount"`
	TransactionCommission  *string         `json:"transaction_commission,omitempty"`
	TransactionCurrency    consts.Currency `json:"transaction_currency"`
	TransactionTotalAmount string          `json:"transaction_total_amount"`

	// Назначение платежа
	PurposeCode    *string `json:"purpose_code,omitempty"`
	PurposeDetails *string `json:"purpose_details,omitempty"`

	// Данные плательщика
	PayerKod            *string           `json:"payer_kod,omitempty"`
	PayerBinIin         *string           `json:"payer_bin_iin,omitempty"`
	PayerName           *string           `json:"payer_name,omitempty"`
	PayerType           *consts.PayerType `json:"payer_type,omitempty"`
	PayerAccountIban    *string           `json:"payer_account_iban,omitempty"`
	PayerBankBic        *string           `json:"payer_bank_bic,omitempty"`
	PayerBankName       *string           `json:"payer_bank_name,omitempty"`
	PayerIsoCountryCode *string           `json:"payer_iso_country_code,omitempty"`

	// Данные реального плательщика
	RealPayerName           *string           `json:"real_payer_name,omitempty"`
	RealPayerBinIin         *string           `json:"real_payer_bin_iin,omitempty"`
	RealPayerIsoCountryCode *string           `json:"real_payer_iso_country_code,omitempty"`
	RealPayerType           *consts.PayerType `json:"real_payer_type,omitempty"`

	// Данные бенефициара
	BeneficiaryKbe            *string           `json:"beneficiary_kbe,omitempty"`
	BeneficiaryBinIin         *string           `json:"beneficiary_bin_iin,omitempty"`
	BeneficiaryName           *string           `json:"beneficiary_name,omitempty"`
	BeneficiaryType           *consts.PayerType `json:"beneficiary_type,omitempty"`
	BeneficiaryAccountIban    *string           `json:"beneficiary_account_iban,omitempty"`
	BeneficiaryBankBic        *string           `json:"beneficiary_bank_bic,omitempty"`
	BeneficiaryBankName       *string           `json:"beneficiary_bank_name,omitempty"`
	BeneficiaryIsoCountryCode *string           `json:"beneficiary_iso_country_code,omitempty"`

	// Данные реального бенефициара
	RealBeneficiaryName        *string           `json:"real_beneficiary_name,omitempty"`
	RealBeneficiaryBinIin      *string           `json:"real_beneficiary_bin_iin,omitempty"`
	RealBeneficiaryCountryCode *string           `json:"real_beneficiary_country_code,omitempty"`
	RealBeneficiaryType        *consts.PayerType `json:"real_beneficiary_type,omitempty"`
}

func (t *Transaction) ToCheckDomesticPaymentReq(payment *Payment) (*colvirBridge.DomesticPaymentRequest, error) {
	colvirReq := colvirBridge.DomesticPaymentRequest{
		ClientType:      colvirBridge.DomesticPaymentClientType_DPCT_MBU,
		PaymentNumber:   t.TransactionNumber,
		DocumentType:    colvirBridge.DomesticPaymentDocumentType_DPDT_DPYITF, // TODO
		Amount:          t.TransactionTotalAmount,
		Currency:        t.TransactionCurrency.String(),
		DocumentDate:    t.TransactionDate.Format(time.DateOnly),
		RealPayer:       t.MakeColvirRealPayer(),
		RealBeneficiary: t.MakeColvirRealBeneficiary(),
		BudgetCode:      payment.KBK,
	}

	if t.ValueDate != nil {
		colvirReq.ValueDate = *t.ValueDate
	}

	if t.PurposeDetails != nil {
		colvirReq.PaymentDetails = *t.PurposeDetails
	}

	if t.PurposeCode != nil {
		colvirReq.PurposeCode = *t.PurposeCode
	}

	if t.PayerBinIin != nil {
		colvirReq.PayerIinBin = *t.PayerBinIin
	}

	if t.PayerAccountIban != nil {
		colvirReq.PayerAccount = *t.PayerAccountIban
	}

	if t.PayerType != nil {
		colvirReq.PayerCustomerType = string(*t.PayerType)
	}

	if t.BeneficiaryBinIin != nil {
		colvirReq.BeneficiaryIinBin = *t.BeneficiaryBinIin
	}

	if t.BeneficiaryAccountIban != nil {
		colvirReq.BeneficiaryAccount = *t.BeneficiaryAccountIban
	}

	if t.BeneficiaryName != nil {
		colvirReq.BeneficiaryName = *t.BeneficiaryName
	}

	if t.BeneficiaryKbe != nil {
		colvirReq.BeneficiaryPartyCode = *t.BeneficiaryKbe
	}

	if t.BeneficiaryBankBic != nil {
		colvirReq.BeneficiaryBankBic = *t.BeneficiaryBankBic
	}

	if t.BeneficiaryBankName != nil {
		colvirReq.BeneficiaryBankName = *t.BeneficiaryBankName
	}

	return &colvirReq, nil
}

func (t *Transaction) MakeColvirRealPayer() *colvirBridge.RealPaymentSide {
	colvirRealPayer := colvirBridge.RealPaymentSide{}

	if t.RealPayerName != nil {
		colvirRealPayer.FullName = *t.RealPayerName
	}

	if t.RealPayerBinIin != nil {
		colvirRealPayer.Iin = *t.RealPayerBinIin
	}

	if t.RealPayerIsoCountryCode != nil {
		colvirRealPayer.CountryIsoCode = *t.RealPayerIsoCountryCode
	}

	return &colvirRealPayer
}

func (t *Transaction) MakeColvirRealBeneficiary() *colvirBridge.RealPaymentSide {
	colvirRealBeneficiary := colvirBridge.RealPaymentSide{}

	if t.RealBeneficiaryName != nil {
		colvirRealBeneficiary.FullName = *t.RealBeneficiaryName
	}

	if t.RealBeneficiaryBinIin != nil {
		colvirRealBeneficiary.Iin = *t.RealBeneficiaryBinIin
	}

	if t.RealBeneficiaryCountryCode != nil {
		colvirRealBeneficiary.CountryIsoCode = *t.RealBeneficiaryCountryCode
	}

	return &colvirRealBeneficiary
}

func (t *Transaction) MakeColvirMassPaymentPayer() (*colvirBridge.DomesticMassPaymentPayer, error) {
	payer := &colvirBridge.DomesticMassPaymentPayer{}

	if t.PayerBinIin != nil {
		payer.TaxIdentificationNumber = *t.PayerBinIin
	}

	if t.PayerName != nil {
		payer.Name = *t.PayerName
	}

	if t.PayerKod != nil {
		payer.PartyCode = &colvirBridge.DomesticMassPaymentPartyCode{
			Code: *t.PayerKod,
		}
	}

	if t.PayerAccountIban != nil {
		payer.AccountIban = *t.PayerAccountIban
	}

	if t.PayerType != nil {
		payer.CustomerType = string(*t.PayerType)
	}

	return payer, nil
}

func (t *Transaction) MakeColvirMassPaymentBeneficiary() (*colvirBridge.DomesticMassPaymentBeneficiary, error) {
	beneficiary := &colvirBridge.DomesticMassPaymentBeneficiary{}

	if t.BeneficiaryBinIin != nil {
		beneficiary.TaxIdentificationNumber = *t.BeneficiaryBinIin
	}

	if t.BeneficiaryName != nil {
		beneficiary.Name = *t.BeneficiaryName
	}

	if t.BeneficiaryKbe != nil {
		beneficiary.PartyCode = &colvirBridge.DomesticMassPaymentPartyCode{
			Code: *t.BeneficiaryKbe,
		}
	}

	if t.BeneficiaryAccountIban != nil {
		beneficiary.AccountIban = *t.BeneficiaryAccountIban
	}

	return beneficiary, nil
}

func (t *Transaction) getPurposeDetails() string {
	if t.PurposeDetails != nil {
		return *t.PurposeDetails
	}
	return ""
}

// EntTransactionToEntity конвертирует ent.Transaction в entity.Transaction
func EntTransactionToEntity(entTr *ent.Transaction) *Transaction {
	tr := &Transaction{
		ID:                         entTr.ID,
		TransactionNumber:          entTr.TransactionNumber,
		InitiatorID:                entTr.InitiatorID,
		IdempotencyKey:             entTr.IdempotencyKey,
		TransactionDate:            entTr.TransactionDate,
		ValueDate:                  entTr.ValueDate,
		TransactionType:            consts.TransactionType(entTr.TransactionType),
		TransactionStatus:          consts.TransactionStatus(entTr.TransactionStatus),
		TransactionAmount:          entTr.TransactionAmount,
		TransactionCommission:      entTr.TransactionComission,
		TransactionCurrency:        consts.Currency(entTr.TransactionCurrency),
		TransactionTotalAmount:     entTr.TransactionTotalAmount,
		PurposeCode:                entTr.PurposeCode,
		PurposeDetails:             entTr.PurposeDetails,
		PayerKod:                   entTr.PayerKod,
		PayerBinIin:                entTr.PayerBinIin,
		PayerName:                  entTr.PayerName,
		PayerAccountIban:           entTr.PayerAccountIban,
		PayerBankBic:               entTr.PayerBankBic,
		PayerBankName:              entTr.PayerBankName,
		PayerIsoCountryCode:        entTr.PayerIsoCountryCode,
		RealPayerName:              entTr.RealPayerName,
		RealPayerBinIin:            entTr.RealPayerBinIin,
		RealPayerIsoCountryCode:    entTr.RealPayerIsoCountryCode,
		BeneficiaryKbe:             entTr.BeneficiaryKbe,
		BeneficiaryBinIin:          entTr.BeneficiaryBinIin,
		BeneficiaryName:            entTr.BeneficiaryName,
		BeneficiaryAccountIban:     entTr.BeneficiaryAccountIban,
		BeneficiaryBankBic:         entTr.BeneficiaryBankBic,
		BeneficiaryBankName:        entTr.BeneficiaryBankName,
		BeneficiaryIsoCountryCode:  entTr.BeneficiaryIsoCountryCode,
		RealBeneficiaryName:        entTr.RealBeneficiaryName,
		RealBeneficiaryBinIin:      entTr.RealBeneficiaryBinIin,
		RealBeneficiaryCountryCode: entTr.RealBeneficiaryCountryCode,
	}

	// Конвертируем опциональные enum поля
	if entTr.TransactionDirection != nil {
		dir := consts.TransactionDirection(*entTr.TransactionDirection)
		tr.TransactionDirection = &dir
	}
	if entTr.PayerType != nil {
		pt := consts.PayerType(*entTr.PayerType)
		tr.PayerType = &pt
	}
	if entTr.RealPayerType != nil {
		pt := consts.PayerType(*entTr.RealPayerType)
		tr.RealPayerType = &pt
	}
	if entTr.BeneficiaryType != nil {
		bt := consts.PayerType(*entTr.BeneficiaryType)
		tr.BeneficiaryType = &bt
	}
	if entTr.RealBeneficiaryType != nil {
		rbt := consts.PayerType(*entTr.RealBeneficiaryType)
		tr.RealBeneficiaryType = &rbt
	}

	return tr
}

// ToDomesticMassPaymentReq создает запрос для массового платежа с данными из таблицы payments
func (t *Transaction) ToDomesticMassPaymentReq(payment *Payment, signatories *Signatory) (*colvirBridge.DomesticMassPaymentRequest, error) {
	payerPart, err := t.MakeColvirMassPaymentPayer()
	if err != nil {
		return nil, err
	}

	beneficiaryPart, err := t.MakeColvirMassPaymentBeneficiary()
	if err != nil {
		return nil, err
	}

	colvirReq := colvirBridge.DomesticMassPaymentRequest{
		Code:         t.TransactionNumber,
		Amount:       t.TransactionTotalAmount,
		Currency:     t.TransactionCurrency.String(),
		DocumentDate: t.TransactionDate.Format(time.DateOnly),
		CustomerType: consts.PayerTypeIndividual.String(),
		Payer:        payerPart,
		Beneficiary:  beneficiaryPart,
	}

	colvirDocType, err := payment.GetColvirDocumentType()
	if err != nil {
		return nil, err
	}
	colvirReq.DocumentType = colvirDocType

	if t.ValueDate != nil {
		colvirReq.ValueDate = *t.ValueDate
	}

	if t.PurposeDetails != nil {
		colvirReq.PaymentDetails = *t.PurposeDetails
	}

	if t.PurposeCode != nil {
		colvirReq.PurposeCode = *t.PurposeCode
	}

	if signatories != nil {
		if signatories.SignatoryA != nil {
			colvirReq.SignatoryA = *signatories.SignatoryA
		}
	}

	if t.BeneficiaryBankBic != nil {
		colvirReq.BeneficiaryBank = *t.BeneficiaryBankBic
	}

	if t.PayerIsoCountryCode != nil {
		colvirReq.PayerISOCountryCode = *t.PayerIsoCountryCode
	}

	if t.BeneficiaryIsoCountryCode != nil {
		colvirReq.BeneficiaryISOCountryCode = *t.BeneficiaryIsoCountryCode
	}

	// Формируем EmployeeDetails из данных payment.EmployeeList
	employeeDetails, err := t.buildEmployeeDetailsFromPayment(payment)
	if err != nil {
		return nil, err
	}
	colvirReq.EmployeeDetails = employeeDetails

	return &colvirReq, nil
}

func (t *Transaction) buildEmployeeDetailsFromPayment(payment *Payment) ([]*colvirBridge.DomesticMassPaymentEmployeeDetail, error) {
	if payment == nil {
		return nil, fmt.Errorf("payment is nil. Payment id: %s", t.ID)
	}

	if payment.EmployeeList == nil {
		return nil, fmt.Errorf("employee list is nil. Payment id: %s", t.ID)
	}

	raw, err := json.Marshal(payment.EmployeeList)
	if err != nil {
		return nil, err
	}

	var employeeList EmployeeList

	if err := json.Unmarshal(raw, &employeeList); err != nil {
		return nil, err
	}

	var result []*colvirBridge.DomesticMassPaymentEmployeeDetail

	for _, emp := range employeeList.Employees {
		lastName := emp.LastName
		name := emp.Name
		middleName := emp.MiddleName

		detail := &colvirBridge.DomesticMassPaymentEmployeeDetail{
			Surname:                 lastName,
			Name:                    name,
			Patronymic:              middleName,
			TaxIdentificationNumber: emp.IIN,
			Period:                  emp.ValuePeriod,
			Amount:                  emp.Amount,
			Currency:                t.TransactionCurrency.String(),
			BirthDate:               emp.Birthday,
			Description:             t.getPurposeDetails(),
		}

		if payment.PaymentType != nil {
			detail.PaymentType = string(*payment.PaymentType)
		}

		result = append(result, detail)
	}

	return result, nil
}

// SetConfig устанавливает значения полей транзакции на основе конфигурации
func (t *Transaction) SetConfig(cfg configs.Config) {
	// Устанавливаем значения в транзакцию
	t.PayerType = &cfg.PayerType
	t.PayerKod = &cfg.PayerKod
	t.PayerBankBic = &cfg.PayerBankBIC
	t.PayerBankName = &cfg.PayerBankName
	t.PayerIsoCountryCode = &cfg.PayerCountry
	t.TransactionDirection = &cfg.Direction
	t.TransactionType = cfg.TransactionType
	t.TransactionCurrency = cfg.Currency
	t.BeneficiaryName = &cfg.BeneficiaryName
	t.BeneficiaryKbe = &cfg.BeneficiaryKbe
	t.BeneficiaryType = &cfg.BeneficiaryType
	t.BeneficiaryAccountIban = &cfg.BeneficiaryAccount
	t.BeneficiaryBankBic = &cfg.BeneficiaryBankBIC
	t.BeneficiaryBankName = &cfg.BeneficiaryBankName
	t.BeneficiaryIsoCountryCode = &cfg.BeneficiaryCountry

	// Устанавливаем BINIIN бенефициара, если он определен
	if t.BeneficiaryBinIin == nil && cfg.BeneficiaryBINIIN != "" {
		t.BeneficiaryBinIin = &cfg.BeneficiaryBINIIN
	}
	// Устанавливаем наименование бенефициара, если он определен
	if t.BeneficiaryName == nil && cfg.BeneficiaryName != "" {
		t.BeneficiaryName = &cfg.BeneficiaryName
	}
	// Если тип реального бенефициара определен, устанавливаем его
	if cfg.RealBeneficiaryType != consts.PayerTypeUndefined {
		t.RealBeneficiaryType = &cfg.RealBeneficiaryType
	}
	if t.RealBeneficiaryCountryCode == nil {
		t.RealBeneficiaryCountryCode = &cfg.BeneficiaryCountry
	}
}
