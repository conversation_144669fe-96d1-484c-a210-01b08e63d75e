package entity

import (
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/payments-sme"
)

type (
	SmePaymentsGetPaymentOrderByTrNumberReq struct {
		TransactionNumber string
	}

	SmePaymentsGetPaymentOrderByTrNumberResult struct {
		ID                string
		Title             string
		Link              string
		Version           int32
		TransactionStatus string
	}
)

// MakeSmePaymentsGetPaymentOrderPbToEntity создает объект из pb.SmePaymentsGetPaymentOrderReq в SmePaymentsGetPaymentOrderReq для передачи в usecase
func MakeSmePaymentsGetPaymentOrderByTrNumberPbToEntity(req *pb.SmePaymentsGetPaymentOrderByTrNumberReq) *SmePaymentsGetPaymentOrderByTrNumberReq {
	if req == nil {
		return &SmePaymentsGetPaymentOrderByTrNumberReq{}
	}
	// write your mapping code here
	return &SmePaymentsGetPaymentOrderByTrNumberReq{
		TransactionNumber: req.GetTransactionNumber(),
	}
}

// MakeSmePaymentsGetPaymentOrderByTrNumberEntityToPb создает объект из SmePaymentsGetPaymentOrderByTrNumber в pb.SmePaymentsGetPaymentOrderResp для возврата ответа из сервиса
func MakeSmePaymentsGetPaymentOrderByTrNumberEntityToPb(res *SmePaymentsGetPaymentOrderByTrNumberResult) *pb.SmePaymentsGetPaymentOrderByTrNumberResp {
	return &pb.SmePaymentsGetPaymentOrderByTrNumberResp{
		Id:                res.ID,
		Title:             res.Title,
		Link:              res.Link,
		Version:           res.Version,
		TransactionStatus: res.TransactionStatus,
	}
}

// MakeSmePaymentsGetPaymentOrderByTrNumberRespPbToEntity создает объект из pb.SmePaymentsGetPaymentOrderByTrNumberResp
// в SmePaymentsGetPaymentOrderByTrNumberResult для возврата ответа из сервиса
func MakeSmePaymentsGetPaymentOrderByTrNumberRespPbToEntity(res *pb.SmePaymentsGetPaymentOrderByTrNumberResp) *SmePaymentsGetPaymentOrderByTrNumberResult {
	if res == nil {
		return &SmePaymentsGetPaymentOrderByTrNumberResult{}
	}
	// write your mapping code here
	return &SmePaymentsGetPaymentOrderByTrNumberResult{
		ID:                res.GetId(),
		Title:             res.GetTitle(),
		Link:              res.GetLink(),
		Version:           res.GetVersion(),
		TransactionStatus: res.GetTransactionStatus(),
	}
}
