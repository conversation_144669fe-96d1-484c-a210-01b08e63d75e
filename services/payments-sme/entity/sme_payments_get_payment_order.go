package entity

import (
	errsPaymentsSme "git.redmadrobot.com/zaman/backend/zaman/errs/paymentsSme"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/payments-sme"
)

type (
	SmePaymentsGetPaymentOrderReq struct {
		TransactionID string
	}

	SmePaymentsGetPaymentOrderResult struct {
		Title   string
		Link    string
		Version string
	}
)

// MakeSmePaymentsGetPaymentOrderPbToEntity создает объект из pb.SmePaymentsGetPaymentOrderReq в SmePaymentsGetPaymentOrderReq для передачи в usecase
func MakeSmePaymentsGetPaymentOrderPbToEntity(req *pb.SmePaymentsGetPaymentOrderReq) *SmePaymentsGetPaymentOrderReq {
	if req == nil {
		return &SmePaymentsGetPaymentOrderReq{}
	}
	// write your mapping code here
	return &SmePaymentsGetPaymentOrderReq{
		TransactionID: req.GetTransactionID(),
	}
}

// MakeSmePaymentsGetPaymentOrderEntityToPb создает объект из SmePaymentsGetPaymentOrder в pb.SmePaymentsGetPaymentOrderResp для возврата ответа из сервиса
func MakeSmePaymentsGetPaymentOrderEntityToPb(res *SmePaymentsGetPaymentOrderResult) *pb.SmePaymentsGetPaymentOrderResp {
	return &pb.SmePaymentsGetPaymentOrderResp{
		Title:   res.Title,
		Link:    res.Link,
		Version: res.Version,
	}
}

func (r *SmePaymentsGetPaymentOrderReq) Validate() error {
	if r.TransactionID == "" {
		return errsPaymentsSme.PaymentsSmeErrs().ValidationErrorError()
	}

	return nil
}
