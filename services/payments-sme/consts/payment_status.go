package consts

// PaymentStatus статус платежа
type PaymentStatus string

const (
	// PaymentStatusInitialized Платёж пришёл на бэкэнд, прошёл валидацию и был принят в обработку
	PaymentStatusInitialized PaymentStatus = "INITIALIZED"
	// PaymentStatusRejected Платёж пришел на бэкэнд, но не прошёл валидацию
	PaymentStatusRejected PaymentStatus = "REJECTED"
	// PaymentStatusInProgress Платёж проведён в платформе Платежи и Переводы, но ещё не обработан в Колвир
	PaymentStatusInProgress PaymentStatus = "IN_PROGRESS"
	// PaymentStatusCompleted Платёж проведён везде по системам банка, деньги переведены
	PaymentStatusCompleted PaymentStatus = "COMPLETED"
)

func (ps PaymentStatus) String() string {
	return string(ps)
}

// Values возвращает все возможные значения PaymentStatus
func (ps PaymentStatus) Values() []string {
	return []string{
		PaymentStatusInitialized.String(),
		PaymentStatusRejected.String(),
		PaymentStatusInProgress.String(),
		PaymentStatusCompleted.String(),
	}
}

// PaymentReasonCode код состояния платежа
type PaymentReasonCode string

const (
	// PaymentReasonCodeValidationError Validation error
	PaymentReasonCodeValidationError PaymentReasonCode = "1005"
	// PaymentReasonCodeWorktimeExceeded Worktime exceeded
	PaymentReasonCodeWorktimeExceeded PaymentReasonCode = "1013"
	// PaymentReasonCodeInsufficientFunds Insufficient funds
	PaymentReasonCodeInsufficientFunds PaymentReasonCode = "1041"
	// PaymentReasonCodeNoActiveAccount No active account
	PaymentReasonCodeNoActiveAccount PaymentReasonCode = "1042"
	// PaymentReasonCodeUnableToProcess Unable to process payment
	PaymentReasonCodeUnableToProcess PaymentReasonCode = "1040"
	// PaymentReasonCodeActionIsForbidden Action is forbidden
	PaymentReasonCodeActionIsForbidden PaymentReasonCode = "1043"
)

func (prc PaymentReasonCode) String() string {
	return string(prc)
}

// Values возвращает все возможные значения PaymentReasonCode
func (prc PaymentReasonCode) Values() []string {
	return []string{
		PaymentReasonCodeValidationError.String(),
		PaymentReasonCodeWorktimeExceeded.String(),
		PaymentReasonCodeInsufficientFunds.String(),
		PaymentReasonCodeNoActiveAccount.String(),
		PaymentReasonCodeUnableToProcess.String(),
		PaymentReasonCodeActionIsForbidden.String(),
	}
}

// PaymentReason описание причины состояния платежа
type PaymentReason string

const (
	// PaymentReasonValidationError Платёж пришёл на бэкэнд, но что-то пошло не так на шаге валидации
	PaymentReasonValidationError PaymentReason = "Validation error"
	// PaymentReasonWorktimeExceeded Платёж требует инициализации и проведения одним оперднём
	PaymentReasonWorktimeExceeded PaymentReason = "Worktime exceeded"
	// PaymentReasonInsufficientFunds Платёж создан, но при попытке провести его в Колвир столкнулись с нехваткой средств
	PaymentReasonInsufficientFunds PaymentReason = "Insufficient funds"
	// PaymentReasonNoActiveAccount Платёж создан, но при попытке провести его в Колвир столкнулись с неактивным статусом счёта
	PaymentReasonNoActiveAccount PaymentReason = "No active account"
	// PaymentReasonUnableToProcess Платёж создан, но при попытке провести его в Колвир столкнулись с ошибками
	PaymentReasonUnableToProcess PaymentReason = "Unable to process payment"
	// PaymentReasonActionIsForbidden Платеж не может быть проведён из-за ограничений или запретов
	PaymentReasonActionIsForbidden PaymentReason = "Action is forbidden"
)

func (pr PaymentReason) String() string {
	return string(pr)
}

// Values возвращает все возможные значения PaymentReason
func (pr PaymentReason) Values() []string {
	return []string{
		PaymentReasonValidationError.String(),
		PaymentReasonWorktimeExceeded.String(),
		PaymentReasonInsufficientFunds.String(),
		PaymentReasonNoActiveAccount.String(),
		PaymentReasonUnableToProcess.String(),
		PaymentReasonActionIsForbidden.String(),
	}
}

// ColvirErrorCode код ошибки платежа в Колвире
type ColvirErrorMessageCode string

const (
	ColvirErrorMessageCodeInsufficientFunds ColvirErrorMessageCode = "DP-00811"
)

// String возвращает строковое представление ColvirErrorMessageCode
func (cec ColvirErrorMessageCode) String() string {
	return string(cec)
}

// Values возвращает все возможные значения ColvirErrorMessageCode
func (cec ColvirErrorMessageCode) Values() []string {
	return []string{
		ColvirErrorMessageCodeInsufficientFunds.String(),
	}
}

// GetReasonByCode возвращает описание причины по коду
func GetReasonByCode(code PaymentReasonCode) PaymentReason {
	switch code {
	case PaymentReasonCodeValidationError:
		return PaymentReasonValidationError
	case PaymentReasonCodeWorktimeExceeded:
		return PaymentReasonWorktimeExceeded
	case PaymentReasonCodeInsufficientFunds:
		return PaymentReasonInsufficientFunds
	case PaymentReasonCodeNoActiveAccount:
		return PaymentReasonNoActiveAccount
	case PaymentReasonCodeUnableToProcess:
		return PaymentReasonUnableToProcess
	case PaymentReasonCodeActionIsForbidden:
		return PaymentReasonActionIsForbidden
	default:
		return ""
	}
}

// GetReasonCodeByReason возвращает код причины по описанию
func GetReasonCodeByReason(reason PaymentReason) PaymentReasonCode {
	switch reason {
	case PaymentReasonValidationError:
		return PaymentReasonCodeValidationError
	case PaymentReasonWorktimeExceeded:
		return PaymentReasonCodeWorktimeExceeded
	case PaymentReasonInsufficientFunds:
		return PaymentReasonCodeInsufficientFunds
	case PaymentReasonNoActiveAccount:
		return PaymentReasonCodeNoActiveAccount
	case PaymentReasonUnableToProcess:
		return PaymentReasonCodeUnableToProcess
	case PaymentReasonActionIsForbidden:
		return PaymentReasonCodeActionIsForbidden
	default:
		return ""
	}
}
