package configs

import (
	"git.redmadrobot.com/zaman/backend/zaman/pkg/helpers/locale"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/consts"
)

// Config содержит конфигурацию для различных типов платежей.
// Включает в себя информацию о направлении, банках, типах транзакций и т.д.
// Используется для настройки платежей в зависимости от кода платежа
type Config struct {
	PaymentCode         consts.PaymentCode
	Direction           consts.TransactionDirection
	PayerBankBIC        string
	PayerBankName       string
	TransactionType     consts.TransactionType
	Currency            consts.Currency
	PaymentType         consts.PaymentType
	KBK                 string
	BeneficiaryBINIIN   string
	BeneficiaryName     string
	BeneficiaryCountry  string
	BeneficiaryAccount  string
	BeneficiaryKbe      string
	BeneficiaryBankBIC  string
	BeneficiaryBankName string
	BeneficiaryType     consts.PayerType
	PayerCountry        string
	PayerKod            string
	PayerType           consts.PayerType
	RealBeneficiaryType consts.PayerType
	ValidPurposeCodes   []string
	Locale              locale.Locale
}

func GetConfig(paymentCode consts.PaymentCode, loc locale.Locale) *Config {
	cfg := &Config{
		PaymentCode: paymentCode,
		Locale:      loc,
	}

	switch paymentCode {
	// ОПВ (Обязательные пенсионные взносы)
	case consts.PaymentCodeBudgetPensionBasic:
		cfg.setOPVConfigs()
	// ОПВР (Обязательные пенсионные взносы работодателя)
	case consts.PaymentCodeBudgetPensionEmployer:
		cfg.setOPVRConfigs()
	// ОППВ (Обязательный профессиональный пенсионный взнос)
	case consts.PaymentCodeBudgetPensionProfessional:
		cfg.setOPPVConfigs()
	// ВОСМС (Взносы на отчисления социальное медицинское страхование)
	case consts.PaymentCodeBudgetMedicalBasic:
		cfg.setVOSMSConfigs()
	// ООСМС (Обязательные отчисления социальное медицинское страхование)
	case consts.PaymentCodeBudgetMedicalEmployer:
		cfg.setOOSMSConfigs()
	// ИПН (Индивидуальный подоходный налог)
	case consts.PaymentCodeBudgetTaxIncomeIndividual:
		cfg.setIPNConfigs()
	// СН (Социальный налог)
	case consts.PaymentCodeBudgetTaxSocial:
		cfg.setSNConfigs()
	// СО (Социальные отчисления)
	case consts.PaymentCodeBudgetSocialBasic:
		cfg.setSOConfigs()
	default:
		// Если код платежа не соответствует ни одному из известных, возвращаем nil
		return nil
	}

	return cfg
}

func (cfg *Config) setOPVConfigs() {
	cfg.Direction = OPVDirection
	cfg.PayerBankBIC = OPVPayerBankBIC
	cfg.TransactionType = OPVTransactionType
	cfg.Currency = OPVCurrency
	cfg.BeneficiaryBINIIN = OPVBeneficiaryBINIIN
	cfg.BeneficiaryCountry = OPVBeneficiaryCountry
	cfg.BeneficiaryAccount = OPVBeneficiaryAccount
	cfg.BeneficiaryKbe = OPVBeneficiaryKbe
	cfg.BeneficiaryBankBIC = OPVBeneficiaryBankBIC
	cfg.BeneficiaryType = OPVBeneficiaryType
	cfg.PayerCountry = OPVPayerCountry
	cfg.PayerKod = OPVPayerKod
	cfg.PayerType = OPVPayerType
	cfg.PaymentType = OPVPaymentType
	cfg.KBK = OPVKbk
	cfg.ValidPurposeCodes = []string{KnpCodeOPV010, KnpCodeOPV019}
	cfg.RealBeneficiaryType = OPVRealBeneficiaryType

	switch cfg.Locale {
	case locale.Ru:
		cfg.BeneficiaryBankName = OPVBeneficiaryBankNameRu
		cfg.BeneficiaryName = OPVBeneficiaryNameRu
		cfg.PayerBankName = OPVPayerBankNameRu
	default:
		cfg.BeneficiaryBankName = OPVBeneficiaryBankNameKz
		cfg.BeneficiaryName = OPVBeneficiaryNameKz
		cfg.PayerBankName = OPVPayerBankNameKz
	}
}

func (cfg *Config) setOPVRConfigs() {
	cfg.Direction = OPVRDirection
	cfg.PayerBankBIC = OPVRPayerBankBIC
	cfg.TransactionType = OPVRTransactionType
	cfg.Currency = OPVRCurrency
	cfg.BeneficiaryBINIIN = OPVRBeneficiaryBINIIN
	cfg.BeneficiaryCountry = OPVRBeneficiaryCountry
	cfg.BeneficiaryAccount = OPVRBeneficiaryAccount
	cfg.BeneficiaryKbe = OPVRBeneficiaryKbe
	cfg.BeneficiaryBankBIC = OPVRBeneficiaryBankBIC
	cfg.BeneficiaryType = OPVRBeneficiaryType
	cfg.PayerCountry = OPVRPayerCountry
	cfg.PayerKod = OPVRPayerKod
	cfg.PayerType = OPVRPayerType
	cfg.PaymentType = OPVRPaymentType
	cfg.KBK = OPVRKbk
	cfg.ValidPurposeCodes = []string{KnpCodeOPVR089, KnpCodeOPVR098}
	cfg.RealBeneficiaryType = OPVRealBeneficiaryType

	switch cfg.Locale {
	case locale.Ru:
		cfg.PayerBankName = OPVRPayerBankNameRu
		cfg.BeneficiaryName = OPVRBeneficiaryNameRu
		cfg.BeneficiaryBankName = OPVRBeneficiaryBankNameRu
	default:
		cfg.PayerBankName = OPVRPayerBankNameKz
		cfg.BeneficiaryName = OPVRBeneficiaryNameKz
		cfg.BeneficiaryBankName = OPVRBeneficiaryBankNameKz
	}
}

func (cfg *Config) setOPPVConfigs() {
	cfg.Direction = OPPVDirection
	cfg.PayerBankBIC = OPPVPayerBankBIC
	cfg.TransactionType = OPPVTransactionType
	cfg.Currency = OPPVCurrency
	cfg.BeneficiaryBINIIN = OPPVBeneficiaryBINIIN
	cfg.BeneficiaryCountry = OPPVBeneficiaryCountry
	cfg.BeneficiaryAccount = OPPVBeneficiaryAccount
	cfg.BeneficiaryKbe = OPPVBeneficiaryKbe
	cfg.BeneficiaryBankBIC = OPPVBeneficiaryBankBIC
	cfg.BeneficiaryType = OPPVBeneficiaryType
	cfg.PayerCountry = OPPVPayerCountry
	cfg.PayerKod = OPPVPayerKod
	cfg.PayerType = OPPVPayerType
	cfg.PaymentType = OPPVPaymentType
	cfg.KBK = OPPVKbk
	cfg.ValidPurposeCodes = []string{KnpCodeOPPV009, KnpCodeOPPV015}
	cfg.RealBeneficiaryType = OPPVRealBeneficiaryType

	switch cfg.Locale {
	case locale.Ru:
		cfg.PayerBankName = OPPVPayerBankNameRu
		cfg.BeneficiaryName = OPPVBeneficiaryNameRu
		cfg.BeneficiaryBankName = OPPVBeneficiaryBankNameRu
	default:
		cfg.PayerBankName = OPPVPayerBankNameKz
		cfg.BeneficiaryName = OPPVBeneficiaryNameKz
		cfg.BeneficiaryBankName = OPPVBeneficiaryBankNameKz
	}
}

func (cfg *Config) setVOSMSConfigs() {
	cfg.Direction = VOSMSDirection
	cfg.PayerBankBIC = VOSMSPayerBankBIC
	cfg.TransactionType = VOSMSTransactionType
	cfg.Currency = VOSMSCurrency
	cfg.BeneficiaryBINIIN = VOSMSBeneficiaryBINIIN
	cfg.BeneficiaryCountry = VOSMSBeneficiaryCountry
	cfg.BeneficiaryAccount = VOSMSBeneficiaryAccount
	cfg.BeneficiaryKbe = VOSMSBeneficiaryKbe
	cfg.BeneficiaryBankBIC = VOSMSBeneficiaryBankBIC
	cfg.BeneficiaryType = VOSMSBeneficiaryType
	cfg.PayerCountry = VOSMSPayerCountry
	cfg.PayerKod = VOSMSPayerKod
	cfg.PayerType = VOSMSPayerType
	cfg.PaymentType = VOSMSPaymentType
	cfg.KBK = VOSMSKbk
	cfg.ValidPurposeCodes = []string{KnpCodeVOSMS122, KnpCodeVOSMS124}
	cfg.RealBeneficiaryType = VOSMSRealBeneficiaryType

	switch cfg.Locale {
	case locale.Ru:
		cfg.PayerBankName = VOSMSPayerBankNameRu
		cfg.BeneficiaryName = VOSMSBeneficiaryNameRu
		cfg.BeneficiaryBankName = VOSMSBeneficiaryBankNameRu
	default:
		cfg.PayerBankName = VOSMSPayerBankNameKz
		cfg.BeneficiaryName = VOSMSBeneficiaryNameKz
		cfg.BeneficiaryBankName = VOSMSBeneficiaryBankNameKz
	}
}

func (cfg *Config) setOOSMSConfigs() {
	cfg.Direction = OOSMSDirection
	cfg.PayerBankBIC = OOSMSPayerBankBIC
	cfg.TransactionType = OOSMSTransactionType
	cfg.Currency = OOSMSCurrency
	cfg.BeneficiaryBINIIN = OOSMSBeneficiaryBINIIN
	cfg.BeneficiaryCountry = OOSMSBeneficiaryCountry
	cfg.BeneficiaryAccount = OOSMSBeneficiaryAccount
	cfg.BeneficiaryKbe = OOSMSBeneficiaryKbe
	cfg.BeneficiaryBankBIC = OOSMSBeneficiaryBankBIC
	cfg.BeneficiaryType = OOSMSBeneficiaryType
	cfg.PayerCountry = OOSMSPayerCountry
	cfg.PayerKod = OOSMSPayerKod
	cfg.PayerType = OOSMSPayerType
	cfg.PaymentType = OOSMSPaymentType
	cfg.KBK = OOSMSKbk
	cfg.ValidPurposeCodes = []string{KnpCodeOOSMS121, KnpCodeOOSMS123}
	cfg.RealBeneficiaryType = OOSMSRealBeneficiaryType

	switch cfg.Locale {
	case locale.Ru:
		cfg.PayerBankName = OOSMSPayerBankNameRu
		cfg.BeneficiaryName = OOSMSBeneficiaryNameRu
		cfg.BeneficiaryBankName = OOSMSBeneficiaryBankNameRu
	default:
		cfg.PayerBankName = OOSMSPayerBankNameKz
		cfg.BeneficiaryName = OOSMSBeneficiaryNameKz
		cfg.BeneficiaryBankName = OOSMSBeneficiaryBankNameKz
	}
}

func (cfg *Config) setIPNConfigs() {
	cfg.Direction = IPNDirection
	cfg.PayerBankBIC = IPNPayerBankBIC
	cfg.TransactionType = IPNTransactionType
	cfg.Currency = IPNCurrency
	cfg.BeneficiaryBINIIN = IPNBeneficiaryBINIIN
	cfg.BeneficiaryCountry = IPNBeneficiaryCountry
	cfg.BeneficiaryAccount = IPNBeneficiaryAccount
	cfg.BeneficiaryKbe = IPNBeneficiaryKbe
	cfg.BeneficiaryBankBIC = IPNBeneficiaryBankBIC
	cfg.BeneficiaryType = IPNBeneficiaryType
	cfg.PayerCountry = IPNPayerCountry
	cfg.PayerKod = IPNPayerKod
	cfg.PayerType = IPNPayerType
	cfg.PaymentType = IPNPaymentType
	cfg.KBK = IPNKbk
	cfg.ValidPurposeCodes = []string{KnpCodeTax911, KnpCodeTax912}
	cfg.RealBeneficiaryType = IPNRealBeneficiaryType

	switch cfg.Locale {
	case locale.Ru:
		cfg.PayerBankName = IPNPayerBankNameRu
		cfg.BeneficiaryName = IPNBeneficiaryNameRu
		cfg.BeneficiaryBankName = IPNBeneficiaryBankNameRu
	default:
		cfg.PayerBankName = IPNPayerBankNameKz
		cfg.BeneficiaryName = IPNBeneficiaryNameKz
		cfg.BeneficiaryBankName = IPNBeneficiaryBankNameKz
	}
}

func (cfg *Config) setSNConfigs() {
	cfg.Direction = SNDirection
	cfg.PayerBankBIC = SNPayerBankBIC
	cfg.TransactionType = SNTransactionType
	cfg.Currency = SNCurrency
	cfg.BeneficiaryBINIIN = SNBeneficiaryBINIIN
	cfg.BeneficiaryCountry = SNBeneficiaryCountry
	cfg.BeneficiaryAccount = SNBeneficiaryAccount
	cfg.BeneficiaryKbe = SNBeneficiaryKbe
	cfg.BeneficiaryBankBIC = SNBeneficiaryBankBIC
	cfg.BeneficiaryType = SNBeneficiaryType
	cfg.PayerCountry = SNPayerCountry
	cfg.PayerKod = SNPayerKod
	cfg.PayerType = SNPayerType
	cfg.PaymentType = SNPaymentType
	cfg.KBK = SNKbk
	cfg.ValidPurposeCodes = []string{KnpCodeTax911, KnpCodeTax912}
	cfg.RealBeneficiaryType = SNRealBeneficiaryType

	switch cfg.Locale {
	case locale.Ru:
		cfg.PayerBankName = SNPayerBankNameRu
		cfg.BeneficiaryName = SNBeneficiaryNameRu
		cfg.BeneficiaryBankName = SNBeneficiaryBankNameRu
	default:
		cfg.PayerBankName = SNPayerBankNameKz
		cfg.BeneficiaryName = SNBeneficiaryNameKz
		cfg.BeneficiaryBankName = SNBeneficiaryBankNameKz
	}
}

func (cfg *Config) setSOConfigs() {
	cfg.Direction = SODirection
	cfg.PayerBankBIC = SOPayerBankBIC
	cfg.TransactionType = SOTransactionType
	cfg.Currency = SOCurrency
	cfg.BeneficiaryBINIIN = SOBeneficiaryBINIIN
	cfg.BeneficiaryCountry = SOBeneficiaryCountry
	cfg.BeneficiaryAccount = SOBeneficiaryAccount
	cfg.BeneficiaryKbe = SOBeneficiaryKbe
	cfg.BeneficiaryBankBIC = SOBeneficiaryBankBIC
	cfg.BeneficiaryType = SOBeneficiaryType
	cfg.PayerCountry = SOPayerCountry
	cfg.PayerKod = SOPayerKod
	cfg.PayerType = SOPayerType
	cfg.PaymentType = SOPaymentType
	cfg.KBK = SOKbk
	cfg.ValidPurposeCodes = []string{KnpCodeSO012, KnpCodeSO017}
	cfg.RealBeneficiaryType = SORealBeneficiaryType

	switch cfg.Locale {
	case locale.Ru:
		cfg.PayerBankName = SOPayerBankNameRu
		cfg.BeneficiaryName = SOBeneficiaryNameRu
		cfg.BeneficiaryBankName = SOBeneficiaryBankNameRu
	default:
		cfg.PayerBankName = SOPayerBankNameKz
		cfg.BeneficiaryName = SOBeneficiaryNameKz
		cfg.BeneficiaryBankName = SOBeneficiaryBankNameKz
	}
}
