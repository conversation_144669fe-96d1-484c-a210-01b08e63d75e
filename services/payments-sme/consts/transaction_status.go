package consts

// TransactionStatus - статус транзакции в БД (transactions)
type TransactionStatus string

const (
	TransactionStatusInitialized TransactionStatus = "INITIALIZED"
	TransactionStatusInProgress  TransactionStatus = "IN_PROGRESS"
	TransactionStatusCompleted   TransactionStatus = "COMPLETED"
	TransactionStatusRejected    TransactionStatus = "REJECTED"
)

func (s TransactionStatus) String() string {
	return string(s)
}

func (s TransactionStatus) Values() []string {
	return []string{
		TransactionStatusInitialized.String(),
		TransactionStatusInProgress.String(),
		TransactionStatusCompleted.String(),
		TransactionStatusRejected.String(),
	}
}

// DocumentPDFStatus - статус транзакции в платежном поручении
type DocumentPDFStatus string

const (
	DocumentPDFStatusInProgress   DocumentPDFStatus = "В обработке"
	DocumentPDFStatusInProgressKZ DocumentPDFStatus = "Өңдеуде"
	DocumentPDFStatusCompleted    DocumentPDFStatus = "Исполнено"
	DocumentPDFStatusCompletedKZ  DocumentPDFStatus = "Орындалды"
	DocumentPDFStatusRejected     DocumentPDFStatus = "Отклонено"
	DocumentPDFStatusRejectedKZ   DocumentPDFStatus = "Бас тартылды"
)

func (s DocumentPDFStatus) String() string {
	return string(s)
}
