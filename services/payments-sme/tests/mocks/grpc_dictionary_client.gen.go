// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/specs/proto/dictionary (interfaces: DictionaryClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"

	dictionary "git.redmadrobot.com/zaman/backend/zaman/specs/proto/dictionary"
)

// MockDictionaryClient is a mock of DictionaryClient interface.
type MockDictionaryClient struct {
	ctrl     *gomock.Controller
	recorder *MockDictionaryClientMockRecorder
}

// MockDictionaryClientMockRecorder is the mock recorder for MockDictionaryClient.
type MockDictionaryClientMockRecorder struct {
	mock *MockDictionaryClient
}

// NewMockDictionaryClient creates a new mock instance.
func NewMockDictionaryClient(ctrl *gomock.Controller) *MockDictionaryClient {
	mock := &MockDictionaryClient{ctrl: ctrl}
	mock.recorder = &MockDictionaryClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDictionaryClient) EXPECT() *MockDictionaryClientMockRecorder {
	return m.recorder
}

// DictCreate mocks base method.
func (m *MockDictionaryClient) DictCreate(arg0 context.Context, arg1 *dictionary.DictCreateReq, arg2 ...grpc.CallOption) (*dictionary.DictCreateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DictCreate", varargs...)
	ret0, _ := ret[0].(*dictionary.DictCreateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DictCreate indicates an expected call of DictCreate.
func (mr *MockDictionaryClientMockRecorder) DictCreate(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DictCreate", reflect.TypeOf((*MockDictionaryClient)(nil).DictCreate), varargs...)
}

// DictDelete mocks base method.
func (m *MockDictionaryClient) DictDelete(arg0 context.Context, arg1 *dictionary.DictDeleteReq, arg2 ...grpc.CallOption) (*dictionary.DictDeleteResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DictDelete", varargs...)
	ret0, _ := ret[0].(*dictionary.DictDeleteResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DictDelete indicates an expected call of DictDelete.
func (mr *MockDictionaryClientMockRecorder) DictDelete(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DictDelete", reflect.TypeOf((*MockDictionaryClient)(nil).DictDelete), varargs...)
}

// DictGet mocks base method.
func (m *MockDictionaryClient) DictGet(arg0 context.Context, arg1 *dictionary.DictGetReq, arg2 ...grpc.CallOption) (*dictionary.DictGetResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DictGet", varargs...)
	ret0, _ := ret[0].(*dictionary.DictGetResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DictGet indicates an expected call of DictGet.
func (mr *MockDictionaryClientMockRecorder) DictGet(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DictGet", reflect.TypeOf((*MockDictionaryClient)(nil).DictGet), varargs...)
}

// DictGetByName mocks base method.
func (m *MockDictionaryClient) DictGetByName(arg0 context.Context, arg1 *dictionary.DictGetByNameReq, arg2 ...grpc.CallOption) (*dictionary.DictGetByNameResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DictGetByName", varargs...)
	ret0, _ := ret[0].(*dictionary.DictGetByNameResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DictGetByName indicates an expected call of DictGetByName.
func (mr *MockDictionaryClientMockRecorder) DictGetByName(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DictGetByName", reflect.TypeOf((*MockDictionaryClient)(nil).DictGetByName), varargs...)
}

// DictGetList mocks base method.
func (m *MockDictionaryClient) DictGetList(arg0 context.Context, arg1 *dictionary.DictGetListReq, arg2 ...grpc.CallOption) (*dictionary.DictGetListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DictGetList", varargs...)
	ret0, _ := ret[0].(*dictionary.DictGetListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DictGetList indicates an expected call of DictGetList.
func (mr *MockDictionaryClientMockRecorder) DictGetList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DictGetList", reflect.TypeOf((*MockDictionaryClient)(nil).DictGetList), varargs...)
}

// DictUpdate mocks base method.
func (m *MockDictionaryClient) DictUpdate(arg0 context.Context, arg1 *dictionary.DictUpdateReq, arg2 ...grpc.CallOption) (*dictionary.DictUpdateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DictUpdate", varargs...)
	ret0, _ := ret[0].(*dictionary.DictUpdateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DictUpdate indicates an expected call of DictUpdate.
func (mr *MockDictionaryClientMockRecorder) DictUpdate(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DictUpdate", reflect.TypeOf((*MockDictionaryClient)(nil).DictUpdate), varargs...)
}

// DocCreate mocks base method.
func (m *MockDictionaryClient) DocCreate(arg0 context.Context, arg1 *dictionary.DocCreateReq, arg2 ...grpc.CallOption) (*dictionary.DocCreateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DocCreate", varargs...)
	ret0, _ := ret[0].(*dictionary.DocCreateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DocCreate indicates an expected call of DocCreate.
func (mr *MockDictionaryClientMockRecorder) DocCreate(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DocCreate", reflect.TypeOf((*MockDictionaryClient)(nil).DocCreate), varargs...)
}

// DocDelete mocks base method.
func (m *MockDictionaryClient) DocDelete(arg0 context.Context, arg1 *dictionary.DocDeleteReq, arg2 ...grpc.CallOption) (*dictionary.DocDeleteResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DocDelete", varargs...)
	ret0, _ := ret[0].(*dictionary.DocDeleteResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DocDelete indicates an expected call of DocDelete.
func (mr *MockDictionaryClientMockRecorder) DocDelete(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DocDelete", reflect.TypeOf((*MockDictionaryClient)(nil).DocDelete), varargs...)
}

// DocGet mocks base method.
func (m *MockDictionaryClient) DocGet(arg0 context.Context, arg1 *dictionary.DocGetReq, arg2 ...grpc.CallOption) (*dictionary.DocGetResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DocGet", varargs...)
	ret0, _ := ret[0].(*dictionary.DocGetResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DocGet indicates an expected call of DocGet.
func (mr *MockDictionaryClientMockRecorder) DocGet(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DocGet", reflect.TypeOf((*MockDictionaryClient)(nil).DocGet), varargs...)
}

// DocGetByName mocks base method.
func (m *MockDictionaryClient) DocGetByName(arg0 context.Context, arg1 *dictionary.DocGetByNameReq, arg2 ...grpc.CallOption) (*dictionary.DocGetByNameResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DocGetByName", varargs...)
	ret0, _ := ret[0].(*dictionary.DocGetByNameResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DocGetByName indicates an expected call of DocGetByName.
func (mr *MockDictionaryClientMockRecorder) DocGetByName(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DocGetByName", reflect.TypeOf((*MockDictionaryClient)(nil).DocGetByName), varargs...)
}

// DocGetList mocks base method.
func (m *MockDictionaryClient) DocGetList(arg0 context.Context, arg1 *dictionary.DocGetListReq, arg2 ...grpc.CallOption) (*dictionary.DocGetListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DocGetList", varargs...)
	ret0, _ := ret[0].(*dictionary.DocGetListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DocGetList indicates an expected call of DocGetList.
func (mr *MockDictionaryClientMockRecorder) DocGetList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DocGetList", reflect.TypeOf((*MockDictionaryClient)(nil).DocGetList), varargs...)
}

// DocGetListByFilter mocks base method.
func (m *MockDictionaryClient) DocGetListByFilter(arg0 context.Context, arg1 *dictionary.DocGetListByFilterReq, arg2 ...grpc.CallOption) (*dictionary.DocGetListByFilterResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DocGetListByFilter", varargs...)
	ret0, _ := ret[0].(*dictionary.DocGetListByFilterResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DocGetListByFilter indicates an expected call of DocGetListByFilter.
func (mr *MockDictionaryClientMockRecorder) DocGetListByFilter(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DocGetListByFilter", reflect.TypeOf((*MockDictionaryClient)(nil).DocGetListByFilter), varargs...)
}

// DocGetListByName mocks base method.
func (m *MockDictionaryClient) DocGetListByName(arg0 context.Context, arg1 *dictionary.DocGetListByNameReq, arg2 ...grpc.CallOption) (*dictionary.DocGetListByNameResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DocGetListByName", varargs...)
	ret0, _ := ret[0].(*dictionary.DocGetListByNameResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DocGetListByName indicates an expected call of DocGetListByName.
func (mr *MockDictionaryClientMockRecorder) DocGetListByName(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DocGetListByName", reflect.TypeOf((*MockDictionaryClient)(nil).DocGetListByName), varargs...)
}

// DocOrderUpdate mocks base method.
func (m *MockDictionaryClient) DocOrderUpdate(arg0 context.Context, arg1 *dictionary.DocOrderUpdateReq, arg2 ...grpc.CallOption) (*dictionary.DocOrderUpdateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DocOrderUpdate", varargs...)
	ret0, _ := ret[0].(*dictionary.DocOrderUpdateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DocOrderUpdate indicates an expected call of DocOrderUpdate.
func (mr *MockDictionaryClientMockRecorder) DocOrderUpdate(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DocOrderUpdate", reflect.TypeOf((*MockDictionaryClient)(nil).DocOrderUpdate), varargs...)
}

// DocTreeGetLine mocks base method.
func (m *MockDictionaryClient) DocTreeGetLine(arg0 context.Context, arg1 *dictionary.DocTreeGetLineReq, arg2 ...grpc.CallOption) (*dictionary.DocTreeGetLineResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DocTreeGetLine", varargs...)
	ret0, _ := ret[0].(*dictionary.DocTreeGetLineResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DocTreeGetLine indicates an expected call of DocTreeGetLine.
func (mr *MockDictionaryClientMockRecorder) DocTreeGetLine(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DocTreeGetLine", reflect.TypeOf((*MockDictionaryClient)(nil).DocTreeGetLine), varargs...)
}

// DocUpdate mocks base method.
func (m *MockDictionaryClient) DocUpdate(arg0 context.Context, arg1 *dictionary.DocUpdateReq, arg2 ...grpc.CallOption) (*dictionary.DocUpdateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DocUpdate", varargs...)
	ret0, _ := ret[0].(*dictionary.DocUpdateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DocUpdate indicates an expected call of DocUpdate.
func (mr *MockDictionaryClientMockRecorder) DocUpdate(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DocUpdate", reflect.TypeOf((*MockDictionaryClient)(nil).DocUpdate), varargs...)
}

// GetPostalIndex mocks base method.
func (m *MockDictionaryClient) GetPostalIndex(arg0 context.Context, arg1 *dictionary.GetPostalIndexReq, arg2 ...grpc.CallOption) (*dictionary.GetPostalIndexResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPostalIndex", varargs...)
	ret0, _ := ret[0].(*dictionary.GetPostalIndexResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostalIndex indicates an expected call of GetPostalIndex.
func (mr *MockDictionaryClientMockRecorder) GetPostalIndex(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostalIndex", reflect.TypeOf((*MockDictionaryClient)(nil).GetPostalIndex), varargs...)
}

// HealthCheck mocks base method.
func (m *MockDictionaryClient) HealthCheck(arg0 context.Context, arg1 *dictionary.HealthCheckReq, arg2 ...grpc.CallOption) (*dictionary.HealthCheckResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HealthCheck", varargs...)
	ret0, _ := ret[0].(*dictionary.HealthCheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockDictionaryClientMockRecorder) HealthCheck(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockDictionaryClient)(nil).HealthCheck), varargs...)
}

// JobGetStatus mocks base method.
func (m *MockDictionaryClient) JobGetStatus(arg0 context.Context, arg1 *dictionary.JobGetStatusReq, arg2 ...grpc.CallOption) (*dictionary.JobGetStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "JobGetStatus", varargs...)
	ret0, _ := ret[0].(*dictionary.JobGetStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// JobGetStatus indicates an expected call of JobGetStatus.
func (mr *MockDictionaryClientMockRecorder) JobGetStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JobGetStatus", reflect.TypeOf((*MockDictionaryClient)(nil).JobGetStatus), varargs...)
}

// JobGetStatusAll mocks base method.
func (m *MockDictionaryClient) JobGetStatusAll(arg0 context.Context, arg1 *dictionary.JobGetStatusAllReq, arg2 ...grpc.CallOption) (*dictionary.JobGetStatusAllResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "JobGetStatusAll", varargs...)
	ret0, _ := ret[0].(*dictionary.JobGetStatusAllResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// JobGetStatusAll indicates an expected call of JobGetStatusAll.
func (mr *MockDictionaryClientMockRecorder) JobGetStatusAll(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JobGetStatusAll", reflect.TypeOf((*MockDictionaryClient)(nil).JobGetStatusAll), varargs...)
}

// JobRun mocks base method.
func (m *MockDictionaryClient) JobRun(arg0 context.Context, arg1 *dictionary.JobRunReq, arg2 ...grpc.CallOption) (*dictionary.JobRunResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "JobRun", varargs...)
	ret0, _ := ret[0].(*dictionary.JobRunResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// JobRun indicates an expected call of JobRun.
func (mr *MockDictionaryClientMockRecorder) JobRun(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JobRun", reflect.TypeOf((*MockDictionaryClient)(nil).JobRun), varargs...)
}

// JobStop mocks base method.
func (m *MockDictionaryClient) JobStop(arg0 context.Context, arg1 *dictionary.JobStopReq, arg2 ...grpc.CallOption) (*dictionary.JobStopResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "JobStop", varargs...)
	ret0, _ := ret[0].(*dictionary.JobStopResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// JobStop indicates an expected call of JobStop.
func (mr *MockDictionaryClientMockRecorder) JobStop(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JobStop", reflect.TypeOf((*MockDictionaryClient)(nil).JobStop), varargs...)
}

// KATOMapFromTSOID mocks base method.
func (m *MockDictionaryClient) KATOMapFromTSOID(arg0 context.Context, arg1 *dictionary.KATOMapFromTSOIDReq, arg2 ...grpc.CallOption) (*dictionary.KATOMapFromTSOIDResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "KATOMapFromTSOID", varargs...)
	ret0, _ := ret[0].(*dictionary.KATOMapFromTSOIDResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// KATOMapFromTSOID indicates an expected call of KATOMapFromTSOID.
func (mr *MockDictionaryClientMockRecorder) KATOMapFromTSOID(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KATOMapFromTSOID", reflect.TypeOf((*MockDictionaryClient)(nil).KATOMapFromTSOID), varargs...)
}

// KATOToABIS mocks base method.
func (m *MockDictionaryClient) KATOToABIS(arg0 context.Context, arg1 *dictionary.KATOToABISReq, arg2 ...grpc.CallOption) (*dictionary.KATOToABISResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "KATOToABIS", varargs...)
	ret0, _ := ret[0].(*dictionary.KATOToABISResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// KATOToABIS indicates an expected call of KATOToABIS.
func (mr *MockDictionaryClientMockRecorder) KATOToABIS(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KATOToABIS", reflect.TypeOf((*MockDictionaryClient)(nil).KATOToABIS), varargs...)
}
