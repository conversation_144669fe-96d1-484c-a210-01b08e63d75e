// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/specs/proto/otp (interfaces: OtpClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"

	otp "git.redmadrobot.com/zaman/backend/zaman/specs/proto/otp"
)

// MockOtpClient is a mock of OtpClient interface.
type MockOtpClient struct {
	ctrl     *gomock.Controller
	recorder *MockOtpClientMockRecorder
}

// MockOtpClientMockRecorder is the mock recorder for MockOtpClient.
type MockOtpClientMockRecorder struct {
	mock *MockOtpClient
}

// NewMockOtpClient creates a new mock instance.
func NewMockOtpClient(ctrl *gomock.Controller) *MockOtpClient {
	mock := &MockOtpClient{ctrl: ctrl}
	mock.recorder = &MockOtpClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOtpClient) EXPECT() *MockOtpClientMockRecorder {
	return m.recorder
}

// GenerateCode mocks base method.
func (m *MockOtpClient) GenerateCode(arg0 context.Context, arg1 *otp.GenerateCodeReq, arg2 ...grpc.CallOption) (*otp.GenerateCodeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateCode", varargs...)
	ret0, _ := ret[0].(*otp.GenerateCodeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateCode indicates an expected call of GenerateCode.
func (mr *MockOtpClientMockRecorder) GenerateCode(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateCode", reflect.TypeOf((*MockOtpClient)(nil).GenerateCode), varargs...)
}

// GenerateRetryCode mocks base method.
func (m *MockOtpClient) GenerateRetryCode(arg0 context.Context, arg1 *otp.GenerateRetryCodeReq, arg2 ...grpc.CallOption) (*otp.GenerateCodeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateRetryCode", varargs...)
	ret0, _ := ret[0].(*otp.GenerateCodeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateRetryCode indicates an expected call of GenerateRetryCode.
func (mr *MockOtpClientMockRecorder) GenerateRetryCode(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateRetryCode", reflect.TypeOf((*MockOtpClient)(nil).GenerateRetryCode), varargs...)
}

// HealthCheck mocks base method.
func (m *MockOtpClient) HealthCheck(arg0 context.Context, arg1 *otp.HealthCheckReq, arg2 ...grpc.CallOption) (*otp.HealthCheckResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HealthCheck", varargs...)
	ret0, _ := ret[0].(*otp.HealthCheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockOtpClientMockRecorder) HealthCheck(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockOtpClient)(nil).HealthCheck), varargs...)
}

// ValidateCode mocks base method.
func (m *MockOtpClient) ValidateCode(arg0 context.Context, arg1 *otp.ValidateCodeReq, arg2 ...grpc.CallOption) (*otp.ValidateCodeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ValidateCode", varargs...)
	ret0, _ := ret[0].(*otp.ValidateCodeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateCode indicates an expected call of ValidateCode.
func (mr *MockOtpClientMockRecorder) ValidateCode(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateCode", reflect.TypeOf((*MockOtpClient)(nil).ValidateCode), varargs...)
}
