// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package mocks

import (
	"testing"

	"github.com/golang/mock/gomock"
)

type Mocks struct {
	GRPC      GRPC
	Providers Providers
}

type GRPC struct {
	Otp          *MockOtpClient
	Dictionary   *MockDictionaryClient
	Colvirbridge *MockColvirbridgeClient
	Users        *MockUsersClient
	Pkbbridge    *MockPkbbridgeClient
	Documents    *MockDocumentsClient
}

type Providers struct {
}

func NewMocks(t *testing.T) *Mocks {
	return &Mocks{
		GRPC: GRPC{
			Otp:          NewMockOtpClient(gomock.NewController(t)),
			Dictionary:   NewMockDictionaryClient(gomock.NewController(t)),
			Colvirbridge: NewMockColvirbridgeClient(gomock.NewController(t)),
			Users:        NewMockUsersClient(gomock.NewController(t)),
			Pkbbridge:    NewMockPkbbridgeClient(gomock.NewController(t)),
			Documents:    NewMockDocumentsClient(gomock.NewController(t)),
		},
		Providers: Providers{},
	}
}
