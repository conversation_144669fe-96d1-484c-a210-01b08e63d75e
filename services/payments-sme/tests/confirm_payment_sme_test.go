package tests

import (
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/uuid"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/confirmations"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/payments"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/rejections"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/transaction"
	colvirBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
	pbPaymentsSme "git.redmadrobot.com/zaman/backend/zaman/specs/proto/payments-sme"
)

var (
	// Моковый ответ на проверку внутреннего платежа (успешно)
	checkDomesticPaymentResponse = &colvirBridge.CheckDomesticPaymentResponse{
		ErrorStatus: colvirBridge.ErrorStatus_NoError,
	}

	// Моковый ответ на проверку внутреннего платежа (ошибка)
	checkDomesticPaymentResponseError = &colvirBridge.CheckDomesticPaymentResponse{
		ErrorStatus: colvirBridge.ErrorStatus_Error,
		ValidationResponse: &colvirBridge.ErrorMessage{
			Code: "1",
		},
		Error: &colvirBridge.ErrorMessage{
			Code: consts.ColvirErrorMessageCodeInsufficientFunds.String(),
		},
	}

	// Моковый ответ на проведение внутреннего платежа (успешно)
	executeDomesticPaymentResponse = &colvirBridge.ExecuteDomesticPaymentResponse{
		ErrorStatus: colvirBridge.ErrorStatus_NoError,
		Result: &colvirBridge.ExecuteDomesticPaymentResult{
			ColvirReferenceID: "1234567890",
			ExecDate:          GetColvirExecDate(),
			StatusCode:        consts.ColvirDocumentStatusBooked.String(),
		},
	}

	// Моковый ответ на проведение внутреннего платежа (ошибка)
	executeDomesticPaymentResponseFailed = &colvirBridge.ExecuteDomesticPaymentResponse{
		ErrorStatus: colvirBridge.ErrorStatus_Error,
		Error: &colvirBridge.ErrorMessage{
			Code: consts.ColvirErrorMessageCodeInsufficientFunds.String(),
		},
	}

	// Моковый ответ на проверку массового платежа (успешно)
	checkDomesticMassPaymentResponse = &colvirBridge.CheckDomesticMassPaymentResponse{
		Code: "0",
	}

	// Моковый ответ на проверку массового платежа (ошибка)
	checkDomesticMassPaymentResponseError = &colvirBridge.CheckDomesticMassPaymentResponse{
		Code: "1",
		ValidationResponse: &colvirBridge.CheckDomesticMassPaymentValidationResponse{
			Code:     "1",
			Message:  "message",
			Context:  "context",
			Severity: "severity",
		},
	}

	// Моковый ответ на проведение массового платежа (успешно)
	executeDomesticMassPaymentResponse = &colvirBridge.ExecuteDomesticMassPaymentResponse{
		Code: "0",
		Result: &colvirBridge.ExecuteDomesticMassPaymentResult{
			ColvirReferenceID: "1234567890",
			StatusCode:        consts.ColvirDocumentStatusBooked.String(),
			AcceptedDate:      "2024-01-01",
			DocumentNumber:    "1234567890",
			Date:              "2024-01-01",
			ExecDate:          *GetColvirExecDate(),
		},
	}
)

func (s *Suite) TestConfirmPaymentSme_InternalPayment_Success() {
	// Создаем transaction в БД (со статусом INITIALIZED)
	transaction, err := s.postgresDB.Transaction.Create().
		SetID(testTransactionID).
		SetTransactionNumber(testTransactionNumber).
		SetTransactionDate(time.Now()).
		SetTransactionType(transaction.TransactionType(consts.TransactionTypePayment)).
		SetInitiatorID(testInitiatorID).
		SetIdempotencyKey(testIdempotencyKey).
		SetTransactionStatus(transaction.TransactionStatus(consts.TransactionStatusInitialized)).
		SetTransactionAmount(testTransactionAmount).
		SetTransactionCurrency(transaction.TransactionCurrency(consts.CurrencyKZT)).
		SetTransactionTotalAmount(testTransactionTotalAmount).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(transaction)

	// Создаем confirmation в БД
	confirmation, err := s.postgresDB.Confirmations.Create().
		SetID(testTransactionID).
		SetConfirmationType(confirmations.ConfirmationType(consts.ConfirmationTypeOtp)).
		SetConfirmationDate(time.Now()).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(confirmation)

	// Создаем payment в БД
	payment, err := s.postgresDB.Payments.Create().
		SetID(testTransactionID).
		SetPaymentCode(testPaymentCode).
		SetPaymentType(payments.PaymentType(consts.PaymentTypePensionTransfer)).
		SetPaymentPeriod(GetPreviousMonthPeriod()).
		SetKbk(consts.KbkIpnWithSourceOfPayment.String()).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(payment)

	// Мокаем проверку возможности платежа
	s.mocks.GRPC.Colvirbridge.EXPECT().CheckDomesticPayment(gomock.Any(), gomock.Any()).
		Return(checkDomesticPaymentResponse, nil).Times(1)

	// Мокаем проведение платежа
	s.mocks.GRPC.Colvirbridge.EXPECT().ExecuteDomesticPayment(gomock.Any(), gomock.Any()).
		Return(executeDomesticPaymentResponse, nil).Times(1)

	// Вызываем метод confirmPaymentSme
	resp, err := s.grpc.ConfirmPaymentSme(s.ctx, &pbPaymentsSme.ConfirmPaymentSmeReq{
		TransactionID: testTransactionID.String(),
	})
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().Equal(string(consts.TransactionStatusInProgress), resp.Status)
}

func (s *Suite) TestConfirmPaymentSme_TransactionStatusRejected() {
	transactionId := uuid.New()

	// Создаем transaction в БД (со статусом REJECTED)
	transaction, err := s.postgresDB.Transaction.Create().
		SetID(transactionId).
		SetTransactionNumber(testTransactionNumber).
		SetTransactionDate(time.Now()).
		SetTransactionType(transaction.TransactionType(consts.TransactionTypePayment)).
		SetInitiatorID(testInitiatorID).
		SetIdempotencyKey(testIdempotencyKey).
		SetTransactionStatus(transaction.TransactionStatus(consts.TransactionStatusRejected)).
		SetTransactionAmount(testTransactionAmount).
		SetTransactionCurrency(transaction.TransactionCurrency(consts.CurrencyKZT)).
		SetTransactionTotalAmount(testTransactionTotalAmount).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(transaction)

	// Вызываем метод confirmPaymentSme
	resp, err := s.grpc.ConfirmPaymentSme(s.ctx, &pbPaymentsSme.ConfirmPaymentSmeReq{
		TransactionID: transactionId.String(),
	})
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().Equal(string(consts.TransactionStatusRejected), resp.Status)
	s.Require().Equal(string(consts.PaymentReasonCodeUnableToProcess), resp.ReasonCode)
	s.Require().Equal(string(consts.PaymentReasonUnableToProcess), resp.Reason)
}

func (s *Suite) TestConfirmPaymentSme_TransactionStatusCompleted() {
	transactionId := uuid.New()

	// Создаем transaction в БД (со статусом COMPLETED)
	transaction, err := s.postgresDB.Transaction.Create().
		SetID(transactionId).
		SetTransactionNumber(testTransactionNumber).
		SetTransactionDate(time.Now()).
		SetTransactionType(transaction.TransactionType(consts.TransactionTypePayment)).
		SetInitiatorID(testInitiatorID).
		SetIdempotencyKey(testIdempotencyKey).
		SetTransactionStatus(transaction.TransactionStatus(consts.TransactionStatusCompleted)).
		SetTransactionAmount(testTransactionAmount).
		SetTransactionCurrency(transaction.TransactionCurrency(consts.CurrencyKZT)).
		SetTransactionTotalAmount(testTransactionTotalAmount).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(transaction)

	// Вызываем метод confirmPaymentSme
	resp, err := s.grpc.ConfirmPaymentSme(s.ctx, &pbPaymentsSme.ConfirmPaymentSmeReq{
		TransactionID: transactionId.String(),
	})
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().Equal(string(consts.TransactionStatusCompleted), resp.Status)
}

func (s *Suite) TestConfirmPaymentSme_TransactionStatusInProgress() {
	transactionId := uuid.New()

	// Создаем transaction в БД (со статусом COMPLETED)
	transaction, err := s.postgresDB.Transaction.Create().
		SetID(transactionId).
		SetTransactionNumber(testTransactionNumber).
		SetTransactionDate(time.Now()).
		SetTransactionType(transaction.TransactionType(consts.TransactionTypePayment)).
		SetInitiatorID(testInitiatorID).
		SetIdempotencyKey(testIdempotencyKey).
		SetTransactionStatus(transaction.TransactionStatus(consts.TransactionStatusInProgress)).
		SetTransactionAmount(testTransactionAmount).
		SetTransactionCurrency(transaction.TransactionCurrency(consts.CurrencyKZT)).
		SetTransactionTotalAmount(testTransactionTotalAmount).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(transaction)

	// Вызываем метод confirmPaymentSme
	resp, err := s.grpc.ConfirmPaymentSme(s.ctx, &pbPaymentsSme.ConfirmPaymentSmeReq{
		TransactionID: transactionId.String(),
	})
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().Equal(string(consts.TransactionStatusInProgress), resp.Status)
}

func (s *Suite) TestConfirmPaymentSme_Rejections() {
	transactionId := uuid.New()

	// Создаем transaction в БД (со статусом INITIALIZED)
	transaction, err := s.postgresDB.Transaction.Create().
		SetID(transactionId).
		SetTransactionNumber(testTransactionNumber).
		SetTransactionDate(time.Now()).
		SetTransactionType(transaction.TransactionType(consts.TransactionTypePayment)).
		SetInitiatorID(testInitiatorID).
		SetIdempotencyKey(testIdempotencyKey).
		SetTransactionStatus(transaction.TransactionStatus(consts.TransactionStatusInitialized)).
		SetTransactionAmount(testTransactionAmount).
		SetTransactionCurrency(transaction.TransactionCurrency(consts.CurrencyKZT)).
		SetTransactionTotalAmount(testTransactionTotalAmount).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(transaction)

	// Создаем rejection в БД
	rejection, err := s.postgresDB.Rejections.Create().
		SetID(testRejectionID).
		SetParentTransactionID(transactionId).
		SetRejectionSource(rejections.RejectionSource(consts.RejectionSourceAml)).
		SetRejectionCode("rejection_code").
		SetRejectionScore("rejection_score").
		SetRejectionReason("rejection_reason").
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(rejection)

	// Вызываем метод confirmPaymentSme
	resp, err := s.grpc.ConfirmPaymentSme(s.ctx, &pbPaymentsSme.ConfirmPaymentSmeReq{
		TransactionID: transactionId.String(),
	})
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().Equal(string(consts.TransactionStatusRejected), resp.Status)
	s.Require().Equal(string(consts.PaymentReasonCodeUnableToProcess), resp.ReasonCode)
	s.Require().Equal(string(consts.PaymentReasonUnableToProcess), resp.Reason)
}

func (s *Suite) TestConfirmPaymentSme_ConfirmationNeeded() {
	transactionId := uuid.New()

	// Создаем transaction в БД (со статусом INITIALIZED)
	transaction, err := s.postgresDB.Transaction.Create().
		SetID(transactionId).
		SetTransactionNumber(testTransactionNumber).
		SetTransactionDate(time.Now()).
		SetTransactionType(transaction.TransactionType(consts.TransactionTypePayment)).
		SetInitiatorID(testInitiatorID).
		SetIdempotencyKey(testIdempotencyKey).
		SetTransactionStatus(transaction.TransactionStatus(consts.TransactionStatusInitialized)).
		SetTransactionAmount(testTransactionAmount).
		SetTransactionCurrency(transaction.TransactionCurrency(consts.CurrencyKZT)).
		SetTransactionTotalAmount(testTransactionTotalAmount).
		SetCreateTime(time.Now()).
		SetUpdateTime(time.Now()).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(transaction)

	// Вызываем метод confirmPaymentSme
	resp, err := s.grpc.ConfirmPaymentSme(s.ctx, &pbPaymentsSme.ConfirmPaymentSmeReq{
		TransactionID: transactionId.String(),
	})
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().Equal(string(consts.TransactionStatusInitialized), resp.Status)
	s.Require().True(resp.OtpNeeded)
}

func (s *Suite) TestConfirmPaymentSme_InternalPayment_CheckDomesticPayment_Failed() {
	transactionId := uuid.New()

	// Создаем transaction в БД (со статусом INITIALIZED)
	transaction, err := s.postgresDB.Transaction.Create().
		SetID(transactionId).
		SetTransactionNumber(testTransactionNumber).
		SetTransactionDate(time.Now()).
		SetTransactionType(transaction.TransactionType(consts.TransactionTypePayment)).
		SetInitiatorID(testInitiatorID).
		SetIdempotencyKey(testIdempotencyKey).
		SetTransactionStatus(transaction.TransactionStatus(consts.TransactionStatusInitialized)).
		SetTransactionAmount(testTransactionAmount).
		SetTransactionCurrency(transaction.TransactionCurrency(consts.CurrencyKZT)).
		SetTransactionTotalAmount(testTransactionTotalAmount).
		SetCreateTime(time.Now()).
		SetUpdateTime(time.Now()).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(transaction)

	// Создаем confirmation в БД
	confirmation, err := s.postgresDB.Confirmations.Create().
		SetID(transactionId).
		SetConfirmationType(confirmations.ConfirmationType(consts.ConfirmationTypeOtp)).
		SetConfirmationDate(time.Now()).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(confirmation)

	// Создаем payment в БД
	payment, err := s.postgresDB.Payments.Create().
		SetID(transactionId).
		SetPaymentCode(testPaymentCode).
		SetPaymentType(payments.PaymentType(consts.PaymentTypePensionTransfer)).
		SetPaymentPeriod(GetPreviousMonthPeriod()).
		SetKbk(consts.KbkIpnWithSourceOfPayment.String()).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(payment)

	// Мокаем проверку возможности платежа (возвращаем ошибку)
	s.mocks.GRPC.Colvirbridge.EXPECT().CheckDomesticPayment(gomock.Any(), gomock.Any()).
		Return(checkDomesticPaymentResponseError, nil).Times(1)

	// Вызываем метод confirmPaymentSme
	resp, err := s.grpc.ConfirmPaymentSme(s.ctx, &pbPaymentsSme.ConfirmPaymentSmeReq{
		TransactionID: transactionId.String(),
	})
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().Equal(string(consts.TransactionStatusRejected), resp.Status)
	s.Require().Equal(string(consts.PaymentReasonCodeUnableToProcess), resp.ReasonCode)
	s.Require().Equal(string(consts.PaymentReasonUnableToProcess), resp.Reason)
}

func (s *Suite) TestConfirmPaymentSme_InternalPayment_ExecDomesticPayment_Failed() {
	transactionId := uuid.New()

	// Создаем transaction в БД (со статусом INITIALIZED)
	transaction, err := s.postgresDB.Transaction.Create().
		SetID(transactionId).
		SetTransactionNumber(testTransactionNumber).
		SetTransactionDate(time.Now()).
		SetTransactionType(transaction.TransactionType(consts.TransactionTypePayment)).
		SetInitiatorID(testInitiatorID).
		SetIdempotencyKey(testIdempotencyKey).
		SetTransactionStatus(transaction.TransactionStatus(consts.TransactionStatusInitialized)).
		SetTransactionAmount(testTransactionAmount).
		SetTransactionCurrency(transaction.TransactionCurrency(consts.CurrencyKZT)).
		SetTransactionTotalAmount(testTransactionTotalAmount).
		SetCreateTime(time.Now()).
		SetUpdateTime(time.Now()).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(transaction)

	// Создаем confirmation в БД
	confirmation, err := s.postgresDB.Confirmations.Create().
		SetID(transactionId).
		SetConfirmationType(confirmations.ConfirmationType(consts.ConfirmationTypeOtp)).
		SetConfirmationDate(time.Now()).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(confirmation)

	// Создаем payment в БД
	payment, err := s.postgresDB.Payments.Create().
		SetID(transactionId).
		SetPaymentCode(testPaymentCode).
		SetPaymentType(payments.PaymentType(consts.PaymentTypePensionTransfer)).
		SetPaymentPeriod(GetPreviousMonthPeriod()).
		SetKbk(consts.KbkIpnWithSourceOfPayment.String()).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(payment)

	// Мокаем проверку возможности платежа
	s.mocks.GRPC.Colvirbridge.EXPECT().CheckDomesticPayment(gomock.Any(), gomock.Any()).
		Return(checkDomesticPaymentResponse, nil).Times(1)

	// Мокаем проведение платежа
	s.mocks.GRPC.Colvirbridge.EXPECT().ExecuteDomesticPayment(gomock.Any(), gomock.Any()).
		Return(executeDomesticPaymentResponseFailed, nil).Times(1)

	// Вызываем метод confirmPaymentSme
	resp, err := s.grpc.ConfirmPaymentSme(s.ctx, &pbPaymentsSme.ConfirmPaymentSmeReq{
		TransactionID: transactionId.String(),
	})

	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().Equal(string(consts.TransactionStatusRejected), resp.Status)
	s.Require().Equal(string(consts.PaymentReasonCodeInsufficientFunds), resp.ReasonCode)
	s.Require().Equal(string(consts.PaymentReasonInsufficientFunds), resp.Reason)
}

func (s *Suite) TestConfirmPaymentSme_MassPayment_Success() {
	transactionId := uuid.New()

	// Создаем transaction в БД (со статусом INITIALIZED)
	transaction, err := s.postgresDB.Transaction.Create().
		SetID(transactionId).
		SetTransactionNumber(testTransactionNumber).
		SetTransactionDate(time.Now()).
		SetTransactionType(transaction.TransactionType(consts.TransactionTypePayment)).
		SetInitiatorID(testInitiatorID).
		SetIdempotencyKey(testIdempotencyKey).
		SetTransactionStatus(transaction.TransactionStatus(consts.TransactionStatusInitialized)).
		SetTransactionAmount(testTransactionAmount).
		SetTransactionCurrency(transaction.TransactionCurrency(consts.CurrencyKZT)).
		SetTransactionTotalAmount(testTransactionTotalAmount).
		SetCreateTime(time.Now()).
		SetUpdateTime(time.Now()).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(transaction)

	// Создаем signatory в БД
	signatory, err := s.postgresDB.Signatories.Create().
		SetID(transactionId).
		SetSignatoryA("Гарри Поттер Джеймсович").
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(signatory)

	// Создаем confirmation в БД
	confirmation, err := s.postgresDB.Confirmations.Create().
		SetID(transactionId).
		SetConfirmationType(confirmations.ConfirmationType(consts.ConfirmationTypeOtp)).
		SetConfirmationDate(time.Now()).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(confirmation)

	// Создаем payment в БД с EmployeeList (массовый платеж)
	payment, err := s.postgresDB.Payments.Create().
		SetID(transactionId).
		SetPaymentCode(testPaymentCode).
		SetPaymentType(payments.PaymentType(consts.PaymentTypePensionTransfer)).
		SetPaymentPeriod(GetPreviousMonthPeriod()).
		SetEmployeeList(map[string]interface{}{
			"employee1": map[string]interface{}{
				"iin":    "123456789012",
				"amount": "50000",
			},
			"employee2": map[string]interface{}{
				"iin":    "987654321098",
				"amount": "50000",
			},
		}).
		SetKbk(consts.KbkIpnWithSourceOfPayment.String()).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(payment)

	// Мокаем проверку возможности массового платежа
	s.mocks.GRPC.Colvirbridge.EXPECT().CheckDomesticMassPayment(gomock.Any(), gomock.Any()).
		Return(checkDomesticMassPaymentResponse, nil).Times(1)

	// Мокаем проведение массового платежа
	s.mocks.GRPC.Colvirbridge.EXPECT().ExecuteDomesticMassPayment(gomock.Any(), gomock.Any()).
		Return(executeDomesticMassPaymentResponse, nil).Times(1)

	// Вызываем метод confirmPaymentSme
	resp, err := s.grpc.ConfirmPaymentSme(s.ctx, &pbPaymentsSme.ConfirmPaymentSmeReq{
		TransactionID: transactionId.String(),
	})
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	// Ожидаем IN_PROGRESS, так как в коде после успешного проведения массового платежа возвращается IN_PROGRESS
	s.Require().Equal(string(consts.TransactionStatusInProgress), resp.Status)
}

func (s *Suite) TestConfirmPaymentSme_MassPayment_CheckDomesticMassPayment_Failed() {
	transactionId := uuid.New()

	// Создаем transaction в БД (со статусом INITIALIZED)
	transaction, err := s.postgresDB.Transaction.Create().
		SetID(transactionId).
		SetTransactionNumber(testTransactionNumber).
		SetTransactionDate(time.Now()).
		SetTransactionType(transaction.TransactionType(consts.TransactionTypePayment)).
		SetInitiatorID(testInitiatorID).
		SetIdempotencyKey(testIdempotencyKey).
		SetTransactionStatus(transaction.TransactionStatus(consts.TransactionStatusInitialized)).
		SetTransactionAmount(testTransactionAmount).
		SetTransactionCurrency(transaction.TransactionCurrency(consts.CurrencyKZT)).
		SetTransactionTotalAmount(testTransactionTotalAmount).
		SetCreateTime(time.Now()).
		SetUpdateTime(time.Now()).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(transaction)

	// Создаем confirmation в БД
	confirmation, err := s.postgresDB.Confirmations.Create().
		SetID(transactionId).
		SetConfirmationType(confirmations.ConfirmationType(consts.ConfirmationTypeOtp)).
		SetConfirmationDate(time.Now()).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(confirmation)

	// Создаем signatory в БД
	signatory, err := s.postgresDB.Signatories.Create().
		SetID(transactionId).
		SetSignatoryA("Гарри Поттер Джеймсович").
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(signatory)

	// Создаем payment в БД с EmployeeList (массовый платеж)
	payment, err := s.postgresDB.Payments.Create().
		SetID(transactionId).
		SetPaymentCode(testPaymentCode).
		SetPaymentType(payments.PaymentType(consts.PaymentTypePensionTransfer)).
		SetPaymentPeriod(GetPreviousMonthPeriod()).
		SetEmployeeList(map[string]interface{}{
			"employee1": map[string]interface{}{
				"iin":    "123456789012",
				"amount": "50000",
			},
		}).
		SetKbk(consts.KbkIpnWithSourceOfPayment.String()).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(payment)

	// Мокаем проверку возможности массового платежа (возвращаем ошибку)
	s.mocks.GRPC.Colvirbridge.EXPECT().CheckDomesticMassPayment(gomock.Any(), gomock.Any()).
		Return(checkDomesticMassPaymentResponseError, nil).Times(1)

	// Вызываем метод confirmPaymentSme
	resp, err := s.grpc.ConfirmPaymentSme(s.ctx, &pbPaymentsSme.ConfirmPaymentSmeReq{
		TransactionID: transactionId.String(),
	})
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().Equal(string(consts.TransactionStatusRejected), resp.Status)
	s.Require().Equal(string(consts.PaymentReasonCodeUnableToProcess), resp.ReasonCode)
	s.Require().Equal(string(consts.PaymentReasonUnableToProcess), resp.Reason)
}

// Тест, когда таблица signatories пустая - вернуть ошибку InternalErrorError
func (s *Suite) TestConfirmPaymentSme_MassPayment_CheckDomesticMassPayment_SignatoryNotFound() {
	transactionId := uuid.New()

	// Создаем transaction в БД (со статусом INITIALIZED)
	transaction, err := s.postgresDB.Transaction.Create().
		SetID(transactionId).
		SetTransactionNumber(testTransactionNumber).
		SetTransactionDate(time.Now()).
		SetTransactionType(transaction.TransactionType(consts.TransactionTypePayment)).
		SetInitiatorID(testInitiatorID).
		SetIdempotencyKey(testIdempotencyKey).
		SetTransactionStatus(transaction.TransactionStatus(consts.TransactionStatusInitialized)).
		SetTransactionAmount(testTransactionAmount).
		SetTransactionCurrency(transaction.TransactionCurrency(consts.CurrencyKZT)).
		SetTransactionTotalAmount(testTransactionTotalAmount).
		SetCreateTime(time.Now()).
		SetUpdateTime(time.Now()).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(transaction)

	// Создаем confirmation в БД
	confirmation, err := s.postgresDB.Confirmations.Create().
		SetID(transactionId).
		SetConfirmationType(confirmations.ConfirmationType(consts.ConfirmationTypeOtp)).
		SetConfirmationDate(time.Now()).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(confirmation)

	// Создаем payment в БД с EmployeeList (массовый платеж)
	payment, err := s.postgresDB.Payments.Create().
		SetID(transactionId).
		SetPaymentCode(testPaymentCode).
		SetPaymentType(payments.PaymentType(consts.PaymentTypePensionTransfer)).
		SetPaymentPeriod(GetPreviousMonthPeriod()).
		SetEmployeeList(map[string]interface{}{
			"employee1": map[string]interface{}{
				"iin":    "123456789012",
				"amount": "50000",
			},
		}).
		SetKbk(consts.KbkIpnWithSourceOfPayment.String()).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(payment)

	// Вызываем метод confirmPaymentSme
	resp, err := s.grpc.ConfirmPaymentSme(s.ctx, &pbPaymentsSme.ConfirmPaymentSmeReq{
		TransactionID: transactionId.String(),
	})
	s.Require().Error(err)
	s.Require().Nil(resp)

	statusErr, ok := status.FromError(err)
	s.Require().True(ok)
	s.Require().Equal(codes.Internal, statusErr.Code())
}
