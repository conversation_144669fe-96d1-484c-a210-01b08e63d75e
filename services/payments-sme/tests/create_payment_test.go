package tests

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"github.com/golang/mock/gomock"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/specs/proto/dictionary"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/consts"
	pbColvirBr "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
	pbPaymentsSme "git.redmadrobot.com/zaman/backend/zaman/specs/proto/payments-sme"
	pkbBridgePb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/pkb-bridge"
	usersPb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"
)

var (
	validOPVPaymentRequest = &pbPaymentsSme.CreatePaymentReq{
		IdempotencyKey: "550e8400-e29b-41d4-a716-************",
		PaymentCode:    "PAYMENT_BUDGET_PENSION_BASIC",
		PaymentData: &pbPaymentsSme.PaymentData{
			PaymentPeriod:          "122024",
			PurposeCode:            "010",
			PurposeDetails:         "Mandatory pension contributions for December 2024",
			Amount:                 "50000.00",
			PayerAccount:           "********************",
			SignatoryA:             "Токаев Касым-Жомарт Кемельевич",
			Kbk:                    consts.KbkIpnWithSourceOfPayment.String(),
			RealBeneficiaryName:    "Первый Первый",
			RealBeneficiaryBINIIN:  "************",
			RealBeneficiaryCountry: "KZ",
			Employees: []*pbPaymentsSme.EmployeeItem{
				{
					Name:        "Aliya",
					MiddleName:  utils.ConvertStringToPtr("Serikkyzy"),
					LastName:    "Nurlanova",
					Iin:         "************",
					Birthday:    "1990-05-15",
					Country:     "KZ",
					Amount:      "25000.00",
					ValuePeriod: "122024",
				},
				{
					Name:        "Erlan",
					MiddleName:  utils.ConvertStringToPtr("Asylbekuly"),
					LastName:    "Kasymov",
					Iin:         "************",
					Birthday:    "1985-03-20",
					Country:     "KZ",
					Amount:      "25000.00",
					ValuePeriod: "122024",
				},
			},
		},
	}

	validIPNPaymentRequest = &pbPaymentsSme.CreatePaymentReq{
		IdempotencyKey: "550e8400-e29b-41d4-a716-************",
		PaymentCode:    "PAYMENT_BUDGET_TAX_INCOME_INDIVIDUAL",
		PaymentData: &pbPaymentsSme.PaymentData{
			PaymentPeriod:          "122024",
			PurposeCode:            "912",
			PurposeDetails:         "Mandatory pension contributions for December 2024",
			Amount:                 "50000.00",
			PayerAccount:           "********************",
			SignatoryA:             "Токаев Касым-Жомарт Кемельевич",
			Kbk:                    consts.KbkIpnWithSourceOfPayment.String(),
			BeneficiaryName:        "ГУ «Комитет казначейства Министерства финансов РК»",
			BeneficiaryBINIIN:      "************",
			RealBeneficiaryName:    "Первый Первый",
			RealBeneficiaryBINIIN:  "************",
			RealBeneficiaryCountry: "KZ",
			Employees: []*pbPaymentsSme.EmployeeItem{
				{
					Name:        "Aliya",
					MiddleName:  utils.ConvertStringToPtr("Serikkyzy"),
					LastName:    "Nurlanova",
					Iin:         "************",
					Birthday:    "1990-05-15",
					Country:     "KZ",
					Amount:      "25000.00",
					ValuePeriod: "122024",
				},
				{
					Name:        "Erlan",
					MiddleName:  utils.ConvertStringToPtr("Asylbekuly"),
					LastName:    "Kasymov",
					Iin:         "************",
					Birthday:    "1985-03-20",
					Country:     "KZ",
					Amount:      "25000.00",
					ValuePeriod: "122024",
				},
			},
		},
	}

	invalidKbkPaymentRequest = &pbPaymentsSme.CreatePaymentReq{
		IdempotencyKey: "550e8400-e29b-41d4-a716-************",
		PaymentCode:    "PAYMENT_BUDGET_PENSION_BASIC",
		PaymentData: &pbPaymentsSme.PaymentData{
			PaymentPeriod:         "122024",
			PurposeCode:           "010",
			PurposeDetails:        "Mandatory pension contributions for December 2024",
			Amount:                "50000.00",
			PayerAccount:          "********************",
			SignatoryA:            "Токаев Касым-Жомарт Кемельевич",
			Kbk:                   "001",
			RealBeneficiaryBINIIN: "",
			Employees: []*pbPaymentsSme.EmployeeItem{
				{
					Name:        "Aliya",
					MiddleName:  utils.ConvertStringToPtr("Serikkyzy"),
					LastName:    "Nurlanova",
					Iin:         "************",
					Birthday:    "1990-05-15",
					Country:     "KZ",
					Amount:      "25000.00",
					ValuePeriod: "122024",
				},
			},
		},
	}

	invalidPaymentPeriodRequest = &pbPaymentsSme.CreatePaymentReq{
		IdempotencyKey: "550e8400-e29b-41d4-a716-************",
		PaymentCode:    "PAYMENT_BUDGET_PENSION_BASIC",
		PaymentData: &pbPaymentsSme.PaymentData{
			PaymentPeriod:  "invalid",
			PurposeCode:    "010",
			PurposeDetails: "Test payment",
			Amount:         "50000.00",
			PayerAccount:   "********************",
			SignatoryA:     "Токаев Касым-Жомарт Кемельевич",
			Employees: []*pbPaymentsSme.EmployeeItem{
				{
					Name:        "Test",
					LastName:    "User",
					Iin:         "************",
					Birthday:    "1990-01-01",
					Country:     "KZ",
					Amount:      "50000.00",
					ValuePeriod: "122024",
				},
			},
		},
	}

	emptyEmployeesRequest = &pbPaymentsSme.CreatePaymentReq{
		IdempotencyKey: "550e8400-e29b-41d4-a716-************",
		PaymentCode:    "PAYMENT_BUDGET_PENSION_BASIC",
		PaymentData: &pbPaymentsSme.PaymentData{
			PaymentPeriod:  "122024",
			PurposeCode:    "010",
			PurposeDetails: "Test payment",
			Amount:         "50000.00",
			PayerAccount:   "********************",
			SignatoryA:     "Токаев Касым-Жомарт Кемельевич",
			Kbk:            consts.KbkIpnWithSourceOfPayment.String(),
			Employees:      []*pbPaymentsSme.EmployeeItem{},
		},
	}

	unsupportedPaymentCodeRequest = &pbPaymentsSme.CreatePaymentReq{
		IdempotencyKey: "550e8400-e29b-41d4-a716-************",
		PaymentCode:    "UNSUPPORTED_PAYMENT_CODE",
		PaymentData: &pbPaymentsSme.PaymentData{
			PaymentPeriod:  "122024",
			PurposeCode:    "010",
			PurposeDetails: "Test payment",
			Amount:         "50000.00",
			PayerAccount:   "********************",
			SignatoryA:     "Токаев Касым-Жомарт Кемельевич",
			Employees: []*pbPaymentsSme.EmployeeItem{
				{
					Name:        "Test",
					LastName:    "User",
					Iin:         "************",
					Birthday:    "1990-01-01",
					Country:     "KZ",
					Amount:      "50000.00",
					ValuePeriod: "122024",
				},
			},
		},
	}

	mockCountryDictResp = &dictionary.DocGetListByNameResp{
		List: []*dictionary.Doc{
			{
				Data: `{"code":"KZ","name":"Kazakhstan","description":"Republic of Kazakhstan"}`,
			},
			{
				Data: `{"code":"RU","name":"Russia","description":"Russian Federation"}`,
			},
			{
				Data: `{"code":"US","name":"United States","description":"United States of America"}`,
			},
		},
	}
)

// operational day variables
var (
	openStatus   = "open"
	operDate     = "24.07.2025"
	nextOperDate = "25.07.2025"
)

// TestCreatePayment_Success проверяет успешное создание платежа
func (s *Suite) TestCreatePayment_Success() {
	s.SetUserContext()
	// Устанавливаем контекст SME приложения
	s.ctx = context.WithValue(s.ctx, utils.ReqSourceKey, utils.SmeApp)

	s.mocks.GRPC.Colvirbridge.EXPECT().GetStatusOperDate(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&pbColvirBr.StatusOperDateResponse{
			Status:           &openStatus,
			Date:             &operDate,
			NextOperationDay: &nextOperDate,
		},
		nil,
	).Times(1)

	s.mocks.GRPC.Users.EXPECT().GetUserByID(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&usersPb.GetUserByIDResp{
			ID:     "550e8400-e29b-41d4-a716-************",
			Phone:  "+77771234567",
			Iin:    "************",
			Status: "ACTIVE",
			Locale: "ru",
		},
		nil,
	).
		Times(1)

	s.mocks.GRPC.Users.EXPECT().GetUserSmeIPInfo(
		gomock.Any(),
		gomock.Any(),
	).Return(&usersPb.GetUserSmeIPInfoResp{
		UserID: "550e8400-e29b-41d4-a716-************",
		Iin:    "************",
		Phone:  "+77771234567",
		Locale: "ru",
	}, nil).
		Times(1)

	s.mocks.GRPC.Pkbbridge.EXPECT().SendJurSearchByIin(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&pkbBridgePb.SendJurSearchByIinResp{
			Name: `ИП "Рога и Копыта"`,
		},
		nil,
	).Times(1)

	s.mocks.GRPC.Dictionary.EXPECT().DocGetListByName(
		gomock.Any(), gomock.Any(),
	).Return(
		mockCountryDictResp, nil,
	).Times(1)

	result, err := s.grpc.CreatePayment(s.ctx, validOPVPaymentRequest)

	s.Require().NoError(err)
	s.Require().NotNil(result)
	s.Require().NotEmpty(result.TransactionID)
	s.Require().Equal(string(consts.PaymentStatusInitialized), result.Status)
	s.Require().True(result.OtpNeeded)
	s.Require().Empty(result.ReasonCode)
	s.Require().Empty(result.Reason)

	// Проверяем, что TransactionID является валидным UUID
	_, err = uuid.Parse(result.TransactionID)
	s.Require().NoError(err)

	// Проверяем, что транзакция, платеж и подпись были созданы в базе данных
	s.verifyTransactionCreated(result.TransactionID, validOPVPaymentRequest.IdempotencyKey)
	s.verifyPaymentCreated(result.TransactionID, validOPVPaymentRequest.PaymentCode)
	s.verifySignatoryCreated(result.TransactionID, validOPVPaymentRequest.PaymentData.SignatoryA)

	transaction, err := s.postgresDB.Transaction.Get(s.ctx, uuid.MustParse(result.TransactionID))
	s.Require().NoError(err)
	s.Require().NotNil(transaction)
}

func (s *Suite) TestCreatePayment_Success_Ipn() {
	s.SetUserContext()
	// Устанавливаем контекст SME приложения
	s.ctx = context.WithValue(s.ctx, utils.ReqSourceKey, utils.SmeApp)

	s.mocks.GRPC.Colvirbridge.EXPECT().GetStatusOperDate(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&pbColvirBr.StatusOperDateResponse{
			Status:           &openStatus,
			Date:             &operDate,
			NextOperationDay: &nextOperDate,
		},
		nil,
	).Times(1)

	s.mocks.GRPC.Users.EXPECT().GetUserByID(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&usersPb.GetUserByIDResp{
			ID:     "550e8400-e29b-41d4-a716-************",
			Phone:  "+77771234567",
			Iin:    "************",
			Status: "ACTIVE",
			Locale: "ru",
		},
		nil,
	).
		Times(1)

	s.mocks.GRPC.Users.EXPECT().GetUserSmeIPInfo(
		gomock.Any(),
		gomock.Any(),
	).Return(&usersPb.GetUserSmeIPInfoResp{
		UserID: "550e8400-e29b-41d4-a716-************",
		Iin:    "************",
		Phone:  "+77771234567",
		Locale: "ru",
	}, nil).
		Times(1)

	s.mocks.GRPC.Pkbbridge.EXPECT().SendJurSearchByIin(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&pkbBridgePb.SendJurSearchByIinResp{
			Name: `ИП "Рога и Копыта"`,
		},
		nil,
	).Times(1)

	s.mocks.GRPC.Dictionary.EXPECT().DocGetListByName(
		gomock.Any(), gomock.Any(),
	).Return(
		mockCountryDictResp, nil,
	).Times(1)

	result, err := s.grpc.CreatePayment(s.ctx, validIPNPaymentRequest)

	data, _ := json.MarshalIndent(result, "", "  ")
	fmt.Println("wtf", string(data))

	s.Require().NoError(err)
	s.Require().NotNil(result)
	// s.Require().NotEmpty(result.TransactionID)
	s.Require().Equal(string(consts.PaymentStatusInitialized), result.Status)
	s.Require().True(result.OtpNeeded)
	s.Require().Empty(result.ReasonCode)
	s.Require().Empty(result.Reason)

	// Проверяем, что TransactionID является валидным UUID
	_, err = uuid.Parse(result.TransactionID)
	s.Require().NoError(err)

	// Проверяем, что транзакция, платеж и подпись были созданы в базе данных
	s.verifyTransactionCreated(result.TransactionID, validIPNPaymentRequest.IdempotencyKey)
	s.verifyPaymentCreated(result.TransactionID, validIPNPaymentRequest.PaymentCode)
	s.verifySignatoryCreated(result.TransactionID, validIPNPaymentRequest.PaymentData.SignatoryA)

	transaction, err := s.postgresDB.Transaction.Get(s.ctx, uuid.MustParse(result.TransactionID))
	s.Require().NoError(err)
	s.Require().NotNil(transaction)

	s.Require().Equal(validIPNPaymentRequest.PaymentData.RealBeneficiaryName, *transaction.RealBeneficiaryName)
	s.Require().Equal(validIPNPaymentRequest.PaymentData.RealBeneficiaryBINIIN, *transaction.RealBeneficiaryBinIin)
	s.Require().Equal(validIPNPaymentRequest.PaymentData.RealBeneficiaryCountry, *transaction.RealBeneficiaryCountryCode)
	s.Require().NotNil(transaction.RealBeneficiaryType)
	s.Require().Equal(string(consts.PayerTypeIndividual), string(*transaction.RealBeneficiaryType))
}

// TestCreatePayment_InvalidPaymentPeriod проверяет обработку ошибки при некорректном периоде платежа
func (s *Suite) TestCreatePayment_InvalidPaymentPeriod() {
	s.SetUserContext()
	// Устанавливаем контекст SME приложения
	s.ctx = context.WithValue(s.ctx, utils.ReqSourceKey, utils.SmeApp)

	s.mocks.GRPC.Colvirbridge.EXPECT().GetStatusOperDate(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&pbColvirBr.StatusOperDateResponse{
			Status:           &openStatus,
			Date:             &operDate,
			NextOperationDay: &nextOperDate,
		},
		nil,
	).Times(1)

	s.mocks.GRPC.Dictionary.EXPECT().DocGetListByName(
		gomock.Any(), gomock.Any(),
	).Return(
		mockCountryDictResp, nil,
	).Times(1)

	result, err := s.grpc.CreatePayment(s.ctx, invalidPaymentPeriodRequest)

	s.Require().NoError(err)
	s.Require().NotNil(result)
	s.Require().Empty(result.TransactionID)
	s.Require().Equal(string(consts.PaymentStatusRejected), result.Status)
	s.Require().False(result.OtpNeeded)
	s.Require().NotEmpty(result.ReasonCode)
	s.Require().NotEmpty(result.Reason)
	s.Require().Contains(result.Reason, "Validation error")
}

// TestCreatePayment_EmptyEmployees_CurrentUserHasKZCitizenship проверяет обработку ошибки при пустом списке сотрудников
func (s *Suite) TestCreatePayment_EmptyEmployees_CurrentUserHasKZCitizenship() {
	s.SetUserContext()
	// Устанавливаем контекст SME приложения
	s.ctx = context.WithValue(s.ctx, utils.ReqSourceKey, utils.SmeApp)

	s.mocks.GRPC.Colvirbridge.EXPECT().GetStatusOperDate(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&pbColvirBr.StatusOperDateResponse{
			Status:           &openStatus,
			Date:             &operDate,
			NextOperationDay: &nextOperDate,
		},
		nil,
	).Times(1)

	s.mocks.GRPC.Users.EXPECT().GetUserByID(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&usersPb.GetUserByIDResp{
			ID:     "550e8400-e29b-41d4-a716-************",
			Phone:  "+77771234567",
			Iin:    "************",
			Status: "ACTIVE",
			Locale: "ru",
		},
		nil,
	).
		Times(1)

	s.mocks.GRPC.Users.EXPECT().GetUserSmeIPInfo(
		gomock.Any(),
		gomock.Any(),
	).Return(&usersPb.GetUserSmeIPInfoResp{
		UserID: "550e8400-e29b-41d4-a716-************",
		Iin:    "************",
		Phone:  "+77771234567",
		Locale: "ru",
	}, nil).
		Times(1)

	s.mocks.GRPC.Pkbbridge.EXPECT().SendJurSearchByIin(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&pkbBridgePb.SendJurSearchByIinResp{
			Name: `ИП "Рога и Копыта"`,
		},
		nil,
	).Times(1)

	s.mocks.GRPC.Pkbbridge.EXPECT().GetPersonalInfoByIin(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&pkbBridgePb.GetPersonalInfoByIinResp{
			Name:       `Касым-Жомарт`,
			Patronymic: `Кемельевич`,
			Surname:    `Токаев`,
			Dob:        `1953-05-17`,
			Iin:        `************`,
			Citizenship: &pkbBridgePb.NameCode{
				Code:       `398`,
				NameKZ:     `Қазақстан Республикасы`,
				NameRU:     `Республика Казахстан`,
				ChangeDate: `2023-01-01`,
			},
		},
		nil,
	).Times(1)

	s.mocks.GRPC.Dictionary.EXPECT().DocGetListByName(
		gomock.Any(), gomock.Any(),
	).Return(
		mockCountryDictResp, nil,
	).Times(1)

	result, err := s.grpc.CreatePayment(s.ctx, emptyEmployeesRequest)

	s.Require().NoError(err)
	s.Require().NotNil(result)
	s.Require().Equal(string(consts.PaymentStatusInitialized), result.Status)
	s.Require().True(result.OtpNeeded)
	s.Require().Empty(result.ReasonCode)
	s.Require().Empty(result.Reason)
}

// TestCreatePayment_UserBlocked проверяет обработку ошибки при заблокированном пользователе
func (s *Suite) TestCreatePayment_UserBlocked() {
	s.SetUserContext()
	// Устанавливаем контекст SME приложения
	s.ctx = context.WithValue(s.ctx, utils.ReqSourceKey, utils.SmeApp)

	s.mocks.GRPC.Colvirbridge.EXPECT().GetStatusOperDate(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&pbColvirBr.StatusOperDateResponse{
			Status:           &openStatus,
			Date:             &operDate,
			NextOperationDay: &nextOperDate,
		},
		nil,
	).Times(1)

	s.mocks.GRPC.Users.EXPECT().GetUserByID(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&usersPb.GetUserByIDResp{
			ID:     "550e8400-e29b-41d4-a716-************",
			Phone:  "+77771234567",
			Iin:    "************",
			Status: "ACTIVE",
			Locale: "ru",
		},
		nil,
	).
		Times(1)

	s.mocks.GRPC.Users.EXPECT().GetUserSmeIPInfo(
		gomock.Any(),
		gomock.Any(),
	).Return(
		nil,
		errors.New("UserBlocked"),
	).Times(1)

	s.mocks.GRPC.Dictionary.EXPECT().DocGetListByName(
		gomock.Any(), gomock.Any(),
	).Return(
		mockCountryDictResp, nil,
	).Times(1)

	result, err := s.grpc.CreatePayment(s.ctx, emptyEmployeesRequest)

	s.Require().NoError(err)
	s.Require().NotNil(result)
	s.Require().Equal(string(consts.PaymentStatusRejected), result.Status)
	s.Require().False(result.OtpNeeded)
	s.Require().Equal(string(consts.PaymentReasonCodeActionIsForbidden), result.ReasonCode)
	s.Require().Equal(string(consts.PaymentReasonActionIsForbidden), result.Reason)
}

// TestCreatePayment_EmptyEmployees_CurrentUserHasNoKZCitizenship проверяет обработку ошибки при пустом списке сотрудников
func (s *Suite) TestCreatePayment_EmptyEmployees_CurrentUserHasNoKZCitizenship() {
	s.SetUserContext()
	// Устанавливаем контекст SME приложения
	s.ctx = context.WithValue(s.ctx, utils.ReqSourceKey, utils.SmeApp)

	s.mocks.GRPC.Colvirbridge.EXPECT().GetStatusOperDate(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&pbColvirBr.StatusOperDateResponse{
			Status:           &openStatus,
			Date:             &operDate,
			NextOperationDay: &nextOperDate,
		},
		nil,
	).Times(1)

	s.mocks.GRPC.Users.EXPECT().GetUserByID(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&usersPb.GetUserByIDResp{
			ID:     "550e8400-e29b-41d4-a716-************",
			Phone:  "+77771234567",
			Iin:    "************",
			Status: "ACTIVE",
			Locale: "ru",
		},
		nil,
	).
		Times(1)

	s.mocks.GRPC.Users.EXPECT().GetUserSmeIPInfo(
		gomock.Any(),
		gomock.Any(),
	).Return(&usersPb.GetUserSmeIPInfoResp{
		UserID: "550e8400-e29b-41d4-a716-************",
		Iin:    "************",
		Phone:  "+77771234567",
		Locale: "ru",
	}, nil).
		Times(1)

	s.mocks.GRPC.Pkbbridge.EXPECT().SendJurSearchByIin(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&pkbBridgePb.SendJurSearchByIinResp{
			Name: `ИП "Рога и Копыта"`,
		},
		nil,
	).Times(1)

	s.mocks.GRPC.Pkbbridge.EXPECT().GetPersonalInfoByIin(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&pkbBridgePb.GetPersonalInfoByIinResp{
			Name:       `Степан`,
			Patronymic: `Аполинарьевич`,
			Surname:    `Иванов`,
			Dob:        `1953-05-17`,
			Iin:        `************`,
			Citizenship: &pkbBridgePb.NameCode{
				Code:       `3166`,
				NameKZ:     `Ресей Педерациясы`,
				NameRU:     `Российская Федерация`,
				ChangeDate: `2023-01-01`,
			},
		},
		nil,
	).Times(1)

	s.mocks.GRPC.Dictionary.EXPECT().DocGetListByName(
		gomock.Any(), gomock.Any(),
	).Return(
		mockCountryDictResp, nil,
	).Times(1)

	result, err := s.grpc.CreatePayment(s.ctx, emptyEmployeesRequest)

	s.Require().NoError(err)
	s.Require().NotNil(result)
	s.Require().Equal(string(consts.PaymentStatusRejected), result.Status)
	s.Require().False(result.OtpNeeded)
}

// TestCreatePayment_UnsupportedPaymentCode проверяет обработку ошибки при неподдерживаемом коде платежа
func (s *Suite) TestCreatePayment_UnsupportedPaymentCode() {
	s.SetUserContext()
	// Устанавливаем контекст SME приложения
	s.ctx = context.WithValue(s.ctx, utils.ReqSourceKey, utils.SmeApp)

	s.mocks.GRPC.Colvirbridge.EXPECT().GetStatusOperDate(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&pbColvirBr.StatusOperDateResponse{
			Status:           &openStatus,
			Date:             &operDate,
			NextOperationDay: &nextOperDate,
		},
		nil,
	).Times(1)

	result, err := s.grpc.CreatePayment(s.ctx, unsupportedPaymentCodeRequest)

	s.Require().NoError(err)
	s.Require().NotNil(result)
	s.Require().Empty(result.TransactionID)
	s.Require().Equal(string(consts.PaymentStatusRejected), result.Status)
	s.Require().False(result.OtpNeeded)
	s.Require().Contains(result.Reason, "Validation error")
}

// TestCreatePayment_DuplicateIdempotencyKey тестирует поведение при повторном вызове с тем же idempotency key
func (s *Suite) TestCreatePayment_DuplicateIdempotencyKey() {
	s.SetUserContext()
	// Устанавливаем контекст SME приложения
	s.ctx = context.WithValue(s.ctx, utils.ReqSourceKey, utils.SmeApp)

	s.mocks.GRPC.Colvirbridge.EXPECT().GetStatusOperDate(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&pbColvirBr.StatusOperDateResponse{
			Status:           &openStatus,
			Date:             &operDate,
			NextOperationDay: &nextOperDate,
		},
		nil,
	).Times(1)

	s.mocks.GRPC.Users.EXPECT().GetUserByID(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&usersPb.GetUserByIDResp{
			ID:     "550e8400-e29b-41d4-a716-************",
			Phone:  "+77771234567",
			Iin:    "************",
			Status: "ACTIVE",
			Locale: "ru",
		},
		nil,
	).
		Times(1)

	s.mocks.GRPC.Users.EXPECT().GetUserSmeIPInfo(
		gomock.Any(),
		gomock.Any(),
	).Return(&usersPb.GetUserSmeIPInfoResp{
		UserID: "550e8400-e29b-41d4-a716-************",
		Iin:    "************",
		Phone:  "+77771234567",
		Locale: "ru",
	}, nil).
		Times(1)

	s.mocks.GRPC.Pkbbridge.EXPECT().SendJurSearchByIin(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&pkbBridgePb.SendJurSearchByIinResp{
			Name: `ИП "Рога и Копыта"`,
		},
		nil,
	).Times(1)

	s.mocks.GRPC.Dictionary.EXPECT().DocGetListByName(
		gomock.Any(), gomock.Any(),
	).Return(
		mockCountryDictResp, nil,
	).Times(1)

	// Первый вызов с валидным idempotency key
	result1, err := s.grpc.CreatePayment(s.ctx, validOPVPaymentRequest)
	s.Require().NoError(err)
	s.Require().NotNil(result1)
	s.Require().Equal(string(consts.PaymentStatusInitialized), result1.Status)

	// Повторно мокаем вызовы
	s.mocks.GRPC.Colvirbridge.EXPECT().GetStatusOperDate(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&pbColvirBr.StatusOperDateResponse{
			Status:           &openStatus,
			Date:             &operDate,
			NextOperationDay: &nextOperDate,
		},
		nil,
	).Times(1)

	s.mocks.GRPC.Users.EXPECT().GetUserByID(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&usersPb.GetUserByIDResp{
			ID:     "550e8400-e29b-41d4-a716-************",
			Phone:  "+77771234567",
			Iin:    "************",
			Status: "ACTIVE",
			Locale: "ru",
		},
		nil,
	).
		Times(1)

	s.mocks.GRPC.Users.EXPECT().GetUserSmeIPInfo(
		gomock.Any(),
		gomock.Any(),
	).Return(&usersPb.GetUserSmeIPInfoResp{
		UserID: "550e8400-e29b-41d4-a716-************",
		Iin:    "************",
		Phone:  "+77771234567",
		Locale: "ru",
	}, nil).
		Times(1)

	s.mocks.GRPC.Pkbbridge.EXPECT().SendJurSearchByIin(
		gomock.Any(),
		gomock.Any(),
	).Return(
		&pkbBridgePb.SendJurSearchByIinResp{
			Name: `ИП "Рога и Копыта"`,
		},
		nil,
	).Times(1)

	s.mocks.GRPC.Dictionary.EXPECT().DocGetListByName(
		gomock.Any(), gomock.Any(),
	).Return(
		mockCountryDictResp, nil,
	).Times(1)

	// Последующий вызов с тем же idempotency key должен вернуть REJECTED статус
	result2, _ := s.grpc.CreatePayment(s.ctx, validOPVPaymentRequest)
	s.Require().NotNil(result2)
	s.Require().Equal(string(consts.PaymentStatusRejected), result2.Status)
	s.Require().Contains(result2.Reason, "Unable to process payment")
}

// TestCreatePayment_WithMissingUserContext тестирует поведение при отсутствии контекста пользователя
func (s *Suite) TestCreatePayment_WithMissingUserContext() {
	// Устанавливаем контекст SME приложения
	s.ctx = context.WithValue(s.ctx, utils.ReqSourceKey, utils.SmeApp)

	result, _ := s.grpc.CreatePayment(context.Background(), validOPVPaymentRequest)
	s.Require().NotNil(result)
	s.Require().Equal(string(consts.PaymentStatusRejected), result.Status)
}

func (s *Suite) TestCreatePayment_WithInvalidKbkCode() {
	s.ctx = context.WithValue(s.ctx, utils.ReqSourceKey, utils.SmeApp)

	result, _ := s.grpc.CreatePayment(context.Background(), invalidKbkPaymentRequest)
	s.Require().NotNil(result)
	s.Require().Equal(string(consts.PaymentReasonCodeUnableToProcess), result.ReasonCode)
	s.Require().Equal(string(consts.PaymentStatusRejected), result.Status)
}

func (s *Suite) verifyTransactionCreated(transactionID, idempotencyKey string) {
	transaction, err := s.postgresDB.Transaction.Get(s.ctx, uuid.MustParse(transactionID))
	s.Require().NoError(err)
	s.Require().NotNil(transaction)
	s.Require().Equal(idempotencyKey, transaction.IdempotencyKey)
	s.Require().Equal(string(consts.TransactionStatusInitialized), string(transaction.TransactionStatus))
	s.Require().Equal(string(consts.TransactionTypePayment), string(transaction.TransactionType))
	s.Require().Equal(string(consts.CurrencyKZT), string(transaction.TransactionCurrency))
	s.Require().NotEmpty(transaction.TransactionNumber)
	s.Require().NotEmpty(transaction.TransactionDirection)
	s.Require().NotEmpty(transaction.TransactionType)
	s.Require().NotEmpty(transaction.TransactionCurrency)
	s.Require().NotEmpty(transaction.InitiatorID)
	s.Require().NotEmpty(transaction.TransactionDate)
	s.Require().NotEmpty(transaction.ValueDate)
	s.Require().NotEmpty(transaction.PayerName)
	s.Require().NotEmpty(transaction.PayerType)
	s.Require().NotEmpty(transaction.PayerKod)
	s.Require().NotEmpty(transaction.PayerBankBic)
	s.Require().NotEmpty(transaction.PayerBankName)
	s.Require().NotEmpty(transaction.PayerIsoCountryCode)
	s.Require().NotEmpty(transaction.BeneficiaryBinIin)
	s.Require().NotEmpty(transaction.BeneficiaryName)
	s.Require().NotEmpty(transaction.BeneficiaryKbe)
	s.Require().NotEmpty(transaction.BeneficiaryType)
	s.Require().NotEmpty(transaction.BeneficiaryAccountIban)
	s.Require().NotEmpty(transaction.BeneficiaryBankBic)
	s.Require().NotEmpty(transaction.BeneficiaryBankName)
	s.Require().NotEmpty(transaction.BeneficiaryIsoCountryCode)
}

func (s *Suite) verifyPaymentCreated(transactionID, paymentCode string) {
	payment, err := s.postgresDB.Payments.Get(s.ctx, uuid.MustParse(transactionID))
	s.Require().NoError(err)
	s.Require().NotNil(payment)
	s.Require().Equal(paymentCode, payment.PaymentCode)
	s.Require().NotEmpty(payment.PaymentPeriod)
	s.Require().NotEmpty(payment.Kbk)
}

func (s *Suite) verifySignatoryCreated(transactionID string, signatoryA string) {
	signatory, err := s.postgresDB.Signatories.Get(s.ctx, uuid.MustParse(transactionID))
	s.Require().NoError(err)
	s.Require().NotNil(signatory)
	s.Require().Equal(transactionID, signatory.ID.String())
	s.Require().Equal(signatoryA, *signatory.SignatoryA)
}
