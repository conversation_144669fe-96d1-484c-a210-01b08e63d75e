package storage

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/abstransactiondocuments"
)

var _ AbsTransactionDocuments = (*storageImpl)(nil)

// AbsTransactionDocuments - интерфейс для работы с данными документов АБС (колвир) по платежу
type AbsTransactionDocuments interface {
	CreateAbsTransactionDocuments(
		ctx context.Context,
		absTrxDoc *entity.AbsTransactionDocument,
	) error
	GetAbsTransactionDocumentByID(ctx context.Context, transactionID uuid.UUID) (*entity.AbsTransactionDocument, error)
}

// CreateAbsTransactionDocuments создает запись с данными документов АБС (колвир) по платежу
func (s *storageImpl) CreateAbsTransactionDocuments(
	ctx context.Context,
	absTrxDoc *entity.AbsTransactionDocument,
) error {
	doc := s.PostgresClient.AbsTransactionDocuments.
		Create().
		SetID(absTrxDoc.ID).
		SetReferenceID(absTrxDoc.ReferenceID)

	if absTrxDoc.DocumentType != nil {
		doc.SetDocumentType(*absTrxDoc.DocumentType)
	}

	if absTrxDoc.ReferenceDate != nil {
		doc.SetReferenceDate(*absTrxDoc.ReferenceDate)
	}

	if absTrxDoc.DocumentStatus != nil {
		doc.SetDocumentStatus(abstransactiondocuments.DocumentStatus(*absTrxDoc.DocumentStatus))
	}

	if absTrxDoc.RejectionReason != nil {
		doc.SetRejectionReason(abstransactiondocuments.RejectionReason(*absTrxDoc.RejectionReason))
	}

	err := doc.Exec(ctx)
	if err != nil {
		return err
	}

	return nil
}

// GetAbsTransactionDocumentByID получает документ АБС (колвир) по платежу по его ID
func (s *storageImpl) GetAbsTransactionDocumentByID(ctx context.Context, transactionID uuid.UUID) (*entity.AbsTransactionDocument, error) {
	doc, err := s.PostgresClient.AbsTransactionDocuments.Query().
		Where(abstransactiondocuments.IDEQ(transactionID)).
		Order(abstransactiondocuments.ByReferenceDate(sql.OrderDesc())).
		First(ctx)
	if err != nil {
		return nil, err
	}

	return entity.EntAbsTransactionDocumentToEntity(doc), nil
}
