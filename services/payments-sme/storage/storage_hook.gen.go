// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package storage

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage -i Storage -t ../../../etc/templates/hook.tmpl -o storage_hook.gen.go -l ""

import (
	"context"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/entity"
)

var _ Storage = (*StorageHook)(nil)

// StorageHook implements Storage interface wrapper
type StorageHook struct {
	Storage
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// Check implements Storage
func (_w *StorageHook) Check(ctx context.Context) (err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.Storage, "Check", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "Check", _params)

	err = _w.Storage.Check(_ctx)
	_w._postCall.Hook(_ctx, _w.Storage, "Check", []any{err})
	return err
}

// CreateAbsTransactionDocuments implements Storage
func (_w *StorageHook) CreateAbsTransactionDocuments(ctx context.Context, absTrxDoc *entity.AbsTransactionDocument) (err error) {
	_params := []any{ctx, absTrxDoc}
	defer _w._onPanic.Hook(_w.Storage, "CreateAbsTransactionDocuments", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "CreateAbsTransactionDocuments", _params)

	err = _w.Storage.CreateAbsTransactionDocuments(_ctx, absTrxDoc)
	_w._postCall.Hook(_ctx, _w.Storage, "CreateAbsTransactionDocuments", []any{err})
	return err
}

// CreateConfirmation implements Storage
func (_w *StorageHook) CreateConfirmation(ctx context.Context, confirmation *entity.Confirmation) (err error) {
	_params := []any{ctx, confirmation}
	defer _w._onPanic.Hook(_w.Storage, "CreateConfirmation", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "CreateConfirmation", _params)

	err = _w.Storage.CreateConfirmation(_ctx, confirmation)
	_w._postCall.Hook(_ctx, _w.Storage, "CreateConfirmation", []any{err})
	return err
}

// CreateEmployee implements Storage
func (_w *StorageHook) CreateEmployee(ctx context.Context, req *entity.CreateEmployeeReq) (ep1 *entity.Employee, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.Storage, "CreateEmployee", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "CreateEmployee", _params)

	ep1, err = _w.Storage.CreateEmployee(_ctx, req)
	_w._postCall.Hook(_ctx, _w.Storage, "CreateEmployee", []any{ep1, err})
	return ep1, err
}

// CreatePayment implements Storage
func (_w *StorageHook) CreatePayment(ctx context.Context, payment *entity.Payment) (pp1 *entity.Payment, err error) {
	_params := []any{ctx, payment}
	defer _w._onPanic.Hook(_w.Storage, "CreatePayment", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "CreatePayment", _params)

	pp1, err = _w.Storage.CreatePayment(_ctx, payment)
	_w._postCall.Hook(_ctx, _w.Storage, "CreatePayment", []any{pp1, err})
	return pp1, err
}

// CreateTransactionWithPayment implements Storage
func (_w *StorageHook) CreateTransactionWithPayment(ctx context.Context, transaction *entity.Transaction, payment *entity.Payment, signatory entity.Signatory) (err error) {
	_params := []any{ctx, transaction, payment, signatory}
	defer _w._onPanic.Hook(_w.Storage, "CreateTransactionWithPayment", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "CreateTransactionWithPayment", _params)

	err = _w.Storage.CreateTransactionWithPayment(_ctx, transaction, payment, signatory)
	_w._postCall.Hook(_ctx, _w.Storage, "CreateTransactionWithPayment", []any{err})
	return err
}

// DeleteEmployee implements Storage
func (_w *StorageHook) DeleteEmployee(ctx context.Context, id uuid.UUID) (err error) {
	_params := []any{ctx, id}
	defer _w._onPanic.Hook(_w.Storage, "DeleteEmployee", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "DeleteEmployee", _params)

	err = _w.Storage.DeleteEmployee(_ctx, id)
	_w._postCall.Hook(_ctx, _w.Storage, "DeleteEmployee", []any{err})
	return err
}

// GetAbsTransactionDocumentByID implements Storage
func (_w *StorageHook) GetAbsTransactionDocumentByID(ctx context.Context, transactionID uuid.UUID) (ap1 *entity.AbsTransactionDocument, err error) {
	_params := []any{ctx, transactionID}
	defer _w._onPanic.Hook(_w.Storage, "GetAbsTransactionDocumentByID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetAbsTransactionDocumentByID", _params)

	ap1, err = _w.Storage.GetAbsTransactionDocumentByID(_ctx, transactionID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetAbsTransactionDocumentByID", []any{ap1, err})
	return ap1, err
}

// GetConfirmationsByTransactionID implements Storage
func (_w *StorageHook) GetConfirmationsByTransactionID(ctx context.Context, transactionID uuid.UUID) (cpa1 []*entity.Confirmation, err error) {
	_params := []any{ctx, transactionID}
	defer _w._onPanic.Hook(_w.Storage, "GetConfirmationsByTransactionID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetConfirmationsByTransactionID", _params)

	cpa1, err = _w.Storage.GetConfirmationsByTransactionID(_ctx, transactionID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetConfirmationsByTransactionID", []any{cpa1, err})
	return cpa1, err
}

// GetEmployeeByID implements Storage
func (_w *StorageHook) GetEmployeeByID(ctx context.Context, id uuid.UUID) (ep1 *entity.Employee, err error) {
	_params := []any{ctx, id}
	defer _w._onPanic.Hook(_w.Storage, "GetEmployeeByID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetEmployeeByID", _params)

	ep1, err = _w.Storage.GetEmployeeByID(_ctx, id)
	_w._postCall.Hook(_ctx, _w.Storage, "GetEmployeeByID", []any{ep1, err})
	return ep1, err
}

// GetEmployeeList implements Storage
func (_w *StorageHook) GetEmployeeList(ctx context.Context, EmployerIinBin string) (epa1 []*entity.Employee, err error) {
	_params := []any{ctx, EmployerIinBin}
	defer _w._onPanic.Hook(_w.Storage, "GetEmployeeList", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetEmployeeList", _params)

	epa1, err = _w.Storage.GetEmployeeList(_ctx, EmployerIinBin)
	_w._postCall.Hook(_ctx, _w.Storage, "GetEmployeeList", []any{epa1, err})
	return epa1, err
}

// GetGeneratedDocumentByTypeAndTrxID implements Storage
func (_w *StorageHook) GetGeneratedDocumentByTypeAndTrxID(ctx context.Context, docType consts.GenDocType, parentTrxID uuid.UUID) (gp1 *entity.GeneratedDocument, err error) {
	_params := []any{ctx, docType, parentTrxID}
	defer _w._onPanic.Hook(_w.Storage, "GetGeneratedDocumentByTypeAndTrxID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetGeneratedDocumentByTypeAndTrxID", _params)

	gp1, err = _w.Storage.GetGeneratedDocumentByTypeAndTrxID(_ctx, docType, parentTrxID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetGeneratedDocumentByTypeAndTrxID", []any{gp1, err})
	return gp1, err
}

// GetPaymentByPaymentID implements Storage
func (_w *StorageHook) GetPaymentByPaymentID(ctx context.Context, paymentID uuid.UUID) (pp1 *entity.Payment, err error) {
	_params := []any{ctx, paymentID}
	defer _w._onPanic.Hook(_w.Storage, "GetPaymentByPaymentID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetPaymentByPaymentID", _params)

	pp1, err = _w.Storage.GetPaymentByPaymentID(_ctx, paymentID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetPaymentByPaymentID", []any{pp1, err})
	return pp1, err
}

// GetRejectionsByTransactionID implements Storage
func (_w *StorageHook) GetRejectionsByTransactionID(ctx context.Context, transactionID uuid.UUID) (rpa1 []*entity.Rejection, err error) {
	_params := []any{ctx, transactionID}
	defer _w._onPanic.Hook(_w.Storage, "GetRejectionsByTransactionID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetRejectionsByTransactionID", _params)

	rpa1, err = _w.Storage.GetRejectionsByTransactionID(_ctx, transactionID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetRejectionsByTransactionID", []any{rpa1, err})
	return rpa1, err
}

// GetSignatoriesByTransactionID implements Storage
func (_w *StorageHook) GetSignatoriesByTransactionID(ctx context.Context, transactionID uuid.UUID) (sp1 *entity.Signatory, err error) {
	_params := []any{ctx, transactionID}
	defer _w._onPanic.Hook(_w.Storage, "GetSignatoriesByTransactionID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetSignatoriesByTransactionID", _params)

	sp1, err = _w.Storage.GetSignatoriesByTransactionID(_ctx, transactionID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetSignatoriesByTransactionID", []any{sp1, err})
	return sp1, err
}

// GetTransactionByID implements Storage
func (_w *StorageHook) GetTransactionByID(ctx context.Context, id uuid.UUID) (tp1 *entity.Transaction, err error) {
	_params := []any{ctx, id}
	defer _w._onPanic.Hook(_w.Storage, "GetTransactionByID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetTransactionByID", _params)

	tp1, err = _w.Storage.GetTransactionByID(_ctx, id)
	_w._postCall.Hook(_ctx, _w.Storage, "GetTransactionByID", []any{tp1, err})
	return tp1, err
}

// GetTransactionByNumber implements Storage
func (_w *StorageHook) GetTransactionByNumber(ctx context.Context, number string) (tp1 *entity.Transaction, err error) {
	_params := []any{ctx, number}
	defer _w._onPanic.Hook(_w.Storage, "GetTransactionByNumber", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetTransactionByNumber", _params)

	tp1, err = _w.Storage.GetTransactionByNumber(_ctx, number)
	_w._postCall.Hook(_ctx, _w.Storage, "GetTransactionByNumber", []any{tp1, err})
	return tp1, err
}

// UpdateEmployee implements Storage
func (_w *StorageHook) UpdateEmployee(ctx context.Context, req *entity.UpdateEmployeeReq) (ep1 *entity.Employee, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.Storage, "UpdateEmployee", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "UpdateEmployee", _params)

	ep1, err = _w.Storage.UpdateEmployee(_ctx, req)
	_w._postCall.Hook(_ctx, _w.Storage, "UpdateEmployee", []any{ep1, err})
	return ep1, err
}

// UpdateTransactionStatus implements Storage
func (_w *StorageHook) UpdateTransactionStatus(ctx context.Context, id uuid.UUID, status consts.TransactionStatus) (err error) {
	_params := []any{ctx, id, status}
	defer _w._onPanic.Hook(_w.Storage, "UpdateTransactionStatus", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "UpdateTransactionStatus", _params)

	err = _w.Storage.UpdateTransactionStatus(_ctx, id, status)
	_w._postCall.Hook(_ctx, _w.Storage, "UpdateTransactionStatus", []any{err})
	return err
}

// UpsertGeneratedDocument implements Storage
func (_w *StorageHook) UpsertGeneratedDocument(ctx context.Context, genDoc *entity.GeneratedDocument) (err error) {
	_params := []any{ctx, genDoc}
	defer _w._onPanic.Hook(_w.Storage, "UpsertGeneratedDocument", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "UpsertGeneratedDocument", _params)

	err = _w.Storage.UpsertGeneratedDocument(_ctx, genDoc)
	_w._postCall.Hook(_ctx, _w.Storage, "UpsertGeneratedDocument", []any{err})
	return err
}

// NewStorageHook returns StorageHook
func NewStorageHook(object Storage, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *StorageHook {
	return &StorageHook{
		Storage:     object,
		_beforeCall: beforeCall,
		_postCall:   postCall,
		_onPanic:    onPanic,
	}
}
