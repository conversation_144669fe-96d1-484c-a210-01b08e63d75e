package storage

import (
	"context"

	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/payments"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/predicate"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/transaction"
)

var _ Transaction = (*storageImpl)(nil)

type Transaction interface {
	GetTransactionByID(ctx context.Context, id uuid.UUID) (*entity.Transaction, error)
	GetTransactionByNumber(ctx context.Context, number string) (*entity.Transaction, error)
	UpdateTransactionStatus(ctx context.Context, id uuid.UUID, status consts.TransactionStatus) error
	CreateTransactionWithPayment(
		ctx context.Context,
		transaction *entity.Transaction,
		payment *entity.Payment,
		signatory entity.Signatory,
	) error
}

func (s *storageImpl) GetTransactionByID(ctx context.Context, id uuid.UUID) (*entity.Transaction, error) {
	return s.getTransactionByWhere(ctx, transaction.IDEQ(id))
}

func (s *storageImpl) GetTransactionByNumber(ctx context.Context, number string) (*entity.Transaction, error) {
	return s.getTransactionByWhere(ctx, transaction.TransactionNumberEQ(number))
}

func (s *storageImpl) getTransactionByWhere(
	ctx context.Context,
	where ...predicate.Transaction,
) (*entity.Transaction, error) {
	trs, err := s.PostgresClient.Transaction.Query().Where(where...).Limit(1).All(ctx)
	if err != nil {
		return nil, err
	}

	if len(trs) != 1 {
		return nil, consts.ErrTransactionNotFound
	}

	return entity.EntTransactionToEntity(trs[0]), nil
}

func (s *storageImpl) UpdateTransactionStatus(ctx context.Context, id uuid.UUID, status consts.TransactionStatus) error {
	return s.PostgresClient.Transaction.Update().
		Where(transaction.IDEQ(id)).
		SetTransactionStatus(transaction.TransactionStatus(status)).
		Exec(ctx)
}

// CreateTransactionWithPayment создает транзакцию и платеж в рамках одной ACID транзакции
func (s *storageImpl) CreateTransactionWithPayment(
	ctx context.Context,
	transaction *entity.Transaction,
	payment *entity.Payment,
	signatory entity.Signatory,
) error {
	if transaction == nil {
		return ErrTransactionIsNil
	}
	if payment == nil {
		return ErrPaymentIsNil
	}

	// Начинаем database transaction
	tx, err := s.PostgresClient.Tx(ctx)
	if err != nil {
		return err
	}

	// Defer rollback в случае ошибки
	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	// 1. Создаем транзакцию
	if _, err := s.createTransactionInTx(ctx, tx, transaction); err != nil {
		return err
	}

	// 2. Создаем платеж (связанный с транзакцией)
	if _, err := s.createPaymentInTx(ctx, tx, payment); err != nil {
		return err
	}

	// 3. Создаем запись в signatories
	if err := s.createSignatory(ctx, tx, signatory); err != nil {
		return err
	}

	// 4. Коммитим транзакцию
	if err = tx.Commit(); err != nil {
		return err
	}

	return nil
}

// createTransactionInTx создает транзакцию в рамках database transaction
func (s *storageImpl) createTransactionInTx(ctx context.Context, tx *ent.Tx, tr *entity.Transaction) (*entity.Transaction, error) {
	createQuery := tx.Transaction.Create().
		SetID(tr.ID).
		SetTransactionNumber(tr.TransactionNumber).
		SetInitiatorID(tr.InitiatorID).
		SetIdempotencyKey(tr.IdempotencyKey).
		SetTransactionDate(tr.TransactionDate).
		SetTransactionType(transaction.TransactionType(tr.TransactionType)).
		SetTransactionStatus(transaction.TransactionStatus(tr.TransactionStatus)).
		SetTransactionAmount(tr.TransactionAmount).
		SetTransactionCurrency(transaction.TransactionCurrency(tr.TransactionCurrency)).
		SetTransactionTotalAmount(tr.TransactionTotalAmount)

	// Устанавливаем опциональные поля
	if tr.PurposeCode != nil {
		createQuery = createQuery.SetPurposeCode(*tr.PurposeCode)
	}
	if tr.PurposeDetails != nil {
		createQuery = createQuery.SetPurposeDetails(*tr.PurposeDetails)
	}
	if tr.PayerBinIin != nil {
		createQuery = createQuery.SetPayerBinIin(*tr.PayerBinIin)
	}
	if tr.PayerName != nil {
		createQuery = createQuery.SetPayerName(*tr.PayerName)
	}
	if tr.PayerAccountIban != nil {
		createQuery = createQuery.SetPayerAccountIban(*tr.PayerAccountIban)
	}
	if tr.BeneficiaryName != nil {
		createQuery = createQuery.SetBeneficiaryName(*tr.BeneficiaryName)
	}
	if tr.BeneficiaryBinIin != nil {
		createQuery = createQuery.SetBeneficiaryBinIin(*tr.BeneficiaryBinIin)
	}
	if tr.ValueDate != nil {
		createQuery = createQuery.SetValueDate(*tr.ValueDate)
	}
	if tr.PayerKod != nil {
		createQuery = createQuery.SetPayerKod(*tr.PayerKod)
	}
	if tr.PayerType != nil {
		createQuery = createQuery.SetPayerType(transaction.PayerType(tr.PayerType.String()))
	}
	if tr.PayerBankBic != nil {
		createQuery = createQuery.SetPayerBankBic(*tr.PayerBankBic)
	}
	if tr.PayerBankName != nil {
		createQuery = createQuery.SetPayerBankName(*tr.PayerBankName)
	}
	if tr.PayerIsoCountryCode != nil {
		createQuery = createQuery.SetPayerIsoCountryCode(*tr.PayerIsoCountryCode)
	}
	if tr.TransactionDirection != nil {
		createQuery = createQuery.SetTransactionDirection(transaction.TransactionDirection(tr.TransactionDirection.String()))
	}
	if tr.BeneficiaryKbe != nil {
		createQuery = createQuery.SetBeneficiaryKbe(*tr.BeneficiaryKbe)
	}
	if tr.BeneficiaryType != nil {
		createQuery = createQuery.SetBeneficiaryType(transaction.BeneficiaryType(tr.BeneficiaryType.String()))
	}
	if tr.BeneficiaryAccountIban != nil {
		createQuery = createQuery.SetBeneficiaryAccountIban(*tr.BeneficiaryAccountIban)
	}
	if tr.BeneficiaryBankBic != nil {
		createQuery = createQuery.SetBeneficiaryBankBic(*tr.BeneficiaryBankBic)
	}
	if tr.BeneficiaryBankName != nil {
		createQuery = createQuery.SetBeneficiaryBankName(*tr.BeneficiaryBankName)
	}
	if tr.BeneficiaryIsoCountryCode != nil {
		createQuery = createQuery.SetBeneficiaryIsoCountryCode(*tr.BeneficiaryIsoCountryCode)
	}
	if tr.RealBeneficiaryName != nil {
		createQuery = createQuery.SetRealBeneficiaryName(*tr.RealBeneficiaryName)
	}
	if tr.RealBeneficiaryBinIin != nil {
		createQuery = createQuery.SetRealBeneficiaryBinIin(*tr.RealBeneficiaryBinIin)
	}
	if tr.RealBeneficiaryCountryCode != nil {
		createQuery = createQuery.SetRealBeneficiaryCountryCode(*tr.RealBeneficiaryCountryCode)
	}
	if tr.RealBeneficiaryType != nil {
		createQuery = createQuery.SetRealBeneficiaryType(transaction.RealBeneficiaryType(*tr.RealBeneficiaryType))
	}

	createdTr, err := createQuery.Save(ctx)
	if err != nil {
		return nil, err
	}

	return entity.EntTransactionToEntity(createdTr), nil
}

// createPaymentInTx создает платеж в рамках database transaction
func (s *storageImpl) createPaymentInTx(ctx context.Context, tx *ent.Tx, payment *entity.Payment) (*entity.Payment, error) {
	createQuery := tx.Payments.Create().
		SetID(payment.ID).
		SetPaymentCode(payment.PaymentCode).
		SetPaymentPeriod(payment.PaymentPeriod).
		SetKbk(payment.KBK)

	if payment.PaymentType != nil {
		createQuery = createQuery.SetPaymentType(payments.PaymentType(payment.PaymentType.String()))
	}

	if payment.EmployeeList != nil {
		createQuery = createQuery.SetEmployeeList(payment.EmployeeList)
	}

	createdPayment, err := createQuery.Save(ctx)
	if err != nil {
		return nil, err
	}

	result := &entity.Payment{
		ID:            createdPayment.ID,
		PaymentCode:   createdPayment.PaymentCode,
		PaymentType:   paymentTypeToEntityPaymentType(createdPayment.PaymentType),
		PaymentPeriod: createdPayment.PaymentPeriod,
		KBK:           createdPayment.Kbk,
	}

	if createdPayment.EmployeeList != nil {
		result.EmployeeList = createdPayment.EmployeeList
	}

	return result, nil
}
