// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/confirmations"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/predicate"
)

// ConfirmationsDelete is the builder for deleting a Confirmations entity.
type ConfirmationsDelete struct {
	config
	hooks    []Hook
	mutation *ConfirmationsMutation
}

// Where appends a list predicates to the ConfirmationsDelete builder.
func (_d *ConfirmationsDelete) Where(ps ...predicate.Confirmations) *ConfirmationsDelete {
	_d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (_d *ConfirmationsDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, _d.sqlExec, _d.mutation, _d.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *ConfirmationsDelete) ExecX(ctx context.Context) int {
	n, err := _d.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (_d *ConfirmationsDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(confirmations.Table, sqlgraph.NewFieldSpec(confirmations.FieldID, field.TypeUUID))
	if ps := _d.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, _d.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	_d.mutation.done = true
	return affected, err
}

// ConfirmationsDeleteOne is the builder for deleting a single Confirmations entity.
type ConfirmationsDeleteOne struct {
	_d *ConfirmationsDelete
}

// Where appends a list predicates to the ConfirmationsDelete builder.
func (_d *ConfirmationsDeleteOne) Where(ps ...predicate.Confirmations) *ConfirmationsDeleteOne {
	_d._d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query.
func (_d *ConfirmationsDeleteOne) Exec(ctx context.Context) error {
	n, err := _d._d.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{confirmations.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *ConfirmationsDeleteOne) ExecX(ctx context.Context) {
	if err := _d.Exec(ctx); err != nil {
		panic(err)
	}
}
