// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/abstransactiondocuments"
)

// AbsTransactionDocumentsCreate is the builder for creating a AbsTransactionDocuments entity.
type AbsTransactionDocumentsCreate struct {
	config
	mutation *AbsTransactionDocumentsMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (_c *AbsTransactionDocumentsCreate) SetCreateTime(v time.Time) *AbsTransactionDocumentsCreate {
	_c.mutation.SetCreateTime(v)
	return _c
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (_c *AbsTransactionDocumentsCreate) SetNillableCreateTime(v *time.Time) *AbsTransactionDocumentsCreate {
	if v != nil {
		_c.SetCreateTime(*v)
	}
	return _c
}

// SetUpdateTime sets the "update_time" field.
func (_c *AbsTransactionDocumentsCreate) SetUpdateTime(v time.Time) *AbsTransactionDocumentsCreate {
	_c.mutation.SetUpdateTime(v)
	return _c
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (_c *AbsTransactionDocumentsCreate) SetNillableUpdateTime(v *time.Time) *AbsTransactionDocumentsCreate {
	if v != nil {
		_c.SetUpdateTime(*v)
	}
	return _c
}

// SetReferenceID sets the "reference_id" field.
func (_c *AbsTransactionDocumentsCreate) SetReferenceID(v string) *AbsTransactionDocumentsCreate {
	_c.mutation.SetReferenceID(v)
	return _c
}

// SetDocumentType sets the "document_type" field.
func (_c *AbsTransactionDocumentsCreate) SetDocumentType(v string) *AbsTransactionDocumentsCreate {
	_c.mutation.SetDocumentType(v)
	return _c
}

// SetNillableDocumentType sets the "document_type" field if the given value is not nil.
func (_c *AbsTransactionDocumentsCreate) SetNillableDocumentType(v *string) *AbsTransactionDocumentsCreate {
	if v != nil {
		_c.SetDocumentType(*v)
	}
	return _c
}

// SetReferenceDate sets the "reference_date" field.
func (_c *AbsTransactionDocumentsCreate) SetReferenceDate(v string) *AbsTransactionDocumentsCreate {
	_c.mutation.SetReferenceDate(v)
	return _c
}

// SetNillableReferenceDate sets the "reference_date" field if the given value is not nil.
func (_c *AbsTransactionDocumentsCreate) SetNillableReferenceDate(v *string) *AbsTransactionDocumentsCreate {
	if v != nil {
		_c.SetReferenceDate(*v)
	}
	return _c
}

// SetDocumentStatus sets the "document_status" field.
func (_c *AbsTransactionDocumentsCreate) SetDocumentStatus(v abstransactiondocuments.DocumentStatus) *AbsTransactionDocumentsCreate {
	_c.mutation.SetDocumentStatus(v)
	return _c
}

// SetNillableDocumentStatus sets the "document_status" field if the given value is not nil.
func (_c *AbsTransactionDocumentsCreate) SetNillableDocumentStatus(v *abstransactiondocuments.DocumentStatus) *AbsTransactionDocumentsCreate {
	if v != nil {
		_c.SetDocumentStatus(*v)
	}
	return _c
}

// SetRejectionReason sets the "rejection_reason" field.
func (_c *AbsTransactionDocumentsCreate) SetRejectionReason(v abstransactiondocuments.RejectionReason) *AbsTransactionDocumentsCreate {
	_c.mutation.SetRejectionReason(v)
	return _c
}

// SetNillableRejectionReason sets the "rejection_reason" field if the given value is not nil.
func (_c *AbsTransactionDocumentsCreate) SetNillableRejectionReason(v *abstransactiondocuments.RejectionReason) *AbsTransactionDocumentsCreate {
	if v != nil {
		_c.SetRejectionReason(*v)
	}
	return _c
}

// SetID sets the "id" field.
func (_c *AbsTransactionDocumentsCreate) SetID(v uuid.UUID) *AbsTransactionDocumentsCreate {
	_c.mutation.SetID(v)
	return _c
}

// Mutation returns the AbsTransactionDocumentsMutation object of the builder.
func (_c *AbsTransactionDocumentsCreate) Mutation() *AbsTransactionDocumentsMutation {
	return _c.mutation
}

// Save creates the AbsTransactionDocuments in the database.
func (_c *AbsTransactionDocumentsCreate) Save(ctx context.Context) (*AbsTransactionDocuments, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *AbsTransactionDocumentsCreate) SaveX(ctx context.Context) *AbsTransactionDocuments {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *AbsTransactionDocumentsCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *AbsTransactionDocumentsCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *AbsTransactionDocumentsCreate) defaults() {
	if _, ok := _c.mutation.CreateTime(); !ok {
		v := abstransactiondocuments.DefaultCreateTime()
		_c.mutation.SetCreateTime(v)
	}
	if _, ok := _c.mutation.UpdateTime(); !ok {
		v := abstransactiondocuments.DefaultUpdateTime()
		_c.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *AbsTransactionDocumentsCreate) check() error {
	if _, ok := _c.mutation.ReferenceID(); !ok {
		return &ValidationError{Name: "reference_id", err: errors.New(`ent: missing required field "AbsTransactionDocuments.reference_id"`)}
	}
	if v, ok := _c.mutation.DocumentStatus(); ok {
		if err := abstransactiondocuments.DocumentStatusValidator(v); err != nil {
			return &ValidationError{Name: "document_status", err: fmt.Errorf(`ent: validator failed for field "AbsTransactionDocuments.document_status": %w`, err)}
		}
	}
	if v, ok := _c.mutation.RejectionReason(); ok {
		if err := abstransactiondocuments.RejectionReasonValidator(v); err != nil {
			return &ValidationError{Name: "rejection_reason", err: fmt.Errorf(`ent: validator failed for field "AbsTransactionDocuments.rejection_reason": %w`, err)}
		}
	}
	return nil
}

func (_c *AbsTransactionDocumentsCreate) sqlSave(ctx context.Context) (*AbsTransactionDocuments, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *AbsTransactionDocumentsCreate) createSpec() (*AbsTransactionDocuments, *sqlgraph.CreateSpec) {
	var (
		_node = &AbsTransactionDocuments{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(abstransactiondocuments.Table, sqlgraph.NewFieldSpec(abstransactiondocuments.FieldID, field.TypeUUID))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := _c.mutation.CreateTime(); ok {
		_spec.SetField(abstransactiondocuments.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = value
	}
	if value, ok := _c.mutation.UpdateTime(); ok {
		_spec.SetField(abstransactiondocuments.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = value
	}
	if value, ok := _c.mutation.ReferenceID(); ok {
		_spec.SetField(abstransactiondocuments.FieldReferenceID, field.TypeString, value)
		_node.ReferenceID = value
	}
	if value, ok := _c.mutation.DocumentType(); ok {
		_spec.SetField(abstransactiondocuments.FieldDocumentType, field.TypeString, value)
		_node.DocumentType = &value
	}
	if value, ok := _c.mutation.ReferenceDate(); ok {
		_spec.SetField(abstransactiondocuments.FieldReferenceDate, field.TypeString, value)
		_node.ReferenceDate = &value
	}
	if value, ok := _c.mutation.DocumentStatus(); ok {
		_spec.SetField(abstransactiondocuments.FieldDocumentStatus, field.TypeEnum, value)
		_node.DocumentStatus = &value
	}
	if value, ok := _c.mutation.RejectionReason(); ok {
		_spec.SetField(abstransactiondocuments.FieldRejectionReason, field.TypeEnum, value)
		_node.RejectionReason = &value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AbsTransactionDocuments.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AbsTransactionDocumentsUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *AbsTransactionDocumentsCreate) OnConflict(opts ...sql.ConflictOption) *AbsTransactionDocumentsUpsertOne {
	_c.conflict = opts
	return &AbsTransactionDocumentsUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AbsTransactionDocuments.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *AbsTransactionDocumentsCreate) OnConflictColumns(columns ...string) *AbsTransactionDocumentsUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &AbsTransactionDocumentsUpsertOne{
		create: _c,
	}
}

type (
	// AbsTransactionDocumentsUpsertOne is the builder for "upsert"-ing
	//  one AbsTransactionDocuments node.
	AbsTransactionDocumentsUpsertOne struct {
		create *AbsTransactionDocumentsCreate
	}

	// AbsTransactionDocumentsUpsert is the "OnConflict" setter.
	AbsTransactionDocumentsUpsert struct {
		*sql.UpdateSet
	}
)

// SetReferenceID sets the "reference_id" field.
func (u *AbsTransactionDocumentsUpsert) SetReferenceID(v string) *AbsTransactionDocumentsUpsert {
	u.Set(abstransactiondocuments.FieldReferenceID, v)
	return u
}

// UpdateReferenceID sets the "reference_id" field to the value that was provided on create.
func (u *AbsTransactionDocumentsUpsert) UpdateReferenceID() *AbsTransactionDocumentsUpsert {
	u.SetExcluded(abstransactiondocuments.FieldReferenceID)
	return u
}

// SetDocumentType sets the "document_type" field.
func (u *AbsTransactionDocumentsUpsert) SetDocumentType(v string) *AbsTransactionDocumentsUpsert {
	u.Set(abstransactiondocuments.FieldDocumentType, v)
	return u
}

// UpdateDocumentType sets the "document_type" field to the value that was provided on create.
func (u *AbsTransactionDocumentsUpsert) UpdateDocumentType() *AbsTransactionDocumentsUpsert {
	u.SetExcluded(abstransactiondocuments.FieldDocumentType)
	return u
}

// ClearDocumentType clears the value of the "document_type" field.
func (u *AbsTransactionDocumentsUpsert) ClearDocumentType() *AbsTransactionDocumentsUpsert {
	u.SetNull(abstransactiondocuments.FieldDocumentType)
	return u
}

// SetReferenceDate sets the "reference_date" field.
func (u *AbsTransactionDocumentsUpsert) SetReferenceDate(v string) *AbsTransactionDocumentsUpsert {
	u.Set(abstransactiondocuments.FieldReferenceDate, v)
	return u
}

// UpdateReferenceDate sets the "reference_date" field to the value that was provided on create.
func (u *AbsTransactionDocumentsUpsert) UpdateReferenceDate() *AbsTransactionDocumentsUpsert {
	u.SetExcluded(abstransactiondocuments.FieldReferenceDate)
	return u
}

// ClearReferenceDate clears the value of the "reference_date" field.
func (u *AbsTransactionDocumentsUpsert) ClearReferenceDate() *AbsTransactionDocumentsUpsert {
	u.SetNull(abstransactiondocuments.FieldReferenceDate)
	return u
}

// SetDocumentStatus sets the "document_status" field.
func (u *AbsTransactionDocumentsUpsert) SetDocumentStatus(v abstransactiondocuments.DocumentStatus) *AbsTransactionDocumentsUpsert {
	u.Set(abstransactiondocuments.FieldDocumentStatus, v)
	return u
}

// UpdateDocumentStatus sets the "document_status" field to the value that was provided on create.
func (u *AbsTransactionDocumentsUpsert) UpdateDocumentStatus() *AbsTransactionDocumentsUpsert {
	u.SetExcluded(abstransactiondocuments.FieldDocumentStatus)
	return u
}

// ClearDocumentStatus clears the value of the "document_status" field.
func (u *AbsTransactionDocumentsUpsert) ClearDocumentStatus() *AbsTransactionDocumentsUpsert {
	u.SetNull(abstransactiondocuments.FieldDocumentStatus)
	return u
}

// SetRejectionReason sets the "rejection_reason" field.
func (u *AbsTransactionDocumentsUpsert) SetRejectionReason(v abstransactiondocuments.RejectionReason) *AbsTransactionDocumentsUpsert {
	u.Set(abstransactiondocuments.FieldRejectionReason, v)
	return u
}

// UpdateRejectionReason sets the "rejection_reason" field to the value that was provided on create.
func (u *AbsTransactionDocumentsUpsert) UpdateRejectionReason() *AbsTransactionDocumentsUpsert {
	u.SetExcluded(abstransactiondocuments.FieldRejectionReason)
	return u
}

// ClearRejectionReason clears the value of the "rejection_reason" field.
func (u *AbsTransactionDocumentsUpsert) ClearRejectionReason() *AbsTransactionDocumentsUpsert {
	u.SetNull(abstransactiondocuments.FieldRejectionReason)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.AbsTransactionDocuments.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(abstransactiondocuments.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AbsTransactionDocumentsUpsertOne) UpdateNewValues() *AbsTransactionDocumentsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(abstransactiondocuments.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(abstransactiondocuments.FieldCreateTime)
		}
		if _, exists := u.create.mutation.UpdateTime(); exists {
			s.SetIgnore(abstransactiondocuments.FieldUpdateTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AbsTransactionDocuments.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *AbsTransactionDocumentsUpsertOne) Ignore() *AbsTransactionDocumentsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AbsTransactionDocumentsUpsertOne) DoNothing() *AbsTransactionDocumentsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AbsTransactionDocumentsCreate.OnConflict
// documentation for more info.
func (u *AbsTransactionDocumentsUpsertOne) Update(set func(*AbsTransactionDocumentsUpsert)) *AbsTransactionDocumentsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AbsTransactionDocumentsUpsert{UpdateSet: update})
	}))
	return u
}

// SetReferenceID sets the "reference_id" field.
func (u *AbsTransactionDocumentsUpsertOne) SetReferenceID(v string) *AbsTransactionDocumentsUpsertOne {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.SetReferenceID(v)
	})
}

// UpdateReferenceID sets the "reference_id" field to the value that was provided on create.
func (u *AbsTransactionDocumentsUpsertOne) UpdateReferenceID() *AbsTransactionDocumentsUpsertOne {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.UpdateReferenceID()
	})
}

// SetDocumentType sets the "document_type" field.
func (u *AbsTransactionDocumentsUpsertOne) SetDocumentType(v string) *AbsTransactionDocumentsUpsertOne {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.SetDocumentType(v)
	})
}

// UpdateDocumentType sets the "document_type" field to the value that was provided on create.
func (u *AbsTransactionDocumentsUpsertOne) UpdateDocumentType() *AbsTransactionDocumentsUpsertOne {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.UpdateDocumentType()
	})
}

// ClearDocumentType clears the value of the "document_type" field.
func (u *AbsTransactionDocumentsUpsertOne) ClearDocumentType() *AbsTransactionDocumentsUpsertOne {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.ClearDocumentType()
	})
}

// SetReferenceDate sets the "reference_date" field.
func (u *AbsTransactionDocumentsUpsertOne) SetReferenceDate(v string) *AbsTransactionDocumentsUpsertOne {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.SetReferenceDate(v)
	})
}

// UpdateReferenceDate sets the "reference_date" field to the value that was provided on create.
func (u *AbsTransactionDocumentsUpsertOne) UpdateReferenceDate() *AbsTransactionDocumentsUpsertOne {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.UpdateReferenceDate()
	})
}

// ClearReferenceDate clears the value of the "reference_date" field.
func (u *AbsTransactionDocumentsUpsertOne) ClearReferenceDate() *AbsTransactionDocumentsUpsertOne {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.ClearReferenceDate()
	})
}

// SetDocumentStatus sets the "document_status" field.
func (u *AbsTransactionDocumentsUpsertOne) SetDocumentStatus(v abstransactiondocuments.DocumentStatus) *AbsTransactionDocumentsUpsertOne {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.SetDocumentStatus(v)
	})
}

// UpdateDocumentStatus sets the "document_status" field to the value that was provided on create.
func (u *AbsTransactionDocumentsUpsertOne) UpdateDocumentStatus() *AbsTransactionDocumentsUpsertOne {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.UpdateDocumentStatus()
	})
}

// ClearDocumentStatus clears the value of the "document_status" field.
func (u *AbsTransactionDocumentsUpsertOne) ClearDocumentStatus() *AbsTransactionDocumentsUpsertOne {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.ClearDocumentStatus()
	})
}

// SetRejectionReason sets the "rejection_reason" field.
func (u *AbsTransactionDocumentsUpsertOne) SetRejectionReason(v abstransactiondocuments.RejectionReason) *AbsTransactionDocumentsUpsertOne {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.SetRejectionReason(v)
	})
}

// UpdateRejectionReason sets the "rejection_reason" field to the value that was provided on create.
func (u *AbsTransactionDocumentsUpsertOne) UpdateRejectionReason() *AbsTransactionDocumentsUpsertOne {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.UpdateRejectionReason()
	})
}

// ClearRejectionReason clears the value of the "rejection_reason" field.
func (u *AbsTransactionDocumentsUpsertOne) ClearRejectionReason() *AbsTransactionDocumentsUpsertOne {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.ClearRejectionReason()
	})
}

// Exec executes the query.
func (u *AbsTransactionDocumentsUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AbsTransactionDocumentsCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AbsTransactionDocumentsUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *AbsTransactionDocumentsUpsertOne) ID(ctx context.Context) (id uuid.UUID, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: AbsTransactionDocumentsUpsertOne.ID is not supported by MySQL driver. Use AbsTransactionDocumentsUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *AbsTransactionDocumentsUpsertOne) IDX(ctx context.Context) uuid.UUID {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// AbsTransactionDocumentsCreateBulk is the builder for creating many AbsTransactionDocuments entities in bulk.
type AbsTransactionDocumentsCreateBulk struct {
	config
	err      error
	builders []*AbsTransactionDocumentsCreate
	conflict []sql.ConflictOption
}

// Save creates the AbsTransactionDocuments entities in the database.
func (_c *AbsTransactionDocumentsCreateBulk) Save(ctx context.Context) ([]*AbsTransactionDocuments, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*AbsTransactionDocuments, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*AbsTransactionDocumentsMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *AbsTransactionDocumentsCreateBulk) SaveX(ctx context.Context) []*AbsTransactionDocuments {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *AbsTransactionDocumentsCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *AbsTransactionDocumentsCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AbsTransactionDocuments.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AbsTransactionDocumentsUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *AbsTransactionDocumentsCreateBulk) OnConflict(opts ...sql.ConflictOption) *AbsTransactionDocumentsUpsertBulk {
	_c.conflict = opts
	return &AbsTransactionDocumentsUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AbsTransactionDocuments.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *AbsTransactionDocumentsCreateBulk) OnConflictColumns(columns ...string) *AbsTransactionDocumentsUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &AbsTransactionDocumentsUpsertBulk{
		create: _c,
	}
}

// AbsTransactionDocumentsUpsertBulk is the builder for "upsert"-ing
// a bulk of AbsTransactionDocuments nodes.
type AbsTransactionDocumentsUpsertBulk struct {
	create *AbsTransactionDocumentsCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.AbsTransactionDocuments.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(abstransactiondocuments.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AbsTransactionDocumentsUpsertBulk) UpdateNewValues() *AbsTransactionDocumentsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(abstransactiondocuments.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(abstransactiondocuments.FieldCreateTime)
			}
			if _, exists := b.mutation.UpdateTime(); exists {
				s.SetIgnore(abstransactiondocuments.FieldUpdateTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AbsTransactionDocuments.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *AbsTransactionDocumentsUpsertBulk) Ignore() *AbsTransactionDocumentsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AbsTransactionDocumentsUpsertBulk) DoNothing() *AbsTransactionDocumentsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AbsTransactionDocumentsCreateBulk.OnConflict
// documentation for more info.
func (u *AbsTransactionDocumentsUpsertBulk) Update(set func(*AbsTransactionDocumentsUpsert)) *AbsTransactionDocumentsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AbsTransactionDocumentsUpsert{UpdateSet: update})
	}))
	return u
}

// SetReferenceID sets the "reference_id" field.
func (u *AbsTransactionDocumentsUpsertBulk) SetReferenceID(v string) *AbsTransactionDocumentsUpsertBulk {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.SetReferenceID(v)
	})
}

// UpdateReferenceID sets the "reference_id" field to the value that was provided on create.
func (u *AbsTransactionDocumentsUpsertBulk) UpdateReferenceID() *AbsTransactionDocumentsUpsertBulk {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.UpdateReferenceID()
	})
}

// SetDocumentType sets the "document_type" field.
func (u *AbsTransactionDocumentsUpsertBulk) SetDocumentType(v string) *AbsTransactionDocumentsUpsertBulk {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.SetDocumentType(v)
	})
}

// UpdateDocumentType sets the "document_type" field to the value that was provided on create.
func (u *AbsTransactionDocumentsUpsertBulk) UpdateDocumentType() *AbsTransactionDocumentsUpsertBulk {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.UpdateDocumentType()
	})
}

// ClearDocumentType clears the value of the "document_type" field.
func (u *AbsTransactionDocumentsUpsertBulk) ClearDocumentType() *AbsTransactionDocumentsUpsertBulk {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.ClearDocumentType()
	})
}

// SetReferenceDate sets the "reference_date" field.
func (u *AbsTransactionDocumentsUpsertBulk) SetReferenceDate(v string) *AbsTransactionDocumentsUpsertBulk {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.SetReferenceDate(v)
	})
}

// UpdateReferenceDate sets the "reference_date" field to the value that was provided on create.
func (u *AbsTransactionDocumentsUpsertBulk) UpdateReferenceDate() *AbsTransactionDocumentsUpsertBulk {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.UpdateReferenceDate()
	})
}

// ClearReferenceDate clears the value of the "reference_date" field.
func (u *AbsTransactionDocumentsUpsertBulk) ClearReferenceDate() *AbsTransactionDocumentsUpsertBulk {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.ClearReferenceDate()
	})
}

// SetDocumentStatus sets the "document_status" field.
func (u *AbsTransactionDocumentsUpsertBulk) SetDocumentStatus(v abstransactiondocuments.DocumentStatus) *AbsTransactionDocumentsUpsertBulk {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.SetDocumentStatus(v)
	})
}

// UpdateDocumentStatus sets the "document_status" field to the value that was provided on create.
func (u *AbsTransactionDocumentsUpsertBulk) UpdateDocumentStatus() *AbsTransactionDocumentsUpsertBulk {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.UpdateDocumentStatus()
	})
}

// ClearDocumentStatus clears the value of the "document_status" field.
func (u *AbsTransactionDocumentsUpsertBulk) ClearDocumentStatus() *AbsTransactionDocumentsUpsertBulk {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.ClearDocumentStatus()
	})
}

// SetRejectionReason sets the "rejection_reason" field.
func (u *AbsTransactionDocumentsUpsertBulk) SetRejectionReason(v abstransactiondocuments.RejectionReason) *AbsTransactionDocumentsUpsertBulk {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.SetRejectionReason(v)
	})
}

// UpdateRejectionReason sets the "rejection_reason" field to the value that was provided on create.
func (u *AbsTransactionDocumentsUpsertBulk) UpdateRejectionReason() *AbsTransactionDocumentsUpsertBulk {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.UpdateRejectionReason()
	})
}

// ClearRejectionReason clears the value of the "rejection_reason" field.
func (u *AbsTransactionDocumentsUpsertBulk) ClearRejectionReason() *AbsTransactionDocumentsUpsertBulk {
	return u.Update(func(s *AbsTransactionDocumentsUpsert) {
		s.ClearRejectionReason()
	})
}

// Exec executes the query.
func (u *AbsTransactionDocumentsUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the AbsTransactionDocumentsCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AbsTransactionDocumentsCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AbsTransactionDocumentsUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
