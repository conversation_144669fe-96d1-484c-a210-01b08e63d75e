// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/generateddocument"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/predicate"
)

// GeneratedDocumentDelete is the builder for deleting a GeneratedDocument entity.
type GeneratedDocumentDelete struct {
	config
	hooks    []Hook
	mutation *GeneratedDocumentMutation
}

// Where appends a list predicates to the GeneratedDocumentDelete builder.
func (_d *GeneratedDocumentDelete) Where(ps ...predicate.GeneratedDocument) *GeneratedDocumentDelete {
	_d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (_d *GeneratedDocumentDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, _d.sqlExec, _d.mutation, _d.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *GeneratedDocumentDelete) ExecX(ctx context.Context) int {
	n, err := _d.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (_d *GeneratedDocumentDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(generateddocument.Table, sqlgraph.NewFieldSpec(generateddocument.FieldID, field.TypeUUID))
	if ps := _d.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, _d.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	_d.mutation.done = true
	return affected, err
}

// GeneratedDocumentDeleteOne is the builder for deleting a single GeneratedDocument entity.
type GeneratedDocumentDeleteOne struct {
	_d *GeneratedDocumentDelete
}

// Where appends a list predicates to the GeneratedDocumentDelete builder.
func (_d *GeneratedDocumentDeleteOne) Where(ps ...predicate.GeneratedDocument) *GeneratedDocumentDeleteOne {
	_d._d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query.
func (_d *GeneratedDocumentDeleteOne) Exec(ctx context.Context) error {
	n, err := _d._d.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{generateddocument.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *GeneratedDocumentDeleteOne) ExecX(ctx context.Context) {
	if err := _d.Exec(ctx); err != nil {
		panic(err)
	}
}
