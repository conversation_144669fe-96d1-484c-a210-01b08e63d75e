// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/migrate"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/abstransactiondocuments"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/confirmations"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/employees"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/generateddocument"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/health"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/payments"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/rejections"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/signatories"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/transaction"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// AbsTransactionDocuments is the client for interacting with the AbsTransactionDocuments builders.
	AbsTransactionDocuments *AbsTransactionDocumentsClient
	// Confirmations is the client for interacting with the Confirmations builders.
	Confirmations *ConfirmationsClient
	// Employees is the client for interacting with the Employees builders.
	Employees *EmployeesClient
	// GeneratedDocument is the client for interacting with the GeneratedDocument builders.
	GeneratedDocument *GeneratedDocumentClient
	// Health is the client for interacting with the Health builders.
	Health *HealthClient
	// Payments is the client for interacting with the Payments builders.
	Payments *PaymentsClient
	// Rejections is the client for interacting with the Rejections builders.
	Rejections *RejectionsClient
	// Signatories is the client for interacting with the Signatories builders.
	Signatories *SignatoriesClient
	// Transaction is the client for interacting with the Transaction builders.
	Transaction *TransactionClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.AbsTransactionDocuments = NewAbsTransactionDocumentsClient(c.config)
	c.Confirmations = NewConfirmationsClient(c.config)
	c.Employees = NewEmployeesClient(c.config)
	c.GeneratedDocument = NewGeneratedDocumentClient(c.config)
	c.Health = NewHealthClient(c.config)
	c.Payments = NewPaymentsClient(c.config)
	c.Rejections = NewRejectionsClient(c.config)
	c.Signatories = NewSignatoriesClient(c.config)
	c.Transaction = NewTransactionClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:                     ctx,
		config:                  cfg,
		AbsTransactionDocuments: NewAbsTransactionDocumentsClient(cfg),
		Confirmations:           NewConfirmationsClient(cfg),
		Employees:               NewEmployeesClient(cfg),
		GeneratedDocument:       NewGeneratedDocumentClient(cfg),
		Health:                  NewHealthClient(cfg),
		Payments:                NewPaymentsClient(cfg),
		Rejections:              NewRejectionsClient(cfg),
		Signatories:             NewSignatoriesClient(cfg),
		Transaction:             NewTransactionClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:                     ctx,
		config:                  cfg,
		AbsTransactionDocuments: NewAbsTransactionDocumentsClient(cfg),
		Confirmations:           NewConfirmationsClient(cfg),
		Employees:               NewEmployeesClient(cfg),
		GeneratedDocument:       NewGeneratedDocumentClient(cfg),
		Health:                  NewHealthClient(cfg),
		Payments:                NewPaymentsClient(cfg),
		Rejections:              NewRejectionsClient(cfg),
		Signatories:             NewSignatoriesClient(cfg),
		Transaction:             NewTransactionClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		AbsTransactionDocuments.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	for _, n := range []interface{ Use(...Hook) }{
		c.AbsTransactionDocuments, c.Confirmations, c.Employees, c.GeneratedDocument,
		c.Health, c.Payments, c.Rejections, c.Signatories, c.Transaction,
	} {
		n.Use(hooks...)
	}
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	for _, n := range []interface{ Intercept(...Interceptor) }{
		c.AbsTransactionDocuments, c.Confirmations, c.Employees, c.GeneratedDocument,
		c.Health, c.Payments, c.Rejections, c.Signatories, c.Transaction,
	} {
		n.Intercept(interceptors...)
	}
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *AbsTransactionDocumentsMutation:
		return c.AbsTransactionDocuments.mutate(ctx, m)
	case *ConfirmationsMutation:
		return c.Confirmations.mutate(ctx, m)
	case *EmployeesMutation:
		return c.Employees.mutate(ctx, m)
	case *GeneratedDocumentMutation:
		return c.GeneratedDocument.mutate(ctx, m)
	case *HealthMutation:
		return c.Health.mutate(ctx, m)
	case *PaymentsMutation:
		return c.Payments.mutate(ctx, m)
	case *RejectionsMutation:
		return c.Rejections.mutate(ctx, m)
	case *SignatoriesMutation:
		return c.Signatories.mutate(ctx, m)
	case *TransactionMutation:
		return c.Transaction.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// AbsTransactionDocumentsClient is a client for the AbsTransactionDocuments schema.
type AbsTransactionDocumentsClient struct {
	config
}

// NewAbsTransactionDocumentsClient returns a client for the AbsTransactionDocuments from the given config.
func NewAbsTransactionDocumentsClient(c config) *AbsTransactionDocumentsClient {
	return &AbsTransactionDocumentsClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `abstransactiondocuments.Hooks(f(g(h())))`.
func (c *AbsTransactionDocumentsClient) Use(hooks ...Hook) {
	c.hooks.AbsTransactionDocuments = append(c.hooks.AbsTransactionDocuments, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `abstransactiondocuments.Intercept(f(g(h())))`.
func (c *AbsTransactionDocumentsClient) Intercept(interceptors ...Interceptor) {
	c.inters.AbsTransactionDocuments = append(c.inters.AbsTransactionDocuments, interceptors...)
}

// Create returns a builder for creating a AbsTransactionDocuments entity.
func (c *AbsTransactionDocumentsClient) Create() *AbsTransactionDocumentsCreate {
	mutation := newAbsTransactionDocumentsMutation(c.config, OpCreate)
	return &AbsTransactionDocumentsCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of AbsTransactionDocuments entities.
func (c *AbsTransactionDocumentsClient) CreateBulk(builders ...*AbsTransactionDocumentsCreate) *AbsTransactionDocumentsCreateBulk {
	return &AbsTransactionDocumentsCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AbsTransactionDocumentsClient) MapCreateBulk(slice any, setFunc func(*AbsTransactionDocumentsCreate, int)) *AbsTransactionDocumentsCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AbsTransactionDocumentsCreateBulk{err: fmt.Errorf("calling to AbsTransactionDocumentsClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AbsTransactionDocumentsCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AbsTransactionDocumentsCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for AbsTransactionDocuments.
func (c *AbsTransactionDocumentsClient) Update() *AbsTransactionDocumentsUpdate {
	mutation := newAbsTransactionDocumentsMutation(c.config, OpUpdate)
	return &AbsTransactionDocumentsUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AbsTransactionDocumentsClient) UpdateOne(_m *AbsTransactionDocuments) *AbsTransactionDocumentsUpdateOne {
	mutation := newAbsTransactionDocumentsMutation(c.config, OpUpdateOne, withAbsTransactionDocuments(_m))
	return &AbsTransactionDocumentsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AbsTransactionDocumentsClient) UpdateOneID(id uuid.UUID) *AbsTransactionDocumentsUpdateOne {
	mutation := newAbsTransactionDocumentsMutation(c.config, OpUpdateOne, withAbsTransactionDocumentsID(id))
	return &AbsTransactionDocumentsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for AbsTransactionDocuments.
func (c *AbsTransactionDocumentsClient) Delete() *AbsTransactionDocumentsDelete {
	mutation := newAbsTransactionDocumentsMutation(c.config, OpDelete)
	return &AbsTransactionDocumentsDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AbsTransactionDocumentsClient) DeleteOne(_m *AbsTransactionDocuments) *AbsTransactionDocumentsDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AbsTransactionDocumentsClient) DeleteOneID(id uuid.UUID) *AbsTransactionDocumentsDeleteOne {
	builder := c.Delete().Where(abstransactiondocuments.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AbsTransactionDocumentsDeleteOne{builder}
}

// Query returns a query builder for AbsTransactionDocuments.
func (c *AbsTransactionDocumentsClient) Query() *AbsTransactionDocumentsQuery {
	return &AbsTransactionDocumentsQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAbsTransactionDocuments},
		inters: c.Interceptors(),
	}
}

// Get returns a AbsTransactionDocuments entity by its id.
func (c *AbsTransactionDocumentsClient) Get(ctx context.Context, id uuid.UUID) (*AbsTransactionDocuments, error) {
	return c.Query().Where(abstransactiondocuments.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AbsTransactionDocumentsClient) GetX(ctx context.Context, id uuid.UUID) *AbsTransactionDocuments {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *AbsTransactionDocumentsClient) Hooks() []Hook {
	return c.hooks.AbsTransactionDocuments
}

// Interceptors returns the client interceptors.
func (c *AbsTransactionDocumentsClient) Interceptors() []Interceptor {
	return c.inters.AbsTransactionDocuments
}

func (c *AbsTransactionDocumentsClient) mutate(ctx context.Context, m *AbsTransactionDocumentsMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AbsTransactionDocumentsCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AbsTransactionDocumentsUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AbsTransactionDocumentsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AbsTransactionDocumentsDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown AbsTransactionDocuments mutation op: %q", m.Op())
	}
}

// ConfirmationsClient is a client for the Confirmations schema.
type ConfirmationsClient struct {
	config
}

// NewConfirmationsClient returns a client for the Confirmations from the given config.
func NewConfirmationsClient(c config) *ConfirmationsClient {
	return &ConfirmationsClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `confirmations.Hooks(f(g(h())))`.
func (c *ConfirmationsClient) Use(hooks ...Hook) {
	c.hooks.Confirmations = append(c.hooks.Confirmations, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `confirmations.Intercept(f(g(h())))`.
func (c *ConfirmationsClient) Intercept(interceptors ...Interceptor) {
	c.inters.Confirmations = append(c.inters.Confirmations, interceptors...)
}

// Create returns a builder for creating a Confirmations entity.
func (c *ConfirmationsClient) Create() *ConfirmationsCreate {
	mutation := newConfirmationsMutation(c.config, OpCreate)
	return &ConfirmationsCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Confirmations entities.
func (c *ConfirmationsClient) CreateBulk(builders ...*ConfirmationsCreate) *ConfirmationsCreateBulk {
	return &ConfirmationsCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ConfirmationsClient) MapCreateBulk(slice any, setFunc func(*ConfirmationsCreate, int)) *ConfirmationsCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ConfirmationsCreateBulk{err: fmt.Errorf("calling to ConfirmationsClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ConfirmationsCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ConfirmationsCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Confirmations.
func (c *ConfirmationsClient) Update() *ConfirmationsUpdate {
	mutation := newConfirmationsMutation(c.config, OpUpdate)
	return &ConfirmationsUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ConfirmationsClient) UpdateOne(_m *Confirmations) *ConfirmationsUpdateOne {
	mutation := newConfirmationsMutation(c.config, OpUpdateOne, withConfirmations(_m))
	return &ConfirmationsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ConfirmationsClient) UpdateOneID(id uuid.UUID) *ConfirmationsUpdateOne {
	mutation := newConfirmationsMutation(c.config, OpUpdateOne, withConfirmationsID(id))
	return &ConfirmationsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Confirmations.
func (c *ConfirmationsClient) Delete() *ConfirmationsDelete {
	mutation := newConfirmationsMutation(c.config, OpDelete)
	return &ConfirmationsDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ConfirmationsClient) DeleteOne(_m *Confirmations) *ConfirmationsDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ConfirmationsClient) DeleteOneID(id uuid.UUID) *ConfirmationsDeleteOne {
	builder := c.Delete().Where(confirmations.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ConfirmationsDeleteOne{builder}
}

// Query returns a query builder for Confirmations.
func (c *ConfirmationsClient) Query() *ConfirmationsQuery {
	return &ConfirmationsQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeConfirmations},
		inters: c.Interceptors(),
	}
}

// Get returns a Confirmations entity by its id.
func (c *ConfirmationsClient) Get(ctx context.Context, id uuid.UUID) (*Confirmations, error) {
	return c.Query().Where(confirmations.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ConfirmationsClient) GetX(ctx context.Context, id uuid.UUID) *Confirmations {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *ConfirmationsClient) Hooks() []Hook {
	return c.hooks.Confirmations
}

// Interceptors returns the client interceptors.
func (c *ConfirmationsClient) Interceptors() []Interceptor {
	return c.inters.Confirmations
}

func (c *ConfirmationsClient) mutate(ctx context.Context, m *ConfirmationsMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ConfirmationsCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ConfirmationsUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ConfirmationsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ConfirmationsDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Confirmations mutation op: %q", m.Op())
	}
}

// EmployeesClient is a client for the Employees schema.
type EmployeesClient struct {
	config
}

// NewEmployeesClient returns a client for the Employees from the given config.
func NewEmployeesClient(c config) *EmployeesClient {
	return &EmployeesClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `employees.Hooks(f(g(h())))`.
func (c *EmployeesClient) Use(hooks ...Hook) {
	c.hooks.Employees = append(c.hooks.Employees, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `employees.Intercept(f(g(h())))`.
func (c *EmployeesClient) Intercept(interceptors ...Interceptor) {
	c.inters.Employees = append(c.inters.Employees, interceptors...)
}

// Create returns a builder for creating a Employees entity.
func (c *EmployeesClient) Create() *EmployeesCreate {
	mutation := newEmployeesMutation(c.config, OpCreate)
	return &EmployeesCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Employees entities.
func (c *EmployeesClient) CreateBulk(builders ...*EmployeesCreate) *EmployeesCreateBulk {
	return &EmployeesCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *EmployeesClient) MapCreateBulk(slice any, setFunc func(*EmployeesCreate, int)) *EmployeesCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &EmployeesCreateBulk{err: fmt.Errorf("calling to EmployeesClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*EmployeesCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &EmployeesCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Employees.
func (c *EmployeesClient) Update() *EmployeesUpdate {
	mutation := newEmployeesMutation(c.config, OpUpdate)
	return &EmployeesUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *EmployeesClient) UpdateOne(_m *Employees) *EmployeesUpdateOne {
	mutation := newEmployeesMutation(c.config, OpUpdateOne, withEmployees(_m))
	return &EmployeesUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *EmployeesClient) UpdateOneID(id uuid.UUID) *EmployeesUpdateOne {
	mutation := newEmployeesMutation(c.config, OpUpdateOne, withEmployeesID(id))
	return &EmployeesUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Employees.
func (c *EmployeesClient) Delete() *EmployeesDelete {
	mutation := newEmployeesMutation(c.config, OpDelete)
	return &EmployeesDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *EmployeesClient) DeleteOne(_m *Employees) *EmployeesDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *EmployeesClient) DeleteOneID(id uuid.UUID) *EmployeesDeleteOne {
	builder := c.Delete().Where(employees.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &EmployeesDeleteOne{builder}
}

// Query returns a query builder for Employees.
func (c *EmployeesClient) Query() *EmployeesQuery {
	return &EmployeesQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeEmployees},
		inters: c.Interceptors(),
	}
}

// Get returns a Employees entity by its id.
func (c *EmployeesClient) Get(ctx context.Context, id uuid.UUID) (*Employees, error) {
	return c.Query().Where(employees.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *EmployeesClient) GetX(ctx context.Context, id uuid.UUID) *Employees {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *EmployeesClient) Hooks() []Hook {
	return c.hooks.Employees
}

// Interceptors returns the client interceptors.
func (c *EmployeesClient) Interceptors() []Interceptor {
	return c.inters.Employees
}

func (c *EmployeesClient) mutate(ctx context.Context, m *EmployeesMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&EmployeesCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&EmployeesUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&EmployeesUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&EmployeesDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Employees mutation op: %q", m.Op())
	}
}

// GeneratedDocumentClient is a client for the GeneratedDocument schema.
type GeneratedDocumentClient struct {
	config
}

// NewGeneratedDocumentClient returns a client for the GeneratedDocument from the given config.
func NewGeneratedDocumentClient(c config) *GeneratedDocumentClient {
	return &GeneratedDocumentClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `generateddocument.Hooks(f(g(h())))`.
func (c *GeneratedDocumentClient) Use(hooks ...Hook) {
	c.hooks.GeneratedDocument = append(c.hooks.GeneratedDocument, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `generateddocument.Intercept(f(g(h())))`.
func (c *GeneratedDocumentClient) Intercept(interceptors ...Interceptor) {
	c.inters.GeneratedDocument = append(c.inters.GeneratedDocument, interceptors...)
}

// Create returns a builder for creating a GeneratedDocument entity.
func (c *GeneratedDocumentClient) Create() *GeneratedDocumentCreate {
	mutation := newGeneratedDocumentMutation(c.config, OpCreate)
	return &GeneratedDocumentCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of GeneratedDocument entities.
func (c *GeneratedDocumentClient) CreateBulk(builders ...*GeneratedDocumentCreate) *GeneratedDocumentCreateBulk {
	return &GeneratedDocumentCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *GeneratedDocumentClient) MapCreateBulk(slice any, setFunc func(*GeneratedDocumentCreate, int)) *GeneratedDocumentCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &GeneratedDocumentCreateBulk{err: fmt.Errorf("calling to GeneratedDocumentClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*GeneratedDocumentCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &GeneratedDocumentCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for GeneratedDocument.
func (c *GeneratedDocumentClient) Update() *GeneratedDocumentUpdate {
	mutation := newGeneratedDocumentMutation(c.config, OpUpdate)
	return &GeneratedDocumentUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *GeneratedDocumentClient) UpdateOne(_m *GeneratedDocument) *GeneratedDocumentUpdateOne {
	mutation := newGeneratedDocumentMutation(c.config, OpUpdateOne, withGeneratedDocument(_m))
	return &GeneratedDocumentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *GeneratedDocumentClient) UpdateOneID(id uuid.UUID) *GeneratedDocumentUpdateOne {
	mutation := newGeneratedDocumentMutation(c.config, OpUpdateOne, withGeneratedDocumentID(id))
	return &GeneratedDocumentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for GeneratedDocument.
func (c *GeneratedDocumentClient) Delete() *GeneratedDocumentDelete {
	mutation := newGeneratedDocumentMutation(c.config, OpDelete)
	return &GeneratedDocumentDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *GeneratedDocumentClient) DeleteOne(_m *GeneratedDocument) *GeneratedDocumentDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *GeneratedDocumentClient) DeleteOneID(id uuid.UUID) *GeneratedDocumentDeleteOne {
	builder := c.Delete().Where(generateddocument.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &GeneratedDocumentDeleteOne{builder}
}

// Query returns a query builder for GeneratedDocument.
func (c *GeneratedDocumentClient) Query() *GeneratedDocumentQuery {
	return &GeneratedDocumentQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeGeneratedDocument},
		inters: c.Interceptors(),
	}
}

// Get returns a GeneratedDocument entity by its id.
func (c *GeneratedDocumentClient) Get(ctx context.Context, id uuid.UUID) (*GeneratedDocument, error) {
	return c.Query().Where(generateddocument.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *GeneratedDocumentClient) GetX(ctx context.Context, id uuid.UUID) *GeneratedDocument {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *GeneratedDocumentClient) Hooks() []Hook {
	return c.hooks.GeneratedDocument
}

// Interceptors returns the client interceptors.
func (c *GeneratedDocumentClient) Interceptors() []Interceptor {
	return c.inters.GeneratedDocument
}

func (c *GeneratedDocumentClient) mutate(ctx context.Context, m *GeneratedDocumentMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&GeneratedDocumentCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&GeneratedDocumentUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&GeneratedDocumentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&GeneratedDocumentDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown GeneratedDocument mutation op: %q", m.Op())
	}
}

// HealthClient is a client for the Health schema.
type HealthClient struct {
	config
}

// NewHealthClient returns a client for the Health from the given config.
func NewHealthClient(c config) *HealthClient {
	return &HealthClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `health.Hooks(f(g(h())))`.
func (c *HealthClient) Use(hooks ...Hook) {
	c.hooks.Health = append(c.hooks.Health, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `health.Intercept(f(g(h())))`.
func (c *HealthClient) Intercept(interceptors ...Interceptor) {
	c.inters.Health = append(c.inters.Health, interceptors...)
}

// Create returns a builder for creating a Health entity.
func (c *HealthClient) Create() *HealthCreate {
	mutation := newHealthMutation(c.config, OpCreate)
	return &HealthCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Health entities.
func (c *HealthClient) CreateBulk(builders ...*HealthCreate) *HealthCreateBulk {
	return &HealthCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *HealthClient) MapCreateBulk(slice any, setFunc func(*HealthCreate, int)) *HealthCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &HealthCreateBulk{err: fmt.Errorf("calling to HealthClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*HealthCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &HealthCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Health.
func (c *HealthClient) Update() *HealthUpdate {
	mutation := newHealthMutation(c.config, OpUpdate)
	return &HealthUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *HealthClient) UpdateOne(_m *Health) *HealthUpdateOne {
	mutation := newHealthMutation(c.config, OpUpdateOne, withHealth(_m))
	return &HealthUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *HealthClient) UpdateOneID(id uuid.UUID) *HealthUpdateOne {
	mutation := newHealthMutation(c.config, OpUpdateOne, withHealthID(id))
	return &HealthUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Health.
func (c *HealthClient) Delete() *HealthDelete {
	mutation := newHealthMutation(c.config, OpDelete)
	return &HealthDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *HealthClient) DeleteOne(_m *Health) *HealthDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *HealthClient) DeleteOneID(id uuid.UUID) *HealthDeleteOne {
	builder := c.Delete().Where(health.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &HealthDeleteOne{builder}
}

// Query returns a query builder for Health.
func (c *HealthClient) Query() *HealthQuery {
	return &HealthQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeHealth},
		inters: c.Interceptors(),
	}
}

// Get returns a Health entity by its id.
func (c *HealthClient) Get(ctx context.Context, id uuid.UUID) (*Health, error) {
	return c.Query().Where(health.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *HealthClient) GetX(ctx context.Context, id uuid.UUID) *Health {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *HealthClient) Hooks() []Hook {
	return c.hooks.Health
}

// Interceptors returns the client interceptors.
func (c *HealthClient) Interceptors() []Interceptor {
	return c.inters.Health
}

func (c *HealthClient) mutate(ctx context.Context, m *HealthMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&HealthCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&HealthUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&HealthUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&HealthDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Health mutation op: %q", m.Op())
	}
}

// PaymentsClient is a client for the Payments schema.
type PaymentsClient struct {
	config
}

// NewPaymentsClient returns a client for the Payments from the given config.
func NewPaymentsClient(c config) *PaymentsClient {
	return &PaymentsClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `payments.Hooks(f(g(h())))`.
func (c *PaymentsClient) Use(hooks ...Hook) {
	c.hooks.Payments = append(c.hooks.Payments, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `payments.Intercept(f(g(h())))`.
func (c *PaymentsClient) Intercept(interceptors ...Interceptor) {
	c.inters.Payments = append(c.inters.Payments, interceptors...)
}

// Create returns a builder for creating a Payments entity.
func (c *PaymentsClient) Create() *PaymentsCreate {
	mutation := newPaymentsMutation(c.config, OpCreate)
	return &PaymentsCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Payments entities.
func (c *PaymentsClient) CreateBulk(builders ...*PaymentsCreate) *PaymentsCreateBulk {
	return &PaymentsCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *PaymentsClient) MapCreateBulk(slice any, setFunc func(*PaymentsCreate, int)) *PaymentsCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &PaymentsCreateBulk{err: fmt.Errorf("calling to PaymentsClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*PaymentsCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &PaymentsCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Payments.
func (c *PaymentsClient) Update() *PaymentsUpdate {
	mutation := newPaymentsMutation(c.config, OpUpdate)
	return &PaymentsUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *PaymentsClient) UpdateOne(_m *Payments) *PaymentsUpdateOne {
	mutation := newPaymentsMutation(c.config, OpUpdateOne, withPayments(_m))
	return &PaymentsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *PaymentsClient) UpdateOneID(id uuid.UUID) *PaymentsUpdateOne {
	mutation := newPaymentsMutation(c.config, OpUpdateOne, withPaymentsID(id))
	return &PaymentsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Payments.
func (c *PaymentsClient) Delete() *PaymentsDelete {
	mutation := newPaymentsMutation(c.config, OpDelete)
	return &PaymentsDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *PaymentsClient) DeleteOne(_m *Payments) *PaymentsDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *PaymentsClient) DeleteOneID(id uuid.UUID) *PaymentsDeleteOne {
	builder := c.Delete().Where(payments.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &PaymentsDeleteOne{builder}
}

// Query returns a query builder for Payments.
func (c *PaymentsClient) Query() *PaymentsQuery {
	return &PaymentsQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePayments},
		inters: c.Interceptors(),
	}
}

// Get returns a Payments entity by its id.
func (c *PaymentsClient) Get(ctx context.Context, id uuid.UUID) (*Payments, error) {
	return c.Query().Where(payments.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *PaymentsClient) GetX(ctx context.Context, id uuid.UUID) *Payments {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *PaymentsClient) Hooks() []Hook {
	return c.hooks.Payments
}

// Interceptors returns the client interceptors.
func (c *PaymentsClient) Interceptors() []Interceptor {
	return c.inters.Payments
}

func (c *PaymentsClient) mutate(ctx context.Context, m *PaymentsMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&PaymentsCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&PaymentsUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&PaymentsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&PaymentsDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Payments mutation op: %q", m.Op())
	}
}

// RejectionsClient is a client for the Rejections schema.
type RejectionsClient struct {
	config
}

// NewRejectionsClient returns a client for the Rejections from the given config.
func NewRejectionsClient(c config) *RejectionsClient {
	return &RejectionsClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `rejections.Hooks(f(g(h())))`.
func (c *RejectionsClient) Use(hooks ...Hook) {
	c.hooks.Rejections = append(c.hooks.Rejections, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `rejections.Intercept(f(g(h())))`.
func (c *RejectionsClient) Intercept(interceptors ...Interceptor) {
	c.inters.Rejections = append(c.inters.Rejections, interceptors...)
}

// Create returns a builder for creating a Rejections entity.
func (c *RejectionsClient) Create() *RejectionsCreate {
	mutation := newRejectionsMutation(c.config, OpCreate)
	return &RejectionsCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Rejections entities.
func (c *RejectionsClient) CreateBulk(builders ...*RejectionsCreate) *RejectionsCreateBulk {
	return &RejectionsCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *RejectionsClient) MapCreateBulk(slice any, setFunc func(*RejectionsCreate, int)) *RejectionsCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &RejectionsCreateBulk{err: fmt.Errorf("calling to RejectionsClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*RejectionsCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &RejectionsCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Rejections.
func (c *RejectionsClient) Update() *RejectionsUpdate {
	mutation := newRejectionsMutation(c.config, OpUpdate)
	return &RejectionsUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *RejectionsClient) UpdateOne(_m *Rejections) *RejectionsUpdateOne {
	mutation := newRejectionsMutation(c.config, OpUpdateOne, withRejections(_m))
	return &RejectionsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *RejectionsClient) UpdateOneID(id uuid.UUID) *RejectionsUpdateOne {
	mutation := newRejectionsMutation(c.config, OpUpdateOne, withRejectionsID(id))
	return &RejectionsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Rejections.
func (c *RejectionsClient) Delete() *RejectionsDelete {
	mutation := newRejectionsMutation(c.config, OpDelete)
	return &RejectionsDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *RejectionsClient) DeleteOne(_m *Rejections) *RejectionsDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *RejectionsClient) DeleteOneID(id uuid.UUID) *RejectionsDeleteOne {
	builder := c.Delete().Where(rejections.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &RejectionsDeleteOne{builder}
}

// Query returns a query builder for Rejections.
func (c *RejectionsClient) Query() *RejectionsQuery {
	return &RejectionsQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeRejections},
		inters: c.Interceptors(),
	}
}

// Get returns a Rejections entity by its id.
func (c *RejectionsClient) Get(ctx context.Context, id uuid.UUID) (*Rejections, error) {
	return c.Query().Where(rejections.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *RejectionsClient) GetX(ctx context.Context, id uuid.UUID) *Rejections {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *RejectionsClient) Hooks() []Hook {
	return c.hooks.Rejections
}

// Interceptors returns the client interceptors.
func (c *RejectionsClient) Interceptors() []Interceptor {
	return c.inters.Rejections
}

func (c *RejectionsClient) mutate(ctx context.Context, m *RejectionsMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&RejectionsCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&RejectionsUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&RejectionsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&RejectionsDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Rejections mutation op: %q", m.Op())
	}
}

// SignatoriesClient is a client for the Signatories schema.
type SignatoriesClient struct {
	config
}

// NewSignatoriesClient returns a client for the Signatories from the given config.
func NewSignatoriesClient(c config) *SignatoriesClient {
	return &SignatoriesClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `signatories.Hooks(f(g(h())))`.
func (c *SignatoriesClient) Use(hooks ...Hook) {
	c.hooks.Signatories = append(c.hooks.Signatories, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `signatories.Intercept(f(g(h())))`.
func (c *SignatoriesClient) Intercept(interceptors ...Interceptor) {
	c.inters.Signatories = append(c.inters.Signatories, interceptors...)
}

// Create returns a builder for creating a Signatories entity.
func (c *SignatoriesClient) Create() *SignatoriesCreate {
	mutation := newSignatoriesMutation(c.config, OpCreate)
	return &SignatoriesCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Signatories entities.
func (c *SignatoriesClient) CreateBulk(builders ...*SignatoriesCreate) *SignatoriesCreateBulk {
	return &SignatoriesCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *SignatoriesClient) MapCreateBulk(slice any, setFunc func(*SignatoriesCreate, int)) *SignatoriesCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &SignatoriesCreateBulk{err: fmt.Errorf("calling to SignatoriesClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*SignatoriesCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &SignatoriesCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Signatories.
func (c *SignatoriesClient) Update() *SignatoriesUpdate {
	mutation := newSignatoriesMutation(c.config, OpUpdate)
	return &SignatoriesUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *SignatoriesClient) UpdateOne(_m *Signatories) *SignatoriesUpdateOne {
	mutation := newSignatoriesMutation(c.config, OpUpdateOne, withSignatories(_m))
	return &SignatoriesUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *SignatoriesClient) UpdateOneID(id uuid.UUID) *SignatoriesUpdateOne {
	mutation := newSignatoriesMutation(c.config, OpUpdateOne, withSignatoriesID(id))
	return &SignatoriesUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Signatories.
func (c *SignatoriesClient) Delete() *SignatoriesDelete {
	mutation := newSignatoriesMutation(c.config, OpDelete)
	return &SignatoriesDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *SignatoriesClient) DeleteOne(_m *Signatories) *SignatoriesDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *SignatoriesClient) DeleteOneID(id uuid.UUID) *SignatoriesDeleteOne {
	builder := c.Delete().Where(signatories.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &SignatoriesDeleteOne{builder}
}

// Query returns a query builder for Signatories.
func (c *SignatoriesClient) Query() *SignatoriesQuery {
	return &SignatoriesQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeSignatories},
		inters: c.Interceptors(),
	}
}

// Get returns a Signatories entity by its id.
func (c *SignatoriesClient) Get(ctx context.Context, id uuid.UUID) (*Signatories, error) {
	return c.Query().Where(signatories.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *SignatoriesClient) GetX(ctx context.Context, id uuid.UUID) *Signatories {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *SignatoriesClient) Hooks() []Hook {
	return c.hooks.Signatories
}

// Interceptors returns the client interceptors.
func (c *SignatoriesClient) Interceptors() []Interceptor {
	return c.inters.Signatories
}

func (c *SignatoriesClient) mutate(ctx context.Context, m *SignatoriesMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&SignatoriesCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&SignatoriesUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&SignatoriesUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&SignatoriesDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Signatories mutation op: %q", m.Op())
	}
}

// TransactionClient is a client for the Transaction schema.
type TransactionClient struct {
	config
}

// NewTransactionClient returns a client for the Transaction from the given config.
func NewTransactionClient(c config) *TransactionClient {
	return &TransactionClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `transaction.Hooks(f(g(h())))`.
func (c *TransactionClient) Use(hooks ...Hook) {
	c.hooks.Transaction = append(c.hooks.Transaction, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `transaction.Intercept(f(g(h())))`.
func (c *TransactionClient) Intercept(interceptors ...Interceptor) {
	c.inters.Transaction = append(c.inters.Transaction, interceptors...)
}

// Create returns a builder for creating a Transaction entity.
func (c *TransactionClient) Create() *TransactionCreate {
	mutation := newTransactionMutation(c.config, OpCreate)
	return &TransactionCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Transaction entities.
func (c *TransactionClient) CreateBulk(builders ...*TransactionCreate) *TransactionCreateBulk {
	return &TransactionCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TransactionClient) MapCreateBulk(slice any, setFunc func(*TransactionCreate, int)) *TransactionCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TransactionCreateBulk{err: fmt.Errorf("calling to TransactionClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TransactionCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TransactionCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Transaction.
func (c *TransactionClient) Update() *TransactionUpdate {
	mutation := newTransactionMutation(c.config, OpUpdate)
	return &TransactionUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TransactionClient) UpdateOne(_m *Transaction) *TransactionUpdateOne {
	mutation := newTransactionMutation(c.config, OpUpdateOne, withTransaction(_m))
	return &TransactionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TransactionClient) UpdateOneID(id uuid.UUID) *TransactionUpdateOne {
	mutation := newTransactionMutation(c.config, OpUpdateOne, withTransactionID(id))
	return &TransactionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Transaction.
func (c *TransactionClient) Delete() *TransactionDelete {
	mutation := newTransactionMutation(c.config, OpDelete)
	return &TransactionDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TransactionClient) DeleteOne(_m *Transaction) *TransactionDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TransactionClient) DeleteOneID(id uuid.UUID) *TransactionDeleteOne {
	builder := c.Delete().Where(transaction.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TransactionDeleteOne{builder}
}

// Query returns a query builder for Transaction.
func (c *TransactionClient) Query() *TransactionQuery {
	return &TransactionQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTransaction},
		inters: c.Interceptors(),
	}
}

// Get returns a Transaction entity by its id.
func (c *TransactionClient) Get(ctx context.Context, id uuid.UUID) (*Transaction, error) {
	return c.Query().Where(transaction.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TransactionClient) GetX(ctx context.Context, id uuid.UUID) *Transaction {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *TransactionClient) Hooks() []Hook {
	return c.hooks.Transaction
}

// Interceptors returns the client interceptors.
func (c *TransactionClient) Interceptors() []Interceptor {
	return c.inters.Transaction
}

func (c *TransactionClient) mutate(ctx context.Context, m *TransactionMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TransactionCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TransactionUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TransactionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TransactionDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Transaction mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		AbsTransactionDocuments, Confirmations, Employees, GeneratedDocument, Health,
		Payments, Rejections, Signatories, Transaction []ent.Hook
	}
	inters struct {
		AbsTransactionDocuments, Confirmations, Employees, GeneratedDocument, Health,
		Payments, Rejections, Signatories, Transaction []ent.Interceptor
	}
)
