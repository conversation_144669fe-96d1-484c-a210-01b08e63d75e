// Code generated by ent, DO NOT EDIT.

package payments

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the payments type in the database.
	Label = "payments"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreateTime holds the string denoting the create_time field in the database.
	FieldCreateTime = "create_time"
	// FieldUpdateTime holds the string denoting the update_time field in the database.
	FieldUpdateTime = "update_time"
	// FieldPaymentCode holds the string denoting the payment_code field in the database.
	FieldPaymentCode = "payment_code"
	// FieldPaymentType holds the string denoting the payment_type field in the database.
	FieldPaymentType = "payment_type"
	// FieldPaymentPeriod holds the string denoting the payment_period field in the database.
	FieldPaymentPeriod = "payment_period"
	// FieldKbk holds the string denoting the kbk field in the database.
	FieldKbk = "kbk"
	// FieldEmployeeList holds the string denoting the employee_list field in the database.
	FieldEmployeeList = "employee_list"
	// Table holds the table name of the payments in the database.
	Table = "payments"
)

// Columns holds all SQL columns for payments fields.
var Columns = []string{
	FieldID,
	FieldCreateTime,
	FieldUpdateTime,
	FieldPaymentCode,
	FieldPaymentType,
	FieldPaymentPeriod,
	FieldKbk,
	FieldEmployeeList,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreateTime holds the default value on creation for the "create_time" field.
	DefaultCreateTime func() time.Time
	// DefaultUpdateTime holds the default value on creation for the "update_time" field.
	DefaultUpdateTime func() time.Time
	// UpdateDefaultUpdateTime holds the default value on update for the "update_time" field.
	UpdateDefaultUpdateTime func() time.Time
)

// PaymentType defines the type for the "payment_type" enum field.
type PaymentType string

// PaymentType values.
const (
	PaymentTypeC PaymentType = "C"
	PaymentTypeD PaymentType = "D"
	PaymentTypeM PaymentType = "M"
	PaymentTypeR PaymentType = "R"
	PaymentTypeS PaymentType = "S"
	PaymentTypeV PaymentType = "V"
	PaymentTypeE PaymentType = "E"
	PaymentTypeP PaymentType = "P"
)

func (pt PaymentType) String() string {
	return string(pt)
}

// PaymentTypeValidator is a validator for the "payment_type" field enum values. It is called by the builders before save.
func PaymentTypeValidator(pt PaymentType) error {
	switch pt {
	case PaymentTypeC, PaymentTypeD, PaymentTypeM, PaymentTypeR, PaymentTypeS, PaymentTypeV, PaymentTypeE, PaymentTypeP:
		return nil
	default:
		return fmt.Errorf("payments: invalid enum value for payment_type field: %q", pt)
	}
}

// OrderOption defines the ordering options for the Payments queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreateTime orders the results by the create_time field.
func ByCreateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateTime, opts...).ToFunc()
}

// ByUpdateTime orders the results by the update_time field.
func ByUpdateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateTime, opts...).ToFunc()
}

// ByPaymentCode orders the results by the payment_code field.
func ByPaymentCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPaymentCode, opts...).ToFunc()
}

// ByPaymentType orders the results by the payment_type field.
func ByPaymentType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPaymentType, opts...).ToFunc()
}

// ByPaymentPeriod orders the results by the payment_period field.
func ByPaymentPeriod(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPaymentPeriod, opts...).ToFunc()
}

// ByKbk orders the results by the kbk field.
func ByKbk(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldKbk, opts...).ToFunc()
}
