// Code generated by ent, DO NOT EDIT.

package payments

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.Payments {
	return predicate.Payments(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.Payments {
	return predicate.Payments(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.Payments {
	return predicate.Payments(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.Payments {
	return predicate.Payments(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.Payments {
	return predicate.Payments(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.Payments {
	return predicate.Payments(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.Payments {
	return predicate.Payments(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.Payments {
	return predicate.Payments(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.Payments {
	return predicate.Payments(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.Payments {
	return predicate.Payments(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.Payments {
	return predicate.Payments(sql.FieldEQ(FieldUpdateTime, v))
}

// PaymentCode applies equality check predicate on the "payment_code" field. It's identical to PaymentCodeEQ.
func PaymentCode(v string) predicate.Payments {
	return predicate.Payments(sql.FieldEQ(FieldPaymentCode, v))
}

// PaymentPeriod applies equality check predicate on the "payment_period" field. It's identical to PaymentPeriodEQ.
func PaymentPeriod(v string) predicate.Payments {
	return predicate.Payments(sql.FieldEQ(FieldPaymentPeriod, v))
}

// Kbk applies equality check predicate on the "kbk" field. It's identical to KbkEQ.
func Kbk(v string) predicate.Payments {
	return predicate.Payments(sql.FieldEQ(FieldKbk, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.Payments {
	return predicate.Payments(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.Payments {
	return predicate.Payments(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.Payments {
	return predicate.Payments(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.Payments {
	return predicate.Payments(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.Payments {
	return predicate.Payments(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.Payments {
	return predicate.Payments(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.Payments {
	return predicate.Payments(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.Payments {
	return predicate.Payments(sql.FieldLTE(FieldCreateTime, v))
}

// CreateTimeIsNil applies the IsNil predicate on the "create_time" field.
func CreateTimeIsNil() predicate.Payments {
	return predicate.Payments(sql.FieldIsNull(FieldCreateTime))
}

// CreateTimeNotNil applies the NotNil predicate on the "create_time" field.
func CreateTimeNotNil() predicate.Payments {
	return predicate.Payments(sql.FieldNotNull(FieldCreateTime))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.Payments {
	return predicate.Payments(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.Payments {
	return predicate.Payments(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.Payments {
	return predicate.Payments(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.Payments {
	return predicate.Payments(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.Payments {
	return predicate.Payments(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.Payments {
	return predicate.Payments(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.Payments {
	return predicate.Payments(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.Payments {
	return predicate.Payments(sql.FieldLTE(FieldUpdateTime, v))
}

// UpdateTimeIsNil applies the IsNil predicate on the "update_time" field.
func UpdateTimeIsNil() predicate.Payments {
	return predicate.Payments(sql.FieldIsNull(FieldUpdateTime))
}

// UpdateTimeNotNil applies the NotNil predicate on the "update_time" field.
func UpdateTimeNotNil() predicate.Payments {
	return predicate.Payments(sql.FieldNotNull(FieldUpdateTime))
}

// PaymentCodeEQ applies the EQ predicate on the "payment_code" field.
func PaymentCodeEQ(v string) predicate.Payments {
	return predicate.Payments(sql.FieldEQ(FieldPaymentCode, v))
}

// PaymentCodeNEQ applies the NEQ predicate on the "payment_code" field.
func PaymentCodeNEQ(v string) predicate.Payments {
	return predicate.Payments(sql.FieldNEQ(FieldPaymentCode, v))
}

// PaymentCodeIn applies the In predicate on the "payment_code" field.
func PaymentCodeIn(vs ...string) predicate.Payments {
	return predicate.Payments(sql.FieldIn(FieldPaymentCode, vs...))
}

// PaymentCodeNotIn applies the NotIn predicate on the "payment_code" field.
func PaymentCodeNotIn(vs ...string) predicate.Payments {
	return predicate.Payments(sql.FieldNotIn(FieldPaymentCode, vs...))
}

// PaymentCodeGT applies the GT predicate on the "payment_code" field.
func PaymentCodeGT(v string) predicate.Payments {
	return predicate.Payments(sql.FieldGT(FieldPaymentCode, v))
}

// PaymentCodeGTE applies the GTE predicate on the "payment_code" field.
func PaymentCodeGTE(v string) predicate.Payments {
	return predicate.Payments(sql.FieldGTE(FieldPaymentCode, v))
}

// PaymentCodeLT applies the LT predicate on the "payment_code" field.
func PaymentCodeLT(v string) predicate.Payments {
	return predicate.Payments(sql.FieldLT(FieldPaymentCode, v))
}

// PaymentCodeLTE applies the LTE predicate on the "payment_code" field.
func PaymentCodeLTE(v string) predicate.Payments {
	return predicate.Payments(sql.FieldLTE(FieldPaymentCode, v))
}

// PaymentCodeContains applies the Contains predicate on the "payment_code" field.
func PaymentCodeContains(v string) predicate.Payments {
	return predicate.Payments(sql.FieldContains(FieldPaymentCode, v))
}

// PaymentCodeHasPrefix applies the HasPrefix predicate on the "payment_code" field.
func PaymentCodeHasPrefix(v string) predicate.Payments {
	return predicate.Payments(sql.FieldHasPrefix(FieldPaymentCode, v))
}

// PaymentCodeHasSuffix applies the HasSuffix predicate on the "payment_code" field.
func PaymentCodeHasSuffix(v string) predicate.Payments {
	return predicate.Payments(sql.FieldHasSuffix(FieldPaymentCode, v))
}

// PaymentCodeEqualFold applies the EqualFold predicate on the "payment_code" field.
func PaymentCodeEqualFold(v string) predicate.Payments {
	return predicate.Payments(sql.FieldEqualFold(FieldPaymentCode, v))
}

// PaymentCodeContainsFold applies the ContainsFold predicate on the "payment_code" field.
func PaymentCodeContainsFold(v string) predicate.Payments {
	return predicate.Payments(sql.FieldContainsFold(FieldPaymentCode, v))
}

// PaymentTypeEQ applies the EQ predicate on the "payment_type" field.
func PaymentTypeEQ(v PaymentType) predicate.Payments {
	return predicate.Payments(sql.FieldEQ(FieldPaymentType, v))
}

// PaymentTypeNEQ applies the NEQ predicate on the "payment_type" field.
func PaymentTypeNEQ(v PaymentType) predicate.Payments {
	return predicate.Payments(sql.FieldNEQ(FieldPaymentType, v))
}

// PaymentTypeIn applies the In predicate on the "payment_type" field.
func PaymentTypeIn(vs ...PaymentType) predicate.Payments {
	return predicate.Payments(sql.FieldIn(FieldPaymentType, vs...))
}

// PaymentTypeNotIn applies the NotIn predicate on the "payment_type" field.
func PaymentTypeNotIn(vs ...PaymentType) predicate.Payments {
	return predicate.Payments(sql.FieldNotIn(FieldPaymentType, vs...))
}

// PaymentTypeIsNil applies the IsNil predicate on the "payment_type" field.
func PaymentTypeIsNil() predicate.Payments {
	return predicate.Payments(sql.FieldIsNull(FieldPaymentType))
}

// PaymentTypeNotNil applies the NotNil predicate on the "payment_type" field.
func PaymentTypeNotNil() predicate.Payments {
	return predicate.Payments(sql.FieldNotNull(FieldPaymentType))
}

// PaymentPeriodEQ applies the EQ predicate on the "payment_period" field.
func PaymentPeriodEQ(v string) predicate.Payments {
	return predicate.Payments(sql.FieldEQ(FieldPaymentPeriod, v))
}

// PaymentPeriodNEQ applies the NEQ predicate on the "payment_period" field.
func PaymentPeriodNEQ(v string) predicate.Payments {
	return predicate.Payments(sql.FieldNEQ(FieldPaymentPeriod, v))
}

// PaymentPeriodIn applies the In predicate on the "payment_period" field.
func PaymentPeriodIn(vs ...string) predicate.Payments {
	return predicate.Payments(sql.FieldIn(FieldPaymentPeriod, vs...))
}

// PaymentPeriodNotIn applies the NotIn predicate on the "payment_period" field.
func PaymentPeriodNotIn(vs ...string) predicate.Payments {
	return predicate.Payments(sql.FieldNotIn(FieldPaymentPeriod, vs...))
}

// PaymentPeriodGT applies the GT predicate on the "payment_period" field.
func PaymentPeriodGT(v string) predicate.Payments {
	return predicate.Payments(sql.FieldGT(FieldPaymentPeriod, v))
}

// PaymentPeriodGTE applies the GTE predicate on the "payment_period" field.
func PaymentPeriodGTE(v string) predicate.Payments {
	return predicate.Payments(sql.FieldGTE(FieldPaymentPeriod, v))
}

// PaymentPeriodLT applies the LT predicate on the "payment_period" field.
func PaymentPeriodLT(v string) predicate.Payments {
	return predicate.Payments(sql.FieldLT(FieldPaymentPeriod, v))
}

// PaymentPeriodLTE applies the LTE predicate on the "payment_period" field.
func PaymentPeriodLTE(v string) predicate.Payments {
	return predicate.Payments(sql.FieldLTE(FieldPaymentPeriod, v))
}

// PaymentPeriodContains applies the Contains predicate on the "payment_period" field.
func PaymentPeriodContains(v string) predicate.Payments {
	return predicate.Payments(sql.FieldContains(FieldPaymentPeriod, v))
}

// PaymentPeriodHasPrefix applies the HasPrefix predicate on the "payment_period" field.
func PaymentPeriodHasPrefix(v string) predicate.Payments {
	return predicate.Payments(sql.FieldHasPrefix(FieldPaymentPeriod, v))
}

// PaymentPeriodHasSuffix applies the HasSuffix predicate on the "payment_period" field.
func PaymentPeriodHasSuffix(v string) predicate.Payments {
	return predicate.Payments(sql.FieldHasSuffix(FieldPaymentPeriod, v))
}

// PaymentPeriodEqualFold applies the EqualFold predicate on the "payment_period" field.
func PaymentPeriodEqualFold(v string) predicate.Payments {
	return predicate.Payments(sql.FieldEqualFold(FieldPaymentPeriod, v))
}

// PaymentPeriodContainsFold applies the ContainsFold predicate on the "payment_period" field.
func PaymentPeriodContainsFold(v string) predicate.Payments {
	return predicate.Payments(sql.FieldContainsFold(FieldPaymentPeriod, v))
}

// KbkEQ applies the EQ predicate on the "kbk" field.
func KbkEQ(v string) predicate.Payments {
	return predicate.Payments(sql.FieldEQ(FieldKbk, v))
}

// KbkNEQ applies the NEQ predicate on the "kbk" field.
func KbkNEQ(v string) predicate.Payments {
	return predicate.Payments(sql.FieldNEQ(FieldKbk, v))
}

// KbkIn applies the In predicate on the "kbk" field.
func KbkIn(vs ...string) predicate.Payments {
	return predicate.Payments(sql.FieldIn(FieldKbk, vs...))
}

// KbkNotIn applies the NotIn predicate on the "kbk" field.
func KbkNotIn(vs ...string) predicate.Payments {
	return predicate.Payments(sql.FieldNotIn(FieldKbk, vs...))
}

// KbkGT applies the GT predicate on the "kbk" field.
func KbkGT(v string) predicate.Payments {
	return predicate.Payments(sql.FieldGT(FieldKbk, v))
}

// KbkGTE applies the GTE predicate on the "kbk" field.
func KbkGTE(v string) predicate.Payments {
	return predicate.Payments(sql.FieldGTE(FieldKbk, v))
}

// KbkLT applies the LT predicate on the "kbk" field.
func KbkLT(v string) predicate.Payments {
	return predicate.Payments(sql.FieldLT(FieldKbk, v))
}

// KbkLTE applies the LTE predicate on the "kbk" field.
func KbkLTE(v string) predicate.Payments {
	return predicate.Payments(sql.FieldLTE(FieldKbk, v))
}

// KbkContains applies the Contains predicate on the "kbk" field.
func KbkContains(v string) predicate.Payments {
	return predicate.Payments(sql.FieldContains(FieldKbk, v))
}

// KbkHasPrefix applies the HasPrefix predicate on the "kbk" field.
func KbkHasPrefix(v string) predicate.Payments {
	return predicate.Payments(sql.FieldHasPrefix(FieldKbk, v))
}

// KbkHasSuffix applies the HasSuffix predicate on the "kbk" field.
func KbkHasSuffix(v string) predicate.Payments {
	return predicate.Payments(sql.FieldHasSuffix(FieldKbk, v))
}

// KbkEqualFold applies the EqualFold predicate on the "kbk" field.
func KbkEqualFold(v string) predicate.Payments {
	return predicate.Payments(sql.FieldEqualFold(FieldKbk, v))
}

// KbkContainsFold applies the ContainsFold predicate on the "kbk" field.
func KbkContainsFold(v string) predicate.Payments {
	return predicate.Payments(sql.FieldContainsFold(FieldKbk, v))
}

// EmployeeListIsNil applies the IsNil predicate on the "employee_list" field.
func EmployeeListIsNil() predicate.Payments {
	return predicate.Payments(sql.FieldIsNull(FieldEmployeeList))
}

// EmployeeListNotNil applies the NotNil predicate on the "employee_list" field.
func EmployeeListNotNil() predicate.Payments {
	return predicate.Payments(sql.FieldNotNull(FieldEmployeeList))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Payments) predicate.Payments {
	return predicate.Payments(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Payments) predicate.Payments {
	return predicate.Payments(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Payments) predicate.Payments {
	return predicate.Payments(sql.NotPredicates(p))
}
