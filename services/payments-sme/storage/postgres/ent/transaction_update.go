// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/predicate"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/transaction"
)

// TransactionUpdate is the builder for updating Transaction entities.
type TransactionUpdate struct {
	config
	hooks     []Hook
	mutation  *TransactionMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the TransactionUpdate builder.
func (_u *TransactionUpdate) Where(ps ...predicate.Transaction) *TransactionUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetTransactionNumber sets the "transaction_number" field.
func (_u *TransactionUpdate) SetTransactionNumber(v string) *TransactionUpdate {
	_u.mutation.SetTransactionNumber(v)
	return _u
}

// SetNillableTransactionNumber sets the "transaction_number" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableTransactionNumber(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetTransactionNumber(*v)
	}
	return _u
}

// SetTransactionDate sets the "transaction_date" field.
func (_u *TransactionUpdate) SetTransactionDate(v time.Time) *TransactionUpdate {
	_u.mutation.SetTransactionDate(v)
	return _u
}

// SetNillableTransactionDate sets the "transaction_date" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableTransactionDate(v *time.Time) *TransactionUpdate {
	if v != nil {
		_u.SetTransactionDate(*v)
	}
	return _u
}

// SetTransactionType sets the "transaction_type" field.
func (_u *TransactionUpdate) SetTransactionType(v transaction.TransactionType) *TransactionUpdate {
	_u.mutation.SetTransactionType(v)
	return _u
}

// SetNillableTransactionType sets the "transaction_type" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableTransactionType(v *transaction.TransactionType) *TransactionUpdate {
	if v != nil {
		_u.SetTransactionType(*v)
	}
	return _u
}

// SetInitiatorID sets the "initiator_id" field.
func (_u *TransactionUpdate) SetInitiatorID(v string) *TransactionUpdate {
	_u.mutation.SetInitiatorID(v)
	return _u
}

// SetNillableInitiatorID sets the "initiator_id" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableInitiatorID(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetInitiatorID(*v)
	}
	return _u
}

// SetIdempotencyKey sets the "idempotency_key" field.
func (_u *TransactionUpdate) SetIdempotencyKey(v string) *TransactionUpdate {
	_u.mutation.SetIdempotencyKey(v)
	return _u
}

// SetNillableIdempotencyKey sets the "idempotency_key" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableIdempotencyKey(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetIdempotencyKey(*v)
	}
	return _u
}

// SetValueDate sets the "value_date" field.
func (_u *TransactionUpdate) SetValueDate(v string) *TransactionUpdate {
	_u.mutation.SetValueDate(v)
	return _u
}

// SetNillableValueDate sets the "value_date" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableValueDate(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetValueDate(*v)
	}
	return _u
}

// ClearValueDate clears the value of the "value_date" field.
func (_u *TransactionUpdate) ClearValueDate() *TransactionUpdate {
	_u.mutation.ClearValueDate()
	return _u
}

// SetTransactionStatus sets the "transaction_status" field.
func (_u *TransactionUpdate) SetTransactionStatus(v transaction.TransactionStatus) *TransactionUpdate {
	_u.mutation.SetTransactionStatus(v)
	return _u
}

// SetNillableTransactionStatus sets the "transaction_status" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableTransactionStatus(v *transaction.TransactionStatus) *TransactionUpdate {
	if v != nil {
		_u.SetTransactionStatus(*v)
	}
	return _u
}

// SetTransactionAmount sets the "transaction_amount" field.
func (_u *TransactionUpdate) SetTransactionAmount(v string) *TransactionUpdate {
	_u.mutation.SetTransactionAmount(v)
	return _u
}

// SetNillableTransactionAmount sets the "transaction_amount" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableTransactionAmount(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetTransactionAmount(*v)
	}
	return _u
}

// SetTransactionComission sets the "transaction_comission" field.
func (_u *TransactionUpdate) SetTransactionComission(v string) *TransactionUpdate {
	_u.mutation.SetTransactionComission(v)
	return _u
}

// SetNillableTransactionComission sets the "transaction_comission" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableTransactionComission(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetTransactionComission(*v)
	}
	return _u
}

// ClearTransactionComission clears the value of the "transaction_comission" field.
func (_u *TransactionUpdate) ClearTransactionComission() *TransactionUpdate {
	_u.mutation.ClearTransactionComission()
	return _u
}

// SetTransactionCurrency sets the "transaction_currency" field.
func (_u *TransactionUpdate) SetTransactionCurrency(v transaction.TransactionCurrency) *TransactionUpdate {
	_u.mutation.SetTransactionCurrency(v)
	return _u
}

// SetNillableTransactionCurrency sets the "transaction_currency" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableTransactionCurrency(v *transaction.TransactionCurrency) *TransactionUpdate {
	if v != nil {
		_u.SetTransactionCurrency(*v)
	}
	return _u
}

// SetTransactionTotalAmount sets the "transaction_total_amount" field.
func (_u *TransactionUpdate) SetTransactionTotalAmount(v string) *TransactionUpdate {
	_u.mutation.SetTransactionTotalAmount(v)
	return _u
}

// SetNillableTransactionTotalAmount sets the "transaction_total_amount" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableTransactionTotalAmount(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetTransactionTotalAmount(*v)
	}
	return _u
}

// SetTransactionDirection sets the "transaction_direction" field.
func (_u *TransactionUpdate) SetTransactionDirection(v transaction.TransactionDirection) *TransactionUpdate {
	_u.mutation.SetTransactionDirection(v)
	return _u
}

// SetNillableTransactionDirection sets the "transaction_direction" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableTransactionDirection(v *transaction.TransactionDirection) *TransactionUpdate {
	if v != nil {
		_u.SetTransactionDirection(*v)
	}
	return _u
}

// ClearTransactionDirection clears the value of the "transaction_direction" field.
func (_u *TransactionUpdate) ClearTransactionDirection() *TransactionUpdate {
	_u.mutation.ClearTransactionDirection()
	return _u
}

// SetPurposeCode sets the "purpose_code" field.
func (_u *TransactionUpdate) SetPurposeCode(v string) *TransactionUpdate {
	_u.mutation.SetPurposeCode(v)
	return _u
}

// SetNillablePurposeCode sets the "purpose_code" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillablePurposeCode(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetPurposeCode(*v)
	}
	return _u
}

// ClearPurposeCode clears the value of the "purpose_code" field.
func (_u *TransactionUpdate) ClearPurposeCode() *TransactionUpdate {
	_u.mutation.ClearPurposeCode()
	return _u
}

// SetPurposeDetails sets the "purpose_details" field.
func (_u *TransactionUpdate) SetPurposeDetails(v string) *TransactionUpdate {
	_u.mutation.SetPurposeDetails(v)
	return _u
}

// SetNillablePurposeDetails sets the "purpose_details" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillablePurposeDetails(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetPurposeDetails(*v)
	}
	return _u
}

// ClearPurposeDetails clears the value of the "purpose_details" field.
func (_u *TransactionUpdate) ClearPurposeDetails() *TransactionUpdate {
	_u.mutation.ClearPurposeDetails()
	return _u
}

// SetPayerKod sets the "payer_kod" field.
func (_u *TransactionUpdate) SetPayerKod(v string) *TransactionUpdate {
	_u.mutation.SetPayerKod(v)
	return _u
}

// SetNillablePayerKod sets the "payer_kod" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillablePayerKod(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetPayerKod(*v)
	}
	return _u
}

// ClearPayerKod clears the value of the "payer_kod" field.
func (_u *TransactionUpdate) ClearPayerKod() *TransactionUpdate {
	_u.mutation.ClearPayerKod()
	return _u
}

// SetPayerBinIin sets the "payer_bin_iin" field.
func (_u *TransactionUpdate) SetPayerBinIin(v string) *TransactionUpdate {
	_u.mutation.SetPayerBinIin(v)
	return _u
}

// SetNillablePayerBinIin sets the "payer_bin_iin" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillablePayerBinIin(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetPayerBinIin(*v)
	}
	return _u
}

// ClearPayerBinIin clears the value of the "payer_bin_iin" field.
func (_u *TransactionUpdate) ClearPayerBinIin() *TransactionUpdate {
	_u.mutation.ClearPayerBinIin()
	return _u
}

// SetPayerName sets the "payer_name" field.
func (_u *TransactionUpdate) SetPayerName(v string) *TransactionUpdate {
	_u.mutation.SetPayerName(v)
	return _u
}

// SetNillablePayerName sets the "payer_name" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillablePayerName(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetPayerName(*v)
	}
	return _u
}

// ClearPayerName clears the value of the "payer_name" field.
func (_u *TransactionUpdate) ClearPayerName() *TransactionUpdate {
	_u.mutation.ClearPayerName()
	return _u
}

// SetPayerType sets the "payer_type" field.
func (_u *TransactionUpdate) SetPayerType(v transaction.PayerType) *TransactionUpdate {
	_u.mutation.SetPayerType(v)
	return _u
}

// SetNillablePayerType sets the "payer_type" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillablePayerType(v *transaction.PayerType) *TransactionUpdate {
	if v != nil {
		_u.SetPayerType(*v)
	}
	return _u
}

// ClearPayerType clears the value of the "payer_type" field.
func (_u *TransactionUpdate) ClearPayerType() *TransactionUpdate {
	_u.mutation.ClearPayerType()
	return _u
}

// SetPayerAccountIban sets the "payer_account_iban" field.
func (_u *TransactionUpdate) SetPayerAccountIban(v string) *TransactionUpdate {
	_u.mutation.SetPayerAccountIban(v)
	return _u
}

// SetNillablePayerAccountIban sets the "payer_account_iban" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillablePayerAccountIban(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetPayerAccountIban(*v)
	}
	return _u
}

// ClearPayerAccountIban clears the value of the "payer_account_iban" field.
func (_u *TransactionUpdate) ClearPayerAccountIban() *TransactionUpdate {
	_u.mutation.ClearPayerAccountIban()
	return _u
}

// SetPayerBankBic sets the "payer_bank_bic" field.
func (_u *TransactionUpdate) SetPayerBankBic(v string) *TransactionUpdate {
	_u.mutation.SetPayerBankBic(v)
	return _u
}

// SetNillablePayerBankBic sets the "payer_bank_bic" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillablePayerBankBic(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetPayerBankBic(*v)
	}
	return _u
}

// ClearPayerBankBic clears the value of the "payer_bank_bic" field.
func (_u *TransactionUpdate) ClearPayerBankBic() *TransactionUpdate {
	_u.mutation.ClearPayerBankBic()
	return _u
}

// SetPayerBankName sets the "payer_bank_name" field.
func (_u *TransactionUpdate) SetPayerBankName(v string) *TransactionUpdate {
	_u.mutation.SetPayerBankName(v)
	return _u
}

// SetNillablePayerBankName sets the "payer_bank_name" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillablePayerBankName(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetPayerBankName(*v)
	}
	return _u
}

// ClearPayerBankName clears the value of the "payer_bank_name" field.
func (_u *TransactionUpdate) ClearPayerBankName() *TransactionUpdate {
	_u.mutation.ClearPayerBankName()
	return _u
}

// SetPayerIsoCountryCode sets the "payer_iso_country_code" field.
func (_u *TransactionUpdate) SetPayerIsoCountryCode(v string) *TransactionUpdate {
	_u.mutation.SetPayerIsoCountryCode(v)
	return _u
}

// SetNillablePayerIsoCountryCode sets the "payer_iso_country_code" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillablePayerIsoCountryCode(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetPayerIsoCountryCode(*v)
	}
	return _u
}

// ClearPayerIsoCountryCode clears the value of the "payer_iso_country_code" field.
func (_u *TransactionUpdate) ClearPayerIsoCountryCode() *TransactionUpdate {
	_u.mutation.ClearPayerIsoCountryCode()
	return _u
}

// SetRealPayerName sets the "real_payer_name" field.
func (_u *TransactionUpdate) SetRealPayerName(v string) *TransactionUpdate {
	_u.mutation.SetRealPayerName(v)
	return _u
}

// SetNillableRealPayerName sets the "real_payer_name" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableRealPayerName(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetRealPayerName(*v)
	}
	return _u
}

// ClearRealPayerName clears the value of the "real_payer_name" field.
func (_u *TransactionUpdate) ClearRealPayerName() *TransactionUpdate {
	_u.mutation.ClearRealPayerName()
	return _u
}

// SetRealPayerBinIin sets the "real_payer_bin_iin" field.
func (_u *TransactionUpdate) SetRealPayerBinIin(v string) *TransactionUpdate {
	_u.mutation.SetRealPayerBinIin(v)
	return _u
}

// SetNillableRealPayerBinIin sets the "real_payer_bin_iin" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableRealPayerBinIin(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetRealPayerBinIin(*v)
	}
	return _u
}

// ClearRealPayerBinIin clears the value of the "real_payer_bin_iin" field.
func (_u *TransactionUpdate) ClearRealPayerBinIin() *TransactionUpdate {
	_u.mutation.ClearRealPayerBinIin()
	return _u
}

// SetRealPayerIsoCountryCode sets the "real_payer_iso_country_code" field.
func (_u *TransactionUpdate) SetRealPayerIsoCountryCode(v string) *TransactionUpdate {
	_u.mutation.SetRealPayerIsoCountryCode(v)
	return _u
}

// SetNillableRealPayerIsoCountryCode sets the "real_payer_iso_country_code" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableRealPayerIsoCountryCode(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetRealPayerIsoCountryCode(*v)
	}
	return _u
}

// ClearRealPayerIsoCountryCode clears the value of the "real_payer_iso_country_code" field.
func (_u *TransactionUpdate) ClearRealPayerIsoCountryCode() *TransactionUpdate {
	_u.mutation.ClearRealPayerIsoCountryCode()
	return _u
}

// SetRealPayerType sets the "real_payer_type" field.
func (_u *TransactionUpdate) SetRealPayerType(v transaction.RealPayerType) *TransactionUpdate {
	_u.mutation.SetRealPayerType(v)
	return _u
}

// SetNillableRealPayerType sets the "real_payer_type" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableRealPayerType(v *transaction.RealPayerType) *TransactionUpdate {
	if v != nil {
		_u.SetRealPayerType(*v)
	}
	return _u
}

// ClearRealPayerType clears the value of the "real_payer_type" field.
func (_u *TransactionUpdate) ClearRealPayerType() *TransactionUpdate {
	_u.mutation.ClearRealPayerType()
	return _u
}

// SetBeneficiaryKbe sets the "beneficiary_kbe" field.
func (_u *TransactionUpdate) SetBeneficiaryKbe(v string) *TransactionUpdate {
	_u.mutation.SetBeneficiaryKbe(v)
	return _u
}

// SetNillableBeneficiaryKbe sets the "beneficiary_kbe" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableBeneficiaryKbe(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetBeneficiaryKbe(*v)
	}
	return _u
}

// ClearBeneficiaryKbe clears the value of the "beneficiary_kbe" field.
func (_u *TransactionUpdate) ClearBeneficiaryKbe() *TransactionUpdate {
	_u.mutation.ClearBeneficiaryKbe()
	return _u
}

// SetBeneficiaryBinIin sets the "beneficiary_bin_iin" field.
func (_u *TransactionUpdate) SetBeneficiaryBinIin(v string) *TransactionUpdate {
	_u.mutation.SetBeneficiaryBinIin(v)
	return _u
}

// SetNillableBeneficiaryBinIin sets the "beneficiary_bin_iin" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableBeneficiaryBinIin(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetBeneficiaryBinIin(*v)
	}
	return _u
}

// ClearBeneficiaryBinIin clears the value of the "beneficiary_bin_iin" field.
func (_u *TransactionUpdate) ClearBeneficiaryBinIin() *TransactionUpdate {
	_u.mutation.ClearBeneficiaryBinIin()
	return _u
}

// SetBeneficiaryName sets the "beneficiary_name" field.
func (_u *TransactionUpdate) SetBeneficiaryName(v string) *TransactionUpdate {
	_u.mutation.SetBeneficiaryName(v)
	return _u
}

// SetNillableBeneficiaryName sets the "beneficiary_name" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableBeneficiaryName(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetBeneficiaryName(*v)
	}
	return _u
}

// ClearBeneficiaryName clears the value of the "beneficiary_name" field.
func (_u *TransactionUpdate) ClearBeneficiaryName() *TransactionUpdate {
	_u.mutation.ClearBeneficiaryName()
	return _u
}

// SetBeneficiaryType sets the "beneficiary_type" field.
func (_u *TransactionUpdate) SetBeneficiaryType(v transaction.BeneficiaryType) *TransactionUpdate {
	_u.mutation.SetBeneficiaryType(v)
	return _u
}

// SetNillableBeneficiaryType sets the "beneficiary_type" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableBeneficiaryType(v *transaction.BeneficiaryType) *TransactionUpdate {
	if v != nil {
		_u.SetBeneficiaryType(*v)
	}
	return _u
}

// ClearBeneficiaryType clears the value of the "beneficiary_type" field.
func (_u *TransactionUpdate) ClearBeneficiaryType() *TransactionUpdate {
	_u.mutation.ClearBeneficiaryType()
	return _u
}

// SetBeneficiaryAccountIban sets the "beneficiary_account_iban" field.
func (_u *TransactionUpdate) SetBeneficiaryAccountIban(v string) *TransactionUpdate {
	_u.mutation.SetBeneficiaryAccountIban(v)
	return _u
}

// SetNillableBeneficiaryAccountIban sets the "beneficiary_account_iban" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableBeneficiaryAccountIban(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetBeneficiaryAccountIban(*v)
	}
	return _u
}

// ClearBeneficiaryAccountIban clears the value of the "beneficiary_account_iban" field.
func (_u *TransactionUpdate) ClearBeneficiaryAccountIban() *TransactionUpdate {
	_u.mutation.ClearBeneficiaryAccountIban()
	return _u
}

// SetBeneficiaryBankBic sets the "beneficiary_bank_bic" field.
func (_u *TransactionUpdate) SetBeneficiaryBankBic(v string) *TransactionUpdate {
	_u.mutation.SetBeneficiaryBankBic(v)
	return _u
}

// SetNillableBeneficiaryBankBic sets the "beneficiary_bank_bic" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableBeneficiaryBankBic(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetBeneficiaryBankBic(*v)
	}
	return _u
}

// ClearBeneficiaryBankBic clears the value of the "beneficiary_bank_bic" field.
func (_u *TransactionUpdate) ClearBeneficiaryBankBic() *TransactionUpdate {
	_u.mutation.ClearBeneficiaryBankBic()
	return _u
}

// SetBeneficiaryBankName sets the "beneficiary_bank_name" field.
func (_u *TransactionUpdate) SetBeneficiaryBankName(v string) *TransactionUpdate {
	_u.mutation.SetBeneficiaryBankName(v)
	return _u
}

// SetNillableBeneficiaryBankName sets the "beneficiary_bank_name" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableBeneficiaryBankName(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetBeneficiaryBankName(*v)
	}
	return _u
}

// ClearBeneficiaryBankName clears the value of the "beneficiary_bank_name" field.
func (_u *TransactionUpdate) ClearBeneficiaryBankName() *TransactionUpdate {
	_u.mutation.ClearBeneficiaryBankName()
	return _u
}

// SetBeneficiaryIsoCountryCode sets the "beneficiary_iso_country_code" field.
func (_u *TransactionUpdate) SetBeneficiaryIsoCountryCode(v string) *TransactionUpdate {
	_u.mutation.SetBeneficiaryIsoCountryCode(v)
	return _u
}

// SetNillableBeneficiaryIsoCountryCode sets the "beneficiary_iso_country_code" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableBeneficiaryIsoCountryCode(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetBeneficiaryIsoCountryCode(*v)
	}
	return _u
}

// ClearBeneficiaryIsoCountryCode clears the value of the "beneficiary_iso_country_code" field.
func (_u *TransactionUpdate) ClearBeneficiaryIsoCountryCode() *TransactionUpdate {
	_u.mutation.ClearBeneficiaryIsoCountryCode()
	return _u
}

// SetRealBeneficiaryName sets the "real_beneficiary_name" field.
func (_u *TransactionUpdate) SetRealBeneficiaryName(v string) *TransactionUpdate {
	_u.mutation.SetRealBeneficiaryName(v)
	return _u
}

// SetNillableRealBeneficiaryName sets the "real_beneficiary_name" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableRealBeneficiaryName(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetRealBeneficiaryName(*v)
	}
	return _u
}

// ClearRealBeneficiaryName clears the value of the "real_beneficiary_name" field.
func (_u *TransactionUpdate) ClearRealBeneficiaryName() *TransactionUpdate {
	_u.mutation.ClearRealBeneficiaryName()
	return _u
}

// SetRealBeneficiaryBinIin sets the "real_beneficiary_bin_iin" field.
func (_u *TransactionUpdate) SetRealBeneficiaryBinIin(v string) *TransactionUpdate {
	_u.mutation.SetRealBeneficiaryBinIin(v)
	return _u
}

// SetNillableRealBeneficiaryBinIin sets the "real_beneficiary_bin_iin" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableRealBeneficiaryBinIin(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetRealBeneficiaryBinIin(*v)
	}
	return _u
}

// ClearRealBeneficiaryBinIin clears the value of the "real_beneficiary_bin_iin" field.
func (_u *TransactionUpdate) ClearRealBeneficiaryBinIin() *TransactionUpdate {
	_u.mutation.ClearRealBeneficiaryBinIin()
	return _u
}

// SetRealBeneficiaryCountryCode sets the "real_beneficiary_country_code" field.
func (_u *TransactionUpdate) SetRealBeneficiaryCountryCode(v string) *TransactionUpdate {
	_u.mutation.SetRealBeneficiaryCountryCode(v)
	return _u
}

// SetNillableRealBeneficiaryCountryCode sets the "real_beneficiary_country_code" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableRealBeneficiaryCountryCode(v *string) *TransactionUpdate {
	if v != nil {
		_u.SetRealBeneficiaryCountryCode(*v)
	}
	return _u
}

// ClearRealBeneficiaryCountryCode clears the value of the "real_beneficiary_country_code" field.
func (_u *TransactionUpdate) ClearRealBeneficiaryCountryCode() *TransactionUpdate {
	_u.mutation.ClearRealBeneficiaryCountryCode()
	return _u
}

// SetRealBeneficiaryType sets the "real_beneficiary_type" field.
func (_u *TransactionUpdate) SetRealBeneficiaryType(v transaction.RealBeneficiaryType) *TransactionUpdate {
	_u.mutation.SetRealBeneficiaryType(v)
	return _u
}

// SetNillableRealBeneficiaryType sets the "real_beneficiary_type" field if the given value is not nil.
func (_u *TransactionUpdate) SetNillableRealBeneficiaryType(v *transaction.RealBeneficiaryType) *TransactionUpdate {
	if v != nil {
		_u.SetRealBeneficiaryType(*v)
	}
	return _u
}

// ClearRealBeneficiaryType clears the value of the "real_beneficiary_type" field.
func (_u *TransactionUpdate) ClearRealBeneficiaryType() *TransactionUpdate {
	_u.mutation.ClearRealBeneficiaryType()
	return _u
}

// Mutation returns the TransactionMutation object of the builder.
func (_u *TransactionUpdate) Mutation() *TransactionMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *TransactionUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *TransactionUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *TransactionUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *TransactionUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *TransactionUpdate) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := transaction.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *TransactionUpdate) check() error {
	if v, ok := _u.mutation.TransactionType(); ok {
		if err := transaction.TransactionTypeValidator(v); err != nil {
			return &ValidationError{Name: "transaction_type", err: fmt.Errorf(`ent: validator failed for field "Transaction.transaction_type": %w`, err)}
		}
	}
	if v, ok := _u.mutation.TransactionStatus(); ok {
		if err := transaction.TransactionStatusValidator(v); err != nil {
			return &ValidationError{Name: "transaction_status", err: fmt.Errorf(`ent: validator failed for field "Transaction.transaction_status": %w`, err)}
		}
	}
	if v, ok := _u.mutation.TransactionCurrency(); ok {
		if err := transaction.TransactionCurrencyValidator(v); err != nil {
			return &ValidationError{Name: "transaction_currency", err: fmt.Errorf(`ent: validator failed for field "Transaction.transaction_currency": %w`, err)}
		}
	}
	if v, ok := _u.mutation.TransactionDirection(); ok {
		if err := transaction.TransactionDirectionValidator(v); err != nil {
			return &ValidationError{Name: "transaction_direction", err: fmt.Errorf(`ent: validator failed for field "Transaction.transaction_direction": %w`, err)}
		}
	}
	if v, ok := _u.mutation.PayerType(); ok {
		if err := transaction.PayerTypeValidator(v); err != nil {
			return &ValidationError{Name: "payer_type", err: fmt.Errorf(`ent: validator failed for field "Transaction.payer_type": %w`, err)}
		}
	}
	if v, ok := _u.mutation.RealPayerType(); ok {
		if err := transaction.RealPayerTypeValidator(v); err != nil {
			return &ValidationError{Name: "real_payer_type", err: fmt.Errorf(`ent: validator failed for field "Transaction.real_payer_type": %w`, err)}
		}
	}
	if v, ok := _u.mutation.BeneficiaryType(); ok {
		if err := transaction.BeneficiaryTypeValidator(v); err != nil {
			return &ValidationError{Name: "beneficiary_type", err: fmt.Errorf(`ent: validator failed for field "Transaction.beneficiary_type": %w`, err)}
		}
	}
	if v, ok := _u.mutation.RealBeneficiaryType(); ok {
		if err := transaction.RealBeneficiaryTypeValidator(v); err != nil {
			return &ValidationError{Name: "real_beneficiary_type", err: fmt.Errorf(`ent: validator failed for field "Transaction.real_beneficiary_type": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *TransactionUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *TransactionUpdate {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *TransactionUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(transaction.Table, transaction.Columns, sqlgraph.NewFieldSpec(transaction.FieldID, field.TypeUUID))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(transaction.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(transaction.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(transaction.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.TransactionNumber(); ok {
		_spec.SetField(transaction.FieldTransactionNumber, field.TypeString, value)
	}
	if value, ok := _u.mutation.TransactionDate(); ok {
		_spec.SetField(transaction.FieldTransactionDate, field.TypeTime, value)
	}
	if value, ok := _u.mutation.TransactionType(); ok {
		_spec.SetField(transaction.FieldTransactionType, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.InitiatorID(); ok {
		_spec.SetField(transaction.FieldInitiatorID, field.TypeString, value)
	}
	if value, ok := _u.mutation.IdempotencyKey(); ok {
		_spec.SetField(transaction.FieldIdempotencyKey, field.TypeString, value)
	}
	if value, ok := _u.mutation.ValueDate(); ok {
		_spec.SetField(transaction.FieldValueDate, field.TypeString, value)
	}
	if _u.mutation.ValueDateCleared() {
		_spec.ClearField(transaction.FieldValueDate, field.TypeString)
	}
	if value, ok := _u.mutation.TransactionStatus(); ok {
		_spec.SetField(transaction.FieldTransactionStatus, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.TransactionAmount(); ok {
		_spec.SetField(transaction.FieldTransactionAmount, field.TypeString, value)
	}
	if value, ok := _u.mutation.TransactionComission(); ok {
		_spec.SetField(transaction.FieldTransactionComission, field.TypeString, value)
	}
	if _u.mutation.TransactionComissionCleared() {
		_spec.ClearField(transaction.FieldTransactionComission, field.TypeString)
	}
	if value, ok := _u.mutation.TransactionCurrency(); ok {
		_spec.SetField(transaction.FieldTransactionCurrency, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.TransactionTotalAmount(); ok {
		_spec.SetField(transaction.FieldTransactionTotalAmount, field.TypeString, value)
	}
	if value, ok := _u.mutation.TransactionDirection(); ok {
		_spec.SetField(transaction.FieldTransactionDirection, field.TypeEnum, value)
	}
	if _u.mutation.TransactionDirectionCleared() {
		_spec.ClearField(transaction.FieldTransactionDirection, field.TypeEnum)
	}
	if value, ok := _u.mutation.PurposeCode(); ok {
		_spec.SetField(transaction.FieldPurposeCode, field.TypeString, value)
	}
	if _u.mutation.PurposeCodeCleared() {
		_spec.ClearField(transaction.FieldPurposeCode, field.TypeString)
	}
	if value, ok := _u.mutation.PurposeDetails(); ok {
		_spec.SetField(transaction.FieldPurposeDetails, field.TypeString, value)
	}
	if _u.mutation.PurposeDetailsCleared() {
		_spec.ClearField(transaction.FieldPurposeDetails, field.TypeString)
	}
	if value, ok := _u.mutation.PayerKod(); ok {
		_spec.SetField(transaction.FieldPayerKod, field.TypeString, value)
	}
	if _u.mutation.PayerKodCleared() {
		_spec.ClearField(transaction.FieldPayerKod, field.TypeString)
	}
	if value, ok := _u.mutation.PayerBinIin(); ok {
		_spec.SetField(transaction.FieldPayerBinIin, field.TypeString, value)
	}
	if _u.mutation.PayerBinIinCleared() {
		_spec.ClearField(transaction.FieldPayerBinIin, field.TypeString)
	}
	if value, ok := _u.mutation.PayerName(); ok {
		_spec.SetField(transaction.FieldPayerName, field.TypeString, value)
	}
	if _u.mutation.PayerNameCleared() {
		_spec.ClearField(transaction.FieldPayerName, field.TypeString)
	}
	if value, ok := _u.mutation.PayerType(); ok {
		_spec.SetField(transaction.FieldPayerType, field.TypeEnum, value)
	}
	if _u.mutation.PayerTypeCleared() {
		_spec.ClearField(transaction.FieldPayerType, field.TypeEnum)
	}
	if value, ok := _u.mutation.PayerAccountIban(); ok {
		_spec.SetField(transaction.FieldPayerAccountIban, field.TypeString, value)
	}
	if _u.mutation.PayerAccountIbanCleared() {
		_spec.ClearField(transaction.FieldPayerAccountIban, field.TypeString)
	}
	if value, ok := _u.mutation.PayerBankBic(); ok {
		_spec.SetField(transaction.FieldPayerBankBic, field.TypeString, value)
	}
	if _u.mutation.PayerBankBicCleared() {
		_spec.ClearField(transaction.FieldPayerBankBic, field.TypeString)
	}
	if value, ok := _u.mutation.PayerBankName(); ok {
		_spec.SetField(transaction.FieldPayerBankName, field.TypeString, value)
	}
	if _u.mutation.PayerBankNameCleared() {
		_spec.ClearField(transaction.FieldPayerBankName, field.TypeString)
	}
	if value, ok := _u.mutation.PayerIsoCountryCode(); ok {
		_spec.SetField(transaction.FieldPayerIsoCountryCode, field.TypeString, value)
	}
	if _u.mutation.PayerIsoCountryCodeCleared() {
		_spec.ClearField(transaction.FieldPayerIsoCountryCode, field.TypeString)
	}
	if value, ok := _u.mutation.RealPayerName(); ok {
		_spec.SetField(transaction.FieldRealPayerName, field.TypeString, value)
	}
	if _u.mutation.RealPayerNameCleared() {
		_spec.ClearField(transaction.FieldRealPayerName, field.TypeString)
	}
	if value, ok := _u.mutation.RealPayerBinIin(); ok {
		_spec.SetField(transaction.FieldRealPayerBinIin, field.TypeString, value)
	}
	if _u.mutation.RealPayerBinIinCleared() {
		_spec.ClearField(transaction.FieldRealPayerBinIin, field.TypeString)
	}
	if value, ok := _u.mutation.RealPayerIsoCountryCode(); ok {
		_spec.SetField(transaction.FieldRealPayerIsoCountryCode, field.TypeString, value)
	}
	if _u.mutation.RealPayerIsoCountryCodeCleared() {
		_spec.ClearField(transaction.FieldRealPayerIsoCountryCode, field.TypeString)
	}
	if value, ok := _u.mutation.RealPayerType(); ok {
		_spec.SetField(transaction.FieldRealPayerType, field.TypeEnum, value)
	}
	if _u.mutation.RealPayerTypeCleared() {
		_spec.ClearField(transaction.FieldRealPayerType, field.TypeEnum)
	}
	if value, ok := _u.mutation.BeneficiaryKbe(); ok {
		_spec.SetField(transaction.FieldBeneficiaryKbe, field.TypeString, value)
	}
	if _u.mutation.BeneficiaryKbeCleared() {
		_spec.ClearField(transaction.FieldBeneficiaryKbe, field.TypeString)
	}
	if value, ok := _u.mutation.BeneficiaryBinIin(); ok {
		_spec.SetField(transaction.FieldBeneficiaryBinIin, field.TypeString, value)
	}
	if _u.mutation.BeneficiaryBinIinCleared() {
		_spec.ClearField(transaction.FieldBeneficiaryBinIin, field.TypeString)
	}
	if value, ok := _u.mutation.BeneficiaryName(); ok {
		_spec.SetField(transaction.FieldBeneficiaryName, field.TypeString, value)
	}
	if _u.mutation.BeneficiaryNameCleared() {
		_spec.ClearField(transaction.FieldBeneficiaryName, field.TypeString)
	}
	if value, ok := _u.mutation.BeneficiaryType(); ok {
		_spec.SetField(transaction.FieldBeneficiaryType, field.TypeEnum, value)
	}
	if _u.mutation.BeneficiaryTypeCleared() {
		_spec.ClearField(transaction.FieldBeneficiaryType, field.TypeEnum)
	}
	if value, ok := _u.mutation.BeneficiaryAccountIban(); ok {
		_spec.SetField(transaction.FieldBeneficiaryAccountIban, field.TypeString, value)
	}
	if _u.mutation.BeneficiaryAccountIbanCleared() {
		_spec.ClearField(transaction.FieldBeneficiaryAccountIban, field.TypeString)
	}
	if value, ok := _u.mutation.BeneficiaryBankBic(); ok {
		_spec.SetField(transaction.FieldBeneficiaryBankBic, field.TypeString, value)
	}
	if _u.mutation.BeneficiaryBankBicCleared() {
		_spec.ClearField(transaction.FieldBeneficiaryBankBic, field.TypeString)
	}
	if value, ok := _u.mutation.BeneficiaryBankName(); ok {
		_spec.SetField(transaction.FieldBeneficiaryBankName, field.TypeString, value)
	}
	if _u.mutation.BeneficiaryBankNameCleared() {
		_spec.ClearField(transaction.FieldBeneficiaryBankName, field.TypeString)
	}
	if value, ok := _u.mutation.BeneficiaryIsoCountryCode(); ok {
		_spec.SetField(transaction.FieldBeneficiaryIsoCountryCode, field.TypeString, value)
	}
	if _u.mutation.BeneficiaryIsoCountryCodeCleared() {
		_spec.ClearField(transaction.FieldBeneficiaryIsoCountryCode, field.TypeString)
	}
	if value, ok := _u.mutation.RealBeneficiaryName(); ok {
		_spec.SetField(transaction.FieldRealBeneficiaryName, field.TypeString, value)
	}
	if _u.mutation.RealBeneficiaryNameCleared() {
		_spec.ClearField(transaction.FieldRealBeneficiaryName, field.TypeString)
	}
	if value, ok := _u.mutation.RealBeneficiaryBinIin(); ok {
		_spec.SetField(transaction.FieldRealBeneficiaryBinIin, field.TypeString, value)
	}
	if _u.mutation.RealBeneficiaryBinIinCleared() {
		_spec.ClearField(transaction.FieldRealBeneficiaryBinIin, field.TypeString)
	}
	if value, ok := _u.mutation.RealBeneficiaryCountryCode(); ok {
		_spec.SetField(transaction.FieldRealBeneficiaryCountryCode, field.TypeString, value)
	}
	if _u.mutation.RealBeneficiaryCountryCodeCleared() {
		_spec.ClearField(transaction.FieldRealBeneficiaryCountryCode, field.TypeString)
	}
	if value, ok := _u.mutation.RealBeneficiaryType(); ok {
		_spec.SetField(transaction.FieldRealBeneficiaryType, field.TypeEnum, value)
	}
	if _u.mutation.RealBeneficiaryTypeCleared() {
		_spec.ClearField(transaction.FieldRealBeneficiaryType, field.TypeEnum)
	}
	_spec.AddModifiers(_u.modifiers...)
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{transaction.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// TransactionUpdateOne is the builder for updating a single Transaction entity.
type TransactionUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *TransactionMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetTransactionNumber sets the "transaction_number" field.
func (_u *TransactionUpdateOne) SetTransactionNumber(v string) *TransactionUpdateOne {
	_u.mutation.SetTransactionNumber(v)
	return _u
}

// SetNillableTransactionNumber sets the "transaction_number" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableTransactionNumber(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetTransactionNumber(*v)
	}
	return _u
}

// SetTransactionDate sets the "transaction_date" field.
func (_u *TransactionUpdateOne) SetTransactionDate(v time.Time) *TransactionUpdateOne {
	_u.mutation.SetTransactionDate(v)
	return _u
}

// SetNillableTransactionDate sets the "transaction_date" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableTransactionDate(v *time.Time) *TransactionUpdateOne {
	if v != nil {
		_u.SetTransactionDate(*v)
	}
	return _u
}

// SetTransactionType sets the "transaction_type" field.
func (_u *TransactionUpdateOne) SetTransactionType(v transaction.TransactionType) *TransactionUpdateOne {
	_u.mutation.SetTransactionType(v)
	return _u
}

// SetNillableTransactionType sets the "transaction_type" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableTransactionType(v *transaction.TransactionType) *TransactionUpdateOne {
	if v != nil {
		_u.SetTransactionType(*v)
	}
	return _u
}

// SetInitiatorID sets the "initiator_id" field.
func (_u *TransactionUpdateOne) SetInitiatorID(v string) *TransactionUpdateOne {
	_u.mutation.SetInitiatorID(v)
	return _u
}

// SetNillableInitiatorID sets the "initiator_id" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableInitiatorID(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetInitiatorID(*v)
	}
	return _u
}

// SetIdempotencyKey sets the "idempotency_key" field.
func (_u *TransactionUpdateOne) SetIdempotencyKey(v string) *TransactionUpdateOne {
	_u.mutation.SetIdempotencyKey(v)
	return _u
}

// SetNillableIdempotencyKey sets the "idempotency_key" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableIdempotencyKey(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetIdempotencyKey(*v)
	}
	return _u
}

// SetValueDate sets the "value_date" field.
func (_u *TransactionUpdateOne) SetValueDate(v string) *TransactionUpdateOne {
	_u.mutation.SetValueDate(v)
	return _u
}

// SetNillableValueDate sets the "value_date" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableValueDate(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetValueDate(*v)
	}
	return _u
}

// ClearValueDate clears the value of the "value_date" field.
func (_u *TransactionUpdateOne) ClearValueDate() *TransactionUpdateOne {
	_u.mutation.ClearValueDate()
	return _u
}

// SetTransactionStatus sets the "transaction_status" field.
func (_u *TransactionUpdateOne) SetTransactionStatus(v transaction.TransactionStatus) *TransactionUpdateOne {
	_u.mutation.SetTransactionStatus(v)
	return _u
}

// SetNillableTransactionStatus sets the "transaction_status" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableTransactionStatus(v *transaction.TransactionStatus) *TransactionUpdateOne {
	if v != nil {
		_u.SetTransactionStatus(*v)
	}
	return _u
}

// SetTransactionAmount sets the "transaction_amount" field.
func (_u *TransactionUpdateOne) SetTransactionAmount(v string) *TransactionUpdateOne {
	_u.mutation.SetTransactionAmount(v)
	return _u
}

// SetNillableTransactionAmount sets the "transaction_amount" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableTransactionAmount(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetTransactionAmount(*v)
	}
	return _u
}

// SetTransactionComission sets the "transaction_comission" field.
func (_u *TransactionUpdateOne) SetTransactionComission(v string) *TransactionUpdateOne {
	_u.mutation.SetTransactionComission(v)
	return _u
}

// SetNillableTransactionComission sets the "transaction_comission" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableTransactionComission(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetTransactionComission(*v)
	}
	return _u
}

// ClearTransactionComission clears the value of the "transaction_comission" field.
func (_u *TransactionUpdateOne) ClearTransactionComission() *TransactionUpdateOne {
	_u.mutation.ClearTransactionComission()
	return _u
}

// SetTransactionCurrency sets the "transaction_currency" field.
func (_u *TransactionUpdateOne) SetTransactionCurrency(v transaction.TransactionCurrency) *TransactionUpdateOne {
	_u.mutation.SetTransactionCurrency(v)
	return _u
}

// SetNillableTransactionCurrency sets the "transaction_currency" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableTransactionCurrency(v *transaction.TransactionCurrency) *TransactionUpdateOne {
	if v != nil {
		_u.SetTransactionCurrency(*v)
	}
	return _u
}

// SetTransactionTotalAmount sets the "transaction_total_amount" field.
func (_u *TransactionUpdateOne) SetTransactionTotalAmount(v string) *TransactionUpdateOne {
	_u.mutation.SetTransactionTotalAmount(v)
	return _u
}

// SetNillableTransactionTotalAmount sets the "transaction_total_amount" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableTransactionTotalAmount(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetTransactionTotalAmount(*v)
	}
	return _u
}

// SetTransactionDirection sets the "transaction_direction" field.
func (_u *TransactionUpdateOne) SetTransactionDirection(v transaction.TransactionDirection) *TransactionUpdateOne {
	_u.mutation.SetTransactionDirection(v)
	return _u
}

// SetNillableTransactionDirection sets the "transaction_direction" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableTransactionDirection(v *transaction.TransactionDirection) *TransactionUpdateOne {
	if v != nil {
		_u.SetTransactionDirection(*v)
	}
	return _u
}

// ClearTransactionDirection clears the value of the "transaction_direction" field.
func (_u *TransactionUpdateOne) ClearTransactionDirection() *TransactionUpdateOne {
	_u.mutation.ClearTransactionDirection()
	return _u
}

// SetPurposeCode sets the "purpose_code" field.
func (_u *TransactionUpdateOne) SetPurposeCode(v string) *TransactionUpdateOne {
	_u.mutation.SetPurposeCode(v)
	return _u
}

// SetNillablePurposeCode sets the "purpose_code" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillablePurposeCode(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetPurposeCode(*v)
	}
	return _u
}

// ClearPurposeCode clears the value of the "purpose_code" field.
func (_u *TransactionUpdateOne) ClearPurposeCode() *TransactionUpdateOne {
	_u.mutation.ClearPurposeCode()
	return _u
}

// SetPurposeDetails sets the "purpose_details" field.
func (_u *TransactionUpdateOne) SetPurposeDetails(v string) *TransactionUpdateOne {
	_u.mutation.SetPurposeDetails(v)
	return _u
}

// SetNillablePurposeDetails sets the "purpose_details" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillablePurposeDetails(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetPurposeDetails(*v)
	}
	return _u
}

// ClearPurposeDetails clears the value of the "purpose_details" field.
func (_u *TransactionUpdateOne) ClearPurposeDetails() *TransactionUpdateOne {
	_u.mutation.ClearPurposeDetails()
	return _u
}

// SetPayerKod sets the "payer_kod" field.
func (_u *TransactionUpdateOne) SetPayerKod(v string) *TransactionUpdateOne {
	_u.mutation.SetPayerKod(v)
	return _u
}

// SetNillablePayerKod sets the "payer_kod" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillablePayerKod(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetPayerKod(*v)
	}
	return _u
}

// ClearPayerKod clears the value of the "payer_kod" field.
func (_u *TransactionUpdateOne) ClearPayerKod() *TransactionUpdateOne {
	_u.mutation.ClearPayerKod()
	return _u
}

// SetPayerBinIin sets the "payer_bin_iin" field.
func (_u *TransactionUpdateOne) SetPayerBinIin(v string) *TransactionUpdateOne {
	_u.mutation.SetPayerBinIin(v)
	return _u
}

// SetNillablePayerBinIin sets the "payer_bin_iin" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillablePayerBinIin(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetPayerBinIin(*v)
	}
	return _u
}

// ClearPayerBinIin clears the value of the "payer_bin_iin" field.
func (_u *TransactionUpdateOne) ClearPayerBinIin() *TransactionUpdateOne {
	_u.mutation.ClearPayerBinIin()
	return _u
}

// SetPayerName sets the "payer_name" field.
func (_u *TransactionUpdateOne) SetPayerName(v string) *TransactionUpdateOne {
	_u.mutation.SetPayerName(v)
	return _u
}

// SetNillablePayerName sets the "payer_name" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillablePayerName(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetPayerName(*v)
	}
	return _u
}

// ClearPayerName clears the value of the "payer_name" field.
func (_u *TransactionUpdateOne) ClearPayerName() *TransactionUpdateOne {
	_u.mutation.ClearPayerName()
	return _u
}

// SetPayerType sets the "payer_type" field.
func (_u *TransactionUpdateOne) SetPayerType(v transaction.PayerType) *TransactionUpdateOne {
	_u.mutation.SetPayerType(v)
	return _u
}

// SetNillablePayerType sets the "payer_type" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillablePayerType(v *transaction.PayerType) *TransactionUpdateOne {
	if v != nil {
		_u.SetPayerType(*v)
	}
	return _u
}

// ClearPayerType clears the value of the "payer_type" field.
func (_u *TransactionUpdateOne) ClearPayerType() *TransactionUpdateOne {
	_u.mutation.ClearPayerType()
	return _u
}

// SetPayerAccountIban sets the "payer_account_iban" field.
func (_u *TransactionUpdateOne) SetPayerAccountIban(v string) *TransactionUpdateOne {
	_u.mutation.SetPayerAccountIban(v)
	return _u
}

// SetNillablePayerAccountIban sets the "payer_account_iban" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillablePayerAccountIban(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetPayerAccountIban(*v)
	}
	return _u
}

// ClearPayerAccountIban clears the value of the "payer_account_iban" field.
func (_u *TransactionUpdateOne) ClearPayerAccountIban() *TransactionUpdateOne {
	_u.mutation.ClearPayerAccountIban()
	return _u
}

// SetPayerBankBic sets the "payer_bank_bic" field.
func (_u *TransactionUpdateOne) SetPayerBankBic(v string) *TransactionUpdateOne {
	_u.mutation.SetPayerBankBic(v)
	return _u
}

// SetNillablePayerBankBic sets the "payer_bank_bic" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillablePayerBankBic(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetPayerBankBic(*v)
	}
	return _u
}

// ClearPayerBankBic clears the value of the "payer_bank_bic" field.
func (_u *TransactionUpdateOne) ClearPayerBankBic() *TransactionUpdateOne {
	_u.mutation.ClearPayerBankBic()
	return _u
}

// SetPayerBankName sets the "payer_bank_name" field.
func (_u *TransactionUpdateOne) SetPayerBankName(v string) *TransactionUpdateOne {
	_u.mutation.SetPayerBankName(v)
	return _u
}

// SetNillablePayerBankName sets the "payer_bank_name" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillablePayerBankName(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetPayerBankName(*v)
	}
	return _u
}

// ClearPayerBankName clears the value of the "payer_bank_name" field.
func (_u *TransactionUpdateOne) ClearPayerBankName() *TransactionUpdateOne {
	_u.mutation.ClearPayerBankName()
	return _u
}

// SetPayerIsoCountryCode sets the "payer_iso_country_code" field.
func (_u *TransactionUpdateOne) SetPayerIsoCountryCode(v string) *TransactionUpdateOne {
	_u.mutation.SetPayerIsoCountryCode(v)
	return _u
}

// SetNillablePayerIsoCountryCode sets the "payer_iso_country_code" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillablePayerIsoCountryCode(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetPayerIsoCountryCode(*v)
	}
	return _u
}

// ClearPayerIsoCountryCode clears the value of the "payer_iso_country_code" field.
func (_u *TransactionUpdateOne) ClearPayerIsoCountryCode() *TransactionUpdateOne {
	_u.mutation.ClearPayerIsoCountryCode()
	return _u
}

// SetRealPayerName sets the "real_payer_name" field.
func (_u *TransactionUpdateOne) SetRealPayerName(v string) *TransactionUpdateOne {
	_u.mutation.SetRealPayerName(v)
	return _u
}

// SetNillableRealPayerName sets the "real_payer_name" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableRealPayerName(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetRealPayerName(*v)
	}
	return _u
}

// ClearRealPayerName clears the value of the "real_payer_name" field.
func (_u *TransactionUpdateOne) ClearRealPayerName() *TransactionUpdateOne {
	_u.mutation.ClearRealPayerName()
	return _u
}

// SetRealPayerBinIin sets the "real_payer_bin_iin" field.
func (_u *TransactionUpdateOne) SetRealPayerBinIin(v string) *TransactionUpdateOne {
	_u.mutation.SetRealPayerBinIin(v)
	return _u
}

// SetNillableRealPayerBinIin sets the "real_payer_bin_iin" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableRealPayerBinIin(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetRealPayerBinIin(*v)
	}
	return _u
}

// ClearRealPayerBinIin clears the value of the "real_payer_bin_iin" field.
func (_u *TransactionUpdateOne) ClearRealPayerBinIin() *TransactionUpdateOne {
	_u.mutation.ClearRealPayerBinIin()
	return _u
}

// SetRealPayerIsoCountryCode sets the "real_payer_iso_country_code" field.
func (_u *TransactionUpdateOne) SetRealPayerIsoCountryCode(v string) *TransactionUpdateOne {
	_u.mutation.SetRealPayerIsoCountryCode(v)
	return _u
}

// SetNillableRealPayerIsoCountryCode sets the "real_payer_iso_country_code" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableRealPayerIsoCountryCode(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetRealPayerIsoCountryCode(*v)
	}
	return _u
}

// ClearRealPayerIsoCountryCode clears the value of the "real_payer_iso_country_code" field.
func (_u *TransactionUpdateOne) ClearRealPayerIsoCountryCode() *TransactionUpdateOne {
	_u.mutation.ClearRealPayerIsoCountryCode()
	return _u
}

// SetRealPayerType sets the "real_payer_type" field.
func (_u *TransactionUpdateOne) SetRealPayerType(v transaction.RealPayerType) *TransactionUpdateOne {
	_u.mutation.SetRealPayerType(v)
	return _u
}

// SetNillableRealPayerType sets the "real_payer_type" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableRealPayerType(v *transaction.RealPayerType) *TransactionUpdateOne {
	if v != nil {
		_u.SetRealPayerType(*v)
	}
	return _u
}

// ClearRealPayerType clears the value of the "real_payer_type" field.
func (_u *TransactionUpdateOne) ClearRealPayerType() *TransactionUpdateOne {
	_u.mutation.ClearRealPayerType()
	return _u
}

// SetBeneficiaryKbe sets the "beneficiary_kbe" field.
func (_u *TransactionUpdateOne) SetBeneficiaryKbe(v string) *TransactionUpdateOne {
	_u.mutation.SetBeneficiaryKbe(v)
	return _u
}

// SetNillableBeneficiaryKbe sets the "beneficiary_kbe" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableBeneficiaryKbe(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetBeneficiaryKbe(*v)
	}
	return _u
}

// ClearBeneficiaryKbe clears the value of the "beneficiary_kbe" field.
func (_u *TransactionUpdateOne) ClearBeneficiaryKbe() *TransactionUpdateOne {
	_u.mutation.ClearBeneficiaryKbe()
	return _u
}

// SetBeneficiaryBinIin sets the "beneficiary_bin_iin" field.
func (_u *TransactionUpdateOne) SetBeneficiaryBinIin(v string) *TransactionUpdateOne {
	_u.mutation.SetBeneficiaryBinIin(v)
	return _u
}

// SetNillableBeneficiaryBinIin sets the "beneficiary_bin_iin" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableBeneficiaryBinIin(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetBeneficiaryBinIin(*v)
	}
	return _u
}

// ClearBeneficiaryBinIin clears the value of the "beneficiary_bin_iin" field.
func (_u *TransactionUpdateOne) ClearBeneficiaryBinIin() *TransactionUpdateOne {
	_u.mutation.ClearBeneficiaryBinIin()
	return _u
}

// SetBeneficiaryName sets the "beneficiary_name" field.
func (_u *TransactionUpdateOne) SetBeneficiaryName(v string) *TransactionUpdateOne {
	_u.mutation.SetBeneficiaryName(v)
	return _u
}

// SetNillableBeneficiaryName sets the "beneficiary_name" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableBeneficiaryName(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetBeneficiaryName(*v)
	}
	return _u
}

// ClearBeneficiaryName clears the value of the "beneficiary_name" field.
func (_u *TransactionUpdateOne) ClearBeneficiaryName() *TransactionUpdateOne {
	_u.mutation.ClearBeneficiaryName()
	return _u
}

// SetBeneficiaryType sets the "beneficiary_type" field.
func (_u *TransactionUpdateOne) SetBeneficiaryType(v transaction.BeneficiaryType) *TransactionUpdateOne {
	_u.mutation.SetBeneficiaryType(v)
	return _u
}

// SetNillableBeneficiaryType sets the "beneficiary_type" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableBeneficiaryType(v *transaction.BeneficiaryType) *TransactionUpdateOne {
	if v != nil {
		_u.SetBeneficiaryType(*v)
	}
	return _u
}

// ClearBeneficiaryType clears the value of the "beneficiary_type" field.
func (_u *TransactionUpdateOne) ClearBeneficiaryType() *TransactionUpdateOne {
	_u.mutation.ClearBeneficiaryType()
	return _u
}

// SetBeneficiaryAccountIban sets the "beneficiary_account_iban" field.
func (_u *TransactionUpdateOne) SetBeneficiaryAccountIban(v string) *TransactionUpdateOne {
	_u.mutation.SetBeneficiaryAccountIban(v)
	return _u
}

// SetNillableBeneficiaryAccountIban sets the "beneficiary_account_iban" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableBeneficiaryAccountIban(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetBeneficiaryAccountIban(*v)
	}
	return _u
}

// ClearBeneficiaryAccountIban clears the value of the "beneficiary_account_iban" field.
func (_u *TransactionUpdateOne) ClearBeneficiaryAccountIban() *TransactionUpdateOne {
	_u.mutation.ClearBeneficiaryAccountIban()
	return _u
}

// SetBeneficiaryBankBic sets the "beneficiary_bank_bic" field.
func (_u *TransactionUpdateOne) SetBeneficiaryBankBic(v string) *TransactionUpdateOne {
	_u.mutation.SetBeneficiaryBankBic(v)
	return _u
}

// SetNillableBeneficiaryBankBic sets the "beneficiary_bank_bic" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableBeneficiaryBankBic(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetBeneficiaryBankBic(*v)
	}
	return _u
}

// ClearBeneficiaryBankBic clears the value of the "beneficiary_bank_bic" field.
func (_u *TransactionUpdateOne) ClearBeneficiaryBankBic() *TransactionUpdateOne {
	_u.mutation.ClearBeneficiaryBankBic()
	return _u
}

// SetBeneficiaryBankName sets the "beneficiary_bank_name" field.
func (_u *TransactionUpdateOne) SetBeneficiaryBankName(v string) *TransactionUpdateOne {
	_u.mutation.SetBeneficiaryBankName(v)
	return _u
}

// SetNillableBeneficiaryBankName sets the "beneficiary_bank_name" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableBeneficiaryBankName(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetBeneficiaryBankName(*v)
	}
	return _u
}

// ClearBeneficiaryBankName clears the value of the "beneficiary_bank_name" field.
func (_u *TransactionUpdateOne) ClearBeneficiaryBankName() *TransactionUpdateOne {
	_u.mutation.ClearBeneficiaryBankName()
	return _u
}

// SetBeneficiaryIsoCountryCode sets the "beneficiary_iso_country_code" field.
func (_u *TransactionUpdateOne) SetBeneficiaryIsoCountryCode(v string) *TransactionUpdateOne {
	_u.mutation.SetBeneficiaryIsoCountryCode(v)
	return _u
}

// SetNillableBeneficiaryIsoCountryCode sets the "beneficiary_iso_country_code" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableBeneficiaryIsoCountryCode(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetBeneficiaryIsoCountryCode(*v)
	}
	return _u
}

// ClearBeneficiaryIsoCountryCode clears the value of the "beneficiary_iso_country_code" field.
func (_u *TransactionUpdateOne) ClearBeneficiaryIsoCountryCode() *TransactionUpdateOne {
	_u.mutation.ClearBeneficiaryIsoCountryCode()
	return _u
}

// SetRealBeneficiaryName sets the "real_beneficiary_name" field.
func (_u *TransactionUpdateOne) SetRealBeneficiaryName(v string) *TransactionUpdateOne {
	_u.mutation.SetRealBeneficiaryName(v)
	return _u
}

// SetNillableRealBeneficiaryName sets the "real_beneficiary_name" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableRealBeneficiaryName(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetRealBeneficiaryName(*v)
	}
	return _u
}

// ClearRealBeneficiaryName clears the value of the "real_beneficiary_name" field.
func (_u *TransactionUpdateOne) ClearRealBeneficiaryName() *TransactionUpdateOne {
	_u.mutation.ClearRealBeneficiaryName()
	return _u
}

// SetRealBeneficiaryBinIin sets the "real_beneficiary_bin_iin" field.
func (_u *TransactionUpdateOne) SetRealBeneficiaryBinIin(v string) *TransactionUpdateOne {
	_u.mutation.SetRealBeneficiaryBinIin(v)
	return _u
}

// SetNillableRealBeneficiaryBinIin sets the "real_beneficiary_bin_iin" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableRealBeneficiaryBinIin(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetRealBeneficiaryBinIin(*v)
	}
	return _u
}

// ClearRealBeneficiaryBinIin clears the value of the "real_beneficiary_bin_iin" field.
func (_u *TransactionUpdateOne) ClearRealBeneficiaryBinIin() *TransactionUpdateOne {
	_u.mutation.ClearRealBeneficiaryBinIin()
	return _u
}

// SetRealBeneficiaryCountryCode sets the "real_beneficiary_country_code" field.
func (_u *TransactionUpdateOne) SetRealBeneficiaryCountryCode(v string) *TransactionUpdateOne {
	_u.mutation.SetRealBeneficiaryCountryCode(v)
	return _u
}

// SetNillableRealBeneficiaryCountryCode sets the "real_beneficiary_country_code" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableRealBeneficiaryCountryCode(v *string) *TransactionUpdateOne {
	if v != nil {
		_u.SetRealBeneficiaryCountryCode(*v)
	}
	return _u
}

// ClearRealBeneficiaryCountryCode clears the value of the "real_beneficiary_country_code" field.
func (_u *TransactionUpdateOne) ClearRealBeneficiaryCountryCode() *TransactionUpdateOne {
	_u.mutation.ClearRealBeneficiaryCountryCode()
	return _u
}

// SetRealBeneficiaryType sets the "real_beneficiary_type" field.
func (_u *TransactionUpdateOne) SetRealBeneficiaryType(v transaction.RealBeneficiaryType) *TransactionUpdateOne {
	_u.mutation.SetRealBeneficiaryType(v)
	return _u
}

// SetNillableRealBeneficiaryType sets the "real_beneficiary_type" field if the given value is not nil.
func (_u *TransactionUpdateOne) SetNillableRealBeneficiaryType(v *transaction.RealBeneficiaryType) *TransactionUpdateOne {
	if v != nil {
		_u.SetRealBeneficiaryType(*v)
	}
	return _u
}

// ClearRealBeneficiaryType clears the value of the "real_beneficiary_type" field.
func (_u *TransactionUpdateOne) ClearRealBeneficiaryType() *TransactionUpdateOne {
	_u.mutation.ClearRealBeneficiaryType()
	return _u
}

// Mutation returns the TransactionMutation object of the builder.
func (_u *TransactionUpdateOne) Mutation() *TransactionMutation {
	return _u.mutation
}

// Where appends a list predicates to the TransactionUpdate builder.
func (_u *TransactionUpdateOne) Where(ps ...predicate.Transaction) *TransactionUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *TransactionUpdateOne) Select(field string, fields ...string) *TransactionUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated Transaction entity.
func (_u *TransactionUpdateOne) Save(ctx context.Context) (*Transaction, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *TransactionUpdateOne) SaveX(ctx context.Context) *Transaction {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *TransactionUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *TransactionUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *TransactionUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := transaction.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *TransactionUpdateOne) check() error {
	if v, ok := _u.mutation.TransactionType(); ok {
		if err := transaction.TransactionTypeValidator(v); err != nil {
			return &ValidationError{Name: "transaction_type", err: fmt.Errorf(`ent: validator failed for field "Transaction.transaction_type": %w`, err)}
		}
	}
	if v, ok := _u.mutation.TransactionStatus(); ok {
		if err := transaction.TransactionStatusValidator(v); err != nil {
			return &ValidationError{Name: "transaction_status", err: fmt.Errorf(`ent: validator failed for field "Transaction.transaction_status": %w`, err)}
		}
	}
	if v, ok := _u.mutation.TransactionCurrency(); ok {
		if err := transaction.TransactionCurrencyValidator(v); err != nil {
			return &ValidationError{Name: "transaction_currency", err: fmt.Errorf(`ent: validator failed for field "Transaction.transaction_currency": %w`, err)}
		}
	}
	if v, ok := _u.mutation.TransactionDirection(); ok {
		if err := transaction.TransactionDirectionValidator(v); err != nil {
			return &ValidationError{Name: "transaction_direction", err: fmt.Errorf(`ent: validator failed for field "Transaction.transaction_direction": %w`, err)}
		}
	}
	if v, ok := _u.mutation.PayerType(); ok {
		if err := transaction.PayerTypeValidator(v); err != nil {
			return &ValidationError{Name: "payer_type", err: fmt.Errorf(`ent: validator failed for field "Transaction.payer_type": %w`, err)}
		}
	}
	if v, ok := _u.mutation.RealPayerType(); ok {
		if err := transaction.RealPayerTypeValidator(v); err != nil {
			return &ValidationError{Name: "real_payer_type", err: fmt.Errorf(`ent: validator failed for field "Transaction.real_payer_type": %w`, err)}
		}
	}
	if v, ok := _u.mutation.BeneficiaryType(); ok {
		if err := transaction.BeneficiaryTypeValidator(v); err != nil {
			return &ValidationError{Name: "beneficiary_type", err: fmt.Errorf(`ent: validator failed for field "Transaction.beneficiary_type": %w`, err)}
		}
	}
	if v, ok := _u.mutation.RealBeneficiaryType(); ok {
		if err := transaction.RealBeneficiaryTypeValidator(v); err != nil {
			return &ValidationError{Name: "real_beneficiary_type", err: fmt.Errorf(`ent: validator failed for field "Transaction.real_beneficiary_type": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *TransactionUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *TransactionUpdateOne {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *TransactionUpdateOne) sqlSave(ctx context.Context) (_node *Transaction, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(transaction.Table, transaction.Columns, sqlgraph.NewFieldSpec(transaction.FieldID, field.TypeUUID))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Transaction.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, transaction.FieldID)
		for _, f := range fields {
			if !transaction.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != transaction.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(transaction.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(transaction.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(transaction.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.TransactionNumber(); ok {
		_spec.SetField(transaction.FieldTransactionNumber, field.TypeString, value)
	}
	if value, ok := _u.mutation.TransactionDate(); ok {
		_spec.SetField(transaction.FieldTransactionDate, field.TypeTime, value)
	}
	if value, ok := _u.mutation.TransactionType(); ok {
		_spec.SetField(transaction.FieldTransactionType, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.InitiatorID(); ok {
		_spec.SetField(transaction.FieldInitiatorID, field.TypeString, value)
	}
	if value, ok := _u.mutation.IdempotencyKey(); ok {
		_spec.SetField(transaction.FieldIdempotencyKey, field.TypeString, value)
	}
	if value, ok := _u.mutation.ValueDate(); ok {
		_spec.SetField(transaction.FieldValueDate, field.TypeString, value)
	}
	if _u.mutation.ValueDateCleared() {
		_spec.ClearField(transaction.FieldValueDate, field.TypeString)
	}
	if value, ok := _u.mutation.TransactionStatus(); ok {
		_spec.SetField(transaction.FieldTransactionStatus, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.TransactionAmount(); ok {
		_spec.SetField(transaction.FieldTransactionAmount, field.TypeString, value)
	}
	if value, ok := _u.mutation.TransactionComission(); ok {
		_spec.SetField(transaction.FieldTransactionComission, field.TypeString, value)
	}
	if _u.mutation.TransactionComissionCleared() {
		_spec.ClearField(transaction.FieldTransactionComission, field.TypeString)
	}
	if value, ok := _u.mutation.TransactionCurrency(); ok {
		_spec.SetField(transaction.FieldTransactionCurrency, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.TransactionTotalAmount(); ok {
		_spec.SetField(transaction.FieldTransactionTotalAmount, field.TypeString, value)
	}
	if value, ok := _u.mutation.TransactionDirection(); ok {
		_spec.SetField(transaction.FieldTransactionDirection, field.TypeEnum, value)
	}
	if _u.mutation.TransactionDirectionCleared() {
		_spec.ClearField(transaction.FieldTransactionDirection, field.TypeEnum)
	}
	if value, ok := _u.mutation.PurposeCode(); ok {
		_spec.SetField(transaction.FieldPurposeCode, field.TypeString, value)
	}
	if _u.mutation.PurposeCodeCleared() {
		_spec.ClearField(transaction.FieldPurposeCode, field.TypeString)
	}
	if value, ok := _u.mutation.PurposeDetails(); ok {
		_spec.SetField(transaction.FieldPurposeDetails, field.TypeString, value)
	}
	if _u.mutation.PurposeDetailsCleared() {
		_spec.ClearField(transaction.FieldPurposeDetails, field.TypeString)
	}
	if value, ok := _u.mutation.PayerKod(); ok {
		_spec.SetField(transaction.FieldPayerKod, field.TypeString, value)
	}
	if _u.mutation.PayerKodCleared() {
		_spec.ClearField(transaction.FieldPayerKod, field.TypeString)
	}
	if value, ok := _u.mutation.PayerBinIin(); ok {
		_spec.SetField(transaction.FieldPayerBinIin, field.TypeString, value)
	}
	if _u.mutation.PayerBinIinCleared() {
		_spec.ClearField(transaction.FieldPayerBinIin, field.TypeString)
	}
	if value, ok := _u.mutation.PayerName(); ok {
		_spec.SetField(transaction.FieldPayerName, field.TypeString, value)
	}
	if _u.mutation.PayerNameCleared() {
		_spec.ClearField(transaction.FieldPayerName, field.TypeString)
	}
	if value, ok := _u.mutation.PayerType(); ok {
		_spec.SetField(transaction.FieldPayerType, field.TypeEnum, value)
	}
	if _u.mutation.PayerTypeCleared() {
		_spec.ClearField(transaction.FieldPayerType, field.TypeEnum)
	}
	if value, ok := _u.mutation.PayerAccountIban(); ok {
		_spec.SetField(transaction.FieldPayerAccountIban, field.TypeString, value)
	}
	if _u.mutation.PayerAccountIbanCleared() {
		_spec.ClearField(transaction.FieldPayerAccountIban, field.TypeString)
	}
	if value, ok := _u.mutation.PayerBankBic(); ok {
		_spec.SetField(transaction.FieldPayerBankBic, field.TypeString, value)
	}
	if _u.mutation.PayerBankBicCleared() {
		_spec.ClearField(transaction.FieldPayerBankBic, field.TypeString)
	}
	if value, ok := _u.mutation.PayerBankName(); ok {
		_spec.SetField(transaction.FieldPayerBankName, field.TypeString, value)
	}
	if _u.mutation.PayerBankNameCleared() {
		_spec.ClearField(transaction.FieldPayerBankName, field.TypeString)
	}
	if value, ok := _u.mutation.PayerIsoCountryCode(); ok {
		_spec.SetField(transaction.FieldPayerIsoCountryCode, field.TypeString, value)
	}
	if _u.mutation.PayerIsoCountryCodeCleared() {
		_spec.ClearField(transaction.FieldPayerIsoCountryCode, field.TypeString)
	}
	if value, ok := _u.mutation.RealPayerName(); ok {
		_spec.SetField(transaction.FieldRealPayerName, field.TypeString, value)
	}
	if _u.mutation.RealPayerNameCleared() {
		_spec.ClearField(transaction.FieldRealPayerName, field.TypeString)
	}
	if value, ok := _u.mutation.RealPayerBinIin(); ok {
		_spec.SetField(transaction.FieldRealPayerBinIin, field.TypeString, value)
	}
	if _u.mutation.RealPayerBinIinCleared() {
		_spec.ClearField(transaction.FieldRealPayerBinIin, field.TypeString)
	}
	if value, ok := _u.mutation.RealPayerIsoCountryCode(); ok {
		_spec.SetField(transaction.FieldRealPayerIsoCountryCode, field.TypeString, value)
	}
	if _u.mutation.RealPayerIsoCountryCodeCleared() {
		_spec.ClearField(transaction.FieldRealPayerIsoCountryCode, field.TypeString)
	}
	if value, ok := _u.mutation.RealPayerType(); ok {
		_spec.SetField(transaction.FieldRealPayerType, field.TypeEnum, value)
	}
	if _u.mutation.RealPayerTypeCleared() {
		_spec.ClearField(transaction.FieldRealPayerType, field.TypeEnum)
	}
	if value, ok := _u.mutation.BeneficiaryKbe(); ok {
		_spec.SetField(transaction.FieldBeneficiaryKbe, field.TypeString, value)
	}
	if _u.mutation.BeneficiaryKbeCleared() {
		_spec.ClearField(transaction.FieldBeneficiaryKbe, field.TypeString)
	}
	if value, ok := _u.mutation.BeneficiaryBinIin(); ok {
		_spec.SetField(transaction.FieldBeneficiaryBinIin, field.TypeString, value)
	}
	if _u.mutation.BeneficiaryBinIinCleared() {
		_spec.ClearField(transaction.FieldBeneficiaryBinIin, field.TypeString)
	}
	if value, ok := _u.mutation.BeneficiaryName(); ok {
		_spec.SetField(transaction.FieldBeneficiaryName, field.TypeString, value)
	}
	if _u.mutation.BeneficiaryNameCleared() {
		_spec.ClearField(transaction.FieldBeneficiaryName, field.TypeString)
	}
	if value, ok := _u.mutation.BeneficiaryType(); ok {
		_spec.SetField(transaction.FieldBeneficiaryType, field.TypeEnum, value)
	}
	if _u.mutation.BeneficiaryTypeCleared() {
		_spec.ClearField(transaction.FieldBeneficiaryType, field.TypeEnum)
	}
	if value, ok := _u.mutation.BeneficiaryAccountIban(); ok {
		_spec.SetField(transaction.FieldBeneficiaryAccountIban, field.TypeString, value)
	}
	if _u.mutation.BeneficiaryAccountIbanCleared() {
		_spec.ClearField(transaction.FieldBeneficiaryAccountIban, field.TypeString)
	}
	if value, ok := _u.mutation.BeneficiaryBankBic(); ok {
		_spec.SetField(transaction.FieldBeneficiaryBankBic, field.TypeString, value)
	}
	if _u.mutation.BeneficiaryBankBicCleared() {
		_spec.ClearField(transaction.FieldBeneficiaryBankBic, field.TypeString)
	}
	if value, ok := _u.mutation.BeneficiaryBankName(); ok {
		_spec.SetField(transaction.FieldBeneficiaryBankName, field.TypeString, value)
	}
	if _u.mutation.BeneficiaryBankNameCleared() {
		_spec.ClearField(transaction.FieldBeneficiaryBankName, field.TypeString)
	}
	if value, ok := _u.mutation.BeneficiaryIsoCountryCode(); ok {
		_spec.SetField(transaction.FieldBeneficiaryIsoCountryCode, field.TypeString, value)
	}
	if _u.mutation.BeneficiaryIsoCountryCodeCleared() {
		_spec.ClearField(transaction.FieldBeneficiaryIsoCountryCode, field.TypeString)
	}
	if value, ok := _u.mutation.RealBeneficiaryName(); ok {
		_spec.SetField(transaction.FieldRealBeneficiaryName, field.TypeString, value)
	}
	if _u.mutation.RealBeneficiaryNameCleared() {
		_spec.ClearField(transaction.FieldRealBeneficiaryName, field.TypeString)
	}
	if value, ok := _u.mutation.RealBeneficiaryBinIin(); ok {
		_spec.SetField(transaction.FieldRealBeneficiaryBinIin, field.TypeString, value)
	}
	if _u.mutation.RealBeneficiaryBinIinCleared() {
		_spec.ClearField(transaction.FieldRealBeneficiaryBinIin, field.TypeString)
	}
	if value, ok := _u.mutation.RealBeneficiaryCountryCode(); ok {
		_spec.SetField(transaction.FieldRealBeneficiaryCountryCode, field.TypeString, value)
	}
	if _u.mutation.RealBeneficiaryCountryCodeCleared() {
		_spec.ClearField(transaction.FieldRealBeneficiaryCountryCode, field.TypeString)
	}
	if value, ok := _u.mutation.RealBeneficiaryType(); ok {
		_spec.SetField(transaction.FieldRealBeneficiaryType, field.TypeEnum, value)
	}
	if _u.mutation.RealBeneficiaryTypeCleared() {
		_spec.ClearField(transaction.FieldRealBeneficiaryType, field.TypeEnum)
	}
	_spec.AddModifiers(_u.modifiers...)
	_node = &Transaction{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{transaction.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
