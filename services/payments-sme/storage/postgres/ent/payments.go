// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/payments"
)

// Payments is the model entity for the Payments schema.
type Payments struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// CreateTime holds the value of the "create_time" field.
	CreateTime time.Time `json:"create_time,omitempty"`
	// UpdateTime holds the value of the "update_time" field.
	UpdateTime time.Time `json:"update_time,omitempty"`
	// PaymentCode holds the value of the "payment_code" field.
	PaymentCode string `json:"payment_code,omitempty"`
	// PaymentType holds the value of the "payment_type" field.
	PaymentType *payments.PaymentType `json:"payment_type,omitempty"`
	// YYYYMM
	PaymentPeriod string `json:"payment_period,omitempty"`
	// Kbk holds the value of the "kbk" field.
	Kbk string `json:"kbk,omitempty"`
	// EmployeeList holds the value of the "employee_list" field.
	EmployeeList map[string]interface{} `json:"employee_list,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Payments) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case payments.FieldEmployeeList:
			values[i] = new([]byte)
		case payments.FieldPaymentCode, payments.FieldPaymentType, payments.FieldPaymentPeriod, payments.FieldKbk:
			values[i] = new(sql.NullString)
		case payments.FieldCreateTime, payments.FieldUpdateTime:
			values[i] = new(sql.NullTime)
		case payments.FieldID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Payments fields.
func (_m *Payments) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case payments.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				_m.ID = *value
			}
		case payments.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				_m.CreateTime = value.Time
			}
		case payments.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				_m.UpdateTime = value.Time
			}
		case payments.FieldPaymentCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field payment_code", values[i])
			} else if value.Valid {
				_m.PaymentCode = value.String
			}
		case payments.FieldPaymentType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field payment_type", values[i])
			} else if value.Valid {
				_m.PaymentType = new(payments.PaymentType)
				*_m.PaymentType = payments.PaymentType(value.String)
			}
		case payments.FieldPaymentPeriod:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field payment_period", values[i])
			} else if value.Valid {
				_m.PaymentPeriod = value.String
			}
		case payments.FieldKbk:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field kbk", values[i])
			} else if value.Valid {
				_m.Kbk = value.String
			}
		case payments.FieldEmployeeList:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field employee_list", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &_m.EmployeeList); err != nil {
					return fmt.Errorf("unmarshal field employee_list: %w", err)
				}
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Payments.
// This includes values selected through modifiers, order, etc.
func (_m *Payments) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// Update returns a builder for updating this Payments.
// Note that you need to call Payments.Unwrap() before calling this method if this Payments
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *Payments) Update() *PaymentsUpdateOne {
	return NewPaymentsClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the Payments entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *Payments) Unwrap() *Payments {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("ent: Payments is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *Payments) String() string {
	var builder strings.Builder
	builder.WriteString("Payments(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("create_time=")
	builder.WriteString(_m.CreateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_time=")
	builder.WriteString(_m.UpdateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("payment_code=")
	builder.WriteString(_m.PaymentCode)
	builder.WriteString(", ")
	if v := _m.PaymentType; v != nil {
		builder.WriteString("payment_type=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("payment_period=")
	builder.WriteString(_m.PaymentPeriod)
	builder.WriteString(", ")
	builder.WriteString("kbk=")
	builder.WriteString(_m.Kbk)
	builder.WriteString(", ")
	builder.WriteString("employee_list=")
	builder.WriteString(fmt.Sprintf("%v", _m.EmployeeList))
	builder.WriteByte(')')
	return builder.String()
}

// PaymentsSlice is a parsable slice of Payments.
type PaymentsSlice []*Payments
