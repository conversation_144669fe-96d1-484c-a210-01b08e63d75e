// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/payments"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/predicate"
)

// PaymentsUpdate is the builder for updating Payments entities.
type PaymentsUpdate struct {
	config
	hooks     []Hook
	mutation  *PaymentsMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the PaymentsUpdate builder.
func (_u *PaymentsUpdate) Where(ps ...predicate.Payments) *PaymentsUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetPaymentCode sets the "payment_code" field.
func (_u *PaymentsUpdate) SetPaymentCode(v string) *PaymentsUpdate {
	_u.mutation.SetPaymentCode(v)
	return _u
}

// SetNillablePaymentCode sets the "payment_code" field if the given value is not nil.
func (_u *PaymentsUpdate) SetNillablePaymentCode(v *string) *PaymentsUpdate {
	if v != nil {
		_u.SetPaymentCode(*v)
	}
	return _u
}

// SetPaymentType sets the "payment_type" field.
func (_u *PaymentsUpdate) SetPaymentType(v payments.PaymentType) *PaymentsUpdate {
	_u.mutation.SetPaymentType(v)
	return _u
}

// SetNillablePaymentType sets the "payment_type" field if the given value is not nil.
func (_u *PaymentsUpdate) SetNillablePaymentType(v *payments.PaymentType) *PaymentsUpdate {
	if v != nil {
		_u.SetPaymentType(*v)
	}
	return _u
}

// ClearPaymentType clears the value of the "payment_type" field.
func (_u *PaymentsUpdate) ClearPaymentType() *PaymentsUpdate {
	_u.mutation.ClearPaymentType()
	return _u
}

// SetPaymentPeriod sets the "payment_period" field.
func (_u *PaymentsUpdate) SetPaymentPeriod(v string) *PaymentsUpdate {
	_u.mutation.SetPaymentPeriod(v)
	return _u
}

// SetNillablePaymentPeriod sets the "payment_period" field if the given value is not nil.
func (_u *PaymentsUpdate) SetNillablePaymentPeriod(v *string) *PaymentsUpdate {
	if v != nil {
		_u.SetPaymentPeriod(*v)
	}
	return _u
}

// SetKbk sets the "kbk" field.
func (_u *PaymentsUpdate) SetKbk(v string) *PaymentsUpdate {
	_u.mutation.SetKbk(v)
	return _u
}

// SetNillableKbk sets the "kbk" field if the given value is not nil.
func (_u *PaymentsUpdate) SetNillableKbk(v *string) *PaymentsUpdate {
	if v != nil {
		_u.SetKbk(*v)
	}
	return _u
}

// SetEmployeeList sets the "employee_list" field.
func (_u *PaymentsUpdate) SetEmployeeList(v map[string]interface{}) *PaymentsUpdate {
	_u.mutation.SetEmployeeList(v)
	return _u
}

// ClearEmployeeList clears the value of the "employee_list" field.
func (_u *PaymentsUpdate) ClearEmployeeList() *PaymentsUpdate {
	_u.mutation.ClearEmployeeList()
	return _u
}

// Mutation returns the PaymentsMutation object of the builder.
func (_u *PaymentsUpdate) Mutation() *PaymentsMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *PaymentsUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *PaymentsUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *PaymentsUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *PaymentsUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *PaymentsUpdate) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := payments.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *PaymentsUpdate) check() error {
	if v, ok := _u.mutation.PaymentType(); ok {
		if err := payments.PaymentTypeValidator(v); err != nil {
			return &ValidationError{Name: "payment_type", err: fmt.Errorf(`ent: validator failed for field "Payments.payment_type": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *PaymentsUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *PaymentsUpdate {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *PaymentsUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(payments.Table, payments.Columns, sqlgraph.NewFieldSpec(payments.FieldID, field.TypeUUID))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(payments.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(payments.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(payments.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.PaymentCode(); ok {
		_spec.SetField(payments.FieldPaymentCode, field.TypeString, value)
	}
	if value, ok := _u.mutation.PaymentType(); ok {
		_spec.SetField(payments.FieldPaymentType, field.TypeEnum, value)
	}
	if _u.mutation.PaymentTypeCleared() {
		_spec.ClearField(payments.FieldPaymentType, field.TypeEnum)
	}
	if value, ok := _u.mutation.PaymentPeriod(); ok {
		_spec.SetField(payments.FieldPaymentPeriod, field.TypeString, value)
	}
	if value, ok := _u.mutation.Kbk(); ok {
		_spec.SetField(payments.FieldKbk, field.TypeString, value)
	}
	if value, ok := _u.mutation.EmployeeList(); ok {
		_spec.SetField(payments.FieldEmployeeList, field.TypeJSON, value)
	}
	if _u.mutation.EmployeeListCleared() {
		_spec.ClearField(payments.FieldEmployeeList, field.TypeJSON)
	}
	_spec.AddModifiers(_u.modifiers...)
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{payments.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// PaymentsUpdateOne is the builder for updating a single Payments entity.
type PaymentsUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *PaymentsMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetPaymentCode sets the "payment_code" field.
func (_u *PaymentsUpdateOne) SetPaymentCode(v string) *PaymentsUpdateOne {
	_u.mutation.SetPaymentCode(v)
	return _u
}

// SetNillablePaymentCode sets the "payment_code" field if the given value is not nil.
func (_u *PaymentsUpdateOne) SetNillablePaymentCode(v *string) *PaymentsUpdateOne {
	if v != nil {
		_u.SetPaymentCode(*v)
	}
	return _u
}

// SetPaymentType sets the "payment_type" field.
func (_u *PaymentsUpdateOne) SetPaymentType(v payments.PaymentType) *PaymentsUpdateOne {
	_u.mutation.SetPaymentType(v)
	return _u
}

// SetNillablePaymentType sets the "payment_type" field if the given value is not nil.
func (_u *PaymentsUpdateOne) SetNillablePaymentType(v *payments.PaymentType) *PaymentsUpdateOne {
	if v != nil {
		_u.SetPaymentType(*v)
	}
	return _u
}

// ClearPaymentType clears the value of the "payment_type" field.
func (_u *PaymentsUpdateOne) ClearPaymentType() *PaymentsUpdateOne {
	_u.mutation.ClearPaymentType()
	return _u
}

// SetPaymentPeriod sets the "payment_period" field.
func (_u *PaymentsUpdateOne) SetPaymentPeriod(v string) *PaymentsUpdateOne {
	_u.mutation.SetPaymentPeriod(v)
	return _u
}

// SetNillablePaymentPeriod sets the "payment_period" field if the given value is not nil.
func (_u *PaymentsUpdateOne) SetNillablePaymentPeriod(v *string) *PaymentsUpdateOne {
	if v != nil {
		_u.SetPaymentPeriod(*v)
	}
	return _u
}

// SetKbk sets the "kbk" field.
func (_u *PaymentsUpdateOne) SetKbk(v string) *PaymentsUpdateOne {
	_u.mutation.SetKbk(v)
	return _u
}

// SetNillableKbk sets the "kbk" field if the given value is not nil.
func (_u *PaymentsUpdateOne) SetNillableKbk(v *string) *PaymentsUpdateOne {
	if v != nil {
		_u.SetKbk(*v)
	}
	return _u
}

// SetEmployeeList sets the "employee_list" field.
func (_u *PaymentsUpdateOne) SetEmployeeList(v map[string]interface{}) *PaymentsUpdateOne {
	_u.mutation.SetEmployeeList(v)
	return _u
}

// ClearEmployeeList clears the value of the "employee_list" field.
func (_u *PaymentsUpdateOne) ClearEmployeeList() *PaymentsUpdateOne {
	_u.mutation.ClearEmployeeList()
	return _u
}

// Mutation returns the PaymentsMutation object of the builder.
func (_u *PaymentsUpdateOne) Mutation() *PaymentsMutation {
	return _u.mutation
}

// Where appends a list predicates to the PaymentsUpdate builder.
func (_u *PaymentsUpdateOne) Where(ps ...predicate.Payments) *PaymentsUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *PaymentsUpdateOne) Select(field string, fields ...string) *PaymentsUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated Payments entity.
func (_u *PaymentsUpdateOne) Save(ctx context.Context) (*Payments, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *PaymentsUpdateOne) SaveX(ctx context.Context) *Payments {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *PaymentsUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *PaymentsUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *PaymentsUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := payments.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *PaymentsUpdateOne) check() error {
	if v, ok := _u.mutation.PaymentType(); ok {
		if err := payments.PaymentTypeValidator(v); err != nil {
			return &ValidationError{Name: "payment_type", err: fmt.Errorf(`ent: validator failed for field "Payments.payment_type": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *PaymentsUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *PaymentsUpdateOne {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *PaymentsUpdateOne) sqlSave(ctx context.Context) (_node *Payments, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(payments.Table, payments.Columns, sqlgraph.NewFieldSpec(payments.FieldID, field.TypeUUID))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Payments.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, payments.FieldID)
		for _, f := range fields {
			if !payments.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != payments.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(payments.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(payments.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(payments.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.PaymentCode(); ok {
		_spec.SetField(payments.FieldPaymentCode, field.TypeString, value)
	}
	if value, ok := _u.mutation.PaymentType(); ok {
		_spec.SetField(payments.FieldPaymentType, field.TypeEnum, value)
	}
	if _u.mutation.PaymentTypeCleared() {
		_spec.ClearField(payments.FieldPaymentType, field.TypeEnum)
	}
	if value, ok := _u.mutation.PaymentPeriod(); ok {
		_spec.SetField(payments.FieldPaymentPeriod, field.TypeString, value)
	}
	if value, ok := _u.mutation.Kbk(); ok {
		_spec.SetField(payments.FieldKbk, field.TypeString, value)
	}
	if value, ok := _u.mutation.EmployeeList(); ok {
		_spec.SetField(payments.FieldEmployeeList, field.TypeJSON, value)
	}
	if _u.mutation.EmployeeListCleared() {
		_spec.ClearField(payments.FieldEmployeeList, field.TypeJSON)
	}
	_spec.AddModifiers(_u.modifiers...)
	_node = &Payments{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{payments.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
