// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/generateddocument"
)

// GeneratedDocument is the model entity for the GeneratedDocument schema.
type GeneratedDocument struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// CreateTime holds the value of the "create_time" field.
	CreateTime time.Time `json:"create_time,omitempty"`
	// UpdateTime holds the value of the "update_time" field.
	UpdateTime time.Time `json:"update_time,omitempty"`
	// ParentTransactionID holds the value of the "parent_transaction_id" field.
	ParentTransactionID uuid.UUID `json:"parent_transaction_id,omitempty"`
	// GeneratedDocumentIntegrationID holds the value of the "generated_document_integration_id" field.
	GeneratedDocumentIntegrationID uuid.UUID `json:"generated_document_integration_id,omitempty"`
	// GeneratedDocumentType holds the value of the "generated_document_type" field.
	GeneratedDocumentType string `json:"generated_document_type,omitempty"`
	selectValues          sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*GeneratedDocument) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case generateddocument.FieldGeneratedDocumentType:
			values[i] = new(sql.NullString)
		case generateddocument.FieldCreateTime, generateddocument.FieldUpdateTime:
			values[i] = new(sql.NullTime)
		case generateddocument.FieldID, generateddocument.FieldParentTransactionID, generateddocument.FieldGeneratedDocumentIntegrationID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the GeneratedDocument fields.
func (_m *GeneratedDocument) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case generateddocument.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				_m.ID = *value
			}
		case generateddocument.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				_m.CreateTime = value.Time
			}
		case generateddocument.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				_m.UpdateTime = value.Time
			}
		case generateddocument.FieldParentTransactionID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field parent_transaction_id", values[i])
			} else if value != nil {
				_m.ParentTransactionID = *value
			}
		case generateddocument.FieldGeneratedDocumentIntegrationID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field generated_document_integration_id", values[i])
			} else if value != nil {
				_m.GeneratedDocumentIntegrationID = *value
			}
		case generateddocument.FieldGeneratedDocumentType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field generated_document_type", values[i])
			} else if value.Valid {
				_m.GeneratedDocumentType = value.String
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the GeneratedDocument.
// This includes values selected through modifiers, order, etc.
func (_m *GeneratedDocument) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// Update returns a builder for updating this GeneratedDocument.
// Note that you need to call GeneratedDocument.Unwrap() before calling this method if this GeneratedDocument
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *GeneratedDocument) Update() *GeneratedDocumentUpdateOne {
	return NewGeneratedDocumentClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the GeneratedDocument entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *GeneratedDocument) Unwrap() *GeneratedDocument {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("ent: GeneratedDocument is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *GeneratedDocument) String() string {
	var builder strings.Builder
	builder.WriteString("GeneratedDocument(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("create_time=")
	builder.WriteString(_m.CreateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_time=")
	builder.WriteString(_m.UpdateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("parent_transaction_id=")
	builder.WriteString(fmt.Sprintf("%v", _m.ParentTransactionID))
	builder.WriteString(", ")
	builder.WriteString("generated_document_integration_id=")
	builder.WriteString(fmt.Sprintf("%v", _m.GeneratedDocumentIntegrationID))
	builder.WriteString(", ")
	builder.WriteString("generated_document_type=")
	builder.WriteString(_m.GeneratedDocumentType)
	builder.WriteByte(')')
	return builder.String()
}

// GeneratedDocuments is a parsable slice of GeneratedDocument.
type GeneratedDocuments []*GeneratedDocument
