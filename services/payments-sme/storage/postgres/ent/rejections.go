// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/rejections"
)

// Rejections is the model entity for the Rejections schema.
type Rejections struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// CreateTime holds the value of the "create_time" field.
	CreateTime time.Time `json:"create_time,omitempty"`
	// UpdateTime holds the value of the "update_time" field.
	UpdateTime time.Time `json:"update_time,omitempty"`
	// ParentTransactionID holds the value of the "parent_transaction_id" field.
	ParentTransactionID uuid.UUID `json:"parent_transaction_id,omitempty"`
	// RejectionSource holds the value of the "rejection_source" field.
	RejectionSource rejections.RejectionSource `json:"rejection_source,omitempty"`
	// RejectionCode holds the value of the "rejection_code" field.
	RejectionCode string `json:"rejection_code,omitempty"`
	// RejectionScore holds the value of the "rejection_score" field.
	RejectionScore string `json:"rejection_score,omitempty"`
	// RejectionReason holds the value of the "rejection_reason" field.
	RejectionReason string `json:"rejection_reason,omitempty"`
	selectValues    sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Rejections) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case rejections.FieldRejectionSource, rejections.FieldRejectionCode, rejections.FieldRejectionScore, rejections.FieldRejectionReason:
			values[i] = new(sql.NullString)
		case rejections.FieldCreateTime, rejections.FieldUpdateTime:
			values[i] = new(sql.NullTime)
		case rejections.FieldID, rejections.FieldParentTransactionID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Rejections fields.
func (_m *Rejections) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case rejections.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				_m.ID = *value
			}
		case rejections.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				_m.CreateTime = value.Time
			}
		case rejections.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				_m.UpdateTime = value.Time
			}
		case rejections.FieldParentTransactionID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field parent_transaction_id", values[i])
			} else if value != nil {
				_m.ParentTransactionID = *value
			}
		case rejections.FieldRejectionSource:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field rejection_source", values[i])
			} else if value.Valid {
				_m.RejectionSource = rejections.RejectionSource(value.String)
			}
		case rejections.FieldRejectionCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field rejection_code", values[i])
			} else if value.Valid {
				_m.RejectionCode = value.String
			}
		case rejections.FieldRejectionScore:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field rejection_score", values[i])
			} else if value.Valid {
				_m.RejectionScore = value.String
			}
		case rejections.FieldRejectionReason:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field rejection_reason", values[i])
			} else if value.Valid {
				_m.RejectionReason = value.String
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Rejections.
// This includes values selected through modifiers, order, etc.
func (_m *Rejections) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// Update returns a builder for updating this Rejections.
// Note that you need to call Rejections.Unwrap() before calling this method if this Rejections
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *Rejections) Update() *RejectionsUpdateOne {
	return NewRejectionsClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the Rejections entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *Rejections) Unwrap() *Rejections {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("ent: Rejections is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *Rejections) String() string {
	var builder strings.Builder
	builder.WriteString("Rejections(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("create_time=")
	builder.WriteString(_m.CreateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_time=")
	builder.WriteString(_m.UpdateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("parent_transaction_id=")
	builder.WriteString(fmt.Sprintf("%v", _m.ParentTransactionID))
	builder.WriteString(", ")
	builder.WriteString("rejection_source=")
	builder.WriteString(fmt.Sprintf("%v", _m.RejectionSource))
	builder.WriteString(", ")
	builder.WriteString("rejection_code=")
	builder.WriteString(_m.RejectionCode)
	builder.WriteString(", ")
	builder.WriteString("rejection_score=")
	builder.WriteString(_m.RejectionScore)
	builder.WriteString(", ")
	builder.WriteString("rejection_reason=")
	builder.WriteString(_m.RejectionReason)
	builder.WriteByte(')')
	return builder.String()
}

// RejectionsSlice is a parsable slice of Rejections.
type RejectionsSlice []*Rejections
