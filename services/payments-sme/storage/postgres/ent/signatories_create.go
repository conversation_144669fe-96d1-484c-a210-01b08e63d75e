// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/signatories"
)

// SignatoriesCreate is the builder for creating a Signatories entity.
type SignatoriesCreate struct {
	config
	mutation *SignatoriesMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetSignatoryA sets the "signatory_a" field.
func (_c *SignatoriesCreate) SetSignatoryA(v string) *SignatoriesCreate {
	_c.mutation.SetSignatoryA(v)
	return _c
}

// SetNillableSignatoryA sets the "signatory_a" field if the given value is not nil.
func (_c *SignatoriesCreate) SetNillableSignatoryA(v *string) *SignatoriesCreate {
	if v != nil {
		_c.SetSignatoryA(*v)
	}
	return _c
}

// SetSignatoryB sets the "signatory_b" field.
func (_c *SignatoriesCreate) SetSignatoryB(v string) *SignatoriesCreate {
	_c.mutation.SetSignatoryB(v)
	return _c
}

// SetNillableSignatoryB sets the "signatory_b" field if the given value is not nil.
func (_c *SignatoriesCreate) SetNillableSignatoryB(v *string) *SignatoriesCreate {
	if v != nil {
		_c.SetSignatoryB(*v)
	}
	return _c
}

// SetID sets the "id" field.
func (_c *SignatoriesCreate) SetID(v uuid.UUID) *SignatoriesCreate {
	_c.mutation.SetID(v)
	return _c
}

// Mutation returns the SignatoriesMutation object of the builder.
func (_c *SignatoriesCreate) Mutation() *SignatoriesMutation {
	return _c.mutation
}

// Save creates the Signatories in the database.
func (_c *SignatoriesCreate) Save(ctx context.Context) (*Signatories, error) {
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *SignatoriesCreate) SaveX(ctx context.Context) *Signatories {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *SignatoriesCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *SignatoriesCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *SignatoriesCreate) check() error {
	return nil
}

func (_c *SignatoriesCreate) sqlSave(ctx context.Context) (*Signatories, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *SignatoriesCreate) createSpec() (*Signatories, *sqlgraph.CreateSpec) {
	var (
		_node = &Signatories{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(signatories.Table, sqlgraph.NewFieldSpec(signatories.FieldID, field.TypeUUID))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := _c.mutation.SignatoryA(); ok {
		_spec.SetField(signatories.FieldSignatoryA, field.TypeString, value)
		_node.SignatoryA = &value
	}
	if value, ok := _c.mutation.SignatoryB(); ok {
		_spec.SetField(signatories.FieldSignatoryB, field.TypeString, value)
		_node.SignatoryB = &value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Signatories.Create().
//		SetSignatoryA(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.SignatoriesUpsert) {
//			SetSignatoryA(v+v).
//		}).
//		Exec(ctx)
func (_c *SignatoriesCreate) OnConflict(opts ...sql.ConflictOption) *SignatoriesUpsertOne {
	_c.conflict = opts
	return &SignatoriesUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Signatories.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *SignatoriesCreate) OnConflictColumns(columns ...string) *SignatoriesUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &SignatoriesUpsertOne{
		create: _c,
	}
}

type (
	// SignatoriesUpsertOne is the builder for "upsert"-ing
	//  one Signatories node.
	SignatoriesUpsertOne struct {
		create *SignatoriesCreate
	}

	// SignatoriesUpsert is the "OnConflict" setter.
	SignatoriesUpsert struct {
		*sql.UpdateSet
	}
)

// SetSignatoryA sets the "signatory_a" field.
func (u *SignatoriesUpsert) SetSignatoryA(v string) *SignatoriesUpsert {
	u.Set(signatories.FieldSignatoryA, v)
	return u
}

// UpdateSignatoryA sets the "signatory_a" field to the value that was provided on create.
func (u *SignatoriesUpsert) UpdateSignatoryA() *SignatoriesUpsert {
	u.SetExcluded(signatories.FieldSignatoryA)
	return u
}

// ClearSignatoryA clears the value of the "signatory_a" field.
func (u *SignatoriesUpsert) ClearSignatoryA() *SignatoriesUpsert {
	u.SetNull(signatories.FieldSignatoryA)
	return u
}

// SetSignatoryB sets the "signatory_b" field.
func (u *SignatoriesUpsert) SetSignatoryB(v string) *SignatoriesUpsert {
	u.Set(signatories.FieldSignatoryB, v)
	return u
}

// UpdateSignatoryB sets the "signatory_b" field to the value that was provided on create.
func (u *SignatoriesUpsert) UpdateSignatoryB() *SignatoriesUpsert {
	u.SetExcluded(signatories.FieldSignatoryB)
	return u
}

// ClearSignatoryB clears the value of the "signatory_b" field.
func (u *SignatoriesUpsert) ClearSignatoryB() *SignatoriesUpsert {
	u.SetNull(signatories.FieldSignatoryB)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Signatories.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(signatories.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *SignatoriesUpsertOne) UpdateNewValues() *SignatoriesUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(signatories.FieldID)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Signatories.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *SignatoriesUpsertOne) Ignore() *SignatoriesUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *SignatoriesUpsertOne) DoNothing() *SignatoriesUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the SignatoriesCreate.OnConflict
// documentation for more info.
func (u *SignatoriesUpsertOne) Update(set func(*SignatoriesUpsert)) *SignatoriesUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&SignatoriesUpsert{UpdateSet: update})
	}))
	return u
}

// SetSignatoryA sets the "signatory_a" field.
func (u *SignatoriesUpsertOne) SetSignatoryA(v string) *SignatoriesUpsertOne {
	return u.Update(func(s *SignatoriesUpsert) {
		s.SetSignatoryA(v)
	})
}

// UpdateSignatoryA sets the "signatory_a" field to the value that was provided on create.
func (u *SignatoriesUpsertOne) UpdateSignatoryA() *SignatoriesUpsertOne {
	return u.Update(func(s *SignatoriesUpsert) {
		s.UpdateSignatoryA()
	})
}

// ClearSignatoryA clears the value of the "signatory_a" field.
func (u *SignatoriesUpsertOne) ClearSignatoryA() *SignatoriesUpsertOne {
	return u.Update(func(s *SignatoriesUpsert) {
		s.ClearSignatoryA()
	})
}

// SetSignatoryB sets the "signatory_b" field.
func (u *SignatoriesUpsertOne) SetSignatoryB(v string) *SignatoriesUpsertOne {
	return u.Update(func(s *SignatoriesUpsert) {
		s.SetSignatoryB(v)
	})
}

// UpdateSignatoryB sets the "signatory_b" field to the value that was provided on create.
func (u *SignatoriesUpsertOne) UpdateSignatoryB() *SignatoriesUpsertOne {
	return u.Update(func(s *SignatoriesUpsert) {
		s.UpdateSignatoryB()
	})
}

// ClearSignatoryB clears the value of the "signatory_b" field.
func (u *SignatoriesUpsertOne) ClearSignatoryB() *SignatoriesUpsertOne {
	return u.Update(func(s *SignatoriesUpsert) {
		s.ClearSignatoryB()
	})
}

// Exec executes the query.
func (u *SignatoriesUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for SignatoriesCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *SignatoriesUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *SignatoriesUpsertOne) ID(ctx context.Context) (id uuid.UUID, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: SignatoriesUpsertOne.ID is not supported by MySQL driver. Use SignatoriesUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *SignatoriesUpsertOne) IDX(ctx context.Context) uuid.UUID {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// SignatoriesCreateBulk is the builder for creating many Signatories entities in bulk.
type SignatoriesCreateBulk struct {
	config
	err      error
	builders []*SignatoriesCreate
	conflict []sql.ConflictOption
}

// Save creates the Signatories entities in the database.
func (_c *SignatoriesCreateBulk) Save(ctx context.Context) ([]*Signatories, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*Signatories, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*SignatoriesMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *SignatoriesCreateBulk) SaveX(ctx context.Context) []*Signatories {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *SignatoriesCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *SignatoriesCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Signatories.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.SignatoriesUpsert) {
//			SetSignatoryA(v+v).
//		}).
//		Exec(ctx)
func (_c *SignatoriesCreateBulk) OnConflict(opts ...sql.ConflictOption) *SignatoriesUpsertBulk {
	_c.conflict = opts
	return &SignatoriesUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Signatories.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *SignatoriesCreateBulk) OnConflictColumns(columns ...string) *SignatoriesUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &SignatoriesUpsertBulk{
		create: _c,
	}
}

// SignatoriesUpsertBulk is the builder for "upsert"-ing
// a bulk of Signatories nodes.
type SignatoriesUpsertBulk struct {
	create *SignatoriesCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Signatories.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(signatories.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *SignatoriesUpsertBulk) UpdateNewValues() *SignatoriesUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(signatories.FieldID)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Signatories.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *SignatoriesUpsertBulk) Ignore() *SignatoriesUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *SignatoriesUpsertBulk) DoNothing() *SignatoriesUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the SignatoriesCreateBulk.OnConflict
// documentation for more info.
func (u *SignatoriesUpsertBulk) Update(set func(*SignatoriesUpsert)) *SignatoriesUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&SignatoriesUpsert{UpdateSet: update})
	}))
	return u
}

// SetSignatoryA sets the "signatory_a" field.
func (u *SignatoriesUpsertBulk) SetSignatoryA(v string) *SignatoriesUpsertBulk {
	return u.Update(func(s *SignatoriesUpsert) {
		s.SetSignatoryA(v)
	})
}

// UpdateSignatoryA sets the "signatory_a" field to the value that was provided on create.
func (u *SignatoriesUpsertBulk) UpdateSignatoryA() *SignatoriesUpsertBulk {
	return u.Update(func(s *SignatoriesUpsert) {
		s.UpdateSignatoryA()
	})
}

// ClearSignatoryA clears the value of the "signatory_a" field.
func (u *SignatoriesUpsertBulk) ClearSignatoryA() *SignatoriesUpsertBulk {
	return u.Update(func(s *SignatoriesUpsert) {
		s.ClearSignatoryA()
	})
}

// SetSignatoryB sets the "signatory_b" field.
func (u *SignatoriesUpsertBulk) SetSignatoryB(v string) *SignatoriesUpsertBulk {
	return u.Update(func(s *SignatoriesUpsert) {
		s.SetSignatoryB(v)
	})
}

// UpdateSignatoryB sets the "signatory_b" field to the value that was provided on create.
func (u *SignatoriesUpsertBulk) UpdateSignatoryB() *SignatoriesUpsertBulk {
	return u.Update(func(s *SignatoriesUpsert) {
		s.UpdateSignatoryB()
	})
}

// ClearSignatoryB clears the value of the "signatory_b" field.
func (u *SignatoriesUpsertBulk) ClearSignatoryB() *SignatoriesUpsertBulk {
	return u.Update(func(s *SignatoriesUpsert) {
		s.ClearSignatoryB()
	})
}

// Exec executes the query.
func (u *SignatoriesUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the SignatoriesCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for SignatoriesCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *SignatoriesUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
