// Code generated by ent, DO NOT EDIT.

package ent

import (
	"time"

	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/abstransactiondocuments"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/employees"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/generateddocument"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/health"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/payments"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/rejections"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/transaction"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/schema"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	abstransactiondocumentsMixin := schema.AbsTransactionDocuments{}.Mixin()
	abstransactiondocumentsMixinFields0 := abstransactiondocumentsMixin[0].Fields()
	_ = abstransactiondocumentsMixinFields0
	abstransactiondocumentsFields := schema.AbsTransactionDocuments{}.Fields()
	_ = abstransactiondocumentsFields
	// abstransactiondocumentsDescCreateTime is the schema descriptor for create_time field.
	abstransactiondocumentsDescCreateTime := abstransactiondocumentsMixinFields0[0].Descriptor()
	// abstransactiondocuments.DefaultCreateTime holds the default value on creation for the create_time field.
	abstransactiondocuments.DefaultCreateTime = abstransactiondocumentsDescCreateTime.Default.(func() time.Time)
	// abstransactiondocumentsDescUpdateTime is the schema descriptor for update_time field.
	abstransactiondocumentsDescUpdateTime := abstransactiondocumentsMixinFields0[1].Descriptor()
	// abstransactiondocuments.DefaultUpdateTime holds the default value on creation for the update_time field.
	abstransactiondocuments.DefaultUpdateTime = abstransactiondocumentsDescUpdateTime.Default.(func() time.Time)
	// abstransactiondocuments.UpdateDefaultUpdateTime holds the default value on update for the update_time field.
	abstransactiondocuments.UpdateDefaultUpdateTime = abstransactiondocumentsDescUpdateTime.UpdateDefault.(func() time.Time)
	employeesMixin := schema.Employees{}.Mixin()
	employeesMixinFields0 := employeesMixin[0].Fields()
	_ = employeesMixinFields0
	employeesFields := schema.Employees{}.Fields()
	_ = employeesFields
	// employeesDescCreateTime is the schema descriptor for create_time field.
	employeesDescCreateTime := employeesMixinFields0[0].Descriptor()
	// employees.DefaultCreateTime holds the default value on creation for the create_time field.
	employees.DefaultCreateTime = employeesDescCreateTime.Default.(func() time.Time)
	// employeesDescUpdateTime is the schema descriptor for update_time field.
	employeesDescUpdateTime := employeesMixinFields0[1].Descriptor()
	// employees.DefaultUpdateTime holds the default value on creation for the update_time field.
	employees.DefaultUpdateTime = employeesDescUpdateTime.Default.(func() time.Time)
	// employees.UpdateDefaultUpdateTime holds the default value on update for the update_time field.
	employees.UpdateDefaultUpdateTime = employeesDescUpdateTime.UpdateDefault.(func() time.Time)
	// employeesDescID is the schema descriptor for id field.
	employeesDescID := employeesFields[0].Descriptor()
	// employees.DefaultID holds the default value on creation for the id field.
	employees.DefaultID = employeesDescID.Default.(func() uuid.UUID)
	generateddocumentMixin := schema.GeneratedDocument{}.Mixin()
	generateddocumentMixinFields0 := generateddocumentMixin[0].Fields()
	_ = generateddocumentMixinFields0
	generateddocumentFields := schema.GeneratedDocument{}.Fields()
	_ = generateddocumentFields
	// generateddocumentDescCreateTime is the schema descriptor for create_time field.
	generateddocumentDescCreateTime := generateddocumentMixinFields0[0].Descriptor()
	// generateddocument.DefaultCreateTime holds the default value on creation for the create_time field.
	generateddocument.DefaultCreateTime = generateddocumentDescCreateTime.Default.(func() time.Time)
	// generateddocumentDescUpdateTime is the schema descriptor for update_time field.
	generateddocumentDescUpdateTime := generateddocumentMixinFields0[1].Descriptor()
	// generateddocument.DefaultUpdateTime holds the default value on creation for the update_time field.
	generateddocument.DefaultUpdateTime = generateddocumentDescUpdateTime.Default.(func() time.Time)
	// generateddocument.UpdateDefaultUpdateTime holds the default value on update for the update_time field.
	generateddocument.UpdateDefaultUpdateTime = generateddocumentDescUpdateTime.UpdateDefault.(func() time.Time)
	// generateddocumentDescID is the schema descriptor for id field.
	generateddocumentDescID := generateddocumentFields[0].Descriptor()
	// generateddocument.DefaultID holds the default value on creation for the id field.
	generateddocument.DefaultID = generateddocumentDescID.Default.(func() uuid.UUID)
	healthFields := schema.Health{}.Fields()
	_ = healthFields
	// healthDescID is the schema descriptor for id field.
	healthDescID := healthFields[0].Descriptor()
	// health.DefaultID holds the default value on creation for the id field.
	health.DefaultID = healthDescID.Default.(func() uuid.UUID)
	paymentsMixin := schema.Payments{}.Mixin()
	paymentsMixinFields0 := paymentsMixin[0].Fields()
	_ = paymentsMixinFields0
	paymentsFields := schema.Payments{}.Fields()
	_ = paymentsFields
	// paymentsDescCreateTime is the schema descriptor for create_time field.
	paymentsDescCreateTime := paymentsMixinFields0[0].Descriptor()
	// payments.DefaultCreateTime holds the default value on creation for the create_time field.
	payments.DefaultCreateTime = paymentsDescCreateTime.Default.(func() time.Time)
	// paymentsDescUpdateTime is the schema descriptor for update_time field.
	paymentsDescUpdateTime := paymentsMixinFields0[1].Descriptor()
	// payments.DefaultUpdateTime holds the default value on creation for the update_time field.
	payments.DefaultUpdateTime = paymentsDescUpdateTime.Default.(func() time.Time)
	// payments.UpdateDefaultUpdateTime holds the default value on update for the update_time field.
	payments.UpdateDefaultUpdateTime = paymentsDescUpdateTime.UpdateDefault.(func() time.Time)
	rejectionsMixin := schema.Rejections{}.Mixin()
	rejectionsMixinFields0 := rejectionsMixin[0].Fields()
	_ = rejectionsMixinFields0
	rejectionsFields := schema.Rejections{}.Fields()
	_ = rejectionsFields
	// rejectionsDescCreateTime is the schema descriptor for create_time field.
	rejectionsDescCreateTime := rejectionsMixinFields0[0].Descriptor()
	// rejections.DefaultCreateTime holds the default value on creation for the create_time field.
	rejections.DefaultCreateTime = rejectionsDescCreateTime.Default.(func() time.Time)
	// rejectionsDescUpdateTime is the schema descriptor for update_time field.
	rejectionsDescUpdateTime := rejectionsMixinFields0[1].Descriptor()
	// rejections.DefaultUpdateTime holds the default value on creation for the update_time field.
	rejections.DefaultUpdateTime = rejectionsDescUpdateTime.Default.(func() time.Time)
	// rejections.UpdateDefaultUpdateTime holds the default value on update for the update_time field.
	rejections.UpdateDefaultUpdateTime = rejectionsDescUpdateTime.UpdateDefault.(func() time.Time)
	transactionMixin := schema.Transaction{}.Mixin()
	transactionMixinFields0 := transactionMixin[0].Fields()
	_ = transactionMixinFields0
	transactionFields := schema.Transaction{}.Fields()
	_ = transactionFields
	// transactionDescCreateTime is the schema descriptor for create_time field.
	transactionDescCreateTime := transactionMixinFields0[0].Descriptor()
	// transaction.DefaultCreateTime holds the default value on creation for the create_time field.
	transaction.DefaultCreateTime = transactionDescCreateTime.Default.(func() time.Time)
	// transactionDescUpdateTime is the schema descriptor for update_time field.
	transactionDescUpdateTime := transactionMixinFields0[1].Descriptor()
	// transaction.DefaultUpdateTime holds the default value on creation for the update_time field.
	transaction.DefaultUpdateTime = transactionDescUpdateTime.Default.(func() time.Time)
	// transaction.UpdateDefaultUpdateTime holds the default value on update for the update_time field.
	transaction.UpdateDefaultUpdateTime = transactionDescUpdateTime.UpdateDefault.(func() time.Time)
	// transactionDescTransactionDate is the schema descriptor for transaction_date field.
	transactionDescTransactionDate := transactionFields[2].Descriptor()
	// transaction.DefaultTransactionDate holds the default value on creation for the transaction_date field.
	transaction.DefaultTransactionDate = transactionDescTransactionDate.Default.(func() time.Time)
	// transactionDescID is the schema descriptor for id field.
	transactionDescID := transactionFields[0].Descriptor()
	// transaction.DefaultID holds the default value on creation for the id field.
	transaction.DefaultID = transactionDescID.Default.(func() uuid.UUID)
}
