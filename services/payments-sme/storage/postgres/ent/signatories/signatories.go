// Code generated by ent, DO NOT EDIT.

package signatories

import (
	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the signatories type in the database.
	Label = "signatories"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldSignatoryA holds the string denoting the signatory_a field in the database.
	FieldSignatoryA = "signatory_a"
	// FieldSignatoryB holds the string denoting the signatory_b field in the database.
	FieldSignatoryB = "signatory_b"
	// Table holds the table name of the signatories in the database.
	Table = "signatories"
)

// Columns holds all SQL columns for signatories fields.
var Columns = []string{
	FieldID,
	FieldSignatoryA,
	FieldSignatoryB,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// OrderOption defines the ordering options for the Signatories queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// BySignatoryA orders the results by the signatory_a field.
func BySignatoryA(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSignatoryA, opts...).ToFunc()
}

// BySignatoryB orders the results by the signatory_b field.
func BySignatoryB(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSignatoryB, opts...).ToFunc()
}
