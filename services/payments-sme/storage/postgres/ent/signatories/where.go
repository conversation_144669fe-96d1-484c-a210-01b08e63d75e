// Code generated by ent, DO NOT EDIT.

package signatories

import (
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.Signatories {
	return predicate.Signatories(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.Signatories {
	return predicate.Signatories(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.Signatories {
	return predicate.Signatories(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.Signatories {
	return predicate.Signatories(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.Signatories {
	return predicate.Signatories(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.Signatories {
	return predicate.Signatories(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.Signatories {
	return predicate.Signatories(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.Signatories {
	return predicate.Signatories(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.Signatories {
	return predicate.Signatories(sql.FieldLTE(FieldID, id))
}

// SignatoryA applies equality check predicate on the "signatory_a" field. It's identical to SignatoryAEQ.
func SignatoryA(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldEQ(FieldSignatoryA, v))
}

// SignatoryB applies equality check predicate on the "signatory_b" field. It's identical to SignatoryBEQ.
func SignatoryB(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldEQ(FieldSignatoryB, v))
}

// SignatoryAEQ applies the EQ predicate on the "signatory_a" field.
func SignatoryAEQ(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldEQ(FieldSignatoryA, v))
}

// SignatoryANEQ applies the NEQ predicate on the "signatory_a" field.
func SignatoryANEQ(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldNEQ(FieldSignatoryA, v))
}

// SignatoryAIn applies the In predicate on the "signatory_a" field.
func SignatoryAIn(vs ...string) predicate.Signatories {
	return predicate.Signatories(sql.FieldIn(FieldSignatoryA, vs...))
}

// SignatoryANotIn applies the NotIn predicate on the "signatory_a" field.
func SignatoryANotIn(vs ...string) predicate.Signatories {
	return predicate.Signatories(sql.FieldNotIn(FieldSignatoryA, vs...))
}

// SignatoryAGT applies the GT predicate on the "signatory_a" field.
func SignatoryAGT(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldGT(FieldSignatoryA, v))
}

// SignatoryAGTE applies the GTE predicate on the "signatory_a" field.
func SignatoryAGTE(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldGTE(FieldSignatoryA, v))
}

// SignatoryALT applies the LT predicate on the "signatory_a" field.
func SignatoryALT(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldLT(FieldSignatoryA, v))
}

// SignatoryALTE applies the LTE predicate on the "signatory_a" field.
func SignatoryALTE(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldLTE(FieldSignatoryA, v))
}

// SignatoryAContains applies the Contains predicate on the "signatory_a" field.
func SignatoryAContains(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldContains(FieldSignatoryA, v))
}

// SignatoryAHasPrefix applies the HasPrefix predicate on the "signatory_a" field.
func SignatoryAHasPrefix(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldHasPrefix(FieldSignatoryA, v))
}

// SignatoryAHasSuffix applies the HasSuffix predicate on the "signatory_a" field.
func SignatoryAHasSuffix(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldHasSuffix(FieldSignatoryA, v))
}

// SignatoryAIsNil applies the IsNil predicate on the "signatory_a" field.
func SignatoryAIsNil() predicate.Signatories {
	return predicate.Signatories(sql.FieldIsNull(FieldSignatoryA))
}

// SignatoryANotNil applies the NotNil predicate on the "signatory_a" field.
func SignatoryANotNil() predicate.Signatories {
	return predicate.Signatories(sql.FieldNotNull(FieldSignatoryA))
}

// SignatoryAEqualFold applies the EqualFold predicate on the "signatory_a" field.
func SignatoryAEqualFold(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldEqualFold(FieldSignatoryA, v))
}

// SignatoryAContainsFold applies the ContainsFold predicate on the "signatory_a" field.
func SignatoryAContainsFold(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldContainsFold(FieldSignatoryA, v))
}

// SignatoryBEQ applies the EQ predicate on the "signatory_b" field.
func SignatoryBEQ(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldEQ(FieldSignatoryB, v))
}

// SignatoryBNEQ applies the NEQ predicate on the "signatory_b" field.
func SignatoryBNEQ(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldNEQ(FieldSignatoryB, v))
}

// SignatoryBIn applies the In predicate on the "signatory_b" field.
func SignatoryBIn(vs ...string) predicate.Signatories {
	return predicate.Signatories(sql.FieldIn(FieldSignatoryB, vs...))
}

// SignatoryBNotIn applies the NotIn predicate on the "signatory_b" field.
func SignatoryBNotIn(vs ...string) predicate.Signatories {
	return predicate.Signatories(sql.FieldNotIn(FieldSignatoryB, vs...))
}

// SignatoryBGT applies the GT predicate on the "signatory_b" field.
func SignatoryBGT(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldGT(FieldSignatoryB, v))
}

// SignatoryBGTE applies the GTE predicate on the "signatory_b" field.
func SignatoryBGTE(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldGTE(FieldSignatoryB, v))
}

// SignatoryBLT applies the LT predicate on the "signatory_b" field.
func SignatoryBLT(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldLT(FieldSignatoryB, v))
}

// SignatoryBLTE applies the LTE predicate on the "signatory_b" field.
func SignatoryBLTE(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldLTE(FieldSignatoryB, v))
}

// SignatoryBContains applies the Contains predicate on the "signatory_b" field.
func SignatoryBContains(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldContains(FieldSignatoryB, v))
}

// SignatoryBHasPrefix applies the HasPrefix predicate on the "signatory_b" field.
func SignatoryBHasPrefix(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldHasPrefix(FieldSignatoryB, v))
}

// SignatoryBHasSuffix applies the HasSuffix predicate on the "signatory_b" field.
func SignatoryBHasSuffix(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldHasSuffix(FieldSignatoryB, v))
}

// SignatoryBIsNil applies the IsNil predicate on the "signatory_b" field.
func SignatoryBIsNil() predicate.Signatories {
	return predicate.Signatories(sql.FieldIsNull(FieldSignatoryB))
}

// SignatoryBNotNil applies the NotNil predicate on the "signatory_b" field.
func SignatoryBNotNil() predicate.Signatories {
	return predicate.Signatories(sql.FieldNotNull(FieldSignatoryB))
}

// SignatoryBEqualFold applies the EqualFold predicate on the "signatory_b" field.
func SignatoryBEqualFold(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldEqualFold(FieldSignatoryB, v))
}

// SignatoryBContainsFold applies the ContainsFold predicate on the "signatory_b" field.
func SignatoryBContainsFold(v string) predicate.Signatories {
	return predicate.Signatories(sql.FieldContainsFold(FieldSignatoryB, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Signatories) predicate.Signatories {
	return predicate.Signatories(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Signatories) predicate.Signatories {
	return predicate.Signatories(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Signatories) predicate.Signatories {
	return predicate.Signatories(sql.NotPredicates(p))
}
