// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/confirmations"
)

// Confirmations is the model entity for the Confirmations schema.
type Confirmations struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// ConfirmationType holds the value of the "confirmation_type" field.
	ConfirmationType confirmations.ConfirmationType `json:"confirmation_type,omitempty"`
	// ConfirmationDate holds the value of the "confirmation_date" field.
	ConfirmationDate time.Time `json:"confirmation_date,omitempty"`
	selectValues     sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Confirmations) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case confirmations.FieldConfirmationType:
			values[i] = new(sql.NullString)
		case confirmations.FieldConfirmationDate:
			values[i] = new(sql.NullTime)
		case confirmations.FieldID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Confirmations fields.
func (_m *Confirmations) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case confirmations.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				_m.ID = *value
			}
		case confirmations.FieldConfirmationType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field confirmation_type", values[i])
			} else if value.Valid {
				_m.ConfirmationType = confirmations.ConfirmationType(value.String)
			}
		case confirmations.FieldConfirmationDate:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field confirmation_date", values[i])
			} else if value.Valid {
				_m.ConfirmationDate = value.Time
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Confirmations.
// This includes values selected through modifiers, order, etc.
func (_m *Confirmations) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// Update returns a builder for updating this Confirmations.
// Note that you need to call Confirmations.Unwrap() before calling this method if this Confirmations
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *Confirmations) Update() *ConfirmationsUpdateOne {
	return NewConfirmationsClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the Confirmations entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *Confirmations) Unwrap() *Confirmations {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("ent: Confirmations is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *Confirmations) String() string {
	var builder strings.Builder
	builder.WriteString("Confirmations(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("confirmation_type=")
	builder.WriteString(fmt.Sprintf("%v", _m.ConfirmationType))
	builder.WriteString(", ")
	builder.WriteString("confirmation_date=")
	builder.WriteString(_m.ConfirmationDate.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// ConfirmationsSlice is a parsable slice of Confirmations.
type ConfirmationsSlice []*Confirmations
