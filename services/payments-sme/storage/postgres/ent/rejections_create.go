// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/rejections"
)

// RejectionsCreate is the builder for creating a Rejections entity.
type RejectionsCreate struct {
	config
	mutation *RejectionsMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (_c *RejectionsCreate) SetCreateTime(v time.Time) *RejectionsCreate {
	_c.mutation.SetCreateTime(v)
	return _c
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (_c *RejectionsCreate) SetNillableCreateTime(v *time.Time) *RejectionsCreate {
	if v != nil {
		_c.SetCreateTime(*v)
	}
	return _c
}

// SetUpdateTime sets the "update_time" field.
func (_c *RejectionsCreate) SetUpdateTime(v time.Time) *RejectionsCreate {
	_c.mutation.SetUpdateTime(v)
	return _c
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (_c *RejectionsCreate) SetNillableUpdateTime(v *time.Time) *RejectionsCreate {
	if v != nil {
		_c.SetUpdateTime(*v)
	}
	return _c
}

// SetParentTransactionID sets the "parent_transaction_id" field.
func (_c *RejectionsCreate) SetParentTransactionID(v uuid.UUID) *RejectionsCreate {
	_c.mutation.SetParentTransactionID(v)
	return _c
}

// SetRejectionSource sets the "rejection_source" field.
func (_c *RejectionsCreate) SetRejectionSource(v rejections.RejectionSource) *RejectionsCreate {
	_c.mutation.SetRejectionSource(v)
	return _c
}

// SetRejectionCode sets the "rejection_code" field.
func (_c *RejectionsCreate) SetRejectionCode(v string) *RejectionsCreate {
	_c.mutation.SetRejectionCode(v)
	return _c
}

// SetRejectionScore sets the "rejection_score" field.
func (_c *RejectionsCreate) SetRejectionScore(v string) *RejectionsCreate {
	_c.mutation.SetRejectionScore(v)
	return _c
}

// SetRejectionReason sets the "rejection_reason" field.
func (_c *RejectionsCreate) SetRejectionReason(v string) *RejectionsCreate {
	_c.mutation.SetRejectionReason(v)
	return _c
}

// SetID sets the "id" field.
func (_c *RejectionsCreate) SetID(v uuid.UUID) *RejectionsCreate {
	_c.mutation.SetID(v)
	return _c
}

// Mutation returns the RejectionsMutation object of the builder.
func (_c *RejectionsCreate) Mutation() *RejectionsMutation {
	return _c.mutation
}

// Save creates the Rejections in the database.
func (_c *RejectionsCreate) Save(ctx context.Context) (*Rejections, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *RejectionsCreate) SaveX(ctx context.Context) *Rejections {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *RejectionsCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *RejectionsCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *RejectionsCreate) defaults() {
	if _, ok := _c.mutation.CreateTime(); !ok {
		v := rejections.DefaultCreateTime()
		_c.mutation.SetCreateTime(v)
	}
	if _, ok := _c.mutation.UpdateTime(); !ok {
		v := rejections.DefaultUpdateTime()
		_c.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *RejectionsCreate) check() error {
	if _, ok := _c.mutation.ParentTransactionID(); !ok {
		return &ValidationError{Name: "parent_transaction_id", err: errors.New(`ent: missing required field "Rejections.parent_transaction_id"`)}
	}
	if _, ok := _c.mutation.RejectionSource(); !ok {
		return &ValidationError{Name: "rejection_source", err: errors.New(`ent: missing required field "Rejections.rejection_source"`)}
	}
	if v, ok := _c.mutation.RejectionSource(); ok {
		if err := rejections.RejectionSourceValidator(v); err != nil {
			return &ValidationError{Name: "rejection_source", err: fmt.Errorf(`ent: validator failed for field "Rejections.rejection_source": %w`, err)}
		}
	}
	if _, ok := _c.mutation.RejectionCode(); !ok {
		return &ValidationError{Name: "rejection_code", err: errors.New(`ent: missing required field "Rejections.rejection_code"`)}
	}
	if _, ok := _c.mutation.RejectionScore(); !ok {
		return &ValidationError{Name: "rejection_score", err: errors.New(`ent: missing required field "Rejections.rejection_score"`)}
	}
	if _, ok := _c.mutation.RejectionReason(); !ok {
		return &ValidationError{Name: "rejection_reason", err: errors.New(`ent: missing required field "Rejections.rejection_reason"`)}
	}
	return nil
}

func (_c *RejectionsCreate) sqlSave(ctx context.Context) (*Rejections, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *RejectionsCreate) createSpec() (*Rejections, *sqlgraph.CreateSpec) {
	var (
		_node = &Rejections{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(rejections.Table, sqlgraph.NewFieldSpec(rejections.FieldID, field.TypeUUID))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := _c.mutation.CreateTime(); ok {
		_spec.SetField(rejections.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = value
	}
	if value, ok := _c.mutation.UpdateTime(); ok {
		_spec.SetField(rejections.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = value
	}
	if value, ok := _c.mutation.ParentTransactionID(); ok {
		_spec.SetField(rejections.FieldParentTransactionID, field.TypeUUID, value)
		_node.ParentTransactionID = value
	}
	if value, ok := _c.mutation.RejectionSource(); ok {
		_spec.SetField(rejections.FieldRejectionSource, field.TypeEnum, value)
		_node.RejectionSource = value
	}
	if value, ok := _c.mutation.RejectionCode(); ok {
		_spec.SetField(rejections.FieldRejectionCode, field.TypeString, value)
		_node.RejectionCode = value
	}
	if value, ok := _c.mutation.RejectionScore(); ok {
		_spec.SetField(rejections.FieldRejectionScore, field.TypeString, value)
		_node.RejectionScore = value
	}
	if value, ok := _c.mutation.RejectionReason(); ok {
		_spec.SetField(rejections.FieldRejectionReason, field.TypeString, value)
		_node.RejectionReason = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Rejections.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.RejectionsUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *RejectionsCreate) OnConflict(opts ...sql.ConflictOption) *RejectionsUpsertOne {
	_c.conflict = opts
	return &RejectionsUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Rejections.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *RejectionsCreate) OnConflictColumns(columns ...string) *RejectionsUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &RejectionsUpsertOne{
		create: _c,
	}
}

type (
	// RejectionsUpsertOne is the builder for "upsert"-ing
	//  one Rejections node.
	RejectionsUpsertOne struct {
		create *RejectionsCreate
	}

	// RejectionsUpsert is the "OnConflict" setter.
	RejectionsUpsert struct {
		*sql.UpdateSet
	}
)

// SetParentTransactionID sets the "parent_transaction_id" field.
func (u *RejectionsUpsert) SetParentTransactionID(v uuid.UUID) *RejectionsUpsert {
	u.Set(rejections.FieldParentTransactionID, v)
	return u
}

// UpdateParentTransactionID sets the "parent_transaction_id" field to the value that was provided on create.
func (u *RejectionsUpsert) UpdateParentTransactionID() *RejectionsUpsert {
	u.SetExcluded(rejections.FieldParentTransactionID)
	return u
}

// SetRejectionSource sets the "rejection_source" field.
func (u *RejectionsUpsert) SetRejectionSource(v rejections.RejectionSource) *RejectionsUpsert {
	u.Set(rejections.FieldRejectionSource, v)
	return u
}

// UpdateRejectionSource sets the "rejection_source" field to the value that was provided on create.
func (u *RejectionsUpsert) UpdateRejectionSource() *RejectionsUpsert {
	u.SetExcluded(rejections.FieldRejectionSource)
	return u
}

// SetRejectionCode sets the "rejection_code" field.
func (u *RejectionsUpsert) SetRejectionCode(v string) *RejectionsUpsert {
	u.Set(rejections.FieldRejectionCode, v)
	return u
}

// UpdateRejectionCode sets the "rejection_code" field to the value that was provided on create.
func (u *RejectionsUpsert) UpdateRejectionCode() *RejectionsUpsert {
	u.SetExcluded(rejections.FieldRejectionCode)
	return u
}

// SetRejectionScore sets the "rejection_score" field.
func (u *RejectionsUpsert) SetRejectionScore(v string) *RejectionsUpsert {
	u.Set(rejections.FieldRejectionScore, v)
	return u
}

// UpdateRejectionScore sets the "rejection_score" field to the value that was provided on create.
func (u *RejectionsUpsert) UpdateRejectionScore() *RejectionsUpsert {
	u.SetExcluded(rejections.FieldRejectionScore)
	return u
}

// SetRejectionReason sets the "rejection_reason" field.
func (u *RejectionsUpsert) SetRejectionReason(v string) *RejectionsUpsert {
	u.Set(rejections.FieldRejectionReason, v)
	return u
}

// UpdateRejectionReason sets the "rejection_reason" field to the value that was provided on create.
func (u *RejectionsUpsert) UpdateRejectionReason() *RejectionsUpsert {
	u.SetExcluded(rejections.FieldRejectionReason)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Rejections.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(rejections.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *RejectionsUpsertOne) UpdateNewValues() *RejectionsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(rejections.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(rejections.FieldCreateTime)
		}
		if _, exists := u.create.mutation.UpdateTime(); exists {
			s.SetIgnore(rejections.FieldUpdateTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Rejections.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *RejectionsUpsertOne) Ignore() *RejectionsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *RejectionsUpsertOne) DoNothing() *RejectionsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the RejectionsCreate.OnConflict
// documentation for more info.
func (u *RejectionsUpsertOne) Update(set func(*RejectionsUpsert)) *RejectionsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&RejectionsUpsert{UpdateSet: update})
	}))
	return u
}

// SetParentTransactionID sets the "parent_transaction_id" field.
func (u *RejectionsUpsertOne) SetParentTransactionID(v uuid.UUID) *RejectionsUpsertOne {
	return u.Update(func(s *RejectionsUpsert) {
		s.SetParentTransactionID(v)
	})
}

// UpdateParentTransactionID sets the "parent_transaction_id" field to the value that was provided on create.
func (u *RejectionsUpsertOne) UpdateParentTransactionID() *RejectionsUpsertOne {
	return u.Update(func(s *RejectionsUpsert) {
		s.UpdateParentTransactionID()
	})
}

// SetRejectionSource sets the "rejection_source" field.
func (u *RejectionsUpsertOne) SetRejectionSource(v rejections.RejectionSource) *RejectionsUpsertOne {
	return u.Update(func(s *RejectionsUpsert) {
		s.SetRejectionSource(v)
	})
}

// UpdateRejectionSource sets the "rejection_source" field to the value that was provided on create.
func (u *RejectionsUpsertOne) UpdateRejectionSource() *RejectionsUpsertOne {
	return u.Update(func(s *RejectionsUpsert) {
		s.UpdateRejectionSource()
	})
}

// SetRejectionCode sets the "rejection_code" field.
func (u *RejectionsUpsertOne) SetRejectionCode(v string) *RejectionsUpsertOne {
	return u.Update(func(s *RejectionsUpsert) {
		s.SetRejectionCode(v)
	})
}

// UpdateRejectionCode sets the "rejection_code" field to the value that was provided on create.
func (u *RejectionsUpsertOne) UpdateRejectionCode() *RejectionsUpsertOne {
	return u.Update(func(s *RejectionsUpsert) {
		s.UpdateRejectionCode()
	})
}

// SetRejectionScore sets the "rejection_score" field.
func (u *RejectionsUpsertOne) SetRejectionScore(v string) *RejectionsUpsertOne {
	return u.Update(func(s *RejectionsUpsert) {
		s.SetRejectionScore(v)
	})
}

// UpdateRejectionScore sets the "rejection_score" field to the value that was provided on create.
func (u *RejectionsUpsertOne) UpdateRejectionScore() *RejectionsUpsertOne {
	return u.Update(func(s *RejectionsUpsert) {
		s.UpdateRejectionScore()
	})
}

// SetRejectionReason sets the "rejection_reason" field.
func (u *RejectionsUpsertOne) SetRejectionReason(v string) *RejectionsUpsertOne {
	return u.Update(func(s *RejectionsUpsert) {
		s.SetRejectionReason(v)
	})
}

// UpdateRejectionReason sets the "rejection_reason" field to the value that was provided on create.
func (u *RejectionsUpsertOne) UpdateRejectionReason() *RejectionsUpsertOne {
	return u.Update(func(s *RejectionsUpsert) {
		s.UpdateRejectionReason()
	})
}

// Exec executes the query.
func (u *RejectionsUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for RejectionsCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *RejectionsUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *RejectionsUpsertOne) ID(ctx context.Context) (id uuid.UUID, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: RejectionsUpsertOne.ID is not supported by MySQL driver. Use RejectionsUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *RejectionsUpsertOne) IDX(ctx context.Context) uuid.UUID {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// RejectionsCreateBulk is the builder for creating many Rejections entities in bulk.
type RejectionsCreateBulk struct {
	config
	err      error
	builders []*RejectionsCreate
	conflict []sql.ConflictOption
}

// Save creates the Rejections entities in the database.
func (_c *RejectionsCreateBulk) Save(ctx context.Context) ([]*Rejections, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*Rejections, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*RejectionsMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *RejectionsCreateBulk) SaveX(ctx context.Context) []*Rejections {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *RejectionsCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *RejectionsCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Rejections.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.RejectionsUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *RejectionsCreateBulk) OnConflict(opts ...sql.ConflictOption) *RejectionsUpsertBulk {
	_c.conflict = opts
	return &RejectionsUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Rejections.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *RejectionsCreateBulk) OnConflictColumns(columns ...string) *RejectionsUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &RejectionsUpsertBulk{
		create: _c,
	}
}

// RejectionsUpsertBulk is the builder for "upsert"-ing
// a bulk of Rejections nodes.
type RejectionsUpsertBulk struct {
	create *RejectionsCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Rejections.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(rejections.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *RejectionsUpsertBulk) UpdateNewValues() *RejectionsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(rejections.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(rejections.FieldCreateTime)
			}
			if _, exists := b.mutation.UpdateTime(); exists {
				s.SetIgnore(rejections.FieldUpdateTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Rejections.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *RejectionsUpsertBulk) Ignore() *RejectionsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *RejectionsUpsertBulk) DoNothing() *RejectionsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the RejectionsCreateBulk.OnConflict
// documentation for more info.
func (u *RejectionsUpsertBulk) Update(set func(*RejectionsUpsert)) *RejectionsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&RejectionsUpsert{UpdateSet: update})
	}))
	return u
}

// SetParentTransactionID sets the "parent_transaction_id" field.
func (u *RejectionsUpsertBulk) SetParentTransactionID(v uuid.UUID) *RejectionsUpsertBulk {
	return u.Update(func(s *RejectionsUpsert) {
		s.SetParentTransactionID(v)
	})
}

// UpdateParentTransactionID sets the "parent_transaction_id" field to the value that was provided on create.
func (u *RejectionsUpsertBulk) UpdateParentTransactionID() *RejectionsUpsertBulk {
	return u.Update(func(s *RejectionsUpsert) {
		s.UpdateParentTransactionID()
	})
}

// SetRejectionSource sets the "rejection_source" field.
func (u *RejectionsUpsertBulk) SetRejectionSource(v rejections.RejectionSource) *RejectionsUpsertBulk {
	return u.Update(func(s *RejectionsUpsert) {
		s.SetRejectionSource(v)
	})
}

// UpdateRejectionSource sets the "rejection_source" field to the value that was provided on create.
func (u *RejectionsUpsertBulk) UpdateRejectionSource() *RejectionsUpsertBulk {
	return u.Update(func(s *RejectionsUpsert) {
		s.UpdateRejectionSource()
	})
}

// SetRejectionCode sets the "rejection_code" field.
func (u *RejectionsUpsertBulk) SetRejectionCode(v string) *RejectionsUpsertBulk {
	return u.Update(func(s *RejectionsUpsert) {
		s.SetRejectionCode(v)
	})
}

// UpdateRejectionCode sets the "rejection_code" field to the value that was provided on create.
func (u *RejectionsUpsertBulk) UpdateRejectionCode() *RejectionsUpsertBulk {
	return u.Update(func(s *RejectionsUpsert) {
		s.UpdateRejectionCode()
	})
}

// SetRejectionScore sets the "rejection_score" field.
func (u *RejectionsUpsertBulk) SetRejectionScore(v string) *RejectionsUpsertBulk {
	return u.Update(func(s *RejectionsUpsert) {
		s.SetRejectionScore(v)
	})
}

// UpdateRejectionScore sets the "rejection_score" field to the value that was provided on create.
func (u *RejectionsUpsertBulk) UpdateRejectionScore() *RejectionsUpsertBulk {
	return u.Update(func(s *RejectionsUpsert) {
		s.UpdateRejectionScore()
	})
}

// SetRejectionReason sets the "rejection_reason" field.
func (u *RejectionsUpsertBulk) SetRejectionReason(v string) *RejectionsUpsertBulk {
	return u.Update(func(s *RejectionsUpsert) {
		s.SetRejectionReason(v)
	})
}

// UpdateRejectionReason sets the "rejection_reason" field to the value that was provided on create.
func (u *RejectionsUpsertBulk) UpdateRejectionReason() *RejectionsUpsertBulk {
	return u.Update(func(s *RejectionsUpsert) {
		s.UpdateRejectionReason()
	})
}

// Exec executes the query.
func (u *RejectionsUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the RejectionsCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for RejectionsCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *RejectionsUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
