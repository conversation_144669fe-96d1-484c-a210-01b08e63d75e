// Code generated by ent, DO NOT EDIT.

package abstransactiondocuments

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the abstransactiondocuments type in the database.
	Label = "abs_transaction_documents"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreateTime holds the string denoting the create_time field in the database.
	FieldCreateTime = "create_time"
	// FieldUpdateTime holds the string denoting the update_time field in the database.
	FieldUpdateTime = "update_time"
	// FieldReferenceID holds the string denoting the reference_id field in the database.
	FieldReferenceID = "reference_id"
	// FieldDocumentType holds the string denoting the document_type field in the database.
	FieldDocumentType = "document_type"
	// FieldReferenceDate holds the string denoting the reference_date field in the database.
	FieldReferenceDate = "reference_date"
	// FieldDocumentStatus holds the string denoting the document_status field in the database.
	FieldDocumentStatus = "document_status"
	// FieldRejectionReason holds the string denoting the rejection_reason field in the database.
	FieldRejectionReason = "rejection_reason"
	// Table holds the table name of the abstransactiondocuments in the database.
	Table = "abs_transaction_documents"
)

// Columns holds all SQL columns for abstransactiondocuments fields.
var Columns = []string{
	FieldID,
	FieldCreateTime,
	FieldUpdateTime,
	FieldReferenceID,
	FieldDocumentType,
	FieldReferenceDate,
	FieldDocumentStatus,
	FieldRejectionReason,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreateTime holds the default value on creation for the "create_time" field.
	DefaultCreateTime func() time.Time
	// DefaultUpdateTime holds the default value on creation for the "update_time" field.
	DefaultUpdateTime func() time.Time
	// UpdateDefaultUpdateTime holds the default value on update for the "update_time" field.
	UpdateDefaultUpdateTime func() time.Time
)

// DocumentStatus defines the type for the "document_status" enum field.
type DocumentStatus string

// DocumentStatus values.
const (
	DocumentStatusNOT_BOOKED DocumentStatus = "NOT_BOOKED"
	DocumentStatusBOOKED     DocumentStatus = "BOOKED"
	DocumentStatusREJECTED   DocumentStatus = "REJECTED"
)

func (ds DocumentStatus) String() string {
	return string(ds)
}

// DocumentStatusValidator is a validator for the "document_status" field enum values. It is called by the builders before save.
func DocumentStatusValidator(ds DocumentStatus) error {
	switch ds {
	case DocumentStatusNOT_BOOKED, DocumentStatusBOOKED, DocumentStatusREJECTED:
		return nil
	default:
		return fmt.Errorf("abstransactiondocuments: invalid enum value for document_status field: %q", ds)
	}
}

// RejectionReason defines the type for the "rejection_reason" enum field.
type RejectionReason string

// RejectionReason values.
const (
	RejectionReasonValidationError        RejectionReason = "Validation error"
	RejectionReasonWorktimeExceeded       RejectionReason = "Worktime exceeded"
	RejectionReasonInsufficientFunds      RejectionReason = "Insufficient funds"
	RejectionReasonNoActiveAccount        RejectionReason = "No active account"
	RejectionReasonUnableToProcessPayment RejectionReason = "Unable to process payment"
	RejectionReasonActionIsForbidden      RejectionReason = "Action is forbidden"
)

func (rr RejectionReason) String() string {
	return string(rr)
}

// RejectionReasonValidator is a validator for the "rejection_reason" field enum values. It is called by the builders before save.
func RejectionReasonValidator(rr RejectionReason) error {
	switch rr {
	case RejectionReasonValidationError, RejectionReasonWorktimeExceeded, RejectionReasonInsufficientFunds, RejectionReasonNoActiveAccount, RejectionReasonUnableToProcessPayment, RejectionReasonActionIsForbidden:
		return nil
	default:
		return fmt.Errorf("abstransactiondocuments: invalid enum value for rejection_reason field: %q", rr)
	}
}

// OrderOption defines the ordering options for the AbsTransactionDocuments queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreateTime orders the results by the create_time field.
func ByCreateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateTime, opts...).ToFunc()
}

// ByUpdateTime orders the results by the update_time field.
func ByUpdateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateTime, opts...).ToFunc()
}

// ByReferenceID orders the results by the reference_id field.
func ByReferenceID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReferenceID, opts...).ToFunc()
}

// ByDocumentType orders the results by the document_type field.
func ByDocumentType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDocumentType, opts...).ToFunc()
}

// ByReferenceDate orders the results by the reference_date field.
func ByReferenceDate(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReferenceDate, opts...).ToFunc()
}

// ByDocumentStatus orders the results by the document_status field.
func ByDocumentStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDocumentStatus, opts...).ToFunc()
}

// ByRejectionReason orders the results by the rejection_reason field.
func ByRejectionReason(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRejectionReason, opts...).ToFunc()
}
