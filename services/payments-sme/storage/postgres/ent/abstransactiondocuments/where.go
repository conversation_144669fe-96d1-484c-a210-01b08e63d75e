// Code generated by ent, DO NOT EDIT.

package abstransactiondocuments

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldEQ(FieldUpdateTime, v))
}

// ReferenceID applies equality check predicate on the "reference_id" field. It's identical to ReferenceIDEQ.
func ReferenceID(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldEQ(FieldReferenceID, v))
}

// DocumentType applies equality check predicate on the "document_type" field. It's identical to DocumentTypeEQ.
func DocumentType(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldEQ(FieldDocumentType, v))
}

// ReferenceDate applies equality check predicate on the "reference_date" field. It's identical to ReferenceDateEQ.
func ReferenceDate(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldEQ(FieldReferenceDate, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldLTE(FieldCreateTime, v))
}

// CreateTimeIsNil applies the IsNil predicate on the "create_time" field.
func CreateTimeIsNil() predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldIsNull(FieldCreateTime))
}

// CreateTimeNotNil applies the NotNil predicate on the "create_time" field.
func CreateTimeNotNil() predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldNotNull(FieldCreateTime))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldLTE(FieldUpdateTime, v))
}

// UpdateTimeIsNil applies the IsNil predicate on the "update_time" field.
func UpdateTimeIsNil() predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldIsNull(FieldUpdateTime))
}

// UpdateTimeNotNil applies the NotNil predicate on the "update_time" field.
func UpdateTimeNotNil() predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldNotNull(FieldUpdateTime))
}

// ReferenceIDEQ applies the EQ predicate on the "reference_id" field.
func ReferenceIDEQ(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldEQ(FieldReferenceID, v))
}

// ReferenceIDNEQ applies the NEQ predicate on the "reference_id" field.
func ReferenceIDNEQ(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldNEQ(FieldReferenceID, v))
}

// ReferenceIDIn applies the In predicate on the "reference_id" field.
func ReferenceIDIn(vs ...string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldIn(FieldReferenceID, vs...))
}

// ReferenceIDNotIn applies the NotIn predicate on the "reference_id" field.
func ReferenceIDNotIn(vs ...string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldNotIn(FieldReferenceID, vs...))
}

// ReferenceIDGT applies the GT predicate on the "reference_id" field.
func ReferenceIDGT(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldGT(FieldReferenceID, v))
}

// ReferenceIDGTE applies the GTE predicate on the "reference_id" field.
func ReferenceIDGTE(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldGTE(FieldReferenceID, v))
}

// ReferenceIDLT applies the LT predicate on the "reference_id" field.
func ReferenceIDLT(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldLT(FieldReferenceID, v))
}

// ReferenceIDLTE applies the LTE predicate on the "reference_id" field.
func ReferenceIDLTE(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldLTE(FieldReferenceID, v))
}

// ReferenceIDContains applies the Contains predicate on the "reference_id" field.
func ReferenceIDContains(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldContains(FieldReferenceID, v))
}

// ReferenceIDHasPrefix applies the HasPrefix predicate on the "reference_id" field.
func ReferenceIDHasPrefix(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldHasPrefix(FieldReferenceID, v))
}

// ReferenceIDHasSuffix applies the HasSuffix predicate on the "reference_id" field.
func ReferenceIDHasSuffix(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldHasSuffix(FieldReferenceID, v))
}

// ReferenceIDEqualFold applies the EqualFold predicate on the "reference_id" field.
func ReferenceIDEqualFold(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldEqualFold(FieldReferenceID, v))
}

// ReferenceIDContainsFold applies the ContainsFold predicate on the "reference_id" field.
func ReferenceIDContainsFold(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldContainsFold(FieldReferenceID, v))
}

// DocumentTypeEQ applies the EQ predicate on the "document_type" field.
func DocumentTypeEQ(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldEQ(FieldDocumentType, v))
}

// DocumentTypeNEQ applies the NEQ predicate on the "document_type" field.
func DocumentTypeNEQ(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldNEQ(FieldDocumentType, v))
}

// DocumentTypeIn applies the In predicate on the "document_type" field.
func DocumentTypeIn(vs ...string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldIn(FieldDocumentType, vs...))
}

// DocumentTypeNotIn applies the NotIn predicate on the "document_type" field.
func DocumentTypeNotIn(vs ...string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldNotIn(FieldDocumentType, vs...))
}

// DocumentTypeGT applies the GT predicate on the "document_type" field.
func DocumentTypeGT(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldGT(FieldDocumentType, v))
}

// DocumentTypeGTE applies the GTE predicate on the "document_type" field.
func DocumentTypeGTE(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldGTE(FieldDocumentType, v))
}

// DocumentTypeLT applies the LT predicate on the "document_type" field.
func DocumentTypeLT(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldLT(FieldDocumentType, v))
}

// DocumentTypeLTE applies the LTE predicate on the "document_type" field.
func DocumentTypeLTE(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldLTE(FieldDocumentType, v))
}

// DocumentTypeContains applies the Contains predicate on the "document_type" field.
func DocumentTypeContains(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldContains(FieldDocumentType, v))
}

// DocumentTypeHasPrefix applies the HasPrefix predicate on the "document_type" field.
func DocumentTypeHasPrefix(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldHasPrefix(FieldDocumentType, v))
}

// DocumentTypeHasSuffix applies the HasSuffix predicate on the "document_type" field.
func DocumentTypeHasSuffix(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldHasSuffix(FieldDocumentType, v))
}

// DocumentTypeIsNil applies the IsNil predicate on the "document_type" field.
func DocumentTypeIsNil() predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldIsNull(FieldDocumentType))
}

// DocumentTypeNotNil applies the NotNil predicate on the "document_type" field.
func DocumentTypeNotNil() predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldNotNull(FieldDocumentType))
}

// DocumentTypeEqualFold applies the EqualFold predicate on the "document_type" field.
func DocumentTypeEqualFold(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldEqualFold(FieldDocumentType, v))
}

// DocumentTypeContainsFold applies the ContainsFold predicate on the "document_type" field.
func DocumentTypeContainsFold(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldContainsFold(FieldDocumentType, v))
}

// ReferenceDateEQ applies the EQ predicate on the "reference_date" field.
func ReferenceDateEQ(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldEQ(FieldReferenceDate, v))
}

// ReferenceDateNEQ applies the NEQ predicate on the "reference_date" field.
func ReferenceDateNEQ(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldNEQ(FieldReferenceDate, v))
}

// ReferenceDateIn applies the In predicate on the "reference_date" field.
func ReferenceDateIn(vs ...string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldIn(FieldReferenceDate, vs...))
}

// ReferenceDateNotIn applies the NotIn predicate on the "reference_date" field.
func ReferenceDateNotIn(vs ...string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldNotIn(FieldReferenceDate, vs...))
}

// ReferenceDateGT applies the GT predicate on the "reference_date" field.
func ReferenceDateGT(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldGT(FieldReferenceDate, v))
}

// ReferenceDateGTE applies the GTE predicate on the "reference_date" field.
func ReferenceDateGTE(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldGTE(FieldReferenceDate, v))
}

// ReferenceDateLT applies the LT predicate on the "reference_date" field.
func ReferenceDateLT(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldLT(FieldReferenceDate, v))
}

// ReferenceDateLTE applies the LTE predicate on the "reference_date" field.
func ReferenceDateLTE(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldLTE(FieldReferenceDate, v))
}

// ReferenceDateContains applies the Contains predicate on the "reference_date" field.
func ReferenceDateContains(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldContains(FieldReferenceDate, v))
}

// ReferenceDateHasPrefix applies the HasPrefix predicate on the "reference_date" field.
func ReferenceDateHasPrefix(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldHasPrefix(FieldReferenceDate, v))
}

// ReferenceDateHasSuffix applies the HasSuffix predicate on the "reference_date" field.
func ReferenceDateHasSuffix(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldHasSuffix(FieldReferenceDate, v))
}

// ReferenceDateIsNil applies the IsNil predicate on the "reference_date" field.
func ReferenceDateIsNil() predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldIsNull(FieldReferenceDate))
}

// ReferenceDateNotNil applies the NotNil predicate on the "reference_date" field.
func ReferenceDateNotNil() predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldNotNull(FieldReferenceDate))
}

// ReferenceDateEqualFold applies the EqualFold predicate on the "reference_date" field.
func ReferenceDateEqualFold(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldEqualFold(FieldReferenceDate, v))
}

// ReferenceDateContainsFold applies the ContainsFold predicate on the "reference_date" field.
func ReferenceDateContainsFold(v string) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldContainsFold(FieldReferenceDate, v))
}

// DocumentStatusEQ applies the EQ predicate on the "document_status" field.
func DocumentStatusEQ(v DocumentStatus) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldEQ(FieldDocumentStatus, v))
}

// DocumentStatusNEQ applies the NEQ predicate on the "document_status" field.
func DocumentStatusNEQ(v DocumentStatus) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldNEQ(FieldDocumentStatus, v))
}

// DocumentStatusIn applies the In predicate on the "document_status" field.
func DocumentStatusIn(vs ...DocumentStatus) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldIn(FieldDocumentStatus, vs...))
}

// DocumentStatusNotIn applies the NotIn predicate on the "document_status" field.
func DocumentStatusNotIn(vs ...DocumentStatus) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldNotIn(FieldDocumentStatus, vs...))
}

// DocumentStatusIsNil applies the IsNil predicate on the "document_status" field.
func DocumentStatusIsNil() predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldIsNull(FieldDocumentStatus))
}

// DocumentStatusNotNil applies the NotNil predicate on the "document_status" field.
func DocumentStatusNotNil() predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldNotNull(FieldDocumentStatus))
}

// RejectionReasonEQ applies the EQ predicate on the "rejection_reason" field.
func RejectionReasonEQ(v RejectionReason) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldEQ(FieldRejectionReason, v))
}

// RejectionReasonNEQ applies the NEQ predicate on the "rejection_reason" field.
func RejectionReasonNEQ(v RejectionReason) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldNEQ(FieldRejectionReason, v))
}

// RejectionReasonIn applies the In predicate on the "rejection_reason" field.
func RejectionReasonIn(vs ...RejectionReason) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldIn(FieldRejectionReason, vs...))
}

// RejectionReasonNotIn applies the NotIn predicate on the "rejection_reason" field.
func RejectionReasonNotIn(vs ...RejectionReason) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldNotIn(FieldRejectionReason, vs...))
}

// RejectionReasonIsNil applies the IsNil predicate on the "rejection_reason" field.
func RejectionReasonIsNil() predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldIsNull(FieldRejectionReason))
}

// RejectionReasonNotNil applies the NotNil predicate on the "rejection_reason" field.
func RejectionReasonNotNil() predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.FieldNotNull(FieldRejectionReason))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.AbsTransactionDocuments) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.AbsTransactionDocuments) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.AbsTransactionDocuments) predicate.AbsTransactionDocuments {
	return predicate.AbsTransactionDocuments(sql.NotPredicates(p))
}
