// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/abstransactiondocuments"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/confirmations"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/employees"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/generateddocument"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/payments"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/predicate"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/rejections"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/signatories"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/transaction"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeAbsTransactionDocuments = "AbsTransactionDocuments"
	TypeConfirmations           = "Confirmations"
	TypeEmployees               = "Employees"
	TypeGeneratedDocument       = "GeneratedDocument"
	TypeHealth                  = "Health"
	TypePayments                = "Payments"
	TypeRejections              = "Rejections"
	TypeSignatories             = "Signatories"
	TypeTransaction             = "Transaction"
)

// AbsTransactionDocumentsMutation represents an operation that mutates the AbsTransactionDocuments nodes in the graph.
type AbsTransactionDocumentsMutation struct {
	config
	op               Op
	typ              string
	id               *uuid.UUID
	create_time      *time.Time
	update_time      *time.Time
	reference_id     *string
	document_type    *string
	reference_date   *string
	document_status  *abstransactiondocuments.DocumentStatus
	rejection_reason *abstransactiondocuments.RejectionReason
	clearedFields    map[string]struct{}
	done             bool
	oldValue         func(context.Context) (*AbsTransactionDocuments, error)
	predicates       []predicate.AbsTransactionDocuments
}

var _ ent.Mutation = (*AbsTransactionDocumentsMutation)(nil)

// abstransactiondocumentsOption allows management of the mutation configuration using functional options.
type abstransactiondocumentsOption func(*AbsTransactionDocumentsMutation)

// newAbsTransactionDocumentsMutation creates new mutation for the AbsTransactionDocuments entity.
func newAbsTransactionDocumentsMutation(c config, op Op, opts ...abstransactiondocumentsOption) *AbsTransactionDocumentsMutation {
	m := &AbsTransactionDocumentsMutation{
		config:        c,
		op:            op,
		typ:           TypeAbsTransactionDocuments,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withAbsTransactionDocumentsID sets the ID field of the mutation.
func withAbsTransactionDocumentsID(id uuid.UUID) abstransactiondocumentsOption {
	return func(m *AbsTransactionDocumentsMutation) {
		var (
			err   error
			once  sync.Once
			value *AbsTransactionDocuments
		)
		m.oldValue = func(ctx context.Context) (*AbsTransactionDocuments, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().AbsTransactionDocuments.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withAbsTransactionDocuments sets the old AbsTransactionDocuments of the mutation.
func withAbsTransactionDocuments(node *AbsTransactionDocuments) abstransactiondocumentsOption {
	return func(m *AbsTransactionDocumentsMutation) {
		m.oldValue = func(context.Context) (*AbsTransactionDocuments, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m AbsTransactionDocumentsMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m AbsTransactionDocumentsMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of AbsTransactionDocuments entities.
func (m *AbsTransactionDocumentsMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *AbsTransactionDocumentsMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *AbsTransactionDocumentsMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().AbsTransactionDocuments.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreateTime sets the "create_time" field.
func (m *AbsTransactionDocumentsMutation) SetCreateTime(t time.Time) {
	m.create_time = &t
}

// CreateTime returns the value of the "create_time" field in the mutation.
func (m *AbsTransactionDocumentsMutation) CreateTime() (r time.Time, exists bool) {
	v := m.create_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateTime returns the old "create_time" field's value of the AbsTransactionDocuments entity.
// If the AbsTransactionDocuments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AbsTransactionDocumentsMutation) OldCreateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateTime: %w", err)
	}
	return oldValue.CreateTime, nil
}

// ClearCreateTime clears the value of the "create_time" field.
func (m *AbsTransactionDocumentsMutation) ClearCreateTime() {
	m.create_time = nil
	m.clearedFields[abstransactiondocuments.FieldCreateTime] = struct{}{}
}

// CreateTimeCleared returns if the "create_time" field was cleared in this mutation.
func (m *AbsTransactionDocumentsMutation) CreateTimeCleared() bool {
	_, ok := m.clearedFields[abstransactiondocuments.FieldCreateTime]
	return ok
}

// ResetCreateTime resets all changes to the "create_time" field.
func (m *AbsTransactionDocumentsMutation) ResetCreateTime() {
	m.create_time = nil
	delete(m.clearedFields, abstransactiondocuments.FieldCreateTime)
}

// SetUpdateTime sets the "update_time" field.
func (m *AbsTransactionDocumentsMutation) SetUpdateTime(t time.Time) {
	m.update_time = &t
}

// UpdateTime returns the value of the "update_time" field in the mutation.
func (m *AbsTransactionDocumentsMutation) UpdateTime() (r time.Time, exists bool) {
	v := m.update_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateTime returns the old "update_time" field's value of the AbsTransactionDocuments entity.
// If the AbsTransactionDocuments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AbsTransactionDocumentsMutation) OldUpdateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateTime: %w", err)
	}
	return oldValue.UpdateTime, nil
}

// ClearUpdateTime clears the value of the "update_time" field.
func (m *AbsTransactionDocumentsMutation) ClearUpdateTime() {
	m.update_time = nil
	m.clearedFields[abstransactiondocuments.FieldUpdateTime] = struct{}{}
}

// UpdateTimeCleared returns if the "update_time" field was cleared in this mutation.
func (m *AbsTransactionDocumentsMutation) UpdateTimeCleared() bool {
	_, ok := m.clearedFields[abstransactiondocuments.FieldUpdateTime]
	return ok
}

// ResetUpdateTime resets all changes to the "update_time" field.
func (m *AbsTransactionDocumentsMutation) ResetUpdateTime() {
	m.update_time = nil
	delete(m.clearedFields, abstransactiondocuments.FieldUpdateTime)
}

// SetReferenceID sets the "reference_id" field.
func (m *AbsTransactionDocumentsMutation) SetReferenceID(s string) {
	m.reference_id = &s
}

// ReferenceID returns the value of the "reference_id" field in the mutation.
func (m *AbsTransactionDocumentsMutation) ReferenceID() (r string, exists bool) {
	v := m.reference_id
	if v == nil {
		return
	}
	return *v, true
}

// OldReferenceID returns the old "reference_id" field's value of the AbsTransactionDocuments entity.
// If the AbsTransactionDocuments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AbsTransactionDocumentsMutation) OldReferenceID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldReferenceID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldReferenceID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldReferenceID: %w", err)
	}
	return oldValue.ReferenceID, nil
}

// ResetReferenceID resets all changes to the "reference_id" field.
func (m *AbsTransactionDocumentsMutation) ResetReferenceID() {
	m.reference_id = nil
}

// SetDocumentType sets the "document_type" field.
func (m *AbsTransactionDocumentsMutation) SetDocumentType(s string) {
	m.document_type = &s
}

// DocumentType returns the value of the "document_type" field in the mutation.
func (m *AbsTransactionDocumentsMutation) DocumentType() (r string, exists bool) {
	v := m.document_type
	if v == nil {
		return
	}
	return *v, true
}

// OldDocumentType returns the old "document_type" field's value of the AbsTransactionDocuments entity.
// If the AbsTransactionDocuments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AbsTransactionDocumentsMutation) OldDocumentType(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDocumentType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDocumentType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDocumentType: %w", err)
	}
	return oldValue.DocumentType, nil
}

// ClearDocumentType clears the value of the "document_type" field.
func (m *AbsTransactionDocumentsMutation) ClearDocumentType() {
	m.document_type = nil
	m.clearedFields[abstransactiondocuments.FieldDocumentType] = struct{}{}
}

// DocumentTypeCleared returns if the "document_type" field was cleared in this mutation.
func (m *AbsTransactionDocumentsMutation) DocumentTypeCleared() bool {
	_, ok := m.clearedFields[abstransactiondocuments.FieldDocumentType]
	return ok
}

// ResetDocumentType resets all changes to the "document_type" field.
func (m *AbsTransactionDocumentsMutation) ResetDocumentType() {
	m.document_type = nil
	delete(m.clearedFields, abstransactiondocuments.FieldDocumentType)
}

// SetReferenceDate sets the "reference_date" field.
func (m *AbsTransactionDocumentsMutation) SetReferenceDate(s string) {
	m.reference_date = &s
}

// ReferenceDate returns the value of the "reference_date" field in the mutation.
func (m *AbsTransactionDocumentsMutation) ReferenceDate() (r string, exists bool) {
	v := m.reference_date
	if v == nil {
		return
	}
	return *v, true
}

// OldReferenceDate returns the old "reference_date" field's value of the AbsTransactionDocuments entity.
// If the AbsTransactionDocuments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AbsTransactionDocumentsMutation) OldReferenceDate(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldReferenceDate is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldReferenceDate requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldReferenceDate: %w", err)
	}
	return oldValue.ReferenceDate, nil
}

// ClearReferenceDate clears the value of the "reference_date" field.
func (m *AbsTransactionDocumentsMutation) ClearReferenceDate() {
	m.reference_date = nil
	m.clearedFields[abstransactiondocuments.FieldReferenceDate] = struct{}{}
}

// ReferenceDateCleared returns if the "reference_date" field was cleared in this mutation.
func (m *AbsTransactionDocumentsMutation) ReferenceDateCleared() bool {
	_, ok := m.clearedFields[abstransactiondocuments.FieldReferenceDate]
	return ok
}

// ResetReferenceDate resets all changes to the "reference_date" field.
func (m *AbsTransactionDocumentsMutation) ResetReferenceDate() {
	m.reference_date = nil
	delete(m.clearedFields, abstransactiondocuments.FieldReferenceDate)
}

// SetDocumentStatus sets the "document_status" field.
func (m *AbsTransactionDocumentsMutation) SetDocumentStatus(as abstransactiondocuments.DocumentStatus) {
	m.document_status = &as
}

// DocumentStatus returns the value of the "document_status" field in the mutation.
func (m *AbsTransactionDocumentsMutation) DocumentStatus() (r abstransactiondocuments.DocumentStatus, exists bool) {
	v := m.document_status
	if v == nil {
		return
	}
	return *v, true
}

// OldDocumentStatus returns the old "document_status" field's value of the AbsTransactionDocuments entity.
// If the AbsTransactionDocuments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AbsTransactionDocumentsMutation) OldDocumentStatus(ctx context.Context) (v *abstransactiondocuments.DocumentStatus, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDocumentStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDocumentStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDocumentStatus: %w", err)
	}
	return oldValue.DocumentStatus, nil
}

// ClearDocumentStatus clears the value of the "document_status" field.
func (m *AbsTransactionDocumentsMutation) ClearDocumentStatus() {
	m.document_status = nil
	m.clearedFields[abstransactiondocuments.FieldDocumentStatus] = struct{}{}
}

// DocumentStatusCleared returns if the "document_status" field was cleared in this mutation.
func (m *AbsTransactionDocumentsMutation) DocumentStatusCleared() bool {
	_, ok := m.clearedFields[abstransactiondocuments.FieldDocumentStatus]
	return ok
}

// ResetDocumentStatus resets all changes to the "document_status" field.
func (m *AbsTransactionDocumentsMutation) ResetDocumentStatus() {
	m.document_status = nil
	delete(m.clearedFields, abstransactiondocuments.FieldDocumentStatus)
}

// SetRejectionReason sets the "rejection_reason" field.
func (m *AbsTransactionDocumentsMutation) SetRejectionReason(ar abstransactiondocuments.RejectionReason) {
	m.rejection_reason = &ar
}

// RejectionReason returns the value of the "rejection_reason" field in the mutation.
func (m *AbsTransactionDocumentsMutation) RejectionReason() (r abstransactiondocuments.RejectionReason, exists bool) {
	v := m.rejection_reason
	if v == nil {
		return
	}
	return *v, true
}

// OldRejectionReason returns the old "rejection_reason" field's value of the AbsTransactionDocuments entity.
// If the AbsTransactionDocuments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AbsTransactionDocumentsMutation) OldRejectionReason(ctx context.Context) (v *abstransactiondocuments.RejectionReason, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRejectionReason is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRejectionReason requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRejectionReason: %w", err)
	}
	return oldValue.RejectionReason, nil
}

// ClearRejectionReason clears the value of the "rejection_reason" field.
func (m *AbsTransactionDocumentsMutation) ClearRejectionReason() {
	m.rejection_reason = nil
	m.clearedFields[abstransactiondocuments.FieldRejectionReason] = struct{}{}
}

// RejectionReasonCleared returns if the "rejection_reason" field was cleared in this mutation.
func (m *AbsTransactionDocumentsMutation) RejectionReasonCleared() bool {
	_, ok := m.clearedFields[abstransactiondocuments.FieldRejectionReason]
	return ok
}

// ResetRejectionReason resets all changes to the "rejection_reason" field.
func (m *AbsTransactionDocumentsMutation) ResetRejectionReason() {
	m.rejection_reason = nil
	delete(m.clearedFields, abstransactiondocuments.FieldRejectionReason)
}

// Where appends a list predicates to the AbsTransactionDocumentsMutation builder.
func (m *AbsTransactionDocumentsMutation) Where(ps ...predicate.AbsTransactionDocuments) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the AbsTransactionDocumentsMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *AbsTransactionDocumentsMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.AbsTransactionDocuments, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *AbsTransactionDocumentsMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *AbsTransactionDocumentsMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (AbsTransactionDocuments).
func (m *AbsTransactionDocumentsMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *AbsTransactionDocumentsMutation) Fields() []string {
	fields := make([]string, 0, 7)
	if m.create_time != nil {
		fields = append(fields, abstransactiondocuments.FieldCreateTime)
	}
	if m.update_time != nil {
		fields = append(fields, abstransactiondocuments.FieldUpdateTime)
	}
	if m.reference_id != nil {
		fields = append(fields, abstransactiondocuments.FieldReferenceID)
	}
	if m.document_type != nil {
		fields = append(fields, abstransactiondocuments.FieldDocumentType)
	}
	if m.reference_date != nil {
		fields = append(fields, abstransactiondocuments.FieldReferenceDate)
	}
	if m.document_status != nil {
		fields = append(fields, abstransactiondocuments.FieldDocumentStatus)
	}
	if m.rejection_reason != nil {
		fields = append(fields, abstransactiondocuments.FieldRejectionReason)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *AbsTransactionDocumentsMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case abstransactiondocuments.FieldCreateTime:
		return m.CreateTime()
	case abstransactiondocuments.FieldUpdateTime:
		return m.UpdateTime()
	case abstransactiondocuments.FieldReferenceID:
		return m.ReferenceID()
	case abstransactiondocuments.FieldDocumentType:
		return m.DocumentType()
	case abstransactiondocuments.FieldReferenceDate:
		return m.ReferenceDate()
	case abstransactiondocuments.FieldDocumentStatus:
		return m.DocumentStatus()
	case abstransactiondocuments.FieldRejectionReason:
		return m.RejectionReason()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *AbsTransactionDocumentsMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case abstransactiondocuments.FieldCreateTime:
		return m.OldCreateTime(ctx)
	case abstransactiondocuments.FieldUpdateTime:
		return m.OldUpdateTime(ctx)
	case abstransactiondocuments.FieldReferenceID:
		return m.OldReferenceID(ctx)
	case abstransactiondocuments.FieldDocumentType:
		return m.OldDocumentType(ctx)
	case abstransactiondocuments.FieldReferenceDate:
		return m.OldReferenceDate(ctx)
	case abstransactiondocuments.FieldDocumentStatus:
		return m.OldDocumentStatus(ctx)
	case abstransactiondocuments.FieldRejectionReason:
		return m.OldRejectionReason(ctx)
	}
	return nil, fmt.Errorf("unknown AbsTransactionDocuments field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AbsTransactionDocumentsMutation) SetField(name string, value ent.Value) error {
	switch name {
	case abstransactiondocuments.FieldCreateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateTime(v)
		return nil
	case abstransactiondocuments.FieldUpdateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateTime(v)
		return nil
	case abstransactiondocuments.FieldReferenceID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetReferenceID(v)
		return nil
	case abstransactiondocuments.FieldDocumentType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDocumentType(v)
		return nil
	case abstransactiondocuments.FieldReferenceDate:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetReferenceDate(v)
		return nil
	case abstransactiondocuments.FieldDocumentStatus:
		v, ok := value.(abstransactiondocuments.DocumentStatus)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDocumentStatus(v)
		return nil
	case abstransactiondocuments.FieldRejectionReason:
		v, ok := value.(abstransactiondocuments.RejectionReason)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRejectionReason(v)
		return nil
	}
	return fmt.Errorf("unknown AbsTransactionDocuments field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *AbsTransactionDocumentsMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *AbsTransactionDocumentsMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AbsTransactionDocumentsMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown AbsTransactionDocuments numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *AbsTransactionDocumentsMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(abstransactiondocuments.FieldCreateTime) {
		fields = append(fields, abstransactiondocuments.FieldCreateTime)
	}
	if m.FieldCleared(abstransactiondocuments.FieldUpdateTime) {
		fields = append(fields, abstransactiondocuments.FieldUpdateTime)
	}
	if m.FieldCleared(abstransactiondocuments.FieldDocumentType) {
		fields = append(fields, abstransactiondocuments.FieldDocumentType)
	}
	if m.FieldCleared(abstransactiondocuments.FieldReferenceDate) {
		fields = append(fields, abstransactiondocuments.FieldReferenceDate)
	}
	if m.FieldCleared(abstransactiondocuments.FieldDocumentStatus) {
		fields = append(fields, abstransactiondocuments.FieldDocumentStatus)
	}
	if m.FieldCleared(abstransactiondocuments.FieldRejectionReason) {
		fields = append(fields, abstransactiondocuments.FieldRejectionReason)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *AbsTransactionDocumentsMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *AbsTransactionDocumentsMutation) ClearField(name string) error {
	switch name {
	case abstransactiondocuments.FieldCreateTime:
		m.ClearCreateTime()
		return nil
	case abstransactiondocuments.FieldUpdateTime:
		m.ClearUpdateTime()
		return nil
	case abstransactiondocuments.FieldDocumentType:
		m.ClearDocumentType()
		return nil
	case abstransactiondocuments.FieldReferenceDate:
		m.ClearReferenceDate()
		return nil
	case abstransactiondocuments.FieldDocumentStatus:
		m.ClearDocumentStatus()
		return nil
	case abstransactiondocuments.FieldRejectionReason:
		m.ClearRejectionReason()
		return nil
	}
	return fmt.Errorf("unknown AbsTransactionDocuments nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *AbsTransactionDocumentsMutation) ResetField(name string) error {
	switch name {
	case abstransactiondocuments.FieldCreateTime:
		m.ResetCreateTime()
		return nil
	case abstransactiondocuments.FieldUpdateTime:
		m.ResetUpdateTime()
		return nil
	case abstransactiondocuments.FieldReferenceID:
		m.ResetReferenceID()
		return nil
	case abstransactiondocuments.FieldDocumentType:
		m.ResetDocumentType()
		return nil
	case abstransactiondocuments.FieldReferenceDate:
		m.ResetReferenceDate()
		return nil
	case abstransactiondocuments.FieldDocumentStatus:
		m.ResetDocumentStatus()
		return nil
	case abstransactiondocuments.FieldRejectionReason:
		m.ResetRejectionReason()
		return nil
	}
	return fmt.Errorf("unknown AbsTransactionDocuments field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *AbsTransactionDocumentsMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *AbsTransactionDocumentsMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *AbsTransactionDocumentsMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *AbsTransactionDocumentsMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *AbsTransactionDocumentsMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *AbsTransactionDocumentsMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *AbsTransactionDocumentsMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown AbsTransactionDocuments unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *AbsTransactionDocumentsMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown AbsTransactionDocuments edge %s", name)
}

// ConfirmationsMutation represents an operation that mutates the Confirmations nodes in the graph.
type ConfirmationsMutation struct {
	config
	op                Op
	typ               string
	id                *uuid.UUID
	confirmation_type *confirmations.ConfirmationType
	confirmation_date *time.Time
	clearedFields     map[string]struct{}
	done              bool
	oldValue          func(context.Context) (*Confirmations, error)
	predicates        []predicate.Confirmations
}

var _ ent.Mutation = (*ConfirmationsMutation)(nil)

// confirmationsOption allows management of the mutation configuration using functional options.
type confirmationsOption func(*ConfirmationsMutation)

// newConfirmationsMutation creates new mutation for the Confirmations entity.
func newConfirmationsMutation(c config, op Op, opts ...confirmationsOption) *ConfirmationsMutation {
	m := &ConfirmationsMutation{
		config:        c,
		op:            op,
		typ:           TypeConfirmations,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withConfirmationsID sets the ID field of the mutation.
func withConfirmationsID(id uuid.UUID) confirmationsOption {
	return func(m *ConfirmationsMutation) {
		var (
			err   error
			once  sync.Once
			value *Confirmations
		)
		m.oldValue = func(ctx context.Context) (*Confirmations, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Confirmations.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withConfirmations sets the old Confirmations of the mutation.
func withConfirmations(node *Confirmations) confirmationsOption {
	return func(m *ConfirmationsMutation) {
		m.oldValue = func(context.Context) (*Confirmations, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m ConfirmationsMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m ConfirmationsMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Confirmations entities.
func (m *ConfirmationsMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *ConfirmationsMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *ConfirmationsMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Confirmations.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetConfirmationType sets the "confirmation_type" field.
func (m *ConfirmationsMutation) SetConfirmationType(ct confirmations.ConfirmationType) {
	m.confirmation_type = &ct
}

// ConfirmationType returns the value of the "confirmation_type" field in the mutation.
func (m *ConfirmationsMutation) ConfirmationType() (r confirmations.ConfirmationType, exists bool) {
	v := m.confirmation_type
	if v == nil {
		return
	}
	return *v, true
}

// OldConfirmationType returns the old "confirmation_type" field's value of the Confirmations entity.
// If the Confirmations object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ConfirmationsMutation) OldConfirmationType(ctx context.Context) (v confirmations.ConfirmationType, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldConfirmationType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldConfirmationType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldConfirmationType: %w", err)
	}
	return oldValue.ConfirmationType, nil
}

// ResetConfirmationType resets all changes to the "confirmation_type" field.
func (m *ConfirmationsMutation) ResetConfirmationType() {
	m.confirmation_type = nil
}

// SetConfirmationDate sets the "confirmation_date" field.
func (m *ConfirmationsMutation) SetConfirmationDate(t time.Time) {
	m.confirmation_date = &t
}

// ConfirmationDate returns the value of the "confirmation_date" field in the mutation.
func (m *ConfirmationsMutation) ConfirmationDate() (r time.Time, exists bool) {
	v := m.confirmation_date
	if v == nil {
		return
	}
	return *v, true
}

// OldConfirmationDate returns the old "confirmation_date" field's value of the Confirmations entity.
// If the Confirmations object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ConfirmationsMutation) OldConfirmationDate(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldConfirmationDate is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldConfirmationDate requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldConfirmationDate: %w", err)
	}
	return oldValue.ConfirmationDate, nil
}

// ResetConfirmationDate resets all changes to the "confirmation_date" field.
func (m *ConfirmationsMutation) ResetConfirmationDate() {
	m.confirmation_date = nil
}

// Where appends a list predicates to the ConfirmationsMutation builder.
func (m *ConfirmationsMutation) Where(ps ...predicate.Confirmations) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the ConfirmationsMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *ConfirmationsMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Confirmations, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *ConfirmationsMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *ConfirmationsMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Confirmations).
func (m *ConfirmationsMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *ConfirmationsMutation) Fields() []string {
	fields := make([]string, 0, 2)
	if m.confirmation_type != nil {
		fields = append(fields, confirmations.FieldConfirmationType)
	}
	if m.confirmation_date != nil {
		fields = append(fields, confirmations.FieldConfirmationDate)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *ConfirmationsMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case confirmations.FieldConfirmationType:
		return m.ConfirmationType()
	case confirmations.FieldConfirmationDate:
		return m.ConfirmationDate()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *ConfirmationsMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case confirmations.FieldConfirmationType:
		return m.OldConfirmationType(ctx)
	case confirmations.FieldConfirmationDate:
		return m.OldConfirmationDate(ctx)
	}
	return nil, fmt.Errorf("unknown Confirmations field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ConfirmationsMutation) SetField(name string, value ent.Value) error {
	switch name {
	case confirmations.FieldConfirmationType:
		v, ok := value.(confirmations.ConfirmationType)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetConfirmationType(v)
		return nil
	case confirmations.FieldConfirmationDate:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetConfirmationDate(v)
		return nil
	}
	return fmt.Errorf("unknown Confirmations field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *ConfirmationsMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *ConfirmationsMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ConfirmationsMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Confirmations numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *ConfirmationsMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *ConfirmationsMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *ConfirmationsMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Confirmations nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *ConfirmationsMutation) ResetField(name string) error {
	switch name {
	case confirmations.FieldConfirmationType:
		m.ResetConfirmationType()
		return nil
	case confirmations.FieldConfirmationDate:
		m.ResetConfirmationDate()
		return nil
	}
	return fmt.Errorf("unknown Confirmations field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *ConfirmationsMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *ConfirmationsMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *ConfirmationsMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *ConfirmationsMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *ConfirmationsMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *ConfirmationsMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *ConfirmationsMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Confirmations unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *ConfirmationsMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Confirmations edge %s", name)
}

// EmployeesMutation represents an operation that mutates the Employees nodes in the graph.
type EmployeesMutation struct {
	config
	op               Op
	typ              string
	id               *uuid.UUID
	create_time      *time.Time
	update_time      *time.Time
	employer_bin_iin *string
	name             *string
	middle_name      *string
	last_name        *string
	birthdate        *time.Time
	country          *string
	employee_iin     *string
	display_order    *int
	adddisplay_order *int
	clearedFields    map[string]struct{}
	done             bool
	oldValue         func(context.Context) (*Employees, error)
	predicates       []predicate.Employees
}

var _ ent.Mutation = (*EmployeesMutation)(nil)

// employeesOption allows management of the mutation configuration using functional options.
type employeesOption func(*EmployeesMutation)

// newEmployeesMutation creates new mutation for the Employees entity.
func newEmployeesMutation(c config, op Op, opts ...employeesOption) *EmployeesMutation {
	m := &EmployeesMutation{
		config:        c,
		op:            op,
		typ:           TypeEmployees,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withEmployeesID sets the ID field of the mutation.
func withEmployeesID(id uuid.UUID) employeesOption {
	return func(m *EmployeesMutation) {
		var (
			err   error
			once  sync.Once
			value *Employees
		)
		m.oldValue = func(ctx context.Context) (*Employees, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Employees.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withEmployees sets the old Employees of the mutation.
func withEmployees(node *Employees) employeesOption {
	return func(m *EmployeesMutation) {
		m.oldValue = func(context.Context) (*Employees, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m EmployeesMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m EmployeesMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Employees entities.
func (m *EmployeesMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *EmployeesMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *EmployeesMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Employees.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreateTime sets the "create_time" field.
func (m *EmployeesMutation) SetCreateTime(t time.Time) {
	m.create_time = &t
}

// CreateTime returns the value of the "create_time" field in the mutation.
func (m *EmployeesMutation) CreateTime() (r time.Time, exists bool) {
	v := m.create_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateTime returns the old "create_time" field's value of the Employees entity.
// If the Employees object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EmployeesMutation) OldCreateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateTime: %w", err)
	}
	return oldValue.CreateTime, nil
}

// ClearCreateTime clears the value of the "create_time" field.
func (m *EmployeesMutation) ClearCreateTime() {
	m.create_time = nil
	m.clearedFields[employees.FieldCreateTime] = struct{}{}
}

// CreateTimeCleared returns if the "create_time" field was cleared in this mutation.
func (m *EmployeesMutation) CreateTimeCleared() bool {
	_, ok := m.clearedFields[employees.FieldCreateTime]
	return ok
}

// ResetCreateTime resets all changes to the "create_time" field.
func (m *EmployeesMutation) ResetCreateTime() {
	m.create_time = nil
	delete(m.clearedFields, employees.FieldCreateTime)
}

// SetUpdateTime sets the "update_time" field.
func (m *EmployeesMutation) SetUpdateTime(t time.Time) {
	m.update_time = &t
}

// UpdateTime returns the value of the "update_time" field in the mutation.
func (m *EmployeesMutation) UpdateTime() (r time.Time, exists bool) {
	v := m.update_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateTime returns the old "update_time" field's value of the Employees entity.
// If the Employees object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EmployeesMutation) OldUpdateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateTime: %w", err)
	}
	return oldValue.UpdateTime, nil
}

// ClearUpdateTime clears the value of the "update_time" field.
func (m *EmployeesMutation) ClearUpdateTime() {
	m.update_time = nil
	m.clearedFields[employees.FieldUpdateTime] = struct{}{}
}

// UpdateTimeCleared returns if the "update_time" field was cleared in this mutation.
func (m *EmployeesMutation) UpdateTimeCleared() bool {
	_, ok := m.clearedFields[employees.FieldUpdateTime]
	return ok
}

// ResetUpdateTime resets all changes to the "update_time" field.
func (m *EmployeesMutation) ResetUpdateTime() {
	m.update_time = nil
	delete(m.clearedFields, employees.FieldUpdateTime)
}

// SetEmployerBinIin sets the "employer_bin_iin" field.
func (m *EmployeesMutation) SetEmployerBinIin(s string) {
	m.employer_bin_iin = &s
}

// EmployerBinIin returns the value of the "employer_bin_iin" field in the mutation.
func (m *EmployeesMutation) EmployerBinIin() (r string, exists bool) {
	v := m.employer_bin_iin
	if v == nil {
		return
	}
	return *v, true
}

// OldEmployerBinIin returns the old "employer_bin_iin" field's value of the Employees entity.
// If the Employees object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EmployeesMutation) OldEmployerBinIin(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEmployerBinIin is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEmployerBinIin requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEmployerBinIin: %w", err)
	}
	return oldValue.EmployerBinIin, nil
}

// ResetEmployerBinIin resets all changes to the "employer_bin_iin" field.
func (m *EmployeesMutation) ResetEmployerBinIin() {
	m.employer_bin_iin = nil
}

// SetName sets the "name" field.
func (m *EmployeesMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *EmployeesMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Employees entity.
// If the Employees object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EmployeesMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *EmployeesMutation) ResetName() {
	m.name = nil
}

// SetMiddleName sets the "middle_name" field.
func (m *EmployeesMutation) SetMiddleName(s string) {
	m.middle_name = &s
}

// MiddleName returns the value of the "middle_name" field in the mutation.
func (m *EmployeesMutation) MiddleName() (r string, exists bool) {
	v := m.middle_name
	if v == nil {
		return
	}
	return *v, true
}

// OldMiddleName returns the old "middle_name" field's value of the Employees entity.
// If the Employees object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EmployeesMutation) OldMiddleName(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMiddleName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMiddleName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMiddleName: %w", err)
	}
	return oldValue.MiddleName, nil
}

// ClearMiddleName clears the value of the "middle_name" field.
func (m *EmployeesMutation) ClearMiddleName() {
	m.middle_name = nil
	m.clearedFields[employees.FieldMiddleName] = struct{}{}
}

// MiddleNameCleared returns if the "middle_name" field was cleared in this mutation.
func (m *EmployeesMutation) MiddleNameCleared() bool {
	_, ok := m.clearedFields[employees.FieldMiddleName]
	return ok
}

// ResetMiddleName resets all changes to the "middle_name" field.
func (m *EmployeesMutation) ResetMiddleName() {
	m.middle_name = nil
	delete(m.clearedFields, employees.FieldMiddleName)
}

// SetLastName sets the "last_name" field.
func (m *EmployeesMutation) SetLastName(s string) {
	m.last_name = &s
}

// LastName returns the value of the "last_name" field in the mutation.
func (m *EmployeesMutation) LastName() (r string, exists bool) {
	v := m.last_name
	if v == nil {
		return
	}
	return *v, true
}

// OldLastName returns the old "last_name" field's value of the Employees entity.
// If the Employees object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EmployeesMutation) OldLastName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLastName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLastName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLastName: %w", err)
	}
	return oldValue.LastName, nil
}

// ResetLastName resets all changes to the "last_name" field.
func (m *EmployeesMutation) ResetLastName() {
	m.last_name = nil
}

// SetBirthdate sets the "birthdate" field.
func (m *EmployeesMutation) SetBirthdate(t time.Time) {
	m.birthdate = &t
}

// Birthdate returns the value of the "birthdate" field in the mutation.
func (m *EmployeesMutation) Birthdate() (r time.Time, exists bool) {
	v := m.birthdate
	if v == nil {
		return
	}
	return *v, true
}

// OldBirthdate returns the old "birthdate" field's value of the Employees entity.
// If the Employees object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EmployeesMutation) OldBirthdate(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBirthdate is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBirthdate requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBirthdate: %w", err)
	}
	return oldValue.Birthdate, nil
}

// ResetBirthdate resets all changes to the "birthdate" field.
func (m *EmployeesMutation) ResetBirthdate() {
	m.birthdate = nil
}

// SetCountry sets the "country" field.
func (m *EmployeesMutation) SetCountry(s string) {
	m.country = &s
}

// Country returns the value of the "country" field in the mutation.
func (m *EmployeesMutation) Country() (r string, exists bool) {
	v := m.country
	if v == nil {
		return
	}
	return *v, true
}

// OldCountry returns the old "country" field's value of the Employees entity.
// If the Employees object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EmployeesMutation) OldCountry(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCountry is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCountry requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCountry: %w", err)
	}
	return oldValue.Country, nil
}

// ResetCountry resets all changes to the "country" field.
func (m *EmployeesMutation) ResetCountry() {
	m.country = nil
}

// SetEmployeeIin sets the "employee_iin" field.
func (m *EmployeesMutation) SetEmployeeIin(s string) {
	m.employee_iin = &s
}

// EmployeeIin returns the value of the "employee_iin" field in the mutation.
func (m *EmployeesMutation) EmployeeIin() (r string, exists bool) {
	v := m.employee_iin
	if v == nil {
		return
	}
	return *v, true
}

// OldEmployeeIin returns the old "employee_iin" field's value of the Employees entity.
// If the Employees object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EmployeesMutation) OldEmployeeIin(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEmployeeIin is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEmployeeIin requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEmployeeIin: %w", err)
	}
	return oldValue.EmployeeIin, nil
}

// ResetEmployeeIin resets all changes to the "employee_iin" field.
func (m *EmployeesMutation) ResetEmployeeIin() {
	m.employee_iin = nil
}

// SetDisplayOrder sets the "display_order" field.
func (m *EmployeesMutation) SetDisplayOrder(i int) {
	m.display_order = &i
	m.adddisplay_order = nil
}

// DisplayOrder returns the value of the "display_order" field in the mutation.
func (m *EmployeesMutation) DisplayOrder() (r int, exists bool) {
	v := m.display_order
	if v == nil {
		return
	}
	return *v, true
}

// OldDisplayOrder returns the old "display_order" field's value of the Employees entity.
// If the Employees object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EmployeesMutation) OldDisplayOrder(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDisplayOrder is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDisplayOrder requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDisplayOrder: %w", err)
	}
	return oldValue.DisplayOrder, nil
}

// AddDisplayOrder adds i to the "display_order" field.
func (m *EmployeesMutation) AddDisplayOrder(i int) {
	if m.adddisplay_order != nil {
		*m.adddisplay_order += i
	} else {
		m.adddisplay_order = &i
	}
}

// AddedDisplayOrder returns the value that was added to the "display_order" field in this mutation.
func (m *EmployeesMutation) AddedDisplayOrder() (r int, exists bool) {
	v := m.adddisplay_order
	if v == nil {
		return
	}
	return *v, true
}

// ResetDisplayOrder resets all changes to the "display_order" field.
func (m *EmployeesMutation) ResetDisplayOrder() {
	m.display_order = nil
	m.adddisplay_order = nil
}

// Where appends a list predicates to the EmployeesMutation builder.
func (m *EmployeesMutation) Where(ps ...predicate.Employees) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the EmployeesMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *EmployeesMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Employees, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *EmployeesMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *EmployeesMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Employees).
func (m *EmployeesMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *EmployeesMutation) Fields() []string {
	fields := make([]string, 0, 10)
	if m.create_time != nil {
		fields = append(fields, employees.FieldCreateTime)
	}
	if m.update_time != nil {
		fields = append(fields, employees.FieldUpdateTime)
	}
	if m.employer_bin_iin != nil {
		fields = append(fields, employees.FieldEmployerBinIin)
	}
	if m.name != nil {
		fields = append(fields, employees.FieldName)
	}
	if m.middle_name != nil {
		fields = append(fields, employees.FieldMiddleName)
	}
	if m.last_name != nil {
		fields = append(fields, employees.FieldLastName)
	}
	if m.birthdate != nil {
		fields = append(fields, employees.FieldBirthdate)
	}
	if m.country != nil {
		fields = append(fields, employees.FieldCountry)
	}
	if m.employee_iin != nil {
		fields = append(fields, employees.FieldEmployeeIin)
	}
	if m.display_order != nil {
		fields = append(fields, employees.FieldDisplayOrder)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *EmployeesMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case employees.FieldCreateTime:
		return m.CreateTime()
	case employees.FieldUpdateTime:
		return m.UpdateTime()
	case employees.FieldEmployerBinIin:
		return m.EmployerBinIin()
	case employees.FieldName:
		return m.Name()
	case employees.FieldMiddleName:
		return m.MiddleName()
	case employees.FieldLastName:
		return m.LastName()
	case employees.FieldBirthdate:
		return m.Birthdate()
	case employees.FieldCountry:
		return m.Country()
	case employees.FieldEmployeeIin:
		return m.EmployeeIin()
	case employees.FieldDisplayOrder:
		return m.DisplayOrder()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *EmployeesMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case employees.FieldCreateTime:
		return m.OldCreateTime(ctx)
	case employees.FieldUpdateTime:
		return m.OldUpdateTime(ctx)
	case employees.FieldEmployerBinIin:
		return m.OldEmployerBinIin(ctx)
	case employees.FieldName:
		return m.OldName(ctx)
	case employees.FieldMiddleName:
		return m.OldMiddleName(ctx)
	case employees.FieldLastName:
		return m.OldLastName(ctx)
	case employees.FieldBirthdate:
		return m.OldBirthdate(ctx)
	case employees.FieldCountry:
		return m.OldCountry(ctx)
	case employees.FieldEmployeeIin:
		return m.OldEmployeeIin(ctx)
	case employees.FieldDisplayOrder:
		return m.OldDisplayOrder(ctx)
	}
	return nil, fmt.Errorf("unknown Employees field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *EmployeesMutation) SetField(name string, value ent.Value) error {
	switch name {
	case employees.FieldCreateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateTime(v)
		return nil
	case employees.FieldUpdateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateTime(v)
		return nil
	case employees.FieldEmployerBinIin:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEmployerBinIin(v)
		return nil
	case employees.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case employees.FieldMiddleName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMiddleName(v)
		return nil
	case employees.FieldLastName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLastName(v)
		return nil
	case employees.FieldBirthdate:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBirthdate(v)
		return nil
	case employees.FieldCountry:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCountry(v)
		return nil
	case employees.FieldEmployeeIin:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEmployeeIin(v)
		return nil
	case employees.FieldDisplayOrder:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDisplayOrder(v)
		return nil
	}
	return fmt.Errorf("unknown Employees field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *EmployeesMutation) AddedFields() []string {
	var fields []string
	if m.adddisplay_order != nil {
		fields = append(fields, employees.FieldDisplayOrder)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *EmployeesMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case employees.FieldDisplayOrder:
		return m.AddedDisplayOrder()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *EmployeesMutation) AddField(name string, value ent.Value) error {
	switch name {
	case employees.FieldDisplayOrder:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddDisplayOrder(v)
		return nil
	}
	return fmt.Errorf("unknown Employees numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *EmployeesMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(employees.FieldCreateTime) {
		fields = append(fields, employees.FieldCreateTime)
	}
	if m.FieldCleared(employees.FieldUpdateTime) {
		fields = append(fields, employees.FieldUpdateTime)
	}
	if m.FieldCleared(employees.FieldMiddleName) {
		fields = append(fields, employees.FieldMiddleName)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *EmployeesMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *EmployeesMutation) ClearField(name string) error {
	switch name {
	case employees.FieldCreateTime:
		m.ClearCreateTime()
		return nil
	case employees.FieldUpdateTime:
		m.ClearUpdateTime()
		return nil
	case employees.FieldMiddleName:
		m.ClearMiddleName()
		return nil
	}
	return fmt.Errorf("unknown Employees nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *EmployeesMutation) ResetField(name string) error {
	switch name {
	case employees.FieldCreateTime:
		m.ResetCreateTime()
		return nil
	case employees.FieldUpdateTime:
		m.ResetUpdateTime()
		return nil
	case employees.FieldEmployerBinIin:
		m.ResetEmployerBinIin()
		return nil
	case employees.FieldName:
		m.ResetName()
		return nil
	case employees.FieldMiddleName:
		m.ResetMiddleName()
		return nil
	case employees.FieldLastName:
		m.ResetLastName()
		return nil
	case employees.FieldBirthdate:
		m.ResetBirthdate()
		return nil
	case employees.FieldCountry:
		m.ResetCountry()
		return nil
	case employees.FieldEmployeeIin:
		m.ResetEmployeeIin()
		return nil
	case employees.FieldDisplayOrder:
		m.ResetDisplayOrder()
		return nil
	}
	return fmt.Errorf("unknown Employees field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *EmployeesMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *EmployeesMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *EmployeesMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *EmployeesMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *EmployeesMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *EmployeesMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *EmployeesMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Employees unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *EmployeesMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Employees edge %s", name)
}

// GeneratedDocumentMutation represents an operation that mutates the GeneratedDocument nodes in the graph.
type GeneratedDocumentMutation struct {
	config
	op                                Op
	typ                               string
	id                                *uuid.UUID
	create_time                       *time.Time
	update_time                       *time.Time
	parent_transaction_id             *uuid.UUID
	generated_document_integration_id *uuid.UUID
	generated_document_type           *string
	clearedFields                     map[string]struct{}
	done                              bool
	oldValue                          func(context.Context) (*GeneratedDocument, error)
	predicates                        []predicate.GeneratedDocument
}

var _ ent.Mutation = (*GeneratedDocumentMutation)(nil)

// generateddocumentOption allows management of the mutation configuration using functional options.
type generateddocumentOption func(*GeneratedDocumentMutation)

// newGeneratedDocumentMutation creates new mutation for the GeneratedDocument entity.
func newGeneratedDocumentMutation(c config, op Op, opts ...generateddocumentOption) *GeneratedDocumentMutation {
	m := &GeneratedDocumentMutation{
		config:        c,
		op:            op,
		typ:           TypeGeneratedDocument,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withGeneratedDocumentID sets the ID field of the mutation.
func withGeneratedDocumentID(id uuid.UUID) generateddocumentOption {
	return func(m *GeneratedDocumentMutation) {
		var (
			err   error
			once  sync.Once
			value *GeneratedDocument
		)
		m.oldValue = func(ctx context.Context) (*GeneratedDocument, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().GeneratedDocument.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withGeneratedDocument sets the old GeneratedDocument of the mutation.
func withGeneratedDocument(node *GeneratedDocument) generateddocumentOption {
	return func(m *GeneratedDocumentMutation) {
		m.oldValue = func(context.Context) (*GeneratedDocument, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m GeneratedDocumentMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m GeneratedDocumentMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of GeneratedDocument entities.
func (m *GeneratedDocumentMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *GeneratedDocumentMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *GeneratedDocumentMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().GeneratedDocument.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreateTime sets the "create_time" field.
func (m *GeneratedDocumentMutation) SetCreateTime(t time.Time) {
	m.create_time = &t
}

// CreateTime returns the value of the "create_time" field in the mutation.
func (m *GeneratedDocumentMutation) CreateTime() (r time.Time, exists bool) {
	v := m.create_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateTime returns the old "create_time" field's value of the GeneratedDocument entity.
// If the GeneratedDocument object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GeneratedDocumentMutation) OldCreateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateTime: %w", err)
	}
	return oldValue.CreateTime, nil
}

// ClearCreateTime clears the value of the "create_time" field.
func (m *GeneratedDocumentMutation) ClearCreateTime() {
	m.create_time = nil
	m.clearedFields[generateddocument.FieldCreateTime] = struct{}{}
}

// CreateTimeCleared returns if the "create_time" field was cleared in this mutation.
func (m *GeneratedDocumentMutation) CreateTimeCleared() bool {
	_, ok := m.clearedFields[generateddocument.FieldCreateTime]
	return ok
}

// ResetCreateTime resets all changes to the "create_time" field.
func (m *GeneratedDocumentMutation) ResetCreateTime() {
	m.create_time = nil
	delete(m.clearedFields, generateddocument.FieldCreateTime)
}

// SetUpdateTime sets the "update_time" field.
func (m *GeneratedDocumentMutation) SetUpdateTime(t time.Time) {
	m.update_time = &t
}

// UpdateTime returns the value of the "update_time" field in the mutation.
func (m *GeneratedDocumentMutation) UpdateTime() (r time.Time, exists bool) {
	v := m.update_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateTime returns the old "update_time" field's value of the GeneratedDocument entity.
// If the GeneratedDocument object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GeneratedDocumentMutation) OldUpdateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateTime: %w", err)
	}
	return oldValue.UpdateTime, nil
}

// ClearUpdateTime clears the value of the "update_time" field.
func (m *GeneratedDocumentMutation) ClearUpdateTime() {
	m.update_time = nil
	m.clearedFields[generateddocument.FieldUpdateTime] = struct{}{}
}

// UpdateTimeCleared returns if the "update_time" field was cleared in this mutation.
func (m *GeneratedDocumentMutation) UpdateTimeCleared() bool {
	_, ok := m.clearedFields[generateddocument.FieldUpdateTime]
	return ok
}

// ResetUpdateTime resets all changes to the "update_time" field.
func (m *GeneratedDocumentMutation) ResetUpdateTime() {
	m.update_time = nil
	delete(m.clearedFields, generateddocument.FieldUpdateTime)
}

// SetParentTransactionID sets the "parent_transaction_id" field.
func (m *GeneratedDocumentMutation) SetParentTransactionID(u uuid.UUID) {
	m.parent_transaction_id = &u
}

// ParentTransactionID returns the value of the "parent_transaction_id" field in the mutation.
func (m *GeneratedDocumentMutation) ParentTransactionID() (r uuid.UUID, exists bool) {
	v := m.parent_transaction_id
	if v == nil {
		return
	}
	return *v, true
}

// OldParentTransactionID returns the old "parent_transaction_id" field's value of the GeneratedDocument entity.
// If the GeneratedDocument object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GeneratedDocumentMutation) OldParentTransactionID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldParentTransactionID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldParentTransactionID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldParentTransactionID: %w", err)
	}
	return oldValue.ParentTransactionID, nil
}

// ResetParentTransactionID resets all changes to the "parent_transaction_id" field.
func (m *GeneratedDocumentMutation) ResetParentTransactionID() {
	m.parent_transaction_id = nil
}

// SetGeneratedDocumentIntegrationID sets the "generated_document_integration_id" field.
func (m *GeneratedDocumentMutation) SetGeneratedDocumentIntegrationID(u uuid.UUID) {
	m.generated_document_integration_id = &u
}

// GeneratedDocumentIntegrationID returns the value of the "generated_document_integration_id" field in the mutation.
func (m *GeneratedDocumentMutation) GeneratedDocumentIntegrationID() (r uuid.UUID, exists bool) {
	v := m.generated_document_integration_id
	if v == nil {
		return
	}
	return *v, true
}

// OldGeneratedDocumentIntegrationID returns the old "generated_document_integration_id" field's value of the GeneratedDocument entity.
// If the GeneratedDocument object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GeneratedDocumentMutation) OldGeneratedDocumentIntegrationID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldGeneratedDocumentIntegrationID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldGeneratedDocumentIntegrationID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldGeneratedDocumentIntegrationID: %w", err)
	}
	return oldValue.GeneratedDocumentIntegrationID, nil
}

// ResetGeneratedDocumentIntegrationID resets all changes to the "generated_document_integration_id" field.
func (m *GeneratedDocumentMutation) ResetGeneratedDocumentIntegrationID() {
	m.generated_document_integration_id = nil
}

// SetGeneratedDocumentType sets the "generated_document_type" field.
func (m *GeneratedDocumentMutation) SetGeneratedDocumentType(s string) {
	m.generated_document_type = &s
}

// GeneratedDocumentType returns the value of the "generated_document_type" field in the mutation.
func (m *GeneratedDocumentMutation) GeneratedDocumentType() (r string, exists bool) {
	v := m.generated_document_type
	if v == nil {
		return
	}
	return *v, true
}

// OldGeneratedDocumentType returns the old "generated_document_type" field's value of the GeneratedDocument entity.
// If the GeneratedDocument object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *GeneratedDocumentMutation) OldGeneratedDocumentType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldGeneratedDocumentType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldGeneratedDocumentType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldGeneratedDocumentType: %w", err)
	}
	return oldValue.GeneratedDocumentType, nil
}

// ResetGeneratedDocumentType resets all changes to the "generated_document_type" field.
func (m *GeneratedDocumentMutation) ResetGeneratedDocumentType() {
	m.generated_document_type = nil
}

// Where appends a list predicates to the GeneratedDocumentMutation builder.
func (m *GeneratedDocumentMutation) Where(ps ...predicate.GeneratedDocument) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the GeneratedDocumentMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *GeneratedDocumentMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.GeneratedDocument, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *GeneratedDocumentMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *GeneratedDocumentMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (GeneratedDocument).
func (m *GeneratedDocumentMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *GeneratedDocumentMutation) Fields() []string {
	fields := make([]string, 0, 5)
	if m.create_time != nil {
		fields = append(fields, generateddocument.FieldCreateTime)
	}
	if m.update_time != nil {
		fields = append(fields, generateddocument.FieldUpdateTime)
	}
	if m.parent_transaction_id != nil {
		fields = append(fields, generateddocument.FieldParentTransactionID)
	}
	if m.generated_document_integration_id != nil {
		fields = append(fields, generateddocument.FieldGeneratedDocumentIntegrationID)
	}
	if m.generated_document_type != nil {
		fields = append(fields, generateddocument.FieldGeneratedDocumentType)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *GeneratedDocumentMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case generateddocument.FieldCreateTime:
		return m.CreateTime()
	case generateddocument.FieldUpdateTime:
		return m.UpdateTime()
	case generateddocument.FieldParentTransactionID:
		return m.ParentTransactionID()
	case generateddocument.FieldGeneratedDocumentIntegrationID:
		return m.GeneratedDocumentIntegrationID()
	case generateddocument.FieldGeneratedDocumentType:
		return m.GeneratedDocumentType()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *GeneratedDocumentMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case generateddocument.FieldCreateTime:
		return m.OldCreateTime(ctx)
	case generateddocument.FieldUpdateTime:
		return m.OldUpdateTime(ctx)
	case generateddocument.FieldParentTransactionID:
		return m.OldParentTransactionID(ctx)
	case generateddocument.FieldGeneratedDocumentIntegrationID:
		return m.OldGeneratedDocumentIntegrationID(ctx)
	case generateddocument.FieldGeneratedDocumentType:
		return m.OldGeneratedDocumentType(ctx)
	}
	return nil, fmt.Errorf("unknown GeneratedDocument field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *GeneratedDocumentMutation) SetField(name string, value ent.Value) error {
	switch name {
	case generateddocument.FieldCreateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateTime(v)
		return nil
	case generateddocument.FieldUpdateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateTime(v)
		return nil
	case generateddocument.FieldParentTransactionID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetParentTransactionID(v)
		return nil
	case generateddocument.FieldGeneratedDocumentIntegrationID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetGeneratedDocumentIntegrationID(v)
		return nil
	case generateddocument.FieldGeneratedDocumentType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetGeneratedDocumentType(v)
		return nil
	}
	return fmt.Errorf("unknown GeneratedDocument field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *GeneratedDocumentMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *GeneratedDocumentMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *GeneratedDocumentMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown GeneratedDocument numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *GeneratedDocumentMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(generateddocument.FieldCreateTime) {
		fields = append(fields, generateddocument.FieldCreateTime)
	}
	if m.FieldCleared(generateddocument.FieldUpdateTime) {
		fields = append(fields, generateddocument.FieldUpdateTime)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *GeneratedDocumentMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *GeneratedDocumentMutation) ClearField(name string) error {
	switch name {
	case generateddocument.FieldCreateTime:
		m.ClearCreateTime()
		return nil
	case generateddocument.FieldUpdateTime:
		m.ClearUpdateTime()
		return nil
	}
	return fmt.Errorf("unknown GeneratedDocument nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *GeneratedDocumentMutation) ResetField(name string) error {
	switch name {
	case generateddocument.FieldCreateTime:
		m.ResetCreateTime()
		return nil
	case generateddocument.FieldUpdateTime:
		m.ResetUpdateTime()
		return nil
	case generateddocument.FieldParentTransactionID:
		m.ResetParentTransactionID()
		return nil
	case generateddocument.FieldGeneratedDocumentIntegrationID:
		m.ResetGeneratedDocumentIntegrationID()
		return nil
	case generateddocument.FieldGeneratedDocumentType:
		m.ResetGeneratedDocumentType()
		return nil
	}
	return fmt.Errorf("unknown GeneratedDocument field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *GeneratedDocumentMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *GeneratedDocumentMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *GeneratedDocumentMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *GeneratedDocumentMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *GeneratedDocumentMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *GeneratedDocumentMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *GeneratedDocumentMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown GeneratedDocument unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *GeneratedDocumentMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown GeneratedDocument edge %s", name)
}

// HealthMutation represents an operation that mutates the Health nodes in the graph.
type HealthMutation struct {
	config
	op            Op
	typ           string
	id            *uuid.UUID
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Health, error)
	predicates    []predicate.Health
}

var _ ent.Mutation = (*HealthMutation)(nil)

// healthOption allows management of the mutation configuration using functional options.
type healthOption func(*HealthMutation)

// newHealthMutation creates new mutation for the Health entity.
func newHealthMutation(c config, op Op, opts ...healthOption) *HealthMutation {
	m := &HealthMutation{
		config:        c,
		op:            op,
		typ:           TypeHealth,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withHealthID sets the ID field of the mutation.
func withHealthID(id uuid.UUID) healthOption {
	return func(m *HealthMutation) {
		var (
			err   error
			once  sync.Once
			value *Health
		)
		m.oldValue = func(ctx context.Context) (*Health, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Health.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withHealth sets the old Health of the mutation.
func withHealth(node *Health) healthOption {
	return func(m *HealthMutation) {
		m.oldValue = func(context.Context) (*Health, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m HealthMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m HealthMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Health entities.
func (m *HealthMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *HealthMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *HealthMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Health.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// Where appends a list predicates to the HealthMutation builder.
func (m *HealthMutation) Where(ps ...predicate.Health) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the HealthMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *HealthMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Health, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *HealthMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *HealthMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Health).
func (m *HealthMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *HealthMutation) Fields() []string {
	fields := make([]string, 0, 0)
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *HealthMutation) Field(name string) (ent.Value, bool) {
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *HealthMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	return nil, fmt.Errorf("unknown Health field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *HealthMutation) SetField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Health field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *HealthMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *HealthMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *HealthMutation) AddField(name string, value ent.Value) error {
	return fmt.Errorf("unknown Health numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *HealthMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *HealthMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *HealthMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Health nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *HealthMutation) ResetField(name string) error {
	return fmt.Errorf("unknown Health field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *HealthMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *HealthMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *HealthMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *HealthMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *HealthMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *HealthMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *HealthMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Health unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *HealthMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Health edge %s", name)
}

// PaymentsMutation represents an operation that mutates the Payments nodes in the graph.
type PaymentsMutation struct {
	config
	op             Op
	typ            string
	id             *uuid.UUID
	create_time    *time.Time
	update_time    *time.Time
	payment_code   *string
	payment_type   *payments.PaymentType
	payment_period *string
	kbk            *string
	employee_list  *map[string]interface{}
	clearedFields  map[string]struct{}
	done           bool
	oldValue       func(context.Context) (*Payments, error)
	predicates     []predicate.Payments
}

var _ ent.Mutation = (*PaymentsMutation)(nil)

// paymentsOption allows management of the mutation configuration using functional options.
type paymentsOption func(*PaymentsMutation)

// newPaymentsMutation creates new mutation for the Payments entity.
func newPaymentsMutation(c config, op Op, opts ...paymentsOption) *PaymentsMutation {
	m := &PaymentsMutation{
		config:        c,
		op:            op,
		typ:           TypePayments,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withPaymentsID sets the ID field of the mutation.
func withPaymentsID(id uuid.UUID) paymentsOption {
	return func(m *PaymentsMutation) {
		var (
			err   error
			once  sync.Once
			value *Payments
		)
		m.oldValue = func(ctx context.Context) (*Payments, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Payments.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withPayments sets the old Payments of the mutation.
func withPayments(node *Payments) paymentsOption {
	return func(m *PaymentsMutation) {
		m.oldValue = func(context.Context) (*Payments, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m PaymentsMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m PaymentsMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Payments entities.
func (m *PaymentsMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *PaymentsMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *PaymentsMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Payments.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreateTime sets the "create_time" field.
func (m *PaymentsMutation) SetCreateTime(t time.Time) {
	m.create_time = &t
}

// CreateTime returns the value of the "create_time" field in the mutation.
func (m *PaymentsMutation) CreateTime() (r time.Time, exists bool) {
	v := m.create_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateTime returns the old "create_time" field's value of the Payments entity.
// If the Payments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PaymentsMutation) OldCreateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateTime: %w", err)
	}
	return oldValue.CreateTime, nil
}

// ClearCreateTime clears the value of the "create_time" field.
func (m *PaymentsMutation) ClearCreateTime() {
	m.create_time = nil
	m.clearedFields[payments.FieldCreateTime] = struct{}{}
}

// CreateTimeCleared returns if the "create_time" field was cleared in this mutation.
func (m *PaymentsMutation) CreateTimeCleared() bool {
	_, ok := m.clearedFields[payments.FieldCreateTime]
	return ok
}

// ResetCreateTime resets all changes to the "create_time" field.
func (m *PaymentsMutation) ResetCreateTime() {
	m.create_time = nil
	delete(m.clearedFields, payments.FieldCreateTime)
}

// SetUpdateTime sets the "update_time" field.
func (m *PaymentsMutation) SetUpdateTime(t time.Time) {
	m.update_time = &t
}

// UpdateTime returns the value of the "update_time" field in the mutation.
func (m *PaymentsMutation) UpdateTime() (r time.Time, exists bool) {
	v := m.update_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateTime returns the old "update_time" field's value of the Payments entity.
// If the Payments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PaymentsMutation) OldUpdateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateTime: %w", err)
	}
	return oldValue.UpdateTime, nil
}

// ClearUpdateTime clears the value of the "update_time" field.
func (m *PaymentsMutation) ClearUpdateTime() {
	m.update_time = nil
	m.clearedFields[payments.FieldUpdateTime] = struct{}{}
}

// UpdateTimeCleared returns if the "update_time" field was cleared in this mutation.
func (m *PaymentsMutation) UpdateTimeCleared() bool {
	_, ok := m.clearedFields[payments.FieldUpdateTime]
	return ok
}

// ResetUpdateTime resets all changes to the "update_time" field.
func (m *PaymentsMutation) ResetUpdateTime() {
	m.update_time = nil
	delete(m.clearedFields, payments.FieldUpdateTime)
}

// SetPaymentCode sets the "payment_code" field.
func (m *PaymentsMutation) SetPaymentCode(s string) {
	m.payment_code = &s
}

// PaymentCode returns the value of the "payment_code" field in the mutation.
func (m *PaymentsMutation) PaymentCode() (r string, exists bool) {
	v := m.payment_code
	if v == nil {
		return
	}
	return *v, true
}

// OldPaymentCode returns the old "payment_code" field's value of the Payments entity.
// If the Payments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PaymentsMutation) OldPaymentCode(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPaymentCode is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPaymentCode requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPaymentCode: %w", err)
	}
	return oldValue.PaymentCode, nil
}

// ResetPaymentCode resets all changes to the "payment_code" field.
func (m *PaymentsMutation) ResetPaymentCode() {
	m.payment_code = nil
}

// SetPaymentType sets the "payment_type" field.
func (m *PaymentsMutation) SetPaymentType(pt payments.PaymentType) {
	m.payment_type = &pt
}

// PaymentType returns the value of the "payment_type" field in the mutation.
func (m *PaymentsMutation) PaymentType() (r payments.PaymentType, exists bool) {
	v := m.payment_type
	if v == nil {
		return
	}
	return *v, true
}

// OldPaymentType returns the old "payment_type" field's value of the Payments entity.
// If the Payments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PaymentsMutation) OldPaymentType(ctx context.Context) (v *payments.PaymentType, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPaymentType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPaymentType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPaymentType: %w", err)
	}
	return oldValue.PaymentType, nil
}

// ClearPaymentType clears the value of the "payment_type" field.
func (m *PaymentsMutation) ClearPaymentType() {
	m.payment_type = nil
	m.clearedFields[payments.FieldPaymentType] = struct{}{}
}

// PaymentTypeCleared returns if the "payment_type" field was cleared in this mutation.
func (m *PaymentsMutation) PaymentTypeCleared() bool {
	_, ok := m.clearedFields[payments.FieldPaymentType]
	return ok
}

// ResetPaymentType resets all changes to the "payment_type" field.
func (m *PaymentsMutation) ResetPaymentType() {
	m.payment_type = nil
	delete(m.clearedFields, payments.FieldPaymentType)
}

// SetPaymentPeriod sets the "payment_period" field.
func (m *PaymentsMutation) SetPaymentPeriod(s string) {
	m.payment_period = &s
}

// PaymentPeriod returns the value of the "payment_period" field in the mutation.
func (m *PaymentsMutation) PaymentPeriod() (r string, exists bool) {
	v := m.payment_period
	if v == nil {
		return
	}
	return *v, true
}

// OldPaymentPeriod returns the old "payment_period" field's value of the Payments entity.
// If the Payments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PaymentsMutation) OldPaymentPeriod(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPaymentPeriod is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPaymentPeriod requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPaymentPeriod: %w", err)
	}
	return oldValue.PaymentPeriod, nil
}

// ResetPaymentPeriod resets all changes to the "payment_period" field.
func (m *PaymentsMutation) ResetPaymentPeriod() {
	m.payment_period = nil
}

// SetKbk sets the "kbk" field.
func (m *PaymentsMutation) SetKbk(s string) {
	m.kbk = &s
}

// Kbk returns the value of the "kbk" field in the mutation.
func (m *PaymentsMutation) Kbk() (r string, exists bool) {
	v := m.kbk
	if v == nil {
		return
	}
	return *v, true
}

// OldKbk returns the old "kbk" field's value of the Payments entity.
// If the Payments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PaymentsMutation) OldKbk(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldKbk is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldKbk requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldKbk: %w", err)
	}
	return oldValue.Kbk, nil
}

// ResetKbk resets all changes to the "kbk" field.
func (m *PaymentsMutation) ResetKbk() {
	m.kbk = nil
}

// SetEmployeeList sets the "employee_list" field.
func (m *PaymentsMutation) SetEmployeeList(value map[string]interface{}) {
	m.employee_list = &value
}

// EmployeeList returns the value of the "employee_list" field in the mutation.
func (m *PaymentsMutation) EmployeeList() (r map[string]interface{}, exists bool) {
	v := m.employee_list
	if v == nil {
		return
	}
	return *v, true
}

// OldEmployeeList returns the old "employee_list" field's value of the Payments entity.
// If the Payments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PaymentsMutation) OldEmployeeList(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEmployeeList is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEmployeeList requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEmployeeList: %w", err)
	}
	return oldValue.EmployeeList, nil
}

// ClearEmployeeList clears the value of the "employee_list" field.
func (m *PaymentsMutation) ClearEmployeeList() {
	m.employee_list = nil
	m.clearedFields[payments.FieldEmployeeList] = struct{}{}
}

// EmployeeListCleared returns if the "employee_list" field was cleared in this mutation.
func (m *PaymentsMutation) EmployeeListCleared() bool {
	_, ok := m.clearedFields[payments.FieldEmployeeList]
	return ok
}

// ResetEmployeeList resets all changes to the "employee_list" field.
func (m *PaymentsMutation) ResetEmployeeList() {
	m.employee_list = nil
	delete(m.clearedFields, payments.FieldEmployeeList)
}

// Where appends a list predicates to the PaymentsMutation builder.
func (m *PaymentsMutation) Where(ps ...predicate.Payments) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the PaymentsMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *PaymentsMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Payments, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *PaymentsMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *PaymentsMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Payments).
func (m *PaymentsMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *PaymentsMutation) Fields() []string {
	fields := make([]string, 0, 7)
	if m.create_time != nil {
		fields = append(fields, payments.FieldCreateTime)
	}
	if m.update_time != nil {
		fields = append(fields, payments.FieldUpdateTime)
	}
	if m.payment_code != nil {
		fields = append(fields, payments.FieldPaymentCode)
	}
	if m.payment_type != nil {
		fields = append(fields, payments.FieldPaymentType)
	}
	if m.payment_period != nil {
		fields = append(fields, payments.FieldPaymentPeriod)
	}
	if m.kbk != nil {
		fields = append(fields, payments.FieldKbk)
	}
	if m.employee_list != nil {
		fields = append(fields, payments.FieldEmployeeList)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *PaymentsMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case payments.FieldCreateTime:
		return m.CreateTime()
	case payments.FieldUpdateTime:
		return m.UpdateTime()
	case payments.FieldPaymentCode:
		return m.PaymentCode()
	case payments.FieldPaymentType:
		return m.PaymentType()
	case payments.FieldPaymentPeriod:
		return m.PaymentPeriod()
	case payments.FieldKbk:
		return m.Kbk()
	case payments.FieldEmployeeList:
		return m.EmployeeList()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *PaymentsMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case payments.FieldCreateTime:
		return m.OldCreateTime(ctx)
	case payments.FieldUpdateTime:
		return m.OldUpdateTime(ctx)
	case payments.FieldPaymentCode:
		return m.OldPaymentCode(ctx)
	case payments.FieldPaymentType:
		return m.OldPaymentType(ctx)
	case payments.FieldPaymentPeriod:
		return m.OldPaymentPeriod(ctx)
	case payments.FieldKbk:
		return m.OldKbk(ctx)
	case payments.FieldEmployeeList:
		return m.OldEmployeeList(ctx)
	}
	return nil, fmt.Errorf("unknown Payments field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *PaymentsMutation) SetField(name string, value ent.Value) error {
	switch name {
	case payments.FieldCreateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateTime(v)
		return nil
	case payments.FieldUpdateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateTime(v)
		return nil
	case payments.FieldPaymentCode:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPaymentCode(v)
		return nil
	case payments.FieldPaymentType:
		v, ok := value.(payments.PaymentType)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPaymentType(v)
		return nil
	case payments.FieldPaymentPeriod:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPaymentPeriod(v)
		return nil
	case payments.FieldKbk:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetKbk(v)
		return nil
	case payments.FieldEmployeeList:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEmployeeList(v)
		return nil
	}
	return fmt.Errorf("unknown Payments field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *PaymentsMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *PaymentsMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *PaymentsMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Payments numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *PaymentsMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(payments.FieldCreateTime) {
		fields = append(fields, payments.FieldCreateTime)
	}
	if m.FieldCleared(payments.FieldUpdateTime) {
		fields = append(fields, payments.FieldUpdateTime)
	}
	if m.FieldCleared(payments.FieldPaymentType) {
		fields = append(fields, payments.FieldPaymentType)
	}
	if m.FieldCleared(payments.FieldEmployeeList) {
		fields = append(fields, payments.FieldEmployeeList)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *PaymentsMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *PaymentsMutation) ClearField(name string) error {
	switch name {
	case payments.FieldCreateTime:
		m.ClearCreateTime()
		return nil
	case payments.FieldUpdateTime:
		m.ClearUpdateTime()
		return nil
	case payments.FieldPaymentType:
		m.ClearPaymentType()
		return nil
	case payments.FieldEmployeeList:
		m.ClearEmployeeList()
		return nil
	}
	return fmt.Errorf("unknown Payments nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *PaymentsMutation) ResetField(name string) error {
	switch name {
	case payments.FieldCreateTime:
		m.ResetCreateTime()
		return nil
	case payments.FieldUpdateTime:
		m.ResetUpdateTime()
		return nil
	case payments.FieldPaymentCode:
		m.ResetPaymentCode()
		return nil
	case payments.FieldPaymentType:
		m.ResetPaymentType()
		return nil
	case payments.FieldPaymentPeriod:
		m.ResetPaymentPeriod()
		return nil
	case payments.FieldKbk:
		m.ResetKbk()
		return nil
	case payments.FieldEmployeeList:
		m.ResetEmployeeList()
		return nil
	}
	return fmt.Errorf("unknown Payments field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *PaymentsMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *PaymentsMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *PaymentsMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *PaymentsMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *PaymentsMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *PaymentsMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *PaymentsMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Payments unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *PaymentsMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Payments edge %s", name)
}

// RejectionsMutation represents an operation that mutates the Rejections nodes in the graph.
type RejectionsMutation struct {
	config
	op                    Op
	typ                   string
	id                    *uuid.UUID
	create_time           *time.Time
	update_time           *time.Time
	parent_transaction_id *uuid.UUID
	rejection_source      *rejections.RejectionSource
	rejection_code        *string
	rejection_score       *string
	rejection_reason      *string
	clearedFields         map[string]struct{}
	done                  bool
	oldValue              func(context.Context) (*Rejections, error)
	predicates            []predicate.Rejections
}

var _ ent.Mutation = (*RejectionsMutation)(nil)

// rejectionsOption allows management of the mutation configuration using functional options.
type rejectionsOption func(*RejectionsMutation)

// newRejectionsMutation creates new mutation for the Rejections entity.
func newRejectionsMutation(c config, op Op, opts ...rejectionsOption) *RejectionsMutation {
	m := &RejectionsMutation{
		config:        c,
		op:            op,
		typ:           TypeRejections,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withRejectionsID sets the ID field of the mutation.
func withRejectionsID(id uuid.UUID) rejectionsOption {
	return func(m *RejectionsMutation) {
		var (
			err   error
			once  sync.Once
			value *Rejections
		)
		m.oldValue = func(ctx context.Context) (*Rejections, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Rejections.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withRejections sets the old Rejections of the mutation.
func withRejections(node *Rejections) rejectionsOption {
	return func(m *RejectionsMutation) {
		m.oldValue = func(context.Context) (*Rejections, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m RejectionsMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m RejectionsMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Rejections entities.
func (m *RejectionsMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *RejectionsMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *RejectionsMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Rejections.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreateTime sets the "create_time" field.
func (m *RejectionsMutation) SetCreateTime(t time.Time) {
	m.create_time = &t
}

// CreateTime returns the value of the "create_time" field in the mutation.
func (m *RejectionsMutation) CreateTime() (r time.Time, exists bool) {
	v := m.create_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateTime returns the old "create_time" field's value of the Rejections entity.
// If the Rejections object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *RejectionsMutation) OldCreateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateTime: %w", err)
	}
	return oldValue.CreateTime, nil
}

// ClearCreateTime clears the value of the "create_time" field.
func (m *RejectionsMutation) ClearCreateTime() {
	m.create_time = nil
	m.clearedFields[rejections.FieldCreateTime] = struct{}{}
}

// CreateTimeCleared returns if the "create_time" field was cleared in this mutation.
func (m *RejectionsMutation) CreateTimeCleared() bool {
	_, ok := m.clearedFields[rejections.FieldCreateTime]
	return ok
}

// ResetCreateTime resets all changes to the "create_time" field.
func (m *RejectionsMutation) ResetCreateTime() {
	m.create_time = nil
	delete(m.clearedFields, rejections.FieldCreateTime)
}

// SetUpdateTime sets the "update_time" field.
func (m *RejectionsMutation) SetUpdateTime(t time.Time) {
	m.update_time = &t
}

// UpdateTime returns the value of the "update_time" field in the mutation.
func (m *RejectionsMutation) UpdateTime() (r time.Time, exists bool) {
	v := m.update_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateTime returns the old "update_time" field's value of the Rejections entity.
// If the Rejections object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *RejectionsMutation) OldUpdateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateTime: %w", err)
	}
	return oldValue.UpdateTime, nil
}

// ClearUpdateTime clears the value of the "update_time" field.
func (m *RejectionsMutation) ClearUpdateTime() {
	m.update_time = nil
	m.clearedFields[rejections.FieldUpdateTime] = struct{}{}
}

// UpdateTimeCleared returns if the "update_time" field was cleared in this mutation.
func (m *RejectionsMutation) UpdateTimeCleared() bool {
	_, ok := m.clearedFields[rejections.FieldUpdateTime]
	return ok
}

// ResetUpdateTime resets all changes to the "update_time" field.
func (m *RejectionsMutation) ResetUpdateTime() {
	m.update_time = nil
	delete(m.clearedFields, rejections.FieldUpdateTime)
}

// SetParentTransactionID sets the "parent_transaction_id" field.
func (m *RejectionsMutation) SetParentTransactionID(u uuid.UUID) {
	m.parent_transaction_id = &u
}

// ParentTransactionID returns the value of the "parent_transaction_id" field in the mutation.
func (m *RejectionsMutation) ParentTransactionID() (r uuid.UUID, exists bool) {
	v := m.parent_transaction_id
	if v == nil {
		return
	}
	return *v, true
}

// OldParentTransactionID returns the old "parent_transaction_id" field's value of the Rejections entity.
// If the Rejections object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *RejectionsMutation) OldParentTransactionID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldParentTransactionID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldParentTransactionID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldParentTransactionID: %w", err)
	}
	return oldValue.ParentTransactionID, nil
}

// ResetParentTransactionID resets all changes to the "parent_transaction_id" field.
func (m *RejectionsMutation) ResetParentTransactionID() {
	m.parent_transaction_id = nil
}

// SetRejectionSource sets the "rejection_source" field.
func (m *RejectionsMutation) SetRejectionSource(rs rejections.RejectionSource) {
	m.rejection_source = &rs
}

// RejectionSource returns the value of the "rejection_source" field in the mutation.
func (m *RejectionsMutation) RejectionSource() (r rejections.RejectionSource, exists bool) {
	v := m.rejection_source
	if v == nil {
		return
	}
	return *v, true
}

// OldRejectionSource returns the old "rejection_source" field's value of the Rejections entity.
// If the Rejections object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *RejectionsMutation) OldRejectionSource(ctx context.Context) (v rejections.RejectionSource, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRejectionSource is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRejectionSource requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRejectionSource: %w", err)
	}
	return oldValue.RejectionSource, nil
}

// ResetRejectionSource resets all changes to the "rejection_source" field.
func (m *RejectionsMutation) ResetRejectionSource() {
	m.rejection_source = nil
}

// SetRejectionCode sets the "rejection_code" field.
func (m *RejectionsMutation) SetRejectionCode(s string) {
	m.rejection_code = &s
}

// RejectionCode returns the value of the "rejection_code" field in the mutation.
func (m *RejectionsMutation) RejectionCode() (r string, exists bool) {
	v := m.rejection_code
	if v == nil {
		return
	}
	return *v, true
}

// OldRejectionCode returns the old "rejection_code" field's value of the Rejections entity.
// If the Rejections object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *RejectionsMutation) OldRejectionCode(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRejectionCode is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRejectionCode requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRejectionCode: %w", err)
	}
	return oldValue.RejectionCode, nil
}

// ResetRejectionCode resets all changes to the "rejection_code" field.
func (m *RejectionsMutation) ResetRejectionCode() {
	m.rejection_code = nil
}

// SetRejectionScore sets the "rejection_score" field.
func (m *RejectionsMutation) SetRejectionScore(s string) {
	m.rejection_score = &s
}

// RejectionScore returns the value of the "rejection_score" field in the mutation.
func (m *RejectionsMutation) RejectionScore() (r string, exists bool) {
	v := m.rejection_score
	if v == nil {
		return
	}
	return *v, true
}

// OldRejectionScore returns the old "rejection_score" field's value of the Rejections entity.
// If the Rejections object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *RejectionsMutation) OldRejectionScore(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRejectionScore is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRejectionScore requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRejectionScore: %w", err)
	}
	return oldValue.RejectionScore, nil
}

// ResetRejectionScore resets all changes to the "rejection_score" field.
func (m *RejectionsMutation) ResetRejectionScore() {
	m.rejection_score = nil
}

// SetRejectionReason sets the "rejection_reason" field.
func (m *RejectionsMutation) SetRejectionReason(s string) {
	m.rejection_reason = &s
}

// RejectionReason returns the value of the "rejection_reason" field in the mutation.
func (m *RejectionsMutation) RejectionReason() (r string, exists bool) {
	v := m.rejection_reason
	if v == nil {
		return
	}
	return *v, true
}

// OldRejectionReason returns the old "rejection_reason" field's value of the Rejections entity.
// If the Rejections object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *RejectionsMutation) OldRejectionReason(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRejectionReason is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRejectionReason requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRejectionReason: %w", err)
	}
	return oldValue.RejectionReason, nil
}

// ResetRejectionReason resets all changes to the "rejection_reason" field.
func (m *RejectionsMutation) ResetRejectionReason() {
	m.rejection_reason = nil
}

// Where appends a list predicates to the RejectionsMutation builder.
func (m *RejectionsMutation) Where(ps ...predicate.Rejections) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the RejectionsMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *RejectionsMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Rejections, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *RejectionsMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *RejectionsMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Rejections).
func (m *RejectionsMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *RejectionsMutation) Fields() []string {
	fields := make([]string, 0, 7)
	if m.create_time != nil {
		fields = append(fields, rejections.FieldCreateTime)
	}
	if m.update_time != nil {
		fields = append(fields, rejections.FieldUpdateTime)
	}
	if m.parent_transaction_id != nil {
		fields = append(fields, rejections.FieldParentTransactionID)
	}
	if m.rejection_source != nil {
		fields = append(fields, rejections.FieldRejectionSource)
	}
	if m.rejection_code != nil {
		fields = append(fields, rejections.FieldRejectionCode)
	}
	if m.rejection_score != nil {
		fields = append(fields, rejections.FieldRejectionScore)
	}
	if m.rejection_reason != nil {
		fields = append(fields, rejections.FieldRejectionReason)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *RejectionsMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case rejections.FieldCreateTime:
		return m.CreateTime()
	case rejections.FieldUpdateTime:
		return m.UpdateTime()
	case rejections.FieldParentTransactionID:
		return m.ParentTransactionID()
	case rejections.FieldRejectionSource:
		return m.RejectionSource()
	case rejections.FieldRejectionCode:
		return m.RejectionCode()
	case rejections.FieldRejectionScore:
		return m.RejectionScore()
	case rejections.FieldRejectionReason:
		return m.RejectionReason()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *RejectionsMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case rejections.FieldCreateTime:
		return m.OldCreateTime(ctx)
	case rejections.FieldUpdateTime:
		return m.OldUpdateTime(ctx)
	case rejections.FieldParentTransactionID:
		return m.OldParentTransactionID(ctx)
	case rejections.FieldRejectionSource:
		return m.OldRejectionSource(ctx)
	case rejections.FieldRejectionCode:
		return m.OldRejectionCode(ctx)
	case rejections.FieldRejectionScore:
		return m.OldRejectionScore(ctx)
	case rejections.FieldRejectionReason:
		return m.OldRejectionReason(ctx)
	}
	return nil, fmt.Errorf("unknown Rejections field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *RejectionsMutation) SetField(name string, value ent.Value) error {
	switch name {
	case rejections.FieldCreateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateTime(v)
		return nil
	case rejections.FieldUpdateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateTime(v)
		return nil
	case rejections.FieldParentTransactionID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetParentTransactionID(v)
		return nil
	case rejections.FieldRejectionSource:
		v, ok := value.(rejections.RejectionSource)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRejectionSource(v)
		return nil
	case rejections.FieldRejectionCode:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRejectionCode(v)
		return nil
	case rejections.FieldRejectionScore:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRejectionScore(v)
		return nil
	case rejections.FieldRejectionReason:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRejectionReason(v)
		return nil
	}
	return fmt.Errorf("unknown Rejections field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *RejectionsMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *RejectionsMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *RejectionsMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Rejections numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *RejectionsMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(rejections.FieldCreateTime) {
		fields = append(fields, rejections.FieldCreateTime)
	}
	if m.FieldCleared(rejections.FieldUpdateTime) {
		fields = append(fields, rejections.FieldUpdateTime)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *RejectionsMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *RejectionsMutation) ClearField(name string) error {
	switch name {
	case rejections.FieldCreateTime:
		m.ClearCreateTime()
		return nil
	case rejections.FieldUpdateTime:
		m.ClearUpdateTime()
		return nil
	}
	return fmt.Errorf("unknown Rejections nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *RejectionsMutation) ResetField(name string) error {
	switch name {
	case rejections.FieldCreateTime:
		m.ResetCreateTime()
		return nil
	case rejections.FieldUpdateTime:
		m.ResetUpdateTime()
		return nil
	case rejections.FieldParentTransactionID:
		m.ResetParentTransactionID()
		return nil
	case rejections.FieldRejectionSource:
		m.ResetRejectionSource()
		return nil
	case rejections.FieldRejectionCode:
		m.ResetRejectionCode()
		return nil
	case rejections.FieldRejectionScore:
		m.ResetRejectionScore()
		return nil
	case rejections.FieldRejectionReason:
		m.ResetRejectionReason()
		return nil
	}
	return fmt.Errorf("unknown Rejections field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *RejectionsMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *RejectionsMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *RejectionsMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *RejectionsMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *RejectionsMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *RejectionsMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *RejectionsMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Rejections unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *RejectionsMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Rejections edge %s", name)
}

// SignatoriesMutation represents an operation that mutates the Signatories nodes in the graph.
type SignatoriesMutation struct {
	config
	op            Op
	typ           string
	id            *uuid.UUID
	signatory_a   *string
	signatory_b   *string
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Signatories, error)
	predicates    []predicate.Signatories
}

var _ ent.Mutation = (*SignatoriesMutation)(nil)

// signatoriesOption allows management of the mutation configuration using functional options.
type signatoriesOption func(*SignatoriesMutation)

// newSignatoriesMutation creates new mutation for the Signatories entity.
func newSignatoriesMutation(c config, op Op, opts ...signatoriesOption) *SignatoriesMutation {
	m := &SignatoriesMutation{
		config:        c,
		op:            op,
		typ:           TypeSignatories,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withSignatoriesID sets the ID field of the mutation.
func withSignatoriesID(id uuid.UUID) signatoriesOption {
	return func(m *SignatoriesMutation) {
		var (
			err   error
			once  sync.Once
			value *Signatories
		)
		m.oldValue = func(ctx context.Context) (*Signatories, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Signatories.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withSignatories sets the old Signatories of the mutation.
func withSignatories(node *Signatories) signatoriesOption {
	return func(m *SignatoriesMutation) {
		m.oldValue = func(context.Context) (*Signatories, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m SignatoriesMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m SignatoriesMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Signatories entities.
func (m *SignatoriesMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *SignatoriesMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *SignatoriesMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Signatories.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetSignatoryA sets the "signatory_a" field.
func (m *SignatoriesMutation) SetSignatoryA(s string) {
	m.signatory_a = &s
}

// SignatoryA returns the value of the "signatory_a" field in the mutation.
func (m *SignatoriesMutation) SignatoryA() (r string, exists bool) {
	v := m.signatory_a
	if v == nil {
		return
	}
	return *v, true
}

// OldSignatoryA returns the old "signatory_a" field's value of the Signatories entity.
// If the Signatories object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SignatoriesMutation) OldSignatoryA(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSignatoryA is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSignatoryA requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSignatoryA: %w", err)
	}
	return oldValue.SignatoryA, nil
}

// ClearSignatoryA clears the value of the "signatory_a" field.
func (m *SignatoriesMutation) ClearSignatoryA() {
	m.signatory_a = nil
	m.clearedFields[signatories.FieldSignatoryA] = struct{}{}
}

// SignatoryACleared returns if the "signatory_a" field was cleared in this mutation.
func (m *SignatoriesMutation) SignatoryACleared() bool {
	_, ok := m.clearedFields[signatories.FieldSignatoryA]
	return ok
}

// ResetSignatoryA resets all changes to the "signatory_a" field.
func (m *SignatoriesMutation) ResetSignatoryA() {
	m.signatory_a = nil
	delete(m.clearedFields, signatories.FieldSignatoryA)
}

// SetSignatoryB sets the "signatory_b" field.
func (m *SignatoriesMutation) SetSignatoryB(s string) {
	m.signatory_b = &s
}

// SignatoryB returns the value of the "signatory_b" field in the mutation.
func (m *SignatoriesMutation) SignatoryB() (r string, exists bool) {
	v := m.signatory_b
	if v == nil {
		return
	}
	return *v, true
}

// OldSignatoryB returns the old "signatory_b" field's value of the Signatories entity.
// If the Signatories object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SignatoriesMutation) OldSignatoryB(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSignatoryB is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSignatoryB requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSignatoryB: %w", err)
	}
	return oldValue.SignatoryB, nil
}

// ClearSignatoryB clears the value of the "signatory_b" field.
func (m *SignatoriesMutation) ClearSignatoryB() {
	m.signatory_b = nil
	m.clearedFields[signatories.FieldSignatoryB] = struct{}{}
}

// SignatoryBCleared returns if the "signatory_b" field was cleared in this mutation.
func (m *SignatoriesMutation) SignatoryBCleared() bool {
	_, ok := m.clearedFields[signatories.FieldSignatoryB]
	return ok
}

// ResetSignatoryB resets all changes to the "signatory_b" field.
func (m *SignatoriesMutation) ResetSignatoryB() {
	m.signatory_b = nil
	delete(m.clearedFields, signatories.FieldSignatoryB)
}

// Where appends a list predicates to the SignatoriesMutation builder.
func (m *SignatoriesMutation) Where(ps ...predicate.Signatories) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the SignatoriesMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *SignatoriesMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Signatories, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *SignatoriesMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *SignatoriesMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Signatories).
func (m *SignatoriesMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *SignatoriesMutation) Fields() []string {
	fields := make([]string, 0, 2)
	if m.signatory_a != nil {
		fields = append(fields, signatories.FieldSignatoryA)
	}
	if m.signatory_b != nil {
		fields = append(fields, signatories.FieldSignatoryB)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *SignatoriesMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case signatories.FieldSignatoryA:
		return m.SignatoryA()
	case signatories.FieldSignatoryB:
		return m.SignatoryB()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *SignatoriesMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case signatories.FieldSignatoryA:
		return m.OldSignatoryA(ctx)
	case signatories.FieldSignatoryB:
		return m.OldSignatoryB(ctx)
	}
	return nil, fmt.Errorf("unknown Signatories field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *SignatoriesMutation) SetField(name string, value ent.Value) error {
	switch name {
	case signatories.FieldSignatoryA:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSignatoryA(v)
		return nil
	case signatories.FieldSignatoryB:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSignatoryB(v)
		return nil
	}
	return fmt.Errorf("unknown Signatories field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *SignatoriesMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *SignatoriesMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *SignatoriesMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Signatories numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *SignatoriesMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(signatories.FieldSignatoryA) {
		fields = append(fields, signatories.FieldSignatoryA)
	}
	if m.FieldCleared(signatories.FieldSignatoryB) {
		fields = append(fields, signatories.FieldSignatoryB)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *SignatoriesMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *SignatoriesMutation) ClearField(name string) error {
	switch name {
	case signatories.FieldSignatoryA:
		m.ClearSignatoryA()
		return nil
	case signatories.FieldSignatoryB:
		m.ClearSignatoryB()
		return nil
	}
	return fmt.Errorf("unknown Signatories nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *SignatoriesMutation) ResetField(name string) error {
	switch name {
	case signatories.FieldSignatoryA:
		m.ResetSignatoryA()
		return nil
	case signatories.FieldSignatoryB:
		m.ResetSignatoryB()
		return nil
	}
	return fmt.Errorf("unknown Signatories field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *SignatoriesMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *SignatoriesMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *SignatoriesMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *SignatoriesMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *SignatoriesMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *SignatoriesMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *SignatoriesMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Signatories unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *SignatoriesMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Signatories edge %s", name)
}

// TransactionMutation represents an operation that mutates the Transaction nodes in the graph.
type TransactionMutation struct {
	config
	op                            Op
	typ                           string
	id                            *uuid.UUID
	create_time                   *time.Time
	update_time                   *time.Time
	transaction_number            *string
	transaction_date              *time.Time
	transaction_type              *transaction.TransactionType
	initiator_id                  *string
	idempotency_key               *string
	value_date                    *string
	transaction_status            *transaction.TransactionStatus
	transaction_amount            *string
	transaction_comission         *string
	transaction_currency          *transaction.TransactionCurrency
	transaction_total_amount      *string
	transaction_direction         *transaction.TransactionDirection
	purpose_code                  *string
	purpose_details               *string
	payer_kod                     *string
	payer_bin_iin                 *string
	payer_name                    *string
	payer_type                    *transaction.PayerType
	payer_account_iban            *string
	payer_bank_bic                *string
	payer_bank_name               *string
	payer_iso_country_code        *string
	real_payer_name               *string
	real_payer_bin_iin            *string
	real_payer_iso_country_code   *string
	real_payer_type               *transaction.RealPayerType
	beneficiary_kbe               *string
	beneficiary_bin_iin           *string
	beneficiary_name              *string
	beneficiary_type              *transaction.BeneficiaryType
	beneficiary_account_iban      *string
	beneficiary_bank_bic          *string
	beneficiary_bank_name         *string
	beneficiary_iso_country_code  *string
	real_beneficiary_name         *string
	real_beneficiary_bin_iin      *string
	real_beneficiary_country_code *string
	real_beneficiary_type         *transaction.RealBeneficiaryType
	clearedFields                 map[string]struct{}
	done                          bool
	oldValue                      func(context.Context) (*Transaction, error)
	predicates                    []predicate.Transaction
}

var _ ent.Mutation = (*TransactionMutation)(nil)

// transactionOption allows management of the mutation configuration using functional options.
type transactionOption func(*TransactionMutation)

// newTransactionMutation creates new mutation for the Transaction entity.
func newTransactionMutation(c config, op Op, opts ...transactionOption) *TransactionMutation {
	m := &TransactionMutation{
		config:        c,
		op:            op,
		typ:           TypeTransaction,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withTransactionID sets the ID field of the mutation.
func withTransactionID(id uuid.UUID) transactionOption {
	return func(m *TransactionMutation) {
		var (
			err   error
			once  sync.Once
			value *Transaction
		)
		m.oldValue = func(ctx context.Context) (*Transaction, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Transaction.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withTransaction sets the old Transaction of the mutation.
func withTransaction(node *Transaction) transactionOption {
	return func(m *TransactionMutation) {
		m.oldValue = func(context.Context) (*Transaction, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m TransactionMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m TransactionMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Transaction entities.
func (m *TransactionMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *TransactionMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *TransactionMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Transaction.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreateTime sets the "create_time" field.
func (m *TransactionMutation) SetCreateTime(t time.Time) {
	m.create_time = &t
}

// CreateTime returns the value of the "create_time" field in the mutation.
func (m *TransactionMutation) CreateTime() (r time.Time, exists bool) {
	v := m.create_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateTime returns the old "create_time" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldCreateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateTime: %w", err)
	}
	return oldValue.CreateTime, nil
}

// ClearCreateTime clears the value of the "create_time" field.
func (m *TransactionMutation) ClearCreateTime() {
	m.create_time = nil
	m.clearedFields[transaction.FieldCreateTime] = struct{}{}
}

// CreateTimeCleared returns if the "create_time" field was cleared in this mutation.
func (m *TransactionMutation) CreateTimeCleared() bool {
	_, ok := m.clearedFields[transaction.FieldCreateTime]
	return ok
}

// ResetCreateTime resets all changes to the "create_time" field.
func (m *TransactionMutation) ResetCreateTime() {
	m.create_time = nil
	delete(m.clearedFields, transaction.FieldCreateTime)
}

// SetUpdateTime sets the "update_time" field.
func (m *TransactionMutation) SetUpdateTime(t time.Time) {
	m.update_time = &t
}

// UpdateTime returns the value of the "update_time" field in the mutation.
func (m *TransactionMutation) UpdateTime() (r time.Time, exists bool) {
	v := m.update_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateTime returns the old "update_time" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldUpdateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateTime: %w", err)
	}
	return oldValue.UpdateTime, nil
}

// ClearUpdateTime clears the value of the "update_time" field.
func (m *TransactionMutation) ClearUpdateTime() {
	m.update_time = nil
	m.clearedFields[transaction.FieldUpdateTime] = struct{}{}
}

// UpdateTimeCleared returns if the "update_time" field was cleared in this mutation.
func (m *TransactionMutation) UpdateTimeCleared() bool {
	_, ok := m.clearedFields[transaction.FieldUpdateTime]
	return ok
}

// ResetUpdateTime resets all changes to the "update_time" field.
func (m *TransactionMutation) ResetUpdateTime() {
	m.update_time = nil
	delete(m.clearedFields, transaction.FieldUpdateTime)
}

// SetTransactionNumber sets the "transaction_number" field.
func (m *TransactionMutation) SetTransactionNumber(s string) {
	m.transaction_number = &s
}

// TransactionNumber returns the value of the "transaction_number" field in the mutation.
func (m *TransactionMutation) TransactionNumber() (r string, exists bool) {
	v := m.transaction_number
	if v == nil {
		return
	}
	return *v, true
}

// OldTransactionNumber returns the old "transaction_number" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldTransactionNumber(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTransactionNumber is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTransactionNumber requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTransactionNumber: %w", err)
	}
	return oldValue.TransactionNumber, nil
}

// ResetTransactionNumber resets all changes to the "transaction_number" field.
func (m *TransactionMutation) ResetTransactionNumber() {
	m.transaction_number = nil
}

// SetTransactionDate sets the "transaction_date" field.
func (m *TransactionMutation) SetTransactionDate(t time.Time) {
	m.transaction_date = &t
}

// TransactionDate returns the value of the "transaction_date" field in the mutation.
func (m *TransactionMutation) TransactionDate() (r time.Time, exists bool) {
	v := m.transaction_date
	if v == nil {
		return
	}
	return *v, true
}

// OldTransactionDate returns the old "transaction_date" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldTransactionDate(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTransactionDate is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTransactionDate requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTransactionDate: %w", err)
	}
	return oldValue.TransactionDate, nil
}

// ResetTransactionDate resets all changes to the "transaction_date" field.
func (m *TransactionMutation) ResetTransactionDate() {
	m.transaction_date = nil
}

// SetTransactionType sets the "transaction_type" field.
func (m *TransactionMutation) SetTransactionType(tt transaction.TransactionType) {
	m.transaction_type = &tt
}

// TransactionType returns the value of the "transaction_type" field in the mutation.
func (m *TransactionMutation) TransactionType() (r transaction.TransactionType, exists bool) {
	v := m.transaction_type
	if v == nil {
		return
	}
	return *v, true
}

// OldTransactionType returns the old "transaction_type" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldTransactionType(ctx context.Context) (v transaction.TransactionType, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTransactionType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTransactionType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTransactionType: %w", err)
	}
	return oldValue.TransactionType, nil
}

// ResetTransactionType resets all changes to the "transaction_type" field.
func (m *TransactionMutation) ResetTransactionType() {
	m.transaction_type = nil
}

// SetInitiatorID sets the "initiator_id" field.
func (m *TransactionMutation) SetInitiatorID(s string) {
	m.initiator_id = &s
}

// InitiatorID returns the value of the "initiator_id" field in the mutation.
func (m *TransactionMutation) InitiatorID() (r string, exists bool) {
	v := m.initiator_id
	if v == nil {
		return
	}
	return *v, true
}

// OldInitiatorID returns the old "initiator_id" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldInitiatorID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldInitiatorID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldInitiatorID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldInitiatorID: %w", err)
	}
	return oldValue.InitiatorID, nil
}

// ResetInitiatorID resets all changes to the "initiator_id" field.
func (m *TransactionMutation) ResetInitiatorID() {
	m.initiator_id = nil
}

// SetIdempotencyKey sets the "idempotency_key" field.
func (m *TransactionMutation) SetIdempotencyKey(s string) {
	m.idempotency_key = &s
}

// IdempotencyKey returns the value of the "idempotency_key" field in the mutation.
func (m *TransactionMutation) IdempotencyKey() (r string, exists bool) {
	v := m.idempotency_key
	if v == nil {
		return
	}
	return *v, true
}

// OldIdempotencyKey returns the old "idempotency_key" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldIdempotencyKey(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIdempotencyKey is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIdempotencyKey requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIdempotencyKey: %w", err)
	}
	return oldValue.IdempotencyKey, nil
}

// ResetIdempotencyKey resets all changes to the "idempotency_key" field.
func (m *TransactionMutation) ResetIdempotencyKey() {
	m.idempotency_key = nil
}

// SetValueDate sets the "value_date" field.
func (m *TransactionMutation) SetValueDate(s string) {
	m.value_date = &s
}

// ValueDate returns the value of the "value_date" field in the mutation.
func (m *TransactionMutation) ValueDate() (r string, exists bool) {
	v := m.value_date
	if v == nil {
		return
	}
	return *v, true
}

// OldValueDate returns the old "value_date" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldValueDate(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldValueDate is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldValueDate requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldValueDate: %w", err)
	}
	return oldValue.ValueDate, nil
}

// ClearValueDate clears the value of the "value_date" field.
func (m *TransactionMutation) ClearValueDate() {
	m.value_date = nil
	m.clearedFields[transaction.FieldValueDate] = struct{}{}
}

// ValueDateCleared returns if the "value_date" field was cleared in this mutation.
func (m *TransactionMutation) ValueDateCleared() bool {
	_, ok := m.clearedFields[transaction.FieldValueDate]
	return ok
}

// ResetValueDate resets all changes to the "value_date" field.
func (m *TransactionMutation) ResetValueDate() {
	m.value_date = nil
	delete(m.clearedFields, transaction.FieldValueDate)
}

// SetTransactionStatus sets the "transaction_status" field.
func (m *TransactionMutation) SetTransactionStatus(ts transaction.TransactionStatus) {
	m.transaction_status = &ts
}

// TransactionStatus returns the value of the "transaction_status" field in the mutation.
func (m *TransactionMutation) TransactionStatus() (r transaction.TransactionStatus, exists bool) {
	v := m.transaction_status
	if v == nil {
		return
	}
	return *v, true
}

// OldTransactionStatus returns the old "transaction_status" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldTransactionStatus(ctx context.Context) (v transaction.TransactionStatus, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTransactionStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTransactionStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTransactionStatus: %w", err)
	}
	return oldValue.TransactionStatus, nil
}

// ResetTransactionStatus resets all changes to the "transaction_status" field.
func (m *TransactionMutation) ResetTransactionStatus() {
	m.transaction_status = nil
}

// SetTransactionAmount sets the "transaction_amount" field.
func (m *TransactionMutation) SetTransactionAmount(s string) {
	m.transaction_amount = &s
}

// TransactionAmount returns the value of the "transaction_amount" field in the mutation.
func (m *TransactionMutation) TransactionAmount() (r string, exists bool) {
	v := m.transaction_amount
	if v == nil {
		return
	}
	return *v, true
}

// OldTransactionAmount returns the old "transaction_amount" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldTransactionAmount(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTransactionAmount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTransactionAmount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTransactionAmount: %w", err)
	}
	return oldValue.TransactionAmount, nil
}

// ResetTransactionAmount resets all changes to the "transaction_amount" field.
func (m *TransactionMutation) ResetTransactionAmount() {
	m.transaction_amount = nil
}

// SetTransactionComission sets the "transaction_comission" field.
func (m *TransactionMutation) SetTransactionComission(s string) {
	m.transaction_comission = &s
}

// TransactionComission returns the value of the "transaction_comission" field in the mutation.
func (m *TransactionMutation) TransactionComission() (r string, exists bool) {
	v := m.transaction_comission
	if v == nil {
		return
	}
	return *v, true
}

// OldTransactionComission returns the old "transaction_comission" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldTransactionComission(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTransactionComission is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTransactionComission requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTransactionComission: %w", err)
	}
	return oldValue.TransactionComission, nil
}

// ClearTransactionComission clears the value of the "transaction_comission" field.
func (m *TransactionMutation) ClearTransactionComission() {
	m.transaction_comission = nil
	m.clearedFields[transaction.FieldTransactionComission] = struct{}{}
}

// TransactionComissionCleared returns if the "transaction_comission" field was cleared in this mutation.
func (m *TransactionMutation) TransactionComissionCleared() bool {
	_, ok := m.clearedFields[transaction.FieldTransactionComission]
	return ok
}

// ResetTransactionComission resets all changes to the "transaction_comission" field.
func (m *TransactionMutation) ResetTransactionComission() {
	m.transaction_comission = nil
	delete(m.clearedFields, transaction.FieldTransactionComission)
}

// SetTransactionCurrency sets the "transaction_currency" field.
func (m *TransactionMutation) SetTransactionCurrency(tc transaction.TransactionCurrency) {
	m.transaction_currency = &tc
}

// TransactionCurrency returns the value of the "transaction_currency" field in the mutation.
func (m *TransactionMutation) TransactionCurrency() (r transaction.TransactionCurrency, exists bool) {
	v := m.transaction_currency
	if v == nil {
		return
	}
	return *v, true
}

// OldTransactionCurrency returns the old "transaction_currency" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldTransactionCurrency(ctx context.Context) (v transaction.TransactionCurrency, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTransactionCurrency is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTransactionCurrency requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTransactionCurrency: %w", err)
	}
	return oldValue.TransactionCurrency, nil
}

// ResetTransactionCurrency resets all changes to the "transaction_currency" field.
func (m *TransactionMutation) ResetTransactionCurrency() {
	m.transaction_currency = nil
}

// SetTransactionTotalAmount sets the "transaction_total_amount" field.
func (m *TransactionMutation) SetTransactionTotalAmount(s string) {
	m.transaction_total_amount = &s
}

// TransactionTotalAmount returns the value of the "transaction_total_amount" field in the mutation.
func (m *TransactionMutation) TransactionTotalAmount() (r string, exists bool) {
	v := m.transaction_total_amount
	if v == nil {
		return
	}
	return *v, true
}

// OldTransactionTotalAmount returns the old "transaction_total_amount" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldTransactionTotalAmount(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTransactionTotalAmount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTransactionTotalAmount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTransactionTotalAmount: %w", err)
	}
	return oldValue.TransactionTotalAmount, nil
}

// ResetTransactionTotalAmount resets all changes to the "transaction_total_amount" field.
func (m *TransactionMutation) ResetTransactionTotalAmount() {
	m.transaction_total_amount = nil
}

// SetTransactionDirection sets the "transaction_direction" field.
func (m *TransactionMutation) SetTransactionDirection(td transaction.TransactionDirection) {
	m.transaction_direction = &td
}

// TransactionDirection returns the value of the "transaction_direction" field in the mutation.
func (m *TransactionMutation) TransactionDirection() (r transaction.TransactionDirection, exists bool) {
	v := m.transaction_direction
	if v == nil {
		return
	}
	return *v, true
}

// OldTransactionDirection returns the old "transaction_direction" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldTransactionDirection(ctx context.Context) (v *transaction.TransactionDirection, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTransactionDirection is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTransactionDirection requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTransactionDirection: %w", err)
	}
	return oldValue.TransactionDirection, nil
}

// ClearTransactionDirection clears the value of the "transaction_direction" field.
func (m *TransactionMutation) ClearTransactionDirection() {
	m.transaction_direction = nil
	m.clearedFields[transaction.FieldTransactionDirection] = struct{}{}
}

// TransactionDirectionCleared returns if the "transaction_direction" field was cleared in this mutation.
func (m *TransactionMutation) TransactionDirectionCleared() bool {
	_, ok := m.clearedFields[transaction.FieldTransactionDirection]
	return ok
}

// ResetTransactionDirection resets all changes to the "transaction_direction" field.
func (m *TransactionMutation) ResetTransactionDirection() {
	m.transaction_direction = nil
	delete(m.clearedFields, transaction.FieldTransactionDirection)
}

// SetPurposeCode sets the "purpose_code" field.
func (m *TransactionMutation) SetPurposeCode(s string) {
	m.purpose_code = &s
}

// PurposeCode returns the value of the "purpose_code" field in the mutation.
func (m *TransactionMutation) PurposeCode() (r string, exists bool) {
	v := m.purpose_code
	if v == nil {
		return
	}
	return *v, true
}

// OldPurposeCode returns the old "purpose_code" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldPurposeCode(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPurposeCode is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPurposeCode requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPurposeCode: %w", err)
	}
	return oldValue.PurposeCode, nil
}

// ClearPurposeCode clears the value of the "purpose_code" field.
func (m *TransactionMutation) ClearPurposeCode() {
	m.purpose_code = nil
	m.clearedFields[transaction.FieldPurposeCode] = struct{}{}
}

// PurposeCodeCleared returns if the "purpose_code" field was cleared in this mutation.
func (m *TransactionMutation) PurposeCodeCleared() bool {
	_, ok := m.clearedFields[transaction.FieldPurposeCode]
	return ok
}

// ResetPurposeCode resets all changes to the "purpose_code" field.
func (m *TransactionMutation) ResetPurposeCode() {
	m.purpose_code = nil
	delete(m.clearedFields, transaction.FieldPurposeCode)
}

// SetPurposeDetails sets the "purpose_details" field.
func (m *TransactionMutation) SetPurposeDetails(s string) {
	m.purpose_details = &s
}

// PurposeDetails returns the value of the "purpose_details" field in the mutation.
func (m *TransactionMutation) PurposeDetails() (r string, exists bool) {
	v := m.purpose_details
	if v == nil {
		return
	}
	return *v, true
}

// OldPurposeDetails returns the old "purpose_details" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldPurposeDetails(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPurposeDetails is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPurposeDetails requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPurposeDetails: %w", err)
	}
	return oldValue.PurposeDetails, nil
}

// ClearPurposeDetails clears the value of the "purpose_details" field.
func (m *TransactionMutation) ClearPurposeDetails() {
	m.purpose_details = nil
	m.clearedFields[transaction.FieldPurposeDetails] = struct{}{}
}

// PurposeDetailsCleared returns if the "purpose_details" field was cleared in this mutation.
func (m *TransactionMutation) PurposeDetailsCleared() bool {
	_, ok := m.clearedFields[transaction.FieldPurposeDetails]
	return ok
}

// ResetPurposeDetails resets all changes to the "purpose_details" field.
func (m *TransactionMutation) ResetPurposeDetails() {
	m.purpose_details = nil
	delete(m.clearedFields, transaction.FieldPurposeDetails)
}

// SetPayerKod sets the "payer_kod" field.
func (m *TransactionMutation) SetPayerKod(s string) {
	m.payer_kod = &s
}

// PayerKod returns the value of the "payer_kod" field in the mutation.
func (m *TransactionMutation) PayerKod() (r string, exists bool) {
	v := m.payer_kod
	if v == nil {
		return
	}
	return *v, true
}

// OldPayerKod returns the old "payer_kod" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldPayerKod(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPayerKod is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPayerKod requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPayerKod: %w", err)
	}
	return oldValue.PayerKod, nil
}

// ClearPayerKod clears the value of the "payer_kod" field.
func (m *TransactionMutation) ClearPayerKod() {
	m.payer_kod = nil
	m.clearedFields[transaction.FieldPayerKod] = struct{}{}
}

// PayerKodCleared returns if the "payer_kod" field was cleared in this mutation.
func (m *TransactionMutation) PayerKodCleared() bool {
	_, ok := m.clearedFields[transaction.FieldPayerKod]
	return ok
}

// ResetPayerKod resets all changes to the "payer_kod" field.
func (m *TransactionMutation) ResetPayerKod() {
	m.payer_kod = nil
	delete(m.clearedFields, transaction.FieldPayerKod)
}

// SetPayerBinIin sets the "payer_bin_iin" field.
func (m *TransactionMutation) SetPayerBinIin(s string) {
	m.payer_bin_iin = &s
}

// PayerBinIin returns the value of the "payer_bin_iin" field in the mutation.
func (m *TransactionMutation) PayerBinIin() (r string, exists bool) {
	v := m.payer_bin_iin
	if v == nil {
		return
	}
	return *v, true
}

// OldPayerBinIin returns the old "payer_bin_iin" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldPayerBinIin(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPayerBinIin is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPayerBinIin requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPayerBinIin: %w", err)
	}
	return oldValue.PayerBinIin, nil
}

// ClearPayerBinIin clears the value of the "payer_bin_iin" field.
func (m *TransactionMutation) ClearPayerBinIin() {
	m.payer_bin_iin = nil
	m.clearedFields[transaction.FieldPayerBinIin] = struct{}{}
}

// PayerBinIinCleared returns if the "payer_bin_iin" field was cleared in this mutation.
func (m *TransactionMutation) PayerBinIinCleared() bool {
	_, ok := m.clearedFields[transaction.FieldPayerBinIin]
	return ok
}

// ResetPayerBinIin resets all changes to the "payer_bin_iin" field.
func (m *TransactionMutation) ResetPayerBinIin() {
	m.payer_bin_iin = nil
	delete(m.clearedFields, transaction.FieldPayerBinIin)
}

// SetPayerName sets the "payer_name" field.
func (m *TransactionMutation) SetPayerName(s string) {
	m.payer_name = &s
}

// PayerName returns the value of the "payer_name" field in the mutation.
func (m *TransactionMutation) PayerName() (r string, exists bool) {
	v := m.payer_name
	if v == nil {
		return
	}
	return *v, true
}

// OldPayerName returns the old "payer_name" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldPayerName(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPayerName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPayerName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPayerName: %w", err)
	}
	return oldValue.PayerName, nil
}

// ClearPayerName clears the value of the "payer_name" field.
func (m *TransactionMutation) ClearPayerName() {
	m.payer_name = nil
	m.clearedFields[transaction.FieldPayerName] = struct{}{}
}

// PayerNameCleared returns if the "payer_name" field was cleared in this mutation.
func (m *TransactionMutation) PayerNameCleared() bool {
	_, ok := m.clearedFields[transaction.FieldPayerName]
	return ok
}

// ResetPayerName resets all changes to the "payer_name" field.
func (m *TransactionMutation) ResetPayerName() {
	m.payer_name = nil
	delete(m.clearedFields, transaction.FieldPayerName)
}

// SetPayerType sets the "payer_type" field.
func (m *TransactionMutation) SetPayerType(tt transaction.PayerType) {
	m.payer_type = &tt
}

// PayerType returns the value of the "payer_type" field in the mutation.
func (m *TransactionMutation) PayerType() (r transaction.PayerType, exists bool) {
	v := m.payer_type
	if v == nil {
		return
	}
	return *v, true
}

// OldPayerType returns the old "payer_type" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldPayerType(ctx context.Context) (v *transaction.PayerType, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPayerType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPayerType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPayerType: %w", err)
	}
	return oldValue.PayerType, nil
}

// ClearPayerType clears the value of the "payer_type" field.
func (m *TransactionMutation) ClearPayerType() {
	m.payer_type = nil
	m.clearedFields[transaction.FieldPayerType] = struct{}{}
}

// PayerTypeCleared returns if the "payer_type" field was cleared in this mutation.
func (m *TransactionMutation) PayerTypeCleared() bool {
	_, ok := m.clearedFields[transaction.FieldPayerType]
	return ok
}

// ResetPayerType resets all changes to the "payer_type" field.
func (m *TransactionMutation) ResetPayerType() {
	m.payer_type = nil
	delete(m.clearedFields, transaction.FieldPayerType)
}

// SetPayerAccountIban sets the "payer_account_iban" field.
func (m *TransactionMutation) SetPayerAccountIban(s string) {
	m.payer_account_iban = &s
}

// PayerAccountIban returns the value of the "payer_account_iban" field in the mutation.
func (m *TransactionMutation) PayerAccountIban() (r string, exists bool) {
	v := m.payer_account_iban
	if v == nil {
		return
	}
	return *v, true
}

// OldPayerAccountIban returns the old "payer_account_iban" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldPayerAccountIban(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPayerAccountIban is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPayerAccountIban requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPayerAccountIban: %w", err)
	}
	return oldValue.PayerAccountIban, nil
}

// ClearPayerAccountIban clears the value of the "payer_account_iban" field.
func (m *TransactionMutation) ClearPayerAccountIban() {
	m.payer_account_iban = nil
	m.clearedFields[transaction.FieldPayerAccountIban] = struct{}{}
}

// PayerAccountIbanCleared returns if the "payer_account_iban" field was cleared in this mutation.
func (m *TransactionMutation) PayerAccountIbanCleared() bool {
	_, ok := m.clearedFields[transaction.FieldPayerAccountIban]
	return ok
}

// ResetPayerAccountIban resets all changes to the "payer_account_iban" field.
func (m *TransactionMutation) ResetPayerAccountIban() {
	m.payer_account_iban = nil
	delete(m.clearedFields, transaction.FieldPayerAccountIban)
}

// SetPayerBankBic sets the "payer_bank_bic" field.
func (m *TransactionMutation) SetPayerBankBic(s string) {
	m.payer_bank_bic = &s
}

// PayerBankBic returns the value of the "payer_bank_bic" field in the mutation.
func (m *TransactionMutation) PayerBankBic() (r string, exists bool) {
	v := m.payer_bank_bic
	if v == nil {
		return
	}
	return *v, true
}

// OldPayerBankBic returns the old "payer_bank_bic" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldPayerBankBic(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPayerBankBic is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPayerBankBic requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPayerBankBic: %w", err)
	}
	return oldValue.PayerBankBic, nil
}

// ClearPayerBankBic clears the value of the "payer_bank_bic" field.
func (m *TransactionMutation) ClearPayerBankBic() {
	m.payer_bank_bic = nil
	m.clearedFields[transaction.FieldPayerBankBic] = struct{}{}
}

// PayerBankBicCleared returns if the "payer_bank_bic" field was cleared in this mutation.
func (m *TransactionMutation) PayerBankBicCleared() bool {
	_, ok := m.clearedFields[transaction.FieldPayerBankBic]
	return ok
}

// ResetPayerBankBic resets all changes to the "payer_bank_bic" field.
func (m *TransactionMutation) ResetPayerBankBic() {
	m.payer_bank_bic = nil
	delete(m.clearedFields, transaction.FieldPayerBankBic)
}

// SetPayerBankName sets the "payer_bank_name" field.
func (m *TransactionMutation) SetPayerBankName(s string) {
	m.payer_bank_name = &s
}

// PayerBankName returns the value of the "payer_bank_name" field in the mutation.
func (m *TransactionMutation) PayerBankName() (r string, exists bool) {
	v := m.payer_bank_name
	if v == nil {
		return
	}
	return *v, true
}

// OldPayerBankName returns the old "payer_bank_name" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldPayerBankName(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPayerBankName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPayerBankName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPayerBankName: %w", err)
	}
	return oldValue.PayerBankName, nil
}

// ClearPayerBankName clears the value of the "payer_bank_name" field.
func (m *TransactionMutation) ClearPayerBankName() {
	m.payer_bank_name = nil
	m.clearedFields[transaction.FieldPayerBankName] = struct{}{}
}

// PayerBankNameCleared returns if the "payer_bank_name" field was cleared in this mutation.
func (m *TransactionMutation) PayerBankNameCleared() bool {
	_, ok := m.clearedFields[transaction.FieldPayerBankName]
	return ok
}

// ResetPayerBankName resets all changes to the "payer_bank_name" field.
func (m *TransactionMutation) ResetPayerBankName() {
	m.payer_bank_name = nil
	delete(m.clearedFields, transaction.FieldPayerBankName)
}

// SetPayerIsoCountryCode sets the "payer_iso_country_code" field.
func (m *TransactionMutation) SetPayerIsoCountryCode(s string) {
	m.payer_iso_country_code = &s
}

// PayerIsoCountryCode returns the value of the "payer_iso_country_code" field in the mutation.
func (m *TransactionMutation) PayerIsoCountryCode() (r string, exists bool) {
	v := m.payer_iso_country_code
	if v == nil {
		return
	}
	return *v, true
}

// OldPayerIsoCountryCode returns the old "payer_iso_country_code" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldPayerIsoCountryCode(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPayerIsoCountryCode is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPayerIsoCountryCode requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPayerIsoCountryCode: %w", err)
	}
	return oldValue.PayerIsoCountryCode, nil
}

// ClearPayerIsoCountryCode clears the value of the "payer_iso_country_code" field.
func (m *TransactionMutation) ClearPayerIsoCountryCode() {
	m.payer_iso_country_code = nil
	m.clearedFields[transaction.FieldPayerIsoCountryCode] = struct{}{}
}

// PayerIsoCountryCodeCleared returns if the "payer_iso_country_code" field was cleared in this mutation.
func (m *TransactionMutation) PayerIsoCountryCodeCleared() bool {
	_, ok := m.clearedFields[transaction.FieldPayerIsoCountryCode]
	return ok
}

// ResetPayerIsoCountryCode resets all changes to the "payer_iso_country_code" field.
func (m *TransactionMutation) ResetPayerIsoCountryCode() {
	m.payer_iso_country_code = nil
	delete(m.clearedFields, transaction.FieldPayerIsoCountryCode)
}

// SetRealPayerName sets the "real_payer_name" field.
func (m *TransactionMutation) SetRealPayerName(s string) {
	m.real_payer_name = &s
}

// RealPayerName returns the value of the "real_payer_name" field in the mutation.
func (m *TransactionMutation) RealPayerName() (r string, exists bool) {
	v := m.real_payer_name
	if v == nil {
		return
	}
	return *v, true
}

// OldRealPayerName returns the old "real_payer_name" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldRealPayerName(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRealPayerName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRealPayerName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRealPayerName: %w", err)
	}
	return oldValue.RealPayerName, nil
}

// ClearRealPayerName clears the value of the "real_payer_name" field.
func (m *TransactionMutation) ClearRealPayerName() {
	m.real_payer_name = nil
	m.clearedFields[transaction.FieldRealPayerName] = struct{}{}
}

// RealPayerNameCleared returns if the "real_payer_name" field was cleared in this mutation.
func (m *TransactionMutation) RealPayerNameCleared() bool {
	_, ok := m.clearedFields[transaction.FieldRealPayerName]
	return ok
}

// ResetRealPayerName resets all changes to the "real_payer_name" field.
func (m *TransactionMutation) ResetRealPayerName() {
	m.real_payer_name = nil
	delete(m.clearedFields, transaction.FieldRealPayerName)
}

// SetRealPayerBinIin sets the "real_payer_bin_iin" field.
func (m *TransactionMutation) SetRealPayerBinIin(s string) {
	m.real_payer_bin_iin = &s
}

// RealPayerBinIin returns the value of the "real_payer_bin_iin" field in the mutation.
func (m *TransactionMutation) RealPayerBinIin() (r string, exists bool) {
	v := m.real_payer_bin_iin
	if v == nil {
		return
	}
	return *v, true
}

// OldRealPayerBinIin returns the old "real_payer_bin_iin" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldRealPayerBinIin(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRealPayerBinIin is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRealPayerBinIin requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRealPayerBinIin: %w", err)
	}
	return oldValue.RealPayerBinIin, nil
}

// ClearRealPayerBinIin clears the value of the "real_payer_bin_iin" field.
func (m *TransactionMutation) ClearRealPayerBinIin() {
	m.real_payer_bin_iin = nil
	m.clearedFields[transaction.FieldRealPayerBinIin] = struct{}{}
}

// RealPayerBinIinCleared returns if the "real_payer_bin_iin" field was cleared in this mutation.
func (m *TransactionMutation) RealPayerBinIinCleared() bool {
	_, ok := m.clearedFields[transaction.FieldRealPayerBinIin]
	return ok
}

// ResetRealPayerBinIin resets all changes to the "real_payer_bin_iin" field.
func (m *TransactionMutation) ResetRealPayerBinIin() {
	m.real_payer_bin_iin = nil
	delete(m.clearedFields, transaction.FieldRealPayerBinIin)
}

// SetRealPayerIsoCountryCode sets the "real_payer_iso_country_code" field.
func (m *TransactionMutation) SetRealPayerIsoCountryCode(s string) {
	m.real_payer_iso_country_code = &s
}

// RealPayerIsoCountryCode returns the value of the "real_payer_iso_country_code" field in the mutation.
func (m *TransactionMutation) RealPayerIsoCountryCode() (r string, exists bool) {
	v := m.real_payer_iso_country_code
	if v == nil {
		return
	}
	return *v, true
}

// OldRealPayerIsoCountryCode returns the old "real_payer_iso_country_code" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldRealPayerIsoCountryCode(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRealPayerIsoCountryCode is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRealPayerIsoCountryCode requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRealPayerIsoCountryCode: %w", err)
	}
	return oldValue.RealPayerIsoCountryCode, nil
}

// ClearRealPayerIsoCountryCode clears the value of the "real_payer_iso_country_code" field.
func (m *TransactionMutation) ClearRealPayerIsoCountryCode() {
	m.real_payer_iso_country_code = nil
	m.clearedFields[transaction.FieldRealPayerIsoCountryCode] = struct{}{}
}

// RealPayerIsoCountryCodeCleared returns if the "real_payer_iso_country_code" field was cleared in this mutation.
func (m *TransactionMutation) RealPayerIsoCountryCodeCleared() bool {
	_, ok := m.clearedFields[transaction.FieldRealPayerIsoCountryCode]
	return ok
}

// ResetRealPayerIsoCountryCode resets all changes to the "real_payer_iso_country_code" field.
func (m *TransactionMutation) ResetRealPayerIsoCountryCode() {
	m.real_payer_iso_country_code = nil
	delete(m.clearedFields, transaction.FieldRealPayerIsoCountryCode)
}

// SetRealPayerType sets the "real_payer_type" field.
func (m *TransactionMutation) SetRealPayerType(tpt transaction.RealPayerType) {
	m.real_payer_type = &tpt
}

// RealPayerType returns the value of the "real_payer_type" field in the mutation.
func (m *TransactionMutation) RealPayerType() (r transaction.RealPayerType, exists bool) {
	v := m.real_payer_type
	if v == nil {
		return
	}
	return *v, true
}

// OldRealPayerType returns the old "real_payer_type" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldRealPayerType(ctx context.Context) (v *transaction.RealPayerType, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRealPayerType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRealPayerType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRealPayerType: %w", err)
	}
	return oldValue.RealPayerType, nil
}

// ClearRealPayerType clears the value of the "real_payer_type" field.
func (m *TransactionMutation) ClearRealPayerType() {
	m.real_payer_type = nil
	m.clearedFields[transaction.FieldRealPayerType] = struct{}{}
}

// RealPayerTypeCleared returns if the "real_payer_type" field was cleared in this mutation.
func (m *TransactionMutation) RealPayerTypeCleared() bool {
	_, ok := m.clearedFields[transaction.FieldRealPayerType]
	return ok
}

// ResetRealPayerType resets all changes to the "real_payer_type" field.
func (m *TransactionMutation) ResetRealPayerType() {
	m.real_payer_type = nil
	delete(m.clearedFields, transaction.FieldRealPayerType)
}

// SetBeneficiaryKbe sets the "beneficiary_kbe" field.
func (m *TransactionMutation) SetBeneficiaryKbe(s string) {
	m.beneficiary_kbe = &s
}

// BeneficiaryKbe returns the value of the "beneficiary_kbe" field in the mutation.
func (m *TransactionMutation) BeneficiaryKbe() (r string, exists bool) {
	v := m.beneficiary_kbe
	if v == nil {
		return
	}
	return *v, true
}

// OldBeneficiaryKbe returns the old "beneficiary_kbe" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldBeneficiaryKbe(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBeneficiaryKbe is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBeneficiaryKbe requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBeneficiaryKbe: %w", err)
	}
	return oldValue.BeneficiaryKbe, nil
}

// ClearBeneficiaryKbe clears the value of the "beneficiary_kbe" field.
func (m *TransactionMutation) ClearBeneficiaryKbe() {
	m.beneficiary_kbe = nil
	m.clearedFields[transaction.FieldBeneficiaryKbe] = struct{}{}
}

// BeneficiaryKbeCleared returns if the "beneficiary_kbe" field was cleared in this mutation.
func (m *TransactionMutation) BeneficiaryKbeCleared() bool {
	_, ok := m.clearedFields[transaction.FieldBeneficiaryKbe]
	return ok
}

// ResetBeneficiaryKbe resets all changes to the "beneficiary_kbe" field.
func (m *TransactionMutation) ResetBeneficiaryKbe() {
	m.beneficiary_kbe = nil
	delete(m.clearedFields, transaction.FieldBeneficiaryKbe)
}

// SetBeneficiaryBinIin sets the "beneficiary_bin_iin" field.
func (m *TransactionMutation) SetBeneficiaryBinIin(s string) {
	m.beneficiary_bin_iin = &s
}

// BeneficiaryBinIin returns the value of the "beneficiary_bin_iin" field in the mutation.
func (m *TransactionMutation) BeneficiaryBinIin() (r string, exists bool) {
	v := m.beneficiary_bin_iin
	if v == nil {
		return
	}
	return *v, true
}

// OldBeneficiaryBinIin returns the old "beneficiary_bin_iin" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldBeneficiaryBinIin(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBeneficiaryBinIin is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBeneficiaryBinIin requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBeneficiaryBinIin: %w", err)
	}
	return oldValue.BeneficiaryBinIin, nil
}

// ClearBeneficiaryBinIin clears the value of the "beneficiary_bin_iin" field.
func (m *TransactionMutation) ClearBeneficiaryBinIin() {
	m.beneficiary_bin_iin = nil
	m.clearedFields[transaction.FieldBeneficiaryBinIin] = struct{}{}
}

// BeneficiaryBinIinCleared returns if the "beneficiary_bin_iin" field was cleared in this mutation.
func (m *TransactionMutation) BeneficiaryBinIinCleared() bool {
	_, ok := m.clearedFields[transaction.FieldBeneficiaryBinIin]
	return ok
}

// ResetBeneficiaryBinIin resets all changes to the "beneficiary_bin_iin" field.
func (m *TransactionMutation) ResetBeneficiaryBinIin() {
	m.beneficiary_bin_iin = nil
	delete(m.clearedFields, transaction.FieldBeneficiaryBinIin)
}

// SetBeneficiaryName sets the "beneficiary_name" field.
func (m *TransactionMutation) SetBeneficiaryName(s string) {
	m.beneficiary_name = &s
}

// BeneficiaryName returns the value of the "beneficiary_name" field in the mutation.
func (m *TransactionMutation) BeneficiaryName() (r string, exists bool) {
	v := m.beneficiary_name
	if v == nil {
		return
	}
	return *v, true
}

// OldBeneficiaryName returns the old "beneficiary_name" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldBeneficiaryName(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBeneficiaryName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBeneficiaryName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBeneficiaryName: %w", err)
	}
	return oldValue.BeneficiaryName, nil
}

// ClearBeneficiaryName clears the value of the "beneficiary_name" field.
func (m *TransactionMutation) ClearBeneficiaryName() {
	m.beneficiary_name = nil
	m.clearedFields[transaction.FieldBeneficiaryName] = struct{}{}
}

// BeneficiaryNameCleared returns if the "beneficiary_name" field was cleared in this mutation.
func (m *TransactionMutation) BeneficiaryNameCleared() bool {
	_, ok := m.clearedFields[transaction.FieldBeneficiaryName]
	return ok
}

// ResetBeneficiaryName resets all changes to the "beneficiary_name" field.
func (m *TransactionMutation) ResetBeneficiaryName() {
	m.beneficiary_name = nil
	delete(m.clearedFields, transaction.FieldBeneficiaryName)
}

// SetBeneficiaryType sets the "beneficiary_type" field.
func (m *TransactionMutation) SetBeneficiaryType(tt transaction.BeneficiaryType) {
	m.beneficiary_type = &tt
}

// BeneficiaryType returns the value of the "beneficiary_type" field in the mutation.
func (m *TransactionMutation) BeneficiaryType() (r transaction.BeneficiaryType, exists bool) {
	v := m.beneficiary_type
	if v == nil {
		return
	}
	return *v, true
}

// OldBeneficiaryType returns the old "beneficiary_type" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldBeneficiaryType(ctx context.Context) (v *transaction.BeneficiaryType, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBeneficiaryType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBeneficiaryType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBeneficiaryType: %w", err)
	}
	return oldValue.BeneficiaryType, nil
}

// ClearBeneficiaryType clears the value of the "beneficiary_type" field.
func (m *TransactionMutation) ClearBeneficiaryType() {
	m.beneficiary_type = nil
	m.clearedFields[transaction.FieldBeneficiaryType] = struct{}{}
}

// BeneficiaryTypeCleared returns if the "beneficiary_type" field was cleared in this mutation.
func (m *TransactionMutation) BeneficiaryTypeCleared() bool {
	_, ok := m.clearedFields[transaction.FieldBeneficiaryType]
	return ok
}

// ResetBeneficiaryType resets all changes to the "beneficiary_type" field.
func (m *TransactionMutation) ResetBeneficiaryType() {
	m.beneficiary_type = nil
	delete(m.clearedFields, transaction.FieldBeneficiaryType)
}

// SetBeneficiaryAccountIban sets the "beneficiary_account_iban" field.
func (m *TransactionMutation) SetBeneficiaryAccountIban(s string) {
	m.beneficiary_account_iban = &s
}

// BeneficiaryAccountIban returns the value of the "beneficiary_account_iban" field in the mutation.
func (m *TransactionMutation) BeneficiaryAccountIban() (r string, exists bool) {
	v := m.beneficiary_account_iban
	if v == nil {
		return
	}
	return *v, true
}

// OldBeneficiaryAccountIban returns the old "beneficiary_account_iban" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldBeneficiaryAccountIban(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBeneficiaryAccountIban is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBeneficiaryAccountIban requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBeneficiaryAccountIban: %w", err)
	}
	return oldValue.BeneficiaryAccountIban, nil
}

// ClearBeneficiaryAccountIban clears the value of the "beneficiary_account_iban" field.
func (m *TransactionMutation) ClearBeneficiaryAccountIban() {
	m.beneficiary_account_iban = nil
	m.clearedFields[transaction.FieldBeneficiaryAccountIban] = struct{}{}
}

// BeneficiaryAccountIbanCleared returns if the "beneficiary_account_iban" field was cleared in this mutation.
func (m *TransactionMutation) BeneficiaryAccountIbanCleared() bool {
	_, ok := m.clearedFields[transaction.FieldBeneficiaryAccountIban]
	return ok
}

// ResetBeneficiaryAccountIban resets all changes to the "beneficiary_account_iban" field.
func (m *TransactionMutation) ResetBeneficiaryAccountIban() {
	m.beneficiary_account_iban = nil
	delete(m.clearedFields, transaction.FieldBeneficiaryAccountIban)
}

// SetBeneficiaryBankBic sets the "beneficiary_bank_bic" field.
func (m *TransactionMutation) SetBeneficiaryBankBic(s string) {
	m.beneficiary_bank_bic = &s
}

// BeneficiaryBankBic returns the value of the "beneficiary_bank_bic" field in the mutation.
func (m *TransactionMutation) BeneficiaryBankBic() (r string, exists bool) {
	v := m.beneficiary_bank_bic
	if v == nil {
		return
	}
	return *v, true
}

// OldBeneficiaryBankBic returns the old "beneficiary_bank_bic" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldBeneficiaryBankBic(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBeneficiaryBankBic is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBeneficiaryBankBic requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBeneficiaryBankBic: %w", err)
	}
	return oldValue.BeneficiaryBankBic, nil
}

// ClearBeneficiaryBankBic clears the value of the "beneficiary_bank_bic" field.
func (m *TransactionMutation) ClearBeneficiaryBankBic() {
	m.beneficiary_bank_bic = nil
	m.clearedFields[transaction.FieldBeneficiaryBankBic] = struct{}{}
}

// BeneficiaryBankBicCleared returns if the "beneficiary_bank_bic" field was cleared in this mutation.
func (m *TransactionMutation) BeneficiaryBankBicCleared() bool {
	_, ok := m.clearedFields[transaction.FieldBeneficiaryBankBic]
	return ok
}

// ResetBeneficiaryBankBic resets all changes to the "beneficiary_bank_bic" field.
func (m *TransactionMutation) ResetBeneficiaryBankBic() {
	m.beneficiary_bank_bic = nil
	delete(m.clearedFields, transaction.FieldBeneficiaryBankBic)
}

// SetBeneficiaryBankName sets the "beneficiary_bank_name" field.
func (m *TransactionMutation) SetBeneficiaryBankName(s string) {
	m.beneficiary_bank_name = &s
}

// BeneficiaryBankName returns the value of the "beneficiary_bank_name" field in the mutation.
func (m *TransactionMutation) BeneficiaryBankName() (r string, exists bool) {
	v := m.beneficiary_bank_name
	if v == nil {
		return
	}
	return *v, true
}

// OldBeneficiaryBankName returns the old "beneficiary_bank_name" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldBeneficiaryBankName(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBeneficiaryBankName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBeneficiaryBankName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBeneficiaryBankName: %w", err)
	}
	return oldValue.BeneficiaryBankName, nil
}

// ClearBeneficiaryBankName clears the value of the "beneficiary_bank_name" field.
func (m *TransactionMutation) ClearBeneficiaryBankName() {
	m.beneficiary_bank_name = nil
	m.clearedFields[transaction.FieldBeneficiaryBankName] = struct{}{}
}

// BeneficiaryBankNameCleared returns if the "beneficiary_bank_name" field was cleared in this mutation.
func (m *TransactionMutation) BeneficiaryBankNameCleared() bool {
	_, ok := m.clearedFields[transaction.FieldBeneficiaryBankName]
	return ok
}

// ResetBeneficiaryBankName resets all changes to the "beneficiary_bank_name" field.
func (m *TransactionMutation) ResetBeneficiaryBankName() {
	m.beneficiary_bank_name = nil
	delete(m.clearedFields, transaction.FieldBeneficiaryBankName)
}

// SetBeneficiaryIsoCountryCode sets the "beneficiary_iso_country_code" field.
func (m *TransactionMutation) SetBeneficiaryIsoCountryCode(s string) {
	m.beneficiary_iso_country_code = &s
}

// BeneficiaryIsoCountryCode returns the value of the "beneficiary_iso_country_code" field in the mutation.
func (m *TransactionMutation) BeneficiaryIsoCountryCode() (r string, exists bool) {
	v := m.beneficiary_iso_country_code
	if v == nil {
		return
	}
	return *v, true
}

// OldBeneficiaryIsoCountryCode returns the old "beneficiary_iso_country_code" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldBeneficiaryIsoCountryCode(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBeneficiaryIsoCountryCode is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBeneficiaryIsoCountryCode requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBeneficiaryIsoCountryCode: %w", err)
	}
	return oldValue.BeneficiaryIsoCountryCode, nil
}

// ClearBeneficiaryIsoCountryCode clears the value of the "beneficiary_iso_country_code" field.
func (m *TransactionMutation) ClearBeneficiaryIsoCountryCode() {
	m.beneficiary_iso_country_code = nil
	m.clearedFields[transaction.FieldBeneficiaryIsoCountryCode] = struct{}{}
}

// BeneficiaryIsoCountryCodeCleared returns if the "beneficiary_iso_country_code" field was cleared in this mutation.
func (m *TransactionMutation) BeneficiaryIsoCountryCodeCleared() bool {
	_, ok := m.clearedFields[transaction.FieldBeneficiaryIsoCountryCode]
	return ok
}

// ResetBeneficiaryIsoCountryCode resets all changes to the "beneficiary_iso_country_code" field.
func (m *TransactionMutation) ResetBeneficiaryIsoCountryCode() {
	m.beneficiary_iso_country_code = nil
	delete(m.clearedFields, transaction.FieldBeneficiaryIsoCountryCode)
}

// SetRealBeneficiaryName sets the "real_beneficiary_name" field.
func (m *TransactionMutation) SetRealBeneficiaryName(s string) {
	m.real_beneficiary_name = &s
}

// RealBeneficiaryName returns the value of the "real_beneficiary_name" field in the mutation.
func (m *TransactionMutation) RealBeneficiaryName() (r string, exists bool) {
	v := m.real_beneficiary_name
	if v == nil {
		return
	}
	return *v, true
}

// OldRealBeneficiaryName returns the old "real_beneficiary_name" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldRealBeneficiaryName(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRealBeneficiaryName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRealBeneficiaryName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRealBeneficiaryName: %w", err)
	}
	return oldValue.RealBeneficiaryName, nil
}

// ClearRealBeneficiaryName clears the value of the "real_beneficiary_name" field.
func (m *TransactionMutation) ClearRealBeneficiaryName() {
	m.real_beneficiary_name = nil
	m.clearedFields[transaction.FieldRealBeneficiaryName] = struct{}{}
}

// RealBeneficiaryNameCleared returns if the "real_beneficiary_name" field was cleared in this mutation.
func (m *TransactionMutation) RealBeneficiaryNameCleared() bool {
	_, ok := m.clearedFields[transaction.FieldRealBeneficiaryName]
	return ok
}

// ResetRealBeneficiaryName resets all changes to the "real_beneficiary_name" field.
func (m *TransactionMutation) ResetRealBeneficiaryName() {
	m.real_beneficiary_name = nil
	delete(m.clearedFields, transaction.FieldRealBeneficiaryName)
}

// SetRealBeneficiaryBinIin sets the "real_beneficiary_bin_iin" field.
func (m *TransactionMutation) SetRealBeneficiaryBinIin(s string) {
	m.real_beneficiary_bin_iin = &s
}

// RealBeneficiaryBinIin returns the value of the "real_beneficiary_bin_iin" field in the mutation.
func (m *TransactionMutation) RealBeneficiaryBinIin() (r string, exists bool) {
	v := m.real_beneficiary_bin_iin
	if v == nil {
		return
	}
	return *v, true
}

// OldRealBeneficiaryBinIin returns the old "real_beneficiary_bin_iin" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldRealBeneficiaryBinIin(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRealBeneficiaryBinIin is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRealBeneficiaryBinIin requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRealBeneficiaryBinIin: %w", err)
	}
	return oldValue.RealBeneficiaryBinIin, nil
}

// ClearRealBeneficiaryBinIin clears the value of the "real_beneficiary_bin_iin" field.
func (m *TransactionMutation) ClearRealBeneficiaryBinIin() {
	m.real_beneficiary_bin_iin = nil
	m.clearedFields[transaction.FieldRealBeneficiaryBinIin] = struct{}{}
}

// RealBeneficiaryBinIinCleared returns if the "real_beneficiary_bin_iin" field was cleared in this mutation.
func (m *TransactionMutation) RealBeneficiaryBinIinCleared() bool {
	_, ok := m.clearedFields[transaction.FieldRealBeneficiaryBinIin]
	return ok
}

// ResetRealBeneficiaryBinIin resets all changes to the "real_beneficiary_bin_iin" field.
func (m *TransactionMutation) ResetRealBeneficiaryBinIin() {
	m.real_beneficiary_bin_iin = nil
	delete(m.clearedFields, transaction.FieldRealBeneficiaryBinIin)
}

// SetRealBeneficiaryCountryCode sets the "real_beneficiary_country_code" field.
func (m *TransactionMutation) SetRealBeneficiaryCountryCode(s string) {
	m.real_beneficiary_country_code = &s
}

// RealBeneficiaryCountryCode returns the value of the "real_beneficiary_country_code" field in the mutation.
func (m *TransactionMutation) RealBeneficiaryCountryCode() (r string, exists bool) {
	v := m.real_beneficiary_country_code
	if v == nil {
		return
	}
	return *v, true
}

// OldRealBeneficiaryCountryCode returns the old "real_beneficiary_country_code" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldRealBeneficiaryCountryCode(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRealBeneficiaryCountryCode is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRealBeneficiaryCountryCode requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRealBeneficiaryCountryCode: %w", err)
	}
	return oldValue.RealBeneficiaryCountryCode, nil
}

// ClearRealBeneficiaryCountryCode clears the value of the "real_beneficiary_country_code" field.
func (m *TransactionMutation) ClearRealBeneficiaryCountryCode() {
	m.real_beneficiary_country_code = nil
	m.clearedFields[transaction.FieldRealBeneficiaryCountryCode] = struct{}{}
}

// RealBeneficiaryCountryCodeCleared returns if the "real_beneficiary_country_code" field was cleared in this mutation.
func (m *TransactionMutation) RealBeneficiaryCountryCodeCleared() bool {
	_, ok := m.clearedFields[transaction.FieldRealBeneficiaryCountryCode]
	return ok
}

// ResetRealBeneficiaryCountryCode resets all changes to the "real_beneficiary_country_code" field.
func (m *TransactionMutation) ResetRealBeneficiaryCountryCode() {
	m.real_beneficiary_country_code = nil
	delete(m.clearedFields, transaction.FieldRealBeneficiaryCountryCode)
}

// SetRealBeneficiaryType sets the "real_beneficiary_type" field.
func (m *TransactionMutation) SetRealBeneficiaryType(tbt transaction.RealBeneficiaryType) {
	m.real_beneficiary_type = &tbt
}

// RealBeneficiaryType returns the value of the "real_beneficiary_type" field in the mutation.
func (m *TransactionMutation) RealBeneficiaryType() (r transaction.RealBeneficiaryType, exists bool) {
	v := m.real_beneficiary_type
	if v == nil {
		return
	}
	return *v, true
}

// OldRealBeneficiaryType returns the old "real_beneficiary_type" field's value of the Transaction entity.
// If the Transaction object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TransactionMutation) OldRealBeneficiaryType(ctx context.Context) (v *transaction.RealBeneficiaryType, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRealBeneficiaryType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRealBeneficiaryType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRealBeneficiaryType: %w", err)
	}
	return oldValue.RealBeneficiaryType, nil
}

// ClearRealBeneficiaryType clears the value of the "real_beneficiary_type" field.
func (m *TransactionMutation) ClearRealBeneficiaryType() {
	m.real_beneficiary_type = nil
	m.clearedFields[transaction.FieldRealBeneficiaryType] = struct{}{}
}

// RealBeneficiaryTypeCleared returns if the "real_beneficiary_type" field was cleared in this mutation.
func (m *TransactionMutation) RealBeneficiaryTypeCleared() bool {
	_, ok := m.clearedFields[transaction.FieldRealBeneficiaryType]
	return ok
}

// ResetRealBeneficiaryType resets all changes to the "real_beneficiary_type" field.
func (m *TransactionMutation) ResetRealBeneficiaryType() {
	m.real_beneficiary_type = nil
	delete(m.clearedFields, transaction.FieldRealBeneficiaryType)
}

// Where appends a list predicates to the TransactionMutation builder.
func (m *TransactionMutation) Where(ps ...predicate.Transaction) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the TransactionMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *TransactionMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Transaction, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *TransactionMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *TransactionMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Transaction).
func (m *TransactionMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *TransactionMutation) Fields() []string {
	fields := make([]string, 0, 40)
	if m.create_time != nil {
		fields = append(fields, transaction.FieldCreateTime)
	}
	if m.update_time != nil {
		fields = append(fields, transaction.FieldUpdateTime)
	}
	if m.transaction_number != nil {
		fields = append(fields, transaction.FieldTransactionNumber)
	}
	if m.transaction_date != nil {
		fields = append(fields, transaction.FieldTransactionDate)
	}
	if m.transaction_type != nil {
		fields = append(fields, transaction.FieldTransactionType)
	}
	if m.initiator_id != nil {
		fields = append(fields, transaction.FieldInitiatorID)
	}
	if m.idempotency_key != nil {
		fields = append(fields, transaction.FieldIdempotencyKey)
	}
	if m.value_date != nil {
		fields = append(fields, transaction.FieldValueDate)
	}
	if m.transaction_status != nil {
		fields = append(fields, transaction.FieldTransactionStatus)
	}
	if m.transaction_amount != nil {
		fields = append(fields, transaction.FieldTransactionAmount)
	}
	if m.transaction_comission != nil {
		fields = append(fields, transaction.FieldTransactionComission)
	}
	if m.transaction_currency != nil {
		fields = append(fields, transaction.FieldTransactionCurrency)
	}
	if m.transaction_total_amount != nil {
		fields = append(fields, transaction.FieldTransactionTotalAmount)
	}
	if m.transaction_direction != nil {
		fields = append(fields, transaction.FieldTransactionDirection)
	}
	if m.purpose_code != nil {
		fields = append(fields, transaction.FieldPurposeCode)
	}
	if m.purpose_details != nil {
		fields = append(fields, transaction.FieldPurposeDetails)
	}
	if m.payer_kod != nil {
		fields = append(fields, transaction.FieldPayerKod)
	}
	if m.payer_bin_iin != nil {
		fields = append(fields, transaction.FieldPayerBinIin)
	}
	if m.payer_name != nil {
		fields = append(fields, transaction.FieldPayerName)
	}
	if m.payer_type != nil {
		fields = append(fields, transaction.FieldPayerType)
	}
	if m.payer_account_iban != nil {
		fields = append(fields, transaction.FieldPayerAccountIban)
	}
	if m.payer_bank_bic != nil {
		fields = append(fields, transaction.FieldPayerBankBic)
	}
	if m.payer_bank_name != nil {
		fields = append(fields, transaction.FieldPayerBankName)
	}
	if m.payer_iso_country_code != nil {
		fields = append(fields, transaction.FieldPayerIsoCountryCode)
	}
	if m.real_payer_name != nil {
		fields = append(fields, transaction.FieldRealPayerName)
	}
	if m.real_payer_bin_iin != nil {
		fields = append(fields, transaction.FieldRealPayerBinIin)
	}
	if m.real_payer_iso_country_code != nil {
		fields = append(fields, transaction.FieldRealPayerIsoCountryCode)
	}
	if m.real_payer_type != nil {
		fields = append(fields, transaction.FieldRealPayerType)
	}
	if m.beneficiary_kbe != nil {
		fields = append(fields, transaction.FieldBeneficiaryKbe)
	}
	if m.beneficiary_bin_iin != nil {
		fields = append(fields, transaction.FieldBeneficiaryBinIin)
	}
	if m.beneficiary_name != nil {
		fields = append(fields, transaction.FieldBeneficiaryName)
	}
	if m.beneficiary_type != nil {
		fields = append(fields, transaction.FieldBeneficiaryType)
	}
	if m.beneficiary_account_iban != nil {
		fields = append(fields, transaction.FieldBeneficiaryAccountIban)
	}
	if m.beneficiary_bank_bic != nil {
		fields = append(fields, transaction.FieldBeneficiaryBankBic)
	}
	if m.beneficiary_bank_name != nil {
		fields = append(fields, transaction.FieldBeneficiaryBankName)
	}
	if m.beneficiary_iso_country_code != nil {
		fields = append(fields, transaction.FieldBeneficiaryIsoCountryCode)
	}
	if m.real_beneficiary_name != nil {
		fields = append(fields, transaction.FieldRealBeneficiaryName)
	}
	if m.real_beneficiary_bin_iin != nil {
		fields = append(fields, transaction.FieldRealBeneficiaryBinIin)
	}
	if m.real_beneficiary_country_code != nil {
		fields = append(fields, transaction.FieldRealBeneficiaryCountryCode)
	}
	if m.real_beneficiary_type != nil {
		fields = append(fields, transaction.FieldRealBeneficiaryType)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *TransactionMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case transaction.FieldCreateTime:
		return m.CreateTime()
	case transaction.FieldUpdateTime:
		return m.UpdateTime()
	case transaction.FieldTransactionNumber:
		return m.TransactionNumber()
	case transaction.FieldTransactionDate:
		return m.TransactionDate()
	case transaction.FieldTransactionType:
		return m.TransactionType()
	case transaction.FieldInitiatorID:
		return m.InitiatorID()
	case transaction.FieldIdempotencyKey:
		return m.IdempotencyKey()
	case transaction.FieldValueDate:
		return m.ValueDate()
	case transaction.FieldTransactionStatus:
		return m.TransactionStatus()
	case transaction.FieldTransactionAmount:
		return m.TransactionAmount()
	case transaction.FieldTransactionComission:
		return m.TransactionComission()
	case transaction.FieldTransactionCurrency:
		return m.TransactionCurrency()
	case transaction.FieldTransactionTotalAmount:
		return m.TransactionTotalAmount()
	case transaction.FieldTransactionDirection:
		return m.TransactionDirection()
	case transaction.FieldPurposeCode:
		return m.PurposeCode()
	case transaction.FieldPurposeDetails:
		return m.PurposeDetails()
	case transaction.FieldPayerKod:
		return m.PayerKod()
	case transaction.FieldPayerBinIin:
		return m.PayerBinIin()
	case transaction.FieldPayerName:
		return m.PayerName()
	case transaction.FieldPayerType:
		return m.PayerType()
	case transaction.FieldPayerAccountIban:
		return m.PayerAccountIban()
	case transaction.FieldPayerBankBic:
		return m.PayerBankBic()
	case transaction.FieldPayerBankName:
		return m.PayerBankName()
	case transaction.FieldPayerIsoCountryCode:
		return m.PayerIsoCountryCode()
	case transaction.FieldRealPayerName:
		return m.RealPayerName()
	case transaction.FieldRealPayerBinIin:
		return m.RealPayerBinIin()
	case transaction.FieldRealPayerIsoCountryCode:
		return m.RealPayerIsoCountryCode()
	case transaction.FieldRealPayerType:
		return m.RealPayerType()
	case transaction.FieldBeneficiaryKbe:
		return m.BeneficiaryKbe()
	case transaction.FieldBeneficiaryBinIin:
		return m.BeneficiaryBinIin()
	case transaction.FieldBeneficiaryName:
		return m.BeneficiaryName()
	case transaction.FieldBeneficiaryType:
		return m.BeneficiaryType()
	case transaction.FieldBeneficiaryAccountIban:
		return m.BeneficiaryAccountIban()
	case transaction.FieldBeneficiaryBankBic:
		return m.BeneficiaryBankBic()
	case transaction.FieldBeneficiaryBankName:
		return m.BeneficiaryBankName()
	case transaction.FieldBeneficiaryIsoCountryCode:
		return m.BeneficiaryIsoCountryCode()
	case transaction.FieldRealBeneficiaryName:
		return m.RealBeneficiaryName()
	case transaction.FieldRealBeneficiaryBinIin:
		return m.RealBeneficiaryBinIin()
	case transaction.FieldRealBeneficiaryCountryCode:
		return m.RealBeneficiaryCountryCode()
	case transaction.FieldRealBeneficiaryType:
		return m.RealBeneficiaryType()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *TransactionMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case transaction.FieldCreateTime:
		return m.OldCreateTime(ctx)
	case transaction.FieldUpdateTime:
		return m.OldUpdateTime(ctx)
	case transaction.FieldTransactionNumber:
		return m.OldTransactionNumber(ctx)
	case transaction.FieldTransactionDate:
		return m.OldTransactionDate(ctx)
	case transaction.FieldTransactionType:
		return m.OldTransactionType(ctx)
	case transaction.FieldInitiatorID:
		return m.OldInitiatorID(ctx)
	case transaction.FieldIdempotencyKey:
		return m.OldIdempotencyKey(ctx)
	case transaction.FieldValueDate:
		return m.OldValueDate(ctx)
	case transaction.FieldTransactionStatus:
		return m.OldTransactionStatus(ctx)
	case transaction.FieldTransactionAmount:
		return m.OldTransactionAmount(ctx)
	case transaction.FieldTransactionComission:
		return m.OldTransactionComission(ctx)
	case transaction.FieldTransactionCurrency:
		return m.OldTransactionCurrency(ctx)
	case transaction.FieldTransactionTotalAmount:
		return m.OldTransactionTotalAmount(ctx)
	case transaction.FieldTransactionDirection:
		return m.OldTransactionDirection(ctx)
	case transaction.FieldPurposeCode:
		return m.OldPurposeCode(ctx)
	case transaction.FieldPurposeDetails:
		return m.OldPurposeDetails(ctx)
	case transaction.FieldPayerKod:
		return m.OldPayerKod(ctx)
	case transaction.FieldPayerBinIin:
		return m.OldPayerBinIin(ctx)
	case transaction.FieldPayerName:
		return m.OldPayerName(ctx)
	case transaction.FieldPayerType:
		return m.OldPayerType(ctx)
	case transaction.FieldPayerAccountIban:
		return m.OldPayerAccountIban(ctx)
	case transaction.FieldPayerBankBic:
		return m.OldPayerBankBic(ctx)
	case transaction.FieldPayerBankName:
		return m.OldPayerBankName(ctx)
	case transaction.FieldPayerIsoCountryCode:
		return m.OldPayerIsoCountryCode(ctx)
	case transaction.FieldRealPayerName:
		return m.OldRealPayerName(ctx)
	case transaction.FieldRealPayerBinIin:
		return m.OldRealPayerBinIin(ctx)
	case transaction.FieldRealPayerIsoCountryCode:
		return m.OldRealPayerIsoCountryCode(ctx)
	case transaction.FieldRealPayerType:
		return m.OldRealPayerType(ctx)
	case transaction.FieldBeneficiaryKbe:
		return m.OldBeneficiaryKbe(ctx)
	case transaction.FieldBeneficiaryBinIin:
		return m.OldBeneficiaryBinIin(ctx)
	case transaction.FieldBeneficiaryName:
		return m.OldBeneficiaryName(ctx)
	case transaction.FieldBeneficiaryType:
		return m.OldBeneficiaryType(ctx)
	case transaction.FieldBeneficiaryAccountIban:
		return m.OldBeneficiaryAccountIban(ctx)
	case transaction.FieldBeneficiaryBankBic:
		return m.OldBeneficiaryBankBic(ctx)
	case transaction.FieldBeneficiaryBankName:
		return m.OldBeneficiaryBankName(ctx)
	case transaction.FieldBeneficiaryIsoCountryCode:
		return m.OldBeneficiaryIsoCountryCode(ctx)
	case transaction.FieldRealBeneficiaryName:
		return m.OldRealBeneficiaryName(ctx)
	case transaction.FieldRealBeneficiaryBinIin:
		return m.OldRealBeneficiaryBinIin(ctx)
	case transaction.FieldRealBeneficiaryCountryCode:
		return m.OldRealBeneficiaryCountryCode(ctx)
	case transaction.FieldRealBeneficiaryType:
		return m.OldRealBeneficiaryType(ctx)
	}
	return nil, fmt.Errorf("unknown Transaction field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TransactionMutation) SetField(name string, value ent.Value) error {
	switch name {
	case transaction.FieldCreateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateTime(v)
		return nil
	case transaction.FieldUpdateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateTime(v)
		return nil
	case transaction.FieldTransactionNumber:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTransactionNumber(v)
		return nil
	case transaction.FieldTransactionDate:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTransactionDate(v)
		return nil
	case transaction.FieldTransactionType:
		v, ok := value.(transaction.TransactionType)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTransactionType(v)
		return nil
	case transaction.FieldInitiatorID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetInitiatorID(v)
		return nil
	case transaction.FieldIdempotencyKey:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIdempotencyKey(v)
		return nil
	case transaction.FieldValueDate:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetValueDate(v)
		return nil
	case transaction.FieldTransactionStatus:
		v, ok := value.(transaction.TransactionStatus)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTransactionStatus(v)
		return nil
	case transaction.FieldTransactionAmount:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTransactionAmount(v)
		return nil
	case transaction.FieldTransactionComission:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTransactionComission(v)
		return nil
	case transaction.FieldTransactionCurrency:
		v, ok := value.(transaction.TransactionCurrency)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTransactionCurrency(v)
		return nil
	case transaction.FieldTransactionTotalAmount:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTransactionTotalAmount(v)
		return nil
	case transaction.FieldTransactionDirection:
		v, ok := value.(transaction.TransactionDirection)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTransactionDirection(v)
		return nil
	case transaction.FieldPurposeCode:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPurposeCode(v)
		return nil
	case transaction.FieldPurposeDetails:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPurposeDetails(v)
		return nil
	case transaction.FieldPayerKod:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPayerKod(v)
		return nil
	case transaction.FieldPayerBinIin:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPayerBinIin(v)
		return nil
	case transaction.FieldPayerName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPayerName(v)
		return nil
	case transaction.FieldPayerType:
		v, ok := value.(transaction.PayerType)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPayerType(v)
		return nil
	case transaction.FieldPayerAccountIban:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPayerAccountIban(v)
		return nil
	case transaction.FieldPayerBankBic:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPayerBankBic(v)
		return nil
	case transaction.FieldPayerBankName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPayerBankName(v)
		return nil
	case transaction.FieldPayerIsoCountryCode:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPayerIsoCountryCode(v)
		return nil
	case transaction.FieldRealPayerName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRealPayerName(v)
		return nil
	case transaction.FieldRealPayerBinIin:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRealPayerBinIin(v)
		return nil
	case transaction.FieldRealPayerIsoCountryCode:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRealPayerIsoCountryCode(v)
		return nil
	case transaction.FieldRealPayerType:
		v, ok := value.(transaction.RealPayerType)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRealPayerType(v)
		return nil
	case transaction.FieldBeneficiaryKbe:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBeneficiaryKbe(v)
		return nil
	case transaction.FieldBeneficiaryBinIin:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBeneficiaryBinIin(v)
		return nil
	case transaction.FieldBeneficiaryName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBeneficiaryName(v)
		return nil
	case transaction.FieldBeneficiaryType:
		v, ok := value.(transaction.BeneficiaryType)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBeneficiaryType(v)
		return nil
	case transaction.FieldBeneficiaryAccountIban:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBeneficiaryAccountIban(v)
		return nil
	case transaction.FieldBeneficiaryBankBic:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBeneficiaryBankBic(v)
		return nil
	case transaction.FieldBeneficiaryBankName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBeneficiaryBankName(v)
		return nil
	case transaction.FieldBeneficiaryIsoCountryCode:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBeneficiaryIsoCountryCode(v)
		return nil
	case transaction.FieldRealBeneficiaryName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRealBeneficiaryName(v)
		return nil
	case transaction.FieldRealBeneficiaryBinIin:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRealBeneficiaryBinIin(v)
		return nil
	case transaction.FieldRealBeneficiaryCountryCode:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRealBeneficiaryCountryCode(v)
		return nil
	case transaction.FieldRealBeneficiaryType:
		v, ok := value.(transaction.RealBeneficiaryType)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRealBeneficiaryType(v)
		return nil
	}
	return fmt.Errorf("unknown Transaction field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *TransactionMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *TransactionMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TransactionMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Transaction numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *TransactionMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(transaction.FieldCreateTime) {
		fields = append(fields, transaction.FieldCreateTime)
	}
	if m.FieldCleared(transaction.FieldUpdateTime) {
		fields = append(fields, transaction.FieldUpdateTime)
	}
	if m.FieldCleared(transaction.FieldValueDate) {
		fields = append(fields, transaction.FieldValueDate)
	}
	if m.FieldCleared(transaction.FieldTransactionComission) {
		fields = append(fields, transaction.FieldTransactionComission)
	}
	if m.FieldCleared(transaction.FieldTransactionDirection) {
		fields = append(fields, transaction.FieldTransactionDirection)
	}
	if m.FieldCleared(transaction.FieldPurposeCode) {
		fields = append(fields, transaction.FieldPurposeCode)
	}
	if m.FieldCleared(transaction.FieldPurposeDetails) {
		fields = append(fields, transaction.FieldPurposeDetails)
	}
	if m.FieldCleared(transaction.FieldPayerKod) {
		fields = append(fields, transaction.FieldPayerKod)
	}
	if m.FieldCleared(transaction.FieldPayerBinIin) {
		fields = append(fields, transaction.FieldPayerBinIin)
	}
	if m.FieldCleared(transaction.FieldPayerName) {
		fields = append(fields, transaction.FieldPayerName)
	}
	if m.FieldCleared(transaction.FieldPayerType) {
		fields = append(fields, transaction.FieldPayerType)
	}
	if m.FieldCleared(transaction.FieldPayerAccountIban) {
		fields = append(fields, transaction.FieldPayerAccountIban)
	}
	if m.FieldCleared(transaction.FieldPayerBankBic) {
		fields = append(fields, transaction.FieldPayerBankBic)
	}
	if m.FieldCleared(transaction.FieldPayerBankName) {
		fields = append(fields, transaction.FieldPayerBankName)
	}
	if m.FieldCleared(transaction.FieldPayerIsoCountryCode) {
		fields = append(fields, transaction.FieldPayerIsoCountryCode)
	}
	if m.FieldCleared(transaction.FieldRealPayerName) {
		fields = append(fields, transaction.FieldRealPayerName)
	}
	if m.FieldCleared(transaction.FieldRealPayerBinIin) {
		fields = append(fields, transaction.FieldRealPayerBinIin)
	}
	if m.FieldCleared(transaction.FieldRealPayerIsoCountryCode) {
		fields = append(fields, transaction.FieldRealPayerIsoCountryCode)
	}
	if m.FieldCleared(transaction.FieldRealPayerType) {
		fields = append(fields, transaction.FieldRealPayerType)
	}
	if m.FieldCleared(transaction.FieldBeneficiaryKbe) {
		fields = append(fields, transaction.FieldBeneficiaryKbe)
	}
	if m.FieldCleared(transaction.FieldBeneficiaryBinIin) {
		fields = append(fields, transaction.FieldBeneficiaryBinIin)
	}
	if m.FieldCleared(transaction.FieldBeneficiaryName) {
		fields = append(fields, transaction.FieldBeneficiaryName)
	}
	if m.FieldCleared(transaction.FieldBeneficiaryType) {
		fields = append(fields, transaction.FieldBeneficiaryType)
	}
	if m.FieldCleared(transaction.FieldBeneficiaryAccountIban) {
		fields = append(fields, transaction.FieldBeneficiaryAccountIban)
	}
	if m.FieldCleared(transaction.FieldBeneficiaryBankBic) {
		fields = append(fields, transaction.FieldBeneficiaryBankBic)
	}
	if m.FieldCleared(transaction.FieldBeneficiaryBankName) {
		fields = append(fields, transaction.FieldBeneficiaryBankName)
	}
	if m.FieldCleared(transaction.FieldBeneficiaryIsoCountryCode) {
		fields = append(fields, transaction.FieldBeneficiaryIsoCountryCode)
	}
	if m.FieldCleared(transaction.FieldRealBeneficiaryName) {
		fields = append(fields, transaction.FieldRealBeneficiaryName)
	}
	if m.FieldCleared(transaction.FieldRealBeneficiaryBinIin) {
		fields = append(fields, transaction.FieldRealBeneficiaryBinIin)
	}
	if m.FieldCleared(transaction.FieldRealBeneficiaryCountryCode) {
		fields = append(fields, transaction.FieldRealBeneficiaryCountryCode)
	}
	if m.FieldCleared(transaction.FieldRealBeneficiaryType) {
		fields = append(fields, transaction.FieldRealBeneficiaryType)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *TransactionMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *TransactionMutation) ClearField(name string) error {
	switch name {
	case transaction.FieldCreateTime:
		m.ClearCreateTime()
		return nil
	case transaction.FieldUpdateTime:
		m.ClearUpdateTime()
		return nil
	case transaction.FieldValueDate:
		m.ClearValueDate()
		return nil
	case transaction.FieldTransactionComission:
		m.ClearTransactionComission()
		return nil
	case transaction.FieldTransactionDirection:
		m.ClearTransactionDirection()
		return nil
	case transaction.FieldPurposeCode:
		m.ClearPurposeCode()
		return nil
	case transaction.FieldPurposeDetails:
		m.ClearPurposeDetails()
		return nil
	case transaction.FieldPayerKod:
		m.ClearPayerKod()
		return nil
	case transaction.FieldPayerBinIin:
		m.ClearPayerBinIin()
		return nil
	case transaction.FieldPayerName:
		m.ClearPayerName()
		return nil
	case transaction.FieldPayerType:
		m.ClearPayerType()
		return nil
	case transaction.FieldPayerAccountIban:
		m.ClearPayerAccountIban()
		return nil
	case transaction.FieldPayerBankBic:
		m.ClearPayerBankBic()
		return nil
	case transaction.FieldPayerBankName:
		m.ClearPayerBankName()
		return nil
	case transaction.FieldPayerIsoCountryCode:
		m.ClearPayerIsoCountryCode()
		return nil
	case transaction.FieldRealPayerName:
		m.ClearRealPayerName()
		return nil
	case transaction.FieldRealPayerBinIin:
		m.ClearRealPayerBinIin()
		return nil
	case transaction.FieldRealPayerIsoCountryCode:
		m.ClearRealPayerIsoCountryCode()
		return nil
	case transaction.FieldRealPayerType:
		m.ClearRealPayerType()
		return nil
	case transaction.FieldBeneficiaryKbe:
		m.ClearBeneficiaryKbe()
		return nil
	case transaction.FieldBeneficiaryBinIin:
		m.ClearBeneficiaryBinIin()
		return nil
	case transaction.FieldBeneficiaryName:
		m.ClearBeneficiaryName()
		return nil
	case transaction.FieldBeneficiaryType:
		m.ClearBeneficiaryType()
		return nil
	case transaction.FieldBeneficiaryAccountIban:
		m.ClearBeneficiaryAccountIban()
		return nil
	case transaction.FieldBeneficiaryBankBic:
		m.ClearBeneficiaryBankBic()
		return nil
	case transaction.FieldBeneficiaryBankName:
		m.ClearBeneficiaryBankName()
		return nil
	case transaction.FieldBeneficiaryIsoCountryCode:
		m.ClearBeneficiaryIsoCountryCode()
		return nil
	case transaction.FieldRealBeneficiaryName:
		m.ClearRealBeneficiaryName()
		return nil
	case transaction.FieldRealBeneficiaryBinIin:
		m.ClearRealBeneficiaryBinIin()
		return nil
	case transaction.FieldRealBeneficiaryCountryCode:
		m.ClearRealBeneficiaryCountryCode()
		return nil
	case transaction.FieldRealBeneficiaryType:
		m.ClearRealBeneficiaryType()
		return nil
	}
	return fmt.Errorf("unknown Transaction nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *TransactionMutation) ResetField(name string) error {
	switch name {
	case transaction.FieldCreateTime:
		m.ResetCreateTime()
		return nil
	case transaction.FieldUpdateTime:
		m.ResetUpdateTime()
		return nil
	case transaction.FieldTransactionNumber:
		m.ResetTransactionNumber()
		return nil
	case transaction.FieldTransactionDate:
		m.ResetTransactionDate()
		return nil
	case transaction.FieldTransactionType:
		m.ResetTransactionType()
		return nil
	case transaction.FieldInitiatorID:
		m.ResetInitiatorID()
		return nil
	case transaction.FieldIdempotencyKey:
		m.ResetIdempotencyKey()
		return nil
	case transaction.FieldValueDate:
		m.ResetValueDate()
		return nil
	case transaction.FieldTransactionStatus:
		m.ResetTransactionStatus()
		return nil
	case transaction.FieldTransactionAmount:
		m.ResetTransactionAmount()
		return nil
	case transaction.FieldTransactionComission:
		m.ResetTransactionComission()
		return nil
	case transaction.FieldTransactionCurrency:
		m.ResetTransactionCurrency()
		return nil
	case transaction.FieldTransactionTotalAmount:
		m.ResetTransactionTotalAmount()
		return nil
	case transaction.FieldTransactionDirection:
		m.ResetTransactionDirection()
		return nil
	case transaction.FieldPurposeCode:
		m.ResetPurposeCode()
		return nil
	case transaction.FieldPurposeDetails:
		m.ResetPurposeDetails()
		return nil
	case transaction.FieldPayerKod:
		m.ResetPayerKod()
		return nil
	case transaction.FieldPayerBinIin:
		m.ResetPayerBinIin()
		return nil
	case transaction.FieldPayerName:
		m.ResetPayerName()
		return nil
	case transaction.FieldPayerType:
		m.ResetPayerType()
		return nil
	case transaction.FieldPayerAccountIban:
		m.ResetPayerAccountIban()
		return nil
	case transaction.FieldPayerBankBic:
		m.ResetPayerBankBic()
		return nil
	case transaction.FieldPayerBankName:
		m.ResetPayerBankName()
		return nil
	case transaction.FieldPayerIsoCountryCode:
		m.ResetPayerIsoCountryCode()
		return nil
	case transaction.FieldRealPayerName:
		m.ResetRealPayerName()
		return nil
	case transaction.FieldRealPayerBinIin:
		m.ResetRealPayerBinIin()
		return nil
	case transaction.FieldRealPayerIsoCountryCode:
		m.ResetRealPayerIsoCountryCode()
		return nil
	case transaction.FieldRealPayerType:
		m.ResetRealPayerType()
		return nil
	case transaction.FieldBeneficiaryKbe:
		m.ResetBeneficiaryKbe()
		return nil
	case transaction.FieldBeneficiaryBinIin:
		m.ResetBeneficiaryBinIin()
		return nil
	case transaction.FieldBeneficiaryName:
		m.ResetBeneficiaryName()
		return nil
	case transaction.FieldBeneficiaryType:
		m.ResetBeneficiaryType()
		return nil
	case transaction.FieldBeneficiaryAccountIban:
		m.ResetBeneficiaryAccountIban()
		return nil
	case transaction.FieldBeneficiaryBankBic:
		m.ResetBeneficiaryBankBic()
		return nil
	case transaction.FieldBeneficiaryBankName:
		m.ResetBeneficiaryBankName()
		return nil
	case transaction.FieldBeneficiaryIsoCountryCode:
		m.ResetBeneficiaryIsoCountryCode()
		return nil
	case transaction.FieldRealBeneficiaryName:
		m.ResetRealBeneficiaryName()
		return nil
	case transaction.FieldRealBeneficiaryBinIin:
		m.ResetRealBeneficiaryBinIin()
		return nil
	case transaction.FieldRealBeneficiaryCountryCode:
		m.ResetRealBeneficiaryCountryCode()
		return nil
	case transaction.FieldRealBeneficiaryType:
		m.ResetRealBeneficiaryType()
		return nil
	}
	return fmt.Errorf("unknown Transaction field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *TransactionMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *TransactionMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *TransactionMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *TransactionMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *TransactionMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *TransactionMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *TransactionMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Transaction unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *TransactionMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Transaction edge %s", name)
}
