// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/generateddocument"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/predicate"
)

// GeneratedDocumentUpdate is the builder for updating GeneratedDocument entities.
type GeneratedDocumentUpdate struct {
	config
	hooks     []Hook
	mutation  *GeneratedDocumentMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the GeneratedDocumentUpdate builder.
func (_u *GeneratedDocumentUpdate) Where(ps ...predicate.GeneratedDocument) *GeneratedDocumentUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetParentTransactionID sets the "parent_transaction_id" field.
func (_u *GeneratedDocumentUpdate) SetParentTransactionID(v uuid.UUID) *GeneratedDocumentUpdate {
	_u.mutation.SetParentTransactionID(v)
	return _u
}

// SetNillableParentTransactionID sets the "parent_transaction_id" field if the given value is not nil.
func (_u *GeneratedDocumentUpdate) SetNillableParentTransactionID(v *uuid.UUID) *GeneratedDocumentUpdate {
	if v != nil {
		_u.SetParentTransactionID(*v)
	}
	return _u
}

// SetGeneratedDocumentIntegrationID sets the "generated_document_integration_id" field.
func (_u *GeneratedDocumentUpdate) SetGeneratedDocumentIntegrationID(v uuid.UUID) *GeneratedDocumentUpdate {
	_u.mutation.SetGeneratedDocumentIntegrationID(v)
	return _u
}

// SetNillableGeneratedDocumentIntegrationID sets the "generated_document_integration_id" field if the given value is not nil.
func (_u *GeneratedDocumentUpdate) SetNillableGeneratedDocumentIntegrationID(v *uuid.UUID) *GeneratedDocumentUpdate {
	if v != nil {
		_u.SetGeneratedDocumentIntegrationID(*v)
	}
	return _u
}

// SetGeneratedDocumentType sets the "generated_document_type" field.
func (_u *GeneratedDocumentUpdate) SetGeneratedDocumentType(v string) *GeneratedDocumentUpdate {
	_u.mutation.SetGeneratedDocumentType(v)
	return _u
}

// SetNillableGeneratedDocumentType sets the "generated_document_type" field if the given value is not nil.
func (_u *GeneratedDocumentUpdate) SetNillableGeneratedDocumentType(v *string) *GeneratedDocumentUpdate {
	if v != nil {
		_u.SetGeneratedDocumentType(*v)
	}
	return _u
}

// Mutation returns the GeneratedDocumentMutation object of the builder.
func (_u *GeneratedDocumentUpdate) Mutation() *GeneratedDocumentMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *GeneratedDocumentUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *GeneratedDocumentUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *GeneratedDocumentUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *GeneratedDocumentUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *GeneratedDocumentUpdate) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := generateddocument.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *GeneratedDocumentUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *GeneratedDocumentUpdate {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *GeneratedDocumentUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	_spec := sqlgraph.NewUpdateSpec(generateddocument.Table, generateddocument.Columns, sqlgraph.NewFieldSpec(generateddocument.FieldID, field.TypeUUID))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(generateddocument.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(generateddocument.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(generateddocument.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.ParentTransactionID(); ok {
		_spec.SetField(generateddocument.FieldParentTransactionID, field.TypeUUID, value)
	}
	if value, ok := _u.mutation.GeneratedDocumentIntegrationID(); ok {
		_spec.SetField(generateddocument.FieldGeneratedDocumentIntegrationID, field.TypeUUID, value)
	}
	if value, ok := _u.mutation.GeneratedDocumentType(); ok {
		_spec.SetField(generateddocument.FieldGeneratedDocumentType, field.TypeString, value)
	}
	_spec.AddModifiers(_u.modifiers...)
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{generateddocument.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// GeneratedDocumentUpdateOne is the builder for updating a single GeneratedDocument entity.
type GeneratedDocumentUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *GeneratedDocumentMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetParentTransactionID sets the "parent_transaction_id" field.
func (_u *GeneratedDocumentUpdateOne) SetParentTransactionID(v uuid.UUID) *GeneratedDocumentUpdateOne {
	_u.mutation.SetParentTransactionID(v)
	return _u
}

// SetNillableParentTransactionID sets the "parent_transaction_id" field if the given value is not nil.
func (_u *GeneratedDocumentUpdateOne) SetNillableParentTransactionID(v *uuid.UUID) *GeneratedDocumentUpdateOne {
	if v != nil {
		_u.SetParentTransactionID(*v)
	}
	return _u
}

// SetGeneratedDocumentIntegrationID sets the "generated_document_integration_id" field.
func (_u *GeneratedDocumentUpdateOne) SetGeneratedDocumentIntegrationID(v uuid.UUID) *GeneratedDocumentUpdateOne {
	_u.mutation.SetGeneratedDocumentIntegrationID(v)
	return _u
}

// SetNillableGeneratedDocumentIntegrationID sets the "generated_document_integration_id" field if the given value is not nil.
func (_u *GeneratedDocumentUpdateOne) SetNillableGeneratedDocumentIntegrationID(v *uuid.UUID) *GeneratedDocumentUpdateOne {
	if v != nil {
		_u.SetGeneratedDocumentIntegrationID(*v)
	}
	return _u
}

// SetGeneratedDocumentType sets the "generated_document_type" field.
func (_u *GeneratedDocumentUpdateOne) SetGeneratedDocumentType(v string) *GeneratedDocumentUpdateOne {
	_u.mutation.SetGeneratedDocumentType(v)
	return _u
}

// SetNillableGeneratedDocumentType sets the "generated_document_type" field if the given value is not nil.
func (_u *GeneratedDocumentUpdateOne) SetNillableGeneratedDocumentType(v *string) *GeneratedDocumentUpdateOne {
	if v != nil {
		_u.SetGeneratedDocumentType(*v)
	}
	return _u
}

// Mutation returns the GeneratedDocumentMutation object of the builder.
func (_u *GeneratedDocumentUpdateOne) Mutation() *GeneratedDocumentMutation {
	return _u.mutation
}

// Where appends a list predicates to the GeneratedDocumentUpdate builder.
func (_u *GeneratedDocumentUpdateOne) Where(ps ...predicate.GeneratedDocument) *GeneratedDocumentUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *GeneratedDocumentUpdateOne) Select(field string, fields ...string) *GeneratedDocumentUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated GeneratedDocument entity.
func (_u *GeneratedDocumentUpdateOne) Save(ctx context.Context) (*GeneratedDocument, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *GeneratedDocumentUpdateOne) SaveX(ctx context.Context) *GeneratedDocument {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *GeneratedDocumentUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *GeneratedDocumentUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *GeneratedDocumentUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := generateddocument.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *GeneratedDocumentUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *GeneratedDocumentUpdateOne {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *GeneratedDocumentUpdateOne) sqlSave(ctx context.Context) (_node *GeneratedDocument, err error) {
	_spec := sqlgraph.NewUpdateSpec(generateddocument.Table, generateddocument.Columns, sqlgraph.NewFieldSpec(generateddocument.FieldID, field.TypeUUID))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "GeneratedDocument.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, generateddocument.FieldID)
		for _, f := range fields {
			if !generateddocument.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != generateddocument.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(generateddocument.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(generateddocument.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(generateddocument.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.ParentTransactionID(); ok {
		_spec.SetField(generateddocument.FieldParentTransactionID, field.TypeUUID, value)
	}
	if value, ok := _u.mutation.GeneratedDocumentIntegrationID(); ok {
		_spec.SetField(generateddocument.FieldGeneratedDocumentIntegrationID, field.TypeUUID, value)
	}
	if value, ok := _u.mutation.GeneratedDocumentType(); ok {
		_spec.SetField(generateddocument.FieldGeneratedDocumentType, field.TypeString, value)
	}
	_spec.AddModifiers(_u.modifiers...)
	_node = &GeneratedDocument{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{generateddocument.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
