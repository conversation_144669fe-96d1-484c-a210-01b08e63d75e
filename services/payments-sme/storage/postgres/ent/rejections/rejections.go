// Code generated by ent, DO NOT EDIT.

package rejections

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the rejections type in the database.
	Label = "rejections"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreateTime holds the string denoting the create_time field in the database.
	FieldCreateTime = "create_time"
	// FieldUpdateTime holds the string denoting the update_time field in the database.
	FieldUpdateTime = "update_time"
	// FieldParentTransactionID holds the string denoting the parent_transaction_id field in the database.
	FieldParentTransactionID = "parent_transaction_id"
	// FieldRejectionSource holds the string denoting the rejection_source field in the database.
	FieldRejectionSource = "rejection_source"
	// FieldRejectionCode holds the string denoting the rejection_code field in the database.
	FieldRejectionCode = "rejection_code"
	// FieldRejectionScore holds the string denoting the rejection_score field in the database.
	FieldRejectionScore = "rejection_score"
	// FieldRejectionReason holds the string denoting the rejection_reason field in the database.
	FieldRejectionReason = "rejection_reason"
	// Table holds the table name of the rejections in the database.
	Table = "rejections"
)

// Columns holds all SQL columns for rejections fields.
var Columns = []string{
	FieldID,
	FieldCreateTime,
	FieldUpdateTime,
	FieldParentTransactionID,
	FieldRejectionSource,
	FieldRejectionCode,
	FieldRejectionScore,
	FieldRejectionReason,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreateTime holds the default value on creation for the "create_time" field.
	DefaultCreateTime func() time.Time
	// DefaultUpdateTime holds the default value on creation for the "update_time" field.
	DefaultUpdateTime func() time.Time
	// UpdateDefaultUpdateTime holds the default value on update for the "update_time" field.
	UpdateDefaultUpdateTime func() time.Time
)

// RejectionSource defines the type for the "rejection_source" enum field.
type RejectionSource string

// RejectionSource values.
const (
	RejectionSourceAML       RejectionSource = "AML"
	RejectionSourceAntifraud RejectionSource = "Antifraud"
)

func (rs RejectionSource) String() string {
	return string(rs)
}

// RejectionSourceValidator is a validator for the "rejection_source" field enum values. It is called by the builders before save.
func RejectionSourceValidator(rs RejectionSource) error {
	switch rs {
	case RejectionSourceAML, RejectionSourceAntifraud:
		return nil
	default:
		return fmt.Errorf("rejections: invalid enum value for rejection_source field: %q", rs)
	}
}

// OrderOption defines the ordering options for the Rejections queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreateTime orders the results by the create_time field.
func ByCreateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateTime, opts...).ToFunc()
}

// ByUpdateTime orders the results by the update_time field.
func ByUpdateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateTime, opts...).ToFunc()
}

// ByParentTransactionID orders the results by the parent_transaction_id field.
func ByParentTransactionID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldParentTransactionID, opts...).ToFunc()
}

// ByRejectionSource orders the results by the rejection_source field.
func ByRejectionSource(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRejectionSource, opts...).ToFunc()
}

// ByRejectionCode orders the results by the rejection_code field.
func ByRejectionCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRejectionCode, opts...).ToFunc()
}

// ByRejectionScore orders the results by the rejection_score field.
func ByRejectionScore(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRejectionScore, opts...).ToFunc()
}

// ByRejectionReason orders the results by the rejection_reason field.
func ByRejectionReason(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRejectionReason, opts...).ToFunc()
}
