// Code generated by ent, DO NOT EDIT.

package rejections

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.Rejections {
	return predicate.Rejections(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.Rejections {
	return predicate.Rejections(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.Rejections {
	return predicate.Rejections(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.Rejections {
	return predicate.Rejections(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.Rejections {
	return predicate.Rejections(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.Rejections {
	return predicate.Rejections(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.Rejections {
	return predicate.Rejections(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.Rejections {
	return predicate.Rejections(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.Rejections {
	return predicate.Rejections(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.Rejections {
	return predicate.Rejections(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.Rejections {
	return predicate.Rejections(sql.FieldEQ(FieldUpdateTime, v))
}

// ParentTransactionID applies equality check predicate on the "parent_transaction_id" field. It's identical to ParentTransactionIDEQ.
func ParentTransactionID(v uuid.UUID) predicate.Rejections {
	return predicate.Rejections(sql.FieldEQ(FieldParentTransactionID, v))
}

// RejectionCode applies equality check predicate on the "rejection_code" field. It's identical to RejectionCodeEQ.
func RejectionCode(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldEQ(FieldRejectionCode, v))
}

// RejectionScore applies equality check predicate on the "rejection_score" field. It's identical to RejectionScoreEQ.
func RejectionScore(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldEQ(FieldRejectionScore, v))
}

// RejectionReason applies equality check predicate on the "rejection_reason" field. It's identical to RejectionReasonEQ.
func RejectionReason(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldEQ(FieldRejectionReason, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.Rejections {
	return predicate.Rejections(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.Rejections {
	return predicate.Rejections(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.Rejections {
	return predicate.Rejections(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.Rejections {
	return predicate.Rejections(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.Rejections {
	return predicate.Rejections(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.Rejections {
	return predicate.Rejections(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.Rejections {
	return predicate.Rejections(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.Rejections {
	return predicate.Rejections(sql.FieldLTE(FieldCreateTime, v))
}

// CreateTimeIsNil applies the IsNil predicate on the "create_time" field.
func CreateTimeIsNil() predicate.Rejections {
	return predicate.Rejections(sql.FieldIsNull(FieldCreateTime))
}

// CreateTimeNotNil applies the NotNil predicate on the "create_time" field.
func CreateTimeNotNil() predicate.Rejections {
	return predicate.Rejections(sql.FieldNotNull(FieldCreateTime))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.Rejections {
	return predicate.Rejections(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.Rejections {
	return predicate.Rejections(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.Rejections {
	return predicate.Rejections(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.Rejections {
	return predicate.Rejections(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.Rejections {
	return predicate.Rejections(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.Rejections {
	return predicate.Rejections(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.Rejections {
	return predicate.Rejections(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.Rejections {
	return predicate.Rejections(sql.FieldLTE(FieldUpdateTime, v))
}

// UpdateTimeIsNil applies the IsNil predicate on the "update_time" field.
func UpdateTimeIsNil() predicate.Rejections {
	return predicate.Rejections(sql.FieldIsNull(FieldUpdateTime))
}

// UpdateTimeNotNil applies the NotNil predicate on the "update_time" field.
func UpdateTimeNotNil() predicate.Rejections {
	return predicate.Rejections(sql.FieldNotNull(FieldUpdateTime))
}

// ParentTransactionIDEQ applies the EQ predicate on the "parent_transaction_id" field.
func ParentTransactionIDEQ(v uuid.UUID) predicate.Rejections {
	return predicate.Rejections(sql.FieldEQ(FieldParentTransactionID, v))
}

// ParentTransactionIDNEQ applies the NEQ predicate on the "parent_transaction_id" field.
func ParentTransactionIDNEQ(v uuid.UUID) predicate.Rejections {
	return predicate.Rejections(sql.FieldNEQ(FieldParentTransactionID, v))
}

// ParentTransactionIDIn applies the In predicate on the "parent_transaction_id" field.
func ParentTransactionIDIn(vs ...uuid.UUID) predicate.Rejections {
	return predicate.Rejections(sql.FieldIn(FieldParentTransactionID, vs...))
}

// ParentTransactionIDNotIn applies the NotIn predicate on the "parent_transaction_id" field.
func ParentTransactionIDNotIn(vs ...uuid.UUID) predicate.Rejections {
	return predicate.Rejections(sql.FieldNotIn(FieldParentTransactionID, vs...))
}

// ParentTransactionIDGT applies the GT predicate on the "parent_transaction_id" field.
func ParentTransactionIDGT(v uuid.UUID) predicate.Rejections {
	return predicate.Rejections(sql.FieldGT(FieldParentTransactionID, v))
}

// ParentTransactionIDGTE applies the GTE predicate on the "parent_transaction_id" field.
func ParentTransactionIDGTE(v uuid.UUID) predicate.Rejections {
	return predicate.Rejections(sql.FieldGTE(FieldParentTransactionID, v))
}

// ParentTransactionIDLT applies the LT predicate on the "parent_transaction_id" field.
func ParentTransactionIDLT(v uuid.UUID) predicate.Rejections {
	return predicate.Rejections(sql.FieldLT(FieldParentTransactionID, v))
}

// ParentTransactionIDLTE applies the LTE predicate on the "parent_transaction_id" field.
func ParentTransactionIDLTE(v uuid.UUID) predicate.Rejections {
	return predicate.Rejections(sql.FieldLTE(FieldParentTransactionID, v))
}

// RejectionSourceEQ applies the EQ predicate on the "rejection_source" field.
func RejectionSourceEQ(v RejectionSource) predicate.Rejections {
	return predicate.Rejections(sql.FieldEQ(FieldRejectionSource, v))
}

// RejectionSourceNEQ applies the NEQ predicate on the "rejection_source" field.
func RejectionSourceNEQ(v RejectionSource) predicate.Rejections {
	return predicate.Rejections(sql.FieldNEQ(FieldRejectionSource, v))
}

// RejectionSourceIn applies the In predicate on the "rejection_source" field.
func RejectionSourceIn(vs ...RejectionSource) predicate.Rejections {
	return predicate.Rejections(sql.FieldIn(FieldRejectionSource, vs...))
}

// RejectionSourceNotIn applies the NotIn predicate on the "rejection_source" field.
func RejectionSourceNotIn(vs ...RejectionSource) predicate.Rejections {
	return predicate.Rejections(sql.FieldNotIn(FieldRejectionSource, vs...))
}

// RejectionCodeEQ applies the EQ predicate on the "rejection_code" field.
func RejectionCodeEQ(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldEQ(FieldRejectionCode, v))
}

// RejectionCodeNEQ applies the NEQ predicate on the "rejection_code" field.
func RejectionCodeNEQ(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldNEQ(FieldRejectionCode, v))
}

// RejectionCodeIn applies the In predicate on the "rejection_code" field.
func RejectionCodeIn(vs ...string) predicate.Rejections {
	return predicate.Rejections(sql.FieldIn(FieldRejectionCode, vs...))
}

// RejectionCodeNotIn applies the NotIn predicate on the "rejection_code" field.
func RejectionCodeNotIn(vs ...string) predicate.Rejections {
	return predicate.Rejections(sql.FieldNotIn(FieldRejectionCode, vs...))
}

// RejectionCodeGT applies the GT predicate on the "rejection_code" field.
func RejectionCodeGT(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldGT(FieldRejectionCode, v))
}

// RejectionCodeGTE applies the GTE predicate on the "rejection_code" field.
func RejectionCodeGTE(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldGTE(FieldRejectionCode, v))
}

// RejectionCodeLT applies the LT predicate on the "rejection_code" field.
func RejectionCodeLT(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldLT(FieldRejectionCode, v))
}

// RejectionCodeLTE applies the LTE predicate on the "rejection_code" field.
func RejectionCodeLTE(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldLTE(FieldRejectionCode, v))
}

// RejectionCodeContains applies the Contains predicate on the "rejection_code" field.
func RejectionCodeContains(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldContains(FieldRejectionCode, v))
}

// RejectionCodeHasPrefix applies the HasPrefix predicate on the "rejection_code" field.
func RejectionCodeHasPrefix(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldHasPrefix(FieldRejectionCode, v))
}

// RejectionCodeHasSuffix applies the HasSuffix predicate on the "rejection_code" field.
func RejectionCodeHasSuffix(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldHasSuffix(FieldRejectionCode, v))
}

// RejectionCodeEqualFold applies the EqualFold predicate on the "rejection_code" field.
func RejectionCodeEqualFold(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldEqualFold(FieldRejectionCode, v))
}

// RejectionCodeContainsFold applies the ContainsFold predicate on the "rejection_code" field.
func RejectionCodeContainsFold(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldContainsFold(FieldRejectionCode, v))
}

// RejectionScoreEQ applies the EQ predicate on the "rejection_score" field.
func RejectionScoreEQ(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldEQ(FieldRejectionScore, v))
}

// RejectionScoreNEQ applies the NEQ predicate on the "rejection_score" field.
func RejectionScoreNEQ(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldNEQ(FieldRejectionScore, v))
}

// RejectionScoreIn applies the In predicate on the "rejection_score" field.
func RejectionScoreIn(vs ...string) predicate.Rejections {
	return predicate.Rejections(sql.FieldIn(FieldRejectionScore, vs...))
}

// RejectionScoreNotIn applies the NotIn predicate on the "rejection_score" field.
func RejectionScoreNotIn(vs ...string) predicate.Rejections {
	return predicate.Rejections(sql.FieldNotIn(FieldRejectionScore, vs...))
}

// RejectionScoreGT applies the GT predicate on the "rejection_score" field.
func RejectionScoreGT(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldGT(FieldRejectionScore, v))
}

// RejectionScoreGTE applies the GTE predicate on the "rejection_score" field.
func RejectionScoreGTE(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldGTE(FieldRejectionScore, v))
}

// RejectionScoreLT applies the LT predicate on the "rejection_score" field.
func RejectionScoreLT(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldLT(FieldRejectionScore, v))
}

// RejectionScoreLTE applies the LTE predicate on the "rejection_score" field.
func RejectionScoreLTE(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldLTE(FieldRejectionScore, v))
}

// RejectionScoreContains applies the Contains predicate on the "rejection_score" field.
func RejectionScoreContains(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldContains(FieldRejectionScore, v))
}

// RejectionScoreHasPrefix applies the HasPrefix predicate on the "rejection_score" field.
func RejectionScoreHasPrefix(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldHasPrefix(FieldRejectionScore, v))
}

// RejectionScoreHasSuffix applies the HasSuffix predicate on the "rejection_score" field.
func RejectionScoreHasSuffix(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldHasSuffix(FieldRejectionScore, v))
}

// RejectionScoreEqualFold applies the EqualFold predicate on the "rejection_score" field.
func RejectionScoreEqualFold(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldEqualFold(FieldRejectionScore, v))
}

// RejectionScoreContainsFold applies the ContainsFold predicate on the "rejection_score" field.
func RejectionScoreContainsFold(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldContainsFold(FieldRejectionScore, v))
}

// RejectionReasonEQ applies the EQ predicate on the "rejection_reason" field.
func RejectionReasonEQ(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldEQ(FieldRejectionReason, v))
}

// RejectionReasonNEQ applies the NEQ predicate on the "rejection_reason" field.
func RejectionReasonNEQ(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldNEQ(FieldRejectionReason, v))
}

// RejectionReasonIn applies the In predicate on the "rejection_reason" field.
func RejectionReasonIn(vs ...string) predicate.Rejections {
	return predicate.Rejections(sql.FieldIn(FieldRejectionReason, vs...))
}

// RejectionReasonNotIn applies the NotIn predicate on the "rejection_reason" field.
func RejectionReasonNotIn(vs ...string) predicate.Rejections {
	return predicate.Rejections(sql.FieldNotIn(FieldRejectionReason, vs...))
}

// RejectionReasonGT applies the GT predicate on the "rejection_reason" field.
func RejectionReasonGT(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldGT(FieldRejectionReason, v))
}

// RejectionReasonGTE applies the GTE predicate on the "rejection_reason" field.
func RejectionReasonGTE(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldGTE(FieldRejectionReason, v))
}

// RejectionReasonLT applies the LT predicate on the "rejection_reason" field.
func RejectionReasonLT(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldLT(FieldRejectionReason, v))
}

// RejectionReasonLTE applies the LTE predicate on the "rejection_reason" field.
func RejectionReasonLTE(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldLTE(FieldRejectionReason, v))
}

// RejectionReasonContains applies the Contains predicate on the "rejection_reason" field.
func RejectionReasonContains(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldContains(FieldRejectionReason, v))
}

// RejectionReasonHasPrefix applies the HasPrefix predicate on the "rejection_reason" field.
func RejectionReasonHasPrefix(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldHasPrefix(FieldRejectionReason, v))
}

// RejectionReasonHasSuffix applies the HasSuffix predicate on the "rejection_reason" field.
func RejectionReasonHasSuffix(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldHasSuffix(FieldRejectionReason, v))
}

// RejectionReasonEqualFold applies the EqualFold predicate on the "rejection_reason" field.
func RejectionReasonEqualFold(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldEqualFold(FieldRejectionReason, v))
}

// RejectionReasonContainsFold applies the ContainsFold predicate on the "rejection_reason" field.
func RejectionReasonContainsFold(v string) predicate.Rejections {
	return predicate.Rejections(sql.FieldContainsFold(FieldRejectionReason, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Rejections) predicate.Rejections {
	return predicate.Rejections(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Rejections) predicate.Rejections {
	return predicate.Rejections(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Rejections) predicate.Rejections {
	return predicate.Rejections(sql.NotPredicates(p))
}
