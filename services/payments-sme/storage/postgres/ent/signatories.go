// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/signatories"
)

// Signatories is the model entity for the Signatories schema.
type Signatories struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// SignatoryA holds the value of the "signatory_a" field.
	SignatoryA *string `json:"signatory_a,omitempty"`
	// SignatoryB holds the value of the "signatory_b" field.
	SignatoryB   *string `json:"signatory_b,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Signatories) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case signatories.FieldSignatoryA, signatories.FieldSignatoryB:
			values[i] = new(sql.NullString)
		case signatories.FieldID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Signatories fields.
func (_m *Signatories) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case signatories.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				_m.ID = *value
			}
		case signatories.FieldSignatoryA:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field signatory_a", values[i])
			} else if value.Valid {
				_m.SignatoryA = new(string)
				*_m.SignatoryA = value.String
			}
		case signatories.FieldSignatoryB:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field signatory_b", values[i])
			} else if value.Valid {
				_m.SignatoryB = new(string)
				*_m.SignatoryB = value.String
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Signatories.
// This includes values selected through modifiers, order, etc.
func (_m *Signatories) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// Update returns a builder for updating this Signatories.
// Note that you need to call Signatories.Unwrap() before calling this method if this Signatories
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *Signatories) Update() *SignatoriesUpdateOne {
	return NewSignatoriesClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the Signatories entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *Signatories) Unwrap() *Signatories {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("ent: Signatories is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *Signatories) String() string {
	var builder strings.Builder
	builder.WriteString("Signatories(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	if v := _m.SignatoryA; v != nil {
		builder.WriteString("signatory_a=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.SignatoryB; v != nil {
		builder.WriteString("signatory_b=")
		builder.WriteString(*v)
	}
	builder.WriteByte(')')
	return builder.String()
}

// SignatoriesSlice is a parsable slice of Signatories.
type SignatoriesSlice []*Signatories
