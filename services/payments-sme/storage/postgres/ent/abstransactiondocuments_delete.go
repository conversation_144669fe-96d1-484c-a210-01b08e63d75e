// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/abstransactiondocuments"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/predicate"
)

// AbsTransactionDocumentsDelete is the builder for deleting a AbsTransactionDocuments entity.
type AbsTransactionDocumentsDelete struct {
	config
	hooks    []Hook
	mutation *AbsTransactionDocumentsMutation
}

// Where appends a list predicates to the AbsTransactionDocumentsDelete builder.
func (_d *AbsTransactionDocumentsDelete) Where(ps ...predicate.AbsTransactionDocuments) *AbsTransactionDocumentsDelete {
	_d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (_d *AbsTransactionDocumentsDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, _d.sqlExec, _d.mutation, _d.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *AbsTransactionDocumentsDelete) ExecX(ctx context.Context) int {
	n, err := _d.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (_d *AbsTransactionDocumentsDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(abstransactiondocuments.Table, sqlgraph.NewFieldSpec(abstransactiondocuments.FieldID, field.TypeUUID))
	if ps := _d.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, _d.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	_d.mutation.done = true
	return affected, err
}

// AbsTransactionDocumentsDeleteOne is the builder for deleting a single AbsTransactionDocuments entity.
type AbsTransactionDocumentsDeleteOne struct {
	_d *AbsTransactionDocumentsDelete
}

// Where appends a list predicates to the AbsTransactionDocumentsDelete builder.
func (_d *AbsTransactionDocumentsDeleteOne) Where(ps ...predicate.AbsTransactionDocuments) *AbsTransactionDocumentsDeleteOne {
	_d._d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query.
func (_d *AbsTransactionDocumentsDeleteOne) Exec(ctx context.Context) error {
	n, err := _d._d.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{abstransactiondocuments.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *AbsTransactionDocumentsDeleteOne) ExecX(ctx context.Context) {
	if err := _d.Exec(ctx); err != nil {
		panic(err)
	}
}
