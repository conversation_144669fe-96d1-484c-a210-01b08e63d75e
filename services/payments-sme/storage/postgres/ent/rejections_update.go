// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/predicate"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/rejections"
)

// RejectionsUpdate is the builder for updating Rejections entities.
type RejectionsUpdate struct {
	config
	hooks     []Hook
	mutation  *RejectionsMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the RejectionsUpdate builder.
func (_u *RejectionsUpdate) Where(ps ...predicate.Rejections) *RejectionsUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetParentTransactionID sets the "parent_transaction_id" field.
func (_u *RejectionsUpdate) SetParentTransactionID(v uuid.UUID) *RejectionsUpdate {
	_u.mutation.SetParentTransactionID(v)
	return _u
}

// SetNillableParentTransactionID sets the "parent_transaction_id" field if the given value is not nil.
func (_u *RejectionsUpdate) SetNillableParentTransactionID(v *uuid.UUID) *RejectionsUpdate {
	if v != nil {
		_u.SetParentTransactionID(*v)
	}
	return _u
}

// SetRejectionSource sets the "rejection_source" field.
func (_u *RejectionsUpdate) SetRejectionSource(v rejections.RejectionSource) *RejectionsUpdate {
	_u.mutation.SetRejectionSource(v)
	return _u
}

// SetNillableRejectionSource sets the "rejection_source" field if the given value is not nil.
func (_u *RejectionsUpdate) SetNillableRejectionSource(v *rejections.RejectionSource) *RejectionsUpdate {
	if v != nil {
		_u.SetRejectionSource(*v)
	}
	return _u
}

// SetRejectionCode sets the "rejection_code" field.
func (_u *RejectionsUpdate) SetRejectionCode(v string) *RejectionsUpdate {
	_u.mutation.SetRejectionCode(v)
	return _u
}

// SetNillableRejectionCode sets the "rejection_code" field if the given value is not nil.
func (_u *RejectionsUpdate) SetNillableRejectionCode(v *string) *RejectionsUpdate {
	if v != nil {
		_u.SetRejectionCode(*v)
	}
	return _u
}

// SetRejectionScore sets the "rejection_score" field.
func (_u *RejectionsUpdate) SetRejectionScore(v string) *RejectionsUpdate {
	_u.mutation.SetRejectionScore(v)
	return _u
}

// SetNillableRejectionScore sets the "rejection_score" field if the given value is not nil.
func (_u *RejectionsUpdate) SetNillableRejectionScore(v *string) *RejectionsUpdate {
	if v != nil {
		_u.SetRejectionScore(*v)
	}
	return _u
}

// SetRejectionReason sets the "rejection_reason" field.
func (_u *RejectionsUpdate) SetRejectionReason(v string) *RejectionsUpdate {
	_u.mutation.SetRejectionReason(v)
	return _u
}

// SetNillableRejectionReason sets the "rejection_reason" field if the given value is not nil.
func (_u *RejectionsUpdate) SetNillableRejectionReason(v *string) *RejectionsUpdate {
	if v != nil {
		_u.SetRejectionReason(*v)
	}
	return _u
}

// Mutation returns the RejectionsMutation object of the builder.
func (_u *RejectionsUpdate) Mutation() *RejectionsMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *RejectionsUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *RejectionsUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *RejectionsUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *RejectionsUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *RejectionsUpdate) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := rejections.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *RejectionsUpdate) check() error {
	if v, ok := _u.mutation.RejectionSource(); ok {
		if err := rejections.RejectionSourceValidator(v); err != nil {
			return &ValidationError{Name: "rejection_source", err: fmt.Errorf(`ent: validator failed for field "Rejections.rejection_source": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *RejectionsUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *RejectionsUpdate {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *RejectionsUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(rejections.Table, rejections.Columns, sqlgraph.NewFieldSpec(rejections.FieldID, field.TypeUUID))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(rejections.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(rejections.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(rejections.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.ParentTransactionID(); ok {
		_spec.SetField(rejections.FieldParentTransactionID, field.TypeUUID, value)
	}
	if value, ok := _u.mutation.RejectionSource(); ok {
		_spec.SetField(rejections.FieldRejectionSource, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.RejectionCode(); ok {
		_spec.SetField(rejections.FieldRejectionCode, field.TypeString, value)
	}
	if value, ok := _u.mutation.RejectionScore(); ok {
		_spec.SetField(rejections.FieldRejectionScore, field.TypeString, value)
	}
	if value, ok := _u.mutation.RejectionReason(); ok {
		_spec.SetField(rejections.FieldRejectionReason, field.TypeString, value)
	}
	_spec.AddModifiers(_u.modifiers...)
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{rejections.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// RejectionsUpdateOne is the builder for updating a single Rejections entity.
type RejectionsUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *RejectionsMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetParentTransactionID sets the "parent_transaction_id" field.
func (_u *RejectionsUpdateOne) SetParentTransactionID(v uuid.UUID) *RejectionsUpdateOne {
	_u.mutation.SetParentTransactionID(v)
	return _u
}

// SetNillableParentTransactionID sets the "parent_transaction_id" field if the given value is not nil.
func (_u *RejectionsUpdateOne) SetNillableParentTransactionID(v *uuid.UUID) *RejectionsUpdateOne {
	if v != nil {
		_u.SetParentTransactionID(*v)
	}
	return _u
}

// SetRejectionSource sets the "rejection_source" field.
func (_u *RejectionsUpdateOne) SetRejectionSource(v rejections.RejectionSource) *RejectionsUpdateOne {
	_u.mutation.SetRejectionSource(v)
	return _u
}

// SetNillableRejectionSource sets the "rejection_source" field if the given value is not nil.
func (_u *RejectionsUpdateOne) SetNillableRejectionSource(v *rejections.RejectionSource) *RejectionsUpdateOne {
	if v != nil {
		_u.SetRejectionSource(*v)
	}
	return _u
}

// SetRejectionCode sets the "rejection_code" field.
func (_u *RejectionsUpdateOne) SetRejectionCode(v string) *RejectionsUpdateOne {
	_u.mutation.SetRejectionCode(v)
	return _u
}

// SetNillableRejectionCode sets the "rejection_code" field if the given value is not nil.
func (_u *RejectionsUpdateOne) SetNillableRejectionCode(v *string) *RejectionsUpdateOne {
	if v != nil {
		_u.SetRejectionCode(*v)
	}
	return _u
}

// SetRejectionScore sets the "rejection_score" field.
func (_u *RejectionsUpdateOne) SetRejectionScore(v string) *RejectionsUpdateOne {
	_u.mutation.SetRejectionScore(v)
	return _u
}

// SetNillableRejectionScore sets the "rejection_score" field if the given value is not nil.
func (_u *RejectionsUpdateOne) SetNillableRejectionScore(v *string) *RejectionsUpdateOne {
	if v != nil {
		_u.SetRejectionScore(*v)
	}
	return _u
}

// SetRejectionReason sets the "rejection_reason" field.
func (_u *RejectionsUpdateOne) SetRejectionReason(v string) *RejectionsUpdateOne {
	_u.mutation.SetRejectionReason(v)
	return _u
}

// SetNillableRejectionReason sets the "rejection_reason" field if the given value is not nil.
func (_u *RejectionsUpdateOne) SetNillableRejectionReason(v *string) *RejectionsUpdateOne {
	if v != nil {
		_u.SetRejectionReason(*v)
	}
	return _u
}

// Mutation returns the RejectionsMutation object of the builder.
func (_u *RejectionsUpdateOne) Mutation() *RejectionsMutation {
	return _u.mutation
}

// Where appends a list predicates to the RejectionsUpdate builder.
func (_u *RejectionsUpdateOne) Where(ps ...predicate.Rejections) *RejectionsUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *RejectionsUpdateOne) Select(field string, fields ...string) *RejectionsUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated Rejections entity.
func (_u *RejectionsUpdateOne) Save(ctx context.Context) (*Rejections, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *RejectionsUpdateOne) SaveX(ctx context.Context) *Rejections {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *RejectionsUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *RejectionsUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *RejectionsUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := rejections.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *RejectionsUpdateOne) check() error {
	if v, ok := _u.mutation.RejectionSource(); ok {
		if err := rejections.RejectionSourceValidator(v); err != nil {
			return &ValidationError{Name: "rejection_source", err: fmt.Errorf(`ent: validator failed for field "Rejections.rejection_source": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *RejectionsUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *RejectionsUpdateOne {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *RejectionsUpdateOne) sqlSave(ctx context.Context) (_node *Rejections, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(rejections.Table, rejections.Columns, sqlgraph.NewFieldSpec(rejections.FieldID, field.TypeUUID))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Rejections.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, rejections.FieldID)
		for _, f := range fields {
			if !rejections.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != rejections.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(rejections.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(rejections.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(rejections.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.ParentTransactionID(); ok {
		_spec.SetField(rejections.FieldParentTransactionID, field.TypeUUID, value)
	}
	if value, ok := _u.mutation.RejectionSource(); ok {
		_spec.SetField(rejections.FieldRejectionSource, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.RejectionCode(); ok {
		_spec.SetField(rejections.FieldRejectionCode, field.TypeString, value)
	}
	if value, ok := _u.mutation.RejectionScore(); ok {
		_spec.SetField(rejections.FieldRejectionScore, field.TypeString, value)
	}
	if value, ok := _u.mutation.RejectionReason(); ok {
		_spec.SetField(rejections.FieldRejectionReason, field.TypeString, value)
	}
	_spec.AddModifiers(_u.modifiers...)
	_node = &Rejections{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{rejections.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
