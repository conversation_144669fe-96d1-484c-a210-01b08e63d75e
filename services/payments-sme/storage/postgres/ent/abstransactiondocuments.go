// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/abstransactiondocuments"
)

// AbsTransactionDocuments is the model entity for the AbsTransactionDocuments schema.
type AbsTransactionDocuments struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// CreateTime holds the value of the "create_time" field.
	CreateTime time.Time `json:"create_time,omitempty"`
	// UpdateTime holds the value of the "update_time" field.
	UpdateTime time.Time `json:"update_time,omitempty"`
	// ReferenceID holds the value of the "reference_id" field.
	ReferenceID string `json:"reference_id,omitempty"`
	// DocumentType holds the value of the "document_type" field.
	DocumentType *string `json:"document_type,omitempty"`
	// ReferenceDate holds the value of the "reference_date" field.
	ReferenceDate *string `json:"reference_date,omitempty"`
	// DocumentStatus holds the value of the "document_status" field.
	DocumentStatus *abstransactiondocuments.DocumentStatus `json:"document_status,omitempty"`
	// RejectionReason holds the value of the "rejection_reason" field.
	RejectionReason *abstransactiondocuments.RejectionReason `json:"rejection_reason,omitempty"`
	selectValues    sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*AbsTransactionDocuments) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case abstransactiondocuments.FieldReferenceID, abstransactiondocuments.FieldDocumentType, abstransactiondocuments.FieldReferenceDate, abstransactiondocuments.FieldDocumentStatus, abstransactiondocuments.FieldRejectionReason:
			values[i] = new(sql.NullString)
		case abstransactiondocuments.FieldCreateTime, abstransactiondocuments.FieldUpdateTime:
			values[i] = new(sql.NullTime)
		case abstransactiondocuments.FieldID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the AbsTransactionDocuments fields.
func (_m *AbsTransactionDocuments) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case abstransactiondocuments.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				_m.ID = *value
			}
		case abstransactiondocuments.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				_m.CreateTime = value.Time
			}
		case abstransactiondocuments.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				_m.UpdateTime = value.Time
			}
		case abstransactiondocuments.FieldReferenceID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field reference_id", values[i])
			} else if value.Valid {
				_m.ReferenceID = value.String
			}
		case abstransactiondocuments.FieldDocumentType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field document_type", values[i])
			} else if value.Valid {
				_m.DocumentType = new(string)
				*_m.DocumentType = value.String
			}
		case abstransactiondocuments.FieldReferenceDate:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field reference_date", values[i])
			} else if value.Valid {
				_m.ReferenceDate = new(string)
				*_m.ReferenceDate = value.String
			}
		case abstransactiondocuments.FieldDocumentStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field document_status", values[i])
			} else if value.Valid {
				_m.DocumentStatus = new(abstransactiondocuments.DocumentStatus)
				*_m.DocumentStatus = abstransactiondocuments.DocumentStatus(value.String)
			}
		case abstransactiondocuments.FieldRejectionReason:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field rejection_reason", values[i])
			} else if value.Valid {
				_m.RejectionReason = new(abstransactiondocuments.RejectionReason)
				*_m.RejectionReason = abstransactiondocuments.RejectionReason(value.String)
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the AbsTransactionDocuments.
// This includes values selected through modifiers, order, etc.
func (_m *AbsTransactionDocuments) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// Update returns a builder for updating this AbsTransactionDocuments.
// Note that you need to call AbsTransactionDocuments.Unwrap() before calling this method if this AbsTransactionDocuments
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *AbsTransactionDocuments) Update() *AbsTransactionDocumentsUpdateOne {
	return NewAbsTransactionDocumentsClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the AbsTransactionDocuments entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *AbsTransactionDocuments) Unwrap() *AbsTransactionDocuments {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("ent: AbsTransactionDocuments is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *AbsTransactionDocuments) String() string {
	var builder strings.Builder
	builder.WriteString("AbsTransactionDocuments(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("create_time=")
	builder.WriteString(_m.CreateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_time=")
	builder.WriteString(_m.UpdateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("reference_id=")
	builder.WriteString(_m.ReferenceID)
	builder.WriteString(", ")
	if v := _m.DocumentType; v != nil {
		builder.WriteString("document_type=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.ReferenceDate; v != nil {
		builder.WriteString("reference_date=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.DocumentStatus; v != nil {
		builder.WriteString("document_status=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := _m.RejectionReason; v != nil {
		builder.WriteString("rejection_reason=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteByte(')')
	return builder.String()
}

// AbsTransactionDocumentsSlice is a parsable slice of AbsTransactionDocuments.
type AbsTransactionDocumentsSlice []*AbsTransactionDocuments
