// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/confirmations"
)

// ConfirmationsCreate is the builder for creating a Confirmations entity.
type ConfirmationsCreate struct {
	config
	mutation *ConfirmationsMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetConfirmationType sets the "confirmation_type" field.
func (_c *ConfirmationsCreate) SetConfirmationType(v confirmations.ConfirmationType) *ConfirmationsCreate {
	_c.mutation.SetConfirmationType(v)
	return _c
}

// SetConfirmationDate sets the "confirmation_date" field.
func (_c *ConfirmationsCreate) SetConfirmationDate(v time.Time) *ConfirmationsCreate {
	_c.mutation.SetConfirmationDate(v)
	return _c
}

// SetID sets the "id" field.
func (_c *ConfirmationsCreate) SetID(v uuid.UUID) *ConfirmationsCreate {
	_c.mutation.SetID(v)
	return _c
}

// Mutation returns the ConfirmationsMutation object of the builder.
func (_c *ConfirmationsCreate) Mutation() *ConfirmationsMutation {
	return _c.mutation
}

// Save creates the Confirmations in the database.
func (_c *ConfirmationsCreate) Save(ctx context.Context) (*Confirmations, error) {
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *ConfirmationsCreate) SaveX(ctx context.Context) *Confirmations {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *ConfirmationsCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *ConfirmationsCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *ConfirmationsCreate) check() error {
	if _, ok := _c.mutation.ConfirmationType(); !ok {
		return &ValidationError{Name: "confirmation_type", err: errors.New(`ent: missing required field "Confirmations.confirmation_type"`)}
	}
	if v, ok := _c.mutation.ConfirmationType(); ok {
		if err := confirmations.ConfirmationTypeValidator(v); err != nil {
			return &ValidationError{Name: "confirmation_type", err: fmt.Errorf(`ent: validator failed for field "Confirmations.confirmation_type": %w`, err)}
		}
	}
	if _, ok := _c.mutation.ConfirmationDate(); !ok {
		return &ValidationError{Name: "confirmation_date", err: errors.New(`ent: missing required field "Confirmations.confirmation_date"`)}
	}
	return nil
}

func (_c *ConfirmationsCreate) sqlSave(ctx context.Context) (*Confirmations, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *ConfirmationsCreate) createSpec() (*Confirmations, *sqlgraph.CreateSpec) {
	var (
		_node = &Confirmations{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(confirmations.Table, sqlgraph.NewFieldSpec(confirmations.FieldID, field.TypeUUID))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := _c.mutation.ConfirmationType(); ok {
		_spec.SetField(confirmations.FieldConfirmationType, field.TypeEnum, value)
		_node.ConfirmationType = value
	}
	if value, ok := _c.mutation.ConfirmationDate(); ok {
		_spec.SetField(confirmations.FieldConfirmationDate, field.TypeTime, value)
		_node.ConfirmationDate = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Confirmations.Create().
//		SetConfirmationType(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.ConfirmationsUpsert) {
//			SetConfirmationType(v+v).
//		}).
//		Exec(ctx)
func (_c *ConfirmationsCreate) OnConflict(opts ...sql.ConflictOption) *ConfirmationsUpsertOne {
	_c.conflict = opts
	return &ConfirmationsUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Confirmations.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *ConfirmationsCreate) OnConflictColumns(columns ...string) *ConfirmationsUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &ConfirmationsUpsertOne{
		create: _c,
	}
}

type (
	// ConfirmationsUpsertOne is the builder for "upsert"-ing
	//  one Confirmations node.
	ConfirmationsUpsertOne struct {
		create *ConfirmationsCreate
	}

	// ConfirmationsUpsert is the "OnConflict" setter.
	ConfirmationsUpsert struct {
		*sql.UpdateSet
	}
)

// SetConfirmationType sets the "confirmation_type" field.
func (u *ConfirmationsUpsert) SetConfirmationType(v confirmations.ConfirmationType) *ConfirmationsUpsert {
	u.Set(confirmations.FieldConfirmationType, v)
	return u
}

// UpdateConfirmationType sets the "confirmation_type" field to the value that was provided on create.
func (u *ConfirmationsUpsert) UpdateConfirmationType() *ConfirmationsUpsert {
	u.SetExcluded(confirmations.FieldConfirmationType)
	return u
}

// SetConfirmationDate sets the "confirmation_date" field.
func (u *ConfirmationsUpsert) SetConfirmationDate(v time.Time) *ConfirmationsUpsert {
	u.Set(confirmations.FieldConfirmationDate, v)
	return u
}

// UpdateConfirmationDate sets the "confirmation_date" field to the value that was provided on create.
func (u *ConfirmationsUpsert) UpdateConfirmationDate() *ConfirmationsUpsert {
	u.SetExcluded(confirmations.FieldConfirmationDate)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Confirmations.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(confirmations.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *ConfirmationsUpsertOne) UpdateNewValues() *ConfirmationsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(confirmations.FieldID)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Confirmations.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *ConfirmationsUpsertOne) Ignore() *ConfirmationsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *ConfirmationsUpsertOne) DoNothing() *ConfirmationsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the ConfirmationsCreate.OnConflict
// documentation for more info.
func (u *ConfirmationsUpsertOne) Update(set func(*ConfirmationsUpsert)) *ConfirmationsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&ConfirmationsUpsert{UpdateSet: update})
	}))
	return u
}

// SetConfirmationType sets the "confirmation_type" field.
func (u *ConfirmationsUpsertOne) SetConfirmationType(v confirmations.ConfirmationType) *ConfirmationsUpsertOne {
	return u.Update(func(s *ConfirmationsUpsert) {
		s.SetConfirmationType(v)
	})
}

// UpdateConfirmationType sets the "confirmation_type" field to the value that was provided on create.
func (u *ConfirmationsUpsertOne) UpdateConfirmationType() *ConfirmationsUpsertOne {
	return u.Update(func(s *ConfirmationsUpsert) {
		s.UpdateConfirmationType()
	})
}

// SetConfirmationDate sets the "confirmation_date" field.
func (u *ConfirmationsUpsertOne) SetConfirmationDate(v time.Time) *ConfirmationsUpsertOne {
	return u.Update(func(s *ConfirmationsUpsert) {
		s.SetConfirmationDate(v)
	})
}

// UpdateConfirmationDate sets the "confirmation_date" field to the value that was provided on create.
func (u *ConfirmationsUpsertOne) UpdateConfirmationDate() *ConfirmationsUpsertOne {
	return u.Update(func(s *ConfirmationsUpsert) {
		s.UpdateConfirmationDate()
	})
}

// Exec executes the query.
func (u *ConfirmationsUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for ConfirmationsCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *ConfirmationsUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *ConfirmationsUpsertOne) ID(ctx context.Context) (id uuid.UUID, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: ConfirmationsUpsertOne.ID is not supported by MySQL driver. Use ConfirmationsUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *ConfirmationsUpsertOne) IDX(ctx context.Context) uuid.UUID {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// ConfirmationsCreateBulk is the builder for creating many Confirmations entities in bulk.
type ConfirmationsCreateBulk struct {
	config
	err      error
	builders []*ConfirmationsCreate
	conflict []sql.ConflictOption
}

// Save creates the Confirmations entities in the database.
func (_c *ConfirmationsCreateBulk) Save(ctx context.Context) ([]*Confirmations, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*Confirmations, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*ConfirmationsMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *ConfirmationsCreateBulk) SaveX(ctx context.Context) []*Confirmations {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *ConfirmationsCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *ConfirmationsCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Confirmations.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.ConfirmationsUpsert) {
//			SetConfirmationType(v+v).
//		}).
//		Exec(ctx)
func (_c *ConfirmationsCreateBulk) OnConflict(opts ...sql.ConflictOption) *ConfirmationsUpsertBulk {
	_c.conflict = opts
	return &ConfirmationsUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Confirmations.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *ConfirmationsCreateBulk) OnConflictColumns(columns ...string) *ConfirmationsUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &ConfirmationsUpsertBulk{
		create: _c,
	}
}

// ConfirmationsUpsertBulk is the builder for "upsert"-ing
// a bulk of Confirmations nodes.
type ConfirmationsUpsertBulk struct {
	create *ConfirmationsCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Confirmations.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(confirmations.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *ConfirmationsUpsertBulk) UpdateNewValues() *ConfirmationsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(confirmations.FieldID)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Confirmations.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *ConfirmationsUpsertBulk) Ignore() *ConfirmationsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *ConfirmationsUpsertBulk) DoNothing() *ConfirmationsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the ConfirmationsCreateBulk.OnConflict
// documentation for more info.
func (u *ConfirmationsUpsertBulk) Update(set func(*ConfirmationsUpsert)) *ConfirmationsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&ConfirmationsUpsert{UpdateSet: update})
	}))
	return u
}

// SetConfirmationType sets the "confirmation_type" field.
func (u *ConfirmationsUpsertBulk) SetConfirmationType(v confirmations.ConfirmationType) *ConfirmationsUpsertBulk {
	return u.Update(func(s *ConfirmationsUpsert) {
		s.SetConfirmationType(v)
	})
}

// UpdateConfirmationType sets the "confirmation_type" field to the value that was provided on create.
func (u *ConfirmationsUpsertBulk) UpdateConfirmationType() *ConfirmationsUpsertBulk {
	return u.Update(func(s *ConfirmationsUpsert) {
		s.UpdateConfirmationType()
	})
}

// SetConfirmationDate sets the "confirmation_date" field.
func (u *ConfirmationsUpsertBulk) SetConfirmationDate(v time.Time) *ConfirmationsUpsertBulk {
	return u.Update(func(s *ConfirmationsUpsert) {
		s.SetConfirmationDate(v)
	})
}

// UpdateConfirmationDate sets the "confirmation_date" field to the value that was provided on create.
func (u *ConfirmationsUpsertBulk) UpdateConfirmationDate() *ConfirmationsUpsertBulk {
	return u.Update(func(s *ConfirmationsUpsert) {
		s.UpdateConfirmationDate()
	})
}

// Exec executes the query.
func (u *ConfirmationsUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the ConfirmationsCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for ConfirmationsCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *ConfirmationsUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
