// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// AbsTransactionDocumentsColumns holds the columns for the "abs_transaction_documents" table.
	AbsTransactionDocumentsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "create_time", Type: field.TypeTime, Nullable: true},
		{Name: "update_time", Type: field.TypeTime, Nullable: true},
		{Name: "reference_id", Type: field.TypeString},
		{Name: "document_type", Type: field.TypeString, Nullable: true},
		{Name: "reference_date", Type: field.TypeString, Nullable: true},
		{Name: "document_status", Type: field.TypeEnum, Nullable: true, Enums: []string{"NOT_BOOKED", "BOOKED", "REJECTED"}},
		{Name: "rejection_reason", Type: field.TypeEnum, Nullable: true, Enums: []string{"Validation error", "Worktime exceeded", "Insufficient funds", "No active account", "Unable to process payment", "Action is forbidden"}},
	}
	// AbsTransactionDocumentsTable holds the schema information for the "abs_transaction_documents" table.
	AbsTransactionDocumentsTable = &schema.Table{
		Name:       "abs_transaction_documents",
		Columns:    AbsTransactionDocumentsColumns,
		PrimaryKey: []*schema.Column{AbsTransactionDocumentsColumns[0]},
	}
	// ConfirmationsColumns holds the columns for the "confirmations" table.
	ConfirmationsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "confirmation_type", Type: field.TypeEnum, Enums: []string{"OTP"}},
		{Name: "confirmation_date", Type: field.TypeTime},
	}
	// ConfirmationsTable holds the schema information for the "confirmations" table.
	ConfirmationsTable = &schema.Table{
		Name:       "confirmations",
		Columns:    ConfirmationsColumns,
		PrimaryKey: []*schema.Column{ConfirmationsColumns[0]},
	}
	// EmployeesColumns holds the columns for the "employees" table.
	EmployeesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "create_time", Type: field.TypeTime, Nullable: true},
		{Name: "update_time", Type: field.TypeTime, Nullable: true},
		{Name: "employer_bin_iin", Type: field.TypeString},
		{Name: "name", Type: field.TypeString},
		{Name: "middle_name", Type: field.TypeString, Nullable: true},
		{Name: "last_name", Type: field.TypeString},
		{Name: "birthdate", Type: field.TypeTime},
		{Name: "country", Type: field.TypeString},
		{Name: "employee_iin", Type: field.TypeString},
		{Name: "display_order", Type: field.TypeInt},
	}
	// EmployeesTable holds the schema information for the "employees" table.
	EmployeesTable = &schema.Table{
		Name:       "employees",
		Columns:    EmployeesColumns,
		PrimaryKey: []*schema.Column{EmployeesColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "employees_employer_bin_iin",
				Unique:  false,
				Columns: []*schema.Column{EmployeesColumns[3]},
			},
			{
				Name:    "employees_employee_iin_employer_bin_iin",
				Unique:  true,
				Columns: []*schema.Column{EmployeesColumns[9], EmployeesColumns[3]},
			},
		},
	}
	// GeneratedDocumentsColumns holds the columns for the "generated_documents" table.
	GeneratedDocumentsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "create_time", Type: field.TypeTime, Nullable: true},
		{Name: "update_time", Type: field.TypeTime, Nullable: true},
		{Name: "parent_transaction_id", Type: field.TypeUUID},
		{Name: "generated_document_integration_id", Type: field.TypeUUID},
		{Name: "generated_document_type", Type: field.TypeString},
	}
	// GeneratedDocumentsTable holds the schema information for the "generated_documents" table.
	GeneratedDocumentsTable = &schema.Table{
		Name:       "generated_documents",
		Columns:    GeneratedDocumentsColumns,
		PrimaryKey: []*schema.Column{GeneratedDocumentsColumns[0]},
	}
	// HealthsColumns holds the columns for the "healths" table.
	HealthsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
	}
	// HealthsTable holds the schema information for the "healths" table.
	HealthsTable = &schema.Table{
		Name:       "healths",
		Columns:    HealthsColumns,
		PrimaryKey: []*schema.Column{HealthsColumns[0]},
	}
	// PaymentsColumns holds the columns for the "payments" table.
	PaymentsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "create_time", Type: field.TypeTime, Nullable: true},
		{Name: "update_time", Type: field.TypeTime, Nullable: true},
		{Name: "payment_code", Type: field.TypeString},
		{Name: "payment_type", Type: field.TypeEnum, Nullable: true, Enums: []string{"C", "D", "M", "R", "S", "V", "E", "P"}},
		{Name: "payment_period", Type: field.TypeString},
		{Name: "kbk", Type: field.TypeString},
		{Name: "employee_list", Type: field.TypeJSON, Nullable: true},
	}
	// PaymentsTable holds the schema information for the "payments" table.
	PaymentsTable = &schema.Table{
		Name:       "payments",
		Columns:    PaymentsColumns,
		PrimaryKey: []*schema.Column{PaymentsColumns[0]},
	}
	// RejectionsColumns holds the columns for the "rejections" table.
	RejectionsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "create_time", Type: field.TypeTime, Nullable: true},
		{Name: "update_time", Type: field.TypeTime, Nullable: true},
		{Name: "parent_transaction_id", Type: field.TypeUUID},
		{Name: "rejection_source", Type: field.TypeEnum, Enums: []string{"AML", "Antifraud"}},
		{Name: "rejection_code", Type: field.TypeString},
		{Name: "rejection_score", Type: field.TypeString},
		{Name: "rejection_reason", Type: field.TypeString},
	}
	// RejectionsTable holds the schema information for the "rejections" table.
	RejectionsTable = &schema.Table{
		Name:       "rejections",
		Columns:    RejectionsColumns,
		PrimaryKey: []*schema.Column{RejectionsColumns[0]},
	}
	// SignatoriesColumns holds the columns for the "signatories" table.
	SignatoriesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "signatory_a", Type: field.TypeString, Nullable: true},
		{Name: "signatory_b", Type: field.TypeString, Nullable: true},
	}
	// SignatoriesTable holds the schema information for the "signatories" table.
	SignatoriesTable = &schema.Table{
		Name:       "signatories",
		Columns:    SignatoriesColumns,
		PrimaryKey: []*schema.Column{SignatoriesColumns[0]},
	}
	// TransactionsColumns holds the columns for the "transactions" table.
	TransactionsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "create_time", Type: field.TypeTime, Nullable: true},
		{Name: "update_time", Type: field.TypeTime, Nullable: true},
		{Name: "transaction_number", Type: field.TypeString, Unique: true},
		{Name: "transaction_date", Type: field.TypeTime},
		{Name: "transaction_type", Type: field.TypeEnum, Enums: []string{"TRANSFER", "PAYMENT", "INVOICE"}},
		{Name: "initiator_id", Type: field.TypeString},
		{Name: "idempotency_key", Type: field.TypeString, Unique: true},
		{Name: "value_date", Type: field.TypeString, Nullable: true},
		{Name: "transaction_status", Type: field.TypeEnum, Enums: []string{"INITIALIZED", "IN_PROGRESS", "COMPLETED", "REJECTED"}},
		{Name: "transaction_amount", Type: field.TypeString},
		{Name: "transaction_comission", Type: field.TypeString, Nullable: true},
		{Name: "transaction_currency", Type: field.TypeEnum, Enums: []string{"KZT"}},
		{Name: "transaction_total_amount", Type: field.TypeString},
		{Name: "transaction_direction", Type: field.TypeEnum, Nullable: true, Enums: []string{"OUTGOING", "INCOMING", "INTERNAL"}},
		{Name: "purpose_code", Type: field.TypeString, Nullable: true},
		{Name: "purpose_details", Type: field.TypeString, Nullable: true},
		{Name: "payer_kod", Type: field.TypeString, Nullable: true},
		{Name: "payer_bin_iin", Type: field.TypeString, Nullable: true},
		{Name: "payer_name", Type: field.TypeString, Nullable: true},
		{Name: "payer_type", Type: field.TypeEnum, Nullable: true, Enums: []string{"individual", "corporate"}},
		{Name: "payer_account_iban", Type: field.TypeString, Nullable: true},
		{Name: "payer_bank_bic", Type: field.TypeString, Nullable: true},
		{Name: "payer_bank_name", Type: field.TypeString, Nullable: true},
		{Name: "payer_iso_country_code", Type: field.TypeString, Nullable: true},
		{Name: "real_payer_name", Type: field.TypeString, Nullable: true},
		{Name: "real_payer_bin_iin", Type: field.TypeString, Nullable: true},
		{Name: "real_payer_iso_country_code", Type: field.TypeString, Nullable: true},
		{Name: "real_payer_type", Type: field.TypeEnum, Nullable: true, Enums: []string{"individual", "corporate"}},
		{Name: "beneficiary_kbe", Type: field.TypeString, Nullable: true},
		{Name: "beneficiary_bin_iin", Type: field.TypeString, Nullable: true},
		{Name: "beneficiary_name", Type: field.TypeString, Nullable: true},
		{Name: "beneficiary_type", Type: field.TypeEnum, Nullable: true, Enums: []string{"individual", "corporate"}},
		{Name: "beneficiary_account_iban", Type: field.TypeString, Nullable: true},
		{Name: "beneficiary_bank_bic", Type: field.TypeString, Nullable: true},
		{Name: "beneficiary_bank_name", Type: field.TypeString, Nullable: true},
		{Name: "beneficiary_iso_country_code", Type: field.TypeString, Nullable: true},
		{Name: "real_beneficiary_name", Type: field.TypeString, Nullable: true},
		{Name: "real_beneficiary_bin_iin", Type: field.TypeString, Nullable: true},
		{Name: "real_beneficiary_country_code", Type: field.TypeString, Nullable: true},
		{Name: "real_beneficiary_type", Type: field.TypeEnum, Nullable: true, Enums: []string{"individual", "corporate"}},
	}
	// TransactionsTable holds the schema information for the "transactions" table.
	TransactionsTable = &schema.Table{
		Name:       "transactions",
		Columns:    TransactionsColumns,
		PrimaryKey: []*schema.Column{TransactionsColumns[0]},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		AbsTransactionDocumentsTable,
		ConfirmationsTable,
		EmployeesTable,
		GeneratedDocumentsTable,
		HealthsTable,
		PaymentsTable,
		RejectionsTable,
		SignatoriesTable,
		TransactionsTable,
	}
)

func init() {
}
