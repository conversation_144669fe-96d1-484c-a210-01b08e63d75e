// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/confirmations"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/predicate"
)

// ConfirmationsUpdate is the builder for updating Confirmations entities.
type ConfirmationsUpdate struct {
	config
	hooks     []Hook
	mutation  *ConfirmationsMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the ConfirmationsUpdate builder.
func (_u *ConfirmationsUpdate) Where(ps ...predicate.Confirmations) *ConfirmationsUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetConfirmationType sets the "confirmation_type" field.
func (_u *ConfirmationsUpdate) SetConfirmationType(v confirmations.ConfirmationType) *ConfirmationsUpdate {
	_u.mutation.SetConfirmationType(v)
	return _u
}

// SetNillableConfirmationType sets the "confirmation_type" field if the given value is not nil.
func (_u *ConfirmationsUpdate) SetNillableConfirmationType(v *confirmations.ConfirmationType) *ConfirmationsUpdate {
	if v != nil {
		_u.SetConfirmationType(*v)
	}
	return _u
}

// SetConfirmationDate sets the "confirmation_date" field.
func (_u *ConfirmationsUpdate) SetConfirmationDate(v time.Time) *ConfirmationsUpdate {
	_u.mutation.SetConfirmationDate(v)
	return _u
}

// SetNillableConfirmationDate sets the "confirmation_date" field if the given value is not nil.
func (_u *ConfirmationsUpdate) SetNillableConfirmationDate(v *time.Time) *ConfirmationsUpdate {
	if v != nil {
		_u.SetConfirmationDate(*v)
	}
	return _u
}

// Mutation returns the ConfirmationsMutation object of the builder.
func (_u *ConfirmationsUpdate) Mutation() *ConfirmationsMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *ConfirmationsUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *ConfirmationsUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *ConfirmationsUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *ConfirmationsUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *ConfirmationsUpdate) check() error {
	if v, ok := _u.mutation.ConfirmationType(); ok {
		if err := confirmations.ConfirmationTypeValidator(v); err != nil {
			return &ValidationError{Name: "confirmation_type", err: fmt.Errorf(`ent: validator failed for field "Confirmations.confirmation_type": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *ConfirmationsUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *ConfirmationsUpdate {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *ConfirmationsUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(confirmations.Table, confirmations.Columns, sqlgraph.NewFieldSpec(confirmations.FieldID, field.TypeUUID))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.ConfirmationType(); ok {
		_spec.SetField(confirmations.FieldConfirmationType, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.ConfirmationDate(); ok {
		_spec.SetField(confirmations.FieldConfirmationDate, field.TypeTime, value)
	}
	_spec.AddModifiers(_u.modifiers...)
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{confirmations.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// ConfirmationsUpdateOne is the builder for updating a single Confirmations entity.
type ConfirmationsUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *ConfirmationsMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetConfirmationType sets the "confirmation_type" field.
func (_u *ConfirmationsUpdateOne) SetConfirmationType(v confirmations.ConfirmationType) *ConfirmationsUpdateOne {
	_u.mutation.SetConfirmationType(v)
	return _u
}

// SetNillableConfirmationType sets the "confirmation_type" field if the given value is not nil.
func (_u *ConfirmationsUpdateOne) SetNillableConfirmationType(v *confirmations.ConfirmationType) *ConfirmationsUpdateOne {
	if v != nil {
		_u.SetConfirmationType(*v)
	}
	return _u
}

// SetConfirmationDate sets the "confirmation_date" field.
func (_u *ConfirmationsUpdateOne) SetConfirmationDate(v time.Time) *ConfirmationsUpdateOne {
	_u.mutation.SetConfirmationDate(v)
	return _u
}

// SetNillableConfirmationDate sets the "confirmation_date" field if the given value is not nil.
func (_u *ConfirmationsUpdateOne) SetNillableConfirmationDate(v *time.Time) *ConfirmationsUpdateOne {
	if v != nil {
		_u.SetConfirmationDate(*v)
	}
	return _u
}

// Mutation returns the ConfirmationsMutation object of the builder.
func (_u *ConfirmationsUpdateOne) Mutation() *ConfirmationsMutation {
	return _u.mutation
}

// Where appends a list predicates to the ConfirmationsUpdate builder.
func (_u *ConfirmationsUpdateOne) Where(ps ...predicate.Confirmations) *ConfirmationsUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *ConfirmationsUpdateOne) Select(field string, fields ...string) *ConfirmationsUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated Confirmations entity.
func (_u *ConfirmationsUpdateOne) Save(ctx context.Context) (*Confirmations, error) {
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *ConfirmationsUpdateOne) SaveX(ctx context.Context) *Confirmations {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *ConfirmationsUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *ConfirmationsUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *ConfirmationsUpdateOne) check() error {
	if v, ok := _u.mutation.ConfirmationType(); ok {
		if err := confirmations.ConfirmationTypeValidator(v); err != nil {
			return &ValidationError{Name: "confirmation_type", err: fmt.Errorf(`ent: validator failed for field "Confirmations.confirmation_type": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *ConfirmationsUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *ConfirmationsUpdateOne {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *ConfirmationsUpdateOne) sqlSave(ctx context.Context) (_node *Confirmations, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(confirmations.Table, confirmations.Columns, sqlgraph.NewFieldSpec(confirmations.FieldID, field.TypeUUID))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Confirmations.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, confirmations.FieldID)
		for _, f := range fields {
			if !confirmations.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != confirmations.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.ConfirmationType(); ok {
		_spec.SetField(confirmations.FieldConfirmationType, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.ConfirmationDate(); ok {
		_spec.SetField(confirmations.FieldConfirmationDate, field.TypeTime, value)
	}
	_spec.AddModifiers(_u.modifiers...)
	_node = &Confirmations{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{confirmations.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
