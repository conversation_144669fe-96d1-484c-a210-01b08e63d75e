// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/abstransactiondocuments"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/predicate"
)

// AbsTransactionDocumentsUpdate is the builder for updating AbsTransactionDocuments entities.
type AbsTransactionDocumentsUpdate struct {
	config
	hooks     []Hook
	mutation  *AbsTransactionDocumentsMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the AbsTransactionDocumentsUpdate builder.
func (_u *AbsTransactionDocumentsUpdate) Where(ps ...predicate.AbsTransactionDocuments) *AbsTransactionDocumentsUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetReferenceID sets the "reference_id" field.
func (_u *AbsTransactionDocumentsUpdate) SetReferenceID(v string) *AbsTransactionDocumentsUpdate {
	_u.mutation.SetReferenceID(v)
	return _u
}

// SetNillableReferenceID sets the "reference_id" field if the given value is not nil.
func (_u *AbsTransactionDocumentsUpdate) SetNillableReferenceID(v *string) *AbsTransactionDocumentsUpdate {
	if v != nil {
		_u.SetReferenceID(*v)
	}
	return _u
}

// SetDocumentType sets the "document_type" field.
func (_u *AbsTransactionDocumentsUpdate) SetDocumentType(v string) *AbsTransactionDocumentsUpdate {
	_u.mutation.SetDocumentType(v)
	return _u
}

// SetNillableDocumentType sets the "document_type" field if the given value is not nil.
func (_u *AbsTransactionDocumentsUpdate) SetNillableDocumentType(v *string) *AbsTransactionDocumentsUpdate {
	if v != nil {
		_u.SetDocumentType(*v)
	}
	return _u
}

// ClearDocumentType clears the value of the "document_type" field.
func (_u *AbsTransactionDocumentsUpdate) ClearDocumentType() *AbsTransactionDocumentsUpdate {
	_u.mutation.ClearDocumentType()
	return _u
}

// SetReferenceDate sets the "reference_date" field.
func (_u *AbsTransactionDocumentsUpdate) SetReferenceDate(v string) *AbsTransactionDocumentsUpdate {
	_u.mutation.SetReferenceDate(v)
	return _u
}

// SetNillableReferenceDate sets the "reference_date" field if the given value is not nil.
func (_u *AbsTransactionDocumentsUpdate) SetNillableReferenceDate(v *string) *AbsTransactionDocumentsUpdate {
	if v != nil {
		_u.SetReferenceDate(*v)
	}
	return _u
}

// ClearReferenceDate clears the value of the "reference_date" field.
func (_u *AbsTransactionDocumentsUpdate) ClearReferenceDate() *AbsTransactionDocumentsUpdate {
	_u.mutation.ClearReferenceDate()
	return _u
}

// SetDocumentStatus sets the "document_status" field.
func (_u *AbsTransactionDocumentsUpdate) SetDocumentStatus(v abstransactiondocuments.DocumentStatus) *AbsTransactionDocumentsUpdate {
	_u.mutation.SetDocumentStatus(v)
	return _u
}

// SetNillableDocumentStatus sets the "document_status" field if the given value is not nil.
func (_u *AbsTransactionDocumentsUpdate) SetNillableDocumentStatus(v *abstransactiondocuments.DocumentStatus) *AbsTransactionDocumentsUpdate {
	if v != nil {
		_u.SetDocumentStatus(*v)
	}
	return _u
}

// ClearDocumentStatus clears the value of the "document_status" field.
func (_u *AbsTransactionDocumentsUpdate) ClearDocumentStatus() *AbsTransactionDocumentsUpdate {
	_u.mutation.ClearDocumentStatus()
	return _u
}

// SetRejectionReason sets the "rejection_reason" field.
func (_u *AbsTransactionDocumentsUpdate) SetRejectionReason(v abstransactiondocuments.RejectionReason) *AbsTransactionDocumentsUpdate {
	_u.mutation.SetRejectionReason(v)
	return _u
}

// SetNillableRejectionReason sets the "rejection_reason" field if the given value is not nil.
func (_u *AbsTransactionDocumentsUpdate) SetNillableRejectionReason(v *abstransactiondocuments.RejectionReason) *AbsTransactionDocumentsUpdate {
	if v != nil {
		_u.SetRejectionReason(*v)
	}
	return _u
}

// ClearRejectionReason clears the value of the "rejection_reason" field.
func (_u *AbsTransactionDocumentsUpdate) ClearRejectionReason() *AbsTransactionDocumentsUpdate {
	_u.mutation.ClearRejectionReason()
	return _u
}

// Mutation returns the AbsTransactionDocumentsMutation object of the builder.
func (_u *AbsTransactionDocumentsUpdate) Mutation() *AbsTransactionDocumentsMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *AbsTransactionDocumentsUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *AbsTransactionDocumentsUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *AbsTransactionDocumentsUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *AbsTransactionDocumentsUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *AbsTransactionDocumentsUpdate) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := abstransactiondocuments.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *AbsTransactionDocumentsUpdate) check() error {
	if v, ok := _u.mutation.DocumentStatus(); ok {
		if err := abstransactiondocuments.DocumentStatusValidator(v); err != nil {
			return &ValidationError{Name: "document_status", err: fmt.Errorf(`ent: validator failed for field "AbsTransactionDocuments.document_status": %w`, err)}
		}
	}
	if v, ok := _u.mutation.RejectionReason(); ok {
		if err := abstransactiondocuments.RejectionReasonValidator(v); err != nil {
			return &ValidationError{Name: "rejection_reason", err: fmt.Errorf(`ent: validator failed for field "AbsTransactionDocuments.rejection_reason": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *AbsTransactionDocumentsUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AbsTransactionDocumentsUpdate {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *AbsTransactionDocumentsUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(abstransactiondocuments.Table, abstransactiondocuments.Columns, sqlgraph.NewFieldSpec(abstransactiondocuments.FieldID, field.TypeUUID))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(abstransactiondocuments.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(abstransactiondocuments.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(abstransactiondocuments.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.ReferenceID(); ok {
		_spec.SetField(abstransactiondocuments.FieldReferenceID, field.TypeString, value)
	}
	if value, ok := _u.mutation.DocumentType(); ok {
		_spec.SetField(abstransactiondocuments.FieldDocumentType, field.TypeString, value)
	}
	if _u.mutation.DocumentTypeCleared() {
		_spec.ClearField(abstransactiondocuments.FieldDocumentType, field.TypeString)
	}
	if value, ok := _u.mutation.ReferenceDate(); ok {
		_spec.SetField(abstransactiondocuments.FieldReferenceDate, field.TypeString, value)
	}
	if _u.mutation.ReferenceDateCleared() {
		_spec.ClearField(abstransactiondocuments.FieldReferenceDate, field.TypeString)
	}
	if value, ok := _u.mutation.DocumentStatus(); ok {
		_spec.SetField(abstransactiondocuments.FieldDocumentStatus, field.TypeEnum, value)
	}
	if _u.mutation.DocumentStatusCleared() {
		_spec.ClearField(abstransactiondocuments.FieldDocumentStatus, field.TypeEnum)
	}
	if value, ok := _u.mutation.RejectionReason(); ok {
		_spec.SetField(abstransactiondocuments.FieldRejectionReason, field.TypeEnum, value)
	}
	if _u.mutation.RejectionReasonCleared() {
		_spec.ClearField(abstransactiondocuments.FieldRejectionReason, field.TypeEnum)
	}
	_spec.AddModifiers(_u.modifiers...)
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{abstransactiondocuments.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// AbsTransactionDocumentsUpdateOne is the builder for updating a single AbsTransactionDocuments entity.
type AbsTransactionDocumentsUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *AbsTransactionDocumentsMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetReferenceID sets the "reference_id" field.
func (_u *AbsTransactionDocumentsUpdateOne) SetReferenceID(v string) *AbsTransactionDocumentsUpdateOne {
	_u.mutation.SetReferenceID(v)
	return _u
}

// SetNillableReferenceID sets the "reference_id" field if the given value is not nil.
func (_u *AbsTransactionDocumentsUpdateOne) SetNillableReferenceID(v *string) *AbsTransactionDocumentsUpdateOne {
	if v != nil {
		_u.SetReferenceID(*v)
	}
	return _u
}

// SetDocumentType sets the "document_type" field.
func (_u *AbsTransactionDocumentsUpdateOne) SetDocumentType(v string) *AbsTransactionDocumentsUpdateOne {
	_u.mutation.SetDocumentType(v)
	return _u
}

// SetNillableDocumentType sets the "document_type" field if the given value is not nil.
func (_u *AbsTransactionDocumentsUpdateOne) SetNillableDocumentType(v *string) *AbsTransactionDocumentsUpdateOne {
	if v != nil {
		_u.SetDocumentType(*v)
	}
	return _u
}

// ClearDocumentType clears the value of the "document_type" field.
func (_u *AbsTransactionDocumentsUpdateOne) ClearDocumentType() *AbsTransactionDocumentsUpdateOne {
	_u.mutation.ClearDocumentType()
	return _u
}

// SetReferenceDate sets the "reference_date" field.
func (_u *AbsTransactionDocumentsUpdateOne) SetReferenceDate(v string) *AbsTransactionDocumentsUpdateOne {
	_u.mutation.SetReferenceDate(v)
	return _u
}

// SetNillableReferenceDate sets the "reference_date" field if the given value is not nil.
func (_u *AbsTransactionDocumentsUpdateOne) SetNillableReferenceDate(v *string) *AbsTransactionDocumentsUpdateOne {
	if v != nil {
		_u.SetReferenceDate(*v)
	}
	return _u
}

// ClearReferenceDate clears the value of the "reference_date" field.
func (_u *AbsTransactionDocumentsUpdateOne) ClearReferenceDate() *AbsTransactionDocumentsUpdateOne {
	_u.mutation.ClearReferenceDate()
	return _u
}

// SetDocumentStatus sets the "document_status" field.
func (_u *AbsTransactionDocumentsUpdateOne) SetDocumentStatus(v abstransactiondocuments.DocumentStatus) *AbsTransactionDocumentsUpdateOne {
	_u.mutation.SetDocumentStatus(v)
	return _u
}

// SetNillableDocumentStatus sets the "document_status" field if the given value is not nil.
func (_u *AbsTransactionDocumentsUpdateOne) SetNillableDocumentStatus(v *abstransactiondocuments.DocumentStatus) *AbsTransactionDocumentsUpdateOne {
	if v != nil {
		_u.SetDocumentStatus(*v)
	}
	return _u
}

// ClearDocumentStatus clears the value of the "document_status" field.
func (_u *AbsTransactionDocumentsUpdateOne) ClearDocumentStatus() *AbsTransactionDocumentsUpdateOne {
	_u.mutation.ClearDocumentStatus()
	return _u
}

// SetRejectionReason sets the "rejection_reason" field.
func (_u *AbsTransactionDocumentsUpdateOne) SetRejectionReason(v abstransactiondocuments.RejectionReason) *AbsTransactionDocumentsUpdateOne {
	_u.mutation.SetRejectionReason(v)
	return _u
}

// SetNillableRejectionReason sets the "rejection_reason" field if the given value is not nil.
func (_u *AbsTransactionDocumentsUpdateOne) SetNillableRejectionReason(v *abstransactiondocuments.RejectionReason) *AbsTransactionDocumentsUpdateOne {
	if v != nil {
		_u.SetRejectionReason(*v)
	}
	return _u
}

// ClearRejectionReason clears the value of the "rejection_reason" field.
func (_u *AbsTransactionDocumentsUpdateOne) ClearRejectionReason() *AbsTransactionDocumentsUpdateOne {
	_u.mutation.ClearRejectionReason()
	return _u
}

// Mutation returns the AbsTransactionDocumentsMutation object of the builder.
func (_u *AbsTransactionDocumentsUpdateOne) Mutation() *AbsTransactionDocumentsMutation {
	return _u.mutation
}

// Where appends a list predicates to the AbsTransactionDocumentsUpdate builder.
func (_u *AbsTransactionDocumentsUpdateOne) Where(ps ...predicate.AbsTransactionDocuments) *AbsTransactionDocumentsUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *AbsTransactionDocumentsUpdateOne) Select(field string, fields ...string) *AbsTransactionDocumentsUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated AbsTransactionDocuments entity.
func (_u *AbsTransactionDocumentsUpdateOne) Save(ctx context.Context) (*AbsTransactionDocuments, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *AbsTransactionDocumentsUpdateOne) SaveX(ctx context.Context) *AbsTransactionDocuments {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *AbsTransactionDocumentsUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *AbsTransactionDocumentsUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *AbsTransactionDocumentsUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := abstransactiondocuments.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *AbsTransactionDocumentsUpdateOne) check() error {
	if v, ok := _u.mutation.DocumentStatus(); ok {
		if err := abstransactiondocuments.DocumentStatusValidator(v); err != nil {
			return &ValidationError{Name: "document_status", err: fmt.Errorf(`ent: validator failed for field "AbsTransactionDocuments.document_status": %w`, err)}
		}
	}
	if v, ok := _u.mutation.RejectionReason(); ok {
		if err := abstransactiondocuments.RejectionReasonValidator(v); err != nil {
			return &ValidationError{Name: "rejection_reason", err: fmt.Errorf(`ent: validator failed for field "AbsTransactionDocuments.rejection_reason": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *AbsTransactionDocumentsUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AbsTransactionDocumentsUpdateOne {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *AbsTransactionDocumentsUpdateOne) sqlSave(ctx context.Context) (_node *AbsTransactionDocuments, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(abstransactiondocuments.Table, abstransactiondocuments.Columns, sqlgraph.NewFieldSpec(abstransactiondocuments.FieldID, field.TypeUUID))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "AbsTransactionDocuments.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, abstransactiondocuments.FieldID)
		for _, f := range fields {
			if !abstransactiondocuments.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != abstransactiondocuments.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(abstransactiondocuments.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(abstransactiondocuments.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(abstransactiondocuments.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.ReferenceID(); ok {
		_spec.SetField(abstransactiondocuments.FieldReferenceID, field.TypeString, value)
	}
	if value, ok := _u.mutation.DocumentType(); ok {
		_spec.SetField(abstransactiondocuments.FieldDocumentType, field.TypeString, value)
	}
	if _u.mutation.DocumentTypeCleared() {
		_spec.ClearField(abstransactiondocuments.FieldDocumentType, field.TypeString)
	}
	if value, ok := _u.mutation.ReferenceDate(); ok {
		_spec.SetField(abstransactiondocuments.FieldReferenceDate, field.TypeString, value)
	}
	if _u.mutation.ReferenceDateCleared() {
		_spec.ClearField(abstransactiondocuments.FieldReferenceDate, field.TypeString)
	}
	if value, ok := _u.mutation.DocumentStatus(); ok {
		_spec.SetField(abstransactiondocuments.FieldDocumentStatus, field.TypeEnum, value)
	}
	if _u.mutation.DocumentStatusCleared() {
		_spec.ClearField(abstransactiondocuments.FieldDocumentStatus, field.TypeEnum)
	}
	if value, ok := _u.mutation.RejectionReason(); ok {
		_spec.SetField(abstransactiondocuments.FieldRejectionReason, field.TypeEnum, value)
	}
	if _u.mutation.RejectionReasonCleared() {
		_spec.ClearField(abstransactiondocuments.FieldRejectionReason, field.TypeEnum)
	}
	_spec.AddModifiers(_u.modifiers...)
	_node = &AbsTransactionDocuments{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{abstransactiondocuments.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
