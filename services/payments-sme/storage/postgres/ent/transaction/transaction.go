// Code generated by ent, DO NOT EDIT.

package transaction

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the transaction type in the database.
	Label = "transaction"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreateTime holds the string denoting the create_time field in the database.
	FieldCreateTime = "create_time"
	// FieldUpdateTime holds the string denoting the update_time field in the database.
	FieldUpdateTime = "update_time"
	// FieldTransactionNumber holds the string denoting the transaction_number field in the database.
	FieldTransactionNumber = "transaction_number"
	// FieldTransactionDate holds the string denoting the transaction_date field in the database.
	FieldTransactionDate = "transaction_date"
	// FieldTransactionType holds the string denoting the transaction_type field in the database.
	FieldTransactionType = "transaction_type"
	// FieldInitiatorID holds the string denoting the initiator_id field in the database.
	FieldInitiatorID = "initiator_id"
	// FieldIdempotencyKey holds the string denoting the idempotency_key field in the database.
	FieldIdempotencyKey = "idempotency_key"
	// FieldValueDate holds the string denoting the value_date field in the database.
	FieldValueDate = "value_date"
	// FieldTransactionStatus holds the string denoting the transaction_status field in the database.
	FieldTransactionStatus = "transaction_status"
	// FieldTransactionAmount holds the string denoting the transaction_amount field in the database.
	FieldTransactionAmount = "transaction_amount"
	// FieldTransactionComission holds the string denoting the transaction_comission field in the database.
	FieldTransactionComission = "transaction_comission"
	// FieldTransactionCurrency holds the string denoting the transaction_currency field in the database.
	FieldTransactionCurrency = "transaction_currency"
	// FieldTransactionTotalAmount holds the string denoting the transaction_total_amount field in the database.
	FieldTransactionTotalAmount = "transaction_total_amount"
	// FieldTransactionDirection holds the string denoting the transaction_direction field in the database.
	FieldTransactionDirection = "transaction_direction"
	// FieldPurposeCode holds the string denoting the purpose_code field in the database.
	FieldPurposeCode = "purpose_code"
	// FieldPurposeDetails holds the string denoting the purpose_details field in the database.
	FieldPurposeDetails = "purpose_details"
	// FieldPayerKod holds the string denoting the payer_kod field in the database.
	FieldPayerKod = "payer_kod"
	// FieldPayerBinIin holds the string denoting the payer_bin_iin field in the database.
	FieldPayerBinIin = "payer_bin_iin"
	// FieldPayerName holds the string denoting the payer_name field in the database.
	FieldPayerName = "payer_name"
	// FieldPayerType holds the string denoting the payer_type field in the database.
	FieldPayerType = "payer_type"
	// FieldPayerAccountIban holds the string denoting the payer_account_iban field in the database.
	FieldPayerAccountIban = "payer_account_iban"
	// FieldPayerBankBic holds the string denoting the payer_bank_bic field in the database.
	FieldPayerBankBic = "payer_bank_bic"
	// FieldPayerBankName holds the string denoting the payer_bank_name field in the database.
	FieldPayerBankName = "payer_bank_name"
	// FieldPayerIsoCountryCode holds the string denoting the payer_iso_country_code field in the database.
	FieldPayerIsoCountryCode = "payer_iso_country_code"
	// FieldRealPayerName holds the string denoting the real_payer_name field in the database.
	FieldRealPayerName = "real_payer_name"
	// FieldRealPayerBinIin holds the string denoting the real_payer_bin_iin field in the database.
	FieldRealPayerBinIin = "real_payer_bin_iin"
	// FieldRealPayerIsoCountryCode holds the string denoting the real_payer_iso_country_code field in the database.
	FieldRealPayerIsoCountryCode = "real_payer_iso_country_code"
	// FieldRealPayerType holds the string denoting the real_payer_type field in the database.
	FieldRealPayerType = "real_payer_type"
	// FieldBeneficiaryKbe holds the string denoting the beneficiary_kbe field in the database.
	FieldBeneficiaryKbe = "beneficiary_kbe"
	// FieldBeneficiaryBinIin holds the string denoting the beneficiary_bin_iin field in the database.
	FieldBeneficiaryBinIin = "beneficiary_bin_iin"
	// FieldBeneficiaryName holds the string denoting the beneficiary_name field in the database.
	FieldBeneficiaryName = "beneficiary_name"
	// FieldBeneficiaryType holds the string denoting the beneficiary_type field in the database.
	FieldBeneficiaryType = "beneficiary_type"
	// FieldBeneficiaryAccountIban holds the string denoting the beneficiary_account_iban field in the database.
	FieldBeneficiaryAccountIban = "beneficiary_account_iban"
	// FieldBeneficiaryBankBic holds the string denoting the beneficiary_bank_bic field in the database.
	FieldBeneficiaryBankBic = "beneficiary_bank_bic"
	// FieldBeneficiaryBankName holds the string denoting the beneficiary_bank_name field in the database.
	FieldBeneficiaryBankName = "beneficiary_bank_name"
	// FieldBeneficiaryIsoCountryCode holds the string denoting the beneficiary_iso_country_code field in the database.
	FieldBeneficiaryIsoCountryCode = "beneficiary_iso_country_code"
	// FieldRealBeneficiaryName holds the string denoting the real_beneficiary_name field in the database.
	FieldRealBeneficiaryName = "real_beneficiary_name"
	// FieldRealBeneficiaryBinIin holds the string denoting the real_beneficiary_bin_iin field in the database.
	FieldRealBeneficiaryBinIin = "real_beneficiary_bin_iin"
	// FieldRealBeneficiaryCountryCode holds the string denoting the real_beneficiary_country_code field in the database.
	FieldRealBeneficiaryCountryCode = "real_beneficiary_country_code"
	// FieldRealBeneficiaryType holds the string denoting the real_beneficiary_type field in the database.
	FieldRealBeneficiaryType = "real_beneficiary_type"
	// Table holds the table name of the transaction in the database.
	Table = "transactions"
)

// Columns holds all SQL columns for transaction fields.
var Columns = []string{
	FieldID,
	FieldCreateTime,
	FieldUpdateTime,
	FieldTransactionNumber,
	FieldTransactionDate,
	FieldTransactionType,
	FieldInitiatorID,
	FieldIdempotencyKey,
	FieldValueDate,
	FieldTransactionStatus,
	FieldTransactionAmount,
	FieldTransactionComission,
	FieldTransactionCurrency,
	FieldTransactionTotalAmount,
	FieldTransactionDirection,
	FieldPurposeCode,
	FieldPurposeDetails,
	FieldPayerKod,
	FieldPayerBinIin,
	FieldPayerName,
	FieldPayerType,
	FieldPayerAccountIban,
	FieldPayerBankBic,
	FieldPayerBankName,
	FieldPayerIsoCountryCode,
	FieldRealPayerName,
	FieldRealPayerBinIin,
	FieldRealPayerIsoCountryCode,
	FieldRealPayerType,
	FieldBeneficiaryKbe,
	FieldBeneficiaryBinIin,
	FieldBeneficiaryName,
	FieldBeneficiaryType,
	FieldBeneficiaryAccountIban,
	FieldBeneficiaryBankBic,
	FieldBeneficiaryBankName,
	FieldBeneficiaryIsoCountryCode,
	FieldRealBeneficiaryName,
	FieldRealBeneficiaryBinIin,
	FieldRealBeneficiaryCountryCode,
	FieldRealBeneficiaryType,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreateTime holds the default value on creation for the "create_time" field.
	DefaultCreateTime func() time.Time
	// DefaultUpdateTime holds the default value on creation for the "update_time" field.
	DefaultUpdateTime func() time.Time
	// UpdateDefaultUpdateTime holds the default value on update for the "update_time" field.
	UpdateDefaultUpdateTime func() time.Time
	// DefaultTransactionDate holds the default value on creation for the "transaction_date" field.
	DefaultTransactionDate func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// TransactionType defines the type for the "transaction_type" enum field.
type TransactionType string

// TransactionType values.
const (
	TransactionTypeTRANSFER TransactionType = "TRANSFER"
	TransactionTypePAYMENT  TransactionType = "PAYMENT"
	TransactionTypeINVOICE  TransactionType = "INVOICE"
)

func (tt TransactionType) String() string {
	return string(tt)
}

// TransactionTypeValidator is a validator for the "transaction_type" field enum values. It is called by the builders before save.
func TransactionTypeValidator(tt TransactionType) error {
	switch tt {
	case TransactionTypeTRANSFER, TransactionTypePAYMENT, TransactionTypeINVOICE:
		return nil
	default:
		return fmt.Errorf("transaction: invalid enum value for transaction_type field: %q", tt)
	}
}

// TransactionStatus defines the type for the "transaction_status" enum field.
type TransactionStatus string

// TransactionStatus values.
const (
	TransactionStatusINITIALIZED TransactionStatus = "INITIALIZED"
	TransactionStatusIN_PROGRESS TransactionStatus = "IN_PROGRESS"
	TransactionStatusCOMPLETED   TransactionStatus = "COMPLETED"
	TransactionStatusREJECTED    TransactionStatus = "REJECTED"
)

func (ts TransactionStatus) String() string {
	return string(ts)
}

// TransactionStatusValidator is a validator for the "transaction_status" field enum values. It is called by the builders before save.
func TransactionStatusValidator(ts TransactionStatus) error {
	switch ts {
	case TransactionStatusINITIALIZED, TransactionStatusIN_PROGRESS, TransactionStatusCOMPLETED, TransactionStatusREJECTED:
		return nil
	default:
		return fmt.Errorf("transaction: invalid enum value for transaction_status field: %q", ts)
	}
}

// TransactionCurrency defines the type for the "transaction_currency" enum field.
type TransactionCurrency string

// TransactionCurrency values.
const (
	TransactionCurrencyKZT TransactionCurrency = "KZT"
)

func (tc TransactionCurrency) String() string {
	return string(tc)
}

// TransactionCurrencyValidator is a validator for the "transaction_currency" field enum values. It is called by the builders before save.
func TransactionCurrencyValidator(tc TransactionCurrency) error {
	switch tc {
	case TransactionCurrencyKZT:
		return nil
	default:
		return fmt.Errorf("transaction: invalid enum value for transaction_currency field: %q", tc)
	}
}

// TransactionDirection defines the type for the "transaction_direction" enum field.
type TransactionDirection string

// TransactionDirection values.
const (
	TransactionDirectionOUTGOING TransactionDirection = "OUTGOING"
	TransactionDirectionINCOMING TransactionDirection = "INCOMING"
	TransactionDirectionINTERNAL TransactionDirection = "INTERNAL"
)

func (td TransactionDirection) String() string {
	return string(td)
}

// TransactionDirectionValidator is a validator for the "transaction_direction" field enum values. It is called by the builders before save.
func TransactionDirectionValidator(td TransactionDirection) error {
	switch td {
	case TransactionDirectionOUTGOING, TransactionDirectionINCOMING, TransactionDirectionINTERNAL:
		return nil
	default:
		return fmt.Errorf("transaction: invalid enum value for transaction_direction field: %q", td)
	}
}

// PayerType defines the type for the "payer_type" enum field.
type PayerType string

// PayerType values.
const (
	PayerTypeIndividual PayerType = "individual"
	PayerTypeCorporate  PayerType = "corporate"
)

func (pt PayerType) String() string {
	return string(pt)
}

// PayerTypeValidator is a validator for the "payer_type" field enum values. It is called by the builders before save.
func PayerTypeValidator(pt PayerType) error {
	switch pt {
	case PayerTypeIndividual, PayerTypeCorporate:
		return nil
	default:
		return fmt.Errorf("transaction: invalid enum value for payer_type field: %q", pt)
	}
}

// RealPayerType defines the type for the "real_payer_type" enum field.
type RealPayerType string

// RealPayerType values.
const (
	RealPayerTypeIndividual RealPayerType = "individual"
	RealPayerTypeCorporate  RealPayerType = "corporate"
)

func (rpt RealPayerType) String() string {
	return string(rpt)
}

// RealPayerTypeValidator is a validator for the "real_payer_type" field enum values. It is called by the builders before save.
func RealPayerTypeValidator(rpt RealPayerType) error {
	switch rpt {
	case RealPayerTypeIndividual, RealPayerTypeCorporate:
		return nil
	default:
		return fmt.Errorf("transaction: invalid enum value for real_payer_type field: %q", rpt)
	}
}

// BeneficiaryType defines the type for the "beneficiary_type" enum field.
type BeneficiaryType string

// BeneficiaryType values.
const (
	BeneficiaryTypeIndividual BeneficiaryType = "individual"
	BeneficiaryTypeCorporate  BeneficiaryType = "corporate"
)

func (bt BeneficiaryType) String() string {
	return string(bt)
}

// BeneficiaryTypeValidator is a validator for the "beneficiary_type" field enum values. It is called by the builders before save.
func BeneficiaryTypeValidator(bt BeneficiaryType) error {
	switch bt {
	case BeneficiaryTypeIndividual, BeneficiaryTypeCorporate:
		return nil
	default:
		return fmt.Errorf("transaction: invalid enum value for beneficiary_type field: %q", bt)
	}
}

// RealBeneficiaryType defines the type for the "real_beneficiary_type" enum field.
type RealBeneficiaryType string

// RealBeneficiaryType values.
const (
	RealBeneficiaryTypeIndividual RealBeneficiaryType = "individual"
	RealBeneficiaryTypeCorporate  RealBeneficiaryType = "corporate"
)

func (rbt RealBeneficiaryType) String() string {
	return string(rbt)
}

// RealBeneficiaryTypeValidator is a validator for the "real_beneficiary_type" field enum values. It is called by the builders before save.
func RealBeneficiaryTypeValidator(rbt RealBeneficiaryType) error {
	switch rbt {
	case RealBeneficiaryTypeIndividual, RealBeneficiaryTypeCorporate:
		return nil
	default:
		return fmt.Errorf("transaction: invalid enum value for real_beneficiary_type field: %q", rbt)
	}
}

// OrderOption defines the ordering options for the Transaction queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreateTime orders the results by the create_time field.
func ByCreateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateTime, opts...).ToFunc()
}

// ByUpdateTime orders the results by the update_time field.
func ByUpdateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateTime, opts...).ToFunc()
}

// ByTransactionNumber orders the results by the transaction_number field.
func ByTransactionNumber(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTransactionNumber, opts...).ToFunc()
}

// ByTransactionDate orders the results by the transaction_date field.
func ByTransactionDate(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTransactionDate, opts...).ToFunc()
}

// ByTransactionType orders the results by the transaction_type field.
func ByTransactionType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTransactionType, opts...).ToFunc()
}

// ByInitiatorID orders the results by the initiator_id field.
func ByInitiatorID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldInitiatorID, opts...).ToFunc()
}

// ByIdempotencyKey orders the results by the idempotency_key field.
func ByIdempotencyKey(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIdempotencyKey, opts...).ToFunc()
}

// ByValueDate orders the results by the value_date field.
func ByValueDate(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldValueDate, opts...).ToFunc()
}

// ByTransactionStatus orders the results by the transaction_status field.
func ByTransactionStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTransactionStatus, opts...).ToFunc()
}

// ByTransactionAmount orders the results by the transaction_amount field.
func ByTransactionAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTransactionAmount, opts...).ToFunc()
}

// ByTransactionComission orders the results by the transaction_comission field.
func ByTransactionComission(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTransactionComission, opts...).ToFunc()
}

// ByTransactionCurrency orders the results by the transaction_currency field.
func ByTransactionCurrency(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTransactionCurrency, opts...).ToFunc()
}

// ByTransactionTotalAmount orders the results by the transaction_total_amount field.
func ByTransactionTotalAmount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTransactionTotalAmount, opts...).ToFunc()
}

// ByTransactionDirection orders the results by the transaction_direction field.
func ByTransactionDirection(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTransactionDirection, opts...).ToFunc()
}

// ByPurposeCode orders the results by the purpose_code field.
func ByPurposeCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPurposeCode, opts...).ToFunc()
}

// ByPurposeDetails orders the results by the purpose_details field.
func ByPurposeDetails(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPurposeDetails, opts...).ToFunc()
}

// ByPayerKod orders the results by the payer_kod field.
func ByPayerKod(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPayerKod, opts...).ToFunc()
}

// ByPayerBinIin orders the results by the payer_bin_iin field.
func ByPayerBinIin(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPayerBinIin, opts...).ToFunc()
}

// ByPayerName orders the results by the payer_name field.
func ByPayerName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPayerName, opts...).ToFunc()
}

// ByPayerType orders the results by the payer_type field.
func ByPayerType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPayerType, opts...).ToFunc()
}

// ByPayerAccountIban orders the results by the payer_account_iban field.
func ByPayerAccountIban(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPayerAccountIban, opts...).ToFunc()
}

// ByPayerBankBic orders the results by the payer_bank_bic field.
func ByPayerBankBic(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPayerBankBic, opts...).ToFunc()
}

// ByPayerBankName orders the results by the payer_bank_name field.
func ByPayerBankName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPayerBankName, opts...).ToFunc()
}

// ByPayerIsoCountryCode orders the results by the payer_iso_country_code field.
func ByPayerIsoCountryCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPayerIsoCountryCode, opts...).ToFunc()
}

// ByRealPayerName orders the results by the real_payer_name field.
func ByRealPayerName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRealPayerName, opts...).ToFunc()
}

// ByRealPayerBinIin orders the results by the real_payer_bin_iin field.
func ByRealPayerBinIin(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRealPayerBinIin, opts...).ToFunc()
}

// ByRealPayerIsoCountryCode orders the results by the real_payer_iso_country_code field.
func ByRealPayerIsoCountryCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRealPayerIsoCountryCode, opts...).ToFunc()
}

// ByRealPayerType orders the results by the real_payer_type field.
func ByRealPayerType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRealPayerType, opts...).ToFunc()
}

// ByBeneficiaryKbe orders the results by the beneficiary_kbe field.
func ByBeneficiaryKbe(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBeneficiaryKbe, opts...).ToFunc()
}

// ByBeneficiaryBinIin orders the results by the beneficiary_bin_iin field.
func ByBeneficiaryBinIin(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBeneficiaryBinIin, opts...).ToFunc()
}

// ByBeneficiaryName orders the results by the beneficiary_name field.
func ByBeneficiaryName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBeneficiaryName, opts...).ToFunc()
}

// ByBeneficiaryType orders the results by the beneficiary_type field.
func ByBeneficiaryType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBeneficiaryType, opts...).ToFunc()
}

// ByBeneficiaryAccountIban orders the results by the beneficiary_account_iban field.
func ByBeneficiaryAccountIban(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBeneficiaryAccountIban, opts...).ToFunc()
}

// ByBeneficiaryBankBic orders the results by the beneficiary_bank_bic field.
func ByBeneficiaryBankBic(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBeneficiaryBankBic, opts...).ToFunc()
}

// ByBeneficiaryBankName orders the results by the beneficiary_bank_name field.
func ByBeneficiaryBankName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBeneficiaryBankName, opts...).ToFunc()
}

// ByBeneficiaryIsoCountryCode orders the results by the beneficiary_iso_country_code field.
func ByBeneficiaryIsoCountryCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBeneficiaryIsoCountryCode, opts...).ToFunc()
}

// ByRealBeneficiaryName orders the results by the real_beneficiary_name field.
func ByRealBeneficiaryName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRealBeneficiaryName, opts...).ToFunc()
}

// ByRealBeneficiaryBinIin orders the results by the real_beneficiary_bin_iin field.
func ByRealBeneficiaryBinIin(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRealBeneficiaryBinIin, opts...).ToFunc()
}

// ByRealBeneficiaryCountryCode orders the results by the real_beneficiary_country_code field.
func ByRealBeneficiaryCountryCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRealBeneficiaryCountryCode, opts...).ToFunc()
}

// ByRealBeneficiaryType orders the results by the real_beneficiary_type field.
func ByRealBeneficiaryType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRealBeneficiaryType, opts...).ToFunc()
}
