// Code generated by ent, DO NOT EDIT.

package transaction

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldUpdateTime, v))
}

// TransactionNumber applies equality check predicate on the "transaction_number" field. It's identical to TransactionNumberEQ.
func TransactionNumber(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldTransactionNumber, v))
}

// TransactionDate applies equality check predicate on the "transaction_date" field. It's identical to TransactionDateEQ.
func TransactionDate(v time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldTransactionDate, v))
}

// InitiatorID applies equality check predicate on the "initiator_id" field. It's identical to InitiatorIDEQ.
func InitiatorID(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldInitiatorID, v))
}

// IdempotencyKey applies equality check predicate on the "idempotency_key" field. It's identical to IdempotencyKeyEQ.
func IdempotencyKey(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldIdempotencyKey, v))
}

// ValueDate applies equality check predicate on the "value_date" field. It's identical to ValueDateEQ.
func ValueDate(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldValueDate, v))
}

// TransactionAmount applies equality check predicate on the "transaction_amount" field. It's identical to TransactionAmountEQ.
func TransactionAmount(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldTransactionAmount, v))
}

// TransactionComission applies equality check predicate on the "transaction_comission" field. It's identical to TransactionComissionEQ.
func TransactionComission(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldTransactionComission, v))
}

// TransactionTotalAmount applies equality check predicate on the "transaction_total_amount" field. It's identical to TransactionTotalAmountEQ.
func TransactionTotalAmount(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldTransactionTotalAmount, v))
}

// PurposeCode applies equality check predicate on the "purpose_code" field. It's identical to PurposeCodeEQ.
func PurposeCode(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldPurposeCode, v))
}

// PurposeDetails applies equality check predicate on the "purpose_details" field. It's identical to PurposeDetailsEQ.
func PurposeDetails(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldPurposeDetails, v))
}

// PayerKod applies equality check predicate on the "payer_kod" field. It's identical to PayerKodEQ.
func PayerKod(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldPayerKod, v))
}

// PayerBinIin applies equality check predicate on the "payer_bin_iin" field. It's identical to PayerBinIinEQ.
func PayerBinIin(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldPayerBinIin, v))
}

// PayerName applies equality check predicate on the "payer_name" field. It's identical to PayerNameEQ.
func PayerName(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldPayerName, v))
}

// PayerAccountIban applies equality check predicate on the "payer_account_iban" field. It's identical to PayerAccountIbanEQ.
func PayerAccountIban(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldPayerAccountIban, v))
}

// PayerBankBic applies equality check predicate on the "payer_bank_bic" field. It's identical to PayerBankBicEQ.
func PayerBankBic(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldPayerBankBic, v))
}

// PayerBankName applies equality check predicate on the "payer_bank_name" field. It's identical to PayerBankNameEQ.
func PayerBankName(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldPayerBankName, v))
}

// PayerIsoCountryCode applies equality check predicate on the "payer_iso_country_code" field. It's identical to PayerIsoCountryCodeEQ.
func PayerIsoCountryCode(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldPayerIsoCountryCode, v))
}

// RealPayerName applies equality check predicate on the "real_payer_name" field. It's identical to RealPayerNameEQ.
func RealPayerName(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldRealPayerName, v))
}

// RealPayerBinIin applies equality check predicate on the "real_payer_bin_iin" field. It's identical to RealPayerBinIinEQ.
func RealPayerBinIin(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldRealPayerBinIin, v))
}

// RealPayerIsoCountryCode applies equality check predicate on the "real_payer_iso_country_code" field. It's identical to RealPayerIsoCountryCodeEQ.
func RealPayerIsoCountryCode(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldRealPayerIsoCountryCode, v))
}

// BeneficiaryKbe applies equality check predicate on the "beneficiary_kbe" field. It's identical to BeneficiaryKbeEQ.
func BeneficiaryKbe(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldBeneficiaryKbe, v))
}

// BeneficiaryBinIin applies equality check predicate on the "beneficiary_bin_iin" field. It's identical to BeneficiaryBinIinEQ.
func BeneficiaryBinIin(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldBeneficiaryBinIin, v))
}

// BeneficiaryName applies equality check predicate on the "beneficiary_name" field. It's identical to BeneficiaryNameEQ.
func BeneficiaryName(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldBeneficiaryName, v))
}

// BeneficiaryAccountIban applies equality check predicate on the "beneficiary_account_iban" field. It's identical to BeneficiaryAccountIbanEQ.
func BeneficiaryAccountIban(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldBeneficiaryAccountIban, v))
}

// BeneficiaryBankBic applies equality check predicate on the "beneficiary_bank_bic" field. It's identical to BeneficiaryBankBicEQ.
func BeneficiaryBankBic(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldBeneficiaryBankBic, v))
}

// BeneficiaryBankName applies equality check predicate on the "beneficiary_bank_name" field. It's identical to BeneficiaryBankNameEQ.
func BeneficiaryBankName(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldBeneficiaryBankName, v))
}

// BeneficiaryIsoCountryCode applies equality check predicate on the "beneficiary_iso_country_code" field. It's identical to BeneficiaryIsoCountryCodeEQ.
func BeneficiaryIsoCountryCode(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldBeneficiaryIsoCountryCode, v))
}

// RealBeneficiaryName applies equality check predicate on the "real_beneficiary_name" field. It's identical to RealBeneficiaryNameEQ.
func RealBeneficiaryName(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldRealBeneficiaryName, v))
}

// RealBeneficiaryBinIin applies equality check predicate on the "real_beneficiary_bin_iin" field. It's identical to RealBeneficiaryBinIinEQ.
func RealBeneficiaryBinIin(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldRealBeneficiaryBinIin, v))
}

// RealBeneficiaryCountryCode applies equality check predicate on the "real_beneficiary_country_code" field. It's identical to RealBeneficiaryCountryCodeEQ.
func RealBeneficiaryCountryCode(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldRealBeneficiaryCountryCode, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldCreateTime, v))
}

// CreateTimeIsNil applies the IsNil predicate on the "create_time" field.
func CreateTimeIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldCreateTime))
}

// CreateTimeNotNil applies the NotNil predicate on the "create_time" field.
func CreateTimeNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldCreateTime))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldUpdateTime, v))
}

// UpdateTimeIsNil applies the IsNil predicate on the "update_time" field.
func UpdateTimeIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldUpdateTime))
}

// UpdateTimeNotNil applies the NotNil predicate on the "update_time" field.
func UpdateTimeNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldUpdateTime))
}

// TransactionNumberEQ applies the EQ predicate on the "transaction_number" field.
func TransactionNumberEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldTransactionNumber, v))
}

// TransactionNumberNEQ applies the NEQ predicate on the "transaction_number" field.
func TransactionNumberNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldTransactionNumber, v))
}

// TransactionNumberIn applies the In predicate on the "transaction_number" field.
func TransactionNumberIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldTransactionNumber, vs...))
}

// TransactionNumberNotIn applies the NotIn predicate on the "transaction_number" field.
func TransactionNumberNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldTransactionNumber, vs...))
}

// TransactionNumberGT applies the GT predicate on the "transaction_number" field.
func TransactionNumberGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldTransactionNumber, v))
}

// TransactionNumberGTE applies the GTE predicate on the "transaction_number" field.
func TransactionNumberGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldTransactionNumber, v))
}

// TransactionNumberLT applies the LT predicate on the "transaction_number" field.
func TransactionNumberLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldTransactionNumber, v))
}

// TransactionNumberLTE applies the LTE predicate on the "transaction_number" field.
func TransactionNumberLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldTransactionNumber, v))
}

// TransactionNumberContains applies the Contains predicate on the "transaction_number" field.
func TransactionNumberContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldTransactionNumber, v))
}

// TransactionNumberHasPrefix applies the HasPrefix predicate on the "transaction_number" field.
func TransactionNumberHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldTransactionNumber, v))
}

// TransactionNumberHasSuffix applies the HasSuffix predicate on the "transaction_number" field.
func TransactionNumberHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldTransactionNumber, v))
}

// TransactionNumberEqualFold applies the EqualFold predicate on the "transaction_number" field.
func TransactionNumberEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldTransactionNumber, v))
}

// TransactionNumberContainsFold applies the ContainsFold predicate on the "transaction_number" field.
func TransactionNumberContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldTransactionNumber, v))
}

// TransactionDateEQ applies the EQ predicate on the "transaction_date" field.
func TransactionDateEQ(v time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldTransactionDate, v))
}

// TransactionDateNEQ applies the NEQ predicate on the "transaction_date" field.
func TransactionDateNEQ(v time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldTransactionDate, v))
}

// TransactionDateIn applies the In predicate on the "transaction_date" field.
func TransactionDateIn(vs ...time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldTransactionDate, vs...))
}

// TransactionDateNotIn applies the NotIn predicate on the "transaction_date" field.
func TransactionDateNotIn(vs ...time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldTransactionDate, vs...))
}

// TransactionDateGT applies the GT predicate on the "transaction_date" field.
func TransactionDateGT(v time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldTransactionDate, v))
}

// TransactionDateGTE applies the GTE predicate on the "transaction_date" field.
func TransactionDateGTE(v time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldTransactionDate, v))
}

// TransactionDateLT applies the LT predicate on the "transaction_date" field.
func TransactionDateLT(v time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldTransactionDate, v))
}

// TransactionDateLTE applies the LTE predicate on the "transaction_date" field.
func TransactionDateLTE(v time.Time) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldTransactionDate, v))
}

// TransactionTypeEQ applies the EQ predicate on the "transaction_type" field.
func TransactionTypeEQ(v TransactionType) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldTransactionType, v))
}

// TransactionTypeNEQ applies the NEQ predicate on the "transaction_type" field.
func TransactionTypeNEQ(v TransactionType) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldTransactionType, v))
}

// TransactionTypeIn applies the In predicate on the "transaction_type" field.
func TransactionTypeIn(vs ...TransactionType) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldTransactionType, vs...))
}

// TransactionTypeNotIn applies the NotIn predicate on the "transaction_type" field.
func TransactionTypeNotIn(vs ...TransactionType) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldTransactionType, vs...))
}

// InitiatorIDEQ applies the EQ predicate on the "initiator_id" field.
func InitiatorIDEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldInitiatorID, v))
}

// InitiatorIDNEQ applies the NEQ predicate on the "initiator_id" field.
func InitiatorIDNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldInitiatorID, v))
}

// InitiatorIDIn applies the In predicate on the "initiator_id" field.
func InitiatorIDIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldInitiatorID, vs...))
}

// InitiatorIDNotIn applies the NotIn predicate on the "initiator_id" field.
func InitiatorIDNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldInitiatorID, vs...))
}

// InitiatorIDGT applies the GT predicate on the "initiator_id" field.
func InitiatorIDGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldInitiatorID, v))
}

// InitiatorIDGTE applies the GTE predicate on the "initiator_id" field.
func InitiatorIDGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldInitiatorID, v))
}

// InitiatorIDLT applies the LT predicate on the "initiator_id" field.
func InitiatorIDLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldInitiatorID, v))
}

// InitiatorIDLTE applies the LTE predicate on the "initiator_id" field.
func InitiatorIDLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldInitiatorID, v))
}

// InitiatorIDContains applies the Contains predicate on the "initiator_id" field.
func InitiatorIDContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldInitiatorID, v))
}

// InitiatorIDHasPrefix applies the HasPrefix predicate on the "initiator_id" field.
func InitiatorIDHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldInitiatorID, v))
}

// InitiatorIDHasSuffix applies the HasSuffix predicate on the "initiator_id" field.
func InitiatorIDHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldInitiatorID, v))
}

// InitiatorIDEqualFold applies the EqualFold predicate on the "initiator_id" field.
func InitiatorIDEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldInitiatorID, v))
}

// InitiatorIDContainsFold applies the ContainsFold predicate on the "initiator_id" field.
func InitiatorIDContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldInitiatorID, v))
}

// IdempotencyKeyEQ applies the EQ predicate on the "idempotency_key" field.
func IdempotencyKeyEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldIdempotencyKey, v))
}

// IdempotencyKeyNEQ applies the NEQ predicate on the "idempotency_key" field.
func IdempotencyKeyNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldIdempotencyKey, v))
}

// IdempotencyKeyIn applies the In predicate on the "idempotency_key" field.
func IdempotencyKeyIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldIdempotencyKey, vs...))
}

// IdempotencyKeyNotIn applies the NotIn predicate on the "idempotency_key" field.
func IdempotencyKeyNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldIdempotencyKey, vs...))
}

// IdempotencyKeyGT applies the GT predicate on the "idempotency_key" field.
func IdempotencyKeyGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldIdempotencyKey, v))
}

// IdempotencyKeyGTE applies the GTE predicate on the "idempotency_key" field.
func IdempotencyKeyGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldIdempotencyKey, v))
}

// IdempotencyKeyLT applies the LT predicate on the "idempotency_key" field.
func IdempotencyKeyLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldIdempotencyKey, v))
}

// IdempotencyKeyLTE applies the LTE predicate on the "idempotency_key" field.
func IdempotencyKeyLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldIdempotencyKey, v))
}

// IdempotencyKeyContains applies the Contains predicate on the "idempotency_key" field.
func IdempotencyKeyContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldIdempotencyKey, v))
}

// IdempotencyKeyHasPrefix applies the HasPrefix predicate on the "idempotency_key" field.
func IdempotencyKeyHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldIdempotencyKey, v))
}

// IdempotencyKeyHasSuffix applies the HasSuffix predicate on the "idempotency_key" field.
func IdempotencyKeyHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldIdempotencyKey, v))
}

// IdempotencyKeyEqualFold applies the EqualFold predicate on the "idempotency_key" field.
func IdempotencyKeyEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldIdempotencyKey, v))
}

// IdempotencyKeyContainsFold applies the ContainsFold predicate on the "idempotency_key" field.
func IdempotencyKeyContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldIdempotencyKey, v))
}

// ValueDateEQ applies the EQ predicate on the "value_date" field.
func ValueDateEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldValueDate, v))
}

// ValueDateNEQ applies the NEQ predicate on the "value_date" field.
func ValueDateNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldValueDate, v))
}

// ValueDateIn applies the In predicate on the "value_date" field.
func ValueDateIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldValueDate, vs...))
}

// ValueDateNotIn applies the NotIn predicate on the "value_date" field.
func ValueDateNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldValueDate, vs...))
}

// ValueDateGT applies the GT predicate on the "value_date" field.
func ValueDateGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldValueDate, v))
}

// ValueDateGTE applies the GTE predicate on the "value_date" field.
func ValueDateGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldValueDate, v))
}

// ValueDateLT applies the LT predicate on the "value_date" field.
func ValueDateLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldValueDate, v))
}

// ValueDateLTE applies the LTE predicate on the "value_date" field.
func ValueDateLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldValueDate, v))
}

// ValueDateContains applies the Contains predicate on the "value_date" field.
func ValueDateContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldValueDate, v))
}

// ValueDateHasPrefix applies the HasPrefix predicate on the "value_date" field.
func ValueDateHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldValueDate, v))
}

// ValueDateHasSuffix applies the HasSuffix predicate on the "value_date" field.
func ValueDateHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldValueDate, v))
}

// ValueDateIsNil applies the IsNil predicate on the "value_date" field.
func ValueDateIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldValueDate))
}

// ValueDateNotNil applies the NotNil predicate on the "value_date" field.
func ValueDateNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldValueDate))
}

// ValueDateEqualFold applies the EqualFold predicate on the "value_date" field.
func ValueDateEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldValueDate, v))
}

// ValueDateContainsFold applies the ContainsFold predicate on the "value_date" field.
func ValueDateContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldValueDate, v))
}

// TransactionStatusEQ applies the EQ predicate on the "transaction_status" field.
func TransactionStatusEQ(v TransactionStatus) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldTransactionStatus, v))
}

// TransactionStatusNEQ applies the NEQ predicate on the "transaction_status" field.
func TransactionStatusNEQ(v TransactionStatus) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldTransactionStatus, v))
}

// TransactionStatusIn applies the In predicate on the "transaction_status" field.
func TransactionStatusIn(vs ...TransactionStatus) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldTransactionStatus, vs...))
}

// TransactionStatusNotIn applies the NotIn predicate on the "transaction_status" field.
func TransactionStatusNotIn(vs ...TransactionStatus) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldTransactionStatus, vs...))
}

// TransactionAmountEQ applies the EQ predicate on the "transaction_amount" field.
func TransactionAmountEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldTransactionAmount, v))
}

// TransactionAmountNEQ applies the NEQ predicate on the "transaction_amount" field.
func TransactionAmountNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldTransactionAmount, v))
}

// TransactionAmountIn applies the In predicate on the "transaction_amount" field.
func TransactionAmountIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldTransactionAmount, vs...))
}

// TransactionAmountNotIn applies the NotIn predicate on the "transaction_amount" field.
func TransactionAmountNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldTransactionAmount, vs...))
}

// TransactionAmountGT applies the GT predicate on the "transaction_amount" field.
func TransactionAmountGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldTransactionAmount, v))
}

// TransactionAmountGTE applies the GTE predicate on the "transaction_amount" field.
func TransactionAmountGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldTransactionAmount, v))
}

// TransactionAmountLT applies the LT predicate on the "transaction_amount" field.
func TransactionAmountLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldTransactionAmount, v))
}

// TransactionAmountLTE applies the LTE predicate on the "transaction_amount" field.
func TransactionAmountLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldTransactionAmount, v))
}

// TransactionAmountContains applies the Contains predicate on the "transaction_amount" field.
func TransactionAmountContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldTransactionAmount, v))
}

// TransactionAmountHasPrefix applies the HasPrefix predicate on the "transaction_amount" field.
func TransactionAmountHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldTransactionAmount, v))
}

// TransactionAmountHasSuffix applies the HasSuffix predicate on the "transaction_amount" field.
func TransactionAmountHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldTransactionAmount, v))
}

// TransactionAmountEqualFold applies the EqualFold predicate on the "transaction_amount" field.
func TransactionAmountEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldTransactionAmount, v))
}

// TransactionAmountContainsFold applies the ContainsFold predicate on the "transaction_amount" field.
func TransactionAmountContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldTransactionAmount, v))
}

// TransactionComissionEQ applies the EQ predicate on the "transaction_comission" field.
func TransactionComissionEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldTransactionComission, v))
}

// TransactionComissionNEQ applies the NEQ predicate on the "transaction_comission" field.
func TransactionComissionNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldTransactionComission, v))
}

// TransactionComissionIn applies the In predicate on the "transaction_comission" field.
func TransactionComissionIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldTransactionComission, vs...))
}

// TransactionComissionNotIn applies the NotIn predicate on the "transaction_comission" field.
func TransactionComissionNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldTransactionComission, vs...))
}

// TransactionComissionGT applies the GT predicate on the "transaction_comission" field.
func TransactionComissionGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldTransactionComission, v))
}

// TransactionComissionGTE applies the GTE predicate on the "transaction_comission" field.
func TransactionComissionGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldTransactionComission, v))
}

// TransactionComissionLT applies the LT predicate on the "transaction_comission" field.
func TransactionComissionLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldTransactionComission, v))
}

// TransactionComissionLTE applies the LTE predicate on the "transaction_comission" field.
func TransactionComissionLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldTransactionComission, v))
}

// TransactionComissionContains applies the Contains predicate on the "transaction_comission" field.
func TransactionComissionContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldTransactionComission, v))
}

// TransactionComissionHasPrefix applies the HasPrefix predicate on the "transaction_comission" field.
func TransactionComissionHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldTransactionComission, v))
}

// TransactionComissionHasSuffix applies the HasSuffix predicate on the "transaction_comission" field.
func TransactionComissionHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldTransactionComission, v))
}

// TransactionComissionIsNil applies the IsNil predicate on the "transaction_comission" field.
func TransactionComissionIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldTransactionComission))
}

// TransactionComissionNotNil applies the NotNil predicate on the "transaction_comission" field.
func TransactionComissionNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldTransactionComission))
}

// TransactionComissionEqualFold applies the EqualFold predicate on the "transaction_comission" field.
func TransactionComissionEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldTransactionComission, v))
}

// TransactionComissionContainsFold applies the ContainsFold predicate on the "transaction_comission" field.
func TransactionComissionContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldTransactionComission, v))
}

// TransactionCurrencyEQ applies the EQ predicate on the "transaction_currency" field.
func TransactionCurrencyEQ(v TransactionCurrency) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldTransactionCurrency, v))
}

// TransactionCurrencyNEQ applies the NEQ predicate on the "transaction_currency" field.
func TransactionCurrencyNEQ(v TransactionCurrency) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldTransactionCurrency, v))
}

// TransactionCurrencyIn applies the In predicate on the "transaction_currency" field.
func TransactionCurrencyIn(vs ...TransactionCurrency) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldTransactionCurrency, vs...))
}

// TransactionCurrencyNotIn applies the NotIn predicate on the "transaction_currency" field.
func TransactionCurrencyNotIn(vs ...TransactionCurrency) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldTransactionCurrency, vs...))
}

// TransactionTotalAmountEQ applies the EQ predicate on the "transaction_total_amount" field.
func TransactionTotalAmountEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldTransactionTotalAmount, v))
}

// TransactionTotalAmountNEQ applies the NEQ predicate on the "transaction_total_amount" field.
func TransactionTotalAmountNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldTransactionTotalAmount, v))
}

// TransactionTotalAmountIn applies the In predicate on the "transaction_total_amount" field.
func TransactionTotalAmountIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldTransactionTotalAmount, vs...))
}

// TransactionTotalAmountNotIn applies the NotIn predicate on the "transaction_total_amount" field.
func TransactionTotalAmountNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldTransactionTotalAmount, vs...))
}

// TransactionTotalAmountGT applies the GT predicate on the "transaction_total_amount" field.
func TransactionTotalAmountGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldTransactionTotalAmount, v))
}

// TransactionTotalAmountGTE applies the GTE predicate on the "transaction_total_amount" field.
func TransactionTotalAmountGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldTransactionTotalAmount, v))
}

// TransactionTotalAmountLT applies the LT predicate on the "transaction_total_amount" field.
func TransactionTotalAmountLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldTransactionTotalAmount, v))
}

// TransactionTotalAmountLTE applies the LTE predicate on the "transaction_total_amount" field.
func TransactionTotalAmountLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldTransactionTotalAmount, v))
}

// TransactionTotalAmountContains applies the Contains predicate on the "transaction_total_amount" field.
func TransactionTotalAmountContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldTransactionTotalAmount, v))
}

// TransactionTotalAmountHasPrefix applies the HasPrefix predicate on the "transaction_total_amount" field.
func TransactionTotalAmountHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldTransactionTotalAmount, v))
}

// TransactionTotalAmountHasSuffix applies the HasSuffix predicate on the "transaction_total_amount" field.
func TransactionTotalAmountHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldTransactionTotalAmount, v))
}

// TransactionTotalAmountEqualFold applies the EqualFold predicate on the "transaction_total_amount" field.
func TransactionTotalAmountEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldTransactionTotalAmount, v))
}

// TransactionTotalAmountContainsFold applies the ContainsFold predicate on the "transaction_total_amount" field.
func TransactionTotalAmountContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldTransactionTotalAmount, v))
}

// TransactionDirectionEQ applies the EQ predicate on the "transaction_direction" field.
func TransactionDirectionEQ(v TransactionDirection) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldTransactionDirection, v))
}

// TransactionDirectionNEQ applies the NEQ predicate on the "transaction_direction" field.
func TransactionDirectionNEQ(v TransactionDirection) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldTransactionDirection, v))
}

// TransactionDirectionIn applies the In predicate on the "transaction_direction" field.
func TransactionDirectionIn(vs ...TransactionDirection) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldTransactionDirection, vs...))
}

// TransactionDirectionNotIn applies the NotIn predicate on the "transaction_direction" field.
func TransactionDirectionNotIn(vs ...TransactionDirection) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldTransactionDirection, vs...))
}

// TransactionDirectionIsNil applies the IsNil predicate on the "transaction_direction" field.
func TransactionDirectionIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldTransactionDirection))
}

// TransactionDirectionNotNil applies the NotNil predicate on the "transaction_direction" field.
func TransactionDirectionNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldTransactionDirection))
}

// PurposeCodeEQ applies the EQ predicate on the "purpose_code" field.
func PurposeCodeEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldPurposeCode, v))
}

// PurposeCodeNEQ applies the NEQ predicate on the "purpose_code" field.
func PurposeCodeNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldPurposeCode, v))
}

// PurposeCodeIn applies the In predicate on the "purpose_code" field.
func PurposeCodeIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldPurposeCode, vs...))
}

// PurposeCodeNotIn applies the NotIn predicate on the "purpose_code" field.
func PurposeCodeNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldPurposeCode, vs...))
}

// PurposeCodeGT applies the GT predicate on the "purpose_code" field.
func PurposeCodeGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldPurposeCode, v))
}

// PurposeCodeGTE applies the GTE predicate on the "purpose_code" field.
func PurposeCodeGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldPurposeCode, v))
}

// PurposeCodeLT applies the LT predicate on the "purpose_code" field.
func PurposeCodeLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldPurposeCode, v))
}

// PurposeCodeLTE applies the LTE predicate on the "purpose_code" field.
func PurposeCodeLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldPurposeCode, v))
}

// PurposeCodeContains applies the Contains predicate on the "purpose_code" field.
func PurposeCodeContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldPurposeCode, v))
}

// PurposeCodeHasPrefix applies the HasPrefix predicate on the "purpose_code" field.
func PurposeCodeHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldPurposeCode, v))
}

// PurposeCodeHasSuffix applies the HasSuffix predicate on the "purpose_code" field.
func PurposeCodeHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldPurposeCode, v))
}

// PurposeCodeIsNil applies the IsNil predicate on the "purpose_code" field.
func PurposeCodeIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldPurposeCode))
}

// PurposeCodeNotNil applies the NotNil predicate on the "purpose_code" field.
func PurposeCodeNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldPurposeCode))
}

// PurposeCodeEqualFold applies the EqualFold predicate on the "purpose_code" field.
func PurposeCodeEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldPurposeCode, v))
}

// PurposeCodeContainsFold applies the ContainsFold predicate on the "purpose_code" field.
func PurposeCodeContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldPurposeCode, v))
}

// PurposeDetailsEQ applies the EQ predicate on the "purpose_details" field.
func PurposeDetailsEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldPurposeDetails, v))
}

// PurposeDetailsNEQ applies the NEQ predicate on the "purpose_details" field.
func PurposeDetailsNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldPurposeDetails, v))
}

// PurposeDetailsIn applies the In predicate on the "purpose_details" field.
func PurposeDetailsIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldPurposeDetails, vs...))
}

// PurposeDetailsNotIn applies the NotIn predicate on the "purpose_details" field.
func PurposeDetailsNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldPurposeDetails, vs...))
}

// PurposeDetailsGT applies the GT predicate on the "purpose_details" field.
func PurposeDetailsGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldPurposeDetails, v))
}

// PurposeDetailsGTE applies the GTE predicate on the "purpose_details" field.
func PurposeDetailsGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldPurposeDetails, v))
}

// PurposeDetailsLT applies the LT predicate on the "purpose_details" field.
func PurposeDetailsLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldPurposeDetails, v))
}

// PurposeDetailsLTE applies the LTE predicate on the "purpose_details" field.
func PurposeDetailsLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldPurposeDetails, v))
}

// PurposeDetailsContains applies the Contains predicate on the "purpose_details" field.
func PurposeDetailsContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldPurposeDetails, v))
}

// PurposeDetailsHasPrefix applies the HasPrefix predicate on the "purpose_details" field.
func PurposeDetailsHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldPurposeDetails, v))
}

// PurposeDetailsHasSuffix applies the HasSuffix predicate on the "purpose_details" field.
func PurposeDetailsHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldPurposeDetails, v))
}

// PurposeDetailsIsNil applies the IsNil predicate on the "purpose_details" field.
func PurposeDetailsIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldPurposeDetails))
}

// PurposeDetailsNotNil applies the NotNil predicate on the "purpose_details" field.
func PurposeDetailsNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldPurposeDetails))
}

// PurposeDetailsEqualFold applies the EqualFold predicate on the "purpose_details" field.
func PurposeDetailsEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldPurposeDetails, v))
}

// PurposeDetailsContainsFold applies the ContainsFold predicate on the "purpose_details" field.
func PurposeDetailsContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldPurposeDetails, v))
}

// PayerKodEQ applies the EQ predicate on the "payer_kod" field.
func PayerKodEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldPayerKod, v))
}

// PayerKodNEQ applies the NEQ predicate on the "payer_kod" field.
func PayerKodNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldPayerKod, v))
}

// PayerKodIn applies the In predicate on the "payer_kod" field.
func PayerKodIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldPayerKod, vs...))
}

// PayerKodNotIn applies the NotIn predicate on the "payer_kod" field.
func PayerKodNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldPayerKod, vs...))
}

// PayerKodGT applies the GT predicate on the "payer_kod" field.
func PayerKodGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldPayerKod, v))
}

// PayerKodGTE applies the GTE predicate on the "payer_kod" field.
func PayerKodGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldPayerKod, v))
}

// PayerKodLT applies the LT predicate on the "payer_kod" field.
func PayerKodLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldPayerKod, v))
}

// PayerKodLTE applies the LTE predicate on the "payer_kod" field.
func PayerKodLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldPayerKod, v))
}

// PayerKodContains applies the Contains predicate on the "payer_kod" field.
func PayerKodContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldPayerKod, v))
}

// PayerKodHasPrefix applies the HasPrefix predicate on the "payer_kod" field.
func PayerKodHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldPayerKod, v))
}

// PayerKodHasSuffix applies the HasSuffix predicate on the "payer_kod" field.
func PayerKodHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldPayerKod, v))
}

// PayerKodIsNil applies the IsNil predicate on the "payer_kod" field.
func PayerKodIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldPayerKod))
}

// PayerKodNotNil applies the NotNil predicate on the "payer_kod" field.
func PayerKodNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldPayerKod))
}

// PayerKodEqualFold applies the EqualFold predicate on the "payer_kod" field.
func PayerKodEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldPayerKod, v))
}

// PayerKodContainsFold applies the ContainsFold predicate on the "payer_kod" field.
func PayerKodContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldPayerKod, v))
}

// PayerBinIinEQ applies the EQ predicate on the "payer_bin_iin" field.
func PayerBinIinEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldPayerBinIin, v))
}

// PayerBinIinNEQ applies the NEQ predicate on the "payer_bin_iin" field.
func PayerBinIinNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldPayerBinIin, v))
}

// PayerBinIinIn applies the In predicate on the "payer_bin_iin" field.
func PayerBinIinIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldPayerBinIin, vs...))
}

// PayerBinIinNotIn applies the NotIn predicate on the "payer_bin_iin" field.
func PayerBinIinNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldPayerBinIin, vs...))
}

// PayerBinIinGT applies the GT predicate on the "payer_bin_iin" field.
func PayerBinIinGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldPayerBinIin, v))
}

// PayerBinIinGTE applies the GTE predicate on the "payer_bin_iin" field.
func PayerBinIinGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldPayerBinIin, v))
}

// PayerBinIinLT applies the LT predicate on the "payer_bin_iin" field.
func PayerBinIinLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldPayerBinIin, v))
}

// PayerBinIinLTE applies the LTE predicate on the "payer_bin_iin" field.
func PayerBinIinLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldPayerBinIin, v))
}

// PayerBinIinContains applies the Contains predicate on the "payer_bin_iin" field.
func PayerBinIinContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldPayerBinIin, v))
}

// PayerBinIinHasPrefix applies the HasPrefix predicate on the "payer_bin_iin" field.
func PayerBinIinHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldPayerBinIin, v))
}

// PayerBinIinHasSuffix applies the HasSuffix predicate on the "payer_bin_iin" field.
func PayerBinIinHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldPayerBinIin, v))
}

// PayerBinIinIsNil applies the IsNil predicate on the "payer_bin_iin" field.
func PayerBinIinIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldPayerBinIin))
}

// PayerBinIinNotNil applies the NotNil predicate on the "payer_bin_iin" field.
func PayerBinIinNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldPayerBinIin))
}

// PayerBinIinEqualFold applies the EqualFold predicate on the "payer_bin_iin" field.
func PayerBinIinEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldPayerBinIin, v))
}

// PayerBinIinContainsFold applies the ContainsFold predicate on the "payer_bin_iin" field.
func PayerBinIinContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldPayerBinIin, v))
}

// PayerNameEQ applies the EQ predicate on the "payer_name" field.
func PayerNameEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldPayerName, v))
}

// PayerNameNEQ applies the NEQ predicate on the "payer_name" field.
func PayerNameNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldPayerName, v))
}

// PayerNameIn applies the In predicate on the "payer_name" field.
func PayerNameIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldPayerName, vs...))
}

// PayerNameNotIn applies the NotIn predicate on the "payer_name" field.
func PayerNameNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldPayerName, vs...))
}

// PayerNameGT applies the GT predicate on the "payer_name" field.
func PayerNameGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldPayerName, v))
}

// PayerNameGTE applies the GTE predicate on the "payer_name" field.
func PayerNameGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldPayerName, v))
}

// PayerNameLT applies the LT predicate on the "payer_name" field.
func PayerNameLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldPayerName, v))
}

// PayerNameLTE applies the LTE predicate on the "payer_name" field.
func PayerNameLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldPayerName, v))
}

// PayerNameContains applies the Contains predicate on the "payer_name" field.
func PayerNameContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldPayerName, v))
}

// PayerNameHasPrefix applies the HasPrefix predicate on the "payer_name" field.
func PayerNameHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldPayerName, v))
}

// PayerNameHasSuffix applies the HasSuffix predicate on the "payer_name" field.
func PayerNameHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldPayerName, v))
}

// PayerNameIsNil applies the IsNil predicate on the "payer_name" field.
func PayerNameIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldPayerName))
}

// PayerNameNotNil applies the NotNil predicate on the "payer_name" field.
func PayerNameNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldPayerName))
}

// PayerNameEqualFold applies the EqualFold predicate on the "payer_name" field.
func PayerNameEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldPayerName, v))
}

// PayerNameContainsFold applies the ContainsFold predicate on the "payer_name" field.
func PayerNameContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldPayerName, v))
}

// PayerTypeEQ applies the EQ predicate on the "payer_type" field.
func PayerTypeEQ(v PayerType) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldPayerType, v))
}

// PayerTypeNEQ applies the NEQ predicate on the "payer_type" field.
func PayerTypeNEQ(v PayerType) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldPayerType, v))
}

// PayerTypeIn applies the In predicate on the "payer_type" field.
func PayerTypeIn(vs ...PayerType) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldPayerType, vs...))
}

// PayerTypeNotIn applies the NotIn predicate on the "payer_type" field.
func PayerTypeNotIn(vs ...PayerType) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldPayerType, vs...))
}

// PayerTypeIsNil applies the IsNil predicate on the "payer_type" field.
func PayerTypeIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldPayerType))
}

// PayerTypeNotNil applies the NotNil predicate on the "payer_type" field.
func PayerTypeNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldPayerType))
}

// PayerAccountIbanEQ applies the EQ predicate on the "payer_account_iban" field.
func PayerAccountIbanEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldPayerAccountIban, v))
}

// PayerAccountIbanNEQ applies the NEQ predicate on the "payer_account_iban" field.
func PayerAccountIbanNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldPayerAccountIban, v))
}

// PayerAccountIbanIn applies the In predicate on the "payer_account_iban" field.
func PayerAccountIbanIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldPayerAccountIban, vs...))
}

// PayerAccountIbanNotIn applies the NotIn predicate on the "payer_account_iban" field.
func PayerAccountIbanNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldPayerAccountIban, vs...))
}

// PayerAccountIbanGT applies the GT predicate on the "payer_account_iban" field.
func PayerAccountIbanGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldPayerAccountIban, v))
}

// PayerAccountIbanGTE applies the GTE predicate on the "payer_account_iban" field.
func PayerAccountIbanGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldPayerAccountIban, v))
}

// PayerAccountIbanLT applies the LT predicate on the "payer_account_iban" field.
func PayerAccountIbanLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldPayerAccountIban, v))
}

// PayerAccountIbanLTE applies the LTE predicate on the "payer_account_iban" field.
func PayerAccountIbanLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldPayerAccountIban, v))
}

// PayerAccountIbanContains applies the Contains predicate on the "payer_account_iban" field.
func PayerAccountIbanContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldPayerAccountIban, v))
}

// PayerAccountIbanHasPrefix applies the HasPrefix predicate on the "payer_account_iban" field.
func PayerAccountIbanHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldPayerAccountIban, v))
}

// PayerAccountIbanHasSuffix applies the HasSuffix predicate on the "payer_account_iban" field.
func PayerAccountIbanHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldPayerAccountIban, v))
}

// PayerAccountIbanIsNil applies the IsNil predicate on the "payer_account_iban" field.
func PayerAccountIbanIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldPayerAccountIban))
}

// PayerAccountIbanNotNil applies the NotNil predicate on the "payer_account_iban" field.
func PayerAccountIbanNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldPayerAccountIban))
}

// PayerAccountIbanEqualFold applies the EqualFold predicate on the "payer_account_iban" field.
func PayerAccountIbanEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldPayerAccountIban, v))
}

// PayerAccountIbanContainsFold applies the ContainsFold predicate on the "payer_account_iban" field.
func PayerAccountIbanContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldPayerAccountIban, v))
}

// PayerBankBicEQ applies the EQ predicate on the "payer_bank_bic" field.
func PayerBankBicEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldPayerBankBic, v))
}

// PayerBankBicNEQ applies the NEQ predicate on the "payer_bank_bic" field.
func PayerBankBicNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldPayerBankBic, v))
}

// PayerBankBicIn applies the In predicate on the "payer_bank_bic" field.
func PayerBankBicIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldPayerBankBic, vs...))
}

// PayerBankBicNotIn applies the NotIn predicate on the "payer_bank_bic" field.
func PayerBankBicNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldPayerBankBic, vs...))
}

// PayerBankBicGT applies the GT predicate on the "payer_bank_bic" field.
func PayerBankBicGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldPayerBankBic, v))
}

// PayerBankBicGTE applies the GTE predicate on the "payer_bank_bic" field.
func PayerBankBicGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldPayerBankBic, v))
}

// PayerBankBicLT applies the LT predicate on the "payer_bank_bic" field.
func PayerBankBicLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldPayerBankBic, v))
}

// PayerBankBicLTE applies the LTE predicate on the "payer_bank_bic" field.
func PayerBankBicLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldPayerBankBic, v))
}

// PayerBankBicContains applies the Contains predicate on the "payer_bank_bic" field.
func PayerBankBicContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldPayerBankBic, v))
}

// PayerBankBicHasPrefix applies the HasPrefix predicate on the "payer_bank_bic" field.
func PayerBankBicHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldPayerBankBic, v))
}

// PayerBankBicHasSuffix applies the HasSuffix predicate on the "payer_bank_bic" field.
func PayerBankBicHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldPayerBankBic, v))
}

// PayerBankBicIsNil applies the IsNil predicate on the "payer_bank_bic" field.
func PayerBankBicIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldPayerBankBic))
}

// PayerBankBicNotNil applies the NotNil predicate on the "payer_bank_bic" field.
func PayerBankBicNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldPayerBankBic))
}

// PayerBankBicEqualFold applies the EqualFold predicate on the "payer_bank_bic" field.
func PayerBankBicEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldPayerBankBic, v))
}

// PayerBankBicContainsFold applies the ContainsFold predicate on the "payer_bank_bic" field.
func PayerBankBicContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldPayerBankBic, v))
}

// PayerBankNameEQ applies the EQ predicate on the "payer_bank_name" field.
func PayerBankNameEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldPayerBankName, v))
}

// PayerBankNameNEQ applies the NEQ predicate on the "payer_bank_name" field.
func PayerBankNameNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldPayerBankName, v))
}

// PayerBankNameIn applies the In predicate on the "payer_bank_name" field.
func PayerBankNameIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldPayerBankName, vs...))
}

// PayerBankNameNotIn applies the NotIn predicate on the "payer_bank_name" field.
func PayerBankNameNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldPayerBankName, vs...))
}

// PayerBankNameGT applies the GT predicate on the "payer_bank_name" field.
func PayerBankNameGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldPayerBankName, v))
}

// PayerBankNameGTE applies the GTE predicate on the "payer_bank_name" field.
func PayerBankNameGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldPayerBankName, v))
}

// PayerBankNameLT applies the LT predicate on the "payer_bank_name" field.
func PayerBankNameLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldPayerBankName, v))
}

// PayerBankNameLTE applies the LTE predicate on the "payer_bank_name" field.
func PayerBankNameLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldPayerBankName, v))
}

// PayerBankNameContains applies the Contains predicate on the "payer_bank_name" field.
func PayerBankNameContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldPayerBankName, v))
}

// PayerBankNameHasPrefix applies the HasPrefix predicate on the "payer_bank_name" field.
func PayerBankNameHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldPayerBankName, v))
}

// PayerBankNameHasSuffix applies the HasSuffix predicate on the "payer_bank_name" field.
func PayerBankNameHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldPayerBankName, v))
}

// PayerBankNameIsNil applies the IsNil predicate on the "payer_bank_name" field.
func PayerBankNameIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldPayerBankName))
}

// PayerBankNameNotNil applies the NotNil predicate on the "payer_bank_name" field.
func PayerBankNameNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldPayerBankName))
}

// PayerBankNameEqualFold applies the EqualFold predicate on the "payer_bank_name" field.
func PayerBankNameEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldPayerBankName, v))
}

// PayerBankNameContainsFold applies the ContainsFold predicate on the "payer_bank_name" field.
func PayerBankNameContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldPayerBankName, v))
}

// PayerIsoCountryCodeEQ applies the EQ predicate on the "payer_iso_country_code" field.
func PayerIsoCountryCodeEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldPayerIsoCountryCode, v))
}

// PayerIsoCountryCodeNEQ applies the NEQ predicate on the "payer_iso_country_code" field.
func PayerIsoCountryCodeNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldPayerIsoCountryCode, v))
}

// PayerIsoCountryCodeIn applies the In predicate on the "payer_iso_country_code" field.
func PayerIsoCountryCodeIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldPayerIsoCountryCode, vs...))
}

// PayerIsoCountryCodeNotIn applies the NotIn predicate on the "payer_iso_country_code" field.
func PayerIsoCountryCodeNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldPayerIsoCountryCode, vs...))
}

// PayerIsoCountryCodeGT applies the GT predicate on the "payer_iso_country_code" field.
func PayerIsoCountryCodeGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldPayerIsoCountryCode, v))
}

// PayerIsoCountryCodeGTE applies the GTE predicate on the "payer_iso_country_code" field.
func PayerIsoCountryCodeGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldPayerIsoCountryCode, v))
}

// PayerIsoCountryCodeLT applies the LT predicate on the "payer_iso_country_code" field.
func PayerIsoCountryCodeLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldPayerIsoCountryCode, v))
}

// PayerIsoCountryCodeLTE applies the LTE predicate on the "payer_iso_country_code" field.
func PayerIsoCountryCodeLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldPayerIsoCountryCode, v))
}

// PayerIsoCountryCodeContains applies the Contains predicate on the "payer_iso_country_code" field.
func PayerIsoCountryCodeContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldPayerIsoCountryCode, v))
}

// PayerIsoCountryCodeHasPrefix applies the HasPrefix predicate on the "payer_iso_country_code" field.
func PayerIsoCountryCodeHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldPayerIsoCountryCode, v))
}

// PayerIsoCountryCodeHasSuffix applies the HasSuffix predicate on the "payer_iso_country_code" field.
func PayerIsoCountryCodeHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldPayerIsoCountryCode, v))
}

// PayerIsoCountryCodeIsNil applies the IsNil predicate on the "payer_iso_country_code" field.
func PayerIsoCountryCodeIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldPayerIsoCountryCode))
}

// PayerIsoCountryCodeNotNil applies the NotNil predicate on the "payer_iso_country_code" field.
func PayerIsoCountryCodeNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldPayerIsoCountryCode))
}

// PayerIsoCountryCodeEqualFold applies the EqualFold predicate on the "payer_iso_country_code" field.
func PayerIsoCountryCodeEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldPayerIsoCountryCode, v))
}

// PayerIsoCountryCodeContainsFold applies the ContainsFold predicate on the "payer_iso_country_code" field.
func PayerIsoCountryCodeContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldPayerIsoCountryCode, v))
}

// RealPayerNameEQ applies the EQ predicate on the "real_payer_name" field.
func RealPayerNameEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldRealPayerName, v))
}

// RealPayerNameNEQ applies the NEQ predicate on the "real_payer_name" field.
func RealPayerNameNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldRealPayerName, v))
}

// RealPayerNameIn applies the In predicate on the "real_payer_name" field.
func RealPayerNameIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldRealPayerName, vs...))
}

// RealPayerNameNotIn applies the NotIn predicate on the "real_payer_name" field.
func RealPayerNameNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldRealPayerName, vs...))
}

// RealPayerNameGT applies the GT predicate on the "real_payer_name" field.
func RealPayerNameGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldRealPayerName, v))
}

// RealPayerNameGTE applies the GTE predicate on the "real_payer_name" field.
func RealPayerNameGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldRealPayerName, v))
}

// RealPayerNameLT applies the LT predicate on the "real_payer_name" field.
func RealPayerNameLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldRealPayerName, v))
}

// RealPayerNameLTE applies the LTE predicate on the "real_payer_name" field.
func RealPayerNameLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldRealPayerName, v))
}

// RealPayerNameContains applies the Contains predicate on the "real_payer_name" field.
func RealPayerNameContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldRealPayerName, v))
}

// RealPayerNameHasPrefix applies the HasPrefix predicate on the "real_payer_name" field.
func RealPayerNameHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldRealPayerName, v))
}

// RealPayerNameHasSuffix applies the HasSuffix predicate on the "real_payer_name" field.
func RealPayerNameHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldRealPayerName, v))
}

// RealPayerNameIsNil applies the IsNil predicate on the "real_payer_name" field.
func RealPayerNameIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldRealPayerName))
}

// RealPayerNameNotNil applies the NotNil predicate on the "real_payer_name" field.
func RealPayerNameNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldRealPayerName))
}

// RealPayerNameEqualFold applies the EqualFold predicate on the "real_payer_name" field.
func RealPayerNameEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldRealPayerName, v))
}

// RealPayerNameContainsFold applies the ContainsFold predicate on the "real_payer_name" field.
func RealPayerNameContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldRealPayerName, v))
}

// RealPayerBinIinEQ applies the EQ predicate on the "real_payer_bin_iin" field.
func RealPayerBinIinEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldRealPayerBinIin, v))
}

// RealPayerBinIinNEQ applies the NEQ predicate on the "real_payer_bin_iin" field.
func RealPayerBinIinNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldRealPayerBinIin, v))
}

// RealPayerBinIinIn applies the In predicate on the "real_payer_bin_iin" field.
func RealPayerBinIinIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldRealPayerBinIin, vs...))
}

// RealPayerBinIinNotIn applies the NotIn predicate on the "real_payer_bin_iin" field.
func RealPayerBinIinNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldRealPayerBinIin, vs...))
}

// RealPayerBinIinGT applies the GT predicate on the "real_payer_bin_iin" field.
func RealPayerBinIinGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldRealPayerBinIin, v))
}

// RealPayerBinIinGTE applies the GTE predicate on the "real_payer_bin_iin" field.
func RealPayerBinIinGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldRealPayerBinIin, v))
}

// RealPayerBinIinLT applies the LT predicate on the "real_payer_bin_iin" field.
func RealPayerBinIinLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldRealPayerBinIin, v))
}

// RealPayerBinIinLTE applies the LTE predicate on the "real_payer_bin_iin" field.
func RealPayerBinIinLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldRealPayerBinIin, v))
}

// RealPayerBinIinContains applies the Contains predicate on the "real_payer_bin_iin" field.
func RealPayerBinIinContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldRealPayerBinIin, v))
}

// RealPayerBinIinHasPrefix applies the HasPrefix predicate on the "real_payer_bin_iin" field.
func RealPayerBinIinHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldRealPayerBinIin, v))
}

// RealPayerBinIinHasSuffix applies the HasSuffix predicate on the "real_payer_bin_iin" field.
func RealPayerBinIinHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldRealPayerBinIin, v))
}

// RealPayerBinIinIsNil applies the IsNil predicate on the "real_payer_bin_iin" field.
func RealPayerBinIinIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldRealPayerBinIin))
}

// RealPayerBinIinNotNil applies the NotNil predicate on the "real_payer_bin_iin" field.
func RealPayerBinIinNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldRealPayerBinIin))
}

// RealPayerBinIinEqualFold applies the EqualFold predicate on the "real_payer_bin_iin" field.
func RealPayerBinIinEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldRealPayerBinIin, v))
}

// RealPayerBinIinContainsFold applies the ContainsFold predicate on the "real_payer_bin_iin" field.
func RealPayerBinIinContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldRealPayerBinIin, v))
}

// RealPayerIsoCountryCodeEQ applies the EQ predicate on the "real_payer_iso_country_code" field.
func RealPayerIsoCountryCodeEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldRealPayerIsoCountryCode, v))
}

// RealPayerIsoCountryCodeNEQ applies the NEQ predicate on the "real_payer_iso_country_code" field.
func RealPayerIsoCountryCodeNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldRealPayerIsoCountryCode, v))
}

// RealPayerIsoCountryCodeIn applies the In predicate on the "real_payer_iso_country_code" field.
func RealPayerIsoCountryCodeIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldRealPayerIsoCountryCode, vs...))
}

// RealPayerIsoCountryCodeNotIn applies the NotIn predicate on the "real_payer_iso_country_code" field.
func RealPayerIsoCountryCodeNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldRealPayerIsoCountryCode, vs...))
}

// RealPayerIsoCountryCodeGT applies the GT predicate on the "real_payer_iso_country_code" field.
func RealPayerIsoCountryCodeGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldRealPayerIsoCountryCode, v))
}

// RealPayerIsoCountryCodeGTE applies the GTE predicate on the "real_payer_iso_country_code" field.
func RealPayerIsoCountryCodeGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldRealPayerIsoCountryCode, v))
}

// RealPayerIsoCountryCodeLT applies the LT predicate on the "real_payer_iso_country_code" field.
func RealPayerIsoCountryCodeLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldRealPayerIsoCountryCode, v))
}

// RealPayerIsoCountryCodeLTE applies the LTE predicate on the "real_payer_iso_country_code" field.
func RealPayerIsoCountryCodeLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldRealPayerIsoCountryCode, v))
}

// RealPayerIsoCountryCodeContains applies the Contains predicate on the "real_payer_iso_country_code" field.
func RealPayerIsoCountryCodeContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldRealPayerIsoCountryCode, v))
}

// RealPayerIsoCountryCodeHasPrefix applies the HasPrefix predicate on the "real_payer_iso_country_code" field.
func RealPayerIsoCountryCodeHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldRealPayerIsoCountryCode, v))
}

// RealPayerIsoCountryCodeHasSuffix applies the HasSuffix predicate on the "real_payer_iso_country_code" field.
func RealPayerIsoCountryCodeHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldRealPayerIsoCountryCode, v))
}

// RealPayerIsoCountryCodeIsNil applies the IsNil predicate on the "real_payer_iso_country_code" field.
func RealPayerIsoCountryCodeIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldRealPayerIsoCountryCode))
}

// RealPayerIsoCountryCodeNotNil applies the NotNil predicate on the "real_payer_iso_country_code" field.
func RealPayerIsoCountryCodeNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldRealPayerIsoCountryCode))
}

// RealPayerIsoCountryCodeEqualFold applies the EqualFold predicate on the "real_payer_iso_country_code" field.
func RealPayerIsoCountryCodeEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldRealPayerIsoCountryCode, v))
}

// RealPayerIsoCountryCodeContainsFold applies the ContainsFold predicate on the "real_payer_iso_country_code" field.
func RealPayerIsoCountryCodeContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldRealPayerIsoCountryCode, v))
}

// RealPayerTypeEQ applies the EQ predicate on the "real_payer_type" field.
func RealPayerTypeEQ(v RealPayerType) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldRealPayerType, v))
}

// RealPayerTypeNEQ applies the NEQ predicate on the "real_payer_type" field.
func RealPayerTypeNEQ(v RealPayerType) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldRealPayerType, v))
}

// RealPayerTypeIn applies the In predicate on the "real_payer_type" field.
func RealPayerTypeIn(vs ...RealPayerType) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldRealPayerType, vs...))
}

// RealPayerTypeNotIn applies the NotIn predicate on the "real_payer_type" field.
func RealPayerTypeNotIn(vs ...RealPayerType) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldRealPayerType, vs...))
}

// RealPayerTypeIsNil applies the IsNil predicate on the "real_payer_type" field.
func RealPayerTypeIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldRealPayerType))
}

// RealPayerTypeNotNil applies the NotNil predicate on the "real_payer_type" field.
func RealPayerTypeNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldRealPayerType))
}

// BeneficiaryKbeEQ applies the EQ predicate on the "beneficiary_kbe" field.
func BeneficiaryKbeEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldBeneficiaryKbe, v))
}

// BeneficiaryKbeNEQ applies the NEQ predicate on the "beneficiary_kbe" field.
func BeneficiaryKbeNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldBeneficiaryKbe, v))
}

// BeneficiaryKbeIn applies the In predicate on the "beneficiary_kbe" field.
func BeneficiaryKbeIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldBeneficiaryKbe, vs...))
}

// BeneficiaryKbeNotIn applies the NotIn predicate on the "beneficiary_kbe" field.
func BeneficiaryKbeNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldBeneficiaryKbe, vs...))
}

// BeneficiaryKbeGT applies the GT predicate on the "beneficiary_kbe" field.
func BeneficiaryKbeGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldBeneficiaryKbe, v))
}

// BeneficiaryKbeGTE applies the GTE predicate on the "beneficiary_kbe" field.
func BeneficiaryKbeGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldBeneficiaryKbe, v))
}

// BeneficiaryKbeLT applies the LT predicate on the "beneficiary_kbe" field.
func BeneficiaryKbeLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldBeneficiaryKbe, v))
}

// BeneficiaryKbeLTE applies the LTE predicate on the "beneficiary_kbe" field.
func BeneficiaryKbeLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldBeneficiaryKbe, v))
}

// BeneficiaryKbeContains applies the Contains predicate on the "beneficiary_kbe" field.
func BeneficiaryKbeContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldBeneficiaryKbe, v))
}

// BeneficiaryKbeHasPrefix applies the HasPrefix predicate on the "beneficiary_kbe" field.
func BeneficiaryKbeHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldBeneficiaryKbe, v))
}

// BeneficiaryKbeHasSuffix applies the HasSuffix predicate on the "beneficiary_kbe" field.
func BeneficiaryKbeHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldBeneficiaryKbe, v))
}

// BeneficiaryKbeIsNil applies the IsNil predicate on the "beneficiary_kbe" field.
func BeneficiaryKbeIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldBeneficiaryKbe))
}

// BeneficiaryKbeNotNil applies the NotNil predicate on the "beneficiary_kbe" field.
func BeneficiaryKbeNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldBeneficiaryKbe))
}

// BeneficiaryKbeEqualFold applies the EqualFold predicate on the "beneficiary_kbe" field.
func BeneficiaryKbeEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldBeneficiaryKbe, v))
}

// BeneficiaryKbeContainsFold applies the ContainsFold predicate on the "beneficiary_kbe" field.
func BeneficiaryKbeContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldBeneficiaryKbe, v))
}

// BeneficiaryBinIinEQ applies the EQ predicate on the "beneficiary_bin_iin" field.
func BeneficiaryBinIinEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldBeneficiaryBinIin, v))
}

// BeneficiaryBinIinNEQ applies the NEQ predicate on the "beneficiary_bin_iin" field.
func BeneficiaryBinIinNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldBeneficiaryBinIin, v))
}

// BeneficiaryBinIinIn applies the In predicate on the "beneficiary_bin_iin" field.
func BeneficiaryBinIinIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldBeneficiaryBinIin, vs...))
}

// BeneficiaryBinIinNotIn applies the NotIn predicate on the "beneficiary_bin_iin" field.
func BeneficiaryBinIinNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldBeneficiaryBinIin, vs...))
}

// BeneficiaryBinIinGT applies the GT predicate on the "beneficiary_bin_iin" field.
func BeneficiaryBinIinGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldBeneficiaryBinIin, v))
}

// BeneficiaryBinIinGTE applies the GTE predicate on the "beneficiary_bin_iin" field.
func BeneficiaryBinIinGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldBeneficiaryBinIin, v))
}

// BeneficiaryBinIinLT applies the LT predicate on the "beneficiary_bin_iin" field.
func BeneficiaryBinIinLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldBeneficiaryBinIin, v))
}

// BeneficiaryBinIinLTE applies the LTE predicate on the "beneficiary_bin_iin" field.
func BeneficiaryBinIinLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldBeneficiaryBinIin, v))
}

// BeneficiaryBinIinContains applies the Contains predicate on the "beneficiary_bin_iin" field.
func BeneficiaryBinIinContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldBeneficiaryBinIin, v))
}

// BeneficiaryBinIinHasPrefix applies the HasPrefix predicate on the "beneficiary_bin_iin" field.
func BeneficiaryBinIinHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldBeneficiaryBinIin, v))
}

// BeneficiaryBinIinHasSuffix applies the HasSuffix predicate on the "beneficiary_bin_iin" field.
func BeneficiaryBinIinHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldBeneficiaryBinIin, v))
}

// BeneficiaryBinIinIsNil applies the IsNil predicate on the "beneficiary_bin_iin" field.
func BeneficiaryBinIinIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldBeneficiaryBinIin))
}

// BeneficiaryBinIinNotNil applies the NotNil predicate on the "beneficiary_bin_iin" field.
func BeneficiaryBinIinNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldBeneficiaryBinIin))
}

// BeneficiaryBinIinEqualFold applies the EqualFold predicate on the "beneficiary_bin_iin" field.
func BeneficiaryBinIinEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldBeneficiaryBinIin, v))
}

// BeneficiaryBinIinContainsFold applies the ContainsFold predicate on the "beneficiary_bin_iin" field.
func BeneficiaryBinIinContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldBeneficiaryBinIin, v))
}

// BeneficiaryNameEQ applies the EQ predicate on the "beneficiary_name" field.
func BeneficiaryNameEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldBeneficiaryName, v))
}

// BeneficiaryNameNEQ applies the NEQ predicate on the "beneficiary_name" field.
func BeneficiaryNameNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldBeneficiaryName, v))
}

// BeneficiaryNameIn applies the In predicate on the "beneficiary_name" field.
func BeneficiaryNameIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldBeneficiaryName, vs...))
}

// BeneficiaryNameNotIn applies the NotIn predicate on the "beneficiary_name" field.
func BeneficiaryNameNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldBeneficiaryName, vs...))
}

// BeneficiaryNameGT applies the GT predicate on the "beneficiary_name" field.
func BeneficiaryNameGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldBeneficiaryName, v))
}

// BeneficiaryNameGTE applies the GTE predicate on the "beneficiary_name" field.
func BeneficiaryNameGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldBeneficiaryName, v))
}

// BeneficiaryNameLT applies the LT predicate on the "beneficiary_name" field.
func BeneficiaryNameLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldBeneficiaryName, v))
}

// BeneficiaryNameLTE applies the LTE predicate on the "beneficiary_name" field.
func BeneficiaryNameLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldBeneficiaryName, v))
}

// BeneficiaryNameContains applies the Contains predicate on the "beneficiary_name" field.
func BeneficiaryNameContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldBeneficiaryName, v))
}

// BeneficiaryNameHasPrefix applies the HasPrefix predicate on the "beneficiary_name" field.
func BeneficiaryNameHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldBeneficiaryName, v))
}

// BeneficiaryNameHasSuffix applies the HasSuffix predicate on the "beneficiary_name" field.
func BeneficiaryNameHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldBeneficiaryName, v))
}

// BeneficiaryNameIsNil applies the IsNil predicate on the "beneficiary_name" field.
func BeneficiaryNameIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldBeneficiaryName))
}

// BeneficiaryNameNotNil applies the NotNil predicate on the "beneficiary_name" field.
func BeneficiaryNameNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldBeneficiaryName))
}

// BeneficiaryNameEqualFold applies the EqualFold predicate on the "beneficiary_name" field.
func BeneficiaryNameEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldBeneficiaryName, v))
}

// BeneficiaryNameContainsFold applies the ContainsFold predicate on the "beneficiary_name" field.
func BeneficiaryNameContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldBeneficiaryName, v))
}

// BeneficiaryTypeEQ applies the EQ predicate on the "beneficiary_type" field.
func BeneficiaryTypeEQ(v BeneficiaryType) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldBeneficiaryType, v))
}

// BeneficiaryTypeNEQ applies the NEQ predicate on the "beneficiary_type" field.
func BeneficiaryTypeNEQ(v BeneficiaryType) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldBeneficiaryType, v))
}

// BeneficiaryTypeIn applies the In predicate on the "beneficiary_type" field.
func BeneficiaryTypeIn(vs ...BeneficiaryType) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldBeneficiaryType, vs...))
}

// BeneficiaryTypeNotIn applies the NotIn predicate on the "beneficiary_type" field.
func BeneficiaryTypeNotIn(vs ...BeneficiaryType) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldBeneficiaryType, vs...))
}

// BeneficiaryTypeIsNil applies the IsNil predicate on the "beneficiary_type" field.
func BeneficiaryTypeIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldBeneficiaryType))
}

// BeneficiaryTypeNotNil applies the NotNil predicate on the "beneficiary_type" field.
func BeneficiaryTypeNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldBeneficiaryType))
}

// BeneficiaryAccountIbanEQ applies the EQ predicate on the "beneficiary_account_iban" field.
func BeneficiaryAccountIbanEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldBeneficiaryAccountIban, v))
}

// BeneficiaryAccountIbanNEQ applies the NEQ predicate on the "beneficiary_account_iban" field.
func BeneficiaryAccountIbanNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldBeneficiaryAccountIban, v))
}

// BeneficiaryAccountIbanIn applies the In predicate on the "beneficiary_account_iban" field.
func BeneficiaryAccountIbanIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldBeneficiaryAccountIban, vs...))
}

// BeneficiaryAccountIbanNotIn applies the NotIn predicate on the "beneficiary_account_iban" field.
func BeneficiaryAccountIbanNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldBeneficiaryAccountIban, vs...))
}

// BeneficiaryAccountIbanGT applies the GT predicate on the "beneficiary_account_iban" field.
func BeneficiaryAccountIbanGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldBeneficiaryAccountIban, v))
}

// BeneficiaryAccountIbanGTE applies the GTE predicate on the "beneficiary_account_iban" field.
func BeneficiaryAccountIbanGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldBeneficiaryAccountIban, v))
}

// BeneficiaryAccountIbanLT applies the LT predicate on the "beneficiary_account_iban" field.
func BeneficiaryAccountIbanLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldBeneficiaryAccountIban, v))
}

// BeneficiaryAccountIbanLTE applies the LTE predicate on the "beneficiary_account_iban" field.
func BeneficiaryAccountIbanLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldBeneficiaryAccountIban, v))
}

// BeneficiaryAccountIbanContains applies the Contains predicate on the "beneficiary_account_iban" field.
func BeneficiaryAccountIbanContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldBeneficiaryAccountIban, v))
}

// BeneficiaryAccountIbanHasPrefix applies the HasPrefix predicate on the "beneficiary_account_iban" field.
func BeneficiaryAccountIbanHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldBeneficiaryAccountIban, v))
}

// BeneficiaryAccountIbanHasSuffix applies the HasSuffix predicate on the "beneficiary_account_iban" field.
func BeneficiaryAccountIbanHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldBeneficiaryAccountIban, v))
}

// BeneficiaryAccountIbanIsNil applies the IsNil predicate on the "beneficiary_account_iban" field.
func BeneficiaryAccountIbanIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldBeneficiaryAccountIban))
}

// BeneficiaryAccountIbanNotNil applies the NotNil predicate on the "beneficiary_account_iban" field.
func BeneficiaryAccountIbanNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldBeneficiaryAccountIban))
}

// BeneficiaryAccountIbanEqualFold applies the EqualFold predicate on the "beneficiary_account_iban" field.
func BeneficiaryAccountIbanEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldBeneficiaryAccountIban, v))
}

// BeneficiaryAccountIbanContainsFold applies the ContainsFold predicate on the "beneficiary_account_iban" field.
func BeneficiaryAccountIbanContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldBeneficiaryAccountIban, v))
}

// BeneficiaryBankBicEQ applies the EQ predicate on the "beneficiary_bank_bic" field.
func BeneficiaryBankBicEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldBeneficiaryBankBic, v))
}

// BeneficiaryBankBicNEQ applies the NEQ predicate on the "beneficiary_bank_bic" field.
func BeneficiaryBankBicNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldBeneficiaryBankBic, v))
}

// BeneficiaryBankBicIn applies the In predicate on the "beneficiary_bank_bic" field.
func BeneficiaryBankBicIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldBeneficiaryBankBic, vs...))
}

// BeneficiaryBankBicNotIn applies the NotIn predicate on the "beneficiary_bank_bic" field.
func BeneficiaryBankBicNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldBeneficiaryBankBic, vs...))
}

// BeneficiaryBankBicGT applies the GT predicate on the "beneficiary_bank_bic" field.
func BeneficiaryBankBicGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldBeneficiaryBankBic, v))
}

// BeneficiaryBankBicGTE applies the GTE predicate on the "beneficiary_bank_bic" field.
func BeneficiaryBankBicGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldBeneficiaryBankBic, v))
}

// BeneficiaryBankBicLT applies the LT predicate on the "beneficiary_bank_bic" field.
func BeneficiaryBankBicLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldBeneficiaryBankBic, v))
}

// BeneficiaryBankBicLTE applies the LTE predicate on the "beneficiary_bank_bic" field.
func BeneficiaryBankBicLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldBeneficiaryBankBic, v))
}

// BeneficiaryBankBicContains applies the Contains predicate on the "beneficiary_bank_bic" field.
func BeneficiaryBankBicContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldBeneficiaryBankBic, v))
}

// BeneficiaryBankBicHasPrefix applies the HasPrefix predicate on the "beneficiary_bank_bic" field.
func BeneficiaryBankBicHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldBeneficiaryBankBic, v))
}

// BeneficiaryBankBicHasSuffix applies the HasSuffix predicate on the "beneficiary_bank_bic" field.
func BeneficiaryBankBicHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldBeneficiaryBankBic, v))
}

// BeneficiaryBankBicIsNil applies the IsNil predicate on the "beneficiary_bank_bic" field.
func BeneficiaryBankBicIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldBeneficiaryBankBic))
}

// BeneficiaryBankBicNotNil applies the NotNil predicate on the "beneficiary_bank_bic" field.
func BeneficiaryBankBicNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldBeneficiaryBankBic))
}

// BeneficiaryBankBicEqualFold applies the EqualFold predicate on the "beneficiary_bank_bic" field.
func BeneficiaryBankBicEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldBeneficiaryBankBic, v))
}

// BeneficiaryBankBicContainsFold applies the ContainsFold predicate on the "beneficiary_bank_bic" field.
func BeneficiaryBankBicContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldBeneficiaryBankBic, v))
}

// BeneficiaryBankNameEQ applies the EQ predicate on the "beneficiary_bank_name" field.
func BeneficiaryBankNameEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldBeneficiaryBankName, v))
}

// BeneficiaryBankNameNEQ applies the NEQ predicate on the "beneficiary_bank_name" field.
func BeneficiaryBankNameNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldBeneficiaryBankName, v))
}

// BeneficiaryBankNameIn applies the In predicate on the "beneficiary_bank_name" field.
func BeneficiaryBankNameIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldBeneficiaryBankName, vs...))
}

// BeneficiaryBankNameNotIn applies the NotIn predicate on the "beneficiary_bank_name" field.
func BeneficiaryBankNameNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldBeneficiaryBankName, vs...))
}

// BeneficiaryBankNameGT applies the GT predicate on the "beneficiary_bank_name" field.
func BeneficiaryBankNameGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldBeneficiaryBankName, v))
}

// BeneficiaryBankNameGTE applies the GTE predicate on the "beneficiary_bank_name" field.
func BeneficiaryBankNameGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldBeneficiaryBankName, v))
}

// BeneficiaryBankNameLT applies the LT predicate on the "beneficiary_bank_name" field.
func BeneficiaryBankNameLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldBeneficiaryBankName, v))
}

// BeneficiaryBankNameLTE applies the LTE predicate on the "beneficiary_bank_name" field.
func BeneficiaryBankNameLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldBeneficiaryBankName, v))
}

// BeneficiaryBankNameContains applies the Contains predicate on the "beneficiary_bank_name" field.
func BeneficiaryBankNameContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldBeneficiaryBankName, v))
}

// BeneficiaryBankNameHasPrefix applies the HasPrefix predicate on the "beneficiary_bank_name" field.
func BeneficiaryBankNameHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldBeneficiaryBankName, v))
}

// BeneficiaryBankNameHasSuffix applies the HasSuffix predicate on the "beneficiary_bank_name" field.
func BeneficiaryBankNameHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldBeneficiaryBankName, v))
}

// BeneficiaryBankNameIsNil applies the IsNil predicate on the "beneficiary_bank_name" field.
func BeneficiaryBankNameIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldBeneficiaryBankName))
}

// BeneficiaryBankNameNotNil applies the NotNil predicate on the "beneficiary_bank_name" field.
func BeneficiaryBankNameNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldBeneficiaryBankName))
}

// BeneficiaryBankNameEqualFold applies the EqualFold predicate on the "beneficiary_bank_name" field.
func BeneficiaryBankNameEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldBeneficiaryBankName, v))
}

// BeneficiaryBankNameContainsFold applies the ContainsFold predicate on the "beneficiary_bank_name" field.
func BeneficiaryBankNameContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldBeneficiaryBankName, v))
}

// BeneficiaryIsoCountryCodeEQ applies the EQ predicate on the "beneficiary_iso_country_code" field.
func BeneficiaryIsoCountryCodeEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldBeneficiaryIsoCountryCode, v))
}

// BeneficiaryIsoCountryCodeNEQ applies the NEQ predicate on the "beneficiary_iso_country_code" field.
func BeneficiaryIsoCountryCodeNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldBeneficiaryIsoCountryCode, v))
}

// BeneficiaryIsoCountryCodeIn applies the In predicate on the "beneficiary_iso_country_code" field.
func BeneficiaryIsoCountryCodeIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldBeneficiaryIsoCountryCode, vs...))
}

// BeneficiaryIsoCountryCodeNotIn applies the NotIn predicate on the "beneficiary_iso_country_code" field.
func BeneficiaryIsoCountryCodeNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldBeneficiaryIsoCountryCode, vs...))
}

// BeneficiaryIsoCountryCodeGT applies the GT predicate on the "beneficiary_iso_country_code" field.
func BeneficiaryIsoCountryCodeGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldBeneficiaryIsoCountryCode, v))
}

// BeneficiaryIsoCountryCodeGTE applies the GTE predicate on the "beneficiary_iso_country_code" field.
func BeneficiaryIsoCountryCodeGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldBeneficiaryIsoCountryCode, v))
}

// BeneficiaryIsoCountryCodeLT applies the LT predicate on the "beneficiary_iso_country_code" field.
func BeneficiaryIsoCountryCodeLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldBeneficiaryIsoCountryCode, v))
}

// BeneficiaryIsoCountryCodeLTE applies the LTE predicate on the "beneficiary_iso_country_code" field.
func BeneficiaryIsoCountryCodeLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldBeneficiaryIsoCountryCode, v))
}

// BeneficiaryIsoCountryCodeContains applies the Contains predicate on the "beneficiary_iso_country_code" field.
func BeneficiaryIsoCountryCodeContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldBeneficiaryIsoCountryCode, v))
}

// BeneficiaryIsoCountryCodeHasPrefix applies the HasPrefix predicate on the "beneficiary_iso_country_code" field.
func BeneficiaryIsoCountryCodeHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldBeneficiaryIsoCountryCode, v))
}

// BeneficiaryIsoCountryCodeHasSuffix applies the HasSuffix predicate on the "beneficiary_iso_country_code" field.
func BeneficiaryIsoCountryCodeHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldBeneficiaryIsoCountryCode, v))
}

// BeneficiaryIsoCountryCodeIsNil applies the IsNil predicate on the "beneficiary_iso_country_code" field.
func BeneficiaryIsoCountryCodeIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldBeneficiaryIsoCountryCode))
}

// BeneficiaryIsoCountryCodeNotNil applies the NotNil predicate on the "beneficiary_iso_country_code" field.
func BeneficiaryIsoCountryCodeNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldBeneficiaryIsoCountryCode))
}

// BeneficiaryIsoCountryCodeEqualFold applies the EqualFold predicate on the "beneficiary_iso_country_code" field.
func BeneficiaryIsoCountryCodeEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldBeneficiaryIsoCountryCode, v))
}

// BeneficiaryIsoCountryCodeContainsFold applies the ContainsFold predicate on the "beneficiary_iso_country_code" field.
func BeneficiaryIsoCountryCodeContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldBeneficiaryIsoCountryCode, v))
}

// RealBeneficiaryNameEQ applies the EQ predicate on the "real_beneficiary_name" field.
func RealBeneficiaryNameEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldRealBeneficiaryName, v))
}

// RealBeneficiaryNameNEQ applies the NEQ predicate on the "real_beneficiary_name" field.
func RealBeneficiaryNameNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldRealBeneficiaryName, v))
}

// RealBeneficiaryNameIn applies the In predicate on the "real_beneficiary_name" field.
func RealBeneficiaryNameIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldRealBeneficiaryName, vs...))
}

// RealBeneficiaryNameNotIn applies the NotIn predicate on the "real_beneficiary_name" field.
func RealBeneficiaryNameNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldRealBeneficiaryName, vs...))
}

// RealBeneficiaryNameGT applies the GT predicate on the "real_beneficiary_name" field.
func RealBeneficiaryNameGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldRealBeneficiaryName, v))
}

// RealBeneficiaryNameGTE applies the GTE predicate on the "real_beneficiary_name" field.
func RealBeneficiaryNameGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldRealBeneficiaryName, v))
}

// RealBeneficiaryNameLT applies the LT predicate on the "real_beneficiary_name" field.
func RealBeneficiaryNameLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldRealBeneficiaryName, v))
}

// RealBeneficiaryNameLTE applies the LTE predicate on the "real_beneficiary_name" field.
func RealBeneficiaryNameLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldRealBeneficiaryName, v))
}

// RealBeneficiaryNameContains applies the Contains predicate on the "real_beneficiary_name" field.
func RealBeneficiaryNameContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldRealBeneficiaryName, v))
}

// RealBeneficiaryNameHasPrefix applies the HasPrefix predicate on the "real_beneficiary_name" field.
func RealBeneficiaryNameHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldRealBeneficiaryName, v))
}

// RealBeneficiaryNameHasSuffix applies the HasSuffix predicate on the "real_beneficiary_name" field.
func RealBeneficiaryNameHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldRealBeneficiaryName, v))
}

// RealBeneficiaryNameIsNil applies the IsNil predicate on the "real_beneficiary_name" field.
func RealBeneficiaryNameIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldRealBeneficiaryName))
}

// RealBeneficiaryNameNotNil applies the NotNil predicate on the "real_beneficiary_name" field.
func RealBeneficiaryNameNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldRealBeneficiaryName))
}

// RealBeneficiaryNameEqualFold applies the EqualFold predicate on the "real_beneficiary_name" field.
func RealBeneficiaryNameEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldRealBeneficiaryName, v))
}

// RealBeneficiaryNameContainsFold applies the ContainsFold predicate on the "real_beneficiary_name" field.
func RealBeneficiaryNameContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldRealBeneficiaryName, v))
}

// RealBeneficiaryBinIinEQ applies the EQ predicate on the "real_beneficiary_bin_iin" field.
func RealBeneficiaryBinIinEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldRealBeneficiaryBinIin, v))
}

// RealBeneficiaryBinIinNEQ applies the NEQ predicate on the "real_beneficiary_bin_iin" field.
func RealBeneficiaryBinIinNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldRealBeneficiaryBinIin, v))
}

// RealBeneficiaryBinIinIn applies the In predicate on the "real_beneficiary_bin_iin" field.
func RealBeneficiaryBinIinIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldRealBeneficiaryBinIin, vs...))
}

// RealBeneficiaryBinIinNotIn applies the NotIn predicate on the "real_beneficiary_bin_iin" field.
func RealBeneficiaryBinIinNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldRealBeneficiaryBinIin, vs...))
}

// RealBeneficiaryBinIinGT applies the GT predicate on the "real_beneficiary_bin_iin" field.
func RealBeneficiaryBinIinGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldRealBeneficiaryBinIin, v))
}

// RealBeneficiaryBinIinGTE applies the GTE predicate on the "real_beneficiary_bin_iin" field.
func RealBeneficiaryBinIinGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldRealBeneficiaryBinIin, v))
}

// RealBeneficiaryBinIinLT applies the LT predicate on the "real_beneficiary_bin_iin" field.
func RealBeneficiaryBinIinLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldRealBeneficiaryBinIin, v))
}

// RealBeneficiaryBinIinLTE applies the LTE predicate on the "real_beneficiary_bin_iin" field.
func RealBeneficiaryBinIinLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldRealBeneficiaryBinIin, v))
}

// RealBeneficiaryBinIinContains applies the Contains predicate on the "real_beneficiary_bin_iin" field.
func RealBeneficiaryBinIinContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldRealBeneficiaryBinIin, v))
}

// RealBeneficiaryBinIinHasPrefix applies the HasPrefix predicate on the "real_beneficiary_bin_iin" field.
func RealBeneficiaryBinIinHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldRealBeneficiaryBinIin, v))
}

// RealBeneficiaryBinIinHasSuffix applies the HasSuffix predicate on the "real_beneficiary_bin_iin" field.
func RealBeneficiaryBinIinHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldRealBeneficiaryBinIin, v))
}

// RealBeneficiaryBinIinIsNil applies the IsNil predicate on the "real_beneficiary_bin_iin" field.
func RealBeneficiaryBinIinIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldRealBeneficiaryBinIin))
}

// RealBeneficiaryBinIinNotNil applies the NotNil predicate on the "real_beneficiary_bin_iin" field.
func RealBeneficiaryBinIinNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldRealBeneficiaryBinIin))
}

// RealBeneficiaryBinIinEqualFold applies the EqualFold predicate on the "real_beneficiary_bin_iin" field.
func RealBeneficiaryBinIinEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldRealBeneficiaryBinIin, v))
}

// RealBeneficiaryBinIinContainsFold applies the ContainsFold predicate on the "real_beneficiary_bin_iin" field.
func RealBeneficiaryBinIinContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldRealBeneficiaryBinIin, v))
}

// RealBeneficiaryCountryCodeEQ applies the EQ predicate on the "real_beneficiary_country_code" field.
func RealBeneficiaryCountryCodeEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldRealBeneficiaryCountryCode, v))
}

// RealBeneficiaryCountryCodeNEQ applies the NEQ predicate on the "real_beneficiary_country_code" field.
func RealBeneficiaryCountryCodeNEQ(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldRealBeneficiaryCountryCode, v))
}

// RealBeneficiaryCountryCodeIn applies the In predicate on the "real_beneficiary_country_code" field.
func RealBeneficiaryCountryCodeIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldRealBeneficiaryCountryCode, vs...))
}

// RealBeneficiaryCountryCodeNotIn applies the NotIn predicate on the "real_beneficiary_country_code" field.
func RealBeneficiaryCountryCodeNotIn(vs ...string) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldRealBeneficiaryCountryCode, vs...))
}

// RealBeneficiaryCountryCodeGT applies the GT predicate on the "real_beneficiary_country_code" field.
func RealBeneficiaryCountryCodeGT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGT(FieldRealBeneficiaryCountryCode, v))
}

// RealBeneficiaryCountryCodeGTE applies the GTE predicate on the "real_beneficiary_country_code" field.
func RealBeneficiaryCountryCodeGTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldGTE(FieldRealBeneficiaryCountryCode, v))
}

// RealBeneficiaryCountryCodeLT applies the LT predicate on the "real_beneficiary_country_code" field.
func RealBeneficiaryCountryCodeLT(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLT(FieldRealBeneficiaryCountryCode, v))
}

// RealBeneficiaryCountryCodeLTE applies the LTE predicate on the "real_beneficiary_country_code" field.
func RealBeneficiaryCountryCodeLTE(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldLTE(FieldRealBeneficiaryCountryCode, v))
}

// RealBeneficiaryCountryCodeContains applies the Contains predicate on the "real_beneficiary_country_code" field.
func RealBeneficiaryCountryCodeContains(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContains(FieldRealBeneficiaryCountryCode, v))
}

// RealBeneficiaryCountryCodeHasPrefix applies the HasPrefix predicate on the "real_beneficiary_country_code" field.
func RealBeneficiaryCountryCodeHasPrefix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasPrefix(FieldRealBeneficiaryCountryCode, v))
}

// RealBeneficiaryCountryCodeHasSuffix applies the HasSuffix predicate on the "real_beneficiary_country_code" field.
func RealBeneficiaryCountryCodeHasSuffix(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldHasSuffix(FieldRealBeneficiaryCountryCode, v))
}

// RealBeneficiaryCountryCodeIsNil applies the IsNil predicate on the "real_beneficiary_country_code" field.
func RealBeneficiaryCountryCodeIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldRealBeneficiaryCountryCode))
}

// RealBeneficiaryCountryCodeNotNil applies the NotNil predicate on the "real_beneficiary_country_code" field.
func RealBeneficiaryCountryCodeNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldRealBeneficiaryCountryCode))
}

// RealBeneficiaryCountryCodeEqualFold applies the EqualFold predicate on the "real_beneficiary_country_code" field.
func RealBeneficiaryCountryCodeEqualFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldEqualFold(FieldRealBeneficiaryCountryCode, v))
}

// RealBeneficiaryCountryCodeContainsFold applies the ContainsFold predicate on the "real_beneficiary_country_code" field.
func RealBeneficiaryCountryCodeContainsFold(v string) predicate.Transaction {
	return predicate.Transaction(sql.FieldContainsFold(FieldRealBeneficiaryCountryCode, v))
}

// RealBeneficiaryTypeEQ applies the EQ predicate on the "real_beneficiary_type" field.
func RealBeneficiaryTypeEQ(v RealBeneficiaryType) predicate.Transaction {
	return predicate.Transaction(sql.FieldEQ(FieldRealBeneficiaryType, v))
}

// RealBeneficiaryTypeNEQ applies the NEQ predicate on the "real_beneficiary_type" field.
func RealBeneficiaryTypeNEQ(v RealBeneficiaryType) predicate.Transaction {
	return predicate.Transaction(sql.FieldNEQ(FieldRealBeneficiaryType, v))
}

// RealBeneficiaryTypeIn applies the In predicate on the "real_beneficiary_type" field.
func RealBeneficiaryTypeIn(vs ...RealBeneficiaryType) predicate.Transaction {
	return predicate.Transaction(sql.FieldIn(FieldRealBeneficiaryType, vs...))
}

// RealBeneficiaryTypeNotIn applies the NotIn predicate on the "real_beneficiary_type" field.
func RealBeneficiaryTypeNotIn(vs ...RealBeneficiaryType) predicate.Transaction {
	return predicate.Transaction(sql.FieldNotIn(FieldRealBeneficiaryType, vs...))
}

// RealBeneficiaryTypeIsNil applies the IsNil predicate on the "real_beneficiary_type" field.
func RealBeneficiaryTypeIsNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldIsNull(FieldRealBeneficiaryType))
}

// RealBeneficiaryTypeNotNil applies the NotNil predicate on the "real_beneficiary_type" field.
func RealBeneficiaryTypeNotNil() predicate.Transaction {
	return predicate.Transaction(sql.FieldNotNull(FieldRealBeneficiaryType))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Transaction) predicate.Transaction {
	return predicate.Transaction(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Transaction) predicate.Transaction {
	return predicate.Transaction(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Transaction) predicate.Transaction {
	return predicate.Transaction(sql.NotPredicates(p))
}
