// Code generated by ent, DO NOT EDIT.

package confirmations

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.Confirmations {
	return predicate.Confirmations(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.Confirmations {
	return predicate.Confirmations(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.Confirmations {
	return predicate.Confirmations(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.Confirmations {
	return predicate.Confirmations(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.Confirmations {
	return predicate.Confirmations(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.Confirmations {
	return predicate.Confirmations(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.Confirmations {
	return predicate.Confirmations(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.Confirmations {
	return predicate.Confirmations(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.Confirmations {
	return predicate.Confirmations(sql.FieldLTE(FieldID, id))
}

// ConfirmationDate applies equality check predicate on the "confirmation_date" field. It's identical to ConfirmationDateEQ.
func ConfirmationDate(v time.Time) predicate.Confirmations {
	return predicate.Confirmations(sql.FieldEQ(FieldConfirmationDate, v))
}

// ConfirmationTypeEQ applies the EQ predicate on the "confirmation_type" field.
func ConfirmationTypeEQ(v ConfirmationType) predicate.Confirmations {
	return predicate.Confirmations(sql.FieldEQ(FieldConfirmationType, v))
}

// ConfirmationTypeNEQ applies the NEQ predicate on the "confirmation_type" field.
func ConfirmationTypeNEQ(v ConfirmationType) predicate.Confirmations {
	return predicate.Confirmations(sql.FieldNEQ(FieldConfirmationType, v))
}

// ConfirmationTypeIn applies the In predicate on the "confirmation_type" field.
func ConfirmationTypeIn(vs ...ConfirmationType) predicate.Confirmations {
	return predicate.Confirmations(sql.FieldIn(FieldConfirmationType, vs...))
}

// ConfirmationTypeNotIn applies the NotIn predicate on the "confirmation_type" field.
func ConfirmationTypeNotIn(vs ...ConfirmationType) predicate.Confirmations {
	return predicate.Confirmations(sql.FieldNotIn(FieldConfirmationType, vs...))
}

// ConfirmationDateEQ applies the EQ predicate on the "confirmation_date" field.
func ConfirmationDateEQ(v time.Time) predicate.Confirmations {
	return predicate.Confirmations(sql.FieldEQ(FieldConfirmationDate, v))
}

// ConfirmationDateNEQ applies the NEQ predicate on the "confirmation_date" field.
func ConfirmationDateNEQ(v time.Time) predicate.Confirmations {
	return predicate.Confirmations(sql.FieldNEQ(FieldConfirmationDate, v))
}

// ConfirmationDateIn applies the In predicate on the "confirmation_date" field.
func ConfirmationDateIn(vs ...time.Time) predicate.Confirmations {
	return predicate.Confirmations(sql.FieldIn(FieldConfirmationDate, vs...))
}

// ConfirmationDateNotIn applies the NotIn predicate on the "confirmation_date" field.
func ConfirmationDateNotIn(vs ...time.Time) predicate.Confirmations {
	return predicate.Confirmations(sql.FieldNotIn(FieldConfirmationDate, vs...))
}

// ConfirmationDateGT applies the GT predicate on the "confirmation_date" field.
func ConfirmationDateGT(v time.Time) predicate.Confirmations {
	return predicate.Confirmations(sql.FieldGT(FieldConfirmationDate, v))
}

// ConfirmationDateGTE applies the GTE predicate on the "confirmation_date" field.
func ConfirmationDateGTE(v time.Time) predicate.Confirmations {
	return predicate.Confirmations(sql.FieldGTE(FieldConfirmationDate, v))
}

// ConfirmationDateLT applies the LT predicate on the "confirmation_date" field.
func ConfirmationDateLT(v time.Time) predicate.Confirmations {
	return predicate.Confirmations(sql.FieldLT(FieldConfirmationDate, v))
}

// ConfirmationDateLTE applies the LTE predicate on the "confirmation_date" field.
func ConfirmationDateLTE(v time.Time) predicate.Confirmations {
	return predicate.Confirmations(sql.FieldLTE(FieldConfirmationDate, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Confirmations) predicate.Confirmations {
	return predicate.Confirmations(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Confirmations) predicate.Confirmations {
	return predicate.Confirmations(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Confirmations) predicate.Confirmations {
	return predicate.Confirmations(sql.NotPredicates(p))
}
