// Code generated by ent, DO NOT EDIT.

package confirmations

import (
	"fmt"

	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the confirmations type in the database.
	Label = "confirmations"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldConfirmationType holds the string denoting the confirmation_type field in the database.
	FieldConfirmationType = "confirmation_type"
	// FieldConfirmationDate holds the string denoting the confirmation_date field in the database.
	FieldConfirmationDate = "confirmation_date"
	// Table holds the table name of the confirmations in the database.
	Table = "confirmations"
)

// Columns holds all SQL columns for confirmations fields.
var Columns = []string{
	FieldID,
	FieldConfirmationType,
	FieldConfirmationDate,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// ConfirmationType defines the type for the "confirmation_type" enum field.
type ConfirmationType string

// ConfirmationType values.
const (
	ConfirmationTypeOTP ConfirmationType = "OTP"
)

func (ct ConfirmationType) String() string {
	return string(ct)
}

// ConfirmationTypeValidator is a validator for the "confirmation_type" field enum values. It is called by the builders before save.
func ConfirmationTypeValidator(ct ConfirmationType) error {
	switch ct {
	case ConfirmationTypeOTP:
		return nil
	default:
		return fmt.Errorf("confirmations: invalid enum value for confirmation_type field: %q", ct)
	}
}

// OrderOption defines the ordering options for the Confirmations queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByConfirmationType orders the results by the confirmation_type field.
func ByConfirmationType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldConfirmationType, opts...).ToFunc()
}

// ByConfirmationDate orders the results by the confirmation_date field.
func ByConfirmationDate(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldConfirmationDate, opts...).ToFunc()
}
