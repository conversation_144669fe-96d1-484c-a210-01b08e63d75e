// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/payments"
)

// PaymentsCreate is the builder for creating a Payments entity.
type PaymentsCreate struct {
	config
	mutation *PaymentsMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (_c *PaymentsCreate) SetCreateTime(v time.Time) *PaymentsCreate {
	_c.mutation.SetCreateTime(v)
	return _c
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (_c *PaymentsCreate) SetNillableCreateTime(v *time.Time) *PaymentsCreate {
	if v != nil {
		_c.SetCreateTime(*v)
	}
	return _c
}

// SetUpdateTime sets the "update_time" field.
func (_c *PaymentsCreate) SetUpdateTime(v time.Time) *PaymentsCreate {
	_c.mutation.SetUpdateTime(v)
	return _c
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (_c *PaymentsCreate) SetNillableUpdateTime(v *time.Time) *PaymentsCreate {
	if v != nil {
		_c.SetUpdateTime(*v)
	}
	return _c
}

// SetPaymentCode sets the "payment_code" field.
func (_c *PaymentsCreate) SetPaymentCode(v string) *PaymentsCreate {
	_c.mutation.SetPaymentCode(v)
	return _c
}

// SetPaymentType sets the "payment_type" field.
func (_c *PaymentsCreate) SetPaymentType(v payments.PaymentType) *PaymentsCreate {
	_c.mutation.SetPaymentType(v)
	return _c
}

// SetNillablePaymentType sets the "payment_type" field if the given value is not nil.
func (_c *PaymentsCreate) SetNillablePaymentType(v *payments.PaymentType) *PaymentsCreate {
	if v != nil {
		_c.SetPaymentType(*v)
	}
	return _c
}

// SetPaymentPeriod sets the "payment_period" field.
func (_c *PaymentsCreate) SetPaymentPeriod(v string) *PaymentsCreate {
	_c.mutation.SetPaymentPeriod(v)
	return _c
}

// SetKbk sets the "kbk" field.
func (_c *PaymentsCreate) SetKbk(v string) *PaymentsCreate {
	_c.mutation.SetKbk(v)
	return _c
}

// SetEmployeeList sets the "employee_list" field.
func (_c *PaymentsCreate) SetEmployeeList(v map[string]interface{}) *PaymentsCreate {
	_c.mutation.SetEmployeeList(v)
	return _c
}

// SetID sets the "id" field.
func (_c *PaymentsCreate) SetID(v uuid.UUID) *PaymentsCreate {
	_c.mutation.SetID(v)
	return _c
}

// Mutation returns the PaymentsMutation object of the builder.
func (_c *PaymentsCreate) Mutation() *PaymentsMutation {
	return _c.mutation
}

// Save creates the Payments in the database.
func (_c *PaymentsCreate) Save(ctx context.Context) (*Payments, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *PaymentsCreate) SaveX(ctx context.Context) *Payments {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *PaymentsCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *PaymentsCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *PaymentsCreate) defaults() {
	if _, ok := _c.mutation.CreateTime(); !ok {
		v := payments.DefaultCreateTime()
		_c.mutation.SetCreateTime(v)
	}
	if _, ok := _c.mutation.UpdateTime(); !ok {
		v := payments.DefaultUpdateTime()
		_c.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *PaymentsCreate) check() error {
	if _, ok := _c.mutation.PaymentCode(); !ok {
		return &ValidationError{Name: "payment_code", err: errors.New(`ent: missing required field "Payments.payment_code"`)}
	}
	if v, ok := _c.mutation.PaymentType(); ok {
		if err := payments.PaymentTypeValidator(v); err != nil {
			return &ValidationError{Name: "payment_type", err: fmt.Errorf(`ent: validator failed for field "Payments.payment_type": %w`, err)}
		}
	}
	if _, ok := _c.mutation.PaymentPeriod(); !ok {
		return &ValidationError{Name: "payment_period", err: errors.New(`ent: missing required field "Payments.payment_period"`)}
	}
	if _, ok := _c.mutation.Kbk(); !ok {
		return &ValidationError{Name: "kbk", err: errors.New(`ent: missing required field "Payments.kbk"`)}
	}
	return nil
}

func (_c *PaymentsCreate) sqlSave(ctx context.Context) (*Payments, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *PaymentsCreate) createSpec() (*Payments, *sqlgraph.CreateSpec) {
	var (
		_node = &Payments{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(payments.Table, sqlgraph.NewFieldSpec(payments.FieldID, field.TypeUUID))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := _c.mutation.CreateTime(); ok {
		_spec.SetField(payments.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = value
	}
	if value, ok := _c.mutation.UpdateTime(); ok {
		_spec.SetField(payments.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = value
	}
	if value, ok := _c.mutation.PaymentCode(); ok {
		_spec.SetField(payments.FieldPaymentCode, field.TypeString, value)
		_node.PaymentCode = value
	}
	if value, ok := _c.mutation.PaymentType(); ok {
		_spec.SetField(payments.FieldPaymentType, field.TypeEnum, value)
		_node.PaymentType = &value
	}
	if value, ok := _c.mutation.PaymentPeriod(); ok {
		_spec.SetField(payments.FieldPaymentPeriod, field.TypeString, value)
		_node.PaymentPeriod = value
	}
	if value, ok := _c.mutation.Kbk(); ok {
		_spec.SetField(payments.FieldKbk, field.TypeString, value)
		_node.Kbk = value
	}
	if value, ok := _c.mutation.EmployeeList(); ok {
		_spec.SetField(payments.FieldEmployeeList, field.TypeJSON, value)
		_node.EmployeeList = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Payments.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.PaymentsUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *PaymentsCreate) OnConflict(opts ...sql.ConflictOption) *PaymentsUpsertOne {
	_c.conflict = opts
	return &PaymentsUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Payments.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *PaymentsCreate) OnConflictColumns(columns ...string) *PaymentsUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &PaymentsUpsertOne{
		create: _c,
	}
}

type (
	// PaymentsUpsertOne is the builder for "upsert"-ing
	//  one Payments node.
	PaymentsUpsertOne struct {
		create *PaymentsCreate
	}

	// PaymentsUpsert is the "OnConflict" setter.
	PaymentsUpsert struct {
		*sql.UpdateSet
	}
)

// SetPaymentCode sets the "payment_code" field.
func (u *PaymentsUpsert) SetPaymentCode(v string) *PaymentsUpsert {
	u.Set(payments.FieldPaymentCode, v)
	return u
}

// UpdatePaymentCode sets the "payment_code" field to the value that was provided on create.
func (u *PaymentsUpsert) UpdatePaymentCode() *PaymentsUpsert {
	u.SetExcluded(payments.FieldPaymentCode)
	return u
}

// SetPaymentType sets the "payment_type" field.
func (u *PaymentsUpsert) SetPaymentType(v payments.PaymentType) *PaymentsUpsert {
	u.Set(payments.FieldPaymentType, v)
	return u
}

// UpdatePaymentType sets the "payment_type" field to the value that was provided on create.
func (u *PaymentsUpsert) UpdatePaymentType() *PaymentsUpsert {
	u.SetExcluded(payments.FieldPaymentType)
	return u
}

// ClearPaymentType clears the value of the "payment_type" field.
func (u *PaymentsUpsert) ClearPaymentType() *PaymentsUpsert {
	u.SetNull(payments.FieldPaymentType)
	return u
}

// SetPaymentPeriod sets the "payment_period" field.
func (u *PaymentsUpsert) SetPaymentPeriod(v string) *PaymentsUpsert {
	u.Set(payments.FieldPaymentPeriod, v)
	return u
}

// UpdatePaymentPeriod sets the "payment_period" field to the value that was provided on create.
func (u *PaymentsUpsert) UpdatePaymentPeriod() *PaymentsUpsert {
	u.SetExcluded(payments.FieldPaymentPeriod)
	return u
}

// SetKbk sets the "kbk" field.
func (u *PaymentsUpsert) SetKbk(v string) *PaymentsUpsert {
	u.Set(payments.FieldKbk, v)
	return u
}

// UpdateKbk sets the "kbk" field to the value that was provided on create.
func (u *PaymentsUpsert) UpdateKbk() *PaymentsUpsert {
	u.SetExcluded(payments.FieldKbk)
	return u
}

// SetEmployeeList sets the "employee_list" field.
func (u *PaymentsUpsert) SetEmployeeList(v map[string]interface{}) *PaymentsUpsert {
	u.Set(payments.FieldEmployeeList, v)
	return u
}

// UpdateEmployeeList sets the "employee_list" field to the value that was provided on create.
func (u *PaymentsUpsert) UpdateEmployeeList() *PaymentsUpsert {
	u.SetExcluded(payments.FieldEmployeeList)
	return u
}

// ClearEmployeeList clears the value of the "employee_list" field.
func (u *PaymentsUpsert) ClearEmployeeList() *PaymentsUpsert {
	u.SetNull(payments.FieldEmployeeList)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Payments.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(payments.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *PaymentsUpsertOne) UpdateNewValues() *PaymentsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(payments.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(payments.FieldCreateTime)
		}
		if _, exists := u.create.mutation.UpdateTime(); exists {
			s.SetIgnore(payments.FieldUpdateTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Payments.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *PaymentsUpsertOne) Ignore() *PaymentsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *PaymentsUpsertOne) DoNothing() *PaymentsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the PaymentsCreate.OnConflict
// documentation for more info.
func (u *PaymentsUpsertOne) Update(set func(*PaymentsUpsert)) *PaymentsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&PaymentsUpsert{UpdateSet: update})
	}))
	return u
}

// SetPaymentCode sets the "payment_code" field.
func (u *PaymentsUpsertOne) SetPaymentCode(v string) *PaymentsUpsertOne {
	return u.Update(func(s *PaymentsUpsert) {
		s.SetPaymentCode(v)
	})
}

// UpdatePaymentCode sets the "payment_code" field to the value that was provided on create.
func (u *PaymentsUpsertOne) UpdatePaymentCode() *PaymentsUpsertOne {
	return u.Update(func(s *PaymentsUpsert) {
		s.UpdatePaymentCode()
	})
}

// SetPaymentType sets the "payment_type" field.
func (u *PaymentsUpsertOne) SetPaymentType(v payments.PaymentType) *PaymentsUpsertOne {
	return u.Update(func(s *PaymentsUpsert) {
		s.SetPaymentType(v)
	})
}

// UpdatePaymentType sets the "payment_type" field to the value that was provided on create.
func (u *PaymentsUpsertOne) UpdatePaymentType() *PaymentsUpsertOne {
	return u.Update(func(s *PaymentsUpsert) {
		s.UpdatePaymentType()
	})
}

// ClearPaymentType clears the value of the "payment_type" field.
func (u *PaymentsUpsertOne) ClearPaymentType() *PaymentsUpsertOne {
	return u.Update(func(s *PaymentsUpsert) {
		s.ClearPaymentType()
	})
}

// SetPaymentPeriod sets the "payment_period" field.
func (u *PaymentsUpsertOne) SetPaymentPeriod(v string) *PaymentsUpsertOne {
	return u.Update(func(s *PaymentsUpsert) {
		s.SetPaymentPeriod(v)
	})
}

// UpdatePaymentPeriod sets the "payment_period" field to the value that was provided on create.
func (u *PaymentsUpsertOne) UpdatePaymentPeriod() *PaymentsUpsertOne {
	return u.Update(func(s *PaymentsUpsert) {
		s.UpdatePaymentPeriod()
	})
}

// SetKbk sets the "kbk" field.
func (u *PaymentsUpsertOne) SetKbk(v string) *PaymentsUpsertOne {
	return u.Update(func(s *PaymentsUpsert) {
		s.SetKbk(v)
	})
}

// UpdateKbk sets the "kbk" field to the value that was provided on create.
func (u *PaymentsUpsertOne) UpdateKbk() *PaymentsUpsertOne {
	return u.Update(func(s *PaymentsUpsert) {
		s.UpdateKbk()
	})
}

// SetEmployeeList sets the "employee_list" field.
func (u *PaymentsUpsertOne) SetEmployeeList(v map[string]interface{}) *PaymentsUpsertOne {
	return u.Update(func(s *PaymentsUpsert) {
		s.SetEmployeeList(v)
	})
}

// UpdateEmployeeList sets the "employee_list" field to the value that was provided on create.
func (u *PaymentsUpsertOne) UpdateEmployeeList() *PaymentsUpsertOne {
	return u.Update(func(s *PaymentsUpsert) {
		s.UpdateEmployeeList()
	})
}

// ClearEmployeeList clears the value of the "employee_list" field.
func (u *PaymentsUpsertOne) ClearEmployeeList() *PaymentsUpsertOne {
	return u.Update(func(s *PaymentsUpsert) {
		s.ClearEmployeeList()
	})
}

// Exec executes the query.
func (u *PaymentsUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for PaymentsCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *PaymentsUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *PaymentsUpsertOne) ID(ctx context.Context) (id uuid.UUID, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: PaymentsUpsertOne.ID is not supported by MySQL driver. Use PaymentsUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *PaymentsUpsertOne) IDX(ctx context.Context) uuid.UUID {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// PaymentsCreateBulk is the builder for creating many Payments entities in bulk.
type PaymentsCreateBulk struct {
	config
	err      error
	builders []*PaymentsCreate
	conflict []sql.ConflictOption
}

// Save creates the Payments entities in the database.
func (_c *PaymentsCreateBulk) Save(ctx context.Context) ([]*Payments, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*Payments, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*PaymentsMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *PaymentsCreateBulk) SaveX(ctx context.Context) []*Payments {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *PaymentsCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *PaymentsCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Payments.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.PaymentsUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *PaymentsCreateBulk) OnConflict(opts ...sql.ConflictOption) *PaymentsUpsertBulk {
	_c.conflict = opts
	return &PaymentsUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Payments.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *PaymentsCreateBulk) OnConflictColumns(columns ...string) *PaymentsUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &PaymentsUpsertBulk{
		create: _c,
	}
}

// PaymentsUpsertBulk is the builder for "upsert"-ing
// a bulk of Payments nodes.
type PaymentsUpsertBulk struct {
	create *PaymentsCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Payments.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(payments.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *PaymentsUpsertBulk) UpdateNewValues() *PaymentsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(payments.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(payments.FieldCreateTime)
			}
			if _, exists := b.mutation.UpdateTime(); exists {
				s.SetIgnore(payments.FieldUpdateTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Payments.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *PaymentsUpsertBulk) Ignore() *PaymentsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *PaymentsUpsertBulk) DoNothing() *PaymentsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the PaymentsCreateBulk.OnConflict
// documentation for more info.
func (u *PaymentsUpsertBulk) Update(set func(*PaymentsUpsert)) *PaymentsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&PaymentsUpsert{UpdateSet: update})
	}))
	return u
}

// SetPaymentCode sets the "payment_code" field.
func (u *PaymentsUpsertBulk) SetPaymentCode(v string) *PaymentsUpsertBulk {
	return u.Update(func(s *PaymentsUpsert) {
		s.SetPaymentCode(v)
	})
}

// UpdatePaymentCode sets the "payment_code" field to the value that was provided on create.
func (u *PaymentsUpsertBulk) UpdatePaymentCode() *PaymentsUpsertBulk {
	return u.Update(func(s *PaymentsUpsert) {
		s.UpdatePaymentCode()
	})
}

// SetPaymentType sets the "payment_type" field.
func (u *PaymentsUpsertBulk) SetPaymentType(v payments.PaymentType) *PaymentsUpsertBulk {
	return u.Update(func(s *PaymentsUpsert) {
		s.SetPaymentType(v)
	})
}

// UpdatePaymentType sets the "payment_type" field to the value that was provided on create.
func (u *PaymentsUpsertBulk) UpdatePaymentType() *PaymentsUpsertBulk {
	return u.Update(func(s *PaymentsUpsert) {
		s.UpdatePaymentType()
	})
}

// ClearPaymentType clears the value of the "payment_type" field.
func (u *PaymentsUpsertBulk) ClearPaymentType() *PaymentsUpsertBulk {
	return u.Update(func(s *PaymentsUpsert) {
		s.ClearPaymentType()
	})
}

// SetPaymentPeriod sets the "payment_period" field.
func (u *PaymentsUpsertBulk) SetPaymentPeriod(v string) *PaymentsUpsertBulk {
	return u.Update(func(s *PaymentsUpsert) {
		s.SetPaymentPeriod(v)
	})
}

// UpdatePaymentPeriod sets the "payment_period" field to the value that was provided on create.
func (u *PaymentsUpsertBulk) UpdatePaymentPeriod() *PaymentsUpsertBulk {
	return u.Update(func(s *PaymentsUpsert) {
		s.UpdatePaymentPeriod()
	})
}

// SetKbk sets the "kbk" field.
func (u *PaymentsUpsertBulk) SetKbk(v string) *PaymentsUpsertBulk {
	return u.Update(func(s *PaymentsUpsert) {
		s.SetKbk(v)
	})
}

// UpdateKbk sets the "kbk" field to the value that was provided on create.
func (u *PaymentsUpsertBulk) UpdateKbk() *PaymentsUpsertBulk {
	return u.Update(func(s *PaymentsUpsert) {
		s.UpdateKbk()
	})
}

// SetEmployeeList sets the "employee_list" field.
func (u *PaymentsUpsertBulk) SetEmployeeList(v map[string]interface{}) *PaymentsUpsertBulk {
	return u.Update(func(s *PaymentsUpsert) {
		s.SetEmployeeList(v)
	})
}

// UpdateEmployeeList sets the "employee_list" field to the value that was provided on create.
func (u *PaymentsUpsertBulk) UpdateEmployeeList() *PaymentsUpsertBulk {
	return u.Update(func(s *PaymentsUpsert) {
		s.UpdateEmployeeList()
	})
}

// ClearEmployeeList clears the value of the "employee_list" field.
func (u *PaymentsUpsertBulk) ClearEmployeeList() *PaymentsUpsertBulk {
	return u.Update(func(s *PaymentsUpsert) {
		s.ClearEmployeeList()
	})
}

// Exec executes the query.
func (u *PaymentsUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the PaymentsCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for PaymentsCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *PaymentsUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
