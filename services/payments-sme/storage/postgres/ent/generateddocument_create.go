// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/generateddocument"
)

// GeneratedDocumentCreate is the builder for creating a GeneratedDocument entity.
type GeneratedDocumentCreate struct {
	config
	mutation *GeneratedDocumentMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (_c *GeneratedDocumentCreate) SetCreateTime(v time.Time) *GeneratedDocumentCreate {
	_c.mutation.SetCreateTime(v)
	return _c
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (_c *GeneratedDocumentCreate) SetNillableCreateTime(v *time.Time) *GeneratedDocumentCreate {
	if v != nil {
		_c.SetCreateTime(*v)
	}
	return _c
}

// SetUpdateTime sets the "update_time" field.
func (_c *GeneratedDocumentCreate) SetUpdateTime(v time.Time) *GeneratedDocumentCreate {
	_c.mutation.SetUpdateTime(v)
	return _c
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (_c *GeneratedDocumentCreate) SetNillableUpdateTime(v *time.Time) *GeneratedDocumentCreate {
	if v != nil {
		_c.SetUpdateTime(*v)
	}
	return _c
}

// SetParentTransactionID sets the "parent_transaction_id" field.
func (_c *GeneratedDocumentCreate) SetParentTransactionID(v uuid.UUID) *GeneratedDocumentCreate {
	_c.mutation.SetParentTransactionID(v)
	return _c
}

// SetGeneratedDocumentIntegrationID sets the "generated_document_integration_id" field.
func (_c *GeneratedDocumentCreate) SetGeneratedDocumentIntegrationID(v uuid.UUID) *GeneratedDocumentCreate {
	_c.mutation.SetGeneratedDocumentIntegrationID(v)
	return _c
}

// SetGeneratedDocumentType sets the "generated_document_type" field.
func (_c *GeneratedDocumentCreate) SetGeneratedDocumentType(v string) *GeneratedDocumentCreate {
	_c.mutation.SetGeneratedDocumentType(v)
	return _c
}

// SetID sets the "id" field.
func (_c *GeneratedDocumentCreate) SetID(v uuid.UUID) *GeneratedDocumentCreate {
	_c.mutation.SetID(v)
	return _c
}

// SetNillableID sets the "id" field if the given value is not nil.
func (_c *GeneratedDocumentCreate) SetNillableID(v *uuid.UUID) *GeneratedDocumentCreate {
	if v != nil {
		_c.SetID(*v)
	}
	return _c
}

// Mutation returns the GeneratedDocumentMutation object of the builder.
func (_c *GeneratedDocumentCreate) Mutation() *GeneratedDocumentMutation {
	return _c.mutation
}

// Save creates the GeneratedDocument in the database.
func (_c *GeneratedDocumentCreate) Save(ctx context.Context) (*GeneratedDocument, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *GeneratedDocumentCreate) SaveX(ctx context.Context) *GeneratedDocument {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *GeneratedDocumentCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *GeneratedDocumentCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *GeneratedDocumentCreate) defaults() {
	if _, ok := _c.mutation.CreateTime(); !ok {
		v := generateddocument.DefaultCreateTime()
		_c.mutation.SetCreateTime(v)
	}
	if _, ok := _c.mutation.UpdateTime(); !ok {
		v := generateddocument.DefaultUpdateTime()
		_c.mutation.SetUpdateTime(v)
	}
	if _, ok := _c.mutation.ID(); !ok {
		v := generateddocument.DefaultID()
		_c.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *GeneratedDocumentCreate) check() error {
	if _, ok := _c.mutation.ParentTransactionID(); !ok {
		return &ValidationError{Name: "parent_transaction_id", err: errors.New(`ent: missing required field "GeneratedDocument.parent_transaction_id"`)}
	}
	if _, ok := _c.mutation.GeneratedDocumentIntegrationID(); !ok {
		return &ValidationError{Name: "generated_document_integration_id", err: errors.New(`ent: missing required field "GeneratedDocument.generated_document_integration_id"`)}
	}
	if _, ok := _c.mutation.GeneratedDocumentType(); !ok {
		return &ValidationError{Name: "generated_document_type", err: errors.New(`ent: missing required field "GeneratedDocument.generated_document_type"`)}
	}
	return nil
}

func (_c *GeneratedDocumentCreate) sqlSave(ctx context.Context) (*GeneratedDocument, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *GeneratedDocumentCreate) createSpec() (*GeneratedDocument, *sqlgraph.CreateSpec) {
	var (
		_node = &GeneratedDocument{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(generateddocument.Table, sqlgraph.NewFieldSpec(generateddocument.FieldID, field.TypeUUID))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := _c.mutation.CreateTime(); ok {
		_spec.SetField(generateddocument.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = value
	}
	if value, ok := _c.mutation.UpdateTime(); ok {
		_spec.SetField(generateddocument.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = value
	}
	if value, ok := _c.mutation.ParentTransactionID(); ok {
		_spec.SetField(generateddocument.FieldParentTransactionID, field.TypeUUID, value)
		_node.ParentTransactionID = value
	}
	if value, ok := _c.mutation.GeneratedDocumentIntegrationID(); ok {
		_spec.SetField(generateddocument.FieldGeneratedDocumentIntegrationID, field.TypeUUID, value)
		_node.GeneratedDocumentIntegrationID = value
	}
	if value, ok := _c.mutation.GeneratedDocumentType(); ok {
		_spec.SetField(generateddocument.FieldGeneratedDocumentType, field.TypeString, value)
		_node.GeneratedDocumentType = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.GeneratedDocument.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.GeneratedDocumentUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *GeneratedDocumentCreate) OnConflict(opts ...sql.ConflictOption) *GeneratedDocumentUpsertOne {
	_c.conflict = opts
	return &GeneratedDocumentUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.GeneratedDocument.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *GeneratedDocumentCreate) OnConflictColumns(columns ...string) *GeneratedDocumentUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &GeneratedDocumentUpsertOne{
		create: _c,
	}
}

type (
	// GeneratedDocumentUpsertOne is the builder for "upsert"-ing
	//  one GeneratedDocument node.
	GeneratedDocumentUpsertOne struct {
		create *GeneratedDocumentCreate
	}

	// GeneratedDocumentUpsert is the "OnConflict" setter.
	GeneratedDocumentUpsert struct {
		*sql.UpdateSet
	}
)

// SetParentTransactionID sets the "parent_transaction_id" field.
func (u *GeneratedDocumentUpsert) SetParentTransactionID(v uuid.UUID) *GeneratedDocumentUpsert {
	u.Set(generateddocument.FieldParentTransactionID, v)
	return u
}

// UpdateParentTransactionID sets the "parent_transaction_id" field to the value that was provided on create.
func (u *GeneratedDocumentUpsert) UpdateParentTransactionID() *GeneratedDocumentUpsert {
	u.SetExcluded(generateddocument.FieldParentTransactionID)
	return u
}

// SetGeneratedDocumentIntegrationID sets the "generated_document_integration_id" field.
func (u *GeneratedDocumentUpsert) SetGeneratedDocumentIntegrationID(v uuid.UUID) *GeneratedDocumentUpsert {
	u.Set(generateddocument.FieldGeneratedDocumentIntegrationID, v)
	return u
}

// UpdateGeneratedDocumentIntegrationID sets the "generated_document_integration_id" field to the value that was provided on create.
func (u *GeneratedDocumentUpsert) UpdateGeneratedDocumentIntegrationID() *GeneratedDocumentUpsert {
	u.SetExcluded(generateddocument.FieldGeneratedDocumentIntegrationID)
	return u
}

// SetGeneratedDocumentType sets the "generated_document_type" field.
func (u *GeneratedDocumentUpsert) SetGeneratedDocumentType(v string) *GeneratedDocumentUpsert {
	u.Set(generateddocument.FieldGeneratedDocumentType, v)
	return u
}

// UpdateGeneratedDocumentType sets the "generated_document_type" field to the value that was provided on create.
func (u *GeneratedDocumentUpsert) UpdateGeneratedDocumentType() *GeneratedDocumentUpsert {
	u.SetExcluded(generateddocument.FieldGeneratedDocumentType)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.GeneratedDocument.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(generateddocument.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *GeneratedDocumentUpsertOne) UpdateNewValues() *GeneratedDocumentUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(generateddocument.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(generateddocument.FieldCreateTime)
		}
		if _, exists := u.create.mutation.UpdateTime(); exists {
			s.SetIgnore(generateddocument.FieldUpdateTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.GeneratedDocument.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *GeneratedDocumentUpsertOne) Ignore() *GeneratedDocumentUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *GeneratedDocumentUpsertOne) DoNothing() *GeneratedDocumentUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the GeneratedDocumentCreate.OnConflict
// documentation for more info.
func (u *GeneratedDocumentUpsertOne) Update(set func(*GeneratedDocumentUpsert)) *GeneratedDocumentUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&GeneratedDocumentUpsert{UpdateSet: update})
	}))
	return u
}

// SetParentTransactionID sets the "parent_transaction_id" field.
func (u *GeneratedDocumentUpsertOne) SetParentTransactionID(v uuid.UUID) *GeneratedDocumentUpsertOne {
	return u.Update(func(s *GeneratedDocumentUpsert) {
		s.SetParentTransactionID(v)
	})
}

// UpdateParentTransactionID sets the "parent_transaction_id" field to the value that was provided on create.
func (u *GeneratedDocumentUpsertOne) UpdateParentTransactionID() *GeneratedDocumentUpsertOne {
	return u.Update(func(s *GeneratedDocumentUpsert) {
		s.UpdateParentTransactionID()
	})
}

// SetGeneratedDocumentIntegrationID sets the "generated_document_integration_id" field.
func (u *GeneratedDocumentUpsertOne) SetGeneratedDocumentIntegrationID(v uuid.UUID) *GeneratedDocumentUpsertOne {
	return u.Update(func(s *GeneratedDocumentUpsert) {
		s.SetGeneratedDocumentIntegrationID(v)
	})
}

// UpdateGeneratedDocumentIntegrationID sets the "generated_document_integration_id" field to the value that was provided on create.
func (u *GeneratedDocumentUpsertOne) UpdateGeneratedDocumentIntegrationID() *GeneratedDocumentUpsertOne {
	return u.Update(func(s *GeneratedDocumentUpsert) {
		s.UpdateGeneratedDocumentIntegrationID()
	})
}

// SetGeneratedDocumentType sets the "generated_document_type" field.
func (u *GeneratedDocumentUpsertOne) SetGeneratedDocumentType(v string) *GeneratedDocumentUpsertOne {
	return u.Update(func(s *GeneratedDocumentUpsert) {
		s.SetGeneratedDocumentType(v)
	})
}

// UpdateGeneratedDocumentType sets the "generated_document_type" field to the value that was provided on create.
func (u *GeneratedDocumentUpsertOne) UpdateGeneratedDocumentType() *GeneratedDocumentUpsertOne {
	return u.Update(func(s *GeneratedDocumentUpsert) {
		s.UpdateGeneratedDocumentType()
	})
}

// Exec executes the query.
func (u *GeneratedDocumentUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for GeneratedDocumentCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *GeneratedDocumentUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *GeneratedDocumentUpsertOne) ID(ctx context.Context) (id uuid.UUID, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: GeneratedDocumentUpsertOne.ID is not supported by MySQL driver. Use GeneratedDocumentUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *GeneratedDocumentUpsertOne) IDX(ctx context.Context) uuid.UUID {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// GeneratedDocumentCreateBulk is the builder for creating many GeneratedDocument entities in bulk.
type GeneratedDocumentCreateBulk struct {
	config
	err      error
	builders []*GeneratedDocumentCreate
	conflict []sql.ConflictOption
}

// Save creates the GeneratedDocument entities in the database.
func (_c *GeneratedDocumentCreateBulk) Save(ctx context.Context) ([]*GeneratedDocument, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*GeneratedDocument, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*GeneratedDocumentMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *GeneratedDocumentCreateBulk) SaveX(ctx context.Context) []*GeneratedDocument {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *GeneratedDocumentCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *GeneratedDocumentCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.GeneratedDocument.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.GeneratedDocumentUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *GeneratedDocumentCreateBulk) OnConflict(opts ...sql.ConflictOption) *GeneratedDocumentUpsertBulk {
	_c.conflict = opts
	return &GeneratedDocumentUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.GeneratedDocument.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *GeneratedDocumentCreateBulk) OnConflictColumns(columns ...string) *GeneratedDocumentUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &GeneratedDocumentUpsertBulk{
		create: _c,
	}
}

// GeneratedDocumentUpsertBulk is the builder for "upsert"-ing
// a bulk of GeneratedDocument nodes.
type GeneratedDocumentUpsertBulk struct {
	create *GeneratedDocumentCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.GeneratedDocument.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(generateddocument.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *GeneratedDocumentUpsertBulk) UpdateNewValues() *GeneratedDocumentUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(generateddocument.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(generateddocument.FieldCreateTime)
			}
			if _, exists := b.mutation.UpdateTime(); exists {
				s.SetIgnore(generateddocument.FieldUpdateTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.GeneratedDocument.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *GeneratedDocumentUpsertBulk) Ignore() *GeneratedDocumentUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *GeneratedDocumentUpsertBulk) DoNothing() *GeneratedDocumentUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the GeneratedDocumentCreateBulk.OnConflict
// documentation for more info.
func (u *GeneratedDocumentUpsertBulk) Update(set func(*GeneratedDocumentUpsert)) *GeneratedDocumentUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&GeneratedDocumentUpsert{UpdateSet: update})
	}))
	return u
}

// SetParentTransactionID sets the "parent_transaction_id" field.
func (u *GeneratedDocumentUpsertBulk) SetParentTransactionID(v uuid.UUID) *GeneratedDocumentUpsertBulk {
	return u.Update(func(s *GeneratedDocumentUpsert) {
		s.SetParentTransactionID(v)
	})
}

// UpdateParentTransactionID sets the "parent_transaction_id" field to the value that was provided on create.
func (u *GeneratedDocumentUpsertBulk) UpdateParentTransactionID() *GeneratedDocumentUpsertBulk {
	return u.Update(func(s *GeneratedDocumentUpsert) {
		s.UpdateParentTransactionID()
	})
}

// SetGeneratedDocumentIntegrationID sets the "generated_document_integration_id" field.
func (u *GeneratedDocumentUpsertBulk) SetGeneratedDocumentIntegrationID(v uuid.UUID) *GeneratedDocumentUpsertBulk {
	return u.Update(func(s *GeneratedDocumentUpsert) {
		s.SetGeneratedDocumentIntegrationID(v)
	})
}

// UpdateGeneratedDocumentIntegrationID sets the "generated_document_integration_id" field to the value that was provided on create.
func (u *GeneratedDocumentUpsertBulk) UpdateGeneratedDocumentIntegrationID() *GeneratedDocumentUpsertBulk {
	return u.Update(func(s *GeneratedDocumentUpsert) {
		s.UpdateGeneratedDocumentIntegrationID()
	})
}

// SetGeneratedDocumentType sets the "generated_document_type" field.
func (u *GeneratedDocumentUpsertBulk) SetGeneratedDocumentType(v string) *GeneratedDocumentUpsertBulk {
	return u.Update(func(s *GeneratedDocumentUpsert) {
		s.SetGeneratedDocumentType(v)
	})
}

// UpdateGeneratedDocumentType sets the "generated_document_type" field to the value that was provided on create.
func (u *GeneratedDocumentUpsertBulk) UpdateGeneratedDocumentType() *GeneratedDocumentUpsertBulk {
	return u.Update(func(s *GeneratedDocumentUpsert) {
		s.UpdateGeneratedDocumentType()
	})
}

// Exec executes the query.
func (u *GeneratedDocumentUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the GeneratedDocumentCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for GeneratedDocumentCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *GeneratedDocumentUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
