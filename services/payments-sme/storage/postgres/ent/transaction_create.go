// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/transaction"
)

// TransactionCreate is the builder for creating a Transaction entity.
type TransactionCreate struct {
	config
	mutation *TransactionMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (_c *TransactionCreate) SetCreateTime(v time.Time) *TransactionCreate {
	_c.mutation.SetCreateTime(v)
	return _c
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (_c *TransactionCreate) SetNillableCreateTime(v *time.Time) *TransactionCreate {
	if v != nil {
		_c.SetCreateTime(*v)
	}
	return _c
}

// SetUpdateTime sets the "update_time" field.
func (_c *TransactionCreate) SetUpdateTime(v time.Time) *TransactionCreate {
	_c.mutation.SetUpdateTime(v)
	return _c
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (_c *TransactionCreate) SetNillableUpdateTime(v *time.Time) *TransactionCreate {
	if v != nil {
		_c.SetUpdateTime(*v)
	}
	return _c
}

// SetTransactionNumber sets the "transaction_number" field.
func (_c *TransactionCreate) SetTransactionNumber(v string) *TransactionCreate {
	_c.mutation.SetTransactionNumber(v)
	return _c
}

// SetTransactionDate sets the "transaction_date" field.
func (_c *TransactionCreate) SetTransactionDate(v time.Time) *TransactionCreate {
	_c.mutation.SetTransactionDate(v)
	return _c
}

// SetNillableTransactionDate sets the "transaction_date" field if the given value is not nil.
func (_c *TransactionCreate) SetNillableTransactionDate(v *time.Time) *TransactionCreate {
	if v != nil {
		_c.SetTransactionDate(*v)
	}
	return _c
}

// SetTransactionType sets the "transaction_type" field.
func (_c *TransactionCreate) SetTransactionType(v transaction.TransactionType) *TransactionCreate {
	_c.mutation.SetTransactionType(v)
	return _c
}

// SetInitiatorID sets the "initiator_id" field.
func (_c *TransactionCreate) SetInitiatorID(v string) *TransactionCreate {
	_c.mutation.SetInitiatorID(v)
	return _c
}

// SetIdempotencyKey sets the "idempotency_key" field.
func (_c *TransactionCreate) SetIdempotencyKey(v string) *TransactionCreate {
	_c.mutation.SetIdempotencyKey(v)
	return _c
}

// SetValueDate sets the "value_date" field.
func (_c *TransactionCreate) SetValueDate(v string) *TransactionCreate {
	_c.mutation.SetValueDate(v)
	return _c
}

// SetNillableValueDate sets the "value_date" field if the given value is not nil.
func (_c *TransactionCreate) SetNillableValueDate(v *string) *TransactionCreate {
	if v != nil {
		_c.SetValueDate(*v)
	}
	return _c
}

// SetTransactionStatus sets the "transaction_status" field.
func (_c *TransactionCreate) SetTransactionStatus(v transaction.TransactionStatus) *TransactionCreate {
	_c.mutation.SetTransactionStatus(v)
	return _c
}

// SetTransactionAmount sets the "transaction_amount" field.
func (_c *TransactionCreate) SetTransactionAmount(v string) *TransactionCreate {
	_c.mutation.SetTransactionAmount(v)
	return _c
}

// SetTransactionComission sets the "transaction_comission" field.
func (_c *TransactionCreate) SetTransactionComission(v string) *TransactionCreate {
	_c.mutation.SetTransactionComission(v)
	return _c
}

// SetNillableTransactionComission sets the "transaction_comission" field if the given value is not nil.
func (_c *TransactionCreate) SetNillableTransactionComission(v *string) *TransactionCreate {
	if v != nil {
		_c.SetTransactionComission(*v)
	}
	return _c
}

// SetTransactionCurrency sets the "transaction_currency" field.
func (_c *TransactionCreate) SetTransactionCurrency(v transaction.TransactionCurrency) *TransactionCreate {
	_c.mutation.SetTransactionCurrency(v)
	return _c
}

// SetTransactionTotalAmount sets the "transaction_total_amount" field.
func (_c *TransactionCreate) SetTransactionTotalAmount(v string) *TransactionCreate {
	_c.mutation.SetTransactionTotalAmount(v)
	return _c
}

// SetTransactionDirection sets the "transaction_direction" field.
func (_c *TransactionCreate) SetTransactionDirection(v transaction.TransactionDirection) *TransactionCreate {
	_c.mutation.SetTransactionDirection(v)
	return _c
}

// SetNillableTransactionDirection sets the "transaction_direction" field if the given value is not nil.
func (_c *TransactionCreate) SetNillableTransactionDirection(v *transaction.TransactionDirection) *TransactionCreate {
	if v != nil {
		_c.SetTransactionDirection(*v)
	}
	return _c
}

// SetPurposeCode sets the "purpose_code" field.
func (_c *TransactionCreate) SetPurposeCode(v string) *TransactionCreate {
	_c.mutation.SetPurposeCode(v)
	return _c
}

// SetNillablePurposeCode sets the "purpose_code" field if the given value is not nil.
func (_c *TransactionCreate) SetNillablePurposeCode(v *string) *TransactionCreate {
	if v != nil {
		_c.SetPurposeCode(*v)
	}
	return _c
}

// SetPurposeDetails sets the "purpose_details" field.
func (_c *TransactionCreate) SetPurposeDetails(v string) *TransactionCreate {
	_c.mutation.SetPurposeDetails(v)
	return _c
}

// SetNillablePurposeDetails sets the "purpose_details" field if the given value is not nil.
func (_c *TransactionCreate) SetNillablePurposeDetails(v *string) *TransactionCreate {
	if v != nil {
		_c.SetPurposeDetails(*v)
	}
	return _c
}

// SetPayerKod sets the "payer_kod" field.
func (_c *TransactionCreate) SetPayerKod(v string) *TransactionCreate {
	_c.mutation.SetPayerKod(v)
	return _c
}

// SetNillablePayerKod sets the "payer_kod" field if the given value is not nil.
func (_c *TransactionCreate) SetNillablePayerKod(v *string) *TransactionCreate {
	if v != nil {
		_c.SetPayerKod(*v)
	}
	return _c
}

// SetPayerBinIin sets the "payer_bin_iin" field.
func (_c *TransactionCreate) SetPayerBinIin(v string) *TransactionCreate {
	_c.mutation.SetPayerBinIin(v)
	return _c
}

// SetNillablePayerBinIin sets the "payer_bin_iin" field if the given value is not nil.
func (_c *TransactionCreate) SetNillablePayerBinIin(v *string) *TransactionCreate {
	if v != nil {
		_c.SetPayerBinIin(*v)
	}
	return _c
}

// SetPayerName sets the "payer_name" field.
func (_c *TransactionCreate) SetPayerName(v string) *TransactionCreate {
	_c.mutation.SetPayerName(v)
	return _c
}

// SetNillablePayerName sets the "payer_name" field if the given value is not nil.
func (_c *TransactionCreate) SetNillablePayerName(v *string) *TransactionCreate {
	if v != nil {
		_c.SetPayerName(*v)
	}
	return _c
}

// SetPayerType sets the "payer_type" field.
func (_c *TransactionCreate) SetPayerType(v transaction.PayerType) *TransactionCreate {
	_c.mutation.SetPayerType(v)
	return _c
}

// SetNillablePayerType sets the "payer_type" field if the given value is not nil.
func (_c *TransactionCreate) SetNillablePayerType(v *transaction.PayerType) *TransactionCreate {
	if v != nil {
		_c.SetPayerType(*v)
	}
	return _c
}

// SetPayerAccountIban sets the "payer_account_iban" field.
func (_c *TransactionCreate) SetPayerAccountIban(v string) *TransactionCreate {
	_c.mutation.SetPayerAccountIban(v)
	return _c
}

// SetNillablePayerAccountIban sets the "payer_account_iban" field if the given value is not nil.
func (_c *TransactionCreate) SetNillablePayerAccountIban(v *string) *TransactionCreate {
	if v != nil {
		_c.SetPayerAccountIban(*v)
	}
	return _c
}

// SetPayerBankBic sets the "payer_bank_bic" field.
func (_c *TransactionCreate) SetPayerBankBic(v string) *TransactionCreate {
	_c.mutation.SetPayerBankBic(v)
	return _c
}

// SetNillablePayerBankBic sets the "payer_bank_bic" field if the given value is not nil.
func (_c *TransactionCreate) SetNillablePayerBankBic(v *string) *TransactionCreate {
	if v != nil {
		_c.SetPayerBankBic(*v)
	}
	return _c
}

// SetPayerBankName sets the "payer_bank_name" field.
func (_c *TransactionCreate) SetPayerBankName(v string) *TransactionCreate {
	_c.mutation.SetPayerBankName(v)
	return _c
}

// SetNillablePayerBankName sets the "payer_bank_name" field if the given value is not nil.
func (_c *TransactionCreate) SetNillablePayerBankName(v *string) *TransactionCreate {
	if v != nil {
		_c.SetPayerBankName(*v)
	}
	return _c
}

// SetPayerIsoCountryCode sets the "payer_iso_country_code" field.
func (_c *TransactionCreate) SetPayerIsoCountryCode(v string) *TransactionCreate {
	_c.mutation.SetPayerIsoCountryCode(v)
	return _c
}

// SetNillablePayerIsoCountryCode sets the "payer_iso_country_code" field if the given value is not nil.
func (_c *TransactionCreate) SetNillablePayerIsoCountryCode(v *string) *TransactionCreate {
	if v != nil {
		_c.SetPayerIsoCountryCode(*v)
	}
	return _c
}

// SetRealPayerName sets the "real_payer_name" field.
func (_c *TransactionCreate) SetRealPayerName(v string) *TransactionCreate {
	_c.mutation.SetRealPayerName(v)
	return _c
}

// SetNillableRealPayerName sets the "real_payer_name" field if the given value is not nil.
func (_c *TransactionCreate) SetNillableRealPayerName(v *string) *TransactionCreate {
	if v != nil {
		_c.SetRealPayerName(*v)
	}
	return _c
}

// SetRealPayerBinIin sets the "real_payer_bin_iin" field.
func (_c *TransactionCreate) SetRealPayerBinIin(v string) *TransactionCreate {
	_c.mutation.SetRealPayerBinIin(v)
	return _c
}

// SetNillableRealPayerBinIin sets the "real_payer_bin_iin" field if the given value is not nil.
func (_c *TransactionCreate) SetNillableRealPayerBinIin(v *string) *TransactionCreate {
	if v != nil {
		_c.SetRealPayerBinIin(*v)
	}
	return _c
}

// SetRealPayerIsoCountryCode sets the "real_payer_iso_country_code" field.
func (_c *TransactionCreate) SetRealPayerIsoCountryCode(v string) *TransactionCreate {
	_c.mutation.SetRealPayerIsoCountryCode(v)
	return _c
}

// SetNillableRealPayerIsoCountryCode sets the "real_payer_iso_country_code" field if the given value is not nil.
func (_c *TransactionCreate) SetNillableRealPayerIsoCountryCode(v *string) *TransactionCreate {
	if v != nil {
		_c.SetRealPayerIsoCountryCode(*v)
	}
	return _c
}

// SetRealPayerType sets the "real_payer_type" field.
func (_c *TransactionCreate) SetRealPayerType(v transaction.RealPayerType) *TransactionCreate {
	_c.mutation.SetRealPayerType(v)
	return _c
}

// SetNillableRealPayerType sets the "real_payer_type" field if the given value is not nil.
func (_c *TransactionCreate) SetNillableRealPayerType(v *transaction.RealPayerType) *TransactionCreate {
	if v != nil {
		_c.SetRealPayerType(*v)
	}
	return _c
}

// SetBeneficiaryKbe sets the "beneficiary_kbe" field.
func (_c *TransactionCreate) SetBeneficiaryKbe(v string) *TransactionCreate {
	_c.mutation.SetBeneficiaryKbe(v)
	return _c
}

// SetNillableBeneficiaryKbe sets the "beneficiary_kbe" field if the given value is not nil.
func (_c *TransactionCreate) SetNillableBeneficiaryKbe(v *string) *TransactionCreate {
	if v != nil {
		_c.SetBeneficiaryKbe(*v)
	}
	return _c
}

// SetBeneficiaryBinIin sets the "beneficiary_bin_iin" field.
func (_c *TransactionCreate) SetBeneficiaryBinIin(v string) *TransactionCreate {
	_c.mutation.SetBeneficiaryBinIin(v)
	return _c
}

// SetNillableBeneficiaryBinIin sets the "beneficiary_bin_iin" field if the given value is not nil.
func (_c *TransactionCreate) SetNillableBeneficiaryBinIin(v *string) *TransactionCreate {
	if v != nil {
		_c.SetBeneficiaryBinIin(*v)
	}
	return _c
}

// SetBeneficiaryName sets the "beneficiary_name" field.
func (_c *TransactionCreate) SetBeneficiaryName(v string) *TransactionCreate {
	_c.mutation.SetBeneficiaryName(v)
	return _c
}

// SetNillableBeneficiaryName sets the "beneficiary_name" field if the given value is not nil.
func (_c *TransactionCreate) SetNillableBeneficiaryName(v *string) *TransactionCreate {
	if v != nil {
		_c.SetBeneficiaryName(*v)
	}
	return _c
}

// SetBeneficiaryType sets the "beneficiary_type" field.
func (_c *TransactionCreate) SetBeneficiaryType(v transaction.BeneficiaryType) *TransactionCreate {
	_c.mutation.SetBeneficiaryType(v)
	return _c
}

// SetNillableBeneficiaryType sets the "beneficiary_type" field if the given value is not nil.
func (_c *TransactionCreate) SetNillableBeneficiaryType(v *transaction.BeneficiaryType) *TransactionCreate {
	if v != nil {
		_c.SetBeneficiaryType(*v)
	}
	return _c
}

// SetBeneficiaryAccountIban sets the "beneficiary_account_iban" field.
func (_c *TransactionCreate) SetBeneficiaryAccountIban(v string) *TransactionCreate {
	_c.mutation.SetBeneficiaryAccountIban(v)
	return _c
}

// SetNillableBeneficiaryAccountIban sets the "beneficiary_account_iban" field if the given value is not nil.
func (_c *TransactionCreate) SetNillableBeneficiaryAccountIban(v *string) *TransactionCreate {
	if v != nil {
		_c.SetBeneficiaryAccountIban(*v)
	}
	return _c
}

// SetBeneficiaryBankBic sets the "beneficiary_bank_bic" field.
func (_c *TransactionCreate) SetBeneficiaryBankBic(v string) *TransactionCreate {
	_c.mutation.SetBeneficiaryBankBic(v)
	return _c
}

// SetNillableBeneficiaryBankBic sets the "beneficiary_bank_bic" field if the given value is not nil.
func (_c *TransactionCreate) SetNillableBeneficiaryBankBic(v *string) *TransactionCreate {
	if v != nil {
		_c.SetBeneficiaryBankBic(*v)
	}
	return _c
}

// SetBeneficiaryBankName sets the "beneficiary_bank_name" field.
func (_c *TransactionCreate) SetBeneficiaryBankName(v string) *TransactionCreate {
	_c.mutation.SetBeneficiaryBankName(v)
	return _c
}

// SetNillableBeneficiaryBankName sets the "beneficiary_bank_name" field if the given value is not nil.
func (_c *TransactionCreate) SetNillableBeneficiaryBankName(v *string) *TransactionCreate {
	if v != nil {
		_c.SetBeneficiaryBankName(*v)
	}
	return _c
}

// SetBeneficiaryIsoCountryCode sets the "beneficiary_iso_country_code" field.
func (_c *TransactionCreate) SetBeneficiaryIsoCountryCode(v string) *TransactionCreate {
	_c.mutation.SetBeneficiaryIsoCountryCode(v)
	return _c
}

// SetNillableBeneficiaryIsoCountryCode sets the "beneficiary_iso_country_code" field if the given value is not nil.
func (_c *TransactionCreate) SetNillableBeneficiaryIsoCountryCode(v *string) *TransactionCreate {
	if v != nil {
		_c.SetBeneficiaryIsoCountryCode(*v)
	}
	return _c
}

// SetRealBeneficiaryName sets the "real_beneficiary_name" field.
func (_c *TransactionCreate) SetRealBeneficiaryName(v string) *TransactionCreate {
	_c.mutation.SetRealBeneficiaryName(v)
	return _c
}

// SetNillableRealBeneficiaryName sets the "real_beneficiary_name" field if the given value is not nil.
func (_c *TransactionCreate) SetNillableRealBeneficiaryName(v *string) *TransactionCreate {
	if v != nil {
		_c.SetRealBeneficiaryName(*v)
	}
	return _c
}

// SetRealBeneficiaryBinIin sets the "real_beneficiary_bin_iin" field.
func (_c *TransactionCreate) SetRealBeneficiaryBinIin(v string) *TransactionCreate {
	_c.mutation.SetRealBeneficiaryBinIin(v)
	return _c
}

// SetNillableRealBeneficiaryBinIin sets the "real_beneficiary_bin_iin" field if the given value is not nil.
func (_c *TransactionCreate) SetNillableRealBeneficiaryBinIin(v *string) *TransactionCreate {
	if v != nil {
		_c.SetRealBeneficiaryBinIin(*v)
	}
	return _c
}

// SetRealBeneficiaryCountryCode sets the "real_beneficiary_country_code" field.
func (_c *TransactionCreate) SetRealBeneficiaryCountryCode(v string) *TransactionCreate {
	_c.mutation.SetRealBeneficiaryCountryCode(v)
	return _c
}

// SetNillableRealBeneficiaryCountryCode sets the "real_beneficiary_country_code" field if the given value is not nil.
func (_c *TransactionCreate) SetNillableRealBeneficiaryCountryCode(v *string) *TransactionCreate {
	if v != nil {
		_c.SetRealBeneficiaryCountryCode(*v)
	}
	return _c
}

// SetRealBeneficiaryType sets the "real_beneficiary_type" field.
func (_c *TransactionCreate) SetRealBeneficiaryType(v transaction.RealBeneficiaryType) *TransactionCreate {
	_c.mutation.SetRealBeneficiaryType(v)
	return _c
}

// SetNillableRealBeneficiaryType sets the "real_beneficiary_type" field if the given value is not nil.
func (_c *TransactionCreate) SetNillableRealBeneficiaryType(v *transaction.RealBeneficiaryType) *TransactionCreate {
	if v != nil {
		_c.SetRealBeneficiaryType(*v)
	}
	return _c
}

// SetID sets the "id" field.
func (_c *TransactionCreate) SetID(v uuid.UUID) *TransactionCreate {
	_c.mutation.SetID(v)
	return _c
}

// SetNillableID sets the "id" field if the given value is not nil.
func (_c *TransactionCreate) SetNillableID(v *uuid.UUID) *TransactionCreate {
	if v != nil {
		_c.SetID(*v)
	}
	return _c
}

// Mutation returns the TransactionMutation object of the builder.
func (_c *TransactionCreate) Mutation() *TransactionMutation {
	return _c.mutation
}

// Save creates the Transaction in the database.
func (_c *TransactionCreate) Save(ctx context.Context) (*Transaction, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *TransactionCreate) SaveX(ctx context.Context) *Transaction {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *TransactionCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *TransactionCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *TransactionCreate) defaults() {
	if _, ok := _c.mutation.CreateTime(); !ok {
		v := transaction.DefaultCreateTime()
		_c.mutation.SetCreateTime(v)
	}
	if _, ok := _c.mutation.UpdateTime(); !ok {
		v := transaction.DefaultUpdateTime()
		_c.mutation.SetUpdateTime(v)
	}
	if _, ok := _c.mutation.TransactionDate(); !ok {
		v := transaction.DefaultTransactionDate()
		_c.mutation.SetTransactionDate(v)
	}
	if _, ok := _c.mutation.ID(); !ok {
		v := transaction.DefaultID()
		_c.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *TransactionCreate) check() error {
	if _, ok := _c.mutation.TransactionNumber(); !ok {
		return &ValidationError{Name: "transaction_number", err: errors.New(`ent: missing required field "Transaction.transaction_number"`)}
	}
	if _, ok := _c.mutation.TransactionDate(); !ok {
		return &ValidationError{Name: "transaction_date", err: errors.New(`ent: missing required field "Transaction.transaction_date"`)}
	}
	if _, ok := _c.mutation.TransactionType(); !ok {
		return &ValidationError{Name: "transaction_type", err: errors.New(`ent: missing required field "Transaction.transaction_type"`)}
	}
	if v, ok := _c.mutation.TransactionType(); ok {
		if err := transaction.TransactionTypeValidator(v); err != nil {
			return &ValidationError{Name: "transaction_type", err: fmt.Errorf(`ent: validator failed for field "Transaction.transaction_type": %w`, err)}
		}
	}
	if _, ok := _c.mutation.InitiatorID(); !ok {
		return &ValidationError{Name: "initiator_id", err: errors.New(`ent: missing required field "Transaction.initiator_id"`)}
	}
	if _, ok := _c.mutation.IdempotencyKey(); !ok {
		return &ValidationError{Name: "idempotency_key", err: errors.New(`ent: missing required field "Transaction.idempotency_key"`)}
	}
	if _, ok := _c.mutation.TransactionStatus(); !ok {
		return &ValidationError{Name: "transaction_status", err: errors.New(`ent: missing required field "Transaction.transaction_status"`)}
	}
	if v, ok := _c.mutation.TransactionStatus(); ok {
		if err := transaction.TransactionStatusValidator(v); err != nil {
			return &ValidationError{Name: "transaction_status", err: fmt.Errorf(`ent: validator failed for field "Transaction.transaction_status": %w`, err)}
		}
	}
	if _, ok := _c.mutation.TransactionAmount(); !ok {
		return &ValidationError{Name: "transaction_amount", err: errors.New(`ent: missing required field "Transaction.transaction_amount"`)}
	}
	if _, ok := _c.mutation.TransactionCurrency(); !ok {
		return &ValidationError{Name: "transaction_currency", err: errors.New(`ent: missing required field "Transaction.transaction_currency"`)}
	}
	if v, ok := _c.mutation.TransactionCurrency(); ok {
		if err := transaction.TransactionCurrencyValidator(v); err != nil {
			return &ValidationError{Name: "transaction_currency", err: fmt.Errorf(`ent: validator failed for field "Transaction.transaction_currency": %w`, err)}
		}
	}
	if _, ok := _c.mutation.TransactionTotalAmount(); !ok {
		return &ValidationError{Name: "transaction_total_amount", err: errors.New(`ent: missing required field "Transaction.transaction_total_amount"`)}
	}
	if v, ok := _c.mutation.TransactionDirection(); ok {
		if err := transaction.TransactionDirectionValidator(v); err != nil {
			return &ValidationError{Name: "transaction_direction", err: fmt.Errorf(`ent: validator failed for field "Transaction.transaction_direction": %w`, err)}
		}
	}
	if v, ok := _c.mutation.PayerType(); ok {
		if err := transaction.PayerTypeValidator(v); err != nil {
			return &ValidationError{Name: "payer_type", err: fmt.Errorf(`ent: validator failed for field "Transaction.payer_type": %w`, err)}
		}
	}
	if v, ok := _c.mutation.RealPayerType(); ok {
		if err := transaction.RealPayerTypeValidator(v); err != nil {
			return &ValidationError{Name: "real_payer_type", err: fmt.Errorf(`ent: validator failed for field "Transaction.real_payer_type": %w`, err)}
		}
	}
	if v, ok := _c.mutation.BeneficiaryType(); ok {
		if err := transaction.BeneficiaryTypeValidator(v); err != nil {
			return &ValidationError{Name: "beneficiary_type", err: fmt.Errorf(`ent: validator failed for field "Transaction.beneficiary_type": %w`, err)}
		}
	}
	if v, ok := _c.mutation.RealBeneficiaryType(); ok {
		if err := transaction.RealBeneficiaryTypeValidator(v); err != nil {
			return &ValidationError{Name: "real_beneficiary_type", err: fmt.Errorf(`ent: validator failed for field "Transaction.real_beneficiary_type": %w`, err)}
		}
	}
	return nil
}

func (_c *TransactionCreate) sqlSave(ctx context.Context) (*Transaction, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *TransactionCreate) createSpec() (*Transaction, *sqlgraph.CreateSpec) {
	var (
		_node = &Transaction{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(transaction.Table, sqlgraph.NewFieldSpec(transaction.FieldID, field.TypeUUID))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := _c.mutation.CreateTime(); ok {
		_spec.SetField(transaction.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = value
	}
	if value, ok := _c.mutation.UpdateTime(); ok {
		_spec.SetField(transaction.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = value
	}
	if value, ok := _c.mutation.TransactionNumber(); ok {
		_spec.SetField(transaction.FieldTransactionNumber, field.TypeString, value)
		_node.TransactionNumber = value
	}
	if value, ok := _c.mutation.TransactionDate(); ok {
		_spec.SetField(transaction.FieldTransactionDate, field.TypeTime, value)
		_node.TransactionDate = value
	}
	if value, ok := _c.mutation.TransactionType(); ok {
		_spec.SetField(transaction.FieldTransactionType, field.TypeEnum, value)
		_node.TransactionType = value
	}
	if value, ok := _c.mutation.InitiatorID(); ok {
		_spec.SetField(transaction.FieldInitiatorID, field.TypeString, value)
		_node.InitiatorID = value
	}
	if value, ok := _c.mutation.IdempotencyKey(); ok {
		_spec.SetField(transaction.FieldIdempotencyKey, field.TypeString, value)
		_node.IdempotencyKey = value
	}
	if value, ok := _c.mutation.ValueDate(); ok {
		_spec.SetField(transaction.FieldValueDate, field.TypeString, value)
		_node.ValueDate = &value
	}
	if value, ok := _c.mutation.TransactionStatus(); ok {
		_spec.SetField(transaction.FieldTransactionStatus, field.TypeEnum, value)
		_node.TransactionStatus = value
	}
	if value, ok := _c.mutation.TransactionAmount(); ok {
		_spec.SetField(transaction.FieldTransactionAmount, field.TypeString, value)
		_node.TransactionAmount = value
	}
	if value, ok := _c.mutation.TransactionComission(); ok {
		_spec.SetField(transaction.FieldTransactionComission, field.TypeString, value)
		_node.TransactionComission = &value
	}
	if value, ok := _c.mutation.TransactionCurrency(); ok {
		_spec.SetField(transaction.FieldTransactionCurrency, field.TypeEnum, value)
		_node.TransactionCurrency = value
	}
	if value, ok := _c.mutation.TransactionTotalAmount(); ok {
		_spec.SetField(transaction.FieldTransactionTotalAmount, field.TypeString, value)
		_node.TransactionTotalAmount = value
	}
	if value, ok := _c.mutation.TransactionDirection(); ok {
		_spec.SetField(transaction.FieldTransactionDirection, field.TypeEnum, value)
		_node.TransactionDirection = &value
	}
	if value, ok := _c.mutation.PurposeCode(); ok {
		_spec.SetField(transaction.FieldPurposeCode, field.TypeString, value)
		_node.PurposeCode = &value
	}
	if value, ok := _c.mutation.PurposeDetails(); ok {
		_spec.SetField(transaction.FieldPurposeDetails, field.TypeString, value)
		_node.PurposeDetails = &value
	}
	if value, ok := _c.mutation.PayerKod(); ok {
		_spec.SetField(transaction.FieldPayerKod, field.TypeString, value)
		_node.PayerKod = &value
	}
	if value, ok := _c.mutation.PayerBinIin(); ok {
		_spec.SetField(transaction.FieldPayerBinIin, field.TypeString, value)
		_node.PayerBinIin = &value
	}
	if value, ok := _c.mutation.PayerName(); ok {
		_spec.SetField(transaction.FieldPayerName, field.TypeString, value)
		_node.PayerName = &value
	}
	if value, ok := _c.mutation.PayerType(); ok {
		_spec.SetField(transaction.FieldPayerType, field.TypeEnum, value)
		_node.PayerType = &value
	}
	if value, ok := _c.mutation.PayerAccountIban(); ok {
		_spec.SetField(transaction.FieldPayerAccountIban, field.TypeString, value)
		_node.PayerAccountIban = &value
	}
	if value, ok := _c.mutation.PayerBankBic(); ok {
		_spec.SetField(transaction.FieldPayerBankBic, field.TypeString, value)
		_node.PayerBankBic = &value
	}
	if value, ok := _c.mutation.PayerBankName(); ok {
		_spec.SetField(transaction.FieldPayerBankName, field.TypeString, value)
		_node.PayerBankName = &value
	}
	if value, ok := _c.mutation.PayerIsoCountryCode(); ok {
		_spec.SetField(transaction.FieldPayerIsoCountryCode, field.TypeString, value)
		_node.PayerIsoCountryCode = &value
	}
	if value, ok := _c.mutation.RealPayerName(); ok {
		_spec.SetField(transaction.FieldRealPayerName, field.TypeString, value)
		_node.RealPayerName = &value
	}
	if value, ok := _c.mutation.RealPayerBinIin(); ok {
		_spec.SetField(transaction.FieldRealPayerBinIin, field.TypeString, value)
		_node.RealPayerBinIin = &value
	}
	if value, ok := _c.mutation.RealPayerIsoCountryCode(); ok {
		_spec.SetField(transaction.FieldRealPayerIsoCountryCode, field.TypeString, value)
		_node.RealPayerIsoCountryCode = &value
	}
	if value, ok := _c.mutation.RealPayerType(); ok {
		_spec.SetField(transaction.FieldRealPayerType, field.TypeEnum, value)
		_node.RealPayerType = &value
	}
	if value, ok := _c.mutation.BeneficiaryKbe(); ok {
		_spec.SetField(transaction.FieldBeneficiaryKbe, field.TypeString, value)
		_node.BeneficiaryKbe = &value
	}
	if value, ok := _c.mutation.BeneficiaryBinIin(); ok {
		_spec.SetField(transaction.FieldBeneficiaryBinIin, field.TypeString, value)
		_node.BeneficiaryBinIin = &value
	}
	if value, ok := _c.mutation.BeneficiaryName(); ok {
		_spec.SetField(transaction.FieldBeneficiaryName, field.TypeString, value)
		_node.BeneficiaryName = &value
	}
	if value, ok := _c.mutation.BeneficiaryType(); ok {
		_spec.SetField(transaction.FieldBeneficiaryType, field.TypeEnum, value)
		_node.BeneficiaryType = &value
	}
	if value, ok := _c.mutation.BeneficiaryAccountIban(); ok {
		_spec.SetField(transaction.FieldBeneficiaryAccountIban, field.TypeString, value)
		_node.BeneficiaryAccountIban = &value
	}
	if value, ok := _c.mutation.BeneficiaryBankBic(); ok {
		_spec.SetField(transaction.FieldBeneficiaryBankBic, field.TypeString, value)
		_node.BeneficiaryBankBic = &value
	}
	if value, ok := _c.mutation.BeneficiaryBankName(); ok {
		_spec.SetField(transaction.FieldBeneficiaryBankName, field.TypeString, value)
		_node.BeneficiaryBankName = &value
	}
	if value, ok := _c.mutation.BeneficiaryIsoCountryCode(); ok {
		_spec.SetField(transaction.FieldBeneficiaryIsoCountryCode, field.TypeString, value)
		_node.BeneficiaryIsoCountryCode = &value
	}
	if value, ok := _c.mutation.RealBeneficiaryName(); ok {
		_spec.SetField(transaction.FieldRealBeneficiaryName, field.TypeString, value)
		_node.RealBeneficiaryName = &value
	}
	if value, ok := _c.mutation.RealBeneficiaryBinIin(); ok {
		_spec.SetField(transaction.FieldRealBeneficiaryBinIin, field.TypeString, value)
		_node.RealBeneficiaryBinIin = &value
	}
	if value, ok := _c.mutation.RealBeneficiaryCountryCode(); ok {
		_spec.SetField(transaction.FieldRealBeneficiaryCountryCode, field.TypeString, value)
		_node.RealBeneficiaryCountryCode = &value
	}
	if value, ok := _c.mutation.RealBeneficiaryType(); ok {
		_spec.SetField(transaction.FieldRealBeneficiaryType, field.TypeEnum, value)
		_node.RealBeneficiaryType = &value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Transaction.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.TransactionUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *TransactionCreate) OnConflict(opts ...sql.ConflictOption) *TransactionUpsertOne {
	_c.conflict = opts
	return &TransactionUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Transaction.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *TransactionCreate) OnConflictColumns(columns ...string) *TransactionUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &TransactionUpsertOne{
		create: _c,
	}
}

type (
	// TransactionUpsertOne is the builder for "upsert"-ing
	//  one Transaction node.
	TransactionUpsertOne struct {
		create *TransactionCreate
	}

	// TransactionUpsert is the "OnConflict" setter.
	TransactionUpsert struct {
		*sql.UpdateSet
	}
)

// SetTransactionNumber sets the "transaction_number" field.
func (u *TransactionUpsert) SetTransactionNumber(v string) *TransactionUpsert {
	u.Set(transaction.FieldTransactionNumber, v)
	return u
}

// UpdateTransactionNumber sets the "transaction_number" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateTransactionNumber() *TransactionUpsert {
	u.SetExcluded(transaction.FieldTransactionNumber)
	return u
}

// SetTransactionDate sets the "transaction_date" field.
func (u *TransactionUpsert) SetTransactionDate(v time.Time) *TransactionUpsert {
	u.Set(transaction.FieldTransactionDate, v)
	return u
}

// UpdateTransactionDate sets the "transaction_date" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateTransactionDate() *TransactionUpsert {
	u.SetExcluded(transaction.FieldTransactionDate)
	return u
}

// SetTransactionType sets the "transaction_type" field.
func (u *TransactionUpsert) SetTransactionType(v transaction.TransactionType) *TransactionUpsert {
	u.Set(transaction.FieldTransactionType, v)
	return u
}

// UpdateTransactionType sets the "transaction_type" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateTransactionType() *TransactionUpsert {
	u.SetExcluded(transaction.FieldTransactionType)
	return u
}

// SetInitiatorID sets the "initiator_id" field.
func (u *TransactionUpsert) SetInitiatorID(v string) *TransactionUpsert {
	u.Set(transaction.FieldInitiatorID, v)
	return u
}

// UpdateInitiatorID sets the "initiator_id" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateInitiatorID() *TransactionUpsert {
	u.SetExcluded(transaction.FieldInitiatorID)
	return u
}

// SetIdempotencyKey sets the "idempotency_key" field.
func (u *TransactionUpsert) SetIdempotencyKey(v string) *TransactionUpsert {
	u.Set(transaction.FieldIdempotencyKey, v)
	return u
}

// UpdateIdempotencyKey sets the "idempotency_key" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateIdempotencyKey() *TransactionUpsert {
	u.SetExcluded(transaction.FieldIdempotencyKey)
	return u
}

// SetValueDate sets the "value_date" field.
func (u *TransactionUpsert) SetValueDate(v string) *TransactionUpsert {
	u.Set(transaction.FieldValueDate, v)
	return u
}

// UpdateValueDate sets the "value_date" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateValueDate() *TransactionUpsert {
	u.SetExcluded(transaction.FieldValueDate)
	return u
}

// ClearValueDate clears the value of the "value_date" field.
func (u *TransactionUpsert) ClearValueDate() *TransactionUpsert {
	u.SetNull(transaction.FieldValueDate)
	return u
}

// SetTransactionStatus sets the "transaction_status" field.
func (u *TransactionUpsert) SetTransactionStatus(v transaction.TransactionStatus) *TransactionUpsert {
	u.Set(transaction.FieldTransactionStatus, v)
	return u
}

// UpdateTransactionStatus sets the "transaction_status" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateTransactionStatus() *TransactionUpsert {
	u.SetExcluded(transaction.FieldTransactionStatus)
	return u
}

// SetTransactionAmount sets the "transaction_amount" field.
func (u *TransactionUpsert) SetTransactionAmount(v string) *TransactionUpsert {
	u.Set(transaction.FieldTransactionAmount, v)
	return u
}

// UpdateTransactionAmount sets the "transaction_amount" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateTransactionAmount() *TransactionUpsert {
	u.SetExcluded(transaction.FieldTransactionAmount)
	return u
}

// SetTransactionComission sets the "transaction_comission" field.
func (u *TransactionUpsert) SetTransactionComission(v string) *TransactionUpsert {
	u.Set(transaction.FieldTransactionComission, v)
	return u
}

// UpdateTransactionComission sets the "transaction_comission" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateTransactionComission() *TransactionUpsert {
	u.SetExcluded(transaction.FieldTransactionComission)
	return u
}

// ClearTransactionComission clears the value of the "transaction_comission" field.
func (u *TransactionUpsert) ClearTransactionComission() *TransactionUpsert {
	u.SetNull(transaction.FieldTransactionComission)
	return u
}

// SetTransactionCurrency sets the "transaction_currency" field.
func (u *TransactionUpsert) SetTransactionCurrency(v transaction.TransactionCurrency) *TransactionUpsert {
	u.Set(transaction.FieldTransactionCurrency, v)
	return u
}

// UpdateTransactionCurrency sets the "transaction_currency" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateTransactionCurrency() *TransactionUpsert {
	u.SetExcluded(transaction.FieldTransactionCurrency)
	return u
}

// SetTransactionTotalAmount sets the "transaction_total_amount" field.
func (u *TransactionUpsert) SetTransactionTotalAmount(v string) *TransactionUpsert {
	u.Set(transaction.FieldTransactionTotalAmount, v)
	return u
}

// UpdateTransactionTotalAmount sets the "transaction_total_amount" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateTransactionTotalAmount() *TransactionUpsert {
	u.SetExcluded(transaction.FieldTransactionTotalAmount)
	return u
}

// SetTransactionDirection sets the "transaction_direction" field.
func (u *TransactionUpsert) SetTransactionDirection(v transaction.TransactionDirection) *TransactionUpsert {
	u.Set(transaction.FieldTransactionDirection, v)
	return u
}

// UpdateTransactionDirection sets the "transaction_direction" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateTransactionDirection() *TransactionUpsert {
	u.SetExcluded(transaction.FieldTransactionDirection)
	return u
}

// ClearTransactionDirection clears the value of the "transaction_direction" field.
func (u *TransactionUpsert) ClearTransactionDirection() *TransactionUpsert {
	u.SetNull(transaction.FieldTransactionDirection)
	return u
}

// SetPurposeCode sets the "purpose_code" field.
func (u *TransactionUpsert) SetPurposeCode(v string) *TransactionUpsert {
	u.Set(transaction.FieldPurposeCode, v)
	return u
}

// UpdatePurposeCode sets the "purpose_code" field to the value that was provided on create.
func (u *TransactionUpsert) UpdatePurposeCode() *TransactionUpsert {
	u.SetExcluded(transaction.FieldPurposeCode)
	return u
}

// ClearPurposeCode clears the value of the "purpose_code" field.
func (u *TransactionUpsert) ClearPurposeCode() *TransactionUpsert {
	u.SetNull(transaction.FieldPurposeCode)
	return u
}

// SetPurposeDetails sets the "purpose_details" field.
func (u *TransactionUpsert) SetPurposeDetails(v string) *TransactionUpsert {
	u.Set(transaction.FieldPurposeDetails, v)
	return u
}

// UpdatePurposeDetails sets the "purpose_details" field to the value that was provided on create.
func (u *TransactionUpsert) UpdatePurposeDetails() *TransactionUpsert {
	u.SetExcluded(transaction.FieldPurposeDetails)
	return u
}

// ClearPurposeDetails clears the value of the "purpose_details" field.
func (u *TransactionUpsert) ClearPurposeDetails() *TransactionUpsert {
	u.SetNull(transaction.FieldPurposeDetails)
	return u
}

// SetPayerKod sets the "payer_kod" field.
func (u *TransactionUpsert) SetPayerKod(v string) *TransactionUpsert {
	u.Set(transaction.FieldPayerKod, v)
	return u
}

// UpdatePayerKod sets the "payer_kod" field to the value that was provided on create.
func (u *TransactionUpsert) UpdatePayerKod() *TransactionUpsert {
	u.SetExcluded(transaction.FieldPayerKod)
	return u
}

// ClearPayerKod clears the value of the "payer_kod" field.
func (u *TransactionUpsert) ClearPayerKod() *TransactionUpsert {
	u.SetNull(transaction.FieldPayerKod)
	return u
}

// SetPayerBinIin sets the "payer_bin_iin" field.
func (u *TransactionUpsert) SetPayerBinIin(v string) *TransactionUpsert {
	u.Set(transaction.FieldPayerBinIin, v)
	return u
}

// UpdatePayerBinIin sets the "payer_bin_iin" field to the value that was provided on create.
func (u *TransactionUpsert) UpdatePayerBinIin() *TransactionUpsert {
	u.SetExcluded(transaction.FieldPayerBinIin)
	return u
}

// ClearPayerBinIin clears the value of the "payer_bin_iin" field.
func (u *TransactionUpsert) ClearPayerBinIin() *TransactionUpsert {
	u.SetNull(transaction.FieldPayerBinIin)
	return u
}

// SetPayerName sets the "payer_name" field.
func (u *TransactionUpsert) SetPayerName(v string) *TransactionUpsert {
	u.Set(transaction.FieldPayerName, v)
	return u
}

// UpdatePayerName sets the "payer_name" field to the value that was provided on create.
func (u *TransactionUpsert) UpdatePayerName() *TransactionUpsert {
	u.SetExcluded(transaction.FieldPayerName)
	return u
}

// ClearPayerName clears the value of the "payer_name" field.
func (u *TransactionUpsert) ClearPayerName() *TransactionUpsert {
	u.SetNull(transaction.FieldPayerName)
	return u
}

// SetPayerType sets the "payer_type" field.
func (u *TransactionUpsert) SetPayerType(v transaction.PayerType) *TransactionUpsert {
	u.Set(transaction.FieldPayerType, v)
	return u
}

// UpdatePayerType sets the "payer_type" field to the value that was provided on create.
func (u *TransactionUpsert) UpdatePayerType() *TransactionUpsert {
	u.SetExcluded(transaction.FieldPayerType)
	return u
}

// ClearPayerType clears the value of the "payer_type" field.
func (u *TransactionUpsert) ClearPayerType() *TransactionUpsert {
	u.SetNull(transaction.FieldPayerType)
	return u
}

// SetPayerAccountIban sets the "payer_account_iban" field.
func (u *TransactionUpsert) SetPayerAccountIban(v string) *TransactionUpsert {
	u.Set(transaction.FieldPayerAccountIban, v)
	return u
}

// UpdatePayerAccountIban sets the "payer_account_iban" field to the value that was provided on create.
func (u *TransactionUpsert) UpdatePayerAccountIban() *TransactionUpsert {
	u.SetExcluded(transaction.FieldPayerAccountIban)
	return u
}

// ClearPayerAccountIban clears the value of the "payer_account_iban" field.
func (u *TransactionUpsert) ClearPayerAccountIban() *TransactionUpsert {
	u.SetNull(transaction.FieldPayerAccountIban)
	return u
}

// SetPayerBankBic sets the "payer_bank_bic" field.
func (u *TransactionUpsert) SetPayerBankBic(v string) *TransactionUpsert {
	u.Set(transaction.FieldPayerBankBic, v)
	return u
}

// UpdatePayerBankBic sets the "payer_bank_bic" field to the value that was provided on create.
func (u *TransactionUpsert) UpdatePayerBankBic() *TransactionUpsert {
	u.SetExcluded(transaction.FieldPayerBankBic)
	return u
}

// ClearPayerBankBic clears the value of the "payer_bank_bic" field.
func (u *TransactionUpsert) ClearPayerBankBic() *TransactionUpsert {
	u.SetNull(transaction.FieldPayerBankBic)
	return u
}

// SetPayerBankName sets the "payer_bank_name" field.
func (u *TransactionUpsert) SetPayerBankName(v string) *TransactionUpsert {
	u.Set(transaction.FieldPayerBankName, v)
	return u
}

// UpdatePayerBankName sets the "payer_bank_name" field to the value that was provided on create.
func (u *TransactionUpsert) UpdatePayerBankName() *TransactionUpsert {
	u.SetExcluded(transaction.FieldPayerBankName)
	return u
}

// ClearPayerBankName clears the value of the "payer_bank_name" field.
func (u *TransactionUpsert) ClearPayerBankName() *TransactionUpsert {
	u.SetNull(transaction.FieldPayerBankName)
	return u
}

// SetPayerIsoCountryCode sets the "payer_iso_country_code" field.
func (u *TransactionUpsert) SetPayerIsoCountryCode(v string) *TransactionUpsert {
	u.Set(transaction.FieldPayerIsoCountryCode, v)
	return u
}

// UpdatePayerIsoCountryCode sets the "payer_iso_country_code" field to the value that was provided on create.
func (u *TransactionUpsert) UpdatePayerIsoCountryCode() *TransactionUpsert {
	u.SetExcluded(transaction.FieldPayerIsoCountryCode)
	return u
}

// ClearPayerIsoCountryCode clears the value of the "payer_iso_country_code" field.
func (u *TransactionUpsert) ClearPayerIsoCountryCode() *TransactionUpsert {
	u.SetNull(transaction.FieldPayerIsoCountryCode)
	return u
}

// SetRealPayerName sets the "real_payer_name" field.
func (u *TransactionUpsert) SetRealPayerName(v string) *TransactionUpsert {
	u.Set(transaction.FieldRealPayerName, v)
	return u
}

// UpdateRealPayerName sets the "real_payer_name" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateRealPayerName() *TransactionUpsert {
	u.SetExcluded(transaction.FieldRealPayerName)
	return u
}

// ClearRealPayerName clears the value of the "real_payer_name" field.
func (u *TransactionUpsert) ClearRealPayerName() *TransactionUpsert {
	u.SetNull(transaction.FieldRealPayerName)
	return u
}

// SetRealPayerBinIin sets the "real_payer_bin_iin" field.
func (u *TransactionUpsert) SetRealPayerBinIin(v string) *TransactionUpsert {
	u.Set(transaction.FieldRealPayerBinIin, v)
	return u
}

// UpdateRealPayerBinIin sets the "real_payer_bin_iin" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateRealPayerBinIin() *TransactionUpsert {
	u.SetExcluded(transaction.FieldRealPayerBinIin)
	return u
}

// ClearRealPayerBinIin clears the value of the "real_payer_bin_iin" field.
func (u *TransactionUpsert) ClearRealPayerBinIin() *TransactionUpsert {
	u.SetNull(transaction.FieldRealPayerBinIin)
	return u
}

// SetRealPayerIsoCountryCode sets the "real_payer_iso_country_code" field.
func (u *TransactionUpsert) SetRealPayerIsoCountryCode(v string) *TransactionUpsert {
	u.Set(transaction.FieldRealPayerIsoCountryCode, v)
	return u
}

// UpdateRealPayerIsoCountryCode sets the "real_payer_iso_country_code" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateRealPayerIsoCountryCode() *TransactionUpsert {
	u.SetExcluded(transaction.FieldRealPayerIsoCountryCode)
	return u
}

// ClearRealPayerIsoCountryCode clears the value of the "real_payer_iso_country_code" field.
func (u *TransactionUpsert) ClearRealPayerIsoCountryCode() *TransactionUpsert {
	u.SetNull(transaction.FieldRealPayerIsoCountryCode)
	return u
}

// SetRealPayerType sets the "real_payer_type" field.
func (u *TransactionUpsert) SetRealPayerType(v transaction.RealPayerType) *TransactionUpsert {
	u.Set(transaction.FieldRealPayerType, v)
	return u
}

// UpdateRealPayerType sets the "real_payer_type" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateRealPayerType() *TransactionUpsert {
	u.SetExcluded(transaction.FieldRealPayerType)
	return u
}

// ClearRealPayerType clears the value of the "real_payer_type" field.
func (u *TransactionUpsert) ClearRealPayerType() *TransactionUpsert {
	u.SetNull(transaction.FieldRealPayerType)
	return u
}

// SetBeneficiaryKbe sets the "beneficiary_kbe" field.
func (u *TransactionUpsert) SetBeneficiaryKbe(v string) *TransactionUpsert {
	u.Set(transaction.FieldBeneficiaryKbe, v)
	return u
}

// UpdateBeneficiaryKbe sets the "beneficiary_kbe" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateBeneficiaryKbe() *TransactionUpsert {
	u.SetExcluded(transaction.FieldBeneficiaryKbe)
	return u
}

// ClearBeneficiaryKbe clears the value of the "beneficiary_kbe" field.
func (u *TransactionUpsert) ClearBeneficiaryKbe() *TransactionUpsert {
	u.SetNull(transaction.FieldBeneficiaryKbe)
	return u
}

// SetBeneficiaryBinIin sets the "beneficiary_bin_iin" field.
func (u *TransactionUpsert) SetBeneficiaryBinIin(v string) *TransactionUpsert {
	u.Set(transaction.FieldBeneficiaryBinIin, v)
	return u
}

// UpdateBeneficiaryBinIin sets the "beneficiary_bin_iin" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateBeneficiaryBinIin() *TransactionUpsert {
	u.SetExcluded(transaction.FieldBeneficiaryBinIin)
	return u
}

// ClearBeneficiaryBinIin clears the value of the "beneficiary_bin_iin" field.
func (u *TransactionUpsert) ClearBeneficiaryBinIin() *TransactionUpsert {
	u.SetNull(transaction.FieldBeneficiaryBinIin)
	return u
}

// SetBeneficiaryName sets the "beneficiary_name" field.
func (u *TransactionUpsert) SetBeneficiaryName(v string) *TransactionUpsert {
	u.Set(transaction.FieldBeneficiaryName, v)
	return u
}

// UpdateBeneficiaryName sets the "beneficiary_name" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateBeneficiaryName() *TransactionUpsert {
	u.SetExcluded(transaction.FieldBeneficiaryName)
	return u
}

// ClearBeneficiaryName clears the value of the "beneficiary_name" field.
func (u *TransactionUpsert) ClearBeneficiaryName() *TransactionUpsert {
	u.SetNull(transaction.FieldBeneficiaryName)
	return u
}

// SetBeneficiaryType sets the "beneficiary_type" field.
func (u *TransactionUpsert) SetBeneficiaryType(v transaction.BeneficiaryType) *TransactionUpsert {
	u.Set(transaction.FieldBeneficiaryType, v)
	return u
}

// UpdateBeneficiaryType sets the "beneficiary_type" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateBeneficiaryType() *TransactionUpsert {
	u.SetExcluded(transaction.FieldBeneficiaryType)
	return u
}

// ClearBeneficiaryType clears the value of the "beneficiary_type" field.
func (u *TransactionUpsert) ClearBeneficiaryType() *TransactionUpsert {
	u.SetNull(transaction.FieldBeneficiaryType)
	return u
}

// SetBeneficiaryAccountIban sets the "beneficiary_account_iban" field.
func (u *TransactionUpsert) SetBeneficiaryAccountIban(v string) *TransactionUpsert {
	u.Set(transaction.FieldBeneficiaryAccountIban, v)
	return u
}

// UpdateBeneficiaryAccountIban sets the "beneficiary_account_iban" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateBeneficiaryAccountIban() *TransactionUpsert {
	u.SetExcluded(transaction.FieldBeneficiaryAccountIban)
	return u
}

// ClearBeneficiaryAccountIban clears the value of the "beneficiary_account_iban" field.
func (u *TransactionUpsert) ClearBeneficiaryAccountIban() *TransactionUpsert {
	u.SetNull(transaction.FieldBeneficiaryAccountIban)
	return u
}

// SetBeneficiaryBankBic sets the "beneficiary_bank_bic" field.
func (u *TransactionUpsert) SetBeneficiaryBankBic(v string) *TransactionUpsert {
	u.Set(transaction.FieldBeneficiaryBankBic, v)
	return u
}

// UpdateBeneficiaryBankBic sets the "beneficiary_bank_bic" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateBeneficiaryBankBic() *TransactionUpsert {
	u.SetExcluded(transaction.FieldBeneficiaryBankBic)
	return u
}

// ClearBeneficiaryBankBic clears the value of the "beneficiary_bank_bic" field.
func (u *TransactionUpsert) ClearBeneficiaryBankBic() *TransactionUpsert {
	u.SetNull(transaction.FieldBeneficiaryBankBic)
	return u
}

// SetBeneficiaryBankName sets the "beneficiary_bank_name" field.
func (u *TransactionUpsert) SetBeneficiaryBankName(v string) *TransactionUpsert {
	u.Set(transaction.FieldBeneficiaryBankName, v)
	return u
}

// UpdateBeneficiaryBankName sets the "beneficiary_bank_name" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateBeneficiaryBankName() *TransactionUpsert {
	u.SetExcluded(transaction.FieldBeneficiaryBankName)
	return u
}

// ClearBeneficiaryBankName clears the value of the "beneficiary_bank_name" field.
func (u *TransactionUpsert) ClearBeneficiaryBankName() *TransactionUpsert {
	u.SetNull(transaction.FieldBeneficiaryBankName)
	return u
}

// SetBeneficiaryIsoCountryCode sets the "beneficiary_iso_country_code" field.
func (u *TransactionUpsert) SetBeneficiaryIsoCountryCode(v string) *TransactionUpsert {
	u.Set(transaction.FieldBeneficiaryIsoCountryCode, v)
	return u
}

// UpdateBeneficiaryIsoCountryCode sets the "beneficiary_iso_country_code" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateBeneficiaryIsoCountryCode() *TransactionUpsert {
	u.SetExcluded(transaction.FieldBeneficiaryIsoCountryCode)
	return u
}

// ClearBeneficiaryIsoCountryCode clears the value of the "beneficiary_iso_country_code" field.
func (u *TransactionUpsert) ClearBeneficiaryIsoCountryCode() *TransactionUpsert {
	u.SetNull(transaction.FieldBeneficiaryIsoCountryCode)
	return u
}

// SetRealBeneficiaryName sets the "real_beneficiary_name" field.
func (u *TransactionUpsert) SetRealBeneficiaryName(v string) *TransactionUpsert {
	u.Set(transaction.FieldRealBeneficiaryName, v)
	return u
}

// UpdateRealBeneficiaryName sets the "real_beneficiary_name" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateRealBeneficiaryName() *TransactionUpsert {
	u.SetExcluded(transaction.FieldRealBeneficiaryName)
	return u
}

// ClearRealBeneficiaryName clears the value of the "real_beneficiary_name" field.
func (u *TransactionUpsert) ClearRealBeneficiaryName() *TransactionUpsert {
	u.SetNull(transaction.FieldRealBeneficiaryName)
	return u
}

// SetRealBeneficiaryBinIin sets the "real_beneficiary_bin_iin" field.
func (u *TransactionUpsert) SetRealBeneficiaryBinIin(v string) *TransactionUpsert {
	u.Set(transaction.FieldRealBeneficiaryBinIin, v)
	return u
}

// UpdateRealBeneficiaryBinIin sets the "real_beneficiary_bin_iin" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateRealBeneficiaryBinIin() *TransactionUpsert {
	u.SetExcluded(transaction.FieldRealBeneficiaryBinIin)
	return u
}

// ClearRealBeneficiaryBinIin clears the value of the "real_beneficiary_bin_iin" field.
func (u *TransactionUpsert) ClearRealBeneficiaryBinIin() *TransactionUpsert {
	u.SetNull(transaction.FieldRealBeneficiaryBinIin)
	return u
}

// SetRealBeneficiaryCountryCode sets the "real_beneficiary_country_code" field.
func (u *TransactionUpsert) SetRealBeneficiaryCountryCode(v string) *TransactionUpsert {
	u.Set(transaction.FieldRealBeneficiaryCountryCode, v)
	return u
}

// UpdateRealBeneficiaryCountryCode sets the "real_beneficiary_country_code" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateRealBeneficiaryCountryCode() *TransactionUpsert {
	u.SetExcluded(transaction.FieldRealBeneficiaryCountryCode)
	return u
}

// ClearRealBeneficiaryCountryCode clears the value of the "real_beneficiary_country_code" field.
func (u *TransactionUpsert) ClearRealBeneficiaryCountryCode() *TransactionUpsert {
	u.SetNull(transaction.FieldRealBeneficiaryCountryCode)
	return u
}

// SetRealBeneficiaryType sets the "real_beneficiary_type" field.
func (u *TransactionUpsert) SetRealBeneficiaryType(v transaction.RealBeneficiaryType) *TransactionUpsert {
	u.Set(transaction.FieldRealBeneficiaryType, v)
	return u
}

// UpdateRealBeneficiaryType sets the "real_beneficiary_type" field to the value that was provided on create.
func (u *TransactionUpsert) UpdateRealBeneficiaryType() *TransactionUpsert {
	u.SetExcluded(transaction.FieldRealBeneficiaryType)
	return u
}

// ClearRealBeneficiaryType clears the value of the "real_beneficiary_type" field.
func (u *TransactionUpsert) ClearRealBeneficiaryType() *TransactionUpsert {
	u.SetNull(transaction.FieldRealBeneficiaryType)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Transaction.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(transaction.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *TransactionUpsertOne) UpdateNewValues() *TransactionUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(transaction.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(transaction.FieldCreateTime)
		}
		if _, exists := u.create.mutation.UpdateTime(); exists {
			s.SetIgnore(transaction.FieldUpdateTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Transaction.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *TransactionUpsertOne) Ignore() *TransactionUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *TransactionUpsertOne) DoNothing() *TransactionUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the TransactionCreate.OnConflict
// documentation for more info.
func (u *TransactionUpsertOne) Update(set func(*TransactionUpsert)) *TransactionUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&TransactionUpsert{UpdateSet: update})
	}))
	return u
}

// SetTransactionNumber sets the "transaction_number" field.
func (u *TransactionUpsertOne) SetTransactionNumber(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetTransactionNumber(v)
	})
}

// UpdateTransactionNumber sets the "transaction_number" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateTransactionNumber() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateTransactionNumber()
	})
}

// SetTransactionDate sets the "transaction_date" field.
func (u *TransactionUpsertOne) SetTransactionDate(v time.Time) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetTransactionDate(v)
	})
}

// UpdateTransactionDate sets the "transaction_date" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateTransactionDate() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateTransactionDate()
	})
}

// SetTransactionType sets the "transaction_type" field.
func (u *TransactionUpsertOne) SetTransactionType(v transaction.TransactionType) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetTransactionType(v)
	})
}

// UpdateTransactionType sets the "transaction_type" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateTransactionType() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateTransactionType()
	})
}

// SetInitiatorID sets the "initiator_id" field.
func (u *TransactionUpsertOne) SetInitiatorID(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetInitiatorID(v)
	})
}

// UpdateInitiatorID sets the "initiator_id" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateInitiatorID() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateInitiatorID()
	})
}

// SetIdempotencyKey sets the "idempotency_key" field.
func (u *TransactionUpsertOne) SetIdempotencyKey(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetIdempotencyKey(v)
	})
}

// UpdateIdempotencyKey sets the "idempotency_key" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateIdempotencyKey() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateIdempotencyKey()
	})
}

// SetValueDate sets the "value_date" field.
func (u *TransactionUpsertOne) SetValueDate(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetValueDate(v)
	})
}

// UpdateValueDate sets the "value_date" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateValueDate() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateValueDate()
	})
}

// ClearValueDate clears the value of the "value_date" field.
func (u *TransactionUpsertOne) ClearValueDate() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearValueDate()
	})
}

// SetTransactionStatus sets the "transaction_status" field.
func (u *TransactionUpsertOne) SetTransactionStatus(v transaction.TransactionStatus) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetTransactionStatus(v)
	})
}

// UpdateTransactionStatus sets the "transaction_status" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateTransactionStatus() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateTransactionStatus()
	})
}

// SetTransactionAmount sets the "transaction_amount" field.
func (u *TransactionUpsertOne) SetTransactionAmount(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetTransactionAmount(v)
	})
}

// UpdateTransactionAmount sets the "transaction_amount" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateTransactionAmount() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateTransactionAmount()
	})
}

// SetTransactionComission sets the "transaction_comission" field.
func (u *TransactionUpsertOne) SetTransactionComission(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetTransactionComission(v)
	})
}

// UpdateTransactionComission sets the "transaction_comission" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateTransactionComission() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateTransactionComission()
	})
}

// ClearTransactionComission clears the value of the "transaction_comission" field.
func (u *TransactionUpsertOne) ClearTransactionComission() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearTransactionComission()
	})
}

// SetTransactionCurrency sets the "transaction_currency" field.
func (u *TransactionUpsertOne) SetTransactionCurrency(v transaction.TransactionCurrency) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetTransactionCurrency(v)
	})
}

// UpdateTransactionCurrency sets the "transaction_currency" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateTransactionCurrency() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateTransactionCurrency()
	})
}

// SetTransactionTotalAmount sets the "transaction_total_amount" field.
func (u *TransactionUpsertOne) SetTransactionTotalAmount(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetTransactionTotalAmount(v)
	})
}

// UpdateTransactionTotalAmount sets the "transaction_total_amount" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateTransactionTotalAmount() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateTransactionTotalAmount()
	})
}

// SetTransactionDirection sets the "transaction_direction" field.
func (u *TransactionUpsertOne) SetTransactionDirection(v transaction.TransactionDirection) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetTransactionDirection(v)
	})
}

// UpdateTransactionDirection sets the "transaction_direction" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateTransactionDirection() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateTransactionDirection()
	})
}

// ClearTransactionDirection clears the value of the "transaction_direction" field.
func (u *TransactionUpsertOne) ClearTransactionDirection() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearTransactionDirection()
	})
}

// SetPurposeCode sets the "purpose_code" field.
func (u *TransactionUpsertOne) SetPurposeCode(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetPurposeCode(v)
	})
}

// UpdatePurposeCode sets the "purpose_code" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdatePurposeCode() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdatePurposeCode()
	})
}

// ClearPurposeCode clears the value of the "purpose_code" field.
func (u *TransactionUpsertOne) ClearPurposeCode() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearPurposeCode()
	})
}

// SetPurposeDetails sets the "purpose_details" field.
func (u *TransactionUpsertOne) SetPurposeDetails(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetPurposeDetails(v)
	})
}

// UpdatePurposeDetails sets the "purpose_details" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdatePurposeDetails() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdatePurposeDetails()
	})
}

// ClearPurposeDetails clears the value of the "purpose_details" field.
func (u *TransactionUpsertOne) ClearPurposeDetails() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearPurposeDetails()
	})
}

// SetPayerKod sets the "payer_kod" field.
func (u *TransactionUpsertOne) SetPayerKod(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetPayerKod(v)
	})
}

// UpdatePayerKod sets the "payer_kod" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdatePayerKod() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdatePayerKod()
	})
}

// ClearPayerKod clears the value of the "payer_kod" field.
func (u *TransactionUpsertOne) ClearPayerKod() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearPayerKod()
	})
}

// SetPayerBinIin sets the "payer_bin_iin" field.
func (u *TransactionUpsertOne) SetPayerBinIin(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetPayerBinIin(v)
	})
}

// UpdatePayerBinIin sets the "payer_bin_iin" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdatePayerBinIin() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdatePayerBinIin()
	})
}

// ClearPayerBinIin clears the value of the "payer_bin_iin" field.
func (u *TransactionUpsertOne) ClearPayerBinIin() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearPayerBinIin()
	})
}

// SetPayerName sets the "payer_name" field.
func (u *TransactionUpsertOne) SetPayerName(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetPayerName(v)
	})
}

// UpdatePayerName sets the "payer_name" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdatePayerName() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdatePayerName()
	})
}

// ClearPayerName clears the value of the "payer_name" field.
func (u *TransactionUpsertOne) ClearPayerName() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearPayerName()
	})
}

// SetPayerType sets the "payer_type" field.
func (u *TransactionUpsertOne) SetPayerType(v transaction.PayerType) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetPayerType(v)
	})
}

// UpdatePayerType sets the "payer_type" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdatePayerType() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdatePayerType()
	})
}

// ClearPayerType clears the value of the "payer_type" field.
func (u *TransactionUpsertOne) ClearPayerType() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearPayerType()
	})
}

// SetPayerAccountIban sets the "payer_account_iban" field.
func (u *TransactionUpsertOne) SetPayerAccountIban(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetPayerAccountIban(v)
	})
}

// UpdatePayerAccountIban sets the "payer_account_iban" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdatePayerAccountIban() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdatePayerAccountIban()
	})
}

// ClearPayerAccountIban clears the value of the "payer_account_iban" field.
func (u *TransactionUpsertOne) ClearPayerAccountIban() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearPayerAccountIban()
	})
}

// SetPayerBankBic sets the "payer_bank_bic" field.
func (u *TransactionUpsertOne) SetPayerBankBic(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetPayerBankBic(v)
	})
}

// UpdatePayerBankBic sets the "payer_bank_bic" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdatePayerBankBic() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdatePayerBankBic()
	})
}

// ClearPayerBankBic clears the value of the "payer_bank_bic" field.
func (u *TransactionUpsertOne) ClearPayerBankBic() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearPayerBankBic()
	})
}

// SetPayerBankName sets the "payer_bank_name" field.
func (u *TransactionUpsertOne) SetPayerBankName(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetPayerBankName(v)
	})
}

// UpdatePayerBankName sets the "payer_bank_name" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdatePayerBankName() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdatePayerBankName()
	})
}

// ClearPayerBankName clears the value of the "payer_bank_name" field.
func (u *TransactionUpsertOne) ClearPayerBankName() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearPayerBankName()
	})
}

// SetPayerIsoCountryCode sets the "payer_iso_country_code" field.
func (u *TransactionUpsertOne) SetPayerIsoCountryCode(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetPayerIsoCountryCode(v)
	})
}

// UpdatePayerIsoCountryCode sets the "payer_iso_country_code" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdatePayerIsoCountryCode() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdatePayerIsoCountryCode()
	})
}

// ClearPayerIsoCountryCode clears the value of the "payer_iso_country_code" field.
func (u *TransactionUpsertOne) ClearPayerIsoCountryCode() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearPayerIsoCountryCode()
	})
}

// SetRealPayerName sets the "real_payer_name" field.
func (u *TransactionUpsertOne) SetRealPayerName(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetRealPayerName(v)
	})
}

// UpdateRealPayerName sets the "real_payer_name" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateRealPayerName() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateRealPayerName()
	})
}

// ClearRealPayerName clears the value of the "real_payer_name" field.
func (u *TransactionUpsertOne) ClearRealPayerName() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearRealPayerName()
	})
}

// SetRealPayerBinIin sets the "real_payer_bin_iin" field.
func (u *TransactionUpsertOne) SetRealPayerBinIin(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetRealPayerBinIin(v)
	})
}

// UpdateRealPayerBinIin sets the "real_payer_bin_iin" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateRealPayerBinIin() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateRealPayerBinIin()
	})
}

// ClearRealPayerBinIin clears the value of the "real_payer_bin_iin" field.
func (u *TransactionUpsertOne) ClearRealPayerBinIin() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearRealPayerBinIin()
	})
}

// SetRealPayerIsoCountryCode sets the "real_payer_iso_country_code" field.
func (u *TransactionUpsertOne) SetRealPayerIsoCountryCode(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetRealPayerIsoCountryCode(v)
	})
}

// UpdateRealPayerIsoCountryCode sets the "real_payer_iso_country_code" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateRealPayerIsoCountryCode() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateRealPayerIsoCountryCode()
	})
}

// ClearRealPayerIsoCountryCode clears the value of the "real_payer_iso_country_code" field.
func (u *TransactionUpsertOne) ClearRealPayerIsoCountryCode() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearRealPayerIsoCountryCode()
	})
}

// SetRealPayerType sets the "real_payer_type" field.
func (u *TransactionUpsertOne) SetRealPayerType(v transaction.RealPayerType) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetRealPayerType(v)
	})
}

// UpdateRealPayerType sets the "real_payer_type" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateRealPayerType() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateRealPayerType()
	})
}

// ClearRealPayerType clears the value of the "real_payer_type" field.
func (u *TransactionUpsertOne) ClearRealPayerType() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearRealPayerType()
	})
}

// SetBeneficiaryKbe sets the "beneficiary_kbe" field.
func (u *TransactionUpsertOne) SetBeneficiaryKbe(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetBeneficiaryKbe(v)
	})
}

// UpdateBeneficiaryKbe sets the "beneficiary_kbe" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateBeneficiaryKbe() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateBeneficiaryKbe()
	})
}

// ClearBeneficiaryKbe clears the value of the "beneficiary_kbe" field.
func (u *TransactionUpsertOne) ClearBeneficiaryKbe() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearBeneficiaryKbe()
	})
}

// SetBeneficiaryBinIin sets the "beneficiary_bin_iin" field.
func (u *TransactionUpsertOne) SetBeneficiaryBinIin(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetBeneficiaryBinIin(v)
	})
}

// UpdateBeneficiaryBinIin sets the "beneficiary_bin_iin" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateBeneficiaryBinIin() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateBeneficiaryBinIin()
	})
}

// ClearBeneficiaryBinIin clears the value of the "beneficiary_bin_iin" field.
func (u *TransactionUpsertOne) ClearBeneficiaryBinIin() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearBeneficiaryBinIin()
	})
}

// SetBeneficiaryName sets the "beneficiary_name" field.
func (u *TransactionUpsertOne) SetBeneficiaryName(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetBeneficiaryName(v)
	})
}

// UpdateBeneficiaryName sets the "beneficiary_name" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateBeneficiaryName() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateBeneficiaryName()
	})
}

// ClearBeneficiaryName clears the value of the "beneficiary_name" field.
func (u *TransactionUpsertOne) ClearBeneficiaryName() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearBeneficiaryName()
	})
}

// SetBeneficiaryType sets the "beneficiary_type" field.
func (u *TransactionUpsertOne) SetBeneficiaryType(v transaction.BeneficiaryType) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetBeneficiaryType(v)
	})
}

// UpdateBeneficiaryType sets the "beneficiary_type" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateBeneficiaryType() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateBeneficiaryType()
	})
}

// ClearBeneficiaryType clears the value of the "beneficiary_type" field.
func (u *TransactionUpsertOne) ClearBeneficiaryType() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearBeneficiaryType()
	})
}

// SetBeneficiaryAccountIban sets the "beneficiary_account_iban" field.
func (u *TransactionUpsertOne) SetBeneficiaryAccountIban(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetBeneficiaryAccountIban(v)
	})
}

// UpdateBeneficiaryAccountIban sets the "beneficiary_account_iban" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateBeneficiaryAccountIban() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateBeneficiaryAccountIban()
	})
}

// ClearBeneficiaryAccountIban clears the value of the "beneficiary_account_iban" field.
func (u *TransactionUpsertOne) ClearBeneficiaryAccountIban() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearBeneficiaryAccountIban()
	})
}

// SetBeneficiaryBankBic sets the "beneficiary_bank_bic" field.
func (u *TransactionUpsertOne) SetBeneficiaryBankBic(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetBeneficiaryBankBic(v)
	})
}

// UpdateBeneficiaryBankBic sets the "beneficiary_bank_bic" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateBeneficiaryBankBic() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateBeneficiaryBankBic()
	})
}

// ClearBeneficiaryBankBic clears the value of the "beneficiary_bank_bic" field.
func (u *TransactionUpsertOne) ClearBeneficiaryBankBic() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearBeneficiaryBankBic()
	})
}

// SetBeneficiaryBankName sets the "beneficiary_bank_name" field.
func (u *TransactionUpsertOne) SetBeneficiaryBankName(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetBeneficiaryBankName(v)
	})
}

// UpdateBeneficiaryBankName sets the "beneficiary_bank_name" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateBeneficiaryBankName() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateBeneficiaryBankName()
	})
}

// ClearBeneficiaryBankName clears the value of the "beneficiary_bank_name" field.
func (u *TransactionUpsertOne) ClearBeneficiaryBankName() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearBeneficiaryBankName()
	})
}

// SetBeneficiaryIsoCountryCode sets the "beneficiary_iso_country_code" field.
func (u *TransactionUpsertOne) SetBeneficiaryIsoCountryCode(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetBeneficiaryIsoCountryCode(v)
	})
}

// UpdateBeneficiaryIsoCountryCode sets the "beneficiary_iso_country_code" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateBeneficiaryIsoCountryCode() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateBeneficiaryIsoCountryCode()
	})
}

// ClearBeneficiaryIsoCountryCode clears the value of the "beneficiary_iso_country_code" field.
func (u *TransactionUpsertOne) ClearBeneficiaryIsoCountryCode() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearBeneficiaryIsoCountryCode()
	})
}

// SetRealBeneficiaryName sets the "real_beneficiary_name" field.
func (u *TransactionUpsertOne) SetRealBeneficiaryName(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetRealBeneficiaryName(v)
	})
}

// UpdateRealBeneficiaryName sets the "real_beneficiary_name" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateRealBeneficiaryName() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateRealBeneficiaryName()
	})
}

// ClearRealBeneficiaryName clears the value of the "real_beneficiary_name" field.
func (u *TransactionUpsertOne) ClearRealBeneficiaryName() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearRealBeneficiaryName()
	})
}

// SetRealBeneficiaryBinIin sets the "real_beneficiary_bin_iin" field.
func (u *TransactionUpsertOne) SetRealBeneficiaryBinIin(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetRealBeneficiaryBinIin(v)
	})
}

// UpdateRealBeneficiaryBinIin sets the "real_beneficiary_bin_iin" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateRealBeneficiaryBinIin() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateRealBeneficiaryBinIin()
	})
}

// ClearRealBeneficiaryBinIin clears the value of the "real_beneficiary_bin_iin" field.
func (u *TransactionUpsertOne) ClearRealBeneficiaryBinIin() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearRealBeneficiaryBinIin()
	})
}

// SetRealBeneficiaryCountryCode sets the "real_beneficiary_country_code" field.
func (u *TransactionUpsertOne) SetRealBeneficiaryCountryCode(v string) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetRealBeneficiaryCountryCode(v)
	})
}

// UpdateRealBeneficiaryCountryCode sets the "real_beneficiary_country_code" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateRealBeneficiaryCountryCode() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateRealBeneficiaryCountryCode()
	})
}

// ClearRealBeneficiaryCountryCode clears the value of the "real_beneficiary_country_code" field.
func (u *TransactionUpsertOne) ClearRealBeneficiaryCountryCode() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearRealBeneficiaryCountryCode()
	})
}

// SetRealBeneficiaryType sets the "real_beneficiary_type" field.
func (u *TransactionUpsertOne) SetRealBeneficiaryType(v transaction.RealBeneficiaryType) *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.SetRealBeneficiaryType(v)
	})
}

// UpdateRealBeneficiaryType sets the "real_beneficiary_type" field to the value that was provided on create.
func (u *TransactionUpsertOne) UpdateRealBeneficiaryType() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateRealBeneficiaryType()
	})
}

// ClearRealBeneficiaryType clears the value of the "real_beneficiary_type" field.
func (u *TransactionUpsertOne) ClearRealBeneficiaryType() *TransactionUpsertOne {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearRealBeneficiaryType()
	})
}

// Exec executes the query.
func (u *TransactionUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for TransactionCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *TransactionUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *TransactionUpsertOne) ID(ctx context.Context) (id uuid.UUID, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: TransactionUpsertOne.ID is not supported by MySQL driver. Use TransactionUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *TransactionUpsertOne) IDX(ctx context.Context) uuid.UUID {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// TransactionCreateBulk is the builder for creating many Transaction entities in bulk.
type TransactionCreateBulk struct {
	config
	err      error
	builders []*TransactionCreate
	conflict []sql.ConflictOption
}

// Save creates the Transaction entities in the database.
func (_c *TransactionCreateBulk) Save(ctx context.Context) ([]*Transaction, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*Transaction, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*TransactionMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *TransactionCreateBulk) SaveX(ctx context.Context) []*Transaction {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *TransactionCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *TransactionCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Transaction.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.TransactionUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *TransactionCreateBulk) OnConflict(opts ...sql.ConflictOption) *TransactionUpsertBulk {
	_c.conflict = opts
	return &TransactionUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Transaction.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *TransactionCreateBulk) OnConflictColumns(columns ...string) *TransactionUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &TransactionUpsertBulk{
		create: _c,
	}
}

// TransactionUpsertBulk is the builder for "upsert"-ing
// a bulk of Transaction nodes.
type TransactionUpsertBulk struct {
	create *TransactionCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Transaction.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(transaction.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *TransactionUpsertBulk) UpdateNewValues() *TransactionUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(transaction.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(transaction.FieldCreateTime)
			}
			if _, exists := b.mutation.UpdateTime(); exists {
				s.SetIgnore(transaction.FieldUpdateTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Transaction.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *TransactionUpsertBulk) Ignore() *TransactionUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *TransactionUpsertBulk) DoNothing() *TransactionUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the TransactionCreateBulk.OnConflict
// documentation for more info.
func (u *TransactionUpsertBulk) Update(set func(*TransactionUpsert)) *TransactionUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&TransactionUpsert{UpdateSet: update})
	}))
	return u
}

// SetTransactionNumber sets the "transaction_number" field.
func (u *TransactionUpsertBulk) SetTransactionNumber(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetTransactionNumber(v)
	})
}

// UpdateTransactionNumber sets the "transaction_number" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateTransactionNumber() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateTransactionNumber()
	})
}

// SetTransactionDate sets the "transaction_date" field.
func (u *TransactionUpsertBulk) SetTransactionDate(v time.Time) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetTransactionDate(v)
	})
}

// UpdateTransactionDate sets the "transaction_date" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateTransactionDate() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateTransactionDate()
	})
}

// SetTransactionType sets the "transaction_type" field.
func (u *TransactionUpsertBulk) SetTransactionType(v transaction.TransactionType) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetTransactionType(v)
	})
}

// UpdateTransactionType sets the "transaction_type" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateTransactionType() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateTransactionType()
	})
}

// SetInitiatorID sets the "initiator_id" field.
func (u *TransactionUpsertBulk) SetInitiatorID(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetInitiatorID(v)
	})
}

// UpdateInitiatorID sets the "initiator_id" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateInitiatorID() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateInitiatorID()
	})
}

// SetIdempotencyKey sets the "idempotency_key" field.
func (u *TransactionUpsertBulk) SetIdempotencyKey(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetIdempotencyKey(v)
	})
}

// UpdateIdempotencyKey sets the "idempotency_key" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateIdempotencyKey() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateIdempotencyKey()
	})
}

// SetValueDate sets the "value_date" field.
func (u *TransactionUpsertBulk) SetValueDate(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetValueDate(v)
	})
}

// UpdateValueDate sets the "value_date" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateValueDate() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateValueDate()
	})
}

// ClearValueDate clears the value of the "value_date" field.
func (u *TransactionUpsertBulk) ClearValueDate() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearValueDate()
	})
}

// SetTransactionStatus sets the "transaction_status" field.
func (u *TransactionUpsertBulk) SetTransactionStatus(v transaction.TransactionStatus) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetTransactionStatus(v)
	})
}

// UpdateTransactionStatus sets the "transaction_status" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateTransactionStatus() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateTransactionStatus()
	})
}

// SetTransactionAmount sets the "transaction_amount" field.
func (u *TransactionUpsertBulk) SetTransactionAmount(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetTransactionAmount(v)
	})
}

// UpdateTransactionAmount sets the "transaction_amount" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateTransactionAmount() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateTransactionAmount()
	})
}

// SetTransactionComission sets the "transaction_comission" field.
func (u *TransactionUpsertBulk) SetTransactionComission(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetTransactionComission(v)
	})
}

// UpdateTransactionComission sets the "transaction_comission" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateTransactionComission() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateTransactionComission()
	})
}

// ClearTransactionComission clears the value of the "transaction_comission" field.
func (u *TransactionUpsertBulk) ClearTransactionComission() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearTransactionComission()
	})
}

// SetTransactionCurrency sets the "transaction_currency" field.
func (u *TransactionUpsertBulk) SetTransactionCurrency(v transaction.TransactionCurrency) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetTransactionCurrency(v)
	})
}

// UpdateTransactionCurrency sets the "transaction_currency" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateTransactionCurrency() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateTransactionCurrency()
	})
}

// SetTransactionTotalAmount sets the "transaction_total_amount" field.
func (u *TransactionUpsertBulk) SetTransactionTotalAmount(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetTransactionTotalAmount(v)
	})
}

// UpdateTransactionTotalAmount sets the "transaction_total_amount" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateTransactionTotalAmount() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateTransactionTotalAmount()
	})
}

// SetTransactionDirection sets the "transaction_direction" field.
func (u *TransactionUpsertBulk) SetTransactionDirection(v transaction.TransactionDirection) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetTransactionDirection(v)
	})
}

// UpdateTransactionDirection sets the "transaction_direction" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateTransactionDirection() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateTransactionDirection()
	})
}

// ClearTransactionDirection clears the value of the "transaction_direction" field.
func (u *TransactionUpsertBulk) ClearTransactionDirection() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearTransactionDirection()
	})
}

// SetPurposeCode sets the "purpose_code" field.
func (u *TransactionUpsertBulk) SetPurposeCode(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetPurposeCode(v)
	})
}

// UpdatePurposeCode sets the "purpose_code" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdatePurposeCode() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdatePurposeCode()
	})
}

// ClearPurposeCode clears the value of the "purpose_code" field.
func (u *TransactionUpsertBulk) ClearPurposeCode() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearPurposeCode()
	})
}

// SetPurposeDetails sets the "purpose_details" field.
func (u *TransactionUpsertBulk) SetPurposeDetails(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetPurposeDetails(v)
	})
}

// UpdatePurposeDetails sets the "purpose_details" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdatePurposeDetails() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdatePurposeDetails()
	})
}

// ClearPurposeDetails clears the value of the "purpose_details" field.
func (u *TransactionUpsertBulk) ClearPurposeDetails() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearPurposeDetails()
	})
}

// SetPayerKod sets the "payer_kod" field.
func (u *TransactionUpsertBulk) SetPayerKod(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetPayerKod(v)
	})
}

// UpdatePayerKod sets the "payer_kod" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdatePayerKod() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdatePayerKod()
	})
}

// ClearPayerKod clears the value of the "payer_kod" field.
func (u *TransactionUpsertBulk) ClearPayerKod() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearPayerKod()
	})
}

// SetPayerBinIin sets the "payer_bin_iin" field.
func (u *TransactionUpsertBulk) SetPayerBinIin(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetPayerBinIin(v)
	})
}

// UpdatePayerBinIin sets the "payer_bin_iin" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdatePayerBinIin() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdatePayerBinIin()
	})
}

// ClearPayerBinIin clears the value of the "payer_bin_iin" field.
func (u *TransactionUpsertBulk) ClearPayerBinIin() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearPayerBinIin()
	})
}

// SetPayerName sets the "payer_name" field.
func (u *TransactionUpsertBulk) SetPayerName(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetPayerName(v)
	})
}

// UpdatePayerName sets the "payer_name" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdatePayerName() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdatePayerName()
	})
}

// ClearPayerName clears the value of the "payer_name" field.
func (u *TransactionUpsertBulk) ClearPayerName() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearPayerName()
	})
}

// SetPayerType sets the "payer_type" field.
func (u *TransactionUpsertBulk) SetPayerType(v transaction.PayerType) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetPayerType(v)
	})
}

// UpdatePayerType sets the "payer_type" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdatePayerType() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdatePayerType()
	})
}

// ClearPayerType clears the value of the "payer_type" field.
func (u *TransactionUpsertBulk) ClearPayerType() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearPayerType()
	})
}

// SetPayerAccountIban sets the "payer_account_iban" field.
func (u *TransactionUpsertBulk) SetPayerAccountIban(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetPayerAccountIban(v)
	})
}

// UpdatePayerAccountIban sets the "payer_account_iban" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdatePayerAccountIban() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdatePayerAccountIban()
	})
}

// ClearPayerAccountIban clears the value of the "payer_account_iban" field.
func (u *TransactionUpsertBulk) ClearPayerAccountIban() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearPayerAccountIban()
	})
}

// SetPayerBankBic sets the "payer_bank_bic" field.
func (u *TransactionUpsertBulk) SetPayerBankBic(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetPayerBankBic(v)
	})
}

// UpdatePayerBankBic sets the "payer_bank_bic" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdatePayerBankBic() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdatePayerBankBic()
	})
}

// ClearPayerBankBic clears the value of the "payer_bank_bic" field.
func (u *TransactionUpsertBulk) ClearPayerBankBic() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearPayerBankBic()
	})
}

// SetPayerBankName sets the "payer_bank_name" field.
func (u *TransactionUpsertBulk) SetPayerBankName(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetPayerBankName(v)
	})
}

// UpdatePayerBankName sets the "payer_bank_name" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdatePayerBankName() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdatePayerBankName()
	})
}

// ClearPayerBankName clears the value of the "payer_bank_name" field.
func (u *TransactionUpsertBulk) ClearPayerBankName() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearPayerBankName()
	})
}

// SetPayerIsoCountryCode sets the "payer_iso_country_code" field.
func (u *TransactionUpsertBulk) SetPayerIsoCountryCode(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetPayerIsoCountryCode(v)
	})
}

// UpdatePayerIsoCountryCode sets the "payer_iso_country_code" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdatePayerIsoCountryCode() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdatePayerIsoCountryCode()
	})
}

// ClearPayerIsoCountryCode clears the value of the "payer_iso_country_code" field.
func (u *TransactionUpsertBulk) ClearPayerIsoCountryCode() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearPayerIsoCountryCode()
	})
}

// SetRealPayerName sets the "real_payer_name" field.
func (u *TransactionUpsertBulk) SetRealPayerName(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetRealPayerName(v)
	})
}

// UpdateRealPayerName sets the "real_payer_name" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateRealPayerName() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateRealPayerName()
	})
}

// ClearRealPayerName clears the value of the "real_payer_name" field.
func (u *TransactionUpsertBulk) ClearRealPayerName() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearRealPayerName()
	})
}

// SetRealPayerBinIin sets the "real_payer_bin_iin" field.
func (u *TransactionUpsertBulk) SetRealPayerBinIin(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetRealPayerBinIin(v)
	})
}

// UpdateRealPayerBinIin sets the "real_payer_bin_iin" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateRealPayerBinIin() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateRealPayerBinIin()
	})
}

// ClearRealPayerBinIin clears the value of the "real_payer_bin_iin" field.
func (u *TransactionUpsertBulk) ClearRealPayerBinIin() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearRealPayerBinIin()
	})
}

// SetRealPayerIsoCountryCode sets the "real_payer_iso_country_code" field.
func (u *TransactionUpsertBulk) SetRealPayerIsoCountryCode(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetRealPayerIsoCountryCode(v)
	})
}

// UpdateRealPayerIsoCountryCode sets the "real_payer_iso_country_code" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateRealPayerIsoCountryCode() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateRealPayerIsoCountryCode()
	})
}

// ClearRealPayerIsoCountryCode clears the value of the "real_payer_iso_country_code" field.
func (u *TransactionUpsertBulk) ClearRealPayerIsoCountryCode() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearRealPayerIsoCountryCode()
	})
}

// SetRealPayerType sets the "real_payer_type" field.
func (u *TransactionUpsertBulk) SetRealPayerType(v transaction.RealPayerType) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetRealPayerType(v)
	})
}

// UpdateRealPayerType sets the "real_payer_type" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateRealPayerType() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateRealPayerType()
	})
}

// ClearRealPayerType clears the value of the "real_payer_type" field.
func (u *TransactionUpsertBulk) ClearRealPayerType() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearRealPayerType()
	})
}

// SetBeneficiaryKbe sets the "beneficiary_kbe" field.
func (u *TransactionUpsertBulk) SetBeneficiaryKbe(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetBeneficiaryKbe(v)
	})
}

// UpdateBeneficiaryKbe sets the "beneficiary_kbe" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateBeneficiaryKbe() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateBeneficiaryKbe()
	})
}

// ClearBeneficiaryKbe clears the value of the "beneficiary_kbe" field.
func (u *TransactionUpsertBulk) ClearBeneficiaryKbe() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearBeneficiaryKbe()
	})
}

// SetBeneficiaryBinIin sets the "beneficiary_bin_iin" field.
func (u *TransactionUpsertBulk) SetBeneficiaryBinIin(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetBeneficiaryBinIin(v)
	})
}

// UpdateBeneficiaryBinIin sets the "beneficiary_bin_iin" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateBeneficiaryBinIin() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateBeneficiaryBinIin()
	})
}

// ClearBeneficiaryBinIin clears the value of the "beneficiary_bin_iin" field.
func (u *TransactionUpsertBulk) ClearBeneficiaryBinIin() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearBeneficiaryBinIin()
	})
}

// SetBeneficiaryName sets the "beneficiary_name" field.
func (u *TransactionUpsertBulk) SetBeneficiaryName(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetBeneficiaryName(v)
	})
}

// UpdateBeneficiaryName sets the "beneficiary_name" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateBeneficiaryName() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateBeneficiaryName()
	})
}

// ClearBeneficiaryName clears the value of the "beneficiary_name" field.
func (u *TransactionUpsertBulk) ClearBeneficiaryName() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearBeneficiaryName()
	})
}

// SetBeneficiaryType sets the "beneficiary_type" field.
func (u *TransactionUpsertBulk) SetBeneficiaryType(v transaction.BeneficiaryType) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetBeneficiaryType(v)
	})
}

// UpdateBeneficiaryType sets the "beneficiary_type" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateBeneficiaryType() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateBeneficiaryType()
	})
}

// ClearBeneficiaryType clears the value of the "beneficiary_type" field.
func (u *TransactionUpsertBulk) ClearBeneficiaryType() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearBeneficiaryType()
	})
}

// SetBeneficiaryAccountIban sets the "beneficiary_account_iban" field.
func (u *TransactionUpsertBulk) SetBeneficiaryAccountIban(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetBeneficiaryAccountIban(v)
	})
}

// UpdateBeneficiaryAccountIban sets the "beneficiary_account_iban" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateBeneficiaryAccountIban() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateBeneficiaryAccountIban()
	})
}

// ClearBeneficiaryAccountIban clears the value of the "beneficiary_account_iban" field.
func (u *TransactionUpsertBulk) ClearBeneficiaryAccountIban() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearBeneficiaryAccountIban()
	})
}

// SetBeneficiaryBankBic sets the "beneficiary_bank_bic" field.
func (u *TransactionUpsertBulk) SetBeneficiaryBankBic(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetBeneficiaryBankBic(v)
	})
}

// UpdateBeneficiaryBankBic sets the "beneficiary_bank_bic" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateBeneficiaryBankBic() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateBeneficiaryBankBic()
	})
}

// ClearBeneficiaryBankBic clears the value of the "beneficiary_bank_bic" field.
func (u *TransactionUpsertBulk) ClearBeneficiaryBankBic() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearBeneficiaryBankBic()
	})
}

// SetBeneficiaryBankName sets the "beneficiary_bank_name" field.
func (u *TransactionUpsertBulk) SetBeneficiaryBankName(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetBeneficiaryBankName(v)
	})
}

// UpdateBeneficiaryBankName sets the "beneficiary_bank_name" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateBeneficiaryBankName() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateBeneficiaryBankName()
	})
}

// ClearBeneficiaryBankName clears the value of the "beneficiary_bank_name" field.
func (u *TransactionUpsertBulk) ClearBeneficiaryBankName() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearBeneficiaryBankName()
	})
}

// SetBeneficiaryIsoCountryCode sets the "beneficiary_iso_country_code" field.
func (u *TransactionUpsertBulk) SetBeneficiaryIsoCountryCode(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetBeneficiaryIsoCountryCode(v)
	})
}

// UpdateBeneficiaryIsoCountryCode sets the "beneficiary_iso_country_code" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateBeneficiaryIsoCountryCode() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateBeneficiaryIsoCountryCode()
	})
}

// ClearBeneficiaryIsoCountryCode clears the value of the "beneficiary_iso_country_code" field.
func (u *TransactionUpsertBulk) ClearBeneficiaryIsoCountryCode() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearBeneficiaryIsoCountryCode()
	})
}

// SetRealBeneficiaryName sets the "real_beneficiary_name" field.
func (u *TransactionUpsertBulk) SetRealBeneficiaryName(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetRealBeneficiaryName(v)
	})
}

// UpdateRealBeneficiaryName sets the "real_beneficiary_name" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateRealBeneficiaryName() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateRealBeneficiaryName()
	})
}

// ClearRealBeneficiaryName clears the value of the "real_beneficiary_name" field.
func (u *TransactionUpsertBulk) ClearRealBeneficiaryName() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearRealBeneficiaryName()
	})
}

// SetRealBeneficiaryBinIin sets the "real_beneficiary_bin_iin" field.
func (u *TransactionUpsertBulk) SetRealBeneficiaryBinIin(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetRealBeneficiaryBinIin(v)
	})
}

// UpdateRealBeneficiaryBinIin sets the "real_beneficiary_bin_iin" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateRealBeneficiaryBinIin() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateRealBeneficiaryBinIin()
	})
}

// ClearRealBeneficiaryBinIin clears the value of the "real_beneficiary_bin_iin" field.
func (u *TransactionUpsertBulk) ClearRealBeneficiaryBinIin() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearRealBeneficiaryBinIin()
	})
}

// SetRealBeneficiaryCountryCode sets the "real_beneficiary_country_code" field.
func (u *TransactionUpsertBulk) SetRealBeneficiaryCountryCode(v string) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetRealBeneficiaryCountryCode(v)
	})
}

// UpdateRealBeneficiaryCountryCode sets the "real_beneficiary_country_code" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateRealBeneficiaryCountryCode() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateRealBeneficiaryCountryCode()
	})
}

// ClearRealBeneficiaryCountryCode clears the value of the "real_beneficiary_country_code" field.
func (u *TransactionUpsertBulk) ClearRealBeneficiaryCountryCode() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearRealBeneficiaryCountryCode()
	})
}

// SetRealBeneficiaryType sets the "real_beneficiary_type" field.
func (u *TransactionUpsertBulk) SetRealBeneficiaryType(v transaction.RealBeneficiaryType) *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.SetRealBeneficiaryType(v)
	})
}

// UpdateRealBeneficiaryType sets the "real_beneficiary_type" field to the value that was provided on create.
func (u *TransactionUpsertBulk) UpdateRealBeneficiaryType() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.UpdateRealBeneficiaryType()
	})
}

// ClearRealBeneficiaryType clears the value of the "real_beneficiary_type" field.
func (u *TransactionUpsertBulk) ClearRealBeneficiaryType() *TransactionUpsertBulk {
	return u.Update(func(s *TransactionUpsert) {
		s.ClearRealBeneficiaryType()
	})
}

// Exec executes the query.
func (u *TransactionUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the TransactionCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for TransactionCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *TransactionUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
