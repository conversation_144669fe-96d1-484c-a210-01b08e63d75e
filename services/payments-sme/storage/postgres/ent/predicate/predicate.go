// Code generated by ent, DO NOT EDIT.

package predicate

import (
	"entgo.io/ent/dialect/sql"
)

// AbsTransactionDocuments is the predicate function for abstransactiondocuments builders.
type AbsTransactionDocuments func(*sql.Selector)

// Confirmations is the predicate function for confirmations builders.
type Confirmations func(*sql.Selector)

// Employees is the predicate function for employees builders.
type Employees func(*sql.Selector)

// GeneratedDocument is the predicate function for generateddocument builders.
type GeneratedDocument func(*sql.Selector)

// Health is the predicate function for health builders.
type Health func(*sql.Selector)

// Payments is the predicate function for payments builders.
type Payments func(*sql.Selector)

// Rejections is the predicate function for rejections builders.
type Rejections func(*sql.Selector)

// Signatories is the predicate function for signatories builders.
type Signatories func(*sql.Selector)

// Transaction is the predicate function for transaction builders.
type Transaction func(*sql.Selector)
