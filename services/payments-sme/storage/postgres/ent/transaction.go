// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/transaction"
)

// Transaction is the model entity for the Transaction schema.
type Transaction struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// CreateTime holds the value of the "create_time" field.
	CreateTime time.Time `json:"create_time,omitempty"`
	// UpdateTime holds the value of the "update_time" field.
	UpdateTime time.Time `json:"update_time,omitempty"`
	// Генерим по шаблону SMETR12345678
	TransactionNumber string `json:"transaction_number,omitempty"`
	// TransactionDate holds the value of the "transaction_date" field.
	TransactionDate time.Time `json:"transaction_date,omitempty"`
	// TransactionType holds the value of the "transaction_type" field.
	TransactionType transaction.TransactionType `json:"transaction_type,omitempty"`
	// InitiatorID holds the value of the "initiator_id" field.
	InitiatorID string `json:"initiator_id,omitempty"`
	// IdempotencyKey holds the value of the "idempotency_key" field.
	IdempotencyKey string `json:"idempotency_key,omitempty"`
	// ValueDate holds the value of the "value_date" field.
	ValueDate *string `json:"value_date,omitempty"`
	// TransactionStatus holds the value of the "transaction_status" field.
	TransactionStatus transaction.TransactionStatus `json:"transaction_status,omitempty"`
	// TransactionAmount holds the value of the "transaction_amount" field.
	TransactionAmount string `json:"transaction_amount,omitempty"`
	// TransactionComission holds the value of the "transaction_comission" field.
	TransactionComission *string `json:"transaction_comission,omitempty"`
	// TransactionCurrency holds the value of the "transaction_currency" field.
	TransactionCurrency transaction.TransactionCurrency `json:"transaction_currency,omitempty"`
	// TransactionTotalAmount holds the value of the "transaction_total_amount" field.
	TransactionTotalAmount string `json:"transaction_total_amount,omitempty"`
	// TransactionDirection holds the value of the "transaction_direction" field.
	TransactionDirection *transaction.TransactionDirection `json:"transaction_direction,omitempty"`
	// PurposeCode holds the value of the "purpose_code" field.
	PurposeCode *string `json:"purpose_code,omitempty"`
	// PurposeDetails holds the value of the "purpose_details" field.
	PurposeDetails *string `json:"purpose_details,omitempty"`
	// PayerKod holds the value of the "payer_kod" field.
	PayerKod *string `json:"payer_kod,omitempty"`
	// PayerBinIin holds the value of the "payer_bin_iin" field.
	PayerBinIin *string `json:"payer_bin_iin,omitempty"`
	// PayerName holds the value of the "payer_name" field.
	PayerName *string `json:"payer_name,omitempty"`
	// PayerType holds the value of the "payer_type" field.
	PayerType *transaction.PayerType `json:"payer_type,omitempty"`
	// PayerAccountIban holds the value of the "payer_account_iban" field.
	PayerAccountIban *string `json:"payer_account_iban,omitempty"`
	// PayerBankBic holds the value of the "payer_bank_bic" field.
	PayerBankBic *string `json:"payer_bank_bic,omitempty"`
	// PayerBankName holds the value of the "payer_bank_name" field.
	PayerBankName *string `json:"payer_bank_name,omitempty"`
	// PayerIsoCountryCode holds the value of the "payer_iso_country_code" field.
	PayerIsoCountryCode *string `json:"payer_iso_country_code,omitempty"`
	// RealPayerName holds the value of the "real_payer_name" field.
	RealPayerName *string `json:"real_payer_name,omitempty"`
	// RealPayerBinIin holds the value of the "real_payer_bin_iin" field.
	RealPayerBinIin *string `json:"real_payer_bin_iin,omitempty"`
	// RealPayerIsoCountryCode holds the value of the "real_payer_iso_country_code" field.
	RealPayerIsoCountryCode *string `json:"real_payer_iso_country_code,omitempty"`
	// RealPayerType holds the value of the "real_payer_type" field.
	RealPayerType *transaction.RealPayerType `json:"real_payer_type,omitempty"`
	// BeneficiaryKbe holds the value of the "beneficiary_kbe" field.
	BeneficiaryKbe *string `json:"beneficiary_kbe,omitempty"`
	// BeneficiaryBinIin holds the value of the "beneficiary_bin_iin" field.
	BeneficiaryBinIin *string `json:"beneficiary_bin_iin,omitempty"`
	// BeneficiaryName holds the value of the "beneficiary_name" field.
	BeneficiaryName *string `json:"beneficiary_name,omitempty"`
	// BeneficiaryType holds the value of the "beneficiary_type" field.
	BeneficiaryType *transaction.BeneficiaryType `json:"beneficiary_type,omitempty"`
	// BeneficiaryAccountIban holds the value of the "beneficiary_account_iban" field.
	BeneficiaryAccountIban *string `json:"beneficiary_account_iban,omitempty"`
	// BeneficiaryBankBic holds the value of the "beneficiary_bank_bic" field.
	BeneficiaryBankBic *string `json:"beneficiary_bank_bic,omitempty"`
	// BeneficiaryBankName holds the value of the "beneficiary_bank_name" field.
	BeneficiaryBankName *string `json:"beneficiary_bank_name,omitempty"`
	// BeneficiaryIsoCountryCode holds the value of the "beneficiary_iso_country_code" field.
	BeneficiaryIsoCountryCode *string `json:"beneficiary_iso_country_code,omitempty"`
	// RealBeneficiaryName holds the value of the "real_beneficiary_name" field.
	RealBeneficiaryName *string `json:"real_beneficiary_name,omitempty"`
	// RealBeneficiaryBinIin holds the value of the "real_beneficiary_bin_iin" field.
	RealBeneficiaryBinIin *string `json:"real_beneficiary_bin_iin,omitempty"`
	// RealBeneficiaryCountryCode holds the value of the "real_beneficiary_country_code" field.
	RealBeneficiaryCountryCode *string `json:"real_beneficiary_country_code,omitempty"`
	// RealBeneficiaryType holds the value of the "real_beneficiary_type" field.
	RealBeneficiaryType *transaction.RealBeneficiaryType `json:"real_beneficiary_type,omitempty"`
	selectValues        sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Transaction) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case transaction.FieldTransactionNumber, transaction.FieldTransactionType, transaction.FieldInitiatorID, transaction.FieldIdempotencyKey, transaction.FieldValueDate, transaction.FieldTransactionStatus, transaction.FieldTransactionAmount, transaction.FieldTransactionComission, transaction.FieldTransactionCurrency, transaction.FieldTransactionTotalAmount, transaction.FieldTransactionDirection, transaction.FieldPurposeCode, transaction.FieldPurposeDetails, transaction.FieldPayerKod, transaction.FieldPayerBinIin, transaction.FieldPayerName, transaction.FieldPayerType, transaction.FieldPayerAccountIban, transaction.FieldPayerBankBic, transaction.FieldPayerBankName, transaction.FieldPayerIsoCountryCode, transaction.FieldRealPayerName, transaction.FieldRealPayerBinIin, transaction.FieldRealPayerIsoCountryCode, transaction.FieldRealPayerType, transaction.FieldBeneficiaryKbe, transaction.FieldBeneficiaryBinIin, transaction.FieldBeneficiaryName, transaction.FieldBeneficiaryType, transaction.FieldBeneficiaryAccountIban, transaction.FieldBeneficiaryBankBic, transaction.FieldBeneficiaryBankName, transaction.FieldBeneficiaryIsoCountryCode, transaction.FieldRealBeneficiaryName, transaction.FieldRealBeneficiaryBinIin, transaction.FieldRealBeneficiaryCountryCode, transaction.FieldRealBeneficiaryType:
			values[i] = new(sql.NullString)
		case transaction.FieldCreateTime, transaction.FieldUpdateTime, transaction.FieldTransactionDate:
			values[i] = new(sql.NullTime)
		case transaction.FieldID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Transaction fields.
func (_m *Transaction) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case transaction.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				_m.ID = *value
			}
		case transaction.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				_m.CreateTime = value.Time
			}
		case transaction.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				_m.UpdateTime = value.Time
			}
		case transaction.FieldTransactionNumber:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field transaction_number", values[i])
			} else if value.Valid {
				_m.TransactionNumber = value.String
			}
		case transaction.FieldTransactionDate:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field transaction_date", values[i])
			} else if value.Valid {
				_m.TransactionDate = value.Time
			}
		case transaction.FieldTransactionType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field transaction_type", values[i])
			} else if value.Valid {
				_m.TransactionType = transaction.TransactionType(value.String)
			}
		case transaction.FieldInitiatorID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field initiator_id", values[i])
			} else if value.Valid {
				_m.InitiatorID = value.String
			}
		case transaction.FieldIdempotencyKey:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field idempotency_key", values[i])
			} else if value.Valid {
				_m.IdempotencyKey = value.String
			}
		case transaction.FieldValueDate:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field value_date", values[i])
			} else if value.Valid {
				_m.ValueDate = new(string)
				*_m.ValueDate = value.String
			}
		case transaction.FieldTransactionStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field transaction_status", values[i])
			} else if value.Valid {
				_m.TransactionStatus = transaction.TransactionStatus(value.String)
			}
		case transaction.FieldTransactionAmount:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field transaction_amount", values[i])
			} else if value.Valid {
				_m.TransactionAmount = value.String
			}
		case transaction.FieldTransactionComission:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field transaction_comission", values[i])
			} else if value.Valid {
				_m.TransactionComission = new(string)
				*_m.TransactionComission = value.String
			}
		case transaction.FieldTransactionCurrency:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field transaction_currency", values[i])
			} else if value.Valid {
				_m.TransactionCurrency = transaction.TransactionCurrency(value.String)
			}
		case transaction.FieldTransactionTotalAmount:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field transaction_total_amount", values[i])
			} else if value.Valid {
				_m.TransactionTotalAmount = value.String
			}
		case transaction.FieldTransactionDirection:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field transaction_direction", values[i])
			} else if value.Valid {
				_m.TransactionDirection = new(transaction.TransactionDirection)
				*_m.TransactionDirection = transaction.TransactionDirection(value.String)
			}
		case transaction.FieldPurposeCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field purpose_code", values[i])
			} else if value.Valid {
				_m.PurposeCode = new(string)
				*_m.PurposeCode = value.String
			}
		case transaction.FieldPurposeDetails:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field purpose_details", values[i])
			} else if value.Valid {
				_m.PurposeDetails = new(string)
				*_m.PurposeDetails = value.String
			}
		case transaction.FieldPayerKod:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field payer_kod", values[i])
			} else if value.Valid {
				_m.PayerKod = new(string)
				*_m.PayerKod = value.String
			}
		case transaction.FieldPayerBinIin:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field payer_bin_iin", values[i])
			} else if value.Valid {
				_m.PayerBinIin = new(string)
				*_m.PayerBinIin = value.String
			}
		case transaction.FieldPayerName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field payer_name", values[i])
			} else if value.Valid {
				_m.PayerName = new(string)
				*_m.PayerName = value.String
			}
		case transaction.FieldPayerType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field payer_type", values[i])
			} else if value.Valid {
				_m.PayerType = new(transaction.PayerType)
				*_m.PayerType = transaction.PayerType(value.String)
			}
		case transaction.FieldPayerAccountIban:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field payer_account_iban", values[i])
			} else if value.Valid {
				_m.PayerAccountIban = new(string)
				*_m.PayerAccountIban = value.String
			}
		case transaction.FieldPayerBankBic:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field payer_bank_bic", values[i])
			} else if value.Valid {
				_m.PayerBankBic = new(string)
				*_m.PayerBankBic = value.String
			}
		case transaction.FieldPayerBankName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field payer_bank_name", values[i])
			} else if value.Valid {
				_m.PayerBankName = new(string)
				*_m.PayerBankName = value.String
			}
		case transaction.FieldPayerIsoCountryCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field payer_iso_country_code", values[i])
			} else if value.Valid {
				_m.PayerIsoCountryCode = new(string)
				*_m.PayerIsoCountryCode = value.String
			}
		case transaction.FieldRealPayerName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field real_payer_name", values[i])
			} else if value.Valid {
				_m.RealPayerName = new(string)
				*_m.RealPayerName = value.String
			}
		case transaction.FieldRealPayerBinIin:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field real_payer_bin_iin", values[i])
			} else if value.Valid {
				_m.RealPayerBinIin = new(string)
				*_m.RealPayerBinIin = value.String
			}
		case transaction.FieldRealPayerIsoCountryCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field real_payer_iso_country_code", values[i])
			} else if value.Valid {
				_m.RealPayerIsoCountryCode = new(string)
				*_m.RealPayerIsoCountryCode = value.String
			}
		case transaction.FieldRealPayerType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field real_payer_type", values[i])
			} else if value.Valid {
				_m.RealPayerType = new(transaction.RealPayerType)
				*_m.RealPayerType = transaction.RealPayerType(value.String)
			}
		case transaction.FieldBeneficiaryKbe:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field beneficiary_kbe", values[i])
			} else if value.Valid {
				_m.BeneficiaryKbe = new(string)
				*_m.BeneficiaryKbe = value.String
			}
		case transaction.FieldBeneficiaryBinIin:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field beneficiary_bin_iin", values[i])
			} else if value.Valid {
				_m.BeneficiaryBinIin = new(string)
				*_m.BeneficiaryBinIin = value.String
			}
		case transaction.FieldBeneficiaryName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field beneficiary_name", values[i])
			} else if value.Valid {
				_m.BeneficiaryName = new(string)
				*_m.BeneficiaryName = value.String
			}
		case transaction.FieldBeneficiaryType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field beneficiary_type", values[i])
			} else if value.Valid {
				_m.BeneficiaryType = new(transaction.BeneficiaryType)
				*_m.BeneficiaryType = transaction.BeneficiaryType(value.String)
			}
		case transaction.FieldBeneficiaryAccountIban:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field beneficiary_account_iban", values[i])
			} else if value.Valid {
				_m.BeneficiaryAccountIban = new(string)
				*_m.BeneficiaryAccountIban = value.String
			}
		case transaction.FieldBeneficiaryBankBic:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field beneficiary_bank_bic", values[i])
			} else if value.Valid {
				_m.BeneficiaryBankBic = new(string)
				*_m.BeneficiaryBankBic = value.String
			}
		case transaction.FieldBeneficiaryBankName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field beneficiary_bank_name", values[i])
			} else if value.Valid {
				_m.BeneficiaryBankName = new(string)
				*_m.BeneficiaryBankName = value.String
			}
		case transaction.FieldBeneficiaryIsoCountryCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field beneficiary_iso_country_code", values[i])
			} else if value.Valid {
				_m.BeneficiaryIsoCountryCode = new(string)
				*_m.BeneficiaryIsoCountryCode = value.String
			}
		case transaction.FieldRealBeneficiaryName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field real_beneficiary_name", values[i])
			} else if value.Valid {
				_m.RealBeneficiaryName = new(string)
				*_m.RealBeneficiaryName = value.String
			}
		case transaction.FieldRealBeneficiaryBinIin:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field real_beneficiary_bin_iin", values[i])
			} else if value.Valid {
				_m.RealBeneficiaryBinIin = new(string)
				*_m.RealBeneficiaryBinIin = value.String
			}
		case transaction.FieldRealBeneficiaryCountryCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field real_beneficiary_country_code", values[i])
			} else if value.Valid {
				_m.RealBeneficiaryCountryCode = new(string)
				*_m.RealBeneficiaryCountryCode = value.String
			}
		case transaction.FieldRealBeneficiaryType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field real_beneficiary_type", values[i])
			} else if value.Valid {
				_m.RealBeneficiaryType = new(transaction.RealBeneficiaryType)
				*_m.RealBeneficiaryType = transaction.RealBeneficiaryType(value.String)
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Transaction.
// This includes values selected through modifiers, order, etc.
func (_m *Transaction) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// Update returns a builder for updating this Transaction.
// Note that you need to call Transaction.Unwrap() before calling this method if this Transaction
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *Transaction) Update() *TransactionUpdateOne {
	return NewTransactionClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the Transaction entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *Transaction) Unwrap() *Transaction {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("ent: Transaction is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *Transaction) String() string {
	var builder strings.Builder
	builder.WriteString("Transaction(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("create_time=")
	builder.WriteString(_m.CreateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_time=")
	builder.WriteString(_m.UpdateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("transaction_number=")
	builder.WriteString(_m.TransactionNumber)
	builder.WriteString(", ")
	builder.WriteString("transaction_date=")
	builder.WriteString(_m.TransactionDate.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("transaction_type=")
	builder.WriteString(fmt.Sprintf("%v", _m.TransactionType))
	builder.WriteString(", ")
	builder.WriteString("initiator_id=")
	builder.WriteString(_m.InitiatorID)
	builder.WriteString(", ")
	builder.WriteString("idempotency_key=")
	builder.WriteString(_m.IdempotencyKey)
	builder.WriteString(", ")
	if v := _m.ValueDate; v != nil {
		builder.WriteString("value_date=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("transaction_status=")
	builder.WriteString(fmt.Sprintf("%v", _m.TransactionStatus))
	builder.WriteString(", ")
	builder.WriteString("transaction_amount=")
	builder.WriteString(_m.TransactionAmount)
	builder.WriteString(", ")
	if v := _m.TransactionComission; v != nil {
		builder.WriteString("transaction_comission=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("transaction_currency=")
	builder.WriteString(fmt.Sprintf("%v", _m.TransactionCurrency))
	builder.WriteString(", ")
	builder.WriteString("transaction_total_amount=")
	builder.WriteString(_m.TransactionTotalAmount)
	builder.WriteString(", ")
	if v := _m.TransactionDirection; v != nil {
		builder.WriteString("transaction_direction=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := _m.PurposeCode; v != nil {
		builder.WriteString("purpose_code=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.PurposeDetails; v != nil {
		builder.WriteString("purpose_details=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.PayerKod; v != nil {
		builder.WriteString("payer_kod=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.PayerBinIin; v != nil {
		builder.WriteString("payer_bin_iin=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.PayerName; v != nil {
		builder.WriteString("payer_name=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.PayerType; v != nil {
		builder.WriteString("payer_type=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := _m.PayerAccountIban; v != nil {
		builder.WriteString("payer_account_iban=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.PayerBankBic; v != nil {
		builder.WriteString("payer_bank_bic=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.PayerBankName; v != nil {
		builder.WriteString("payer_bank_name=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.PayerIsoCountryCode; v != nil {
		builder.WriteString("payer_iso_country_code=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.RealPayerName; v != nil {
		builder.WriteString("real_payer_name=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.RealPayerBinIin; v != nil {
		builder.WriteString("real_payer_bin_iin=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.RealPayerIsoCountryCode; v != nil {
		builder.WriteString("real_payer_iso_country_code=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.RealPayerType; v != nil {
		builder.WriteString("real_payer_type=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := _m.BeneficiaryKbe; v != nil {
		builder.WriteString("beneficiary_kbe=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.BeneficiaryBinIin; v != nil {
		builder.WriteString("beneficiary_bin_iin=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.BeneficiaryName; v != nil {
		builder.WriteString("beneficiary_name=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.BeneficiaryType; v != nil {
		builder.WriteString("beneficiary_type=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := _m.BeneficiaryAccountIban; v != nil {
		builder.WriteString("beneficiary_account_iban=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.BeneficiaryBankBic; v != nil {
		builder.WriteString("beneficiary_bank_bic=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.BeneficiaryBankName; v != nil {
		builder.WriteString("beneficiary_bank_name=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.BeneficiaryIsoCountryCode; v != nil {
		builder.WriteString("beneficiary_iso_country_code=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.RealBeneficiaryName; v != nil {
		builder.WriteString("real_beneficiary_name=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.RealBeneficiaryBinIin; v != nil {
		builder.WriteString("real_beneficiary_bin_iin=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.RealBeneficiaryCountryCode; v != nil {
		builder.WriteString("real_beneficiary_country_code=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.RealBeneficiaryType; v != nil {
		builder.WriteString("real_beneficiary_type=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteByte(')')
	return builder.String()
}

// Transactions is a parsable slice of Transaction.
type Transactions []*Transaction
