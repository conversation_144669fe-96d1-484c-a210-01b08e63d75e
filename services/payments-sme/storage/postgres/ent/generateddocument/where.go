// Code generated by ent, DO NOT EDIT.

package generateddocument

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldEQ(FieldID, id))
}

// ID<PERSON>Q applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldEQ(FieldUpdateTime, v))
}

// ParentTransactionID applies equality check predicate on the "parent_transaction_id" field. It's identical to ParentTransactionIDEQ.
func ParentTransactionID(v uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldEQ(FieldParentTransactionID, v))
}

// GeneratedDocumentIntegrationID applies equality check predicate on the "generated_document_integration_id" field. It's identical to GeneratedDocumentIntegrationIDEQ.
func GeneratedDocumentIntegrationID(v uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldEQ(FieldGeneratedDocumentIntegrationID, v))
}

// GeneratedDocumentType applies equality check predicate on the "generated_document_type" field. It's identical to GeneratedDocumentTypeEQ.
func GeneratedDocumentType(v string) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldEQ(FieldGeneratedDocumentType, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldLTE(FieldCreateTime, v))
}

// CreateTimeIsNil applies the IsNil predicate on the "create_time" field.
func CreateTimeIsNil() predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldIsNull(FieldCreateTime))
}

// CreateTimeNotNil applies the NotNil predicate on the "create_time" field.
func CreateTimeNotNil() predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldNotNull(FieldCreateTime))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldLTE(FieldUpdateTime, v))
}

// UpdateTimeIsNil applies the IsNil predicate on the "update_time" field.
func UpdateTimeIsNil() predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldIsNull(FieldUpdateTime))
}

// UpdateTimeNotNil applies the NotNil predicate on the "update_time" field.
func UpdateTimeNotNil() predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldNotNull(FieldUpdateTime))
}

// ParentTransactionIDEQ applies the EQ predicate on the "parent_transaction_id" field.
func ParentTransactionIDEQ(v uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldEQ(FieldParentTransactionID, v))
}

// ParentTransactionIDNEQ applies the NEQ predicate on the "parent_transaction_id" field.
func ParentTransactionIDNEQ(v uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldNEQ(FieldParentTransactionID, v))
}

// ParentTransactionIDIn applies the In predicate on the "parent_transaction_id" field.
func ParentTransactionIDIn(vs ...uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldIn(FieldParentTransactionID, vs...))
}

// ParentTransactionIDNotIn applies the NotIn predicate on the "parent_transaction_id" field.
func ParentTransactionIDNotIn(vs ...uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldNotIn(FieldParentTransactionID, vs...))
}

// ParentTransactionIDGT applies the GT predicate on the "parent_transaction_id" field.
func ParentTransactionIDGT(v uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldGT(FieldParentTransactionID, v))
}

// ParentTransactionIDGTE applies the GTE predicate on the "parent_transaction_id" field.
func ParentTransactionIDGTE(v uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldGTE(FieldParentTransactionID, v))
}

// ParentTransactionIDLT applies the LT predicate on the "parent_transaction_id" field.
func ParentTransactionIDLT(v uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldLT(FieldParentTransactionID, v))
}

// ParentTransactionIDLTE applies the LTE predicate on the "parent_transaction_id" field.
func ParentTransactionIDLTE(v uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldLTE(FieldParentTransactionID, v))
}

// GeneratedDocumentIntegrationIDEQ applies the EQ predicate on the "generated_document_integration_id" field.
func GeneratedDocumentIntegrationIDEQ(v uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldEQ(FieldGeneratedDocumentIntegrationID, v))
}

// GeneratedDocumentIntegrationIDNEQ applies the NEQ predicate on the "generated_document_integration_id" field.
func GeneratedDocumentIntegrationIDNEQ(v uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldNEQ(FieldGeneratedDocumentIntegrationID, v))
}

// GeneratedDocumentIntegrationIDIn applies the In predicate on the "generated_document_integration_id" field.
func GeneratedDocumentIntegrationIDIn(vs ...uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldIn(FieldGeneratedDocumentIntegrationID, vs...))
}

// GeneratedDocumentIntegrationIDNotIn applies the NotIn predicate on the "generated_document_integration_id" field.
func GeneratedDocumentIntegrationIDNotIn(vs ...uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldNotIn(FieldGeneratedDocumentIntegrationID, vs...))
}

// GeneratedDocumentIntegrationIDGT applies the GT predicate on the "generated_document_integration_id" field.
func GeneratedDocumentIntegrationIDGT(v uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldGT(FieldGeneratedDocumentIntegrationID, v))
}

// GeneratedDocumentIntegrationIDGTE applies the GTE predicate on the "generated_document_integration_id" field.
func GeneratedDocumentIntegrationIDGTE(v uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldGTE(FieldGeneratedDocumentIntegrationID, v))
}

// GeneratedDocumentIntegrationIDLT applies the LT predicate on the "generated_document_integration_id" field.
func GeneratedDocumentIntegrationIDLT(v uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldLT(FieldGeneratedDocumentIntegrationID, v))
}

// GeneratedDocumentIntegrationIDLTE applies the LTE predicate on the "generated_document_integration_id" field.
func GeneratedDocumentIntegrationIDLTE(v uuid.UUID) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldLTE(FieldGeneratedDocumentIntegrationID, v))
}

// GeneratedDocumentTypeEQ applies the EQ predicate on the "generated_document_type" field.
func GeneratedDocumentTypeEQ(v string) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldEQ(FieldGeneratedDocumentType, v))
}

// GeneratedDocumentTypeNEQ applies the NEQ predicate on the "generated_document_type" field.
func GeneratedDocumentTypeNEQ(v string) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldNEQ(FieldGeneratedDocumentType, v))
}

// GeneratedDocumentTypeIn applies the In predicate on the "generated_document_type" field.
func GeneratedDocumentTypeIn(vs ...string) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldIn(FieldGeneratedDocumentType, vs...))
}

// GeneratedDocumentTypeNotIn applies the NotIn predicate on the "generated_document_type" field.
func GeneratedDocumentTypeNotIn(vs ...string) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldNotIn(FieldGeneratedDocumentType, vs...))
}

// GeneratedDocumentTypeGT applies the GT predicate on the "generated_document_type" field.
func GeneratedDocumentTypeGT(v string) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldGT(FieldGeneratedDocumentType, v))
}

// GeneratedDocumentTypeGTE applies the GTE predicate on the "generated_document_type" field.
func GeneratedDocumentTypeGTE(v string) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldGTE(FieldGeneratedDocumentType, v))
}

// GeneratedDocumentTypeLT applies the LT predicate on the "generated_document_type" field.
func GeneratedDocumentTypeLT(v string) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldLT(FieldGeneratedDocumentType, v))
}

// GeneratedDocumentTypeLTE applies the LTE predicate on the "generated_document_type" field.
func GeneratedDocumentTypeLTE(v string) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldLTE(FieldGeneratedDocumentType, v))
}

// GeneratedDocumentTypeContains applies the Contains predicate on the "generated_document_type" field.
func GeneratedDocumentTypeContains(v string) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldContains(FieldGeneratedDocumentType, v))
}

// GeneratedDocumentTypeHasPrefix applies the HasPrefix predicate on the "generated_document_type" field.
func GeneratedDocumentTypeHasPrefix(v string) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldHasPrefix(FieldGeneratedDocumentType, v))
}

// GeneratedDocumentTypeHasSuffix applies the HasSuffix predicate on the "generated_document_type" field.
func GeneratedDocumentTypeHasSuffix(v string) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldHasSuffix(FieldGeneratedDocumentType, v))
}

// GeneratedDocumentTypeEqualFold applies the EqualFold predicate on the "generated_document_type" field.
func GeneratedDocumentTypeEqualFold(v string) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldEqualFold(FieldGeneratedDocumentType, v))
}

// GeneratedDocumentTypeContainsFold applies the ContainsFold predicate on the "generated_document_type" field.
func GeneratedDocumentTypeContainsFold(v string) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.FieldContainsFold(FieldGeneratedDocumentType, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.GeneratedDocument) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.GeneratedDocument) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.GeneratedDocument) predicate.GeneratedDocument {
	return predicate.GeneratedDocument(sql.NotPredicates(p))
}
