// Code generated by ent, DO NOT EDIT.

package generateddocument

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the generateddocument type in the database.
	Label = "generated_document"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreateTime holds the string denoting the create_time field in the database.
	FieldCreateTime = "create_time"
	// FieldUpdateTime holds the string denoting the update_time field in the database.
	FieldUpdateTime = "update_time"
	// FieldParentTransactionID holds the string denoting the parent_transaction_id field in the database.
	FieldParentTransactionID = "parent_transaction_id"
	// FieldGeneratedDocumentIntegrationID holds the string denoting the generated_document_integration_id field in the database.
	FieldGeneratedDocumentIntegrationID = "generated_document_integration_id"
	// FieldGeneratedDocumentType holds the string denoting the generated_document_type field in the database.
	FieldGeneratedDocumentType = "generated_document_type"
	// Table holds the table name of the generateddocument in the database.
	Table = "generated_documents"
)

// Columns holds all SQL columns for generateddocument fields.
var Columns = []string{
	FieldID,
	FieldCreateTime,
	FieldUpdateTime,
	FieldParentTransactionID,
	FieldGeneratedDocumentIntegrationID,
	FieldGeneratedDocumentType,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreateTime holds the default value on creation for the "create_time" field.
	DefaultCreateTime func() time.Time
	// DefaultUpdateTime holds the default value on creation for the "update_time" field.
	DefaultUpdateTime func() time.Time
	// UpdateDefaultUpdateTime holds the default value on update for the "update_time" field.
	UpdateDefaultUpdateTime func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// OrderOption defines the ordering options for the GeneratedDocument queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreateTime orders the results by the create_time field.
func ByCreateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateTime, opts...).ToFunc()
}

// ByUpdateTime orders the results by the update_time field.
func ByUpdateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateTime, opts...).ToFunc()
}

// ByParentTransactionID orders the results by the parent_transaction_id field.
func ByParentTransactionID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldParentTransactionID, opts...).ToFunc()
}

// ByGeneratedDocumentIntegrationID orders the results by the generated_document_integration_id field.
func ByGeneratedDocumentIntegrationID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldGeneratedDocumentIntegrationID, opts...).ToFunc()
}

// ByGeneratedDocumentType orders the results by the generated_document_type field.
func ByGeneratedDocumentType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldGeneratedDocumentType, opts...).ToFunc()
}
