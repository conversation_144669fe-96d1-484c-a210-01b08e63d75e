// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/predicate"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent/signatories"
)

// SignatoriesUpdate is the builder for updating Signatories entities.
type SignatoriesUpdate struct {
	config
	hooks     []Hook
	mutation  *SignatoriesMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the SignatoriesUpdate builder.
func (_u *SignatoriesUpdate) Where(ps ...predicate.Signatories) *SignatoriesUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetSignatoryA sets the "signatory_a" field.
func (_u *SignatoriesUpdate) SetSignatoryA(v string) *SignatoriesUpdate {
	_u.mutation.SetSignatoryA(v)
	return _u
}

// SetNillableSignatoryA sets the "signatory_a" field if the given value is not nil.
func (_u *SignatoriesUpdate) SetNillableSignatoryA(v *string) *SignatoriesUpdate {
	if v != nil {
		_u.SetSignatoryA(*v)
	}
	return _u
}

// ClearSignatoryA clears the value of the "signatory_a" field.
func (_u *SignatoriesUpdate) ClearSignatoryA() *SignatoriesUpdate {
	_u.mutation.ClearSignatoryA()
	return _u
}

// SetSignatoryB sets the "signatory_b" field.
func (_u *SignatoriesUpdate) SetSignatoryB(v string) *SignatoriesUpdate {
	_u.mutation.SetSignatoryB(v)
	return _u
}

// SetNillableSignatoryB sets the "signatory_b" field if the given value is not nil.
func (_u *SignatoriesUpdate) SetNillableSignatoryB(v *string) *SignatoriesUpdate {
	if v != nil {
		_u.SetSignatoryB(*v)
	}
	return _u
}

// ClearSignatoryB clears the value of the "signatory_b" field.
func (_u *SignatoriesUpdate) ClearSignatoryB() *SignatoriesUpdate {
	_u.mutation.ClearSignatoryB()
	return _u
}

// Mutation returns the SignatoriesMutation object of the builder.
func (_u *SignatoriesUpdate) Mutation() *SignatoriesMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *SignatoriesUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *SignatoriesUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *SignatoriesUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *SignatoriesUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *SignatoriesUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *SignatoriesUpdate {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *SignatoriesUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	_spec := sqlgraph.NewUpdateSpec(signatories.Table, signatories.Columns, sqlgraph.NewFieldSpec(signatories.FieldID, field.TypeUUID))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.SignatoryA(); ok {
		_spec.SetField(signatories.FieldSignatoryA, field.TypeString, value)
	}
	if _u.mutation.SignatoryACleared() {
		_spec.ClearField(signatories.FieldSignatoryA, field.TypeString)
	}
	if value, ok := _u.mutation.SignatoryB(); ok {
		_spec.SetField(signatories.FieldSignatoryB, field.TypeString, value)
	}
	if _u.mutation.SignatoryBCleared() {
		_spec.ClearField(signatories.FieldSignatoryB, field.TypeString)
	}
	_spec.AddModifiers(_u.modifiers...)
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{signatories.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// SignatoriesUpdateOne is the builder for updating a single Signatories entity.
type SignatoriesUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *SignatoriesMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetSignatoryA sets the "signatory_a" field.
func (_u *SignatoriesUpdateOne) SetSignatoryA(v string) *SignatoriesUpdateOne {
	_u.mutation.SetSignatoryA(v)
	return _u
}

// SetNillableSignatoryA sets the "signatory_a" field if the given value is not nil.
func (_u *SignatoriesUpdateOne) SetNillableSignatoryA(v *string) *SignatoriesUpdateOne {
	if v != nil {
		_u.SetSignatoryA(*v)
	}
	return _u
}

// ClearSignatoryA clears the value of the "signatory_a" field.
func (_u *SignatoriesUpdateOne) ClearSignatoryA() *SignatoriesUpdateOne {
	_u.mutation.ClearSignatoryA()
	return _u
}

// SetSignatoryB sets the "signatory_b" field.
func (_u *SignatoriesUpdateOne) SetSignatoryB(v string) *SignatoriesUpdateOne {
	_u.mutation.SetSignatoryB(v)
	return _u
}

// SetNillableSignatoryB sets the "signatory_b" field if the given value is not nil.
func (_u *SignatoriesUpdateOne) SetNillableSignatoryB(v *string) *SignatoriesUpdateOne {
	if v != nil {
		_u.SetSignatoryB(*v)
	}
	return _u
}

// ClearSignatoryB clears the value of the "signatory_b" field.
func (_u *SignatoriesUpdateOne) ClearSignatoryB() *SignatoriesUpdateOne {
	_u.mutation.ClearSignatoryB()
	return _u
}

// Mutation returns the SignatoriesMutation object of the builder.
func (_u *SignatoriesUpdateOne) Mutation() *SignatoriesMutation {
	return _u.mutation
}

// Where appends a list predicates to the SignatoriesUpdate builder.
func (_u *SignatoriesUpdateOne) Where(ps ...predicate.Signatories) *SignatoriesUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *SignatoriesUpdateOne) Select(field string, fields ...string) *SignatoriesUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated Signatories entity.
func (_u *SignatoriesUpdateOne) Save(ctx context.Context) (*Signatories, error) {
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *SignatoriesUpdateOne) SaveX(ctx context.Context) *Signatories {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *SignatoriesUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *SignatoriesUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *SignatoriesUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *SignatoriesUpdateOne {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *SignatoriesUpdateOne) sqlSave(ctx context.Context) (_node *Signatories, err error) {
	_spec := sqlgraph.NewUpdateSpec(signatories.Table, signatories.Columns, sqlgraph.NewFieldSpec(signatories.FieldID, field.TypeUUID))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Signatories.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, signatories.FieldID)
		for _, f := range fields {
			if !signatories.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != signatories.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.SignatoryA(); ok {
		_spec.SetField(signatories.FieldSignatoryA, field.TypeString, value)
	}
	if _u.mutation.SignatoryACleared() {
		_spec.ClearField(signatories.FieldSignatoryA, field.TypeString)
	}
	if value, ok := _u.mutation.SignatoryB(); ok {
		_spec.SetField(signatories.FieldSignatoryB, field.TypeString, value)
	}
	if _u.mutation.SignatoryBCleared() {
		_spec.ClearField(signatories.FieldSignatoryB, field.TypeString)
	}
	_spec.AddModifiers(_u.modifiers...)
	_node = &Signatories{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{signatories.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
