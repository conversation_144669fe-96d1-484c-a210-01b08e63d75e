package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx/annotation"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx/mixin"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/consts"
)

type Transaction struct {
	ent.Schema
}

//nolint:funlen
func (Transaction) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default(uuid.New).
			Annotations(annotation.CustomAnnotation{Description: "Уникальный идентификатор платежа в БД Payments SME"}),

		field.String("transaction_number").
			Unique().
			Annotations(annotation.CustomAnnotation{Description: "Номер операции, отображаемый в истории/на квитанции"}).
			Comment("Генерим по шаблону SMETR12345678"),

		field.Time("transaction_date").
			Default(time.Now).
			Annotations(annotation.CustomAnnotation{Description: "Дата и время создания операции в БД Payments SME"}),

		field.Enum("transaction_type").
			Values(
				consts.TransactionTypeTransfer.String(),
				consts.TransactionTypePayment.String(),
				consts.TransactionTypeInvoice.String(),
			).
			Annotations(annotation.CustomAnnotation{Description: "Тип транзакции"}),

		field.String("initiator_id").
			Annotations(annotation.CustomAnnotation{Description: "Идентификатор пользователя, запустившего платёж"}),

		field.String("idempotency_key").
			Unique().
			Annotations(annotation.CustomAnnotation{Description: "Ключ идемпотентности присланный системой-инициатором платежа."}),

		field.String("value_date").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Дата исполнения платежа"}),

		field.Enum("transaction_status").
			Values(
				consts.TransactionStatusInitialized.String(),
				consts.TransactionStatusInProgress.String(),
				consts.TransactionStatusCompleted.String(),
				consts.TransactionStatusRejected.String(),
			).
			Annotations(annotation.CustomAnnotation{Description: "Статус транзакции в текущий момент"}),

		field.String("transaction_amount").
			Annotations(annotation.CustomAnnotation{Description: "Сумма транзакции (без комиссий)"}),

		//nolint:misspell
		field.String("transaction_comission").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Сумма комиссий по транзакции"}),

		field.Enum("transaction_currency").
			Values(
				consts.CurrencyKZT.String(),
			).
			Annotations(annotation.CustomAnnotation{Description: "Валюта транзакции"}),

		field.String("transaction_total_amount").
			Annotations(annotation.CustomAnnotation{Description: "Сумма транзакции (с комиссиями)"}),

		field.Enum("transaction_direction").
			Values(
				consts.TransactionDirectionOutgoing.String(),
				consts.TransactionDirectionIncoming.String(),
				consts.TransactionDirectionInternal.String(),
			).
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Направление движения средств по транзакции относительно клиента"}),

		field.String("purpose_code").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Код назначения платежа"}),

		field.String("purpose_details").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Назначение платежа текстом"}),

		field.String("payer_kod").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Код Отправителя Денег"}),

		field.String("payer_bin_iin").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "БИН/ИИН отправителя"}),

		field.String("payer_name").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Название/ФИО отправителя"}),

		field.Enum("payer_type").
			Values(
				consts.PayerTypeIndividual.String(),
				consts.PayerTypeCorporate.String(),
			).
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Тип отправителя"}),

		field.String("payer_account_iban").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Номер счёта отправителя"}),

		field.String("payer_bank_bic").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "БИК банка отправителя"}),

		field.String("payer_bank_name").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Название банка отправителя"}),

		field.String("payer_iso_country_code").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Код страны резидентства отправителя"}),

		field.String("real_payer_name").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Название/ФИО фактического отправителя"}),

		field.String("real_payer_bin_iin").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "БИН/ИИН фактического отправителя"}),

		field.String("real_payer_iso_country_code").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Код страны резидентства фактического отправителя"}),

		field.Enum("real_payer_type").
			Values(
				consts.PayerTypeIndividual.String(),
				consts.PayerTypeCorporate.String(),
			).
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Тип фактического отправителя"}),

		field.String("beneficiary_kbe").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Код Бенефициара"}),

		field.String("beneficiary_bin_iin").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Номер счёта получателя"}),

		field.String("beneficiary_name").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Название/ФИО получателя"}),

		field.Enum("beneficiary_type").
			Values(
				consts.PayerTypeIndividual.String(),
				consts.PayerTypeCorporate.String(),
			).
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Тип получателя"}),

		field.String("beneficiary_account_iban").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Номер счёта получателя"}),

		field.String("beneficiary_bank_bic").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "БИК банка получателя"}),

		field.String("beneficiary_bank_name").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Название банка получателя"}),

		field.String("beneficiary_iso_country_code").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Код страны резидентства получателя"}),

		field.String("real_beneficiary_name").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Название/ФИО фактического получателя"}),

		field.String("real_beneficiary_bin_iin").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "БИН/ИИН фактического получателя"}),

		field.String("real_beneficiary_country_code").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Код страны резидентства фактического получателя"}),

		field.Enum("real_beneficiary_type").
			Values(
				consts.PayerTypeIndividual.String(),
				consts.PayerTypeCorporate.String(),
			).
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Тип фактического получателя"}),
	}
}

func (Transaction) Indexes() []ent.Index {
	return []ent.Index{}
}

func (Transaction) Edges() []ent.Edge {
	return []ent.Edge{}
}

func (Transaction) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixin.Time{},
	}
}
