package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx/annotation"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx/mixin"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/consts"
)

type AbsTransactionDocuments struct {
	ent.Schema
}

func (AbsTransactionDocuments) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Annotations(annotation.CustomAnnotation{Description: "Связь с транзакцией"}),

		field.String("reference_id").
			Annotations(annotation.CustomAnnotation{Description: "Идентификатор платежа в АБС"}),

		field.String("document_type").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Тип документа создаваемого в АБС"}),

		field.String("reference_date").
			Optional().
			Nillable().
			Annotations(annotation.CustomAnnotation{Description: "Дата и время создания операции в АБС"}),

		field.Enum("document_status").
			Optional().
			Nillable().
			Values(
				consts.ColvirDocumentStatusNotBooked.String(),
				consts.ColvirDocumentStatusBooked.String(),
				consts.ColvirDocumentStatusRejected.String(),
			).
			Annotations(annotation.CustomAnnotation{Description: "Статус документа в АБС"}),
		field.Enum("rejection_reason").
			Optional().
			Nillable().
			Values(
				consts.PaymentReasonValidationError.String(),
				consts.PaymentReasonWorktimeExceeded.String(),
				consts.PaymentReasonInsufficientFunds.String(),
				consts.PaymentReasonNoActiveAccount.String(),
				consts.PaymentReasonUnableToProcess.String(),
				consts.PaymentReasonActionIsForbidden.String(),
			).
			Annotations(annotation.CustomAnnotation{Description: "Причина отказа в колвире"}),
	}
}

func (AbsTransactionDocuments) Indexes() []ent.Index {
	return []ent.Index{}
}

func (AbsTransactionDocuments) Edges() []ent.Edge {
	return []ent.Edge{}
}

func (AbsTransactionDocuments) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixin.Time{},
	}
}
