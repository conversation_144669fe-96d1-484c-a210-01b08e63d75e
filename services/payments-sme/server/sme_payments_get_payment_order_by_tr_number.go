package server

import (
	"context"

	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/payments-sme"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/entity"
)

func (s *Server) SmePaymentsGetPaymentOrderByTrNumber(ctx context.Context, req *pb.SmePaymentsGetPaymentOrderByTrNumberReq) (*pb.SmePaymentsGetPaymentOrderByTrNumberResp, error) {
	smePaymentsGetPaymentOrderByTrNumberEntity := entity.MakeSmePaymentsGetPaymentOrderByTrNumberPbToEntity(req)

	smePaymentsGetPaymentOrderByTrNumber, err := s.useCase.SmePaymentsGetPaymentOrderByTrNumber(ctx, smePaymentsGetPaymentOrderByTrNumberEntity)
	if err != nil {
		return nil, err
	}

	return entity.MakeSmePaymentsGetPaymentOrderByTrNumberEntityToPb(smePaymentsGetPaymentOrderByTrNumber), nil
}
