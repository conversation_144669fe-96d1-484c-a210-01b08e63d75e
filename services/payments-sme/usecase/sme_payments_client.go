package usecase

import (
	"context"
	"errors"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	paymentsSmeErrs "git.redmadrobot.com/zaman/backend/zaman/errs/paymentsSme"
	usersErrs "git.redmadrobot.com/zaman/backend/zaman/errs/users"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/entity"
	pkbBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/pkb-bridge"
	"git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"
)

func (u *useCasesImpl) SmePaymentsClient(ctx context.Context, req *entity.SmePaymentsClientReq) (*entity.SmePaymentsClientResult, error) {
	logger := logs.FromContext(ctx)

	origin, err := utils.GetOrigin(ctx)
	if err != nil {
		logger.Error().Msgf("error getting origin from context: %+v", err)
		return nil, paymentsSmeErrs.PaymentsSmeErrs().InternalErrorError()
	}
	logger.Debug().Interface("origin", origin).Msg("got origin from context")

	if origin != utils.UserOriginSme {
		logger.Error().Msgf("origin not sme: %s", origin)
		return nil, paymentsSmeErrs.PaymentsSmeErrs().OriginNotSMEError()
	}

	userID, err := utils.GetUserIDFromContext(ctx)
	if err != nil {
		logger.Error().Msgf("error getting user id from context: %+v", err)
		return nil, paymentsSmeErrs.PaymentsSmeErrs().InternalErrorError()
	}
	logger.Debug().Interface("userID", userID).Msg("got user id from context")

	user, err := u.Providers.Users.GetUserByID(ctx, &users.GetUserByIDReq{
		ID: userID,
	})
	if err != nil {
		if errors.Is(err, usersErrs.UsersErrs().BlockedError()) || errors.Is(err, usersErrs.UsersErrs().NotFoundError()) {
			logger.Error().Msgf("user is blocked or not found: %+v", err)
			return nil, paymentsSmeErrs.PaymentsSmeErrs().ForbiddenError()
		}
		logger.Error().Msgf("error getting user by id: %+v", err)
		return nil, paymentsSmeErrs.PaymentsSmeErrs().InternalErrorError()
	}
	logger.Debug().Interface("user", user).Msgf("got user by id: %s", userID)

	personalInfo, err := u.Providers.Pkbbridge.GetPersonalInfoByIin(ctx, &pkbBridge.GetPersonalInfoByIinReq{
		Iin:             user.Iin,
		MaxDataAgeHours: 14 * 24, // 14 календарных дней - максимально допустимый срок годности данных из БД Postgres (personal_infos)
	})
	if err != nil {
		logger.Error().Msgf("error getting personal info by iin: %+v", err)
		return nil, paymentsSmeErrs.PaymentsSmeErrs().IntegrationErrorError()
	}
	if personalInfo == nil {
		logger.Error().Msgf("personal info is nil")
		return nil, paymentsSmeErrs.PaymentsSmeErrs().IntegrationErrorError()
	}
	logger.Debug().Interface("personalInfo", personalInfo).Msgf("got personal info by iin: %s", user.Iin)

	jurSearch, err := u.Providers.Pkbbridge.SendJurSearchByIin(ctx, &pkbBridge.SendJurSearchByIinReq{
		Iin: user.Iin,
	})
	if err != nil {
		logger.Error().Msgf("error sending jur search by iin: %+v", err)
		return nil, paymentsSmeErrs.PaymentsSmeErrs().IntegrationErrorError()
	}
	if jurSearch == nil {
		logger.Error().Msgf("jur search is nil")
		return nil, paymentsSmeErrs.PaymentsSmeErrs().IntegrationErrorError()
	}
	logger.Debug().Interface("jurSearch", jurSearch).Msgf("got jur search by iin: %s", user.Iin)

	clientResult := &entity.SmePaymentsClientResult{
		Iin:            personalInfo.Iin,
		Name:           personalInfo.Name,
		Surname:        personalInfo.Surname,
		Patronymic:     personalInfo.Patronymic,
		Birthdate:      personalInfo.Dob,
		EnterpriseName: jurSearch.Name,
		// Пока нет возможности получить KATO код и ID адреса предприятия - нужно дополнить метод SendJurSearchByIin
		// EnterpriseAddressKATOCode: , // TODO
		// EnterpriseAddressKATOId: , // TODO
	}

	return clientResult, nil
}
