package usecase

import (
	"context"
	"errors"
	"strconv"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	errsPaymentsSme "git.redmadrobot.com/zaman/backend/zaman/errs/paymentsSme"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/entity"
)

// SmePaymentsGetPaymentOrderByTrNumber - получает информацию о платежном поручении по номеру транзакции.
// Является оберткой над SmePaymentsGetPaymentOrder, которая сначала ищет транзакцию по номеру,
// а затем вызывает SmePaymentsGetPaymentOrder
func (u *useCasesImpl) SmePaymentsGetPaymentOrderByTrNumber(
	ctx context.Context,
	req *entity.SmePaymentsGetPaymentOrderByTrNumberReq,
) (*entity.SmePaymentsGetPaymentOrderByTrNumberResult, error) {
	logger := logs.FromContext(ctx)

	if req.TransactionNumber == "" {
		logger.Error().Msg("transaction number is empty")

		return nil, errsPaymentsSme.PaymentsSmeErrs().TransactionNumberIsEmptyError()
	}

	// Ищем в БД транзакцию с соответствующим transactionNumber. Если не нашли возвращаем 404 ошибку.
	transaction, err := u.Providers.Storage.GetTransactionByNumber(ctx, req.TransactionNumber)
	if err != nil {
		if errors.Is(err, consts.ErrTransactionNotFound) {
			logger.Error().Err(err).Msgf("transaction not found by number: %s", req.TransactionNumber)

			return nil, errsPaymentsSme.PaymentsSmeErrs().TransactionNotFoundError()
		}
		logger.Error().Err(err).Msgf("failed to get transaction by number: %s", req.TransactionNumber)

		return nil, errsPaymentsSme.PaymentsSmeErrs().InternalErrorError()
	}

	// Если нашли, вызываем SmePaymentsGetPaymentOrder с ID найденной транзакции.
	paymentOrder, err := u.SmePaymentsGetPaymentOrder(ctx, &entity.SmePaymentsGetPaymentOrderReq{TransactionID: transaction.ID.String()})
	if paymentOrder == nil {
		return nil, err
	}

	paymentOrderVersion, err := strconv.Atoi(paymentOrder.Version)
	if err != nil {
		logger.Error().Err(err).Msgf("failed to convert version to int: %s", paymentOrder.Version)

		return nil, errsPaymentsSme.PaymentsSmeErrs().InternalErrorError()
	}

	return &entity.SmePaymentsGetPaymentOrderByTrNumberResult{
		ID:                transaction.ID.String(),
		Title:             paymentOrder.Title,
		Link:              paymentOrder.Link,
		Version:           int32(paymentOrderVersion), //nolint:gosec
		TransactionStatus: string(transaction.TransactionStatus),
	}, err
}
