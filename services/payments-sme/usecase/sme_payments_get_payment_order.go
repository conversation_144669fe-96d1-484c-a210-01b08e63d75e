package usecase

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"github.com/google/uuid"

	"git.redmadrobot.com/backend-go/rmr-pkg/tools/moneywords"
	"github.com/shopspring/decimal"

	errsPaymentsSme "git.redmadrobot.com/zaman/backend/zaman/errs/paymentsSme"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/helpers/locale"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils/fdate"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/consts/configs"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/entity"
	colvirBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
	"git.redmadrobot.com/zaman/backend/zaman/specs/proto/documents"
)

const (
	paymentTmpl     = "paymentOrderSMEIP"
	massPaymentTmpl = "massPaymentOrderSMEIP"
)

// SmePaymentsGetPaymentOrder - получает информацию о платежном поручении по transactionId.
// Ссылка на документацию: https://rmrkz.atlassian.net/wiki/spaces/ZS/pages/109839339/PAYM.ORDERS+-
func (u *useCasesImpl) SmePaymentsGetPaymentOrder(ctx context.Context, req *entity.SmePaymentsGetPaymentOrderReq) (*entity.SmePaymentsGetPaymentOrderResult, error) {
	logger := logs.FromContext(ctx)

	transactionUUID, err := uuid.Parse(req.TransactionID)
	if err != nil {
		logger.Error().Err(err).Msgf("failed to parse transaction id: %s", req.TransactionID)
		return nil, errsPaymentsSme.PaymentsSmeErrs().ValidationErrorError()
	}

	// Получаем данные документа колвира из БД
	absTrxDoc, err := u.Providers.Storage.GetAbsTransactionDocumentByID(ctx, transactionUUID)
	if err != nil {
		if errors.Is(err, consts.ErrAbsTransactionDocumentNotFound) {
			logger.Error().Err(err).Msgf("abs transaction document not found: %s", transactionUUID)
			return nil, errsPaymentsSme.PaymentsSmeErrs().AbsTransactionDocumentNotFoundError()
		}
		logger.Error().Err(err).Msgf("failed to get abs transaction document by id: %s", transactionUUID)
		return nil, errsPaymentsSme.PaymentsSmeErrs().InternalErrorError()
	}
	logger.Debug().Interface("absTrxDoc", absTrxDoc).Msgf("abs transaction document with transaction id: %s", transactionUUID)

	// 1. Ищем в БД транзакцию с соответствующим transactionId. Если не нашли возвращаем 404 ошибку.
	transaction, err := u.Providers.Storage.GetTransactionByID(ctx, transactionUUID)
	if err != nil {
		if errors.Is(err, consts.ErrTransactionNotFound) {
			logger.Error().Err(err).Msgf("transaction not found: %s", transactionUUID)
			return nil, errsPaymentsSme.PaymentsSmeErrs().TransactionNotFoundError()
		}
		logger.Error().Err(err).Msgf("failed to get transaction by id: %s", transactionUUID)
		return nil, errsPaymentsSme.PaymentsSmeErrs().InternalErrorError()
	}

	// проверяем тип транзакции. Должен быть "PAYMENT"
	if transaction.TransactionType != consts.TransactionTypePayment {
		logger.Error().Msgf("invalid transaction_type. Transaction type: %v", transaction.TransactionType)
		return nil, errsPaymentsSme.PaymentsSmeErrs().ValidationErrorError()
	}

	// 2. Актуализируем статус Проверяем статус транзакции (transaction_status).
	err = u.actualizeTransactionStatus(ctx, transaction, absTrxDoc)
	if err != nil {
		return nil, err
	}

	// 3. Проверяем наличие generated_document_integration_id (идентификатора сгенерированного документа) в БД
	genDocType := consts.GenDocTypePaymentOrder // возможно тип документа будет меняться в будущих дополнениях логики, пока будет эта константа

	// genDoc - документ, который уже был сгенерирован ранее
	genDoc, err := u.Providers.Storage.GetGeneratedDocumentByTypeAndTrxID(ctx, genDocType, transactionUUID)
	if err != nil {
		if errors.Is(err, consts.ErrGeneratedDocumentNotFound) {
			// Если документ не нашелся - не передаем generated_document_integration_id в запрос генерации документа
			logger.Warn().Err(err).Msgf("generated document not found by parent_transaction_id (%s) and generated_document_type (%s)", transactionUUID, genDocType)
		} else {
			logger.Error().Err(err).Msgf("failed to get generated document by parent_transaction_id (%s) and generated_document_type (%s)", transactionUUID, genDocType)
			return nil, errsPaymentsSme.PaymentsSmeErrs().InternalErrorError()
		}
	}

	// 4. Генерируем поле writtenAmount из amount транзакции (сумма платежа прописью)
	writtenAmount, err := u.getWrittenAmount(ctx, transaction.TransactionTotalAmount)
	if err != nil {
		logger.Error().Err(err).Msgf("failed to get written amount for transaction id: %s", transactionUUID)
		return nil, errsPaymentsSme.PaymentsSmeErrs().InternalErrorError()
	}
	logger.Debug().Interface("writtenAmount", writtenAmount).Msgf("writtenAmount from transaction.TransactionTotalAmount: %v", transaction.TransactionTotalAmount)

	// 5. Получаем платеж из БД (payments)
	payment, err := u.Providers.Storage.GetPaymentByPaymentID(ctx, transactionUUID)
	if err != nil {
		if errors.Is(err, consts.ErrPaymentNotFound) {
			logger.Error().Err(err).Msgf("payment not found by id: %s", transactionUUID)
			return nil, errsPaymentsSme.PaymentsSmeErrs().PaymentNotFoundError()
		}
		logger.Error().Err(err).Msgf("failed to get payment by id: %s", transactionUUID)
		return nil, errsPaymentsSme.PaymentsSmeErrs().InternalErrorError()
	}

	// Получаем подписи из БД, чтобы передать их в запрос на генерацию документа (если нет подписей, то оставить пустыми)
	signatories, err := u.Providers.Storage.GetSignatoriesByTransactionID(ctx, transactionUUID)
	if err != nil {
		if errors.Is(err, consts.ErrSignatoryNotFound) {
			logger.Warn().Err(err).Msgf("signatories not found by transaction id: %s", transactionUUID)
			signatories = &entity.Signatory{}
		} else {
			logger.Error().Err(err).Msgf("failed to get signatories by transaction id: %s", transactionUUID)
			return nil, errsPaymentsSme.PaymentsSmeErrs().InternalErrorError()
		}
	}
	logger.Debug().Interface("signatories", signatories).Msgf("signatories with transaction id: %s", transactionUUID)

	// 6. Выполняем запрос на генерацию документа в зависимости от типа документа
	genDocResp, err := u.generatePaymentDocument(ctx, transaction, absTrxDoc, payment, signatories, writtenAmount, transactionUUID)
	if err != nil {
		return nil, err
	}

	// 7. Сохраняем/обновляем запись в generated_documents
	err = u.upsertGeneratedDocument(ctx, transactionUUID, genDocResp, genDoc)
	if err != nil {
		return nil, err
	}

	return &entity.SmePaymentsGetPaymentOrderResult{
		Title:   genDocResp.Title,
		Link:    genDocResp.Link,
		Version: strconv.Itoa(int(genDocResp.Version)),
	}, nil
}

// actualizeTransactionStatus - актуализирует статус транзакции в БД.
func (u *useCasesImpl) actualizeTransactionStatus(ctx context.Context, transaction *entity.Transaction, absTrxDoc *entity.AbsTransactionDocument) error {
	logger := logs.FromContext(ctx)

	// Если статус финальный (REJECTED, COMPLETED), то актуализация не нужна
	if transaction.TransactionStatus == consts.TransactionStatusCompleted || transaction.TransactionStatus == consts.TransactionStatusRejected {
		return nil
	}

	// Передаем тип клиента для колвира (MBU)
	clientType := consts.CustomerTypeIndividualEntrepreneur.String()

	// Идем в колвир, чтобы получить актуальный статус транзакции
	loadDomesticPaymentStatusResp, err := u.Providers.Colvirbridge.LoadDomesticPaymentStatus(ctx, &colvirBridge.LoadDomesticPaymentStatusRequest{
		PaymentIds: []string{absTrxDoc.ReferenceID},
		ClientType: &clientType,
	})
	if err != nil {
		logger.Error().Err(err).Msgf("failed to load domestic payment status: %s", absTrxDoc.ReferenceID)
		return errsPaymentsSme.PaymentsSmeErrs().IntegrationErrorError()
	}
	logger.Debug().Interface("loadDomesticPaymentStatusResp", loadDomesticPaymentStatusResp).Msgf("response from colvirbridge with payment id: %s", absTrxDoc.ReferenceID)

	if loadDomesticPaymentStatusResp.GetErrorStatus() != colvirBridge.ErrorStatus_NoError {
		logger.Error().Msgf("error status from colvirbridge: %s", loadDomesticPaymentStatusResp.GetErrorStatus())
		return errsPaymentsSme.PaymentsSmeErrs().IntegrationErrorError()
	}

	if len(loadDomesticPaymentStatusResp.PaymentStatuses) != 1 {
		logger.Error().Msgf("expected 1 payment status, got %d", len(loadDomesticPaymentStatusResp.PaymentStatuses))
		return errsPaymentsSme.PaymentsSmeErrs().IntegrationErrorError()
	}

	paymentStatus := loadDomesticPaymentStatusResp.PaymentStatuses[0]

	// Определяем статус транзакции на основе статуса платежа в колвир и обновляем его в БД
	var transactionStatus consts.TransactionStatus
	switch paymentStatus.StatusCode {
	case string(consts.ColvirDocumentStatusNull):
		transactionStatus = consts.TransactionStatusInitialized
	case string(consts.ColvirDocumentStatusNotBooked):
		transactionStatus = consts.TransactionStatusInProgress
	case string(consts.ColvirDocumentStatusBooked):
		transactionStatus = consts.TransactionStatusCompleted
	case string(consts.ColvirDocumentStatusRejected):
		transactionStatus = consts.TransactionStatusRejected
	default:
		logger.Error().Interface("paymentStatus", paymentStatus).Msgf("unexpected payment status from colvirbridge")
		return errsPaymentsSme.PaymentsSmeErrs().IntegrationErrorError()
	}

	// Если статус изменился, обновляем его в БД
	if transactionStatus != transaction.TransactionStatus {
		err := u.Providers.Storage.UpdateTransactionStatus(ctx, transaction.ID, transactionStatus)
		if err != nil {
			logger.Error().Err(err).Msgf("failed to update transaction status: %s", transaction.ID)
			return errsPaymentsSme.PaymentsSmeErrs().InternalErrorError()
		}
		logger.Info().Msgf("transaction status updated: %s -> %s", transaction.TransactionStatus, transactionStatus)

		transaction.TransactionStatus = transactionStatus
	}

	return nil
}

// generatePaymentDocument - генерирует документ в зависимости от типа шаблона (обычный или массовый платеж)
func (u *useCasesImpl) generatePaymentDocument(
	ctx context.Context,
	transaction *entity.Transaction,
	absTrxDoc *entity.AbsTransactionDocument,
	payment *entity.Payment,
	signatories *entity.Signatory,
	writtenAmount string,
	transactionUUID uuid.UUID,
) (*documents.DocumentResp, error) {
	logger := logs.FromContext(ctx)

	// Определяем тип шаблона на основе наличия сотрудников
	// paymentOrderSMEIP - если payments.employee_list IS NULL
	// massPaymentOrderSMEIP - payments.employee_list IS NOT NULL
	tmpl := paymentTmpl
	if len(payment.EmployeeList) > 0 {
		tmpl = massPaymentTmpl
	}

	var genDocResp *documents.DocumentResp

	switch tmpl {
	case paymentTmpl:
		genDocReq, err := u.makePaymentOrderSMEIPReq(ctx, transaction, absTrxDoc, payment, signatories, writtenAmount)
		if err != nil {
			logger.Error().Err(err).Msgf("failed to make payment order smeip req with transaction id: %s", transactionUUID)
			return nil, errsPaymentsSme.PaymentsSmeErrs().InternalErrorError()
		}

		genDocResp, err = u.Providers.Documents.GeneratePaymentOrderSMEIP(ctx, genDocReq)
		if err != nil {
			logger.Error().Err(err).Msgf("failed to generate payment order smeip with transaction id: %s", transactionUUID)
			return nil, errsPaymentsSme.PaymentsSmeErrs().IntegrationErrorError()
		}
		logger.Debug().Interface("genDocResp", genDocResp).Msgf("response from documents service with payment id: %s", transactionUUID)

	case massPaymentTmpl:
		genDocReq, err := u.makeMassPaymentOrderSMEIPReq(ctx, transaction, absTrxDoc, payment, signatories, writtenAmount)
		if err != nil {
			logger.Error().Err(err).Msgf("failed to make mass payment order smeip req with transaction id: %s", transactionUUID)
			return nil, errsPaymentsSme.PaymentsSmeErrs().InternalErrorError()
		}

		genDocResp, err = u.Providers.Documents.GenerateMassPaymentOrderSMEIP(ctx, genDocReq)
		if err != nil {
			logger.Error().Err(err).Msgf("failed to generate mass payment order smeip with transaction id: %s", transactionUUID)
			return nil, errsPaymentsSme.PaymentsSmeErrs().IntegrationErrorError()
		}
		logger.Debug().Interface("genDocResp", genDocResp).Msgf("response from documents service with payment id: %s", transactionUUID)
	}

	if genDocResp == nil {
		logger.Error().Msgf("genDocResp is nil")
		return nil, errsPaymentsSme.PaymentsSmeErrs().InternalErrorError()
	}

	return genDocResp, nil
}

// makePaymentOrderSMEIPReq - формирует запрос на генерацию платежного поручения без реестра
func (u *useCasesImpl) makePaymentOrderSMEIPReq(
	ctx context.Context,
	transaction *entity.Transaction,
	absTrxDoc *entity.AbsTransactionDocument,
	payment *entity.Payment,
	signatories *entity.Signatory,
	writtenAmount string,
) (*documents.GeneratePaymentOrderSMEIPReq, error) {
	logger := logs.FromContext(ctx)

	loc, err := utils.GetLocaleFromContext(ctx, true, true)
	if err != nil {
		loc = locale.Kk
	}

	lang := documents.LK_LK_RU
	if loc == locale.Kk {
		lang = documents.LK_LK_KZ
	}

	// Получаем конфиги для платежа
	paymentConfigs := configs.GetConfig(consts.PaymentCode(payment.PaymentCode), loc)
	if paymentConfigs == nil {
		logger.Error().Msgf("failed to get payment configs for payment code: %s", payment.PaymentCode)
		return nil, fmt.Errorf("failed to get payment configs for payment code: %s", payment.PaymentCode)
	}

	// Определяем статус документа в платежном поручении
	docPDFStatus, err := u.transactionStatusToDocumentStatus(ctx, transaction.TransactionStatus, loc)
	if err != nil {
		return nil, err
	}

	// Преобразуем даты в формат DD.MM.YYYY
	valueDateDotFormatted, err := utils.ParseStringToDotFormat(getStringValue(transaction.ValueDate))
	if err != nil {
		logger.Error().Err(err).Msgf("failed to get dot formatted value date for transaction id: %s", transaction.ID)
		return nil, err
	}

	referenceDateDotFormatted, err := utils.ParseStringToDotFormat(getStringValue(absTrxDoc.ReferenceDate))
	if err != nil {
		logger.Error().Err(err).Msgf("failed to get dot formatted reference date for transaction id: %s", transaction.ID)
		return nil, err
	}

	// Преобразуем период платежа в формат MM.YYYY
	paymentPeriod, err := u.paymentPeriodToDotFormat(payment.PaymentPeriod)
	if err != nil {
		logger.Error().Err(err).Msgf("failed to get dot formatted payment period for payment id: %s", payment.ID)
		return nil, err
	}

	// PurposeDetails должен быть в формате "Обязательные пенсионные взносы за Декабрь 2025"
	purposeDetails, err := u.getPurposeDetails(ctx, transaction, payment)
	if err != nil {
		logger.Error().Err(err).Msgf("failed to get purpose details for payment id: %s", payment.ID)
		return nil, err
	}

	return &documents.GeneratePaymentOrderSMEIPReq{
		TransactionNumber: transaction.TransactionNumber,
		TransactionStatus: string(docPDFStatus),

		// Данные плательщика
		PayerName:     getStringValue(transaction.PayerName),
		PayerBINIIN:   getStringValue(transaction.PayerBinIin),
		PayerAccount:  getStringValue(transaction.PayerAccountIban),
		PayerKod:      getStringValue(transaction.PayerKod),
		PayerCountry:  getStringValue(transaction.PayerIsoCountryCode),
		PayerBankName: getStringValue(transaction.PayerBankName),
		PayerBankBIC:  getStringValue(transaction.PayerBankBic),

		// Данные получателя
		BeneficiaryName:     paymentConfigs.BeneficiaryName,
		BeneficiaryBINIIN:   paymentConfigs.BeneficiaryBINIIN,
		BeneficiaryAccount:  paymentConfigs.BeneficiaryAccount,
		BeneficiaryKbe:      paymentConfigs.BeneficiaryKbe,
		BeneficiaryCountry:  paymentConfigs.BeneficiaryCountry,
		BeneficiaryBankName: paymentConfigs.BeneficiaryBankName,
		BeneficiaryBankBIC:  paymentConfigs.BeneficiaryBankBIC,

		// Данные фактического получателя (сотрудника)
		RealBeneficiaryName:    getStringValue(transaction.PayerName),
		RealBeneficiaryBINIIN:  getStringValue(transaction.PayerBinIin),
		RealBeneficiaryCountry: getStringValue(transaction.PayerIsoCountryCode),

		// Назначение платежа
		PurposeDetails: purposeDetails,
		PurposeCode:    getStringValue(transaction.PurposeCode),

		// Сумма и даты
		WrittenAmount: writtenAmount,
		Amount:        transaction.TransactionTotalAmount,
		ValueDate:     valueDateDotFormatted,

		// Дата платежа
		ReferenceDate: referenceDateDotFormatted,

		// КБК
		Kbk: payment.KBK,

		// Период платежа
		PaymentPeriod: paymentPeriod,

		// Подписи
		SignatoryA: getStringValueOrNotRequired(signatories.SignatoryA, loc),
		SignatoryB: getStringValueOrNotRequired(signatories.SignatoryB, loc),

		Lang: lang,
	}, nil
}

func (u *useCasesImpl) makeMassPaymentOrderSMEIPReq(
	ctx context.Context,
	transaction *entity.Transaction,
	absTrxDoc *entity.AbsTransactionDocument,
	payment *entity.Payment,
	signatories *entity.Signatory,
	writtenAmount string,
) (*documents.GenerateMassPaymentOrderSMEIPReq, error) {
	logger := logs.FromContext(ctx)

	loc, err := utils.GetLocaleFromContext(ctx, true, true)
	if err != nil {
		loc = locale.Kk
	}

	lang := documents.LK_LK_RU
	if loc == locale.Kk {
		lang = documents.LK_LK_KZ
	}

	docPDFStatus, err := u.transactionStatusToDocumentStatus(ctx, transaction.TransactionStatus, loc)
	if err != nil {
		return nil, err
	}

	// Парсим EmployeeList из JSON
	employeeList, err := u.parseEmployeeList(payment.EmployeeList)
	if err != nil {
		logger.Error().Err(err).Msgf("failed to parse employee list from payment.EmployeeList")
		return nil, err
	}

	paymentOrders := make([]*documents.GeneratePaymentOrderSMEIPReq, len(employeeList))

	for i, employee := range employeeList {
		paymentOrders[i], err = u.makePaymentOrderSMEIPReqForEmployee(ctx, transaction, absTrxDoc, payment, employee, signatories, strconv.Itoa(i+1))
		if err != nil {
			logger.Error().Err(err).Msgf("failed to make payment order smeip req for employee: %s", employee.Name)
			return nil, errsPaymentsSme.PaymentsSmeErrs().InternalErrorError()
		}
	}

	// Преобразуем дату валютирования в формат DD.MM.YYYY
	valueDateDotFormatted, err := utils.ParseStringToDotFormat(getStringValue(transaction.ValueDate))
	if err != nil {
		logger.Error().Err(err).Msgf("failed to get dot formatted value date for transaction id: %s", transaction.ID)
		return nil, err
	}

	// Преобразуем дату проведения платежа в колвир в формат DD.MM.YYYY
	referenceDateDotFormatted, err := utils.ParseStringToDotFormat(getStringValue(absTrxDoc.ReferenceDate))
	if err != nil {
		logger.Error().Err(err).Msgf("failed to get dot formatted reference date for transaction id: %s", transaction.ID)
		return nil, err
	}

	return &documents.GenerateMassPaymentOrderSMEIPReq{
		TransactionNumber: transaction.TransactionNumber,
		TransactionStatus: string(docPDFStatus),

		// Данные плательщика
		PayerBankName: getStringValue(transaction.PayerBankName),
		PayerBankBIC:  getStringValue(transaction.PayerBankBic),

		// Данные получателя
		BeneficiaryBankName: getStringValue(transaction.BeneficiaryBankName),
		BeneficiaryBankBIC:  getStringValue(transaction.BeneficiaryBankBic),
		BeneficiaryAccount:  getStringValue(transaction.BeneficiaryAccountIban),

		// Количество платежных поручений (employeeList)
		CountEmployeeList: uint64(len(employeeList)),

		ValueDate:     valueDateDotFormatted,
		ReferenceDate: referenceDateDotFormatted,

		WrittenAmount: writtenAmount,
		Amount:        transaction.TransactionTotalAmount,

		SignatoryA: getStringValueOrNotRequired(signatories.SignatoryA, loc),
		SignatoryB: getStringValueOrNotRequired(signatories.SignatoryB, loc),

		PaymentOrder: paymentOrders,

		Lang: lang,
	}, nil
}

func (u *useCasesImpl) upsertGeneratedDocument(
	ctx context.Context,
	transactionUUID uuid.UUID,
	genDocResp *documents.DocumentResp,
	genDoc *entity.GeneratedDocument,
) error {
	logger := logs.FromContext(ctx)
	var err error

	// genDocUpsert - запись для сохранения/обновления в generated_documents
	genDocUpsert := &entity.GeneratedDocument{
		ParentTransactionID:   transactionUUID,
		GeneratedDocumentType: consts.GenDocTypePaymentOrder.String(),
	}

	if genDoc == nil || genDoc.ID == uuid.Nil {
		genDocUpsert.ID = uuid.New()
	} else {
		genDocUpsert.ID = genDoc.ID
	}

	if genDocResp.Id != "" {
		genDocUpsert.GeneratedDocumentIntegrationID, err = uuid.Parse(genDocResp.Id)
		if err != nil {
			logger.Error().Err(err).Msgf("failed to parse generated document integration id: %s", genDocResp.Id)
			return errsPaymentsSme.PaymentsSmeErrs().InternalErrorError()
		}
	}

	err = u.Providers.Storage.UpsertGeneratedDocument(ctx, genDocUpsert)
	if err != nil {
		logger.Error().Err(err).Msgf("failed to upsert generated document for transaction id: %s", transactionUUID)
		return errsPaymentsSme.PaymentsSmeErrs().InternalErrorError()
	}

	return nil
}

// parseEmployeeList парсит EmployeeList из JSON map[string]any в структурированные данные
func (u *useCasesImpl) parseEmployeeList(employeeListData map[string]any) ([]entity.EmployeeItem, error) {
	if employeeListData == nil {
		return []entity.EmployeeItem{}, nil
	}

	// Сериализуем в JSON для корректного парсинга
	raw, err := json.Marshal(employeeListData)
	if err != nil {
		return nil, err
	}

	var employeeList entity.EmployeeList
	if err := json.Unmarshal(raw, &employeeList); err != nil {
		return nil, err
	}

	// Конвертируем в EmployeeItem для совместимости
	result := make([]entity.EmployeeItem, len(employeeList.Employees))
	for i, emp := range employeeList.Employees {
		result[i] = entity.EmployeeItem{
			Name:        emp.Name,
			MiddleName:  getStringPointer(emp.MiddleName),
			LastName:    emp.LastName,
			IIN:         emp.IIN,
			Birthday:    emp.Birthday,
			Country:     emp.Country,
			Amount:      emp.Amount,
			ValuePeriod: emp.ValuePeriod,
		}
	}

	return result, nil
}

// makePaymentOrderSMEIPReqForEmployee создает запрос для генерации платежного поручения для конкретного сотрудника
func (u *useCasesImpl) makePaymentOrderSMEIPReqForEmployee(
	ctx context.Context,
	transaction *entity.Transaction,
	absTrxDoc *entity.AbsTransactionDocument,
	payment *entity.Payment,
	employee entity.EmployeeItem,
	signatories *entity.Signatory,
	employeeOrderNumber string,
) (*documents.GeneratePaymentOrderSMEIPReq, error) {
	logger := logs.FromContext(ctx)

	loc, err := utils.GetLocaleFromContext(ctx, true, true)
	if err != nil {
		loc = locale.Kk
	}

	// Получаем конфиги для платежа
	paymentConfigs := configs.GetConfig(consts.PaymentCode(payment.PaymentCode), loc)
	if paymentConfigs == nil {
		logger.Error().Msgf("failed to get payment configs for payment code: %s", payment.PaymentCode)
		return nil, fmt.Errorf("failed to get payment configs for payment code: %s", payment.PaymentCode)
	}

	docPDFStatus, err := u.transactionStatusToDocumentStatus(ctx, transaction.TransactionStatus, loc)
	if err != nil {
		return nil, err
	}

	// Формируем полное имя сотрудника
	employeeName := employee.LastName + " " + employee.Name
	if employee.MiddleName != nil && *employee.MiddleName != "" {
		employeeName += " " + *employee.MiddleName
	}

	// Формируем сумму платежя для сотрудника прописью
	employeeWrittenAmount, err := u.getWrittenAmount(ctx, employee.Amount)
	if err != nil {
		logger.Error().Err(err).Msgf("failed to get employee written amount for employee: %s", employee.Name)
		return nil, fmt.Errorf("failed to get employee written amount: %w", err)
	}

	// Преобразуем дату валютирования в формат DD.MM.YYYY
	valueDateDotFormatted, err := utils.ParseStringToDotFormat(getStringValue(transaction.ValueDate))
	if err != nil {
		logger.Error().Err(err).Msgf("failed to get dot formatted value date for transaction id: %s", transaction.ID)
		return nil, err
	}

	// Преобразуем дату проведения платежа в колвир в формат DD.MM.YYYY
	referenceDateDotFormatted, err := utils.ParseStringToDotFormat(getStringValue(absTrxDoc.ReferenceDate))
	if err != nil {
		logger.Error().Err(err).Msgf("failed to get dot formatted reference date for transaction id: %s", transaction.ID)
		return nil, err
	}

	// Преобразуем период платежа в формат MM.YYYY
	paymentPeriod, err := u.paymentPeriodToDotFormat(employee.ValuePeriod)
	if err != nil {
		logger.Error().Err(err).Msgf("failed to get dot formatted payment period for payment id: %s", payment.ID)
		return nil, err
	}

	purposeDetails, err := u.getPurposeDetails(ctx, transaction, payment)
	if err != nil {
		logger.Error().Err(err).Msgf("failed to get purpose details for payment id: %s", payment.ID)
		return nil, err
	}

	return &documents.GeneratePaymentOrderSMEIPReq{
		TransactionNumber: transaction.TransactionNumber,
		TransactionStatus: string(docPDFStatus),

		// Данные плательщика
		PayerName:     getStringValue(transaction.PayerName),
		PayerBINIIN:   getStringValue(transaction.PayerBinIin),
		PayerAccount:  getStringValue(transaction.PayerAccountIban),
		PayerKod:      getStringValue(transaction.PayerKod),
		PayerCountry:  getStringValue(transaction.PayerIsoCountryCode),
		PayerBankName: getStringValue(transaction.PayerBankName),
		PayerBankBIC:  getStringValue(transaction.PayerBankBic),

		// Данные получателя
		BeneficiaryName:     paymentConfigs.BeneficiaryName,
		BeneficiaryBINIIN:   paymentConfigs.BeneficiaryBINIIN,
		BeneficiaryAccount:  paymentConfigs.BeneficiaryAccount,
		BeneficiaryKbe:      paymentConfigs.BeneficiaryKbe,
		BeneficiaryCountry:  paymentConfigs.BeneficiaryCountry,
		BeneficiaryBankName: paymentConfigs.BeneficiaryBankName,
		BeneficiaryBankBIC:  paymentConfigs.BeneficiaryBankBIC,

		// Данные фактического получателя (сотрудника)
		RealBeneficiaryName:    employeeName,
		RealBeneficiaryBINIIN:  employee.IIN,
		RealBeneficiaryCountry: employee.Country,

		// Назначение платежа для сотрудника
		PurposeDetails: purposeDetails,
		PurposeCode:    getStringValue(transaction.PurposeCode),

		// Сумма для конкретного сотрудника
		WrittenAmount: employeeWrittenAmount,
		Amount:        employee.Amount,
		ValueDate:     valueDateDotFormatted,

		// Дата платежа
		ReferenceDate: referenceDateDotFormatted,

		// КБК
		Kbk: payment.KBK,

		// Период платежа
		PaymentPeriod: paymentPeriod,

		// Подписи
		SignatoryA: getStringValueOrNotRequired(signatories.SignatoryA, loc),
		SignatoryB: getStringValueOrNotRequired(signatories.SignatoryB, loc),

		// Порядковый номер платежного поручения для сотрудника
		EmployeeOrderNumber: employeeOrderNumber,
	}, nil
}

func getStringValue(value *string) string {
	if value == nil {
		return ""
	}

	return *value
}

func getStringPointer(value string) *string {
	if value == "" {
		return nil
	}
	return &value
}

// getStringValueOrNotRequired преобразует указатель в строку, если строка не пустая, иначе возвращает "не предусмотрено"
func getStringValueOrNotRequired(value *string, loc locale.Locale) string {
	if value == nil {
		if loc == locale.Ru {
			return consts.DocumentPDFNotSpecifiedRu.String()
		}
		return consts.DocumentPDFNotSpecifiedKZ.String()
	}
	return *value
}

// transactionStatusToDocumentStatus преобразует статус транзакции в статус платежного поручения
func (u *useCasesImpl) transactionStatusToDocumentStatus(ctx context.Context, status consts.TransactionStatus, loc locale.Locale) (consts.DocumentPDFStatus, error) {
	logger := logs.FromContext(ctx)

	if loc == locale.Ru {
		switch status {
		case consts.TransactionStatusInitialized, consts.TransactionStatusInProgress:
			return consts.DocumentPDFStatusInProgress, nil
		case consts.TransactionStatusCompleted:
			return consts.DocumentPDFStatusCompleted, nil
		case consts.TransactionStatusRejected:
			return consts.DocumentPDFStatusRejected, nil
		default:
			logger.Error().Msgf("unknown transaction status: %s", status)
			return "", fmt.Errorf("unknown transaction status: %s", status)
		}
	}

	switch status {
	case consts.TransactionStatusInitialized, consts.TransactionStatusInProgress:
		return consts.DocumentPDFStatusInProgressKZ, nil
	case consts.TransactionStatusCompleted:
		return consts.DocumentPDFStatusCompletedKZ, nil
	case consts.TransactionStatusRejected:
		return consts.DocumentPDFStatusRejectedKZ, nil
	default:
		logger.Error().Msgf("unknown transaction status: %s", status)
		return "", fmt.Errorf("unknown transaction status: %s", status)
	}
}

// paymentPeriodToDotFormat преобразует период платежа в формат MM.YYYY
func (u *useCasesImpl) paymentPeriodToDotFormat(paymentPeriod string) (string, error) {
	if paymentPeriod == "" {
		return "", nil
	}

	if len(paymentPeriod) != 6 {
		return "", fmt.Errorf("payment period must be in format MMYYYY, got: %s", paymentPeriod)
	}

	monthStr := paymentPeriod[:2]
	yearStr := paymentPeriod[2:]

	// Проверяем, что месяц и год - числа
	month, err1 := strconv.Atoi(monthStr)
	year, err2 := strconv.Atoi(yearStr)

	if err1 != nil || err2 != nil || month < 1 || month > 12 || year < 1000 || year > 9999 {
		return "", fmt.Errorf("invalid payment period: %s", paymentPeriod)
	}

	return fmt.Sprintf("%02d.%04d", month, year), nil
}

// getPurposeDetails форматирует назначение платежа в формат "Назначение платежа за Август 2025"
func (u *useCasesImpl) getPurposeDetails(ctx context.Context, transaction *entity.Transaction, payment *entity.Payment) (string, error) {
	logger := logs.FromContext(ctx)

	userLocale, err := utils.GetLocaleFromContext(ctx, true, true)
	if err != nil {
		logger.Error().Err(err).Msg("failed to get locale from context")
		return "", fmt.Errorf("failed to get locale from context")
	}

	paymentPeriodTime, ok := utils.ParseDate(payment.PaymentPeriod)
	if !ok {
		logger.Error().Msgf("failed to parse payment period for payment id: %s", payment.ID)
		return "", fmt.Errorf("failed to parse payment period for payment id: %s", payment.ID)
	}

	purposeDetails := fmt.Sprintf("%s за %s", getStringValue(transaction.PurposeDetails), fdate.FormatDateToMonthYear(paymentPeriodTime, userLocale.String()))

	return purposeDetails, nil
}

// getWrittenAmount форматирует численную сумму платежа (string) в формат прописью
func (u *useCasesImpl) getWrittenAmount(ctx context.Context, amountString string) (string, error) {
	logger := logs.FromContext(ctx)

	var writtenAmount string

	// Преобразуем сумму платежа в десятичный формат
	amountDecimal, err := decimal.NewFromString(amountString)
	if err != nil {
		logger.Error().Err(err).Msgf("failed to parse into decimal transaction amount (%s)", amountString)
		return "", fmt.Errorf("failed to parse into decimal transaction amount (%s). Error: %w", amountString, err)
	}
	logger.Debug().Interface("amountDecimal", amountDecimal).Msgf("amountDecimal from transaction.TransactionTotalAmount (%s)", amountString)

	userLocale, err := utils.GetLocaleFromContext(ctx, true, true)
	if err != nil {
		logger.Warn().Err(err).Msg("failed to get locale from context, using default: kk")
		userLocale = locale.Kk
	}

	if userLocale == locale.Kk {
		writtenAmount = moneywords.ToKazakh(amountDecimal)
	} else {
		writtenAmount = moneywords.ToRussian(amountDecimal)
	}
	logger.Debug().Interface("writtenAmount", writtenAmount).Msgf("writtenAmount from transaction.TransactionTotalAmount (%s)", amountString)

	return writtenAmount, nil
}
