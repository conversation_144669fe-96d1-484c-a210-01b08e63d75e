package usecase

import (
	"context"
	"fmt"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/conversion"
	"github.com/google/uuid"

	errs "git.redmadrobot.com/zaman/backend/zaman/errs/paymentsSme"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/entity"
	"git.redmadrobot.com/zaman/backend/zaman/specs/proto/otp"
)

func (u *useCasesImpl) SmePaymentsOtpValidate(ctx context.Context, req *entity.SmePaymentsOtpValidateReq) (*entity.SmePaymentsOtpValidateResult, error) {
	logger := logs.FromContext(ctx)

	// Проверка происхождения запроса
	origin, err := utils.GetOrigin(ctx)
	if err != nil {
		logger.Error().Msgf("GetOrigin failed: %v", err)

		return nil, err
	}

	if origin != utils.UserOriginSme {
		errOriginMismatch := errs.PaymentsSmeErrs().OriginNotSMEError()
		logger.Error().Err(errOriginMismatch)

		return nil, errOriginMismatch
	}

	transactionUUID, err := uuid.Parse(req.TransactionID)
	if err != nil {
		logger.Error().Msgf("failed to parse transactionID: %+v", err)
		return &entity.SmePaymentsOtpValidateResult{
			Error: conversion.Ptr(err.Error()),
		}, nil
	}

	// 1. Проверяем в БД наличие транзакции с transactionId
	transaction, err := u.Providers.Storage.GetTransactionByID(ctx, transactionUUID)
	if err != nil {
		if err.Error() == "transaction not found" {
			logger.Error().Msgf("transaction not found: %s", transactionUUID)
			return nil, errs.PaymentsSmeErrs().TransactionNotFoundError()
		}
		logger.Error().Msgf("get transaction by id: %+v", err)
		return nil, errs.PaymentsSmeErrs().InternalErrorError()
	}
	logger.Debug().Interface("transaction", transaction).Msg("transaction")

	// 1.1 Достаем ИИН текущего пользователя из контекста и сервиса Users
	smeIPInfo, err := u.getUserSmeIPInfo(ctx)
	if err != nil {
		logger.Error().Msgf("failed to get user sme IP info: %v", err)

		return nil, err
	}
	// 1.2 Проверяем транзакцию и ИИН из нее на nil
	if transaction == nil || transaction.PayerBinIin == nil {
		logger.Error().Msgf("transaction or PayerBinIin is nil for transactionID: %s", transactionUUID)

		return nil, errs.PaymentsSmeErrs().TransactionNotFoundError()
	}
	// 1.3 Проверяем, что ИИН текущего пользователя с ИИН из транзакции
	if *transaction.PayerBinIin != smeIPInfo.GetIin() {
		iinMismatchError := fmt.Errorf("payer bin/iin %s does not match sme IP info iin %s", *transaction.PayerBinIin, smeIPInfo.GetIin())
		logger.Error().Msgf("%s", iinMismatchError.Error())

		return nil, iinMismatchError
	}

	// 2. Проверяем под платежом наличие авторизаций в confirmations. Если есть, то возвращаем успех
	confirmations, err := u.Providers.Storage.GetConfirmationsByTransactionID(ctx, transactionUUID)
	if err != nil {
		logger.Error().Msgf("cannot get confirmations by transaction id: %s", transactionUUID)
		return nil, errs.PaymentsSmeErrs().InternalErrorError()
	}

	if len(confirmations) > 0 {
		logger.Info().Interface("confirmations", confirmations).Msg("confirmations")
		return &entity.SmePaymentsOtpValidateResult{
			Success: conversion.Ptr(true),
		}, nil
	}

	// 3. Вызываем сервис OTP для проверки кода
	otpResponse, err := u.Providers.Otp.ValidateCode(ctx, &otp.ValidateCodeReq{
		AttemptId: req.AttemptID,
		Code:      req.Code,
	})
	if err != nil {
		logger.Error().Msgf("failed to validate code: %+v", err)
		return &entity.SmePaymentsOtpValidateResult{
			Error: conversion.Ptr(err.Error()),
		}, nil
	}
	logger.Debug().Interface("otpResponse", otpResponse).Msg("otpResponse")

	if otpResponse.Success {
		logger.Info().Msgf("code validated successfully")
	} else {
		logger.Error().Msgf("code validation failed")
		return &entity.SmePaymentsOtpValidateResult{
			Error: conversion.Ptr("code validation failed"),
		}, nil
	}

	// 4. Записываем в БД confirmations данные о подписи
	conf := &entity.Confirmation{
		ID:               transactionUUID,
		ConfirmationType: consts.ConfirmationTypeOtp,
		ConfirmationDate: time.Now(),
	}

	err = u.Providers.Storage.CreateConfirmation(ctx, conf)
	if err != nil {
		logger.Error().Msgf("failed to create confirmation: %+v", err)
		return nil, errs.PaymentsSmeErrs().InternalErrorError()
	}

	return &entity.SmePaymentsOtpValidateResult{
		Success: conversion.Ptr(true),
	}, nil
}
