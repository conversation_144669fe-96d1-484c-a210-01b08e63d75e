// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package usecase

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/usecase -i PaymentsSme -t ../../../etc/templates/hook.tmpl -o usecase_hook.gen.go -l ""

import (
	"context"

	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"

	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/entity"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"
)

var _ PaymentsSme = (*PaymentsSmeHook)(nil)

// PaymentsSmeHook implements PaymentsSme interface wrapper
type PaymentsSmeHook struct {
	PaymentsSme
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// ConfirmPaymentSme implements PaymentsSme
func (_w *PaymentsSmeHook) ConfirmPaymentSme(ctx context.Context, req *entity.ConfirmPaymentSmeReq) (cp1 *entity.ConfirmPaymentSmeResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.PaymentsSme, "ConfirmPaymentSme", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.PaymentsSme, "ConfirmPaymentSme", _params)

	cp1, err = _w.PaymentsSme.ConfirmPaymentSme(_ctx, req)
	_w._postCall.Hook(_ctx, _w.PaymentsSme, "ConfirmPaymentSme", []any{cp1, err})
	return cp1, err
}

// CreateEmployee implements PaymentsSme
func (_w *PaymentsSmeHook) CreateEmployee(ctx context.Context, req *entity.CreateEmployeeReq) (cp1 *entity.CreateEmployeeResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.PaymentsSme, "CreateEmployee", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.PaymentsSme, "CreateEmployee", _params)

	cp1, err = _w.PaymentsSme.CreateEmployee(_ctx, req)
	_w._postCall.Hook(_ctx, _w.PaymentsSme, "CreateEmployee", []any{cp1, err})
	return cp1, err
}

// CreatePayment implements PaymentsSme
func (_w *PaymentsSmeHook) CreatePayment(ctx context.Context, req *entity.CreatePaymentReq) (cp1 *entity.CreatePaymentResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.PaymentsSme, "CreatePayment", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.PaymentsSme, "CreatePayment", _params)

	cp1, err = _w.PaymentsSme.CreatePayment(_ctx, req)
	_w._postCall.Hook(_ctx, _w.PaymentsSme, "CreatePayment", []any{cp1, err})
	return cp1, err
}

// DeleteEmployee implements PaymentsSme
func (_w *PaymentsSmeHook) DeleteEmployee(ctx context.Context, req *entity.DeleteEmployeeReq) (dp1 *entity.DeleteEmployeeResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.PaymentsSme, "DeleteEmployee", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.PaymentsSme, "DeleteEmployee", _params)

	dp1, err = _w.PaymentsSme.DeleteEmployee(_ctx, req)
	_w._postCall.Hook(_ctx, _w.PaymentsSme, "DeleteEmployee", []any{dp1, err})
	return dp1, err
}

// GetBankList implements PaymentsSme
func (_w *PaymentsSmeHook) GetBankList(ctx context.Context, req *entity.GetBankListReq) (gp1 *entity.GetBankListResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.PaymentsSme, "GetBankList", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.PaymentsSme, "GetBankList", _params)

	gp1, err = _w.PaymentsSme.GetBankList(_ctx, req)
	_w._postCall.Hook(_ctx, _w.PaymentsSme, "GetBankList", []any{gp1, err})
	return gp1, err
}

// GetCountryList implements PaymentsSme
func (_w *PaymentsSmeHook) GetCountryList(ctx context.Context, req *entity.GetCountryListReq) (gp1 *entity.GetCountryListResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.PaymentsSme, "GetCountryList", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.PaymentsSme, "GetCountryList", _params)

	gp1, err = _w.PaymentsSme.GetCountryList(_ctx, req)
	_w._postCall.Hook(_ctx, _w.PaymentsSme, "GetCountryList", []any{gp1, err})
	return gp1, err
}

// GetEmployeeList implements PaymentsSme
func (_w *PaymentsSmeHook) GetEmployeeList(ctx context.Context, req *entity.GetEmployeeListReq) (gp1 *entity.GetEmployeeListResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.PaymentsSme, "GetEmployeeList", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.PaymentsSme, "GetEmployeeList", _params)

	gp1, err = _w.PaymentsSme.GetEmployeeList(_ctx, req)
	_w._postCall.Hook(_ctx, _w.PaymentsSme, "GetEmployeeList", []any{gp1, err})
	return gp1, err
}

// GetKbeKodList implements PaymentsSme
func (_w *PaymentsSmeHook) GetKbeKodList(ctx context.Context, req *entity.GetKbeKodListReq) (gp1 *entity.GetKbeKodListResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.PaymentsSme, "GetKbeKodList", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.PaymentsSme, "GetKbeKodList", _params)

	gp1, err = _w.PaymentsSme.GetKbeKodList(_ctx, req)
	_w._postCall.Hook(_ctx, _w.PaymentsSme, "GetKbeKodList", []any{gp1, err})
	return gp1, err
}

// GetKbkList implements PaymentsSme
func (_w *PaymentsSmeHook) GetKbkList(ctx context.Context, req *entity.GetKbkListReq) (gp1 *entity.GetKbkListResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.PaymentsSme, "GetKbkList", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.PaymentsSme, "GetKbkList", _params)

	gp1, err = _w.PaymentsSme.GetKbkList(_ctx, req)
	_w._postCall.Hook(_ctx, _w.PaymentsSme, "GetKbkList", []any{gp1, err})
	return gp1, err
}

// GetKnpList implements PaymentsSme
func (_w *PaymentsSmeHook) GetKnpList(ctx context.Context, req *entity.GetKnpListReq) (gp1 *entity.GetKnpListResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.PaymentsSme, "GetKnpList", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.PaymentsSme, "GetKnpList", _params)

	gp1, err = _w.PaymentsSme.GetKnpList(_ctx, req)
	_w._postCall.Hook(_ctx, _w.PaymentsSme, "GetKnpList", []any{gp1, err})
	return gp1, err
}

// GetTaxAuthorityList implements PaymentsSme
func (_w *PaymentsSmeHook) GetTaxAuthorityList(ctx context.Context, req *entity.GetTaxAuthorityListReq) (gp1 *entity.GetTaxAuthorityListResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.PaymentsSme, "GetTaxAuthorityList", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.PaymentsSme, "GetTaxAuthorityList", _params)

	gp1, err = _w.PaymentsSme.GetTaxAuthorityList(_ctx, req)
	_w._postCall.Hook(_ctx, _w.PaymentsSme, "GetTaxAuthorityList", []any{gp1, err})
	return gp1, err
}

// HealthCheck implements PaymentsSme
func (_w *PaymentsSmeHook) HealthCheck(ctx context.Context) (hp1 *entity.Health, err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.PaymentsSme, "HealthCheck", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.PaymentsSme, "HealthCheck", _params)

	hp1, err = _w.PaymentsSme.HealthCheck(_ctx)
	_w._postCall.Hook(_ctx, _w.PaymentsSme, "HealthCheck", []any{hp1, err})
	return hp1, err
}

// HealthEvent implements PaymentsSme
func (_w *PaymentsSmeHook) HealthEvent(ctx context.Context, message *kafka.Message) {
	_params := []any{ctx, message}
	defer _w._onPanic.Hook(_w.PaymentsSme, "HealthEvent", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.PaymentsSme, "HealthEvent", _params)

	_w.PaymentsSme.HealthEvent(_ctx, message)
	_w._postCall.Hook(_ctx, _w.PaymentsSme, "HealthEvent", []any{})
	return
}

// InitConsumer implements PaymentsSme
func (_w *PaymentsSmeHook) InitConsumer(ctx context.Context) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.PaymentsSme, "InitConsumer", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.PaymentsSme, "InitConsumer", _params)

	_w.PaymentsSme.InitConsumer(_ctx)
	_w._postCall.Hook(_ctx, _w.PaymentsSme, "InitConsumer", []any{})
	return
}

// SmePaymentsClient implements PaymentsSme
func (_w *PaymentsSmeHook) SmePaymentsClient(ctx context.Context, req *entity.SmePaymentsClientReq) (sp1 *entity.SmePaymentsClientResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.PaymentsSme, "SmePaymentsClient", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.PaymentsSme, "SmePaymentsClient", _params)

	sp1, err = _w.PaymentsSme.SmePaymentsClient(_ctx, req)
	_w._postCall.Hook(_ctx, _w.PaymentsSme, "SmePaymentsClient", []any{sp1, err})
	return sp1, err
}

// SmePaymentsCreateOtp implements PaymentsSme
func (_w *PaymentsSmeHook) SmePaymentsCreateOtp(ctx context.Context, req *entity.SmePaymentsCreateOtpReq) (sp1 *entity.SmePaymentsCreateOtpResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.PaymentsSme, "SmePaymentsCreateOtp", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.PaymentsSme, "SmePaymentsCreateOtp", _params)

	sp1, err = _w.PaymentsSme.SmePaymentsCreateOtp(_ctx, req)
	_w._postCall.Hook(_ctx, _w.PaymentsSme, "SmePaymentsCreateOtp", []any{sp1, err})
	return sp1, err
}

// SmePaymentsGetPaymentOrder implements PaymentsSme
func (_w *PaymentsSmeHook) SmePaymentsGetPaymentOrder(ctx context.Context, req *entity.SmePaymentsGetPaymentOrderReq) (sp1 *entity.SmePaymentsGetPaymentOrderResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.PaymentsSme, "SmePaymentsGetPaymentOrder", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.PaymentsSme, "SmePaymentsGetPaymentOrder", _params)

	sp1, err = _w.PaymentsSme.SmePaymentsGetPaymentOrder(_ctx, req)
	_w._postCall.Hook(_ctx, _w.PaymentsSme, "SmePaymentsGetPaymentOrder", []any{sp1, err})
	return sp1, err
}

// SmePaymentsGetPaymentOrderByTrNumber implements PaymentsSme
func (_w *PaymentsSmeHook) SmePaymentsGetPaymentOrderByTrNumber(ctx context.Context, req *entity.SmePaymentsGetPaymentOrderByTrNumberReq) (sp1 *entity.SmePaymentsGetPaymentOrderByTrNumberResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.PaymentsSme, "SmePaymentsGetPaymentOrderByTrNumber", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.PaymentsSme, "SmePaymentsGetPaymentOrderByTrNumber", _params)

	sp1, err = _w.PaymentsSme.SmePaymentsGetPaymentOrderByTrNumber(_ctx, req)
	_w._postCall.Hook(_ctx, _w.PaymentsSme, "SmePaymentsGetPaymentOrderByTrNumber", []any{sp1, err})
	return sp1, err
}

// SmePaymentsOtpResend implements PaymentsSme
func (_w *PaymentsSmeHook) SmePaymentsOtpResend(ctx context.Context, req *entity.SmePaymentsOtpResendReq) (sp1 *entity.SmePaymentsOtpResendResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.PaymentsSme, "SmePaymentsOtpResend", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.PaymentsSme, "SmePaymentsOtpResend", _params)

	sp1, err = _w.PaymentsSme.SmePaymentsOtpResend(_ctx, req)
	_w._postCall.Hook(_ctx, _w.PaymentsSme, "SmePaymentsOtpResend", []any{sp1, err})
	return sp1, err
}

// SmePaymentsOtpValidate implements PaymentsSme
func (_w *PaymentsSmeHook) SmePaymentsOtpValidate(ctx context.Context, req *entity.SmePaymentsOtpValidateReq) (sp1 *entity.SmePaymentsOtpValidateResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.PaymentsSme, "SmePaymentsOtpValidate", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.PaymentsSme, "SmePaymentsOtpValidate", _params)

	sp1, err = _w.PaymentsSme.SmePaymentsOtpValidate(_ctx, req)
	_w._postCall.Hook(_ctx, _w.PaymentsSme, "SmePaymentsOtpValidate", []any{sp1, err})
	return sp1, err
}

// SmePaymentsWorktime implements PaymentsSme
func (_w *PaymentsSmeHook) SmePaymentsWorktime(ctx context.Context, req *entity.SmePaymentsWorktimeReq) (sp1 *entity.SmePaymentsWorktimeResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.PaymentsSme, "SmePaymentsWorktime", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.PaymentsSme, "SmePaymentsWorktime", _params)

	sp1, err = _w.PaymentsSme.SmePaymentsWorktime(_ctx, req)
	_w._postCall.Hook(_ctx, _w.PaymentsSme, "SmePaymentsWorktime", []any{sp1, err})
	return sp1, err
}

// UpdateEmployee implements PaymentsSme
func (_w *PaymentsSmeHook) UpdateEmployee(ctx context.Context, req *entity.UpdateEmployeeReq) (up1 *entity.UpdateEmployeeResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.PaymentsSme, "UpdateEmployee", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.PaymentsSme, "UpdateEmployee", _params)

	up1, err = _w.PaymentsSme.UpdateEmployee(_ctx, req)
	_w._postCall.Hook(_ctx, _w.PaymentsSme, "UpdateEmployee", []any{up1, err})
	return up1, err
}

// NewPaymentsSmeHook returns PaymentsSmeHook
func NewPaymentsSmeHook(object PaymentsSme, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *PaymentsSmeHook {
	return &PaymentsSmeHook{
		PaymentsSme: object,
		_beforeCall: beforeCall,
		_postCall:   postCall,
		_onPanic:    onPanic,
	}
}
