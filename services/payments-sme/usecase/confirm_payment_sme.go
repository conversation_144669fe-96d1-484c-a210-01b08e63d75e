package usecase

import (
	"context"
	"errors"
	"fmt"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/generic"
	"github.com/google/uuid"

	errs "git.redmadrobot.com/zaman/backend/zaman/errs/paymentsSme"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/storage/postgres/ent"
	colvir_bridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
)

const (
	maxRetries = 3
)

// ConfirmPaymentSme подтверждает платеж в колвир и возвращает результат.
// Подразумевается, что платеж уже прошел все проверки и находится в статусе IN_PROGRESS.
//
// Ссылка на документацию: https://rmrkz.atlassian.net/wiki/spaces/ZS/pages/109741472/PAYM.INIT+-
func (u *useCasesImpl) ConfirmPaymentSme(ctx context.Context, req *entity.ConfirmPaymentSmeReq) (*entity.ConfirmPaymentSmeResult, error) {
	logger := logs.FromContext(ctx)

	// 1. Проверка, что transactionID не пустой
	if err := req.Validate(); err != nil {
		logger.Error().Msgf("validation error: %s", err)
		return nil, errs.PaymentsSmeErrs().ValidationErrorError()
	}

	transactionUUID, err := uuid.Parse(req.TransactionID)
	if err != nil {
		logger.Error().Msgf("invalid transactionID: %s", req.TransactionID)
		return nil, errs.PaymentsSmeErrs().ValidationErrorError()
	}

	// 2. Получаем платёж из БД по transactionId
	transaction, err := u.Providers.Storage.GetTransactionByID(ctx, transactionUUID)
	if err != nil {
		if errors.Is(err, consts.ErrTransactionNotFound) {
			logger.Error().Msgf("transaction not found: %s", transactionUUID)
			return nil, errs.PaymentsSmeErrs().TransactionNotFoundError()
		}
		logger.Error().Msgf("error getting transaction by id: %+v", err)
		return nil, errs.PaymentsSmeErrs().InternalErrorError()
	}

	// 2.1 Проверяем, есть ли в таблице abs_transaction_documents запись с ID = req.transactionID
	// Если запись есть - значит, данная транзакция уже была обработана в колвир: вернуть ответ фронту
	// Если записи нет - значит, данная транзакция еще не была обработана в колвир: продолжить обработку
	absTrxDoc, err := u.Providers.Storage.GetAbsTransactionDocumentByID(ctx, transactionUUID)
	if err != nil {
		if ent.IsNotFound(err) {
			logger.Debug().Msgf("abs transaction document not found: %s. Continue processing.", transactionUUID)
		} else {
			logger.Error().Msgf("error getting abs transaction document by id: %+v", err)
			return nil, fmt.Errorf("error getting abs transaction document by id: %w", err)
		}
	}

	if absTrxDoc != nil {
		logger.Warn().Msgf("abs transaction document found: %s. Return response to frontend.", transactionUUID)

		if absTrxDoc.RejectionReason != nil {
			return &entity.ConfirmPaymentSmeResult{
				Status:     string(transaction.TransactionStatus),
				ReasonCode: string(consts.GetReasonCodeByReason(consts.PaymentReason(*absTrxDoc.RejectionReason))),
				Reason:     *absTrxDoc.RejectionReason,
				OtpNeeded:  false,
			}, nil
		}

		return &entity.ConfirmPaymentSmeResult{
			Status: string(transaction.TransactionStatus),
		}, nil
	}

	// 3. Проверяем transactions.transaction_status
	transactionStatusResult, err := u.checkTransactionStatus(ctx, transaction)
	if err != nil {
		return nil, err
	}

	if transactionStatusResult != nil {
		return transactionStatusResult, nil
	}

	// 4. Проверяем под платежом наличие отказов в rejections
	rejectionsResult, err := u.checkRejections(ctx, transactionUUID)
	if err != nil {
		return nil, err
	}

	if rejectionsResult != nil {
		return rejectionsResult, nil
	}

	// 5. Проверяем под платежом наличие подтверждений в таблице confirmations.
	confirmationsResult, err := u.checkConfirmations(ctx, transactionUUID)
	if err != nil {
		return nil, err
	}

	if confirmationsResult != nil {
		return confirmationsResult, nil
	}

	// 6. Дебетование посредством списания со счёта клиента (интеграция с колвир)
	return u.processPayment(ctx, transaction)
}

// checkTransactionStatus проверяет статус транзакции и возвращает результат в зависимости от статуса.
func (u *useCasesImpl) checkTransactionStatus(ctx context.Context, transaction *entity.Transaction) (*entity.ConfirmPaymentSmeResult, error) {
	logger := logs.FromContext(ctx)

	switch transaction.TransactionStatus {
	case consts.TransactionStatusRejected:
		return &entity.ConfirmPaymentSmeResult{
			Status:     string(consts.TransactionStatusRejected),
			ReasonCode: string(consts.PaymentReasonCodeUnableToProcess),
			Reason:     string(consts.PaymentReasonUnableToProcess),
		}, nil
	case consts.TransactionStatusCompleted:
		return &entity.ConfirmPaymentSmeResult{
			Status: string(consts.TransactionStatusCompleted),
		}, nil
	case consts.TransactionStatusInProgress:
		return &entity.ConfirmPaymentSmeResult{
			Status: string(consts.TransactionStatusInProgress),
		}, nil
	case consts.TransactionStatusInitialized:
		return nil, nil
	default:
		logger.Error().Msgf("invalid transaction status: %s", transaction.TransactionStatus)
		return nil, errs.PaymentsSmeErrs().ValidationErrorError()
	}
}

// checkRejections проверяет наличие отказов в таблице rejections. Например, отказ от антифрод.
func (u *useCasesImpl) checkRejections(ctx context.Context, transactionUUID uuid.UUID) (*entity.ConfirmPaymentSmeResult, error) {
	logger := logs.FromContext(ctx)

	rejections, err := u.Providers.Storage.GetRejectionsByTransactionID(ctx, transactionUUID)
	if err != nil {
		logger.Error().Msgf("cannot get rejections by transaction id: %s", transactionUUID)
		return nil, errs.PaymentsSmeErrs().InternalErrorError()
	}

	if len(rejections) > 0 {
		logger.Debug().Msgf("rejections found: %d", len(rejections))

		// Выставляем статус REJECTED в БД
		if err := u.Providers.Storage.UpdateTransactionStatus(ctx, transactionUUID, consts.TransactionStatusRejected); err != nil {
			logger.Error().Msgf("error update transaction status: %+v", err)
			return nil, errs.PaymentsSmeErrs().InternalErrorError()
		}

		return &entity.ConfirmPaymentSmeResult{
			Status:     string(consts.TransactionStatusRejected),
			ReasonCode: string(consts.PaymentReasonCodeUnableToProcess),
			Reason:     string(consts.PaymentReasonUnableToProcess),
		}, nil
	}

	return nil, nil
}

// checkConfirmations проверяет наличие подтверждений в таблице confirmations. Например, подтверждение по ОТП или SMS.
func (u *useCasesImpl) checkConfirmations(ctx context.Context, transactionUUID uuid.UUID) (*entity.ConfirmPaymentSmeResult, error) {
	logger := logs.FromContext(ctx)

	confirmations, err := u.Providers.Storage.GetConfirmationsByTransactionID(ctx, transactionUUID)
	if err != nil {
		logger.Error().Msgf("cannot get confirmations by transaction id: %s", transactionUUID)
		return nil, errs.PaymentsSmeErrs().InternalErrorError()
	}

	// если нет авторизаций, то возвращаем ответ со статусом status = INITIALIZED и otpNeeded = true.
	if len(confirmations) == 0 {
		logger.Debug().Msgf("confirmations not found")
		return &entity.ConfirmPaymentSmeResult{
			Status:    string(consts.TransactionStatusInitialized),
			OtpNeeded: true,
		}, nil
	}

	logger.Debug().Msgf("%d confirmations found: %+v", len(confirmations), confirmations)
	return nil, nil
}

func (u *useCasesImpl) processPayment(ctx context.Context, transaction *entity.Transaction) (*entity.ConfirmPaymentSmeResult, error) {
	logger := logs.FromContext(ctx)

	// 6.1 Определение типа платежа (с реестром или без)
	payment, err := u.Providers.Storage.GetPaymentByPaymentID(ctx, transaction.ID) // paymentID = transactionID
	if err != nil {
		logger.Debug().Msgf("cannot get payment by payment id: %s", transaction.ID)
		return nil, errs.PaymentsSmeErrs().InternalErrorError()
	}

	if payment == nil {
		logger.Error().Msgf("payment with id %s not found", transaction.ID)
		return nil, errs.PaymentsSmeErrs().PaymentNotFoundError()
	}

	// если в payment.employeeList нет элементов, то это внутренний платёж без реестра
	innerPaymentType := consts.PaymentTypeColvirInternalPayment
	if len(payment.EmployeeList) > 0 {
		// если в payment.employeeList есть элементы, то это внутренний платёж с реестром
		innerPaymentType = consts.PaymentTypeColvirInternalPaymentWithRegistry
	}

	if innerPaymentType == consts.PaymentTypeColvirInternalPayment {
		return u.processInternalPayment(ctx, transaction, payment)
	}

	return u.processInternalPaymentWithRegistry(ctx, transaction, payment)
}

// processInternalPayment проводит дебетование для внутреннего платежа без реестра
func (u *useCasesImpl) processInternalPayment(ctx context.Context, transaction *entity.Transaction, payment *entity.Payment) (*entity.ConfirmPaymentSmeResult, error) {
	logger := logs.FromContext(ctx)

	// 6.2 Проверка возможности платежа для внутреннего платёжа (с ретраями)
	checkDomesticPaymentRes, err := u.checkDomesticPayment(ctx, transaction, payment)
	if err != nil {
		return nil, err
	}

	// если код ошибки не 0, то возвращаем ошибку
	if checkDomesticPaymentRes.ErrorStatus != colvir_bridge.ErrorStatus_NoError {
		logger.Error().Interface("checkDomesticPaymentRes", checkDomesticPaymentRes).Msg("check domestic payment failed")

		// Меняем статус на REJECTED в БД
		if err := u.Providers.Storage.UpdateTransactionStatus(ctx, transaction.ID, consts.TransactionStatusRejected); err != nil {
			logger.Error().Msgf("error update transaction status: %+v", err)
			return nil, errs.PaymentsSmeErrs().InternalErrorError()
		}

		return &entity.ConfirmPaymentSmeResult{
			Status:     string(consts.TransactionStatusRejected),
			ReasonCode: string(consts.PaymentReasonCodeUnableToProcess),
			Reason:     string(consts.PaymentReasonUnableToProcess),
		}, nil
	}

	// 6.3 Проведение платежа в колвир
	execDomesticPaymentRes, err := u.executeDomesticPayment(ctx, transaction, payment)
	if err != nil {
		return nil, err
	}

	// Если код ошибки не 0, то возвращаем ошибку
	if execDomesticPaymentRes.ErrorStatus != colvir_bridge.ErrorStatus_NoError {
		logger.Error().Interface("execDomesticPaymentRes", execDomesticPaymentRes).Msg("execute domestic payment failed")

		// Меняем статус на REJECTED в БД
		if err := u.Providers.Storage.UpdateTransactionStatus(ctx, transaction.ID, consts.TransactionStatusRejected); err != nil {
			logger.Error().Msgf("error update transaction status: %+v", err)
			return nil, errs.PaymentsSmeErrs().InternalErrorError()
		}

		// Просаживаем данные документа колвир в БД (abs_transaction_documents) - со статусом REJECTED
		colvirDocType, err := payment.GetColvirDocumentType()
		if err != nil {
			logger.Error().Msgf("error get colvir document type: %s", err)
			return nil, errs.PaymentsSmeErrs().InternalErrorError()
		}

		absTrxDoc := &entity.AbsTransactionDocument{
			ID:             transaction.ID,
			DocumentType:   &colvirDocType,
			DocumentStatus: getStringPointer(consts.ColvirDocumentStatusRejected.String()),
		}

		// Если ошибка DP-00811, то возвращаем ошибку InsufficientFunds
		if execDomesticPaymentRes.Error != nil && execDomesticPaymentRes.Error.Code == consts.ColvirErrorMessageCodeInsufficientFunds.String() {
			absTrxDoc.RejectionReason = getStringPointer(consts.PaymentReasonInsufficientFunds.String())
			if err := u.Providers.Storage.CreateAbsTransactionDocuments(ctx, absTrxDoc); err != nil {
				logger.Error().Msgf("error create abs transaction documents: %+v", err)
				return nil, errs.PaymentsSmeErrs().InternalErrorError()
			}

			return &entity.ConfirmPaymentSmeResult{
				Status:     string(consts.TransactionStatusRejected),
				ReasonCode: string(consts.PaymentReasonCodeInsufficientFunds),
				Reason:     string(consts.PaymentReasonInsufficientFunds),
			}, nil
		}

		absTrxDoc.RejectionReason = getStringPointer(consts.PaymentReasonUnableToProcess.String())
		if err := u.Providers.Storage.CreateAbsTransactionDocuments(ctx, absTrxDoc); err != nil {
			logger.Error().Msgf("error create abs transaction documents: %+v", err)
			return nil, errs.PaymentsSmeErrs().InternalErrorError()
		}

		return &entity.ConfirmPaymentSmeResult{
			Status:     string(consts.TransactionStatusRejected),
			ReasonCode: string(consts.PaymentReasonCodeUnableToProcess),
			Reason:     string(consts.PaymentReasonUnableToProcess),
		}, nil
	}

	// Записываем в БД информацию о документе
	if execDomesticPaymentRes.Result == nil {
		logger.Error().Msg("execute domestic payment result is nil")
		return nil, errs.PaymentsSmeErrs().InternalErrorError()
	}

	absTrxDoc := &entity.AbsTransactionDocument{
		ID:             transaction.ID,
		ReferenceID:    execDomesticPaymentRes.Result.ColvirReferenceID,
		ReferenceDate:  execDomesticPaymentRes.Result.ExecDate,
		DocumentStatus: &execDomesticPaymentRes.Result.StatusCode,
	}

	if err := u.Providers.Storage.CreateAbsTransactionDocuments(ctx, absTrxDoc); err != nil {
		logger.Error().Msgf("error create abs transaction documents: %+v", err)
		return nil, errs.PaymentsSmeErrs().InternalErrorError()
	}

	// Поменять статус на IN_PROGRESS в БД
	if err := u.Providers.Storage.UpdateTransactionStatus(ctx, transaction.ID, consts.TransactionStatusInProgress); err != nil {
		logger.Error().Msgf("error update transaction status: %+v", err)
		return nil, errs.PaymentsSmeErrs().InternalErrorError()
	}

	return &entity.ConfirmPaymentSmeResult{
		Status: string(consts.TransactionStatusInProgress),
	}, nil
}

// checkDomesticPayment проверяет возможность проведения платежа для внутреннего платёжа без реестра (валидация с колвир)
func (u *useCasesImpl) checkDomesticPayment(ctx context.Context, transaction *entity.Transaction, payment *entity.Payment) (*colvir_bridge.CheckDomesticPaymentResponse, error) {
	logger := logs.FromContext(ctx)

	checkDomesticPaymentRes, err := generic.RetryWithBackoff(generic.ParamsRetryWithBackoff[colvir_bridge.CheckDomesticPaymentResponse]{
		MaxRetries: maxRetries,
		ErrMapFunc: func(err error) error {
			return err
		},
		CheckRetryNecessity: func(err error) bool {
			// так как метод колвир бриджа возвращает ошибку, только если статус не 200, то всегда нужно ретраить
			return true
		},
		Operation: func() (*colvir_bridge.CheckDomesticPaymentResponse, error) {
			checkReq, err := transaction.ToCheckDomesticPaymentReq(payment)
			if err != nil {
				return nil, fmt.Errorf("error to check domestic payment req: %w", err)
			}

			checkRes, err := u.Providers.Colvirbridge.CheckDomesticPayment(ctx, checkReq)
			if err != nil {
				return nil, fmt.Errorf("error check domestic payment: %w", err)
			}

			return checkRes, nil
		},
	})
	if err != nil {
		logger.Error().Msgf("failed to colvir_bridge.CheckDomesticPayment: %s", err)
		return nil, errs.PaymentsSmeErrs().InternalErrorError()
	}
	logger.Debug().Interface("checkDomesticPaymentRes", checkDomesticPaymentRes).Msg("check domestic payment response")

	if checkDomesticPaymentRes == nil {
		logger.Error().Msg("check domestic payment response is nil")
		return nil, errs.PaymentsSmeErrs().InternalErrorError()
	}

	return checkDomesticPaymentRes, nil
}

func (u *useCasesImpl) executeDomesticPayment(ctx context.Context, transaction *entity.Transaction, payment *entity.Payment) (*colvir_bridge.ExecuteDomesticPaymentResponse, error) {
	logger := logs.FromContext(ctx)

	execDomesticPaymentRes, err := generic.RetryWithBackoff(generic.ParamsRetryWithBackoff[colvir_bridge.ExecuteDomesticPaymentResponse]{
		MaxRetries: maxRetries,
		ErrMapFunc: func(err error) error {
			return err
		},
		CheckRetryNecessity: func(err error) bool {
			// так как метод колвир бриджа возвращает ошибку, только если статус не 200, то всегда нужно ретраить
			return true
		},
		Operation: func() (*colvir_bridge.ExecuteDomesticPaymentResponse, error) {
			execReq, err := transaction.ToCheckDomesticPaymentReq(payment)
			if err != nil {
				return nil, fmt.Errorf("error to execute domestic payment req: %w", err)
			}

			execRes, err := u.Providers.Colvirbridge.ExecuteDomesticPayment(ctx, execReq)
			if err != nil {
				return nil, fmt.Errorf("error execute domestic payment: %w", err)
			}

			return execRes, nil
		},
	})
	if err != nil {
		logger.Error().Msgf("failed to colvir_bridge.ExecuteDomesticPayment: %s", err)
		return nil, errs.PaymentsSmeErrs().InternalErrorError()
	}
	logger.Debug().Interface("execDomesticPaymentRes", execDomesticPaymentRes).Msg("execute domestic payment response")

	if execDomesticPaymentRes == nil {
		logger.Error().Msg("execute domestic payment response is nil")
		return nil, errs.PaymentsSmeErrs().InternalErrorError()
	}

	return execDomesticPaymentRes, nil
}

func (u *useCasesImpl) processInternalPaymentWithRegistry(ctx context.Context, transaction *entity.Transaction, payment *entity.Payment) (*entity.ConfirmPaymentSmeResult, error) {
	logger := logs.FromContext(ctx)

	// получаем подписи из БД (для колвира)
	signatories, err := u.Providers.Storage.GetSignatoriesByTransactionID(ctx, transaction.ID)
	if err != nil {
		if err == consts.ErrSignatoryNotFound {
			logger.Error().Msgf("signatories not found for transaction %s", transaction.ID)
			return nil, errs.PaymentsSmeErrs().InternalErrorError()
		} else {
			logger.Error().Msgf("error get signatories by transaction id: %s", transaction.ID)
			return nil, errs.PaymentsSmeErrs().InternalErrorError()
		}
	}

	// 6.2 Проверка возможности платежа для внутреннего платёжа с реестром (с механизмом ретраев)
	checkDomesticMassPaymentRes, err := u.checkDomesticMassPayment(ctx, transaction, payment, signatories)
	if err != nil {
		return nil, err
	}

	// если код ошибки не 0, то возвращаем ошибку
	if checkDomesticMassPaymentRes.Code != "0" {
		logger.Error().Interface("checkDomesticMassPaymentRes", checkDomesticMassPaymentRes).Msg("check domestic mass payment failed")

		// Меняем статус на REJECTED в БД
		if err := u.Providers.Storage.UpdateTransactionStatus(ctx, transaction.ID, consts.TransactionStatusRejected); err != nil {
			logger.Error().Msgf("error update transaction status: %+v", err)
			return nil, errs.PaymentsSmeErrs().InternalErrorError()
		}

		return &entity.ConfirmPaymentSmeResult{
			Status:     string(consts.TransactionStatusRejected),
			ReasonCode: string(consts.PaymentReasonCodeUnableToProcess),
			Reason:     string(consts.PaymentReasonUnableToProcess),
		}, nil
	}

	// 6.3 Проведение платежа в колвир
	executeDomesticMassPaymentResp, err := u.executeDomesticMassPayment(ctx, transaction, payment, signatories)
	if err != nil {
		return nil, err
	}

	// если код ошибки не 0, то возвращаем ошибку
	if executeDomesticMassPaymentResp.Code != "0" {
		logger.Error().Interface("executeDomesticMassPaymentResp", executeDomesticMassPaymentResp).Msg("execute domestic mass payment failed")

		// Меняем статус на REJECTED в БД
		if err := u.Providers.Storage.UpdateTransactionStatus(ctx, transaction.ID, consts.TransactionStatusRejected); err != nil {
			logger.Error().Msgf("error update transaction status: %+v", err)
			return nil, errs.PaymentsSmeErrs().InternalErrorError()
		}

		// Просаживаем данные документа колвир в БД (abs_transaction_documents) - со статусом REJECTED
		colvirDocType, err := payment.GetColvirDocumentType()
		if err != nil {
			logger.Error().Msgf("error get colvir document type: %s", err)
			return nil, errs.PaymentsSmeErrs().InternalErrorError()
		}

		absTrxDoc := &entity.AbsTransactionDocument{
			ID:             transaction.ID,
			DocumentType:   &colvirDocType,
			DocumentStatus: getStringPointer(consts.ColvirDocumentStatusRejected.String()),
		}

		// Если ошибка DP-00811, то возвращаем ошибку InsufficientFunds
		if executeDomesticMassPaymentResp.Errors != nil && executeDomesticMassPaymentResp.Errors.Message == "DP-00811" {
			absTrxDoc.RejectionReason = getStringPointer(consts.PaymentReasonInsufficientFunds.String())
			if err := u.Providers.Storage.CreateAbsTransactionDocuments(ctx, absTrxDoc); err != nil {
				logger.Error().Msgf("error create abs transaction documents: %+v", err)
				return nil, errs.PaymentsSmeErrs().InternalErrorError()
			}

			return &entity.ConfirmPaymentSmeResult{
				Status:     string(consts.TransactionStatusRejected),
				ReasonCode: string(consts.PaymentReasonCodeInsufficientFunds),
				Reason:     string(consts.PaymentReasonInsufficientFunds),
			}, nil
		}

		absTrxDoc.RejectionReason = getStringPointer(consts.PaymentReasonUnableToProcess.String())
		if err := u.Providers.Storage.CreateAbsTransactionDocuments(ctx, absTrxDoc); err != nil {
			logger.Error().Msgf("error create abs transaction documents: %+v", err)
			return nil, errs.PaymentsSmeErrs().InternalErrorError()
		}

		return &entity.ConfirmPaymentSmeResult{
			Status:     string(consts.TransactionStatusRejected),
			ReasonCode: string(consts.PaymentReasonCodeUnableToProcess),
			Reason:     string(consts.PaymentReasonUnableToProcess),
		}, nil
	}

	// Записываем в БД информацию о документе
	if executeDomesticMassPaymentResp.Result == nil {
		logger.Error().Msg("execute domestic mass payment result is nil")
		return nil, errs.PaymentsSmeErrs().InternalErrorError()
	}

	colvirDocType, err := payment.GetColvirDocumentType()
	if err != nil {
		logger.Error().Msgf("error get colvir document type: %s", err)
		return nil, errs.PaymentsSmeErrs().InternalErrorError()
	}

	absTrxDoc := &entity.AbsTransactionDocument{
		ID:             transaction.ID,
		ReferenceID:    executeDomesticMassPaymentResp.Result.ColvirReferenceID,
		DocumentType:   &colvirDocType,
		ReferenceDate:  &executeDomesticMassPaymentResp.Result.ExecDate,
		DocumentStatus: &executeDomesticMassPaymentResp.Result.StatusCode,
	}

	if err := u.Providers.Storage.CreateAbsTransactionDocuments(ctx, absTrxDoc); err != nil {
		logger.Error().Msgf("error create abs transaction documents: %+v", err)
		return nil, errs.PaymentsSmeErrs().InternalErrorError()
	}

	// Меняем статус на IN_PROGRESS в БД
	if err := u.Providers.Storage.UpdateTransactionStatus(ctx, transaction.ID, consts.TransactionStatusInProgress); err != nil {
		logger.Error().Msgf("error update transaction status: %+v", err)
		return nil, errs.PaymentsSmeErrs().InternalErrorError()
	}

	return &entity.ConfirmPaymentSmeResult{
		Status: string(consts.TransactionStatusInProgress),
	}, nil
}

func (u *useCasesImpl) checkDomesticMassPayment(ctx context.Context, transaction *entity.Transaction, payment *entity.Payment, signatories *entity.Signatory) (*colvir_bridge.CheckDomesticMassPaymentResponse, error) {
	logger := logs.FromContext(ctx)

	checkDomesticMassPaymentRes, err := generic.RetryWithBackoff(generic.ParamsRetryWithBackoff[colvir_bridge.CheckDomesticMassPaymentResponse]{
		MaxRetries: maxRetries,
		ErrMapFunc: func(err error) error {
			return err
		},
		CheckRetryNecessity: func(err error) bool {
			// так как метод колвир бриджа возвращает ошибку, только если статус не 200, то всегда нужно ретраить
			return true
		},
		Operation: func() (*colvir_bridge.CheckDomesticMassPaymentResponse, error) {
			checkReq, err := transaction.ToDomesticMassPaymentReq(payment, signatories)
			if err != nil {
				return nil, fmt.Errorf("error to check domestic mass payment req: %w", err)
			}

			checkRes, err := u.Providers.Colvirbridge.CheckDomesticMassPayment(ctx, checkReq)
			if err != nil {
				return nil, fmt.Errorf("error check domestic mass payment: %w", err)
			}

			return checkRes, nil
		},
	})
	if err != nil {
		logger.Error().Msgf("failed to colvir_bridge.CheckDomesticMassPayment: %s", err)
		return nil, errs.PaymentsSmeErrs().InternalErrorError()
	}
	logger.Debug().Interface("checkDomesticMassPaymentRes", checkDomesticMassPaymentRes).Msg("check domestic mass payment response")

	if checkDomesticMassPaymentRes == nil {
		logger.Error().Msg("check domestic mass payment response is nil")
		return nil, errs.PaymentsSmeErrs().InternalErrorError()
	}

	return checkDomesticMassPaymentRes, nil
}

func (u *useCasesImpl) executeDomesticMassPayment(ctx context.Context, transaction *entity.Transaction, payment *entity.Payment, signatories *entity.Signatory) (*colvir_bridge.ExecuteDomesticMassPaymentResponse, error) {
	logger := logs.FromContext(ctx)

	execDomesticMassPaymentRes, err := generic.RetryWithBackoff(generic.ParamsRetryWithBackoff[colvir_bridge.ExecuteDomesticMassPaymentResponse]{
		MaxRetries: maxRetries,
		ErrMapFunc: func(err error) error {
			return err
		},
		CheckRetryNecessity: func(err error) bool {
			// так как метод колвир бриджа возвращает ошибку, только если статус не 200, то всегда нужно ретраить
			return true
		},
		Operation: func() (*colvir_bridge.ExecuteDomesticMassPaymentResponse, error) {
			execReq, err := transaction.ToDomesticMassPaymentReq(payment, signatories)
			if err != nil {
				return nil, fmt.Errorf("error to execute domestic mass payment req: %w", err)
			}

			execRes, err := u.Providers.Colvirbridge.ExecuteDomesticMassPayment(ctx, execReq)
			if err != nil {
				return nil, fmt.Errorf("error execute domestic mass payment: %w", err)
			}

			return execRes, nil
		},
	})
	if err != nil {
		logger.Error().Msgf("failed to colvir_bridge.ExecuteDomesticMassPayment: %s", err)
		return nil, errs.PaymentsSmeErrs().InternalErrorError()
	}
	logger.Debug().Interface("execDomesticMassPaymentRes", execDomesticMassPaymentRes).Msg("execute domestic mass payment response")

	if execDomesticMassPaymentRes == nil {
		logger.Error().Msg("execute domestic mass payment response is nil")
		return nil, errs.PaymentsSmeErrs().InternalErrorError()
	}

	return execDomesticMassPaymentRes, nil
}
