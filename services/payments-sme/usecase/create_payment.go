package usecase

import (
	"context"
	"fmt"
	"os"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/serial"
	"github.com/google/uuid"

	errs "git.redmadrobot.com/zaman/backend/zaman/errs/paymentsSme"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/consts/configs"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/entity"
	pkbBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/pkb-bridge"
	usersPb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"
)

// CreatePayment создает платеж в рамках транзакции
// Параметры:
// - ctx: контекст запроса
// - req: запрос на создание платежа
// Возвращает:
// - *entity.CreatePaymentResult: результат создания платежа
// - error: ошибка, если возникла
func (u *useCasesImpl) CreatePayment(
	ctx context.Context,
	req *entity.CreatePaymentReq,
) (*entity.CreatePaymentResult, error) {
	// Получаем текущее время KZ
	// Используется для установки даты транзакции и платежа
	currentTime, err := utils.GetCurrentKzTime()
	if err != nil {
		errGettingCurrentKZTime := fmt.Errorf("error getting current KZ time: %w", err)

		return u.createErrorResponse(
			ctx,
			consts.PaymentReasonCodeUnableToProcess,
			errGettingCurrentKZTime,
		), errGettingCurrentKZTime
	}

	// Проверка происхождения запроса
	origin, err := utils.GetOrigin(ctx)
	if err != nil {
		return u.createErrorResponse(ctx, consts.PaymentReasonCodeUnableToProcess, err), err
	}

	if origin != utils.UserOriginSme {
		errOriginMismatch := errs.PaymentsSmeErrs().OriginNotSMEError()

		return u.createErrorResponse(ctx, consts.PaymentReasonCodeActionIsForbidden, errOriginMismatch), errOriginMismatch
	}

	// Проверка опердня (должен быть рабочий день)
	operDateResp, err := u.SmePaymentsWorktime(ctx, &entity.SmePaymentsWorktimeReq{})
	if err != nil {
		return u.createErrorResponse(ctx, consts.PaymentReasonCodeValidationError, err), err
	}

	if !operDateResp.IsDateOperational {
		return u.createErrorResponse(
			ctx,
			consts.PaymentReasonCodeWorktimeExceeded,
			fmt.Errorf("operational date %s is not operational", operDateResp.Date),
		), nil
	}

	loc, err := utils.GetLocaleFromContext(ctx, true, true)
	if err != nil {
		return u.createErrorResponse(
			ctx,
			consts.PaymentReasonCodeValidationError,
			err,
		), err
	}

	// считываем конфигурацию платежа по коду платежа
	cfg := configs.GetConfig(req.PaymentCode, loc)
	if cfg == nil {
		errConfig := fmt.Errorf("config for payment code %s not found", req.PaymentCode)

		return u.createErrorResponse(
			ctx,
			consts.PaymentReasonCodeValidationError,
			errConfig,
		), errConfig
	}

	// Получаем список доступных стран для валидации
	countries, err := u.getAvailableCountries(ctx)
	if err != nil {
		errGettingCountries := fmt.Errorf("error getting available countries: %w", err)

		return u.createErrorResponse(
			ctx,
			consts.PaymentReasonCodeUnableToProcess,
			errGettingCountries,
		), errGettingCountries
	}

	// Валидация платежа
	if err := req.Validate(ctx, *cfg, &countries); err != nil {
		return u.createErrorResponse(ctx, consts.PaymentReasonCodeValidationError, err), err
	}

	// получаем данные клиента
	smeIPInfo, err := u.getUserSmeIPInfo(ctx)
	if err != nil {
		smeIPError := fmt.Errorf("failed to get user sme IP info: %v", err)

		return u.createErrorResponse(ctx, consts.PaymentReasonCodeActionIsForbidden, smeIPError), smeIPError
	}

	// Получаем текущее имя плательщика (ИП) из пкб-бриджа
	jurSearch, err := u.Providers.Pkbbridge.SendJurSearchByIin(ctx, &pkbBridge.SendJurSearchByIinReq{
		Iin: smeIPInfo.GetIin(),
	})
	if err != nil {
		pkbBridgeError := fmt.Errorf("error getting personal data from pkb-bridge: %w", err)

		return u.createErrorResponse(ctx, consts.PaymentReasonCodeUnableToProcess, pkbBridgeError), pkbBridgeError
	}

	return u.createPayment(
		ctx,
		req,
		*cfg,
		smeIPInfo.GetUserID(),
		smeIPInfo.GetIin(),
		jurSearch.GetName(),
		currentTime,
	)
}

// createPayment создает платеж в рамках транзакции
// Параметры:
// - ctx: контекст запроса
// - req: запрос на создание платежа
// - cfg: конфигурация платежа
// - userID: ID пользователя, инициировавшего платеж
// - userIIN: ИИН пользователя, инициировавшего платеж
// - jurName: имя юридического лица (ИП), инициировавшего платеж
// - currentTime: текущее время в банке
// Возвращает:
// - *entity.CreatePaymentResult: результат создания платежа
// - error: ошибка, если возникла
func (u *useCasesImpl) createPayment(
	ctx context.Context,
	req *entity.CreatePaymentReq,
	cfg configs.Config,
	userID,
	userIIN,
	jurName string,
	currentTime time.Time,
) (*entity.CreatePaymentResult, error) {
	// Генерируем уникальный номер транзакции вида: SMETR060102150405123456
	transactionNumber, err := generateTransactionNumber(ctx, currentTime)
	if err != nil {
		errUniqueNumber := fmt.Errorf("error generating unique transaction number: %w", err)

		return u.createErrorResponse(
			ctx,
			consts.PaymentReasonCodeUnableToProcess,
			errUniqueNumber,
		), errUniqueNumber
	}

	// Конвертируем запрос в Transaction entity (родительская сущность)
	transaction := entity.MakeTransactionFromRequest(
		req,
		userID,
		userIIN,
		jurName,
		transactionNumber,
		currentTime,
	)
	// Устанавливаем конфиги
	transaction.SetConfig(cfg)

	// Конвертируем запрос в Payment entity (дочерняя сущность)
	payment := entity.MakePaymentFromRequest(
		req,
		transaction.ID,
	)

	// Устанавливаем конфиги
	payment.SetConfig(cfg, req)

	// Обрабатываем список сотрудников
	// Если список сотрудников пустой, то создается запись о текущем пользователе
	if err := u.processEmployeeList(ctx, userIIN, payment, req); err != nil {
		errProcessingEmployeeList := fmt.Errorf("error processing employee list: %w", err)

		return u.createErrorResponse(
			ctx,
			consts.PaymentReasonCodeUnableToProcess,
			errProcessingEmployeeList,
		), errProcessingEmployeeList
	}
	// Создаем запись о подписантах платежа
	signatory := entity.MakeSignatoryFromRequest(req, transaction.ID)

	// Создаем документы транзакция и платеж в рамках одной транзакции БД
	if err := u.Providers.Storage.CreateTransactionWithPayment(ctx, transaction, payment, signatory); err != nil {
		errStorage := fmt.Errorf("error creating transaction and payment in db: %w", err)

		// Если ошибка сохранения в БД, возвращаем ошибку
		return u.createErrorResponse(
			ctx,
			consts.PaymentReasonCodeUnableToProcess,
			errStorage,
		), errStorage
	}

	// Успешное создание платежа
	return &entity.CreatePaymentResult{
		TransactionID: &transaction.ID,
		Status:        consts.PaymentStatusInitialized,
		ReasonCode:    nil, // Нет ошибки
		Reason:        nil, // Нет ошибки
		OtpNeeded:     true,
	}, nil
}

// processEmployeeList обрабатывает список сотрудников для платежа
// Если список сотрудников пустой, то создается запись о текущем пользователе
// Если список сотрудников не пустой, то он сохраняется в платеж как есть
func (u *useCasesImpl) processEmployeeList(
	ctx context.Context,
	currentUserIIN string,
	payment *entity.Payment,
	req *entity.CreatePaymentReq,
) error {
	payment.PaymentPeriod = req.PaymentData.PaymentPeriod
	employeeList := make(map[string]any)
	// Конвертируем список сотрудников в JSON для хранения в БД
	if len(req.PaymentData.Employees) > 0 {
		employeeList[entity.EmployeesFieldName] = req.PaymentData.Employees
	} else {
		// Если список сотрудников пустой, значит платеж за себя,
		// в таком случае, информацию о текущем пользователе
		employee, err := u.getEmployeeItemOfCurrentUser(
			ctx,
			currentUserIIN,
			req.PaymentData.Amount,
			req.PaymentData.PaymentPeriod,
		)
		if err != nil {
			return err
		}
		// Создаем список сотрудников с одним элементом - текущим пользователем
		employeeList[entity.EmployeesFieldName] = []entity.EmployeeItem{
			employee,
		}
	}
	// Устанавливаем список сотрудников в платеж
	payment.EmployeeList = employeeList

	return nil
}

// getEmployeeItemOfCurrentUser получает информацию о текущем пользователе и создает запись сотрудника
// Используется для платежей, где нет списка сотрудников (например, платежи за себя)
func (u *useCasesImpl) getEmployeeItemOfCurrentUser(
	ctx context.Context,
	currentUserIIN,
	amount,
	valuePeriod string,
) (entity.EmployeeItem, error) {
	personalInfo, err := u.Providers.Pkbbridge.GetPersonalInfoByIin(
		ctx,
		&pkbBridge.GetPersonalInfoByIinReq{
			Iin:             currentUserIIN,
			MaxDataAgeHours: 14 * 24, // 14 календарных дней - максимально допустимый срок годности данных из БД Postgres (personal_infos)
		},
	)
	if err != nil {
		return entity.EmployeeItem{}, fmt.Errorf("failed to get personal info by IIN: %w", err)
	}

	// Проверяем гражданство
	countryCode := personalInfo.GetCitizenship().GetCode()
	if countryCode != consts.ISOLocalCountryCode {
		logger := logs.FromContext(ctx)
		logger.Error().Msgf(
			"Only %s (KZ) iso contry code is supported, but got: `%s`, of the person with IIN: %s, ",
			consts.ISOLocalCountryCode,
			countryCode,
			currentUserIIN,
		)

		return entity.EmployeeItem{}, fmt.Errorf("only KZ citizenship is acceptable")
	}

	middleName := personalInfo.GetPatronymic()
	return entity.EmployeeItem{
		Name:        personalInfo.GetName(),
		MiddleName:  &middleName,
		LastName:    personalInfo.GetSurname(),
		IIN:         currentUserIIN,
		Birthday:    personalInfo.GetDob(),
		Country:     consts.LocalCountryCode,
		Amount:      amount,
		ValuePeriod: valuePeriod,
	}, nil
}

// createErrorResponse создает ответ для ошибок валидации без TransactionID
func (u *useCasesImpl) createErrorResponse(
	ctx context.Context,
	reasonCode consts.PaymentReasonCode,
	err error,
) *entity.CreatePaymentResult {
	logger := logs.FromContext(ctx)
	logger.Error().Err(err).Msgf("%s", err.Error())
	reason := consts.GetReasonByCode(reasonCode)

	return &entity.CreatePaymentResult{
		TransactionID: nil, // Нет TransactionID при ошибках валидации
		Status:        consts.PaymentStatusRejected,
		ReasonCode:    &reasonCode,
		Reason:        &reason,
		OtpNeeded:     false,
	}
}

func (u *useCasesImpl) getUserSmeIPInfo(ctx context.Context) (*usersPb.GetUserSmeIPInfoResp, error) {
	userInfo, err := u.getUserInfo(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed getUserSmeIPInfo: %v", err)
	}

	user, err := u.Providers.Users.GetUserByID(ctx, &usersPb.GetUserByIDReq{
		ID: userInfo.UserID.String(),
	})
	if err != nil {
		return nil, fmt.Errorf("failed users.GetUserByID: %v", err)
	}

	// Получаем текущего ИП пользователя
	currentUserIPInfo, err := u.Providers.Users.GetUserSmeIPInfo(ctx, &usersPb.GetUserSmeIPInfoReq{
		Iin: user.Iin,
	})
	if err != nil {
		return nil, fmt.Errorf("failed users.GetUserSmeIPInfoo: %v", err)
	}

	return currentUserIPInfo, nil
}

func (u *useCasesImpl) getAvailableCountries(ctx context.Context) ([]entity.Country, error) {
	countryListResp, err := u.GetCountryList(ctx, &entity.GetCountryListReq{})
	if err != nil {
		return nil, fmt.Errorf("failed to get sme-country list: %w", err)
	}

	return countryListResp.Countries, nil
}

// generateTransactionNumber генерирует уникальный номер транзакции
func generateTransactionNumber(ctx context.Context, currentTime time.Time) (string, error) {
	hashString := os.Getenv(envVarHostName)
	if hashString == "" {
		logger := logs.FromContext(ctx)
		logger.Warn().Msgf("%s env var is empty", envVarHostName)

		hashString = uuid.New().String()
	}

	transactionNumber, err := serial.UniqueNumber(transactionNumberPrefix, hashString, currentTime)
	if err != nil {
		return "", err
	}

	return transactionNumber, nil
}
