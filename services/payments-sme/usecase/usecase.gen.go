// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package usecase

import (
	"context"

	paymentssme "git.redmadrobot.com/zaman/backend/zaman/config/services/payments-sme"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/bus"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/presenters"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"

	"git.redmadrobot.com/backend-go/rmr-pkg/proxy/hooks"

	"git.redmadrobot.com/zaman/backend/zaman/cmd/services/payments-sme/providers"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/entity"
)

var _ PaymentsSme = (*useCasesImpl)(nil)

type PaymentsSme interface {
	HealthCheck(ctx context.Context) (*entity.Health, error)
	HealthEvent(ctx context.Context, message *kafka.Message)
	InitConsumer(ctx context.Context)
	SmePaymentsClient(ctx context.Context, req *entity.SmePaymentsClientReq) (*entity.SmePaymentsClientResult, error)
	ConfirmPaymentSme(ctx context.Context, req *entity.ConfirmPaymentSmeReq) (*entity.ConfirmPaymentSmeResult, error)
	SmePaymentsCreateOtp(ctx context.Context, req *entity.SmePaymentsCreateOtpReq) (*entity.SmePaymentsCreateOtpResult, error)
	SmePaymentsOtpResend(ctx context.Context, req *entity.SmePaymentsOtpResendReq) (*entity.SmePaymentsOtpResendResult, error)
	SmePaymentsOtpValidate(ctx context.Context, req *entity.SmePaymentsOtpValidateReq) (*entity.SmePaymentsOtpValidateResult, error)
	GetKbeKodList(ctx context.Context, req *entity.GetKbeKodListReq) (*entity.GetKbeKodListResult, error)
	GetKnpList(ctx context.Context, req *entity.GetKnpListReq) (*entity.GetKnpListResult, error)
	GetBankList(ctx context.Context, req *entity.GetBankListReq) (*entity.GetBankListResult, error)
	GetCountryList(ctx context.Context, req *entity.GetCountryListReq) (*entity.GetCountryListResult, error)
	GetKbkList(ctx context.Context, req *entity.GetKbkListReq) (*entity.GetKbkListResult, error)
	GetTaxAuthorityList(ctx context.Context, req *entity.GetTaxAuthorityListReq) (*entity.GetTaxAuthorityListResult, error)
	SmePaymentsWorktime(ctx context.Context, req *entity.SmePaymentsWorktimeReq) (*entity.SmePaymentsWorktimeResult, error)
	CreatePayment(ctx context.Context, req *entity.CreatePaymentReq) (*entity.CreatePaymentResult, error)
	SmePaymentsGetPaymentOrder(ctx context.Context, req *entity.SmePaymentsGetPaymentOrderReq) (*entity.SmePaymentsGetPaymentOrderResult, error)
	SmePaymentsGetPaymentOrderByTrNumber(ctx context.Context, req *entity.SmePaymentsGetPaymentOrderByTrNumberReq) (*entity.SmePaymentsGetPaymentOrderByTrNumberResult, error)
	GetEmployeeList(ctx context.Context, req *entity.GetEmployeeListReq) (*entity.GetEmployeeListResult, error)
	DeleteEmployee(ctx context.Context, req *entity.DeleteEmployeeReq) (*entity.DeleteEmployeeResult, error)
	CreateEmployee(ctx context.Context, req *entity.CreateEmployeeReq) (*entity.CreateEmployeeResult, error)
	UpdateEmployee(ctx context.Context, req *entity.UpdateEmployeeReq) (*entity.UpdateEmployeeResult, error)
}

type useCasesImpl struct {
	PaymentsSme
	cfg       *paymentssme.Config
	Providers providers.ServiceLocatorImpl
}

func New(ctx context.Context, locator providers.ServiceLocatorImpl, cfg *paymentssme.Config) *PaymentsSmeHook {
	useCases := &useCasesImpl{
		cfg:       cfg,
		Providers: locator,
	}

	logger := logs.FromContext(ctx)

	hook := NewPaymentsSmeHook(
		useCases,
		hooks.GrpcServiceLogBeforeCall(logger, presenters.ViewLogs, presenters.DefaultViewOptions()),
		hooks.GrpcServiceLogPostCall(logger, presenters.ViewLogs, presenters.DefaultViewOptions()),
		hooks.GrpcServiceLogPanic(logger, presenters.ViewLogs, presenters.DefaultViewOptions()),
	)
	useCases.PaymentsSme = hook

	return hook
}
func (u *useCasesImpl) InitConsumer(ctx context.Context) {
	register := func(ctx context.Context, busName string, handler func(context.Context, *kafka.Message)) error {
		subscriber := bus.SubscriberFn[*kafka.Message](func(ctx context.Context, messages ...*kafka.Message) error {
			for _, message := range messages {
				handler(ctx, message)
				message.Ack(ctx)
			}
			return nil
		})

		err := u.Providers.Event.Subscribe(ctx, busName, subscriber)
		if err != nil {
			return err
		}
		return nil
	}

	eventRegistry := u.EventRegistry()
	for busName, handler := range eventRegistry {
		err := register(ctx, busName, handler)
		if err != nil {
			logs.FromContext(ctx).Err(err).Msg("unable to register handler")
		}
	}
}
