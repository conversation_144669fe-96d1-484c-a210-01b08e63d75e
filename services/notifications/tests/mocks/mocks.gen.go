// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package mocks

import (
	"testing"

	"github.com/golang/mock/gomock"
)

type Mocks struct {
	GRPC      GRPC
	Providers Providers
}

type GRPC struct {
	Smsbridge *MockSmsbridgeClient
	Users     *MockUsersClient
}

type Providers struct {
}

func NewMocks(t *testing.T) *Mocks {
	return &Mocks{
		GRPC: GRPC{
			Smsbridge: NewMockSmsbridgeClient(gomock.NewController(t)),
			Users:     NewMockUsersClient(gomock.NewController(t)),
		},
		Providers: Providers{},
	}
}
