// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/specs/proto/sms-bridge (interfaces: SmsbridgeClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"

	sms_bridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/sms-bridge"
)

// MockSmsbridgeClient is a mock of SmsbridgeClient interface.
type MockSmsbridgeClient struct {
	ctrl     *gomock.Controller
	recorder *MockSmsbridgeClientMockRecorder
}

// MockSmsbridgeClientMockRecorder is the mock recorder for MockSmsbridgeClient.
type MockSmsbridgeClientMockRecorder struct {
	mock *MockSmsbridgeClient
}

// NewMockSmsbridgeClient creates a new mock instance.
func NewMockSmsbridgeClient(ctrl *gomock.Controller) *MockSmsbridgeClient {
	mock := &MockSmsbridgeClient{ctrl: ctrl}
	mock.recorder = &MockSmsbridgeClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSmsbridgeClient) EXPECT() *MockSmsbridgeClientMockRecorder {
	return m.recorder
}

// HealthCheck mocks base method.
func (m *MockSmsbridgeClient) HealthCheck(arg0 context.Context, arg1 *sms_bridge.HealthCheckReq, arg2 ...grpc.CallOption) (*sms_bridge.HealthCheckResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HealthCheck", varargs...)
	ret0, _ := ret[0].(*sms_bridge.HealthCheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockSmsbridgeClientMockRecorder) HealthCheck(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockSmsbridgeClient)(nil).HealthCheck), varargs...)
}

// SendSMS mocks base method.
func (m *MockSmsbridgeClient) SendSMS(arg0 context.Context, arg1 *sms_bridge.SendSmsReq, arg2 ...grpc.CallOption) (*sms_bridge.SendSmsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendSMS", varargs...)
	ret0, _ := ret[0].(*sms_bridge.SendSmsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendSMS indicates an expected call of SendSMS.
func (mr *MockSmsbridgeClientMockRecorder) SendSMS(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendSMS", reflect.TypeOf((*MockSmsbridgeClient)(nil).SendSMS), varargs...)
}

// SmsStatus mocks base method.
func (m *MockSmsbridgeClient) SmsStatus(arg0 context.Context, arg1 *sms_bridge.SmsStatusReq, arg2 ...grpc.CallOption) (*sms_bridge.SmsStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SmsStatus", varargs...)
	ret0, _ := ret[0].(*sms_bridge.SmsStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SmsStatus indicates an expected call of SmsStatus.
func (mr *MockSmsbridgeClientMockRecorder) SmsStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SmsStatus", reflect.TypeOf((*MockSmsbridgeClient)(nil).SmsStatus), varargs...)
}
