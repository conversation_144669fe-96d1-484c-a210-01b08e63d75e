// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/predicate"
	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/smstemplates"
)

// SMSTemplatesDelete is the builder for deleting a SMSTemplates entity.
type SMSTemplatesDelete struct {
	config
	hooks    []Hook
	mutation *SMSTemplatesMutation
}

// Where appends a list predicates to the SMSTemplatesDelete builder.
func (_d *SMSTemplatesDelete) Where(ps ...predicate.SMSTemplates) *SMSTemplatesDelete {
	_d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (_d *SMSTemplatesDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, _d.sqlExec, _d.mutation, _d.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *SMSTemplatesDelete) ExecX(ctx context.Context) int {
	n, err := _d.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (_d *SMSTemplatesDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(smstemplates.Table, sqlgraph.NewFieldSpec(smstemplates.FieldID, field.TypeUUID))
	if ps := _d.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, _d.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	_d.mutation.done = true
	return affected, err
}

// SMSTemplatesDeleteOne is the builder for deleting a single SMSTemplates entity.
type SMSTemplatesDeleteOne struct {
	_d *SMSTemplatesDelete
}

// Where appends a list predicates to the SMSTemplatesDelete builder.
func (_d *SMSTemplatesDeleteOne) Where(ps ...predicate.SMSTemplates) *SMSTemplatesDeleteOne {
	_d._d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query.
func (_d *SMSTemplatesDeleteOne) Exec(ctx context.Context) error {
	n, err := _d._d.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{smstemplates.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *SMSTemplatesDeleteOne) ExecX(ctx context.Context) {
	if err := _d.Exec(ctx); err != nil {
		panic(err)
	}
}
