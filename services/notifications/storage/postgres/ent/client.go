// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/migrate"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"

	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/health"
	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/sms"
	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/smstemplates"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// Health is the client for interacting with the Health builders.
	Health *HealthClient
	// SMS is the client for interacting with the SMS builders.
	SMS *SMSClient
	// SMSTemplates is the client for interacting with the SMSTemplates builders.
	SMSTemplates *SMSTemplatesClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.Health = NewHealthClient(c.config)
	c.SMS = NewSMSClient(c.config)
	c.SMSTemplates = NewSMSTemplatesClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:          ctx,
		config:       cfg,
		Health:       NewHealthClient(cfg),
		SMS:          NewSMSClient(cfg),
		SMSTemplates: NewSMSTemplatesClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:          ctx,
		config:       cfg,
		Health:       NewHealthClient(cfg),
		SMS:          NewSMSClient(cfg),
		SMSTemplates: NewSMSTemplatesClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		Health.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	c.Health.Use(hooks...)
	c.SMS.Use(hooks...)
	c.SMSTemplates.Use(hooks...)
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	c.Health.Intercept(interceptors...)
	c.SMS.Intercept(interceptors...)
	c.SMSTemplates.Intercept(interceptors...)
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *HealthMutation:
		return c.Health.mutate(ctx, m)
	case *SMSMutation:
		return c.SMS.mutate(ctx, m)
	case *SMSTemplatesMutation:
		return c.SMSTemplates.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// HealthClient is a client for the Health schema.
type HealthClient struct {
	config
}

// NewHealthClient returns a client for the Health from the given config.
func NewHealthClient(c config) *HealthClient {
	return &HealthClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `health.Hooks(f(g(h())))`.
func (c *HealthClient) Use(hooks ...Hook) {
	c.hooks.Health = append(c.hooks.Health, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `health.Intercept(f(g(h())))`.
func (c *HealthClient) Intercept(interceptors ...Interceptor) {
	c.inters.Health = append(c.inters.Health, interceptors...)
}

// Create returns a builder for creating a Health entity.
func (c *HealthClient) Create() *HealthCreate {
	mutation := newHealthMutation(c.config, OpCreate)
	return &HealthCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Health entities.
func (c *HealthClient) CreateBulk(builders ...*HealthCreate) *HealthCreateBulk {
	return &HealthCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *HealthClient) MapCreateBulk(slice any, setFunc func(*HealthCreate, int)) *HealthCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &HealthCreateBulk{err: fmt.Errorf("calling to HealthClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*HealthCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &HealthCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Health.
func (c *HealthClient) Update() *HealthUpdate {
	mutation := newHealthMutation(c.config, OpUpdate)
	return &HealthUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *HealthClient) UpdateOne(_m *Health) *HealthUpdateOne {
	mutation := newHealthMutation(c.config, OpUpdateOne, withHealth(_m))
	return &HealthUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *HealthClient) UpdateOneID(id uuid.UUID) *HealthUpdateOne {
	mutation := newHealthMutation(c.config, OpUpdateOne, withHealthID(id))
	return &HealthUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Health.
func (c *HealthClient) Delete() *HealthDelete {
	mutation := newHealthMutation(c.config, OpDelete)
	return &HealthDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *HealthClient) DeleteOne(_m *Health) *HealthDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *HealthClient) DeleteOneID(id uuid.UUID) *HealthDeleteOne {
	builder := c.Delete().Where(health.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &HealthDeleteOne{builder}
}

// Query returns a query builder for Health.
func (c *HealthClient) Query() *HealthQuery {
	return &HealthQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeHealth},
		inters: c.Interceptors(),
	}
}

// Get returns a Health entity by its id.
func (c *HealthClient) Get(ctx context.Context, id uuid.UUID) (*Health, error) {
	return c.Query().Where(health.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *HealthClient) GetX(ctx context.Context, id uuid.UUID) *Health {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *HealthClient) Hooks() []Hook {
	return c.hooks.Health
}

// Interceptors returns the client interceptors.
func (c *HealthClient) Interceptors() []Interceptor {
	return c.inters.Health
}

func (c *HealthClient) mutate(ctx context.Context, m *HealthMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&HealthCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&HealthUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&HealthUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&HealthDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Health mutation op: %q", m.Op())
	}
}

// SMSClient is a client for the SMS schema.
type SMSClient struct {
	config
}

// NewSMSClient returns a client for the SMS from the given config.
func NewSMSClient(c config) *SMSClient {
	return &SMSClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `sms.Hooks(f(g(h())))`.
func (c *SMSClient) Use(hooks ...Hook) {
	c.hooks.SMS = append(c.hooks.SMS, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `sms.Intercept(f(g(h())))`.
func (c *SMSClient) Intercept(interceptors ...Interceptor) {
	c.inters.SMS = append(c.inters.SMS, interceptors...)
}

// Create returns a builder for creating a SMS entity.
func (c *SMSClient) Create() *SMSCreate {
	mutation := newSMSMutation(c.config, OpCreate)
	return &SMSCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of SMS entities.
func (c *SMSClient) CreateBulk(builders ...*SMSCreate) *SMSCreateBulk {
	return &SMSCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *SMSClient) MapCreateBulk(slice any, setFunc func(*SMSCreate, int)) *SMSCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &SMSCreateBulk{err: fmt.Errorf("calling to SMSClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*SMSCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &SMSCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for SMS.
func (c *SMSClient) Update() *SMSUpdate {
	mutation := newSMSMutation(c.config, OpUpdate)
	return &SMSUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *SMSClient) UpdateOne(_m *SMS) *SMSUpdateOne {
	mutation := newSMSMutation(c.config, OpUpdateOne, withSMS(_m))
	return &SMSUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *SMSClient) UpdateOneID(id int32) *SMSUpdateOne {
	mutation := newSMSMutation(c.config, OpUpdateOne, withSMSID(id))
	return &SMSUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for SMS.
func (c *SMSClient) Delete() *SMSDelete {
	mutation := newSMSMutation(c.config, OpDelete)
	return &SMSDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *SMSClient) DeleteOne(_m *SMS) *SMSDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *SMSClient) DeleteOneID(id int32) *SMSDeleteOne {
	builder := c.Delete().Where(sms.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &SMSDeleteOne{builder}
}

// Query returns a query builder for SMS.
func (c *SMSClient) Query() *SMSQuery {
	return &SMSQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeSMS},
		inters: c.Interceptors(),
	}
}

// Get returns a SMS entity by its id.
func (c *SMSClient) Get(ctx context.Context, id int32) (*SMS, error) {
	return c.Query().Where(sms.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *SMSClient) GetX(ctx context.Context, id int32) *SMS {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *SMSClient) Hooks() []Hook {
	return c.hooks.SMS
}

// Interceptors returns the client interceptors.
func (c *SMSClient) Interceptors() []Interceptor {
	return c.inters.SMS
}

func (c *SMSClient) mutate(ctx context.Context, m *SMSMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&SMSCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&SMSUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&SMSUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&SMSDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown SMS mutation op: %q", m.Op())
	}
}

// SMSTemplatesClient is a client for the SMSTemplates schema.
type SMSTemplatesClient struct {
	config
}

// NewSMSTemplatesClient returns a client for the SMSTemplates from the given config.
func NewSMSTemplatesClient(c config) *SMSTemplatesClient {
	return &SMSTemplatesClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `smstemplates.Hooks(f(g(h())))`.
func (c *SMSTemplatesClient) Use(hooks ...Hook) {
	c.hooks.SMSTemplates = append(c.hooks.SMSTemplates, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `smstemplates.Intercept(f(g(h())))`.
func (c *SMSTemplatesClient) Intercept(interceptors ...Interceptor) {
	c.inters.SMSTemplates = append(c.inters.SMSTemplates, interceptors...)
}

// Create returns a builder for creating a SMSTemplates entity.
func (c *SMSTemplatesClient) Create() *SMSTemplatesCreate {
	mutation := newSMSTemplatesMutation(c.config, OpCreate)
	return &SMSTemplatesCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of SMSTemplates entities.
func (c *SMSTemplatesClient) CreateBulk(builders ...*SMSTemplatesCreate) *SMSTemplatesCreateBulk {
	return &SMSTemplatesCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *SMSTemplatesClient) MapCreateBulk(slice any, setFunc func(*SMSTemplatesCreate, int)) *SMSTemplatesCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &SMSTemplatesCreateBulk{err: fmt.Errorf("calling to SMSTemplatesClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*SMSTemplatesCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &SMSTemplatesCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for SMSTemplates.
func (c *SMSTemplatesClient) Update() *SMSTemplatesUpdate {
	mutation := newSMSTemplatesMutation(c.config, OpUpdate)
	return &SMSTemplatesUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *SMSTemplatesClient) UpdateOne(_m *SMSTemplates) *SMSTemplatesUpdateOne {
	mutation := newSMSTemplatesMutation(c.config, OpUpdateOne, withSMSTemplates(_m))
	return &SMSTemplatesUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *SMSTemplatesClient) UpdateOneID(id uuid.UUID) *SMSTemplatesUpdateOne {
	mutation := newSMSTemplatesMutation(c.config, OpUpdateOne, withSMSTemplatesID(id))
	return &SMSTemplatesUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for SMSTemplates.
func (c *SMSTemplatesClient) Delete() *SMSTemplatesDelete {
	mutation := newSMSTemplatesMutation(c.config, OpDelete)
	return &SMSTemplatesDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *SMSTemplatesClient) DeleteOne(_m *SMSTemplates) *SMSTemplatesDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *SMSTemplatesClient) DeleteOneID(id uuid.UUID) *SMSTemplatesDeleteOne {
	builder := c.Delete().Where(smstemplates.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &SMSTemplatesDeleteOne{builder}
}

// Query returns a query builder for SMSTemplates.
func (c *SMSTemplatesClient) Query() *SMSTemplatesQuery {
	return &SMSTemplatesQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeSMSTemplates},
		inters: c.Interceptors(),
	}
}

// Get returns a SMSTemplates entity by its id.
func (c *SMSTemplatesClient) Get(ctx context.Context, id uuid.UUID) (*SMSTemplates, error) {
	return c.Query().Where(smstemplates.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *SMSTemplatesClient) GetX(ctx context.Context, id uuid.UUID) *SMSTemplates {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *SMSTemplatesClient) Hooks() []Hook {
	return c.hooks.SMSTemplates
}

// Interceptors returns the client interceptors.
func (c *SMSTemplatesClient) Interceptors() []Interceptor {
	return c.inters.SMSTemplates
}

func (c *SMSTemplatesClient) mutate(ctx context.Context, m *SMSTemplatesMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&SMSTemplatesCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&SMSTemplatesUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&SMSTemplatesUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&SMSTemplatesDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown SMSTemplates mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		Health, SMS, SMSTemplates []ent.Hook
	}
	inters struct {
		Health, SMS, SMSTemplates []ent.Interceptor
	}
)
