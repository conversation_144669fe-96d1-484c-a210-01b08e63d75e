// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/smstemplates"
)

// SMSTemplates is the model entity for the SMSTemplates schema.
type SMSTemplates struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// CreateTime holds the value of the "create_time" field.
	CreateTime time.Time `json:"create_time,omitempty"`
	// UpdateTime holds the value of the "update_time" field.
	UpdateTime time.Time `json:"update_time,omitempty"`
	// Type holds the value of the "type" field.
	Type string `json:"type,omitempty"`
	// Ru holds the value of the "ru" field.
	Ru string `json:"ru,omitempty"`
	// RuLatin holds the value of the "ru_latin" field.
	RuLatin string `json:"ru_latin,omitempty"`
	// Kz holds the value of the "kz" field.
	Kz string `json:"kz,omitempty"`
	// KzLatin holds the value of the "kz_latin" field.
	KzLatin string `json:"kz_latin,omitempty"`
	// En holds the value of the "en" field.
	En           string `json:"en,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*SMSTemplates) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case smstemplates.FieldType, smstemplates.FieldRu, smstemplates.FieldRuLatin, smstemplates.FieldKz, smstemplates.FieldKzLatin, smstemplates.FieldEn:
			values[i] = new(sql.NullString)
		case smstemplates.FieldCreateTime, smstemplates.FieldUpdateTime:
			values[i] = new(sql.NullTime)
		case smstemplates.FieldID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the SMSTemplates fields.
func (_m *SMSTemplates) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case smstemplates.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				_m.ID = *value
			}
		case smstemplates.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				_m.CreateTime = value.Time
			}
		case smstemplates.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				_m.UpdateTime = value.Time
			}
		case smstemplates.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				_m.Type = value.String
			}
		case smstemplates.FieldRu:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field ru", values[i])
			} else if value.Valid {
				_m.Ru = value.String
			}
		case smstemplates.FieldRuLatin:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field ru_latin", values[i])
			} else if value.Valid {
				_m.RuLatin = value.String
			}
		case smstemplates.FieldKz:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field kz", values[i])
			} else if value.Valid {
				_m.Kz = value.String
			}
		case smstemplates.FieldKzLatin:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field kz_latin", values[i])
			} else if value.Valid {
				_m.KzLatin = value.String
			}
		case smstemplates.FieldEn:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field en", values[i])
			} else if value.Valid {
				_m.En = value.String
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the SMSTemplates.
// This includes values selected through modifiers, order, etc.
func (_m *SMSTemplates) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// Update returns a builder for updating this SMSTemplates.
// Note that you need to call SMSTemplates.Unwrap() before calling this method if this SMSTemplates
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *SMSTemplates) Update() *SMSTemplatesUpdateOne {
	return NewSMSTemplatesClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the SMSTemplates entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *SMSTemplates) Unwrap() *SMSTemplates {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("ent: SMSTemplates is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *SMSTemplates) String() string {
	var builder strings.Builder
	builder.WriteString("SMSTemplates(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("create_time=")
	builder.WriteString(_m.CreateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_time=")
	builder.WriteString(_m.UpdateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(_m.Type)
	builder.WriteString(", ")
	builder.WriteString("ru=")
	builder.WriteString(_m.Ru)
	builder.WriteString(", ")
	builder.WriteString("ru_latin=")
	builder.WriteString(_m.RuLatin)
	builder.WriteString(", ")
	builder.WriteString("kz=")
	builder.WriteString(_m.Kz)
	builder.WriteString(", ")
	builder.WriteString("kz_latin=")
	builder.WriteString(_m.KzLatin)
	builder.WriteString(", ")
	builder.WriteString("en=")
	builder.WriteString(_m.En)
	builder.WriteByte(')')
	return builder.String()
}

// SMSTemplatesSlice is a parsable slice of SMSTemplates.
type SMSTemplatesSlice []*SMSTemplates
