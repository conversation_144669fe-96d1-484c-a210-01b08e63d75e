// Code generated by ent, DO NOT EDIT.

package ent

import (
	"time"

	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/health"
	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/sms"
	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/smstemplates"
	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/schema"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	healthFields := schema.Health{}.Fields()
	_ = healthFields
	// healthDescID is the schema descriptor for id field.
	healthDescID := healthFields[0].Descriptor()
	// health.DefaultID holds the default value on creation for the id field.
	health.DefaultID = healthDescID.Default.(func() uuid.UUID)
	smsMixin := schema.SMS{}.Mixin()
	smsMixinFields0 := smsMixin[0].Fields()
	_ = smsMixinFields0
	smsFields := schema.SMS{}.Fields()
	_ = smsFields
	// smsDescCreateTime is the schema descriptor for create_time field.
	smsDescCreateTime := smsMixinFields0[0].Descriptor()
	// sms.DefaultCreateTime holds the default value on creation for the create_time field.
	sms.DefaultCreateTime = smsDescCreateTime.Default.(func() time.Time)
	// smsDescUpdateTime is the schema descriptor for update_time field.
	smsDescUpdateTime := smsMixinFields0[1].Descriptor()
	// sms.DefaultUpdateTime holds the default value on creation for the update_time field.
	sms.DefaultUpdateTime = smsDescUpdateTime.Default.(func() time.Time)
	// sms.UpdateDefaultUpdateTime holds the default value on update for the update_time field.
	sms.UpdateDefaultUpdateTime = smsDescUpdateTime.UpdateDefault.(func() time.Time)
	smstemplatesMixin := schema.SMSTemplates{}.Mixin()
	smstemplatesMixinFields0 := smstemplatesMixin[0].Fields()
	_ = smstemplatesMixinFields0
	smstemplatesFields := schema.SMSTemplates{}.Fields()
	_ = smstemplatesFields
	// smstemplatesDescCreateTime is the schema descriptor for create_time field.
	smstemplatesDescCreateTime := smstemplatesMixinFields0[0].Descriptor()
	// smstemplates.DefaultCreateTime holds the default value on creation for the create_time field.
	smstemplates.DefaultCreateTime = smstemplatesDescCreateTime.Default.(func() time.Time)
	// smstemplatesDescUpdateTime is the schema descriptor for update_time field.
	smstemplatesDescUpdateTime := smstemplatesMixinFields0[1].Descriptor()
	// smstemplates.DefaultUpdateTime holds the default value on creation for the update_time field.
	smstemplates.DefaultUpdateTime = smstemplatesDescUpdateTime.Default.(func() time.Time)
	// smstemplates.UpdateDefaultUpdateTime holds the default value on update for the update_time field.
	smstemplates.UpdateDefaultUpdateTime = smstemplatesDescUpdateTime.UpdateDefault.(func() time.Time)
	// smstemplatesDescID is the schema descriptor for id field.
	smstemplatesDescID := smstemplatesFields[0].Descriptor()
	// smstemplates.DefaultID holds the default value on creation for the id field.
	smstemplates.DefaultID = smstemplatesDescID.Default.(func() uuid.UUID)
}
