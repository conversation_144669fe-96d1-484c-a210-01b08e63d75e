// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/health"
	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/predicate"
)

// HealthUpdate is the builder for updating Health entities.
type HealthUpdate struct {
	config
	hooks     []Hook
	mutation  *HealthMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the HealthUpdate builder.
func (_u *HealthUpdate) Where(ps ...predicate.Health) *HealthUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// Mutation returns the HealthMutation object of the builder.
func (_u *HealthUpdate) Mutation() *HealthMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *HealthUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *HealthUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *HealthUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *HealthUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *HealthUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *HealthUpdate {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *HealthUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	_spec := sqlgraph.NewUpdateSpec(health.Table, health.Columns, sqlgraph.NewFieldSpec(health.FieldID, field.TypeUUID))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	_spec.AddModifiers(_u.modifiers...)
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{health.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// HealthUpdateOne is the builder for updating a single Health entity.
type HealthUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *HealthMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Mutation returns the HealthMutation object of the builder.
func (_u *HealthUpdateOne) Mutation() *HealthMutation {
	return _u.mutation
}

// Where appends a list predicates to the HealthUpdate builder.
func (_u *HealthUpdateOne) Where(ps ...predicate.Health) *HealthUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *HealthUpdateOne) Select(field string, fields ...string) *HealthUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated Health entity.
func (_u *HealthUpdateOne) Save(ctx context.Context) (*Health, error) {
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *HealthUpdateOne) SaveX(ctx context.Context) *Health {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *HealthUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *HealthUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *HealthUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *HealthUpdateOne {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *HealthUpdateOne) sqlSave(ctx context.Context) (_node *Health, err error) {
	_spec := sqlgraph.NewUpdateSpec(health.Table, health.Columns, sqlgraph.NewFieldSpec(health.FieldID, field.TypeUUID))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Health.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, health.FieldID)
		for _, f := range fields {
			if !health.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != health.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	_spec.AddModifiers(_u.modifiers...)
	_node = &Health{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{health.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
