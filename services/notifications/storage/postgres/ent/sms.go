// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"

	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/sms"
)

// SMS is the model entity for the SMS schema.
type SMS struct {
	config `json:"-"`
	// ID of the ent.
	ID int32 `json:"id,omitempty"`
	// CreateTime holds the value of the "create_time" field.
	CreateTime time.Time `json:"create_time,omitempty"`
	// UpdateTime holds the value of the "update_time" field.
	UpdateTime time.Time `json:"update_time,omitempty"`
	// PhoneNumber holds the value of the "phone_number" field.
	PhoneNumber string `json:"phone_number,omitempty"`
	// Type holds the value of the "type" field.
	Type string `json:"type,omitempty"`
	// Args holds the value of the "args" field.
	Args map[string]string `json:"args,omitempty"`
	// Text holds the value of the "text" field.
	Text string `json:"text,omitempty"`
	// Metadata holds the value of the "metadata" field.
	Metadata     map[string]string `json:"metadata,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*SMS) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case sms.FieldArgs, sms.FieldMetadata:
			values[i] = new([]byte)
		case sms.FieldID:
			values[i] = new(sql.NullInt64)
		case sms.FieldPhoneNumber, sms.FieldType, sms.FieldText:
			values[i] = new(sql.NullString)
		case sms.FieldCreateTime, sms.FieldUpdateTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the SMS fields.
func (_m *SMS) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case sms.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			_m.ID = int32(value.Int64)
		case sms.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				_m.CreateTime = value.Time
			}
		case sms.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				_m.UpdateTime = value.Time
			}
		case sms.FieldPhoneNumber:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field phone_number", values[i])
			} else if value.Valid {
				_m.PhoneNumber = value.String
			}
		case sms.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				_m.Type = value.String
			}
		case sms.FieldArgs:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field args", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &_m.Args); err != nil {
					return fmt.Errorf("unmarshal field args: %w", err)
				}
			}
		case sms.FieldText:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field text", values[i])
			} else if value.Valid {
				_m.Text = value.String
			}
		case sms.FieldMetadata:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &_m.Metadata); err != nil {
					return fmt.Errorf("unmarshal field metadata: %w", err)
				}
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the SMS.
// This includes values selected through modifiers, order, etc.
func (_m *SMS) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// Update returns a builder for updating this SMS.
// Note that you need to call SMS.Unwrap() before calling this method if this SMS
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *SMS) Update() *SMSUpdateOne {
	return NewSMSClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the SMS entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *SMS) Unwrap() *SMS {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("ent: SMS is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *SMS) String() string {
	var builder strings.Builder
	builder.WriteString("SMS(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("create_time=")
	builder.WriteString(_m.CreateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_time=")
	builder.WriteString(_m.UpdateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("phone_number=")
	builder.WriteString(_m.PhoneNumber)
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(_m.Type)
	builder.WriteString(", ")
	builder.WriteString("args=")
	builder.WriteString(fmt.Sprintf("%v", _m.Args))
	builder.WriteString(", ")
	builder.WriteString("text=")
	builder.WriteString(_m.Text)
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(fmt.Sprintf("%v", _m.Metadata))
	builder.WriteByte(')')
	return builder.String()
}

// SMSs is a parsable slice of SMS.
type SMSs []*SMS
