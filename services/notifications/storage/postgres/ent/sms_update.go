// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/predicate"
	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/sms"
)

// SMSUpdate is the builder for updating SMS entities.
type SMSUpdate struct {
	config
	hooks     []Hook
	mutation  *SMSMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the SMSUpdate builder.
func (_u *SMSUpdate) Where(ps ...predicate.SMS) *SMSUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetPhoneNumber sets the "phone_number" field.
func (_u *SMSUpdate) SetPhoneNumber(v string) *SMSUpdate {
	_u.mutation.SetPhoneNumber(v)
	return _u
}

// SetNillablePhoneNumber sets the "phone_number" field if the given value is not nil.
func (_u *SMSUpdate) SetNillablePhoneNumber(v *string) *SMSUpdate {
	if v != nil {
		_u.SetPhoneNumber(*v)
	}
	return _u
}

// SetType sets the "type" field.
func (_u *SMSUpdate) SetType(v string) *SMSUpdate {
	_u.mutation.SetType(v)
	return _u
}

// SetNillableType sets the "type" field if the given value is not nil.
func (_u *SMSUpdate) SetNillableType(v *string) *SMSUpdate {
	if v != nil {
		_u.SetType(*v)
	}
	return _u
}

// SetArgs sets the "args" field.
func (_u *SMSUpdate) SetArgs(v map[string]string) *SMSUpdate {
	_u.mutation.SetArgs(v)
	return _u
}

// ClearArgs clears the value of the "args" field.
func (_u *SMSUpdate) ClearArgs() *SMSUpdate {
	_u.mutation.ClearArgs()
	return _u
}

// SetText sets the "text" field.
func (_u *SMSUpdate) SetText(v string) *SMSUpdate {
	_u.mutation.SetText(v)
	return _u
}

// SetNillableText sets the "text" field if the given value is not nil.
func (_u *SMSUpdate) SetNillableText(v *string) *SMSUpdate {
	if v != nil {
		_u.SetText(*v)
	}
	return _u
}

// SetMetadata sets the "metadata" field.
func (_u *SMSUpdate) SetMetadata(v map[string]string) *SMSUpdate {
	_u.mutation.SetMetadata(v)
	return _u
}

// ClearMetadata clears the value of the "metadata" field.
func (_u *SMSUpdate) ClearMetadata() *SMSUpdate {
	_u.mutation.ClearMetadata()
	return _u
}

// Mutation returns the SMSMutation object of the builder.
func (_u *SMSUpdate) Mutation() *SMSMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *SMSUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *SMSUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *SMSUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *SMSUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *SMSUpdate) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := sms.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *SMSUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *SMSUpdate {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *SMSUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	_spec := sqlgraph.NewUpdateSpec(sms.Table, sms.Columns, sqlgraph.NewFieldSpec(sms.FieldID, field.TypeInt32))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(sms.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(sms.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(sms.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.PhoneNumber(); ok {
		_spec.SetField(sms.FieldPhoneNumber, field.TypeString, value)
	}
	if value, ok := _u.mutation.GetType(); ok {
		_spec.SetField(sms.FieldType, field.TypeString, value)
	}
	if value, ok := _u.mutation.Args(); ok {
		_spec.SetField(sms.FieldArgs, field.TypeJSON, value)
	}
	if _u.mutation.ArgsCleared() {
		_spec.ClearField(sms.FieldArgs, field.TypeJSON)
	}
	if value, ok := _u.mutation.Text(); ok {
		_spec.SetField(sms.FieldText, field.TypeString, value)
	}
	if value, ok := _u.mutation.Metadata(); ok {
		_spec.SetField(sms.FieldMetadata, field.TypeJSON, value)
	}
	if _u.mutation.MetadataCleared() {
		_spec.ClearField(sms.FieldMetadata, field.TypeJSON)
	}
	_spec.AddModifiers(_u.modifiers...)
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{sms.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// SMSUpdateOne is the builder for updating a single SMS entity.
type SMSUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *SMSMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetPhoneNumber sets the "phone_number" field.
func (_u *SMSUpdateOne) SetPhoneNumber(v string) *SMSUpdateOne {
	_u.mutation.SetPhoneNumber(v)
	return _u
}

// SetNillablePhoneNumber sets the "phone_number" field if the given value is not nil.
func (_u *SMSUpdateOne) SetNillablePhoneNumber(v *string) *SMSUpdateOne {
	if v != nil {
		_u.SetPhoneNumber(*v)
	}
	return _u
}

// SetType sets the "type" field.
func (_u *SMSUpdateOne) SetType(v string) *SMSUpdateOne {
	_u.mutation.SetType(v)
	return _u
}

// SetNillableType sets the "type" field if the given value is not nil.
func (_u *SMSUpdateOne) SetNillableType(v *string) *SMSUpdateOne {
	if v != nil {
		_u.SetType(*v)
	}
	return _u
}

// SetArgs sets the "args" field.
func (_u *SMSUpdateOne) SetArgs(v map[string]string) *SMSUpdateOne {
	_u.mutation.SetArgs(v)
	return _u
}

// ClearArgs clears the value of the "args" field.
func (_u *SMSUpdateOne) ClearArgs() *SMSUpdateOne {
	_u.mutation.ClearArgs()
	return _u
}

// SetText sets the "text" field.
func (_u *SMSUpdateOne) SetText(v string) *SMSUpdateOne {
	_u.mutation.SetText(v)
	return _u
}

// SetNillableText sets the "text" field if the given value is not nil.
func (_u *SMSUpdateOne) SetNillableText(v *string) *SMSUpdateOne {
	if v != nil {
		_u.SetText(*v)
	}
	return _u
}

// SetMetadata sets the "metadata" field.
func (_u *SMSUpdateOne) SetMetadata(v map[string]string) *SMSUpdateOne {
	_u.mutation.SetMetadata(v)
	return _u
}

// ClearMetadata clears the value of the "metadata" field.
func (_u *SMSUpdateOne) ClearMetadata() *SMSUpdateOne {
	_u.mutation.ClearMetadata()
	return _u
}

// Mutation returns the SMSMutation object of the builder.
func (_u *SMSUpdateOne) Mutation() *SMSMutation {
	return _u.mutation
}

// Where appends a list predicates to the SMSUpdate builder.
func (_u *SMSUpdateOne) Where(ps ...predicate.SMS) *SMSUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *SMSUpdateOne) Select(field string, fields ...string) *SMSUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated SMS entity.
func (_u *SMSUpdateOne) Save(ctx context.Context) (*SMS, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *SMSUpdateOne) SaveX(ctx context.Context) *SMS {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *SMSUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *SMSUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *SMSUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := sms.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *SMSUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *SMSUpdateOne {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *SMSUpdateOne) sqlSave(ctx context.Context) (_node *SMS, err error) {
	_spec := sqlgraph.NewUpdateSpec(sms.Table, sms.Columns, sqlgraph.NewFieldSpec(sms.FieldID, field.TypeInt32))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "SMS.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, sms.FieldID)
		for _, f := range fields {
			if !sms.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != sms.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(sms.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(sms.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(sms.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.PhoneNumber(); ok {
		_spec.SetField(sms.FieldPhoneNumber, field.TypeString, value)
	}
	if value, ok := _u.mutation.GetType(); ok {
		_spec.SetField(sms.FieldType, field.TypeString, value)
	}
	if value, ok := _u.mutation.Args(); ok {
		_spec.SetField(sms.FieldArgs, field.TypeJSON, value)
	}
	if _u.mutation.ArgsCleared() {
		_spec.ClearField(sms.FieldArgs, field.TypeJSON)
	}
	if value, ok := _u.mutation.Text(); ok {
		_spec.SetField(sms.FieldText, field.TypeString, value)
	}
	if value, ok := _u.mutation.Metadata(); ok {
		_spec.SetField(sms.FieldMetadata, field.TypeJSON, value)
	}
	if _u.mutation.MetadataCleared() {
		_spec.ClearField(sms.FieldMetadata, field.TypeJSON)
	}
	_spec.AddModifiers(_u.modifiers...)
	_node = &SMS{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{sms.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
