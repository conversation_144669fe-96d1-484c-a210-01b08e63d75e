// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/smstemplates"
)

// SMSTemplatesCreate is the builder for creating a SMSTemplates entity.
type SMSTemplatesCreate struct {
	config
	mutation *SMSTemplatesMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (_c *SMSTemplatesCreate) SetCreateTime(v time.Time) *SMSTemplatesCreate {
	_c.mutation.SetCreateTime(v)
	return _c
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (_c *SMSTemplatesCreate) SetNillableCreateTime(v *time.Time) *SMSTemplatesCreate {
	if v != nil {
		_c.SetCreateTime(*v)
	}
	return _c
}

// SetUpdateTime sets the "update_time" field.
func (_c *SMSTemplatesCreate) SetUpdateTime(v time.Time) *SMSTemplatesCreate {
	_c.mutation.SetUpdateTime(v)
	return _c
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (_c *SMSTemplatesCreate) SetNillableUpdateTime(v *time.Time) *SMSTemplatesCreate {
	if v != nil {
		_c.SetUpdateTime(*v)
	}
	return _c
}

// SetType sets the "type" field.
func (_c *SMSTemplatesCreate) SetType(v string) *SMSTemplatesCreate {
	_c.mutation.SetType(v)
	return _c
}

// SetRu sets the "ru" field.
func (_c *SMSTemplatesCreate) SetRu(v string) *SMSTemplatesCreate {
	_c.mutation.SetRu(v)
	return _c
}

// SetRuLatin sets the "ru_latin" field.
func (_c *SMSTemplatesCreate) SetRuLatin(v string) *SMSTemplatesCreate {
	_c.mutation.SetRuLatin(v)
	return _c
}

// SetKz sets the "kz" field.
func (_c *SMSTemplatesCreate) SetKz(v string) *SMSTemplatesCreate {
	_c.mutation.SetKz(v)
	return _c
}

// SetKzLatin sets the "kz_latin" field.
func (_c *SMSTemplatesCreate) SetKzLatin(v string) *SMSTemplatesCreate {
	_c.mutation.SetKzLatin(v)
	return _c
}

// SetEn sets the "en" field.
func (_c *SMSTemplatesCreate) SetEn(v string) *SMSTemplatesCreate {
	_c.mutation.SetEn(v)
	return _c
}

// SetID sets the "id" field.
func (_c *SMSTemplatesCreate) SetID(v uuid.UUID) *SMSTemplatesCreate {
	_c.mutation.SetID(v)
	return _c
}

// SetNillableID sets the "id" field if the given value is not nil.
func (_c *SMSTemplatesCreate) SetNillableID(v *uuid.UUID) *SMSTemplatesCreate {
	if v != nil {
		_c.SetID(*v)
	}
	return _c
}

// Mutation returns the SMSTemplatesMutation object of the builder.
func (_c *SMSTemplatesCreate) Mutation() *SMSTemplatesMutation {
	return _c.mutation
}

// Save creates the SMSTemplates in the database.
func (_c *SMSTemplatesCreate) Save(ctx context.Context) (*SMSTemplates, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *SMSTemplatesCreate) SaveX(ctx context.Context) *SMSTemplates {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *SMSTemplatesCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *SMSTemplatesCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *SMSTemplatesCreate) defaults() {
	if _, ok := _c.mutation.CreateTime(); !ok {
		v := smstemplates.DefaultCreateTime()
		_c.mutation.SetCreateTime(v)
	}
	if _, ok := _c.mutation.UpdateTime(); !ok {
		v := smstemplates.DefaultUpdateTime()
		_c.mutation.SetUpdateTime(v)
	}
	if _, ok := _c.mutation.ID(); !ok {
		v := smstemplates.DefaultID()
		_c.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *SMSTemplatesCreate) check() error {
	if _, ok := _c.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "SMSTemplates.type"`)}
	}
	if _, ok := _c.mutation.Ru(); !ok {
		return &ValidationError{Name: "ru", err: errors.New(`ent: missing required field "SMSTemplates.ru"`)}
	}
	if _, ok := _c.mutation.RuLatin(); !ok {
		return &ValidationError{Name: "ru_latin", err: errors.New(`ent: missing required field "SMSTemplates.ru_latin"`)}
	}
	if _, ok := _c.mutation.Kz(); !ok {
		return &ValidationError{Name: "kz", err: errors.New(`ent: missing required field "SMSTemplates.kz"`)}
	}
	if _, ok := _c.mutation.KzLatin(); !ok {
		return &ValidationError{Name: "kz_latin", err: errors.New(`ent: missing required field "SMSTemplates.kz_latin"`)}
	}
	if _, ok := _c.mutation.En(); !ok {
		return &ValidationError{Name: "en", err: errors.New(`ent: missing required field "SMSTemplates.en"`)}
	}
	return nil
}

func (_c *SMSTemplatesCreate) sqlSave(ctx context.Context) (*SMSTemplates, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *SMSTemplatesCreate) createSpec() (*SMSTemplates, *sqlgraph.CreateSpec) {
	var (
		_node = &SMSTemplates{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(smstemplates.Table, sqlgraph.NewFieldSpec(smstemplates.FieldID, field.TypeUUID))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := _c.mutation.CreateTime(); ok {
		_spec.SetField(smstemplates.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = value
	}
	if value, ok := _c.mutation.UpdateTime(); ok {
		_spec.SetField(smstemplates.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = value
	}
	if value, ok := _c.mutation.GetType(); ok {
		_spec.SetField(smstemplates.FieldType, field.TypeString, value)
		_node.Type = value
	}
	if value, ok := _c.mutation.Ru(); ok {
		_spec.SetField(smstemplates.FieldRu, field.TypeString, value)
		_node.Ru = value
	}
	if value, ok := _c.mutation.RuLatin(); ok {
		_spec.SetField(smstemplates.FieldRuLatin, field.TypeString, value)
		_node.RuLatin = value
	}
	if value, ok := _c.mutation.Kz(); ok {
		_spec.SetField(smstemplates.FieldKz, field.TypeString, value)
		_node.Kz = value
	}
	if value, ok := _c.mutation.KzLatin(); ok {
		_spec.SetField(smstemplates.FieldKzLatin, field.TypeString, value)
		_node.KzLatin = value
	}
	if value, ok := _c.mutation.En(); ok {
		_spec.SetField(smstemplates.FieldEn, field.TypeString, value)
		_node.En = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.SMSTemplates.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.SMSTemplatesUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *SMSTemplatesCreate) OnConflict(opts ...sql.ConflictOption) *SMSTemplatesUpsertOne {
	_c.conflict = opts
	return &SMSTemplatesUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.SMSTemplates.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *SMSTemplatesCreate) OnConflictColumns(columns ...string) *SMSTemplatesUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &SMSTemplatesUpsertOne{
		create: _c,
	}
}

type (
	// SMSTemplatesUpsertOne is the builder for "upsert"-ing
	//  one SMSTemplates node.
	SMSTemplatesUpsertOne struct {
		create *SMSTemplatesCreate
	}

	// SMSTemplatesUpsert is the "OnConflict" setter.
	SMSTemplatesUpsert struct {
		*sql.UpdateSet
	}
)

// SetType sets the "type" field.
func (u *SMSTemplatesUpsert) SetType(v string) *SMSTemplatesUpsert {
	u.Set(smstemplates.FieldType, v)
	return u
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *SMSTemplatesUpsert) UpdateType() *SMSTemplatesUpsert {
	u.SetExcluded(smstemplates.FieldType)
	return u
}

// SetRu sets the "ru" field.
func (u *SMSTemplatesUpsert) SetRu(v string) *SMSTemplatesUpsert {
	u.Set(smstemplates.FieldRu, v)
	return u
}

// UpdateRu sets the "ru" field to the value that was provided on create.
func (u *SMSTemplatesUpsert) UpdateRu() *SMSTemplatesUpsert {
	u.SetExcluded(smstemplates.FieldRu)
	return u
}

// SetRuLatin sets the "ru_latin" field.
func (u *SMSTemplatesUpsert) SetRuLatin(v string) *SMSTemplatesUpsert {
	u.Set(smstemplates.FieldRuLatin, v)
	return u
}

// UpdateRuLatin sets the "ru_latin" field to the value that was provided on create.
func (u *SMSTemplatesUpsert) UpdateRuLatin() *SMSTemplatesUpsert {
	u.SetExcluded(smstemplates.FieldRuLatin)
	return u
}

// SetKz sets the "kz" field.
func (u *SMSTemplatesUpsert) SetKz(v string) *SMSTemplatesUpsert {
	u.Set(smstemplates.FieldKz, v)
	return u
}

// UpdateKz sets the "kz" field to the value that was provided on create.
func (u *SMSTemplatesUpsert) UpdateKz() *SMSTemplatesUpsert {
	u.SetExcluded(smstemplates.FieldKz)
	return u
}

// SetKzLatin sets the "kz_latin" field.
func (u *SMSTemplatesUpsert) SetKzLatin(v string) *SMSTemplatesUpsert {
	u.Set(smstemplates.FieldKzLatin, v)
	return u
}

// UpdateKzLatin sets the "kz_latin" field to the value that was provided on create.
func (u *SMSTemplatesUpsert) UpdateKzLatin() *SMSTemplatesUpsert {
	u.SetExcluded(smstemplates.FieldKzLatin)
	return u
}

// SetEn sets the "en" field.
func (u *SMSTemplatesUpsert) SetEn(v string) *SMSTemplatesUpsert {
	u.Set(smstemplates.FieldEn, v)
	return u
}

// UpdateEn sets the "en" field to the value that was provided on create.
func (u *SMSTemplatesUpsert) UpdateEn() *SMSTemplatesUpsert {
	u.SetExcluded(smstemplates.FieldEn)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.SMSTemplates.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(smstemplates.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *SMSTemplatesUpsertOne) UpdateNewValues() *SMSTemplatesUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(smstemplates.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(smstemplates.FieldCreateTime)
		}
		if _, exists := u.create.mutation.UpdateTime(); exists {
			s.SetIgnore(smstemplates.FieldUpdateTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.SMSTemplates.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *SMSTemplatesUpsertOne) Ignore() *SMSTemplatesUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *SMSTemplatesUpsertOne) DoNothing() *SMSTemplatesUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the SMSTemplatesCreate.OnConflict
// documentation for more info.
func (u *SMSTemplatesUpsertOne) Update(set func(*SMSTemplatesUpsert)) *SMSTemplatesUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&SMSTemplatesUpsert{UpdateSet: update})
	}))
	return u
}

// SetType sets the "type" field.
func (u *SMSTemplatesUpsertOne) SetType(v string) *SMSTemplatesUpsertOne {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.SetType(v)
	})
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *SMSTemplatesUpsertOne) UpdateType() *SMSTemplatesUpsertOne {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.UpdateType()
	})
}

// SetRu sets the "ru" field.
func (u *SMSTemplatesUpsertOne) SetRu(v string) *SMSTemplatesUpsertOne {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.SetRu(v)
	})
}

// UpdateRu sets the "ru" field to the value that was provided on create.
func (u *SMSTemplatesUpsertOne) UpdateRu() *SMSTemplatesUpsertOne {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.UpdateRu()
	})
}

// SetRuLatin sets the "ru_latin" field.
func (u *SMSTemplatesUpsertOne) SetRuLatin(v string) *SMSTemplatesUpsertOne {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.SetRuLatin(v)
	})
}

// UpdateRuLatin sets the "ru_latin" field to the value that was provided on create.
func (u *SMSTemplatesUpsertOne) UpdateRuLatin() *SMSTemplatesUpsertOne {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.UpdateRuLatin()
	})
}

// SetKz sets the "kz" field.
func (u *SMSTemplatesUpsertOne) SetKz(v string) *SMSTemplatesUpsertOne {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.SetKz(v)
	})
}

// UpdateKz sets the "kz" field to the value that was provided on create.
func (u *SMSTemplatesUpsertOne) UpdateKz() *SMSTemplatesUpsertOne {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.UpdateKz()
	})
}

// SetKzLatin sets the "kz_latin" field.
func (u *SMSTemplatesUpsertOne) SetKzLatin(v string) *SMSTemplatesUpsertOne {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.SetKzLatin(v)
	})
}

// UpdateKzLatin sets the "kz_latin" field to the value that was provided on create.
func (u *SMSTemplatesUpsertOne) UpdateKzLatin() *SMSTemplatesUpsertOne {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.UpdateKzLatin()
	})
}

// SetEn sets the "en" field.
func (u *SMSTemplatesUpsertOne) SetEn(v string) *SMSTemplatesUpsertOne {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.SetEn(v)
	})
}

// UpdateEn sets the "en" field to the value that was provided on create.
func (u *SMSTemplatesUpsertOne) UpdateEn() *SMSTemplatesUpsertOne {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.UpdateEn()
	})
}

// Exec executes the query.
func (u *SMSTemplatesUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for SMSTemplatesCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *SMSTemplatesUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *SMSTemplatesUpsertOne) ID(ctx context.Context) (id uuid.UUID, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: SMSTemplatesUpsertOne.ID is not supported by MySQL driver. Use SMSTemplatesUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *SMSTemplatesUpsertOne) IDX(ctx context.Context) uuid.UUID {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// SMSTemplatesCreateBulk is the builder for creating many SMSTemplates entities in bulk.
type SMSTemplatesCreateBulk struct {
	config
	err      error
	builders []*SMSTemplatesCreate
	conflict []sql.ConflictOption
}

// Save creates the SMSTemplates entities in the database.
func (_c *SMSTemplatesCreateBulk) Save(ctx context.Context) ([]*SMSTemplates, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*SMSTemplates, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*SMSTemplatesMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *SMSTemplatesCreateBulk) SaveX(ctx context.Context) []*SMSTemplates {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *SMSTemplatesCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *SMSTemplatesCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.SMSTemplates.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.SMSTemplatesUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *SMSTemplatesCreateBulk) OnConflict(opts ...sql.ConflictOption) *SMSTemplatesUpsertBulk {
	_c.conflict = opts
	return &SMSTemplatesUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.SMSTemplates.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *SMSTemplatesCreateBulk) OnConflictColumns(columns ...string) *SMSTemplatesUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &SMSTemplatesUpsertBulk{
		create: _c,
	}
}

// SMSTemplatesUpsertBulk is the builder for "upsert"-ing
// a bulk of SMSTemplates nodes.
type SMSTemplatesUpsertBulk struct {
	create *SMSTemplatesCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.SMSTemplates.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(smstemplates.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *SMSTemplatesUpsertBulk) UpdateNewValues() *SMSTemplatesUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(smstemplates.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(smstemplates.FieldCreateTime)
			}
			if _, exists := b.mutation.UpdateTime(); exists {
				s.SetIgnore(smstemplates.FieldUpdateTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.SMSTemplates.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *SMSTemplatesUpsertBulk) Ignore() *SMSTemplatesUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *SMSTemplatesUpsertBulk) DoNothing() *SMSTemplatesUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the SMSTemplatesCreateBulk.OnConflict
// documentation for more info.
func (u *SMSTemplatesUpsertBulk) Update(set func(*SMSTemplatesUpsert)) *SMSTemplatesUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&SMSTemplatesUpsert{UpdateSet: update})
	}))
	return u
}

// SetType sets the "type" field.
func (u *SMSTemplatesUpsertBulk) SetType(v string) *SMSTemplatesUpsertBulk {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.SetType(v)
	})
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *SMSTemplatesUpsertBulk) UpdateType() *SMSTemplatesUpsertBulk {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.UpdateType()
	})
}

// SetRu sets the "ru" field.
func (u *SMSTemplatesUpsertBulk) SetRu(v string) *SMSTemplatesUpsertBulk {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.SetRu(v)
	})
}

// UpdateRu sets the "ru" field to the value that was provided on create.
func (u *SMSTemplatesUpsertBulk) UpdateRu() *SMSTemplatesUpsertBulk {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.UpdateRu()
	})
}

// SetRuLatin sets the "ru_latin" field.
func (u *SMSTemplatesUpsertBulk) SetRuLatin(v string) *SMSTemplatesUpsertBulk {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.SetRuLatin(v)
	})
}

// UpdateRuLatin sets the "ru_latin" field to the value that was provided on create.
func (u *SMSTemplatesUpsertBulk) UpdateRuLatin() *SMSTemplatesUpsertBulk {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.UpdateRuLatin()
	})
}

// SetKz sets the "kz" field.
func (u *SMSTemplatesUpsertBulk) SetKz(v string) *SMSTemplatesUpsertBulk {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.SetKz(v)
	})
}

// UpdateKz sets the "kz" field to the value that was provided on create.
func (u *SMSTemplatesUpsertBulk) UpdateKz() *SMSTemplatesUpsertBulk {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.UpdateKz()
	})
}

// SetKzLatin sets the "kz_latin" field.
func (u *SMSTemplatesUpsertBulk) SetKzLatin(v string) *SMSTemplatesUpsertBulk {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.SetKzLatin(v)
	})
}

// UpdateKzLatin sets the "kz_latin" field to the value that was provided on create.
func (u *SMSTemplatesUpsertBulk) UpdateKzLatin() *SMSTemplatesUpsertBulk {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.UpdateKzLatin()
	})
}

// SetEn sets the "en" field.
func (u *SMSTemplatesUpsertBulk) SetEn(v string) *SMSTemplatesUpsertBulk {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.SetEn(v)
	})
}

// UpdateEn sets the "en" field to the value that was provided on create.
func (u *SMSTemplatesUpsertBulk) UpdateEn() *SMSTemplatesUpsertBulk {
	return u.Update(func(s *SMSTemplatesUpsert) {
		s.UpdateEn()
	})
}

// Exec executes the query.
func (u *SMSTemplatesUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the SMSTemplatesCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for SMSTemplatesCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *SMSTemplatesUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
