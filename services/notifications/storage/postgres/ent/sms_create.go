// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/sms"
)

// SMSCreate is the builder for creating a SMS entity.
type SMSCreate struct {
	config
	mutation *SMSMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (_c *SMSCreate) SetCreateTime(v time.Time) *SMSCreate {
	_c.mutation.SetCreateTime(v)
	return _c
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (_c *SMSCreate) SetNillableCreateTime(v *time.Time) *SMSCreate {
	if v != nil {
		_c.SetCreateTime(*v)
	}
	return _c
}

// SetUpdateTime sets the "update_time" field.
func (_c *SMSCreate) SetUpdateTime(v time.Time) *SMSCreate {
	_c.mutation.SetUpdateTime(v)
	return _c
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (_c *SMSCreate) SetNillableUpdateTime(v *time.Time) *SMSCreate {
	if v != nil {
		_c.SetUpdateTime(*v)
	}
	return _c
}

// SetPhoneNumber sets the "phone_number" field.
func (_c *SMSCreate) SetPhoneNumber(v string) *SMSCreate {
	_c.mutation.SetPhoneNumber(v)
	return _c
}

// SetType sets the "type" field.
func (_c *SMSCreate) SetType(v string) *SMSCreate {
	_c.mutation.SetType(v)
	return _c
}

// SetArgs sets the "args" field.
func (_c *SMSCreate) SetArgs(v map[string]string) *SMSCreate {
	_c.mutation.SetArgs(v)
	return _c
}

// SetText sets the "text" field.
func (_c *SMSCreate) SetText(v string) *SMSCreate {
	_c.mutation.SetText(v)
	return _c
}

// SetMetadata sets the "metadata" field.
func (_c *SMSCreate) SetMetadata(v map[string]string) *SMSCreate {
	_c.mutation.SetMetadata(v)
	return _c
}

// SetID sets the "id" field.
func (_c *SMSCreate) SetID(v int32) *SMSCreate {
	_c.mutation.SetID(v)
	return _c
}

// Mutation returns the SMSMutation object of the builder.
func (_c *SMSCreate) Mutation() *SMSMutation {
	return _c.mutation
}

// Save creates the SMS in the database.
func (_c *SMSCreate) Save(ctx context.Context) (*SMS, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *SMSCreate) SaveX(ctx context.Context) *SMS {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *SMSCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *SMSCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *SMSCreate) defaults() {
	if _, ok := _c.mutation.CreateTime(); !ok {
		v := sms.DefaultCreateTime()
		_c.mutation.SetCreateTime(v)
	}
	if _, ok := _c.mutation.UpdateTime(); !ok {
		v := sms.DefaultUpdateTime()
		_c.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *SMSCreate) check() error {
	if _, ok := _c.mutation.PhoneNumber(); !ok {
		return &ValidationError{Name: "phone_number", err: errors.New(`ent: missing required field "SMS.phone_number"`)}
	}
	if _, ok := _c.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "SMS.type"`)}
	}
	if _, ok := _c.mutation.Text(); !ok {
		return &ValidationError{Name: "text", err: errors.New(`ent: missing required field "SMS.text"`)}
	}
	return nil
}

func (_c *SMSCreate) sqlSave(ctx context.Context) (*SMS, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int32(id)
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *SMSCreate) createSpec() (*SMS, *sqlgraph.CreateSpec) {
	var (
		_node = &SMS{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(sms.Table, sqlgraph.NewFieldSpec(sms.FieldID, field.TypeInt32))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := _c.mutation.CreateTime(); ok {
		_spec.SetField(sms.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = value
	}
	if value, ok := _c.mutation.UpdateTime(); ok {
		_spec.SetField(sms.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = value
	}
	if value, ok := _c.mutation.PhoneNumber(); ok {
		_spec.SetField(sms.FieldPhoneNumber, field.TypeString, value)
		_node.PhoneNumber = value
	}
	if value, ok := _c.mutation.GetType(); ok {
		_spec.SetField(sms.FieldType, field.TypeString, value)
		_node.Type = value
	}
	if value, ok := _c.mutation.Args(); ok {
		_spec.SetField(sms.FieldArgs, field.TypeJSON, value)
		_node.Args = value
	}
	if value, ok := _c.mutation.Text(); ok {
		_spec.SetField(sms.FieldText, field.TypeString, value)
		_node.Text = value
	}
	if value, ok := _c.mutation.Metadata(); ok {
		_spec.SetField(sms.FieldMetadata, field.TypeJSON, value)
		_node.Metadata = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.SMS.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.SMSUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *SMSCreate) OnConflict(opts ...sql.ConflictOption) *SMSUpsertOne {
	_c.conflict = opts
	return &SMSUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.SMS.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *SMSCreate) OnConflictColumns(columns ...string) *SMSUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &SMSUpsertOne{
		create: _c,
	}
}

type (
	// SMSUpsertOne is the builder for "upsert"-ing
	//  one SMS node.
	SMSUpsertOne struct {
		create *SMSCreate
	}

	// SMSUpsert is the "OnConflict" setter.
	SMSUpsert struct {
		*sql.UpdateSet
	}
)

// SetPhoneNumber sets the "phone_number" field.
func (u *SMSUpsert) SetPhoneNumber(v string) *SMSUpsert {
	u.Set(sms.FieldPhoneNumber, v)
	return u
}

// UpdatePhoneNumber sets the "phone_number" field to the value that was provided on create.
func (u *SMSUpsert) UpdatePhoneNumber() *SMSUpsert {
	u.SetExcluded(sms.FieldPhoneNumber)
	return u
}

// SetType sets the "type" field.
func (u *SMSUpsert) SetType(v string) *SMSUpsert {
	u.Set(sms.FieldType, v)
	return u
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *SMSUpsert) UpdateType() *SMSUpsert {
	u.SetExcluded(sms.FieldType)
	return u
}

// SetArgs sets the "args" field.
func (u *SMSUpsert) SetArgs(v map[string]string) *SMSUpsert {
	u.Set(sms.FieldArgs, v)
	return u
}

// UpdateArgs sets the "args" field to the value that was provided on create.
func (u *SMSUpsert) UpdateArgs() *SMSUpsert {
	u.SetExcluded(sms.FieldArgs)
	return u
}

// ClearArgs clears the value of the "args" field.
func (u *SMSUpsert) ClearArgs() *SMSUpsert {
	u.SetNull(sms.FieldArgs)
	return u
}

// SetText sets the "text" field.
func (u *SMSUpsert) SetText(v string) *SMSUpsert {
	u.Set(sms.FieldText, v)
	return u
}

// UpdateText sets the "text" field to the value that was provided on create.
func (u *SMSUpsert) UpdateText() *SMSUpsert {
	u.SetExcluded(sms.FieldText)
	return u
}

// SetMetadata sets the "metadata" field.
func (u *SMSUpsert) SetMetadata(v map[string]string) *SMSUpsert {
	u.Set(sms.FieldMetadata, v)
	return u
}

// UpdateMetadata sets the "metadata" field to the value that was provided on create.
func (u *SMSUpsert) UpdateMetadata() *SMSUpsert {
	u.SetExcluded(sms.FieldMetadata)
	return u
}

// ClearMetadata clears the value of the "metadata" field.
func (u *SMSUpsert) ClearMetadata() *SMSUpsert {
	u.SetNull(sms.FieldMetadata)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.SMS.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(sms.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *SMSUpsertOne) UpdateNewValues() *SMSUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(sms.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(sms.FieldCreateTime)
		}
		if _, exists := u.create.mutation.UpdateTime(); exists {
			s.SetIgnore(sms.FieldUpdateTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.SMS.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *SMSUpsertOne) Ignore() *SMSUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *SMSUpsertOne) DoNothing() *SMSUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the SMSCreate.OnConflict
// documentation for more info.
func (u *SMSUpsertOne) Update(set func(*SMSUpsert)) *SMSUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&SMSUpsert{UpdateSet: update})
	}))
	return u
}

// SetPhoneNumber sets the "phone_number" field.
func (u *SMSUpsertOne) SetPhoneNumber(v string) *SMSUpsertOne {
	return u.Update(func(s *SMSUpsert) {
		s.SetPhoneNumber(v)
	})
}

// UpdatePhoneNumber sets the "phone_number" field to the value that was provided on create.
func (u *SMSUpsertOne) UpdatePhoneNumber() *SMSUpsertOne {
	return u.Update(func(s *SMSUpsert) {
		s.UpdatePhoneNumber()
	})
}

// SetType sets the "type" field.
func (u *SMSUpsertOne) SetType(v string) *SMSUpsertOne {
	return u.Update(func(s *SMSUpsert) {
		s.SetType(v)
	})
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *SMSUpsertOne) UpdateType() *SMSUpsertOne {
	return u.Update(func(s *SMSUpsert) {
		s.UpdateType()
	})
}

// SetArgs sets the "args" field.
func (u *SMSUpsertOne) SetArgs(v map[string]string) *SMSUpsertOne {
	return u.Update(func(s *SMSUpsert) {
		s.SetArgs(v)
	})
}

// UpdateArgs sets the "args" field to the value that was provided on create.
func (u *SMSUpsertOne) UpdateArgs() *SMSUpsertOne {
	return u.Update(func(s *SMSUpsert) {
		s.UpdateArgs()
	})
}

// ClearArgs clears the value of the "args" field.
func (u *SMSUpsertOne) ClearArgs() *SMSUpsertOne {
	return u.Update(func(s *SMSUpsert) {
		s.ClearArgs()
	})
}

// SetText sets the "text" field.
func (u *SMSUpsertOne) SetText(v string) *SMSUpsertOne {
	return u.Update(func(s *SMSUpsert) {
		s.SetText(v)
	})
}

// UpdateText sets the "text" field to the value that was provided on create.
func (u *SMSUpsertOne) UpdateText() *SMSUpsertOne {
	return u.Update(func(s *SMSUpsert) {
		s.UpdateText()
	})
}

// SetMetadata sets the "metadata" field.
func (u *SMSUpsertOne) SetMetadata(v map[string]string) *SMSUpsertOne {
	return u.Update(func(s *SMSUpsert) {
		s.SetMetadata(v)
	})
}

// UpdateMetadata sets the "metadata" field to the value that was provided on create.
func (u *SMSUpsertOne) UpdateMetadata() *SMSUpsertOne {
	return u.Update(func(s *SMSUpsert) {
		s.UpdateMetadata()
	})
}

// ClearMetadata clears the value of the "metadata" field.
func (u *SMSUpsertOne) ClearMetadata() *SMSUpsertOne {
	return u.Update(func(s *SMSUpsert) {
		s.ClearMetadata()
	})
}

// Exec executes the query.
func (u *SMSUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for SMSCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *SMSUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *SMSUpsertOne) ID(ctx context.Context) (id int32, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *SMSUpsertOne) IDX(ctx context.Context) int32 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// SMSCreateBulk is the builder for creating many SMS entities in bulk.
type SMSCreateBulk struct {
	config
	err      error
	builders []*SMSCreate
	conflict []sql.ConflictOption
}

// Save creates the SMS entities in the database.
func (_c *SMSCreateBulk) Save(ctx context.Context) ([]*SMS, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*SMS, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*SMSMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int32(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *SMSCreateBulk) SaveX(ctx context.Context) []*SMS {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *SMSCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *SMSCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.SMS.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.SMSUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *SMSCreateBulk) OnConflict(opts ...sql.ConflictOption) *SMSUpsertBulk {
	_c.conflict = opts
	return &SMSUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.SMS.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *SMSCreateBulk) OnConflictColumns(columns ...string) *SMSUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &SMSUpsertBulk{
		create: _c,
	}
}

// SMSUpsertBulk is the builder for "upsert"-ing
// a bulk of SMS nodes.
type SMSUpsertBulk struct {
	create *SMSCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.SMS.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(sms.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *SMSUpsertBulk) UpdateNewValues() *SMSUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(sms.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(sms.FieldCreateTime)
			}
			if _, exists := b.mutation.UpdateTime(); exists {
				s.SetIgnore(sms.FieldUpdateTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.SMS.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *SMSUpsertBulk) Ignore() *SMSUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *SMSUpsertBulk) DoNothing() *SMSUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the SMSCreateBulk.OnConflict
// documentation for more info.
func (u *SMSUpsertBulk) Update(set func(*SMSUpsert)) *SMSUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&SMSUpsert{UpdateSet: update})
	}))
	return u
}

// SetPhoneNumber sets the "phone_number" field.
func (u *SMSUpsertBulk) SetPhoneNumber(v string) *SMSUpsertBulk {
	return u.Update(func(s *SMSUpsert) {
		s.SetPhoneNumber(v)
	})
}

// UpdatePhoneNumber sets the "phone_number" field to the value that was provided on create.
func (u *SMSUpsertBulk) UpdatePhoneNumber() *SMSUpsertBulk {
	return u.Update(func(s *SMSUpsert) {
		s.UpdatePhoneNumber()
	})
}

// SetType sets the "type" field.
func (u *SMSUpsertBulk) SetType(v string) *SMSUpsertBulk {
	return u.Update(func(s *SMSUpsert) {
		s.SetType(v)
	})
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *SMSUpsertBulk) UpdateType() *SMSUpsertBulk {
	return u.Update(func(s *SMSUpsert) {
		s.UpdateType()
	})
}

// SetArgs sets the "args" field.
func (u *SMSUpsertBulk) SetArgs(v map[string]string) *SMSUpsertBulk {
	return u.Update(func(s *SMSUpsert) {
		s.SetArgs(v)
	})
}

// UpdateArgs sets the "args" field to the value that was provided on create.
func (u *SMSUpsertBulk) UpdateArgs() *SMSUpsertBulk {
	return u.Update(func(s *SMSUpsert) {
		s.UpdateArgs()
	})
}

// ClearArgs clears the value of the "args" field.
func (u *SMSUpsertBulk) ClearArgs() *SMSUpsertBulk {
	return u.Update(func(s *SMSUpsert) {
		s.ClearArgs()
	})
}

// SetText sets the "text" field.
func (u *SMSUpsertBulk) SetText(v string) *SMSUpsertBulk {
	return u.Update(func(s *SMSUpsert) {
		s.SetText(v)
	})
}

// UpdateText sets the "text" field to the value that was provided on create.
func (u *SMSUpsertBulk) UpdateText() *SMSUpsertBulk {
	return u.Update(func(s *SMSUpsert) {
		s.UpdateText()
	})
}

// SetMetadata sets the "metadata" field.
func (u *SMSUpsertBulk) SetMetadata(v map[string]string) *SMSUpsertBulk {
	return u.Update(func(s *SMSUpsert) {
		s.SetMetadata(v)
	})
}

// UpdateMetadata sets the "metadata" field to the value that was provided on create.
func (u *SMSUpsertBulk) UpdateMetadata() *SMSUpsertBulk {
	return u.Update(func(s *SMSUpsert) {
		s.UpdateMetadata()
	})
}

// ClearMetadata clears the value of the "metadata" field.
func (u *SMSUpsertBulk) ClearMetadata() *SMSUpsertBulk {
	return u.Update(func(s *SMSUpsert) {
		s.ClearMetadata()
	})
}

// Exec executes the query.
func (u *SMSUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the SMSCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for SMSCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *SMSUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
