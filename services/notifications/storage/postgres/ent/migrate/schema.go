// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// HealthsColumns holds the columns for the "healths" table.
	HealthsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
	}
	// HealthsTable holds the schema information for the "healths" table.
	HealthsTable = &schema.Table{
		Name:       "healths",
		Columns:    HealthsColumns,
		PrimaryKey: []*schema.Column{HealthsColumns[0]},
	}
	// SmSsColumns holds the columns for the "sm_ss" table.
	SmSsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt32, Increment: true},
		{Name: "create_time", Type: field.TypeTime, Nullable: true},
		{Name: "update_time", Type: field.TypeTime, Nullable: true},
		{Name: "phone_number", Type: field.TypeString},
		{Name: "type", Type: field.TypeString},
		{Name: "args", Type: field.TypeJSON, Nullable: true},
		{Name: "text", Type: field.TypeString},
		{Name: "metadata", Type: field.TypeJSON, Nullable: true},
	}
	// SmSsTable holds the schema information for the "sm_ss" table.
	SmSsTable = &schema.Table{
		Name:       "sm_ss",
		Columns:    SmSsColumns,
		PrimaryKey: []*schema.Column{SmSsColumns[0]},
	}
	// SmsTemplatesColumns holds the columns for the "sms_templates" table.
	SmsTemplatesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "create_time", Type: field.TypeTime, Nullable: true},
		{Name: "update_time", Type: field.TypeTime, Nullable: true},
		{Name: "type", Type: field.TypeString, Unique: true},
		{Name: "ru", Type: field.TypeString},
		{Name: "ru_latin", Type: field.TypeString},
		{Name: "kz", Type: field.TypeString},
		{Name: "kz_latin", Type: field.TypeString},
		{Name: "en", Type: field.TypeString},
	}
	// SmsTemplatesTable holds the schema information for the "sms_templates" table.
	SmsTemplatesTable = &schema.Table{
		Name:       "sms_templates",
		Columns:    SmsTemplatesColumns,
		PrimaryKey: []*schema.Column{SmsTemplatesColumns[0]},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		HealthsTable,
		SmSsTable,
		SmsTemplatesTable,
	}
)

func init() {
}
