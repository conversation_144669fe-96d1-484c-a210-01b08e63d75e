// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/predicate"
	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/sms"
	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/smstemplates"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeHealth       = "Health"
	TypeSMS          = "SMS"
	TypeSMSTemplates = "SMSTemplates"
)

// HealthMutation represents an operation that mutates the Health nodes in the graph.
type HealthMutation struct {
	config
	op            Op
	typ           string
	id            *uuid.UUID
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Health, error)
	predicates    []predicate.Health
}

var _ ent.Mutation = (*HealthMutation)(nil)

// healthOption allows management of the mutation configuration using functional options.
type healthOption func(*HealthMutation)

// newHealthMutation creates new mutation for the Health entity.
func newHealthMutation(c config, op Op, opts ...healthOption) *HealthMutation {
	m := &HealthMutation{
		config:        c,
		op:            op,
		typ:           TypeHealth,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withHealthID sets the ID field of the mutation.
func withHealthID(id uuid.UUID) healthOption {
	return func(m *HealthMutation) {
		var (
			err   error
			once  sync.Once
			value *Health
		)
		m.oldValue = func(ctx context.Context) (*Health, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Health.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withHealth sets the old Health of the mutation.
func withHealth(node *Health) healthOption {
	return func(m *HealthMutation) {
		m.oldValue = func(context.Context) (*Health, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m HealthMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m HealthMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Health entities.
func (m *HealthMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *HealthMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *HealthMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Health.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// Where appends a list predicates to the HealthMutation builder.
func (m *HealthMutation) Where(ps ...predicate.Health) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the HealthMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *HealthMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Health, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *HealthMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *HealthMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Health).
func (m *HealthMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *HealthMutation) Fields() []string {
	fields := make([]string, 0, 0)
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *HealthMutation) Field(name string) (ent.Value, bool) {
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *HealthMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	return nil, fmt.Errorf("unknown Health field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *HealthMutation) SetField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Health field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *HealthMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *HealthMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *HealthMutation) AddField(name string, value ent.Value) error {
	return fmt.Errorf("unknown Health numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *HealthMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *HealthMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *HealthMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Health nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *HealthMutation) ResetField(name string) error {
	return fmt.Errorf("unknown Health field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *HealthMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *HealthMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *HealthMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *HealthMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *HealthMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *HealthMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *HealthMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Health unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *HealthMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Health edge %s", name)
}

// SMSMutation represents an operation that mutates the SMS nodes in the graph.
type SMSMutation struct {
	config
	op            Op
	typ           string
	id            *int32
	create_time   *time.Time
	update_time   *time.Time
	phone_number  *string
	_type         *string
	args          *map[string]string
	text          *string
	metadata      *map[string]string
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*SMS, error)
	predicates    []predicate.SMS
}

var _ ent.Mutation = (*SMSMutation)(nil)

// smsOption allows management of the mutation configuration using functional options.
type smsOption func(*SMSMutation)

// newSMSMutation creates new mutation for the SMS entity.
func newSMSMutation(c config, op Op, opts ...smsOption) *SMSMutation {
	m := &SMSMutation{
		config:        c,
		op:            op,
		typ:           TypeSMS,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withSMSID sets the ID field of the mutation.
func withSMSID(id int32) smsOption {
	return func(m *SMSMutation) {
		var (
			err   error
			once  sync.Once
			value *SMS
		)
		m.oldValue = func(ctx context.Context) (*SMS, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().SMS.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withSMS sets the old SMS of the mutation.
func withSMS(node *SMS) smsOption {
	return func(m *SMSMutation) {
		m.oldValue = func(context.Context) (*SMS, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m SMSMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m SMSMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of SMS entities.
func (m *SMSMutation) SetID(id int32) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *SMSMutation) ID() (id int32, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *SMSMutation) IDs(ctx context.Context) ([]int32, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int32{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().SMS.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreateTime sets the "create_time" field.
func (m *SMSMutation) SetCreateTime(t time.Time) {
	m.create_time = &t
}

// CreateTime returns the value of the "create_time" field in the mutation.
func (m *SMSMutation) CreateTime() (r time.Time, exists bool) {
	v := m.create_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateTime returns the old "create_time" field's value of the SMS entity.
// If the SMS object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SMSMutation) OldCreateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateTime: %w", err)
	}
	return oldValue.CreateTime, nil
}

// ClearCreateTime clears the value of the "create_time" field.
func (m *SMSMutation) ClearCreateTime() {
	m.create_time = nil
	m.clearedFields[sms.FieldCreateTime] = struct{}{}
}

// CreateTimeCleared returns if the "create_time" field was cleared in this mutation.
func (m *SMSMutation) CreateTimeCleared() bool {
	_, ok := m.clearedFields[sms.FieldCreateTime]
	return ok
}

// ResetCreateTime resets all changes to the "create_time" field.
func (m *SMSMutation) ResetCreateTime() {
	m.create_time = nil
	delete(m.clearedFields, sms.FieldCreateTime)
}

// SetUpdateTime sets the "update_time" field.
func (m *SMSMutation) SetUpdateTime(t time.Time) {
	m.update_time = &t
}

// UpdateTime returns the value of the "update_time" field in the mutation.
func (m *SMSMutation) UpdateTime() (r time.Time, exists bool) {
	v := m.update_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateTime returns the old "update_time" field's value of the SMS entity.
// If the SMS object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SMSMutation) OldUpdateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateTime: %w", err)
	}
	return oldValue.UpdateTime, nil
}

// ClearUpdateTime clears the value of the "update_time" field.
func (m *SMSMutation) ClearUpdateTime() {
	m.update_time = nil
	m.clearedFields[sms.FieldUpdateTime] = struct{}{}
}

// UpdateTimeCleared returns if the "update_time" field was cleared in this mutation.
func (m *SMSMutation) UpdateTimeCleared() bool {
	_, ok := m.clearedFields[sms.FieldUpdateTime]
	return ok
}

// ResetUpdateTime resets all changes to the "update_time" field.
func (m *SMSMutation) ResetUpdateTime() {
	m.update_time = nil
	delete(m.clearedFields, sms.FieldUpdateTime)
}

// SetPhoneNumber sets the "phone_number" field.
func (m *SMSMutation) SetPhoneNumber(s string) {
	m.phone_number = &s
}

// PhoneNumber returns the value of the "phone_number" field in the mutation.
func (m *SMSMutation) PhoneNumber() (r string, exists bool) {
	v := m.phone_number
	if v == nil {
		return
	}
	return *v, true
}

// OldPhoneNumber returns the old "phone_number" field's value of the SMS entity.
// If the SMS object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SMSMutation) OldPhoneNumber(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPhoneNumber is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPhoneNumber requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPhoneNumber: %w", err)
	}
	return oldValue.PhoneNumber, nil
}

// ResetPhoneNumber resets all changes to the "phone_number" field.
func (m *SMSMutation) ResetPhoneNumber() {
	m.phone_number = nil
}

// SetType sets the "type" field.
func (m *SMSMutation) SetType(s string) {
	m._type = &s
}

// GetType returns the value of the "type" field in the mutation.
func (m *SMSMutation) GetType() (r string, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the SMS entity.
// If the SMS object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SMSMutation) OldType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *SMSMutation) ResetType() {
	m._type = nil
}

// SetArgs sets the "args" field.
func (m *SMSMutation) SetArgs(value map[string]string) {
	m.args = &value
}

// Args returns the value of the "args" field in the mutation.
func (m *SMSMutation) Args() (r map[string]string, exists bool) {
	v := m.args
	if v == nil {
		return
	}
	return *v, true
}

// OldArgs returns the old "args" field's value of the SMS entity.
// If the SMS object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SMSMutation) OldArgs(ctx context.Context) (v map[string]string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldArgs is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldArgs requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldArgs: %w", err)
	}
	return oldValue.Args, nil
}

// ClearArgs clears the value of the "args" field.
func (m *SMSMutation) ClearArgs() {
	m.args = nil
	m.clearedFields[sms.FieldArgs] = struct{}{}
}

// ArgsCleared returns if the "args" field was cleared in this mutation.
func (m *SMSMutation) ArgsCleared() bool {
	_, ok := m.clearedFields[sms.FieldArgs]
	return ok
}

// ResetArgs resets all changes to the "args" field.
func (m *SMSMutation) ResetArgs() {
	m.args = nil
	delete(m.clearedFields, sms.FieldArgs)
}

// SetText sets the "text" field.
func (m *SMSMutation) SetText(s string) {
	m.text = &s
}

// Text returns the value of the "text" field in the mutation.
func (m *SMSMutation) Text() (r string, exists bool) {
	v := m.text
	if v == nil {
		return
	}
	return *v, true
}

// OldText returns the old "text" field's value of the SMS entity.
// If the SMS object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SMSMutation) OldText(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldText is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldText requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldText: %w", err)
	}
	return oldValue.Text, nil
}

// ResetText resets all changes to the "text" field.
func (m *SMSMutation) ResetText() {
	m.text = nil
}

// SetMetadata sets the "metadata" field.
func (m *SMSMutation) SetMetadata(value map[string]string) {
	m.metadata = &value
}

// Metadata returns the value of the "metadata" field in the mutation.
func (m *SMSMutation) Metadata() (r map[string]string, exists bool) {
	v := m.metadata
	if v == nil {
		return
	}
	return *v, true
}

// OldMetadata returns the old "metadata" field's value of the SMS entity.
// If the SMS object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SMSMutation) OldMetadata(ctx context.Context) (v map[string]string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMetadata is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMetadata requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMetadata: %w", err)
	}
	return oldValue.Metadata, nil
}

// ClearMetadata clears the value of the "metadata" field.
func (m *SMSMutation) ClearMetadata() {
	m.metadata = nil
	m.clearedFields[sms.FieldMetadata] = struct{}{}
}

// MetadataCleared returns if the "metadata" field was cleared in this mutation.
func (m *SMSMutation) MetadataCleared() bool {
	_, ok := m.clearedFields[sms.FieldMetadata]
	return ok
}

// ResetMetadata resets all changes to the "metadata" field.
func (m *SMSMutation) ResetMetadata() {
	m.metadata = nil
	delete(m.clearedFields, sms.FieldMetadata)
}

// Where appends a list predicates to the SMSMutation builder.
func (m *SMSMutation) Where(ps ...predicate.SMS) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the SMSMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *SMSMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.SMS, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *SMSMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *SMSMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (SMS).
func (m *SMSMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *SMSMutation) Fields() []string {
	fields := make([]string, 0, 7)
	if m.create_time != nil {
		fields = append(fields, sms.FieldCreateTime)
	}
	if m.update_time != nil {
		fields = append(fields, sms.FieldUpdateTime)
	}
	if m.phone_number != nil {
		fields = append(fields, sms.FieldPhoneNumber)
	}
	if m._type != nil {
		fields = append(fields, sms.FieldType)
	}
	if m.args != nil {
		fields = append(fields, sms.FieldArgs)
	}
	if m.text != nil {
		fields = append(fields, sms.FieldText)
	}
	if m.metadata != nil {
		fields = append(fields, sms.FieldMetadata)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *SMSMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case sms.FieldCreateTime:
		return m.CreateTime()
	case sms.FieldUpdateTime:
		return m.UpdateTime()
	case sms.FieldPhoneNumber:
		return m.PhoneNumber()
	case sms.FieldType:
		return m.GetType()
	case sms.FieldArgs:
		return m.Args()
	case sms.FieldText:
		return m.Text()
	case sms.FieldMetadata:
		return m.Metadata()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *SMSMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case sms.FieldCreateTime:
		return m.OldCreateTime(ctx)
	case sms.FieldUpdateTime:
		return m.OldUpdateTime(ctx)
	case sms.FieldPhoneNumber:
		return m.OldPhoneNumber(ctx)
	case sms.FieldType:
		return m.OldType(ctx)
	case sms.FieldArgs:
		return m.OldArgs(ctx)
	case sms.FieldText:
		return m.OldText(ctx)
	case sms.FieldMetadata:
		return m.OldMetadata(ctx)
	}
	return nil, fmt.Errorf("unknown SMS field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *SMSMutation) SetField(name string, value ent.Value) error {
	switch name {
	case sms.FieldCreateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateTime(v)
		return nil
	case sms.FieldUpdateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateTime(v)
		return nil
	case sms.FieldPhoneNumber:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPhoneNumber(v)
		return nil
	case sms.FieldType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case sms.FieldArgs:
		v, ok := value.(map[string]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetArgs(v)
		return nil
	case sms.FieldText:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetText(v)
		return nil
	case sms.FieldMetadata:
		v, ok := value.(map[string]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMetadata(v)
		return nil
	}
	return fmt.Errorf("unknown SMS field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *SMSMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *SMSMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *SMSMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown SMS numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *SMSMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(sms.FieldCreateTime) {
		fields = append(fields, sms.FieldCreateTime)
	}
	if m.FieldCleared(sms.FieldUpdateTime) {
		fields = append(fields, sms.FieldUpdateTime)
	}
	if m.FieldCleared(sms.FieldArgs) {
		fields = append(fields, sms.FieldArgs)
	}
	if m.FieldCleared(sms.FieldMetadata) {
		fields = append(fields, sms.FieldMetadata)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *SMSMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *SMSMutation) ClearField(name string) error {
	switch name {
	case sms.FieldCreateTime:
		m.ClearCreateTime()
		return nil
	case sms.FieldUpdateTime:
		m.ClearUpdateTime()
		return nil
	case sms.FieldArgs:
		m.ClearArgs()
		return nil
	case sms.FieldMetadata:
		m.ClearMetadata()
		return nil
	}
	return fmt.Errorf("unknown SMS nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *SMSMutation) ResetField(name string) error {
	switch name {
	case sms.FieldCreateTime:
		m.ResetCreateTime()
		return nil
	case sms.FieldUpdateTime:
		m.ResetUpdateTime()
		return nil
	case sms.FieldPhoneNumber:
		m.ResetPhoneNumber()
		return nil
	case sms.FieldType:
		m.ResetType()
		return nil
	case sms.FieldArgs:
		m.ResetArgs()
		return nil
	case sms.FieldText:
		m.ResetText()
		return nil
	case sms.FieldMetadata:
		m.ResetMetadata()
		return nil
	}
	return fmt.Errorf("unknown SMS field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *SMSMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *SMSMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *SMSMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *SMSMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *SMSMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *SMSMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *SMSMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown SMS unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *SMSMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown SMS edge %s", name)
}

// SMSTemplatesMutation represents an operation that mutates the SMSTemplates nodes in the graph.
type SMSTemplatesMutation struct {
	config
	op            Op
	typ           string
	id            *uuid.UUID
	create_time   *time.Time
	update_time   *time.Time
	_type         *string
	ru            *string
	ru_latin      *string
	kz            *string
	kz_latin      *string
	en            *string
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*SMSTemplates, error)
	predicates    []predicate.SMSTemplates
}

var _ ent.Mutation = (*SMSTemplatesMutation)(nil)

// smstemplatesOption allows management of the mutation configuration using functional options.
type smstemplatesOption func(*SMSTemplatesMutation)

// newSMSTemplatesMutation creates new mutation for the SMSTemplates entity.
func newSMSTemplatesMutation(c config, op Op, opts ...smstemplatesOption) *SMSTemplatesMutation {
	m := &SMSTemplatesMutation{
		config:        c,
		op:            op,
		typ:           TypeSMSTemplates,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withSMSTemplatesID sets the ID field of the mutation.
func withSMSTemplatesID(id uuid.UUID) smstemplatesOption {
	return func(m *SMSTemplatesMutation) {
		var (
			err   error
			once  sync.Once
			value *SMSTemplates
		)
		m.oldValue = func(ctx context.Context) (*SMSTemplates, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().SMSTemplates.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withSMSTemplates sets the old SMSTemplates of the mutation.
func withSMSTemplates(node *SMSTemplates) smstemplatesOption {
	return func(m *SMSTemplatesMutation) {
		m.oldValue = func(context.Context) (*SMSTemplates, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m SMSTemplatesMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m SMSTemplatesMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of SMSTemplates entities.
func (m *SMSTemplatesMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *SMSTemplatesMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *SMSTemplatesMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().SMSTemplates.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreateTime sets the "create_time" field.
func (m *SMSTemplatesMutation) SetCreateTime(t time.Time) {
	m.create_time = &t
}

// CreateTime returns the value of the "create_time" field in the mutation.
func (m *SMSTemplatesMutation) CreateTime() (r time.Time, exists bool) {
	v := m.create_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateTime returns the old "create_time" field's value of the SMSTemplates entity.
// If the SMSTemplates object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SMSTemplatesMutation) OldCreateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateTime: %w", err)
	}
	return oldValue.CreateTime, nil
}

// ClearCreateTime clears the value of the "create_time" field.
func (m *SMSTemplatesMutation) ClearCreateTime() {
	m.create_time = nil
	m.clearedFields[smstemplates.FieldCreateTime] = struct{}{}
}

// CreateTimeCleared returns if the "create_time" field was cleared in this mutation.
func (m *SMSTemplatesMutation) CreateTimeCleared() bool {
	_, ok := m.clearedFields[smstemplates.FieldCreateTime]
	return ok
}

// ResetCreateTime resets all changes to the "create_time" field.
func (m *SMSTemplatesMutation) ResetCreateTime() {
	m.create_time = nil
	delete(m.clearedFields, smstemplates.FieldCreateTime)
}

// SetUpdateTime sets the "update_time" field.
func (m *SMSTemplatesMutation) SetUpdateTime(t time.Time) {
	m.update_time = &t
}

// UpdateTime returns the value of the "update_time" field in the mutation.
func (m *SMSTemplatesMutation) UpdateTime() (r time.Time, exists bool) {
	v := m.update_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateTime returns the old "update_time" field's value of the SMSTemplates entity.
// If the SMSTemplates object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SMSTemplatesMutation) OldUpdateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateTime: %w", err)
	}
	return oldValue.UpdateTime, nil
}

// ClearUpdateTime clears the value of the "update_time" field.
func (m *SMSTemplatesMutation) ClearUpdateTime() {
	m.update_time = nil
	m.clearedFields[smstemplates.FieldUpdateTime] = struct{}{}
}

// UpdateTimeCleared returns if the "update_time" field was cleared in this mutation.
func (m *SMSTemplatesMutation) UpdateTimeCleared() bool {
	_, ok := m.clearedFields[smstemplates.FieldUpdateTime]
	return ok
}

// ResetUpdateTime resets all changes to the "update_time" field.
func (m *SMSTemplatesMutation) ResetUpdateTime() {
	m.update_time = nil
	delete(m.clearedFields, smstemplates.FieldUpdateTime)
}

// SetType sets the "type" field.
func (m *SMSTemplatesMutation) SetType(s string) {
	m._type = &s
}

// GetType returns the value of the "type" field in the mutation.
func (m *SMSTemplatesMutation) GetType() (r string, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the SMSTemplates entity.
// If the SMSTemplates object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SMSTemplatesMutation) OldType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *SMSTemplatesMutation) ResetType() {
	m._type = nil
}

// SetRu sets the "ru" field.
func (m *SMSTemplatesMutation) SetRu(s string) {
	m.ru = &s
}

// Ru returns the value of the "ru" field in the mutation.
func (m *SMSTemplatesMutation) Ru() (r string, exists bool) {
	v := m.ru
	if v == nil {
		return
	}
	return *v, true
}

// OldRu returns the old "ru" field's value of the SMSTemplates entity.
// If the SMSTemplates object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SMSTemplatesMutation) OldRu(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRu is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRu requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRu: %w", err)
	}
	return oldValue.Ru, nil
}

// ResetRu resets all changes to the "ru" field.
func (m *SMSTemplatesMutation) ResetRu() {
	m.ru = nil
}

// SetRuLatin sets the "ru_latin" field.
func (m *SMSTemplatesMutation) SetRuLatin(s string) {
	m.ru_latin = &s
}

// RuLatin returns the value of the "ru_latin" field in the mutation.
func (m *SMSTemplatesMutation) RuLatin() (r string, exists bool) {
	v := m.ru_latin
	if v == nil {
		return
	}
	return *v, true
}

// OldRuLatin returns the old "ru_latin" field's value of the SMSTemplates entity.
// If the SMSTemplates object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SMSTemplatesMutation) OldRuLatin(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRuLatin is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRuLatin requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRuLatin: %w", err)
	}
	return oldValue.RuLatin, nil
}

// ResetRuLatin resets all changes to the "ru_latin" field.
func (m *SMSTemplatesMutation) ResetRuLatin() {
	m.ru_latin = nil
}

// SetKz sets the "kz" field.
func (m *SMSTemplatesMutation) SetKz(s string) {
	m.kz = &s
}

// Kz returns the value of the "kz" field in the mutation.
func (m *SMSTemplatesMutation) Kz() (r string, exists bool) {
	v := m.kz
	if v == nil {
		return
	}
	return *v, true
}

// OldKz returns the old "kz" field's value of the SMSTemplates entity.
// If the SMSTemplates object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SMSTemplatesMutation) OldKz(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldKz is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldKz requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldKz: %w", err)
	}
	return oldValue.Kz, nil
}

// ResetKz resets all changes to the "kz" field.
func (m *SMSTemplatesMutation) ResetKz() {
	m.kz = nil
}

// SetKzLatin sets the "kz_latin" field.
func (m *SMSTemplatesMutation) SetKzLatin(s string) {
	m.kz_latin = &s
}

// KzLatin returns the value of the "kz_latin" field in the mutation.
func (m *SMSTemplatesMutation) KzLatin() (r string, exists bool) {
	v := m.kz_latin
	if v == nil {
		return
	}
	return *v, true
}

// OldKzLatin returns the old "kz_latin" field's value of the SMSTemplates entity.
// If the SMSTemplates object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SMSTemplatesMutation) OldKzLatin(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldKzLatin is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldKzLatin requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldKzLatin: %w", err)
	}
	return oldValue.KzLatin, nil
}

// ResetKzLatin resets all changes to the "kz_latin" field.
func (m *SMSTemplatesMutation) ResetKzLatin() {
	m.kz_latin = nil
}

// SetEn sets the "en" field.
func (m *SMSTemplatesMutation) SetEn(s string) {
	m.en = &s
}

// En returns the value of the "en" field in the mutation.
func (m *SMSTemplatesMutation) En() (r string, exists bool) {
	v := m.en
	if v == nil {
		return
	}
	return *v, true
}

// OldEn returns the old "en" field's value of the SMSTemplates entity.
// If the SMSTemplates object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SMSTemplatesMutation) OldEn(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEn is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEn requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEn: %w", err)
	}
	return oldValue.En, nil
}

// ResetEn resets all changes to the "en" field.
func (m *SMSTemplatesMutation) ResetEn() {
	m.en = nil
}

// Where appends a list predicates to the SMSTemplatesMutation builder.
func (m *SMSTemplatesMutation) Where(ps ...predicate.SMSTemplates) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the SMSTemplatesMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *SMSTemplatesMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.SMSTemplates, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *SMSTemplatesMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *SMSTemplatesMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (SMSTemplates).
func (m *SMSTemplatesMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *SMSTemplatesMutation) Fields() []string {
	fields := make([]string, 0, 8)
	if m.create_time != nil {
		fields = append(fields, smstemplates.FieldCreateTime)
	}
	if m.update_time != nil {
		fields = append(fields, smstemplates.FieldUpdateTime)
	}
	if m._type != nil {
		fields = append(fields, smstemplates.FieldType)
	}
	if m.ru != nil {
		fields = append(fields, smstemplates.FieldRu)
	}
	if m.ru_latin != nil {
		fields = append(fields, smstemplates.FieldRuLatin)
	}
	if m.kz != nil {
		fields = append(fields, smstemplates.FieldKz)
	}
	if m.kz_latin != nil {
		fields = append(fields, smstemplates.FieldKzLatin)
	}
	if m.en != nil {
		fields = append(fields, smstemplates.FieldEn)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *SMSTemplatesMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case smstemplates.FieldCreateTime:
		return m.CreateTime()
	case smstemplates.FieldUpdateTime:
		return m.UpdateTime()
	case smstemplates.FieldType:
		return m.GetType()
	case smstemplates.FieldRu:
		return m.Ru()
	case smstemplates.FieldRuLatin:
		return m.RuLatin()
	case smstemplates.FieldKz:
		return m.Kz()
	case smstemplates.FieldKzLatin:
		return m.KzLatin()
	case smstemplates.FieldEn:
		return m.En()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *SMSTemplatesMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case smstemplates.FieldCreateTime:
		return m.OldCreateTime(ctx)
	case smstemplates.FieldUpdateTime:
		return m.OldUpdateTime(ctx)
	case smstemplates.FieldType:
		return m.OldType(ctx)
	case smstemplates.FieldRu:
		return m.OldRu(ctx)
	case smstemplates.FieldRuLatin:
		return m.OldRuLatin(ctx)
	case smstemplates.FieldKz:
		return m.OldKz(ctx)
	case smstemplates.FieldKzLatin:
		return m.OldKzLatin(ctx)
	case smstemplates.FieldEn:
		return m.OldEn(ctx)
	}
	return nil, fmt.Errorf("unknown SMSTemplates field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *SMSTemplatesMutation) SetField(name string, value ent.Value) error {
	switch name {
	case smstemplates.FieldCreateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateTime(v)
		return nil
	case smstemplates.FieldUpdateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateTime(v)
		return nil
	case smstemplates.FieldType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case smstemplates.FieldRu:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRu(v)
		return nil
	case smstemplates.FieldRuLatin:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRuLatin(v)
		return nil
	case smstemplates.FieldKz:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetKz(v)
		return nil
	case smstemplates.FieldKzLatin:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetKzLatin(v)
		return nil
	case smstemplates.FieldEn:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEn(v)
		return nil
	}
	return fmt.Errorf("unknown SMSTemplates field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *SMSTemplatesMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *SMSTemplatesMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *SMSTemplatesMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown SMSTemplates numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *SMSTemplatesMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(smstemplates.FieldCreateTime) {
		fields = append(fields, smstemplates.FieldCreateTime)
	}
	if m.FieldCleared(smstemplates.FieldUpdateTime) {
		fields = append(fields, smstemplates.FieldUpdateTime)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *SMSTemplatesMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *SMSTemplatesMutation) ClearField(name string) error {
	switch name {
	case smstemplates.FieldCreateTime:
		m.ClearCreateTime()
		return nil
	case smstemplates.FieldUpdateTime:
		m.ClearUpdateTime()
		return nil
	}
	return fmt.Errorf("unknown SMSTemplates nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *SMSTemplatesMutation) ResetField(name string) error {
	switch name {
	case smstemplates.FieldCreateTime:
		m.ResetCreateTime()
		return nil
	case smstemplates.FieldUpdateTime:
		m.ResetUpdateTime()
		return nil
	case smstemplates.FieldType:
		m.ResetType()
		return nil
	case smstemplates.FieldRu:
		m.ResetRu()
		return nil
	case smstemplates.FieldRuLatin:
		m.ResetRuLatin()
		return nil
	case smstemplates.FieldKz:
		m.ResetKz()
		return nil
	case smstemplates.FieldKzLatin:
		m.ResetKzLatin()
		return nil
	case smstemplates.FieldEn:
		m.ResetEn()
		return nil
	}
	return fmt.Errorf("unknown SMSTemplates field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *SMSTemplatesMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *SMSTemplatesMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *SMSTemplatesMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *SMSTemplatesMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *SMSTemplatesMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *SMSTemplatesMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *SMSTemplatesMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown SMSTemplates unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *SMSTemplatesMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown SMSTemplates edge %s", name)
}
