// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/predicate"
	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/smstemplates"
)

// SMSTemplatesUpdate is the builder for updating SMSTemplates entities.
type SMSTemplatesUpdate struct {
	config
	hooks     []Hook
	mutation  *SMSTemplatesMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the SMSTemplatesUpdate builder.
func (_u *SMSTemplatesUpdate) Where(ps ...predicate.SMSTemplates) *SMSTemplatesUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetType sets the "type" field.
func (_u *SMSTemplatesUpdate) SetType(v string) *SMSTemplatesUpdate {
	_u.mutation.SetType(v)
	return _u
}

// SetNillableType sets the "type" field if the given value is not nil.
func (_u *SMSTemplatesUpdate) SetNillableType(v *string) *SMSTemplatesUpdate {
	if v != nil {
		_u.SetType(*v)
	}
	return _u
}

// SetRu sets the "ru" field.
func (_u *SMSTemplatesUpdate) SetRu(v string) *SMSTemplatesUpdate {
	_u.mutation.SetRu(v)
	return _u
}

// SetNillableRu sets the "ru" field if the given value is not nil.
func (_u *SMSTemplatesUpdate) SetNillableRu(v *string) *SMSTemplatesUpdate {
	if v != nil {
		_u.SetRu(*v)
	}
	return _u
}

// SetRuLatin sets the "ru_latin" field.
func (_u *SMSTemplatesUpdate) SetRuLatin(v string) *SMSTemplatesUpdate {
	_u.mutation.SetRuLatin(v)
	return _u
}

// SetNillableRuLatin sets the "ru_latin" field if the given value is not nil.
func (_u *SMSTemplatesUpdate) SetNillableRuLatin(v *string) *SMSTemplatesUpdate {
	if v != nil {
		_u.SetRuLatin(*v)
	}
	return _u
}

// SetKz sets the "kz" field.
func (_u *SMSTemplatesUpdate) SetKz(v string) *SMSTemplatesUpdate {
	_u.mutation.SetKz(v)
	return _u
}

// SetNillableKz sets the "kz" field if the given value is not nil.
func (_u *SMSTemplatesUpdate) SetNillableKz(v *string) *SMSTemplatesUpdate {
	if v != nil {
		_u.SetKz(*v)
	}
	return _u
}

// SetKzLatin sets the "kz_latin" field.
func (_u *SMSTemplatesUpdate) SetKzLatin(v string) *SMSTemplatesUpdate {
	_u.mutation.SetKzLatin(v)
	return _u
}

// SetNillableKzLatin sets the "kz_latin" field if the given value is not nil.
func (_u *SMSTemplatesUpdate) SetNillableKzLatin(v *string) *SMSTemplatesUpdate {
	if v != nil {
		_u.SetKzLatin(*v)
	}
	return _u
}

// SetEn sets the "en" field.
func (_u *SMSTemplatesUpdate) SetEn(v string) *SMSTemplatesUpdate {
	_u.mutation.SetEn(v)
	return _u
}

// SetNillableEn sets the "en" field if the given value is not nil.
func (_u *SMSTemplatesUpdate) SetNillableEn(v *string) *SMSTemplatesUpdate {
	if v != nil {
		_u.SetEn(*v)
	}
	return _u
}

// Mutation returns the SMSTemplatesMutation object of the builder.
func (_u *SMSTemplatesUpdate) Mutation() *SMSTemplatesMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *SMSTemplatesUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *SMSTemplatesUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *SMSTemplatesUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *SMSTemplatesUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *SMSTemplatesUpdate) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := smstemplates.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *SMSTemplatesUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *SMSTemplatesUpdate {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *SMSTemplatesUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	_spec := sqlgraph.NewUpdateSpec(smstemplates.Table, smstemplates.Columns, sqlgraph.NewFieldSpec(smstemplates.FieldID, field.TypeUUID))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(smstemplates.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(smstemplates.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(smstemplates.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.GetType(); ok {
		_spec.SetField(smstemplates.FieldType, field.TypeString, value)
	}
	if value, ok := _u.mutation.Ru(); ok {
		_spec.SetField(smstemplates.FieldRu, field.TypeString, value)
	}
	if value, ok := _u.mutation.RuLatin(); ok {
		_spec.SetField(smstemplates.FieldRuLatin, field.TypeString, value)
	}
	if value, ok := _u.mutation.Kz(); ok {
		_spec.SetField(smstemplates.FieldKz, field.TypeString, value)
	}
	if value, ok := _u.mutation.KzLatin(); ok {
		_spec.SetField(smstemplates.FieldKzLatin, field.TypeString, value)
	}
	if value, ok := _u.mutation.En(); ok {
		_spec.SetField(smstemplates.FieldEn, field.TypeString, value)
	}
	_spec.AddModifiers(_u.modifiers...)
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{smstemplates.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// SMSTemplatesUpdateOne is the builder for updating a single SMSTemplates entity.
type SMSTemplatesUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *SMSTemplatesMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetType sets the "type" field.
func (_u *SMSTemplatesUpdateOne) SetType(v string) *SMSTemplatesUpdateOne {
	_u.mutation.SetType(v)
	return _u
}

// SetNillableType sets the "type" field if the given value is not nil.
func (_u *SMSTemplatesUpdateOne) SetNillableType(v *string) *SMSTemplatesUpdateOne {
	if v != nil {
		_u.SetType(*v)
	}
	return _u
}

// SetRu sets the "ru" field.
func (_u *SMSTemplatesUpdateOne) SetRu(v string) *SMSTemplatesUpdateOne {
	_u.mutation.SetRu(v)
	return _u
}

// SetNillableRu sets the "ru" field if the given value is not nil.
func (_u *SMSTemplatesUpdateOne) SetNillableRu(v *string) *SMSTemplatesUpdateOne {
	if v != nil {
		_u.SetRu(*v)
	}
	return _u
}

// SetRuLatin sets the "ru_latin" field.
func (_u *SMSTemplatesUpdateOne) SetRuLatin(v string) *SMSTemplatesUpdateOne {
	_u.mutation.SetRuLatin(v)
	return _u
}

// SetNillableRuLatin sets the "ru_latin" field if the given value is not nil.
func (_u *SMSTemplatesUpdateOne) SetNillableRuLatin(v *string) *SMSTemplatesUpdateOne {
	if v != nil {
		_u.SetRuLatin(*v)
	}
	return _u
}

// SetKz sets the "kz" field.
func (_u *SMSTemplatesUpdateOne) SetKz(v string) *SMSTemplatesUpdateOne {
	_u.mutation.SetKz(v)
	return _u
}

// SetNillableKz sets the "kz" field if the given value is not nil.
func (_u *SMSTemplatesUpdateOne) SetNillableKz(v *string) *SMSTemplatesUpdateOne {
	if v != nil {
		_u.SetKz(*v)
	}
	return _u
}

// SetKzLatin sets the "kz_latin" field.
func (_u *SMSTemplatesUpdateOne) SetKzLatin(v string) *SMSTemplatesUpdateOne {
	_u.mutation.SetKzLatin(v)
	return _u
}

// SetNillableKzLatin sets the "kz_latin" field if the given value is not nil.
func (_u *SMSTemplatesUpdateOne) SetNillableKzLatin(v *string) *SMSTemplatesUpdateOne {
	if v != nil {
		_u.SetKzLatin(*v)
	}
	return _u
}

// SetEn sets the "en" field.
func (_u *SMSTemplatesUpdateOne) SetEn(v string) *SMSTemplatesUpdateOne {
	_u.mutation.SetEn(v)
	return _u
}

// SetNillableEn sets the "en" field if the given value is not nil.
func (_u *SMSTemplatesUpdateOne) SetNillableEn(v *string) *SMSTemplatesUpdateOne {
	if v != nil {
		_u.SetEn(*v)
	}
	return _u
}

// Mutation returns the SMSTemplatesMutation object of the builder.
func (_u *SMSTemplatesUpdateOne) Mutation() *SMSTemplatesMutation {
	return _u.mutation
}

// Where appends a list predicates to the SMSTemplatesUpdate builder.
func (_u *SMSTemplatesUpdateOne) Where(ps ...predicate.SMSTemplates) *SMSTemplatesUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *SMSTemplatesUpdateOne) Select(field string, fields ...string) *SMSTemplatesUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated SMSTemplates entity.
func (_u *SMSTemplatesUpdateOne) Save(ctx context.Context) (*SMSTemplates, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *SMSTemplatesUpdateOne) SaveX(ctx context.Context) *SMSTemplates {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *SMSTemplatesUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *SMSTemplatesUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *SMSTemplatesUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := smstemplates.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *SMSTemplatesUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *SMSTemplatesUpdateOne {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *SMSTemplatesUpdateOne) sqlSave(ctx context.Context) (_node *SMSTemplates, err error) {
	_spec := sqlgraph.NewUpdateSpec(smstemplates.Table, smstemplates.Columns, sqlgraph.NewFieldSpec(smstemplates.FieldID, field.TypeUUID))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "SMSTemplates.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, smstemplates.FieldID)
		for _, f := range fields {
			if !smstemplates.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != smstemplates.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(smstemplates.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(smstemplates.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(smstemplates.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.GetType(); ok {
		_spec.SetField(smstemplates.FieldType, field.TypeString, value)
	}
	if value, ok := _u.mutation.Ru(); ok {
		_spec.SetField(smstemplates.FieldRu, field.TypeString, value)
	}
	if value, ok := _u.mutation.RuLatin(); ok {
		_spec.SetField(smstemplates.FieldRuLatin, field.TypeString, value)
	}
	if value, ok := _u.mutation.Kz(); ok {
		_spec.SetField(smstemplates.FieldKz, field.TypeString, value)
	}
	if value, ok := _u.mutation.KzLatin(); ok {
		_spec.SetField(smstemplates.FieldKzLatin, field.TypeString, value)
	}
	if value, ok := _u.mutation.En(); ok {
		_spec.SetField(smstemplates.FieldEn, field.TypeString, value)
	}
	_spec.AddModifiers(_u.modifiers...)
	_node = &SMSTemplates{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{smstemplates.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
