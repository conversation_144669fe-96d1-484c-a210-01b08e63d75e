// Code generated by ent, DO NOT EDIT.

package smstemplates

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the smstemplates type in the database.
	Label = "sms_templates"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreateTime holds the string denoting the create_time field in the database.
	FieldCreateTime = "create_time"
	// FieldUpdateTime holds the string denoting the update_time field in the database.
	FieldUpdateTime = "update_time"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldRu holds the string denoting the ru field in the database.
	FieldRu = "ru"
	// FieldRuLatin holds the string denoting the ru_latin field in the database.
	FieldRuLatin = "ru_latin"
	// FieldKz holds the string denoting the kz field in the database.
	FieldKz = "kz"
	// FieldKzLatin holds the string denoting the kz_latin field in the database.
	FieldKzLatin = "kz_latin"
	// FieldEn holds the string denoting the en field in the database.
	FieldEn = "en"
	// Table holds the table name of the smstemplates in the database.
	Table = "sms_templates"
)

// Columns holds all SQL columns for smstemplates fields.
var Columns = []string{
	FieldID,
	FieldCreateTime,
	FieldUpdateTime,
	FieldType,
	FieldRu,
	FieldRuLatin,
	FieldKz,
	FieldKzLatin,
	FieldEn,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreateTime holds the default value on creation for the "create_time" field.
	DefaultCreateTime func() time.Time
	// DefaultUpdateTime holds the default value on creation for the "update_time" field.
	DefaultUpdateTime func() time.Time
	// UpdateDefaultUpdateTime holds the default value on update for the "update_time" field.
	UpdateDefaultUpdateTime func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// OrderOption defines the ordering options for the SMSTemplates queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreateTime orders the results by the create_time field.
func ByCreateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateTime, opts...).ToFunc()
}

// ByUpdateTime orders the results by the update_time field.
func ByUpdateTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdateTime, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByRu orders the results by the ru field.
func ByRu(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRu, opts...).ToFunc()
}

// ByRuLatin orders the results by the ru_latin field.
func ByRuLatin(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRuLatin, opts...).ToFunc()
}

// ByKz orders the results by the kz field.
func ByKz(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldKz, opts...).ToFunc()
}

// ByKzLatin orders the results by the kz_latin field.
func ByKzLatin(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldKzLatin, opts...).ToFunc()
}

// ByEn orders the results by the en field.
func ByEn(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEn, opts...).ToFunc()
}
