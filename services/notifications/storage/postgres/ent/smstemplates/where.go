// Code generated by ent, DO NOT EDIT.

package smstemplates

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEQ(FieldUpdateTime, v))
}

// Type applies equality check predicate on the "type" field. It's identical to TypeEQ.
func Type(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEQ(FieldType, v))
}

// Ru applies equality check predicate on the "ru" field. It's identical to RuEQ.
func Ru(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEQ(FieldRu, v))
}

// RuLatin applies equality check predicate on the "ru_latin" field. It's identical to RuLatinEQ.
func RuLatin(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEQ(FieldRuLatin, v))
}

// Kz applies equality check predicate on the "kz" field. It's identical to KzEQ.
func Kz(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEQ(FieldKz, v))
}

// KzLatin applies equality check predicate on the "kz_latin" field. It's identical to KzLatinEQ.
func KzLatin(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEQ(FieldKzLatin, v))
}

// En applies equality check predicate on the "en" field. It's identical to EnEQ.
func En(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEQ(FieldEn, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldLTE(FieldCreateTime, v))
}

// CreateTimeIsNil applies the IsNil predicate on the "create_time" field.
func CreateTimeIsNil() predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldIsNull(FieldCreateTime))
}

// CreateTimeNotNil applies the NotNil predicate on the "create_time" field.
func CreateTimeNotNil() predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldNotNull(FieldCreateTime))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldLTE(FieldUpdateTime, v))
}

// UpdateTimeIsNil applies the IsNil predicate on the "update_time" field.
func UpdateTimeIsNil() predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldIsNull(FieldUpdateTime))
}

// UpdateTimeNotNil applies the NotNil predicate on the "update_time" field.
func UpdateTimeNotNil() predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldNotNull(FieldUpdateTime))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldNotIn(FieldType, vs...))
}

// TypeGT applies the GT predicate on the "type" field.
func TypeGT(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldGT(FieldType, v))
}

// TypeGTE applies the GTE predicate on the "type" field.
func TypeGTE(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldGTE(FieldType, v))
}

// TypeLT applies the LT predicate on the "type" field.
func TypeLT(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldLT(FieldType, v))
}

// TypeLTE applies the LTE predicate on the "type" field.
func TypeLTE(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldLTE(FieldType, v))
}

// TypeContains applies the Contains predicate on the "type" field.
func TypeContains(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldContains(FieldType, v))
}

// TypeHasPrefix applies the HasPrefix predicate on the "type" field.
func TypeHasPrefix(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldHasPrefix(FieldType, v))
}

// TypeHasSuffix applies the HasSuffix predicate on the "type" field.
func TypeHasSuffix(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldHasSuffix(FieldType, v))
}

// TypeEqualFold applies the EqualFold predicate on the "type" field.
func TypeEqualFold(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEqualFold(FieldType, v))
}

// TypeContainsFold applies the ContainsFold predicate on the "type" field.
func TypeContainsFold(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldContainsFold(FieldType, v))
}

// RuEQ applies the EQ predicate on the "ru" field.
func RuEQ(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEQ(FieldRu, v))
}

// RuNEQ applies the NEQ predicate on the "ru" field.
func RuNEQ(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldNEQ(FieldRu, v))
}

// RuIn applies the In predicate on the "ru" field.
func RuIn(vs ...string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldIn(FieldRu, vs...))
}

// RuNotIn applies the NotIn predicate on the "ru" field.
func RuNotIn(vs ...string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldNotIn(FieldRu, vs...))
}

// RuGT applies the GT predicate on the "ru" field.
func RuGT(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldGT(FieldRu, v))
}

// RuGTE applies the GTE predicate on the "ru" field.
func RuGTE(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldGTE(FieldRu, v))
}

// RuLT applies the LT predicate on the "ru" field.
func RuLT(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldLT(FieldRu, v))
}

// RuLTE applies the LTE predicate on the "ru" field.
func RuLTE(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldLTE(FieldRu, v))
}

// RuContains applies the Contains predicate on the "ru" field.
func RuContains(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldContains(FieldRu, v))
}

// RuHasPrefix applies the HasPrefix predicate on the "ru" field.
func RuHasPrefix(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldHasPrefix(FieldRu, v))
}

// RuHasSuffix applies the HasSuffix predicate on the "ru" field.
func RuHasSuffix(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldHasSuffix(FieldRu, v))
}

// RuEqualFold applies the EqualFold predicate on the "ru" field.
func RuEqualFold(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEqualFold(FieldRu, v))
}

// RuContainsFold applies the ContainsFold predicate on the "ru" field.
func RuContainsFold(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldContainsFold(FieldRu, v))
}

// RuLatinEQ applies the EQ predicate on the "ru_latin" field.
func RuLatinEQ(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEQ(FieldRuLatin, v))
}

// RuLatinNEQ applies the NEQ predicate on the "ru_latin" field.
func RuLatinNEQ(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldNEQ(FieldRuLatin, v))
}

// RuLatinIn applies the In predicate on the "ru_latin" field.
func RuLatinIn(vs ...string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldIn(FieldRuLatin, vs...))
}

// RuLatinNotIn applies the NotIn predicate on the "ru_latin" field.
func RuLatinNotIn(vs ...string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldNotIn(FieldRuLatin, vs...))
}

// RuLatinGT applies the GT predicate on the "ru_latin" field.
func RuLatinGT(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldGT(FieldRuLatin, v))
}

// RuLatinGTE applies the GTE predicate on the "ru_latin" field.
func RuLatinGTE(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldGTE(FieldRuLatin, v))
}

// RuLatinLT applies the LT predicate on the "ru_latin" field.
func RuLatinLT(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldLT(FieldRuLatin, v))
}

// RuLatinLTE applies the LTE predicate on the "ru_latin" field.
func RuLatinLTE(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldLTE(FieldRuLatin, v))
}

// RuLatinContains applies the Contains predicate on the "ru_latin" field.
func RuLatinContains(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldContains(FieldRuLatin, v))
}

// RuLatinHasPrefix applies the HasPrefix predicate on the "ru_latin" field.
func RuLatinHasPrefix(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldHasPrefix(FieldRuLatin, v))
}

// RuLatinHasSuffix applies the HasSuffix predicate on the "ru_latin" field.
func RuLatinHasSuffix(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldHasSuffix(FieldRuLatin, v))
}

// RuLatinEqualFold applies the EqualFold predicate on the "ru_latin" field.
func RuLatinEqualFold(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEqualFold(FieldRuLatin, v))
}

// RuLatinContainsFold applies the ContainsFold predicate on the "ru_latin" field.
func RuLatinContainsFold(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldContainsFold(FieldRuLatin, v))
}

// KzEQ applies the EQ predicate on the "kz" field.
func KzEQ(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEQ(FieldKz, v))
}

// KzNEQ applies the NEQ predicate on the "kz" field.
func KzNEQ(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldNEQ(FieldKz, v))
}

// KzIn applies the In predicate on the "kz" field.
func KzIn(vs ...string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldIn(FieldKz, vs...))
}

// KzNotIn applies the NotIn predicate on the "kz" field.
func KzNotIn(vs ...string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldNotIn(FieldKz, vs...))
}

// KzGT applies the GT predicate on the "kz" field.
func KzGT(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldGT(FieldKz, v))
}

// KzGTE applies the GTE predicate on the "kz" field.
func KzGTE(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldGTE(FieldKz, v))
}

// KzLT applies the LT predicate on the "kz" field.
func KzLT(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldLT(FieldKz, v))
}

// KzLTE applies the LTE predicate on the "kz" field.
func KzLTE(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldLTE(FieldKz, v))
}

// KzContains applies the Contains predicate on the "kz" field.
func KzContains(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldContains(FieldKz, v))
}

// KzHasPrefix applies the HasPrefix predicate on the "kz" field.
func KzHasPrefix(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldHasPrefix(FieldKz, v))
}

// KzHasSuffix applies the HasSuffix predicate on the "kz" field.
func KzHasSuffix(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldHasSuffix(FieldKz, v))
}

// KzEqualFold applies the EqualFold predicate on the "kz" field.
func KzEqualFold(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEqualFold(FieldKz, v))
}

// KzContainsFold applies the ContainsFold predicate on the "kz" field.
func KzContainsFold(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldContainsFold(FieldKz, v))
}

// KzLatinEQ applies the EQ predicate on the "kz_latin" field.
func KzLatinEQ(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEQ(FieldKzLatin, v))
}

// KzLatinNEQ applies the NEQ predicate on the "kz_latin" field.
func KzLatinNEQ(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldNEQ(FieldKzLatin, v))
}

// KzLatinIn applies the In predicate on the "kz_latin" field.
func KzLatinIn(vs ...string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldIn(FieldKzLatin, vs...))
}

// KzLatinNotIn applies the NotIn predicate on the "kz_latin" field.
func KzLatinNotIn(vs ...string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldNotIn(FieldKzLatin, vs...))
}

// KzLatinGT applies the GT predicate on the "kz_latin" field.
func KzLatinGT(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldGT(FieldKzLatin, v))
}

// KzLatinGTE applies the GTE predicate on the "kz_latin" field.
func KzLatinGTE(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldGTE(FieldKzLatin, v))
}

// KzLatinLT applies the LT predicate on the "kz_latin" field.
func KzLatinLT(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldLT(FieldKzLatin, v))
}

// KzLatinLTE applies the LTE predicate on the "kz_latin" field.
func KzLatinLTE(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldLTE(FieldKzLatin, v))
}

// KzLatinContains applies the Contains predicate on the "kz_latin" field.
func KzLatinContains(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldContains(FieldKzLatin, v))
}

// KzLatinHasPrefix applies the HasPrefix predicate on the "kz_latin" field.
func KzLatinHasPrefix(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldHasPrefix(FieldKzLatin, v))
}

// KzLatinHasSuffix applies the HasSuffix predicate on the "kz_latin" field.
func KzLatinHasSuffix(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldHasSuffix(FieldKzLatin, v))
}

// KzLatinEqualFold applies the EqualFold predicate on the "kz_latin" field.
func KzLatinEqualFold(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEqualFold(FieldKzLatin, v))
}

// KzLatinContainsFold applies the ContainsFold predicate on the "kz_latin" field.
func KzLatinContainsFold(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldContainsFold(FieldKzLatin, v))
}

// EnEQ applies the EQ predicate on the "en" field.
func EnEQ(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEQ(FieldEn, v))
}

// EnNEQ applies the NEQ predicate on the "en" field.
func EnNEQ(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldNEQ(FieldEn, v))
}

// EnIn applies the In predicate on the "en" field.
func EnIn(vs ...string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldIn(FieldEn, vs...))
}

// EnNotIn applies the NotIn predicate on the "en" field.
func EnNotIn(vs ...string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldNotIn(FieldEn, vs...))
}

// EnGT applies the GT predicate on the "en" field.
func EnGT(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldGT(FieldEn, v))
}

// EnGTE applies the GTE predicate on the "en" field.
func EnGTE(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldGTE(FieldEn, v))
}

// EnLT applies the LT predicate on the "en" field.
func EnLT(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldLT(FieldEn, v))
}

// EnLTE applies the LTE predicate on the "en" field.
func EnLTE(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldLTE(FieldEn, v))
}

// EnContains applies the Contains predicate on the "en" field.
func EnContains(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldContains(FieldEn, v))
}

// EnHasPrefix applies the HasPrefix predicate on the "en" field.
func EnHasPrefix(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldHasPrefix(FieldEn, v))
}

// EnHasSuffix applies the HasSuffix predicate on the "en" field.
func EnHasSuffix(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldHasSuffix(FieldEn, v))
}

// EnEqualFold applies the EqualFold predicate on the "en" field.
func EnEqualFold(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldEqualFold(FieldEn, v))
}

// EnContainsFold applies the ContainsFold predicate on the "en" field.
func EnContainsFold(v string) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.FieldContainsFold(FieldEn, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.SMSTemplates) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.SMSTemplates) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.SMSTemplates) predicate.SMSTemplates {
	return predicate.SMSTemplates(sql.NotPredicates(p))
}
