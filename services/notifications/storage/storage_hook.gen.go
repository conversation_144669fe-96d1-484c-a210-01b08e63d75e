// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package storage

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage -i Storage -t ../../../etc/templates/hook.tmpl -o storage_hook.gen.go -l ""

import (
	"context"
	"time"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"

	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/entity"
)

var _ Storage = (*StorageHook)(nil)

// StorageHook implements Storage interface wrapper
type StorageHook struct {
	Storage
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// Check implements Storage
func (_w *StorageHook) Check(ctx context.Context) (err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.Storage, "Check", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "Check", _params)

	err = _w.Storage.Check(_ctx)
	_w._postCall.Hook(_ctx, _w.Storage, "Check", []any{err})
	return err
}

// GetSMSTemplate implements Storage
func (_w *StorageHook) GetSMSTemplate(ctx context.Context, name string) (sp1 *entity.SMSTemplate, err error) {
	_params := []any{ctx, name}
	defer _w._onPanic.Hook(_w.Storage, "GetSMSTemplate", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetSMSTemplate", _params)

	sp1, err = _w.Storage.GetSMSTemplate(_ctx, name)
	_w._postCall.Hook(_ctx, _w.Storage, "GetSMSTemplate", []any{sp1, err})
	return sp1, err
}

// SMSbyType implements Storage
func (_w *StorageHook) SMSbyType(ctx context.Context, req *entity.NotificationsByTypeReq, since time.Time) (na1 []entity.Notification, err error) {
	_params := []any{ctx, req, since}
	defer _w._onPanic.Hook(_w.Storage, "SMSbyType", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "SMSbyType", _params)

	na1, err = _w.Storage.SMSbyType(_ctx, req, since)
	_w._postCall.Hook(_ctx, _w.Storage, "SMSbyType", []any{na1, err})
	return na1, err
}

// SaveSMS implements Storage
func (_w *StorageHook) SaveSMS(ctx context.Context, req *entity.SendSMSReq) (id int32, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.Storage, "SaveSMS", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "SaveSMS", _params)

	id, err = _w.Storage.SaveSMS(_ctx, req)
	_w._postCall.Hook(_ctx, _w.Storage, "SaveSMS", []any{id, err})
	return id, err
}

// SetSmsID implements Storage
func (_w *StorageHook) SetSmsID(ctx context.Context, ID int32, smsID string) (err error) {
	_params := []any{ctx, ID, smsID}
	defer _w._onPanic.Hook(_w.Storage, "SetSmsID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "SetSmsID", _params)

	err = _w.Storage.SetSmsID(_ctx, ID, smsID)
	_w._postCall.Hook(_ctx, _w.Storage, "SetSmsID", []any{err})
	return err
}

// NewStorageHook returns StorageHook
func NewStorageHook(object Storage, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *StorageHook {
	return &StorageHook{
		Storage:     object,
		_beforeCall: beforeCall,
		_postCall:   postCall,
		_onPanic:    onPanic,
	}
}
