// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package storage

import (
	"context"

	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent"
	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/storage/postgres/ent/migrate"

	"entgo.io/ent/dialect/sql"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/presenters"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx/events"
	"git.redmadrobot.com/backend-go/rmr-pkg/proxy/hooks"
)

var _ Storage = (*storageImpl)(nil)

type (
	storageImpl struct {
		Storage
		PostgresClient  *ent.Client
		SQLClient       *sql.Driver
		eventsPublisher *events.Publisher
	}

	StorageDependencies struct {
		PostgresClient *ent.Client
		SQLClient      *sql.Driver
	}
)

func NewStorage(deps StorageDependencies) *StorageHook {
	eventsPublisher := events.NewPublisher(events.Logger(presenters.ViewLogs, presenters.DefaultViewOptions()))

	storage := &storageImpl{
		PostgresClient:  deps.PostgresClient,
		SQLClient:       deps.SQLClient,
		eventsPublisher: eventsPublisher,
	}
	// Подписываемся на хуки мутаций ent
	storage.PostgresClient.Use(events.NewDBEventHook(eventsPublisher.DBConsumer()))

	// Initialize hooks
	hook := NewStorageHook(
		storage,
		hooks.LogBeforeCall(presenters.ViewLogs, presenters.DefaultViewOptions()),
		hooks.LogPostCall(presenters.ViewLogs, presenters.DefaultViewOptions()),
		hooks.LogPanic(presenters.ViewLogs, presenters.DefaultViewOptions()),
	)
	storage.Storage = hook

	return hook
}
func NewPostgresDBClient(ctx context.Context, drv *sql.Driver, debug bool) (*ent.Client, error) {
	logger := logs.FromContext(ctx)

	options := []ent.Option{ent.Driver(drv), ent.Log(logger.Print)}
	if debug {
		options = append(options, ent.Debug())
	}
	client := ent.NewClient(options...)
	if debug {
		client = client.Debug()
	}
	if err := client.Schema.Create(ctx,
		migrate.WithDropColumn(true),
		migrate.WithDropIndex(true)); err != nil {
		return nil, err
	}

	return client, nil
}
