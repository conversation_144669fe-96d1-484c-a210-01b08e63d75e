// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package usecase

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/notifications/usecase -i Notifications -t ../../../etc/templates/hook.tmpl -o usecase_hook.gen.go -l ""

import (
	"context"

	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"

	"git.redmadrobot.com/zaman/backend/zaman/services/notifications/entity"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"
)

var _ Notifications = (*NotificationsHook)(nil)

// NotificationsHook implements Notifications interface wrapper
type NotificationsHook struct {
	Notifications
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// HealthCheck implements Notifications
func (_w *NotificationsHook) HealthCheck(ctx context.Context) (hp1 *entity.Health, err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.Notifications, "HealthCheck", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Notifications, "HealthCheck", _params)

	hp1, err = _w.Notifications.HealthCheck(_ctx)
	_w._postCall.Hook(_ctx, _w.Notifications, "HealthCheck", []any{hp1, err})
	return hp1, err
}

// HealthEvent implements Notifications
func (_w *NotificationsHook) HealthEvent(ctx context.Context, message *kafka.Message) {
	_params := []any{ctx, message}
	defer _w._onPanic.Hook(_w.Notifications, "HealthEvent", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Notifications, "HealthEvent", _params)

	_w.Notifications.HealthEvent(_ctx, message)
	_w._postCall.Hook(_ctx, _w.Notifications, "HealthEvent", []any{})
	return
}

// InitConsumer implements Notifications
func (_w *NotificationsHook) InitConsumer(ctx context.Context) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.Notifications, "InitConsumer", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Notifications, "InitConsumer", _params)

	_w.Notifications.InitConsumer(_ctx)
	_w._postCall.Hook(_ctx, _w.Notifications, "InitConsumer", []any{})
	return
}

// NotificationsByType implements Notifications
func (_w *NotificationsHook) NotificationsByType(ctx context.Context, req *entity.NotificationsByTypeReq) (np1 *entity.NotificationsByTypeResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.Notifications, "NotificationsByType", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Notifications, "NotificationsByType", _params)

	np1, err = _w.Notifications.NotificationsByType(_ctx, req)
	_w._postCall.Hook(_ctx, _w.Notifications, "NotificationsByType", []any{np1, err})
	return np1, err
}

// SendSMS implements Notifications
func (_w *NotificationsHook) SendSMS(ctx context.Context, req *entity.SendSMSReq) (sp1 *entity.SendSMSResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.Notifications, "SendSMS", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Notifications, "SendSMS", _params)

	sp1, err = _w.Notifications.SendSMS(_ctx, req)
	_w._postCall.Hook(_ctx, _w.Notifications, "SendSMS", []any{sp1, err})
	return sp1, err
}

// NewNotificationsHook returns NotificationsHook
func NewNotificationsHook(object Notifications, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *NotificationsHook {
	return &NotificationsHook{
		Notifications: object,
		_beforeCall:   beforeCall,
		_postCall:     postCall,
		_onPanic:      onPanic,
	}
}
