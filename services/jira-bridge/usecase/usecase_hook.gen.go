// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package usecase

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/jira-bridge/usecase -i JiraBridge -t ../../../etc/templates/hook.tmpl -o usecase_hook.gen.go -l ""

import (
	"context"

	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"

	"git.redmadrobot.com/zaman/backend/zaman/services/jira-bridge/entity"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"
)

var _ JiraBridge = (*JiraBridgeHook)(nil)

// JiraBridgeHook implements JiraBridge interface wrapper
type JiraBridgeHook struct {
	JiraBridge
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// CreateIssue implements JiraBridge
func (_w *JiraBridgeHook) CreateIssue(ctx context.Context, req *entity.CreateIssueReq) (cp1 *entity.CreateIssueResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.JiraBridge, "CreateIssue", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.JiraBridge, "CreateIssue", _params)

	cp1, err = _w.JiraBridge.CreateIssue(_ctx, req)
	_w._postCall.Hook(_ctx, _w.JiraBridge, "CreateIssue", []any{cp1, err})
	return cp1, err
}

// HealthCheck implements JiraBridge
func (_w *JiraBridgeHook) HealthCheck(ctx context.Context) (hp1 *entity.Health, err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.JiraBridge, "HealthCheck", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.JiraBridge, "HealthCheck", _params)

	hp1, err = _w.JiraBridge.HealthCheck(_ctx)
	_w._postCall.Hook(_ctx, _w.JiraBridge, "HealthCheck", []any{hp1, err})
	return hp1, err
}

// HealthEvent implements JiraBridge
func (_w *JiraBridgeHook) HealthEvent(ctx context.Context, message *kafka.Message) {
	_params := []any{ctx, message}
	defer _w._onPanic.Hook(_w.JiraBridge, "HealthEvent", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.JiraBridge, "HealthEvent", _params)

	_w.JiraBridge.HealthEvent(_ctx, message)
	_w._postCall.Hook(_ctx, _w.JiraBridge, "HealthEvent", []any{})
	return
}

// InitConsumer implements JiraBridge
func (_w *JiraBridgeHook) InitConsumer(ctx context.Context) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.JiraBridge, "InitConsumer", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.JiraBridge, "InitConsumer", _params)

	_w.JiraBridge.InitConsumer(_ctx)
	_w._postCall.Hook(_ctx, _w.JiraBridge, "InitConsumer", []any{})
	return
}

// NewJiraBridgeHook returns JiraBridgeHook
func NewJiraBridgeHook(object JiraBridge, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *JiraBridgeHook {
	return &JiraBridgeHook{
		JiraBridge:  object,
		_beforeCall: beforeCall,
		_postCall:   postCall,
		_onPanic:    onPanic,
	}
}
