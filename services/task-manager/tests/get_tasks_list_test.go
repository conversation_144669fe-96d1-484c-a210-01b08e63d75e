package tests

import (
	"time"

	"git.redmadrobot.com/zaman/backend/zaman/services/task-manager/consts"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/task-manager"
)

func (s *Suite) TestGetTasksList_FilterByStatus_Success() {
	parentTaskInfo, err := s.postgresDB.Tasks.Create().
		SetStatus(consts.TaskStatusFailed).
		SetErrorDescription("test error description").
		SetPayload(map[string]interface{}{"foo": "bar"}).
		SetType("test-type").
		Save(s.ctx)
	s.Require().NoError(err)

	childTask, err := s.postgresDB.Tasks.Create().
		SetStatus(consts.TaskStatusRunning).
		SetPayload(map[string]interface{}{"foo": "bar"}).
		SetType("test-type").
		Save(s.ctx)
	s.Require().NoError(err)

	_, err = s.postgresDB.TaskDependencies.Create().
		SetTaskID(childTask.ID).
		SetDependencyID(parentTaskInfo.ID).
		SetExecutionOrder(1).
		SetNillableParallelGroup(nil).
		Save(s.ctx)
	s.Require().NoError(err)

	statusFilter := consts.TaskStatusRunning
	res, err := s.grpc.GetTasksList(s.ctx, &pb.GetTasksListReq{
		StatusFilter:  &statusFilter,
		TypeFilter:    nil,
		CreatedAfter:  nil,
		CreatedBefore: nil,
		Page:          nil,
		PageSize:      nil,
	})
	s.Require().NoError(err)

	s.Require().Len(res.Tasks, 1)
	s.Require().Equal(childTask.ID.String(), res.Tasks[0].TaskId)
}

func (s *Suite) TestGetTasksList_FilterByType_Success() {
	typeFilter := "test-type-1"

	parentTaskInfo, err := s.postgresDB.Tasks.Create().
		SetStatus(consts.TaskStatusFailed).
		SetErrorDescription("test error description").
		SetPayload(map[string]interface{}{"foo": "bar"}).
		SetType(typeFilter).
		Save(s.ctx)
	s.Require().NoError(err)

	childTask, err := s.postgresDB.Tasks.Create().
		SetStatus(consts.TaskStatusRunning).
		SetPayload(map[string]interface{}{"foo": "bar"}).
		SetType("test-type").
		Save(s.ctx)
	s.Require().NoError(err)

	_, err = s.postgresDB.TaskDependencies.Create().
		SetTaskID(childTask.ID).
		SetDependencyID(parentTaskInfo.ID).
		SetExecutionOrder(1).
		SetNillableParallelGroup(nil).
		Save(s.ctx)
	s.Require().NoError(err)

	res, err := s.grpc.GetTasksList(s.ctx, &pb.GetTasksListReq{
		StatusFilter:  nil,
		TypeFilter:    &typeFilter,
		CreatedAfter:  nil,
		CreatedBefore: nil,
		Page:          nil,
		PageSize:      nil,
	})
	s.Require().NoError(err)

	s.Require().Len(res.Tasks, 1)
	s.Require().Equal(parentTaskInfo.ID.String(), res.Tasks[0].TaskId)
}

func (s *Suite) TestGetTasksList_FilterByTypeAndStatus_Success() {
	typeFilter := "test-type"

	parentTaskInfo, err := s.postgresDB.Tasks.Create().
		SetStatus(consts.TaskStatusFailed).
		SetErrorDescription("test error description").
		SetPayload(map[string]interface{}{"foo": "bar"}).
		SetType(typeFilter).
		Save(s.ctx)
	s.Require().NoError(err)

	childTask, err := s.postgresDB.Tasks.Create().
		SetStatus(consts.TaskStatusRunning).
		SetPayload(map[string]interface{}{"foo": "bar"}).
		SetType("test-type").
		Save(s.ctx)
	s.Require().NoError(err)

	_, err = s.postgresDB.TaskDependencies.Create().
		SetTaskID(childTask.ID).
		SetDependencyID(parentTaskInfo.ID).
		SetExecutionOrder(1).
		SetNillableParallelGroup(nil).
		Save(s.ctx)
	s.Require().NoError(err)

	statusFilter := consts.TaskStatusRunning
	res, err := s.grpc.GetTasksList(s.ctx, &pb.GetTasksListReq{
		StatusFilter:  &statusFilter,
		TypeFilter:    &typeFilter,
		CreatedAfter:  nil,
		CreatedBefore: nil,
		Page:          nil,
		PageSize:      nil,
	})
	s.Require().NoError(err)
	s.Require().Len(res.Tasks, 1)
	s.Require().Equal(childTask.ID.String(), res.Tasks[0].TaskId)
}

func (s *Suite) TestGetTasksList_FilterByCreatedAfter_Success() {
	typeFilter := "test-type"

	parentTaskInfo, err := s.postgresDB.Tasks.Create().
		SetStatus(consts.TaskStatusFailed).
		SetErrorDescription("test error description").
		SetPayload(map[string]interface{}{"foo": "bar"}).
		SetCreatedAt(time.Now()).
		SetType(typeFilter).
		Save(s.ctx)
	s.Require().NoError(err)

	childTask, err := s.postgresDB.Tasks.Create().
		SetStatus(consts.TaskStatusRunning).
		SetPayload(map[string]interface{}{"foo": "bar"}).
		SetType("test-type").
		SetCreatedAt(time.Now().Add(-time.Hour)).
		Save(s.ctx)
	s.Require().NoError(err)

	_, err = s.postgresDB.TaskDependencies.Create().
		SetTaskID(childTask.ID).
		SetDependencyID(parentTaskInfo.ID).
		SetExecutionOrder(1).
		SetNillableParallelGroup(nil).
		Save(s.ctx)
	s.Require().NoError(err)

	createdAt := time.Now()
	createdAtFilter := createdAt.Format(consts.DefaultDateTimeFormat)
	res, err := s.grpc.GetTasksList(s.ctx, &pb.GetTasksListReq{
		StatusFilter:  nil,
		TypeFilter:    nil,
		CreatedAfter:  &createdAtFilter,
		CreatedBefore: nil,
		Page:          nil,
		PageSize:      nil,
	})
	s.Require().NoError(err)
	s.Require().Len(res.Tasks, 1)
	s.Require().Equal(parentTaskInfo.ID.String(), res.Tasks[0].TaskId)
}

func (s *Suite) TestGetTasksList_FilterByCreatedBefore_Success() {
	typeFilter := "test-type"

	parentTaskInfo, err := s.postgresDB.Tasks.Create().
		SetStatus(consts.TaskStatusFailed).
		SetErrorDescription("test error description").
		SetPayload(map[string]interface{}{"foo": "bar"}).
		SetCreatedAt(time.Now().Add(-time.Minute)).
		SetType(typeFilter).
		Save(s.ctx)
	s.Require().NoError(err)

	childTask, err := s.postgresDB.Tasks.Create().
		SetStatus(consts.TaskStatusRunning).
		SetPayload(map[string]interface{}{"foo": "bar"}).
		SetType("test-type").
		SetCreatedAt(time.Now().Add(-time.Hour)).
		Save(s.ctx)
	s.Require().NoError(err)

	_, err = s.postgresDB.TaskDependencies.Create().
		SetTaskID(childTask.ID).
		SetDependencyID(parentTaskInfo.ID).
		SetExecutionOrder(1).
		SetNillableParallelGroup(nil).
		Save(s.ctx)
	s.Require().NoError(err)

	createdBefore := time.Now()
	createdBeforeFilter := createdBefore.Format(consts.DefaultDateTimeFormat)
	res, err := s.grpc.GetTasksList(s.ctx, &pb.GetTasksListReq{
		StatusFilter:  nil,
		TypeFilter:    nil,
		CreatedAfter:  nil,
		CreatedBefore: &createdBeforeFilter,
		Page:          nil,
		PageSize:      nil,
	})
	s.Require().NoError(err)
	s.Require().Len(res.Tasks, 2)
}

func (s *Suite) TestGetTasksList_EmptyFilter_Success() {
	parentTaskInfo, err := s.postgresDB.Tasks.Create().
		SetStatus(consts.TaskStatusFailed).
		SetErrorDescription("test error description").
		SetPayload(map[string]interface{}{"foo": "bar"}).
		SetType("test-type").
		Save(s.ctx)
	s.Require().NoError(err)

	childTask, err := s.postgresDB.Tasks.Create().
		SetStatus(consts.TaskStatusRunning).
		SetPayload(map[string]interface{}{"foo": "bar"}).
		SetType("test-type").
		Save(s.ctx)
	s.Require().NoError(err)

	_, err = s.postgresDB.TaskDependencies.Create().
		SetTaskID(childTask.ID).
		SetDependencyID(parentTaskInfo.ID).
		SetExecutionOrder(1).
		SetNillableParallelGroup(nil).
		Save(s.ctx)
	s.Require().NoError(err)

	res, err := s.grpc.GetTasksList(s.ctx, &pb.GetTasksListReq{
		StatusFilter:  nil,
		TypeFilter:    nil,
		CreatedAfter:  nil,
		CreatedBefore: nil,
		Page:          nil,
		PageSize:      nil,
	})
	s.Require().NoError(err)
	s.Require().Len(res.Tasks, 2)
	s.Require().True(res.TotalCount == 2)
}

func (s *Suite) TestGetTasksList_PageCheck_Success() {
	parentTaskInfo, err := s.postgresDB.Tasks.Create().
		SetStatus(consts.TaskStatusFailed).
		SetErrorDescription("test error description").
		SetPayload(map[string]interface{}{"foo": "bar"}).
		SetType("test-type").
		Save(s.ctx)
	s.Require().NoError(err)

	childTask, err := s.postgresDB.Tasks.Create().
		SetStatus(consts.TaskStatusRunning).
		SetPayload(map[string]interface{}{"foo": "bar"}).
		SetType("test-type").
		Save(s.ctx)
	s.Require().NoError(err)

	_, err = s.postgresDB.TaskDependencies.Create().
		SetTaskID(childTask.ID).
		SetDependencyID(parentTaskInfo.ID).
		SetExecutionOrder(1).
		SetNillableParallelGroup(nil).
		Save(s.ctx)
	s.Require().NoError(err)

	pageSize := int64(1)
	page := int64(2)
	res, err := s.grpc.GetTasksList(s.ctx, &pb.GetTasksListReq{
		StatusFilter:  nil,
		TypeFilter:    nil,
		CreatedAfter:  nil,
		CreatedBefore: nil,
		Page:          &page,
		PageSize:      &pageSize,
	})
	s.Require().NoError(err)
	s.Require().Len(res.Tasks, 1)
	s.Require().True(res.TotalCount == 2)
}

func (s *Suite) TestGetTasksList_LimitCheck_Success() {
	parentTaskInfo, err := s.postgresDB.Tasks.Create().
		SetStatus(consts.TaskStatusFailed).
		SetErrorDescription("test error description").
		SetPayload(map[string]interface{}{"foo": "bar"}).
		SetType("test-type").
		Save(s.ctx)
	s.Require().NoError(err)

	childTask, err := s.postgresDB.Tasks.Create().
		SetStatus(consts.TaskStatusRunning).
		SetPayload(map[string]interface{}{"foo": "bar"}).
		SetType("test-type").
		Save(s.ctx)
	s.Require().NoError(err)

	_, err = s.postgresDB.TaskDependencies.Create().
		SetTaskID(childTask.ID).
		SetDependencyID(parentTaskInfo.ID).
		SetExecutionOrder(1).
		SetNillableParallelGroup(nil).
		Save(s.ctx)
	s.Require().NoError(err)

	pageSize := int64(1)
	res, err := s.grpc.GetTasksList(s.ctx, &pb.GetTasksListReq{
		StatusFilter:  nil,
		TypeFilter:    nil,
		CreatedAfter:  nil,
		CreatedBefore: nil,
		Page:          nil,
		PageSize:      &pageSize,
	})
	s.Require().NoError(err)
	s.Require().Len(res.Tasks, 1)
	s.Require().True(res.TotalCount == 2)
}

func (s *Suite) TestGetTasksList_FilterByCreatedBefore_ParseError() {
	typeFilter := "test-type"

	parentTaskInfo, err := s.postgresDB.Tasks.Create().
		SetStatus(consts.TaskStatusFailed).
		SetErrorDescription("test error description").
		SetPayload(map[string]interface{}{"foo": "bar"}).
		SetCreatedAt(time.Now().Add(-time.Minute)).
		SetType(typeFilter).
		Save(s.ctx)
	s.Require().NoError(err)

	childTask, err := s.postgresDB.Tasks.Create().
		SetStatus(consts.TaskStatusRunning).
		SetPayload(map[string]interface{}{"foo": "bar"}).
		SetType("test-type").
		Save(s.ctx)
	s.Require().NoError(err)

	_, err = s.postgresDB.TaskDependencies.Create().
		SetTaskID(childTask.ID).
		SetDependencyID(parentTaskInfo.ID).
		SetExecutionOrder(1).
		SetNillableParallelGroup(nil).
		Save(s.ctx)
	s.Require().NoError(err)

	createdBeforeFilter := time.Now().String()
	res, err := s.grpc.GetTasksList(s.ctx, &pb.GetTasksListReq{
		StatusFilter:  nil,
		TypeFilter:    nil,
		CreatedAfter:  nil,
		CreatedBefore: &createdBeforeFilter,
		Page:          nil,
		PageSize:      nil,
	})
	s.Require().NotNil(err)
	s.Require().Nil(res)
}

func (s *Suite) TestGetTasksList_FilterByCreatedAfter_ParseError() {
	typeFilter := "test-type"

	parentTaskInfo, err := s.postgresDB.Tasks.Create().
		SetStatus(consts.TaskStatusFailed).
		SetErrorDescription("test error description").
		SetPayload(map[string]interface{}{"foo": "bar"}).
		SetCreatedAt(time.Now().Add(-time.Minute)).
		SetType(typeFilter).
		Save(s.ctx)
	s.Require().NoError(err)

	childTask, err := s.postgresDB.Tasks.Create().
		SetStatus(consts.TaskStatusRunning).
		SetPayload(map[string]interface{}{"foo": "bar"}).
		SetType("test-type").
		Save(s.ctx)
	s.Require().NoError(err)

	_, err = s.postgresDB.TaskDependencies.Create().
		SetTaskID(childTask.ID).
		SetDependencyID(parentTaskInfo.ID).
		SetExecutionOrder(1).
		SetNillableParallelGroup(nil).
		Save(s.ctx)
	s.Require().NoError(err)

	createdAfterFilter := time.Now().String()
	res, err := s.grpc.GetTasksList(s.ctx, &pb.GetTasksListReq{
		StatusFilter:  nil,
		TypeFilter:    nil,
		CreatedAfter:  &createdAfterFilter,
		CreatedBefore: nil,
		Page:          nil,
		PageSize:      nil,
	})
	s.Require().NotNil(err)
	s.Require().Nil(res)
}
