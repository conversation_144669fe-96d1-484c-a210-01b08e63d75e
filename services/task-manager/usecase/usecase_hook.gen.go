// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package usecase

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/task-manager/usecase -i TaskManager -t ../../../etc/templates/hook.tmpl -o usecase_hook.gen.go -l ""

import (
	"context"

	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"

	"git.redmadrobot.com/zaman/backend/zaman/services/task-manager/entity"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"
)

var _ TaskManager = (*TaskManagerHook)(nil)

// TaskManagerHook implements TaskManager interface wrapper
type TaskManagerHook struct {
	TaskManager
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// CreateTask implements TaskManager
func (_w *TaskManagerHook) CreateTask(ctx context.Context, req *entity.CreateTaskReq) (cp1 *entity.CreateTaskResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.TaskManager, "CreateTask", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.TaskManager, "CreateTask", _params)

	cp1, err = _w.TaskManager.CreateTask(_ctx, req)
	_w._postCall.Hook(_ctx, _w.TaskManager, "CreateTask", []any{cp1, err})
	return cp1, err
}

// GetTaskInfo implements TaskManager
func (_w *TaskManagerHook) GetTaskInfo(ctx context.Context, req *entity.GetTaskInfoReq) (gp1 *entity.GetTaskInfoResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.TaskManager, "GetTaskInfo", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.TaskManager, "GetTaskInfo", _params)

	gp1, err = _w.TaskManager.GetTaskInfo(_ctx, req)
	_w._postCall.Hook(_ctx, _w.TaskManager, "GetTaskInfo", []any{gp1, err})
	return gp1, err
}

// GetTasksList implements TaskManager
func (_w *TaskManagerHook) GetTasksList(ctx context.Context, req *entity.GetTasksListReq) (gp1 *entity.GetTasksListResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.TaskManager, "GetTasksList", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.TaskManager, "GetTasksList", _params)

	gp1, err = _w.TaskManager.GetTasksList(_ctx, req)
	_w._postCall.Hook(_ctx, _w.TaskManager, "GetTasksList", []any{gp1, err})
	return gp1, err
}

// HealthCheck implements TaskManager
func (_w *TaskManagerHook) HealthCheck(ctx context.Context) (hp1 *entity.Health, err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.TaskManager, "HealthCheck", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.TaskManager, "HealthCheck", _params)

	hp1, err = _w.TaskManager.HealthCheck(_ctx)
	_w._postCall.Hook(_ctx, _w.TaskManager, "HealthCheck", []any{hp1, err})
	return hp1, err
}

// HealthEvent implements TaskManager
func (_w *TaskManagerHook) HealthEvent(ctx context.Context, message *kafka.Message) {
	_params := []any{ctx, message}
	defer _w._onPanic.Hook(_w.TaskManager, "HealthEvent", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.TaskManager, "HealthEvent", _params)

	_w.TaskManager.HealthEvent(_ctx, message)
	_w._postCall.Hook(_ctx, _w.TaskManager, "HealthEvent", []any{})
	return
}

// InitConsumer implements TaskManager
func (_w *TaskManagerHook) InitConsumer(ctx context.Context) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.TaskManager, "InitConsumer", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.TaskManager, "InitConsumer", _params)

	_w.TaskManager.InitConsumer(_ctx)
	_w._postCall.Hook(_ctx, _w.TaskManager, "InitConsumer", []any{})
	return
}

// UpdateTask implements TaskManager
func (_w *TaskManagerHook) UpdateTask(ctx context.Context, req *entity.UpdateTaskReq) (up1 *entity.UpdateTaskResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.TaskManager, "UpdateTask", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.TaskManager, "UpdateTask", _params)

	up1, err = _w.TaskManager.UpdateTask(_ctx, req)
	_w._postCall.Hook(_ctx, _w.TaskManager, "UpdateTask", []any{up1, err})
	return up1, err
}

// NewTaskManagerHook returns TaskManagerHook
func NewTaskManagerHook(object TaskManager, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *TaskManagerHook {
	return &TaskManagerHook{
		TaskManager: object,
		_beforeCall: beforeCall,
		_postCall:   postCall,
		_onPanic:    onPanic,
	}
}
