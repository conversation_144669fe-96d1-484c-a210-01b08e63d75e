// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package usecase

import (
	"context"

	taskmanager "git.redmadrobot.com/zaman/backend/zaman/config/services/task-manager"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/bus"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/presenters"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"

	"git.redmadrobot.com/backend-go/rmr-pkg/proxy/hooks"

	"git.redmadrobot.com/zaman/backend/zaman/cmd/services/task-manager/providers"
	"git.redmadrobot.com/zaman/backend/zaman/services/task-manager/entity"
)

var _ TaskManager = (*useCasesImpl)(nil)

type TaskManager interface {
	HealthCheck(ctx context.Context) (*entity.Health, error)
	HealthEvent(ctx context.Context, message *kafka.Message)
	InitConsumer(ctx context.Context)
	CreateTask(ctx context.Context, req *entity.CreateTaskReq) (*entity.CreateTaskResult, error)
	UpdateTask(ctx context.Context, req *entity.UpdateTaskReq) (*entity.UpdateTaskResult, error)
	GetTaskInfo(ctx context.Context, req *entity.GetTaskInfoReq) (*entity.GetTaskInfoResult, error)
	GetTasksList(ctx context.Context, req *entity.GetTasksListReq) (*entity.GetTasksListResult, error)
}

type useCasesImpl struct {
	TaskManager
	cfg       *taskmanager.Config
	Providers providers.ServiceLocatorImpl
}

func New(ctx context.Context, locator providers.ServiceLocatorImpl, cfg *taskmanager.Config) *TaskManagerHook {
	useCases := &useCasesImpl{
		cfg:       cfg,
		Providers: locator,
	}

	logger := logs.FromContext(ctx)

	hook := NewTaskManagerHook(
		useCases,
		hooks.GrpcServiceLogBeforeCall(logger, presenters.ViewLogs, presenters.DefaultViewOptions()),
		hooks.GrpcServiceLogPostCall(logger, presenters.ViewLogs, presenters.DefaultViewOptions()),
		hooks.GrpcServiceLogPanic(logger, presenters.ViewLogs, presenters.DefaultViewOptions()),
	)
	useCases.TaskManager = hook

	return hook
}
func (u *useCasesImpl) InitConsumer(ctx context.Context) {
	register := func(ctx context.Context, busName string, handler func(context.Context, *kafka.Message)) error {
		subscriber := bus.SubscriberFn[*kafka.Message](func(ctx context.Context, messages ...*kafka.Message) error {
			for _, message := range messages {
				handler(ctx, message)
				message.Ack(ctx)
			}
			return nil
		})

		err := u.Providers.Event.Subscribe(ctx, busName, subscriber)
		if err != nil {
			return err
		}
		return nil
	}

	eventRegistry := u.EventRegistry()
	for busName, handler := range eventRegistry {
		err := register(ctx, busName, handler)
		if err != nil {
			logs.FromContext(ctx).Err(err).Msg("unable to register handler")
		}
	}
}
