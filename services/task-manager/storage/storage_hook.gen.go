// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package storage

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/task-manager/storage -i Storage -t ../../../etc/templates/hook.tmpl -o storage_hook.gen.go -l ""

import (
	"context"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/task-manager/entity"
)

var _ Storage = (*StorageHook)(nil)

// StorageHook implements Storage interface wrapper
type StorageHook struct {
	Storage
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// Check implements Storage
func (_w *StorageHook) Check(ctx context.Context) (err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.Storage, "Check", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "Check", _params)

	err = _w.Storage.Check(_ctx)
	_w._postCall.Hook(_ctx, _w.Storage, "Check", []any{err})
	return err
}

// CreateTask implements Storage
func (_w *StorageHook) CreateTask(ctx context.Context, task *entity.ParamCreateTask) (cp1 *entity.CreateTaskResult, err error) {
	_params := []any{ctx, task}
	defer _w._onPanic.Hook(_w.Storage, "CreateTask", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "CreateTask", _params)

	cp1, err = _w.Storage.CreateTask(_ctx, task)
	_w._postCall.Hook(_ctx, _w.Storage, "CreateTask", []any{cp1, err})
	return cp1, err
}

// GetFailedTasksWithDependencies implements Storage
func (_w *StorageHook) GetFailedTasksWithDependencies(ctx context.Context) (tpa1 []*entity.Task, err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.Storage, "GetFailedTasksWithDependencies", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetFailedTasksWithDependencies", _params)

	tpa1, err = _w.Storage.GetFailedTasksWithDependencies(_ctx)
	_w._postCall.Hook(_ctx, _w.Storage, "GetFailedTasksWithDependencies", []any{tpa1, err})
	return tpa1, err
}

// GetTask implements Storage
func (_w *StorageHook) GetTask(ctx context.Context, taskID uuid.UUID) (gp1 *entity.GetTaskInfoResult, err error) {
	_params := []any{ctx, taskID}
	defer _w._onPanic.Hook(_w.Storage, "GetTask", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetTask", _params)

	gp1, err = _w.Storage.GetTask(_ctx, taskID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetTask", []any{gp1, err})
	return gp1, err
}

// GetTaskWithDependencies implements Storage
func (_w *StorageHook) GetTaskWithDependencies(ctx context.Context, taskID uuid.UUID) (tp1 *entity.Task, err error) {
	_params := []any{ctx, taskID}
	defer _w._onPanic.Hook(_w.Storage, "GetTaskWithDependencies", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetTaskWithDependencies", _params)

	tp1, err = _w.Storage.GetTaskWithDependencies(_ctx, taskID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetTaskWithDependencies", []any{tp1, err})
	return tp1, err
}

// GetTasksList implements Storage
func (_w *StorageHook) GetTasksList(ctx context.Context, params *entity.ParamsGetTasksList) (gp1 *entity.GetTasksListResult, err error) {
	_params := []any{ctx, params}
	defer _w._onPanic.Hook(_w.Storage, "GetTasksList", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetTasksList", _params)

	gp1, err = _w.Storage.GetTasksList(_ctx, params)
	_w._postCall.Hook(_ctx, _w.Storage, "GetTasksList", []any{gp1, err})
	return gp1, err
}

// MarkTaskAsFailed implements Storage
func (_w *StorageHook) MarkTaskAsFailed(ctx context.Context, taskID uuid.UUID) (err error) {
	_params := []any{ctx, taskID}
	defer _w._onPanic.Hook(_w.Storage, "MarkTaskAsFailed", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "MarkTaskAsFailed", _params)

	err = _w.Storage.MarkTaskAsFailed(_ctx, taskID)
	_w._postCall.Hook(_ctx, _w.Storage, "MarkTaskAsFailed", []any{err})
	return err
}

// RecordTaskRestart implements Storage
func (_w *StorageHook) RecordTaskRestart(ctx context.Context, param *entity.ParamRestartTask) (err error) {
	_params := []any{ctx, param}
	defer _w._onPanic.Hook(_w.Storage, "RecordTaskRestart", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "RecordTaskRestart", _params)

	err = _w.Storage.RecordTaskRestart(_ctx, param)
	_w._postCall.Hook(_ctx, _w.Storage, "RecordTaskRestart", []any{err})
	return err
}

// UpdateTask implements Storage
func (_w *StorageHook) UpdateTask(ctx context.Context, task *entity.ParamUpdateTask) (gp1 *entity.GetTaskInfoResult, err error) {
	_params := []any{ctx, task}
	defer _w._onPanic.Hook(_w.Storage, "UpdateTask", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "UpdateTask", _params)

	gp1, err = _w.Storage.UpdateTask(_ctx, task)
	_w._postCall.Hook(_ctx, _w.Storage, "UpdateTask", []any{gp1, err})
	return gp1, err
}

// UpdateTaskRestartInfo implements Storage
func (_w *StorageHook) UpdateTaskRestartInfo(ctx context.Context, param *entity.ParamsUpdateRestartTask) (err error) {
	_params := []any{ctx, param}
	defer _w._onPanic.Hook(_w.Storage, "UpdateTaskRestartInfo", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "UpdateTaskRestartInfo", _params)

	err = _w.Storage.UpdateTaskRestartInfo(_ctx, param)
	_w._postCall.Hook(_ctx, _w.Storage, "UpdateTaskRestartInfo", []any{err})
	return err
}

// NewStorageHook returns StorageHook
func NewStorageHook(object Storage, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *StorageHook {
	return &StorageHook{
		Storage:     object,
		_beforeCall: beforeCall,
		_postCall:   postCall,
		_onPanic:    onPanic,
	}
}
