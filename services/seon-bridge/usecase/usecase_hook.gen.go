// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package usecase

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/seon-bridge/usecase -i SeonBridge -t ../../../etc/templates/hook.tmpl -o usecase_hook.gen.go -l ""

import (
	"context"

	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"

	"git.redmadrobot.com/zaman/backend/zaman/services/seon-bridge/entity"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"
)

var _ SeonBridge = (*SeonBridgeHook)(nil)

// SeonBridgeHook implements SeonBridge interface wrapper
type SeonBridgeHook struct {
	SeonBridge
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// GetFraudData implements SeonBridge
func (_w *SeonBridgeHook) GetFraudData(ctx context.Context, req *entity.GetFraudDataReq) (gp1 *entity.GetFraudDataResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.SeonBridge, "GetFraudData", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.SeonBridge, "GetFraudData", _params)

	gp1, err = _w.SeonBridge.GetFraudData(_ctx, req)
	_w._postCall.Hook(_ctx, _w.SeonBridge, "GetFraudData", []any{gp1, err})
	return gp1, err
}

// HealthCheck implements SeonBridge
func (_w *SeonBridgeHook) HealthCheck(ctx context.Context) (hp1 *entity.Health, err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.SeonBridge, "HealthCheck", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.SeonBridge, "HealthCheck", _params)

	hp1, err = _w.SeonBridge.HealthCheck(_ctx)
	_w._postCall.Hook(_ctx, _w.SeonBridge, "HealthCheck", []any{hp1, err})
	return hp1, err
}

// HealthEvent implements SeonBridge
func (_w *SeonBridgeHook) HealthEvent(ctx context.Context, message *kafka.Message) {
	_params := []any{ctx, message}
	defer _w._onPanic.Hook(_w.SeonBridge, "HealthEvent", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.SeonBridge, "HealthEvent", _params)

	_w.SeonBridge.HealthEvent(_ctx, message)
	_w._postCall.Hook(_ctx, _w.SeonBridge, "HealthEvent", []any{})
	return
}

// InitConsumer implements SeonBridge
func (_w *SeonBridgeHook) InitConsumer(ctx context.Context) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.SeonBridge, "InitConsumer", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.SeonBridge, "InitConsumer", _params)

	_w.SeonBridge.InitConsumer(_ctx)
	_w._postCall.Hook(_ctx, _w.SeonBridge, "InitConsumer", []any{})
	return
}

// NewSeonBridgeHook returns SeonBridgeHook
func NewSeonBridgeHook(object SeonBridge, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *SeonBridgeHook {
	return &SeonBridgeHook{
		SeonBridge:  object,
		_beforeCall: beforeCall,
		_postCall:   postCall,
		_onPanic:    onPanic,
	}
}
