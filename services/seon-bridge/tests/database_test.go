// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package tests

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/db"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/mongodb"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/pg"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	openDBTryLimit    = 10
	openDBTryInterval = 3 * time.Second
)

// CreateTestDB The function checks if the database name contains "test" and returns an error if it doesn't.
// It then calls the dropDatabase function to drop any existing database with the same name,
// and finally calls createDatabase function to create a new database with the given configuration.
func (s *Suite) CreateTestDB() (*sql.DB, error) {
	logger := logs.FromContext(s.ctx)

	// Check if the database name contains "test" to ensure we're not accidentally using a production database
	if !strings.Contains(s.cfg.PostgresDB.Database, "test") {
		return nil, fmt.Errorf("database name must contain 'test'")
	}

	for i := 0; i < openDBTryLimit; i++ {
		logger.Debug().Msgf("Attempting to drop database (attempt %d/%d)...", i+1, openDBTryLimit)
		err := dropDatabase(s.cfg.PostgresDB, s.ctx)
		if err == nil {
			logger.Debug().Msg("Successfully dropped database")
			break
		}
		logger.Warn().Err(err).Msgf("Failed to drop database. Retrying in %v...", openDBTryInterval)

		time.Sleep(openDBTryInterval)
	}

	var conn *sql.DB
	var err error

	for i := 0; i < openDBTryLimit; i++ {
		logger.Debug().Msgf("Attempting to connect to database (attempt %d/%d)...", i+1, openDBTryLimit)
		conn, err = db.DefaultConnect(s.cfg.PostgresDB)
		if err == nil {
			logger.Debug().Msg("Successfully connected to database")
			break
		}
		logger.Warn().Err(err).Msgf("Failed to connect to database. Retrying in %v...", openDBTryInterval)

		time.Sleep(openDBTryInterval)
	}

	logger.Debug().Msgf("Creating database %s...", s.cfg.PostgresDB.Database)
	err = pg.DatabaseCreate(conn, s.cfg.PostgresDB.Database)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to create database")
	} else {
		logger.Debug().Msgf("Successfully created database %s", s.cfg.PostgresDB.Database)
	}

	return conn, nil
}

// TruncateDatabase truncates all tables in the test database.
// It constructs a SQL query to truncate the tables with a CASCADE option,
// ensuring that all dependent objects are also truncated.
// This is useful for cleaning up the database state between tests.
func (s *Suite) TruncateDatabase() error {
	tx, err := s.dbConn.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer func() {
		if tx != nil {
			_ = tx.Rollback()
		}
	}()

	rows, err := tx.Query(`
		SELECT tablename
		FROM pg_tables
		WHERE schemaname = 'public'
	`)
	if err != nil {
		return fmt.Errorf("failed to list tables: %w", err)
	}

	var tables []string
	for rows.Next() {
		var table string
		if err := rows.Scan(&table); err != nil {
			return fmt.Errorf("failed to scan table name: %w", err)
		}
		tables = append(tables, table)
	}

	if err = rows.Err(); err != nil {
		return err
	}

	rows.Close()

	for _, table := range tables {
		query := fmt.Sprintf(`TRUNCATE "%s" CASCADE;`, table)
		if _, err := tx.Exec(query); err != nil {
			return fmt.Errorf("failed to truncate table %s: %w", table, err)
		}
	}

	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	tx = nil

	return nil
}

// The dropDatabase function connects to the database using the provided configuration and drops any existing database
// with the specified name using the DatabaseDrop function.
func dropDatabase(cfg *db.Config, ctx context.Context) error {
	logger := logs.FromContext(ctx)

	logger.Debug().Msgf("Connecting to database to drop %s...", cfg.Database)
	conn, err := db.DefaultConnect(cfg)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to connect to database")
		return err
	}
	logger.Debug().Msg("Successfully connected to database")
	defer func() { _ = conn.Close() }()

	logger.Debug().Msgf("Executing DROP DATABASE IF EXISTS %s", cfg.Database)
	query := fmt.Sprintf(`DROP DATABASE IF EXISTS %s`, cfg.Database)
	if _, err := conn.Exec(query); err != nil {
		logger.Error().Err(err).Msgf("Failed to drop database %s", cfg.Database)
		return fmt.Errorf("can't drop database %s: %w", cfg.Database, err)
	}
	logger.Debug().Msg("Successfully executed DROP DATABASE command")

	return nil
}
func (s *Suite) TruncateMongoDB() error {
	// Check if the database name contains "test" to ensure we're not accidentally using a production database
	if !strings.Contains(s.cfg.MongoDB.Database, "test") {
		return fmt.Errorf("MongoDB database name must contain 'test'")
	}

	return clearMongoDB(s.mongoClient, s.cfg.MongoDB.Database)
}

func clearMongoDB(client *mongo.Client, dbName string) error {
	return mongodb.DatabaseClear(client, dbName)
}
