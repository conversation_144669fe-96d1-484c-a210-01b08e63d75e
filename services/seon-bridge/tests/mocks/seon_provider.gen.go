// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/pkg/seon (interfaces: SeonProvider)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"

	seon "git.redmadrobot.com/zaman/backend/zaman/pkg/seon"
	utils "git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
)

// MockSeonProvider is a mock of SeonProvider interface.
type MockSeonProvider struct {
	ctrl     *gomock.Controller
	recorder *MockSeonProviderMockRecorder
}

// MockSeonProviderMockRecorder is the mock recorder for MockSeonProvider.
type MockSeonProviderMockRecorder struct {
	mock *MockSeonProvider
}

// NewMockSeonProvider creates a new mock instance.
func NewMockSeonProvider(ctrl *gomock.Controller) *MockSeonProvider {
	mock := &MockSeonProvider{ctrl: ctrl}
	mock.recorder = &MockSeonProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSeonProvider) EXPECT() *MockSeonProviderMockRecorder {
	return m.recorder
}

// GetFraudData mocks base method.
func (m *MockSeonProvider) GetFraudData(arg0 context.Context, arg1 seon.GetFraudDataReq) (*seon.FraudAPIResponse, *utils.RequestLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFraudData", arg0, arg1)
	ret0, _ := ret[0].(*seon.FraudAPIResponse)
	ret1, _ := ret[1].(*utils.RequestLog)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetFraudData indicates an expected call of GetFraudData.
func (mr *MockSeonProviderMockRecorder) GetFraudData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFraudData", reflect.TypeOf((*MockSeonProvider)(nil).GetFraudData), arg0, arg1)
}
