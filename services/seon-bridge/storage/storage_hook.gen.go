// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package storage

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/seon-bridge/storage -i Storage -t ../../../etc/templates/hook.tmpl -o storage_hook.gen.go -l ""

import (
	"context"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"
)

var _ Storage = (*StorageHook)(nil)

// StorageHook implements Storage interface wrapper
type StorageHook struct {
	Storage
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// Check implements Storage
func (_w *StorageHook) Check(ctx context.Context) (err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.<PERSON>orage, "Check", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "Check", _params)

	err = _w.Storage.Check(_ctx)
	_w._postCall.Hook(_ctx, _w.Storage, "Check", []any{err})
	return err
}

// CreateRequestLogID implements Storage
func (_w *StorageHook) CreateRequestLogID(ctx context.Context, externalID string) (i1 int64, err error) {
	_params := []any{ctx, externalID}
	defer _w._onPanic.Hook(_w.Storage, "CreateRequestLogID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "CreateRequestLogID", _params)

	i1, err = _w.Storage.CreateRequestLogID(_ctx, externalID)
	_w._postCall.Hook(_ctx, _w.Storage, "CreateRequestLogID", []any{i1, err})
	return i1, err
}

// SaveRawData implements Storage
func (_w *StorageHook) SaveRawData(ctx context.Context, data interface{}, collection string) (s1 string, err error) {
	_params := []any{ctx, data, collection}
	defer _w._onPanic.Hook(_w.Storage, "SaveRawData", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "SaveRawData", _params)

	s1, err = _w.Storage.SaveRawData(_ctx, data, collection)
	_w._postCall.Hook(_ctx, _w.Storage, "SaveRawData", []any{s1, err})
	return s1, err
}

// NewStorageHook returns StorageHook
func NewStorageHook(object Storage, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *StorageHook {
	return &StorageHook{
		Storage:     object,
		_beforeCall: beforeCall,
		_postCall:   postCall,
		_onPanic:    onPanic,
	}
}
