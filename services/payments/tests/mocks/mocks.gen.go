// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package mocks

import (
	"testing"

	"github.com/golang/mock/gomock"
)

type Mocks struct {
	GRPC      GRPC
	Providers Providers
}

type GRPC struct {
	Users            *MockUsersClient
	Colvirbridge     *MockColvirbridgeClient
	Otp              *MockOtpClient
	Dictionary       *MockDictionaryClient
	Amlbridge        *MockAmlbridgeClient
	Documents        *MockDocumentsClient
	Apbridge         *MockApbridgeClient
	Antifraud        *MockAntifraudClient
	Processingbridge *MockProcessingbridgeClient
	Paymentssme      *MockPaymentssmeClient
}

type Providers struct {
}

func NewMocks(t *testing.T) *Mocks {
	return &Mocks{
		GRPC: GRPC{
			Users:            NewMockUsersClient(gomock.NewController(t)),
			Colvirbridge:     NewMockColvirbridgeClient(gomock.NewController(t)),
			Otp:              NewMockOtpClient(gomock.NewController(t)),
			Dictionary:       NewMockDictionaryClient(gomock.NewController(t)),
			Amlbridge:        NewMockAmlbridgeClient(gomock.NewController(t)),
			Documents:        NewMockDocumentsClient(gomock.NewController(t)),
			Apbridge:         NewMockApbridgeClient(gomock.NewController(t)),
			Antifraud:        NewMockAntifraudClient(gomock.NewController(t)),
			Processingbridge: NewMockProcessingbridgeClient(gomock.NewController(t)),
			Paymentssme:      NewMockPaymentssmeClient(gomock.NewController(t)),
		},
		Providers: Providers{},
	}
}
