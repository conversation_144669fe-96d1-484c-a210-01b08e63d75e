// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/specs/proto/payments-sme (interfaces: PaymentssmeClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"

	payments_sme "git.redmadrobot.com/zaman/backend/zaman/specs/proto/payments-sme"
)

// MockPaymentssmeClient is a mock of PaymentssmeClient interface.
type MockPaymentssmeClient struct {
	ctrl     *gomock.Controller
	recorder *MockPaymentssmeClientMockRecorder
}

// MockPaymentssmeClientMockRecorder is the mock recorder for MockPaymentssmeClient.
type MockPaymentssmeClientMockRecorder struct {
	mock *MockPaymentssmeClient
}

// NewMockPaymentssmeClient creates a new mock instance.
func NewMockPaymentssmeClient(ctrl *gomock.Controller) *MockPaymentssmeClient {
	mock := &MockPaymentssmeClient{ctrl: ctrl}
	mock.recorder = &MockPaymentssmeClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPaymentssmeClient) EXPECT() *MockPaymentssmeClientMockRecorder {
	return m.recorder
}

// ConfirmPaymentSme mocks base method.
func (m *MockPaymentssmeClient) ConfirmPaymentSme(arg0 context.Context, arg1 *payments_sme.ConfirmPaymentSmeReq, arg2 ...grpc.CallOption) (*payments_sme.ConfirmPaymentSmeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConfirmPaymentSme", varargs...)
	ret0, _ := ret[0].(*payments_sme.ConfirmPaymentSmeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmPaymentSme indicates an expected call of ConfirmPaymentSme.
func (mr *MockPaymentssmeClientMockRecorder) ConfirmPaymentSme(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmPaymentSme", reflect.TypeOf((*MockPaymentssmeClient)(nil).ConfirmPaymentSme), varargs...)
}

// CreateEmployee mocks base method.
func (m *MockPaymentssmeClient) CreateEmployee(arg0 context.Context, arg1 *payments_sme.CreateEmployeeReq, arg2 ...grpc.CallOption) (*payments_sme.EmployeeInfo, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateEmployee", varargs...)
	ret0, _ := ret[0].(*payments_sme.EmployeeInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateEmployee indicates an expected call of CreateEmployee.
func (mr *MockPaymentssmeClientMockRecorder) CreateEmployee(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateEmployee", reflect.TypeOf((*MockPaymentssmeClient)(nil).CreateEmployee), varargs...)
}

// CreatePayment mocks base method.
func (m *MockPaymentssmeClient) CreatePayment(arg0 context.Context, arg1 *payments_sme.CreatePaymentReq, arg2 ...grpc.CallOption) (*payments_sme.CreatePaymentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreatePayment", varargs...)
	ret0, _ := ret[0].(*payments_sme.CreatePaymentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePayment indicates an expected call of CreatePayment.
func (mr *MockPaymentssmeClientMockRecorder) CreatePayment(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePayment", reflect.TypeOf((*MockPaymentssmeClient)(nil).CreatePayment), varargs...)
}

// DeleteEmployee mocks base method.
func (m *MockPaymentssmeClient) DeleteEmployee(arg0 context.Context, arg1 *payments_sme.DeleteEmployeeReq, arg2 ...grpc.CallOption) (*payments_sme.DeleteEmployeeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteEmployee", varargs...)
	ret0, _ := ret[0].(*payments_sme.DeleteEmployeeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteEmployee indicates an expected call of DeleteEmployee.
func (mr *MockPaymentssmeClientMockRecorder) DeleteEmployee(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteEmployee", reflect.TypeOf((*MockPaymentssmeClient)(nil).DeleteEmployee), varargs...)
}

// GetBankList mocks base method.
func (m *MockPaymentssmeClient) GetBankList(arg0 context.Context, arg1 *payments_sme.GetBankListReq, arg2 ...grpc.CallOption) (*payments_sme.GetBankListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBankList", varargs...)
	ret0, _ := ret[0].(*payments_sme.GetBankListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankList indicates an expected call of GetBankList.
func (mr *MockPaymentssmeClientMockRecorder) GetBankList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankList", reflect.TypeOf((*MockPaymentssmeClient)(nil).GetBankList), varargs...)
}

// GetCountryList mocks base method.
func (m *MockPaymentssmeClient) GetCountryList(arg0 context.Context, arg1 *payments_sme.GetCountryListReq, arg2 ...grpc.CallOption) (*payments_sme.GetCountryListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCountryList", varargs...)
	ret0, _ := ret[0].(*payments_sme.GetCountryListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCountryList indicates an expected call of GetCountryList.
func (mr *MockPaymentssmeClientMockRecorder) GetCountryList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCountryList", reflect.TypeOf((*MockPaymentssmeClient)(nil).GetCountryList), varargs...)
}

// GetEmployeeList mocks base method.
func (m *MockPaymentssmeClient) GetEmployeeList(arg0 context.Context, arg1 *payments_sme.GetEmployeeListReq, arg2 ...grpc.CallOption) (*payments_sme.GetEmployeeListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEmployeeList", varargs...)
	ret0, _ := ret[0].(*payments_sme.GetEmployeeListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEmployeeList indicates an expected call of GetEmployeeList.
func (mr *MockPaymentssmeClientMockRecorder) GetEmployeeList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEmployeeList", reflect.TypeOf((*MockPaymentssmeClient)(nil).GetEmployeeList), varargs...)
}

// GetKbeKodList mocks base method.
func (m *MockPaymentssmeClient) GetKbeKodList(arg0 context.Context, arg1 *payments_sme.GetKbeKodListReq, arg2 ...grpc.CallOption) (*payments_sme.GetKbeKodListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetKbeKodList", varargs...)
	ret0, _ := ret[0].(*payments_sme.GetKbeKodListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetKbeKodList indicates an expected call of GetKbeKodList.
func (mr *MockPaymentssmeClientMockRecorder) GetKbeKodList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKbeKodList", reflect.TypeOf((*MockPaymentssmeClient)(nil).GetKbeKodList), varargs...)
}

// GetKbkList mocks base method.
func (m *MockPaymentssmeClient) GetKbkList(arg0 context.Context, arg1 *payments_sme.GetKbkListReq, arg2 ...grpc.CallOption) (*payments_sme.GetKbkListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetKbkList", varargs...)
	ret0, _ := ret[0].(*payments_sme.GetKbkListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetKbkList indicates an expected call of GetKbkList.
func (mr *MockPaymentssmeClientMockRecorder) GetKbkList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKbkList", reflect.TypeOf((*MockPaymentssmeClient)(nil).GetKbkList), varargs...)
}

// GetKnpList mocks base method.
func (m *MockPaymentssmeClient) GetKnpList(arg0 context.Context, arg1 *payments_sme.GetKnpListReq, arg2 ...grpc.CallOption) (*payments_sme.GetKnpListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetKnpList", varargs...)
	ret0, _ := ret[0].(*payments_sme.GetKnpListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetKnpList indicates an expected call of GetKnpList.
func (mr *MockPaymentssmeClientMockRecorder) GetKnpList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKnpList", reflect.TypeOf((*MockPaymentssmeClient)(nil).GetKnpList), varargs...)
}

// GetTaxAuthorityList mocks base method.
func (m *MockPaymentssmeClient) GetTaxAuthorityList(arg0 context.Context, arg1 *payments_sme.GetTaxAuthorityListReq, arg2 ...grpc.CallOption) (*payments_sme.GetTaxAuthorityListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTaxAuthorityList", varargs...)
	ret0, _ := ret[0].(*payments_sme.GetTaxAuthorityListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaxAuthorityList indicates an expected call of GetTaxAuthorityList.
func (mr *MockPaymentssmeClientMockRecorder) GetTaxAuthorityList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaxAuthorityList", reflect.TypeOf((*MockPaymentssmeClient)(nil).GetTaxAuthorityList), varargs...)
}

// HealthCheck mocks base method.
func (m *MockPaymentssmeClient) HealthCheck(arg0 context.Context, arg1 *payments_sme.HealthCheckReq, arg2 ...grpc.CallOption) (*payments_sme.HealthCheckResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HealthCheck", varargs...)
	ret0, _ := ret[0].(*payments_sme.HealthCheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockPaymentssmeClientMockRecorder) HealthCheck(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockPaymentssmeClient)(nil).HealthCheck), varargs...)
}

// SmePaymentsClient mocks base method.
func (m *MockPaymentssmeClient) SmePaymentsClient(arg0 context.Context, arg1 *payments_sme.SmePaymentsClientReq, arg2 ...grpc.CallOption) (*payments_sme.SmePaymentsClientResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SmePaymentsClient", varargs...)
	ret0, _ := ret[0].(*payments_sme.SmePaymentsClientResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SmePaymentsClient indicates an expected call of SmePaymentsClient.
func (mr *MockPaymentssmeClientMockRecorder) SmePaymentsClient(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SmePaymentsClient", reflect.TypeOf((*MockPaymentssmeClient)(nil).SmePaymentsClient), varargs...)
}

// SmePaymentsCreateOtp mocks base method.
func (m *MockPaymentssmeClient) SmePaymentsCreateOtp(arg0 context.Context, arg1 *payments_sme.SmePaymentsCreateOtpReq, arg2 ...grpc.CallOption) (*payments_sme.SmePaymentsCreateOtpResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SmePaymentsCreateOtp", varargs...)
	ret0, _ := ret[0].(*payments_sme.SmePaymentsCreateOtpResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SmePaymentsCreateOtp indicates an expected call of SmePaymentsCreateOtp.
func (mr *MockPaymentssmeClientMockRecorder) SmePaymentsCreateOtp(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SmePaymentsCreateOtp", reflect.TypeOf((*MockPaymentssmeClient)(nil).SmePaymentsCreateOtp), varargs...)
}

// SmePaymentsGetPaymentOrder mocks base method.
func (m *MockPaymentssmeClient) SmePaymentsGetPaymentOrder(arg0 context.Context, arg1 *payments_sme.SmePaymentsGetPaymentOrderReq, arg2 ...grpc.CallOption) (*payments_sme.SmePaymentsGetPaymentOrderResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SmePaymentsGetPaymentOrder", varargs...)
	ret0, _ := ret[0].(*payments_sme.SmePaymentsGetPaymentOrderResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SmePaymentsGetPaymentOrder indicates an expected call of SmePaymentsGetPaymentOrder.
func (mr *MockPaymentssmeClientMockRecorder) SmePaymentsGetPaymentOrder(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SmePaymentsGetPaymentOrder", reflect.TypeOf((*MockPaymentssmeClient)(nil).SmePaymentsGetPaymentOrder), varargs...)
}

// SmePaymentsGetPaymentOrderByTrNumber mocks base method.
func (m *MockPaymentssmeClient) SmePaymentsGetPaymentOrderByTrNumber(arg0 context.Context, arg1 *payments_sme.SmePaymentsGetPaymentOrderByTrNumberReq, arg2 ...grpc.CallOption) (*payments_sme.SmePaymentsGetPaymentOrderByTrNumberResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SmePaymentsGetPaymentOrderByTrNumber", varargs...)
	ret0, _ := ret[0].(*payments_sme.SmePaymentsGetPaymentOrderByTrNumberResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SmePaymentsGetPaymentOrderByTrNumber indicates an expected call of SmePaymentsGetPaymentOrderByTrNumber.
func (mr *MockPaymentssmeClientMockRecorder) SmePaymentsGetPaymentOrderByTrNumber(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SmePaymentsGetPaymentOrderByTrNumber", reflect.TypeOf((*MockPaymentssmeClient)(nil).SmePaymentsGetPaymentOrderByTrNumber), varargs...)
}

// SmePaymentsOtpResend mocks base method.
func (m *MockPaymentssmeClient) SmePaymentsOtpResend(arg0 context.Context, arg1 *payments_sme.SmePaymentsOtpResendReq, arg2 ...grpc.CallOption) (*payments_sme.SmePaymentsOtpResendResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SmePaymentsOtpResend", varargs...)
	ret0, _ := ret[0].(*payments_sme.SmePaymentsOtpResendResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SmePaymentsOtpResend indicates an expected call of SmePaymentsOtpResend.
func (mr *MockPaymentssmeClientMockRecorder) SmePaymentsOtpResend(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SmePaymentsOtpResend", reflect.TypeOf((*MockPaymentssmeClient)(nil).SmePaymentsOtpResend), varargs...)
}

// SmePaymentsOtpValidate mocks base method.
func (m *MockPaymentssmeClient) SmePaymentsOtpValidate(arg0 context.Context, arg1 *payments_sme.SmePaymentsOtpValidateReq, arg2 ...grpc.CallOption) (*payments_sme.SmePaymentsOtpValidateResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SmePaymentsOtpValidate", varargs...)
	ret0, _ := ret[0].(*payments_sme.SmePaymentsOtpValidateResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SmePaymentsOtpValidate indicates an expected call of SmePaymentsOtpValidate.
func (mr *MockPaymentssmeClientMockRecorder) SmePaymentsOtpValidate(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SmePaymentsOtpValidate", reflect.TypeOf((*MockPaymentssmeClient)(nil).SmePaymentsOtpValidate), varargs...)
}

// SmePaymentsWorktime mocks base method.
func (m *MockPaymentssmeClient) SmePaymentsWorktime(arg0 context.Context, arg1 *payments_sme.SmePaymentsWorktimeReq, arg2 ...grpc.CallOption) (*payments_sme.SmePaymentsWorktimeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SmePaymentsWorktime", varargs...)
	ret0, _ := ret[0].(*payments_sme.SmePaymentsWorktimeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SmePaymentsWorktime indicates an expected call of SmePaymentsWorktime.
func (mr *MockPaymentssmeClientMockRecorder) SmePaymentsWorktime(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SmePaymentsWorktime", reflect.TypeOf((*MockPaymentssmeClient)(nil).SmePaymentsWorktime), varargs...)
}

// UpdateEmployee mocks base method.
func (m *MockPaymentssmeClient) UpdateEmployee(arg0 context.Context, arg1 *payments_sme.UpdateEmployeeReq, arg2 ...grpc.CallOption) (*payments_sme.EmployeeInfo, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateEmployee", varargs...)
	ret0, _ := ret[0].(*payments_sme.EmployeeInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateEmployee indicates an expected call of UpdateEmployee.
func (mr *MockPaymentssmeClientMockRecorder) UpdateEmployee(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEmployee", reflect.TypeOf((*MockPaymentssmeClient)(nil).UpdateEmployee), varargs...)
}
