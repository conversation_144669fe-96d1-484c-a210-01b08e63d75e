package entity

import "git.redmadrobot.com/zaman/backend/zaman/pkg/helpers/locale"

type TransactionReceiptType string

const (
	PaymentByAccountReceiptTypeRu             TransactionReceiptType = "paymentByAccountReceiptRu"
	PaymentByAccountReceiptTypeKz             TransactionReceiptType = "paymentByAccountReceiptKz"
	PaymentForMobileReceiptTypeRu             TransactionReceiptType = "paymentForMobileReceiptRu"
	PaymentForMobileReceiptTypeKz             TransactionReceiptType = "paymentForMobileReceiptKz"
	InternalPaymentByPhoneNumberReceiptTypeRu TransactionReceiptType = "internalPaymentByPhoneNumberReceiptRu"
	InternalPaymentByPhoneNumberReceiptTypeKz TransactionReceiptType = "internalPaymentByPhoneNumberReceiptKz"
	PaymentSmeReceiptTypeRu                   TransactionReceiptType = "paymentSmeReceiptTypeRu"
	PaymentSmeReceiptTypeKz                   TransactionReceiptType = "paymentSmeReceiptTypeKz"
)

func MakeTransactionReceiptTypeFromLocale(
	l locale.Locale,
	transactionType TransactionType,
) TransactionReceiptType {
	if l == locale.Ru {
		switch transactionType {
		default:
			fallthrough
		case TransactionTypePaymentByAccount:
			return PaymentByAccountReceiptTypeRu
		case TransactionTypePaymentMobile:
			return PaymentForMobileReceiptTypeRu
		case TransactionTypeInternalPaymentByPhoneNumber:
			return InternalPaymentByPhoneNumberReceiptTypeRu
		}
	}

	switch transactionType {
	default:
		fallthrough
	case TransactionTypePaymentByAccount:
		return PaymentByAccountReceiptTypeKz
	case TransactionTypePaymentMobile:
		return PaymentForMobileReceiptTypeKz
	case TransactionTypeInternalPaymentByPhoneNumber:
		return InternalPaymentByPhoneNumberReceiptTypeKz
	}
}

type TransactionReceipt struct {
	ID                string                 `json:"id"`
	Title             string                 `json:"title"`
	Type              TransactionReceiptType `json:"type"`
	Version           int32                  `json:"version"`
	FileLink          string                 `json:"file_link"`
	TransactionStatus TransactionStatus      `json:"transaction_status"`
}
