package usecase

import (
	"context"
	"errors"
	"slices"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"github.com/google/uuid"

	errs "git.redmadrobot.com/zaman/backend/zaman/errs/payments"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/helpers/locale"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	paymentssmeconfigs "git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/consts/configs"
	paymentssmeentity "git.redmadrobot.com/zaman/backend/zaman/services/payments-sme/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/payments/entity"
	"git.redmadrobot.com/zaman/backend/zaman/specs/proto/documents"
	paymentssmepb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/payments-sme"
	"git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"
)

func (u *useCasesImpl) GetTransactionReceipt(
	ctx context.Context, req *entity.GetTransactionReceiptReq,
) (*entity.GetTransactionReceiptResult, error) {
	transactionID, err := uuid.Parse(req.TransactionID)
	if err != nil {
		return nil, errs.PaymentsErrs().CodeInvalidTransactionIDError()
	}

	userID, err := utils.GetUserIDFromContext(ctx)
	if err != nil {
		return nil, err
	}

	userInfo, err := u.Providers.Users.GetUserByID(ctx, &users.GetUserByIDReq{
		ID: userID,
	})
	if err != nil {
		return nil, err
	}

	loc, err := utils.GetLocaleFromContext(ctx, true, true)
	if err != nil {
		loc = locale.Kk
	}

	transaction, err := u.Providers.Storage.GetTransactionByID(ctx, transactionID, userInfo.Iin)
	if err != nil {
		return nil, err
	}

	receiptDocType := entity.MakeTransactionReceiptTypeFromLocale(loc, transaction.TransactionType)
	var (
		receipt    *entity.TransactionReceipt
		receiptIdx = -1
	)

	for i, r := range transaction.TransactionDetails.Receipts {
		if r.Type != receiptDocType || r.TransactionStatus != transaction.Status {
			continue
		}

		receipt = &r
		receiptIdx = i
	}

	// Квитанции есть и со статусом всё в порядке
	if receipt != nil {
		return &entity.GetTransactionReceiptResult{
			Title: receipt.Title,
			Link:  receipt.FileLink,
		}, nil
	}

	newReceipt, err := u.generateTransactionReceipt(ctx, loc, transaction)
	if err != nil {
		return nil, err
	}

	if receipt == nil {
		transaction.TransactionDetails.Receipts = append(transaction.TransactionDetails.Receipts, newReceipt)
	} else {
		transaction.TransactionDetails.Receipts = append(
			transaction.TransactionDetails.Receipts[:receiptIdx],
			transaction.TransactionDetails.Receipts[receiptIdx+1:]...,
		)

		transaction.TransactionDetails.Receipts = append(transaction.TransactionDetails.Receipts, newReceipt)
	}

	// Update transaction
	if err := u.Providers.Storage.UpdateTransaction(ctx, transaction); err != nil {
		return nil, err
	}

	return &entity.GetTransactionReceiptResult{
		Title: newReceipt.Title,
		Link:  newReceipt.FileLink,
	}, nil
}

func (u *useCasesImpl) generateTransactionReceipt(
	ctx context.Context, l locale.Locale, transaction *entity.Transaction,
) (entity.TransactionReceipt, error) {
	origin, err := utils.GetOrigin(ctx)
	if err != nil {
		logs.FromContext(ctx).Err(err).Msg("failed parse origin from context")
		return entity.TransactionReceipt{}, err
	}

	var result *documents.DocumentResp
	switch origin {
	case utils.UserOriginMobile:
		switch transaction.TransactionType {
		case entity.TransactionTypePaymentByAccount:
			result, err = u.Providers.Documents.GeneratePaymentByAccountReceipt(
				ctx, transaction.ToGeneratePaymentByAccountReceiptReq(l),
			)
		case entity.TransactionTypePaymentMobile:
			result, err = u.Providers.Documents.GeneratePaymentForMobileReceipt(
				ctx, transaction.ToGeneratePaymentForMobileReceiptReq(l),
			)
		case entity.TransactionTypeInternalPaymentByPhoneNumber:
			result, err = u.Providers.Documents.GenerateInternalPaymentByPhoneNumberReceipt(
				ctx, transaction.ToGenerateInternalPaymentByPhoneNumberReceiptReq(l),
			)
		default:
			err = errs.PaymentsErrs().CodeTransactionTypeNotAvailableForReceiptError()
		}
	case utils.UserOriginSme:
		// TODO: Удалить этот костыль, когда Payments&Transfers SME напишут свой сервис для истории операций
		// Для SME-платежей квитанция формируется в отдельном сервисе payments-sme
		if isSmePaymentTransaction(transaction) {
			return u.generateSmePaymentOrder(ctx, l, transaction.TransactionNumber)
		}

		result, err = u.Providers.Documents.GeneratePaymentByAccountSmeReceipt(
			ctx, transaction.ToGeneratePaymentByAccountSmeReceiptReq(l),
		)
	default:
		err = errors.New("unknown origin")
	}

	if err != nil {
		return entity.TransactionReceipt{}, err
	}

	return entity.TransactionReceipt{
		ID:                result.Id,
		Title:             result.Title,
		Type:              entity.TransactionReceiptType(result.Type),
		Version:           result.Version,
		FileLink:          result.Link,
		TransactionStatus: transaction.Status,
	}, nil
}

// generateSmePaymentOrder вызывает внешний сервис payments-sme для получения информации о платежном поручении
// по номеру транзакции
func (u *useCasesImpl) generateSmePaymentOrder(
	ctx context.Context,
	l locale.Locale,
	transactionNumber string,
) (entity.TransactionReceipt, error) {
	// Вызов внешнего сервиса payments-sme для получения информации о платежном поручении по номеру транзакции
	smePaymentOrderPb, err := u.Providers.Paymentssme.SmePaymentsGetPaymentOrderByTrNumber(ctx, &paymentssmepb.SmePaymentsGetPaymentOrderByTrNumberReq{
		TransactionNumber: transactionNumber,
	})
	if err != nil {
		return entity.TransactionReceipt{}, err
	}
	// Преобразование ответа из pb в entity
	smePaymentOrder := paymentssmeentity.MakeSmePaymentsGetPaymentOrderByTrNumberRespPbToEntity(smePaymentOrderPb)
	// Определение типа квитанции в зависимости от локали
	transactionReceiptType := entity.PaymentSmeReceiptTypeRu
	if l == locale.Kk {
		transactionReceiptType = entity.PaymentSmeReceiptTypeKz
	}
	// Формирование и возврат результата
	return entity.TransactionReceipt{
		ID:                smePaymentOrder.ID,
		Title:             smePaymentOrder.Title,
		Type:              transactionReceiptType,
		FileLink:          smePaymentOrder.Link,
		TransactionStatus: entity.TransactionStatus(smePaymentOrder.TransactionStatus),
	}, nil
}

// isSmePaymentTransaction определяет, является ли транзакция SME-платежом
// SME-платежи - это платежи с кодами назначения из конфигурации SmeKnpCodes,
// и при этом БИК Банка-контрагент должен быть либо GCVPKZ2A, либо KKMFKZ2A
func isSmePaymentTransaction(transaction *entity.Transaction) bool {
	return slices.Contains(paymentssmeconfigs.GetSmeKnpCodes(), transaction.PurposeCode) &&
		(transaction.TransactionDetails.CounterpartyData.BankBic == paymentssmeconfigs.NAOGov4CBankBic ||
			transaction.TransactionDetails.CounterpartyData.BankBic == paymentssmeconfigs.KKMFBankBic)
}
