// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/specs/proto/loans (interfaces: LoansClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"

	loans "git.redmadrobot.com/zaman/backend/zaman/specs/proto/loans"
)

// MockLoansClient is a mock of LoansClient interface.
type MockLoansClient struct {
	ctrl     *gomock.Controller
	recorder *MockLoansClientMockRecorder
}

// MockLoansClientMockRecorder is the mock recorder for MockLoansClient.
type MockLoansClientMockRecorder struct {
	mock *MockLoansClient
}

// NewMockLoansClient creates a new mock instance.
func NewMockLoansClient(ctrl *gomock.Controller) *MockLoansClient {
	mock := &MockLoansClient{ctrl: ctrl}
	mock.recorder = &MockLoansClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLoansClient) EXPECT() *MockLoansClientMockRecorder {
	return m.recorder
}

// CalculateLoanTerms mocks base method.
func (m *MockLoansClient) CalculateLoanTerms(arg0 context.Context, arg1 *loans.CalculateCreditReq, arg2 ...grpc.CallOption) (*loans.CalculateCreditResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CalculateLoanTerms", varargs...)
	ret0, _ := ret[0].(*loans.CalculateCreditResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculateLoanTerms indicates an expected call of CalculateLoanTerms.
func (mr *MockLoansClientMockRecorder) CalculateLoanTerms(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculateLoanTerms", reflect.TypeOf((*MockLoansClient)(nil).CalculateLoanTerms), varargs...)
}

// CancelLoanApplication mocks base method.
func (m *MockLoansClient) CancelLoanApplication(arg0 context.Context, arg1 *loans.CancelLoanApplicationReq, arg2 ...grpc.CallOption) (*loans.CancelLoanApplicationResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CancelLoanApplication", varargs...)
	ret0, _ := ret[0].(*loans.CancelLoanApplicationResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelLoanApplication indicates an expected call of CancelLoanApplication.
func (mr *MockLoansClientMockRecorder) CancelLoanApplication(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelLoanApplication", reflect.TypeOf((*MockLoansClient)(nil).CancelLoanApplication), varargs...)
}

// ChangeDisbursementControl mocks base method.
func (m *MockLoansClient) ChangeDisbursementControl(arg0 context.Context, arg1 *loans.ChangeDisbursementControlReq, arg2 ...grpc.CallOption) (*loans.ChangeDisbursementControlResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ChangeDisbursementControl", varargs...)
	ret0, _ := ret[0].(*loans.ChangeDisbursementControlResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChangeDisbursementControl indicates an expected call of ChangeDisbursementControl.
func (mr *MockLoansClientMockRecorder) ChangeDisbursementControl(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChangeDisbursementControl", reflect.TypeOf((*MockLoansClient)(nil).ChangeDisbursementControl), varargs...)
}

// CheckActiveLoanAppExists mocks base method.
func (m *MockLoansClient) CheckActiveLoanAppExists(arg0 context.Context, arg1 *loans.CheckActiveLoanAppExistsReq, arg2 ...grpc.CallOption) (*loans.CheckActiveLoanAppExistsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckActiveLoanAppExists", varargs...)
	ret0, _ := ret[0].(*loans.CheckActiveLoanAppExistsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckActiveLoanAppExists indicates an expected call of CheckActiveLoanAppExists.
func (mr *MockLoansClientMockRecorder) CheckActiveLoanAppExists(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckActiveLoanAppExists", reflect.TypeOf((*MockLoansClient)(nil).CheckActiveLoanAppExists), varargs...)
}

// ConfirmSignDocument mocks base method.
func (m *MockLoansClient) ConfirmSignDocument(arg0 context.Context, arg1 *loans.ConfirmSignDocumentReq, arg2 ...grpc.CallOption) (*loans.ConfirmSignDocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConfirmSignDocument", varargs...)
	ret0, _ := ret[0].(*loans.ConfirmSignDocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmSignDocument indicates an expected call of ConfirmSignDocument.
func (mr *MockLoansClientMockRecorder) ConfirmSignDocument(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmSignDocument", reflect.TypeOf((*MockLoansClient)(nil).ConfirmSignDocument), varargs...)
}

// CreateLoanApplication mocks base method.
func (m *MockLoansClient) CreateLoanApplication(arg0 context.Context, arg1 *loans.CreateLoanApplicationReq, arg2 ...grpc.CallOption) (*loans.CreateLoanApplicationResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateLoanApplication", varargs...)
	ret0, _ := ret[0].(*loans.CreateLoanApplicationResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateLoanApplication indicates an expected call of CreateLoanApplication.
func (mr *MockLoansClientMockRecorder) CreateLoanApplication(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLoanApplication", reflect.TypeOf((*MockLoansClient)(nil).CreateLoanApplication), varargs...)
}

// GenerateLoanAppDocuments mocks base method.
func (m *MockLoansClient) GenerateLoanAppDocuments(arg0 context.Context, arg1 *loans.GenerateLoanAppDocumentsReq, arg2 ...grpc.CallOption) (*loans.GenerateLoanAppDocumentsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateLoanAppDocuments", varargs...)
	ret0, _ := ret[0].(*loans.GenerateLoanAppDocumentsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateLoanAppDocuments indicates an expected call of GenerateLoanAppDocuments.
func (mr *MockLoansClientMockRecorder) GenerateLoanAppDocuments(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateLoanAppDocuments", reflect.TypeOf((*MockLoansClient)(nil).GenerateLoanAppDocuments), varargs...)
}

// GetApprovedLoanAppStatus mocks base method.
func (m *MockLoansClient) GetApprovedLoanAppStatus(arg0 context.Context, arg1 *loans.GetApprovedLoanAppStatusReq, arg2 ...grpc.CallOption) (*loans.GetApprovedLoanAppStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetApprovedLoanAppStatus", varargs...)
	ret0, _ := ret[0].(*loans.GetApprovedLoanAppStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApprovedLoanAppStatus indicates an expected call of GetApprovedLoanAppStatus.
func (mr *MockLoansClientMockRecorder) GetApprovedLoanAppStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApprovedLoanAppStatus", reflect.TypeOf((*MockLoansClient)(nil).GetApprovedLoanAppStatus), varargs...)
}

// GetBanksForStatement mocks base method.
func (m *MockLoansClient) GetBanksForStatement(arg0 context.Context, arg1 *loans.GetBankStatementReq, arg2 ...grpc.CallOption) (*loans.GetBankStatementResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBanksForStatement", varargs...)
	ret0, _ := ret[0].(*loans.GetBankStatementResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBanksForStatement indicates an expected call of GetBanksForStatement.
func (mr *MockLoansClientMockRecorder) GetBanksForStatement(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBanksForStatement", reflect.TypeOf((*MockLoansClient)(nil).GetBanksForStatement), varargs...)
}

// GetBanksForStatementV2 mocks base method.
func (m *MockLoansClient) GetBanksForStatementV2(arg0 context.Context, arg1 *loans.GetBankStatementV2Req, arg2 ...grpc.CallOption) (*loans.GetBankStatementV2Resp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBanksForStatementV2", varargs...)
	ret0, _ := ret[0].(*loans.GetBankStatementV2Resp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBanksForStatementV2 indicates an expected call of GetBanksForStatementV2.
func (mr *MockLoansClientMockRecorder) GetBanksForStatementV2(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBanksForStatementV2", reflect.TypeOf((*MockLoansClient)(nil).GetBanksForStatementV2), varargs...)
}

// GetBtsData mocks base method.
func (m *MockLoansClient) GetBtsData(arg0 context.Context, arg1 *loans.BtsDataReq, arg2 ...grpc.CallOption) (*loans.BtsDataResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBtsData", varargs...)
	ret0, _ := ret[0].(*loans.BtsDataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBtsData indicates an expected call of GetBtsData.
func (mr *MockLoansClientMockRecorder) GetBtsData(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBtsData", reflect.TypeOf((*MockLoansClient)(nil).GetBtsData), varargs...)
}

// GetCreditOnboardingTexts mocks base method.
func (m *MockLoansClient) GetCreditOnboardingTexts(arg0 context.Context, arg1 *loans.GetCreditOnboardingTextsReq, arg2 ...grpc.CallOption) (*loans.GetCreditOnboardingTextsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCreditOnboardingTexts", varargs...)
	ret0, _ := ret[0].(*loans.GetCreditOnboardingTextsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCreditOnboardingTexts indicates an expected call of GetCreditOnboardingTexts.
func (mr *MockLoansClientMockRecorder) GetCreditOnboardingTexts(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCreditOnboardingTexts", reflect.TypeOf((*MockLoansClient)(nil).GetCreditOnboardingTexts), varargs...)
}

// GetDocumentForSign mocks base method.
func (m *MockLoansClient) GetDocumentForSign(arg0 context.Context, arg1 *loans.GetDocumentForSignReq, arg2 ...grpc.CallOption) (*loans.GetDocumentForSignResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDocumentForSign", varargs...)
	ret0, _ := ret[0].(*loans.GetDocumentForSignResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDocumentForSign indicates an expected call of GetDocumentForSign.
func (mr *MockLoansClientMockRecorder) GetDocumentForSign(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDocumentForSign", reflect.TypeOf((*MockLoansClient)(nil).GetDocumentForSign), varargs...)
}

// GetEducationTypes mocks base method.
func (m *MockLoansClient) GetEducationTypes(arg0 context.Context, arg1 *loans.GetEducationTypesReq, arg2 ...grpc.CallOption) (*loans.GetEducationTypesResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEducationTypes", varargs...)
	ret0, _ := ret[0].(*loans.GetEducationTypesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEducationTypes indicates an expected call of GetEducationTypes.
func (mr *MockLoansClientMockRecorder) GetEducationTypes(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEducationTypes", reflect.TypeOf((*MockLoansClient)(nil).GetEducationTypes), varargs...)
}

// GetEmploymentTypes mocks base method.
func (m *MockLoansClient) GetEmploymentTypes(arg0 context.Context, arg1 *loans.GetEmploymentTypesReq, arg2 ...grpc.CallOption) (*loans.GetEmploymentTypesResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEmploymentTypes", varargs...)
	ret0, _ := ret[0].(*loans.GetEmploymentTypesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEmploymentTypes indicates an expected call of GetEmploymentTypes.
func (mr *MockLoansClientMockRecorder) GetEmploymentTypes(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEmploymentTypes", reflect.TypeOf((*MockLoansClient)(nil).GetEmploymentTypes), varargs...)
}

// GetInternalChecksResult mocks base method.
func (m *MockLoansClient) GetInternalChecksResult(arg0 context.Context, arg1 *loans.GetInternalChecksResultReq, arg2 ...grpc.CallOption) (*loans.GetInternalChecksResultResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetInternalChecksResult", varargs...)
	ret0, _ := ret[0].(*loans.GetInternalChecksResultResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInternalChecksResult indicates an expected call of GetInternalChecksResult.
func (mr *MockLoansClientMockRecorder) GetInternalChecksResult(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInternalChecksResult", reflect.TypeOf((*MockLoansClient)(nil).GetInternalChecksResult), varargs...)
}

// GetLoanApplicationByColvirReferenceID mocks base method.
func (m *MockLoansClient) GetLoanApplicationByColvirReferenceID(arg0 context.Context, arg1 *loans.GetLoanApplicationByColvirReferenceIDReq, arg2 ...grpc.CallOption) (*loans.GetLoanApplicationByColvirReferenceIDResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLoanApplicationByColvirReferenceID", varargs...)
	ret0, _ := ret[0].(*loans.GetLoanApplicationByColvirReferenceIDResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanApplicationByColvirReferenceID indicates an expected call of GetLoanApplicationByColvirReferenceID.
func (mr *MockLoansClientMockRecorder) GetLoanApplicationByColvirReferenceID(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanApplicationByColvirReferenceID", reflect.TypeOf((*MockLoansClient)(nil).GetLoanApplicationByColvirReferenceID), varargs...)
}

// GetLoanCalcData mocks base method.
func (m *MockLoansClient) GetLoanCalcData(arg0 context.Context, arg1 *loans.GetCreditCalcDataReq, arg2 ...grpc.CallOption) (*loans.GetCreditCalcDataResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLoanCalcData", varargs...)
	ret0, _ := ret[0].(*loans.GetCreditCalcDataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanCalcData indicates an expected call of GetLoanCalcData.
func (mr *MockLoansClientMockRecorder) GetLoanCalcData(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanCalcData", reflect.TypeOf((*MockLoansClient)(nil).GetLoanCalcData), varargs...)
}

// GetLoanCalcDataSme mocks base method.
func (m *MockLoansClient) GetLoanCalcDataSme(arg0 context.Context, arg1 *loans.GetCreditCalcDataSmeReq, arg2 ...grpc.CallOption) (*loans.GetCreditCalcDataSmeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLoanCalcDataSme", varargs...)
	ret0, _ := ret[0].(*loans.GetCreditCalcDataSmeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanCalcDataSme indicates an expected call of GetLoanCalcDataSme.
func (mr *MockLoansClientMockRecorder) GetLoanCalcDataSme(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanCalcDataSme", reflect.TypeOf((*MockLoansClient)(nil).GetLoanCalcDataSme), varargs...)
}

// GetLoans mocks base method.
func (m *MockLoansClient) GetLoans(arg0 context.Context, arg1 *loans.GetLoansReq, arg2 ...grpc.CallOption) (*loans.GetLoansResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLoans", varargs...)
	ret0, _ := ret[0].(*loans.GetLoansResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoans indicates an expected call of GetLoans.
func (mr *MockLoansClientMockRecorder) GetLoans(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoans", reflect.TypeOf((*MockLoansClient)(nil).GetLoans), varargs...)
}

// GetLoansDetails mocks base method.
func (m *MockLoansClient) GetLoansDetails(arg0 context.Context, arg1 *loans.GetLoansDetailsReq, arg2 ...grpc.CallOption) (*loans.GetLoansDetailsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLoansDetails", varargs...)
	ret0, _ := ret[0].(*loans.GetLoansDetailsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoansDetails indicates an expected call of GetLoansDetails.
func (mr *MockLoansClientMockRecorder) GetLoansDetails(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoansDetails", reflect.TypeOf((*MockLoansClient)(nil).GetLoansDetails), varargs...)
}

// GetRefinancingInfo mocks base method.
func (m *MockLoansClient) GetRefinancingInfo(arg0 context.Context, arg1 *loans.GetRefinancingInfoReq, arg2 ...grpc.CallOption) (*loans.GetRefinancingInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRefinancingInfo", varargs...)
	ret0, _ := ret[0].(*loans.GetRefinancingInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRefinancingInfo indicates an expected call of GetRefinancingInfo.
func (mr *MockLoansClientMockRecorder) GetRefinancingInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRefinancingInfo", reflect.TypeOf((*MockLoansClient)(nil).GetRefinancingInfo), varargs...)
}

// GetRelationTypes mocks base method.
func (m *MockLoansClient) GetRelationTypes(arg0 context.Context, arg1 *loans.GetRelationTypesReq, arg2 ...grpc.CallOption) (*loans.GetRelationTypesResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRelationTypes", varargs...)
	ret0, _ := ret[0].(*loans.GetRelationTypesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRelationTypes indicates an expected call of GetRelationTypes.
func (mr *MockLoansClientMockRecorder) GetRelationTypes(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRelationTypes", reflect.TypeOf((*MockLoansClient)(nil).GetRelationTypes), varargs...)
}

// GetScoringResult mocks base method.
func (m *MockLoansClient) GetScoringResult(arg0 context.Context, arg1 *loans.GetScoringResultReq, arg2 ...grpc.CallOption) (*loans.GetScoringResultResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetScoringResult", varargs...)
	ret0, _ := ret[0].(*loans.GetScoringResultResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetScoringResult indicates an expected call of GetScoringResult.
func (mr *MockLoansClientMockRecorder) GetScoringResult(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetScoringResult", reflect.TypeOf((*MockLoansClient)(nil).GetScoringResult), varargs...)
}

// GetSurvey mocks base method.
func (m *MockLoansClient) GetSurvey(arg0 context.Context, arg1 *loans.GetSurveyReq, arg2 ...grpc.CallOption) (*loans.GetSurveyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSurvey", varargs...)
	ret0, _ := ret[0].(*loans.GetSurveyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSurvey indicates an expected call of GetSurvey.
func (mr *MockLoansClientMockRecorder) GetSurvey(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSurvey", reflect.TypeOf((*MockLoansClient)(nil).GetSurvey), varargs...)
}

// GetSurveyByUserID mocks base method.
func (m *MockLoansClient) GetSurveyByUserID(arg0 context.Context, arg1 *loans.GetSurveyByUserIDReq, arg2 ...grpc.CallOption) (*loans.GetSurveyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSurveyByUserID", varargs...)
	ret0, _ := ret[0].(*loans.GetSurveyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSurveyByUserID indicates an expected call of GetSurveyByUserID.
func (mr *MockLoansClientMockRecorder) GetSurveyByUserID(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSurveyByUserID", reflect.TypeOf((*MockLoansClient)(nil).GetSurveyByUserID), varargs...)
}

// HealthCheck mocks base method.
func (m *MockLoansClient) HealthCheck(arg0 context.Context, arg1 *loans.HealthCheckReq, arg2 ...grpc.CallOption) (*loans.HealthCheckResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HealthCheck", varargs...)
	ret0, _ := ret[0].(*loans.HealthCheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockLoansClientMockRecorder) HealthCheck(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockLoansClient)(nil).HealthCheck), varargs...)
}

// PostEarlyRepay mocks base method.
func (m *MockLoansClient) PostEarlyRepay(arg0 context.Context, arg1 *loans.PostEarlyRepayReq, arg2 ...grpc.CallOption) (*loans.PostEarlyRepayResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostEarlyRepay", varargs...)
	ret0, _ := ret[0].(*loans.PostEarlyRepayResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostEarlyRepay indicates an expected call of PostEarlyRepay.
func (mr *MockLoansClientMockRecorder) PostEarlyRepay(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostEarlyRepay", reflect.TypeOf((*MockLoansClient)(nil).PostEarlyRepay), varargs...)
}

// PostEdsBtsData mocks base method.
func (m *MockLoansClient) PostEdsBtsData(arg0 context.Context, arg1 *loans.PostEdsBtsDataReq, arg2 ...grpc.CallOption) (*loans.PostEdsBtsDataResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostEdsBtsData", varargs...)
	ret0, _ := ret[0].(*loans.PostEdsBtsDataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostEdsBtsData indicates an expected call of PostEdsBtsData.
func (mr *MockLoansClientMockRecorder) PostEdsBtsData(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostEdsBtsData", reflect.TypeOf((*MockLoansClient)(nil).PostEdsBtsData), varargs...)
}

// PostIdentifyBtsData mocks base method.
func (m *MockLoansClient) PostIdentifyBtsData(arg0 context.Context, arg1 *loans.PostIdentifyBtsDataReq, arg2 ...grpc.CallOption) (*loans.PostIdentifyBtsDataResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostIdentifyBtsData", varargs...)
	ret0, _ := ret[0].(*loans.PostIdentifyBtsDataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostIdentifyBtsData indicates an expected call of PostIdentifyBtsData.
func (mr *MockLoansClientMockRecorder) PostIdentifyBtsData(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostIdentifyBtsData", reflect.TypeOf((*MockLoansClient)(nil).PostIdentifyBtsData), varargs...)
}

// PostIdentifyBtsDataSme mocks base method.
func (m *MockLoansClient) PostIdentifyBtsDataSme(arg0 context.Context, arg1 *loans.PostIdentifyBtsDataSmeReq, arg2 ...grpc.CallOption) (*loans.PostIdentifyBtsDataSmeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostIdentifyBtsDataSme", varargs...)
	ret0, _ := ret[0].(*loans.PostIdentifyBtsDataSmeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostIdentifyBtsDataSme indicates an expected call of PostIdentifyBtsDataSme.
func (mr *MockLoansClientMockRecorder) PostIdentifyBtsDataSme(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostIdentifyBtsDataSme", reflect.TypeOf((*MockLoansClient)(nil).PostIdentifyBtsDataSme), varargs...)
}

// PublishSprLoanData mocks base method.
func (m *MockLoansClient) PublishSprLoanData(arg0 context.Context, arg1 *loans.PublishSprLoanDataReq, arg2 ...grpc.CallOption) (*loans.PublishSprLoanDataResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PublishSprLoanData", varargs...)
	ret0, _ := ret[0].(*loans.PublishSprLoanDataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PublishSprLoanData indicates an expected call of PublishSprLoanData.
func (mr *MockLoansClientMockRecorder) PublishSprLoanData(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishSprLoanData", reflect.TypeOf((*MockLoansClient)(nil).PublishSprLoanData), varargs...)
}

// SaveSurvey mocks base method.
func (m *MockLoansClient) SaveSurvey(arg0 context.Context, arg1 *loans.SaveSurveyReq, arg2 ...grpc.CallOption) (*loans.SaveSurveyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SaveSurvey", varargs...)
	ret0, _ := ret[0].(*loans.SaveSurveyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveSurvey indicates an expected call of SaveSurvey.
func (mr *MockLoansClientMockRecorder) SaveSurvey(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveSurvey", reflect.TypeOf((*MockLoansClient)(nil).SaveSurvey), varargs...)
}

// SaveUserExternalBankLoans mocks base method.
func (m *MockLoansClient) SaveUserExternalBankLoans(arg0 context.Context, arg1 *loans.SaveUserExternalBankLoansReq, arg2 ...grpc.CallOption) (*loans.SaveUserExternalBankLoansResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SaveUserExternalBankLoans", varargs...)
	ret0, _ := ret[0].(*loans.SaveUserExternalBankLoansResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveUserExternalBankLoans indicates an expected call of SaveUserExternalBankLoans.
func (mr *MockLoansClientMockRecorder) SaveUserExternalBankLoans(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveUserExternalBankLoans", reflect.TypeOf((*MockLoansClient)(nil).SaveUserExternalBankLoans), varargs...)
}

// UpdateLoanApplication mocks base method.
func (m *MockLoansClient) UpdateLoanApplication(arg0 context.Context, arg1 *loans.UpdateLoanApplicationReq, arg2 ...grpc.CallOption) (*loans.UpdateLoanApplicationResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateLoanApplication", varargs...)
	ret0, _ := ret[0].(*loans.UpdateLoanApplicationResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateLoanApplication indicates an expected call of UpdateLoanApplication.
func (mr *MockLoansClientMockRecorder) UpdateLoanApplication(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLoanApplication", reflect.TypeOf((*MockLoansClient)(nil).UpdateLoanApplication), varargs...)
}
