// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package usecase

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/crm/usecase -i Crm -t ../../../etc/templates/hook.tmpl -o usecase_hook.gen.go -l ""

import (
	"context"

	"git.redmadrobot.com/zaman/backend/zaman/services/crm/entity"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"
)

var _ Crm = (*CrmHook)(nil)

// CrmHook implements Crm interface wrapper
type CrmHook struct {
	Crm
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// GetAccountDetails implements Crm
func (_w *CrmHook) GetAccountDetails(ctx context.Context, req *entity.GetAccountDetailsReq) (gp1 *entity.GetAccountDetailsResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.Crm, "GetAccountDetails", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Crm, "GetAccountDetails", _params)

	gp1, err = _w.Crm.GetAccountDetails(_ctx, req)
	_w._postCall.Hook(_ctx, _w.Crm, "GetAccountDetails", []any{gp1, err})
	return gp1, err
}

// GetAccountTransactions implements Crm
func (_w *CrmHook) GetAccountTransactions(ctx context.Context, req *entity.GetAccountTransactionsReq) (gp1 *entity.GetAccountTransactionsResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.Crm, "GetAccountTransactions", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Crm, "GetAccountTransactions", _params)

	gp1, err = _w.Crm.GetAccountTransactions(_ctx, req)
	_w._postCall.Hook(_ctx, _w.Crm, "GetAccountTransactions", []any{gp1, err})
	return gp1, err
}

// GetClientInfo implements Crm
func (_w *CrmHook) GetClientInfo(ctx context.Context, req *entity.GetClientInfoReq) (gp1 *entity.GetClientInfoResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.Crm, "GetClientInfo", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Crm, "GetClientInfo", _params)

	gp1, err = _w.Crm.GetClientInfo(_ctx, req)
	_w._postCall.Hook(_ctx, _w.Crm, "GetClientInfo", []any{gp1, err})
	return gp1, err
}

// GetDepositDetails implements Crm
func (_w *CrmHook) GetDepositDetails(ctx context.Context, req *entity.GetDepositDetailsReq) (gp1 *entity.GetDepositDetailsResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.Crm, "GetDepositDetails", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Crm, "GetDepositDetails", _params)

	gp1, err = _w.Crm.GetDepositDetails(_ctx, req)
	_w._postCall.Hook(_ctx, _w.Crm, "GetDepositDetails", []any{gp1, err})
	return gp1, err
}

// GetLoanDetails implements Crm
func (_w *CrmHook) GetLoanDetails(ctx context.Context, req *entity.GetLoanDetailsReq) (gp1 *entity.GetLoanDetailsResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.Crm, "GetLoanDetails", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Crm, "GetLoanDetails", _params)

	gp1, err = _w.Crm.GetLoanDetails(_ctx, req)
	_w._postCall.Hook(_ctx, _w.Crm, "GetLoanDetails", []any{gp1, err})
	return gp1, err
}

// HealthCheck implements Crm
func (_w *CrmHook) HealthCheck(ctx context.Context) (hp1 *entity.Health, err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.Crm, "HealthCheck", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Crm, "HealthCheck", _params)

	hp1, err = _w.Crm.HealthCheck(_ctx)
	_w._postCall.Hook(_ctx, _w.Crm, "HealthCheck", []any{hp1, err})
	return hp1, err
}

// LogoutUserByPhoneNumber implements Crm
func (_w *CrmHook) LogoutUserByPhoneNumber(ctx context.Context, req *entity.LogoutUserByPhoneNumberReq) (lp1 *entity.LogoutUserByPhoneNumberResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.Crm, "LogoutUserByPhoneNumber", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Crm, "LogoutUserByPhoneNumber", _params)

	lp1, err = _w.Crm.LogoutUserByPhoneNumber(_ctx, req)
	_w._postCall.Hook(_ctx, _w.Crm, "LogoutUserByPhoneNumber", []any{lp1, err})
	return lp1, err
}

// NewCrmHook returns CrmHook
func NewCrmHook(object Crm, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *CrmHook {
	return &CrmHook{
		Crm:         object,
		_beforeCall: beforeCall,
		_postCall:   postCall,
		_onPanic:    onPanic,
	}
}
