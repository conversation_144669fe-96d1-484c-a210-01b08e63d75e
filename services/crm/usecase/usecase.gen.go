// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package usecase

import (
	"context"

	"git.redmadrobot.com/zaman/backend/zaman/config/services/crm"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/presenters"

	"git.redmadrobot.com/backend-go/rmr-pkg/proxy/hooks"

	"git.redmadrobot.com/zaman/backend/zaman/cmd/services/crm/providers"
	"git.redmadrobot.com/zaman/backend/zaman/services/crm/entity"
)

var _ Crm = (*useCasesImpl)(nil)

type Crm interface {
	HealthCheck(ctx context.Context) (*entity.Health, error)
	LogoutUserByPhoneNumber(ctx context.Context, req *entity.LogoutUserByPhoneNumberReq) (*entity.LogoutUserByPhoneNumberResult, error)
	GetClientInfo(ctx context.Context, req *entity.GetClientInfoReq) (*entity.GetClientInfoResult, error)
	GetLoanDetails(ctx context.Context, req *entity.GetLoanDetailsReq) (*entity.GetLoanDetailsResult, error)
	GetAccountDetails(ctx context.Context, req *entity.GetAccountDetailsReq) (*entity.GetAccountDetailsResult, error)
	GetAccountTransactions(ctx context.Context, req *entity.GetAccountTransactionsReq) (*entity.GetAccountTransactionsResult, error)
	GetDepositDetails(ctx context.Context, req *entity.GetDepositDetailsReq) (*entity.GetDepositDetailsResult, error)
}

type useCasesImpl struct {
	Crm
	cfg       *crm.Config
	Providers providers.ServiceLocatorImpl
}

func New(ctx context.Context, locator providers.ServiceLocatorImpl, cfg *crm.Config) *CrmHook {
	useCases := &useCasesImpl{
		cfg:       cfg,
		Providers: locator,
	}

	logger := logs.FromContext(ctx)

	hook := NewCrmHook(
		useCases,
		hooks.GrpcServiceLogBeforeCall(logger, presenters.ViewLogs, presenters.DefaultViewOptions()),
		hooks.GrpcServiceLogPostCall(logger, presenters.ViewLogs, presenters.DefaultViewOptions()),
		hooks.GrpcServiceLogPanic(logger, presenters.ViewLogs, presenters.DefaultViewOptions()),
	)
	useCases.Crm = hook

	return hook
}
