// Code generated by ent, DO NOT EDIT.

package sessionlink

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/liveness/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldEQ(FieldUpdateTime, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldEQ(FieldUserID, v))
}

// IDUserSession applies equality check predicate on the "id_user_session" field. It's identical to IDUserSessionEQ.
func IDUserSession(v uuid.UUID) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldEQ(FieldIDUserSession, v))
}

// BtsDataLink applies equality check predicate on the "bts_data_link" field. It's identical to BtsDataLinkEQ.
func BtsDataLink(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldEQ(FieldBtsDataLink, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldLTE(FieldCreateTime, v))
}

// CreateTimeIsNil applies the IsNil predicate on the "create_time" field.
func CreateTimeIsNil() predicate.SessionLink {
	return predicate.SessionLink(sql.FieldIsNull(FieldCreateTime))
}

// CreateTimeNotNil applies the NotNil predicate on the "create_time" field.
func CreateTimeNotNil() predicate.SessionLink {
	return predicate.SessionLink(sql.FieldNotNull(FieldCreateTime))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldLTE(FieldUpdateTime, v))
}

// UpdateTimeIsNil applies the IsNil predicate on the "update_time" field.
func UpdateTimeIsNil() predicate.SessionLink {
	return predicate.SessionLink(sql.FieldIsNull(FieldUpdateTime))
}

// UpdateTimeNotNil applies the NotNil predicate on the "update_time" field.
func UpdateTimeNotNil() predicate.SessionLink {
	return predicate.SessionLink(sql.FieldNotNull(FieldUpdateTime))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldLTE(FieldUserID, v))
}

// UserIDContains applies the Contains predicate on the "user_id" field.
func UserIDContains(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldContains(FieldUserID, v))
}

// UserIDHasPrefix applies the HasPrefix predicate on the "user_id" field.
func UserIDHasPrefix(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldHasPrefix(FieldUserID, v))
}

// UserIDHasSuffix applies the HasSuffix predicate on the "user_id" field.
func UserIDHasSuffix(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldHasSuffix(FieldUserID, v))
}

// UserIDEqualFold applies the EqualFold predicate on the "user_id" field.
func UserIDEqualFold(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldEqualFold(FieldUserID, v))
}

// UserIDContainsFold applies the ContainsFold predicate on the "user_id" field.
func UserIDContainsFold(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldContainsFold(FieldUserID, v))
}

// IDUserSessionEQ applies the EQ predicate on the "id_user_session" field.
func IDUserSessionEQ(v uuid.UUID) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldEQ(FieldIDUserSession, v))
}

// IDUserSessionNEQ applies the NEQ predicate on the "id_user_session" field.
func IDUserSessionNEQ(v uuid.UUID) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldNEQ(FieldIDUserSession, v))
}

// IDUserSessionIn applies the In predicate on the "id_user_session" field.
func IDUserSessionIn(vs ...uuid.UUID) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldIn(FieldIDUserSession, vs...))
}

// IDUserSessionNotIn applies the NotIn predicate on the "id_user_session" field.
func IDUserSessionNotIn(vs ...uuid.UUID) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldNotIn(FieldIDUserSession, vs...))
}

// IDUserSessionGT applies the GT predicate on the "id_user_session" field.
func IDUserSessionGT(v uuid.UUID) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldGT(FieldIDUserSession, v))
}

// IDUserSessionGTE applies the GTE predicate on the "id_user_session" field.
func IDUserSessionGTE(v uuid.UUID) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldGTE(FieldIDUserSession, v))
}

// IDUserSessionLT applies the LT predicate on the "id_user_session" field.
func IDUserSessionLT(v uuid.UUID) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldLT(FieldIDUserSession, v))
}

// IDUserSessionLTE applies the LTE predicate on the "id_user_session" field.
func IDUserSessionLTE(v uuid.UUID) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldLTE(FieldIDUserSession, v))
}

// BtsDataLinkEQ applies the EQ predicate on the "bts_data_link" field.
func BtsDataLinkEQ(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldEQ(FieldBtsDataLink, v))
}

// BtsDataLinkNEQ applies the NEQ predicate on the "bts_data_link" field.
func BtsDataLinkNEQ(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldNEQ(FieldBtsDataLink, v))
}

// BtsDataLinkIn applies the In predicate on the "bts_data_link" field.
func BtsDataLinkIn(vs ...string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldIn(FieldBtsDataLink, vs...))
}

// BtsDataLinkNotIn applies the NotIn predicate on the "bts_data_link" field.
func BtsDataLinkNotIn(vs ...string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldNotIn(FieldBtsDataLink, vs...))
}

// BtsDataLinkGT applies the GT predicate on the "bts_data_link" field.
func BtsDataLinkGT(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldGT(FieldBtsDataLink, v))
}

// BtsDataLinkGTE applies the GTE predicate on the "bts_data_link" field.
func BtsDataLinkGTE(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldGTE(FieldBtsDataLink, v))
}

// BtsDataLinkLT applies the LT predicate on the "bts_data_link" field.
func BtsDataLinkLT(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldLT(FieldBtsDataLink, v))
}

// BtsDataLinkLTE applies the LTE predicate on the "bts_data_link" field.
func BtsDataLinkLTE(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldLTE(FieldBtsDataLink, v))
}

// BtsDataLinkContains applies the Contains predicate on the "bts_data_link" field.
func BtsDataLinkContains(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldContains(FieldBtsDataLink, v))
}

// BtsDataLinkHasPrefix applies the HasPrefix predicate on the "bts_data_link" field.
func BtsDataLinkHasPrefix(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldHasPrefix(FieldBtsDataLink, v))
}

// BtsDataLinkHasSuffix applies the HasSuffix predicate on the "bts_data_link" field.
func BtsDataLinkHasSuffix(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldHasSuffix(FieldBtsDataLink, v))
}

// BtsDataLinkEqualFold applies the EqualFold predicate on the "bts_data_link" field.
func BtsDataLinkEqualFold(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldEqualFold(FieldBtsDataLink, v))
}

// BtsDataLinkContainsFold applies the ContainsFold predicate on the "bts_data_link" field.
func BtsDataLinkContainsFold(v string) predicate.SessionLink {
	return predicate.SessionLink(sql.FieldContainsFold(FieldBtsDataLink, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.SessionLink) predicate.SessionLink {
	return predicate.SessionLink(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.SessionLink) predicate.SessionLink {
	return predicate.SessionLink(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.SessionLink) predicate.SessionLink {
	return predicate.SessionLink(sql.NotPredicates(p))
}
