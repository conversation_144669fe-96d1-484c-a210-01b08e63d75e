package tests

import (
	"time"

	"github.com/google/uuid"

	pd "git.redmadrobot.com/zaman/backend/zaman/specs/proto/liveness"
)

var testDataLink = "123456"

func (s *Suite) TestGetSessionLinks_Success() {
	type testCase struct {
		name    string
		prepare func()
		data    *pd.GetSessionLinksRq
		expect  func(rs *pd.GetSessionLinksRs, err error)
	}

	testCases := []testCase{
		{
			name: "GetSessionLinks_Success_all_item",
			prepare: func() {
				today := time.Now()
				yesterday := today.AddDate(0, 0, -1)

				userID1 := uuid.New()
				userID2 := uuid.New()
				userID3 := uuid.New()
				userID4 := uuid.New()

				testUserSessionID1 := uuid.New()
				testUserSessionID2 := uuid.New()
				testUserSessionID3 := uuid.New()
				testUserSessionID4 := uuid.New()
				err := s.postgresDB.SessionLink.Create().
					SetUserID(userID1.String()).
					SetIDUserSession(testUserSessionID1).
					SetBtsDataLink(testDataLink).
					Exec(s.ctx)
				s.Require().NoError(err)

				err = s.postgresDB.SessionLink.Create().
					SetUserID(userID2.String()).
					SetIDUserSession(testUserSessionID2).
					SetBtsDataLink(testDataLink).
					SetCreateTime(yesterday).
					Exec(s.ctx)
				s.Require().NoError(err)

				err = s.postgresDB.SessionLink.Create().
					SetUserID(userID3.String()).
					SetIDUserSession(testUserSessionID3).
					SetBtsDataLink(testDataLink).
					SetCreateTime(yesterday).
					Exec(s.ctx)
				s.Require().NoError(err)

				err = s.postgresDB.SessionLink.Create().
					SetUserID(userID4.String()).
					SetIDUserSession(testUserSessionID4).
					SetBtsDataLink(testDataLink).
					SetCreateTime(yesterday).
					Exec(s.ctx)
				s.Require().NoError(err)
			},
			data: &pd.GetSessionLinksRq{},
			expect: func(rs *pd.GetSessionLinksRs, err error) {
				s.Require().NoError(err)
				s.Require().Len(rs.List, 4)
			},
		},
		{
			name: "GetSessionLinks_Success_3_item",
			prepare: func() {
				today := time.Now()
				date := today.AddDate(0, 0, -2)

				userID1 := uuid.New()
				userID2 := uuid.New()
				userID3 := uuid.New()
				userID4 := uuid.New()

				testUserSessionID1 := uuid.New()
				testUserSessionID2 := uuid.New()
				testUserSessionID3 := uuid.New()
				testUserSessionID4 := uuid.New()

				err := s.postgresDB.SessionLink.Create().
					SetUserID(userID1.String()).
					SetIDUserSession(testUserSessionID1).
					SetBtsDataLink(testDataLink).
					Exec(s.ctx)
				s.Require().NoError(err)

				err = s.postgresDB.SessionLink.Create().
					SetUserID(userID2.String()).
					SetIDUserSession(testUserSessionID2).
					SetBtsDataLink(testDataLink).
					SetCreateTime(date.Add(time.Minute * 5)).
					Exec(s.ctx)
				s.Require().NoError(err)

				err = s.postgresDB.SessionLink.Create().
					SetUserID(userID3.String()).
					SetIDUserSession(testUserSessionID3).
					SetBtsDataLink(testDataLink).
					SetCreateTime(date.Add(time.Minute * 5)).
					Exec(s.ctx)
				s.Require().NoError(err)

				err = s.postgresDB.SessionLink.Create().
					SetUserID(userID4.String()).
					SetIDUserSession(testUserSessionID4).
					SetBtsDataLink(testDataLink).
					SetCreateTime(date.Add(time.Minute * 5)).
					Exec(s.ctx)
				s.Require().NoError(err)
			},
			data: &pd.GetSessionLinksRq{
				CreateTimeTo: time.Now().AddDate(0, 0, -2).Add(time.Minute * 10).Format(time.RFC3339),
			},
			expect: func(rs *pd.GetSessionLinksRs, err error) {
				s.Require().NoError(err)
				s.Require().Len(rs.List, 3)
			},
		},
	}

	for _, item := range testCases {
		item.prepare()
		list, err := s.grpc.GetSessionLinks(s.ctx, item.data)
		item.expect(list, err)
	}
}
