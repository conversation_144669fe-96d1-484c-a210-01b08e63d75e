package tests

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/helpers/localizedvalue"
	"math/rand/v2"
	"strconv"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/httpx/mw"
	"github.com/go-faker/faker/v4"
	"google.golang.org/protobuf/types/known/timestamppb"

	"git.redmadrobot.com/zaman/backend/zaman/services/loans/storage/postgres/ent/actionshistory"

	userplatform "git.redmadrobot.com/zaman/backend/zaman/pkg/helpers/user_platform"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/storage/postgres/ent/survey"

	"git.redmadrobot.com/backend-go/rmr-pkg/tools/conversion"
	"github.com/google/uuid"

	cardsAccounts "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/storage/postgres/ent"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/storage/postgres/ent/loanapplication"
	"git.redmadrobot.com/zaman/backend/zaman/services/users/storage/postgres/ent/users"
	amlBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/aml-bridge"
	pbColvirBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
	pbDictionary "git.redmadrobot.com/zaman/backend/zaman/specs/proto/dictionary"
	pbKgdBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/kgd-bridge"
	pbLoans "git.redmadrobot.com/zaman/backend/zaman/specs/proto/loans"
	pbPkbBridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/pkb-bridge"
	pbUsers "git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"
)

const (
	testLoanAmount              uint32 = 100000
	smeTestLoanAmount           uint32 = 250000
	testUser2ID                        = "zcec29ba-e2d5-4db6-8265-3efd85fd95d1"
	testEmail                          = "<EMAIL>"
	testChildrenCount           int32  = 2
	testUserIin                        = "*********123"
	testUserSurname                    = "testSurname"
	testUserName                       = "testName"
	testUserPatronymic                 = "testPatronymic"
	testUserFullName                   = testUserSurname + " " + testUserName + " " + testUserPatronymic
	testUserStatus                     = users.StatusActive
	testUserPhone                      = "+77005553535"
	testJuicySessionID                 = "4d41e509-1d1e-4530-a1d6"
	testBirthDate                      = "1979-10-01"
	testCity                           = "Усть-Каменогорск"
	testCountry                        = "Казахстан"
	testRefinancingTotalAmount         = 1_000_000
	testRefinancingCreditAmount        = 200_000
	testRefinancingRefAmount           = 800_000
)

var (
	testUserPlatform    = userplatform.Android
	testUserPlatformStr = testUserPlatform.String()
	testUserAgent       = uuid.NewString() + " " + testUserPlatform.String()
	testLoanAmountMax   = 3000000
	testLoanAmountMin   = 1000000
	testLoanTermMax     = 60
	testLoanPercentPaid = int32(0)

	testTimeDueDate      = time.Date(2025, time.April, 1, 0, 0, 0, 0, time.UTC)
	testAdditionalAmount = "100000.00"
	testUserID           = uuid.New()

	testLoanTermInterest = entity.LoanTermInterest{
		ID:           "1",
		LoanTerm:     3,
		LoanInterest: 30,
	}
	smeTestLoanTermInterest = entity.LoanTermInterest{
		ID:           "1",
		LoanTerm:     3,
		LoanInterest: 60,
	}
	testLoanPurpose = entity.LoanPurpose{
		ID:          "1",
		Description: "На покупку товара",
	}
	testAddressWithNilFlat = pbLoans.SaveSurveyAddress{
		KatoCodes: []string{uuid.NewString(), uuid.NewString(), uuid.NewString()},
		Street:    "testFactStreet",
		Building:  "testFactBuilding",
		Flat:      nil,
	}
	testAddressWithFlat = pbLoans.SaveSurveyAddress{
		KatoCodes: []string{uuid.NewString(), uuid.NewString(), uuid.NewString()},
		Street:    "testFactStreet",
		Building:  "testFactBuilding",
		Flat:      nil,
	}
	testRelationType = entity.GetRelationTypesResult{RelationTypes: []*entity.RelationType{{
		ID:   "1",
		Code: "1",
		Name: "Супруг / Супруга",
	}}}.RelationTypes[0]

	pbTestContactPersons = []*pbLoans.SaveSurveyContactPerson{
		{
			RelID:     testRelationType.ID,
			FirstName: "testFirstName",
			LastName:  "testLastName",
			Phone:     testUserPhone,
		},
	}
	testContactPersons = []*entity.SaveSurveyContactPerson{
		{
			RelType:   testRelationType.Name,
			FirstName: pbTestContactPersons[0].FirstName,
			LastName:  pbTestContactPersons[0].LastName,
			Phone:     pbTestContactPersons[0].Phone,
		},
	}

	testUserProfileShortResponseWithUnderAge = &pbUsers.GetPersonalDataResp{
		Age: 10,
		Gender: &pbUsers.SubjectDataParams{
			Code: 1,
		},
		ColvirInfo: &pbUsers.UserColvirInfo{
			ColvirClientCode:  uuid.NewString(),
			ColvirClientDepId: uuid.NewString(),
			ColvirClientId:    uuid.NewString(),
		},
	}

	testMockAmlBridgeCheckOnlineClientCardResponseSuccess = &amlBridge.CheckOnlineClientCardResponse{
		Status:     0,
		Uid:        uuid.NewString(),
		BsClientID: uuid.NewString(),
		Comment:    conversion.Ptr(uuid.NewString()),
	}
	testMockAmlBridgeCheckOnlineClientCardResponseFail = &amlBridge.CheckOnlineClientCardResponse{
		Status:     1,
		Uid:        uuid.NewString(),
		BsClientID: uuid.NewString(),
		Comment:    conversion.Ptr(uuid.NewString()),
	}

	testEducationType = entity.GetEducationTypesResult{EducationTypes: []*entity.EducationType{{
		ID:   "1",
		Code: "1",
		Name: "Начальное",
	}}}.EducationTypes[0]

	testEmpType = entity.GetEmploymentTypesResult{EmploymentTypes: []*entity.EmploymentType{{
		ID:   "1",
		Code: "1",
		Name: "Сотрудник компании",
	}}}.EmploymentTypes[0]

	testKgdDebtsRespSuccess = &pbKgdBridge.GetDebtsResp{
		TotalArrear:    0,
		TotalTaxArrear: 0,
		TaxOrgInfo: []*pbKgdBridge.TaxOrgInfo{
			{
				TotalArrear:    0,
				TotalTaxArrear: 0,
				TaxPayerInfo:   &pbKgdBridge.TaxPayerInfo{},
			},
		},
	}
	testKgdDebtsRespFailed = &pbKgdBridge.GetDebtsResp{
		TotalArrear:    100000,
		TotalTaxArrear: 100000,
		TaxOrgInfo: []*pbKgdBridge.TaxOrgInfo{
			{
				TotalArrear:    100000,
				TotalTaxArrear: 100000,
				TaxPayerInfo:   &pbKgdBridge.TaxPayerInfo{},
			},
		},
	}

	testColvirFindTaxPayerRespSuccess = &pbColvirBridge.FindTaxPayerResp{
		TaxPayerStatus: pbColvirBridge.TaxPayerStatus_ACTIVE,
	}
	testColvirFindTaxPayerRespFailed = &pbColvirBridge.FindTaxPayerResp{
		TaxPayerStatus: pbColvirBridge.TaxPayerStatus_INACTIVE,
	}

	testColvirFindClientAccountListStatusActive   = "ACTIVE"
	testColvirFindClientAccountListStatusArrested = "ARRESTED"

	testColvirFindClientAccountListRespSuccess = &pbColvirBridge.FindClientAccountListResp{
		Accounts: []*pbColvirBridge.Account{
			{
				Status: testColvirFindClientAccountListStatusActive,
			},
		},
	}
	testColvirFindClientAccountListRespFailed = &pbColvirBridge.FindClientAccountListResp{
		Accounts: []*pbColvirBridge.Account{
			{
				Status: testColvirFindClientAccountListStatusArrested,
			},
		},
	}

	testCardsAccountsGetAccountsResponseNoArrestedAccounts = &cardsAccounts.GetAccountsResponse{
		Accounts: []*cardsAccounts.Account{
			{
				ID: uuid.NewString(),
				Arrest: &cardsAccounts.Arrest{
					Blocking: false,
				},
			},
			{
				ID: uuid.NewString(),
				Arrest: &cardsAccounts.Arrest{
					Blocking: false,
				},
			},
		},
	}

	testCardsAccountsGetAccountsResponseHasArrestedAccounts = &cardsAccounts.GetAccountsResponse{
		Accounts: []*cardsAccounts.Account{
			{
				ID: uuid.NewString(),
				Arrest: &cardsAccounts.Arrest{
					Blocking: false,
				},
			},
			{
				ID: uuid.NewString(),
				Arrest: &cardsAccounts.Arrest{
					Blocking: true,
				},
			},
			{
				ID: uuid.NewString(),
				Arrest: &cardsAccounts.Arrest{
					Blocking: false,
				},
			},
		},
	}

	testColvirLoadClientBankRelationLinkResponseSuccessAffiliatedPerson = &pbColvirBridge.LoadClientBankRelationLinkResponse{
		ExecuteResult:   nil,
		Result:          &pbColvirBridge.LoadClientBankRelationLinkResult{},
		ColvirRequestId: "",
		Errors:          nil,
	}

	testColvirLoadClientBankRelationLinkResponseSuccessNotAffiliatedPerson = &pbColvirBridge.LoadClientBankRelationLinkResponse{
		ExecuteResult:   nil,
		Result:          nil,
		ColvirRequestId: "",
		Errors:          nil,
	}

	testColvirLoadClientBankRelationLinkResponseErrorAsField = &pbColvirBridge.LoadClientBankRelationLinkResponse{
		ExecuteResult:   nil,
		Result:          nil,
		ColvirRequestId: "",
		Errors: &pbColvirBridge.LoadClientBankRelationLinkErrors{
			Code:     "testErrorCode",
			Message:  "testErrorMessage",
			Severity: "testErrorSeverity",
		},
	}

	testPkbGetSusnStatusRespSuccess = &pbPkbBridge.GetSusnStatusResp{
		Iin: testUserIin,
		Status: []*pbPkbBridge.SusnStatus{
			{
				StatusCode: "0",
			},
			{
				StatusCode: "0",
			},
		},
	}
	successfulGetPermittedDocsResp = &pbPkbBridge.GetPermitDocumentsByIinResp{
		Iin: testIIN,
		TaxPayerLicenses: []*pbPkbBridge.TaxPayerLicense{
			{
				ActivityType: &pbPkbBridge.ActivityType{
					Code:   "NotRestricted",
					NameRu: "string",
					NameKz: "string",
				},
				DocumentID:        "*********",
				ValidityStartDate: "2022-01-01T00:00:00",
				ValidityEndDate:   "2025-01-01T00:00:00",
				Licensiar: &pbPkbBridge.Licensiar{
					Code:   "ABC123",
					NameRu: "Test Licensing Authority",
					NameKz: "Test Licensing Authority KZ",
				},
			},
		},
	}
	testPkbGetSusnStatusRespFail = &pbPkbBridge.GetSusnStatusResp{
		Iin: testUserIin,
		Status: []*pbPkbBridge.SusnStatus{
			{
				StatusCode: "0",
			},
			{
				StatusCode: "1",
			},
		},
	}

	testPkbGetArmyStatusRespSuccess = &pbPkbBridge.ArmyStatusResp{
		Iin:    testUserIin,
		InArmy: false,
	}

	testPkbGetArmyStatusRespFail = &pbPkbBridge.ArmyStatusResp{
		Iin:    testUserIin,
		InArmy: true,
	}

	testPkbGetBankruptcyCreateReportStatusRespFail = &pbPkbBridge.CreateReportResp{
		Status:          "NEW",
		ReportID:        "",
		ReadyPercentage: 0,
	}

	testPkbGetBankruptcyCreateReportStatusRespSuccess = &pbPkbBridge.CreateReportResp{
		Status:          "DONE",
		ReportID:        "",
		ReadyPercentage: 0,
	}

	testPkbGetBankruptcyGetReportStatusRespFail = &pbPkbBridge.GetReportStatusResp{
		Status:          "NEW",
		ReportID:        "",
		ReadyPercentage: 0,
	}

	testPkbGetBankruptcyGetReportStatusRespSuccess = &pbPkbBridge.GetReportStatusResp{
		Status:          "DONE",
		ReportID:        "",
		ReadyPercentage: 0,
	}

	testPkbGetBankruptcyGetReportRespFail = &pbPkbBridge.GetReportResp{
		ReportId: "",
		Sources: []*pbPkbBridge.Source{
			conversion.Ptr(pbPkbBridge.Source{
				Code: "KGD12",
			}),
		},
	}

	testPkbGetBankruptcyGetReportRespSuccess = &pbPkbBridge.GetReportResp{
		ReportId: "",
		Sources: []*pbPkbBridge.Source{
			conversion.Ptr(pbPkbBridge.Source{
				Code: "KGD01",
			}),
		},
	}

	testUsersGetPersonalDataMockSuccess = &pbUsers.GetPersonalDataResp{
		Iin: uuid.NewString(),
		Age: 22,
		ColvirInfo: &pbUsers.UserColvirInfo{
			ColvirClientCode:  uuid.NewString(),
			ColvirClientDepId: uuid.NewString(),
			ColvirClientId:    uuid.NewString(),
		},
	}

	testUsersGetPersonalDataMockFailUnderAge = &pbUsers.GetPersonalDataResp{
		Iin: uuid.NewString(),
		Age: 10,
		ColvirInfo: &pbUsers.UserColvirInfo{
			ColvirClientCode:  uuid.NewString(),
			ColvirClientDepId: uuid.NewString(),
			ColvirClientId:    uuid.NewString(),
		},
	}

	testOfferTitleRu   = "Test DictionaryRefinanceTitle RU"
	testOfferTitleKz   = "Test DictionaryRefinanceTitle KZ"
	testOfferTextRu    = "Test Text RU"
	testOfferTextKz    = "Test Text KZ"
	testOfferSubTextRu = "Test Offer SubText %d amount %d term RU"
	testOfferSubTextKk = "Test Offer SubText %d amount %d term KK"

	testOfferDoc = entity.DictionaryOfferInfo{
		OfferTitle: &entity.DictionaryTitleLocalizedValue{
			RU: testOfferTitleRu,
			KZ: testOfferTitleKz,
		},

		OfferText: &entity.DictionaryTextLocalizedValue{
			RU: testOfferTextRu,
			KZ: testOfferTextKz,
		},
		OfferSubText: &entity.DictionaryTextLocalizedValue{
			RU: testOfferSubTextRu,
			KZ: testOfferSubTextKk,
		},
	}

	testFinanceInfoDoc = entity.DictionaryFinance{
		Title: localizedvalue.LocalizedValue{
			KZ: "Қаржыландыру ресімделді",
			RU: "Финансирование оформлено",
		},
		BreakTitle: localizedvalue.LocalizedValue{
			KZ: "Қаржыландыру өңделу барысында",
			RU: "Финансирование в обработке",
		},
		TermsFmt: localizedvalue.LocalizedValue{
			KZ: "%d айға",
			RU: "на %d месяцев",
		},
		InfoSubTitle: localizedvalue.LocalizedValue{
			KZ: "Ақша Сіздің ағымдағы шотыңызға түскенде SMS жібереміз",
			RU: "Пришлём СМС, когда деньги поступят на ваш текущий счёт",
		},
	}

	testLoanTypeDoc = entity.DictionaryLoanTypes{
		LoanAmount: &entity.LoanAmount{
			MaxLimit: uint32(testLoanAmountMax),
			MinLimit: uint32(testLoanAmountMin),
		},
		LoanPurposes: []*entity.DictionaryLoanPurpose{
			{
				Description: &entity.DictionaryDescriptionLocalizedValue{
					KZ: testLoanPurpose.Description,
					RU: testLoanPurpose.Description,
				},
				ID: uuid.NewString(),
			},
		},
		LoanTermInterests: []*entity.LoanTermInterest{
			{
				DefaultInterest: false,
				ID:              "8",
				LoanInterest:    30,
				LoanTerm:        uint32(testLoanTermMax),
			},
		},
	}

	testOnboardingDoc = entity.DictionaryOnboarding{
		DictionaryTexts: []entity.DictionaryOnboardingTextData{
			{
				Code:  "intro",
				Image: "https://example.com/images/intro.png",
				Description: entity.OnboardingTextDescription{
					Kk: "Қош келдіңіз!",
					Ru: "Добро пожаловать!",
				},
			},
			{
				Code:  "usage",
				Image: "https://example.com/images/usage.png",
				Description: entity.OnboardingTextDescription{
					Kk: "Қолдану тәсілі",
					Ru: "Как использовать",
				},
			},
		},
	}

	testColvirbridgeLoanCalcLoadPreScheduleResponse = &pbColvirBridge.LoanCalcLoadPreScheduleResponse{
		YearEffectiveRate: strconv.Itoa(rand.IntN(100)),
		Schedule: []*pbColvirBridge.LoanCalcLoadPreScheduleScheduleEntry{
			{
				Date:         "2025-05-28T00:00:00",
				Amount:       strconv.Itoa(rand.IntN(100)),
				DebtAfterPay: strconv.Itoa(rand.IntN(100)),
				Details: []*pbColvirBridge.LoanCalcLoadPreScheduleDetail{
					{
						Code:   consts.LoanCalcLoadPreScheduleScheduleEntryCodeCrInter,
						Amount: strconv.Itoa(rand.IntN(100)),
					},
					{
						Code:   consts.LoanCalcLoadPreScheduleScheduleEntryCodeCrInter,
						Amount: strconv.Itoa(rand.IntN(100)),
					},
					{
						Code:   consts.LoanCalcLoadPreScheduleScheduleEntryCodeCrInter,
						Amount: strconv.Itoa(rand.IntN(100)),
					},
					{
						Code:   consts.LoanCalcLoadPreScheduleScheduleEntryCodeCrPd,
						Amount: strconv.Itoa(rand.IntN(100)),
					},
					{
						Code:   consts.LoanCalcLoadPreScheduleScheduleEntryCodeCrPd,
						Amount: strconv.Itoa(rand.IntN(100)),
					},
					{
						Code:   consts.LoanCalcLoadPreScheduleScheduleEntryCodeCrPd,
						Amount: strconv.Itoa(rand.IntN(100)),
					},
				},
			},
		},
	}

	testClientCard = &pbColvirBridge.GetClientCardResponse{
		FilialCode:  "001",
		ClientCode:  "C123456",
		ClientDepId: "D987654",
		ClientId:    "ID7890",
		Status:      "ACTIVE",
		ClientFiz: &pbColvirBridge.ClientFiz{
			ClientCode:      "C123456",
			ClientDepID:     "D987654",
			ClientID:        "ID7890",
			LastName:        "Иванов",
			FirstName:       "Иван",
			MiddleName:      "Иванович",
			BirthDate:       "1990-01-01",
			Gender:          "M",
			Resident:        true,
			ResidenceCode:   "KZ",
			CitizenshipCode: "KZ",
			DocType:         "UDL",
			DocSeries:       "AB",
			DocNumber:       "1234567",
			DocIssueDate:    "2010-05-01",
			DocExpireDate:   "2030-05-01",
			DocIssuePlace:   "МИНЮСТ КАЗАХСТАН",
		},
	}
	testLoansGetMissedPayments = &pbColvirBridge.LoansGetMissedPaymentsResponse{
		External:       "EXT123456",
		Agreement:      "AG987654",
		Department:     "LOANS",
		BankCode:       "BK001",
		FromDate:       time.Now().Format("2006-01-02"), // сегодняшняя дата
		HasDelay:       true,
		DelayDaysCount: 15,
	}

	testDictionaryProductionCalendar = &pbDictionary.DocGetByNameResp{
		Doc: &pbDictionary.Doc{
			Id:     "1",
			DictId: "1",
			Name:   "Production Calendar",
			Data: `{
		"holidays": ["2025-01-01", "2025-03-08", "2025-05-01"],
		"bank_working_hours": {
			"start_hour": "09:00",
			"end_hour": "18:00",
			"allowed_days": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]
		},
		"biometry_working_hours": {
			"start_hour": "10:00",
			"end_hour": "17:00",
			"allowed_days": ["Monday", "Tuesday", "Wednesday", "Thursday"]
		},
		"exchange_working_hours": {
			"start_hour": "09:30",
			"end_hour": "21:30",
			"allowed_days": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
			"call_deadline_hour": "21:20",
			"breaks": [
			]
		}
	}`,
		},
	}
)

func createLoanApplicationStorage(ctx context.Context, postgresClient *ent.Client, application *ent.LoanApplication) (*ent.LoanApplication, error) {
	storageApplication, err := postgresClient.LoanApplication.Create().
		SetStatus(application.Status).
		SetPurpose(application.Purpose).
		SetUserID(application.UserID).
		SetUserIin(application.UserIin).
		SetBusinessType(application.BusinessType).
		SetJuicySessionID(application.JuicySessionID).
		SetUserFullNameAtCreation(testUserFullName).
		SetColvirReferenceID(testColvirReferenceID).
		Save(ctx)

	return storageApplication, err
}

func (s *Suite) createLoanConditionsStorageFromEntAndAppID(applicationID uuid.UUID, conditions *ent.LoanCondition) (*ent.LoanCondition, error) {
	loanCondition, err := s.postgresDB.LoanCondition.Create().
		SetApplicationID(applicationID).
		SetAmount(conditions.Amount).
		SetInterest(conditions.Interest).
		SetTerm(conditions.Term).
		SetIsOriginal(conditions.IsOriginal).
		SetIsApprovedBySpr(conditions.IsApprovedBySpr).
		SetIsFinallyChosenByUser(conditions.IsFinallyChosenByUser).
		Save(s.ctx)
	return loanCondition, err
}

type ExpectedInternalCheckResult string

func (s *Suite) createLoanApplicationStorage(loanApp *entity.LoanApplication, externalBankLoans []*ent.ExternalBankLoan) (*ent.LoanApplication, error) {
	storageApplication, err := s.postgresDB.LoanApplication.Create().
		SetID(uuid.MustParse(loanApp.ID)).
		SetStatus(loanapplication.Status(loanApp.Status)).
		SetPurpose(loanApp.Purpose).
		SetUserID(loanApp.UserID).
		SetUserIin(loanApp.UserIin).
		SetJuicySessionID(loanApp.JuicySessionID).
		SetBusinessType(loanapplication.BusinessType(loanApp.BusinessType)).
		SetUserFullNameAtCreation(loanApp.UserFullNameAtCreation).
		SetType(loanapplication.Type(loanApp.Type)).
		AddExternalBankLoans(externalBankLoans...).
		Save(s.ctx)
	if err != nil {
		return nil, err
	}
	s.Require().NotNil(storageApplication)

	appSpr, err := s.postgresDB.LoanApplicationSpr.Create().
		SetApplicationID(storageApplication.ID).
		Save(s.ctx)
	if err != nil {
		return nil, err
	}
	s.Require().NotNil(appSpr)

	storageApplication.Edges.Spr = appSpr

	s.Require().NotNil(storageApplication.Edges.Spr)
	s.Require().NotEqual(0, storageApplication.Edges.Spr.ID)

	return storageApplication, nil
}

func (s *Suite) createLoanApplicationStorageWithStatus(status consts.LoanApplicationStatus) *ent.LoanApplication {
	storageApplication, err := s.postgresDB.LoanApplication.Create().
		SetStatus(loanapplication.Status(status)).
		SetPurpose(testLoanPurpose.Description).
		SetUserID(testUserID.String()).
		SetUserIin(testUserIin).
		SetJuicySessionID(testJuicySessionID).
		SetBusinessType(loanapplication.BusinessType(consts.LoanApplicationBusinessTypeRetail)).
		SetUserFullNameAtCreation(testUserFullName).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(storageApplication)

	appSpr, err := s.postgresDB.LoanApplicationSpr.Create().
		SetApplicationID(storageApplication.ID).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(appSpr)

	storageApplication.Edges.Spr = appSpr

	s.Require().NotNil(storageApplication.Edges.Spr)
	s.Require().NotEqual(0, storageApplication.Edges.Spr.ID)

	return storageApplication
}

func (s *Suite) getLoanAppWithHistoryActionsAfterInternalChecks(applicationID string, historyActionTypeChecksPassed consts.HistoryActionType) *ent.LoanApplication {
	var loanApplication *ent.LoanApplication = nil
	var err error = nil

	applicationUUID, err := uuid.Parse(applicationID)
	s.Require().NoError(err)

	maxRetries := 1000
	retryCount := 0
	var historyActions []*ent.ActionsHistory
	areChecksInProgress := true

	for {
		loanApplication, err = s.postgresDB.LoanApplication.Query().Where(loanapplication.ID(applicationUUID)).WithHistoryActions().First(s.ctx)
		s.Require().NoError(err)

		historyActions, err = loanApplication.Edges.HistoryActionsOrErr()
		s.Require().NoError(err)

		if loanApplication.Status.String() == consts.LoanApplicationStatusRejected.String() {
			areChecksInProgress = false
			break
		}

		var historyActionChecksPassed *ent.ActionsHistory = nil
		for _, historyAction := range historyActions {
			now, err := utils.GetCurrentKzTime()
			s.Require().NoError(err)
			startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

			if historyAction.ActionType.String() == historyActionTypeChecksPassed.String() && historyAction.CreateTime.After(startOfDay) {
				historyActionChecksPassed = historyAction
			}
			if historyAction.ActionType.String() == consts.TechError.String() || historyAction.ActionType.String() == consts.IntegrationError.String() {
				s.FailNowf("tech/integration error happened during internal checks", "history action: %+v", historyAction)
			}
		}
		if historyActionChecksPassed != nil {
			areChecksInProgress = false
			break
		}

		retryCount++
		if retryCount >= maxRetries {
			s.Require().FailNow("Exceeded max retries without finding ChecksCompleted action")
		}

		time.Sleep(1 * time.Millisecond)
	}

	s.Require().Falsef(areChecksInProgress, "Expected checks to not be in progress, but they are")
	s.Require().NotNil(historyActions)

	return loanApplication
}

func compareContactPersons(expected []*pbLoans.SaveSurveyContactPerson, actual []map[string]interface{}) error {
	if len(expected) != len(actual) {
		return errors.New("number of contact persons mismatch")
	}

	for i, contactPerson := range expected {
		if _, ok := actual[i]["rel_type"].(string); !ok {
			return fmt.Errorf("contact person %d rel_type is empty", i)
		}
		if contactPerson.FirstName != actual[i]["first_name"].(string) {
			return fmt.Errorf("contact person %d first name mismatch", i)
		}
		if contactPerson.LastName != actual[i]["last_name"].(string) {
			return fmt.Errorf("contact person %d last name mismatch", i)
		}
		if contactPerson.Phone != actual[i]["phone"].(string) {
			return fmt.Errorf("contact person %d phone mismatch", i)
		}
	}

	return nil
}

func (s *Suite) createSurveyStorage(survey *ent.Survey) (*ent.Survey, error) {
	return s.postgresDB.Survey.Create().
		SetEmail(survey.Email).
		SetAddress(survey.Address).
		SetEducationType(survey.EducationType).
		SetEmpType(survey.EmpType).
		SetContactPersons(survey.ContactPersons).
		SetChildren(survey.Children).
		SetApplicationID(survey.ApplicationID).
		SetUserID(survey.UserID).
		SetIP(survey.IP).
		SetUserAgent(survey.UserAgent).
		SetUserPlatform(survey.UserPlatform).
		Save(s.ctx)
}

func (s *Suite) createFilledSurveyStorage(applicationID uuid.UUID) (*ent.Survey, error) {
	return s.createSurveyStorage(
		s.getFilledEntSurvey(applicationID),
	)
}

func (s *Suite) getFilledEntSurvey(applicationID uuid.UUID) *ent.Survey {
	return &ent.Survey{
		ApplicationID: applicationID,
		Email:         uuid.NewString(),
		Address: map[string]interface{}{
			"kato_codes": []string{uuid.NewString(), uuid.NewString(), uuid.NewString()},
			"street":     uuid.NewString(),
			"building":   uuid.NewString(),
			"flat":       uuid.NewString(),
		},
		EducationType: testEducationType.Name,
		EmpType:       testEmpType.Name,

		ContactPersons: []map[string]interface{}{
			{
				"rel_type":   testRelationType.Name,
				"first_name": uuid.NewString(),
				"last_name":  uuid.NewString(),
				"phone":      uuid.NewString(),
			},
		},
		Children:     rand.Int32(),
		UserID:       testUserID,
		IP:           uuid.NewString(),
		UserAgent:    testUserAgent,
		UserPlatform: survey.UserPlatform(testUserPlatformStr),
	}
}

func addUserIDToContext(ctx context.Context) context.Context {
	userInfo := map[string]interface{}{
		"user_id":      testUserID,
		"session_id":   uuid.NewString(),
		"phone_number": testUserPhone,
	}

	// SA1029: should not use built-in type string as key for value; define your own type to avoid collisions (staticcheck)
	//nolint:staticcheck
	return context.WithValue(ctx, "userInfo", userInfo)
}

func (s *Suite) generateGetSurveyAddress(levels int) *pbLoans.GetSurveyAddress {
	address := &pbLoans.GetSurveyAddress{}

	if levels >= 2 {
		address.Region = &pbLoans.KatoData{
			Code:     uuid.NewString(),
			Name:     uuid.NewString(),
			ID:       int32(rand.Int()),
			ParentID: int32(0),
		}
		address.District = &pbLoans.KatoData{
			Code:     uuid.NewString(),
			Name:     uuid.NewString(),
			ID:       int32(rand.Int()),
			ParentID: address.GetRegion().GetID(),
		}
	}

	if levels >= 3 {
		address.SettlementArea = &pbLoans.KatoData{
			Code:     uuid.NewString(),
			Name:     uuid.NewString(),
			ID:       int32(rand.Int()),
			ParentID: address.GetDistrict().GetID(),
		}
	}
	if levels >= 4 {
		address.Settlement = &pbLoans.KatoData{
			Code:     uuid.NewString(),
			Name:     uuid.NewString(),
			ID:       int32(rand.Int()),
			ParentID: address.GetSettlementArea().GetID(),
		}
	}
	if levels >= 5 {
		address.Locality = &pbLoans.KatoData{
			Code:     uuid.NewString(),
			Name:     uuid.NewString(),
			ID:       int32(rand.Int()),
			ParentID: address.GetSettlement().GetID(),
		}
	}

	address.IsFull = true

	address.Street = conversion.Ptr(uuid.NewString())
	address.Building = conversion.Ptr(uuid.NewString())
	address.Flat = conversion.Ptr(uuid.NewString())

	return address
}

func (s *Suite) generateGetSurveyResp(levels int) *pbLoans.GetSurveyResp {
	address := s.generateGetSurveyAddress(levels)

	result := &pbLoans.GetSurveyResp{
		Address: address,
		EducationType: &pbLoans.EducationType{
			ID:   testEducationType.ID,
			Name: testEducationType.Name,
			Code: testEducationType.Code,
		},
		EmpType: &pbLoans.EmploymentType{
			ID:   testEmpType.ID,
			Name: testEmpType.Name,
			Code: testEmpType.Code,
		},
		ContactPersons: []*pbLoans.GetSurveyContactPerson{
			{
				RelType: &pbLoans.RelationType{
					ID:   testRelationType.ID,
					Name: testRelationType.Name,
					Code: testRelationType.Code,
				},
				FirstName: uuid.NewString(),
				LastName:  uuid.NewString(),
				Phone:     uuid.NewString(),
			},
		},
		ChildrenCount: conversion.Ptr(int32(rand.Int())),
		Email:         conversion.Ptr(uuid.NewString()),
		Ip:            uuid.NewString(),
		UserAgent:     testUserAgent,
		UserPlatform:  testUserPlatformStr,
	}

	if address.GetRegion() != nil {
		result.Address.KatoCodes = append(result.Address.KatoCodes, address.GetRegion().GetCode())
	}
	if address.GetDistrict() != nil {
		result.Address.KatoCodes = append(result.Address.KatoCodes, address.GetDistrict().GetCode())
	}
	if address.GetSettlementArea() != nil {
		result.Address.KatoCodes = append(result.Address.KatoCodes, address.GetSettlementArea().GetCode())
	}
	if address.GetSettlement() != nil {
		result.Address.KatoCodes = append(result.Address.KatoCodes, address.GetSettlement().GetCode())
	}
	if address.GetLocality() != nil {
		result.Address.KatoCodes = append(result.Address.KatoCodes, address.GetLocality().GetCode())
	}

	return result
}

func (s *Suite) surveyAddressToDictKatoData(address *pbLoans.GetSurveyAddress) []*entity.DictionaryKatoData {
	result := []*entity.DictionaryKatoData{
		{
			ID:        address.GetRegion().GetID(),
			Code:      address.GetRegion().GetCode(),
			CodeTsoid: uuid.NewString(),
			NameLocalized: entity.DictionaryKatoDataName{
				En: address.GetRegion().GetName(),
				Ru: address.GetRegion().GetName(),
				Kz: address.GetRegion().GetName(),
			},
			ParentID: address.GetRegion().GetParentID(),
		},
		{
			ID:        address.GetDistrict().GetID(),
			Code:      address.GetDistrict().GetCode(),
			CodeTsoid: uuid.NewString(),
			NameLocalized: entity.DictionaryKatoDataName{
				En: address.GetDistrict().GetName(),
				Ru: address.GetDistrict().GetName(),
				Kz: address.GetDistrict().GetName(),
			},
			ParentID: address.GetDistrict().GetParentID(),
		},
	}

	if address.GetSettlementArea() != nil {
		result = append(result, &entity.DictionaryKatoData{
			ID:        address.GetSettlementArea().GetID(),
			Code:      address.GetSettlementArea().GetCode(),
			CodeTsoid: uuid.NewString(),
			NameLocalized: entity.DictionaryKatoDataName{
				En: address.GetSettlementArea().GetName(),
				Ru: address.GetSettlementArea().GetName(),
				Kz: address.GetSettlementArea().GetName(),
			},
			ParentID: address.GetSettlementArea().GetParentID(),
		})
	}
	if address.GetSettlement() != nil {
		result = append(result, &entity.DictionaryKatoData{
			ID:        address.GetSettlement().GetID(),
			Code:      address.GetSettlement().GetCode(),
			CodeTsoid: uuid.NewString(),
			NameLocalized: entity.DictionaryKatoDataName{
				En: address.GetSettlement().GetName(),
				Ru: address.GetSettlement().GetName(),
				Kz: address.GetSettlement().GetName(),
			},
			ParentID: address.GetSettlement().GetParentID(),
		})
	}
	if address.GetLocality() != nil {
		result = append(result, &entity.DictionaryKatoData{
			ID:        address.GetLocality().GetID(),
			Code:      address.GetLocality().GetCode(),
			CodeTsoid: uuid.NewString(),
			NameLocalized: entity.DictionaryKatoDataName{
				En: address.GetLocality().GetName(),
				Ru: address.GetLocality().GetName(),
				Kz: address.GetLocality().GetName(),
			},
			ParentID: address.GetLocality().GetParentID(),
		})
	}
	return result
}

func (s *Suite) katoDataToDictKatoData(katoData *pbLoans.KatoData) *entity.DictionaryKatoData {
	return &entity.DictionaryKatoData{
		ID:   katoData.GetID(),
		Code: katoData.GetCode(),
		NameLocalized: entity.DictionaryKatoDataName{
			En: katoData.GetName(),
			Ru: katoData.GetName(),
			Kz: katoData.GetName(),
		},
		ParentID: katoData.GetParentID(),
	}
}

func (s *Suite) dictKatoDataToDictDocsList(dictKatoData []*entity.DictionaryKatoData) []*pbDictionary.Doc {
	var dictDocsList []*pbDictionary.Doc
	for _, dictKatoData := range dictKatoData {
		jsonData, err := json.Marshal(dictKatoData)
		s.Require().NoError(err)
		dictDocsList = append(dictDocsList, &pbDictionary.Doc{
			Data: string(jsonData),
		})
	}
	return dictDocsList
}

func (s *Suite) dictOfferDataToDictDocsList(dictOfferData []*entity.DictionaryOfferInfo) []*pbDictionary.Doc {
	var dictDocsList []*pbDictionary.Doc
	for _, dictOfferData := range dictOfferData {
		jsonData, err := json.Marshal(dictOfferData)
		s.Require().NoError(err)
		dictDocsList = append(dictDocsList, &pbDictionary.Doc{
			Data: string(jsonData),
		})
	}
	return dictDocsList
}

func (s *Suite) makeUsersGetPersonalDataMockResponse(age uint32, getSurveyAddress *pbLoans.GetSurveyAddress) *pbUsers.GetPersonalDataResp {
	now, err := utils.GetCurrentKzTime()
	s.Require().NoError(err)

	birthDateStr := now.AddDate(-int(age), 0, 0).Format("2006-01-02")

	return &pbUsers.GetPersonalDataResp{
		Surname:    testUserSurname,
		Name:       testUserName,
		Patronymic: testUserPatronymic,
		Age:        age,
		BirthDate:  birthDateStr,
		Gender: &pbUsers.SubjectDataParams{
			Code: 1,
		},
		ColvirInfo: &pbUsers.UserColvirInfo{
			ColvirClientCode:  uuid.NewString(),
			ColvirClientDepId: uuid.NewString(),
			ColvirClientId:    uuid.NewString(),
		},
		RegAddress: &pbUsers.RegAddress{
			Region: &pbUsers.SubjectDataParams{
				NameRu: getSurveyAddress.GetRegion().GetName(),
				NameKz: getSurveyAddress.GetRegion().GetName(),
				Code:   rand.Int64(),
			},
			District: &pbUsers.SubjectDataParams{
				NameRu: getSurveyAddress.GetDistrict().GetName(),
				NameKz: getSurveyAddress.GetDistrict().GetName(),
				Code:   rand.Int64(),
			},
			City:     getSurveyAddress.GetSettlementArea().GetName(),
			Street:   getSurveyAddress.GetStreet(),
			Building: getSurveyAddress.GetBuilding(),
			Flat:     getSurveyAddress.GetFlat(),
		},
		Citizenship: &pbUsers.SubjectDataParams{
			Code: consts.UsersCitizenshipKazakhstanCode,
		},
		Documents: []*pbUsers.Document{
			{
				Type: &pbUsers.DocumentDataParams{
					Code: consts.UsersCitizenshipDocTypeCode,
				},
				Status: &pbUsers.DocumentDataParams{
					Code: consts.UsersDocumentStatusValidCode,
				},
				Number: uuid.NewString(),
				IssueOrganization: &pbUsers.DocumentDataParams{
					NameRu: uuid.NewString(),
				},
				BeginDate: uuid.NewString(),
			},
		},
	}
}

func (s *Suite) generateGetLoansExpectedResp(status consts.LoanApplicationStatus, lang string) *pbLoans.GetLoansResp {
	offer := &pbLoans.Offer{}

	// Добавляем SubText для обоих языков
	subTextRu := fmt.Sprintf(testOfferSubTextRu, testLoanAmountMax/1000000, testLoanTermMax/12)
	subTextKz := fmt.Sprintf(testOfferSubTextKk, testLoanAmountMax/1000000, testLoanTermMax/12)

	switch lang {
	case "ru":
		offer.Title = testOfferTitleRu
		offer.Text = testOfferTextRu
		offer.SubText = subTextRu
	case "kk":
		offer.Title = testOfferTitleKz
		offer.Text = testOfferTextKz
		offer.SubText = subTextKz
	default:
		// По умолчанию используем русский язык
		offer.Title = testOfferTitleRu
		offer.Text = testOfferTextRu
		offer.SubText = subTextRu
	}

	statusStr := status.String()
	loanAmountStr := fmt.Sprintf("%d", testLoanAmount)

	loan := &pbLoans.Loan{
		ProductType: entity.DefaultProductType,
		Status:      &statusStr,
		Amount: &pbLoans.Amount{
			Value:        loanAmountStr,
			CurrencyCode: entity.DefaultCurrencyCode,
		},
		NextPayment: &pbLoans.NextPaymentInfo{
			Amount: &pbLoans.Amount{},
		},
		Purpose: &testLoanPurpose.Description,
	}

	switch status {
	case consts.LoanApplicationStatusWaiting:
	case consts.LoanApplicationStatusApproved:
		loan.ApplicationDueDate = timestamppb.New(testTimeDueDate.AddDate(0, 0, 5))
	case consts.LoanApplicationStatusPendingFunds:
	case consts.LoanApplicationStatusCompleted:
		loan.Amount.Value = "75826.85"
		loan.PercentPaid = &testLoanPercentPaid
		loan.NextPayment.Date = timestamppb.New(time.Date(2025, time.June, 11, 0, 0, 0, 0, time.UTC))
		loan.NextPayment.Amount.Value = "75826.85"
		loan.NextPayment.Amount.CurrencyCode = entity.DefaultCurrencyCode
	}

	return &pbLoans.GetLoansResp{
		Offer: offer,
		Loans: []*pbLoans.Loan{loan},
	}
}

func addIPAndUserAgentAndPlatformToContext(ctx context.Context) context.Context {
	ctx = context.WithValue(ctx, mw.CtxKeyRealIP, uuid.NewString())
	ctx = context.WithValue(ctx, mw.CtxKeyUserAgent, testUserAgent)
	ctx = context.WithValue(ctx, mw.CtxKeyUserPlatform, testUserPlatformStr)
	return ctx
}

func (s *Suite) generateLoanApplication() (*entity.LoanApplication, error) {
	loanApplication := &entity.LoanApplication{}
	err := faker.FakeData(&loanApplication)
	if err != nil {
		return nil, err
	}
	loanApplication.ID = uuid.NewString()
	loanApplication.Type = consts.LoanApplicationTypeLoan
	loanApplication.Status = consts.LoanApplicationStatusDraft
	loanApplication.Purpose = testLoanPurpose.Description
	loanApplication.UserID = testUserID.String()
	loanApplication.UserIin = testUserIin
	loanApplication.JuicySessionID = testJuicySessionID
	loanApplication.BusinessType = consts.LoanApplicationBusinessTypeRetail
	loanApplication.UserFullNameAtCreation = testUserFullName
	return loanApplication, nil
}

func (s *Suite) generateExternalBankLoan() ([]*entity.ExternalBankLoan, error) {
	externalBankLoans := []*entity.ExternalBankLoan{
		{},
	}
	err := faker.FakeData(&externalBankLoans)
	if err != nil {
		return nil, err
	}
	for _, externalBankLoan := range externalBankLoans {
		externalBankLoan.ID = uuid.NewString()
	}
	normalizeContractDates(externalBankLoans)
	return externalBankLoans, nil
}

func (s *Suite) createExternalBankLoanStorage(ctx context.Context, externalBankLoans []*entity.ExternalBankLoan) ([]*ent.ExternalBankLoan, error) {
	externalBankLoansCreateRecords := make([]*ent.ExternalBankLoanCreate, 0, len(externalBankLoans))
	for _, externalBankLoan := range externalBankLoans {
		record := s.postgresDB.ExternalBankLoan.Create().
			SetID(uuid.MustParse(externalBankLoan.ID)).
			SetBankName(externalBankLoan.BankName).
			SetBankBin(externalBankLoan.BankBIN).
			SetOutstandingAmount(externalBankLoan.OutstandingAmount).
			SetPaymentAmount(externalBankLoan.PaymentAmount).
			SetCurrencyCode(externalBankLoan.CurrencyCode).
			SetContractNumber(externalBankLoan.ContractNumber).
			SetContractDate(externalBankLoan.ContractDate).
			SetColvirPaymentReference(externalBankLoan.ColvirPaymentReference)
		externalBankLoansCreateRecords = append(externalBankLoansCreateRecords, record)
	}
	externalBankLoansStorage, err := s.postgresDB.ExternalBankLoan.CreateBulk(externalBankLoansCreateRecords...).Save(ctx)
	return externalBankLoansStorage, err
}

func normalizeContractDates(loans []*entity.ExternalBankLoan) {
	for _, loan := range loans {
		loan.ContractDate = loan.ContractDate.Truncate(time.Microsecond)
	}
}

func (s *Suite) generateRefinancingConditions() (*entity.RefinancingConditions, error) {
	refinancingConditions := &entity.RefinancingConditions{}
	err := faker.FakeData(refinancingConditions)
	if err != nil {
		return nil, err
	}
	refinancingConditions.ID = uuid.NewString()
	refinancingConditions.TotalAmount = testRefinancingTotalAmount
	refinancingConditions.RefAmount = testRefinancingRefAmount
	refinancingConditions.CreditAmount = testRefinancingCreditAmount
	refinancingConditions.Interest = testLoanTermInterest.LoanInterest
	refinancingConditions.Term = testLoanTermInterest.LoanTerm
	refinancingConditions.KDN = rand.Float64()
	refinancingConditions.KDD = rand.Float64()
	return refinancingConditions, nil
}

func (s *Suite) createLoanConditionsStorage(loanConditions *entity.LoanConditions, loanAppStorage *ent.LoanApplication) (*ent.LoanCondition, error) {
	result, err := s.postgresDB.LoanCondition.Create().
		SetAmount(loanConditions.Amount).
		SetInterest(loanConditions.Interest).
		SetTerm(loanConditions.Term).
		SetIsOriginal(loanConditions.IsOriginal).
		SetIsApprovedBySpr(loanConditions.IsApprovedBySpr).
		SetIsFinallyChosenByUser(loanConditions.IsFinallyChosenByUser).
		SetApplication(loanAppStorage).
		Save(s.ctx)
	return result, err
}

func (s *Suite) generateLoanConditions() (*entity.LoanConditions, error) {
	loanConditions := &entity.LoanConditions{}
	err := faker.FakeData(loanConditions)
	if err != nil {
		return nil, err
	}
	loanConditions.ID = uuid.NewString()
	loanConditions.Amount = testLoanAmount
	loanConditions.Interest = testLoanTermInterest.LoanInterest
	loanConditions.Term = testLoanTermInterest.LoanTerm
	loanConditions.KDN = rand.Float64()
	loanConditions.KDD = rand.Float64()
	loanConditions.IsOriginal = true
	loanConditions.IsApprovedBySpr = false
	loanConditions.IsFinallyChosenByUser = false
	return loanConditions, nil
}

func (s *Suite) createRefinancingConditionsStorage(refinancingConditions *entity.RefinancingConditions, loanAppStorage *ent.LoanApplication) (*ent.RefinancingCondition, error) {
	refinancingConditionsStorage, err := s.postgresDB.RefinancingCondition.Create().
		SetID(uuid.MustParse(refinancingConditions.ID)).
		SetTotalAmount(refinancingConditions.TotalAmount).
		SetRefAmount(refinancingConditions.RefAmount).
		SetCreditAmount(refinancingConditions.CreditAmount).
		SetKdd(refinancingConditions.KDD).
		SetKdn(refinancingConditions.KDN).
		SetTerm(refinancingConditions.Term).
		SetInterest(refinancingConditions.Interest).
		SetApplication(loanAppStorage).
		SetCreateTime(refinancingConditions.CreatedAt).
		Save(s.ctx)
	return refinancingConditionsStorage, err
}

func (s *Suite) createHistoryActionStorage(ctx context.Context, historyAction *entity.HistoryAction) (*ent.ActionsHistory, error) {
	return s.postgresDB.ActionsHistory.Create().
		SetApplicationID(uuid.MustParse(historyAction.ApplicationID)).
		SetActionType(actionshistory.ActionType(historyAction.ActionType)).
		SetMessage(historyAction.Message).
		SetCreateTime(historyAction.CreatedAt).
		Save(ctx)
}
