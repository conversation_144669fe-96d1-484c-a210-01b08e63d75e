package tests

import (
	"context"
	"encoding/json"
	"math/rand"
	"strconv"

	"entgo.io/ent/dialect/sql"
	"github.com/go-faker/faker/v4"
	"github.com/golang/mock/gomock"
	"github.com/google/uuid"
	"google.golang.org/grpc"

	"git.redmadrobot.com/zaman/backend/zaman/services/loans/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/storage/postgres/ent/actionshistory"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/storage/postgres/ent/disbursementcontrol"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/storage/postgres/ent/document"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/storage/postgres/ent/loanapplication"
	cards_accounts "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"
	"git.redmadrobot.com/zaman/backend/zaman/specs/proto/dictionary"
	"git.redmadrobot.com/zaman/backend/zaman/specs/proto/documents"
	"git.redmadrobot.com/zaman/backend/zaman/specs/proto/liveness"
	"git.redmadrobot.com/zaman/backend/zaman/specs/proto/loans"
	pkb_bridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/pkb-bridge"
	pbTaskManager "git.redmadrobot.com/zaman/backend/zaman/specs/proto/task-manager"
	usersPb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"
)

func (s *Suite) TestPostIdentifyBtsDataSME_Success() {
	s.ctx = addUserIDToContext(s.ctx)

	loanAppStorage, err := s.postgresDB.LoanApplication.Create().
		SetBusinessType(loanapplication.BusinessTypeRETAIL).
		SetStatus(loanapplication.StatusAPPROVED).
		SetUserID(testUserID.String()).
		SetPurpose(uuid.NewString()).
		SetUserIin(uuid.NewString()).
		SetJuicySessionID(testJuicySessionID).
		SetUserFullNameAtCreation(testUserFullName).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(loanAppStorage)

	loanConditionStorage, err := s.postgresDB.LoanCondition.Create().
		SetAmount(uint32(rand.Int31())).
		SetInterest(uint32(rand.Int31())).
		SetTerm(uint32(rand.Int31())).
		SetIsOriginal(true).
		SetIsApprovedBySpr(true).
		SetIsFinallyChosenByUser(true).
		SetApplication(loanAppStorage).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(loanConditionStorage)

	_, err = s.postgresDB.DisbursementControl.Create().
		SetMode(disbursementcontrol.Mode(consts.LoanDisbursementAutoMode)).
		Save(s.ctx)
	s.Require().NoError(err)

	expectedGetSurveyResp := s.generateGetSurveyResp(5)
	dictKatoData := s.surveyAddressToDictKatoData(expectedGetSurveyResp.GetAddress())
	dictExpectedDocsList := s.dictKatoDataToDictDocsList(dictKatoData)
	surveyStorage, err := s.createSurveyStorageByGetSurveyResp(expectedGetSurveyResp, loanAppStorage.ID)
	s.Require().NoError(err)
	s.Require().NotNil(surveyStorage)

	s.mocks.GRPC.Pkbbridge.EXPECT().GetPermitDocumentsByIin(gomock.Any(), gomock.Any()).Return(successfulGetPermittedDocsResp, nil).Times(1)

	s.mocks.GRPC.Dictionary.EXPECT().DocGetListByFilter(gomock.Any(), gomock.Any()).Return(
		&dictionary.DocGetListByFilterResp{
			List: dictExpectedDocsList,
		}, nil,
	).Times(2)

	s.mocks.GRPC.Pkbbridge.EXPECT().SendJurSearchByIin(gomock.Any(), gomock.Any()).Return(&pkb_bridge.SendJurSearchByIinResp{
		Iin:  testIIN,
		Name: testUserFullName,
	}, nil).Times(1)

	postIdentifyBtsDataUsersGetPersonalDataRespSuccess, err := getPostIdentifyBtsDataUsersGetPersonalDataRespSuccess()
	s.Require().NoError(err)

	livenessPersonalData := &liveness.PersonalData{}
	err = faker.FakeData(livenessPersonalData)
	s.Require().NoError(err)
	livenessPersonalData.Name = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Name
	livenessPersonalData.Surname = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Surname
	livenessPersonalData.Patronymic = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Patronymic

	if len(livenessPersonalData.Documents) == 0 {
		livenessPersonalData.Documents = []*liveness.Document{
			{
				Type:   &liveness.DocumentType{},
				Status: &liveness.DocumentStatus{},
			},
		}
	}

	livenessPersonalData.Documents[0].Type.Code = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Documents[0].Type.Code
	livenessPersonalData.Documents[0].Status.Code = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Documents[0].Status.Code
	livenessPersonalData.Documents[0].BeginDate = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Documents[0].BeginDate
	livenessPersonalData.Documents[0].EndDate = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Documents[0].EndDate
	livenessPersonalData.Citizenship.Code = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Citizenship.Code
	livenessPersonalData.BirthDate = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.BirthDate
	s.mocks.GRPC.Liveness.EXPECT().VerifyAndGetPersonalData(gomock.Any(), gomock.Any()).Return(
		&liveness.VerifyLivenessCodeResp{
			PersonalData: livenessPersonalData,
		}, nil,
	).Times(1)

	s.mocks.GRPC.Users.EXPECT().GetPersonalData(
		gomock.Any(), gomock.Any(),
	).Return(postIdentifyBtsDataUsersGetPersonalDataRespSuccess, nil).Times(1)

	cardsAccountsMockResp := &cards_accounts.GetAccountsResponse{
		Accounts: []*cards_accounts.Account{
			{
				Status: consts.BankAccountStatusActive.String(),
				ID:     uuid.NewString(),
				Arrest: &cards_accounts.Arrest{
					Blocking: false,
				},
				Currency: consts.DefaultKazakhstanCurrency,
			},
		},
	}
	s.mocks.GRPC.Cardsaccounts.EXPECT().GetAccounts(gomock.Any(), gomock.Any()).Return(
		cardsAccountsMockResp, nil,
	).Times(1)

	employmentDict := s.getEmploymentDict()
	dictEmploymentJsonData, err := json.Marshal(employmentDict)
	s.Require().NoError(err)

	relationDict := s.getRelationDict()
	dictRelationJsonData, err := json.Marshal(relationDict)
	s.Require().NoError(err)

	docGetByNameCall := func(_ context.Context, req *dictionary.DocGetByNameReq, _ ...grpc.CallOption) (*dictionary.DocGetByNameResp, error) {
		switch req.DictName {
		case consts.DictNameRelationTypes:
			return &dictionary.DocGetByNameResp{
				Doc: &dictionary.Doc{
					Id:   "1",
					Name: consts.DictNameRelationTypes,
					Data: string(dictRelationJsonData),
				},
			}, nil
		case consts.DictNameEmploymentTypes:
			return &dictionary.DocGetByNameResp{
				Doc: &dictionary.Doc{
					Id:   "1",
					Name: consts.DictNameEmploymentTypes,
					Data: string(dictEmploymentJsonData),
				},
			}, nil
		case consts.DictNameEducationTypes:
			dictEducationData := s.getEducationDict()
			dictEducationJsonData, err := json.Marshal(dictEducationData)
			s.Require().NoError(err)

			return &dictionary.DocGetByNameResp{
				Doc: &dictionary.Doc{
					Id:   "1",
					Name: consts.DictNameEducationTypes,
					Data: string(dictEducationJsonData),
				},
			}, nil
		case consts.DictNameExchangeInfo:
			dictExchangeInfo := s.getExchangeInfo()
			dictExchangeJsonData, err := json.Marshal(dictExchangeInfo)
			s.Require().NoError(err)

			return &dictionary.DocGetByNameResp{
				Doc: &dictionary.Doc{
					Data: string(dictExchangeJsonData),
				},
			}, nil
		case consts.DictNameProductExchangeInfo:
			productExchangeInfo := entity.DictionaryProductExchangeInfo{
				ProductName:  uuid.NewString(),
				ProductPrice: rand.Uint64(),
				CurrencyRate: strconv.FormatFloat(rand.Float64(), 'f', -1, 64),
			}

			productExchangeInfoJson, err := json.Marshal(productExchangeInfo)
			s.Require().NoError(err)

			return &dictionary.DocGetByNameResp{
				Doc: &dictionary.Doc{
					Data: string(productExchangeInfoJson),
				},
			}, nil
		case consts.DictNameZamanBankInfo:
			mockZamanBankInfo := entity.DictionaryZamanBankInfo{
				Bin: uuid.NewString(),
				Address: entity.BankInfoLocalizedValue{
					RU: uuid.NewString(),
					KZ: uuid.NewString(),
				},
				City: entity.BankInfoLocalizedValue{
					RU: uuid.NewString(),
					KZ: uuid.NewString(),
				},
				PhoneNumber: uuid.NewString(),
				Email:       uuid.NewString(),
				Iik:         uuid.NewString(),
				Bic:         uuid.NewString(),
				Name: entity.BankInfoLocalizedValue{
					RU: uuid.NewString(),
					KZ: uuid.NewString(),
				},
				Representative: entity.BankInfoRepresentative{
					FullName: uuid.NewString(),
					JobPosition: entity.BankInfoLocalizedValue{
						RU: uuid.NewString(),
						KZ: uuid.NewString(),
					},
					DocumentNameGenitive: entity.BankInfoLocalizedValue{
						RU: uuid.NewString(),
						KZ: uuid.NewString(),
					},
				},
				Links: entity.BankInfoLinks{
					Main:      uuid.NewString(),
					Documents: uuid.NewString(),
				},
			}

			mockZamanInfoJson, err := json.Marshal(mockZamanBankInfo)
			s.Require().NoError(err)

			return &dictionary.DocGetByNameResp{
				Doc: &dictionary.Doc{
					Data: string(mockZamanInfoJson),
				},
			}, nil
		case consts.DictNameProductionCalendar:
			mockProductionCalendar := s.getProductionCalendar()
			mockProductionCalendarJson, err := json.Marshal(mockProductionCalendar)
			s.Require().NoError(err)

			return &dictionary.DocGetByNameResp{
				Doc: &dictionary.Doc{
					Data: string(mockProductionCalendarJson),
				},
			}, nil
		}

		return nil, nil
	}

	s.mocks.GRPC.Dictionary.EXPECT().
		DocGetByName(gomock.Any(), gomock.Any()).
		DoAndReturn(docGetByNameCall).
		Times(11)

	s.mocks.GRPC.Colvirbridge.EXPECT().LoanCalcLoadPreSchedule(gomock.Any(), gomock.Any()).Return(
		testColvirbridgeLoanCalcLoadPreScheduleResponse, nil,
	).Times(1)

	s.mocks.GRPC.Documents.EXPECT().GenerateCreditContractWithRepaymentScheduleSMEIP(gomock.Any(), gomock.Any()).Return(
		&documents.DocumentResp{
			Id:      uuid.NewString(),
			Title:   uuid.NewString(),
			Type:    consts.DocTypeCreditContractWithRepaymentScheduleSMEIP.String(),
			Version: rand.Int31(),
			Link:    uuid.NewString(),
		}, nil,
	).Times(1)

	s.mocks.GRPC.Documents.EXPECT().GenerateAppSaleOfGoodsDocumentSMEIP(gomock.Any(), gomock.Any()).Return(
		&documents.DocumentResp{
			Id:      uuid.NewString(),
			Title:   uuid.NewString(),
			Type:    consts.DocTypeApplicationSaleOfGoodsSMEIP.String(),
			Version: rand.Int31(),
			Link:    uuid.NewString(),
		}, nil,
	).Times(1)

	s.mocks.GRPC.Documents.EXPECT().GenerateBankServiceApplicationSMEIP(gomock.Any(), gomock.Any()).Return(&documents.DocumentResp{
		Id:      uuid.NewString(),
		Title:   uuid.NewString(),
		Type:    consts.DocTypeBankingServiceApplicationSMEIP.String(),
		Version: rand.Int31(),
		Link:    uuid.NewString(),
	}, nil).Times(1)

	_, err = s.postgresDB.ActionsHistory.Create().
		SetActionType(actionshistory.ActionType(consts.ApprovedLoanAppInternalChecksPassed)).
		SetApplicationID(loanAppStorage.ID).
		Save(s.ctx)
	s.Require().NoError(err)

	createTaskMockResp := &pbTaskManager.CreateTaskResp{}
	err = faker.FakeData(createTaskMockResp)
	s.Require().NoError(err)

	s.mocks.GRPC.Taskmanager.EXPECT().CreateTask(gomock.Any(), gomock.Any()).Return(createTaskMockResp, nil).Times(1)

	_, err = s.postgresDB.Document.Create().
		SetType(document.Type(consts.DocTypeBankingServiceApplicationSMEIP)).
		SetDocID(uuid.New().String()).
		SetApplicationID(loanAppStorage.ID).
		Save(s.ctx)
	s.Require().NoError(err)

	successfulGetPermittedDocsResp := &pkb_bridge.GetPermitDocumentsByIinResp{
		Iin: testIIN,
		TaxPayerLicenses: []*pkb_bridge.TaxPayerLicense{
			{
				ActivityType: &pkb_bridge.ActivityType{
					Code:   "NotRestricted",
					NameRu: "string",
					NameKz: "string",
				},
				DocumentID:        "*********",
				ValidityStartDate: "2022-01-01T00:00:00",
				ValidityEndDate:   "2025-01-01T00:00:00",
				Licensiar: &pkb_bridge.Licensiar{
					Code:   "ABC123",
					NameRu: "Test Licensing Authority",
					NameKz: "Test Licensing Authority KZ",
				},
			},
		},
	}
	s.mocks.GRPC.Pkbbridge.EXPECT().GetPermitDocumentsByIin(gomock.Any(), gomock.Any()).Return(successfulGetPermittedDocsResp, nil).Times(1)

	resp, err := s.grpc.PostIdentifyBtsDataSme(
		s.ctx, &loans.PostIdentifyBtsDataSmeReq{
			ApplicationID: loanAppStorage.ID.String(),
			Code:          uuid.NewString(),
		},
	)
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().Nil(resp.Reason)
	s.Require().Equal(consts.LoanApplicationStatusApproved.String(), resp.ApplicationStatus)

	storageLoanApp, err := s.postgresDB.LoanApplication.Query().Where(loanapplication.ID(loanAppStorage.ID)).First(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(storageLoanApp)
	s.Require().NotNil(storageLoanApp.DisbursementAccountID)
	s.Require().Equal(cardsAccountsMockResp.GetAccounts()[0].ID, *storageLoanApp.DisbursementAccountID)

	docsApplicationSaleOfGoods, err := s.postgresDB.Document.Query().Where(
		document.HasApplicationWith(
			func(selector *sql.Selector) {
				selector.Where(sql.EQ(loanapplication.FieldID, loanAppStorage.ID))
			},
		),
		document.TypeEQ(document.Type(consts.DocTypeApplicationSaleOfGoodsSMEIP)),
	).All(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(docsApplicationSaleOfGoods)
	s.Require().NotNil(docsApplicationSaleOfGoods[0])
	s.Require().Equal(docsApplicationSaleOfGoods[0].Type.String(), consts.DocTypeApplicationSaleOfGoodsSMEIP.String())

	docsCreditContract, err := s.postgresDB.Document.Query().Where(
		document.HasApplicationWith(
			func(selector *sql.Selector) {
				selector.Where(sql.EQ(loanapplication.FieldID, loanAppStorage.ID))
			},
		),
		document.TypeEQ(document.Type(consts.DocTypeCreditContractWithRepaymentScheduleSMEIP)),
	).All(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(docsCreditContract)
	s.Require().NotNil(docsCreditContract[0])
	s.Require().Equal(
		docsCreditContract[0].Type.String(), consts.DocTypeCreditContractWithRepaymentScheduleSMEIP.String(),
	)

	docsBankingServiceApplication, err := s.postgresDB.Document.Query().Where(
		document.HasApplicationWith(
			func(selector *sql.Selector) {
				selector.Where(sql.EQ(loanapplication.FieldID, loanAppStorage.ID))
			},
		),
		document.TypeEQ(document.Type(consts.DocTypeBankingServiceApplicationSMEIP)),
	).All(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(docsBankingServiceApplication)
	s.Require().NotNil(docsBankingServiceApplication[0])
	s.Require().Equal(
		docsBankingServiceApplication[0].Type.String(), consts.DocTypeBankingServiceApplicationSMEIP.String(),
	)
}

func (s *Suite) TestPostIdentifyBtsDataSme_NoEligibleBankAccount() {
	s.ctx = addUserIDToContext(s.ctx)

	loanAppStorage, err := s.postgresDB.LoanApplication.Create().
		SetBusinessType(loanapplication.BusinessTypeRETAIL).
		SetStatus(loanapplication.StatusAPPROVED).
		SetUserID(testUserID.String()).
		SetPurpose(uuid.NewString()).
		SetUserIin(uuid.NewString()).
		SetJuicySessionID(testJuicySessionID).
		SetUserFullNameAtCreation(testUserFullName).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(loanAppStorage)

	loanConditionStorage, err := s.postgresDB.LoanCondition.Create().
		SetAmount(uint32(rand.Int31())).
		SetInterest(uint32(rand.Int31())).
		SetTerm(uint32(rand.Int31())).
		SetIsOriginal(true).
		SetIsApprovedBySpr(true).
		SetIsFinallyChosenByUser(true).
		SetApplication(loanAppStorage).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(loanConditionStorage)

	s.mocks.GRPC.Users.EXPECT().GetUserSmeIPInfo(gomock.Any(), gomock.Any()).Return(&usersPb.GetUserSmeIPInfoResp{
		Phone: "+***********",
		SmeIpInfo: &usersPb.SmeIPInfo{
			RegistrationDate: "2020-01-01",
			RegistrationCert: "cert123",
		},
	}, nil).Times(1)

	s.mocks.GRPC.Pkbbridge.EXPECT().GetPermitDocumentsByIin(gomock.Any(), gomock.Any()).Return(successfulGetPermittedDocsResp, nil).Times(1)

	postIdentifyBtsDataUsersGetPersonalDataRespSuccess, err := getPostIdentifyBtsDataUsersGetPersonalDataRespSuccess()
	s.Require().NoError(err)

	s.mocks.GRPC.Users.EXPECT().
		GetPersonalData(gomock.Any(), gomock.Any()).
		Return(postIdentifyBtsDataUsersGetPersonalDataRespSuccess, nil).
		Times(1)

	calendarInfo, err := json.Marshal(docCalendarInfo)
	s.Require().NoError(err)

	s.mocks.GRPC.Dictionary.EXPECT().DocGetByName(gomock.Any(), gomock.Any()).Return(&dictionary.DocGetByNameResp{
		Doc: &dictionary.Doc{
			Id:   "0",
			Data: string(calendarInfo),
		},
	}, nil).Times(1)

	livenessPersonalData := &liveness.PersonalData{}
	err = faker.FakeData(livenessPersonalData)
	s.Require().NoError(err)
	livenessPersonalData.Name = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Name
	livenessPersonalData.Surname = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Surname
	livenessPersonalData.Patronymic = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Patronymic

	if len(livenessPersonalData.Documents) == 0 {
		livenessPersonalData.Documents = []*liveness.Document{
			{
				Type:   &liveness.DocumentType{},
				Status: &liveness.DocumentStatus{},
			},
		}
	}

	livenessPersonalData.Documents[0].Type.Code = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Documents[0].Type.Code
	livenessPersonalData.Documents[0].Status.Code = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Documents[0].Status.Code
	livenessPersonalData.Documents[0].BeginDate = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Documents[0].BeginDate
	livenessPersonalData.Documents[0].EndDate = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Documents[0].EndDate
	livenessPersonalData.Documents[0].IssueOrganization = &liveness.DocumentIssueOrganization{
		NameRu:     postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Documents[0].IssueOrganization.NameRu,
		Code:       postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Documents[0].IssueOrganization.Code,
		NameKz:     postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Documents[0].IssueOrganization.NameKz,
		ChangeDate: postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Documents[0].IssueOrganization.ChangeDate,
	}
	livenessPersonalData.Citizenship.Code = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Citizenship.Code
	livenessPersonalData.BirthDate = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.BirthDate

	s.mocks.GRPC.Liveness.EXPECT().
		VerifyAndGetPersonalData(gomock.Any(), gomock.Any()).
		Return(
			&liveness.VerifyLivenessCodeResp{
				PersonalData: livenessPersonalData,
			}, nil,
		).
		Times(1)

	cardsAccountsMockResp := &cards_accounts.GetAccountsResponse{
		Accounts: []*cards_accounts.Account{
			{
				Status: "", // активного счета нет
				ID:     uuid.NewString(),
				Arrest: &cards_accounts.Arrest{
					Blocking: false,
				},
				Currency: consts.DefaultKazakhstanCurrency,
			},
		},
	}

	s.mocks.GRPC.Cardsaccounts.EXPECT().
		GetAccounts(gomock.Any(), gomock.Any()).
		Return(cardsAccountsMockResp, nil).
		Times(1)

	_, err = s.postgresDB.ActionsHistory.Create().
		SetActionType(actionshistory.ActionType(consts.ApprovedLoanAppInternalChecksPassed)).
		SetApplicationID(loanAppStorage.ID).
		Save(s.ctx)
	s.Require().NoError(err)

	s.mocks.GRPC.Users.EXPECT().GetUserSmeIPInfo(gomock.Any(), gomock.Any()).Return(&usersPb.GetUserSmeIPInfoResp{
		Phone: "+***********",
		SmeIpInfo: &usersPb.SmeIPInfo{
			RegistrationDate: "2020-01-01",
			RegistrationCert: "cert123",
		},
	}, nil).Times(1)

	resp, err := s.grpc.PostIdentifyBtsDataSme(
		s.ctx, &loans.PostIdentifyBtsDataSmeReq{
			ApplicationID: loanAppStorage.ID.String(),
			Code:          uuid.NewString(),
		},
	)
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().Equal(consts.LoanApplicationStatusRejected.String(), resp.ApplicationStatus)
	s.Require().NotNil(resp.GetReason())
	s.Require().Equal(consts.ApprovedLoanAppTechError.String(), resp.GetReason().GetCode())
}
