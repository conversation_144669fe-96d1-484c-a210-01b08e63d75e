// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge (interfaces: ColvirbridgeClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"

	colvir_bridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
)

// MockColvirbridgeClient is a mock of ColvirbridgeClient interface.
type MockColvirbridgeClient struct {
	ctrl     *gomock.Controller
	recorder *MockColvirbridgeClientMockRecorder
}

// MockColvirbridgeClientMockRecorder is the mock recorder for MockColvirbridgeClient.
type MockColvirbridgeClientMockRecorder struct {
	mock *MockColvirbridgeClient
}

// NewMockColvirbridgeClient creates a new mock instance.
func NewMockColvirbridgeClient(ctrl *gomock.Controller) *MockColvirbridgeClient {
	mock := &MockColvirbridgeClient{ctrl: ctrl}
	mock.recorder = &MockColvirbridgeClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockColvirbridgeClient) EXPECT() *MockColvirbridgeClientMockRecorder {
	return m.recorder
}

// CheckClientAgreementRKO mocks base method.
func (m *MockColvirbridgeClient) CheckClientAgreementRKO(arg0 context.Context, arg1 *colvir_bridge.CheckClientAgreementReq, arg2 ...grpc.CallOption) (*colvir_bridge.CheckClientAgreementResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckClientAgreementRKO", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.CheckClientAgreementResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckClientAgreementRKO indicates an expected call of CheckClientAgreementRKO.
func (mr *MockColvirbridgeClientMockRecorder) CheckClientAgreementRKO(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckClientAgreementRKO", reflect.TypeOf((*MockColvirbridgeClient)(nil).CheckClientAgreementRKO), varargs...)
}

// CheckDomesticMassPayment mocks base method.
func (m *MockColvirbridgeClient) CheckDomesticMassPayment(arg0 context.Context, arg1 *colvir_bridge.DomesticMassPaymentRequest, arg2 ...grpc.CallOption) (*colvir_bridge.CheckDomesticMassPaymentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckDomesticMassPayment", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.CheckDomesticMassPaymentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckDomesticMassPayment indicates an expected call of CheckDomesticMassPayment.
func (mr *MockColvirbridgeClientMockRecorder) CheckDomesticMassPayment(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckDomesticMassPayment", reflect.TypeOf((*MockColvirbridgeClient)(nil).CheckDomesticMassPayment), varargs...)
}

// CheckDomesticPayment mocks base method.
func (m *MockColvirbridgeClient) CheckDomesticPayment(arg0 context.Context, arg1 *colvir_bridge.DomesticPaymentRequest, arg2 ...grpc.CallOption) (*colvir_bridge.CheckDomesticPaymentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckDomesticPayment", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.CheckDomesticPaymentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckDomesticPayment indicates an expected call of CheckDomesticPayment.
func (mr *MockColvirbridgeClientMockRecorder) CheckDomesticPayment(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckDomesticPayment", reflect.TypeOf((*MockColvirbridgeClient)(nil).CheckDomesticPayment), varargs...)
}

// CloseLimit mocks base method.
func (m *MockColvirbridgeClient) CloseLimit(arg0 context.Context, arg1 *colvir_bridge.CloseLimitRequest, arg2 ...grpc.CallOption) (*colvir_bridge.CloseLimitResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CloseLimit", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.CloseLimitResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CloseLimit indicates an expected call of CloseLimit.
func (mr *MockColvirbridgeClientMockRecorder) CloseLimit(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloseLimit", reflect.TypeOf((*MockColvirbridgeClient)(nil).CloseLimit), varargs...)
}

// CreateClient mocks base method.
func (m *MockColvirbridgeClient) CreateClient(arg0 context.Context, arg1 *colvir_bridge.CreateClientRequest, arg2 ...grpc.CallOption) (*colvir_bridge.CreateClientResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateClient", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.CreateClientResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateClient indicates an expected call of CreateClient.
func (mr *MockColvirbridgeClientMockRecorder) CreateClient(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateClient", reflect.TypeOf((*MockColvirbridgeClient)(nil).CreateClient), varargs...)
}

// CreateClientAgreement mocks base method.
func (m *MockColvirbridgeClient) CreateClientAgreement(arg0 context.Context, arg1 *colvir_bridge.CreateClientAgreementReq, arg2 ...grpc.CallOption) (*colvir_bridge.CreateClientAgreementResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateClientAgreement", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.CreateClientAgreementResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateClientAgreement indicates an expected call of CreateClientAgreement.
func (mr *MockColvirbridgeClientMockRecorder) CreateClientAgreement(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateClientAgreement", reflect.TypeOf((*MockColvirbridgeClient)(nil).CreateClientAgreement), varargs...)
}

// CreateClientSMEIP mocks base method.
func (m *MockColvirbridgeClient) CreateClientSMEIP(arg0 context.Context, arg1 *colvir_bridge.CreateClientSMEIPRequest, arg2 ...grpc.CallOption) (*colvir_bridge.CreateClientSMEIPResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateClientSMEIP", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.CreateClientSMEIPResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateClientSMEIP indicates an expected call of CreateClientSMEIP.
func (mr *MockColvirbridgeClientMockRecorder) CreateClientSMEIP(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateClientSMEIP", reflect.TypeOf((*MockColvirbridgeClient)(nil).CreateClientSMEIP), varargs...)
}

// CreateLimit mocks base method.
func (m *MockColvirbridgeClient) CreateLimit(arg0 context.Context, arg1 *colvir_bridge.CreateLimitRequest, arg2 ...grpc.CallOption) (*colvir_bridge.CreateLimitResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateLimit", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.CreateLimitResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateLimit indicates an expected call of CreateLimit.
func (mr *MockColvirbridgeClientMockRecorder) CreateLimit(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLimit", reflect.TypeOf((*MockColvirbridgeClient)(nil).CreateLimit), varargs...)
}

// DeleteLimit mocks base method.
func (m *MockColvirbridgeClient) DeleteLimit(arg0 context.Context, arg1 *colvir_bridge.DeleteLimitRequest, arg2 ...grpc.CallOption) (*colvir_bridge.DeleteLimitResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteLimit", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.DeleteLimitResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteLimit indicates an expected call of DeleteLimit.
func (mr *MockColvirbridgeClientMockRecorder) DeleteLimit(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteLimit", reflect.TypeOf((*MockColvirbridgeClient)(nil).DeleteLimit), varargs...)
}

// ExecuteDepositOperation mocks base method.
func (m *MockColvirbridgeClient) ExecuteDepositOperation(arg0 context.Context, arg1 *colvir_bridge.DepositOperationRequest, arg2 ...grpc.CallOption) (*colvir_bridge.DepositOperationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ExecuteDepositOperation", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.DepositOperationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExecuteDepositOperation indicates an expected call of ExecuteDepositOperation.
func (mr *MockColvirbridgeClientMockRecorder) ExecuteDepositOperation(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteDepositOperation", reflect.TypeOf((*MockColvirbridgeClient)(nil).ExecuteDepositOperation), varargs...)
}

// ExecuteDomesticMassPayment mocks base method.
func (m *MockColvirbridgeClient) ExecuteDomesticMassPayment(arg0 context.Context, arg1 *colvir_bridge.DomesticMassPaymentRequest, arg2 ...grpc.CallOption) (*colvir_bridge.ExecuteDomesticMassPaymentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ExecuteDomesticMassPayment", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.ExecuteDomesticMassPaymentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExecuteDomesticMassPayment indicates an expected call of ExecuteDomesticMassPayment.
func (mr *MockColvirbridgeClientMockRecorder) ExecuteDomesticMassPayment(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteDomesticMassPayment", reflect.TypeOf((*MockColvirbridgeClient)(nil).ExecuteDomesticMassPayment), varargs...)
}

// ExecuteDomesticPayment mocks base method.
func (m *MockColvirbridgeClient) ExecuteDomesticPayment(arg0 context.Context, arg1 *colvir_bridge.DomesticPaymentRequest, arg2 ...grpc.CallOption) (*colvir_bridge.ExecuteDomesticPaymentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ExecuteDomesticPayment", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.ExecuteDomesticPaymentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExecuteDomesticPayment indicates an expected call of ExecuteDomesticPayment.
func (mr *MockColvirbridgeClientMockRecorder) ExecuteDomesticPayment(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteDomesticPayment", reflect.TypeOf((*MockColvirbridgeClient)(nil).ExecuteDomesticPayment), varargs...)
}

// FindClient mocks base method.
func (m *MockColvirbridgeClient) FindClient(arg0 context.Context, arg1 *colvir_bridge.FindClientReq, arg2 ...grpc.CallOption) (*colvir_bridge.FindClientResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindClient", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.FindClientResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindClient indicates an expected call of FindClient.
func (mr *MockColvirbridgeClientMockRecorder) FindClient(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindClient", reflect.TypeOf((*MockColvirbridgeClient)(nil).FindClient), varargs...)
}

// FindClientAccount mocks base method.
func (m *MockColvirbridgeClient) FindClientAccount(arg0 context.Context, arg1 *colvir_bridge.FindClientAccountReq, arg2 ...grpc.CallOption) (*colvir_bridge.FindClientAccountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindClientAccount", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.FindClientAccountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindClientAccount indicates an expected call of FindClientAccount.
func (mr *MockColvirbridgeClientMockRecorder) FindClientAccount(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindClientAccount", reflect.TypeOf((*MockColvirbridgeClient)(nil).FindClientAccount), varargs...)
}

// FindClientAccountList mocks base method.
func (m *MockColvirbridgeClient) FindClientAccountList(arg0 context.Context, arg1 *colvir_bridge.FindClientAccountListReq, arg2 ...grpc.CallOption) (*colvir_bridge.FindClientAccountListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindClientAccountList", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.FindClientAccountListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindClientAccountList indicates an expected call of FindClientAccountList.
func (mr *MockColvirbridgeClientMockRecorder) FindClientAccountList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindClientAccountList", reflect.TypeOf((*MockColvirbridgeClient)(nil).FindClientAccountList), varargs...)
}

// FindClientSMEIP mocks base method.
func (m *MockColvirbridgeClient) FindClientSMEIP(arg0 context.Context, arg1 *colvir_bridge.FindClientSMEIPReq, arg2 ...grpc.CallOption) (*colvir_bridge.FindClientSMEIPResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindClientSMEIP", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.FindClientSMEIPResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindClientSMEIP indicates an expected call of FindClientSMEIP.
func (mr *MockColvirbridgeClientMockRecorder) FindClientSMEIP(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindClientSMEIP", reflect.TypeOf((*MockColvirbridgeClient)(nil).FindClientSMEIP), varargs...)
}

// FindTaxPayer mocks base method.
func (m *MockColvirbridgeClient) FindTaxPayer(arg0 context.Context, arg1 *colvir_bridge.FindTaxPayerReq, arg2 ...grpc.CallOption) (*colvir_bridge.FindTaxPayerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindTaxPayer", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.FindTaxPayerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindTaxPayer indicates an expected call of FindTaxPayer.
func (mr *MockColvirbridgeClientMockRecorder) FindTaxPayer(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindTaxPayer", reflect.TypeOf((*MockColvirbridgeClient)(nil).FindTaxPayer), varargs...)
}

// GetAllLoans mocks base method.
func (m *MockColvirbridgeClient) GetAllLoans(arg0 context.Context, arg1 *colvir_bridge.GetAllLoansRequest, arg2 ...grpc.CallOption) (*colvir_bridge.GetAllLoansResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllLoans", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.GetAllLoansResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllLoans indicates an expected call of GetAllLoans.
func (mr *MockColvirbridgeClientMockRecorder) GetAllLoans(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllLoans", reflect.TypeOf((*MockColvirbridgeClient)(nil).GetAllLoans), varargs...)
}

// GetClient mocks base method.
func (m *MockColvirbridgeClient) GetClient(arg0 context.Context, arg1 *colvir_bridge.GetClientRequest, arg2 ...grpc.CallOption) (*colvir_bridge.GetClientResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClient", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.GetClientResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClient indicates an expected call of GetClient.
func (mr *MockColvirbridgeClientMockRecorder) GetClient(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClient", reflect.TypeOf((*MockColvirbridgeClient)(nil).GetClient), varargs...)
}

// GetClientCard mocks base method.
func (m *MockColvirbridgeClient) GetClientCard(arg0 context.Context, arg1 *colvir_bridge.GetClientCardRequest, arg2 ...grpc.CallOption) (*colvir_bridge.GetClientCardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClientCard", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.GetClientCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientCard indicates an expected call of GetClientCard.
func (mr *MockColvirbridgeClientMockRecorder) GetClientCard(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientCard", reflect.TypeOf((*MockColvirbridgeClient)(nil).GetClientCard), varargs...)
}

// GetClientDeposits mocks base method.
func (m *MockColvirbridgeClient) GetClientDeposits(arg0 context.Context, arg1 *colvir_bridge.GetClientDepositsRequest, arg2 ...grpc.CallOption) (*colvir_bridge.GetClientDepositsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClientDeposits", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.GetClientDepositsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientDeposits indicates an expected call of GetClientDeposits.
func (mr *MockColvirbridgeClientMockRecorder) GetClientDeposits(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientDeposits", reflect.TypeOf((*MockColvirbridgeClient)(nil).GetClientDeposits), varargs...)
}

// GetClientIPCard mocks base method.
func (m *MockColvirbridgeClient) GetClientIPCard(arg0 context.Context, arg1 *colvir_bridge.GetClientIPCardRequest, arg2 ...grpc.CallOption) (*colvir_bridge.GetClientIPCardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClientIPCard", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.GetClientIPCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientIPCard indicates an expected call of GetClientIPCard.
func (mr *MockColvirbridgeClientMockRecorder) GetClientIPCard(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientIPCard", reflect.TypeOf((*MockColvirbridgeClient)(nil).GetClientIPCard), varargs...)
}

// GetClientIPs mocks base method.
func (m *MockColvirbridgeClient) GetClientIPs(arg0 context.Context, arg1 *colvir_bridge.GetClientIPsReq, arg2 ...grpc.CallOption) (*colvir_bridge.GetClientIPsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClientIPs", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.GetClientIPsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientIPs indicates an expected call of GetClientIPs.
func (mr *MockColvirbridgeClientMockRecorder) GetClientIPs(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientIPs", reflect.TypeOf((*MockColvirbridgeClient)(nil).GetClientIPs), varargs...)
}

// GetClientLoans mocks base method.
func (m *MockColvirbridgeClient) GetClientLoans(arg0 context.Context, arg1 *colvir_bridge.GetClientLoansReq, arg2 ...grpc.CallOption) (*colvir_bridge.GetClientLoansResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClientLoans", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.GetClientLoansResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientLoans indicates an expected call of GetClientLoans.
func (mr *MockColvirbridgeClientMockRecorder) GetClientLoans(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientLoans", reflect.TypeOf((*MockColvirbridgeClient)(nil).GetClientLoans), varargs...)
}

// GetClientSMEIP mocks base method.
func (m *MockColvirbridgeClient) GetClientSMEIP(arg0 context.Context, arg1 *colvir_bridge.GetClientSMEIPRequest, arg2 ...grpc.CallOption) (*colvir_bridge.GetClientSMEIPResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClientSMEIP", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.GetClientSMEIPResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientSMEIP indicates an expected call of GetClientSMEIP.
func (mr *MockColvirbridgeClientMockRecorder) GetClientSMEIP(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientSMEIP", reflect.TypeOf((*MockColvirbridgeClient)(nil).GetClientSMEIP), varargs...)
}

// GetClients mocks base method.
func (m *MockColvirbridgeClient) GetClients(arg0 context.Context, arg1 *colvir_bridge.GetClientsReq, arg2 ...grpc.CallOption) (*colvir_bridge.GetClientsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClients", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.GetClientsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClients indicates an expected call of GetClients.
func (mr *MockColvirbridgeClientMockRecorder) GetClients(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClients", reflect.TypeOf((*MockColvirbridgeClient)(nil).GetClients), varargs...)
}

// GetClientsByFilter mocks base method.
func (m *MockColvirbridgeClient) GetClientsByFilter(arg0 context.Context, arg1 *colvir_bridge.GetClientsByFilterReq, arg2 ...grpc.CallOption) (*colvir_bridge.GetClientsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClientsByFilter", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.GetClientsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClientsByFilter indicates an expected call of GetClientsByFilter.
func (mr *MockColvirbridgeClientMockRecorder) GetClientsByFilter(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientsByFilter", reflect.TypeOf((*MockColvirbridgeClient)(nil).GetClientsByFilter), varargs...)
}

// GetColvirClient mocks base method.
func (m *MockColvirbridgeClient) GetColvirClient(arg0 context.Context, arg1 *colvir_bridge.GetColvirClientRequest, arg2 ...grpc.CallOption) (*colvir_bridge.GetColvirClientResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetColvirClient", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.GetColvirClientResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetColvirClient indicates an expected call of GetColvirClient.
func (mr *MockColvirbridgeClientMockRecorder) GetColvirClient(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetColvirClient", reflect.TypeOf((*MockColvirbridgeClient)(nil).GetColvirClient), varargs...)
}

// GetCreditContractDetails mocks base method.
func (m *MockColvirbridgeClient) GetCreditContractDetails(arg0 context.Context, arg1 *colvir_bridge.GetCreditContractDetailsReq, arg2 ...grpc.CallOption) (*colvir_bridge.GetCreditContractDetailsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCreditContractDetails", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.GetCreditContractDetailsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCreditContractDetails indicates an expected call of GetCreditContractDetails.
func (mr *MockColvirbridgeClientMockRecorder) GetCreditContractDetails(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCreditContractDetails", reflect.TypeOf((*MockColvirbridgeClient)(nil).GetCreditContractDetails), varargs...)
}

// GetLimitInfo mocks base method.
func (m *MockColvirbridgeClient) GetLimitInfo(arg0 context.Context, arg1 *colvir_bridge.GetLimitInfoRequest, arg2 ...grpc.CallOption) (*colvir_bridge.GetLimitInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLimitInfo", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.GetLimitInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLimitInfo indicates an expected call of GetLimitInfo.
func (mr *MockColvirbridgeClientMockRecorder) GetLimitInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLimitInfo", reflect.TypeOf((*MockColvirbridgeClient)(nil).GetLimitInfo), varargs...)
}

// GetLoanAmounts mocks base method.
func (m *MockColvirbridgeClient) GetLoanAmounts(arg0 context.Context, arg1 *colvir_bridge.GetLoanAmountsRequest, arg2 ...grpc.CallOption) (*colvir_bridge.GetLoanAmountsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLoanAmounts", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.GetLoanAmountsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanAmounts indicates an expected call of GetLoanAmounts.
func (mr *MockColvirbridgeClientMockRecorder) GetLoanAmounts(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanAmounts", reflect.TypeOf((*MockColvirbridgeClient)(nil).GetLoanAmounts), varargs...)
}

// GetLoanDetails mocks base method.
func (m *MockColvirbridgeClient) GetLoanDetails(arg0 context.Context, arg1 *colvir_bridge.GetLoanDetailsReq, arg2 ...grpc.CallOption) (*colvir_bridge.GetLoanDetailsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLoanDetails", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.GetLoanDetailsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanDetails indicates an expected call of GetLoanDetails.
func (mr *MockColvirbridgeClientMockRecorder) GetLoanDetails(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanDetails", reflect.TypeOf((*MockColvirbridgeClient)(nil).GetLoanDetails), varargs...)
}

// GetLoanSchedule mocks base method.
func (m *MockColvirbridgeClient) GetLoanSchedule(arg0 context.Context, arg1 *colvir_bridge.GetLoanScheduleReq, arg2 ...grpc.CallOption) (*colvir_bridge.GetLoanScheduleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLoanSchedule", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.GetLoanScheduleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLoanSchedule indicates an expected call of GetLoanSchedule.
func (mr *MockColvirbridgeClientMockRecorder) GetLoanSchedule(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLoanSchedule", reflect.TypeOf((*MockColvirbridgeClient)(nil).GetLoanSchedule), varargs...)
}

// GetOverdueLoans mocks base method.
func (m *MockColvirbridgeClient) GetOverdueLoans(arg0 context.Context, arg1 *colvir_bridge.GetOverdueLoansRequest, arg2 ...grpc.CallOption) (*colvir_bridge.GetOverdueLoansResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOverdueLoans", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.GetOverdueLoansResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOverdueLoans indicates an expected call of GetOverdueLoans.
func (mr *MockColvirbridgeClientMockRecorder) GetOverdueLoans(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOverdueLoans", reflect.TypeOf((*MockColvirbridgeClient)(nil).GetOverdueLoans), varargs...)
}

// GetStatusOperDate mocks base method.
func (m *MockColvirbridgeClient) GetStatusOperDate(arg0 context.Context, arg1 *colvir_bridge.StatusOperDateRequest, arg2 ...grpc.CallOption) (*colvir_bridge.StatusOperDateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStatusOperDate", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.StatusOperDateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStatusOperDate indicates an expected call of GetStatusOperDate.
func (mr *MockColvirbridgeClientMockRecorder) GetStatusOperDate(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStatusOperDate", reflect.TypeOf((*MockColvirbridgeClient)(nil).GetStatusOperDate), varargs...)
}

// GetTaxPayer mocks base method.
func (m *MockColvirbridgeClient) GetTaxPayer(arg0 context.Context, arg1 *colvir_bridge.FindTaxPayerReq, arg2 ...grpc.CallOption) (*colvir_bridge.GetTaxPayerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTaxPayer", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.GetTaxPayerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaxPayer indicates an expected call of GetTaxPayer.
func (mr *MockColvirbridgeClientMockRecorder) GetTaxPayer(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaxPayer", reflect.TypeOf((*MockColvirbridgeClient)(nil).GetTaxPayer), varargs...)
}

// HealthCheck mocks base method.
func (m *MockColvirbridgeClient) HealthCheck(arg0 context.Context, arg1 *colvir_bridge.HealthCheckReq, arg2 ...grpc.CallOption) (*colvir_bridge.HealthCheckResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HealthCheck", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.HealthCheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockColvirbridgeClientMockRecorder) HealthCheck(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockColvirbridgeClient)(nil).HealthCheck), varargs...)
}

// LoadAccountTransactions mocks base method.
func (m *MockColvirbridgeClient) LoadAccountTransactions(arg0 context.Context, arg1 *colvir_bridge.LoadAccountTransactionsReq, arg2 ...grpc.CallOption) (*colvir_bridge.LoadAccountTransactionsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LoadAccountTransactions", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.LoadAccountTransactionsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadAccountTransactions indicates an expected call of LoadAccountTransactions.
func (mr *MockColvirbridgeClientMockRecorder) LoadAccountTransactions(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadAccountTransactions", reflect.TypeOf((*MockColvirbridgeClient)(nil).LoadAccountTransactions), varargs...)
}

// LoadBankHolidays mocks base method.
func (m *MockColvirbridgeClient) LoadBankHolidays(arg0 context.Context, arg1 *colvir_bridge.LoadBankHolidaysRequest, arg2 ...grpc.CallOption) (*colvir_bridge.LoadBankHolidaysResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LoadBankHolidays", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.LoadBankHolidaysResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadBankHolidays indicates an expected call of LoadBankHolidays.
func (mr *MockColvirbridgeClientMockRecorder) LoadBankHolidays(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadBankHolidays", reflect.TypeOf((*MockColvirbridgeClient)(nil).LoadBankHolidays), varargs...)
}

// LoadBankList mocks base method.
func (m *MockColvirbridgeClient) LoadBankList(arg0 context.Context, arg1 *colvir_bridge.LoadBankListRequest, arg2 ...grpc.CallOption) (*colvir_bridge.LoadBankListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LoadBankList", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.LoadBankListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadBankList indicates an expected call of LoadBankList.
func (mr *MockColvirbridgeClientMockRecorder) LoadBankList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadBankList", reflect.TypeOf((*MockColvirbridgeClient)(nil).LoadBankList), varargs...)
}

// LoadClientBankRelationLink mocks base method.
func (m *MockColvirbridgeClient) LoadClientBankRelationLink(arg0 context.Context, arg1 *colvir_bridge.LoadClientBankRelationLinkRequest, arg2 ...grpc.CallOption) (*colvir_bridge.LoadClientBankRelationLinkResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LoadClientBankRelationLink", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.LoadClientBankRelationLinkResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadClientBankRelationLink indicates an expected call of LoadClientBankRelationLink.
func (mr *MockColvirbridgeClientMockRecorder) LoadClientBankRelationLink(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadClientBankRelationLink", reflect.TypeOf((*MockColvirbridgeClient)(nil).LoadClientBankRelationLink), varargs...)
}

// LoadCountryDictionary mocks base method.
func (m *MockColvirbridgeClient) LoadCountryDictionary(arg0 context.Context, arg1 *colvir_bridge.LoadCountryDictionaryRequest, arg2 ...grpc.CallOption) (*colvir_bridge.LoadCountryDictionaryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LoadCountryDictionary", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.LoadCountryDictionaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadCountryDictionary indicates an expected call of LoadCountryDictionary.
func (mr *MockColvirbridgeClientMockRecorder) LoadCountryDictionary(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadCountryDictionary", reflect.TypeOf((*MockColvirbridgeClient)(nil).LoadCountryDictionary), varargs...)
}

// LoadDomesticPaymentStatus mocks base method.
func (m *MockColvirbridgeClient) LoadDomesticPaymentStatus(arg0 context.Context, arg1 *colvir_bridge.LoadDomesticPaymentStatusRequest, arg2 ...grpc.CallOption) (*colvir_bridge.LoadDomesticPaymentStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LoadDomesticPaymentStatus", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.LoadDomesticPaymentStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadDomesticPaymentStatus indicates an expected call of LoadDomesticPaymentStatus.
func (mr *MockColvirbridgeClientMockRecorder) LoadDomesticPaymentStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadDomesticPaymentStatus", reflect.TypeOf((*MockColvirbridgeClient)(nil).LoadDomesticPaymentStatus), varargs...)
}

// LoadKBEDictionary mocks base method.
func (m *MockColvirbridgeClient) LoadKBEDictionary(arg0 context.Context, arg1 *colvir_bridge.LoadKBEDictionaryRequest, arg2 ...grpc.CallOption) (*colvir_bridge.LoadKBEDictionaryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LoadKBEDictionary", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.LoadKBEDictionaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadKBEDictionary indicates an expected call of LoadKBEDictionary.
func (mr *MockColvirbridgeClientMockRecorder) LoadKBEDictionary(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadKBEDictionary", reflect.TypeOf((*MockColvirbridgeClient)(nil).LoadKBEDictionary), varargs...)
}

// LoadKBKDictionary mocks base method.
func (m *MockColvirbridgeClient) LoadKBKDictionary(arg0 context.Context, arg1 *colvir_bridge.LoadKBKDictionaryRequest, arg2 ...grpc.CallOption) (*colvir_bridge.LoadKBKDictionaryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LoadKBKDictionary", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.LoadKBKDictionaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadKBKDictionary indicates an expected call of LoadKBKDictionary.
func (mr *MockColvirbridgeClientMockRecorder) LoadKBKDictionary(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadKBKDictionary", reflect.TypeOf((*MockColvirbridgeClient)(nil).LoadKBKDictionary), varargs...)
}

// LoadKNPDictionary mocks base method.
func (m *MockColvirbridgeClient) LoadKNPDictionary(arg0 context.Context, arg1 *colvir_bridge.LoadKNPDictionaryRequest, arg2 ...grpc.CallOption) (*colvir_bridge.LoadKNPDictionaryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LoadKNPDictionary", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.LoadKNPDictionaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadKNPDictionary indicates an expected call of LoadKNPDictionary.
func (mr *MockColvirbridgeClientMockRecorder) LoadKNPDictionary(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadKNPDictionary", reflect.TypeOf((*MockColvirbridgeClient)(nil).LoadKNPDictionary), varargs...)
}

// LoadLoanAgreementDetails mocks base method.
func (m *MockColvirbridgeClient) LoadLoanAgreementDetails(arg0 context.Context, arg1 *colvir_bridge.LoadLoanAgreementDetailsRequest, arg2 ...grpc.CallOption) (*colvir_bridge.LoadLoanAgreementDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LoadLoanAgreementDetails", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.LoadLoanAgreementDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadLoanAgreementDetails indicates an expected call of LoadLoanAgreementDetails.
func (mr *MockColvirbridgeClientMockRecorder) LoadLoanAgreementDetails(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadLoanAgreementDetails", reflect.TypeOf((*MockColvirbridgeClient)(nil).LoadLoanAgreementDetails), varargs...)
}

// LoadOverdueLoanPaymentReport mocks base method.
func (m *MockColvirbridgeClient) LoadOverdueLoanPaymentReport(arg0 context.Context, arg1 *colvir_bridge.LoadOverdueLoanPaymentReportRequest, arg2 ...grpc.CallOption) (*colvir_bridge.LoadOverdueLoanPaymentReportResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LoadOverdueLoanPaymentReport", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.LoadOverdueLoanPaymentReportResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadOverdueLoanPaymentReport indicates an expected call of LoadOverdueLoanPaymentReport.
func (mr *MockColvirbridgeClientMockRecorder) LoadOverdueLoanPaymentReport(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadOverdueLoanPaymentReport", reflect.TypeOf((*MockColvirbridgeClient)(nil).LoadOverdueLoanPaymentReport), varargs...)
}

// LoadUGDDictionary mocks base method.
func (m *MockColvirbridgeClient) LoadUGDDictionary(arg0 context.Context, arg1 *colvir_bridge.LoadUGDDictionaryRequest, arg2 ...grpc.CallOption) (*colvir_bridge.LoadUGDDictionaryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LoadUGDDictionary", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.LoadUGDDictionaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadUGDDictionary indicates an expected call of LoadUGDDictionary.
func (mr *MockColvirbridgeClientMockRecorder) LoadUGDDictionary(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadUGDDictionary", reflect.TypeOf((*MockColvirbridgeClient)(nil).LoadUGDDictionary), varargs...)
}

// LoanCalcLoadPreSchedule mocks base method.
func (m *MockColvirbridgeClient) LoanCalcLoadPreSchedule(arg0 context.Context, arg1 *colvir_bridge.LoanCalcLoadPreScheduleRequest, arg2 ...grpc.CallOption) (*colvir_bridge.LoanCalcLoadPreScheduleResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LoanCalcLoadPreSchedule", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.LoanCalcLoadPreScheduleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoanCalcLoadPreSchedule indicates an expected call of LoanCalcLoadPreSchedule.
func (mr *MockColvirbridgeClientMockRecorder) LoanCalcLoadPreSchedule(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoanCalcLoadPreSchedule", reflect.TypeOf((*MockColvirbridgeClient)(nil).LoanCalcLoadPreSchedule), varargs...)
}

// LoansCalculateSchedule mocks base method.
func (m *MockColvirbridgeClient) LoansCalculateSchedule(arg0 context.Context, arg1 *colvir_bridge.LoansCalculateScheduleRequest, arg2 ...grpc.CallOption) (*colvir_bridge.LoansCalculateScheduleResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LoansCalculateSchedule", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.LoansCalculateScheduleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoansCalculateSchedule indicates an expected call of LoansCalculateSchedule.
func (mr *MockColvirbridgeClientMockRecorder) LoansCalculateSchedule(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoansCalculateSchedule", reflect.TypeOf((*MockColvirbridgeClient)(nil).LoansCalculateSchedule), varargs...)
}

// LoansCalculateTotalCost mocks base method.
func (m *MockColvirbridgeClient) LoansCalculateTotalCost(arg0 context.Context, arg1 *colvir_bridge.LoansCalculateTotalCostRequest, arg2 ...grpc.CallOption) (*colvir_bridge.LoansCalculateScheduleResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LoansCalculateTotalCost", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.LoansCalculateScheduleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoansCalculateTotalCost indicates an expected call of LoansCalculateTotalCost.
func (mr *MockColvirbridgeClientMockRecorder) LoansCalculateTotalCost(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoansCalculateTotalCost", reflect.TypeOf((*MockColvirbridgeClient)(nil).LoansCalculateTotalCost), varargs...)
}

// LoansGetMissedPayments mocks base method.
func (m *MockColvirbridgeClient) LoansGetMissedPayments(arg0 context.Context, arg1 *colvir_bridge.LoansGetMissedPaymentsRequest, arg2 ...grpc.CallOption) (*colvir_bridge.LoansGetMissedPaymentsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LoansGetMissedPayments", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.LoansGetMissedPaymentsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoansGetMissedPayments indicates an expected call of LoansGetMissedPayments.
func (mr *MockColvirbridgeClientMockRecorder) LoansGetMissedPayments(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoansGetMissedPayments", reflect.TypeOf((*MockColvirbridgeClient)(nil).LoansGetMissedPayments), varargs...)
}

// LoansRegisterShd mocks base method.
func (m *MockColvirbridgeClient) LoansRegisterShd(arg0 context.Context, arg1 *colvir_bridge.LoansRegisterShdRequest, arg2 ...grpc.CallOption) (*colvir_bridge.LoansRegisterShdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LoansRegisterShd", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.LoansRegisterShdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoansRegisterShd indicates an expected call of LoansRegisterShd.
func (mr *MockColvirbridgeClientMockRecorder) LoansRegisterShd(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoansRegisterShd", reflect.TypeOf((*MockColvirbridgeClient)(nil).LoansRegisterShd), varargs...)
}

// OpenClientCard mocks base method.
func (m *MockColvirbridgeClient) OpenClientCard(arg0 context.Context, arg1 *colvir_bridge.OpenClientCardRequest, arg2 ...grpc.CallOption) (*colvir_bridge.OpenClientCardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OpenClientCard", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.OpenClientCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OpenClientCard indicates an expected call of OpenClientCard.
func (mr *MockColvirbridgeClientMockRecorder) OpenClientCard(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OpenClientCard", reflect.TypeOf((*MockColvirbridgeClient)(nil).OpenClientCard), varargs...)
}

// OpenDeposit mocks base method.
func (m *MockColvirbridgeClient) OpenDeposit(arg0 context.Context, arg1 *colvir_bridge.OpenDepositRequest, arg2 ...grpc.CallOption) (*colvir_bridge.OpenDepositResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OpenDeposit", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.OpenDepositResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OpenDeposit indicates an expected call of OpenDeposit.
func (mr *MockColvirbridgeClientMockRecorder) OpenDeposit(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OpenDeposit", reflect.TypeOf((*MockColvirbridgeClient)(nil).OpenDeposit), varargs...)
}

// ProvidingLoan mocks base method.
func (m *MockColvirbridgeClient) ProvidingLoan(arg0 context.Context, arg1 *colvir_bridge.ProvidingLoanReq, arg2 ...grpc.CallOption) (*colvir_bridge.ProvidingLoanResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProvidingLoan", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.ProvidingLoanResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProvidingLoan indicates an expected call of ProvidingLoan.
func (mr *MockColvirbridgeClientMockRecorder) ProvidingLoan(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProvidingLoan", reflect.TypeOf((*MockColvirbridgeClient)(nil).ProvidingLoan), varargs...)
}

// RepayLoanEarly mocks base method.
func (m *MockColvirbridgeClient) RepayLoanEarly(arg0 context.Context, arg1 *colvir_bridge.RepayLoanEarlyReq, arg2 ...grpc.CallOption) (*colvir_bridge.RepayLoanEarlyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RepayLoanEarly", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.RepayLoanEarlyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RepayLoanEarly indicates an expected call of RepayLoanEarly.
func (mr *MockColvirbridgeClientMockRecorder) RepayLoanEarly(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RepayLoanEarly", reflect.TypeOf((*MockColvirbridgeClient)(nil).RepayLoanEarly), varargs...)
}

// SaveLoan mocks base method.
func (m *MockColvirbridgeClient) SaveLoan(arg0 context.Context, arg1 *colvir_bridge.SaveLoanReq, arg2 ...grpc.CallOption) (*colvir_bridge.SaveLoanResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SaveLoan", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.SaveLoanResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveLoan indicates an expected call of SaveLoan.
func (mr *MockColvirbridgeClientMockRecorder) SaveLoan(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveLoan", reflect.TypeOf((*MockColvirbridgeClient)(nil).SaveLoan), varargs...)
}

// UpdateClient mocks base method.
func (m *MockColvirbridgeClient) UpdateClient(arg0 context.Context, arg1 *colvir_bridge.UpdateClientRequest, arg2 ...grpc.CallOption) (*colvir_bridge.UpdateClientResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateClient", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.UpdateClientResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateClient indicates an expected call of UpdateClient.
func (mr *MockColvirbridgeClientMockRecorder) UpdateClient(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateClient", reflect.TypeOf((*MockColvirbridgeClient)(nil).UpdateClient), varargs...)
}

// UpdateClientSMEIP mocks base method.
func (m *MockColvirbridgeClient) UpdateClientSMEIP(arg0 context.Context, arg1 *colvir_bridge.UpdateClientSMEIPRequest, arg2 ...grpc.CallOption) (*colvir_bridge.UpdateClientSMEIPResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateClientSMEIP", varargs...)
	ret0, _ := ret[0].(*colvir_bridge.UpdateClientSMEIPResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateClientSMEIP indicates an expected call of UpdateClientSMEIP.
func (mr *MockColvirbridgeClientMockRecorder) UpdateClientSMEIP(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateClientSMEIP", reflect.TypeOf((*MockColvirbridgeClient)(nil).UpdateClientSMEIP), varargs...)
}
