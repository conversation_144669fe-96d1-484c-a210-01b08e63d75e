// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/specs/proto/antifraud (interfaces: AntifraudClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"

	antifraud "git.redmadrobot.com/zaman/backend/zaman/specs/proto/antifraud"
)

// MockAntifraudClient is a mock of AntifraudClient interface.
type MockAntifraudClient struct {
	ctrl     *gomock.Controller
	recorder *MockAntifraudClientMockRecorder
}

// MockAntifraudClientMockRecorder is the mock recorder for MockAntifraudClient.
type MockAntifraudClientMockRecorder struct {
	mock *MockAntifraudClient
}

// NewMockAntifraudClient creates a new mock instance.
func NewMockAntifraudClient(ctrl *gomock.Controller) *MockAntifraudClient {
	mock := &MockAntifraudClient{ctrl: ctrl}
	mock.recorder = &MockAntifraudClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAntifraudClient) EXPECT() *MockAntifraudClientMockRecorder {
	return m.recorder
}

// FraudCheckClient mocks base method.
func (m *MockAntifraudClient) FraudCheckClient(arg0 context.Context, arg1 *antifraud.FraudCheckClientReq, arg2 ...grpc.CallOption) (*antifraud.FraudCheckClientResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FraudCheckClient", varargs...)
	ret0, _ := ret[0].(*antifraud.FraudCheckClientResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FraudCheckClient indicates an expected call of FraudCheckClient.
func (mr *MockAntifraudClientMockRecorder) FraudCheckClient(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FraudCheckClient", reflect.TypeOf((*MockAntifraudClient)(nil).FraudCheckClient), varargs...)
}

// HealthCheck mocks base method.
func (m *MockAntifraudClient) HealthCheck(arg0 context.Context, arg1 *antifraud.HealthCheckReq, arg2 ...grpc.CallOption) (*antifraud.HealthCheckResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HealthCheck", varargs...)
	ret0, _ := ret[0].(*antifraud.HealthCheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockAntifraudClientMockRecorder) HealthCheck(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockAntifraudClient)(nil).HealthCheck), varargs...)
}
