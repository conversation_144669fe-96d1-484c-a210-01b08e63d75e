// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/specs/proto/bts-bridge (interfaces: BtsbridgeClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"

	bts_bridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/bts-bridge"
)

// MockBtsbridgeClient is a mock of BtsbridgeClient interface.
type MockBtsbridgeClient struct {
	ctrl     *gomock.Controller
	recorder *MockBtsbridgeClientMockRecorder
}

// MockBtsbridgeClientMockRecorder is the mock recorder for MockBtsbridgeClient.
type MockBtsbridgeClientMockRecorder struct {
	mock *MockBtsbridgeClient
}

// NewMockBtsbridgeClient creates a new mock instance.
func NewMockBtsbridgeClient(ctrl *gomock.Controller) *MockBtsbridgeClient {
	mock := &MockBtsbridgeClient{ctrl: ctrl}
	mock.recorder = &MockBtsbridgeClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBtsbridgeClient) EXPECT() *MockBtsbridgeClientMockRecorder {
	return m.recorder
}

// CreateTrustedPhone mocks base method.
func (m *MockBtsbridgeClient) CreateTrustedPhone(arg0 context.Context, arg1 *bts_bridge.CreateTrustedPhoneReq, arg2 ...grpc.CallOption) (*bts_bridge.CreateTrustedPhoneResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateTrustedPhone", varargs...)
	ret0, _ := ret[0].(*bts_bridge.CreateTrustedPhoneResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTrustedPhone indicates an expected call of CreateTrustedPhone.
func (mr *MockBtsbridgeClientMockRecorder) CreateTrustedPhone(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTrustedPhone", reflect.TypeOf((*MockBtsbridgeClient)(nil).CreateTrustedPhone), varargs...)
}

// GetIdentificationSessionList mocks base method.
func (m *MockBtsbridgeClient) GetIdentificationSessionList(arg0 context.Context, arg1 *bts_bridge.GetIdentificationSessionListReq, arg2 ...grpc.CallOption) (*bts_bridge.GetIdentificationSessionListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetIdentificationSessionList", varargs...)
	ret0, _ := ret[0].(*bts_bridge.GetIdentificationSessionListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIdentificationSessionList indicates an expected call of GetIdentificationSessionList.
func (mr *MockBtsbridgeClientMockRecorder) GetIdentificationSessionList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIdentificationSessionList", reflect.TypeOf((*MockBtsbridgeClient)(nil).GetIdentificationSessionList), varargs...)
}

// GetIdentificationSessionReport mocks base method.
func (m *MockBtsbridgeClient) GetIdentificationSessionReport(arg0 context.Context, arg1 *bts_bridge.GetIdentificationSessionReportReq, arg2 ...grpc.CallOption) (*bts_bridge.GetIdentificationSessionReportResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetIdentificationSessionReport", varargs...)
	ret0, _ := ret[0].(*bts_bridge.GetIdentificationSessionReportResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIdentificationSessionReport indicates an expected call of GetIdentificationSessionReport.
func (mr *MockBtsbridgeClientMockRecorder) GetIdentificationSessionReport(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIdentificationSessionReport", reflect.TypeOf((*MockBtsbridgeClient)(nil).GetIdentificationSessionReport), varargs...)
}

// GetIdentificationSessionReportBySessionID mocks base method.
func (m *MockBtsbridgeClient) GetIdentificationSessionReportBySessionID(arg0 context.Context, arg1 *bts_bridge.GetIdentificationSessionReportSessionIDReq, arg2 ...grpc.CallOption) (*bts_bridge.GetIdentificationSessionReportSessionIDResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetIdentificationSessionReportBySessionID", varargs...)
	ret0, _ := ret[0].(*bts_bridge.GetIdentificationSessionReportSessionIDResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIdentificationSessionReportBySessionID indicates an expected call of GetIdentificationSessionReportBySessionID.
func (mr *MockBtsbridgeClientMockRecorder) GetIdentificationSessionReportBySessionID(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIdentificationSessionReportBySessionID", reflect.TypeOf((*MockBtsbridgeClient)(nil).GetIdentificationSessionReportBySessionID), varargs...)
}

// GetLastBtsVerifyResultByIin mocks base method.
func (m *MockBtsbridgeClient) GetLastBtsVerifyResultByIin(arg0 context.Context, arg1 *bts_bridge.GetLastBtsVerifyResultByIinReq, arg2 ...grpc.CallOption) (*bts_bridge.GetLastBtsVerifyResultByIinResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLastBtsVerifyResultByIin", varargs...)
	ret0, _ := ret[0].(*bts_bridge.GetLastBtsVerifyResultByIinResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastBtsVerifyResultByIin indicates an expected call of GetLastBtsVerifyResultByIin.
func (mr *MockBtsbridgeClientMockRecorder) GetLastBtsVerifyResultByIin(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastBtsVerifyResultByIin", reflect.TypeOf((*MockBtsbridgeClient)(nil).GetLastBtsVerifyResultByIin), varargs...)
}

// GetLiveness3DPhoto mocks base method.
func (m *MockBtsbridgeClient) GetLiveness3DPhoto(arg0 context.Context, arg1 *bts_bridge.GetLiveness3DPhotoReq, arg2 ...grpc.CallOption) (*bts_bridge.GetLiveness3DPhotoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLiveness3DPhoto", varargs...)
	ret0, _ := ret[0].(*bts_bridge.GetLiveness3DPhotoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLiveness3DPhoto indicates an expected call of GetLiveness3DPhoto.
func (mr *MockBtsbridgeClientMockRecorder) GetLiveness3DPhoto(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLiveness3DPhoto", reflect.TypeOf((*MockBtsbridgeClient)(nil).GetLiveness3DPhoto), varargs...)
}

// GetLiveness3DVideo mocks base method.
func (m *MockBtsbridgeClient) GetLiveness3DVideo(arg0 context.Context, arg1 *bts_bridge.GetLiveness3DVideoReq, arg2 ...grpc.CallOption) (*bts_bridge.GetLiveness3DVideoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLiveness3DVideo", varargs...)
	ret0, _ := ret[0].(*bts_bridge.GetLiveness3DVideoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLiveness3DVideo indicates an expected call of GetLiveness3DVideo.
func (mr *MockBtsbridgeClientMockRecorder) GetLiveness3DVideo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLiveness3DVideo", reflect.TypeOf((*MockBtsbridgeClient)(nil).GetLiveness3DVideo), varargs...)
}

// GetPersonalData mocks base method.
func (m *MockBtsbridgeClient) GetPersonalData(arg0 context.Context, arg1 *bts_bridge.GetPersonalDataReq, arg2 ...grpc.CallOption) (*bts_bridge.GetPersonalDataResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPersonalData", varargs...)
	ret0, _ := ret[0].(*bts_bridge.GetPersonalDataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPersonalData indicates an expected call of GetPersonalData.
func (mr *MockBtsbridgeClientMockRecorder) GetPersonalData(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersonalData", reflect.TypeOf((*MockBtsbridgeClient)(nil).GetPersonalData), varargs...)
}

// GetSignedFiles mocks base method.
func (m *MockBtsbridgeClient) GetSignedFiles(arg0 context.Context, arg1 *bts_bridge.GetSignedFilesReq, arg2 ...grpc.CallOption) (*bts_bridge.GetSignedFilesResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSignedFiles", varargs...)
	ret0, _ := ret[0].(*bts_bridge.GetSignedFilesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSignedFiles indicates an expected call of GetSignedFiles.
func (mr *MockBtsbridgeClientMockRecorder) GetSignedFiles(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSignedFiles", reflect.TypeOf((*MockBtsbridgeClient)(nil).GetSignedFiles), varargs...)
}

// HealthCheck mocks base method.
func (m *MockBtsbridgeClient) HealthCheck(arg0 context.Context, arg1 *bts_bridge.HealthCheckReq, arg2 ...grpc.CallOption) (*bts_bridge.HealthCheckResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HealthCheck", varargs...)
	ret0, _ := ret[0].(*bts_bridge.HealthCheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockBtsbridgeClientMockRecorder) HealthCheck(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockBtsbridgeClient)(nil).HealthCheck), varargs...)
}

// RequestPersonalData mocks base method.
func (m *MockBtsbridgeClient) RequestPersonalData(arg0 context.Context, arg1 *bts_bridge.RequestPersonalDataReq, arg2 ...grpc.CallOption) (*bts_bridge.RequestPersonalDataResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RequestPersonalData", varargs...)
	ret0, _ := ret[0].(*bts_bridge.RequestPersonalDataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestPersonalData indicates an expected call of RequestPersonalData.
func (mr *MockBtsbridgeClientMockRecorder) RequestPersonalData(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestPersonalData", reflect.TypeOf((*MockBtsbridgeClient)(nil).RequestPersonalData), varargs...)
}

// RequestVerify mocks base method.
func (m *MockBtsbridgeClient) RequestVerify(arg0 context.Context, arg1 *bts_bridge.RequestVerifyReq, arg2 ...grpc.CallOption) (*bts_bridge.RequestVerifyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RequestVerify", varargs...)
	ret0, _ := ret[0].(*bts_bridge.RequestVerifyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestVerify indicates an expected call of RequestVerify.
func (mr *MockBtsbridgeClientMockRecorder) RequestVerify(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestVerify", reflect.TypeOf((*MockBtsbridgeClient)(nil).RequestVerify), varargs...)
}

// UploadFileForSign mocks base method.
func (m *MockBtsbridgeClient) UploadFileForSign(arg0 context.Context, arg1 *bts_bridge.UploadFileForSignReq, arg2 ...grpc.CallOption) (*bts_bridge.UploadFileForSignResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UploadFileForSign", varargs...)
	ret0, _ := ret[0].(*bts_bridge.UploadFileForSignResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UploadFileForSign indicates an expected call of UploadFileForSign.
func (mr *MockBtsbridgeClientMockRecorder) UploadFileForSign(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadFileForSign", reflect.TypeOf((*MockBtsbridgeClient)(nil).UploadFileForSign), varargs...)
}
