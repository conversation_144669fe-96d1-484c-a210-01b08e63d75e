// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/specs/proto/liveness (interfaces: LivenessClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"

	liveness "git.redmadrobot.com/zaman/backend/zaman/specs/proto/liveness"
)

// MockLivenessClient is a mock of LivenessClient interface.
type MockLivenessClient struct {
	ctrl     *gomock.Controller
	recorder *MockLivenessClientMockRecorder
}

// MockLivenessClientMockRecorder is the mock recorder for MockLivenessClient.
type MockLivenessClientMockRecorder struct {
	mock *MockLivenessClient
}

// NewMockLivenessClient creates a new mock instance.
func NewMockLivenessClient(ctrl *gomock.Controller) *MockLivenessClient {
	mock := &MockLivenessClient{ctrl: ctrl}
	mock.recorder = &MockLivenessClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLivenessClient) EXPECT() *MockLivenessClientMockRecorder {
	return m.recorder
}

// CreateLivenessLink mocks base method.
func (m *MockLivenessClient) CreateLivenessLink(arg0 context.Context, arg1 *liveness.LivenessLinkReq, arg2 ...grpc.CallOption) (*liveness.LivenessLinkResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateLivenessLink", varargs...)
	ret0, _ := ret[0].(*liveness.LivenessLinkResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateLivenessLink indicates an expected call of CreateLivenessLink.
func (mr *MockLivenessClientMockRecorder) CreateLivenessLink(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateLivenessLink", reflect.TypeOf((*MockLivenessClient)(nil).CreateLivenessLink), varargs...)
}

// GetSessionLinks mocks base method.
func (m *MockLivenessClient) GetSessionLinks(arg0 context.Context, arg1 *liveness.GetSessionLinksRq, arg2 ...grpc.CallOption) (*liveness.GetSessionLinksRs, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSessionLinks", varargs...)
	ret0, _ := ret[0].(*liveness.GetSessionLinksRs)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSessionLinks indicates an expected call of GetSessionLinks.
func (mr *MockLivenessClientMockRecorder) GetSessionLinks(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSessionLinks", reflect.TypeOf((*MockLivenessClient)(nil).GetSessionLinks), varargs...)
}

// HealthCheck mocks base method.
func (m *MockLivenessClient) HealthCheck(arg0 context.Context, arg1 *liveness.HealthCheckReq, arg2 ...grpc.CallOption) (*liveness.HealthCheckResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HealthCheck", varargs...)
	ret0, _ := ret[0].(*liveness.HealthCheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockLivenessClientMockRecorder) HealthCheck(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockLivenessClient)(nil).HealthCheck), varargs...)
}

// ProcessFailedLiveness mocks base method.
func (m *MockLivenessClient) ProcessFailedLiveness(arg0 context.Context, arg1 *liveness.ProcessFailedLivenessReq, arg2 ...grpc.CallOption) (*liveness.ProcessFailedLivenessResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessFailedLiveness", varargs...)
	ret0, _ := ret[0].(*liveness.ProcessFailedLivenessResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessFailedLiveness indicates an expected call of ProcessFailedLiveness.
func (mr *MockLivenessClientMockRecorder) ProcessFailedLiveness(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessFailedLiveness", reflect.TypeOf((*MockLivenessClient)(nil).ProcessFailedLiveness), varargs...)
}

// UploadEdsFiles mocks base method.
func (m *MockLivenessClient) UploadEdsFiles(arg0 context.Context, arg1 *liveness.UploadEdsFilesReq, arg2 ...grpc.CallOption) (*liveness.UploadEdsFilesResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UploadEdsFiles", varargs...)
	ret0, _ := ret[0].(*liveness.UploadEdsFilesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UploadEdsFiles indicates an expected call of UploadEdsFiles.
func (mr *MockLivenessClientMockRecorder) UploadEdsFiles(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadEdsFiles", reflect.TypeOf((*MockLivenessClient)(nil).UploadEdsFiles), varargs...)
}

// VerifyAndGetPersonalData mocks base method.
func (m *MockLivenessClient) VerifyAndGetPersonalData(arg0 context.Context, arg1 *liveness.VerifyLivenessCodeReq, arg2 ...grpc.CallOption) (*liveness.VerifyLivenessCodeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyAndGetPersonalData", varargs...)
	ret0, _ := ret[0].(*liveness.VerifyLivenessCodeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyAndGetPersonalData indicates an expected call of VerifyAndGetPersonalData.
func (mr *MockLivenessClientMockRecorder) VerifyAndGetPersonalData(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyAndGetPersonalData", reflect.TypeOf((*MockLivenessClient)(nil).VerifyAndGetPersonalData), varargs...)
}

// VerifyEdsAndSaveFiles mocks base method.
func (m *MockLivenessClient) VerifyEdsAndSaveFiles(arg0 context.Context, arg1 *liveness.VerifyEdsAndSaveFilesReq, arg2 ...grpc.CallOption) (*liveness.VerifyEdsAndSaveFilesResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyEdsAndSaveFiles", varargs...)
	ret0, _ := ret[0].(*liveness.VerifyEdsAndSaveFilesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyEdsAndSaveFiles indicates an expected call of VerifyEdsAndSaveFiles.
func (mr *MockLivenessClientMockRecorder) VerifyEdsAndSaveFiles(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyEdsAndSaveFiles", reflect.TypeOf((*MockLivenessClient)(nil).VerifyEdsAndSaveFiles), varargs...)
}
