// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/specs/proto/aml-bridge (interfaces: AmlbridgeClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"

	aml_bridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/aml-bridge"
)

// MockAmlbridgeClient is a mock of AmlbridgeClient interface.
type MockAmlbridgeClient struct {
	ctrl     *gomock.Controller
	recorder *MockAmlbridgeClientMockRecorder
}

// MockAmlbridgeClientMockRecorder is the mock recorder for MockAmlbridgeClient.
type MockAmlbridgeClientMockRecorder struct {
	mock *MockAmlbridgeClient
}

// NewMockAmlbridgeClient creates a new mock instance.
func NewMockAmlbridgeClient(ctrl *gomock.Controller) *MockAmlbridgeClient {
	mock := &MockAmlbridgeClient{ctrl: ctrl}
	mock.recorder = &MockAmlbridgeClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAmlbridgeClient) EXPECT() *MockAmlbridgeClientMockRecorder {
	return m.recorder
}

// CheckOnlineClientCard mocks base method.
func (m *MockAmlbridgeClient) CheckOnlineClientCard(arg0 context.Context, arg1 *aml_bridge.CheckOnlineClientCardRequest, arg2 ...grpc.CallOption) (*aml_bridge.CheckOnlineClientCardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckOnlineClientCard", varargs...)
	ret0, _ := ret[0].(*aml_bridge.CheckOnlineClientCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckOnlineClientCard indicates an expected call of CheckOnlineClientCard.
func (mr *MockAmlbridgeClientMockRecorder) CheckOnlineClientCard(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckOnlineClientCard", reflect.TypeOf((*MockAmlbridgeClient)(nil).CheckOnlineClientCard), varargs...)
}

// HealthCheck mocks base method.
func (m *MockAmlbridgeClient) HealthCheck(arg0 context.Context, arg1 *aml_bridge.HealthCheckReq, arg2 ...grpc.CallOption) (*aml_bridge.HealthCheckResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HealthCheck", varargs...)
	ret0, _ := ret[0].(*aml_bridge.HealthCheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockAmlbridgeClientMockRecorder) HealthCheck(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockAmlbridgeClient)(nil).HealthCheck), varargs...)
}

// NewCheckOnlineClientCard mocks base method.
func (m *MockAmlbridgeClient) NewCheckOnlineClientCard(arg0 context.Context, arg1 *aml_bridge.NewCheckOnlineClientCardRequest, arg2 ...grpc.CallOption) (*aml_bridge.NewCheckOnlineClientCardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "NewCheckOnlineClientCard", varargs...)
	ret0, _ := ret[0].(*aml_bridge.NewCheckOnlineClientCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewCheckOnlineClientCard indicates an expected call of NewCheckOnlineClientCard.
func (mr *MockAmlbridgeClientMockRecorder) NewCheckOnlineClientCard(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewCheckOnlineClientCard", reflect.TypeOf((*MockAmlbridgeClient)(nil).NewCheckOnlineClientCard), varargs...)
}

// NewCheckOnlineOperation mocks base method.
func (m *MockAmlbridgeClient) NewCheckOnlineOperation(arg0 context.Context, arg1 *aml_bridge.NewCheckOnlineOperationRequest, arg2 ...grpc.CallOption) (*aml_bridge.NewCheckOnlineOperationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "NewCheckOnlineOperation", varargs...)
	ret0, _ := ret[0].(*aml_bridge.NewCheckOnlineOperationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewCheckOnlineOperation indicates an expected call of NewCheckOnlineOperation.
func (mr *MockAmlbridgeClientMockRecorder) NewCheckOnlineOperation(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewCheckOnlineOperation", reflect.TypeOf((*MockAmlbridgeClient)(nil).NewCheckOnlineOperation), varargs...)
}
