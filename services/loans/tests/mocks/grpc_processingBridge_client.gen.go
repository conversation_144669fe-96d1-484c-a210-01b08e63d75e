// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge (interfaces: ProcessingbridgeClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"

	processing_bridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/processing-bridge"
)

// MockProcessingbridgeClient is a mock of ProcessingbridgeClient interface.
type MockProcessingbridgeClient struct {
	ctrl     *gomock.Controller
	recorder *MockProcessingbridgeClientMockRecorder
}

// MockProcessingbridgeClientMockRecorder is the mock recorder for MockProcessingbridgeClient.
type MockProcessingbridgeClientMockRecorder struct {
	mock *MockProcessingbridgeClient
}

// NewMockProcessingbridgeClient creates a new mock instance.
func NewMockProcessingbridgeClient(ctrl *gomock.Controller) *MockProcessingbridgeClient {
	mock := &MockProcessingbridgeClient{ctrl: ctrl}
	mock.recorder = &MockProcessingbridgeClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProcessingbridgeClient) EXPECT() *MockProcessingbridgeClientMockRecorder {
	return m.recorder
}

// CreateClientAccountAndCard mocks base method.
func (m *MockProcessingbridgeClient) CreateClientAccountAndCard(arg0 context.Context, arg1 *processing_bridge.CreateClientAccountAndCardReq, arg2 ...grpc.CallOption) (*processing_bridge.CreateClientAccountAndCardResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateClientAccountAndCard", varargs...)
	ret0, _ := ret[0].(*processing_bridge.CreateClientAccountAndCardResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateClientAccountAndCard indicates an expected call of CreateClientAccountAndCard.
func (mr *MockProcessingbridgeClientMockRecorder) CreateClientAccountAndCard(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateClientAccountAndCard", reflect.TypeOf((*MockProcessingbridgeClient)(nil).CreateClientAccountAndCard), varargs...)
}

// CreatePaymentCreditAuth mocks base method.
func (m *MockProcessingbridgeClient) CreatePaymentCreditAuth(arg0 context.Context, arg1 *processing_bridge.CreatePaymentCreditAuthReq, arg2 ...grpc.CallOption) (*processing_bridge.CreatePaymentCreditAuthResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreatePaymentCreditAuth", varargs...)
	ret0, _ := ret[0].(*processing_bridge.CreatePaymentCreditAuthResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePaymentCreditAuth indicates an expected call of CreatePaymentCreditAuth.
func (mr *MockProcessingbridgeClientMockRecorder) CreatePaymentCreditAuth(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePaymentCreditAuth", reflect.TypeOf((*MockProcessingbridgeClient)(nil).CreatePaymentCreditAuth), varargs...)
}

// CreatePaymentDebitAuth mocks base method.
func (m *MockProcessingbridgeClient) CreatePaymentDebitAuth(arg0 context.Context, arg1 *processing_bridge.CreatePaymentDebitAuthReq, arg2 ...grpc.CallOption) (*processing_bridge.CreatePaymentDebitAuthResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreatePaymentDebitAuth", varargs...)
	ret0, _ := ret[0].(*processing_bridge.CreatePaymentDebitAuthResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePaymentDebitAuth indicates an expected call of CreatePaymentDebitAuth.
func (mr *MockProcessingbridgeClientMockRecorder) CreatePaymentDebitAuth(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePaymentDebitAuth", reflect.TypeOf((*MockProcessingbridgeClient)(nil).CreatePaymentDebitAuth), varargs...)
}

// GetCardAndFinContractStatus mocks base method.
func (m *MockProcessingbridgeClient) GetCardAndFinContractStatus(arg0 context.Context, arg1 *processing_bridge.GetFinContractStatusReq, arg2 ...grpc.CallOption) (*processing_bridge.GetFinContractStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCardAndFinContractStatus", varargs...)
	ret0, _ := ret[0].(*processing_bridge.GetFinContractStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCardAndFinContractStatus indicates an expected call of GetCardAndFinContractStatus.
func (mr *MockProcessingbridgeClientMockRecorder) GetCardAndFinContractStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCardAndFinContractStatus", reflect.TypeOf((*MockProcessingbridgeClient)(nil).GetCardAndFinContractStatus), varargs...)
}

// GetPaymentFinDoc mocks base method.
func (m *MockProcessingbridgeClient) GetPaymentFinDoc(arg0 context.Context, arg1 *processing_bridge.GetPaymentFinDocReq, arg2 ...grpc.CallOption) (*processing_bridge.GetPaymentFinDocResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPaymentFinDoc", varargs...)
	ret0, _ := ret[0].(*processing_bridge.GetPaymentFinDocResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentFinDoc indicates an expected call of GetPaymentFinDoc.
func (mr *MockProcessingbridgeClientMockRecorder) GetPaymentFinDoc(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentFinDoc", reflect.TypeOf((*MockProcessingbridgeClient)(nil).GetPaymentFinDoc), varargs...)
}

// GetRequisitesUnmasked mocks base method.
func (m *MockProcessingbridgeClient) GetRequisitesUnmasked(arg0 context.Context, arg1 *processing_bridge.GetRequisitesUnmaskedReq, arg2 ...grpc.CallOption) (*processing_bridge.GetRequisitesUnmaskedResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRequisitesUnmasked", varargs...)
	ret0, _ := ret[0].(*processing_bridge.GetRequisitesUnmaskedResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRequisitesUnmasked indicates an expected call of GetRequisitesUnmasked.
func (mr *MockProcessingbridgeClientMockRecorder) GetRequisitesUnmasked(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRequisitesUnmasked", reflect.TypeOf((*MockProcessingbridgeClient)(nil).GetRequisitesUnmasked), varargs...)
}

// GetTokenizeCardInfo mocks base method.
func (m *MockProcessingbridgeClient) GetTokenizeCardInfo(arg0 context.Context, arg1 *processing_bridge.GetTokenizeCardInfoReq, arg2 ...grpc.CallOption) (*processing_bridge.GetTokenizeCardInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTokenizeCardInfo", varargs...)
	ret0, _ := ret[0].(*processing_bridge.GetTokenizeCardInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenizeCardInfo indicates an expected call of GetTokenizeCardInfo.
func (mr *MockProcessingbridgeClientMockRecorder) GetTokenizeCardInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenizeCardInfo", reflect.TypeOf((*MockProcessingbridgeClient)(nil).GetTokenizeCardInfo), varargs...)
}

// GetTokenizeInitData mocks base method.
func (m *MockProcessingbridgeClient) GetTokenizeInitData(arg0 context.Context, arg1 *processing_bridge.GetTokenizeInitDataReq, arg2 ...grpc.CallOption) (*processing_bridge.GetTokenizeInitDataResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTokenizeInitData", varargs...)
	ret0, _ := ret[0].(*processing_bridge.GetTokenizeInitDataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenizeInitData indicates an expected call of GetTokenizeInitData.
func (mr *MockProcessingbridgeClientMockRecorder) GetTokenizeInitData(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenizeInitData", reflect.TypeOf((*MockProcessingbridgeClient)(nil).GetTokenizeInitData), varargs...)
}

// HealthCheck mocks base method.
func (m *MockProcessingbridgeClient) HealthCheck(arg0 context.Context, arg1 *processing_bridge.HealthCheckReq, arg2 ...grpc.CallOption) (*processing_bridge.HealthCheckResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HealthCheck", varargs...)
	ret0, _ := ret[0].(*processing_bridge.HealthCheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockProcessingbridgeClientMockRecorder) HealthCheck(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockProcessingbridgeClient)(nil).HealthCheck), varargs...)
}

// PaymentCredit mocks base method.
func (m *MockProcessingbridgeClient) PaymentCredit(arg0 context.Context, arg1 *processing_bridge.PaymentCreditReq, arg2 ...grpc.CallOption) (*processing_bridge.PaymentCreditResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PaymentCredit", varargs...)
	ret0, _ := ret[0].(*processing_bridge.PaymentCreditResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PaymentCredit indicates an expected call of PaymentCredit.
func (mr *MockProcessingbridgeClientMockRecorder) PaymentCredit(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PaymentCredit", reflect.TypeOf((*MockProcessingbridgeClient)(nil).PaymentCredit), varargs...)
}

// PaymentCreditPresent mocks base method.
func (m *MockProcessingbridgeClient) PaymentCreditPresent(arg0 context.Context, arg1 *processing_bridge.PaymentCreditPresentReq, arg2 ...grpc.CallOption) (*processing_bridge.PaymentCreditPresentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PaymentCreditPresent", varargs...)
	ret0, _ := ret[0].(*processing_bridge.PaymentCreditPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PaymentCreditPresent indicates an expected call of PaymentCreditPresent.
func (mr *MockProcessingbridgeClientMockRecorder) PaymentCreditPresent(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PaymentCreditPresent", reflect.TypeOf((*MockProcessingbridgeClient)(nil).PaymentCreditPresent), varargs...)
}

// PaymentCreditReverse mocks base method.
func (m *MockProcessingbridgeClient) PaymentCreditReverse(arg0 context.Context, arg1 *processing_bridge.PaymentCreditReverseReq, arg2 ...grpc.CallOption) (*processing_bridge.PaymentCreditReverseResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PaymentCreditReverse", varargs...)
	ret0, _ := ret[0].(*processing_bridge.PaymentCreditReverseResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PaymentCreditReverse indicates an expected call of PaymentCreditReverse.
func (mr *MockProcessingbridgeClientMockRecorder) PaymentCreditReverse(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PaymentCreditReverse", reflect.TypeOf((*MockProcessingbridgeClient)(nil).PaymentCreditReverse), varargs...)
}

// PaymentDebit mocks base method.
func (m *MockProcessingbridgeClient) PaymentDebit(arg0 context.Context, arg1 *processing_bridge.PaymentDebitReq, arg2 ...grpc.CallOption) (*processing_bridge.PaymentDebitResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PaymentDebit", varargs...)
	ret0, _ := ret[0].(*processing_bridge.PaymentDebitResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PaymentDebit indicates an expected call of PaymentDebit.
func (mr *MockProcessingbridgeClientMockRecorder) PaymentDebit(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PaymentDebit", reflect.TypeOf((*MockProcessingbridgeClient)(nil).PaymentDebit), varargs...)
}

// PaymentDebitPresent mocks base method.
func (m *MockProcessingbridgeClient) PaymentDebitPresent(arg0 context.Context, arg1 *processing_bridge.PaymentDebitPresentReq, arg2 ...grpc.CallOption) (*processing_bridge.PaymentDebitPresentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PaymentDebitPresent", varargs...)
	ret0, _ := ret[0].(*processing_bridge.PaymentDebitPresentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PaymentDebitPresent indicates an expected call of PaymentDebitPresent.
func (mr *MockProcessingbridgeClientMockRecorder) PaymentDebitPresent(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PaymentDebitPresent", reflect.TypeOf((*MockProcessingbridgeClient)(nil).PaymentDebitPresent), varargs...)
}

// PaymentDebitReverse mocks base method.
func (m *MockProcessingbridgeClient) PaymentDebitReverse(arg0 context.Context, arg1 *processing_bridge.PaymentDebitReverseReq, arg2 ...grpc.CallOption) (*processing_bridge.PaymentDebitReverseResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PaymentDebitReverse", varargs...)
	ret0, _ := ret[0].(*processing_bridge.PaymentDebitReverseResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PaymentDebitReverse indicates an expected call of PaymentDebitReverse.
func (mr *MockProcessingbridgeClientMockRecorder) PaymentDebitReverse(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PaymentDebitReverse", reflect.TypeOf((*MockProcessingbridgeClient)(nil).PaymentDebitReverse), varargs...)
}
