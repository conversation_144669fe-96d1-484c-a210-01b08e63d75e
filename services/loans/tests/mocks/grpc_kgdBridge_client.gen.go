// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/specs/proto/kgd-bridge (interfaces: KgdbridgeClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"

	kgd_bridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/kgd-bridge"
)

// MockKgdbridgeClient is a mock of KgdbridgeClient interface.
type MockKgdbridgeClient struct {
	ctrl     *gomock.Controller
	recorder *MockKgdbridgeClientMockRecorder
}

// MockKgdbridgeClientMockRecorder is the mock recorder for MockKgdbridgeClient.
type MockKgdbridgeClientMockRecorder struct {
	mock *MockKgdbridgeClient
}

// NewMockKgdbridgeClient creates a new mock instance.
func NewMockKgdbridgeClient(ctrl *gomock.Controller) *MockKgdbridgeClient {
	mock := &MockKgdbridgeClient{ctrl: ctrl}
	mock.recorder = &MockKgdbridgeClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockKgdbridgeClient) EXPECT() *MockKgdbridgeClientMockRecorder {
	return m.recorder
}

// GetDebts mocks base method.
func (m *MockKgdbridgeClient) GetDebts(arg0 context.Context, arg1 *kgd_bridge.GetDebtsReq, arg2 ...grpc.CallOption) (*kgd_bridge.GetDebtsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDebts", varargs...)
	ret0, _ := ret[0].(*kgd_bridge.GetDebtsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDebts indicates an expected call of GetDebts.
func (mr *MockKgdbridgeClientMockRecorder) GetDebts(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDebts", reflect.TypeOf((*MockKgdbridgeClient)(nil).GetDebts), varargs...)
}

// GetPersonData mocks base method.
func (m *MockKgdbridgeClient) GetPersonData(arg0 context.Context, arg1 *kgd_bridge.GetPersonDataReq, arg2 ...grpc.CallOption) (*kgd_bridge.GetPersonDataResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPersonData", varargs...)
	ret0, _ := ret[0].(*kgd_bridge.GetPersonDataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPersonData indicates an expected call of GetPersonData.
func (mr *MockKgdbridgeClientMockRecorder) GetPersonData(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersonData", reflect.TypeOf((*MockKgdbridgeClient)(nil).GetPersonData), varargs...)
}

// HealthCheck mocks base method.
func (m *MockKgdbridgeClient) HealthCheck(arg0 context.Context, arg1 *kgd_bridge.HealthCheckReq, arg2 ...grpc.CallOption) (*kgd_bridge.HealthCheckResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HealthCheck", varargs...)
	ret0, _ := ret[0].(*kgd_bridge.HealthCheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockKgdbridgeClientMockRecorder) HealthCheck(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockKgdbridgeClient)(nil).HealthCheck), varargs...)
}
