// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package mocks

import (
	"testing"

	"github.com/golang/mock/gomock"
)

type Mocks struct {
	GRPC      GRPC
	Providers Providers
}

type GRPC struct {
	Users            *MockUsersClient
	Documents        *MockDocumentsClient
	Cardsaccounts    *MockCardsaccountsClient
	Kgdbridge        *MockKgdbridgeClient
	Colvirbridge     *MockColvirbridgeClient
	Amlbridge        *MockAmlbridgeClient
	Antifraud        *MockAntifraudClient
	Pkbbridge        *MockPkbbridgeClient
	Btsbridge        *MockBtsbridgeClient
	Dictionary       *MockDictionaryClient
	Liveness         *MockLivenessClient
	Taskmanager      *MockTaskmanagerClient
	Jirabridge       *MockJirabridgeClient
	Bsasbridge       *MockBsasbridgeClient
	Payments         *MockPaymentsClient
	Processingbridge *MockProcessingbridgeClient
	Fileguard        *MockFileguardClient
}

type Providers struct {
}

func NewMocks(t *testing.T) *Mocks {
	return &Mocks{
		GRPC: GRPC{
			Users:            NewMockUsersClient(gomock.NewController(t)),
			Documents:        NewMockDocumentsClient(gomock.NewController(t)),
			Cardsaccounts:    NewMockCardsaccountsClient(gomock.NewController(t)),
			Kgdbridge:        NewMockKgdbridgeClient(gomock.NewController(t)),
			Colvirbridge:     NewMockColvirbridgeClient(gomock.NewController(t)),
			Amlbridge:        NewMockAmlbridgeClient(gomock.NewController(t)),
			Antifraud:        NewMockAntifraudClient(gomock.NewController(t)),
			Pkbbridge:        NewMockPkbbridgeClient(gomock.NewController(t)),
			Btsbridge:        NewMockBtsbridgeClient(gomock.NewController(t)),
			Dictionary:       NewMockDictionaryClient(gomock.NewController(t)),
			Liveness:         NewMockLivenessClient(gomock.NewController(t)),
			Taskmanager:      NewMockTaskmanagerClient(gomock.NewController(t)),
			Jirabridge:       NewMockJirabridgeClient(gomock.NewController(t)),
			Bsasbridge:       NewMockBsasbridgeClient(gomock.NewController(t)),
			Payments:         NewMockPaymentsClient(gomock.NewController(t)),
			Processingbridge: NewMockProcessingbridgeClient(gomock.NewController(t)),
			Fileguard:        NewMockFileguardClient(gomock.NewController(t)),
		},
		Providers: Providers{},
	}
}
