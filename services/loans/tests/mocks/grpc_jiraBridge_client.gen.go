// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/specs/proto/jira-bridge (interfaces: JirabridgeClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"

	jira_bridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/jira-bridge"
)

// MockJirabridgeClient is a mock of JirabridgeClient interface.
type MockJirabridgeClient struct {
	ctrl     *gomock.Controller
	recorder *MockJirabridgeClientMockRecorder
}

// MockJirabridgeClientMockRecorder is the mock recorder for MockJirabridgeClient.
type MockJirabridgeClientMockRecorder struct {
	mock *MockJirabridgeClient
}

// NewMockJirabridgeClient creates a new mock instance.
func NewMockJirabridgeClient(ctrl *gomock.Controller) *MockJirabridgeClient {
	mock := &MockJirabridgeClient{ctrl: ctrl}
	mock.recorder = &MockJirabridgeClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockJirabridgeClient) EXPECT() *MockJirabridgeClientMockRecorder {
	return m.recorder
}

// CreateIssue mocks base method.
func (m *MockJirabridgeClient) CreateIssue(arg0 context.Context, arg1 *jira_bridge.CreateIssueReq, arg2 ...grpc.CallOption) (*jira_bridge.CreateIssueResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateIssue", varargs...)
	ret0, _ := ret[0].(*jira_bridge.CreateIssueResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateIssue indicates an expected call of CreateIssue.
func (mr *MockJirabridgeClientMockRecorder) CreateIssue(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIssue", reflect.TypeOf((*MockJirabridgeClient)(nil).CreateIssue), varargs...)
}

// HealthCheck mocks base method.
func (m *MockJirabridgeClient) HealthCheck(arg0 context.Context, arg1 *jira_bridge.HealthCheckReq, arg2 ...grpc.CallOption) (*jira_bridge.HealthCheckResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HealthCheck", varargs...)
	ret0, _ := ret[0].(*jira_bridge.HealthCheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockJirabridgeClientMockRecorder) HealthCheck(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockJirabridgeClient)(nil).HealthCheck), varargs...)
}
