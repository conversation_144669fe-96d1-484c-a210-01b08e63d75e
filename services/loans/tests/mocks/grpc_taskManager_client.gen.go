// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/specs/proto/task-manager (interfaces: TaskmanagerClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"

	task_manager "git.redmadrobot.com/zaman/backend/zaman/specs/proto/task-manager"
)

// MockTaskmanagerClient is a mock of TaskmanagerClient interface.
type MockTaskmanagerClient struct {
	ctrl     *gomock.Controller
	recorder *MockTaskmanagerClientMockRecorder
}

// MockTaskmanagerClientMockRecorder is the mock recorder for MockTaskmanagerClient.
type MockTaskmanagerClientMockRecorder struct {
	mock *MockTaskmanagerClient
}

// NewMockTaskmanagerClient creates a new mock instance.
func NewMockTaskmanagerClient(ctrl *gomock.Controller) *MockTaskmanagerClient {
	mock := &MockTaskmanagerClient{ctrl: ctrl}
	mock.recorder = &MockTaskmanagerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTaskmanagerClient) EXPECT() *MockTaskmanagerClientMockRecorder {
	return m.recorder
}

// CreateTask mocks base method.
func (m *MockTaskmanagerClient) CreateTask(arg0 context.Context, arg1 *task_manager.CreateTaskReq, arg2 ...grpc.CallOption) (*task_manager.CreateTaskResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateTask", varargs...)
	ret0, _ := ret[0].(*task_manager.CreateTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTask indicates an expected call of CreateTask.
func (mr *MockTaskmanagerClientMockRecorder) CreateTask(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTask", reflect.TypeOf((*MockTaskmanagerClient)(nil).CreateTask), varargs...)
}

// GetTaskInfo mocks base method.
func (m *MockTaskmanagerClient) GetTaskInfo(arg0 context.Context, arg1 *task_manager.GetTaskInfoReq, arg2 ...grpc.CallOption) (*task_manager.GetTaskInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTaskInfo", varargs...)
	ret0, _ := ret[0].(*task_manager.GetTaskInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaskInfo indicates an expected call of GetTaskInfo.
func (mr *MockTaskmanagerClientMockRecorder) GetTaskInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaskInfo", reflect.TypeOf((*MockTaskmanagerClient)(nil).GetTaskInfo), varargs...)
}

// GetTasksList mocks base method.
func (m *MockTaskmanagerClient) GetTasksList(arg0 context.Context, arg1 *task_manager.GetTasksListReq, arg2 ...grpc.CallOption) (*task_manager.GetTasksListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTasksList", varargs...)
	ret0, _ := ret[0].(*task_manager.GetTasksListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTasksList indicates an expected call of GetTasksList.
func (mr *MockTaskmanagerClientMockRecorder) GetTasksList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTasksList", reflect.TypeOf((*MockTaskmanagerClient)(nil).GetTasksList), varargs...)
}

// HealthCheck mocks base method.
func (m *MockTaskmanagerClient) HealthCheck(arg0 context.Context, arg1 *task_manager.HealthCheckReq, arg2 ...grpc.CallOption) (*task_manager.HealthCheckResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HealthCheck", varargs...)
	ret0, _ := ret[0].(*task_manager.HealthCheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockTaskmanagerClientMockRecorder) HealthCheck(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockTaskmanagerClient)(nil).HealthCheck), varargs...)
}

// UpdateTask mocks base method.
func (m *MockTaskmanagerClient) UpdateTask(arg0 context.Context, arg1 *task_manager.UpdateTaskReq, arg2 ...grpc.CallOption) (*task_manager.UpdateTaskResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateTask", varargs...)
	ret0, _ := ret[0].(*task_manager.UpdateTaskResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTask indicates an expected call of UpdateTask.
func (mr *MockTaskmanagerClientMockRecorder) UpdateTask(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTask", reflect.TypeOf((*MockTaskmanagerClient)(nil).UpdateTask), varargs...)
}
