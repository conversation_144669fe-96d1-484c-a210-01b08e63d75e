package tests

import (
	"encoding/json"
	"math/rand"
	"slices"
	"strconv"

	"git.redmadrobot.com/backend-go/rmr-pkg/tools/conversion"
	"github.com/go-faker/faker/v4"
	"github.com/golang/mock/gomock"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/storage/postgres/ent"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/storage/postgres/ent/actionshistory"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/storage/postgres/ent/disbursementcontrol"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/storage/postgres/ent/document"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/storage/postgres/ent/loanapplication"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/usecase"
	cards_accounts "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"
	"git.redmadrobot.com/zaman/backend/zaman/specs/proto/dictionary"
	"git.redmadrobot.com/zaman/backend/zaman/specs/proto/documents"
	"git.redmadrobot.com/zaman/backend/zaman/specs/proto/liveness"
	"git.redmadrobot.com/zaman/backend/zaman/specs/proto/loans"
	pbTaskManager "git.redmadrobot.com/zaman/backend/zaman/specs/proto/task-manager"
	"git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"
	usersPb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"
)

func getPostIdentifyBtsDataUsersGetPersonalDataRespSuccess() (*users.GetPersonalDataResp, error) {
	return &users.GetPersonalDataResp{
		Name:       testUserName,
		Patronymic: testUserPatronymic,
		Surname:    testUserSurname,
		Iin:        testUserIin,
		BirthDate:  testBirthDate,
		Age:        42,
		Gender: &users.SubjectDataParams{
			NameRu:     "Мужской",
			NameKz:     "Ер",
			Code:       1,
			ChangeDate: "2008-03-01T13:21:45+05:00",
		},
		Citizenship: &users.SubjectDataParams{
			NameRu:     "КАЗАХСТАН",
			NameKz:     "ҚАЗАҚСТАН",
			Code:       consts.UsersCitizenshipKazakhstanCode,
			ChangeDate: "2008-03-01T13:21:44+05:00",
		},
		BirthPlace: &users.BirthPlace{
			City: testCity,
			Country: &users.SubjectDataParams{
				NameRu:     "Казахстан",
				NameKz:     "Қазақстан",
				Code:       consts.UsersCitizenshipKazakhstanCode,
				ChangeDate: "2008-03-01T13:21:44+05:00",
			},
			District: &users.SubjectDataParams{
				NameRu:     "АЛМАТЫ",
				NameKz:     "АЛМАТЫ",
				Code:       1910,
				ChangeDate: "2008-03-01T13:21:45+05:00",
			},
			Region: &users.SubjectDataParams{
				NameRu:     "АЛМАЛИНСКИЙ",
				NameKz:     "АЛМАЛЫ",
				Code:       1910274,
				ChangeDate: "2008-03-01T13:21:45+05:00",
			},
		},
		RegAddress: &users.RegAddress{
			Country: &users.SubjectDataParams{
				NameRu:     "Казахстан",
				NameKz:     "Қазақстан",
				Code:       consts.UsersCitizenshipKazakhstanCode,
				ChangeDate: "2024-06-10T11:16:01+05:00",
			},
			District: &users.SubjectDataParams{
				NameRu:     "КАРАГАНДИНСКАЯ ОБЛАСТЬ",
				NameKz:     "ҚАРАҒАНДЫ ОБЛЫСЫ",
				Code:       1930,
				ChangeDate: "2024-06-10T11:16:01+05:00",
			},
			Region: &users.SubjectDataParams{
				NameRu:     "КАРАГАНДА",
				NameKz:     "ҚАРАҒАНДЫ",
				Code:       1930401,
				ChangeDate: "2024-06-10T11:16:01+05:00",
			},
			Street:    "МИКРОРАЙОН 13",
			Flat:      "45",
			Building:  "11",
			City:      "ҚАЛА IШIНДЕГI АУДАНЫ Әлихан Бөкейхан",
			BeginDate: "2018-10-26",
		},
		Documents: []*users.Document{
			{
				Number:     "092362141",
				Name:       testUserName,
				Patronymic: testUserPatronymic,
				Surname:    testUserSurname,
				BeginDate:  "2022-09-01",
				EndDate:    "2030-01-16",
				BirthDate:  testBirthDate,
				Type: &users.DocumentDataParams{
					NameRu:     "УД",
					NameKz:     "УД",
					Code:       consts.UsersCitizenshipDocTypeCode,
					ChangeDate: "2008-03-01T13:21:45+05:00",
				},
				Status: &users.DocumentDataParams{
					NameRu:     "ДОКУМЕНТ ДЕЙСТВИТЕЛЕН",
					NameKz:     "ҚҰЖАТ ЖАРАМДЫ",
					Code:       consts.UsersDocumentStatusValidCode,
					ChangeDate: "2008-03-01T13:21:45+05:00",
				},
				IssueOrganization: &users.DocumentDataParams{
					NameRu:     "МИНИСТЕРСТВО ВНУТРЕННИХ ДЕЛ РК",
					NameKz:     "ҚР ІШКІ ІСТЕР МИНИСТРЛІГІ",
					Code:       consts.UsersCitizenshipDocTypeCode,
					ChangeDate: "2008-03-01T13:21:45+05:00",
				},
			},
		},
		ColvirInfo: &users.UserColvirInfo{
			ColvirClientCode:  "00007380",
			ColvirClientDepId: "692",
			ColvirClientId:    "24378",
		},
	}, nil
}

func (s *Suite) TestPostIdentifyBtsData_Success_NoCreditContractCounterInStorage() {
	s.testPostIdentifyBtsData(testPostIdentifyBtsDataParams{
		CreditContractCounterInStorage: nil,
	})
}

func (s *Suite) TestPostIdentifyBtsData_Success_CreditContractCounterInStorage15() {
	s.testPostIdentifyBtsData(testPostIdentifyBtsDataParams{
		CreditContractCounterInStorage: conversion.Ptr(int64(15)),
	})
}

func (s *Suite) TestPostIdentifyBtsData_Success_CreditContractCounterInStorage100000() {
	s.testPostIdentifyBtsData(testPostIdentifyBtsDataParams{
		CreditContractCounterInStorage: conversion.Ptr(int64(15)),
	})
}

type testPostIdentifyBtsDataParams struct {
	CreditContractCounterInStorage *int64
}

func (s *Suite) testPostIdentifyBtsData(params testPostIdentifyBtsDataParams) {
	s.ctx = addUserIDToContext(s.ctx)

	loanAppStorage, err := s.postgresDB.LoanApplication.Create().
		SetBusinessType(loanapplication.BusinessTypeRETAIL).
		SetStatus(loanapplication.StatusAPPROVED).
		SetUserID(testUserID.String()).
		SetPurpose(uuid.NewString()).
		SetUserIin(uuid.NewString()).
		SetJuicySessionID(testJuicySessionID).
		SetUserFullNameAtCreation(testUserFullName).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(loanAppStorage)

	loanConditionStorage, err := s.postgresDB.LoanCondition.Create().
		SetAmount(uint32(rand.Int31())).
		SetInterest(uint32(rand.Int31())).
		SetTerm(uint32(rand.Int31())).
		SetIsOriginal(true).
		SetIsApprovedBySpr(true).
		SetIsFinallyChosenByUser(true).
		SetApplication(loanAppStorage).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(loanConditionStorage)

	_, err = s.postgresDB.DisbursementControl.Create().
		SetMode(disbursementcontrol.Mode(consts.LoanDisbursementAutoMode)).
		Save(s.ctx)
	s.Require().NoError(err)

	expectedGetSurveyResp := s.generateGetSurveyResp(5)
	dictKatoData := s.surveyAddressToDictKatoData(expectedGetSurveyResp.GetAddress())
	dictExpectedDocsList := s.dictKatoDataToDictDocsList(dictKatoData)
	surveyStorage, err := s.createSurveyStorageByGetSurveyResp(expectedGetSurveyResp, loanAppStorage.ID)
	s.Require().NoError(err)
	s.Require().NotNil(surveyStorage)

	s.mocks.GRPC.Dictionary.EXPECT().DocGetListByFilter(gomock.Any(), gomock.Any()).Return(&dictionary.DocGetListByFilterResp{
		List: dictExpectedDocsList,
	}, nil).Times(2)

	calendarInfo, err := json.Marshal(docCalendarInfo)
	s.Require().NoError(err)

	s.mocks.GRPC.Dictionary.EXPECT().DocGetByName(gomock.Any(), gomock.Any()).Return(&dictionary.DocGetByNameResp{
		Doc: &dictionary.Doc{
			Id:   "0",
			Data: string(calendarInfo),
		},
	}, nil).Times(1)

	postIdentifyBtsDataUsersGetPersonalDataRespSuccess, err := getPostIdentifyBtsDataUsersGetPersonalDataRespSuccess()
	s.Require().NoError(err)

	livenessPersonalData := &liveness.PersonalData{
		Documents: []*liveness.Document{
			{
				Type:   &liveness.DocumentType{},
				Status: &liveness.DocumentStatus{},
			},
		},
	}
	err = faker.FakeData(livenessPersonalData)
	s.Require().NoError(err)
	livenessPersonalData.Name = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Name
	livenessPersonalData.Surname = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Surname
	livenessPersonalData.Patronymic = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Patronymic
	if len(livenessPersonalData.Documents) > 0 {
		livenessPersonalData.Documents[0].Type.Code = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Documents[0].Type.Code
		livenessPersonalData.Documents[0].Status.Code = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Documents[0].Status.Code
	}

	livenessPersonalData.Citizenship.Code = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Citizenship.Code
	livenessPersonalData.BirthDate = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.BirthDate

	s.mocks.GRPC.Liveness.EXPECT().VerifyAndGetPersonalData(gomock.Any(), gomock.Any()).Return(&liveness.VerifyLivenessCodeResp{
		PersonalData: livenessPersonalData,
	}, nil).Times(1)

	s.mocks.GRPC.Users.EXPECT().GetPersonalData(gomock.Any(), gomock.Any()).Return(
		postIdentifyBtsDataUsersGetPersonalDataRespSuccess, nil,
	)

	cardsAccountsMockResp := &cards_accounts.GetAccountsResponse{
		Accounts: []*cards_accounts.Account{
			{
				Status: consts.BankAccountStatusActive.String(),
				ID:     uuid.NewString(),
				Arrest: &cards_accounts.Arrest{
					Blocking: false,
				},
				Currency: consts.DefaultKazakhstanCurrency,
			},
		},
	}
	s.mocks.GRPC.Cardsaccounts.EXPECT().GetAccounts(gomock.Any(), gomock.Any()).Return(cardsAccountsMockResp, nil).Times(1)

	dictEducationData := s.getEducationDict()
	dictEducationJsonData, err := json.Marshal(dictEducationData)
	s.Require().NoError(err)
	s.mocks.GRPC.Dictionary.EXPECT().DocGetByName(gomock.Any(), gomock.Any()).Return(&dictionary.DocGetByNameResp{
		Doc: &dictionary.Doc{
			Id:   "1",
			Name: consts.DictNameEducationTypes,
			Data: string(dictEducationJsonData),
		},
	}, nil).Times(1)

	dictEmploymentData := s.getEmploymentDict()
	dictEmploymentJsonData, err := json.Marshal(dictEmploymentData)
	s.Require().NoError(err)
	s.mocks.GRPC.Dictionary.EXPECT().DocGetByName(gomock.Any(), gomock.Any()).Return(&dictionary.DocGetByNameResp{
		Doc: &dictionary.Doc{
			Id:   "1",
			Name: consts.DictNameEmploymentTypes,
			Data: string(dictEmploymentJsonData),
		},
	}, nil).Times(1)

	dictRelationData := s.getRelationDict()
	dictRelationJsonData, err := json.Marshal(dictRelationData)
	s.Require().NoError(err)
	s.mocks.GRPC.Dictionary.EXPECT().DocGetByName(gomock.Any(), gomock.Any()).Return(&dictionary.DocGetByNameResp{
		Doc: &dictionary.Doc{
			Id:   "1",
			Name: consts.DictNameRelationTypes,
			Data: string(dictRelationJsonData),
		},
	}, nil).Times(1)

	dictEducationData2 := s.getEducationDict()
	dictEducationJsonData2, err := json.Marshal(dictEducationData2)
	s.Require().NoError(err)
	s.mocks.GRPC.Dictionary.EXPECT().DocGetByName(gomock.Any(), gomock.Any()).Return(&dictionary.DocGetByNameResp{
		Doc: &dictionary.Doc{
			Id:   "1",
			Name: consts.DictNameEducationTypes,
			Data: string(dictEducationJsonData2),
		},
	}, nil).Times(1)

	dictEmploymentData2 := s.getEmploymentDict()
	dictEmploymentJsonData2, err := json.Marshal(dictEmploymentData2)
	s.Require().NoError(err)
	s.mocks.GRPC.Dictionary.EXPECT().DocGetByName(gomock.Any(), gomock.Any()).Return(&dictionary.DocGetByNameResp{
		Doc: &dictionary.Doc{
			Id:   "1",
			Name: consts.DictNameEmploymentTypes,
			Data: string(dictEmploymentJsonData2),
		},
	}, nil).Times(1)

	dictRelationData2 := s.getRelationDict()
	dictRelationJsonData2, err := json.Marshal(dictRelationData2)
	s.Require().NoError(err)
	s.mocks.GRPC.Dictionary.EXPECT().DocGetByName(gomock.Any(), gomock.Any()).Return(&dictionary.DocGetByNameResp{
		Doc: &dictionary.Doc{
			Id:   "1",
			Name: consts.DictNameRelationTypes,
			Data: string(dictRelationJsonData2),
		},
	}, nil).Times(1)

	mockZamanBankInfo := entity.DictionaryZamanBankInfo{
		Bin: uuid.NewString(),
		Address: entity.BankInfoLocalizedValue{
			RU: uuid.NewString(),
			KZ: uuid.NewString(),
		},
		City: entity.BankInfoLocalizedValue{
			RU: uuid.NewString(),
			KZ: uuid.NewString(),
		},
		PhoneNumber: uuid.NewString(),
		Email:       uuid.NewString(),
		Iik:         uuid.NewString(),
		Bic:         uuid.NewString(),
		Name: entity.BankInfoLocalizedValue{
			RU: uuid.NewString(),
			KZ: uuid.NewString(),
		},
		Representative: entity.BankInfoRepresentative{
			FullName: uuid.NewString(),
			JobPosition: entity.BankInfoLocalizedValue{
				RU: uuid.NewString(),
				KZ: uuid.NewString(),
			},
			DocumentNameGenitive: entity.BankInfoLocalizedValue{
				RU: uuid.NewString(),
				KZ: uuid.NewString(),
			},
		},
		Links: entity.BankInfoLinks{
			Main:      uuid.NewString(),
			Documents: uuid.NewString(),
		},
	}

	mockZamanInfoJson, err := json.Marshal(mockZamanBankInfo)
	s.Require().NoError(err)

	s.mocks.GRPC.Dictionary.EXPECT().DocGetByName(gomock.Any(), gomock.Any()).Return(&dictionary.DocGetByNameResp{
		Doc: &dictionary.Doc{
			Data: string(mockZamanInfoJson),
		},
	}, nil).Times(1)

	productExchangeInfo := entity.DictionaryProductExchangeInfo{
		ProductName:  uuid.NewString(),
		ProductPrice: rand.Uint64(),
		CurrencyRate: strconv.FormatFloat(rand.Float64(), 'f', -1, 64),
	}

	productExchangeInfoJson, err := json.Marshal(productExchangeInfo)
	s.Require().NoError(err)

	s.mocks.GRPC.Dictionary.EXPECT().DocGetByName(gomock.Any(), gomock.Any()).Return(&dictionary.DocGetByNameResp{
		Doc: &dictionary.Doc{
			Data: string(productExchangeInfoJson),
		},
	}, nil).Times(1)

	s.mocks.GRPC.Documents.EXPECT().GenerateCreditContractWithRepaymentSchedule(gomock.Any(), gomock.Any()).Return(&documents.DocumentResp{
		Id:      uuid.NewString(),
		Title:   uuid.NewString(),
		Type:    consts.DocTypeCreditContractWithRepaymentSchedule.String(),
		Version: rand.Int31(),
		Link:    uuid.NewString(),
	}, nil).Times(1)

	mockExchangeInfo := entity.DictionaryExchangeInfo{}
	err = faker.FakeData(&mockExchangeInfo)
	s.Require().NoError(err)

	mockExchangeInfoJson, err := json.Marshal(mockExchangeInfo)
	s.Require().NoError(err)

	s.mocks.GRPC.Dictionary.EXPECT().DocGetByName(gomock.Any(), gomock.Any()).Return(&dictionary.DocGetByNameResp{
		Doc: &dictionary.Doc{
			Data: string(mockExchangeInfoJson),
		},
	}, nil).Times(1)

	s.mocks.GRPC.Documents.EXPECT().GenerateAppSaleOfGoodsDocument(gomock.Any(), gomock.Any()).Return(&documents.DocumentResp{
		Id:      uuid.NewString(),
		Title:   uuid.NewString(),
		Type:    consts.DocTypeApplicationSaleOfGoods.String(),
		Version: rand.Int31(),
		Link:    uuid.NewString(),
	}, nil).Times(1)

	s.mocks.GRPC.Documents.EXPECT().GenerateBankingServiceApplicationDocument(gomock.Any(), gomock.Any()).Return(&documents.DocumentResp{
		Id:      uuid.NewString(),
		Title:   uuid.NewString(),
		Type:    consts.DocTypeBankingServiceApplication.String(),
		Version: rand.Int31(),
		Link:    uuid.NewString(),
	}, nil)

	s.mocks.GRPC.Colvirbridge.EXPECT().LoanCalcLoadPreSchedule(gomock.Any(), gomock.Any()).Return(
		testColvirbridgeLoanCalcLoadPreScheduleResponse, nil,
	).Times(1)

	_, err = s.postgresDB.ActionsHistory.Create().
		SetActionType(actionshistory.ActionType(consts.ApprovedLoanAppInternalChecksPassed)).
		SetApplicationID(loanAppStorage.ID).
		Save(s.ctx)
	s.Require().NoError(err)

	createTaskMockResp := &pbTaskManager.CreateTaskResp{}
	err = faker.FakeData(createTaskMockResp)
	s.Require().NoError(err)

	s.mocks.GRPC.Taskmanager.EXPECT().CreateTask(gomock.Any(), gomock.Any()).Return(createTaskMockResp, nil).Times(1)

	_, err = s.postgresDB.Document.Create().
		SetType(document.Type(consts.DocTypeBankingServiceApplication)).
		SetDocID(uuid.New().String()).
		SetApplicationID(loanAppStorage.ID).
		Save(s.ctx)

	s.mocks.GRPC.Dictionary.EXPECT().DocGetByName(gomock.Any(), gomock.Any()).Return(&dictionary.DocGetByNameResp{
		Doc: &dictionary.Doc{
			Id:   "0",
			Data: string(calendarInfo),
		},
	}, nil).Times(1)

	// Для проверки
	creditContractCounter := int64(0)
	if params.CreditContractCounterInStorage != nil {
		creditContractCounter = *params.CreditContractCounterInStorage
	}
	expectedCreditContractSerialNumber := creditContractCounter + 1

	kzTime, err := utils.GetCurrentKzTime()
	s.Require().NoError(err)

	prefix, err := usecase.GenerateCreditContractPrefix(s.ctx, consts.GenerateCreditContractWithRepaymentScheduleProductCode, consts.LoanApplicationBusinessType(loanAppStorage.BusinessType), consts.GenerateCreditContractWithRepaymentScheduleDepartmentCode, kzTime.Year())
	s.Require().NoError(err)

	expectedCreditContractNumber, err := usecase.CombineCreditContractNumberPrefixAndSerialNumber(prefix, expectedCreditContractSerialNumber)
	if creditContractCounter != 0 {
		_, err = s.postgresDB.CreditContractWithRepaymentScheduleCounter.Create().
			SetPrefix(prefix).
			SetCounter(creditContractCounter).
			Save(s.ctx)
		s.Require().NoError(err)
	}

	resp, err := s.grpc.PostIdentifyBtsData(s.ctx, &loans.PostIdentifyBtsDataReq{
		ApplicationID: loanAppStorage.ID.String(),
		Code:          uuid.NewString(),
	})
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().Nil(resp.Reason)
	s.Require().Equal(consts.LoanApplicationStatusApproved.String(), resp.ApplicationStatus)

	storageLoanApp, err := s.postgresDB.LoanApplication.Query().Where(loanapplication.ID(loanAppStorage.ID)).
		WithDocuments().
		First(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(storageLoanApp)
	s.Require().NotNil(storageLoanApp.DisbursementAccountID)
	s.Require().Equal(cardsAccountsMockResp.GetAccounts()[0].ID, *storageLoanApp.DisbursementAccountID)

	loanAppDocs, err := storageLoanApp.Edges.DocumentsOrErr()
	s.Require().NoError(err)
	creditContractIdx := slices.IndexFunc(loanAppDocs, func(doc *ent.Document) bool {
		return doc.Type == document.Type(consts.DocTypeCreditContractWithRepaymentSchedule)
	})
	s.Require().NotEqual(creditContractIdx, -1)
	creditContract := loanAppDocs[creditContractIdx]
	s.Require().NotNil(creditContract)
	s.Require().Equal(&expectedCreditContractNumber, creditContract.Number)
}

func (s *Suite) TestPostIdentifyBtsData_NoEligibleBankAccount() {
	s.ctx = addUserIDToContext(s.ctx)

	loanAppStorage, err := s.postgresDB.LoanApplication.Create().
		SetBusinessType(loanapplication.BusinessTypeRETAIL).
		SetStatus(loanapplication.StatusAPPROVED).
		SetUserID(testUserID.String()).
		SetPurpose(uuid.NewString()).
		SetUserIin(uuid.NewString()).
		SetJuicySessionID(testJuicySessionID).
		SetUserFullNameAtCreation(testUserFullName).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(loanAppStorage)

	loanConditionStorage, err := s.postgresDB.LoanCondition.Create().
		SetAmount(uint32(rand.Int31())).
		SetInterest(uint32(rand.Int31())).
		SetTerm(uint32(rand.Int31())).
		SetIsOriginal(true).
		SetIsApprovedBySpr(true).
		SetIsFinallyChosenByUser(true).
		SetApplication(loanAppStorage).
		Save(s.ctx)
	s.Require().NoError(err)
	s.Require().NotNil(loanConditionStorage)

	calendarInfo, err := json.Marshal(docCalendarInfo)
	s.Require().NoError(err)

	s.mocks.GRPC.Dictionary.EXPECT().DocGetByName(gomock.Any(), gomock.Any()).Return(&dictionary.DocGetByNameResp{
		Doc: &dictionary.Doc{
			Id:   "0",
			Data: string(calendarInfo),
		},
	}, nil).Times(1)

	postIdentifyBtsDataUsersGetPersonalDataRespSuccess, err := getPostIdentifyBtsDataUsersGetPersonalDataRespSuccess()
	s.Require().NoError(err)

	livenessPersonalData := &liveness.PersonalData{
		Documents: []*liveness.Document{
			{
				Type:   &liveness.DocumentType{},
				Status: &liveness.DocumentStatus{},
			},
		},
	}
	err = faker.FakeData(livenessPersonalData)
	s.Require().NoError(err)

	if len(livenessPersonalData.Documents) == 0 {
		livenessPersonalData.Documents = []*liveness.Document{
			{
				Type:   &liveness.DocumentType{},
				Status: &liveness.DocumentStatus{},
			},
		}
	}
	livenessPersonalData.Name = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Name
	livenessPersonalData.Surname = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Surname
	livenessPersonalData.Patronymic = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Patronymic
	livenessPersonalData.Documents[0].Type.Code = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Documents[0].Type.Code
	livenessPersonalData.Documents[0].Status.Code = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Documents[0].Status.Code
	livenessPersonalData.Citizenship.Code = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.Citizenship.Code
	livenessPersonalData.BirthDate = postIdentifyBtsDataUsersGetPersonalDataRespSuccess.BirthDate

	s.mocks.GRPC.Liveness.EXPECT().
		VerifyAndGetPersonalData(gomock.Any(), gomock.Any()).
		Return(&liveness.VerifyLivenessCodeResp{
			PersonalData: livenessPersonalData,
		}, nil).
		Times(1)

	s.mocks.GRPC.Users.EXPECT().GetPersonalData(gomock.Any(), gomock.Any()).Return(
		postIdentifyBtsDataUsersGetPersonalDataRespSuccess, nil,
	)

	cardsAccountsMockResp := &cards_accounts.GetAccountsResponse{
		Accounts: []*cards_accounts.Account{
			{
				Status: "", // активного счета нет
				ID:     uuid.NewString(),
				Arrest: &cards_accounts.Arrest{
					Blocking: false,
				},
				Currency: consts.DefaultKazakhstanCurrency,
			},
		},
	}

	s.mocks.GRPC.Cardsaccounts.EXPECT().
		GetAccounts(gomock.Any(), gomock.Any()).
		Return(cardsAccountsMockResp, nil).
		Times(1)

	_, err = s.postgresDB.ActionsHistory.Create().
		SetActionType(actionshistory.ActionType(consts.ApprovedLoanAppInternalChecksPassed)).
		SetApplicationID(loanAppStorage.ID).
		Save(s.ctx)
	s.Require().NoError(err)

	s.mocks.GRPC.Users.EXPECT().GetUserSmeIPInfo(gomock.Any(), gomock.Any()).Return(&usersPb.GetUserSmeIPInfoResp{
		Phone: "+***********",
		SmeIpInfo: &usersPb.SmeIPInfo{
			RegistrationDate: "2020-01-01",
			RegistrationCert: "cert123",
		},
	}, nil).Times(1)

	resp, err := s.grpc.PostIdentifyBtsData(s.ctx, &loans.PostIdentifyBtsDataReq{
		ApplicationID: loanAppStorage.ID.String(),
		Code:          uuid.NewString(),
	})
	s.Require().NoError(err)
	s.Require().NotNil(resp)
	s.Require().Equal(consts.LoanApplicationStatusRejected.String(), resp.ApplicationStatus)
	s.Require().NotNil(resp.GetReason())
	s.Require().Equal(consts.ApprovedLoanAppTechError.String(), resp.GetReason().GetCode())
}
