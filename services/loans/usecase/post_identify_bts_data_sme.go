package usecase

import (
	"context"
	"fmt"
	"slices"
	"strconv"
	"time"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/helpers/getpermitdocuments"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/helpers/ipinfo"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/acceptlanguage"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/conversion"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"google.golang.org/protobuf/types/known/timestamppb"

	loansErrs "git.redmadrobot.com/zaman/backend/zaman/errs/loans"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/helpers/locale"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/helpers/personaldata"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/helpers/survey"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/entity"
	cards_accounts "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"
	pkb_bridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/pkb-bridge"

	colvir_bridge "git.redmadrobot.com/zaman/backend/zaman/specs/proto/colvir-bridge"
	"git.redmadrobot.com/zaman/backend/zaman/specs/proto/documents"
	pbLiveness "git.redmadrobot.com/zaman/backend/zaman/specs/proto/liveness"

	"git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"
)

// PostIdentifyBtsDataSme - метод для верификации полученного кода в финале прохождения Liveness
//
//nolint:gocyclo,funlen
func (u *useCasesImpl) PostIdentifyBtsDataSme(ctx context.Context, req *entity.PostIdentifyBtsDataSmeReq) (*entity.PostIdentifyBtsDataSmeResult, error) {
	logger := logs.FromContext(ctx)
	logger = conversion.Ptr(
		logger.
			With().
			Str(consts.LoggerFieldLoanApplicationID, req.ApplicationID).
			Logger(),
	)
	ctx = logs.ToContext(ctx, logger)

	// Получаем userInfo из контекста запроса
	userInfo, err := utils.GetUserDataFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to utils.GetUserDataFromContext: %w", err)
	}

	// Получаем данные по кредиту из БД
	loanApp, err := u.Providers.Storage.GetLoanApplicationByID(ctx, req.ApplicationID, entity.LoanAppParams{
		WithConditions:     true,
		WithHistoryActions: true,
	})
	if err != nil {
		return nil, loansErrs.LoansErrs().ApplicationNotFoundError()
	}
	logger.Debug().Interface("loanApp", loanApp).Msg("loanApp")

	ctxForWaitingInternalChecks, cancel := context.WithTimeout(ctx, PostIdentifyBtsDataTimeout)
	defer cancel()

	for {
		approvedLoanAppStatus, err := u.GetApprovedLoanAppStatus(ctxForWaitingInternalChecks, &entity.GetApprovedLoanAppStatusReq{
			ApplicationID: loanApp.ID,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to GetApprovedLoanAppStatus: %w", err)
		}

		if approvedLoanAppStatus.ApplicationStatus == consts.LoanApplicationStatusApproved &&
			!approvedLoanAppStatus.IsInProgress &&
			approvedLoanAppStatus.Reason == nil {
			break
		}

		if approvedLoanAppStatus.ApplicationStatus == consts.LoanApplicationStatusRejected || approvedLoanAppStatus.Reason != nil {
			return &entity.PostIdentifyBtsDataSmeResult{
				ApplicationStatus: approvedLoanAppStatus.ApplicationStatus,
				Reason:            approvedLoanAppStatus.Reason,
			}, nil
		}

		select {
		case <-ctxForWaitingInternalChecks.Done():
			return nil, fmt.Errorf("timeout while waiting for internal checks to pass")
		case <-time.After(PostIdentifyBtsDataInternalChecksFetchInterval):
		}
	}

	// Проверяем код и получаем из ЦОИД персональные данные пользователя через сервис liveness
	livenessVerifyResp, err := u.Providers.Liveness.VerifyAndGetPersonalData(ctx, &pbLiveness.VerifyLivenessCodeReq{
		Iin:  loanApp.UserIin,
		Code: req.Code,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to Liveness.VerifyAndGetPersonalData: %w", err)
	}

	livenessPersonalData := livenessVerifyResp.GetPersonalData()
	if livenessPersonalData == nil {
		return nil, fmt.Errorf("no personal data for user with iin: %s in liveness verify resp", loanApp.UserIin)
	}
	logger.Debug().Interface("livenessPersonalData", livenessPersonalData).Msg("livenessPersonalData")

	// Получаем данные юзера из сервиса users
	usersPersonalData, err := u.Providers.Users.GetPersonalData(ctx, &users.GetPersonalDataReq{
		Iin: loanApp.UserIin,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to Users.GetPersonalData: %w", err)
	}
	logger.Debug().Interface("usersPersonalData", usersPersonalData).Msg("usersPersonalData")

	// Получаем данные ИП из сервиса users
	ipInfo, err := u.Providers.Users.GetUserSmeIPInfo(ctx, &users.GetUserSmeIPInfoReq{
		Iin: loanApp.UserIin,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to Users.GetUserSmeIPInfo: %w", err)
	}
	logger.Debug().Interface("usersUserSmeIPInfo", ipInfo).Msg("ipInfo")

	// Получаем лицензию ИП из сервиса pkb-bridge
	license, err := u.Providers.Pkbbridge.GetPermitDocumentsByIin(ctx, &pkb_bridge.GetPermitDocumentsByIinReq{
		Iin: loanApp.UserIin,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to Pkbbridge.GetPermitDocumentsByIin: %w", err)
	}
	logger.Debug().Interface("license", license).Msg("license")

	// Освежить персональные данные свежими данными из/после Liveness Verify
	personalData, err := fillPersonalDataByLivenessPersonalData(usersPersonalData, livenessPersonalData)
	if err != nil {
		return nil, fmt.Errorf("failed to fillPersonalDataByLivenessPersonalData: %w", err)
	}
	logger.Debug().Interface("personalData", personalData).Msg("personalData")

	// Получаем из данных юзера вспомогательную информацию от АБИС
	colvirInfo := personalData.GetColvirInfo()
	if colvirInfo == nil {
		return nil, fmt.Errorf("no colvir info for user: %s", loanApp.UserIin)
	}

	// Получаем код клиента АБИС
	clientCode := colvirInfo.GetColvirClientCode()
	if clientCode == "" {
		return nil, fmt.Errorf("no colvir client code for user: %s", loanApp.UserIin)
	}

	// Синхронные проверки после обновления персональных данных
	results, err := u.postIdentifyBtsDataInternalChecks(ctx, loanApp, personalData)
	if err != nil {
		return nil, fmt.Errorf("failed to postIdentifyBtsDataInternalChecks: %w", err)
	}
	failedCheckIdx := slices.IndexFunc(results, func(result *entity.LoanCheckResult) bool {
		return !result.Success
	})
	if failedCheckIdx != -1 {
		failedCheck := results[failedCheckIdx]
		if failedCheck == nil {
			return nil, fmt.Errorf("not expected to get nil as %d element of sync check results %+v", failedCheckIdx, results)
		}
		reason := consts.MapFailedInternalCheckToReason(ctx, failedCheck.ActionType, failedCheck.Message)
		return &entity.PostIdentifyBtsDataSmeResult{
			ApplicationStatus: consts.LoanApplicationStatusRejected,
			Reason:            reason,
		}, nil
	}

	bankAccount, err := u.getRichestActiveNotArrestedKZTBankAccount(ctx)
	if err != nil {
		if errors.Is(err, loansErrs.LoansErrs().BankAccountNotFoundError()) {
			logger.Debug().Msg("bankAccount not found")

			err = u.Providers.Storage.SaveHistoryAction(ctx, &entity.HistoryAction{
				ActionType:    consts.ApprovedLoanAppTechError,
				ApplicationID: req.ApplicationID,
			})
			if err != nil {
				logger.Error().Msgf("could not save history action %s", consts.ApprovedLoanAppTechError)
			}

			reason := consts.MapFailedInternalCheckToReason(ctx, consts.ApprovedLoanAppTechError, "")
			return &entity.PostIdentifyBtsDataSmeResult{
				ApplicationStatus: consts.LoanApplicationStatusRejected,
				Reason:            reason,
			}, nil
		}
		return nil, fmt.Errorf("failed to getRichestActiveNotArrestedKZTBankAccount: %w", err)
	}
	logger.Debug().Interface("bankAccount", bankAccount).Msg("bankAccount")

	err = u.Providers.Storage.UpdateDisbursementAccountID(ctx, loanApp.ID, bankAccount.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to Storage.UpdateDisbursementAccountID: %w", err)
	}
	logger.Debug().Msgf("updated disbursement account id: %s for loan app: %s", bankAccount.ID, loanApp.ID)

	productType, err := getProductType(loanApp.BusinessType)
	if err != nil {
		return nil, fmt.Errorf("failed to getProductType: %w", err)
	}
	logger.Debug().Interface("productType", productType).Msg("productType")

	now, err := utils.GetCurrentKzTime()
	if err != nil {
		return nil, fmt.Errorf("failed to utils.GetCurrentKzTime: %w", err)
	}

	docTypeCreditContractWithRepaymentSchedule := consts.DocTypeCreditContractWithRepaymentScheduleSMEIP

	regAddress := personalData.GetRegAddress()
	if regAddress == nil {
		return nil, fmt.Errorf("reg address is nil for user iin %s", personalData.Iin)
	}

	if loanApp.FinallyChosenConditions == nil {
		return nil, fmt.Errorf("expected finally chosen condition to exist for loan application: %s", loanApp.ID)
	}
	ctxRULocale := context.WithValue(ctx, acceptlanguage.AcceptLanguageKey, locale.Ru.String())

	surveyRU, err := u.GetSurvey(ctxRULocale, &entity.GetSurveyReq{})
	if err != nil {
		return nil, fmt.Errorf("failed to GetSurvey: %w", err)
	}
	logger.Debug().Interface("surveyRU", surveyRU).Msg("surveyRU")
	if surveyRU.SurveyID == nil {
		return nil, fmt.Errorf("no survey found for user: %s", userInfo.UserID)
	}
	surveyEmail := ""
	if surveyRU.Email == nil {
		return nil, fmt.Errorf("expected email to exist in survey. Survey: %+v", surveyRU)
	} else {
		surveyEmail = *surveyRU.Email
	}
	factAddressRU := survey.GetAddress(surveyRU)
	if !factAddressRU.IsFull {
		return nil, fmt.Errorf("expected fact address to be full. Fact address: %+v", factAddressRU)
	}
	if factAddressRU.Street == nil || factAddressRU.Building == nil {
		return nil, fmt.Errorf("expected fact address to have street and building. Fact address: %+v", factAddressRU)
	}
	var settlementArea, settlement, locality *string

	if factAddressRU.SettlementArea != nil {
		settlementArea = &factAddressRU.SettlementArea.Name
	}
	if factAddressRU.Settlement != nil {
		settlement = &factAddressRU.Settlement.Name
	}
	if factAddressRU.Locality != nil {
		locality = &factAddressRU.Locality.Name
	}

	ctxKZLocale := context.WithValue(ctx, acceptlanguage.AcceptLanguageKey, locale.Kk.String())
	surveyKZ, err := u.GetSurvey(ctxKZLocale, &entity.GetSurveyReq{})
	if err != nil {
		return nil, fmt.Errorf("failed to GetSurvey: %w", err)
	}
	logger.Debug().Interface("surveyKZ", surveyKZ).Msg("surveyKZ")
	if surveyKZ.SurveyID == nil {
		return nil, fmt.Errorf("no survey found for user: %s", userInfo.UserID)
	}

	factAddressKZ := survey.GetAddress(surveyKZ)
	if !factAddressKZ.IsFull {
		return nil, fmt.Errorf("expected fact address to be full. Fact address: %+v", factAddressKZ)
	}
	if factAddressKZ.Street == nil || factAddressKZ.Building == nil {
		return nil, fmt.Errorf("expected fact address to have street and building. Fact address: %+v", factAddressKZ)
	}
	var settlementAreaKZ, settlementKZ, localityKZ *string

	if factAddressKZ.SettlementArea != nil {
		settlementAreaKZ = &factAddressKZ.SettlementArea.Name
	}
	if factAddressKZ.Settlement != nil {
		settlementKZ = &factAddressKZ.Settlement.Name
	}
	if factAddressKZ.Locality != nil {
		localityKZ = &factAddressKZ.Locality.Name
	}

	conditions := loanApp.FinallyChosenConditions
	if conditions == nil {
		return nil, fmt.Errorf("expected finally chosen condition to exist for loan application: %s, it should be set after approval from scoring", loanApp.ID)
	}
	logger.Debug().Interface("conditions", conditions).Msg("conditions")

	term, err := loanApp.GetTerm()
	if err != nil {
		return nil, fmt.Errorf("failed to GetTerm: %w", err)
	}

	preScheduleDateFormat := time.DateOnly
	beginDate := now.Format(preScheduleDateFormat)
	endDate := now.AddDate(0, int(term), 0).Format(preScheduleDateFormat)

	loanColovirFields := entity.LoanApplication{
		ColvirProductCode:      &productType,
		ColvirContractFromDate: &beginDate,
		ColvirContractToDate:   &endDate,
	}
	// Добавляем данные кредитного договора для Colvir
	err = u.Providers.Storage.SetColvirFields(ctx, req.ApplicationID, &loanColovirFields)
	if err != nil {
		return nil, fmt.Errorf("failed to SetColvirFields: %w", err)
	}

	interest, err := loanApp.GetInterest()
	if err != nil {
		return nil, fmt.Errorf("failed to GetInterest: %w", err)
	}

	preScheduleReq := &colvir_bridge.LoanCalcLoadPreScheduleRequest{
		ProductCode: productType,
		BeginDate:   beginDate,
		EndDate:     endDate,
		PercentRate: strconv.Itoa(int(interest)),
		//nolint:gosec
		PayDay:      int32(now.Day()),
		ContractSum: strconv.FormatUint(uint64(conditions.Amount), 10),
	}
	logger.Debug().Interface("preScheduleReq", preScheduleReq).Msg("preScheduleReq")

	preSchedule, err := u.Providers.Colvirbridge.LoanCalcLoadPreSchedule(ctx, preScheduleReq)
	if err != nil {
		return nil, fmt.Errorf("failed to Colvirbridge.LoanCalcLoadPreSchedule: %w", err)
	}
	logger.Debug().Interface("preSchedule", preSchedule).Msg("preSchedule")

	totalPaymentsMarkupSum := decimal.Zero
	totalPaymentsBaseCost := decimal.Zero
	for i, scheduleItem := range preSchedule.GetSchedule() {
		crInterDetailIdx := slices.IndexFunc(scheduleItem.GetDetails(), func(detail *colvir_bridge.LoanCalcLoadPreScheduleDetail) bool {
			return detail.GetCode() == consts.LoanCalcLoadPreScheduleScheduleEntryCodeCrInter
		})
		if crInterDetailIdx == -1 {
			return nil, fmt.Errorf("expected %s detail to exist for schedule item: %d. Pre schedule response: %+v", consts.LoanCalcLoadPreScheduleScheduleEntryCodeCrInter, i, preSchedule)
		}
		crInterDetail := scheduleItem.GetDetails()[crInterDetailIdx]
		decimalAmount, err := decimal.NewFromString(crInterDetail.GetAmount())
		if err != nil {
			return nil, fmt.Errorf("failed to decimal.NewFromString: %w", err)
		}
		totalPaymentsMarkupSum = totalPaymentsMarkupSum.Add(decimalAmount)

		crCdDetailIdx := slices.IndexFunc(scheduleItem.GetDetails(), func(detail *colvir_bridge.LoanCalcLoadPreScheduleDetail) bool {
			return detail.GetCode() == consts.LoanCalcLoadPreScheduleScheduleEntryCodeCrPd
		})
		if crCdDetailIdx == -1 {
			return nil, fmt.Errorf("expected %s detail to exist for schedule item: %d. Pre schedule response: %+v", consts.LoanCalcLoadPreScheduleScheduleEntryCodeCrPd, i, preSchedule)
		}
		crCdDetail := scheduleItem.GetDetails()[crCdDetailIdx]
		decimalAmount, err = decimal.NewFromString(crCdDetail.GetAmount())
		if err != nil {
			return nil, fmt.Errorf("failed to decimal.NewFromString: %w", err)
		}
		totalPaymentsBaseCost = totalPaymentsBaseCost.Add(decimalAmount)
	}
	totalPaymentsAmount := totalPaymentsBaseCost.Add(totalPaymentsMarkupSum)

	contractNumber, err := u.GenerateContractNumber(ctx, consts.GenerateCreditContractWithRepaymentScheduleProductCode, loanApp.BusinessType, consts.GenerateCreditContractWithRepaymentScheduleDepartmentCode, now.Year())
	if err != nil {
		return nil, fmt.Errorf("failed to GenerateContractNumber: %w", err)
	}
	logger.Debug().Interface("contractNumber", contractNumber).Msg("contractNumber")

	zamanBankInfo, err := u.getZamanBankInfo(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to getZamanBankInfo: %w", err)
	}
	logger.Debug().Interface("zamanBankInfo", zamanBankInfo).Msg("zamanBankInfo")

	productInfo, err := u.getProductInfo(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to getProductInfo: %w", err)
	}
	logger.Debug().Interface("productInfo", productInfo).Msg("productInfo")

	productCurrencyRateDecimal, err := decimal.NewFromString(productInfo.CurrencyRate)
	if err != nil {
		return nil, fmt.Errorf("failed to decimal.NewFromString: %w", err)
	}
	logger.Debug().Interface("productCurrencyRateDecimal", productCurrencyRateDecimal).Msg("productCurrencyRateDecimal")

	organizationInfo, err := u.Providers.Pkbbridge.SendJurSearchByIin(ctx, &pkb_bridge.SendJurSearchByIinReq{
		Iin: personalData.Iin,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to Pkbbridge.SendJurSearchByIin: %w", err)
	}
	logger.Debug().Interface("organizationInfo", organizationInfo).Msg("organizationInfo")

	documentPlaceholdersPayments := make([]*documents.GenerateCreditContractWithRepaymentScheduleReqPayment, 0, len(preSchedule.GetSchedule()))
	for _, payment := range preSchedule.GetSchedule() {
		if payment == nil {
			return nil, fmt.Errorf("payment is nil")
		}
		markupIdx := slices.IndexFunc(payment.Details, func(detail *colvir_bridge.LoanCalcLoadPreScheduleDetail) bool {
			return detail.GetCode() == consts.LoanCalcLoadPreScheduleScheduleEntryCodeCrInter
		})
		if markupIdx == -1 {
			return nil, fmt.Errorf("failed to find markup detail for payment: %+v", payment)
		}
		markupDetail := payment.Details[markupIdx]
		baseCostIdx := slices.IndexFunc(payment.Details, func(detail *colvir_bridge.LoanCalcLoadPreScheduleDetail) bool {
			return detail.GetCode() == consts.LoanCalcLoadPreScheduleScheduleEntryCodeCrPd
		})
		if baseCostIdx == -1 {
			return nil, fmt.Errorf("failed to find base cost detail for payment: %+v", payment)
		}
		date, err := utils.GetDayOnlyStringFromColvirDateString(payment.GetDate())
		if err != nil {
			return nil, fmt.Errorf("failed to get correct date for payment: %+v", payment)
		}
		baseCostDetail := payment.Details[baseCostIdx]
		documentPlaceholdersPayments = append(documentPlaceholdersPayments, &documents.GenerateCreditContractWithRepaymentScheduleReqPayment{
			Date:          date,
			Amount:        payment.GetAmount(),
			Markup:        markupDetail.GetAmount(),
			BaseCost:      baseCostDetail.GetAmount(),
			RemainingDebt: payment.GetDebtAfterPay(),
		})
	}
	logger.Debug().Interface("documentPlaceholdersPayments", documentPlaceholdersPayments).Msg("documentPlaceholdersPayments")

	generateCreditContractWithRepaymentScheduleReq := &documents.GenerateCreditContractWithRepaymentScheduleSMEIPReq{
		Contract: &documents.GenerateCreditContractWithRepaymentScheduleReqContract{
			Number:       contractNumber,
			ContractDate: utils.GetDateAsDotFormattedString(now),
			ContractTime: utils.GetTimeAsColonFormattedString(now),
		},
		Bank: &documents.GenerateCreditContractWithRepaymentScheduleReqBank{
			City: &documents.GenerateCreditContractWithRepaymentScheduleReqBankCity{
				Name: entity.MakeZamanBankInfoLocalizedValueToGenerateCreditContractWithRepaymentScheduleDocLocalizedValue(&zamanBankInfo.City),
			},
			Name:    entity.MakeZamanBankInfoLocalizedValueToGenerateCreditContractWithRepaymentScheduleDocLocalizedValue(&zamanBankInfo.Name),
			Bin:     zamanBankInfo.Bin,
			Address: entity.MakeZamanBankInfoLocalizedValueToGenerateCreditContractWithRepaymentScheduleDocLocalizedValue(&zamanBankInfo.Address),
			Email:   zamanBankInfo.Email,
			Website: zamanBankInfo.Links.Main,
			Iik:     zamanBankInfo.Iik,
			Phone:   zamanBankInfo.PhoneNumber,
			Bic:     zamanBankInfo.Bic,
			Representative: &documents.GenerateCreditContractWithRepaymentScheduleReqBankRepresentative{
				FullName: zamanBankInfo.Representative.FullName,

				JobPosition:          entity.MakeZamanBankInfoLocalizedValueToGenerateCreditContractWithRepaymentScheduleDocLocalizedValue(&zamanBankInfo.Representative.JobPosition),
				DocumentNameGenitive: entity.MakeZamanBankInfoLocalizedValueToGenerateCreditContractWithRepaymentScheduleDocLocalizedValue(&zamanBankInfo.Representative.DocumentNameGenitive),
			},
		},
		User: &documents.GenerateCreditContractWithRepaymentScheduleReqUser{
			FullName:         utils.JoinStringsWithSpaces(personalData.GetSurname(), personalData.GetName(), personalData.GetPatronymic()),
			Iin:              personalData.GetIin(),
			OrganizationName: utils.FormatIPName(organizationInfo.Name),
			LegalAddress: &documents.GenerateCreditContractWithRepaymentScheduleReqLocalizedValue{
				RU: utils.BuildAddress(
					conversion.Ptr(personaldata.GetRegAddressCountry(personalData).GetNameRu()),
					conversion.Ptr(personaldata.GetRegAddressDistrict(personalData).GetNameRu()),
					conversion.Ptr(personaldata.GetRegAddressRegion(personalData).GetNameRu()),
					conversion.Ptr(personaldata.GetRegAddressCity(personalData)),
					conversion.Ptr(personaldata.GetRegAddressStreet(personalData)),
					conversion.Ptr(entity.GetBuildingStringByLocale(conversion.Ptr(personaldata.GetRegAddressBuilding(personalData)), locale.Ru)),
					conversion.Ptr(entity.GetApartmentStringByLocale(conversion.Ptr(personaldata.GetRegAddressFlat(personalData)), locale.Ru)),
				),
				KZ: utils.BuildAddress(
					conversion.Ptr(personaldata.GetRegAddressCountry(personalData).GetNameKz()),
					conversion.Ptr(personaldata.GetRegAddressDistrict(personalData).GetNameKz()),
					conversion.Ptr(personaldata.GetRegAddressRegion(personalData).GetNameKz()),
					conversion.Ptr(personaldata.GetRegAddressCity(personalData)),
					conversion.Ptr(personaldata.GetRegAddressStreet(personalData)),
					conversion.Ptr(entity.GetBuildingStringByLocale(conversion.Ptr(personaldata.GetRegAddressBuilding(personalData)), locale.Kk)),
					conversion.Ptr(entity.GetApartmentStringByLocale(conversion.Ptr(personaldata.GetRegAddressFlat(personalData)), locale.Kk)),
				),
			},
			ActualAddress: &documents.GenerateCreditContractWithRepaymentScheduleReqLocalizedValue{
				RU: utils.BuildAddress(
					conversion.Ptr(personaldata.GetRegAddressCountry(personalData).GetNameRu()),
					conversion.Ptr(survey.GetRegin(factAddressRU).Name),
					conversion.Ptr(survey.GetDistrict(factAddressRU).Name),
					settlementArea,
					settlement,
					locality,
					factAddressRU.Street,
					conversion.Ptr(entity.GetBuildingStringByLocale(factAddressRU.Building, locale.Ru)),
					conversion.Ptr(entity.GetApartmentStringByLocale(factAddressRU.Flat, locale.Ru)),
				),
				KZ: utils.BuildAddress(
					conversion.Ptr(regAddress.GetCountry().GetNameKz()),
					conversion.Ptr(survey.GetRegin(factAddressKZ).Name),
					conversion.Ptr(survey.GetDistrict(factAddressKZ).Name),
					settlementAreaKZ,
					settlementKZ,
					localityKZ,
					factAddressKZ.Street,
					conversion.Ptr(entity.GetBuildingStringByLocale(factAddressKZ.Building, locale.Kk)),
					conversion.Ptr(entity.GetApartmentStringByLocale(factAddressKZ.Flat, locale.Kk)),
				),
			},
			MobilePhone:   userInfo.Phone,
			Email:         surveyEmail,
			LandlinePhone: " ",
		},

		PaymentsSummary: &documents.GenerateCreditContractWithRepaymentScheduleReqPaymentsSummary{
			Amount:        totalPaymentsAmount.String(),
			Markup:        totalPaymentsMarkupSum.String(),
			BaseCost:      totalPaymentsBaseCost.String(),
			RemainingDebt: "0",
		},
		Payments: documentPlaceholdersPayments,
		Currency: &documents.GenerateCreditContractWithRepaymentScheduleReqLocalizedValue{
			RU: "тенге",
			KZ: "теңге",
		},

		PaymentScheduleDate: now.Format(time.DateOnly),
		Product: &documents.GenerateCreditContractWithRepaymentScheduleReqProduct{
			Cost: totalPaymentsBaseCost.String(),
		},
		MarkupAmountSum: totalPaymentsMarkupSum.String(),
		FinancingTerm:   strconv.Itoa(int(term)),
	}
	logger.Debug().Interface("generateCreditContractWithRepaymentScheduleReq", generateCreditContractWithRepaymentScheduleReq).Msg("generateCreditContractWithRepaymentScheduleReq")

	docsCreditContractDoc, err := u.Providers.Documents.GenerateCreditContractWithRepaymentScheduleSMEIP(ctx, generateCreditContractWithRepaymentScheduleReq)
	if err != nil {
		return nil, fmt.Errorf("failed to Documents.GenerateCreditContractWithRepaymentScheduleSMEIP: %w", err)
	}
	logger.Debug().Interface("docsCreditContractDoc", docsCreditContractDoc).Msg("docsCreditContractDoc")

	loansCreditContractDoc, err := u.Providers.Storage.GetDocumentByTypeAndAppID(ctx, docTypeCreditContractWithRepaymentSchedule, req.ApplicationID)
	if err != nil && !errors.Is(err, loansErrs.LoansErrs().DocumentNotFoundError()) {
		return nil, fmt.Errorf("failed to Storage.GetDocumentByTypeAndAppID: %w", err)
	}
	logger.Debug().Interface("loansCreditContractDoc", loansCreditContractDoc).Msg("loansCreditContractDoc")

	if loansCreditContractDoc == nil {
		entityCreditContractWithRepaymentScheduleDocToSave := &entity.Document{
			Type:          docTypeCreditContractWithRepaymentSchedule,
			DocID:         docsCreditContractDoc.GetId(),
			ApplicationID: req.ApplicationID,
			Number:        &contractNumber,
		}
		logger.Debug().Interface("entityCreditContractWithRepaymentScheduleDocToSave", entityCreditContractWithRepaymentScheduleDocToSave).Msg("entityCreditContractWithRepaymentScheduleDocToSave")
		savedCreditContractDoc, err := u.Providers.Storage.SaveDocument(ctx, entityCreditContractWithRepaymentScheduleDocToSave)
		logger.Debug().Interface("savedCreditContractDoc", savedCreditContractDoc).Msg("savedCreditContractDoc")
		if err != nil {
			return nil, fmt.Errorf("failed to Storage.SaveDocument: %w", err)
		}
	} else {
		updatedDoc := *loansCreditContractDoc
		updatedDoc.Number = &contractNumber
		updatedDoc.DocID = docsCreditContractDoc.GetId()
		err = u.Providers.Storage.UpdateDocumentByTypeAndAppID(ctx, docTypeCreditContractWithRepaymentSchedule, req.ApplicationID, &updatedDoc)
		if err != nil {
			return nil, fmt.Errorf("failed to Storage.UpdateDocumentByTypeAndAppID: %w", err)
		}
		logger.Debug().Interface("updatedDoc", updatedDoc).Msg("updatedDoc")
	}

	productPriceMyrDecimal, err := decimal.NewFromString(strconv.FormatUint(productInfo.ProductPrice, 10))
	if err != nil {
		return nil, fmt.Errorf("failed to decimal.NewFromString: %w", err)
	}
	productCostInMyrDecimal := totalPaymentsBaseCost.Mul(productCurrencyRateDecimal)
	productQuantity := productCostInMyrDecimal.Div(productPriceMyrDecimal)
	productQuantityFloat, _ := productQuantity.Float64()
	// Округляем до 8 знаков после запятой
	productQuantityFloat = utils.CeilToDecimalPlaces(productQuantityFloat, 8)

	exchangeInfo, err := u.getExchangeInfo(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to getExchangeInfo: %w", err)
	}

	// Формируем документ 'Заявка на продажу товара'
	err = u.createAndAttachAppSaleOfGoodsDocumentSme(ctx, loanApp, now, personalData, zamanBankInfo, bankAccount,
		exchangeInfo, contractNumber, productInfo, totalPaymentsAmount, totalPaymentsMarkupSum, totalPaymentsBaseCost,
		productQuantityFloat, organizationInfo.Name)
	if err != nil {
		return nil, fmt.Errorf("failed to createAndAttachAppSaleOfGoodsDocumentSme: %w", err)
	}
	logger.Debug().Msg("created and attached app sale of goods document")

	var identityDocument *users.Document
	var passportDocument *users.Document

	// Проверяем необходимые документы
	for _, document := range personalData.Documents {
		// Недействительный документ пропускаем
		if document.GetStatus().GetCode() != consts.UsersDocumentStatusValidCode {
			continue
		}

		if document.GetType().GetCode() == consts.UsersCitizenshipDocTypeCode {
			identityDocument = document
			break // Документ гражданства найден - используем его (высший приоритет)
		}

		if document.GetType().GetCode() == consts.UsersPassportDocTypeCode {
			passportDocument = document
		}
	}

	// Если документ гражданства не найден, используем паспорт
	if identityDocument == nil {
		identityDocument = passportDocument
	}
	logger.Debug().Interface("identityDocument", identityDocument).Msg("identityDocument")

	if identityDocument == nil {
		return nil, errors.New("can't find user's identityDocument")
	}

	err = u.createAndAttachAppBankServiceApplicationSme(ctx, loanApp, surveyRU, personalData, organizationInfo, ipInfo, license)
	if err != nil {
		return nil, fmt.Errorf("failed to createAndAttachAppBankServiceApplicationSme: %w", err)
	}
	logger.Debug().Msg("created and attached bank service application document")

	approvedLoanAppStatus, err := u.GetApprovedLoanAppStatus(ctx, &entity.GetApprovedLoanAppStatusReq{
		ApplicationID: loanApp.ID,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to GetApprovedLoanAppStatus: %w", err)
	}
	logger.Debug().Interface("approvedLoanAppStatus", approvedLoanAppStatus).Msg("approvedLoanAppStatus")

	result := &entity.PostIdentifyBtsDataSmeResult{
		ApplicationStatus: approvedLoanAppStatus.ApplicationStatus,
		Reason:            approvedLoanAppStatus.Reason,
	}

	err = u.sendRefreshPersonalDataNotify(ctx, personalData)
	if err != nil {
		logger.Error().Msgf("Could not create task to refresh personal data: %+v", err)
	}

	err = u.sendLoanSignDocumentsReadyNotify(ctx, userInfo.UserID.String(), userInfo.Phone)
	if err != nil {
		logger.Error().Msgf("Can't add SMS notification to Kafka: %+v", err)
	}

	err = u.updateIPColvirInfo(ctx, organizationInfo, surveyRU, loanApp.UserIin, consts.ColvirClientFullUpdateEventType)
	if err != nil {
		logger.Error().Msgf("Can't add event for update colvirSME: %+v", err)
	}

	logger.Debug().Interface("result", result).Msg("result")
	return result, nil
}

// createAndAttachAppSaleOfGoodsDocumentSme - метод для вызова генерации и сохранения для кредитной заявки документа 'Заявка на продажу товара'
func (u *useCasesImpl) createAndAttachAppSaleOfGoodsDocumentSme(ctx context.Context, loanApp *entity.LoanApplication,
	date time.Time, personalData *users.GetPersonalDataResp, zamanBankInfo *entity.DictionaryZamanBankInfo,
	activeBankAccount *cards_accounts.Account, exchangeInfo *entity.DictionaryExchangeInfo,
	contractNumber string, productInfo *entity.DictionaryProductExchangeInfo,
	totalPaymentsAmount decimal.Decimal, paymentsMarkupSum decimal.Decimal, paymentsSumExcludingMarkup decimal.Decimal, productQuantityFloat float64,
	organizationName string,
) error {
	logger := logs.FromContext(ctx)

	// Получаем режим работы обработчика
	disbursementControl, err := u.Providers.Storage.GetDisbursementControlRecord(ctx)
	if err != nil {
		return fmt.Errorf("could not get disbursement control record: %w", err)
	}

	// В зависимости от режима работы обработчика выбираем название продукта
	productName := productInfo.ProductName
	if disbursementControl.Mode == consts.LoanDisbursementManualMode {
		productName = productInfo.ManualProductName
	}

	term, err := loanApp.GetTerm()
	if err != nil {
		return err
	}

	generateAppSaleOfGoodsDocumentSMEIPReq := &documents.GenerateAppSaleOfGoodsDocumentSMEIPReq{
		PersonIIN:        personalData.Iin,
		PersonFullName:   utils.JoinStringsWithSpaces(personalData.GetSurname(), personalData.GetName(), personalData.GetPatronymic()),
		OrganizationName: utils.FormatIPName(organizationName),
		BankData: &documents.SaleOfGoodsBankData{
			ReprFullName: zamanBankInfo.Representative.FullName,
			ReprPosition: &documents.LocalizationData{
				Kk: zamanBankInfo.Representative.JobPosition.KZ,
				Ru: zamanBankInfo.Representative.JobPosition.RU,
			},
			BankBIN:            zamanBankInfo.Bin,
			BankAccountPayment: activeBankAccount.Iban,
			UserBankAccount:    activeBankAccount.Iban,
			BuyerName:          exchangeInfo.Buyer.Name,
			BuyerID:            exchangeInfo.Buyer.ID,
			BuyerAddress:       exchangeInfo.Buyer.Address,
			BankName: &documents.LocalizationData{
				Kk: zamanBankInfo.Name.KZ,
				Ru: zamanBankInfo.Name.RU,
			},
		},
		ContractData: &documents.SaleOfGoodsContractData{
			ContractNumber: contractNumber,
			ContractDate:   utils.GetDateAsDotFormattedString(date),
		},
		ProductData: &documents.SaleOfGoodsProductData{
			ProductName: productName,
			// TODO: change it for correct credit end date
			PaymentDate:         utils.GetDateAsDotFormattedString(date.AddDate(0, int(term), 0)),
			ProductTotalCost:    totalPaymentsAmount.StringFixed(2),
			ProductMarkupAmount: paymentsMarkupSum.StringFixed(2),
			ProductExcludeCost:  paymentsSumExcludingMarkup.StringFixed(2),
			ProductQty:          productQuantityFloat,
		},
	}
	logger.Debug().Interface("generateAppSaleOfGoodsDocumentSMEIPReq", generateAppSaleOfGoodsDocumentSMEIPReq).Msg("generateAppSaleOfGoodsDocumentSMEIPReq")
	doc, err := u.Providers.Documents.GenerateAppSaleOfGoodsDocumentSMEIP(ctx, generateAppSaleOfGoodsDocumentSMEIPReq)
	if err != nil {
		return fmt.Errorf("failed to Documents.GenerateAppSaleOfGoodsDocumentSMEIP: %w", err)
	}

	// Пробуем получить документ 'Заявка на продажу товара' из БД
	appSaleDoc, err := u.Providers.Storage.GetDocumentByTypeAndAppID(ctx, consts.DocTypeApplicationSaleOfGoodsSMEIP, loanApp.ID)
	if err != nil && !errors.Is(err, loansErrs.LoansErrs().DocumentNotFoundError()) {
		return err
	}

	if appSaleDoc == nil {
		docToSave := &entity.Document{
			Type:          consts.DocTypeApplicationSaleOfGoodsSMEIP,
			DocID:         doc.GetId(),
			ApplicationID: loanApp.ID,
		}
		_, err = u.Providers.Storage.SaveDocument(ctx, docToSave)
		if err != nil {
			return err
		}
	} else {
		updatedDoc := *appSaleDoc
		updatedDoc.DocID = doc.GetId()
		err = u.Providers.Storage.UpdateDocumentByTypeAndAppID(ctx, consts.DocTypeApplicationSaleOfGoodsSMEIP, loanApp.ID, &updatedDoc)
		if err != nil {
			return err
		}
	}

	return nil
}

//nolint:funlen
func (u *useCasesImpl) createAndAttachAppBankServiceApplicationSme(ctx context.Context, loanApp *entity.LoanApplication, surveyRU *entity.GetSurveyResult, personalDataResp *users.GetPersonalDataResp, organizationInfo *pkb_bridge.SendJurSearchByIinResp, ipInfo *users.GetUserSmeIPInfoResp, license *pkb_bridge.GetPermitDocumentsByIinResp) error {
	logger := logs.FromContext(ctx)

	if surveyRU == nil {
		return errors.New("survey ru is nil")
	}

	email := ""
	if surveyRU.Email == nil {
		return fmt.Errorf("expected email to exist in survey. Survey: %+v", surveyRU)
	} else {
		email = *surveyRU.Email
	}
	logger.Debug().Interface("email", email).Msg("email")

	birthPlace := personaldata.GetBirthPlace(personalDataResp)
	logger.Debug().Interface("birthPlace", birthPlace).Msg("birthPlace")

	tBirthDate, err := utils.ConvertDateStringToTime(personalDataResp.BirthDate)
	if err != nil {
		return err
	}
	logger.Debug().Interface("tBirthDate", tBirthDate).Msg("tBirthDate")

	var identityDocument *users.Document

	for _, document := range personalDataResp.Documents {
		if document.GetType().GetCode() == consts.UsersCitizenshipDocTypeCode &&
			document.GetStatus().GetCode() == consts.UsersDocumentStatusValidCode {
			identityDocument = document
		}
	}
	logger.Debug().Interface("identityDocument", identityDocument).Msg("identityDocument")

	if identityDocument == nil {
		return errors.New("can't find user's identityDocument")
	}

	regAddress := personaldata.GetRegAddress(personalDataResp)
	logger.Debug().Interface("regAddress", regAddress).Msg("regAddress")

	factAddressRU := survey.GetAddress(surveyRU)
	if !factAddressRU.IsFull {
		return fmt.Errorf("expected fact address to be full. Fact address: %+v", factAddressRU)
	}
	logger.Debug().Interface("factAddressRU", factAddressRU).Msg("factAddressRU")

	smeIPInfo := ipinfo.GetSmeIPInfo(ipInfo)
	logger.Debug().Interface("smeIpInfo", smeIPInfo).Msg("smeIpInfo")

	taxPayerLicense := getpermitdocuments.GetTaxPayerLicenses(license)
	logger.Debug().Interface("taxPayerLicense", taxPayerLicense).Msg("taxPayerLicense")

	generateBankServiceApplicationSMEIPReq := &documents.GenerateBankServiceApplicationSMEIPReq{
		Ipname:         utils.FormatIPName(organizationInfo.Name),
		PersonFullName: utils.JoinStringsWithSpaces(personalDataResp.GetSurname(), personalDataResp.GetName(), personalDataResp.GetPatronymic()),
		PersonIIN:      personalDataResp.Iin,
		Phone:          personalDataResp.Phone,
		Email:          email,
		OkedName:       organizationInfo.OkedName,
		OkedCode:       organizationInfo.OkedCode,
		ResidencyInfo: &documents.BankServiceApplicationSMEIPResidencyInfo{
			ResidenceCountry:        personaldata.GetCountryRu(personalDataResp),
			TaxResidenceCountry:     personaldata.GetCountryRu(personalDataResp),
			ResidenceCountryCode:    personaldata.CodeToStringCode(personaldata.GetCountry(personaldata.GetBirthPlace(personalDataResp)).Code),
			TaxResidenceCountryCode: personaldata.CodeToStringCode(personaldata.GetCountry(personaldata.GetBirthPlace(personalDataResp)).Code),
		},
		ResidentialAddress: &documents.BankServiceApplicationSMEIPResidentialAddress{
			ResidentialCountry:      regAddress.Country.NameRu,
			ResidentialOtherCountry: "",
			ResidentialDistrict:     survey.GetDistrict(factAddressRU).Name,
			ResidentialCity:         survey.Locality(factAddressRU).Name,
			ResidentialRegion:       survey.GetRegin(factAddressRU).Name,
			ResidentialStreet:       conversion.PointerStrToEmptyStrOrValue(factAddressRU.Street),
			ResidentialBuilding:     conversion.PointerStrToEmptyStrOrValue(factAddressRU.Building),
			ResidentialFlat:         conversion.PointerStrToEmptyStrOrValue(factAddressRU.Flat),
		},
		BirthLocation: &documents.BankServiceApplicationSMEIPBirthLocation{
			BirthCountry: birthPlace.Country.NameRu,
			BirthPlace:   birthPlace.City,
			Citizenship:  personaldata.GetCitizenship(personalDataResp).NameRu,
		},
		BirthDate: timestamppb.New(tBirthDate),
		IdentityDocument: &documents.BankServiceApplicationSMEIPIdentityDocument{
			DocNumber: identityDocument.Number,
			DocCode:   identityDocument.GetType().GetCode(),
			DocSeries: "",
			DocIssueDate: timestamppb.New(func(sT string) time.Time {
				toTime, err := utils.ConvertDateStringToTime(sT)
				if err != nil {
					logger.Error().Msgf("field DocIssueDate value:'%s' in incorrect format date", sT)
					return time.Time{}
				}

				return toTime
			}(identityDocument.GetBeginDate())),
			IssueOrganization: identityDocument.IssueOrganization.NameRu,
			DocExpDate: timestamppb.New(func(sT string) time.Time {
				toTime, err := utils.ConvertDateStringToTime(sT)
				if err != nil {
					logger.Error().Msgf("field DocExpDate value:'%s' in incorrect format date", sT)
					return time.Time{}
				}

				return toTime
			}(identityDocument.GetEndDate())),
		},
		RegistrationAddress: &documents.BankServiceApplicationSMEIPRegistrationAddress{
			OtherCountry: "",
			District:     regAddress.District.NameRu,
			City:         regAddress.City,
			Region:       regAddress.Region.NameRu,
			Street:       regAddress.Street,
			Building:     regAddress.Building,
			Flat:         regAddress.Flat,
		},
		IsPDL:            false,
		IsBeneficiary:    true,
		BeneficiaryOwner: "",
		UpToLimit:        true,
		SourcesOfIncome: &documents.BankServiceApplicationSMEIPSourcesOfIncome{
			Wages:              true,
			SocialPayments:     false,
			Inheritance:        false,
			BankFinancing:      true,
			ReturnOnInvestment: false,
			CompanyShare:       false,
			Sale:               false,
			BusinessActivities: true,
			OtherIncome:        nil,
		},
		TemporaryResidencyDocument: nil,
		Director: &documents.Director{
			DirectorFullName: utils.JoinStringsWithSpaces(personalDataResp.GetSurname(), personalDataResp.GetName(), personalDataResp.GetPatronymic()),
			Post:             "",
			BirthDay:         timestamppb.New(tBirthDate),
			BirthLocation: &documents.BankServiceApplicationSMEIPBirthLocation{
				BirthCountry: birthPlace.Country.NameRu,
				BirthPlace:   birthPlace.City,
				Citizenship:  personaldata.GetCitizenship(personalDataResp).NameRu,
			},
			IdentityDocument: &documents.BankServiceApplicationSMEIPIdentityDocument{
				DocNumber: identityDocument.Number,
				DocSeries: "",
				DocIssueDate: timestamppb.New(func(sT string) time.Time {
					toTime, err := utils.ConvertDateStringToTime(sT)
					if err != nil {
						logger.Error().Msgf("field DocIssueDate value:'%s' in incorrect format date", sT)
						return time.Time{}
					}

					return toTime
				}(identityDocument.GetBeginDate())),
				IssueOrganization: identityDocument.IssueOrganization.NameRu,
				DocExpDate: timestamppb.New(func(sT string) time.Time {
					toTime, err := utils.ConvertDateStringToTime(sT)
					if err != nil {
						logger.Error().Msgf("field DocExpDate value:'%s' in incorrect format date", sT)
						return time.Time{}
					}

					return toTime
				}(identityDocument.GetEndDate())),
			},
		},
		IsLeader:   false,
		CreateDate: timestamppb.New(time.Now()),
		Sign:       nil,
		Registration: &documents.BankServiceApplicationSMEIPRegistration{
			RegistrationCert: smeIPInfo.RegistrationCert,
			RegistrationDate: timestamppb.New(func(sT string) time.Time {
				toTime, err := utils.ConvertDateStringToTime(sT)
				if err != nil {
					logger.Error().Msgf("field RegistrationDate value:'%s' in incorrect format date", sT)
					return time.Time{}
				}
				return toTime
			}(smeIPInfo.RegistrationDate)),
		},
		License: makeLicenseGenerateBankServiceApplicationSMEIP(ctx, taxPayerLicense),
	}
	logger.Debug().Interface("generateBankServiceApplicationSMEIPReq", generateBankServiceApplicationSMEIPReq).Msg("generateBankServiceApplicationSMEIPReq")

	doc, err := u.Providers.Documents.GenerateBankServiceApplicationSMEIP(ctx, generateBankServiceApplicationSMEIPReq)
	if err != nil {
		return fmt.Errorf("failed to Documents.GenerateBankServiceApplicationSMEIP: %w", err)
	}
	logger.Debug().Interface("doc", doc).Msg("doc")

	// Пробуем получить документ 'Заявление' из БД
	appDoc, err := u.Providers.Storage.GetDocumentByTypeAndAppID(ctx, consts.DocTypeBankingServiceApplicationSMEIP, loanApp.ID)
	if err != nil && !errors.Is(err, loansErrs.LoansErrs().DocumentNotFoundError()) {
		return err
	}
	logger.Debug().Interface("appDoc", appDoc).Msg("appDoc")

	if appDoc == nil {
		docToSave := &entity.Document{
			Type:          consts.DocTypeBankingServiceApplicationSMEIP,
			DocID:         doc.GetId(),
			ApplicationID: loanApp.ID,
		}
		logger.Debug().Interface("docToSave", docToSave).Msg("docToSave")
		_, err = u.Providers.Storage.SaveDocument(ctx, docToSave)
		if err != nil {
			return err
		}
	} else {
		updatedDoc := *appDoc
		updatedDoc.DocID = doc.GetId()
		logger.Debug().Interface("updatedDoc", updatedDoc).Msg("updatedDoc")
		err = u.Providers.Storage.UpdateDocumentByTypeAndAppID(ctx, consts.DocTypeBankingServiceApplicationSMEIP, loanApp.ID, &updatedDoc)
		if err != nil {
			return err
		}
	}

	return nil
}

func makeLicenseGenerateBankServiceApplicationSMEIP(ctx context.Context, taxPayerLicense []*pkb_bridge.TaxPayerLicense) *documents.BankServiceApplicationSMEIPLicense {
	logger := logs.FromContext(ctx)
	if taxPayerLicense != nil || len(taxPayerLicense) == 0 {
		return &documents.BankServiceApplicationSMEIPLicense{}
	}

	checkpoint := false
	if taxPayerLicense[0].ValidityEndDate != "" {
		endDate, err := utils.ConvertDateStringToTime(taxPayerLicense[0].ValidityEndDate)
		if err != nil {
			logger.Error().Err(err).Str("date", taxPayerLicense[0].ValidityEndDate).Msg("invalid license expiry date format")
			checkpoint = true
		} else {
			// Сравниваем с текущей датой
			now := time.Now()
			checkpoint = endDate.After(now) // Лицензия действительна, если дата окончания в будущем
			if checkpoint {
				logger.Info().
					Time("now", now).
					Time("endDate", endDate).
					Msg("license has expired")
			}
		}
	}

	licensiar := getpermitdocuments.GetLicensiar(taxPayerLicense[0])
	result := &documents.BankServiceApplicationSMEIPLicense{
		Checkbox:      checkpoint,
		LicenseNumber: taxPayerLicense[0].GlobalUniqueNumber,
		Licensiar:     licensiar.NameRu,
		ValidityDate: timestamppb.New(func(sT string) time.Time {
			toTime, err := utils.ConvertDateStringToTime(sT)
			if err != nil {
				logger.Error().Msgf("field RegistrationDate value:'%s' in incorrect format date", sT)
				return time.Time{}
			}
			return toTime
		}(taxPayerLicense[0].ValidityEndDate)),
		IssueDate: timestamppb.New(func(sT string) time.Time {
			toTime, err := utils.ConvertDateStringToTime(sT)
			if err != nil {
				logger.Error().Msgf("field RegistrationDate value:'%s' in incorrect format date", sT)
				return time.Time{}
			}
			return toTime
		}(taxPayerLicense[0].ValidityStartDate)),
	}
	return result
}
