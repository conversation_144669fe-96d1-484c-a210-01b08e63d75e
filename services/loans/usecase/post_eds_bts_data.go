package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/tools/conversion"

	"git.redmadrobot.com/zaman/backend/zaman/topics/taskmanager/payloads/types"

	"git.redmadrobot.com/zaman/backend/zaman/topics/taskmanager/payloads"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	loansErrs "git.redmadrobot.com/zaman/backend/zaman/errs/loans"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/entity"
	pbLiveness "git.redmadrobot.com/zaman/backend/zaman/specs/proto/liveness"
	pbTaskManager "git.redmadrobot.com/zaman/backend/zaman/specs/proto/task-manager"
)

// PostEdsBtsData - метод для верификации полученного кода в финале подписания документов ЭЦП
func (u *useCasesImpl) PostEdsBtsData(ctx context.Context, req *entity.PostEdsBtsDataReq) (*entity.PostEdsBtsDataResult, error) {
	logger := logs.FromContext(ctx)
	logger = conversion.Ptr(
		logger.
			With().
			Str(consts.LoggerFieldLoanApplicationID, req.ApplicationID).
			Logger(),
	)
	ctx = logs.ToContext(ctx, logger)

	// Получаем локаль пользователя из контекста запроса
	loc, err := utils.GetLocaleFromContext(ctx, true, false)
	if err != nil {
		return nil, fmt.Errorf("failed to utils.GetLocaleFromContext: %w", err)
	}
	logger.Debug().Interface("loc", loc).Msg("locale")

	// Получаем данные по кредиту из БД
	loanApp, err := u.Providers.Storage.GetLoanApplicationByID(ctx, req.ApplicationID, entity.LoanAppParams{
		WithConditions:     true,
		WithHistoryActions: true,
	})
	if err != nil {
		return nil, loansErrs.LoansErrs().ApplicationNotFoundError()
	}
	logger.Debug().Interface("loanApp", loc).Msg("loanApp")

	// Проверяем в корректном ли статусе находится кредитная заявка
	if !loanApp.IsAppApproved() {
		logger.Warn().Interface("loanApp", loanApp).Msgf("loan app is not approved but PostEdsBtsData is requested")
		return nil, loansErrs.LoansErrs().AppHasNotSuitableStatusError()
	}

	// Проверяем код и сохраняем подписанные ЭЦП документы через liveness
	resp, err := u.Providers.Liveness.VerifyEdsAndSaveFiles(ctx, &pbLiveness.VerifyEdsAndSaveFilesReq{
		Iin:  loanApp.UserIin,
		Code: req.Code,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to Liveness.VerifyEdsAndSaveFiles: %w", err)
	}
	logger.Debug().Interface("resp", resp).Msg("VerifyEdsAndSaveFiles resp")

	// Обновляем информацию о подписанных ЭЦП версиях документов в кредитной заявке
	for _, document := range resp.Documents {
		logger.Debug().Interface("document", document).Msg("document")
		savedDoc, err := u.Providers.Storage.GetDocumentByTypeAndAppID(ctx, consts.DocumentType(document.DocType), loanApp.ID)
		logger.Debug().Interface("savedDoc", savedDoc).Msg("savedDoc")
		if err != nil {
			return nil, fmt.Errorf("failed to get document by type and app id. type: %s, loanAppID: %s. err: %w", consts.DocumentType(document.DocType).String(), loanApp.ID, err)
		}

		err = u.Providers.Storage.SetSignedDocID(ctx, savedDoc.ID, document.DocumentID)
		if err != nil {
			return nil, fmt.Errorf("failed to Storage.SetSignedDocID. savedDoc.ID: %s, document.DocumentID: %s. err: %w", savedDoc.ID, document.DocumentID, err)
		}
	}

	currentTime, err := utils.GetCurrentKzTime()
	if err != nil {
		return nil, fmt.Errorf("failed to get current Kazakhstan time: %w", err)
	}
	calendar, err := u.getProductionCalendarInfo(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get ProductionCalendarInfo: %w", err)
	}

	var postponeDisbursement bool
	isBreak := u.isBreakNowCheck(ctx, currentTime, calendar.ExchangeWorkingHours.Breaks)
	logger.Debug().Interface("isBreak", isBreak).Msg("isBreak")

	isWorkingHours := u.isWorkingHoursCheck(ctx, currentTime, calendar.ExchangeWorkingHours)
	logger.Debug().Interface("isWorkingHours", isWorkingHours).Msg("isWorkingHours")

	if isBreak || !isWorkingHours {
		postponeDisbursement = true
		logger.Info().Msg("Current time is during the break or outside working hours, postponing disbursement")
	}

	// Создание и отправка task (процесс выдачи займа) в topic через Task Manager
	if err = u.createLoanDisbursementTaskOrSave(ctx, req, postponeDisbursement); err != nil {
		return nil, fmt.Errorf("failed to create loan disbursement task: %w", err)
	}

	// Получаем справочную информацию по финансам
	financeInfo, err := u.getDictFinanceInfo(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get DictFinanceInfo: %w", err)
	}

	result, err := entity.MakeBtsDataResultFromLoanConditions(ctx, loanApp, loc, financeInfo, postponeDisbursement)
	if err != nil {
		return nil, fmt.Errorf("failed to MakeBtsDataResultFromLoanConditions: %w", err)
	}

	return result, nil
}

// Создание и отправка task (процесс выдачи займа) в topic через Task Manager или сохранение payload в БД
func (u *useCasesImpl) createLoanDisbursementTaskOrSave(ctx context.Context, req *entity.PostEdsBtsDataReq, postponeDisbursement bool) error {
	logger := logs.FromContext(ctx)
	logger = conversion.Ptr(
		logger.
			With().
			Str(consts.LoggerFieldLoanApplicationID, req.ApplicationID).
			Logger(),
	)
	ctx = logs.ToContext(ctx, logger)

	requestID := utils.ExtractRequestID(ctx)

	// Origin для понимания с какого Gateway (Retail, SME)
	origin, err := utils.GetOrigin(ctx)
	if err != nil {
		return err
	}

	userID, err := utils.GetUserIDFromContext(ctx)
	if err != nil {
		return err
	}

	disbursementUUID, err := u.createLoanDisbursement(ctx, entity.LoanDisbursementEventPayload{
		ApplicationID: req.ApplicationID,
		RequestID:     requestID,
		Origin:        origin,
		UserID:        userID,
	}, postponeDisbursement)
	if err != nil {
		return fmt.Errorf("failed to create loan disbursement task: %w", err)
	}

	payload := payloads.СreateLoanDisbursement{
		DisbursementUUID: disbursementUUID,
		ApplicationID:    req.ApplicationID,
		RequestID:        requestID,
		Origin:           origin,
		UserID:           userID,
	}

	payloadData, err := json.Marshal(payload)
	if err != nil {
		logger.Error().Msgf("Failed to marshal loan disbursement payload: %+v, error: %v", payload, err)
		return fmt.Errorf("failed to marshal payload: %v - have error: %w", payload, err)
	}
	logger.Info().Msgf("Serialized payload: %+v, %+v", payload, payloadData)

	// Создаем task для отправки в TaskManager
	taskReq := &pbTaskManager.CreateTaskReq{
		TaskType:    types.TaskTypeLoanDisbursementAfterEDS.String(),
		TaskPayload: string(payloadData),
	}

	logger.Info().Msgf("Sending task request: %+v", taskReq)
	// Создаем task и пишем в топик
	createTaskResp, err := u.Providers.Taskmanager.CreateTask(ctx, taskReq)
	if err != nil {
		logger.Error().Msgf("Failed to create task in task manager: %v", err)
		return fmt.Errorf("failed to create task: %w", err)
	}
	logger.Info().Msgf("Task %s created: %s, %+v", types.TaskTypeLoanDisbursementAfterEDS.String(), createTaskResp.TaskId, createTaskResp.TaskPayload)
	return nil
}

func (u *useCasesImpl) createLoanDisbursement(ctx context.Context, disbursementPayload entity.LoanDisbursementEventPayload, isBreak bool) (string, error) {
	logger := logs.FromContext(ctx)

	disbursementID, err := u.Providers.Storage.CreateLoanDisbursement(ctx, disbursementPayload, isBreak)
	if err != nil {
		return "", fmt.Errorf("failed to save loan disbursement payload: %w", err)
	}
	if isBreak {
		return "", fmt.Errorf("cannot create loan disbursement during break time, applicationID: %s", disbursementPayload.ApplicationID)
	}

	logger.Info().Msgf("Successfully saved payload for loan disbursement, applicationID: %s", disbursementPayload.ApplicationID)
	return disbursementID, nil
}

// isBreakNowCheck проверяет, находится ли текущее время в перерыве
func (u *useCasesImpl) isBreakNowCheck(ctx context.Context, t time.Time, breaks []entity.BreakPeriod) bool {
	logger := logs.FromContext(ctx)
	currentDay := t.Weekday().String()
	currentTime := t.Format("15:04")

	for _, b := range breaks {
		if b.Day != currentDay {
			continue
		}

		// Парсим время начала и конца перерыва
		start, ok1 := utils.ParseHoursTime(b.StartHour)
		end, ok2 := utils.ParseHoursTime(b.EndHour)
		now, ok3 := utils.ParseHoursTime(currentTime)

		if !ok1 || !ok2 || !ok3 {
			logger.Warn().Msgf("Failed to parse working hours, start: %s, end: %s, now: %s", b.StartHour, b.EndHour, currentTime)
			continue
		}

		if now.After(start) && now.Before(end) {
			logger.Info().Msgf("Current time is during a break, now: %s, break start: %s, break end: %s, reason: %s", currentTime, b.StartHour, b.EndHour, b.Reason)
			return true
		}
	}

	logger.Debug().Msgf("Not in break time, current day: %s, current time: %s", currentDay, currentTime)
	return false
}

// isWorkingHoursCheck проверяет, находится ли текущее время в рабочем времени графика
func (u *useCasesImpl) isWorkingHoursCheck(ctx context.Context, t time.Time, ex entity.ExchangeWorkingHours) bool {
	logger := logs.FromContext(ctx)

	currentDay := t.Weekday().String()
	currentTimeStr := t.Format("15:04")

	// Проверка дня недели (если список дней задан)
	if len(ex.AllowedDays) > 0 {
		allowed := slices.Contains(ex.AllowedDays, currentDay)
		if !allowed {
			logger.Info().Msgf("Working hours not allowed, current day: %s", currentDay)
			return false
		}
	}

	// Парсим часы начала и конца
	start, ok1 := utils.ParseHoursTime(ex.StartHour)
	end, ok2 := utils.ParseHoursTime(ex.EndHour)
	now, ok3 := utils.ParseHoursTime(currentTimeStr)
	if !ok1 || !ok2 || !ok3 {
		logger.Warn().Msgf("Failed to parse working hours, start: %s, end: %s, now: %s", ex.StartHour, ex.EndHour, currentTimeStr)
		return false
	}

	// Обрабатываем случай когда рабочий период проходит через полночь
	inPeriod := false
	if end.After(start) || end.Equal(start) {
		// Обычный случай: start <= now <= end
		if (now.Equal(start) || now.After(start)) && (now.Equal(end) || now.Before(end)) {
			inPeriod = true
		}
	} else {
		// Период через полночь: например 21:00 - 03:00
		// В этом случае считаем, что текущее время в рабочем периоде,
		// если оно либо после (или равно) времени начала, либо до (или равно) времени конца.
		// То есть объединяем интервалы [start, 23:59] и [00:00, end].
		if now.Equal(start) || now.After(start) {
			inPeriod = true
		}
		// Когда now == end или now < end — также внутри периода после полуночи
		if now.Equal(end) || now.Before(end) {
			inPeriod = true
		}
	}

	if !inPeriod {
		logger.Info().Msgf("Current time is outside working hours, now: %s, start: %s, end: %s", currentTimeStr, ex.StartHour, ex.EndHour)
		return false
	}

	logger.Debug().Msgf("Working hours check: %t", true)
	return true
}
