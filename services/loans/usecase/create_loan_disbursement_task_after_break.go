package usecase

import (
	"context"
	"encoding/json"
	"fmt"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	pbTaskManager "git.redmadrobot.com/zaman/backend/zaman/specs/proto/task-manager"
	"git.redmadrobot.com/zaman/backend/zaman/topics/taskmanager/payloads"
	"git.redmadrobot.com/zaman/backend/zaman/topics/taskmanager/payloads/types"
)

func (u *useCasesImpl) CreateLoanDisbursementTaskAfterBreak(ctx context.Context) error {
	logger := logs.FromContext(ctx)
	logger.Info().Msg("Starting CreateLoanDisbursementTaskAfterBreak cron")
	logger.Debug().Msg("Fetching loan disbursement payloads with status 'PENDING_FUNDS'")

	currentTime, err := utils.GetCurrentKzTime()
	if err != nil {
		return fmt.Errorf("failed to get current Kazakhstan time: %w", err)
	}

	calendar, err := u.getProductionCalendarInfo(ctx)
	if err != nil {
		return fmt.Errorf("failed to get ProductionCalendarInfo: %w", err)
	}

	isBreak := u.isBreakNowCheck(ctx, currentTime, calendar.ExchangeWorkingHours.Breaks)
	logger.Debug().Interface("isBreak", isBreak).Msg("isBreak")

	isWorkingHours := u.isWorkingHoursCheck(ctx, currentTime, calendar.ExchangeWorkingHours)
	logger.Debug().Interface("isWorkingHours", isWorkingHours).Msg("isWorkingHours")

	if isBreak || !isWorkingHours {
		logger.Info().Msgf("Current time %s is during the break or not working hours, skipping task creation", currentTime)
		return nil
	}

	// Получаем не обработанные заявки на выдачу займа
	disbursementPayloads, err := u.Providers.Storage.GetLoanDisbursementNotCompleted(ctx, true)
	if err != nil {
		logger.Error().Msgf("Error fetching payloads: %v", err)
		return fmt.Errorf("failed to get loan disbursement payloads: %w", err)
	}
	logger.Debug().Msgf("Fetched %d payloads", len(disbursementPayloads))

	for _, payload := range disbursementPayloads {
		logger.Debug().Msgf("Processing payload: %+v", payload)
		req := payloads.СreateLoanDisbursement{
			DisbursementUUID: payload.ID,
			ApplicationID:    payload.ApplicationID,
			RequestID:        payload.RequestID,
			Origin:           payload.Origin,
			UserID:           payload.UserID,
		}

		logger.Debug().Msgf("Creating loan disbursement task for ApplicationID: %s, RequestID: %s", req.ApplicationID, req.RequestID)
		// записываем таску в кафку
		err := u.createLoanDisbursementTask(ctx, req)
		if err != nil {
			logger.Error().Msgf("Error creating loan disbursement task: %v", err)
			return err
		}

		logger.Debug().Msgf("Updating payload status to 'COMPLETED' for ID: %v", payload.ID)

		// меняем статус обработки крона на false
		err = u.Providers.Storage.UpdateLoanDisbursementCronStatus(ctx, payload.ID, false)
		if err != nil {
			logger.Error().Msgf("Error updating payload status: %v", err)
			return err
		}
		logger.Debug().Msgf("Payload status updated for ID: %v", payload.ID)
	}

	logger.Info().Msg("Finished CreateLoanDisbursementTaskAfterBreak cron")
	return nil
}

// Создание и отправка task (процесс выдачи займа) в topic через Task Manager
func (u *useCasesImpl) createLoanDisbursementTask(ctx context.Context, req payloads.СreateLoanDisbursement) error {
	logger := logs.FromContext(ctx)
	logger.Debug().Msgf("Preparing payload for ApplicationID: %s, RequestID: %s", req.ApplicationID, req.RequestID)

	payloadData, err := json.Marshal(req)
	if err != nil {
		logger.Error().Msgf("Failed to marshal loan disbursement payload: %+v, error: %v", req, err)
		return fmt.Errorf("failed to marshal payload: %v - have error: %w", req, err)
	}
	logger.Debug().Msgf("Payload marshaled: %s", string(payloadData))
	logger.Info().Msgf("Serialized payload: %+v, %+v", req, payloadData)

	// Создаем task для отправки в TaskManager
	taskReq := &pbTaskManager.CreateTaskReq{
		TaskType:    types.TaskTypeLoanDisbursementAfterEDS.String(),
		TaskPayload: string(payloadData),
	}

	logger.Debug().Msgf("Task request prepared: %+v", taskReq)
	logger.Info().Msgf("Sending task request: %+v", taskReq)
	// Создаем task и пишем в топик
	createTaskResp, err := u.Providers.Taskmanager.CreateTask(ctx, taskReq)
	if err != nil {
		logger.Error().Msgf("Failed to create task in task manager: %v", err)
		return fmt.Errorf("failed to create task: %w", err)
	}
	logger.Debug().Msgf("Task created: ID=%s, Payload=%s", createTaskResp.TaskId, createTaskResp.TaskPayload)
	logger.Info().Msgf("Task %s created: %s, %+v", types.TaskTypeLoanDisbursementAfterEDS.String(), createTaskResp.TaskId, createTaskResp.TaskPayload)

	return nil
}
