package storage

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"slices"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx/transaction"
	"git.redmadrobot.com/backend-go/rmr-pkg/tools/conversion"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/loans/storage/postgres/ent/predicate"

	pg "git.redmadrobot.com/zaman/backend/zaman/services/loans/storage/postgres"

	"git.redmadrobot.com/zaman/backend/zaman/services/loans/storage/postgres/ent/actionshistory"

	"git.redmadrobot.com/zaman/backend/zaman/services/loans/consts"

	loansErrs "git.redmadrobot.com/zaman/backend/zaman/errs/loans"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/storage/postgres/ent"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/storage/postgres/ent/loanapplication"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/storage/postgres/ent/loancondition"

	"git.redmadrobot.com/zaman/backend/zaman/services/loans/entity"
)

var _ LoanApplication = (*storageImpl)(nil)

type LoanApplication interface {
	CreateLoanApplication(ctx context.Context, req *entity.CreateLoanApplicationReq, termInterest *entity.LoanTermInterest, userID string, userIin string, purpose string, userFullName string) (*entity.LoanApplication, error)
	// UpdateLoanApplication обновляет существующую кредитную заявку: сумму, цель, срок, процентную ставку и JuicySessionID
	UpdateLoanApplication(ctx context.Context, req *entity.UpdateLoanApplicationReq, termInterest *entity.LoanTermInterest, purposeID string) error
	UpdateLoanApplicationStatus(ctx context.Context, applicationID string, newStatus consts.LoanApplicationStatus, userID string, userIin string) error
	GetLoanApplicationsByStatusesAndUserID(ctx context.Context, userID string, statuses []consts.LoanApplicationStatus, params entity.LoanAppParams) ([]*entity.LoanApplication, error)
	GetLoanApplicationsByStatusesAndColvirReferenceID(ctx context.Context, colvirReferenceID string, statuses []consts.LoanApplicationStatus) (*entity.LoanApplication, error)
	GetLoanApplicationsByStatuses(ctx context.Context, statuses []consts.LoanApplicationStatus, params entity.LoanAppParams) ([]*entity.LoanApplication, error)
	GetOutdatedLoanApplications(ctx context.Context, daysToExpire int) ([]*entity.LoanApplication, error)
	GetLoanApplicationByID(ctx context.Context, applicationID string, params entity.LoanAppParams) (*entity.LoanApplication, error)
	UpdateLoanApplicationStatusBatch(ctx context.Context, applicationIDs []string, status consts.LoanApplicationStatus) (int64, error)
	UpdateDisbursementAccountID(ctx context.Context, applicationID string, disbursementAccountID string) error
	SetColvirFields(ctx context.Context, applicationID string, req *entity.LoanApplication) error
	GetLoanApplicationsByIDsAndStatuses(ctx context.Context, statuses []consts.LoanApplicationStatus, applicationIDs []string) ([]*entity.LoanApplication, error)
	GetCountLoanLastSprRejectedApplicationsByUserID(ctx context.Context, userID string) (int16, error)
	GetAllApplicationsByStatusesAndUserIDExceptCurrentAppID(ctx context.Context, userID string, statuses []consts.LoanApplicationStatus, applicationID string) ([]*entity.LoanApplication, error)
	MakeLoanApplicationRefinancing(ctx context.Context, userID string, applicationID string, refinancingConditions *entity.RefinancingConditions, externalBankLoans []*entity.ExternalBankLoan) error
	GetWaitingLoanApplications(ctx context.Context, updateTimeLT int, params entity.LoanAppParams) ([]*entity.LoanApplication, error)
	UpdateClientRisks(ctx context.Context, applicationID string, clientRisks bool) error
}

func (s *storageImpl) CreateLoanApplication(ctx context.Context, req *entity.CreateLoanApplicationReq, termInterest *entity.LoanTermInterest, userID string, userIin string, purpose string, userFullName string) (*entity.LoanApplication, error) {
	tx, err := pg.MakeTxFn(s.PostgresClient)(ctx, &sql.TxOptions{Isolation: sql.LevelRepeatableRead})
	if err != nil {
		return nil, fmt.Errorf("could not begin transaction: %w", err)
	}
	defer func() {
		if rbErr := tx.Rollback(); rbErr != nil && !errors.Is(rbErr, sql.ErrTxDone) {
			logs.FromContext(ctx).Err(rbErr).Msg("failed to rollback transaction")
		}
	}()

	activeStatuses := consts.GetActiveStatuses()
	activeStatusesEnt := make([]loanapplication.Status, 0, len(activeStatuses))
	for _, status := range activeStatuses {
		activeStatusesEnt = append(activeStatusesEnt, loanapplication.Status(status))
	}

	activeLoanApplicationExists, err := tx.Client.LoanApplication.Query().
		Where(
			// Проверять нужно именно по ИИНу, потому что у пользователя в разных направлениях (Retail, SME) разные ID, но одинаковый ИИН
			loanapplication.UserIinEQ(userIin),
			loanapplication.StatusIn(activeStatusesEnt...),
		).
		Exist(ctx)
	if err != nil {
		return nil, err
	}

	status := loanapplication.StatusDRAFT
	if activeLoanApplicationExists {
		status = loanapplication.StatusREJECTED
	}

	application, err := tx.Client.LoanApplication.Create().
		SetStatus(status).
		SetPurpose(purpose).
		SetUserID(userID).
		SetUserIin(userIin).
		SetJuicySessionID(req.JuicySessionID).
		SetBusinessType(loanapplication.BusinessType(req.BusinessType)).
		SetUserFullNameAtCreation(userFullName).
		Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("could not create loan application: %w", err)
	}

	// Создаём условия кредита только если переданы данные о процентной ставке и сроке
	var condition *ent.LoanCondition
	if termInterest != nil {
		var err error
		condition, err = tx.Client.LoanCondition.Create().
			SetApplication(application).
			SetAmount(req.Amount).
			SetInterest(termInterest.LoanInterest).
			SetTerm(termInterest.LoanTerm).
			SetIsOriginal(true).
			SetIsApprovedBySpr(false).
			SetIsFinallyChosenByUser(false).
			SetKdn(0).
			SetKdd(0).
			Save(ctx)
		if err != nil {
			return nil, fmt.Errorf("could not create loan condition: %w", err)
		}

		application.Edges.Conditions = append(application.Edges.Conditions, condition)
	}

	spr, err := tx.Client.LoanApplicationSpr.Create().
		SetApplicationID(application.ID).
		Save(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create loan app spr entry: %w", err)
	}

	application.Edges.Spr = spr

	entityApplication, err := entity.MakeStorageLoanApplicationToEntity(application)
	if err != nil {
		return nil, err
	}

	saveHistoryActionsReq := entity.HistoryActions{
		&entity.HistoryAction{
			ActionType:    consts.CreatedLoanApplication,
			ApplicationID: application.ID.String(),
		},
	}

	if activeLoanApplicationExists {
		saveHistoryActionsReq = append(saveHistoryActionsReq, &entity.HistoryAction{
			ActionType:    consts.ActiveApplicationCheckFailed,
			ApplicationID: application.ID.String(),
		})
	}

	err = s.SaveHistoryActionsWithTransaction(ctx, tx, saveHistoryActionsReq)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return entityApplication, nil
}

func (s *storageImpl) UpdateLoanApplication(ctx context.Context, req *entity.UpdateLoanApplicationReq, termInterest *entity.LoanTermInterest, purposeID string) error {
	tx, err := pg.MakeTxFn(s.PostgresClient)(ctx, &sql.TxOptions{Isolation: sql.LevelRepeatableRead})
	if err != nil {
		return fmt.Errorf("could not begin transaction: %w", err)
	}
	defer func() {
		if rbErr := tx.Rollback(); rbErr != nil && !errors.Is(rbErr, sql.ErrTxDone) {
			logs.FromContext(ctx).Err(rbErr).Msg("failed to rollback transaction")
		}
	}()

	// Парсим ID заявки
	applicationUUID, err := uuid.Parse(req.ApplicationID)
	if err != nil {
		return fmt.Errorf("invalid application ID: %w", err)
	}

	// Обновляем основные поля кредитной заявки
	err = tx.Client.LoanApplication.
		UpdateOneID(applicationUUID).
		SetPurpose(purposeID).
		SetJuicySessionID(req.JuicySessionID).
		Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to update loan application: %w", err)
	}

	// Обновляем первоначальные условия кредита если они существуют
	err = tx.Client.LoanCondition.
		Update().
		Where(
			loancondition.HasApplicationWith(loanapplication.IDEQ(applicationUUID)),
			loancondition.IsOriginalEQ(true),
		).
		SetAmount(req.Amount).
		SetInterest(termInterest.LoanInterest).
		SetTerm(termInterest.LoanTerm).
		Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to update loan conditions: %w", err)
	}

	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

func (s *storageImpl) UpdateLoanApplicationStatus(ctx context.Context, applicationID string, newStatus consts.LoanApplicationStatus, userID string, userIin string) error {
	tx, err := pg.MakeTxFn(s.PostgresClient)(ctx, &sql.TxOptions{Isolation: sql.LevelRepeatableRead})
	if err != nil {
		return fmt.Errorf("could not begin transaction: %w", err)
	}
	defer func() {
		if rbErr := tx.Rollback(); rbErr != nil && !errors.Is(rbErr, sql.ErrTxDone) {
			logs.FromContext(ctx).Err(rbErr).Msg("failed to rollback transaction")
		}
	}()

	err = s.UpdateLoanApplicationStatusWithTx(ctx, tx, applicationID, newStatus, userID, userIin)
	if err != nil {
		return err
	}

	err = tx.Commit()
	if err != nil {
		return err
	}

	return nil
}

func (s *storageImpl) UpdateLoanApplicationStatusWithTx(ctx context.Context, tx *transaction.Tx[ent.Client], applicationID string, newStatus consts.LoanApplicationStatus, _ string, userIin string) error {
	applicationUUID, err := uuid.Parse(applicationID)
	if err != nil {
		return err
	}
	// validTransitions — список возможных переходов статуса заявки на кредит. Значением является возможный следующий статус заявки
	validTransitions := map[loanapplication.Status][]consts.LoanApplicationStatus{
		loanapplication.StatusDRAFT:         {consts.LoanApplicationStatusWaiting, consts.LoanApplicationStatusCancelled, consts.LoanApplicationStatusRejected, consts.LoanApplicationStatusExpired},
		loanapplication.StatusWAITING:       {consts.LoanApplicationStatusApproved, consts.LoanApplicationStatusRejected, consts.LoanApplicationStatusCancelled},
		loanapplication.StatusAPPROVED:      {consts.LoanApplicationStatusExpired, consts.LoanApplicationStatusCancelled, consts.LoanApplicationStatusRejected, consts.LoanApplicationStatusPendingFunds},
		loanapplication.StatusPENDING_FUNDS: {consts.LoanApplicationStatusCompleted, consts.LoanApplicationStatusApproved},
		loanapplication.StatusEXPIRED:       {},
		loanapplication.StatusCOMPLETED:     {consts.LoanApplicationStatusPaid},
		loanapplication.StatusCANCELLED:     {},
		loanapplication.StatusREJECTED:      {},
	}

	activeStatuses := consts.GetActiveStatuses()
	activeStatusesEnt := make([]loanapplication.Status, 0, len(activeStatuses))
	for _, status := range activeStatuses {
		activeStatusesEnt = append(activeStatusesEnt, loanapplication.Status(status))
	}

	activeLoanApplicationExists, err := tx.Client.LoanApplication.Query().
		Where(
			loanapplication.UserIinEQ(userIin),
			loanapplication.StatusIn(activeStatusesEnt...),
			loanapplication.IDNEQ(applicationUUID),
		).
		Exist(ctx)
	if err != nil {
		return err
	}
	// TODO: возможно стоит вынести эту логику в юзкейс что бы стораж был тупым, а не умным
	// Если есть активные заявки - создаёт заявку в статусе Rejected
	if activeLoanApplicationExists && slices.Contains(activeStatuses, newStatus) {
		activeAppExistsErr := loansErrs.LoansErrs().ActiveApplicationAlreadyExistsError()

		innerTx, err := pg.MakeTxFn(s.PostgresClient)(ctx, &sql.TxOptions{Isolation: sql.LevelReadCommitted})
		if err != nil {
			return fmt.Errorf("could not begin transaction: %w", err)
		}
		defer func() {
			if rbErr := innerTx.Rollback(); rbErr != nil && !errors.Is(rbErr, sql.ErrTxDone) {
				logs.FromContext(ctx).Err(rbErr).Msg("failed to rollback transaction")
			}
		}()

		err = innerTx.Client.LoanApplication.Update().Where(loanapplication.IDEQ(applicationUUID)).SetStatus(loanapplication.StatusREJECTED).Exec(ctx)
		if err != nil {
			return err
		}

		err = s.SaveHistoryActionWithTransaction(ctx, innerTx, &entity.HistoryAction{
			ActionType:    consts.ActiveApplicationCheckFailed,
			ApplicationID: applicationUUID.String(),
		})
		if err != nil {
			return err
		}

		err = innerTx.Commit()
		if err != nil {
			return err
		}

		return activeAppExistsErr
	}

	application, err := tx.Client.LoanApplication.Get(ctx, applicationUUID)
	if err != nil {
		if ent.IsNotFound(err) {
			return loansErrs.LoansErrs().ApplicationNotFoundError()
		}
		return fmt.Errorf("could not retrieve loan application: %w", err)
	}

	if validNextStatuses, ok := validTransitions[application.Status]; ok {
		if !slices.Contains(validNextStatuses, newStatus) {
			return fmt.Errorf("invalid status transition: %s -> %s", application.Status, newStatus)
		}
	} else {
		return fmt.Errorf("invalid status transition: %s -> %s", application.Status, newStatus)
	}

	err = tx.Client.LoanApplication.Update().
		SetStatus(loanapplication.Status(newStatus)).
		Where(loanapplication.IDEQ(applicationUUID)).
		Exec(ctx)
	if err != nil {
		return fmt.Errorf("could not update loan application status: %w", err)
	}

	historyActionType := newStatus.ToHistoryActionType()
	if historyActionType != "" {
		err = s.SaveHistoryActionWithTransaction(ctx, tx, &entity.HistoryAction{
			ActionType:    historyActionType,
			ApplicationID: applicationID,
		})
		if err != nil {
			return err
		}
	}

	return nil
}

// TODO: вынести общую с другими методами логику в отдельный метод
func (s *storageImpl) GetLoanApplicationsByStatusesAndUserID(ctx context.Context, userID string, statuses []consts.LoanApplicationStatus, params entity.LoanAppParams) ([]*entity.LoanApplication, error) {
	applicationsQuery := s.PostgresClient.LoanApplication.Query().
		Where(
			loanapplication.UserID(userID),
			loanapplication.StatusIn(
				entity.MakeEntityStatusesToPostgres(statuses)...,
			),
		).WithSpr()

	if params.WithConditions {
		applicationsQuery = applicationsQuery.WithConditions()
	}
	if params.WithRefinancingConditions {
		applicationsQuery = applicationsQuery.WithRefinancingConditions()
	}
	if params.WithExternalBankLoans {
		applicationsQuery = applicationsQuery.WithExternalBankLoans()
	}
	if params.WithDocs {
		applicationsQuery = applicationsQuery.WithDocuments()
	}
	if params.WithHistoryActions {
		applicationsQuery = applicationsQuery.WithHistoryActions()
	}
	if params.WithSurvey {
		applicationsQuery = applicationsQuery.WithSurvey()
	}
	if params.BusinessType != "" {
		applicationsQuery = applicationsQuery.Where(loanapplication.BusinessTypeEQ(params.BusinessType))
	}

	applications, err := applicationsQuery.All(ctx)
	if err != nil {
		return nil, err
	}

	entityApplications := make([]*entity.LoanApplication, len(applications))

	for i, application := range applications {
		entityApplication, err := entity.MakeStorageLoanApplicationToEntity(application)
		if err != nil {
			return nil, err
		}

		entityApplications[i] = entityApplication
	}

	return entityApplications, nil
}

func (s *storageImpl) GetLoanApplicationsByStatusesAndColvirReferenceID(ctx context.Context, colvirReferenceID string, statuses []consts.LoanApplicationStatus) (*entity.LoanApplication, error) {
	where := []predicate.LoanApplication{loanapplication.ColvirReferenceID(colvirReferenceID)}
	if len(statuses) > 0 {
		where = append(where, loanapplication.StatusIn(
			entity.MakeEntityStatusesToPostgres(statuses)...,
		))
	}

	data, err := s.PostgresClient.LoanApplication.Query().
		Where(
			where...,
		).Order(ent.Desc(loanapplication.FieldCreateTime)).First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, loansErrs.LoansErrs().ApplicationNotFoundError()
		}
		return nil, err
	}

	application, err := entity.MakeStorageLoanApplicationToEntity(data)
	if err != nil {
		return nil, err
	}

	return application, nil
}

func (s *storageImpl) GetLoanApplicationsByStatuses(ctx context.Context, statuses []consts.LoanApplicationStatus, params entity.LoanAppParams) ([]*entity.LoanApplication, error) {
	var entityApplications []*entity.LoanApplication
	applicationsQuery := s.PostgresClient.LoanApplication.Query().
		Where(
			loanapplication.StatusIn(
				entity.MakeEntityStatusesToPostgres(statuses)...,
			),
		).WithSpr()

	if params.WithConditions {
		applicationsQuery = applicationsQuery.WithConditions()
	}
	if params.WithRefinancingConditions {
		applicationsQuery = applicationsQuery.WithRefinancingConditions()
	}
	if params.WithExternalBankLoans {
		applicationsQuery = applicationsQuery.WithExternalBankLoans()
	}
	if params.WithDocs {
		applicationsQuery = applicationsQuery.WithDocuments()
	}
	if params.WithHistoryActions {
		applicationsQuery = applicationsQuery.WithHistoryActions()
	}
	if params.WithSurvey {
		applicationsQuery = applicationsQuery.WithSurvey()
	}
	applications, err := applicationsQuery.All(ctx)
	if err != nil {
		return nil, err
	}

	for _, application := range applications {
		entityApplication, err := entity.MakeStorageLoanApplicationToEntity(application)
		if err != nil {
			return nil, err
		}

		entityApplications = append(entityApplications, entityApplication)
	}

	return entityApplications, nil
}

func (s *storageImpl) GetLoanApplicationByID(ctx context.Context, applicationID string, params entity.LoanAppParams) (*entity.LoanApplication, error) {
	applicationUUID, err := uuid.Parse(applicationID)
	if err != nil {
		return nil, err
	}

	applicationsQuery := s.PostgresClient.LoanApplication.Query().
		Where(loanapplication.ID(applicationUUID)).WithSpr()

	// TODO: вынести это в отдельную функцию
	if params.WithConditions {
		applicationsQuery = applicationsQuery.WithConditions()
	}
	if params.WithRefinancingConditions {
		applicationsQuery = applicationsQuery.WithRefinancingConditions()
	}
	if params.WithExternalBankLoans {
		applicationsQuery = applicationsQuery.WithExternalBankLoans()
	}
	if params.WithDocs {
		applicationsQuery = applicationsQuery.WithDocuments(func(query *ent.DocumentQuery) {
			query.WithApplication()
		})
	}
	if params.WithHistoryActions {
		applicationsQuery = applicationsQuery.WithHistoryActions()
	}
	if params.WithSurvey {
		applicationsQuery = applicationsQuery.WithSurvey()
	}
	applications, err := applicationsQuery.All(ctx)
	if err != nil {
		return nil, err
	}
	if len(applications) == 0 {
		return nil, loansErrs.LoansErrs().ApplicationNotFoundError()
	} else if len(applications) > 1 {
		return nil, fmt.Errorf("more than one application found")
	}
	application := applications[0]

	entityApplication, err := entity.MakeStorageLoanApplicationToEntity(application)
	if err != nil {
		return nil, err
	}

	return entityApplication, nil
}

// UpdateLoanApplicationStatusBatch - изменяет статусы кредитных заявок одним запросом. Использовать с осторожностью ибо здесь не имеется проверка на правильный переход из одного статуса в другой
func (s *storageImpl) UpdateLoanApplicationStatusBatch(ctx context.Context, applicationIds []string, status consts.LoanApplicationStatus) (int64, error) {
	if len(applicationIds) == 0 {
		return 0, fmt.Errorf("application ids is empty")
	}

	tx, err := pg.MakeTxFn(s.PostgresClient)(ctx, &sql.TxOptions{Isolation: sql.LevelReadCommitted})
	if err != nil {
		return 0, err
	}
	defer func() {
		if rbErr := tx.Rollback(); rbErr != nil && !errors.Is(rbErr, sql.ErrTxDone) {
			logs.FromContext(ctx).Err(rbErr).Msg("failed to rollback transaction")
		}
	}()

	applicationUuids := make([]uuid.UUID, 0, len(applicationIds))
	for _, id := range applicationIds {
		currentUUID, err := uuid.Parse(id)
		if err != nil {
			return 0, err
		}
		applicationUuids = append(applicationUuids, currentUUID)
	}
	affectedRows, err := tx.Client.LoanApplication.Update().Where(loanapplication.IDIn(applicationUuids...)).SetStatus(loanapplication.Status(status)).Save(ctx)
	if err != nil {
		return 0, err
	}

	historyActionReqSlice := make(entity.HistoryActions, 0, len(applicationUuids))
	for _, applicationID := range applicationUuids {
		historyActionType := status.ToHistoryActionType()
		if historyActionType == "" {
			continue
		}
		historyActionReqSlice = append(historyActionReqSlice, &entity.HistoryAction{
			ActionType:    historyActionType,
			ApplicationID: applicationID.String(),
		})
	}

	err = s.SaveHistoryActionsWithTransaction(ctx, tx, historyActionReqSlice)
	if err != nil {
		return 0, err
	}

	err = tx.Commit()
	if err != nil {
		return 0, err
	}

	return int64(affectedRows), nil
}

func (s *storageImpl) GetOutdatedLoanApplications(ctx context.Context, daysToExpire int) ([]*entity.LoanApplication, error) {
	statusesToExpire := []consts.LoanApplicationStatus{
		consts.LoanApplicationStatusDraft,
		consts.LoanApplicationStatusApproved,
	}

	outdatedTime := time.Now().Add(-time.Hour * 24 * time.Duration(daysToExpire))

	var entityApplications []*entity.LoanApplication
	applicationsQuery := s.PostgresClient.LoanApplication.Query().
		Where(
			loanapplication.StatusIn(
				entity.MakeEntityStatusesToPostgres(statusesToExpire)...),
			loanapplication.UpdateTimeLT(outdatedTime),
			loanapplication.Not(
				loanapplication.HasHistoryActionsWith(
					actionshistory.CreateTimeGTE(outdatedTime),
				),
			),
			loanapplication.Not(
				loanapplication.HasHistoryActionsWith(
					actionshistory.CreateTimeGTE(outdatedTime),
					actionshistory.ActionTypeIn(
						actionshistory.ActionType(consts.IntegrationError),
						actionshistory.ActionType(consts.TechError),
					),
				),
			),
		)

	applications, err := applicationsQuery.All(ctx)
	if err != nil {
		return nil, err
	}

	for _, application := range applications {
		entityApplication, err := entity.MakeStorageLoanApplicationToEntity(application)
		if err != nil {
			return nil, err
		}
		entityApplications = append(entityApplications, entityApplication)
	}

	return entityApplications, nil
}

func (s *storageImpl) UpdateDisbursementAccountID(ctx context.Context, applicationID string, disbursementAccountID string) error {
	applicationUUID, err := uuid.Parse(applicationID)
	if err != nil {
		return err
	}

	tx, err := pg.MakeTxFn(s.PostgresClient)(ctx, &sql.TxOptions{Isolation: sql.LevelReadCommitted})
	if err != nil {
		return err
	}
	defer func() {
		if rbErr := tx.Rollback(); rbErr != nil && !errors.Is(rbErr, sql.ErrTxDone) {
			logs.FromContext(ctx).Err(rbErr).Msg("failed to rollback transaction")
		}
	}()

	err = s.PostgresClient.LoanApplication.Update().
		Where(loanapplication.ID(applicationUUID)).
		SetDisbursementAccountID(disbursementAccountID).
		Exec(ctx)
	if err != nil {
		return err
	}

	err = tx.Commit()
	if err != nil {
		return err
	}

	return nil
}

func (s *storageImpl) SetColvirFields(ctx context.Context, applicationID string, req *entity.LoanApplication) error {
	applicationUUID, err := uuid.Parse(applicationID)
	if err != nil {
		return err
	}

	tx, err := pg.MakeTxFn(s.PostgresClient)(ctx, &sql.TxOptions{Isolation: sql.LevelReadCommitted})
	if err != nil {
		return err
	}
	defer func() {
		if rbErr := tx.Rollback(); rbErr != nil && !errors.Is(rbErr, sql.ErrTxDone) {
			logs.FromContext(ctx).Err(rbErr).Msg("failed to rollback transaction")
		}
	}()

	update := s.PostgresClient.LoanApplication.Update().
		Where(loanapplication.ID(applicationUUID))

	if req.ColvirReferenceID != nil {
		update = update.SetColvirReferenceID(*req.ColvirReferenceID)
	}
	if req.ColvirProductCode != nil {
		update = update.SetColvirProductCode(*req.ColvirProductCode)
	}
	if req.ColvirContractFromDate != nil {
		update = update.SetColvirFromDate(*req.ColvirContractFromDate)
	}
	if req.ColvirContractToDate != nil {
		update = update.SetColvirToDate(*req.ColvirContractToDate)
	}

	if err = update.Exec(ctx); err != nil {
		return err
	}

	return tx.Commit()
}

func (s *storageImpl) GetLoanApplicationsByIDsAndStatuses(ctx context.Context, statuses []consts.LoanApplicationStatus, applicationIDs []string) ([]*entity.LoanApplication, error) {
	applicationUuids := make([]uuid.UUID, 0, len(applicationIDs))
	for _, id := range applicationIDs {
		currentUUID, err := uuid.Parse(id)
		if err != nil {
			return nil, err
		}
		applicationUuids = append(applicationUuids, currentUUID)
	}

	var entityApplications []*entity.LoanApplication
	applicationsQuery := s.PostgresClient.LoanApplication.Query().
		Where(
			loanapplication.StatusIn(
				entity.MakeEntityStatusesToPostgres(statuses)...,
			),
			loanapplication.IDIn(applicationUuids...),
		).
		WithHistoryActions() // TODO добавить params как входящий параметр метода и брать это оттуда

	applications, err := applicationsQuery.All(ctx)
	if err != nil {
		return nil, err
	}

	for _, application := range applications {
		entityApplication, err := entity.MakeStorageLoanApplicationToEntity(application)
		if err != nil {
			return nil, err
		}

		entityApplications = append(entityApplications, entityApplication)
	}

	return entityApplications, nil
}

func (s *storageImpl) GetCountLoanLastSprRejectedApplicationsByUserID(ctx context.Context, userID string) (int16, error) {
	// Получаем последнюю заявку, которая была одобрена СПР
	approvedApp, err := s.PostgresClient.LoanApplication.Query().
		Where(
			loanapplication.UserID(userID),
			loanapplication.HasHistoryActionsWith(
				actionshistory.ActionTypeEQ(actionshistory.ActionType(consts.ScoringCheckApproved.String())),
			),
		).
		Order(ent.Desc(loanapplication.FieldCreateTime)).
		First(ctx)
	if err != nil {
		if !ent.IsNotFound(err) {
			return 0, err
		}
	}

	// Получаем заявки, которые были отвергнуты СПР после последней подтвержденной
	rejectedAppsQuery := s.PostgresClient.LoanApplication.Query().
		Where(
			loanapplication.UserID(userID),
			loanapplication.HasHistoryActionsWith(
				actionshistory.ActionTypeIn(
					actionshistory.ActionType(consts.ScoringCheckRejected.String()),
					actionshistory.ActionType(consts.ScoringCheckRejectedWithStop.String())),
			),
		)

	// Если была заявка, когда-то одобренная СПР
	if approvedApp != nil {
		rejectedAppsQuery.Where(loanapplication.CreateTimeGT(approvedApp.CreateTime))
	}

	rejectedApps, err := rejectedAppsQuery.All(ctx)
	if err != nil {
		if !ent.IsNotFound(err) {
			return 0, err
		}
	}

	// Количество последних отвергнутых СПР заявок
	rejectedAppsCount := len(rejectedApps)

	//nolint:gosec
	return int16(rejectedAppsCount), nil
}

func (s *storageImpl) GetAllApplicationsByStatusesAndUserIDExceptCurrentAppID(ctx context.Context, userID string, statuses []consts.LoanApplicationStatus, applicationID string) ([]*entity.LoanApplication, error) {
	applicationUUID, err := uuid.Parse(applicationID)
	if err != nil {
		return make([]*entity.LoanApplication, 0), err
	}

	applicationsQuery := s.PostgresClient.LoanApplication.Query().
		Where(
			loanapplication.UserID(userID),
			loanapplication.StatusIn(
				entity.MakeEntityStatusesToPostgres(statuses)...,
			),
			loanapplication.IDNEQ(applicationUUID),
		).Order(ent.Desc(loanapplication.FieldUpdateTime))

	applications, err := applicationsQuery.All(ctx)
	if err != nil {
		return nil, err
	}

	var entityApplications []*entity.LoanApplication
	for _, application := range applications {
		entityApplication, err := entity.MakeStorageLoanApplicationToEntity(application)
		if err != nil {
			return nil, err
		}
		entityApplications = append(entityApplications, entityApplication)
	}

	return entityApplications, nil
}

func (s *storageImpl) MakeLoanApplicationRefinancing(ctx context.Context, userID string, applicationID string, refinancingConditions *entity.RefinancingConditions, externalBankLoans []*entity.ExternalBankLoan) error {
	applicationUUID, err := uuid.Parse(applicationID)
	if err != nil {
		return err
	}

	tx, err := pg.MakeTxFn(s.PostgresClient)(ctx, &sql.TxOptions{Isolation: sql.LevelReadCommitted})
	if err != nil {
		return err
	}
	defer func() {
		if rbErr := tx.Rollback(); rbErr != nil && !errors.Is(rbErr, sql.ErrTxDone) {
			logs.FromContext(ctx).Err(rbErr).Msg("failed to rollback transaction")
		}
	}()

	err = tx.Client.LoanApplication.Update().
		Where(loanapplication.ID(applicationUUID)).
		SetType(loanapplication.Type(consts.LoanApplicationTypeRefinancing)).
		Exec(ctx)

	if err != nil {
		return fmt.Errorf("failed to update loan application %s: %w", applicationUUID, err)
	}

	// Insert external bank loans with tx
	_, err = s.CreateExternalBankLoansWithTransaction(ctx, tx, externalBankLoans, conversion.Ptr(applicationUUID))
	if err != nil {
		return err
	}

	err = tx.Client.RefinancingCondition.Create().
		SetTerm(refinancingConditions.Term).
		SetInterest(refinancingConditions.Interest).
		SetTotalAmount(refinancingConditions.TotalAmount).
		SetCreditAmount(refinancingConditions.CreditAmount).
		SetRefAmount(refinancingConditions.RefAmount).
		SetKdn(refinancingConditions.KDN).
		SetKdd(refinancingConditions.KDD).
		SetApplicationID(applicationUUID).
		Exec(ctx)
	if err != nil {
		return err
	}

	err = tx.Commit()
	if err != nil {
		return err
	}

	return nil
}

func (s *storageImpl) GetWaitingLoanApplications(ctx context.Context, updateTimeLT int, params entity.LoanAppParams) ([]*entity.LoanApplication, error) {
	outdatedTime := time.Now().Add(-time.Hour * time.Duration(updateTimeLT))

	var entityApplications []*entity.LoanApplication
	applicationsQuery := s.PostgresClient.LoanApplication.
		Query().
		Where(
			loanapplication.StatusEQ(loanapplication.Status(consts.LoanApplicationStatusWaiting)),
			loanapplication.UpdateTimeLT(outdatedTime),
			loanapplication.Not(
				loanapplication.HasHistoryActionsWith(
					actionshistory.CreateTimeGTE(outdatedTime),
				),
			),
		)

	// TODO: вынести в отдельную функцию
	if params.WithConditions {
		applicationsQuery = applicationsQuery.WithConditions()
	}
	if params.WithRefinancingConditions {
		applicationsQuery = applicationsQuery.WithRefinancingConditions()
	}
	if params.WithExternalBankLoans {
		applicationsQuery = applicationsQuery.WithExternalBankLoans()
	}
	if params.WithDocs {
		applicationsQuery = applicationsQuery.WithDocuments(func(query *ent.DocumentQuery) {
			query.WithApplication()
		})
	}
	if params.WithHistoryActions {
		applicationsQuery = applicationsQuery.WithHistoryActions()
	}
	if params.WithSurvey {
		applicationsQuery = applicationsQuery.WithSurvey()
	}

	applications, err := applicationsQuery.All(ctx)
	if err != nil {
		return nil, err
	}

	for _, application := range applications {
		entityApplication, err := entity.MakeStorageLoanApplicationToEntity(application)
		if err != nil {
			return nil, fmt.Errorf("waiting loan applications to entity, application - %+v, err - %v", application, err)
		}
		entityApplications = append(entityApplications, entityApplication)
	}

	return entityApplications, nil
}

func (s *storageImpl) UpdateClientRisks(ctx context.Context, applicationID string, clientRisks bool) error {
	applicationUUID, err := uuid.Parse(applicationID)
	if err != nil {
		return err
	}

	err = s.PostgresClient.LoanApplication.
		Update().
		Where(loanapplication.ID(applicationUUID)).
		SetClientRisks(clientRisks).
		Exec(ctx)
	if err != nil {
		return err
	}

	return nil
}
