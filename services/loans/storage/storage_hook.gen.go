// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package storage

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/loans/storage -i Storage -t ../../../etc/templates/hook.tmpl -o storage_hook.gen.go -l ""

import (
	"context"

	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx/transaction"
	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/loans/consts"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/storage/postgres/ent"
)

var _ Storage = (*StorageHook)(nil)

// StorageHook implements Storage interface wrapper
type StorageHook struct {
	Storage
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// AddScoringConditions implements Storage
func (_w *StorageHook) AddScoringConditions(ctx context.Context, applicationID string, conditions []*entity.LoanConditions) (lpa1 []*entity.LoanConditions, err error) {
	_params := []any{ctx, applicationID, conditions}
	defer _w._onPanic.Hook(_w.Storage, "AddScoringConditions", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "AddScoringConditions", _params)

	lpa1, err = _w.Storage.AddScoringConditions(_ctx, applicationID, conditions)
	_w._postCall.Hook(_ctx, _w.Storage, "AddScoringConditions", []any{lpa1, err})
	return lpa1, err
}

// Check implements Storage
func (_w *StorageHook) Check(ctx context.Context) (err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.Storage, "Check", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "Check", _params)

	err = _w.Storage.Check(_ctx)
	_w._postCall.Hook(_ctx, _w.Storage, "Check", []any{err})
	return err
}

// CreateExternalBankLoans implements Storage
func (_w *StorageHook) CreateExternalBankLoans(ctx context.Context, externalBankLoans []*entity.ExternalBankLoan, loanAppID *uuid.UUID) (epa1 []*entity.ExternalBankLoan, err error) {
	_params := []any{ctx, externalBankLoans, loanAppID}
	defer _w._onPanic.Hook(_w.Storage, "CreateExternalBankLoans", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "CreateExternalBankLoans", _params)

	epa1, err = _w.Storage.CreateExternalBankLoans(_ctx, externalBankLoans, loanAppID)
	_w._postCall.Hook(_ctx, _w.Storage, "CreateExternalBankLoans", []any{epa1, err})
	return epa1, err
}

// CreateExternalBankLoansWithTransaction implements Storage
func (_w *StorageHook) CreateExternalBankLoansWithTransaction(ctx context.Context, tx *transaction.Tx[ent.Client], externalBankLoans []*entity.ExternalBankLoan, loanAppID *uuid.UUID) (epa1 []*entity.ExternalBankLoan, err error) {
	_params := []any{ctx, tx, externalBankLoans, loanAppID}
	defer _w._onPanic.Hook(_w.Storage, "CreateExternalBankLoansWithTransaction", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "CreateExternalBankLoansWithTransaction", _params)

	epa1, err = _w.Storage.CreateExternalBankLoansWithTransaction(_ctx, tx, externalBankLoans, loanAppID)
	_w._postCall.Hook(_ctx, _w.Storage, "CreateExternalBankLoansWithTransaction", []any{epa1, err})
	return epa1, err
}

// CreateLoanApplication implements Storage
func (_w *StorageHook) CreateLoanApplication(ctx context.Context, req *entity.CreateLoanApplicationReq, termInterest *entity.LoanTermInterest, userID string, userIin string, purpose string, userFullName string) (lp1 *entity.LoanApplication, err error) {
	_params := []any{ctx, req, termInterest, userID, userIin, purpose, userFullName}
	defer _w._onPanic.Hook(_w.Storage, "CreateLoanApplication", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "CreateLoanApplication", _params)

	lp1, err = _w.Storage.CreateLoanApplication(_ctx, req, termInterest, userID, userIin, purpose, userFullName)
	_w._postCall.Hook(_ctx, _w.Storage, "CreateLoanApplication", []any{lp1, err})
	return lp1, err
}

// CreateLoanDisbursement implements Storage
func (_w *StorageHook) CreateLoanDisbursement(ctx context.Context, payload entity.LoanDisbursementEventPayload, cronStatus bool) (s1 string, err error) {
	_params := []any{ctx, payload, cronStatus}
	defer _w._onPanic.Hook(_w.Storage, "CreateLoanDisbursement", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "CreateLoanDisbursement", _params)

	s1, err = _w.Storage.CreateLoanDisbursement(_ctx, payload, cronStatus)
	_w._postCall.Hook(_ctx, _w.Storage, "CreateLoanDisbursement", []any{s1, err})
	return s1, err
}

// CreateNewTrackNumber implements Storage
func (_w *StorageHook) CreateNewTrackNumber(ctx context.Context, appID string) (tp1 *entity.TrackSprNumber, err error) {
	_params := []any{ctx, appID}
	defer _w._onPanic.Hook(_w.Storage, "CreateNewTrackNumber", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "CreateNewTrackNumber", _params)

	tp1, err = _w.Storage.CreateNewTrackNumber(_ctx, appID)
	_w._postCall.Hook(_ctx, _w.Storage, "CreateNewTrackNumber", []any{tp1, err})
	return tp1, err
}

// CreateOrUpdateControlRecord implements Storage
func (_w *StorageHook) CreateOrUpdateControlRecord(ctx context.Context, req *entity.DisbursementControl) (err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.Storage, "CreateOrUpdateControlRecord", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "CreateOrUpdateControlRecord", _params)

	err = _w.Storage.CreateOrUpdateControlRecord(_ctx, req)
	_w._postCall.Hook(_ctx, _w.Storage, "CreateOrUpdateControlRecord", []any{err})
	return err
}

// CreateUserExternalBankLoans implements Storage
func (_w *StorageHook) CreateUserExternalBankLoans(ctx context.Context, userExternalBankLoans []*entity.UserExternalBankLoan, loanAppID uuid.UUID) (upa1 []*entity.UserExternalBankLoan, err error) {
	_params := []any{ctx, userExternalBankLoans, loanAppID}
	defer _w._onPanic.Hook(_w.Storage, "CreateUserExternalBankLoans", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "CreateUserExternalBankLoans", _params)

	upa1, err = _w.Storage.CreateUserExternalBankLoans(_ctx, userExternalBankLoans, loanAppID)
	_w._postCall.Hook(_ctx, _w.Storage, "CreateUserExternalBankLoans", []any{upa1, err})
	return upa1, err
}

// CreateUserExternalBankLoansWithTransaction implements Storage
func (_w *StorageHook) CreateUserExternalBankLoansWithTransaction(ctx context.Context, tx *transaction.Tx[ent.Client], userExternalBankLoans []*entity.UserExternalBankLoan, loanAppID uuid.UUID) (upa1 []*entity.UserExternalBankLoan, err error) {
	_params := []any{ctx, tx, userExternalBankLoans, loanAppID}
	defer _w._onPanic.Hook(_w.Storage, "CreateUserExternalBankLoansWithTransaction", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "CreateUserExternalBankLoansWithTransaction", _params)

	upa1, err = _w.Storage.CreateUserExternalBankLoansWithTransaction(_ctx, tx, userExternalBankLoans, loanAppID)
	_w._postCall.Hook(_ctx, _w.Storage, "CreateUserExternalBankLoansWithTransaction", []any{upa1, err})
	return upa1, err
}

// GetAllApplicationsByStatusesAndUserIDExceptCurrentAppID implements Storage
func (_w *StorageHook) GetAllApplicationsByStatusesAndUserIDExceptCurrentAppID(ctx context.Context, userID string, statuses []consts.LoanApplicationStatus, applicationID string) (lpa1 []*entity.LoanApplication, err error) {
	_params := []any{ctx, userID, statuses, applicationID}
	defer _w._onPanic.Hook(_w.Storage, "GetAllApplicationsByStatusesAndUserIDExceptCurrentAppID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetAllApplicationsByStatusesAndUserIDExceptCurrentAppID", _params)

	lpa1, err = _w.Storage.GetAllApplicationsByStatusesAndUserIDExceptCurrentAppID(_ctx, userID, statuses, applicationID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetAllApplicationsByStatusesAndUserIDExceptCurrentAppID", []any{lpa1, err})
	return lpa1, err
}

// GetAllDocumentsByTypeAndAppID implements Storage
func (_w *StorageHook) GetAllDocumentsByTypeAndAppID(ctx context.Context, docType consts.DocumentType, appID string) (dpa1 []*entity.Document, err error) {
	_params := []any{ctx, docType, appID}
	defer _w._onPanic.Hook(_w.Storage, "GetAllDocumentsByTypeAndAppID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetAllDocumentsByTypeAndAppID", _params)

	dpa1, err = _w.Storage.GetAllDocumentsByTypeAndAppID(_ctx, docType, appID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetAllDocumentsByTypeAndAppID", []any{dpa1, err})
	return dpa1, err
}

// GetCountLoanLastSprRejectedApplicationsByUserID implements Storage
func (_w *StorageHook) GetCountLoanLastSprRejectedApplicationsByUserID(ctx context.Context, userID string) (i1 int16, err error) {
	_params := []any{ctx, userID}
	defer _w._onPanic.Hook(_w.Storage, "GetCountLoanLastSprRejectedApplicationsByUserID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetCountLoanLastSprRejectedApplicationsByUserID", _params)

	i1, err = _w.Storage.GetCountLoanLastSprRejectedApplicationsByUserID(_ctx, userID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetCountLoanLastSprRejectedApplicationsByUserID", []any{i1, err})
	return i1, err
}

// GetDisbursementControlRecord implements Storage
func (_w *StorageHook) GetDisbursementControlRecord(ctx context.Context) (dp1 *entity.DisbursementControl, err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.Storage, "GetDisbursementControlRecord", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetDisbursementControlRecord", _params)

	dp1, err = _w.Storage.GetDisbursementControlRecord(_ctx)
	_w._postCall.Hook(_ctx, _w.Storage, "GetDisbursementControlRecord", []any{dp1, err})
	return dp1, err
}

// GetDocumentByDocID implements Storage
func (_w *StorageHook) GetDocumentByDocID(ctx context.Context, docID string) (dp1 *entity.Document, err error) {
	_params := []any{ctx, docID}
	defer _w._onPanic.Hook(_w.Storage, "GetDocumentByDocID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetDocumentByDocID", _params)

	dp1, err = _w.Storage.GetDocumentByDocID(_ctx, docID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetDocumentByDocID", []any{dp1, err})
	return dp1, err
}

// GetDocumentByTypeAndAppID implements Storage
func (_w *StorageHook) GetDocumentByTypeAndAppID(ctx context.Context, docType consts.DocumentType, appID string) (dp1 *entity.Document, err error) {
	_params := []any{ctx, docType, appID}
	defer _w._onPanic.Hook(_w.Storage, "GetDocumentByTypeAndAppID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetDocumentByTypeAndAppID", _params)

	dp1, err = _w.Storage.GetDocumentByTypeAndAppID(_ctx, docType, appID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetDocumentByTypeAndAppID", []any{dp1, err})
	return dp1, err
}

// GetHistoryActionsByApplicationID implements Storage
func (_w *StorageHook) GetHistoryActionsByApplicationID(ctx context.Context, applicationID string) (h1 entity.HistoryActions, err error) {
	_params := []any{ctx, applicationID}
	defer _w._onPanic.Hook(_w.Storage, "GetHistoryActionsByApplicationID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetHistoryActionsByApplicationID", _params)

	h1, err = _w.Storage.GetHistoryActionsByApplicationID(_ctx, applicationID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetHistoryActionsByApplicationID", []any{h1, err})
	return h1, err
}

// GetLatestTrackNumberByAppID implements Storage
func (_w *StorageHook) GetLatestTrackNumberByAppID(ctx context.Context, appID string) (tp1 *entity.TrackSprNumber, err error) {
	_params := []any{ctx, appID}
	defer _w._onPanic.Hook(_w.Storage, "GetLatestTrackNumberByAppID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetLatestTrackNumberByAppID", _params)

	tp1, err = _w.Storage.GetLatestTrackNumberByAppID(_ctx, appID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetLatestTrackNumberByAppID", []any{tp1, err})
	return tp1, err
}

// GetLoanApplicationByID implements Storage
func (_w *StorageHook) GetLoanApplicationByID(ctx context.Context, applicationID string, params entity.LoanAppParams) (lp1 *entity.LoanApplication, err error) {
	_params := []any{ctx, applicationID, params}
	defer _w._onPanic.Hook(_w.Storage, "GetLoanApplicationByID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetLoanApplicationByID", _params)

	lp1, err = _w.Storage.GetLoanApplicationByID(_ctx, applicationID, params)
	_w._postCall.Hook(_ctx, _w.Storage, "GetLoanApplicationByID", []any{lp1, err})
	return lp1, err
}

// GetLoanApplicationsByIDsAndStatuses implements Storage
func (_w *StorageHook) GetLoanApplicationsByIDsAndStatuses(ctx context.Context, statuses []consts.LoanApplicationStatus, applicationIDs []string) (lpa1 []*entity.LoanApplication, err error) {
	_params := []any{ctx, statuses, applicationIDs}
	defer _w._onPanic.Hook(_w.Storage, "GetLoanApplicationsByIDsAndStatuses", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetLoanApplicationsByIDsAndStatuses", _params)

	lpa1, err = _w.Storage.GetLoanApplicationsByIDsAndStatuses(_ctx, statuses, applicationIDs)
	_w._postCall.Hook(_ctx, _w.Storage, "GetLoanApplicationsByIDsAndStatuses", []any{lpa1, err})
	return lpa1, err
}

// GetLoanApplicationsByStatuses implements Storage
func (_w *StorageHook) GetLoanApplicationsByStatuses(ctx context.Context, statuses []consts.LoanApplicationStatus, params entity.LoanAppParams) (lpa1 []*entity.LoanApplication, err error) {
	_params := []any{ctx, statuses, params}
	defer _w._onPanic.Hook(_w.Storage, "GetLoanApplicationsByStatuses", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetLoanApplicationsByStatuses", _params)

	lpa1, err = _w.Storage.GetLoanApplicationsByStatuses(_ctx, statuses, params)
	_w._postCall.Hook(_ctx, _w.Storage, "GetLoanApplicationsByStatuses", []any{lpa1, err})
	return lpa1, err
}

// GetLoanApplicationsByStatusesAndColvirReferenceID implements Storage
func (_w *StorageHook) GetLoanApplicationsByStatusesAndColvirReferenceID(ctx context.Context, colvirReferenceID string, statuses []consts.LoanApplicationStatus) (lp1 *entity.LoanApplication, err error) {
	_params := []any{ctx, colvirReferenceID, statuses}
	defer _w._onPanic.Hook(_w.Storage, "GetLoanApplicationsByStatusesAndColvirReferenceID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetLoanApplicationsByStatusesAndColvirReferenceID", _params)

	lp1, err = _w.Storage.GetLoanApplicationsByStatusesAndColvirReferenceID(_ctx, colvirReferenceID, statuses)
	_w._postCall.Hook(_ctx, _w.Storage, "GetLoanApplicationsByStatusesAndColvirReferenceID", []any{lp1, err})
	return lp1, err
}

// GetLoanApplicationsByStatusesAndUserID implements Storage
func (_w *StorageHook) GetLoanApplicationsByStatusesAndUserID(ctx context.Context, userID string, statuses []consts.LoanApplicationStatus, params entity.LoanAppParams) (lpa1 []*entity.LoanApplication, err error) {
	_params := []any{ctx, userID, statuses, params}
	defer _w._onPanic.Hook(_w.Storage, "GetLoanApplicationsByStatusesAndUserID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetLoanApplicationsByStatusesAndUserID", _params)

	lpa1, err = _w.Storage.GetLoanApplicationsByStatusesAndUserID(_ctx, userID, statuses, params)
	_w._postCall.Hook(_ctx, _w.Storage, "GetLoanApplicationsByStatusesAndUserID", []any{lpa1, err})
	return lpa1, err
}

// GetLoanDisbursementByID implements Storage
func (_w *StorageHook) GetLoanDisbursementByID(ctx context.Context, loanDisbursementID string) (lp1 *entity.LoanDisbursement, err error) {
	_params := []any{ctx, loanDisbursementID}
	defer _w._onPanic.Hook(_w.Storage, "GetLoanDisbursementByID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetLoanDisbursementByID", _params)

	lp1, err = _w.Storage.GetLoanDisbursementByID(_ctx, loanDisbursementID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetLoanDisbursementByID", []any{lp1, err})
	return lp1, err
}

// GetLoanDisbursementNotCompleted implements Storage
func (_w *StorageHook) GetLoanDisbursementNotCompleted(ctx context.Context, cronStatus bool) (lpa1 []*entity.LoanDisbursementEventPayload, err error) {
	_params := []any{ctx, cronStatus}
	defer _w._onPanic.Hook(_w.Storage, "GetLoanDisbursementNotCompleted", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetLoanDisbursementNotCompleted", _params)

	lpa1, err = _w.Storage.GetLoanDisbursementNotCompleted(_ctx, cronStatus)
	_w._postCall.Hook(_ctx, _w.Storage, "GetLoanDisbursementNotCompleted", []any{lpa1, err})
	return lpa1, err
}

// GetNextRRNCounter implements Storage
func (_w *StorageHook) GetNextRRNCounter(ctx context.Context) (u1 uint64, err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.Storage, "GetNextRRNCounter", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetNextRRNCounter", _params)

	u1, err = _w.Storage.GetNextRRNCounter(_ctx)
	_w._postCall.Hook(_ctx, _w.Storage, "GetNextRRNCounter", []any{u1, err})
	return u1, err
}

// GetOutdatedLoanApplications implements Storage
func (_w *StorageHook) GetOutdatedLoanApplications(ctx context.Context, daysToExpire int) (lpa1 []*entity.LoanApplication, err error) {
	_params := []any{ctx, daysToExpire}
	defer _w._onPanic.Hook(_w.Storage, "GetOutdatedLoanApplications", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetOutdatedLoanApplications", _params)

	lpa1, err = _w.Storage.GetOutdatedLoanApplications(_ctx, daysToExpire)
	_w._postCall.Hook(_ctx, _w.Storage, "GetOutdatedLoanApplications", []any{lpa1, err})
	return lpa1, err
}

// GetSurvey implements Storage
func (_w *StorageHook) GetSurvey(ctx context.Context, req *entity.GetSurveyReq, userID uuid.UUID) (gp1 *entity.GetSurveyResult, err error) {
	_params := []any{ctx, req, userID}
	defer _w._onPanic.Hook(_w.Storage, "GetSurvey", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetSurvey", _params)

	gp1, err = _w.Storage.GetSurvey(_ctx, req, userID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetSurvey", []any{gp1, err})
	return gp1, err
}

// GetSurveyByApplicationID implements Storage
func (_w *StorageHook) GetSurveyByApplicationID(ctx context.Context, applicationID string) (gp1 *entity.GetSurveyResult, err error) {
	_params := []any{ctx, applicationID}
	defer _w._onPanic.Hook(_w.Storage, "GetSurveyByApplicationID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetSurveyByApplicationID", _params)

	gp1, err = _w.Storage.GetSurveyByApplicationID(_ctx, applicationID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetSurveyByApplicationID", []any{gp1, err})
	return gp1, err
}

// GetSurveyByID implements Storage
func (_w *StorageHook) GetSurveyByID(ctx context.Context, ID string) (gp1 *entity.GetSurveyResult, err error) {
	_params := []any{ctx, ID}
	defer _w._onPanic.Hook(_w.Storage, "GetSurveyByID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetSurveyByID", _params)

	gp1, err = _w.Storage.GetSurveyByID(_ctx, ID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetSurveyByID", []any{gp1, err})
	return gp1, err
}

// GetTrackDataByTrackID implements Storage
func (_w *StorageHook) GetTrackDataByTrackID(ctx context.Context, trackID uint64) (tp1 *entity.TrackSprNumber, err error) {
	_params := []any{ctx, trackID}
	defer _w._onPanic.Hook(_w.Storage, "GetTrackDataByTrackID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetTrackDataByTrackID", _params)

	tp1, err = _w.Storage.GetTrackDataByTrackID(_ctx, trackID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetTrackDataByTrackID", []any{tp1, err})
	return tp1, err
}

// GetWaitingLoanApplications implements Storage
func (_w *StorageHook) GetWaitingLoanApplications(ctx context.Context, updateTimeLT int, params entity.LoanAppParams) (lpa1 []*entity.LoanApplication, err error) {
	_params := []any{ctx, updateTimeLT, params}
	defer _w._onPanic.Hook(_w.Storage, "GetWaitingLoanApplications", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetWaitingLoanApplications", _params)

	lpa1, err = _w.Storage.GetWaitingLoanApplications(_ctx, updateTimeLT, params)
	_w._postCall.Hook(_ctx, _w.Storage, "GetWaitingLoanApplications", []any{lpa1, err})
	return lpa1, err
}

// MakeLoanApplicationRefinancing implements Storage
func (_w *StorageHook) MakeLoanApplicationRefinancing(ctx context.Context, userID string, applicationID string, refinancingConditions *entity.RefinancingConditions, externalBankLoans []*entity.ExternalBankLoan) (err error) {
	_params := []any{ctx, userID, applicationID, refinancingConditions, externalBankLoans}
	defer _w._onPanic.Hook(_w.Storage, "MakeLoanApplicationRefinancing", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "MakeLoanApplicationRefinancing", _params)

	err = _w.Storage.MakeLoanApplicationRefinancing(_ctx, userID, applicationID, refinancingConditions, externalBankLoans)
	_w._postCall.Hook(_ctx, _w.Storage, "MakeLoanApplicationRefinancing", []any{err})
	return err
}

// ReserveNextCounterForPrefix implements Storage
func (_w *StorageHook) ReserveNextCounterForPrefix(ctx context.Context, prefix string) (i1 int64, err error) {
	_params := []any{ctx, prefix}
	defer _w._onPanic.Hook(_w.Storage, "ReserveNextCounterForPrefix", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "ReserveNextCounterForPrefix", _params)

	i1, err = _w.Storage.ReserveNextCounterForPrefix(_ctx, prefix)
	_w._postCall.Hook(_ctx, _w.Storage, "ReserveNextCounterForPrefix", []any{i1, err})
	return i1, err
}

// SaveDocument implements Storage
func (_w *StorageHook) SaveDocument(ctx context.Context, req *entity.Document) (dp1 *entity.Document, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.Storage, "SaveDocument", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "SaveDocument", _params)

	dp1, err = _w.Storage.SaveDocument(_ctx, req)
	_w._postCall.Hook(_ctx, _w.Storage, "SaveDocument", []any{dp1, err})
	return dp1, err
}

// SaveDocuments implements Storage
func (_w *StorageHook) SaveDocuments(ctx context.Context, req []*entity.Document) (err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.Storage, "SaveDocuments", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "SaveDocuments", _params)

	err = _w.Storage.SaveDocuments(_ctx, req)
	_w._postCall.Hook(_ctx, _w.Storage, "SaveDocuments", []any{err})
	return err
}

// SaveHistoryAction implements Storage
func (_w *StorageHook) SaveHistoryAction(ctx context.Context, req *entity.HistoryAction) (err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.Storage, "SaveHistoryAction", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "SaveHistoryAction", _params)

	err = _w.Storage.SaveHistoryAction(_ctx, req)
	_w._postCall.Hook(_ctx, _w.Storage, "SaveHistoryAction", []any{err})
	return err
}

// SaveHistoryActions implements Storage
func (_w *StorageHook) SaveHistoryActions(ctx context.Context, req entity.HistoryActions) (err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.Storage, "SaveHistoryActions", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "SaveHistoryActions", _params)

	err = _w.Storage.SaveHistoryActions(_ctx, req)
	_w._postCall.Hook(_ctx, _w.Storage, "SaveHistoryActions", []any{err})
	return err
}

// SaveSurvey implements Storage
func (_w *StorageHook) SaveSurvey(ctx context.Context, req *entity.SaveSurveyReq, userID uuid.UUID) (sp1 *entity.SaveSurveyResult, err error) {
	_params := []any{ctx, req, userID}
	defer _w._onPanic.Hook(_w.Storage, "SaveSurvey", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "SaveSurvey", _params)

	sp1, err = _w.Storage.SaveSurvey(_ctx, req, userID)
	_w._postCall.Hook(_ctx, _w.Storage, "SaveSurvey", []any{sp1, err})
	return sp1, err
}

// SetBankStatementApproveStatus implements Storage
func (_w *StorageHook) SetBankStatementApproveStatus(ctx context.Context, docID string, approveStatus consts.BankStatementApproveStatus) (err error) {
	_params := []any{ctx, docID, approveStatus}
	defer _w._onPanic.Hook(_w.Storage, "SetBankStatementApproveStatus", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "SetBankStatementApproveStatus", _params)

	err = _w.Storage.SetBankStatementApproveStatus(_ctx, docID, approveStatus)
	_w._postCall.Hook(_ctx, _w.Storage, "SetBankStatementApproveStatus", []any{err})
	return err
}

// SetBankStatementIsActive implements Storage
func (_w *StorageHook) SetBankStatementIsActive(ctx context.Context, docID string, isActive bool) (err error) {
	_params := []any{ctx, docID, isActive}
	defer _w._onPanic.Hook(_w.Storage, "SetBankStatementIsActive", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "SetBankStatementIsActive", _params)

	err = _w.Storage.SetBankStatementIsActive(_ctx, docID, isActive)
	_w._postCall.Hook(_ctx, _w.Storage, "SetBankStatementIsActive", []any{err})
	return err
}

// SetColvirFields implements Storage
func (_w *StorageHook) SetColvirFields(ctx context.Context, applicationID string, req *entity.LoanApplication) (err error) {
	_params := []any{ctx, applicationID, req}
	defer _w._onPanic.Hook(_w.Storage, "SetColvirFields", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "SetColvirFields", _params)

	err = _w.Storage.SetColvirFields(_ctx, applicationID, req)
	_w._postCall.Hook(_ctx, _w.Storage, "SetColvirFields", []any{err})
	return err
}

// SetConditionsFlags implements Storage
func (_w *StorageHook) SetConditionsFlags(ctx context.Context, applicationID string, conditionID string, params entity.LoanConditionsParams) (err error) {
	_params := []any{ctx, applicationID, conditionID, params}
	defer _w._onPanic.Hook(_w.Storage, "SetConditionsFlags", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "SetConditionsFlags", _params)

	err = _w.Storage.SetConditionsFlags(_ctx, applicationID, conditionID, params)
	_w._postCall.Hook(_ctx, _w.Storage, "SetConditionsFlags", []any{err})
	return err
}

// SetSignedDocID implements Storage
func (_w *StorageHook) SetSignedDocID(ctx context.Context, docID string, signedDocID string) (err error) {
	_params := []any{ctx, docID, signedDocID}
	defer _w._onPanic.Hook(_w.Storage, "SetSignedDocID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "SetSignedDocID", _params)

	err = _w.Storage.SetSignedDocID(_ctx, docID, signedDocID)
	_w._postCall.Hook(_ctx, _w.Storage, "SetSignedDocID", []any{err})
	return err
}

// UpdateClientRisks implements Storage
func (_w *StorageHook) UpdateClientRisks(ctx context.Context, applicationID string, clientRisks bool) (err error) {
	_params := []any{ctx, applicationID, clientRisks}
	defer _w._onPanic.Hook(_w.Storage, "UpdateClientRisks", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "UpdateClientRisks", _params)

	err = _w.Storage.UpdateClientRisks(_ctx, applicationID, clientRisks)
	_w._postCall.Hook(_ctx, _w.Storage, "UpdateClientRisks", []any{err})
	return err
}

// UpdateDisbursementAccountID implements Storage
func (_w *StorageHook) UpdateDisbursementAccountID(ctx context.Context, applicationID string, disbursementAccountID string) (err error) {
	_params := []any{ctx, applicationID, disbursementAccountID}
	defer _w._onPanic.Hook(_w.Storage, "UpdateDisbursementAccountID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "UpdateDisbursementAccountID", _params)

	err = _w.Storage.UpdateDisbursementAccountID(_ctx, applicationID, disbursementAccountID)
	_w._postCall.Hook(_ctx, _w.Storage, "UpdateDisbursementAccountID", []any{err})
	return err
}

// UpdateDocumentByTypeAndAppID implements Storage
func (_w *StorageHook) UpdateDocumentByTypeAndAppID(ctx context.Context, docType consts.DocumentType, appID string, req *entity.Document) (err error) {
	_params := []any{ctx, docType, appID, req}
	defer _w._onPanic.Hook(_w.Storage, "UpdateDocumentByTypeAndAppID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "UpdateDocumentByTypeAndAppID", _params)

	err = _w.Storage.UpdateDocumentByTypeAndAppID(_ctx, docType, appID, req)
	_w._postCall.Hook(_ctx, _w.Storage, "UpdateDocumentByTypeAndAppID", []any{err})
	return err
}

// UpdateExternalBankLoans implements Storage
func (_w *StorageHook) UpdateExternalBankLoans(ctx context.Context, externalBankLoans []*entity.ExternalBankLoan) (err error) {
	_params := []any{ctx, externalBankLoans}
	defer _w._onPanic.Hook(_w.Storage, "UpdateExternalBankLoans", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "UpdateExternalBankLoans", _params)

	err = _w.Storage.UpdateExternalBankLoans(_ctx, externalBankLoans)
	_w._postCall.Hook(_ctx, _w.Storage, "UpdateExternalBankLoans", []any{err})
	return err
}

// UpdateLoanApplication implements Storage
func (_w *StorageHook) UpdateLoanApplication(ctx context.Context, req *entity.UpdateLoanApplicationReq, termInterest *entity.LoanTermInterest, purposeID string) (err error) {
	_params := []any{ctx, req, termInterest, purposeID}
	defer _w._onPanic.Hook(_w.Storage, "UpdateLoanApplication", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "UpdateLoanApplication", _params)

	err = _w.Storage.UpdateLoanApplication(_ctx, req, termInterest, purposeID)
	_w._postCall.Hook(_ctx, _w.Storage, "UpdateLoanApplication", []any{err})
	return err
}

// UpdateLoanApplicationStatus implements Storage
func (_w *StorageHook) UpdateLoanApplicationStatus(ctx context.Context, applicationID string, newStatus consts.LoanApplicationStatus, userID string, userIin string) (err error) {
	_params := []any{ctx, applicationID, newStatus, userID, userIin}
	defer _w._onPanic.Hook(_w.Storage, "UpdateLoanApplicationStatus", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "UpdateLoanApplicationStatus", _params)

	err = _w.Storage.UpdateLoanApplicationStatus(_ctx, applicationID, newStatus, userID, userIin)
	_w._postCall.Hook(_ctx, _w.Storage, "UpdateLoanApplicationStatus", []any{err})
	return err
}

// UpdateLoanApplicationStatusBatch implements Storage
func (_w *StorageHook) UpdateLoanApplicationStatusBatch(ctx context.Context, applicationIDs []string, status consts.LoanApplicationStatus) (i1 int64, err error) {
	_params := []any{ctx, applicationIDs, status}
	defer _w._onPanic.Hook(_w.Storage, "UpdateLoanApplicationStatusBatch", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "UpdateLoanApplicationStatusBatch", _params)

	i1, err = _w.Storage.UpdateLoanApplicationStatusBatch(_ctx, applicationIDs, status)
	_w._postCall.Hook(_ctx, _w.Storage, "UpdateLoanApplicationStatusBatch", []any{i1, err})
	return i1, err
}

// UpdateLoanDisbursement implements Storage
func (_w *StorageHook) UpdateLoanDisbursement(ctx context.Context, loanDisbursement *entity.LoanDisbursement) (err error) {
	_params := []any{ctx, loanDisbursement}
	defer _w._onPanic.Hook(_w.Storage, "UpdateLoanDisbursement", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "UpdateLoanDisbursement", _params)

	err = _w.Storage.UpdateLoanDisbursement(_ctx, loanDisbursement)
	_w._postCall.Hook(_ctx, _w.Storage, "UpdateLoanDisbursement", []any{err})
	return err
}

// UpdateLoanDisbursementCronStatus implements Storage
func (_w *StorageHook) UpdateLoanDisbursementCronStatus(ctx context.Context, disbursementID string, cronStatus bool) (err error) {
	_params := []any{ctx, disbursementID, cronStatus}
	defer _w._onPanic.Hook(_w.Storage, "UpdateLoanDisbursementCronStatus", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "UpdateLoanDisbursementCronStatus", _params)

	err = _w.Storage.UpdateLoanDisbursementCronStatus(_ctx, disbursementID, cronStatus)
	_w._postCall.Hook(_ctx, _w.Storage, "UpdateLoanDisbursementCronStatus", []any{err})
	return err
}

// NewStorageHook returns StorageHook
func NewStorageHook(object Storage, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *StorageHook {
	return &StorageHook{
		Storage:     object,
		_beforeCall: beforeCall,
		_postCall:   postCall,
		_onPanic:    onPanic,
	}
}
