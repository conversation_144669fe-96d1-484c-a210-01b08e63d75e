package entity

import (
	"context"
	"fmt"
	"strconv"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/helpers/locale"
	"git.redmadrobot.com/zaman/backend/zaman/pkg/helpers/localizedvalue"
	"git.redmadrobot.com/zaman/backend/zaman/services/loans/consts"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/loans"
)

var (
	refinancingRefLabel = localizedvalue.LocalizedValue{
		KZ: "Басқа банктердегі шотқа",
		RU: "На счёт в другие банки",
	}

	refinancingCreditLabel = localizedvalue.LocalizedValue{
		KZ: "Ағымдағы шотыңызға",
		RU: "На ваш текущий счёт",
	}

	refinancingHint = localizedvalue.LocalizedValue{
		KZ: "Қаржыландыруды растау үшін басқа банктердегі несиелерді мерзімінен бұрын өтеу туралы өтініш беріңіз",
		RU: "Для подтверждения финансирования подайте заявление о досрочном погашении кредитов в других банках",
	}
)

type (
	PostEdsBtsDataReq struct {
		ApplicationID string
		Code          string
	}

	PostEdsBtsDataResult struct {
		ProductType     string
		Title           string
		Amount          *LoanAmountData
		Term            string
		SubTitle        string
		RefinancingData *RefinancingData
	}

	LoanAmountData struct {
		Value    string
		Currency string
	}

	CreditData struct {
		Amount LoanAmountData
		Label  string
	}

	RefinancingData struct {
		Ref    CreditData
		Credit CreditData
		Hint   string
	}

	// storage LoanDisbursementPayload
	LoanDisbursementPayload struct {
		ApplicationID string
		RequestID     string
		Origin        string
	}
)

// MakePostEdsBtsDataPbToEntity создает объект из pb.PostEdsBtsDataReq в PostEdsBtsDataReq для передачи в usecase
func MakePostEdsBtsDataPbToEntity(req *pb.PostEdsBtsDataReq) *PostEdsBtsDataReq {
	if req == nil {
		return &PostEdsBtsDataReq{}
	}

	return &PostEdsBtsDataReq{
		ApplicationID: req.GetApplicationID(),
		Code:          req.GetCode(),
	}
}

// MakePostEdsBtsDataEntityToPb создает объект из PostEdsBtsData в pb.PostEdsBtsDataResp для возврата ответа из сервиса
func MakePostEdsBtsDataEntityToPb(res *PostEdsBtsDataResult) *pb.PostEdsBtsDataResp {
	return &pb.PostEdsBtsDataResp{
		Title: res.Title,
		Amount: &pb.Amount{
			Value:        res.Amount.Value,
			CurrencyCode: res.Amount.Currency,
		},
		Term:     res.Term,
		Subtitle: res.SubTitle,
	}
}

func MakeBtsDataResultFromLoanConditions(ctx context.Context, loanApp *LoanApplication, loc locale.Locale, financeInfo *DictionaryFinance, isBreak bool) (*PostEdsBtsDataResult, error) {
	logger := logs.FromContext(ctx)
	amount, err := loanApp.GetAmount(
		WithGetCreditAmountForRefinancing(true), // по ФТ нужно получить значение поля CreditAmount, вместо TotalAmount
	)
	if err != nil {
		return nil, err
	}

	result := &PostEdsBtsDataResult{
		ProductType: loanApp.Type.String(),
		Amount: &LoanAmountData{
			Value:    strconv.FormatUint(uint64(amount), 10),
			Currency: consts.DefaultKazakhstanCurrency,
		},
	}

	// Сейчас работаем с текстами в константах
	switch loc {
	case locale.Ru:
		if isBreak {
			result.Title = financeInfo.BreakTitle.RU
		} else {
			result.Title = financeInfo.Title.RU
		}
		result.Term = fmt.Sprintf(financeInfo.TermsFmt.RU, loanApp.FinallyChosenConditions.Term)
		result.SubTitle = financeInfo.InfoSubTitle.RU
	case locale.Kk:
		if isBreak {
			result.Title = financeInfo.BreakTitle.KZ
		} else {
			result.Title = financeInfo.Title.KZ
		}
		result.Term = fmt.Sprintf(financeInfo.TermsFmt.KZ, loanApp.FinallyChosenConditions.Term)
		result.SubTitle = financeInfo.InfoSubTitle.KZ
	case locale.En:
		logger.Warn().Msg("locale.En is not supported, fallback to locale.Kk")
		if isBreak {
			result.Title = financeInfo.BreakTitle.KZ
		} else {
			result.Title = financeInfo.Title.KZ
		}
		result.Term = fmt.Sprintf(financeInfo.TermsFmt.KZ, loanApp.FinallyChosenConditions.Term)
		result.SubTitle = financeInfo.InfoSubTitle.KZ
	}

	if loanApp.IsRefinancing() {
		var externalBankLoansAmount uint32
		for _, loan := range loanApp.ExternalBankLoans {
			externalBankLoansAmount += loan.OutstandingAmount
		}

		refLabel, err := refinancingRefLabel.GetLocalizedValue(loc)
		if err != nil {
			return nil, err
		}

		creditLabel, err := refinancingCreditLabel.GetLocalizedValue(loc)
		if err != nil {
			return nil, err
		}

		hint, err := refinancingHint.GetLocalizedValue(loc)
		if err != nil {
			return nil, err
		}

		refinancingData := &RefinancingData{
			Ref: CreditData{
				Amount: LoanAmountData{
					Value:    strconv.FormatUint(uint64(externalBankLoansAmount), 10),
					Currency: consts.DefaultKazakhstanCurrency,
				},
				Label: refLabel,
			},
			Credit: CreditData{
				Amount: LoanAmountData{
					Value:    strconv.FormatUint(uint64(amount), 10),
					Currency: consts.DefaultKazakhstanCurrency,
				},
				Label: creditLabel,
			},
			Hint: hint,
		}

		result.RefinancingData = refinancingData
	}

	return result, nil
}
