// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/specs/proto/notifications (interfaces: NotificationsClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"

	notifications "git.redmadrobot.com/zaman/backend/zaman/specs/proto/notifications"
)

// MockNotificationsClient is a mock of NotificationsClient interface.
type MockNotificationsClient struct {
	ctrl     *gomock.Controller
	recorder *MockNotificationsClientMockRecorder
}

// MockNotificationsClientMockRecorder is the mock recorder for MockNotificationsClient.
type MockNotificationsClientMockRecorder struct {
	mock *MockNotificationsClient
}

// NewMockNotificationsClient creates a new mock instance.
func NewMockNotificationsClient(ctrl *gomock.Controller) *MockNotificationsClient {
	mock := &MockNotificationsClient{ctrl: ctrl}
	mock.recorder = &MockNotificationsClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNotificationsClient) EXPECT() *MockNotificationsClientMockRecorder {
	return m.recorder
}

// HealthCheck mocks base method.
func (m *MockNotificationsClient) HealthCheck(arg0 context.Context, arg1 *notifications.HealthCheckReq, arg2 ...grpc.CallOption) (*notifications.HealthCheckResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HealthCheck", varargs...)
	ret0, _ := ret[0].(*notifications.HealthCheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockNotificationsClientMockRecorder) HealthCheck(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockNotificationsClient)(nil).HealthCheck), varargs...)
}

// NotificationsByType mocks base method.
func (m *MockNotificationsClient) NotificationsByType(arg0 context.Context, arg1 *notifications.NotificationsByTypeReq, arg2 ...grpc.CallOption) (*notifications.NotificationsByTypeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "NotificationsByType", varargs...)
	ret0, _ := ret[0].(*notifications.NotificationsByTypeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NotificationsByType indicates an expected call of NotificationsByType.
func (mr *MockNotificationsClientMockRecorder) NotificationsByType(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NotificationsByType", reflect.TypeOf((*MockNotificationsClient)(nil).NotificationsByType), varargs...)
}

// SendSMS mocks base method.
func (m *MockNotificationsClient) SendSMS(arg0 context.Context, arg1 *notifications.SendSmsReq, arg2 ...grpc.CallOption) (*notifications.SendSmsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendSMS", varargs...)
	ret0, _ := ret[0].(*notifications.SendSmsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendSMS indicates an expected call of SendSMS.
func (mr *MockNotificationsClientMockRecorder) SendSMS(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendSMS", reflect.TypeOf((*MockNotificationsClient)(nil).SendSMS), varargs...)
}
