// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package mocks

import (
	"testing"

	"github.com/golang/mock/gomock"
)

type Mocks struct {
	GRPC      GRPC
	Providers Providers
}

type GRPC struct {
	Users            *MockUsersClient
	Colvirbridge     *MockColvirbridgeClient
	Taskmanager      *MockTaskmanagerClient
	Pkbbridge        *MockPkbbridgeClient
	Dictionary       *MockDictionaryClient
	Processingbridge *MockProcessingbridgeClient
	Notifications    *MockNotificationsClient
	Amlbridge        *MockAmlbridgeClient
	Documents        *MockDocumentsClient
	Antifraud        *MockAntifraudClient
}

type Providers struct {
}

func NewMocks(t *testing.T) *Mocks {
	return &Mocks{
		GRPC: GRPC{
			Users:            NewMockUsersClient(gomock.NewController(t)),
			Colvirbridge:     NewMockColvirbridgeClient(gomock.NewController(t)),
			Taskmanager:      NewMockTaskmanagerClient(gomock.NewController(t)),
			Pkbbridge:        NewMockPkbbridgeClient(gomock.NewController(t)),
			Dictionary:       NewMockDictionaryClient(gomock.NewController(t)),
			Processingbridge: NewMockProcessingbridgeClient(gomock.NewController(t)),
			Notifications:    NewMockNotificationsClient(gomock.NewController(t)),
			Amlbridge:        NewMockAmlbridgeClient(gomock.NewController(t)),
			Documents:        NewMockDocumentsClient(gomock.NewController(t)),
			Antifraud:        NewMockAntifraudClient(gomock.NewController(t)),
		},
		Providers: Providers{},
	}
}
