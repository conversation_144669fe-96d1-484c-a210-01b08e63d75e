// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package server

import (
	"git.redmadrobot.com/backend-go/rmr-pkg/grpcx"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"go.opentelemetry.io/otel/propagation"

	cardsaccounts "git.redmadrobot.com/zaman/backend/zaman/config/services/cards-accounts"

	"google.golang.org/grpc"
	"google.golang.org/grpc/health/grpc_health_v1"

	"git.redmadrobot.com/zaman/backend/zaman/services/cards-accounts/usecase"
	pb "git.redmadrobot.com/zaman/backend/zaman/specs/proto/cards-accounts"
)

type Server struct {
	pb.UnimplementedCardsaccountsServer

	cfg     *cardsaccounts.Config
	useCase usecase.CardsAccounts
}

func NewServerOptions(useCases usecase.CardsAccounts, cfg *cardsaccounts.Config) *Server {
	return &Server{
		cfg:     cfg,
		useCase: useCases,
	}
}

func (s *Server) NewServer(cfg *grpcx.Config) (*grpc.Server, error) {
	options, err := grpcx.SetOptions(cfg)
	if err != nil {
		return nil, err
	}

	allOptions := []grpc.ServerOption{
		grpc.ChainUnaryInterceptor(grpcx.SentryInterceptor),
		grpc.ChainUnaryInterceptor(grpcx.SecureErrorInterceptor(cfg.ServiceID)),
		grpc.ChainUnaryInterceptor(otelgrpc.UnaryServerInterceptor()),
		grpc.ChainUnaryInterceptor(grpcx.RequestIDServerInterceptor()),
		grpc.ChainUnaryInterceptor(grpcx.AcceptLanguageServerInterceptor()),
		grpc.ChainUnaryInterceptor(grpcx.UserAgentServerInterceptor()),
		grpc.ChainUnaryInterceptor(grpcx.UserPlatformServerInterceptor()),
		grpc.ChainUnaryInterceptor(grpcx.RealIPServerInterceptor()),
		grpc.StatsHandler(
			otelgrpc.NewServerHandler(
				otelgrpc.WithPropagators(propagation.TraceContext{}),
			),
		),
	}
	allOptions = append(allOptions, options...)
	srv := grpc.NewServer(allOptions...)

	grpc_health_v1.RegisterHealthServer(srv, s)
	pb.RegisterCardsaccountsServer(srv, s)

	return srv, nil
}
