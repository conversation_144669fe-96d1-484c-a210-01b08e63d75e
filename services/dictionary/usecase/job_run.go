package usecase

import (
	"context"
	"fmt"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs"
	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/arcodesync"
	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/banksync"
	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/calendarkzsync"
	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/clientaddrsprepared"
	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/clientaddrsupdate"
	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/countrysync"
	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/countrytsoidsync"
	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/katoactual"
	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/katosync"
	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/kbesync"
	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/knpsync"
	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/mccsync"
	raionindex "git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/regionindex"
	smebanksync "git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/sme-banksync"
	smecountrysync "git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/sme-countrysync"
	smekbesync "git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/sme-kbesync"
	smekbksync "git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/sme-kbksync"
	smeknpkzsync "git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/sme-knp-kz-sync"
	smeknpsync "git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/sme-knpsync"
	smeregions "git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/sme-ugd-regions-sync"
	smeugdsync "git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/sme-ugdsync"
	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/spikato"
	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/spisync"
	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/tsoidkato"
	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/tsoidmanual"
	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs/tsoidsync"
)

func (u *useCasesImpl) JobRun(ctx context.Context, req *entity.JobRunReq) (*entity.JobRunResult, error) { //nolint: funlen
	log := logs.FromContext(ctx)

	jm := u.Providers.JobsManager()

	log.Info().Msgf("JobsManager: %v", jm)

	jobCtx := context.Background()
	jobCtx = logs.ToContext(jobCtx, log)

	var job jobs.Job
	switch req.Name {
	case katosync.JobName:
		log.Info().Msgf("kato sync job add: '%s'", req.Name)
		job = katosync.NewJob(jobCtx, log, req.Source, req.Lang, u.Providers.Storage, u.Providers.ObjectStorage)
	case tsoidsync.JobName:
		log.Info().Msgf("tsoid sync job add: '%s'", req.Name)
		job = tsoidsync.NewJob(jobCtx, log, req.Source, req.Lang, u.Providers.Storage, u.Providers.ObjectStorage)
	case tsoidkato.JobName:
		log.Info().Msgf("tsoid-kato sync job add: '%s'", req.Name)
		job = tsoidkato.NewJob(jobCtx, log, u.Providers.Storage, true)
	case knpsync.JobName:
		log.Info().Msgf("knp sync job add: '%s'", req.Name)
		job = knpsync.NewJob(jobCtx, log, u.Providers.Storage, u.Providers.Colvirbridge)
	case kbesync.JobName:
		log.Info().Msgf("kbe sync job add: '%s'", req.Name)
		job = kbesync.NewJob(jobCtx, log, u.Providers.Storage, req.Source, u.Providers.ObjectStorage)
	case banksync.JobName:
		log.Info().Msgf("bank sync job add: '%s'", req.Name)
		job = banksync.NewJob(jobCtx, log, u.Providers.Storage, u.Providers.Colvirbridge)
	case calendarkzsync.JobName:
		log.Info().Msgf("calendar kz sync job add: '%s'", req.Name)
		job = calendarkzsync.NewJob(jobCtx, log, u.Providers.Storage, u.Providers.Colvirbridge)
	case countrysync.JobName:
		log.Info().Msgf("country kz sync job add: '%s'", req.Name)
		job = countrysync.NewJob(jobCtx, log, u.Providers.Storage, u.Providers.Colvirbridge, req.Source, u.Providers.ObjectStorage)
	case spisync.JobName:
		log.Info().Msgf("spisync job add: '%s'", req.Name)
		job = spisync.NewJob(jobCtx, log, req.Source, req.Lang, u.Providers.Storage, u.Providers.ObjectStorage)
	case spikato.JobName:
		log.Info().Msgf("link SPI - KATO job add: '%s'", req.Name)
		job = spikato.NewJob(jobCtx, log, u.Providers.Storage, true)
	case tsoidmanual.JobName:
		log.Info().Msgf("tsoid-manual job add: '%s'", req.Name)
		job = tsoidmanual.NewJob(jobCtx, log, req.Source, req.Lang, u.Providers.Storage, u.Providers.ObjectStorage)
	case raionindex.JobName:
		log.Info().Msgf("raion-index job add: '%s'", req.Name)
		job = raionindex.NewJob(jobCtx, log, req.Source, req.Lang, u.Providers.Storage, u.Providers.ObjectStorage)
	case smekbesync.JobName:
		log.Info().Msgf("%s job add: '%s'", smekbesync.JobName, req.Name)
		job = smekbesync.NewJob(jobCtx, log, u.Providers.Storage, req.Source, u.Providers.ObjectStorage)
	case smeknpsync.JobName:
		log.Info().Msgf("%s job add: '%s'", smeknpsync.JobName, req.Name)
		job = smeknpsync.NewJob(jobCtx, log, u.Providers.Storage, u.Providers.Colvirbridge)
	case smeknpkzsync.JobName:
		log.Info().Msgf("%s job add: '%s'", smeknpkzsync.JobName, req.Name)
		job = smeknpkzsync.NewJob(jobCtx, log, u.Providers.Storage, req.Source, u.Providers.ObjectStorage)
	case smebanksync.JobName:
		log.Info().Msgf("%s job add: '%s'", smebanksync.JobName, req.Name)
		job = smebanksync.NewJob(jobCtx, log, u.Providers.Storage, u.Providers.Colvirbridge)
	case smecountrysync.JobName:
		log.Info().Msgf("%s job add: '%s'", smecountrysync.JobName, req.Name)
		job = smecountrysync.NewJob(jobCtx, log, u.Providers.Storage, u.Providers.Colvirbridge, req.Source, u.Providers.ObjectStorage)
	case smeugdsync.JobName:
		log.Info().Msgf("%s job add: '%s'", smeugdsync.JobName, req.Name)
		job = smeugdsync.NewJob(jobCtx, log, u.Providers.Storage, req.Source, u.Providers.ObjectStorage, u.Providers.Colvirbridge)
	case smekbksync.JobName:
		log.Info().Msgf("%s job add: '%s'", smekbksync.JobName, req.Name)
		job = smekbksync.NewJob(jobCtx, log, u.Providers.Storage, req.Source, u.Providers.ObjectStorage, u.Providers.Colvirbridge)
	case katoactual.JobName:
		log.Info().Msgf("%s job add: '%s'", katoactual.JobName, req.Name)
		job = katoactual.NewJob(jobCtx, log, u.Providers.Storage, true)
	case countrytsoidsync.JobName:
		log.Info().Msgf("%s job add: '%s'", countrytsoidsync.JobName, req.Name)
		job = countrytsoidsync.NewJob(jobCtx, log, req.Source, u.Providers.Storage, u.Providers.ObjectStorage)
	case clientaddrsupdate.JobName:
		log.Info().Msgf("%s job add: '%s'", clientaddrsupdate.JobName, req.Name)
		job = clientaddrsupdate.NewJob(jobCtx, u.cfg.GRPC, log, req.Source, u.Providers.Colvirbridge, u.Providers.ObjectStorage)
	case smeregions.JobName:
		log.Info().Msgf("%s job add: '%s'", smeregions.JobName, req.Name)
		job = smeregions.NewJob(jobCtx, log, u.Providers.Storage, req.Source, u.Providers.ObjectStorage)
	case clientaddrsprepared.JobName:
		log.Info().Msgf("%s job add: '%s'", clientaddrsprepared.JobName, req.Name)
		job = clientaddrsprepared.NewJob(jobCtx, u.cfg.GRPC, log, req.Source, u.Providers.Colvirbridge, u.Providers.Users, u.Providers.Dictionary, u.Providers.Btsbridge, u.Providers.ObjectStorage)
	case arcodesync.JobName:
		log.Info().Msgf("%s job add: '%s'", arcodesync.JobName, req.Name)
		job = arcodesync.NewJob(jobCtx, log, req.Source, req.Lang, u.Providers.Storage, u.Providers.ObjectStorage)
	case mccsync.JobName:
		log.Info().Msgf("%s job add: '%s'", mccsync.JobName, req.Name)
		job = mccsync.NewJob(jobCtx, log, u.Providers.Storage, req.Source, u.Providers.ObjectStorage)
	default:
		log.Error().Msgf("bad name for job: '%s'", req.Name)
		return nil, fmt.Errorf("job type '%s' not exists", req.Name)
	}

	err := jm.Run(req.Name, job)
	if err != nil {
		return nil, err
	}

	return &entity.JobRunResult{}, nil
}
