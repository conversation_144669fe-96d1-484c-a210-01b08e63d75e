package mccsync

import (
	"context"
	"maps"
	"strings"
	"sync"

	"git.redmadrobot.com/backend-go/rmr-pkg/providers/s3"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"

	errs "git.redmadrobot.com/zaman/backend/zaman/errs/dictionary"
	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/jobs"
	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/storage"
)

const (
	JobName  = "invalid-mcc-sync"
	DictName = "invalid_mcc"
)

type Job struct {
	ctx      context.Context
	cancelFn context.CancelFunc

	log *zerolog.Logger

	dictStorage storage.Storage

	source    string
	s3storage s3.ClientInterface

	status      map[string]interface{}
	statusMutex sync.RWMutex

	wg sync.WaitGroup

	statusCallback jobs.NewStatusCallback
}

func NewJob(
	ctx context.Context,
	log *zerolog.Logger,
	dictStorage storage.Storage,
	source string,
	s3storage s3.ClientInterface,
) *Job {
	newCtx, cancelFn := context.WithCancel(ctx)

	return &Job{
		ctx:         newCtx,
		cancelFn:    cancelFn,
		log:         log,
		dictStorage: dictStorage,
		source:      source,
		s3storage:   s3storage,
		status: map[string]interface{}{
			"status": jobs.JobStatusNew,
			"name":   JobName,
		},
	}
}

func (j *Job) Run() error {
	j.setStatus(jobs.JobStatusWorked)

	j.wg.Add(1)
	go func() {
		defer j.wg.Done()

		file, err := j.s3storage.GetFile(j.ctx, j.source)
		if err != nil {
			j.log.Error().Err(err).Msg("Failed to get file")
			j.setStatusError(err.Error())
			return
		}
		defer file.Body.Close()

		mccStorage, err := NewStorage(
			j.ctx,
			j.dictStorage,
			DictName,
		)
		if err != nil {
			j.log.Error().Err(err).Msg("Failed to create mcc storage")
			j.setStatusError(err.Error())
			return
		}

		mccList := NewCSVConverter(file.Body).Convert()
		for _, mcc := range mccList {
			existMCC, err := mccStorage.GetByName(mcc.MCC)
			if err != nil {
				if !strings.Contains(err.Error(), errs.DictionaryErrs().DocNotFoundError().Error()) {
					log.Error().Err(err)
					j.setStatusError(err.Error())
					return
				}
			}

			newMCC := mccUpdate(existMCC, mcc)

			if err := mccStorage.Save(newMCC); err != nil {
				log.Error().Err(err).Msg("failed save mcc")
				j.setStatusError(err.Error())
				return
			}
		}

		j.setStatus(jobs.JobStatusCompleted)
	}()

	return nil
}

func (j *Job) Stop() error {
	j.cancelFn()
	j.wg.Wait()
	j.setStatus(jobs.JobStatusStopped)
	return nil
}

func (j *Job) Status() map[string]interface{} {
	j.statusMutex.RLock()
	status := maps.Clone(j.status)
	defer j.statusMutex.RUnlock()

	return status
}

func (j *Job) Name() string {
	return JobName
}

func (j *Job) SetStatusCallback(fn jobs.NewStatusCallback) {
	j.statusCallback = fn
}

func (j *Job) setStatus(status string) {
	j.statusMutex.Lock()
	j.status["status"] = status
	j.statusMutex.Unlock()

	if j.statusCallback != nil {
		j.statusCallback(j)
	}
}

func (j *Job) setStatusError(msg string) {
	j.statusMutex.Lock()
	j.status["status"] = jobs.JobStatusError
	j.status["error"] = msg
	j.statusMutex.Unlock()
}
