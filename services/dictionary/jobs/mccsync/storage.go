package mccsync

import (
	"context"
	"encoding/json"
	"strings"

	errs "git.redmadrobot.com/zaman/backend/zaman/errs/dictionary"
	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/storage"
)

const (
	DocNamePrefix = "mcc"
)

type Storage struct {
	ctx               context.Context
	dictionaryStorage storage.Storage
	defaultDictName   string
	defaultDictID     string
}

func NewStorage(ctx context.Context, dictionaryStorage storage.Storage, dictName string) (*Storage, error) {
	mccStorage := &Storage{
		ctx:               ctx,
		dictionaryStorage: dictionaryStorage,
		defaultDictName:   dictName,
	}

	err := mccStorage.checkDictExists()
	if err != nil {
		return nil, err
	}

	return mccStorage, nil
}

func (s *Storage) CheckDict() error {
	dict, err := s.dictionaryStorage.GetDictByName(s.ctx, s.defaultDictName)
	if err != nil {
		return err
	}

	s.defaultDictID = dict.ID

	return nil
}

func (s *Storage) CreateDict() error {
	dict := &entity.Dict{
		Name:        s.defaultDictName,
		Description: "Список запрещенных MCC кодов",
		Schema:      "{}",
	}

	var err error
	dict, err = s.dictionaryStorage.CreateDict(s.ctx, *dict)
	if err != nil {
		return err
	}

	s.defaultDictID = dict.ID

	return nil
}

func (s *Storage) checkDictExists() error {
	err := s.CheckDict()
	if err == nil {
		return nil
	}

	if !strings.Contains(err.Error(), errs.DictionaryErrs().NotFoundError().Error()) {
		return err
	}

	// Создаем словарь если его нет
	err = s.CreateDict()
	if err != nil {
		return err
	}

	return nil
}

func (s *Storage) GetByName(name string) (*MCC, error) {
	doc, err := s.dictionaryStorage.GetDocByName(s.ctx, s.defaultDictName, DocNamePrefix+name)
	if err != nil {
		return nil, err
	}

	mcc := &MCC{}
	err = json.Unmarshal([]byte(doc.Data), mcc)
	if err != nil {
		return nil, err
	}

	mcc.DocumentID = doc.ID

	return mcc, nil
}

func (s *Storage) Save(mcc *MCC) error {
	mccJSON, err := json.Marshal(mcc)
	if err != nil {
		return err
	}

	doc := &entity.Doc{
		ID:     mcc.DocumentID,
		DictID: s.defaultDictID,
		Name:   DocNamePrefix + mcc.MCC,
		Data:   string(mccJSON),
		Valid:  true,
	}

	if strings.TrimSpace(mcc.DocumentID) == "" {
		// Новая запись
		_, err = s.dictionaryStorage.CreateDoc(s.ctx, *doc)
	} else {
		// Обновляем существующую запись
		_, err = s.dictionaryStorage.UpdateDoc(s.ctx, *doc)
	}
	if err != nil {
		return err
	}

	return nil
}
