package mccsync

import (
	"bufio"
	"errors"
	"io"
	"strings"

	"github.com/rs/zerolog/log"
)

type CSVConverter struct {
	source io.Reader
}

func NewCSVConverter(source io.Reader) *CSVConverter {
	return &CSVConverter{
		source: source,
	}
}

func (c *CSVConverter) Convert() (mccList []*MCC) {
	separator := ";"
	scanner := bufio.NewScanner(c.source)
	for scanner.Scan() {
		line := scanner.Text()
		mcc, err := convertLineToMCC(line, separator)
		if err != nil {
			log.Error().Msgf("CSV conversion error: %v ('%s')", err, line)
			continue
		}

		mccList = append(mccList, mcc)
	}

	return mccList
}

func convertLineToMCC(line, separator string) (*MCC, error) {
	data := strings.Split(line, separator)
	if len(data) != 2 {
		return nil, errors.New("invalid format line")
	}

	mcc := &MCC{
		MCC:  data[0],
		Name: data[1],
	}

	if mcc.MCC == "" {
		return nil, errors.New("empty mcc code")
	}

	if mcc.Name == "" {
		return nil, errors.New("empty mcc name")
	}

	return mcc, nil
}
