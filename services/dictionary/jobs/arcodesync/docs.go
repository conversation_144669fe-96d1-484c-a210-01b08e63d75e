package arcodesync

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"github.com/rs/zerolog/log"

	errs "git.redmadrobot.com/zaman/backend/zaman/errs/dictionary"
	"git.redmadrobot.com/zaman/backend/zaman/services/dictionary/entity"
)

type DictTypes interface {
	ProcessRow(storage KATOStorage, csvRow string, separator string, lang string) error
}

type (
	AtsTypes struct {
		ID     int `json:"id"`     // required
		Code   int `json:"code"`   // required
		Actual int `json:"actual"` // Действующее значение 1 // required

		NameLocalized Names `json:"name_localized"`  // Название объекта адреса на разных языках // required
		NameShortName Names `json:"name_short_name"` // Короткое название объекта // not_required
	}

	BuildingsPointers struct {
		ID     int `json:"id"`     // required
		Code   int `json:"code"`   // required
		Actual int `json:"actual"` // Действующее значение 1 // required

		NameLocalized Names `json:"name_localized"`  // Название объекта адреса на разных языках // required
		NameShortName Names `json:"name_short_name"` // Короткое название объекта // not_required
	}

	GeonimsTypes struct {
		ID     int    `json:"id"`      // required
		Code   int    `json:"code"`    // required
		ThisIs string `json:"this_is"` // Для дорог значение 'road' // not_required
		Actual int    `json:"actual"`  // Действующее значение 1 // required

		NameLocalized Names `json:"name_localized"`  // Название объекта адреса на разных языках // required
		NameShortName Names `json:"name_short_name"` // Короткое название объекта // not_required
	}

	RoomsTypes struct {
		ID     int `json:"id"`     // required
		Code   int `json:"code"`   // required
		Actual int `json:"actual"` // Действующее значение 1 // required

		NameLocalized Names `json:"name_localized"`  // Название объекта адреса на разных языках // required
		NameShortName Names `json:"name_short_name"` // Короткое название объекта // not_required
	}

	ATS struct {
		ID        int    `json:"id"`          // required
		ParentID  *int   `json:"parent_id"`   // not_required
		AtsTypeID int    `json:"ats_type_id"` // required
		RCO       string `json:"rco"`         // required
		Cato      string `json:"cato"`        // not_required
		Actual    int    `json:"actual"`      // Действующее значение 1 // required

		NameLocalized Names `json:"name_localized"` // Название объекта адреса на разных языках // required
	}

	Buildings struct {
		ID                  int    `json:"id"`                    // required
		GeonimsID           *int   `json:"geonims_id"`            // not_required
		AtsID               int    `json:"ats_id"`                // required
		BuildingsPointersID int    `json:"buildings_pointers_id"` // required
		ThisIs              string `json:"this_is"`               // Для дорог значение 'usual' // required
		ParentID            *int   `json:"parent_id"`             // not_required
		GroudID             int    `json:"ground_id"`             // required
		RCA                 string `json:"rca"`                   // required
		Number              string `json:"number"`                // required
		Distance            *int   `json:"distance"`              // not_required
		Actual              int    `json:"actual"`                // required
	}

	Geonims struct {
		ID            int    `json:"id"`              // required
		ParentID      *int   `json:"parent_id"`       // not_required
		AtsID         int    `json:"ats_id"`          // required
		GeonimsTypeID int    `json:"geonims_type_id"` // required
		RCO           string `json:"rco"`             // required
		Cato          string `json:"cato"`            // not_required
		NameLocalized Names  `json:"name_localized"`  // Название объекта адреса на разных языках // required
		Actual        int    `json:"actual"`          // required
	}

	Grounds struct {
		ID             int    `json:"id"`              // required
		GeonimsID      *int   `json:"geonims_id"`      // not_required
		AtsID          int    `json:"ats_id"`          // required
		RCA            string `json:"rca"`             // required
		Number         string `json:"number"`          // required
		CadastreNumber string `json:"cadastre_number"` // not_required
		Actual         int    `json:"actual"`          // required
	}

	PB struct {
		ID          int    `json:"id"`           // required
		RoomTypeID  int    `json:"room_type_id"` // required
		BuildingsID int    `json:"buildings_id"` // required
		RCA         string `json:"rca"`          // required
		Number      string `json:"number"`       // required
		Actual      int    `json:"actual"`       // required
	}

	Names struct {
		RU string `json:"ru"`
		EN string `json:"en"`
		KZ string `json:"kz"`
	}
)

func (a AtsTypes) ProcessRow(storage KATOStorage, csvRow string, separator string, _ string) error {
	csvStrs := strings.Split(csvRow, separator)
	if len(csvStrs) != 7 {
		return fmt.Errorf("AtsTypes ProcessRow: csvRow does not have enough fields: %v", csvRow)
	}

	// проверяем данные из строки
	id, errID := strconv.Atoi(csvStrs[0])
	if errID != nil {
		return fmt.Errorf("AtsTypes ProcessRow: %v; Atoi err:%w", csvStrs[0], errID)
	}

	code, errCode := strconv.Atoi(csvStrs[1])
	if errCode != nil {
		return fmt.Errorf("AtsTypes ProcessRow: %v; Atoi err:%w", csvStrs[1], errCode)
	}

	actual, erractual := strconv.Atoi(csvStrs[6])
	if erractual != nil {
		return fmt.Errorf("AtsTypes ProcessRow: %v; Atoi err:%w", csvStrs[6], erractual)
	}

	valueKz := csvStrs[2]
	if valueKz == "" {
		return fmt.Errorf("AtsTypes ProcessRow %v; valuekz is empty", csvStrs[2])
	}

	valueRU := csvStrs[3]
	if valueRU == "" {
		return fmt.Errorf("AtsTypes ProcessRow %v; valuekz is empty", csvStrs[3])
	}

	sortValueKz := csvStrs[4]
	sortValueRU := csvStrs[5]

	atsTypes := AtsTypes{
		ID:            id,
		Code:          code,
		Actual:        actual,
		NameLocalized: Names{RU: valueRU, KZ: valueKz},
		NameShortName: Names{RU: sortValueRU, KZ: sortValueKz},
	}

	// проверяем если такой
	existsDoc, err := storage.GetByName(fmt.Sprintf("%v_%v", DictNameAtsType, code))
	if err != nil {
		if !strings.Contains(err.Error(), errs.DictionaryErrs().DocNotFoundError().Error()) {
			log.Error().Err(err)
			return err
		}
	}

	docID := ""
	if existsDoc != nil {
		docID = existsDoc.ID
	}

	atsTypesJSON, errMarshal := json.Marshal(atsTypes)
	if errMarshal != nil {
		return fmt.Errorf("can not marshal atsTypes JSON: %w", errMarshal)
	}

	errSave := storage.Save(docID, &entity.Doc{
		ID:     docID,
		DictID: storage.GetDefaultDictID(),
		Name:   fmt.Sprintf("%v_%v", DictNameAtsType, code),
		Data:   string(atsTypesJSON),
		Valid:  true,
	})
	if errSave != nil {
		return errSave
	}

	return nil
}

func (a BuildingsPointers) ProcessRow(storage KATOStorage, csvRow string, separator string, _ string) error {
	csvStrs := strings.Split(csvRow, separator)
	if len(csvStrs) != 7 {
		return fmt.Errorf("BuildingsPointers ProcessRow: csvRow does not have enough fields: %v", csvRow)
	}

	// проверяем данные из строки
	id, errID := strconv.Atoi(csvStrs[0])
	if errID != nil {
		return fmt.Errorf("BuildingsPointers ProcessRow: %v; Atoi err:%w", csvStrs[0], errID)
	}

	code, errCode := strconv.Atoi(csvStrs[1])
	if errCode != nil {
		return fmt.Errorf("BuildingsPointers ProcessRow: %v; Atoi err:%w", csvStrs[1], errCode)
	}

	valueKz := csvStrs[2]
	if valueKz == "" {
		return fmt.Errorf("BuildingsPointers ProcessRow %v; valuekz is empty", csvStrs[2])
	}

	valueRU := csvStrs[3]
	if valueRU == "" {
		return fmt.Errorf("BuildingsPointers ProcessRow %v; valuekz is empty", csvStrs[3])
	}

	actual, erractual := strconv.Atoi(csvStrs[6])
	if erractual != nil {
		return fmt.Errorf("BuildingsPointers ProcessRow: %v; Atoi err:%w", csvStrs[6], erractual)
	}

	sortValueKz := csvStrs[4]
	sortValueRU := csvStrs[5]
	itemTypes := BuildingsPointers{
		ID:            id,
		Code:          code,
		Actual:        actual,
		NameLocalized: Names{RU: valueRU, KZ: valueKz},
		NameShortName: Names{RU: sortValueRU, KZ: sortValueKz},
	}

	// проверяем если такой
	existsDoc, err := storage.GetByName(fmt.Sprintf("%v_%v", DictNameBuildingsPointers, code))
	if err != nil {
		if !strings.Contains(err.Error(), errs.DictionaryErrs().DocNotFoundError().Error()) {
			log.Error().Err(err)
			return err
		}
	}

	docID := ""
	if existsDoc != nil {
		docID = existsDoc.ID
	}

	itemJSON, errMarshal := json.Marshal(itemTypes)
	if errMarshal != nil {
		return fmt.Errorf("can not marshal BuildingsPointers JSON: %w", errMarshal)
	}

	errSave := storage.Save(docID, &entity.Doc{
		ID:     docID,
		DictID: storage.GetDefaultDictID(),
		Name:   fmt.Sprintf("%v_%v", DictNameBuildingsPointers, code),
		Data:   string(itemJSON),
		Valid:  true,
	})
	if errSave != nil {
		return errSave
	}

	return nil
}

func (a GeonimsTypes) ProcessRow(storage KATOStorage, csvRow string, separator string, _ string) error {
	csvStrs := strings.Split(csvRow, separator)
	if len(csvStrs) != 8 {
		return fmt.Errorf("GeonimsTypes ProcessRow: csvRow does not have enough fields: %v", csvRow)
	}

	// проверяем данные из строки
	id, errID := strconv.Atoi(csvStrs[0])
	if errID != nil {
		return fmt.Errorf("GeonimsTypes ProcessRow: %v; Atoi err:%w", csvStrs[0], errID)
	}

	code, errCode := strconv.Atoi(csvStrs[1])
	if errCode != nil {
		return fmt.Errorf("GeonimsTypes ProcessRow: %v; Atoi err:%w", csvStrs[1], errCode)
	}

	actual, erractual := strconv.Atoi(csvStrs[7])
	if erractual != nil {
		return fmt.Errorf("GeonimsTypes ProcessRow: %v; Atoi err:%w", csvStrs[7], erractual)
	}

	thisIs := csvStrs[2]
	valueKz := csvStrs[3]
	if valueKz == "" {
		return fmt.Errorf("GeonimsTypes ProcessRow %v; valuekz is empty", csvStrs[3])
	}

	valueRU := csvStrs[4]
	if valueRU == "" {
		return fmt.Errorf("GeonimsTypes ProcessRow %v; valuekz is empty", csvStrs[4])
	}

	sortValueKz := csvStrs[5]
	sortValueRU := csvStrs[6]

	item := GeonimsTypes{
		ID:            id,
		Code:          code,
		Actual:        actual,
		ThisIs:        thisIs,
		NameLocalized: Names{RU: valueRU, KZ: valueKz},
		NameShortName: Names{RU: sortValueRU, KZ: sortValueKz},
	}

	// проверяем если такой
	existsDoc, err := storage.GetByName(fmt.Sprintf("%v_%v", DictNameGeonimsTypes, code))
	if err != nil {
		if !strings.Contains(err.Error(), errs.DictionaryErrs().DocNotFoundError().Error()) {
			log.Error().Err(err)
			return err
		}
	}

	docID := ""
	if existsDoc != nil {
		docID = existsDoc.ID
	}

	itemJSON, errMarshal := json.Marshal(item)
	if errMarshal != nil {
		return fmt.Errorf("can not marshal GeonimsTypes JSON: %w", errMarshal)
	}

	errSave := storage.Save(docID, &entity.Doc{
		ID:     docID,
		DictID: storage.GetDefaultDictID(),
		Name:   fmt.Sprintf("%v_%v", DictNameGeonimsTypes, code),
		Data:   string(itemJSON),
		Valid:  true,
	})
	if errSave != nil {
		return errSave
	}

	return nil
}

func (a RoomsTypes) ProcessRow(storage KATOStorage, csvRow string, separator string, _ string) error {
	csvStrs := strings.Split(csvRow, separator)
	if len(csvStrs) != 7 {
		return fmt.Errorf("RoomsTypes ProcessRow: csvRow does not have enough fields: %v", csvRow)
	}

	// проверяем данные из строки
	id, errID := strconv.Atoi(csvStrs[0])
	if errID != nil {
		return fmt.Errorf("RoomsTypes ProcessRow: %v; Atoi err:%w", csvStrs[0], errID)
	}

	code, errCode := strconv.Atoi(csvStrs[1])
	if errCode != nil {
		return fmt.Errorf("RoomsTypes ProcessRow: %v; Atoi err:%w", csvStrs[1], errCode)
	}
	valueKz := csvStrs[2]
	if valueKz == "" {
		return fmt.Errorf("RoomsTypes ProcessRow %v; valuekz is empty", csvStrs[2])
	}

	valueRU := csvStrs[3]
	if valueRU == "" {
		return fmt.Errorf("RoomsTypes ProcessRow %v; valuekz is empty", csvStrs[3])
	}

	actual, erractual := strconv.Atoi(csvStrs[6])
	if erractual != nil {
		return fmt.Errorf("RoomsTypes ProcessRow: %v; Atoi err:%w", csvStrs[6], erractual)
	}

	sortValueKz := csvStrs[4]
	sortValueRU := csvStrs[5]

	itemTypes := RoomsTypes{
		ID:            id,
		Code:          code,
		Actual:        actual,
		NameLocalized: Names{RU: valueRU, KZ: valueKz},
		NameShortName: Names{RU: sortValueRU, KZ: sortValueKz},
	}

	// проверяем если такой
	existsDoc, err := storage.GetByName(fmt.Sprintf("%v_%v", DictNameRoomsTypes, code))
	if err != nil {
		if !strings.Contains(err.Error(), errs.DictionaryErrs().DocNotFoundError().Error()) {
			log.Error().Err(err)
			return err
		}
	}

	docID := ""
	if existsDoc != nil {
		docID = existsDoc.ID
	}

	itemJSON, errMarshal := json.Marshal(itemTypes)
	if errMarshal != nil {
		return fmt.Errorf("can not marshal RoomsTypes JSON: %w", errMarshal)
	}

	errSave := storage.Save(docID, &entity.Doc{
		ID:     docID,
		DictID: storage.GetDefaultDictID(),
		Name:   fmt.Sprintf("%v_%v", DictNameRoomsTypes, code),
		Data:   string(itemJSON),
		Valid:  true,
	})
	if errSave != nil {
		return errSave
	}

	return nil
}

func (a ATS) ProcessRow(storage KATOStorage, csvRow string, separator string, _ string) error {
	csvStrs := strings.Split(csvRow, separator)
	if len(csvStrs) != 8 {
		return fmt.Errorf("ATS ProcessRow: csvRow does not have enough fields: %v", csvRow)
	}

	// проверяем данные из строки
	id, errID := strconv.Atoi(csvStrs[0])
	if errID != nil {
		return fmt.Errorf("ATS ProcessRow: %v; Atoi err:%w", csvStrs[0], errID)
	}

	var parentID *int
	if csvStrs[1] != "" {
		parent, errParentID := strconv.Atoi(csvStrs[1])
		if errParentID != nil {
			return fmt.Errorf("ATS ProcessRow: %v; Atoi err:%w", csvStrs[1], errParentID)
		}
		parentID = &parent
	}

	atsTypeID, errAtsTypeID := strconv.Atoi(csvStrs[2])
	if errAtsTypeID != nil {
		return fmt.Errorf("ATS ProcessRow: %v; Atoi err:%w", csvStrs[2], errAtsTypeID)
	}

	rco := csvStrs[3]
	if rco == "" {
		return fmt.Errorf("ATS ProcessRow %v; rco is empty", csvStrs[3])
	}

	valueKz := csvStrs[5]
	if valueKz == "" {
		return fmt.Errorf("ATS ProcessRow %v; valuekz is empty", csvStrs[5])
	}

	valueRU := csvStrs[6]
	if valueRU == "" {
		return fmt.Errorf("ATS ProcessRow %v; valuekz is empty", csvStrs[6])
	}

	actual, erractual := strconv.Atoi(csvStrs[7])
	if erractual != nil {
		return fmt.Errorf("ATS ProcessRow: %v; Atoi err:%w", csvStrs[7], erractual)
	}

	cato := csvStrs[4]
	itemTypes := ATS{
		ID:            id,
		ParentID:      parentID,
		AtsTypeID:     atsTypeID,
		RCO:           rco,
		Cato:          cato,
		Actual:        actual,
		NameLocalized: Names{RU: valueRU, KZ: valueKz},
	}

	// проверяем если такой
	existsDoc, err := storage.GetByName(fmt.Sprintf("%v_%v", DictNameAts, rco))
	if err != nil {
		if !strings.Contains(err.Error(), errs.DictionaryErrs().DocNotFoundError().Error()) {
			log.Error().Err(err)
			return err
		}
	}

	docID := ""
	if existsDoc != nil {
		docID = existsDoc.ID
	}

	itemJSON, errMarshal := json.Marshal(itemTypes)
	if errMarshal != nil {
		return fmt.Errorf("can not marshal ATS JSON: %w", errMarshal)
	}

	errSave := storage.Save(docID, &entity.Doc{
		ID:     docID,
		DictID: storage.GetDefaultDictID(),
		Name:   fmt.Sprintf("%v_%v", DictNameAts, rco),
		Data:   string(itemJSON),
		Valid:  true,
	})
	if errSave != nil {
		return errSave
	}

	return nil
}

func (a Buildings) ProcessRow(storage KATOStorage, csvRow string, separator string, _ string) error {
	csvStrs := strings.Split(csvRow, separator)
	if len(csvStrs) != 11 {
		return fmt.Errorf("buildings ProcessRow: csvRow does not have enough fields: %v", csvRow)
	}

	// проверяем данные из строки
	id, errID := strconv.Atoi(csvStrs[0])
	if errID != nil {
		return fmt.Errorf("buildings ProcessRow: %v; Atoi err:%w", csvStrs[0], errID)
	}

	var geonimID *int
	if csvStrs[1] != "" {
		geonim, errGeonims := strconv.Atoi(csvStrs[1])
		if errGeonims != nil {
			return fmt.Errorf("buildings ProcessRow: %v; Atoi err:%w", csvStrs[1], errGeonims)
		}
		geonimID = &geonim
	}

	atsID, errAtsID := strconv.Atoi(csvStrs[2])
	if errAtsID != nil {
		return fmt.Errorf("buildings ProcessRow: %v; Atoi err:%w", csvStrs[2], errAtsID)
	}

	buildingsPointerID, errBuildingsPointerID := strconv.Atoi(csvStrs[3])
	if errBuildingsPointerID != nil {
		return fmt.Errorf("buildings ProcessRow: %v; Atoi err:%w", csvStrs[3], errBuildingsPointerID)
	}

	var parentID *int
	if csvStrs[5] != "" {
		parent, errParentID := strconv.Atoi(csvStrs[5])
		if errParentID != nil {
			return fmt.Errorf("buildings ProcessRow: %v; Atoi err:%w", csvStrs[5], errParentID)
		}

		parentID = &parent
	}

	groundID, errGroundID := strconv.Atoi(csvStrs[6])
	if errGroundID != nil {
		return fmt.Errorf("buildings ProcessRow: %v; Atoi err:%w", csvStrs[6], errGroundID)
	}

	rca := csvStrs[7]
	if rca == "" {
		return fmt.Errorf("buildings ProcessRow: %v; rca is empty", csvStrs[7])
	}

	number := csvStrs[8]
	if number == "" {
		return fmt.Errorf("buildings ProcessRow: %v; number is empty", csvStrs[8])
	}

	var distance *int
	if csvStrs[9] != "" {
		dist, errDistance := strconv.Atoi(csvStrs[9])
		if errDistance != nil {
			return fmt.Errorf("buildings ProcessRow: %v; Atoi err:%w", csvStrs[9], errDistance)
		}
		distance = &dist
	}

	actual, errActual := strconv.Atoi(csvStrs[10])
	if errActual != nil {
		return fmt.Errorf("buildings ProcessRow: %v; Atoi err:%w", csvStrs[10], errActual)
	}

	thisIS := csvStrs[4]

	itemTypes := Buildings{
		ID:                  id,
		GeonimsID:           geonimID,
		AtsID:               atsID,
		BuildingsPointersID: buildingsPointerID,
		ThisIs:              thisIS,
		ParentID:            parentID,
		GroudID:             groundID,
		RCA:                 rca,
		Number:              number,
		Distance:            distance,
		Actual:              actual,
	}

	// проверяем если такой
	existsDoc, err := storage.GetByName(fmt.Sprintf("%v_%v", DictNameBuildings, rca))
	if err != nil {
		if !strings.Contains(err.Error(), errs.DictionaryErrs().DocNotFoundError().Error()) {
			log.Error().Err(err)
			return err
		}
	}

	docID := ""
	if existsDoc != nil {
		docID = existsDoc.ID
	}

	itemJSON, errMarshal := json.Marshal(itemTypes)
	if errMarshal != nil {
		return fmt.Errorf("can not marshal buildings JSON: %w", errMarshal)
	}

	errSave := storage.Save(docID, &entity.Doc{
		ID:     docID,
		DictID: storage.GetDefaultDictID(),
		Name:   fmt.Sprintf("%v_%v", DictNameBuildings, rca),
		Data:   string(itemJSON),
		Valid:  true,
	})
	if errSave != nil {
		return errSave
	}

	return nil
}

func (a Geonims) ProcessRow(storage KATOStorage, csvRow string, separator string, _ string) error {
	csvStrs := strings.Split(csvRow, separator)
	if len(csvStrs) != 9 {
		return fmt.Errorf("geonims ProcessRow: csvRow does not have enough fields: %v", csvRow)
	}

	// проверяем данные из строки
	id, errID := strconv.Atoi(csvStrs[0])
	if errID != nil {
		return fmt.Errorf("geonims ProcessRow: %v; Atoi err:%w", csvStrs[0], errID)
	}

	var parentID *int
	if csvStrs[1] != "" {
		parent, errParentID := strconv.Atoi(csvStrs[1])
		if errParentID != nil {
			return fmt.Errorf("geonims ProcessRow: %v; Atoi err:%w", csvStrs[1], errParentID)
		}
		parentID = &parent
	}

	atsID, errAtsID := strconv.Atoi(csvStrs[2])
	if errAtsID != nil {
		return fmt.Errorf("geonims ProcessRow: %v; Atoi err:%w", csvStrs[2], errAtsID)
	}

	geonimTypeID, errGeonims := strconv.Atoi(csvStrs[3])
	if errGeonims != nil {
		return fmt.Errorf("geonims ProcessRow: %v; Atoi err:%w", csvStrs[3], errGeonims)
	}

	rco := csvStrs[4]
	if rco == "" {
		return fmt.Errorf("geonims ProcessRow: %v; rco is empty", csvStrs[4])
	}

	valueKz := csvStrs[6]
	if valueKz == "" {
		return fmt.Errorf("geonims ProcessRow %v; valuekz is empty", csvStrs[6])
	}

	valueRU := csvStrs[7]
	if valueRU == "" {
		return fmt.Errorf("geonims ProcessRow %v; valuekz is empty", csvStrs[7])
	}

	names := Names{
		KZ: valueKz,
		RU: valueRU,
	}

	actual, errActual := strconv.Atoi(csvStrs[8])
	if errActual != nil {
		return fmt.Errorf("geonims ProcessRow: %v; Atoi err:%w", csvStrs[8], errActual)
	}

	cato := csvStrs[5]
	itemTypes := Geonims{
		ID:            id,
		ParentID:      parentID,
		AtsID:         atsID,
		GeonimsTypeID: geonimTypeID,
		RCO:           rco,
		Cato:          cato,
		NameLocalized: names,
		Actual:        actual,
	}

	// проверяем если такой
	existsDoc, err := storage.GetByName(fmt.Sprintf("%v_%v", DictNameGeonims, rco))
	if err != nil {
		if !strings.Contains(err.Error(), errs.DictionaryErrs().DocNotFoundError().Error()) {
			log.Error().Err(err)
			return err
		}
	}

	docID := ""
	if existsDoc != nil {
		docID = existsDoc.ID
	}

	itemJSON, errMarshal := json.Marshal(itemTypes)
	if errMarshal != nil {
		return fmt.Errorf("can not marshal geonims JSON: %w", errMarshal)
	}

	errSave := storage.Save(docID, &entity.Doc{
		ID:     docID,
		DictID: storage.GetDefaultDictID(),
		Name:   fmt.Sprintf("%v_%v", DictNameGeonims, rco),
		Data:   string(itemJSON),
		Valid:  true,
	})
	if errSave != nil {
		return errSave
	}

	return nil
}

func (a Grounds) ProcessRow(storage KATOStorage, csvRow string, separator string, _ string) error {
	csvStrs := strings.Split(csvRow, separator)
	if len(csvStrs) != 7 {
		return fmt.Errorf("grounds ProcessRow: csvRow does not have enough fields: %v", csvRow)
	}

	// проверяем данные из строки
	id, errID := strconv.Atoi(csvStrs[0])
	if errID != nil {
		return fmt.Errorf("grounds ProcessRow: %v; Atoi err:%w", csvStrs[0], errID)
	}

	var geonimID *int
	if csvStrs[1] != "" {
		geonim, errGeonims := strconv.Atoi(csvStrs[1])
		if errGeonims != nil {
			return fmt.Errorf("grounds ProcessRow: %v; Atoi err:%w", csvStrs[1], errGeonims)
		}
		geonimID = &geonim
	}

	atsID, errAtsID := strconv.Atoi(csvStrs[2])
	if errAtsID != nil {
		return fmt.Errorf("grounds ProcessRow: %v; Atoi err:%w", csvStrs[2], errAtsID)
	}

	rca := csvStrs[3]
	if rca == "" {
		return fmt.Errorf("grounds ProcessRow: %v; rca is empty", csvStrs[3])
	}

	number := csvStrs[4]
	if number == "" {
		return fmt.Errorf("grounds ProcessRow: %v; number is empty", csvStrs[4])
	}

	actual, errActual := strconv.Atoi(csvStrs[6])
	if errActual != nil {
		return fmt.Errorf("grounds ProcessRow: %v; Atoi err:%w", csvStrs[6], errActual)
	}

	cadastreNum := csvStrs[5]
	itemTypes := Grounds{
		ID:             id,
		GeonimsID:      geonimID,
		AtsID:          atsID,
		RCA:            rca,
		Number:         number,
		CadastreNumber: cadastreNum,
		Actual:         actual,
	}

	// проверяем если такой
	existsDoc, err := storage.GetByName(fmt.Sprintf("%v_%v", DictNameGrounds, rca))
	if err != nil {
		if !strings.Contains(err.Error(), errs.DictionaryErrs().DocNotFoundError().Error()) {
			log.Error().Err(err)
			return err
		}
	}

	docID := ""
	if existsDoc != nil {
		docID = existsDoc.ID
	}

	itemJSON, errMarshal := json.Marshal(itemTypes)
	if errMarshal != nil {
		return fmt.Errorf("can not marshal grounds JSON: %w", errMarshal)
	}

	errSave := storage.Save(docID, &entity.Doc{
		ID:     docID,
		DictID: storage.GetDefaultDictID(),
		Name:   fmt.Sprintf("%v_%v", DictNameGrounds, rca),
		Data:   string(itemJSON),
		Valid:  true,
	})
	if errSave != nil {
		return errSave
	}

	return nil
}

func (a PB) ProcessRow(storage KATOStorage, csvRow string, separator string, _ string) error {
	csvStrs := strings.Split(csvRow, separator)
	if len(csvStrs) != 6 {
		return fmt.Errorf("PB ProcessRow: csvRow does not have enough fields: %v", csvRow)
	}

	// проверяем данные из строки
	id, errID := strconv.Atoi(csvStrs[0])
	if errID != nil {
		return fmt.Errorf("PB ProcessRow: %v; Atoi err:%w", csvStrs[0], errID)
	}

	roomTypeID, errRoomTypeID := strconv.Atoi(csvStrs[1])
	if errRoomTypeID != nil {
		return fmt.Errorf("PB ProcessRow: %v; Atoi err:%w", csvStrs[1], errRoomTypeID)
	}

	buildingsID, errBuldingsID := strconv.Atoi(csvStrs[2])
	if errBuldingsID != nil {
		return fmt.Errorf("PB ProcessRow: %v; Atoi err:%w", csvStrs[2], errBuldingsID)
	}

	rca := csvStrs[3]
	if rca == "" {
		return fmt.Errorf("PB ProcessRow: %v; rca is empty", csvStrs[3])
	}

	number := csvStrs[4]
	if number == "" {
		return fmt.Errorf("PB ProcessRow: %v; number is empty", csvStrs[4])
	}

	actual, errActual := strconv.Atoi(csvStrs[5])
	if errActual != nil {
		return fmt.Errorf("PB ProcessRow: %v; Atoi err:%w", csvStrs[5], errActual)
	}

	itemTypes := PB{
		ID:          id,
		RoomTypeID:  roomTypeID,
		BuildingsID: buildingsID,
		RCA:         rca,
		Number:      number,
		Actual:      actual,
	}

	// проверяем если такой
	existsDoc, err := storage.GetByName(fmt.Sprintf("%v_%v", DictNamePB, rca))
	if err != nil {
		if !strings.Contains(err.Error(), errs.DictionaryErrs().DocNotFoundError().Error()) {
			log.Error().Err(err)
			return err
		}
	}

	docID := ""
	if existsDoc != nil {
		docID = existsDoc.ID
	}

	itemJSON, errMarshal := json.Marshal(itemTypes)
	if errMarshal != nil {
		return fmt.Errorf("can not marshal PB JSON: %w", errMarshal)
	}

	errSave := storage.Save(docID, &entity.Doc{
		ID:     docID,
		DictID: storage.GetDefaultDictID(),
		Name:   fmt.Sprintf("%v_%v", DictNamePB, rca),
		Data:   string(itemJSON),
		Valid:  true,
	})
	if errSave != nil {
		return errSave
	}

	return nil
}
