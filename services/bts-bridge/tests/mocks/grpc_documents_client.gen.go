// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/specs/proto/documents (interfaces: DocumentsClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"

	documents "git.redmadrobot.com/zaman/backend/zaman/specs/proto/documents"
)

// MockDocumentsClient is a mock of DocumentsClient interface.
type MockDocumentsClient struct {
	ctrl     *gomock.Controller
	recorder *MockDocumentsClientMockRecorder
}

// MockDocumentsClientMockRecorder is the mock recorder for MockDocumentsClient.
type MockDocumentsClientMockRecorder struct {
	mock *MockDocumentsClient
}

// NewMockDocumentsClient creates a new mock instance.
func NewMockDocumentsClient(ctrl *gomock.Controller) *MockDocumentsClient {
	mock := &MockDocumentsClient{ctrl: ctrl}
	mock.recorder = &MockDocumentsClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDocumentsClient) EXPECT() *MockDocumentsClientMockRecorder {
	return m.recorder
}

// AccountDocument mocks base method.
func (m *MockDocumentsClient) AccountDocument(arg0 context.Context, arg1 *documents.GenerateAccountDocumentReq, arg2 ...grpc.CallOption) (*documents.GenerateAccountDocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AccountDocument", varargs...)
	ret0, _ := ret[0].(*documents.GenerateAccountDocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AccountDocument indicates an expected call of AccountDocument.
func (mr *MockDocumentsClientMockRecorder) AccountDocument(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AccountDocument", reflect.TypeOf((*MockDocumentsClient)(nil).AccountDocument), varargs...)
}

// ConfirmSignDocument mocks base method.
func (m *MockDocumentsClient) ConfirmSignDocument(arg0 context.Context, arg1 *documents.ConfirmSignDocumentReq, arg2 ...grpc.CallOption) (*documents.ConfirmSignDocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConfirmSignDocument", varargs...)
	ret0, _ := ret[0].(*documents.ConfirmSignDocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmSignDocument indicates an expected call of ConfirmSignDocument.
func (mr *MockDocumentsClientMockRecorder) ConfirmSignDocument(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmSignDocument", reflect.TypeOf((*MockDocumentsClient)(nil).ConfirmSignDocument), varargs...)
}

// ConfirmSignDocumentWeb mocks base method.
func (m *MockDocumentsClient) ConfirmSignDocumentWeb(arg0 context.Context, arg1 *documents.ConfirmSignDocumentWebReq, arg2 ...grpc.CallOption) (*documents.ConfirmSignDocumentWebResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConfirmSignDocumentWeb", varargs...)
	ret0, _ := ret[0].(*documents.ConfirmSignDocumentWebResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmSignDocumentWeb indicates an expected call of ConfirmSignDocumentWeb.
func (mr *MockDocumentsClientMockRecorder) ConfirmSignDocumentWeb(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmSignDocumentWeb", reflect.TypeOf((*MockDocumentsClient)(nil).ConfirmSignDocumentWeb), varargs...)
}

// ConfirmSignDocuments mocks base method.
func (m *MockDocumentsClient) ConfirmSignDocuments(arg0 context.Context, arg1 *documents.ConfirmSignDocumentsReq, arg2 ...grpc.CallOption) (*documents.ConfirmSignDocumentsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConfirmSignDocuments", varargs...)
	ret0, _ := ret[0].(*documents.ConfirmSignDocumentsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmSignDocuments indicates an expected call of ConfirmSignDocuments.
func (mr *MockDocumentsClientMockRecorder) ConfirmSignDocuments(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmSignDocuments", reflect.TypeOf((*MockDocumentsClient)(nil).ConfirmSignDocuments), varargs...)
}

// ConfirmSignDocumentsBatch mocks base method.
func (m *MockDocumentsClient) ConfirmSignDocumentsBatch(arg0 context.Context, arg1 *documents.ConfirmSignDocumentsBatchReq, arg2 ...grpc.CallOption) (*documents.ConfirmSignDocumentsBatchResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConfirmSignDocumentsBatch", varargs...)
	ret0, _ := ret[0].(*documents.ConfirmSignDocumentsBatchResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmSignDocumentsBatch indicates an expected call of ConfirmSignDocumentsBatch.
func (mr *MockDocumentsClientMockRecorder) ConfirmSignDocumentsBatch(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmSignDocumentsBatch", reflect.TypeOf((*MockDocumentsClient)(nil).ConfirmSignDocumentsBatch), varargs...)
}

// CreateOrderBsas mocks base method.
func (m *MockDocumentsClient) CreateOrderBsas(arg0 context.Context, arg1 *documents.CreateOrderBsasReq, arg2 ...grpc.CallOption) (*documents.CreateOrderBsasResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateOrderBsas", varargs...)
	ret0, _ := ret[0].(*documents.CreateOrderBsasResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOrderBsas indicates an expected call of CreateOrderBsas.
func (mr *MockDocumentsClientMockRecorder) CreateOrderBsas(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrderBsas", reflect.TypeOf((*MockDocumentsClient)(nil).CreateOrderBsas), varargs...)
}

// DownloadDocument mocks base method.
func (m *MockDocumentsClient) DownloadDocument(arg0 context.Context, arg1 *documents.DownloadDocumentReq, arg2 ...grpc.CallOption) (*documents.DownloadDocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DownloadDocument", varargs...)
	ret0, _ := ret[0].(*documents.DownloadDocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DownloadDocument indicates an expected call of DownloadDocument.
func (mr *MockDocumentsClientMockRecorder) DownloadDocument(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadDocument", reflect.TypeOf((*MockDocumentsClient)(nil).DownloadDocument), varargs...)
}

// GenerateAppSaleOfGoodsDocument mocks base method.
func (m *MockDocumentsClient) GenerateAppSaleOfGoodsDocument(arg0 context.Context, arg1 *documents.GenerateAppSaleOfGoodsDocumentReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateAppSaleOfGoodsDocument", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateAppSaleOfGoodsDocument indicates an expected call of GenerateAppSaleOfGoodsDocument.
func (mr *MockDocumentsClientMockRecorder) GenerateAppSaleOfGoodsDocument(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateAppSaleOfGoodsDocument", reflect.TypeOf((*MockDocumentsClient)(nil).GenerateAppSaleOfGoodsDocument), varargs...)
}

// GenerateAppSaleOfGoodsDocumentSMEIP mocks base method.
func (m *MockDocumentsClient) GenerateAppSaleOfGoodsDocumentSMEIP(arg0 context.Context, arg1 *documents.GenerateAppSaleOfGoodsDocumentSMEIPReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateAppSaleOfGoodsDocumentSMEIP", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateAppSaleOfGoodsDocumentSMEIP indicates an expected call of GenerateAppSaleOfGoodsDocumentSMEIP.
func (mr *MockDocumentsClientMockRecorder) GenerateAppSaleOfGoodsDocumentSMEIP(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateAppSaleOfGoodsDocumentSMEIP", reflect.TypeOf((*MockDocumentsClient)(nil).GenerateAppSaleOfGoodsDocumentSMEIP), varargs...)
}

// GenerateBankServiceApplicationSMEIP mocks base method.
func (m *MockDocumentsClient) GenerateBankServiceApplicationSMEIP(arg0 context.Context, arg1 *documents.GenerateBankServiceApplicationSMEIPReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateBankServiceApplicationSMEIP", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateBankServiceApplicationSMEIP indicates an expected call of GenerateBankServiceApplicationSMEIP.
func (mr *MockDocumentsClientMockRecorder) GenerateBankServiceApplicationSMEIP(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateBankServiceApplicationSMEIP", reflect.TypeOf((*MockDocumentsClient)(nil).GenerateBankServiceApplicationSMEIP), varargs...)
}

// GenerateBankingServiceApplicationDocument mocks base method.
func (m *MockDocumentsClient) GenerateBankingServiceApplicationDocument(arg0 context.Context, arg1 *documents.GenerateBankingServiceApplicationDocumentReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateBankingServiceApplicationDocument", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateBankingServiceApplicationDocument indicates an expected call of GenerateBankingServiceApplicationDocument.
func (mr *MockDocumentsClientMockRecorder) GenerateBankingServiceApplicationDocument(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateBankingServiceApplicationDocument", reflect.TypeOf((*MockDocumentsClient)(nil).GenerateBankingServiceApplicationDocument), varargs...)
}

// GenerateCollectionIncome mocks base method.
func (m *MockDocumentsClient) GenerateCollectionIncome(arg0 context.Context, arg1 *documents.GenerateCollectionIncomeReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateCollectionIncome", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateCollectionIncome indicates an expected call of GenerateCollectionIncome.
func (mr *MockDocumentsClientMockRecorder) GenerateCollectionIncome(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateCollectionIncome", reflect.TypeOf((*MockDocumentsClient)(nil).GenerateCollectionIncome), varargs...)
}

// GenerateComplexConsentDocument mocks base method.
func (m *MockDocumentsClient) GenerateComplexConsentDocument(arg0 context.Context, arg1 *documents.GenerateComplexConsentDocumentReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateComplexConsentDocument", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateComplexConsentDocument indicates an expected call of GenerateComplexConsentDocument.
func (mr *MockDocumentsClientMockRecorder) GenerateComplexConsentDocument(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateComplexConsentDocument", reflect.TypeOf((*MockDocumentsClient)(nil).GenerateComplexConsentDocument), varargs...)
}

// GenerateComplexConsentDocumentSMEIP mocks base method.
func (m *MockDocumentsClient) GenerateComplexConsentDocumentSMEIP(arg0 context.Context, arg1 *documents.GenerateComplexConsentDocumentSMEIPReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateComplexConsentDocumentSMEIP", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateComplexConsentDocumentSMEIP indicates an expected call of GenerateComplexConsentDocumentSMEIP.
func (mr *MockDocumentsClientMockRecorder) GenerateComplexConsentDocumentSMEIP(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateComplexConsentDocumentSMEIP", reflect.TypeOf((*MockDocumentsClient)(nil).GenerateComplexConsentDocumentSMEIP), varargs...)
}

// GenerateCreditContractWithRepaymentSchedule mocks base method.
func (m *MockDocumentsClient) GenerateCreditContractWithRepaymentSchedule(arg0 context.Context, arg1 *documents.GenerateCreditContractWithRepaymentScheduleReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateCreditContractWithRepaymentSchedule", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateCreditContractWithRepaymentSchedule indicates an expected call of GenerateCreditContractWithRepaymentSchedule.
func (mr *MockDocumentsClientMockRecorder) GenerateCreditContractWithRepaymentSchedule(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateCreditContractWithRepaymentSchedule", reflect.TypeOf((*MockDocumentsClient)(nil).GenerateCreditContractWithRepaymentSchedule), varargs...)
}

// GenerateCreditContractWithRepaymentScheduleSMEIP mocks base method.
func (m *MockDocumentsClient) GenerateCreditContractWithRepaymentScheduleSMEIP(arg0 context.Context, arg1 *documents.GenerateCreditContractWithRepaymentScheduleSMEIPReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateCreditContractWithRepaymentScheduleSMEIP", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateCreditContractWithRepaymentScheduleSMEIP indicates an expected call of GenerateCreditContractWithRepaymentScheduleSMEIP.
func (mr *MockDocumentsClientMockRecorder) GenerateCreditContractWithRepaymentScheduleSMEIP(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateCreditContractWithRepaymentScheduleSMEIP", reflect.TypeOf((*MockDocumentsClient)(nil).GenerateCreditContractWithRepaymentScheduleSMEIP), varargs...)
}

// GenerateDepositApplicationDocument mocks base method.
func (m *MockDocumentsClient) GenerateDepositApplicationDocument(arg0 context.Context, arg1 *documents.GenerateDepositApplicationDocumentReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateDepositApplicationDocument", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateDepositApplicationDocument indicates an expected call of GenerateDepositApplicationDocument.
func (mr *MockDocumentsClientMockRecorder) GenerateDepositApplicationDocument(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateDepositApplicationDocument", reflect.TypeOf((*MockDocumentsClient)(nil).GenerateDepositApplicationDocument), varargs...)
}

// GenerateDepositCloseApplicationDocument mocks base method.
func (m *MockDocumentsClient) GenerateDepositCloseApplicationDocument(arg0 context.Context, arg1 *documents.GenerateDepositCloseApplicationDocumentReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateDepositCloseApplicationDocument", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateDepositCloseApplicationDocument indicates an expected call of GenerateDepositCloseApplicationDocument.
func (mr *MockDocumentsClientMockRecorder) GenerateDepositCloseApplicationDocument(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateDepositCloseApplicationDocument", reflect.TypeOf((*MockDocumentsClient)(nil).GenerateDepositCloseApplicationDocument), varargs...)
}

// GenerateDocumentBID mocks base method.
func (m *MockDocumentsClient) GenerateDocumentBID(arg0 context.Context, arg1 *documents.GenerateDocumentBIDReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateDocumentBID", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateDocumentBID indicates an expected call of GenerateDocumentBID.
func (mr *MockDocumentsClientMockRecorder) GenerateDocumentBID(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateDocumentBID", reflect.TypeOf((*MockDocumentsClient)(nil).GenerateDocumentBID), varargs...)
}

// GenerateDocumentByType mocks base method.
func (m *MockDocumentsClient) GenerateDocumentByType(arg0 context.Context, arg1 *documents.GenerateDocumentReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateDocumentByType", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateDocumentByType indicates an expected call of GenerateDocumentByType.
func (mr *MockDocumentsClientMockRecorder) GenerateDocumentByType(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateDocumentByType", reflect.TypeOf((*MockDocumentsClient)(nil).GenerateDocumentByType), varargs...)
}

// GenerateDocumentForOpeningAccount mocks base method.
func (m *MockDocumentsClient) GenerateDocumentForOpeningAccount(arg0 context.Context, arg1 *documents.GenerateDocumentForOpeningAccountReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateDocumentForOpeningAccount", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateDocumentForOpeningAccount indicates an expected call of GenerateDocumentForOpeningAccount.
func (mr *MockDocumentsClientMockRecorder) GenerateDocumentForOpeningAccount(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateDocumentForOpeningAccount", reflect.TypeOf((*MockDocumentsClient)(nil).GenerateDocumentForOpeningAccount), varargs...)
}

// GenerateDocumentForOpeningAccountSME mocks base method.
func (m *MockDocumentsClient) GenerateDocumentForOpeningAccountSME(arg0 context.Context, arg1 *documents.GenerateDocumentForOpeningAccountSMEReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateDocumentForOpeningAccountSME", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateDocumentForOpeningAccountSME indicates an expected call of GenerateDocumentForOpeningAccountSME.
func (mr *MockDocumentsClientMockRecorder) GenerateDocumentForOpeningAccountSME(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateDocumentForOpeningAccountSME", reflect.TypeOf((*MockDocumentsClient)(nil).GenerateDocumentForOpeningAccountSME), varargs...)
}

// GenerateDocumentForOpeningAdditionalAccountSME mocks base method.
func (m *MockDocumentsClient) GenerateDocumentForOpeningAdditionalAccountSME(arg0 context.Context, arg1 *documents.GenerateDocumentForOpeningAdditionalAccountSMEReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateDocumentForOpeningAdditionalAccountSME", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateDocumentForOpeningAdditionalAccountSME indicates an expected call of GenerateDocumentForOpeningAdditionalAccountSME.
func (mr *MockDocumentsClientMockRecorder) GenerateDocumentForOpeningAdditionalAccountSME(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateDocumentForOpeningAdditionalAccountSME", reflect.TypeOf((*MockDocumentsClient)(nil).GenerateDocumentForOpeningAdditionalAccountSME), varargs...)
}

// GenerateInternalPaymentByPhoneNumberReceipt mocks base method.
func (m *MockDocumentsClient) GenerateInternalPaymentByPhoneNumberReceipt(arg0 context.Context, arg1 *documents.GenerateInternalPaymentByPhoneNumberReceiptReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateInternalPaymentByPhoneNumberReceipt", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateInternalPaymentByPhoneNumberReceipt indicates an expected call of GenerateInternalPaymentByPhoneNumberReceipt.
func (mr *MockDocumentsClientMockRecorder) GenerateInternalPaymentByPhoneNumberReceipt(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateInternalPaymentByPhoneNumberReceipt", reflect.TypeOf((*MockDocumentsClient)(nil).GenerateInternalPaymentByPhoneNumberReceipt), varargs...)
}

// GenerateLoansFundsTransferRequestRefinance mocks base method.
func (m *MockDocumentsClient) GenerateLoansFundsTransferRequestRefinance(arg0 context.Context, arg1 *documents.GenerateLoansFundsTransferRequestRefinanceRequest, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateLoansFundsTransferRequestRefinance", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateLoansFundsTransferRequestRefinance indicates an expected call of GenerateLoansFundsTransferRequestRefinance.
func (mr *MockDocumentsClientMockRecorder) GenerateLoansFundsTransferRequestRefinance(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateLoansFundsTransferRequestRefinance", reflect.TypeOf((*MockDocumentsClient)(nil).GenerateLoansFundsTransferRequestRefinance), varargs...)
}

// GenerateMassPaymentOrderSMEIP mocks base method.
func (m *MockDocumentsClient) GenerateMassPaymentOrderSMEIP(arg0 context.Context, arg1 *documents.GenerateMassPaymentOrderSMEIPReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateMassPaymentOrderSMEIP", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateMassPaymentOrderSMEIP indicates an expected call of GenerateMassPaymentOrderSMEIP.
func (mr *MockDocumentsClientMockRecorder) GenerateMassPaymentOrderSMEIP(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateMassPaymentOrderSMEIP", reflect.TypeOf((*MockDocumentsClient)(nil).GenerateMassPaymentOrderSMEIP), varargs...)
}

// GenerateOtcTemplateCertificate mocks base method.
func (m *MockDocumentsClient) GenerateOtcTemplateCertificate(arg0 context.Context, arg1 *documents.GenerateOtcTemplateCertificateReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateOtcTemplateCertificate", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateOtcTemplateCertificate indicates an expected call of GenerateOtcTemplateCertificate.
func (mr *MockDocumentsClientMockRecorder) GenerateOtcTemplateCertificate(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateOtcTemplateCertificate", reflect.TypeOf((*MockDocumentsClient)(nil).GenerateOtcTemplateCertificate), varargs...)
}

// GeneratePaymentByAccountReceipt mocks base method.
func (m *MockDocumentsClient) GeneratePaymentByAccountReceipt(arg0 context.Context, arg1 *documents.GeneratePaymentByAccountReceiptReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GeneratePaymentByAccountReceipt", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GeneratePaymentByAccountReceipt indicates an expected call of GeneratePaymentByAccountReceipt.
func (mr *MockDocumentsClientMockRecorder) GeneratePaymentByAccountReceipt(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeneratePaymentByAccountReceipt", reflect.TypeOf((*MockDocumentsClient)(nil).GeneratePaymentByAccountReceipt), varargs...)
}

// GeneratePaymentByAccountSmeReceipt mocks base method.
func (m *MockDocumentsClient) GeneratePaymentByAccountSmeReceipt(arg0 context.Context, arg1 *documents.GeneratePaymentByAccountReceiptSmeReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GeneratePaymentByAccountSmeReceipt", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GeneratePaymentByAccountSmeReceipt indicates an expected call of GeneratePaymentByAccountSmeReceipt.
func (mr *MockDocumentsClientMockRecorder) GeneratePaymentByAccountSmeReceipt(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeneratePaymentByAccountSmeReceipt", reflect.TypeOf((*MockDocumentsClient)(nil).GeneratePaymentByAccountSmeReceipt), varargs...)
}

// GeneratePaymentForMobileReceipt mocks base method.
func (m *MockDocumentsClient) GeneratePaymentForMobileReceipt(arg0 context.Context, arg1 *documents.GeneratePaymentForMobileReceiptReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GeneratePaymentForMobileReceipt", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GeneratePaymentForMobileReceipt indicates an expected call of GeneratePaymentForMobileReceipt.
func (mr *MockDocumentsClientMockRecorder) GeneratePaymentForMobileReceipt(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeneratePaymentForMobileReceipt", reflect.TypeOf((*MockDocumentsClient)(nil).GeneratePaymentForMobileReceipt), varargs...)
}

// GeneratePaymentOrderSMEIP mocks base method.
func (m *MockDocumentsClient) GeneratePaymentOrderSMEIP(arg0 context.Context, arg1 *documents.GeneratePaymentOrderSMEIPReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GeneratePaymentOrderSMEIP", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GeneratePaymentOrderSMEIP indicates an expected call of GeneratePaymentOrderSMEIP.
func (mr *MockDocumentsClientMockRecorder) GeneratePaymentOrderSMEIP(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeneratePaymentOrderSMEIP", reflect.TypeOf((*MockDocumentsClient)(nil).GeneratePaymentOrderSMEIP), varargs...)
}

// GeneratePersonalDataAgreement mocks base method.
func (m *MockDocumentsClient) GeneratePersonalDataAgreement(arg0 context.Context, arg1 *documents.GeneratePersonalDataAgreementReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GeneratePersonalDataAgreement", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GeneratePersonalDataAgreement indicates an expected call of GeneratePersonalDataAgreement.
func (mr *MockDocumentsClientMockRecorder) GeneratePersonalDataAgreement(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeneratePersonalDataAgreement", reflect.TypeOf((*MockDocumentsClient)(nil).GeneratePersonalDataAgreement), varargs...)
}

// GeneratePersonalDataAgreementSME mocks base method.
func (m *MockDocumentsClient) GeneratePersonalDataAgreementSME(arg0 context.Context, arg1 *documents.GeneratePersonalDataAgreementSMEReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GeneratePersonalDataAgreementSME", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GeneratePersonalDataAgreementSME indicates an expected call of GeneratePersonalDataAgreementSME.
func (mr *MockDocumentsClientMockRecorder) GeneratePersonalDataAgreementSME(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeneratePersonalDataAgreementSME", reflect.TypeOf((*MockDocumentsClient)(nil).GeneratePersonalDataAgreementSME), varargs...)
}

// GenerateSTBTransactionTemplateCertificate mocks base method.
func (m *MockDocumentsClient) GenerateSTBTransactionTemplateCertificate(arg0 context.Context, arg1 *documents.GenerateSTBTransactionTemplateCertificateReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateSTBTransactionTemplateCertificate", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateSTBTransactionTemplateCertificate indicates an expected call of GenerateSTBTransactionTemplateCertificate.
func (mr *MockDocumentsClientMockRecorder) GenerateSTBTransactionTemplateCertificate(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateSTBTransactionTemplateCertificate", reflect.TypeOf((*MockDocumentsClient)(nil).GenerateSTBTransactionTemplateCertificate), varargs...)
}

// GenerateSprProtocol mocks base method.
func (m *MockDocumentsClient) GenerateSprProtocol(arg0 context.Context, arg1 *documents.GenerateSprProtocolReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateSprProtocol", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateSprProtocol indicates an expected call of GenerateSprProtocol.
func (mr *MockDocumentsClientMockRecorder) GenerateSprProtocol(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateSprProtocol", reflect.TypeOf((*MockDocumentsClient)(nil).GenerateSprProtocol), varargs...)
}

// GetDocumentByID mocks base method.
func (m *MockDocumentsClient) GetDocumentByID(arg0 context.Context, arg1 *documents.GetDocumentByIDReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDocumentByID", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDocumentByID indicates an expected call of GetDocumentByID.
func (mr *MockDocumentsClientMockRecorder) GetDocumentByID(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDocumentByID", reflect.TypeOf((*MockDocumentsClient)(nil).GetDocumentByID), varargs...)
}

// GetDocumentByType mocks base method.
func (m *MockDocumentsClient) GetDocumentByType(arg0 context.Context, arg1 *documents.GetDocumentByTypeReq, arg2 ...grpc.CallOption) (*documents.DocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDocumentByType", varargs...)
	ret0, _ := ret[0].(*documents.DocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDocumentByType indicates an expected call of GetDocumentByType.
func (mr *MockDocumentsClientMockRecorder) GetDocumentByType(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDocumentByType", reflect.TypeOf((*MockDocumentsClient)(nil).GetDocumentByType), varargs...)
}

// GetDocumentContent mocks base method.
func (m *MockDocumentsClient) GetDocumentContent(arg0 context.Context, arg1 *documents.GetDocumentContentReq, arg2 ...grpc.CallOption) (*documents.GetDocumentContentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDocumentContent", varargs...)
	ret0, _ := ret[0].(*documents.GetDocumentContentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDocumentContent indicates an expected call of GetDocumentContent.
func (mr *MockDocumentsClientMockRecorder) GetDocumentContent(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDocumentContent", reflect.TypeOf((*MockDocumentsClient)(nil).GetDocumentContent), varargs...)
}

// GetDocumentSign mocks base method.
func (m *MockDocumentsClient) GetDocumentSign(arg0 context.Context, arg1 *documents.GetDocumentSignReq, arg2 ...grpc.CallOption) (*documents.GetDocumentSignResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDocumentSign", varargs...)
	ret0, _ := ret[0].(*documents.GetDocumentSignResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDocumentSign indicates an expected call of GetDocumentSign.
func (mr *MockDocumentsClientMockRecorder) GetDocumentSign(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDocumentSign", reflect.TypeOf((*MockDocumentsClient)(nil).GetDocumentSign), varargs...)
}

// GetOrderResultBsas mocks base method.
func (m *MockDocumentsClient) GetOrderResultBsas(arg0 context.Context, arg1 *documents.GetOrderResultBsasReq, arg2 ...grpc.CallOption) (*documents.GetOrderResultBsasResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOrderResultBsas", varargs...)
	ret0, _ := ret[0].(*documents.GetOrderResultBsasResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderResultBsas indicates an expected call of GetOrderResultBsas.
func (mr *MockDocumentsClientMockRecorder) GetOrderResultBsas(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderResultBsas", reflect.TypeOf((*MockDocumentsClient)(nil).GetOrderResultBsas), varargs...)
}

// GetOtcReportInfo mocks base method.
func (m *MockDocumentsClient) GetOtcReportInfo(arg0 context.Context, arg1 *documents.GetOtcReportInfoReq, arg2 ...grpc.CallOption) (*documents.GetOtcReportInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOtcReportInfo", varargs...)
	ret0, _ := ret[0].(*documents.GetOtcReportInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOtcReportInfo indicates an expected call of GetOtcReportInfo.
func (mr *MockDocumentsClientMockRecorder) GetOtcReportInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOtcReportInfo", reflect.TypeOf((*MockDocumentsClient)(nil).GetOtcReportInfo), varargs...)
}

// GetSellToBMIS mocks base method.
func (m *MockDocumentsClient) GetSellToBMIS(arg0 context.Context, arg1 *documents.GetSellToBMISReq, arg2 ...grpc.CallOption) (*documents.GetSellToBMISResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSellToBMIS", varargs...)
	ret0, _ := ret[0].(*documents.GetSellToBMISResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSellToBMIS indicates an expected call of GetSellToBMIS.
func (mr *MockDocumentsClientMockRecorder) GetSellToBMIS(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSellToBMIS", reflect.TypeOf((*MockDocumentsClient)(nil).GetSellToBMIS), varargs...)
}

// GetSystemMapping mocks base method.
func (m *MockDocumentsClient) GetSystemMapping(arg0 context.Context, arg1 *documents.GetSystemMappingReq, arg2 ...grpc.CallOption) (*documents.GetSystemMappingResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSystemMapping", varargs...)
	ret0, _ := ret[0].(*documents.GetSystemMappingResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSystemMapping indicates an expected call of GetSystemMapping.
func (mr *MockDocumentsClientMockRecorder) GetSystemMapping(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSystemMapping", reflect.TypeOf((*MockDocumentsClient)(nil).GetSystemMapping), varargs...)
}

// GetUserDocuments mocks base method.
func (m *MockDocumentsClient) GetUserDocuments(arg0 context.Context, arg1 *documents.GetUserDocumentsReq, arg2 ...grpc.CallOption) (*documents.GetUserDocumentsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserDocuments", varargs...)
	ret0, _ := ret[0].(*documents.GetUserDocumentsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserDocuments indicates an expected call of GetUserDocuments.
func (mr *MockDocumentsClientMockRecorder) GetUserDocuments(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDocuments", reflect.TypeOf((*MockDocumentsClient)(nil).GetUserDocuments), varargs...)
}

// HealthCheck mocks base method.
func (m *MockDocumentsClient) HealthCheck(arg0 context.Context, arg1 *documents.HealthCheckReq, arg2 ...grpc.CallOption) (*documents.HealthCheckResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HealthCheck", varargs...)
	ret0, _ := ret[0].(*documents.HealthCheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockDocumentsClientMockRecorder) HealthCheck(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockDocumentsClient)(nil).HealthCheck), varargs...)
}

// SaveLiveness3DFile mocks base method.
func (m *MockDocumentsClient) SaveLiveness3DFile(arg0 context.Context, arg1 *documents.SaveLiveness3DFileReq, arg2 ...grpc.CallOption) (*documents.SaveLiveness3DFileResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SaveLiveness3DFile", varargs...)
	ret0, _ := ret[0].(*documents.SaveLiveness3DFileResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveLiveness3DFile indicates an expected call of SaveLiveness3DFile.
func (mr *MockDocumentsClientMockRecorder) SaveLiveness3DFile(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveLiveness3DFile", reflect.TypeOf((*MockDocumentsClient)(nil).SaveLiveness3DFile), varargs...)
}

// SaveSignedBtsDocuments mocks base method.
func (m *MockDocumentsClient) SaveSignedBtsDocuments(arg0 context.Context, arg1 *documents.SaveSignedBtsDocumentsReq, arg2 ...grpc.CallOption) (*documents.SaveSignedBtsDocumentsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SaveSignedBtsDocuments", varargs...)
	ret0, _ := ret[0].(*documents.SaveSignedBtsDocumentsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveSignedBtsDocuments indicates an expected call of SaveSignedBtsDocuments.
func (mr *MockDocumentsClientMockRecorder) SaveSignedBtsDocuments(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveSignedBtsDocuments", reflect.TypeOf((*MockDocumentsClient)(nil).SaveSignedBtsDocuments), varargs...)
}

// SignDocumentByID mocks base method.
func (m *MockDocumentsClient) SignDocumentByID(arg0 context.Context, arg1 *documents.SignDocumentReq, arg2 ...grpc.CallOption) (*documents.SignDocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SignDocumentByID", varargs...)
	ret0, _ := ret[0].(*documents.SignDocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SignDocumentByID indicates an expected call of SignDocumentByID.
func (mr *MockDocumentsClientMockRecorder) SignDocumentByID(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SignDocumentByID", reflect.TypeOf((*MockDocumentsClient)(nil).SignDocumentByID), varargs...)
}

// SignDocumentByIDWeb mocks base method.
func (m *MockDocumentsClient) SignDocumentByIDWeb(arg0 context.Context, arg1 *documents.SignDocumentWebReq, arg2 ...grpc.CallOption) (*documents.SignDocumentWebResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SignDocumentByIDWeb", varargs...)
	ret0, _ := ret[0].(*documents.SignDocumentWebResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SignDocumentByIDWeb indicates an expected call of SignDocumentByIDWeb.
func (mr *MockDocumentsClientMockRecorder) SignDocumentByIDWeb(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SignDocumentByIDWeb", reflect.TypeOf((*MockDocumentsClient)(nil).SignDocumentByIDWeb), varargs...)
}

// SignDocumentForUser mocks base method.
func (m *MockDocumentsClient) SignDocumentForUser(arg0 context.Context, arg1 *documents.SignDocumentForUserReq, arg2 ...grpc.CallOption) (*documents.SignDocumentForUserResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SignDocumentForUser", varargs...)
	ret0, _ := ret[0].(*documents.SignDocumentForUserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SignDocumentForUser indicates an expected call of SignDocumentForUser.
func (mr *MockDocumentsClientMockRecorder) SignDocumentForUser(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SignDocumentForUser", reflect.TypeOf((*MockDocumentsClient)(nil).SignDocumentForUser), varargs...)
}
