// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package usecase

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/foreign-activity/usecase -i ForeignActivity -t ../../../etc/templates/hook.tmpl -o usecase_hook.gen.go -l ""

import (
	"context"

	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka"

	"git.redmadrobot.com/zaman/backend/zaman/services/foreign-activity/entity"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"
)

var _ ForeignActivity = (*ForeignActivityHook)(nil)

// ForeignActivityHook implements ForeignActivity interface wrapper
type ForeignActivityHook struct {
	ForeignActivity
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// ConversionSum implements ForeignActivity
func (_w *ForeignActivityHook) ConversionSum(ctx context.Context, req *entity.ConversionSumReq) (cp1 *entity.ConversionSumResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.ForeignActivity, "ConversionSum", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.ForeignActivity, "ConversionSum", _params)

	cp1, err = _w.ForeignActivity.ConversionSum(_ctx, req)
	_w._postCall.Hook(_ctx, _w.ForeignActivity, "ConversionSum", []any{cp1, err})
	return cp1, err
}

// CreateConvertation implements ForeignActivity
func (_w *ForeignActivityHook) CreateConvertation(ctx context.Context, req *entity.CreateConvertationReq) (cp1 *entity.CreateConvertationResult, err error) {
	_params := []any{ctx, req}
	defer _w._onPanic.Hook(_w.ForeignActivity, "CreateConvertation", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.ForeignActivity, "CreateConvertation", _params)

	cp1, err = _w.ForeignActivity.CreateConvertation(_ctx, req)
	_w._postCall.Hook(_ctx, _w.ForeignActivity, "CreateConvertation", []any{cp1, err})
	return cp1, err
}

// HealthCheck implements ForeignActivity
func (_w *ForeignActivityHook) HealthCheck(ctx context.Context) (hp1 *entity.Health, err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.ForeignActivity, "HealthCheck", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.ForeignActivity, "HealthCheck", _params)

	hp1, err = _w.ForeignActivity.HealthCheck(_ctx)
	_w._postCall.Hook(_ctx, _w.ForeignActivity, "HealthCheck", []any{hp1, err})
	return hp1, err
}

// HealthEvent implements ForeignActivity
func (_w *ForeignActivityHook) HealthEvent(ctx context.Context, message *kafka.Message) {
	_params := []any{ctx, message}
	defer _w._onPanic.Hook(_w.ForeignActivity, "HealthEvent", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.ForeignActivity, "HealthEvent", _params)

	_w.ForeignActivity.HealthEvent(_ctx, message)
	_w._postCall.Hook(_ctx, _w.ForeignActivity, "HealthEvent", []any{})
	return
}

// InitConsumer implements ForeignActivity
func (_w *ForeignActivityHook) InitConsumer(ctx context.Context) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.ForeignActivity, "InitConsumer", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.ForeignActivity, "InitConsumer", _params)

	_w.ForeignActivity.InitConsumer(_ctx)
	_w._postCall.Hook(_ctx, _w.ForeignActivity, "InitConsumer", []any{})
	return
}

// NewForeignActivityHook returns ForeignActivityHook
func NewForeignActivityHook(object ForeignActivity, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *ForeignActivityHook {
	return &ForeignActivityHook{
		ForeignActivity: object,
		_beforeCall:     beforeCall,
		_postCall:       postCall,
		_onPanic:        onPanic,
	}
}
