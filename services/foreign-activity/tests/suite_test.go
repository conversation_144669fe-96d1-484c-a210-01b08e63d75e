// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package tests

import (
	"context"
	"database/sql"
	"fmt"
	"math/rand"
	"net"
	"os"
	"sync"
	"testing"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/db"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/kafka/doubleconfluent"
	"git.redmadrobot.com/backend-go/rmr-pkg/providers/redis"

	"git.redmadrobot.com/zaman/backend/zaman/services/foreign-activity/storage"
	"git.redmadrobot.com/zaman/backend/zaman/services/foreign-activity/storage/postgres/ent"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"google.golang.org/grpc"

	"git.redmadrobot.com/zaman/backend/zaman/services/foreign-activity/tests/mocks"
	pbForeignActivity "git.redmadrobot.com/zaman/backend/zaman/specs/proto/foreign-activity"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"
	"git.redmadrobot.com/backend-go/rmr-pkg/grpcx"

	"git.redmadrobot.com/zaman/backend/zaman/cmd/services/foreign-activity/providers"
	foreignactivity "git.redmadrobot.com/zaman/backend/zaman/config/services/foreign-activity"
	"git.redmadrobot.com/zaman/backend/zaman/services/foreign-activity/server"
	"git.redmadrobot.com/zaman/backend/zaman/services/foreign-activity/usecase"
)

const (
	reqSource    = "TEST"
	reqSourceKey = "reqSource"
)

// getTestSuffix returns a unique suffix for test databases
func getTestSuffix() string {
	if suffix := os.Getenv("POSTGRES_DB_SUFFIX"); suffix != "" {
		return suffix
	}

	// Generate a unique suffix with timestamp, random component, and process ID
	// Format: test_<timestamp>_<random>_<pid>
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	randomPart := r.Intn(100000)
	pid := os.Getpid()

	return fmt.Sprintf("test_%d_%d_%d", time.Now().UnixNano()%100000, randomPart, pid)
}

// getRandomPortOffset returns a random port offset to avoid conflicts
func getRandomPortOffset() int {
	// Use process ID, random component, and timestamp for more uniqueness
	source := rand.NewSource(time.Now().UnixNano())
	r := rand.New(source)

	// Start with process ID remainder as base offset
	pid := os.Getpid() % 1000

	// Add random offset (1000-5000 range) to avoid conflicts
	randomOffset := r.Intn(4000) + 1000

	return (pid + randomOffset) % 15000
}

type Suite struct {
	suite.Suite

	ctx        context.Context
	cancel     context.CancelFunc
	grpcServer *grpc.ClientConn

	grpc       pbForeignActivity.ForeignactivityClient
	postgresDB *ent.Client
	dbConn     *sql.DB
	redis      *redis.Client
	mocks      mocks.Mocks
	cfg        *foreignactivity.Config

	// Store the database suffix for this test suite instance
	testDbSuffix   string
	grpcPortOffset int
}

func (s *Suite) SetupSuite() {
	var err error
	t := s.T()

	// Set the local time zone to UTC for consistent time handling in tests
	time.Local = time.UTC
	s.cfg, _ = foreignactivity.LoadFromEnv()

	// Generate unique identifiers for this test suite
	s.testDbSuffix = getTestSuffix()
	s.grpcPortOffset = getRandomPortOffset()

	// Create root context
	rootCtx := context.Background()

	// Initialize the logger with UUID for tracing
	log := logs.Logger(rootCtx, s.cfg.Logger, logs.InstanceIDTag.Option(uuid.New()))

	// Set up a context with a cancel function that includes logging
	ctx, cancel := context.WithCancel(log.WithContext(rootCtx))

	// Set the request source in the context
	ctx = context.WithValue(ctx, reqSourceKey, reqSource)

	s.ctx = ctx
	s.cancel = cancel

	// Initialize service locator for dependency injection
	locator := providers.NewServiceLocator(ctx)
	// Initialize Database
	s.cfg.PostgresDB.User = db.DriverPostgres
	s.cfg.PostgresDB.Port = db.CfgDefaultPort
	if os.Getenv("POSTGRES_DB_PORT_TEST") != "" {
		s.cfg.PostgresDB.Port = os.Getenv("POSTGRES_DB_PORT_TEST")
		s.cfg.PostgresDB.Host = "localhost"
	}

	// Create a unique database name for this test run using service name and suffix
	dbNameBase := "foreignactivity"
	s.cfg.PostgresDB.Database = fmt.Sprintf("%s_%s", dbNameBase, s.testDbSuffix)

	dbStorage := s.SetupStorage(ctx)
	locator.Register("Storage", dbStorage)

	// Initialize Kafka
	eventBus := doubleconfluent.NewEventBusDouble(s.cfg.Kafka.Prefix, true)
	t.Cleanup(func() { eventBus.Close(ctx) })
	locator.Register("Event", eventBus)

	// Initialize Redis cache
	if os.Getenv("REDIS_DB_PORT_TEST") != "" {
		s.cfg.Redis.Port = os.Getenv("REDIS_DB_PORT_TEST")
		s.cfg.Redis.Host = "localhost"

		// Use a unique DB number for each test run to avoid conflicts
		dbNum := s.cfg.Redis.Database
		if dbNum == 0 {
			dbNum = 1 // Default to DB 1 if not specified
		}

		// Generate a unique database number based on multiple factors
		r := rand.New(rand.NewSource(time.Now().UnixNano()))
		dbOffset := r.Intn(10) // 0-9 offset

		// Redis has 16 databases (0-15), ensure we stay in that range
		s.cfg.Redis.Database = (dbNum + dbOffset) % 16
	} else {
		redisServer := redis.NewTestRedisServer()
		err = redisServer.Run(*s.cfg.Redis)
		s.dieOnErr(err)
		t.Cleanup(func() { redisServer.Close() })
	}
	// Create a new Redis client
	redisClient, err := redis.NewClient(ctx, s.cfg.Redis)
	s.dieOnErr(err)
	s.redis = redisClient

	// Register the Redis client in the service locator
	locator.Register("Redis", redisClient)

	// Initialize Mocks for external services
	s.mocks = *mocks.NewMocks(t)

	// Register mocked gRPC clients in the service locator
	locator.Register("Users", s.mocks.GRPC.Users)
	locator.Register("Payments", s.mocks.GRPC.Payments)
	locator.Register("Dictionary", s.mocks.GRPC.Dictionary)
	locator.Register("Notifications", s.mocks.GRPC.Notifications)

	// Register mocked providers in the service locator
	// Initialize Use Cases
	useCases := usecase.New(s.ctx, *locator, s.cfg)

	// gRPC port generation
	grpcPort, err := getFreePort()
	s.dieOnErr(err, "failed to get free port")
	s.cfg.GRPC.Port = grpcPort

	// gRPC server
	service := server.NewServerOptions(useCases, s.cfg)
	grpcServer, err := service.NewServer(s.cfg.GRPC)
	s.dieOnErr(err)

	// Chanel for start notification
	ready := make(chan struct{})

	// Running gRPC server
	go func() {
		close(ready) // Сигнализируем, что сервер стартует (после Listen, если нужно, можно подвинуть)
		err := grpcx.StartServer(ctx, s.cfg.GRPC, grpcServer)
		s.dieOnErr(err)
	}()

	// Wait for the server to start
	<-ready

	// Wait for gRPC server to be ready
	if err := waitForPortReady(grpcPort, 5*time.Second); err != nil {
		s.dieOnErr(fmt.Errorf("gRPC server not ready on port %s: %w", grpcPort, err))
	}

	// Register gRPC client
	s.SetupGRPCClient(grpcPort)
}

func getFreePort() (string, error) {
	l, err := net.Listen("tcp", ":0")
	if err != nil {
		return "", fmt.Errorf("get free port: %w", err)
	}
	defer l.Close()
	return fmt.Sprintf("%d", l.Addr().(*net.TCPAddr).Port), nil
}

func waitForPortReady(port string, timeout time.Duration) error {
	deadline := time.Now().Add(timeout)
	for time.Now().Before(deadline) {
		conn, err := net.DialTimeout("tcp", net.JoinHostPort("localhost", port), 100*time.Millisecond)
		if err == nil {
			conn.Close()
			return nil
		}
		time.Sleep(100 * time.Millisecond)
	}
	return fmt.Errorf("timeout waiting for port %s to be ready", port)
}

// TearDownSuite Cleanup the suite at the end of tests
func (s *Suite) TearDownSuite() {
	if s.cancel != nil {
		s.cancel()
	}

	var wg sync.WaitGroup
	errCh := make(chan error, 3)

	if s.grpcServer != nil {
		wg.Add(1)
		go func() {
			defer wg.Done()
			if err := s.grpcServer.Close(); err != nil {
				errCh <- fmt.Errorf("failed to close gRPC server: %w", err)
			}
		}()
	}
	if s.redis != nil {
		wg.Add(1)
		go func() {
			defer wg.Done()
			s.redis.Close(s.ctx)
		}()
	}
	if s.postgresDB != nil {
		wg.Add(1)
		go func() {
			defer wg.Done()
			if err := s.postgresDB.Close(); err != nil {
				errCh <- fmt.Errorf("failed to close database connection: %w", err)
			}
		}()
	}

	wg.Wait()
	close(errCh)
	for err := range errCh {
		s.T().Errorf("Error during cleanup: %v", err)
	}
}

// SetupTest cleans up the test environment by truncating the database before each test.
func (s *Suite) SetupTest() {
	err := s.TruncateDatabase()
	s.dieOnErr(err)
}

// Helper function to terminate the test suite on error
func (s *Suite) dieOnErr(err error, msg ...string) bool {
	if err != nil {
		return s.FailNow(fmt.Sprintf("Unexpected error %s", msg), err)
	}
	return true
}

// TestRunner Main test runner for the suite
func TestRunner(t *testing.T) {
	// Enable parallel execution of tests for better performance
	// Each test suite will run in its own goroutine
	t.Parallel()

	suite.Run(t, new(Suite))
}
func (s *Suite) SetupStorage(ctx context.Context) *storage.StorageHook {
	// Create a test database
	admConn, err := s.CreateTestDB()
	s.dieOnErr(err)
	defer admConn.Close()

	// Run database migrations
	err = db.Migrate(ctx, s.cfg.PostgresDB, db.MigrateDefault(s.cfg.PostgresDB))
	s.dieOnErr(err)

	// Get the database driver
	dbDriver, err := entx.Driver(s.cfg.PostgresDB)
	s.dieOnErr(err)

	// Set the database connection
	s.dbConn = dbDriver.DB()

	// Initialize the ent client (ORM for database interaction)
	postgresDBClient, err := storage.NewPostgresDBClient(ctx, dbDriver, s.cfg.PostgresDB.Debug)
	s.dieOnErr(err)

	deps := storage.StorageDependencies{
		PostgresClient: postgresDBClient,
		SQLClient:      dbDriver,
	}

	// Register the storage component in the service locator
	dbStorage := storage.NewStorage(deps)
	s.postgresDB = postgresDBClient

	return dbStorage
}
