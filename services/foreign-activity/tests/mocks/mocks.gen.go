// Code generated by git.redmadrobot.com/backend-go/meroving. DO NOT EDIT.
package mocks

import (
	"testing"

	"github.com/golang/mock/gomock"
)

type Mocks struct {
	GRPC      GRPC
	Providers Providers
}

type GRPC struct {
	Users         *MockUsersClient
	Payments      *MockPaymentsClient
	Dictionary    *MockDictionaryClient
	Notifications *MockNotificationsClient
}

type Providers struct {
}

func NewMocks(t *testing.T) *Mocks {
	return &Mocks{
		GRPC: GRPC{
			Users:         NewMockUsersClient(gomock.NewController(t)),
			Payments:      NewMockPaymentsClient(gomock.NewController(t)),
			Dictionary:    NewMockDictionaryClient(gomock.NewController(t)),
			Notifications: NewMockNotificationsClient(gomock.NewController(t)),
		},
		Providers: Providers{},
	}
}
