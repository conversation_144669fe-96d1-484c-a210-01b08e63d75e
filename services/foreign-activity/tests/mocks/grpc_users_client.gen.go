// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/specs/proto/users (interfaces: UsersClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"

	users "git.redmadrobot.com/zaman/backend/zaman/specs/proto/users"
)

// MockUsersClient is a mock of UsersClient interface.
type MockUsersClient struct {
	ctrl     *gomock.Controller
	recorder *MockUsersClientMockRecorder
}

// MockUsersClientMockRecorder is the mock recorder for MockUsersClient.
type MockUsersClientMockRecorder struct {
	mock *MockUsersClient
}

// NewMockUsersClient creates a new mock instance.
func NewMockUsersClient(ctrl *gomock.Controller) *MockUsersClient {
	mock := &MockUsersClient{ctrl: ctrl}
	mock.recorder = &MockUsersClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUsersClient) EXPECT() *MockUsersClientMockRecorder {
	return m.recorder
}

// ConfirmLogin mocks base method.
func (m *MockUsersClient) ConfirmLogin(arg0 context.Context, arg1 *users.ConfirmLoginReq, arg2 ...grpc.CallOption) (*users.ConfirmLoginResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConfirmLogin", varargs...)
	ret0, _ := ret[0].(*users.ConfirmLoginResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmLogin indicates an expected call of ConfirmLogin.
func (mr *MockUsersClientMockRecorder) ConfirmLogin(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmLogin", reflect.TypeOf((*MockUsersClient)(nil).ConfirmLogin), varargs...)
}

// ForceLogoutByPhoneNumber mocks base method.
func (m *MockUsersClient) ForceLogoutByPhoneNumber(arg0 context.Context, arg1 *users.ForceLogoutByPhoneNumberReq, arg2 ...grpc.CallOption) (*users.ForceLogoutByPhoneNumberResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ForceLogoutByPhoneNumber", varargs...)
	ret0, _ := ret[0].(*users.ForceLogoutByPhoneNumberResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ForceLogoutByPhoneNumber indicates an expected call of ForceLogoutByPhoneNumber.
func (mr *MockUsersClientMockRecorder) ForceLogoutByPhoneNumber(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ForceLogoutByPhoneNumber", reflect.TypeOf((*MockUsersClient)(nil).ForceLogoutByPhoneNumber), varargs...)
}

// GetDocumentForSign mocks base method.
func (m *MockUsersClient) GetDocumentForSign(arg0 context.Context, arg1 *users.GetDocumentForSignReq, arg2 ...grpc.CallOption) (*users.SignDocumentResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDocumentForSign", varargs...)
	ret0, _ := ret[0].(*users.SignDocumentResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDocumentForSign indicates an expected call of GetDocumentForSign.
func (mr *MockUsersClientMockRecorder) GetDocumentForSign(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDocumentForSign", reflect.TypeOf((*MockUsersClient)(nil).GetDocumentForSign), varargs...)
}

// GetLivenessLink mocks base method.
func (m *MockUsersClient) GetLivenessLink(arg0 context.Context, arg1 *users.GetLivenessLinkReq, arg2 ...grpc.CallOption) (*users.GetLivenessLinkResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLivenessLink", varargs...)
	ret0, _ := ret[0].(*users.GetLivenessLinkResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLivenessLink indicates an expected call of GetLivenessLink.
func (mr *MockUsersClientMockRecorder) GetLivenessLink(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLivenessLink", reflect.TypeOf((*MockUsersClient)(nil).GetLivenessLink), varargs...)
}

// GetPersonalData mocks base method.
func (m *MockUsersClient) GetPersonalData(arg0 context.Context, arg1 *users.GetPersonalDataReq, arg2 ...grpc.CallOption) (*users.GetPersonalDataResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPersonalData", varargs...)
	ret0, _ := ret[0].(*users.GetPersonalDataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPersonalData indicates an expected call of GetPersonalData.
func (mr *MockUsersClientMockRecorder) GetPersonalData(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersonalData", reflect.TypeOf((*MockUsersClient)(nil).GetPersonalData), varargs...)
}

// GetUserByID mocks base method.
func (m *MockUsersClient) GetUserByID(arg0 context.Context, arg1 *users.GetUserByIDReq, arg2 ...grpc.CallOption) (*users.GetUserByIDResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserByID", varargs...)
	ret0, _ := ret[0].(*users.GetUserByIDResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserByID indicates an expected call of GetUserByID.
func (mr *MockUsersClientMockRecorder) GetUserByID(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserByID", reflect.TypeOf((*MockUsersClient)(nil).GetUserByID), varargs...)
}

// GetUserByIIN mocks base method.
func (m *MockUsersClient) GetUserByIIN(arg0 context.Context, arg1 *users.GetUserByIINReq, arg2 ...grpc.CallOption) (*users.GetUserByIINResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserByIIN", varargs...)
	ret0, _ := ret[0].(*users.GetUserByIINResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserByIIN indicates an expected call of GetUserByIIN.
func (mr *MockUsersClientMockRecorder) GetUserByIIN(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserByIIN", reflect.TypeOf((*MockUsersClient)(nil).GetUserByIIN), varargs...)
}

// GetUserByPhoneNumber mocks base method.
func (m *MockUsersClient) GetUserByPhoneNumber(arg0 context.Context, arg1 *users.GetUserByPhoneNumberReq, arg2 ...grpc.CallOption) (*users.GetUserByPhoneNumberResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserByPhoneNumber", varargs...)
	ret0, _ := ret[0].(*users.GetUserByPhoneNumberResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserByPhoneNumber indicates an expected call of GetUserByPhoneNumber.
func (mr *MockUsersClientMockRecorder) GetUserByPhoneNumber(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserByPhoneNumber", reflect.TypeOf((*MockUsersClient)(nil).GetUserByPhoneNumber), varargs...)
}

// GetUserDataByID mocks base method.
func (m *MockUsersClient) GetUserDataByID(arg0 context.Context, arg1 *users.UserDataReq, arg2 ...grpc.CallOption) (*users.UserDataResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserDataByID", varargs...)
	ret0, _ := ret[0].(*users.UserDataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserDataByID indicates an expected call of GetUserDataByID.
func (mr *MockUsersClientMockRecorder) GetUserDataByID(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDataByID", reflect.TypeOf((*MockUsersClient)(nil).GetUserDataByID), varargs...)
}

// GetUserDebts mocks base method.
func (m *MockUsersClient) GetUserDebts(arg0 context.Context, arg1 *users.GetUserDebtsReq, arg2 ...grpc.CallOption) (*users.GetUserDebtsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserDebts", varargs...)
	ret0, _ := ret[0].(*users.GetUserDebtsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserDebts indicates an expected call of GetUserDebts.
func (mr *MockUsersClientMockRecorder) GetUserDebts(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDebts", reflect.TypeOf((*MockUsersClient)(nil).GetUserDebts), varargs...)
}

// GetUserSmeIPInfo mocks base method.
func (m *MockUsersClient) GetUserSmeIPInfo(arg0 context.Context, arg1 *users.GetUserSmeIPInfoReq, arg2 ...grpc.CallOption) (*users.GetUserSmeIPInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserSmeIPInfo", varargs...)
	ret0, _ := ret[0].(*users.GetUserSmeIPInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserSmeIPInfo indicates an expected call of GetUserSmeIPInfo.
func (mr *MockUsersClientMockRecorder) GetUserSmeIPInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserSmeIPInfo", reflect.TypeOf((*MockUsersClient)(nil).GetUserSmeIPInfo), varargs...)
}

// GetUsers mocks base method.
func (m *MockUsersClient) GetUsers(arg0 context.Context, arg1 *users.GetUsersReq, arg2 ...grpc.CallOption) (*users.GetUsersResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUsers", varargs...)
	ret0, _ := ret[0].(*users.GetUsersResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUsers indicates an expected call of GetUsers.
func (mr *MockUsersClientMockRecorder) GetUsers(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsers", reflect.TypeOf((*MockUsersClient)(nil).GetUsers), varargs...)
}

// GetUsersBySpec mocks base method.
func (m *MockUsersClient) GetUsersBySpec(arg0 context.Context, arg1 *users.GetUsersBySpecReq, arg2 ...grpc.CallOption) (*users.GetUsersBySpecResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUsersBySpec", varargs...)
	ret0, _ := ret[0].(*users.GetUsersBySpecResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUsersBySpec indicates an expected call of GetUsersBySpec.
func (mr *MockUsersClientMockRecorder) GetUsersBySpec(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsersBySpec", reflect.TypeOf((*MockUsersClient)(nil).GetUsersBySpec), varargs...)
}

// HealthCheck mocks base method.
func (m *MockUsersClient) HealthCheck(arg0 context.Context, arg1 *users.HealthCheckReq, arg2 ...grpc.CallOption) (*users.HealthCheckResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HealthCheck", varargs...)
	ret0, _ := ret[0].(*users.HealthCheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockUsersClientMockRecorder) HealthCheck(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockUsersClient)(nil).HealthCheck), varargs...)
}

// Identify mocks base method.
func (m *MockUsersClient) Identify(arg0 context.Context, arg1 *users.IdentifyReq, arg2 ...grpc.CallOption) (*users.IdentifyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Identify", varargs...)
	ret0, _ := ret[0].(*users.IdentifyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Identify indicates an expected call of Identify.
func (mr *MockUsersClientMockRecorder) Identify(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Identify", reflect.TypeOf((*MockUsersClient)(nil).Identify), varargs...)
}

// Login mocks base method.
func (m *MockUsersClient) Login(arg0 context.Context, arg1 *users.LoginReq, arg2 ...grpc.CallOption) (*users.LoginResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Login", varargs...)
	ret0, _ := ret[0].(*users.LoginResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Login indicates an expected call of Login.
func (mr *MockUsersClientMockRecorder) Login(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Login", reflect.TypeOf((*MockUsersClient)(nil).Login), varargs...)
}

// Logout mocks base method.
func (m *MockUsersClient) Logout(arg0 context.Context, arg1 *users.LogoutReq, arg2 ...grpc.CallOption) (*users.LogoutResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Logout", varargs...)
	ret0, _ := ret[0].(*users.LogoutResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Logout indicates an expected call of Logout.
func (mr *MockUsersClientMockRecorder) Logout(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Logout", reflect.TypeOf((*MockUsersClient)(nil).Logout), varargs...)
}

// NewAmlCheckByUser mocks base method.
func (m *MockUsersClient) NewAmlCheckByUser(arg0 context.Context, arg1 *users.NewAmlCheckByUserRequest, arg2 ...grpc.CallOption) (*users.NewAmlCheckByUserResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "NewAmlCheckByUser", varargs...)
	ret0, _ := ret[0].(*users.NewAmlCheckByUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewAmlCheckByUser indicates an expected call of NewAmlCheckByUser.
func (mr *MockUsersClientMockRecorder) NewAmlCheckByUser(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewAmlCheckByUser", reflect.TypeOf((*MockUsersClient)(nil).NewAmlCheckByUser), varargs...)
}

// RefreshToken mocks base method.
func (m *MockUsersClient) RefreshToken(arg0 context.Context, arg1 *users.RefreshTokenReq, arg2 ...grpc.CallOption) (*users.RefreshTokenResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RefreshToken", varargs...)
	ret0, _ := ret[0].(*users.RefreshTokenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RefreshToken indicates an expected call of RefreshToken.
func (mr *MockUsersClientMockRecorder) RefreshToken(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefreshToken", reflect.TypeOf((*MockUsersClient)(nil).RefreshToken), varargs...)
}

// UpdateUserLocale mocks base method.
func (m *MockUsersClient) UpdateUserLocale(arg0 context.Context, arg1 *users.UpdateUserLocaleReq, arg2 ...grpc.CallOption) (*users.UpdateUserLocaleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateUserLocale", varargs...)
	ret0, _ := ret[0].(*users.UpdateUserLocaleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateUserLocale indicates an expected call of UpdateUserLocale.
func (mr *MockUsersClientMockRecorder) UpdateUserLocale(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserLocale", reflect.TypeOf((*MockUsersClient)(nil).UpdateUserLocale), varargs...)
}
