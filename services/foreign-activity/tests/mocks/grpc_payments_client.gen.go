// Code generated by MockGen. DO NOT EDIT.
// Source: git.redmadrobot.com/zaman/backend/zaman/specs/proto/payments (interfaces: PaymentsClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"

	payments "git.redmadrobot.com/zaman/backend/zaman/specs/proto/payments"
)

// MockPaymentsClient is a mock of PaymentsClient interface.
type MockPaymentsClient struct {
	ctrl     *gomock.Controller
	recorder *MockPaymentsClientMockRecorder
}

// MockPaymentsClientMockRecorder is the mock recorder for MockPaymentsClient.
type MockPaymentsClientMockRecorder struct {
	mock *MockPaymentsClient
}

// NewMockPaymentsClient creates a new mock instance.
func NewMockPaymentsClient(ctrl *gomock.Controller) *MockPaymentsClient {
	mock := &MockPaymentsClient{ctrl: ctrl}
	mock.recorder = &MockPaymentsClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPaymentsClient) EXPECT() *MockPaymentsClientMockRecorder {
	return m.recorder
}

// AstanaPlat mocks base method.
func (m *MockPaymentsClient) AstanaPlat(arg0 context.Context, arg1 *payments.AstanaPlatReq, arg2 ...grpc.CallOption) (*payments.AstanaPlatResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AstanaPlat", varargs...)
	ret0, _ := ret[0].(*payments.AstanaPlatResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AstanaPlat indicates an expected call of AstanaPlat.
func (mr *MockPaymentsClientMockRecorder) AstanaPlat(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AstanaPlat", reflect.TypeOf((*MockPaymentsClient)(nil).AstanaPlat), varargs...)
}

// CheckAccountIin mocks base method.
func (m *MockPaymentsClient) CheckAccountIin(arg0 context.Context, arg1 *payments.CheckAccountIinReq, arg2 ...grpc.CallOption) (*payments.CheckAccountIinResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckAccountIin", varargs...)
	ret0, _ := ret[0].(*payments.CheckAccountIinResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckAccountIin indicates an expected call of CheckAccountIin.
func (mr *MockPaymentsClientMockRecorder) CheckAccountIin(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAccountIin", reflect.TypeOf((*MockPaymentsClient)(nil).CheckAccountIin), varargs...)
}

// CheckClientByPhoneNumber mocks base method.
func (m *MockPaymentsClient) CheckClientByPhoneNumber(arg0 context.Context, arg1 *payments.CheckClientByPhoneNumberReq, arg2 ...grpc.CallOption) (*payments.CheckClientByPhoneNumberResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckClientByPhoneNumber", varargs...)
	ret0, _ := ret[0].(*payments.CheckClientByPhoneNumberResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckClientByPhoneNumber indicates an expected call of CheckClientByPhoneNumber.
func (mr *MockPaymentsClientMockRecorder) CheckClientByPhoneNumber(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckClientByPhoneNumber", reflect.TypeOf((*MockPaymentsClient)(nil).CheckClientByPhoneNumber), varargs...)
}

// CheckPhoneNumber mocks base method.
func (m *MockPaymentsClient) CheckPhoneNumber(arg0 context.Context, arg1 *payments.CheckPhoneNumberReq, arg2 ...grpc.CallOption) (*payments.CheckPhoneNumberResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckPhoneNumber", varargs...)
	ret0, _ := ret[0].(*payments.CheckPhoneNumberResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckPhoneNumber indicates an expected call of CheckPhoneNumber.
func (mr *MockPaymentsClientMockRecorder) CheckPhoneNumber(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckPhoneNumber", reflect.TypeOf((*MockPaymentsClient)(nil).CheckPhoneNumber), varargs...)
}

// ConfirmInternalPaymentByPhoneNumber mocks base method.
func (m *MockPaymentsClient) ConfirmInternalPaymentByPhoneNumber(arg0 context.Context, arg1 *payments.ConfirmInternalPaymentByPhoneNumberReq, arg2 ...grpc.CallOption) (*payments.ConfirmInternalPaymentByPhoneNumberResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConfirmInternalPaymentByPhoneNumber", varargs...)
	ret0, _ := ret[0].(*payments.ConfirmInternalPaymentByPhoneNumberResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmInternalPaymentByPhoneNumber indicates an expected call of ConfirmInternalPaymentByPhoneNumber.
func (mr *MockPaymentsClientMockRecorder) ConfirmInternalPaymentByPhoneNumber(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmInternalPaymentByPhoneNumber", reflect.TypeOf((*MockPaymentsClient)(nil).ConfirmInternalPaymentByPhoneNumber), varargs...)
}

// ConfirmPaymentByAccount mocks base method.
func (m *MockPaymentsClient) ConfirmPaymentByAccount(arg0 context.Context, arg1 *payments.ConfirmPaymentByAccountReq, arg2 ...grpc.CallOption) (*payments.ConfirmPaymentByAccountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConfirmPaymentByAccount", varargs...)
	ret0, _ := ret[0].(*payments.ConfirmPaymentByAccountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmPaymentByAccount indicates an expected call of ConfirmPaymentByAccount.
func (mr *MockPaymentsClientMockRecorder) ConfirmPaymentByAccount(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmPaymentByAccount", reflect.TypeOf((*MockPaymentsClient)(nil).ConfirmPaymentByAccount), varargs...)
}

// ConfirmPaymentForMobile mocks base method.
func (m *MockPaymentsClient) ConfirmPaymentForMobile(arg0 context.Context, arg1 *payments.ConfirmPaymentForMobileReq, arg2 ...grpc.CallOption) (*payments.ConfirmPaymentForMobileResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConfirmPaymentForMobile", varargs...)
	ret0, _ := ret[0].(*payments.ConfirmPaymentForMobileResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmPaymentForMobile indicates an expected call of ConfirmPaymentForMobile.
func (mr *MockPaymentsClientMockRecorder) ConfirmPaymentForMobile(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmPaymentForMobile", reflect.TypeOf((*MockPaymentsClient)(nil).ConfirmPaymentForMobile), varargs...)
}

// CreateInternalPaymentByPhoneNumber mocks base method.
func (m *MockPaymentsClient) CreateInternalPaymentByPhoneNumber(arg0 context.Context, arg1 *payments.CreateInternalPaymentByPhoneNumberReq, arg2 ...grpc.CallOption) (*payments.CreateInternalPaymentByPhoneNumberResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateInternalPaymentByPhoneNumber", varargs...)
	ret0, _ := ret[0].(*payments.CreateInternalPaymentByPhoneNumberResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateInternalPaymentByPhoneNumber indicates an expected call of CreateInternalPaymentByPhoneNumber.
func (mr *MockPaymentsClientMockRecorder) CreateInternalPaymentByPhoneNumber(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInternalPaymentByPhoneNumber", reflect.TypeOf((*MockPaymentsClient)(nil).CreateInternalPaymentByPhoneNumber), varargs...)
}

// CreatePaymentByAccount mocks base method.
func (m *MockPaymentsClient) CreatePaymentByAccount(arg0 context.Context, arg1 *payments.CreatePaymentByAccountReq, arg2 ...grpc.CallOption) (*payments.CreatePaymentByAccountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreatePaymentByAccount", varargs...)
	ret0, _ := ret[0].(*payments.CreatePaymentByAccountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePaymentByAccount indicates an expected call of CreatePaymentByAccount.
func (mr *MockPaymentsClientMockRecorder) CreatePaymentByAccount(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePaymentByAccount", reflect.TypeOf((*MockPaymentsClient)(nil).CreatePaymentByAccount), varargs...)
}

// CreatePaymentForMobile mocks base method.
func (m *MockPaymentsClient) CreatePaymentForMobile(arg0 context.Context, arg1 *payments.CreatePaymentForMobileReq, arg2 ...grpc.CallOption) (*payments.CreatePaymentForMobileResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreatePaymentForMobile", varargs...)
	ret0, _ := ret[0].(*payments.CreatePaymentForMobileResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePaymentForMobile indicates an expected call of CreatePaymentForMobile.
func (mr *MockPaymentsClientMockRecorder) CreatePaymentForMobile(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePaymentForMobile", reflect.TypeOf((*MockPaymentsClient)(nil).CreatePaymentForMobile), varargs...)
}

// CreatePaymentKaspiQR mocks base method.
func (m *MockPaymentsClient) CreatePaymentKaspiQR(arg0 context.Context, arg1 *payments.CreatePaymentKaspiQRReq, arg2 ...grpc.CallOption) (*payments.CreatePaymentKaspiQRResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreatePaymentKaspiQR", varargs...)
	ret0, _ := ret[0].(*payments.CreatePaymentKaspiQRResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePaymentKaspiQR indicates an expected call of CreatePaymentKaspiQR.
func (mr *MockPaymentsClientMockRecorder) CreatePaymentKaspiQR(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePaymentKaspiQR", reflect.TypeOf((*MockPaymentsClient)(nil).CreatePaymentKaspiQR), varargs...)
}

// CreateSelfTransfer mocks base method.
func (m *MockPaymentsClient) CreateSelfTransfer(arg0 context.Context, arg1 *payments.CreateSelfTransferReq, arg2 ...grpc.CallOption) (*payments.CreateSelfTransferResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateSelfTransfer", varargs...)
	ret0, _ := ret[0].(*payments.CreateSelfTransferResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSelfTransfer indicates an expected call of CreateSelfTransfer.
func (mr *MockPaymentsClientMockRecorder) CreateSelfTransfer(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSelfTransfer", reflect.TypeOf((*MockPaymentsClient)(nil).CreateSelfTransfer), varargs...)
}

// GetTransactionByID mocks base method.
func (m *MockPaymentsClient) GetTransactionByID(arg0 context.Context, arg1 *payments.GetTransactionByIdReq, arg2 ...grpc.CallOption) (*payments.GetTransactionByIdResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransactionByID", varargs...)
	ret0, _ := ret[0].(*payments.GetTransactionByIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionByID indicates an expected call of GetTransactionByID.
func (mr *MockPaymentsClientMockRecorder) GetTransactionByID(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionByID", reflect.TypeOf((*MockPaymentsClient)(nil).GetTransactionByID), varargs...)
}

// GetTransactionReceipt mocks base method.
func (m *MockPaymentsClient) GetTransactionReceipt(arg0 context.Context, arg1 *payments.GetTransactionReceiptReq, arg2 ...grpc.CallOption) (*payments.GetTransactionReceiptResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransactionReceipt", varargs...)
	ret0, _ := ret[0].(*payments.GetTransactionReceiptResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionReceipt indicates an expected call of GetTransactionReceipt.
func (mr *MockPaymentsClientMockRecorder) GetTransactionReceipt(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionReceipt", reflect.TypeOf((*MockPaymentsClient)(nil).GetTransactionReceipt), varargs...)
}

// GetTransactions mocks base method.
func (m *MockPaymentsClient) GetTransactions(arg0 context.Context, arg1 *payments.GetTransactionsReq, arg2 ...grpc.CallOption) (*payments.GetTransactionsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransactions", varargs...)
	ret0, _ := ret[0].(*payments.GetTransactionsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactions indicates an expected call of GetTransactions.
func (mr *MockPaymentsClientMockRecorder) GetTransactions(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactions", reflect.TypeOf((*MockPaymentsClient)(nil).GetTransactions), varargs...)
}

// HealthCheck mocks base method.
func (m *MockPaymentsClient) HealthCheck(arg0 context.Context, arg1 *payments.HealthCheckReq, arg2 ...grpc.CallOption) (*payments.HealthCheckResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HealthCheck", varargs...)
	ret0, _ := ret[0].(*payments.HealthCheckResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HealthCheck indicates an expected call of HealthCheck.
func (mr *MockPaymentsClientMockRecorder) HealthCheck(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HealthCheck", reflect.TypeOf((*MockPaymentsClient)(nil).HealthCheck), varargs...)
}

// LoadAccountTransactions mocks base method.
func (m *MockPaymentsClient) LoadAccountTransactions(arg0 context.Context, arg1 *payments.LoadAccountTransactionsReq, arg2 ...grpc.CallOption) (*payments.LoadAccountTransactionsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LoadAccountTransactions", varargs...)
	ret0, _ := ret[0].(*payments.LoadAccountTransactionsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadAccountTransactions indicates an expected call of LoadAccountTransactions.
func (mr *MockPaymentsClientMockRecorder) LoadAccountTransactions(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadAccountTransactions", reflect.TypeOf((*MockPaymentsClient)(nil).LoadAccountTransactions), varargs...)
}

// QRSessionTermination mocks base method.
func (m *MockPaymentsClient) QRSessionTermination(arg0 context.Context, arg1 *payments.QRSessionTerminationReq, arg2 ...grpc.CallOption) (*payments.QRSessionTerminationResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QRSessionTermination", varargs...)
	ret0, _ := ret[0].(*payments.QRSessionTerminationResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QRSessionTermination indicates an expected call of QRSessionTermination.
func (mr *MockPaymentsClientMockRecorder) QRSessionTermination(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QRSessionTermination", reflect.TypeOf((*MockPaymentsClient)(nil).QRSessionTermination), varargs...)
}

// QRToken mocks base method.
func (m *MockPaymentsClient) QRToken(arg0 context.Context, arg1 *payments.QRTokenReq, arg2 ...grpc.CallOption) (*payments.QRTokenResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QRToken", varargs...)
	ret0, _ := ret[0].(*payments.QRTokenResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QRToken indicates an expected call of QRToken.
func (mr *MockPaymentsClientMockRecorder) QRToken(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QRToken", reflect.TypeOf((*MockPaymentsClient)(nil).QRToken), varargs...)
}
