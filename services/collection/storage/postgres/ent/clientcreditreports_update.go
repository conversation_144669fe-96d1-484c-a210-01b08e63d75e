// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/collection/storage/postgres/ent/clientcreditreports"
	"git.redmadrobot.com/zaman/backend/zaman/services/collection/storage/postgres/ent/internal"
	"git.redmadrobot.com/zaman/backend/zaman/services/collection/storage/postgres/ent/predicate"
)

// ClientCreditReportsUpdate is the builder for updating ClientCreditReports entities.
type ClientCreditReportsUpdate struct {
	config
	hooks     []Hook
	mutation  *ClientCreditReportsMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the ClientCreditReportsUpdate builder.
func (_u *ClientCreditReportsUpdate) Where(ps ...predicate.ClientCreditReports) *ClientCreditReportsUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetIdentityDocumentNumber sets the "identity_document_number" field.
func (_u *ClientCreditReportsUpdate) SetIdentityDocumentNumber(v string) *ClientCreditReportsUpdate {
	_u.mutation.SetIdentityDocumentNumber(v)
	return _u
}

// SetNillableIdentityDocumentNumber sets the "identity_document_number" field if the given value is not nil.
func (_u *ClientCreditReportsUpdate) SetNillableIdentityDocumentNumber(v *string) *ClientCreditReportsUpdate {
	if v != nil {
		_u.SetIdentityDocumentNumber(*v)
	}
	return _u
}

// SetReportDate sets the "report_date" field.
func (_u *ClientCreditReportsUpdate) SetReportDate(v time.Time) *ClientCreditReportsUpdate {
	_u.mutation.SetReportDate(v)
	return _u
}

// SetNillableReportDate sets the "report_date" field if the given value is not nil.
func (_u *ClientCreditReportsUpdate) SetNillableReportDate(v *time.Time) *ClientCreditReportsUpdate {
	if v != nil {
		_u.SetReportDate(*v)
	}
	return _u
}

// SetDocumentType sets the "document_type" field.
func (_u *ClientCreditReportsUpdate) SetDocumentType(v string) *ClientCreditReportsUpdate {
	_u.mutation.SetDocumentType(v)
	return _u
}

// SetNillableDocumentType sets the "document_type" field if the given value is not nil.
func (_u *ClientCreditReportsUpdate) SetNillableDocumentType(v *string) *ClientCreditReportsUpdate {
	if v != nil {
		_u.SetDocumentType(*v)
	}
	return _u
}

// SetFileLink sets the "file_link" field.
func (_u *ClientCreditReportsUpdate) SetFileLink(v string) *ClientCreditReportsUpdate {
	_u.mutation.SetFileLink(v)
	return _u
}

// SetNillableFileLink sets the "file_link" field if the given value is not nil.
func (_u *ClientCreditReportsUpdate) SetNillableFileLink(v *string) *ClientCreditReportsUpdate {
	if v != nil {
		_u.SetFileLink(*v)
	}
	return _u
}

// SetUpdatedAt sets the "updated_at" field.
func (_u *ClientCreditReportsUpdate) SetUpdatedAt(v time.Time) *ClientCreditReportsUpdate {
	_u.mutation.SetUpdatedAt(v)
	return _u
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (_u *ClientCreditReportsUpdate) SetNillableUpdatedAt(v *time.Time) *ClientCreditReportsUpdate {
	if v != nil {
		_u.SetUpdatedAt(*v)
	}
	return _u
}

// SetDeletedAt sets the "deleted_at" field.
func (_u *ClientCreditReportsUpdate) SetDeletedAt(v time.Time) *ClientCreditReportsUpdate {
	_u.mutation.SetDeletedAt(v)
	return _u
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (_u *ClientCreditReportsUpdate) SetNillableDeletedAt(v *time.Time) *ClientCreditReportsUpdate {
	if v != nil {
		_u.SetDeletedAt(*v)
	}
	return _u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (_u *ClientCreditReportsUpdate) ClearDeletedAt() *ClientCreditReportsUpdate {
	_u.mutation.ClearDeletedAt()
	return _u
}

// Mutation returns the ClientCreditReportsMutation object of the builder.
func (_u *ClientCreditReportsUpdate) Mutation() *ClientCreditReportsMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *ClientCreditReportsUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *ClientCreditReportsUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *ClientCreditReportsUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *ClientCreditReportsUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *ClientCreditReportsUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *ClientCreditReportsUpdate {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *ClientCreditReportsUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	_spec := sqlgraph.NewUpdateSpec(clientcreditreports.Table, clientcreditreports.Columns, sqlgraph.NewFieldSpec(clientcreditreports.FieldID, field.TypeInt64))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.IdentityDocumentNumber(); ok {
		_spec.SetField(clientcreditreports.FieldIdentityDocumentNumber, field.TypeString, value)
	}
	if value, ok := _u.mutation.ReportDate(); ok {
		_spec.SetField(clientcreditreports.FieldReportDate, field.TypeTime, value)
	}
	if value, ok := _u.mutation.DocumentType(); ok {
		_spec.SetField(clientcreditreports.FieldDocumentType, field.TypeString, value)
	}
	if value, ok := _u.mutation.FileLink(); ok {
		_spec.SetField(clientcreditreports.FieldFileLink, field.TypeString, value)
	}
	if value, ok := _u.mutation.UpdatedAt(); ok {
		_spec.SetField(clientcreditreports.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := _u.mutation.DeletedAt(); ok {
		_spec.SetField(clientcreditreports.FieldDeletedAt, field.TypeTime, value)
	}
	if _u.mutation.DeletedAtCleared() {
		_spec.ClearField(clientcreditreports.FieldDeletedAt, field.TypeTime)
	}
	_spec.Node.Schema = _u.schemaConfig.ClientCreditReports
	ctx = internal.NewSchemaConfigContext(ctx, _u.schemaConfig)
	_spec.AddModifiers(_u.modifiers...)
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{clientcreditreports.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// ClientCreditReportsUpdateOne is the builder for updating a single ClientCreditReports entity.
type ClientCreditReportsUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *ClientCreditReportsMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetIdentityDocumentNumber sets the "identity_document_number" field.
func (_u *ClientCreditReportsUpdateOne) SetIdentityDocumentNumber(v string) *ClientCreditReportsUpdateOne {
	_u.mutation.SetIdentityDocumentNumber(v)
	return _u
}

// SetNillableIdentityDocumentNumber sets the "identity_document_number" field if the given value is not nil.
func (_u *ClientCreditReportsUpdateOne) SetNillableIdentityDocumentNumber(v *string) *ClientCreditReportsUpdateOne {
	if v != nil {
		_u.SetIdentityDocumentNumber(*v)
	}
	return _u
}

// SetReportDate sets the "report_date" field.
func (_u *ClientCreditReportsUpdateOne) SetReportDate(v time.Time) *ClientCreditReportsUpdateOne {
	_u.mutation.SetReportDate(v)
	return _u
}

// SetNillableReportDate sets the "report_date" field if the given value is not nil.
func (_u *ClientCreditReportsUpdateOne) SetNillableReportDate(v *time.Time) *ClientCreditReportsUpdateOne {
	if v != nil {
		_u.SetReportDate(*v)
	}
	return _u
}

// SetDocumentType sets the "document_type" field.
func (_u *ClientCreditReportsUpdateOne) SetDocumentType(v string) *ClientCreditReportsUpdateOne {
	_u.mutation.SetDocumentType(v)
	return _u
}

// SetNillableDocumentType sets the "document_type" field if the given value is not nil.
func (_u *ClientCreditReportsUpdateOne) SetNillableDocumentType(v *string) *ClientCreditReportsUpdateOne {
	if v != nil {
		_u.SetDocumentType(*v)
	}
	return _u
}

// SetFileLink sets the "file_link" field.
func (_u *ClientCreditReportsUpdateOne) SetFileLink(v string) *ClientCreditReportsUpdateOne {
	_u.mutation.SetFileLink(v)
	return _u
}

// SetNillableFileLink sets the "file_link" field if the given value is not nil.
func (_u *ClientCreditReportsUpdateOne) SetNillableFileLink(v *string) *ClientCreditReportsUpdateOne {
	if v != nil {
		_u.SetFileLink(*v)
	}
	return _u
}

// SetUpdatedAt sets the "updated_at" field.
func (_u *ClientCreditReportsUpdateOne) SetUpdatedAt(v time.Time) *ClientCreditReportsUpdateOne {
	_u.mutation.SetUpdatedAt(v)
	return _u
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (_u *ClientCreditReportsUpdateOne) SetNillableUpdatedAt(v *time.Time) *ClientCreditReportsUpdateOne {
	if v != nil {
		_u.SetUpdatedAt(*v)
	}
	return _u
}

// SetDeletedAt sets the "deleted_at" field.
func (_u *ClientCreditReportsUpdateOne) SetDeletedAt(v time.Time) *ClientCreditReportsUpdateOne {
	_u.mutation.SetDeletedAt(v)
	return _u
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (_u *ClientCreditReportsUpdateOne) SetNillableDeletedAt(v *time.Time) *ClientCreditReportsUpdateOne {
	if v != nil {
		_u.SetDeletedAt(*v)
	}
	return _u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (_u *ClientCreditReportsUpdateOne) ClearDeletedAt() *ClientCreditReportsUpdateOne {
	_u.mutation.ClearDeletedAt()
	return _u
}

// Mutation returns the ClientCreditReportsMutation object of the builder.
func (_u *ClientCreditReportsUpdateOne) Mutation() *ClientCreditReportsMutation {
	return _u.mutation
}

// Where appends a list predicates to the ClientCreditReportsUpdate builder.
func (_u *ClientCreditReportsUpdateOne) Where(ps ...predicate.ClientCreditReports) *ClientCreditReportsUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *ClientCreditReportsUpdateOne) Select(field string, fields ...string) *ClientCreditReportsUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated ClientCreditReports entity.
func (_u *ClientCreditReportsUpdateOne) Save(ctx context.Context) (*ClientCreditReports, error) {
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *ClientCreditReportsUpdateOne) SaveX(ctx context.Context) *ClientCreditReports {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *ClientCreditReportsUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *ClientCreditReportsUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *ClientCreditReportsUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *ClientCreditReportsUpdateOne {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *ClientCreditReportsUpdateOne) sqlSave(ctx context.Context) (_node *ClientCreditReports, err error) {
	_spec := sqlgraph.NewUpdateSpec(clientcreditreports.Table, clientcreditreports.Columns, sqlgraph.NewFieldSpec(clientcreditreports.FieldID, field.TypeInt64))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "ClientCreditReports.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, clientcreditreports.FieldID)
		for _, f := range fields {
			if !clientcreditreports.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != clientcreditreports.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.IdentityDocumentNumber(); ok {
		_spec.SetField(clientcreditreports.FieldIdentityDocumentNumber, field.TypeString, value)
	}
	if value, ok := _u.mutation.ReportDate(); ok {
		_spec.SetField(clientcreditreports.FieldReportDate, field.TypeTime, value)
	}
	if value, ok := _u.mutation.DocumentType(); ok {
		_spec.SetField(clientcreditreports.FieldDocumentType, field.TypeString, value)
	}
	if value, ok := _u.mutation.FileLink(); ok {
		_spec.SetField(clientcreditreports.FieldFileLink, field.TypeString, value)
	}
	if value, ok := _u.mutation.UpdatedAt(); ok {
		_spec.SetField(clientcreditreports.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := _u.mutation.DeletedAt(); ok {
		_spec.SetField(clientcreditreports.FieldDeletedAt, field.TypeTime, value)
	}
	if _u.mutation.DeletedAtCleared() {
		_spec.ClearField(clientcreditreports.FieldDeletedAt, field.TypeTime)
	}
	_spec.Node.Schema = _u.schemaConfig.ClientCreditReports
	ctx = internal.NewSchemaConfigContext(ctx, _u.schemaConfig)
	_spec.AddModifiers(_u.modifiers...)
	_node = &ClientCreditReports{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{clientcreditreports.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
