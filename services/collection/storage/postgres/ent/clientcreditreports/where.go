// Code generated by ent, DO NOT EDIT.

package clientcreditreports

import (
	"time"

	"entgo.io/ent/dialect/sql"

	"git.redmadrobot.com/zaman/backend/zaman/services/collection/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldLTE(FieldID, id))
}

// IdentityDocumentNumber applies equality check predicate on the "identity_document_number" field. It's identical to IdentityDocumentNumberEQ.
func IdentityDocumentNumber(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldEQ(FieldIdentityDocumentNumber, v))
}

// ReportDate applies equality check predicate on the "report_date" field. It's identical to ReportDateEQ.
func ReportDate(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldEQ(FieldReportDate, v))
}

// DocumentType applies equality check predicate on the "document_type" field. It's identical to DocumentTypeEQ.
func DocumentType(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldEQ(FieldDocumentType, v))
}

// FileLink applies equality check predicate on the "file_link" field. It's identical to FileLinkEQ.
func FileLink(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldEQ(FieldFileLink, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldEQ(FieldDeletedAt, v))
}

// IdentityDocumentNumberEQ applies the EQ predicate on the "identity_document_number" field.
func IdentityDocumentNumberEQ(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldEQ(FieldIdentityDocumentNumber, v))
}

// IdentityDocumentNumberNEQ applies the NEQ predicate on the "identity_document_number" field.
func IdentityDocumentNumberNEQ(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldNEQ(FieldIdentityDocumentNumber, v))
}

// IdentityDocumentNumberIn applies the In predicate on the "identity_document_number" field.
func IdentityDocumentNumberIn(vs ...string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldIn(FieldIdentityDocumentNumber, vs...))
}

// IdentityDocumentNumberNotIn applies the NotIn predicate on the "identity_document_number" field.
func IdentityDocumentNumberNotIn(vs ...string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldNotIn(FieldIdentityDocumentNumber, vs...))
}

// IdentityDocumentNumberGT applies the GT predicate on the "identity_document_number" field.
func IdentityDocumentNumberGT(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldGT(FieldIdentityDocumentNumber, v))
}

// IdentityDocumentNumberGTE applies the GTE predicate on the "identity_document_number" field.
func IdentityDocumentNumberGTE(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldGTE(FieldIdentityDocumentNumber, v))
}

// IdentityDocumentNumberLT applies the LT predicate on the "identity_document_number" field.
func IdentityDocumentNumberLT(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldLT(FieldIdentityDocumentNumber, v))
}

// IdentityDocumentNumberLTE applies the LTE predicate on the "identity_document_number" field.
func IdentityDocumentNumberLTE(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldLTE(FieldIdentityDocumentNumber, v))
}

// IdentityDocumentNumberContains applies the Contains predicate on the "identity_document_number" field.
func IdentityDocumentNumberContains(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldContains(FieldIdentityDocumentNumber, v))
}

// IdentityDocumentNumberHasPrefix applies the HasPrefix predicate on the "identity_document_number" field.
func IdentityDocumentNumberHasPrefix(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldHasPrefix(FieldIdentityDocumentNumber, v))
}

// IdentityDocumentNumberHasSuffix applies the HasSuffix predicate on the "identity_document_number" field.
func IdentityDocumentNumberHasSuffix(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldHasSuffix(FieldIdentityDocumentNumber, v))
}

// IdentityDocumentNumberEqualFold applies the EqualFold predicate on the "identity_document_number" field.
func IdentityDocumentNumberEqualFold(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldEqualFold(FieldIdentityDocumentNumber, v))
}

// IdentityDocumentNumberContainsFold applies the ContainsFold predicate on the "identity_document_number" field.
func IdentityDocumentNumberContainsFold(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldContainsFold(FieldIdentityDocumentNumber, v))
}

// ReportDateEQ applies the EQ predicate on the "report_date" field.
func ReportDateEQ(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldEQ(FieldReportDate, v))
}

// ReportDateNEQ applies the NEQ predicate on the "report_date" field.
func ReportDateNEQ(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldNEQ(FieldReportDate, v))
}

// ReportDateIn applies the In predicate on the "report_date" field.
func ReportDateIn(vs ...time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldIn(FieldReportDate, vs...))
}

// ReportDateNotIn applies the NotIn predicate on the "report_date" field.
func ReportDateNotIn(vs ...time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldNotIn(FieldReportDate, vs...))
}

// ReportDateGT applies the GT predicate on the "report_date" field.
func ReportDateGT(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldGT(FieldReportDate, v))
}

// ReportDateGTE applies the GTE predicate on the "report_date" field.
func ReportDateGTE(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldGTE(FieldReportDate, v))
}

// ReportDateLT applies the LT predicate on the "report_date" field.
func ReportDateLT(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldLT(FieldReportDate, v))
}

// ReportDateLTE applies the LTE predicate on the "report_date" field.
func ReportDateLTE(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldLTE(FieldReportDate, v))
}

// DocumentTypeEQ applies the EQ predicate on the "document_type" field.
func DocumentTypeEQ(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldEQ(FieldDocumentType, v))
}

// DocumentTypeNEQ applies the NEQ predicate on the "document_type" field.
func DocumentTypeNEQ(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldNEQ(FieldDocumentType, v))
}

// DocumentTypeIn applies the In predicate on the "document_type" field.
func DocumentTypeIn(vs ...string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldIn(FieldDocumentType, vs...))
}

// DocumentTypeNotIn applies the NotIn predicate on the "document_type" field.
func DocumentTypeNotIn(vs ...string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldNotIn(FieldDocumentType, vs...))
}

// DocumentTypeGT applies the GT predicate on the "document_type" field.
func DocumentTypeGT(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldGT(FieldDocumentType, v))
}

// DocumentTypeGTE applies the GTE predicate on the "document_type" field.
func DocumentTypeGTE(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldGTE(FieldDocumentType, v))
}

// DocumentTypeLT applies the LT predicate on the "document_type" field.
func DocumentTypeLT(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldLT(FieldDocumentType, v))
}

// DocumentTypeLTE applies the LTE predicate on the "document_type" field.
func DocumentTypeLTE(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldLTE(FieldDocumentType, v))
}

// DocumentTypeContains applies the Contains predicate on the "document_type" field.
func DocumentTypeContains(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldContains(FieldDocumentType, v))
}

// DocumentTypeHasPrefix applies the HasPrefix predicate on the "document_type" field.
func DocumentTypeHasPrefix(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldHasPrefix(FieldDocumentType, v))
}

// DocumentTypeHasSuffix applies the HasSuffix predicate on the "document_type" field.
func DocumentTypeHasSuffix(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldHasSuffix(FieldDocumentType, v))
}

// DocumentTypeEqualFold applies the EqualFold predicate on the "document_type" field.
func DocumentTypeEqualFold(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldEqualFold(FieldDocumentType, v))
}

// DocumentTypeContainsFold applies the ContainsFold predicate on the "document_type" field.
func DocumentTypeContainsFold(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldContainsFold(FieldDocumentType, v))
}

// FileLinkEQ applies the EQ predicate on the "file_link" field.
func FileLinkEQ(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldEQ(FieldFileLink, v))
}

// FileLinkNEQ applies the NEQ predicate on the "file_link" field.
func FileLinkNEQ(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldNEQ(FieldFileLink, v))
}

// FileLinkIn applies the In predicate on the "file_link" field.
func FileLinkIn(vs ...string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldIn(FieldFileLink, vs...))
}

// FileLinkNotIn applies the NotIn predicate on the "file_link" field.
func FileLinkNotIn(vs ...string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldNotIn(FieldFileLink, vs...))
}

// FileLinkGT applies the GT predicate on the "file_link" field.
func FileLinkGT(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldGT(FieldFileLink, v))
}

// FileLinkGTE applies the GTE predicate on the "file_link" field.
func FileLinkGTE(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldGTE(FieldFileLink, v))
}

// FileLinkLT applies the LT predicate on the "file_link" field.
func FileLinkLT(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldLT(FieldFileLink, v))
}

// FileLinkLTE applies the LTE predicate on the "file_link" field.
func FileLinkLTE(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldLTE(FieldFileLink, v))
}

// FileLinkContains applies the Contains predicate on the "file_link" field.
func FileLinkContains(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldContains(FieldFileLink, v))
}

// FileLinkHasPrefix applies the HasPrefix predicate on the "file_link" field.
func FileLinkHasPrefix(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldHasPrefix(FieldFileLink, v))
}

// FileLinkHasSuffix applies the HasSuffix predicate on the "file_link" field.
func FileLinkHasSuffix(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldHasSuffix(FieldFileLink, v))
}

// FileLinkEqualFold applies the EqualFold predicate on the "file_link" field.
func FileLinkEqualFold(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldEqualFold(FieldFileLink, v))
}

// FileLinkContainsFold applies the ContainsFold predicate on the "file_link" field.
func FileLinkContainsFold(v string) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldContainsFold(FieldFileLink, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.FieldNotNull(FieldDeletedAt))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.ClientCreditReports) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.ClientCreditReports) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.ClientCreditReports) predicate.ClientCreditReports {
	return predicate.ClientCreditReports(sql.NotPredicates(p))
}
