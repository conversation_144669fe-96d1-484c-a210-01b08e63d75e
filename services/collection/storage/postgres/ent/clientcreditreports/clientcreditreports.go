// Code generated by ent, DO NOT EDIT.

package clientcreditreports

import (
	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the clientcreditreports type in the database.
	Label = "client_credit_reports"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldIdentityDocumentNumber holds the string denoting the identity_document_number field in the database.
	FieldIdentityDocumentNumber = "identity_document_number"
	// FieldReportDate holds the string denoting the report_date field in the database.
	FieldReportDate = "report_date"
	// FieldDocumentType holds the string denoting the document_type field in the database.
	FieldDocumentType = "document_type"
	// FieldFileLink holds the string denoting the file_link field in the database.
	FieldFileLink = "file_link"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// Table holds the table name of the clientcreditreports in the database.
	Table = "client_credit_reports"
)

// Columns holds all SQL columns for clientcreditreports fields.
var Columns = []string{
	FieldID,
	FieldIdentityDocumentNumber,
	FieldReportDate,
	FieldDocumentType,
	FieldFileLink,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldDeletedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// IDValidator is a validator for the "id" field. It is called by the builders before save.
	IDValidator func(int64) error
)

// OrderOption defines the ordering options for the ClientCreditReports queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByIdentityDocumentNumber orders the results by the identity_document_number field.
func ByIdentityDocumentNumber(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIdentityDocumentNumber, opts...).ToFunc()
}

// ByReportDate orders the results by the report_date field.
func ByReportDate(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReportDate, opts...).ToFunc()
}

// ByDocumentType orders the results by the document_type field.
func ByDocumentType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDocumentType, opts...).ToFunc()
}

// ByFileLink orders the results by the file_link field.
func ByFileLink(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFileLink, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}
