// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/collection/storage/postgres/ent/creditagreement"
	"git.redmadrobot.com/zaman/backend/zaman/services/collection/storage/postgres/ent/creditdebtstatus"
)

// CreditDebtStatusCreate is the builder for creating a CreditDebtStatus entity.
type CreditDebtStatusCreate struct {
	config
	mutation *CreditDebtStatusMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreditAgreementID sets the "credit_agreement_id" field.
func (_c *CreditDebtStatusCreate) SetCreditAgreementID(v int) *CreditDebtStatusCreate {
	_c.mutation.SetCreditAgreementID(v)
	return _c
}

// SetDelayDaysCount sets the "delay_days_count" field.
func (_c *CreditDebtStatusCreate) SetDelayDaysCount(v int) *CreditDebtStatusCreate {
	_c.mutation.SetDelayDaysCount(v)
	return _c
}

// SetMainDebtExpiredDays sets the "main_debt_expired_days" field.
func (_c *CreditDebtStatusCreate) SetMainDebtExpiredDays(v int) *CreditDebtStatusCreate {
	_c.mutation.SetMainDebtExpiredDays(v)
	return _c
}

// SetInterestExpiredDays sets the "interest_expired_days" field.
func (_c *CreditDebtStatusCreate) SetInterestExpiredDays(v int) *CreditDebtStatusCreate {
	_c.mutation.SetInterestExpiredDays(v)
	return _c
}

// SetRestrictionFl sets the "restriction_fl" field.
func (_c *CreditDebtStatusCreate) SetRestrictionFl(v bool) *CreditDebtStatusCreate {
	_c.mutation.SetRestrictionFl(v)
	return _c
}

// SetNillableRestrictionFl sets the "restriction_fl" field if the given value is not nil.
func (_c *CreditDebtStatusCreate) SetNillableRestrictionFl(v *bool) *CreditDebtStatusCreate {
	if v != nil {
		_c.SetRestrictionFl(*v)
	}
	return _c
}

// SetMainDebt sets the "main_debt" field.
func (_c *CreditDebtStatusCreate) SetMainDebt(v float64) *CreditDebtStatusCreate {
	_c.mutation.SetMainDebt(v)
	return _c
}

// SetAmountInterest sets the "amount_interest" field.
func (_c *CreditDebtStatusCreate) SetAmountInterest(v float64) *CreditDebtStatusCreate {
	_c.mutation.SetAmountInterest(v)
	return _c
}

// SetOverdueDebt sets the "overdue_debt" field.
func (_c *CreditDebtStatusCreate) SetOverdueDebt(v float64) *CreditDebtStatusCreate {
	_c.mutation.SetOverdueDebt(v)
	return _c
}

// SetOverdueInterest sets the "overdue_interest" field.
func (_c *CreditDebtStatusCreate) SetOverdueInterest(v float64) *CreditDebtStatusCreate {
	_c.mutation.SetOverdueInterest(v)
	return _c
}

// SetFine sets the "fine" field.
func (_c *CreditDebtStatusCreate) SetFine(v float64) *CreditDebtStatusCreate {
	_c.mutation.SetFine(v)
	return _c
}

// SetStatusDate sets the "status_date" field.
func (_c *CreditDebtStatusCreate) SetStatusDate(v time.Time) *CreditDebtStatusCreate {
	_c.mutation.SetStatusDate(v)
	return _c
}

// SetIsArchived sets the "is_archived" field.
func (_c *CreditDebtStatusCreate) SetIsArchived(v bool) *CreditDebtStatusCreate {
	_c.mutation.SetIsArchived(v)
	return _c
}

// SetNillableIsArchived sets the "is_archived" field if the given value is not nil.
func (_c *CreditDebtStatusCreate) SetNillableIsArchived(v *bool) *CreditDebtStatusCreate {
	if v != nil {
		_c.SetIsArchived(*v)
	}
	return _c
}

// SetCreatedAt sets the "created_at" field.
func (_c *CreditDebtStatusCreate) SetCreatedAt(v time.Time) *CreditDebtStatusCreate {
	_c.mutation.SetCreatedAt(v)
	return _c
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (_c *CreditDebtStatusCreate) SetNillableCreatedAt(v *time.Time) *CreditDebtStatusCreate {
	if v != nil {
		_c.SetCreatedAt(*v)
	}
	return _c
}

// SetUpdatedAt sets the "updated_at" field.
func (_c *CreditDebtStatusCreate) SetUpdatedAt(v time.Time) *CreditDebtStatusCreate {
	_c.mutation.SetUpdatedAt(v)
	return _c
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (_c *CreditDebtStatusCreate) SetNillableUpdatedAt(v *time.Time) *CreditDebtStatusCreate {
	if v != nil {
		_c.SetUpdatedAt(*v)
	}
	return _c
}

// SetDeletedAt sets the "deleted_at" field.
func (_c *CreditDebtStatusCreate) SetDeletedAt(v time.Time) *CreditDebtStatusCreate {
	_c.mutation.SetDeletedAt(v)
	return _c
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (_c *CreditDebtStatusCreate) SetNillableDeletedAt(v *time.Time) *CreditDebtStatusCreate {
	if v != nil {
		_c.SetDeletedAt(*v)
	}
	return _c
}

// SetID sets the "id" field.
func (_c *CreditDebtStatusCreate) SetID(v int) *CreditDebtStatusCreate {
	_c.mutation.SetID(v)
	return _c
}

// SetCreditAgreement sets the "credit_agreement" edge to the CreditAgreement entity.
func (_c *CreditDebtStatusCreate) SetCreditAgreement(v *CreditAgreement) *CreditDebtStatusCreate {
	return _c.SetCreditAgreementID(v.ID)
}

// Mutation returns the CreditDebtStatusMutation object of the builder.
func (_c *CreditDebtStatusCreate) Mutation() *CreditDebtStatusMutation {
	return _c.mutation
}

// Save creates the CreditDebtStatus in the database.
func (_c *CreditDebtStatusCreate) Save(ctx context.Context) (*CreditDebtStatus, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *CreditDebtStatusCreate) SaveX(ctx context.Context) *CreditDebtStatus {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *CreditDebtStatusCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *CreditDebtStatusCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *CreditDebtStatusCreate) defaults() {
	if _, ok := _c.mutation.RestrictionFl(); !ok {
		v := creditdebtstatus.DefaultRestrictionFl
		_c.mutation.SetRestrictionFl(v)
	}
	if _, ok := _c.mutation.IsArchived(); !ok {
		v := creditdebtstatus.DefaultIsArchived
		_c.mutation.SetIsArchived(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *CreditDebtStatusCreate) check() error {
	if _, ok := _c.mutation.CreditAgreementID(); !ok {
		return &ValidationError{Name: "credit_agreement_id", err: errors.New(`ent: missing required field "CreditDebtStatus.credit_agreement_id"`)}
	}
	if _, ok := _c.mutation.DelayDaysCount(); !ok {
		return &ValidationError{Name: "delay_days_count", err: errors.New(`ent: missing required field "CreditDebtStatus.delay_days_count"`)}
	}
	if _, ok := _c.mutation.MainDebtExpiredDays(); !ok {
		return &ValidationError{Name: "main_debt_expired_days", err: errors.New(`ent: missing required field "CreditDebtStatus.main_debt_expired_days"`)}
	}
	if _, ok := _c.mutation.InterestExpiredDays(); !ok {
		return &ValidationError{Name: "interest_expired_days", err: errors.New(`ent: missing required field "CreditDebtStatus.interest_expired_days"`)}
	}
	if _, ok := _c.mutation.RestrictionFl(); !ok {
		return &ValidationError{Name: "restriction_fl", err: errors.New(`ent: missing required field "CreditDebtStatus.restriction_fl"`)}
	}
	if _, ok := _c.mutation.MainDebt(); !ok {
		return &ValidationError{Name: "main_debt", err: errors.New(`ent: missing required field "CreditDebtStatus.main_debt"`)}
	}
	if _, ok := _c.mutation.AmountInterest(); !ok {
		return &ValidationError{Name: "amount_interest", err: errors.New(`ent: missing required field "CreditDebtStatus.amount_interest"`)}
	}
	if _, ok := _c.mutation.OverdueDebt(); !ok {
		return &ValidationError{Name: "overdue_debt", err: errors.New(`ent: missing required field "CreditDebtStatus.overdue_debt"`)}
	}
	if _, ok := _c.mutation.OverdueInterest(); !ok {
		return &ValidationError{Name: "overdue_interest", err: errors.New(`ent: missing required field "CreditDebtStatus.overdue_interest"`)}
	}
	if _, ok := _c.mutation.Fine(); !ok {
		return &ValidationError{Name: "fine", err: errors.New(`ent: missing required field "CreditDebtStatus.fine"`)}
	}
	if _, ok := _c.mutation.StatusDate(); !ok {
		return &ValidationError{Name: "status_date", err: errors.New(`ent: missing required field "CreditDebtStatus.status_date"`)}
	}
	if _, ok := _c.mutation.IsArchived(); !ok {
		return &ValidationError{Name: "is_archived", err: errors.New(`ent: missing required field "CreditDebtStatus.is_archived"`)}
	}
	if v, ok := _c.mutation.ID(); ok {
		if err := creditdebtstatus.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "CreditDebtStatus.id": %w`, err)}
		}
	}
	if len(_c.mutation.CreditAgreementIDs()) == 0 {
		return &ValidationError{Name: "credit_agreement", err: errors.New(`ent: missing required edge "CreditDebtStatus.credit_agreement"`)}
	}
	return nil
}

func (_c *CreditDebtStatusCreate) sqlSave(ctx context.Context) (*CreditDebtStatus, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int(id)
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *CreditDebtStatusCreate) createSpec() (*CreditDebtStatus, *sqlgraph.CreateSpec) {
	var (
		_node = &CreditDebtStatus{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(creditdebtstatus.Table, sqlgraph.NewFieldSpec(creditdebtstatus.FieldID, field.TypeInt))
	)
	_spec.Schema = _c.schemaConfig.CreditDebtStatus
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := _c.mutation.DelayDaysCount(); ok {
		_spec.SetField(creditdebtstatus.FieldDelayDaysCount, field.TypeInt, value)
		_node.DelayDaysCount = value
	}
	if value, ok := _c.mutation.MainDebtExpiredDays(); ok {
		_spec.SetField(creditdebtstatus.FieldMainDebtExpiredDays, field.TypeInt, value)
		_node.MainDebtExpiredDays = value
	}
	if value, ok := _c.mutation.InterestExpiredDays(); ok {
		_spec.SetField(creditdebtstatus.FieldInterestExpiredDays, field.TypeInt, value)
		_node.InterestExpiredDays = value
	}
	if value, ok := _c.mutation.RestrictionFl(); ok {
		_spec.SetField(creditdebtstatus.FieldRestrictionFl, field.TypeBool, value)
		_node.RestrictionFl = value
	}
	if value, ok := _c.mutation.MainDebt(); ok {
		_spec.SetField(creditdebtstatus.FieldMainDebt, field.TypeFloat64, value)
		_node.MainDebt = value
	}
	if value, ok := _c.mutation.AmountInterest(); ok {
		_spec.SetField(creditdebtstatus.FieldAmountInterest, field.TypeFloat64, value)
		_node.AmountInterest = value
	}
	if value, ok := _c.mutation.OverdueDebt(); ok {
		_spec.SetField(creditdebtstatus.FieldOverdueDebt, field.TypeFloat64, value)
		_node.OverdueDebt = value
	}
	if value, ok := _c.mutation.OverdueInterest(); ok {
		_spec.SetField(creditdebtstatus.FieldOverdueInterest, field.TypeFloat64, value)
		_node.OverdueInterest = value
	}
	if value, ok := _c.mutation.Fine(); ok {
		_spec.SetField(creditdebtstatus.FieldFine, field.TypeFloat64, value)
		_node.Fine = value
	}
	if value, ok := _c.mutation.StatusDate(); ok {
		_spec.SetField(creditdebtstatus.FieldStatusDate, field.TypeTime, value)
		_node.StatusDate = value
	}
	if value, ok := _c.mutation.IsArchived(); ok {
		_spec.SetField(creditdebtstatus.FieldIsArchived, field.TypeBool, value)
		_node.IsArchived = value
	}
	if value, ok := _c.mutation.CreatedAt(); ok {
		_spec.SetField(creditdebtstatus.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = &value
	}
	if value, ok := _c.mutation.UpdatedAt(); ok {
		_spec.SetField(creditdebtstatus.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = &value
	}
	if value, ok := _c.mutation.DeletedAt(); ok {
		_spec.SetField(creditdebtstatus.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = &value
	}
	if nodes := _c.mutation.CreditAgreementIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   creditdebtstatus.CreditAgreementTable,
			Columns: []string{creditdebtstatus.CreditAgreementColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(creditagreement.FieldID, field.TypeInt),
			},
		}
		edge.Schema = _c.schemaConfig.CreditDebtStatus
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.CreditAgreementID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.CreditDebtStatus.Create().
//		SetCreditAgreementID(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.CreditDebtStatusUpsert) {
//			SetCreditAgreementID(v+v).
//		}).
//		Exec(ctx)
func (_c *CreditDebtStatusCreate) OnConflict(opts ...sql.ConflictOption) *CreditDebtStatusUpsertOne {
	_c.conflict = opts
	return &CreditDebtStatusUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.CreditDebtStatus.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *CreditDebtStatusCreate) OnConflictColumns(columns ...string) *CreditDebtStatusUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &CreditDebtStatusUpsertOne{
		create: _c,
	}
}

type (
	// CreditDebtStatusUpsertOne is the builder for "upsert"-ing
	//  one CreditDebtStatus node.
	CreditDebtStatusUpsertOne struct {
		create *CreditDebtStatusCreate
	}

	// CreditDebtStatusUpsert is the "OnConflict" setter.
	CreditDebtStatusUpsert struct {
		*sql.UpdateSet
	}
)

// SetCreditAgreementID sets the "credit_agreement_id" field.
func (u *CreditDebtStatusUpsert) SetCreditAgreementID(v int) *CreditDebtStatusUpsert {
	u.Set(creditdebtstatus.FieldCreditAgreementID, v)
	return u
}

// UpdateCreditAgreementID sets the "credit_agreement_id" field to the value that was provided on create.
func (u *CreditDebtStatusUpsert) UpdateCreditAgreementID() *CreditDebtStatusUpsert {
	u.SetExcluded(creditdebtstatus.FieldCreditAgreementID)
	return u
}

// SetDelayDaysCount sets the "delay_days_count" field.
func (u *CreditDebtStatusUpsert) SetDelayDaysCount(v int) *CreditDebtStatusUpsert {
	u.Set(creditdebtstatus.FieldDelayDaysCount, v)
	return u
}

// UpdateDelayDaysCount sets the "delay_days_count" field to the value that was provided on create.
func (u *CreditDebtStatusUpsert) UpdateDelayDaysCount() *CreditDebtStatusUpsert {
	u.SetExcluded(creditdebtstatus.FieldDelayDaysCount)
	return u
}

// AddDelayDaysCount adds v to the "delay_days_count" field.
func (u *CreditDebtStatusUpsert) AddDelayDaysCount(v int) *CreditDebtStatusUpsert {
	u.Add(creditdebtstatus.FieldDelayDaysCount, v)
	return u
}

// SetMainDebtExpiredDays sets the "main_debt_expired_days" field.
func (u *CreditDebtStatusUpsert) SetMainDebtExpiredDays(v int) *CreditDebtStatusUpsert {
	u.Set(creditdebtstatus.FieldMainDebtExpiredDays, v)
	return u
}

// UpdateMainDebtExpiredDays sets the "main_debt_expired_days" field to the value that was provided on create.
func (u *CreditDebtStatusUpsert) UpdateMainDebtExpiredDays() *CreditDebtStatusUpsert {
	u.SetExcluded(creditdebtstatus.FieldMainDebtExpiredDays)
	return u
}

// AddMainDebtExpiredDays adds v to the "main_debt_expired_days" field.
func (u *CreditDebtStatusUpsert) AddMainDebtExpiredDays(v int) *CreditDebtStatusUpsert {
	u.Add(creditdebtstatus.FieldMainDebtExpiredDays, v)
	return u
}

// SetInterestExpiredDays sets the "interest_expired_days" field.
func (u *CreditDebtStatusUpsert) SetInterestExpiredDays(v int) *CreditDebtStatusUpsert {
	u.Set(creditdebtstatus.FieldInterestExpiredDays, v)
	return u
}

// UpdateInterestExpiredDays sets the "interest_expired_days" field to the value that was provided on create.
func (u *CreditDebtStatusUpsert) UpdateInterestExpiredDays() *CreditDebtStatusUpsert {
	u.SetExcluded(creditdebtstatus.FieldInterestExpiredDays)
	return u
}

// AddInterestExpiredDays adds v to the "interest_expired_days" field.
func (u *CreditDebtStatusUpsert) AddInterestExpiredDays(v int) *CreditDebtStatusUpsert {
	u.Add(creditdebtstatus.FieldInterestExpiredDays, v)
	return u
}

// SetRestrictionFl sets the "restriction_fl" field.
func (u *CreditDebtStatusUpsert) SetRestrictionFl(v bool) *CreditDebtStatusUpsert {
	u.Set(creditdebtstatus.FieldRestrictionFl, v)
	return u
}

// UpdateRestrictionFl sets the "restriction_fl" field to the value that was provided on create.
func (u *CreditDebtStatusUpsert) UpdateRestrictionFl() *CreditDebtStatusUpsert {
	u.SetExcluded(creditdebtstatus.FieldRestrictionFl)
	return u
}

// SetMainDebt sets the "main_debt" field.
func (u *CreditDebtStatusUpsert) SetMainDebt(v float64) *CreditDebtStatusUpsert {
	u.Set(creditdebtstatus.FieldMainDebt, v)
	return u
}

// UpdateMainDebt sets the "main_debt" field to the value that was provided on create.
func (u *CreditDebtStatusUpsert) UpdateMainDebt() *CreditDebtStatusUpsert {
	u.SetExcluded(creditdebtstatus.FieldMainDebt)
	return u
}

// AddMainDebt adds v to the "main_debt" field.
func (u *CreditDebtStatusUpsert) AddMainDebt(v float64) *CreditDebtStatusUpsert {
	u.Add(creditdebtstatus.FieldMainDebt, v)
	return u
}

// SetAmountInterest sets the "amount_interest" field.
func (u *CreditDebtStatusUpsert) SetAmountInterest(v float64) *CreditDebtStatusUpsert {
	u.Set(creditdebtstatus.FieldAmountInterest, v)
	return u
}

// UpdateAmountInterest sets the "amount_interest" field to the value that was provided on create.
func (u *CreditDebtStatusUpsert) UpdateAmountInterest() *CreditDebtStatusUpsert {
	u.SetExcluded(creditdebtstatus.FieldAmountInterest)
	return u
}

// AddAmountInterest adds v to the "amount_interest" field.
func (u *CreditDebtStatusUpsert) AddAmountInterest(v float64) *CreditDebtStatusUpsert {
	u.Add(creditdebtstatus.FieldAmountInterest, v)
	return u
}

// SetOverdueDebt sets the "overdue_debt" field.
func (u *CreditDebtStatusUpsert) SetOverdueDebt(v float64) *CreditDebtStatusUpsert {
	u.Set(creditdebtstatus.FieldOverdueDebt, v)
	return u
}

// UpdateOverdueDebt sets the "overdue_debt" field to the value that was provided on create.
func (u *CreditDebtStatusUpsert) UpdateOverdueDebt() *CreditDebtStatusUpsert {
	u.SetExcluded(creditdebtstatus.FieldOverdueDebt)
	return u
}

// AddOverdueDebt adds v to the "overdue_debt" field.
func (u *CreditDebtStatusUpsert) AddOverdueDebt(v float64) *CreditDebtStatusUpsert {
	u.Add(creditdebtstatus.FieldOverdueDebt, v)
	return u
}

// SetOverdueInterest sets the "overdue_interest" field.
func (u *CreditDebtStatusUpsert) SetOverdueInterest(v float64) *CreditDebtStatusUpsert {
	u.Set(creditdebtstatus.FieldOverdueInterest, v)
	return u
}

// UpdateOverdueInterest sets the "overdue_interest" field to the value that was provided on create.
func (u *CreditDebtStatusUpsert) UpdateOverdueInterest() *CreditDebtStatusUpsert {
	u.SetExcluded(creditdebtstatus.FieldOverdueInterest)
	return u
}

// AddOverdueInterest adds v to the "overdue_interest" field.
func (u *CreditDebtStatusUpsert) AddOverdueInterest(v float64) *CreditDebtStatusUpsert {
	u.Add(creditdebtstatus.FieldOverdueInterest, v)
	return u
}

// SetFine sets the "fine" field.
func (u *CreditDebtStatusUpsert) SetFine(v float64) *CreditDebtStatusUpsert {
	u.Set(creditdebtstatus.FieldFine, v)
	return u
}

// UpdateFine sets the "fine" field to the value that was provided on create.
func (u *CreditDebtStatusUpsert) UpdateFine() *CreditDebtStatusUpsert {
	u.SetExcluded(creditdebtstatus.FieldFine)
	return u
}

// AddFine adds v to the "fine" field.
func (u *CreditDebtStatusUpsert) AddFine(v float64) *CreditDebtStatusUpsert {
	u.Add(creditdebtstatus.FieldFine, v)
	return u
}

// SetStatusDate sets the "status_date" field.
func (u *CreditDebtStatusUpsert) SetStatusDate(v time.Time) *CreditDebtStatusUpsert {
	u.Set(creditdebtstatus.FieldStatusDate, v)
	return u
}

// UpdateStatusDate sets the "status_date" field to the value that was provided on create.
func (u *CreditDebtStatusUpsert) UpdateStatusDate() *CreditDebtStatusUpsert {
	u.SetExcluded(creditdebtstatus.FieldStatusDate)
	return u
}

// SetIsArchived sets the "is_archived" field.
func (u *CreditDebtStatusUpsert) SetIsArchived(v bool) *CreditDebtStatusUpsert {
	u.Set(creditdebtstatus.FieldIsArchived, v)
	return u
}

// UpdateIsArchived sets the "is_archived" field to the value that was provided on create.
func (u *CreditDebtStatusUpsert) UpdateIsArchived() *CreditDebtStatusUpsert {
	u.SetExcluded(creditdebtstatus.FieldIsArchived)
	return u
}

// SetCreatedAt sets the "created_at" field.
func (u *CreditDebtStatusUpsert) SetCreatedAt(v time.Time) *CreditDebtStatusUpsert {
	u.Set(creditdebtstatus.FieldCreatedAt, v)
	return u
}

// UpdateCreatedAt sets the "created_at" field to the value that was provided on create.
func (u *CreditDebtStatusUpsert) UpdateCreatedAt() *CreditDebtStatusUpsert {
	u.SetExcluded(creditdebtstatus.FieldCreatedAt)
	return u
}

// ClearCreatedAt clears the value of the "created_at" field.
func (u *CreditDebtStatusUpsert) ClearCreatedAt() *CreditDebtStatusUpsert {
	u.SetNull(creditdebtstatus.FieldCreatedAt)
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *CreditDebtStatusUpsert) SetUpdatedAt(v time.Time) *CreditDebtStatusUpsert {
	u.Set(creditdebtstatus.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *CreditDebtStatusUpsert) UpdateUpdatedAt() *CreditDebtStatusUpsert {
	u.SetExcluded(creditdebtstatus.FieldUpdatedAt)
	return u
}

// ClearUpdatedAt clears the value of the "updated_at" field.
func (u *CreditDebtStatusUpsert) ClearUpdatedAt() *CreditDebtStatusUpsert {
	u.SetNull(creditdebtstatus.FieldUpdatedAt)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *CreditDebtStatusUpsert) SetDeletedAt(v time.Time) *CreditDebtStatusUpsert {
	u.Set(creditdebtstatus.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *CreditDebtStatusUpsert) UpdateDeletedAt() *CreditDebtStatusUpsert {
	u.SetExcluded(creditdebtstatus.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *CreditDebtStatusUpsert) ClearDeletedAt() *CreditDebtStatusUpsert {
	u.SetNull(creditdebtstatus.FieldDeletedAt)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.CreditDebtStatus.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(creditdebtstatus.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *CreditDebtStatusUpsertOne) UpdateNewValues() *CreditDebtStatusUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(creditdebtstatus.FieldID)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.CreditDebtStatus.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *CreditDebtStatusUpsertOne) Ignore() *CreditDebtStatusUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *CreditDebtStatusUpsertOne) DoNothing() *CreditDebtStatusUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the CreditDebtStatusCreate.OnConflict
// documentation for more info.
func (u *CreditDebtStatusUpsertOne) Update(set func(*CreditDebtStatusUpsert)) *CreditDebtStatusUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&CreditDebtStatusUpsert{UpdateSet: update})
	}))
	return u
}

// SetCreditAgreementID sets the "credit_agreement_id" field.
func (u *CreditDebtStatusUpsertOne) SetCreditAgreementID(v int) *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetCreditAgreementID(v)
	})
}

// UpdateCreditAgreementID sets the "credit_agreement_id" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertOne) UpdateCreditAgreementID() *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateCreditAgreementID()
	})
}

// SetDelayDaysCount sets the "delay_days_count" field.
func (u *CreditDebtStatusUpsertOne) SetDelayDaysCount(v int) *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetDelayDaysCount(v)
	})
}

// AddDelayDaysCount adds v to the "delay_days_count" field.
func (u *CreditDebtStatusUpsertOne) AddDelayDaysCount(v int) *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.AddDelayDaysCount(v)
	})
}

// UpdateDelayDaysCount sets the "delay_days_count" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertOne) UpdateDelayDaysCount() *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateDelayDaysCount()
	})
}

// SetMainDebtExpiredDays sets the "main_debt_expired_days" field.
func (u *CreditDebtStatusUpsertOne) SetMainDebtExpiredDays(v int) *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetMainDebtExpiredDays(v)
	})
}

// AddMainDebtExpiredDays adds v to the "main_debt_expired_days" field.
func (u *CreditDebtStatusUpsertOne) AddMainDebtExpiredDays(v int) *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.AddMainDebtExpiredDays(v)
	})
}

// UpdateMainDebtExpiredDays sets the "main_debt_expired_days" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertOne) UpdateMainDebtExpiredDays() *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateMainDebtExpiredDays()
	})
}

// SetInterestExpiredDays sets the "interest_expired_days" field.
func (u *CreditDebtStatusUpsertOne) SetInterestExpiredDays(v int) *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetInterestExpiredDays(v)
	})
}

// AddInterestExpiredDays adds v to the "interest_expired_days" field.
func (u *CreditDebtStatusUpsertOne) AddInterestExpiredDays(v int) *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.AddInterestExpiredDays(v)
	})
}

// UpdateInterestExpiredDays sets the "interest_expired_days" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertOne) UpdateInterestExpiredDays() *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateInterestExpiredDays()
	})
}

// SetRestrictionFl sets the "restriction_fl" field.
func (u *CreditDebtStatusUpsertOne) SetRestrictionFl(v bool) *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetRestrictionFl(v)
	})
}

// UpdateRestrictionFl sets the "restriction_fl" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertOne) UpdateRestrictionFl() *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateRestrictionFl()
	})
}

// SetMainDebt sets the "main_debt" field.
func (u *CreditDebtStatusUpsertOne) SetMainDebt(v float64) *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetMainDebt(v)
	})
}

// AddMainDebt adds v to the "main_debt" field.
func (u *CreditDebtStatusUpsertOne) AddMainDebt(v float64) *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.AddMainDebt(v)
	})
}

// UpdateMainDebt sets the "main_debt" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertOne) UpdateMainDebt() *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateMainDebt()
	})
}

// SetAmountInterest sets the "amount_interest" field.
func (u *CreditDebtStatusUpsertOne) SetAmountInterest(v float64) *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetAmountInterest(v)
	})
}

// AddAmountInterest adds v to the "amount_interest" field.
func (u *CreditDebtStatusUpsertOne) AddAmountInterest(v float64) *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.AddAmountInterest(v)
	})
}

// UpdateAmountInterest sets the "amount_interest" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertOne) UpdateAmountInterest() *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateAmountInterest()
	})
}

// SetOverdueDebt sets the "overdue_debt" field.
func (u *CreditDebtStatusUpsertOne) SetOverdueDebt(v float64) *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetOverdueDebt(v)
	})
}

// AddOverdueDebt adds v to the "overdue_debt" field.
func (u *CreditDebtStatusUpsertOne) AddOverdueDebt(v float64) *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.AddOverdueDebt(v)
	})
}

// UpdateOverdueDebt sets the "overdue_debt" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertOne) UpdateOverdueDebt() *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateOverdueDebt()
	})
}

// SetOverdueInterest sets the "overdue_interest" field.
func (u *CreditDebtStatusUpsertOne) SetOverdueInterest(v float64) *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetOverdueInterest(v)
	})
}

// AddOverdueInterest adds v to the "overdue_interest" field.
func (u *CreditDebtStatusUpsertOne) AddOverdueInterest(v float64) *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.AddOverdueInterest(v)
	})
}

// UpdateOverdueInterest sets the "overdue_interest" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertOne) UpdateOverdueInterest() *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateOverdueInterest()
	})
}

// SetFine sets the "fine" field.
func (u *CreditDebtStatusUpsertOne) SetFine(v float64) *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetFine(v)
	})
}

// AddFine adds v to the "fine" field.
func (u *CreditDebtStatusUpsertOne) AddFine(v float64) *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.AddFine(v)
	})
}

// UpdateFine sets the "fine" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertOne) UpdateFine() *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateFine()
	})
}

// SetStatusDate sets the "status_date" field.
func (u *CreditDebtStatusUpsertOne) SetStatusDate(v time.Time) *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetStatusDate(v)
	})
}

// UpdateStatusDate sets the "status_date" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertOne) UpdateStatusDate() *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateStatusDate()
	})
}

// SetIsArchived sets the "is_archived" field.
func (u *CreditDebtStatusUpsertOne) SetIsArchived(v bool) *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetIsArchived(v)
	})
}

// UpdateIsArchived sets the "is_archived" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertOne) UpdateIsArchived() *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateIsArchived()
	})
}

// SetCreatedAt sets the "created_at" field.
func (u *CreditDebtStatusUpsertOne) SetCreatedAt(v time.Time) *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetCreatedAt(v)
	})
}

// UpdateCreatedAt sets the "created_at" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertOne) UpdateCreatedAt() *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateCreatedAt()
	})
}

// ClearCreatedAt clears the value of the "created_at" field.
func (u *CreditDebtStatusUpsertOne) ClearCreatedAt() *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.ClearCreatedAt()
	})
}

// SetUpdatedAt sets the "updated_at" field.
func (u *CreditDebtStatusUpsertOne) SetUpdatedAt(v time.Time) *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertOne) UpdateUpdatedAt() *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateUpdatedAt()
	})
}

// ClearUpdatedAt clears the value of the "updated_at" field.
func (u *CreditDebtStatusUpsertOne) ClearUpdatedAt() *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.ClearUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *CreditDebtStatusUpsertOne) SetDeletedAt(v time.Time) *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertOne) UpdateDeletedAt() *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *CreditDebtStatusUpsertOne) ClearDeletedAt() *CreditDebtStatusUpsertOne {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.ClearDeletedAt()
	})
}

// Exec executes the query.
func (u *CreditDebtStatusUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for CreditDebtStatusCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *CreditDebtStatusUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *CreditDebtStatusUpsertOne) ID(ctx context.Context) (id int, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *CreditDebtStatusUpsertOne) IDX(ctx context.Context) int {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// CreditDebtStatusCreateBulk is the builder for creating many CreditDebtStatus entities in bulk.
type CreditDebtStatusCreateBulk struct {
	config
	err      error
	builders []*CreditDebtStatusCreate
	conflict []sql.ConflictOption
}

// Save creates the CreditDebtStatus entities in the database.
func (_c *CreditDebtStatusCreateBulk) Save(ctx context.Context) ([]*CreditDebtStatus, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*CreditDebtStatus, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*CreditDebtStatusMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *CreditDebtStatusCreateBulk) SaveX(ctx context.Context) []*CreditDebtStatus {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *CreditDebtStatusCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *CreditDebtStatusCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.CreditDebtStatus.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.CreditDebtStatusUpsert) {
//			SetCreditAgreementID(v+v).
//		}).
//		Exec(ctx)
func (_c *CreditDebtStatusCreateBulk) OnConflict(opts ...sql.ConflictOption) *CreditDebtStatusUpsertBulk {
	_c.conflict = opts
	return &CreditDebtStatusUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.CreditDebtStatus.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *CreditDebtStatusCreateBulk) OnConflictColumns(columns ...string) *CreditDebtStatusUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &CreditDebtStatusUpsertBulk{
		create: _c,
	}
}

// CreditDebtStatusUpsertBulk is the builder for "upsert"-ing
// a bulk of CreditDebtStatus nodes.
type CreditDebtStatusUpsertBulk struct {
	create *CreditDebtStatusCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.CreditDebtStatus.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(creditdebtstatus.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *CreditDebtStatusUpsertBulk) UpdateNewValues() *CreditDebtStatusUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(creditdebtstatus.FieldID)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.CreditDebtStatus.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *CreditDebtStatusUpsertBulk) Ignore() *CreditDebtStatusUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *CreditDebtStatusUpsertBulk) DoNothing() *CreditDebtStatusUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the CreditDebtStatusCreateBulk.OnConflict
// documentation for more info.
func (u *CreditDebtStatusUpsertBulk) Update(set func(*CreditDebtStatusUpsert)) *CreditDebtStatusUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&CreditDebtStatusUpsert{UpdateSet: update})
	}))
	return u
}

// SetCreditAgreementID sets the "credit_agreement_id" field.
func (u *CreditDebtStatusUpsertBulk) SetCreditAgreementID(v int) *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetCreditAgreementID(v)
	})
}

// UpdateCreditAgreementID sets the "credit_agreement_id" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertBulk) UpdateCreditAgreementID() *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateCreditAgreementID()
	})
}

// SetDelayDaysCount sets the "delay_days_count" field.
func (u *CreditDebtStatusUpsertBulk) SetDelayDaysCount(v int) *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetDelayDaysCount(v)
	})
}

// AddDelayDaysCount adds v to the "delay_days_count" field.
func (u *CreditDebtStatusUpsertBulk) AddDelayDaysCount(v int) *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.AddDelayDaysCount(v)
	})
}

// UpdateDelayDaysCount sets the "delay_days_count" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertBulk) UpdateDelayDaysCount() *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateDelayDaysCount()
	})
}

// SetMainDebtExpiredDays sets the "main_debt_expired_days" field.
func (u *CreditDebtStatusUpsertBulk) SetMainDebtExpiredDays(v int) *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetMainDebtExpiredDays(v)
	})
}

// AddMainDebtExpiredDays adds v to the "main_debt_expired_days" field.
func (u *CreditDebtStatusUpsertBulk) AddMainDebtExpiredDays(v int) *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.AddMainDebtExpiredDays(v)
	})
}

// UpdateMainDebtExpiredDays sets the "main_debt_expired_days" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertBulk) UpdateMainDebtExpiredDays() *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateMainDebtExpiredDays()
	})
}

// SetInterestExpiredDays sets the "interest_expired_days" field.
func (u *CreditDebtStatusUpsertBulk) SetInterestExpiredDays(v int) *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetInterestExpiredDays(v)
	})
}

// AddInterestExpiredDays adds v to the "interest_expired_days" field.
func (u *CreditDebtStatusUpsertBulk) AddInterestExpiredDays(v int) *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.AddInterestExpiredDays(v)
	})
}

// UpdateInterestExpiredDays sets the "interest_expired_days" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertBulk) UpdateInterestExpiredDays() *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateInterestExpiredDays()
	})
}

// SetRestrictionFl sets the "restriction_fl" field.
func (u *CreditDebtStatusUpsertBulk) SetRestrictionFl(v bool) *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetRestrictionFl(v)
	})
}

// UpdateRestrictionFl sets the "restriction_fl" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertBulk) UpdateRestrictionFl() *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateRestrictionFl()
	})
}

// SetMainDebt sets the "main_debt" field.
func (u *CreditDebtStatusUpsertBulk) SetMainDebt(v float64) *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetMainDebt(v)
	})
}

// AddMainDebt adds v to the "main_debt" field.
func (u *CreditDebtStatusUpsertBulk) AddMainDebt(v float64) *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.AddMainDebt(v)
	})
}

// UpdateMainDebt sets the "main_debt" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertBulk) UpdateMainDebt() *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateMainDebt()
	})
}

// SetAmountInterest sets the "amount_interest" field.
func (u *CreditDebtStatusUpsertBulk) SetAmountInterest(v float64) *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetAmountInterest(v)
	})
}

// AddAmountInterest adds v to the "amount_interest" field.
func (u *CreditDebtStatusUpsertBulk) AddAmountInterest(v float64) *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.AddAmountInterest(v)
	})
}

// UpdateAmountInterest sets the "amount_interest" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertBulk) UpdateAmountInterest() *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateAmountInterest()
	})
}

// SetOverdueDebt sets the "overdue_debt" field.
func (u *CreditDebtStatusUpsertBulk) SetOverdueDebt(v float64) *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetOverdueDebt(v)
	})
}

// AddOverdueDebt adds v to the "overdue_debt" field.
func (u *CreditDebtStatusUpsertBulk) AddOverdueDebt(v float64) *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.AddOverdueDebt(v)
	})
}

// UpdateOverdueDebt sets the "overdue_debt" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertBulk) UpdateOverdueDebt() *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateOverdueDebt()
	})
}

// SetOverdueInterest sets the "overdue_interest" field.
func (u *CreditDebtStatusUpsertBulk) SetOverdueInterest(v float64) *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetOverdueInterest(v)
	})
}

// AddOverdueInterest adds v to the "overdue_interest" field.
func (u *CreditDebtStatusUpsertBulk) AddOverdueInterest(v float64) *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.AddOverdueInterest(v)
	})
}

// UpdateOverdueInterest sets the "overdue_interest" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertBulk) UpdateOverdueInterest() *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateOverdueInterest()
	})
}

// SetFine sets the "fine" field.
func (u *CreditDebtStatusUpsertBulk) SetFine(v float64) *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetFine(v)
	})
}

// AddFine adds v to the "fine" field.
func (u *CreditDebtStatusUpsertBulk) AddFine(v float64) *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.AddFine(v)
	})
}

// UpdateFine sets the "fine" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertBulk) UpdateFine() *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateFine()
	})
}

// SetStatusDate sets the "status_date" field.
func (u *CreditDebtStatusUpsertBulk) SetStatusDate(v time.Time) *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetStatusDate(v)
	})
}

// UpdateStatusDate sets the "status_date" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertBulk) UpdateStatusDate() *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateStatusDate()
	})
}

// SetIsArchived sets the "is_archived" field.
func (u *CreditDebtStatusUpsertBulk) SetIsArchived(v bool) *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetIsArchived(v)
	})
}

// UpdateIsArchived sets the "is_archived" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertBulk) UpdateIsArchived() *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateIsArchived()
	})
}

// SetCreatedAt sets the "created_at" field.
func (u *CreditDebtStatusUpsertBulk) SetCreatedAt(v time.Time) *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetCreatedAt(v)
	})
}

// UpdateCreatedAt sets the "created_at" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertBulk) UpdateCreatedAt() *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateCreatedAt()
	})
}

// ClearCreatedAt clears the value of the "created_at" field.
func (u *CreditDebtStatusUpsertBulk) ClearCreatedAt() *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.ClearCreatedAt()
	})
}

// SetUpdatedAt sets the "updated_at" field.
func (u *CreditDebtStatusUpsertBulk) SetUpdatedAt(v time.Time) *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertBulk) UpdateUpdatedAt() *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateUpdatedAt()
	})
}

// ClearUpdatedAt clears the value of the "updated_at" field.
func (u *CreditDebtStatusUpsertBulk) ClearUpdatedAt() *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.ClearUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *CreditDebtStatusUpsertBulk) SetDeletedAt(v time.Time) *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *CreditDebtStatusUpsertBulk) UpdateDeletedAt() *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *CreditDebtStatusUpsertBulk) ClearDeletedAt() *CreditDebtStatusUpsertBulk {
	return u.Update(func(s *CreditDebtStatusUpsert) {
		s.ClearDeletedAt()
	})
}

// Exec executes the query.
func (u *CreditDebtStatusUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the CreditDebtStatusCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for CreditDebtStatusCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *CreditDebtStatusUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
