// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/collection/storage/postgres/ent/clientcreditreports"
)

// ClientCreditReportsCreate is the builder for creating a ClientCreditReports entity.
type ClientCreditReportsCreate struct {
	config
	mutation *ClientCreditReportsMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetIdentityDocumentNumber sets the "identity_document_number" field.
func (_c *ClientCreditReportsCreate) SetIdentityDocumentNumber(v string) *ClientCreditReportsCreate {
	_c.mutation.SetIdentityDocumentNumber(v)
	return _c
}

// SetReportDate sets the "report_date" field.
func (_c *ClientCreditReportsCreate) SetReportDate(v time.Time) *ClientCreditReportsCreate {
	_c.mutation.SetReportDate(v)
	return _c
}

// SetDocumentType sets the "document_type" field.
func (_c *ClientCreditReportsCreate) SetDocumentType(v string) *ClientCreditReportsCreate {
	_c.mutation.SetDocumentType(v)
	return _c
}

// SetFileLink sets the "file_link" field.
func (_c *ClientCreditReportsCreate) SetFileLink(v string) *ClientCreditReportsCreate {
	_c.mutation.SetFileLink(v)
	return _c
}

// SetCreatedAt sets the "created_at" field.
func (_c *ClientCreditReportsCreate) SetCreatedAt(v time.Time) *ClientCreditReportsCreate {
	_c.mutation.SetCreatedAt(v)
	return _c
}

// SetUpdatedAt sets the "updated_at" field.
func (_c *ClientCreditReportsCreate) SetUpdatedAt(v time.Time) *ClientCreditReportsCreate {
	_c.mutation.SetUpdatedAt(v)
	return _c
}

// SetDeletedAt sets the "deleted_at" field.
func (_c *ClientCreditReportsCreate) SetDeletedAt(v time.Time) *ClientCreditReportsCreate {
	_c.mutation.SetDeletedAt(v)
	return _c
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (_c *ClientCreditReportsCreate) SetNillableDeletedAt(v *time.Time) *ClientCreditReportsCreate {
	if v != nil {
		_c.SetDeletedAt(*v)
	}
	return _c
}

// SetID sets the "id" field.
func (_c *ClientCreditReportsCreate) SetID(v int64) *ClientCreditReportsCreate {
	_c.mutation.SetID(v)
	return _c
}

// Mutation returns the ClientCreditReportsMutation object of the builder.
func (_c *ClientCreditReportsCreate) Mutation() *ClientCreditReportsMutation {
	return _c.mutation
}

// Save creates the ClientCreditReports in the database.
func (_c *ClientCreditReportsCreate) Save(ctx context.Context) (*ClientCreditReports, error) {
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *ClientCreditReportsCreate) SaveX(ctx context.Context) *ClientCreditReports {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *ClientCreditReportsCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *ClientCreditReportsCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *ClientCreditReportsCreate) check() error {
	if _, ok := _c.mutation.IdentityDocumentNumber(); !ok {
		return &ValidationError{Name: "identity_document_number", err: errors.New(`ent: missing required field "ClientCreditReports.identity_document_number"`)}
	}
	if _, ok := _c.mutation.ReportDate(); !ok {
		return &ValidationError{Name: "report_date", err: errors.New(`ent: missing required field "ClientCreditReports.report_date"`)}
	}
	if _, ok := _c.mutation.DocumentType(); !ok {
		return &ValidationError{Name: "document_type", err: errors.New(`ent: missing required field "ClientCreditReports.document_type"`)}
	}
	if _, ok := _c.mutation.FileLink(); !ok {
		return &ValidationError{Name: "file_link", err: errors.New(`ent: missing required field "ClientCreditReports.file_link"`)}
	}
	if _, ok := _c.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "ClientCreditReports.created_at"`)}
	}
	if _, ok := _c.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "ClientCreditReports.updated_at"`)}
	}
	if v, ok := _c.mutation.ID(); ok {
		if err := clientcreditreports.IDValidator(v); err != nil {
			return &ValidationError{Name: "id", err: fmt.Errorf(`ent: validator failed for field "ClientCreditReports.id": %w`, err)}
		}
	}
	return nil
}

func (_c *ClientCreditReportsCreate) sqlSave(ctx context.Context) (*ClientCreditReports, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *ClientCreditReportsCreate) createSpec() (*ClientCreditReports, *sqlgraph.CreateSpec) {
	var (
		_node = &ClientCreditReports{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(clientcreditreports.Table, sqlgraph.NewFieldSpec(clientcreditreports.FieldID, field.TypeInt64))
	)
	_spec.Schema = _c.schemaConfig.ClientCreditReports
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := _c.mutation.IdentityDocumentNumber(); ok {
		_spec.SetField(clientcreditreports.FieldIdentityDocumentNumber, field.TypeString, value)
		_node.IdentityDocumentNumber = value
	}
	if value, ok := _c.mutation.ReportDate(); ok {
		_spec.SetField(clientcreditreports.FieldReportDate, field.TypeTime, value)
		_node.ReportDate = value
	}
	if value, ok := _c.mutation.DocumentType(); ok {
		_spec.SetField(clientcreditreports.FieldDocumentType, field.TypeString, value)
		_node.DocumentType = value
	}
	if value, ok := _c.mutation.FileLink(); ok {
		_spec.SetField(clientcreditreports.FieldFileLink, field.TypeString, value)
		_node.FileLink = value
	}
	if value, ok := _c.mutation.CreatedAt(); ok {
		_spec.SetField(clientcreditreports.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := _c.mutation.UpdatedAt(); ok {
		_spec.SetField(clientcreditreports.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := _c.mutation.DeletedAt(); ok {
		_spec.SetField(clientcreditreports.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = &value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.ClientCreditReports.Create().
//		SetIdentityDocumentNumber(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.ClientCreditReportsUpsert) {
//			SetIdentityDocumentNumber(v+v).
//		}).
//		Exec(ctx)
func (_c *ClientCreditReportsCreate) OnConflict(opts ...sql.ConflictOption) *ClientCreditReportsUpsertOne {
	_c.conflict = opts
	return &ClientCreditReportsUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.ClientCreditReports.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *ClientCreditReportsCreate) OnConflictColumns(columns ...string) *ClientCreditReportsUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &ClientCreditReportsUpsertOne{
		create: _c,
	}
}

type (
	// ClientCreditReportsUpsertOne is the builder for "upsert"-ing
	//  one ClientCreditReports node.
	ClientCreditReportsUpsertOne struct {
		create *ClientCreditReportsCreate
	}

	// ClientCreditReportsUpsert is the "OnConflict" setter.
	ClientCreditReportsUpsert struct {
		*sql.UpdateSet
	}
)

// SetIdentityDocumentNumber sets the "identity_document_number" field.
func (u *ClientCreditReportsUpsert) SetIdentityDocumentNumber(v string) *ClientCreditReportsUpsert {
	u.Set(clientcreditreports.FieldIdentityDocumentNumber, v)
	return u
}

// UpdateIdentityDocumentNumber sets the "identity_document_number" field to the value that was provided on create.
func (u *ClientCreditReportsUpsert) UpdateIdentityDocumentNumber() *ClientCreditReportsUpsert {
	u.SetExcluded(clientcreditreports.FieldIdentityDocumentNumber)
	return u
}

// SetReportDate sets the "report_date" field.
func (u *ClientCreditReportsUpsert) SetReportDate(v time.Time) *ClientCreditReportsUpsert {
	u.Set(clientcreditreports.FieldReportDate, v)
	return u
}

// UpdateReportDate sets the "report_date" field to the value that was provided on create.
func (u *ClientCreditReportsUpsert) UpdateReportDate() *ClientCreditReportsUpsert {
	u.SetExcluded(clientcreditreports.FieldReportDate)
	return u
}

// SetDocumentType sets the "document_type" field.
func (u *ClientCreditReportsUpsert) SetDocumentType(v string) *ClientCreditReportsUpsert {
	u.Set(clientcreditreports.FieldDocumentType, v)
	return u
}

// UpdateDocumentType sets the "document_type" field to the value that was provided on create.
func (u *ClientCreditReportsUpsert) UpdateDocumentType() *ClientCreditReportsUpsert {
	u.SetExcluded(clientcreditreports.FieldDocumentType)
	return u
}

// SetFileLink sets the "file_link" field.
func (u *ClientCreditReportsUpsert) SetFileLink(v string) *ClientCreditReportsUpsert {
	u.Set(clientcreditreports.FieldFileLink, v)
	return u
}

// UpdateFileLink sets the "file_link" field to the value that was provided on create.
func (u *ClientCreditReportsUpsert) UpdateFileLink() *ClientCreditReportsUpsert {
	u.SetExcluded(clientcreditreports.FieldFileLink)
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *ClientCreditReportsUpsert) SetUpdatedAt(v time.Time) *ClientCreditReportsUpsert {
	u.Set(clientcreditreports.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *ClientCreditReportsUpsert) UpdateUpdatedAt() *ClientCreditReportsUpsert {
	u.SetExcluded(clientcreditreports.FieldUpdatedAt)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *ClientCreditReportsUpsert) SetDeletedAt(v time.Time) *ClientCreditReportsUpsert {
	u.Set(clientcreditreports.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *ClientCreditReportsUpsert) UpdateDeletedAt() *ClientCreditReportsUpsert {
	u.SetExcluded(clientcreditreports.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *ClientCreditReportsUpsert) ClearDeletedAt() *ClientCreditReportsUpsert {
	u.SetNull(clientcreditreports.FieldDeletedAt)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.ClientCreditReports.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(clientcreditreports.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *ClientCreditReportsUpsertOne) UpdateNewValues() *ClientCreditReportsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(clientcreditreports.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(clientcreditreports.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.ClientCreditReports.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *ClientCreditReportsUpsertOne) Ignore() *ClientCreditReportsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *ClientCreditReportsUpsertOne) DoNothing() *ClientCreditReportsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the ClientCreditReportsCreate.OnConflict
// documentation for more info.
func (u *ClientCreditReportsUpsertOne) Update(set func(*ClientCreditReportsUpsert)) *ClientCreditReportsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&ClientCreditReportsUpsert{UpdateSet: update})
	}))
	return u
}

// SetIdentityDocumentNumber sets the "identity_document_number" field.
func (u *ClientCreditReportsUpsertOne) SetIdentityDocumentNumber(v string) *ClientCreditReportsUpsertOne {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.SetIdentityDocumentNumber(v)
	})
}

// UpdateIdentityDocumentNumber sets the "identity_document_number" field to the value that was provided on create.
func (u *ClientCreditReportsUpsertOne) UpdateIdentityDocumentNumber() *ClientCreditReportsUpsertOne {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.UpdateIdentityDocumentNumber()
	})
}

// SetReportDate sets the "report_date" field.
func (u *ClientCreditReportsUpsertOne) SetReportDate(v time.Time) *ClientCreditReportsUpsertOne {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.SetReportDate(v)
	})
}

// UpdateReportDate sets the "report_date" field to the value that was provided on create.
func (u *ClientCreditReportsUpsertOne) UpdateReportDate() *ClientCreditReportsUpsertOne {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.UpdateReportDate()
	})
}

// SetDocumentType sets the "document_type" field.
func (u *ClientCreditReportsUpsertOne) SetDocumentType(v string) *ClientCreditReportsUpsertOne {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.SetDocumentType(v)
	})
}

// UpdateDocumentType sets the "document_type" field to the value that was provided on create.
func (u *ClientCreditReportsUpsertOne) UpdateDocumentType() *ClientCreditReportsUpsertOne {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.UpdateDocumentType()
	})
}

// SetFileLink sets the "file_link" field.
func (u *ClientCreditReportsUpsertOne) SetFileLink(v string) *ClientCreditReportsUpsertOne {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.SetFileLink(v)
	})
}

// UpdateFileLink sets the "file_link" field to the value that was provided on create.
func (u *ClientCreditReportsUpsertOne) UpdateFileLink() *ClientCreditReportsUpsertOne {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.UpdateFileLink()
	})
}

// SetUpdatedAt sets the "updated_at" field.
func (u *ClientCreditReportsUpsertOne) SetUpdatedAt(v time.Time) *ClientCreditReportsUpsertOne {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *ClientCreditReportsUpsertOne) UpdateUpdatedAt() *ClientCreditReportsUpsertOne {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *ClientCreditReportsUpsertOne) SetDeletedAt(v time.Time) *ClientCreditReportsUpsertOne {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *ClientCreditReportsUpsertOne) UpdateDeletedAt() *ClientCreditReportsUpsertOne {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *ClientCreditReportsUpsertOne) ClearDeletedAt() *ClientCreditReportsUpsertOne {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.ClearDeletedAt()
	})
}

// Exec executes the query.
func (u *ClientCreditReportsUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for ClientCreditReportsCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *ClientCreditReportsUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *ClientCreditReportsUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *ClientCreditReportsUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// ClientCreditReportsCreateBulk is the builder for creating many ClientCreditReports entities in bulk.
type ClientCreditReportsCreateBulk struct {
	config
	err      error
	builders []*ClientCreditReportsCreate
	conflict []sql.ConflictOption
}

// Save creates the ClientCreditReports entities in the database.
func (_c *ClientCreditReportsCreateBulk) Save(ctx context.Context) ([]*ClientCreditReports, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*ClientCreditReports, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*ClientCreditReportsMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *ClientCreditReportsCreateBulk) SaveX(ctx context.Context) []*ClientCreditReports {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *ClientCreditReportsCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *ClientCreditReportsCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.ClientCreditReports.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.ClientCreditReportsUpsert) {
//			SetIdentityDocumentNumber(v+v).
//		}).
//		Exec(ctx)
func (_c *ClientCreditReportsCreateBulk) OnConflict(opts ...sql.ConflictOption) *ClientCreditReportsUpsertBulk {
	_c.conflict = opts
	return &ClientCreditReportsUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.ClientCreditReports.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *ClientCreditReportsCreateBulk) OnConflictColumns(columns ...string) *ClientCreditReportsUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &ClientCreditReportsUpsertBulk{
		create: _c,
	}
}

// ClientCreditReportsUpsertBulk is the builder for "upsert"-ing
// a bulk of ClientCreditReports nodes.
type ClientCreditReportsUpsertBulk struct {
	create *ClientCreditReportsCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.ClientCreditReports.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(clientcreditreports.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *ClientCreditReportsUpsertBulk) UpdateNewValues() *ClientCreditReportsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(clientcreditreports.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(clientcreditreports.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.ClientCreditReports.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *ClientCreditReportsUpsertBulk) Ignore() *ClientCreditReportsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *ClientCreditReportsUpsertBulk) DoNothing() *ClientCreditReportsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the ClientCreditReportsCreateBulk.OnConflict
// documentation for more info.
func (u *ClientCreditReportsUpsertBulk) Update(set func(*ClientCreditReportsUpsert)) *ClientCreditReportsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&ClientCreditReportsUpsert{UpdateSet: update})
	}))
	return u
}

// SetIdentityDocumentNumber sets the "identity_document_number" field.
func (u *ClientCreditReportsUpsertBulk) SetIdentityDocumentNumber(v string) *ClientCreditReportsUpsertBulk {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.SetIdentityDocumentNumber(v)
	})
}

// UpdateIdentityDocumentNumber sets the "identity_document_number" field to the value that was provided on create.
func (u *ClientCreditReportsUpsertBulk) UpdateIdentityDocumentNumber() *ClientCreditReportsUpsertBulk {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.UpdateIdentityDocumentNumber()
	})
}

// SetReportDate sets the "report_date" field.
func (u *ClientCreditReportsUpsertBulk) SetReportDate(v time.Time) *ClientCreditReportsUpsertBulk {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.SetReportDate(v)
	})
}

// UpdateReportDate sets the "report_date" field to the value that was provided on create.
func (u *ClientCreditReportsUpsertBulk) UpdateReportDate() *ClientCreditReportsUpsertBulk {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.UpdateReportDate()
	})
}

// SetDocumentType sets the "document_type" field.
func (u *ClientCreditReportsUpsertBulk) SetDocumentType(v string) *ClientCreditReportsUpsertBulk {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.SetDocumentType(v)
	})
}

// UpdateDocumentType sets the "document_type" field to the value that was provided on create.
func (u *ClientCreditReportsUpsertBulk) UpdateDocumentType() *ClientCreditReportsUpsertBulk {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.UpdateDocumentType()
	})
}

// SetFileLink sets the "file_link" field.
func (u *ClientCreditReportsUpsertBulk) SetFileLink(v string) *ClientCreditReportsUpsertBulk {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.SetFileLink(v)
	})
}

// UpdateFileLink sets the "file_link" field to the value that was provided on create.
func (u *ClientCreditReportsUpsertBulk) UpdateFileLink() *ClientCreditReportsUpsertBulk {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.UpdateFileLink()
	})
}

// SetUpdatedAt sets the "updated_at" field.
func (u *ClientCreditReportsUpsertBulk) SetUpdatedAt(v time.Time) *ClientCreditReportsUpsertBulk {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *ClientCreditReportsUpsertBulk) UpdateUpdatedAt() *ClientCreditReportsUpsertBulk {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *ClientCreditReportsUpsertBulk) SetDeletedAt(v time.Time) *ClientCreditReportsUpsertBulk {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *ClientCreditReportsUpsertBulk) UpdateDeletedAt() *ClientCreditReportsUpsertBulk {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *ClientCreditReportsUpsertBulk) ClearDeletedAt() *ClientCreditReportsUpsertBulk {
	return u.Update(func(s *ClientCreditReportsUpsert) {
		s.ClearDeletedAt()
	})
}

// Exec executes the query.
func (u *ClientCreditReportsUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the ClientCreditReportsCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for ClientCreditReportsCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *ClientCreditReportsUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
