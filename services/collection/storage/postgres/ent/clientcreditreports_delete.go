// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/collection/storage/postgres/ent/clientcreditreports"
	"git.redmadrobot.com/zaman/backend/zaman/services/collection/storage/postgres/ent/internal"
	"git.redmadrobot.com/zaman/backend/zaman/services/collection/storage/postgres/ent/predicate"
)

// ClientCreditReportsDelete is the builder for deleting a ClientCreditReports entity.
type ClientCreditReportsDelete struct {
	config
	hooks    []Hook
	mutation *ClientCreditReportsMutation
}

// Where appends a list predicates to the ClientCreditReportsDelete builder.
func (_d *ClientCreditReportsDelete) Where(ps ...predicate.ClientCreditReports) *ClientCreditReportsDelete {
	_d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (_d *ClientCreditReportsDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, _d.sqlExec, _d.mutation, _d.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *ClientCreditReportsDelete) ExecX(ctx context.Context) int {
	n, err := _d.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (_d *ClientCreditReportsDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(clientcreditreports.Table, sqlgraph.NewFieldSpec(clientcreditreports.FieldID, field.TypeInt64))
	_spec.Node.Schema = _d.schemaConfig.ClientCreditReports
	ctx = internal.NewSchemaConfigContext(ctx, _d.schemaConfig)
	if ps := _d.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, _d.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	_d.mutation.done = true
	return affected, err
}

// ClientCreditReportsDeleteOne is the builder for deleting a single ClientCreditReports entity.
type ClientCreditReportsDeleteOne struct {
	_d *ClientCreditReportsDelete
}

// Where appends a list predicates to the ClientCreditReportsDelete builder.
func (_d *ClientCreditReportsDeleteOne) Where(ps ...predicate.ClientCreditReports) *ClientCreditReportsDeleteOne {
	_d._d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query.
func (_d *ClientCreditReportsDeleteOne) Exec(ctx context.Context) error {
	n, err := _d._d.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{clientcreditreports.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *ClientCreditReportsDeleteOne) ExecX(ctx context.Context) {
	if err := _d.Exec(ctx); err != nil {
		panic(err)
	}
}
