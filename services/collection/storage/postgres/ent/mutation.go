// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/collection/storage/postgres/ent/branche"
	"git.redmadrobot.com/zaman/backend/zaman/services/collection/storage/postgres/ent/clientaddress"
	"git.redmadrobot.com/zaman/backend/zaman/services/collection/storage/postgres/ent/clientcontacts"
	"git.redmadrobot.com/zaman/backend/zaman/services/collection/storage/postgres/ent/clientcreditreports"
	"git.redmadrobot.com/zaman/backend/zaman/services/collection/storage/postgres/ent/clients"
	"git.redmadrobot.com/zaman/backend/zaman/services/collection/storage/postgres/ent/contactpersons"
	"git.redmadrobot.com/zaman/backend/zaman/services/collection/storage/postgres/ent/creditagreement"
	"git.redmadrobot.com/zaman/backend/zaman/services/collection/storage/postgres/ent/creditdebtstatus"
	"git.redmadrobot.com/zaman/backend/zaman/services/collection/storage/postgres/ent/payments"
	"git.redmadrobot.com/zaman/backend/zaman/services/collection/storage/postgres/ent/predicate"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeBranche             = "Branche"
	TypeClientAddress       = "ClientAddress"
	TypeClientContacts      = "ClientContacts"
	TypeClientCreditReports = "ClientCreditReports"
	TypeClients             = "Clients"
	TypeContactPersons      = "ContactPersons"
	TypeCreditAgreement     = "CreditAgreement"
	TypeCreditDebtStatus    = "CreditDebtStatus"
	TypeHealth              = "Health"
	TypePayments            = "Payments"
)

// BrancheMutation represents an operation that mutates the Branche nodes in the graph.
type BrancheMutation struct {
	config
	op             Op
	typ            string
	id             *int
	filial_code    *string
	name           *string
	created_at     *time.Time
	updated_at     *time.Time
	deleted_at     *time.Time
	clearedFields  map[string]struct{}
	clients        map[int]struct{}
	removedclients map[int]struct{}
	clearedclients bool
	done           bool
	oldValue       func(context.Context) (*Branche, error)
	predicates     []predicate.Branche
}

var _ ent.Mutation = (*BrancheMutation)(nil)

// brancheOption allows management of the mutation configuration using functional options.
type brancheOption func(*BrancheMutation)

// newBrancheMutation creates new mutation for the Branche entity.
func newBrancheMutation(c config, op Op, opts ...brancheOption) *BrancheMutation {
	m := &BrancheMutation{
		config:        c,
		op:            op,
		typ:           TypeBranche,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withBrancheID sets the ID field of the mutation.
func withBrancheID(id int) brancheOption {
	return func(m *BrancheMutation) {
		var (
			err   error
			once  sync.Once
			value *Branche
		)
		m.oldValue = func(ctx context.Context) (*Branche, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Branche.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withBranche sets the old Branche of the mutation.
func withBranche(node *Branche) brancheOption {
	return func(m *BrancheMutation) {
		m.oldValue = func(context.Context) (*Branche, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m BrancheMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m BrancheMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Branche entities.
func (m *BrancheMutation) SetID(id int) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *BrancheMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *BrancheMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Branche.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetFilialCode sets the "filial_code" field.
func (m *BrancheMutation) SetFilialCode(s string) {
	m.filial_code = &s
}

// FilialCode returns the value of the "filial_code" field in the mutation.
func (m *BrancheMutation) FilialCode() (r string, exists bool) {
	v := m.filial_code
	if v == nil {
		return
	}
	return *v, true
}

// OldFilialCode returns the old "filial_code" field's value of the Branche entity.
// If the Branche object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BrancheMutation) OldFilialCode(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFilialCode is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFilialCode requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFilialCode: %w", err)
	}
	return oldValue.FilialCode, nil
}

// ResetFilialCode resets all changes to the "filial_code" field.
func (m *BrancheMutation) ResetFilialCode() {
	m.filial_code = nil
}

// SetName sets the "name" field.
func (m *BrancheMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *BrancheMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Branche entity.
// If the Branche object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BrancheMutation) OldName(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ClearName clears the value of the "name" field.
func (m *BrancheMutation) ClearName() {
	m.name = nil
	m.clearedFields[branche.FieldName] = struct{}{}
}

// NameCleared returns if the "name" field was cleared in this mutation.
func (m *BrancheMutation) NameCleared() bool {
	_, ok := m.clearedFields[branche.FieldName]
	return ok
}

// ResetName resets all changes to the "name" field.
func (m *BrancheMutation) ResetName() {
	m.name = nil
	delete(m.clearedFields, branche.FieldName)
}

// SetCreatedAt sets the "created_at" field.
func (m *BrancheMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *BrancheMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Branche entity.
// If the Branche object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BrancheMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *BrancheMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *BrancheMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *BrancheMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Branche entity.
// If the Branche object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BrancheMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *BrancheMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetDeletedAt sets the "deleted_at" field.
func (m *BrancheMutation) SetDeletedAt(t time.Time) {
	m.deleted_at = &t
}

// DeletedAt returns the value of the "deleted_at" field in the mutation.
func (m *BrancheMutation) DeletedAt() (r time.Time, exists bool) {
	v := m.deleted_at
	if v == nil {
		return
	}
	return *v, true
}

// OldDeletedAt returns the old "deleted_at" field's value of the Branche entity.
// If the Branche object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BrancheMutation) OldDeletedAt(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeletedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeletedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeletedAt: %w", err)
	}
	return oldValue.DeletedAt, nil
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (m *BrancheMutation) ClearDeletedAt() {
	m.deleted_at = nil
	m.clearedFields[branche.FieldDeletedAt] = struct{}{}
}

// DeletedAtCleared returns if the "deleted_at" field was cleared in this mutation.
func (m *BrancheMutation) DeletedAtCleared() bool {
	_, ok := m.clearedFields[branche.FieldDeletedAt]
	return ok
}

// ResetDeletedAt resets all changes to the "deleted_at" field.
func (m *BrancheMutation) ResetDeletedAt() {
	m.deleted_at = nil
	delete(m.clearedFields, branche.FieldDeletedAt)
}

// AddClientIDs adds the "clients" edge to the Clients entity by ids.
func (m *BrancheMutation) AddClientIDs(ids ...int) {
	if m.clients == nil {
		m.clients = make(map[int]struct{})
	}
	for i := range ids {
		m.clients[ids[i]] = struct{}{}
	}
}

// ClearClients clears the "clients" edge to the Clients entity.
func (m *BrancheMutation) ClearClients() {
	m.clearedclients = true
}

// ClientsCleared reports if the "clients" edge to the Clients entity was cleared.
func (m *BrancheMutation) ClientsCleared() bool {
	return m.clearedclients
}

// RemoveClientIDs removes the "clients" edge to the Clients entity by IDs.
func (m *BrancheMutation) RemoveClientIDs(ids ...int) {
	if m.removedclients == nil {
		m.removedclients = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.clients, ids[i])
		m.removedclients[ids[i]] = struct{}{}
	}
}

// RemovedClients returns the removed IDs of the "clients" edge to the Clients entity.
func (m *BrancheMutation) RemovedClientsIDs() (ids []int) {
	for id := range m.removedclients {
		ids = append(ids, id)
	}
	return
}

// ClientsIDs returns the "clients" edge IDs in the mutation.
func (m *BrancheMutation) ClientsIDs() (ids []int) {
	for id := range m.clients {
		ids = append(ids, id)
	}
	return
}

// ResetClients resets all changes to the "clients" edge.
func (m *BrancheMutation) ResetClients() {
	m.clients = nil
	m.clearedclients = false
	m.removedclients = nil
}

// Where appends a list predicates to the BrancheMutation builder.
func (m *BrancheMutation) Where(ps ...predicate.Branche) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the BrancheMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *BrancheMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Branche, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *BrancheMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *BrancheMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Branche).
func (m *BrancheMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *BrancheMutation) Fields() []string {
	fields := make([]string, 0, 5)
	if m.filial_code != nil {
		fields = append(fields, branche.FieldFilialCode)
	}
	if m.name != nil {
		fields = append(fields, branche.FieldName)
	}
	if m.created_at != nil {
		fields = append(fields, branche.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, branche.FieldUpdatedAt)
	}
	if m.deleted_at != nil {
		fields = append(fields, branche.FieldDeletedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *BrancheMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case branche.FieldFilialCode:
		return m.FilialCode()
	case branche.FieldName:
		return m.Name()
	case branche.FieldCreatedAt:
		return m.CreatedAt()
	case branche.FieldUpdatedAt:
		return m.UpdatedAt()
	case branche.FieldDeletedAt:
		return m.DeletedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *BrancheMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case branche.FieldFilialCode:
		return m.OldFilialCode(ctx)
	case branche.FieldName:
		return m.OldName(ctx)
	case branche.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case branche.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case branche.FieldDeletedAt:
		return m.OldDeletedAt(ctx)
	}
	return nil, fmt.Errorf("unknown Branche field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *BrancheMutation) SetField(name string, value ent.Value) error {
	switch name {
	case branche.FieldFilialCode:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFilialCode(v)
		return nil
	case branche.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case branche.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case branche.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case branche.FieldDeletedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeletedAt(v)
		return nil
	}
	return fmt.Errorf("unknown Branche field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *BrancheMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *BrancheMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *BrancheMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Branche numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *BrancheMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(branche.FieldName) {
		fields = append(fields, branche.FieldName)
	}
	if m.FieldCleared(branche.FieldDeletedAt) {
		fields = append(fields, branche.FieldDeletedAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *BrancheMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *BrancheMutation) ClearField(name string) error {
	switch name {
	case branche.FieldName:
		m.ClearName()
		return nil
	case branche.FieldDeletedAt:
		m.ClearDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown Branche nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *BrancheMutation) ResetField(name string) error {
	switch name {
	case branche.FieldFilialCode:
		m.ResetFilialCode()
		return nil
	case branche.FieldName:
		m.ResetName()
		return nil
	case branche.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case branche.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case branche.FieldDeletedAt:
		m.ResetDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown Branche field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *BrancheMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clients != nil {
		edges = append(edges, branche.EdgeClients)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *BrancheMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case branche.EdgeClients:
		ids := make([]ent.Value, 0, len(m.clients))
		for id := range m.clients {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *BrancheMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	if m.removedclients != nil {
		edges = append(edges, branche.EdgeClients)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *BrancheMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case branche.EdgeClients:
		ids := make([]ent.Value, 0, len(m.removedclients))
		for id := range m.removedclients {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *BrancheMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedclients {
		edges = append(edges, branche.EdgeClients)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *BrancheMutation) EdgeCleared(name string) bool {
	switch name {
	case branche.EdgeClients:
		return m.clearedclients
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *BrancheMutation) ClearEdge(name string) error {
	switch name {
	}
	return fmt.Errorf("unknown Branche unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *BrancheMutation) ResetEdge(name string) error {
	switch name {
	case branche.EdgeClients:
		m.ResetClients()
		return nil
	}
	return fmt.Errorf("unknown Branche edge %s", name)
}

// ClientAddressMutation represents an operation that mutates the ClientAddress nodes in the graph.
type ClientAddressMutation struct {
	config
	op            Op
	typ           string
	id            *int
	address_type  *string
	address       *string
	postcode      *string
	created_at    *time.Time
	updated_at    *time.Time
	deleted_at    *time.Time
	clearedFields map[string]struct{}
	client        *int
	clearedclient bool
	done          bool
	oldValue      func(context.Context) (*ClientAddress, error)
	predicates    []predicate.ClientAddress
}

var _ ent.Mutation = (*ClientAddressMutation)(nil)

// clientaddressOption allows management of the mutation configuration using functional options.
type clientaddressOption func(*ClientAddressMutation)

// newClientAddressMutation creates new mutation for the ClientAddress entity.
func newClientAddressMutation(c config, op Op, opts ...clientaddressOption) *ClientAddressMutation {
	m := &ClientAddressMutation{
		config:        c,
		op:            op,
		typ:           TypeClientAddress,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withClientAddressID sets the ID field of the mutation.
func withClientAddressID(id int) clientaddressOption {
	return func(m *ClientAddressMutation) {
		var (
			err   error
			once  sync.Once
			value *ClientAddress
		)
		m.oldValue = func(ctx context.Context) (*ClientAddress, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().ClientAddress.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withClientAddress sets the old ClientAddress of the mutation.
func withClientAddress(node *ClientAddress) clientaddressOption {
	return func(m *ClientAddressMutation) {
		m.oldValue = func(context.Context) (*ClientAddress, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m ClientAddressMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m ClientAddressMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of ClientAddress entities.
func (m *ClientAddressMutation) SetID(id int) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *ClientAddressMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *ClientAddressMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().ClientAddress.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetClientID sets the "client_id" field.
func (m *ClientAddressMutation) SetClientID(i int) {
	m.client = &i
}

// ClientID returns the value of the "client_id" field in the mutation.
func (m *ClientAddressMutation) ClientID() (r int, exists bool) {
	v := m.client
	if v == nil {
		return
	}
	return *v, true
}

// OldClientID returns the old "client_id" field's value of the ClientAddress entity.
// If the ClientAddress object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientAddressMutation) OldClientID(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldClientID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldClientID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldClientID: %w", err)
	}
	return oldValue.ClientID, nil
}

// ResetClientID resets all changes to the "client_id" field.
func (m *ClientAddressMutation) ResetClientID() {
	m.client = nil
}

// SetAddressType sets the "address_type" field.
func (m *ClientAddressMutation) SetAddressType(s string) {
	m.address_type = &s
}

// AddressType returns the value of the "address_type" field in the mutation.
func (m *ClientAddressMutation) AddressType() (r string, exists bool) {
	v := m.address_type
	if v == nil {
		return
	}
	return *v, true
}

// OldAddressType returns the old "address_type" field's value of the ClientAddress entity.
// If the ClientAddress object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientAddressMutation) OldAddressType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAddressType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAddressType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAddressType: %w", err)
	}
	return oldValue.AddressType, nil
}

// ResetAddressType resets all changes to the "address_type" field.
func (m *ClientAddressMutation) ResetAddressType() {
	m.address_type = nil
}

// SetAddress sets the "address" field.
func (m *ClientAddressMutation) SetAddress(s string) {
	m.address = &s
}

// Address returns the value of the "address" field in the mutation.
func (m *ClientAddressMutation) Address() (r string, exists bool) {
	v := m.address
	if v == nil {
		return
	}
	return *v, true
}

// OldAddress returns the old "address" field's value of the ClientAddress entity.
// If the ClientAddress object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientAddressMutation) OldAddress(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAddress is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAddress requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAddress: %w", err)
	}
	return oldValue.Address, nil
}

// ResetAddress resets all changes to the "address" field.
func (m *ClientAddressMutation) ResetAddress() {
	m.address = nil
}

// SetPostcode sets the "postcode" field.
func (m *ClientAddressMutation) SetPostcode(s string) {
	m.postcode = &s
}

// Postcode returns the value of the "postcode" field in the mutation.
func (m *ClientAddressMutation) Postcode() (r string, exists bool) {
	v := m.postcode
	if v == nil {
		return
	}
	return *v, true
}

// OldPostcode returns the old "postcode" field's value of the ClientAddress entity.
// If the ClientAddress object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientAddressMutation) OldPostcode(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPostcode is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPostcode requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPostcode: %w", err)
	}
	return oldValue.Postcode, nil
}

// ClearPostcode clears the value of the "postcode" field.
func (m *ClientAddressMutation) ClearPostcode() {
	m.postcode = nil
	m.clearedFields[clientaddress.FieldPostcode] = struct{}{}
}

// PostcodeCleared returns if the "postcode" field was cleared in this mutation.
func (m *ClientAddressMutation) PostcodeCleared() bool {
	_, ok := m.clearedFields[clientaddress.FieldPostcode]
	return ok
}

// ResetPostcode resets all changes to the "postcode" field.
func (m *ClientAddressMutation) ResetPostcode() {
	m.postcode = nil
	delete(m.clearedFields, clientaddress.FieldPostcode)
}

// SetCreatedAt sets the "created_at" field.
func (m *ClientAddressMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *ClientAddressMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the ClientAddress entity.
// If the ClientAddress object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientAddressMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *ClientAddressMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *ClientAddressMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *ClientAddressMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the ClientAddress entity.
// If the ClientAddress object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientAddressMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *ClientAddressMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetDeletedAt sets the "deleted_at" field.
func (m *ClientAddressMutation) SetDeletedAt(t time.Time) {
	m.deleted_at = &t
}

// DeletedAt returns the value of the "deleted_at" field in the mutation.
func (m *ClientAddressMutation) DeletedAt() (r time.Time, exists bool) {
	v := m.deleted_at
	if v == nil {
		return
	}
	return *v, true
}

// OldDeletedAt returns the old "deleted_at" field's value of the ClientAddress entity.
// If the ClientAddress object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientAddressMutation) OldDeletedAt(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeletedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeletedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeletedAt: %w", err)
	}
	return oldValue.DeletedAt, nil
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (m *ClientAddressMutation) ClearDeletedAt() {
	m.deleted_at = nil
	m.clearedFields[clientaddress.FieldDeletedAt] = struct{}{}
}

// DeletedAtCleared returns if the "deleted_at" field was cleared in this mutation.
func (m *ClientAddressMutation) DeletedAtCleared() bool {
	_, ok := m.clearedFields[clientaddress.FieldDeletedAt]
	return ok
}

// ResetDeletedAt resets all changes to the "deleted_at" field.
func (m *ClientAddressMutation) ResetDeletedAt() {
	m.deleted_at = nil
	delete(m.clearedFields, clientaddress.FieldDeletedAt)
}

// ClearClient clears the "client" edge to the Clients entity.
func (m *ClientAddressMutation) ClearClient() {
	m.clearedclient = true
	m.clearedFields[clientaddress.FieldClientID] = struct{}{}
}

// ClientCleared reports if the "client" edge to the Clients entity was cleared.
func (m *ClientAddressMutation) ClientCleared() bool {
	return m.clearedclient
}

// ClientIDs returns the "client" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// ClientID instead. It exists only for internal usage by the builders.
func (m *ClientAddressMutation) ClientIDs() (ids []int) {
	if id := m.client; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetClient resets all changes to the "client" edge.
func (m *ClientAddressMutation) ResetClient() {
	m.client = nil
	m.clearedclient = false
}

// Where appends a list predicates to the ClientAddressMutation builder.
func (m *ClientAddressMutation) Where(ps ...predicate.ClientAddress) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the ClientAddressMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *ClientAddressMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.ClientAddress, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *ClientAddressMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *ClientAddressMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (ClientAddress).
func (m *ClientAddressMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *ClientAddressMutation) Fields() []string {
	fields := make([]string, 0, 7)
	if m.client != nil {
		fields = append(fields, clientaddress.FieldClientID)
	}
	if m.address_type != nil {
		fields = append(fields, clientaddress.FieldAddressType)
	}
	if m.address != nil {
		fields = append(fields, clientaddress.FieldAddress)
	}
	if m.postcode != nil {
		fields = append(fields, clientaddress.FieldPostcode)
	}
	if m.created_at != nil {
		fields = append(fields, clientaddress.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, clientaddress.FieldUpdatedAt)
	}
	if m.deleted_at != nil {
		fields = append(fields, clientaddress.FieldDeletedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *ClientAddressMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case clientaddress.FieldClientID:
		return m.ClientID()
	case clientaddress.FieldAddressType:
		return m.AddressType()
	case clientaddress.FieldAddress:
		return m.Address()
	case clientaddress.FieldPostcode:
		return m.Postcode()
	case clientaddress.FieldCreatedAt:
		return m.CreatedAt()
	case clientaddress.FieldUpdatedAt:
		return m.UpdatedAt()
	case clientaddress.FieldDeletedAt:
		return m.DeletedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *ClientAddressMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case clientaddress.FieldClientID:
		return m.OldClientID(ctx)
	case clientaddress.FieldAddressType:
		return m.OldAddressType(ctx)
	case clientaddress.FieldAddress:
		return m.OldAddress(ctx)
	case clientaddress.FieldPostcode:
		return m.OldPostcode(ctx)
	case clientaddress.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case clientaddress.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case clientaddress.FieldDeletedAt:
		return m.OldDeletedAt(ctx)
	}
	return nil, fmt.Errorf("unknown ClientAddress field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ClientAddressMutation) SetField(name string, value ent.Value) error {
	switch name {
	case clientaddress.FieldClientID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetClientID(v)
		return nil
	case clientaddress.FieldAddressType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAddressType(v)
		return nil
	case clientaddress.FieldAddress:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAddress(v)
		return nil
	case clientaddress.FieldPostcode:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPostcode(v)
		return nil
	case clientaddress.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case clientaddress.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case clientaddress.FieldDeletedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeletedAt(v)
		return nil
	}
	return fmt.Errorf("unknown ClientAddress field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *ClientAddressMutation) AddedFields() []string {
	var fields []string
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *ClientAddressMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ClientAddressMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown ClientAddress numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *ClientAddressMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(clientaddress.FieldPostcode) {
		fields = append(fields, clientaddress.FieldPostcode)
	}
	if m.FieldCleared(clientaddress.FieldDeletedAt) {
		fields = append(fields, clientaddress.FieldDeletedAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *ClientAddressMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *ClientAddressMutation) ClearField(name string) error {
	switch name {
	case clientaddress.FieldPostcode:
		m.ClearPostcode()
		return nil
	case clientaddress.FieldDeletedAt:
		m.ClearDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown ClientAddress nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *ClientAddressMutation) ResetField(name string) error {
	switch name {
	case clientaddress.FieldClientID:
		m.ResetClientID()
		return nil
	case clientaddress.FieldAddressType:
		m.ResetAddressType()
		return nil
	case clientaddress.FieldAddress:
		m.ResetAddress()
		return nil
	case clientaddress.FieldPostcode:
		m.ResetPostcode()
		return nil
	case clientaddress.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case clientaddress.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case clientaddress.FieldDeletedAt:
		m.ResetDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown ClientAddress field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *ClientAddressMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.client != nil {
		edges = append(edges, clientaddress.EdgeClient)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *ClientAddressMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case clientaddress.EdgeClient:
		if id := m.client; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *ClientAddressMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *ClientAddressMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *ClientAddressMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedclient {
		edges = append(edges, clientaddress.EdgeClient)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *ClientAddressMutation) EdgeCleared(name string) bool {
	switch name {
	case clientaddress.EdgeClient:
		return m.clearedclient
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *ClientAddressMutation) ClearEdge(name string) error {
	switch name {
	case clientaddress.EdgeClient:
		m.ClearClient()
		return nil
	}
	return fmt.Errorf("unknown ClientAddress unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *ClientAddressMutation) ResetEdge(name string) error {
	switch name {
	case clientaddress.EdgeClient:
		m.ResetClient()
		return nil
	}
	return fmt.Errorf("unknown ClientAddress edge %s", name)
}

// ClientContactsMutation represents an operation that mutates the ClientContacts nodes in the graph.
type ClientContactsMutation struct {
	config
	op            Op
	typ           string
	id            *int
	phone         *string
	email         *string
	created_at    *time.Time
	updated_at    *time.Time
	clearedFields map[string]struct{}
	client        *int
	clearedclient bool
	done          bool
	oldValue      func(context.Context) (*ClientContacts, error)
	predicates    []predicate.ClientContacts
}

var _ ent.Mutation = (*ClientContactsMutation)(nil)

// clientcontactsOption allows management of the mutation configuration using functional options.
type clientcontactsOption func(*ClientContactsMutation)

// newClientContactsMutation creates new mutation for the ClientContacts entity.
func newClientContactsMutation(c config, op Op, opts ...clientcontactsOption) *ClientContactsMutation {
	m := &ClientContactsMutation{
		config:        c,
		op:            op,
		typ:           TypeClientContacts,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withClientContactsID sets the ID field of the mutation.
func withClientContactsID(id int) clientcontactsOption {
	return func(m *ClientContactsMutation) {
		var (
			err   error
			once  sync.Once
			value *ClientContacts
		)
		m.oldValue = func(ctx context.Context) (*ClientContacts, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().ClientContacts.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withClientContacts sets the old ClientContacts of the mutation.
func withClientContacts(node *ClientContacts) clientcontactsOption {
	return func(m *ClientContactsMutation) {
		m.oldValue = func(context.Context) (*ClientContacts, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m ClientContactsMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m ClientContactsMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of ClientContacts entities.
func (m *ClientContactsMutation) SetID(id int) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *ClientContactsMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *ClientContactsMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().ClientContacts.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetClientID sets the "client_id" field.
func (m *ClientContactsMutation) SetClientID(i int) {
	m.client = &i
}

// ClientID returns the value of the "client_id" field in the mutation.
func (m *ClientContactsMutation) ClientID() (r int, exists bool) {
	v := m.client
	if v == nil {
		return
	}
	return *v, true
}

// OldClientID returns the old "client_id" field's value of the ClientContacts entity.
// If the ClientContacts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientContactsMutation) OldClientID(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldClientID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldClientID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldClientID: %w", err)
	}
	return oldValue.ClientID, nil
}

// ResetClientID resets all changes to the "client_id" field.
func (m *ClientContactsMutation) ResetClientID() {
	m.client = nil
}

// SetPhone sets the "phone" field.
func (m *ClientContactsMutation) SetPhone(s string) {
	m.phone = &s
}

// Phone returns the value of the "phone" field in the mutation.
func (m *ClientContactsMutation) Phone() (r string, exists bool) {
	v := m.phone
	if v == nil {
		return
	}
	return *v, true
}

// OldPhone returns the old "phone" field's value of the ClientContacts entity.
// If the ClientContacts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientContactsMutation) OldPhone(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPhone is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPhone requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPhone: %w", err)
	}
	return oldValue.Phone, nil
}

// ResetPhone resets all changes to the "phone" field.
func (m *ClientContactsMutation) ResetPhone() {
	m.phone = nil
}

// SetEmail sets the "email" field.
func (m *ClientContactsMutation) SetEmail(s string) {
	m.email = &s
}

// Email returns the value of the "email" field in the mutation.
func (m *ClientContactsMutation) Email() (r string, exists bool) {
	v := m.email
	if v == nil {
		return
	}
	return *v, true
}

// OldEmail returns the old "email" field's value of the ClientContacts entity.
// If the ClientContacts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientContactsMutation) OldEmail(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEmail is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEmail requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEmail: %w", err)
	}
	return oldValue.Email, nil
}

// ClearEmail clears the value of the "email" field.
func (m *ClientContactsMutation) ClearEmail() {
	m.email = nil
	m.clearedFields[clientcontacts.FieldEmail] = struct{}{}
}

// EmailCleared returns if the "email" field was cleared in this mutation.
func (m *ClientContactsMutation) EmailCleared() bool {
	_, ok := m.clearedFields[clientcontacts.FieldEmail]
	return ok
}

// ResetEmail resets all changes to the "email" field.
func (m *ClientContactsMutation) ResetEmail() {
	m.email = nil
	delete(m.clearedFields, clientcontacts.FieldEmail)
}

// SetCreatedAt sets the "created_at" field.
func (m *ClientContactsMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *ClientContactsMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the ClientContacts entity.
// If the ClientContacts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientContactsMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *ClientContactsMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *ClientContactsMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *ClientContactsMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the ClientContacts entity.
// If the ClientContacts object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientContactsMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *ClientContactsMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// ClearClient clears the "client" edge to the Clients entity.
func (m *ClientContactsMutation) ClearClient() {
	m.clearedclient = true
	m.clearedFields[clientcontacts.FieldClientID] = struct{}{}
}

// ClientCleared reports if the "client" edge to the Clients entity was cleared.
func (m *ClientContactsMutation) ClientCleared() bool {
	return m.clearedclient
}

// ClientIDs returns the "client" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// ClientID instead. It exists only for internal usage by the builders.
func (m *ClientContactsMutation) ClientIDs() (ids []int) {
	if id := m.client; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetClient resets all changes to the "client" edge.
func (m *ClientContactsMutation) ResetClient() {
	m.client = nil
	m.clearedclient = false
}

// Where appends a list predicates to the ClientContactsMutation builder.
func (m *ClientContactsMutation) Where(ps ...predicate.ClientContacts) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the ClientContactsMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *ClientContactsMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.ClientContacts, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *ClientContactsMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *ClientContactsMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (ClientContacts).
func (m *ClientContactsMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *ClientContactsMutation) Fields() []string {
	fields := make([]string, 0, 5)
	if m.client != nil {
		fields = append(fields, clientcontacts.FieldClientID)
	}
	if m.phone != nil {
		fields = append(fields, clientcontacts.FieldPhone)
	}
	if m.email != nil {
		fields = append(fields, clientcontacts.FieldEmail)
	}
	if m.created_at != nil {
		fields = append(fields, clientcontacts.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, clientcontacts.FieldUpdatedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *ClientContactsMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case clientcontacts.FieldClientID:
		return m.ClientID()
	case clientcontacts.FieldPhone:
		return m.Phone()
	case clientcontacts.FieldEmail:
		return m.Email()
	case clientcontacts.FieldCreatedAt:
		return m.CreatedAt()
	case clientcontacts.FieldUpdatedAt:
		return m.UpdatedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *ClientContactsMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case clientcontacts.FieldClientID:
		return m.OldClientID(ctx)
	case clientcontacts.FieldPhone:
		return m.OldPhone(ctx)
	case clientcontacts.FieldEmail:
		return m.OldEmail(ctx)
	case clientcontacts.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case clientcontacts.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	}
	return nil, fmt.Errorf("unknown ClientContacts field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ClientContactsMutation) SetField(name string, value ent.Value) error {
	switch name {
	case clientcontacts.FieldClientID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetClientID(v)
		return nil
	case clientcontacts.FieldPhone:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPhone(v)
		return nil
	case clientcontacts.FieldEmail:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEmail(v)
		return nil
	case clientcontacts.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case clientcontacts.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	}
	return fmt.Errorf("unknown ClientContacts field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *ClientContactsMutation) AddedFields() []string {
	var fields []string
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *ClientContactsMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ClientContactsMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown ClientContacts numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *ClientContactsMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(clientcontacts.FieldEmail) {
		fields = append(fields, clientcontacts.FieldEmail)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *ClientContactsMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *ClientContactsMutation) ClearField(name string) error {
	switch name {
	case clientcontacts.FieldEmail:
		m.ClearEmail()
		return nil
	}
	return fmt.Errorf("unknown ClientContacts nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *ClientContactsMutation) ResetField(name string) error {
	switch name {
	case clientcontacts.FieldClientID:
		m.ResetClientID()
		return nil
	case clientcontacts.FieldPhone:
		m.ResetPhone()
		return nil
	case clientcontacts.FieldEmail:
		m.ResetEmail()
		return nil
	case clientcontacts.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case clientcontacts.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	}
	return fmt.Errorf("unknown ClientContacts field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *ClientContactsMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.client != nil {
		edges = append(edges, clientcontacts.EdgeClient)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *ClientContactsMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case clientcontacts.EdgeClient:
		if id := m.client; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *ClientContactsMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *ClientContactsMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *ClientContactsMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedclient {
		edges = append(edges, clientcontacts.EdgeClient)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *ClientContactsMutation) EdgeCleared(name string) bool {
	switch name {
	case clientcontacts.EdgeClient:
		return m.clearedclient
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *ClientContactsMutation) ClearEdge(name string) error {
	switch name {
	case clientcontacts.EdgeClient:
		m.ClearClient()
		return nil
	}
	return fmt.Errorf("unknown ClientContacts unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *ClientContactsMutation) ResetEdge(name string) error {
	switch name {
	case clientcontacts.EdgeClient:
		m.ResetClient()
		return nil
	}
	return fmt.Errorf("unknown ClientContacts edge %s", name)
}

// ClientCreditReportsMutation represents an operation that mutates the ClientCreditReports nodes in the graph.
type ClientCreditReportsMutation struct {
	config
	op                       Op
	typ                      string
	id                       *int64
	identity_document_number *string
	report_date              *time.Time
	document_type            *string
	file_link                *string
	created_at               *time.Time
	updated_at               *time.Time
	deleted_at               *time.Time
	clearedFields            map[string]struct{}
	done                     bool
	oldValue                 func(context.Context) (*ClientCreditReports, error)
	predicates               []predicate.ClientCreditReports
}

var _ ent.Mutation = (*ClientCreditReportsMutation)(nil)

// clientcreditreportsOption allows management of the mutation configuration using functional options.
type clientcreditreportsOption func(*ClientCreditReportsMutation)

// newClientCreditReportsMutation creates new mutation for the ClientCreditReports entity.
func newClientCreditReportsMutation(c config, op Op, opts ...clientcreditreportsOption) *ClientCreditReportsMutation {
	m := &ClientCreditReportsMutation{
		config:        c,
		op:            op,
		typ:           TypeClientCreditReports,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withClientCreditReportsID sets the ID field of the mutation.
func withClientCreditReportsID(id int64) clientcreditreportsOption {
	return func(m *ClientCreditReportsMutation) {
		var (
			err   error
			once  sync.Once
			value *ClientCreditReports
		)
		m.oldValue = func(ctx context.Context) (*ClientCreditReports, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().ClientCreditReports.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withClientCreditReports sets the old ClientCreditReports of the mutation.
func withClientCreditReports(node *ClientCreditReports) clientcreditreportsOption {
	return func(m *ClientCreditReportsMutation) {
		m.oldValue = func(context.Context) (*ClientCreditReports, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m ClientCreditReportsMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m ClientCreditReportsMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of ClientCreditReports entities.
func (m *ClientCreditReportsMutation) SetID(id int64) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *ClientCreditReportsMutation) ID() (id int64, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *ClientCreditReportsMutation) IDs(ctx context.Context) ([]int64, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int64{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().ClientCreditReports.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetIdentityDocumentNumber sets the "identity_document_number" field.
func (m *ClientCreditReportsMutation) SetIdentityDocumentNumber(s string) {
	m.identity_document_number = &s
}

// IdentityDocumentNumber returns the value of the "identity_document_number" field in the mutation.
func (m *ClientCreditReportsMutation) IdentityDocumentNumber() (r string, exists bool) {
	v := m.identity_document_number
	if v == nil {
		return
	}
	return *v, true
}

// OldIdentityDocumentNumber returns the old "identity_document_number" field's value of the ClientCreditReports entity.
// If the ClientCreditReports object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientCreditReportsMutation) OldIdentityDocumentNumber(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIdentityDocumentNumber is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIdentityDocumentNumber requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIdentityDocumentNumber: %w", err)
	}
	return oldValue.IdentityDocumentNumber, nil
}

// ResetIdentityDocumentNumber resets all changes to the "identity_document_number" field.
func (m *ClientCreditReportsMutation) ResetIdentityDocumentNumber() {
	m.identity_document_number = nil
}

// SetReportDate sets the "report_date" field.
func (m *ClientCreditReportsMutation) SetReportDate(t time.Time) {
	m.report_date = &t
}

// ReportDate returns the value of the "report_date" field in the mutation.
func (m *ClientCreditReportsMutation) ReportDate() (r time.Time, exists bool) {
	v := m.report_date
	if v == nil {
		return
	}
	return *v, true
}

// OldReportDate returns the old "report_date" field's value of the ClientCreditReports entity.
// If the ClientCreditReports object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientCreditReportsMutation) OldReportDate(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldReportDate is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldReportDate requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldReportDate: %w", err)
	}
	return oldValue.ReportDate, nil
}

// ResetReportDate resets all changes to the "report_date" field.
func (m *ClientCreditReportsMutation) ResetReportDate() {
	m.report_date = nil
}

// SetDocumentType sets the "document_type" field.
func (m *ClientCreditReportsMutation) SetDocumentType(s string) {
	m.document_type = &s
}

// DocumentType returns the value of the "document_type" field in the mutation.
func (m *ClientCreditReportsMutation) DocumentType() (r string, exists bool) {
	v := m.document_type
	if v == nil {
		return
	}
	return *v, true
}

// OldDocumentType returns the old "document_type" field's value of the ClientCreditReports entity.
// If the ClientCreditReports object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientCreditReportsMutation) OldDocumentType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDocumentType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDocumentType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDocumentType: %w", err)
	}
	return oldValue.DocumentType, nil
}

// ResetDocumentType resets all changes to the "document_type" field.
func (m *ClientCreditReportsMutation) ResetDocumentType() {
	m.document_type = nil
}

// SetFileLink sets the "file_link" field.
func (m *ClientCreditReportsMutation) SetFileLink(s string) {
	m.file_link = &s
}

// FileLink returns the value of the "file_link" field in the mutation.
func (m *ClientCreditReportsMutation) FileLink() (r string, exists bool) {
	v := m.file_link
	if v == nil {
		return
	}
	return *v, true
}

// OldFileLink returns the old "file_link" field's value of the ClientCreditReports entity.
// If the ClientCreditReports object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientCreditReportsMutation) OldFileLink(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFileLink is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFileLink requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFileLink: %w", err)
	}
	return oldValue.FileLink, nil
}

// ResetFileLink resets all changes to the "file_link" field.
func (m *ClientCreditReportsMutation) ResetFileLink() {
	m.file_link = nil
}

// SetCreatedAt sets the "created_at" field.
func (m *ClientCreditReportsMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *ClientCreditReportsMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the ClientCreditReports entity.
// If the ClientCreditReports object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientCreditReportsMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *ClientCreditReportsMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *ClientCreditReportsMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *ClientCreditReportsMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the ClientCreditReports entity.
// If the ClientCreditReports object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientCreditReportsMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *ClientCreditReportsMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetDeletedAt sets the "deleted_at" field.
func (m *ClientCreditReportsMutation) SetDeletedAt(t time.Time) {
	m.deleted_at = &t
}

// DeletedAt returns the value of the "deleted_at" field in the mutation.
func (m *ClientCreditReportsMutation) DeletedAt() (r time.Time, exists bool) {
	v := m.deleted_at
	if v == nil {
		return
	}
	return *v, true
}

// OldDeletedAt returns the old "deleted_at" field's value of the ClientCreditReports entity.
// If the ClientCreditReports object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientCreditReportsMutation) OldDeletedAt(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeletedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeletedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeletedAt: %w", err)
	}
	return oldValue.DeletedAt, nil
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (m *ClientCreditReportsMutation) ClearDeletedAt() {
	m.deleted_at = nil
	m.clearedFields[clientcreditreports.FieldDeletedAt] = struct{}{}
}

// DeletedAtCleared returns if the "deleted_at" field was cleared in this mutation.
func (m *ClientCreditReportsMutation) DeletedAtCleared() bool {
	_, ok := m.clearedFields[clientcreditreports.FieldDeletedAt]
	return ok
}

// ResetDeletedAt resets all changes to the "deleted_at" field.
func (m *ClientCreditReportsMutation) ResetDeletedAt() {
	m.deleted_at = nil
	delete(m.clearedFields, clientcreditreports.FieldDeletedAt)
}

// Where appends a list predicates to the ClientCreditReportsMutation builder.
func (m *ClientCreditReportsMutation) Where(ps ...predicate.ClientCreditReports) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the ClientCreditReportsMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *ClientCreditReportsMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.ClientCreditReports, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *ClientCreditReportsMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *ClientCreditReportsMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (ClientCreditReports).
func (m *ClientCreditReportsMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *ClientCreditReportsMutation) Fields() []string {
	fields := make([]string, 0, 7)
	if m.identity_document_number != nil {
		fields = append(fields, clientcreditreports.FieldIdentityDocumentNumber)
	}
	if m.report_date != nil {
		fields = append(fields, clientcreditreports.FieldReportDate)
	}
	if m.document_type != nil {
		fields = append(fields, clientcreditreports.FieldDocumentType)
	}
	if m.file_link != nil {
		fields = append(fields, clientcreditreports.FieldFileLink)
	}
	if m.created_at != nil {
		fields = append(fields, clientcreditreports.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, clientcreditreports.FieldUpdatedAt)
	}
	if m.deleted_at != nil {
		fields = append(fields, clientcreditreports.FieldDeletedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *ClientCreditReportsMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case clientcreditreports.FieldIdentityDocumentNumber:
		return m.IdentityDocumentNumber()
	case clientcreditreports.FieldReportDate:
		return m.ReportDate()
	case clientcreditreports.FieldDocumentType:
		return m.DocumentType()
	case clientcreditreports.FieldFileLink:
		return m.FileLink()
	case clientcreditreports.FieldCreatedAt:
		return m.CreatedAt()
	case clientcreditreports.FieldUpdatedAt:
		return m.UpdatedAt()
	case clientcreditreports.FieldDeletedAt:
		return m.DeletedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *ClientCreditReportsMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case clientcreditreports.FieldIdentityDocumentNumber:
		return m.OldIdentityDocumentNumber(ctx)
	case clientcreditreports.FieldReportDate:
		return m.OldReportDate(ctx)
	case clientcreditreports.FieldDocumentType:
		return m.OldDocumentType(ctx)
	case clientcreditreports.FieldFileLink:
		return m.OldFileLink(ctx)
	case clientcreditreports.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case clientcreditreports.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case clientcreditreports.FieldDeletedAt:
		return m.OldDeletedAt(ctx)
	}
	return nil, fmt.Errorf("unknown ClientCreditReports field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ClientCreditReportsMutation) SetField(name string, value ent.Value) error {
	switch name {
	case clientcreditreports.FieldIdentityDocumentNumber:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIdentityDocumentNumber(v)
		return nil
	case clientcreditreports.FieldReportDate:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetReportDate(v)
		return nil
	case clientcreditreports.FieldDocumentType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDocumentType(v)
		return nil
	case clientcreditreports.FieldFileLink:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFileLink(v)
		return nil
	case clientcreditreports.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case clientcreditreports.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case clientcreditreports.FieldDeletedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeletedAt(v)
		return nil
	}
	return fmt.Errorf("unknown ClientCreditReports field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *ClientCreditReportsMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *ClientCreditReportsMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ClientCreditReportsMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown ClientCreditReports numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *ClientCreditReportsMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(clientcreditreports.FieldDeletedAt) {
		fields = append(fields, clientcreditreports.FieldDeletedAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *ClientCreditReportsMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *ClientCreditReportsMutation) ClearField(name string) error {
	switch name {
	case clientcreditreports.FieldDeletedAt:
		m.ClearDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown ClientCreditReports nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *ClientCreditReportsMutation) ResetField(name string) error {
	switch name {
	case clientcreditreports.FieldIdentityDocumentNumber:
		m.ResetIdentityDocumentNumber()
		return nil
	case clientcreditreports.FieldReportDate:
		m.ResetReportDate()
		return nil
	case clientcreditreports.FieldDocumentType:
		m.ResetDocumentType()
		return nil
	case clientcreditreports.FieldFileLink:
		m.ResetFileLink()
		return nil
	case clientcreditreports.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case clientcreditreports.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case clientcreditreports.FieldDeletedAt:
		m.ResetDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown ClientCreditReports field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *ClientCreditReportsMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *ClientCreditReportsMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *ClientCreditReportsMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *ClientCreditReportsMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *ClientCreditReportsMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *ClientCreditReportsMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *ClientCreditReportsMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown ClientCreditReports unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *ClientCreditReportsMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown ClientCreditReports edge %s", name)
}

// ClientsMutation represents an operation that mutates the Clients nodes in the graph.
type ClientsMutation struct {
	config
	op                       Op
	typ                      string
	id                       *int
	user_id                  *uuid.UUID
	iin                      *string
	client_type              *string
	client_name              *string
	se_name                  *string
	created_at               *time.Time
	updated_at               *time.Time
	deleted_at               *time.Time
	clearedFields            map[string]struct{}
	branche                  *int
	clearedbranche           bool
	client_addresses         map[int]struct{}
	removedclient_addresses  map[int]struct{}
	clearedclient_addresses  bool
	client_contacts          map[int]struct{}
	removedclient_contacts   map[int]struct{}
	clearedclient_contacts   bool
	credit_agreements        map[int]struct{}
	removedcredit_agreements map[int]struct{}
	clearedcredit_agreements bool
	contact_persons          map[int]struct{}
	removedcontact_persons   map[int]struct{}
	clearedcontact_persons   bool
	done                     bool
	oldValue                 func(context.Context) (*Clients, error)
	predicates               []predicate.Clients
}

var _ ent.Mutation = (*ClientsMutation)(nil)

// clientsOption allows management of the mutation configuration using functional options.
type clientsOption func(*ClientsMutation)

// newClientsMutation creates new mutation for the Clients entity.
func newClientsMutation(c config, op Op, opts ...clientsOption) *ClientsMutation {
	m := &ClientsMutation{
		config:        c,
		op:            op,
		typ:           TypeClients,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withClientsID sets the ID field of the mutation.
func withClientsID(id int) clientsOption {
	return func(m *ClientsMutation) {
		var (
			err   error
			once  sync.Once
			value *Clients
		)
		m.oldValue = func(ctx context.Context) (*Clients, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Clients.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withClients sets the old Clients of the mutation.
func withClients(node *Clients) clientsOption {
	return func(m *ClientsMutation) {
		m.oldValue = func(context.Context) (*Clients, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m ClientsMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m ClientsMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Clients entities.
func (m *ClientsMutation) SetID(id int) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *ClientsMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *ClientsMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Clients.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetUserID sets the "user_id" field.
func (m *ClientsMutation) SetUserID(u uuid.UUID) {
	m.user_id = &u
}

// UserID returns the value of the "user_id" field in the mutation.
func (m *ClientsMutation) UserID() (r uuid.UUID, exists bool) {
	v := m.user_id
	if v == nil {
		return
	}
	return *v, true
}

// OldUserID returns the old "user_id" field's value of the Clients entity.
// If the Clients object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientsMutation) OldUserID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserID: %w", err)
	}
	return oldValue.UserID, nil
}

// ResetUserID resets all changes to the "user_id" field.
func (m *ClientsMutation) ResetUserID() {
	m.user_id = nil
}

// SetBranchID sets the "branch_id" field.
func (m *ClientsMutation) SetBranchID(i int) {
	m.branche = &i
}

// BranchID returns the value of the "branch_id" field in the mutation.
func (m *ClientsMutation) BranchID() (r int, exists bool) {
	v := m.branche
	if v == nil {
		return
	}
	return *v, true
}

// OldBranchID returns the old "branch_id" field's value of the Clients entity.
// If the Clients object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientsMutation) OldBranchID(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBranchID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBranchID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBranchID: %w", err)
	}
	return oldValue.BranchID, nil
}

// ResetBranchID resets all changes to the "branch_id" field.
func (m *ClientsMutation) ResetBranchID() {
	m.branche = nil
}

// SetIin sets the "iin" field.
func (m *ClientsMutation) SetIin(s string) {
	m.iin = &s
}

// Iin returns the value of the "iin" field in the mutation.
func (m *ClientsMutation) Iin() (r string, exists bool) {
	v := m.iin
	if v == nil {
		return
	}
	return *v, true
}

// OldIin returns the old "iin" field's value of the Clients entity.
// If the Clients object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientsMutation) OldIin(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIin is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIin requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIin: %w", err)
	}
	return oldValue.Iin, nil
}

// ResetIin resets all changes to the "iin" field.
func (m *ClientsMutation) ResetIin() {
	m.iin = nil
}

// SetClientType sets the "client_type" field.
func (m *ClientsMutation) SetClientType(s string) {
	m.client_type = &s
}

// ClientType returns the value of the "client_type" field in the mutation.
func (m *ClientsMutation) ClientType() (r string, exists bool) {
	v := m.client_type
	if v == nil {
		return
	}
	return *v, true
}

// OldClientType returns the old "client_type" field's value of the Clients entity.
// If the Clients object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientsMutation) OldClientType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldClientType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldClientType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldClientType: %w", err)
	}
	return oldValue.ClientType, nil
}

// ResetClientType resets all changes to the "client_type" field.
func (m *ClientsMutation) ResetClientType() {
	m.client_type = nil
}

// SetClientName sets the "client_name" field.
func (m *ClientsMutation) SetClientName(s string) {
	m.client_name = &s
}

// ClientName returns the value of the "client_name" field in the mutation.
func (m *ClientsMutation) ClientName() (r string, exists bool) {
	v := m.client_name
	if v == nil {
		return
	}
	return *v, true
}

// OldClientName returns the old "client_name" field's value of the Clients entity.
// If the Clients object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientsMutation) OldClientName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldClientName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldClientName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldClientName: %w", err)
	}
	return oldValue.ClientName, nil
}

// ResetClientName resets all changes to the "client_name" field.
func (m *ClientsMutation) ResetClientName() {
	m.client_name = nil
}

// SetSeName sets the "se_name" field.
func (m *ClientsMutation) SetSeName(s string) {
	m.se_name = &s
}

// SeName returns the value of the "se_name" field in the mutation.
func (m *ClientsMutation) SeName() (r string, exists bool) {
	v := m.se_name
	if v == nil {
		return
	}
	return *v, true
}

// OldSeName returns the old "se_name" field's value of the Clients entity.
// If the Clients object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientsMutation) OldSeName(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSeName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSeName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSeName: %w", err)
	}
	return oldValue.SeName, nil
}

// ClearSeName clears the value of the "se_name" field.
func (m *ClientsMutation) ClearSeName() {
	m.se_name = nil
	m.clearedFields[clients.FieldSeName] = struct{}{}
}

// SeNameCleared returns if the "se_name" field was cleared in this mutation.
func (m *ClientsMutation) SeNameCleared() bool {
	_, ok := m.clearedFields[clients.FieldSeName]
	return ok
}

// ResetSeName resets all changes to the "se_name" field.
func (m *ClientsMutation) ResetSeName() {
	m.se_name = nil
	delete(m.clearedFields, clients.FieldSeName)
}

// SetCreatedAt sets the "created_at" field.
func (m *ClientsMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *ClientsMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Clients entity.
// If the Clients object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientsMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *ClientsMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *ClientsMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *ClientsMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Clients entity.
// If the Clients object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientsMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *ClientsMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetDeletedAt sets the "deleted_at" field.
func (m *ClientsMutation) SetDeletedAt(t time.Time) {
	m.deleted_at = &t
}

// DeletedAt returns the value of the "deleted_at" field in the mutation.
func (m *ClientsMutation) DeletedAt() (r time.Time, exists bool) {
	v := m.deleted_at
	if v == nil {
		return
	}
	return *v, true
}

// OldDeletedAt returns the old "deleted_at" field's value of the Clients entity.
// If the Clients object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ClientsMutation) OldDeletedAt(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeletedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeletedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeletedAt: %w", err)
	}
	return oldValue.DeletedAt, nil
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (m *ClientsMutation) ClearDeletedAt() {
	m.deleted_at = nil
	m.clearedFields[clients.FieldDeletedAt] = struct{}{}
}

// DeletedAtCleared returns if the "deleted_at" field was cleared in this mutation.
func (m *ClientsMutation) DeletedAtCleared() bool {
	_, ok := m.clearedFields[clients.FieldDeletedAt]
	return ok
}

// ResetDeletedAt resets all changes to the "deleted_at" field.
func (m *ClientsMutation) ResetDeletedAt() {
	m.deleted_at = nil
	delete(m.clearedFields, clients.FieldDeletedAt)
}

// SetBrancheID sets the "branche" edge to the Branche entity by id.
func (m *ClientsMutation) SetBrancheID(id int) {
	m.branche = &id
}

// ClearBranche clears the "branche" edge to the Branche entity.
func (m *ClientsMutation) ClearBranche() {
	m.clearedbranche = true
	m.clearedFields[clients.FieldBranchID] = struct{}{}
}

// BrancheCleared reports if the "branche" edge to the Branche entity was cleared.
func (m *ClientsMutation) BrancheCleared() bool {
	return m.clearedbranche
}

// BrancheID returns the "branche" edge ID in the mutation.
func (m *ClientsMutation) BrancheID() (id int, exists bool) {
	if m.branche != nil {
		return *m.branche, true
	}
	return
}

// BrancheIDs returns the "branche" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// BrancheID instead. It exists only for internal usage by the builders.
func (m *ClientsMutation) BrancheIDs() (ids []int) {
	if id := m.branche; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetBranche resets all changes to the "branche" edge.
func (m *ClientsMutation) ResetBranche() {
	m.branche = nil
	m.clearedbranche = false
}

// AddClientAddressIDs adds the "client_addresses" edge to the ClientAddress entity by ids.
func (m *ClientsMutation) AddClientAddressIDs(ids ...int) {
	if m.client_addresses == nil {
		m.client_addresses = make(map[int]struct{})
	}
	for i := range ids {
		m.client_addresses[ids[i]] = struct{}{}
	}
}

// ClearClientAddresses clears the "client_addresses" edge to the ClientAddress entity.
func (m *ClientsMutation) ClearClientAddresses() {
	m.clearedclient_addresses = true
}

// ClientAddressesCleared reports if the "client_addresses" edge to the ClientAddress entity was cleared.
func (m *ClientsMutation) ClientAddressesCleared() bool {
	return m.clearedclient_addresses
}

// RemoveClientAddressIDs removes the "client_addresses" edge to the ClientAddress entity by IDs.
func (m *ClientsMutation) RemoveClientAddressIDs(ids ...int) {
	if m.removedclient_addresses == nil {
		m.removedclient_addresses = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.client_addresses, ids[i])
		m.removedclient_addresses[ids[i]] = struct{}{}
	}
}

// RemovedClientAddresses returns the removed IDs of the "client_addresses" edge to the ClientAddress entity.
func (m *ClientsMutation) RemovedClientAddressesIDs() (ids []int) {
	for id := range m.removedclient_addresses {
		ids = append(ids, id)
	}
	return
}

// ClientAddressesIDs returns the "client_addresses" edge IDs in the mutation.
func (m *ClientsMutation) ClientAddressesIDs() (ids []int) {
	for id := range m.client_addresses {
		ids = append(ids, id)
	}
	return
}

// ResetClientAddresses resets all changes to the "client_addresses" edge.
func (m *ClientsMutation) ResetClientAddresses() {
	m.client_addresses = nil
	m.clearedclient_addresses = false
	m.removedclient_addresses = nil
}

// AddClientContactIDs adds the "client_contacts" edge to the ClientContacts entity by ids.
func (m *ClientsMutation) AddClientContactIDs(ids ...int) {
	if m.client_contacts == nil {
		m.client_contacts = make(map[int]struct{})
	}
	for i := range ids {
		m.client_contacts[ids[i]] = struct{}{}
	}
}

// ClearClientContacts clears the "client_contacts" edge to the ClientContacts entity.
func (m *ClientsMutation) ClearClientContacts() {
	m.clearedclient_contacts = true
}

// ClientContactsCleared reports if the "client_contacts" edge to the ClientContacts entity was cleared.
func (m *ClientsMutation) ClientContactsCleared() bool {
	return m.clearedclient_contacts
}

// RemoveClientContactIDs removes the "client_contacts" edge to the ClientContacts entity by IDs.
func (m *ClientsMutation) RemoveClientContactIDs(ids ...int) {
	if m.removedclient_contacts == nil {
		m.removedclient_contacts = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.client_contacts, ids[i])
		m.removedclient_contacts[ids[i]] = struct{}{}
	}
}

// RemovedClientContacts returns the removed IDs of the "client_contacts" edge to the ClientContacts entity.
func (m *ClientsMutation) RemovedClientContactsIDs() (ids []int) {
	for id := range m.removedclient_contacts {
		ids = append(ids, id)
	}
	return
}

// ClientContactsIDs returns the "client_contacts" edge IDs in the mutation.
func (m *ClientsMutation) ClientContactsIDs() (ids []int) {
	for id := range m.client_contacts {
		ids = append(ids, id)
	}
	return
}

// ResetClientContacts resets all changes to the "client_contacts" edge.
func (m *ClientsMutation) ResetClientContacts() {
	m.client_contacts = nil
	m.clearedclient_contacts = false
	m.removedclient_contacts = nil
}

// AddCreditAgreementIDs adds the "credit_agreements" edge to the CreditAgreement entity by ids.
func (m *ClientsMutation) AddCreditAgreementIDs(ids ...int) {
	if m.credit_agreements == nil {
		m.credit_agreements = make(map[int]struct{})
	}
	for i := range ids {
		m.credit_agreements[ids[i]] = struct{}{}
	}
}

// ClearCreditAgreements clears the "credit_agreements" edge to the CreditAgreement entity.
func (m *ClientsMutation) ClearCreditAgreements() {
	m.clearedcredit_agreements = true
}

// CreditAgreementsCleared reports if the "credit_agreements" edge to the CreditAgreement entity was cleared.
func (m *ClientsMutation) CreditAgreementsCleared() bool {
	return m.clearedcredit_agreements
}

// RemoveCreditAgreementIDs removes the "credit_agreements" edge to the CreditAgreement entity by IDs.
func (m *ClientsMutation) RemoveCreditAgreementIDs(ids ...int) {
	if m.removedcredit_agreements == nil {
		m.removedcredit_agreements = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.credit_agreements, ids[i])
		m.removedcredit_agreements[ids[i]] = struct{}{}
	}
}

// RemovedCreditAgreements returns the removed IDs of the "credit_agreements" edge to the CreditAgreement entity.
func (m *ClientsMutation) RemovedCreditAgreementsIDs() (ids []int) {
	for id := range m.removedcredit_agreements {
		ids = append(ids, id)
	}
	return
}

// CreditAgreementsIDs returns the "credit_agreements" edge IDs in the mutation.
func (m *ClientsMutation) CreditAgreementsIDs() (ids []int) {
	for id := range m.credit_agreements {
		ids = append(ids, id)
	}
	return
}

// ResetCreditAgreements resets all changes to the "credit_agreements" edge.
func (m *ClientsMutation) ResetCreditAgreements() {
	m.credit_agreements = nil
	m.clearedcredit_agreements = false
	m.removedcredit_agreements = nil
}

// AddContactPersonIDs adds the "contact_persons" edge to the ContactPersons entity by ids.
func (m *ClientsMutation) AddContactPersonIDs(ids ...int) {
	if m.contact_persons == nil {
		m.contact_persons = make(map[int]struct{})
	}
	for i := range ids {
		m.contact_persons[ids[i]] = struct{}{}
	}
}

// ClearContactPersons clears the "contact_persons" edge to the ContactPersons entity.
func (m *ClientsMutation) ClearContactPersons() {
	m.clearedcontact_persons = true
}

// ContactPersonsCleared reports if the "contact_persons" edge to the ContactPersons entity was cleared.
func (m *ClientsMutation) ContactPersonsCleared() bool {
	return m.clearedcontact_persons
}

// RemoveContactPersonIDs removes the "contact_persons" edge to the ContactPersons entity by IDs.
func (m *ClientsMutation) RemoveContactPersonIDs(ids ...int) {
	if m.removedcontact_persons == nil {
		m.removedcontact_persons = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.contact_persons, ids[i])
		m.removedcontact_persons[ids[i]] = struct{}{}
	}
}

// RemovedContactPersons returns the removed IDs of the "contact_persons" edge to the ContactPersons entity.
func (m *ClientsMutation) RemovedContactPersonsIDs() (ids []int) {
	for id := range m.removedcontact_persons {
		ids = append(ids, id)
	}
	return
}

// ContactPersonsIDs returns the "contact_persons" edge IDs in the mutation.
func (m *ClientsMutation) ContactPersonsIDs() (ids []int) {
	for id := range m.contact_persons {
		ids = append(ids, id)
	}
	return
}

// ResetContactPersons resets all changes to the "contact_persons" edge.
func (m *ClientsMutation) ResetContactPersons() {
	m.contact_persons = nil
	m.clearedcontact_persons = false
	m.removedcontact_persons = nil
}

// Where appends a list predicates to the ClientsMutation builder.
func (m *ClientsMutation) Where(ps ...predicate.Clients) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the ClientsMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *ClientsMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Clients, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *ClientsMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *ClientsMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Clients).
func (m *ClientsMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *ClientsMutation) Fields() []string {
	fields := make([]string, 0, 9)
	if m.user_id != nil {
		fields = append(fields, clients.FieldUserID)
	}
	if m.branche != nil {
		fields = append(fields, clients.FieldBranchID)
	}
	if m.iin != nil {
		fields = append(fields, clients.FieldIin)
	}
	if m.client_type != nil {
		fields = append(fields, clients.FieldClientType)
	}
	if m.client_name != nil {
		fields = append(fields, clients.FieldClientName)
	}
	if m.se_name != nil {
		fields = append(fields, clients.FieldSeName)
	}
	if m.created_at != nil {
		fields = append(fields, clients.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, clients.FieldUpdatedAt)
	}
	if m.deleted_at != nil {
		fields = append(fields, clients.FieldDeletedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *ClientsMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case clients.FieldUserID:
		return m.UserID()
	case clients.FieldBranchID:
		return m.BranchID()
	case clients.FieldIin:
		return m.Iin()
	case clients.FieldClientType:
		return m.ClientType()
	case clients.FieldClientName:
		return m.ClientName()
	case clients.FieldSeName:
		return m.SeName()
	case clients.FieldCreatedAt:
		return m.CreatedAt()
	case clients.FieldUpdatedAt:
		return m.UpdatedAt()
	case clients.FieldDeletedAt:
		return m.DeletedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *ClientsMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case clients.FieldUserID:
		return m.OldUserID(ctx)
	case clients.FieldBranchID:
		return m.OldBranchID(ctx)
	case clients.FieldIin:
		return m.OldIin(ctx)
	case clients.FieldClientType:
		return m.OldClientType(ctx)
	case clients.FieldClientName:
		return m.OldClientName(ctx)
	case clients.FieldSeName:
		return m.OldSeName(ctx)
	case clients.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case clients.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case clients.FieldDeletedAt:
		return m.OldDeletedAt(ctx)
	}
	return nil, fmt.Errorf("unknown Clients field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ClientsMutation) SetField(name string, value ent.Value) error {
	switch name {
	case clients.FieldUserID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserID(v)
		return nil
	case clients.FieldBranchID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBranchID(v)
		return nil
	case clients.FieldIin:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIin(v)
		return nil
	case clients.FieldClientType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetClientType(v)
		return nil
	case clients.FieldClientName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetClientName(v)
		return nil
	case clients.FieldSeName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSeName(v)
		return nil
	case clients.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case clients.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case clients.FieldDeletedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeletedAt(v)
		return nil
	}
	return fmt.Errorf("unknown Clients field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *ClientsMutation) AddedFields() []string {
	var fields []string
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *ClientsMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ClientsMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Clients numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *ClientsMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(clients.FieldSeName) {
		fields = append(fields, clients.FieldSeName)
	}
	if m.FieldCleared(clients.FieldDeletedAt) {
		fields = append(fields, clients.FieldDeletedAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *ClientsMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *ClientsMutation) ClearField(name string) error {
	switch name {
	case clients.FieldSeName:
		m.ClearSeName()
		return nil
	case clients.FieldDeletedAt:
		m.ClearDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown Clients nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *ClientsMutation) ResetField(name string) error {
	switch name {
	case clients.FieldUserID:
		m.ResetUserID()
		return nil
	case clients.FieldBranchID:
		m.ResetBranchID()
		return nil
	case clients.FieldIin:
		m.ResetIin()
		return nil
	case clients.FieldClientType:
		m.ResetClientType()
		return nil
	case clients.FieldClientName:
		m.ResetClientName()
		return nil
	case clients.FieldSeName:
		m.ResetSeName()
		return nil
	case clients.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case clients.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case clients.FieldDeletedAt:
		m.ResetDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown Clients field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *ClientsMutation) AddedEdges() []string {
	edges := make([]string, 0, 5)
	if m.branche != nil {
		edges = append(edges, clients.EdgeBranche)
	}
	if m.client_addresses != nil {
		edges = append(edges, clients.EdgeClientAddresses)
	}
	if m.client_contacts != nil {
		edges = append(edges, clients.EdgeClientContacts)
	}
	if m.credit_agreements != nil {
		edges = append(edges, clients.EdgeCreditAgreements)
	}
	if m.contact_persons != nil {
		edges = append(edges, clients.EdgeContactPersons)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *ClientsMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case clients.EdgeBranche:
		if id := m.branche; id != nil {
			return []ent.Value{*id}
		}
	case clients.EdgeClientAddresses:
		ids := make([]ent.Value, 0, len(m.client_addresses))
		for id := range m.client_addresses {
			ids = append(ids, id)
		}
		return ids
	case clients.EdgeClientContacts:
		ids := make([]ent.Value, 0, len(m.client_contacts))
		for id := range m.client_contacts {
			ids = append(ids, id)
		}
		return ids
	case clients.EdgeCreditAgreements:
		ids := make([]ent.Value, 0, len(m.credit_agreements))
		for id := range m.credit_agreements {
			ids = append(ids, id)
		}
		return ids
	case clients.EdgeContactPersons:
		ids := make([]ent.Value, 0, len(m.contact_persons))
		for id := range m.contact_persons {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *ClientsMutation) RemovedEdges() []string {
	edges := make([]string, 0, 5)
	if m.removedclient_addresses != nil {
		edges = append(edges, clients.EdgeClientAddresses)
	}
	if m.removedclient_contacts != nil {
		edges = append(edges, clients.EdgeClientContacts)
	}
	if m.removedcredit_agreements != nil {
		edges = append(edges, clients.EdgeCreditAgreements)
	}
	if m.removedcontact_persons != nil {
		edges = append(edges, clients.EdgeContactPersons)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *ClientsMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case clients.EdgeClientAddresses:
		ids := make([]ent.Value, 0, len(m.removedclient_addresses))
		for id := range m.removedclient_addresses {
			ids = append(ids, id)
		}
		return ids
	case clients.EdgeClientContacts:
		ids := make([]ent.Value, 0, len(m.removedclient_contacts))
		for id := range m.removedclient_contacts {
			ids = append(ids, id)
		}
		return ids
	case clients.EdgeCreditAgreements:
		ids := make([]ent.Value, 0, len(m.removedcredit_agreements))
		for id := range m.removedcredit_agreements {
			ids = append(ids, id)
		}
		return ids
	case clients.EdgeContactPersons:
		ids := make([]ent.Value, 0, len(m.removedcontact_persons))
		for id := range m.removedcontact_persons {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *ClientsMutation) ClearedEdges() []string {
	edges := make([]string, 0, 5)
	if m.clearedbranche {
		edges = append(edges, clients.EdgeBranche)
	}
	if m.clearedclient_addresses {
		edges = append(edges, clients.EdgeClientAddresses)
	}
	if m.clearedclient_contacts {
		edges = append(edges, clients.EdgeClientContacts)
	}
	if m.clearedcredit_agreements {
		edges = append(edges, clients.EdgeCreditAgreements)
	}
	if m.clearedcontact_persons {
		edges = append(edges, clients.EdgeContactPersons)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *ClientsMutation) EdgeCleared(name string) bool {
	switch name {
	case clients.EdgeBranche:
		return m.clearedbranche
	case clients.EdgeClientAddresses:
		return m.clearedclient_addresses
	case clients.EdgeClientContacts:
		return m.clearedclient_contacts
	case clients.EdgeCreditAgreements:
		return m.clearedcredit_agreements
	case clients.EdgeContactPersons:
		return m.clearedcontact_persons
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *ClientsMutation) ClearEdge(name string) error {
	switch name {
	case clients.EdgeBranche:
		m.ClearBranche()
		return nil
	}
	return fmt.Errorf("unknown Clients unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *ClientsMutation) ResetEdge(name string) error {
	switch name {
	case clients.EdgeBranche:
		m.ResetBranche()
		return nil
	case clients.EdgeClientAddresses:
		m.ResetClientAddresses()
		return nil
	case clients.EdgeClientContacts:
		m.ResetClientContacts()
		return nil
	case clients.EdgeCreditAgreements:
		m.ResetCreditAgreements()
		return nil
	case clients.EdgeContactPersons:
		m.ResetContactPersons()
		return nil
	}
	return fmt.Errorf("unknown Clients edge %s", name)
}

// ContactPersonsMutation represents an operation that mutates the ContactPersons nodes in the graph.
type ContactPersonsMutation struct {
	config
	op                 Op
	typ                string
	id                 *int
	relation_type_id   *string
	relation_type_name *string
	first_name         *string
	last_name          *string
	phone              *string
	created_at         *time.Time
	updated_at         *time.Time
	deleted_at         *time.Time
	survey_id          *uuid.UUID
	clearedFields      map[string]struct{}
	client             *int
	clearedclient      bool
	done               bool
	oldValue           func(context.Context) (*ContactPersons, error)
	predicates         []predicate.ContactPersons
}

var _ ent.Mutation = (*ContactPersonsMutation)(nil)

// contactpersonsOption allows management of the mutation configuration using functional options.
type contactpersonsOption func(*ContactPersonsMutation)

// newContactPersonsMutation creates new mutation for the ContactPersons entity.
func newContactPersonsMutation(c config, op Op, opts ...contactpersonsOption) *ContactPersonsMutation {
	m := &ContactPersonsMutation{
		config:        c,
		op:            op,
		typ:           TypeContactPersons,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withContactPersonsID sets the ID field of the mutation.
func withContactPersonsID(id int) contactpersonsOption {
	return func(m *ContactPersonsMutation) {
		var (
			err   error
			once  sync.Once
			value *ContactPersons
		)
		m.oldValue = func(ctx context.Context) (*ContactPersons, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().ContactPersons.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withContactPersons sets the old ContactPersons of the mutation.
func withContactPersons(node *ContactPersons) contactpersonsOption {
	return func(m *ContactPersonsMutation) {
		m.oldValue = func(context.Context) (*ContactPersons, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m ContactPersonsMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m ContactPersonsMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of ContactPersons entities.
func (m *ContactPersonsMutation) SetID(id int) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *ContactPersonsMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *ContactPersonsMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().ContactPersons.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetClientID sets the "client_id" field.
func (m *ContactPersonsMutation) SetClientID(i int) {
	m.client = &i
}

// ClientID returns the value of the "client_id" field in the mutation.
func (m *ContactPersonsMutation) ClientID() (r int, exists bool) {
	v := m.client
	if v == nil {
		return
	}
	return *v, true
}

// OldClientID returns the old "client_id" field's value of the ContactPersons entity.
// If the ContactPersons object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ContactPersonsMutation) OldClientID(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldClientID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldClientID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldClientID: %w", err)
	}
	return oldValue.ClientID, nil
}

// ResetClientID resets all changes to the "client_id" field.
func (m *ContactPersonsMutation) ResetClientID() {
	m.client = nil
}

// SetRelationTypeID sets the "relation_type_id" field.
func (m *ContactPersonsMutation) SetRelationTypeID(s string) {
	m.relation_type_id = &s
}

// RelationTypeID returns the value of the "relation_type_id" field in the mutation.
func (m *ContactPersonsMutation) RelationTypeID() (r string, exists bool) {
	v := m.relation_type_id
	if v == nil {
		return
	}
	return *v, true
}

// OldRelationTypeID returns the old "relation_type_id" field's value of the ContactPersons entity.
// If the ContactPersons object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ContactPersonsMutation) OldRelationTypeID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRelationTypeID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRelationTypeID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRelationTypeID: %w", err)
	}
	return oldValue.RelationTypeID, nil
}

// ResetRelationTypeID resets all changes to the "relation_type_id" field.
func (m *ContactPersonsMutation) ResetRelationTypeID() {
	m.relation_type_id = nil
}

// SetRelationTypeName sets the "relation_type_name" field.
func (m *ContactPersonsMutation) SetRelationTypeName(s string) {
	m.relation_type_name = &s
}

// RelationTypeName returns the value of the "relation_type_name" field in the mutation.
func (m *ContactPersonsMutation) RelationTypeName() (r string, exists bool) {
	v := m.relation_type_name
	if v == nil {
		return
	}
	return *v, true
}

// OldRelationTypeName returns the old "relation_type_name" field's value of the ContactPersons entity.
// If the ContactPersons object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ContactPersonsMutation) OldRelationTypeName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRelationTypeName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRelationTypeName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRelationTypeName: %w", err)
	}
	return oldValue.RelationTypeName, nil
}

// ResetRelationTypeName resets all changes to the "relation_type_name" field.
func (m *ContactPersonsMutation) ResetRelationTypeName() {
	m.relation_type_name = nil
}

// SetFirstName sets the "first_name" field.
func (m *ContactPersonsMutation) SetFirstName(s string) {
	m.first_name = &s
}

// FirstName returns the value of the "first_name" field in the mutation.
func (m *ContactPersonsMutation) FirstName() (r string, exists bool) {
	v := m.first_name
	if v == nil {
		return
	}
	return *v, true
}

// OldFirstName returns the old "first_name" field's value of the ContactPersons entity.
// If the ContactPersons object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ContactPersonsMutation) OldFirstName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFirstName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFirstName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFirstName: %w", err)
	}
	return oldValue.FirstName, nil
}

// ResetFirstName resets all changes to the "first_name" field.
func (m *ContactPersonsMutation) ResetFirstName() {
	m.first_name = nil
}

// SetLastName sets the "last_name" field.
func (m *ContactPersonsMutation) SetLastName(s string) {
	m.last_name = &s
}

// LastName returns the value of the "last_name" field in the mutation.
func (m *ContactPersonsMutation) LastName() (r string, exists bool) {
	v := m.last_name
	if v == nil {
		return
	}
	return *v, true
}

// OldLastName returns the old "last_name" field's value of the ContactPersons entity.
// If the ContactPersons object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ContactPersonsMutation) OldLastName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLastName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLastName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLastName: %w", err)
	}
	return oldValue.LastName, nil
}

// ResetLastName resets all changes to the "last_name" field.
func (m *ContactPersonsMutation) ResetLastName() {
	m.last_name = nil
}

// SetPhone sets the "phone" field.
func (m *ContactPersonsMutation) SetPhone(s string) {
	m.phone = &s
}

// Phone returns the value of the "phone" field in the mutation.
func (m *ContactPersonsMutation) Phone() (r string, exists bool) {
	v := m.phone
	if v == nil {
		return
	}
	return *v, true
}

// OldPhone returns the old "phone" field's value of the ContactPersons entity.
// If the ContactPersons object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ContactPersonsMutation) OldPhone(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPhone is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPhone requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPhone: %w", err)
	}
	return oldValue.Phone, nil
}

// ResetPhone resets all changes to the "phone" field.
func (m *ContactPersonsMutation) ResetPhone() {
	m.phone = nil
}

// SetCreatedAt sets the "created_at" field.
func (m *ContactPersonsMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *ContactPersonsMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the ContactPersons entity.
// If the ContactPersons object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ContactPersonsMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *ContactPersonsMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *ContactPersonsMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *ContactPersonsMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the ContactPersons entity.
// If the ContactPersons object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ContactPersonsMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *ContactPersonsMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetDeletedAt sets the "deleted_at" field.
func (m *ContactPersonsMutation) SetDeletedAt(t time.Time) {
	m.deleted_at = &t
}

// DeletedAt returns the value of the "deleted_at" field in the mutation.
func (m *ContactPersonsMutation) DeletedAt() (r time.Time, exists bool) {
	v := m.deleted_at
	if v == nil {
		return
	}
	return *v, true
}

// OldDeletedAt returns the old "deleted_at" field's value of the ContactPersons entity.
// If the ContactPersons object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ContactPersonsMutation) OldDeletedAt(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeletedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeletedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeletedAt: %w", err)
	}
	return oldValue.DeletedAt, nil
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (m *ContactPersonsMutation) ClearDeletedAt() {
	m.deleted_at = nil
	m.clearedFields[contactpersons.FieldDeletedAt] = struct{}{}
}

// DeletedAtCleared returns if the "deleted_at" field was cleared in this mutation.
func (m *ContactPersonsMutation) DeletedAtCleared() bool {
	_, ok := m.clearedFields[contactpersons.FieldDeletedAt]
	return ok
}

// ResetDeletedAt resets all changes to the "deleted_at" field.
func (m *ContactPersonsMutation) ResetDeletedAt() {
	m.deleted_at = nil
	delete(m.clearedFields, contactpersons.FieldDeletedAt)
}

// SetSurveyID sets the "survey_id" field.
func (m *ContactPersonsMutation) SetSurveyID(u uuid.UUID) {
	m.survey_id = &u
}

// SurveyID returns the value of the "survey_id" field in the mutation.
func (m *ContactPersonsMutation) SurveyID() (r uuid.UUID, exists bool) {
	v := m.survey_id
	if v == nil {
		return
	}
	return *v, true
}

// OldSurveyID returns the old "survey_id" field's value of the ContactPersons entity.
// If the ContactPersons object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ContactPersonsMutation) OldSurveyID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSurveyID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSurveyID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSurveyID: %w", err)
	}
	return oldValue.SurveyID, nil
}

// ResetSurveyID resets all changes to the "survey_id" field.
func (m *ContactPersonsMutation) ResetSurveyID() {
	m.survey_id = nil
}

// ClearClient clears the "client" edge to the Clients entity.
func (m *ContactPersonsMutation) ClearClient() {
	m.clearedclient = true
	m.clearedFields[contactpersons.FieldClientID] = struct{}{}
}

// ClientCleared reports if the "client" edge to the Clients entity was cleared.
func (m *ContactPersonsMutation) ClientCleared() bool {
	return m.clearedclient
}

// ClientIDs returns the "client" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// ClientID instead. It exists only for internal usage by the builders.
func (m *ContactPersonsMutation) ClientIDs() (ids []int) {
	if id := m.client; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetClient resets all changes to the "client" edge.
func (m *ContactPersonsMutation) ResetClient() {
	m.client = nil
	m.clearedclient = false
}

// Where appends a list predicates to the ContactPersonsMutation builder.
func (m *ContactPersonsMutation) Where(ps ...predicate.ContactPersons) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the ContactPersonsMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *ContactPersonsMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.ContactPersons, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *ContactPersonsMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *ContactPersonsMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (ContactPersons).
func (m *ContactPersonsMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *ContactPersonsMutation) Fields() []string {
	fields := make([]string, 0, 10)
	if m.client != nil {
		fields = append(fields, contactpersons.FieldClientID)
	}
	if m.relation_type_id != nil {
		fields = append(fields, contactpersons.FieldRelationTypeID)
	}
	if m.relation_type_name != nil {
		fields = append(fields, contactpersons.FieldRelationTypeName)
	}
	if m.first_name != nil {
		fields = append(fields, contactpersons.FieldFirstName)
	}
	if m.last_name != nil {
		fields = append(fields, contactpersons.FieldLastName)
	}
	if m.phone != nil {
		fields = append(fields, contactpersons.FieldPhone)
	}
	if m.created_at != nil {
		fields = append(fields, contactpersons.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, contactpersons.FieldUpdatedAt)
	}
	if m.deleted_at != nil {
		fields = append(fields, contactpersons.FieldDeletedAt)
	}
	if m.survey_id != nil {
		fields = append(fields, contactpersons.FieldSurveyID)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *ContactPersonsMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case contactpersons.FieldClientID:
		return m.ClientID()
	case contactpersons.FieldRelationTypeID:
		return m.RelationTypeID()
	case contactpersons.FieldRelationTypeName:
		return m.RelationTypeName()
	case contactpersons.FieldFirstName:
		return m.FirstName()
	case contactpersons.FieldLastName:
		return m.LastName()
	case contactpersons.FieldPhone:
		return m.Phone()
	case contactpersons.FieldCreatedAt:
		return m.CreatedAt()
	case contactpersons.FieldUpdatedAt:
		return m.UpdatedAt()
	case contactpersons.FieldDeletedAt:
		return m.DeletedAt()
	case contactpersons.FieldSurveyID:
		return m.SurveyID()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *ContactPersonsMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case contactpersons.FieldClientID:
		return m.OldClientID(ctx)
	case contactpersons.FieldRelationTypeID:
		return m.OldRelationTypeID(ctx)
	case contactpersons.FieldRelationTypeName:
		return m.OldRelationTypeName(ctx)
	case contactpersons.FieldFirstName:
		return m.OldFirstName(ctx)
	case contactpersons.FieldLastName:
		return m.OldLastName(ctx)
	case contactpersons.FieldPhone:
		return m.OldPhone(ctx)
	case contactpersons.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case contactpersons.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case contactpersons.FieldDeletedAt:
		return m.OldDeletedAt(ctx)
	case contactpersons.FieldSurveyID:
		return m.OldSurveyID(ctx)
	}
	return nil, fmt.Errorf("unknown ContactPersons field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ContactPersonsMutation) SetField(name string, value ent.Value) error {
	switch name {
	case contactpersons.FieldClientID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetClientID(v)
		return nil
	case contactpersons.FieldRelationTypeID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRelationTypeID(v)
		return nil
	case contactpersons.FieldRelationTypeName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRelationTypeName(v)
		return nil
	case contactpersons.FieldFirstName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFirstName(v)
		return nil
	case contactpersons.FieldLastName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLastName(v)
		return nil
	case contactpersons.FieldPhone:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPhone(v)
		return nil
	case contactpersons.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case contactpersons.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case contactpersons.FieldDeletedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeletedAt(v)
		return nil
	case contactpersons.FieldSurveyID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSurveyID(v)
		return nil
	}
	return fmt.Errorf("unknown ContactPersons field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *ContactPersonsMutation) AddedFields() []string {
	var fields []string
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *ContactPersonsMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ContactPersonsMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown ContactPersons numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *ContactPersonsMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(contactpersons.FieldDeletedAt) {
		fields = append(fields, contactpersons.FieldDeletedAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *ContactPersonsMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *ContactPersonsMutation) ClearField(name string) error {
	switch name {
	case contactpersons.FieldDeletedAt:
		m.ClearDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown ContactPersons nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *ContactPersonsMutation) ResetField(name string) error {
	switch name {
	case contactpersons.FieldClientID:
		m.ResetClientID()
		return nil
	case contactpersons.FieldRelationTypeID:
		m.ResetRelationTypeID()
		return nil
	case contactpersons.FieldRelationTypeName:
		m.ResetRelationTypeName()
		return nil
	case contactpersons.FieldFirstName:
		m.ResetFirstName()
		return nil
	case contactpersons.FieldLastName:
		m.ResetLastName()
		return nil
	case contactpersons.FieldPhone:
		m.ResetPhone()
		return nil
	case contactpersons.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case contactpersons.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case contactpersons.FieldDeletedAt:
		m.ResetDeletedAt()
		return nil
	case contactpersons.FieldSurveyID:
		m.ResetSurveyID()
		return nil
	}
	return fmt.Errorf("unknown ContactPersons field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *ContactPersonsMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.client != nil {
		edges = append(edges, contactpersons.EdgeClient)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *ContactPersonsMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case contactpersons.EdgeClient:
		if id := m.client; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *ContactPersonsMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *ContactPersonsMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *ContactPersonsMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedclient {
		edges = append(edges, contactpersons.EdgeClient)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *ContactPersonsMutation) EdgeCleared(name string) bool {
	switch name {
	case contactpersons.EdgeClient:
		return m.clearedclient
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *ContactPersonsMutation) ClearEdge(name string) error {
	switch name {
	case contactpersons.EdgeClient:
		m.ClearClient()
		return nil
	}
	return fmt.Errorf("unknown ContactPersons unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *ContactPersonsMutation) ResetEdge(name string) error {
	switch name {
	case contactpersons.EdgeClient:
		m.ResetClient()
		return nil
	}
	return fmt.Errorf("unknown ContactPersons edge %s", name)
}

// CreditAgreementMutation represents an operation that mutates the CreditAgreement nodes in the graph.
type CreditAgreementMutation struct {
	config
	op                   Op
	typ                  string
	id                   *int
	branch_id            *int
	addbranch_id         *int
	credit_code          *string
	product_name         *string
	from_date            *time.Time
	to_date              *time.Time
	credit_term          *int
	addcredit_term       *int
	created_at           *time.Time
	updated_at           *time.Time
	deleted_at           *time.Time
	clearedFields        map[string]struct{}
	client               *int
	clearedclient        bool
	debt_statuses        map[int]struct{}
	removeddebt_statuses map[int]struct{}
	cleareddebt_statuses bool
	done                 bool
	oldValue             func(context.Context) (*CreditAgreement, error)
	predicates           []predicate.CreditAgreement
}

var _ ent.Mutation = (*CreditAgreementMutation)(nil)

// creditagreementOption allows management of the mutation configuration using functional options.
type creditagreementOption func(*CreditAgreementMutation)

// newCreditAgreementMutation creates new mutation for the CreditAgreement entity.
func newCreditAgreementMutation(c config, op Op, opts ...creditagreementOption) *CreditAgreementMutation {
	m := &CreditAgreementMutation{
		config:        c,
		op:            op,
		typ:           TypeCreditAgreement,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withCreditAgreementID sets the ID field of the mutation.
func withCreditAgreementID(id int) creditagreementOption {
	return func(m *CreditAgreementMutation) {
		var (
			err   error
			once  sync.Once
			value *CreditAgreement
		)
		m.oldValue = func(ctx context.Context) (*CreditAgreement, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().CreditAgreement.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withCreditAgreement sets the old CreditAgreement of the mutation.
func withCreditAgreement(node *CreditAgreement) creditagreementOption {
	return func(m *CreditAgreementMutation) {
		m.oldValue = func(context.Context) (*CreditAgreement, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m CreditAgreementMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m CreditAgreementMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of CreditAgreement entities.
func (m *CreditAgreementMutation) SetID(id int) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *CreditAgreementMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *CreditAgreementMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().CreditAgreement.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetClientID sets the "client_id" field.
func (m *CreditAgreementMutation) SetClientID(i int) {
	m.client = &i
}

// ClientID returns the value of the "client_id" field in the mutation.
func (m *CreditAgreementMutation) ClientID() (r int, exists bool) {
	v := m.client
	if v == nil {
		return
	}
	return *v, true
}

// OldClientID returns the old "client_id" field's value of the CreditAgreement entity.
// If the CreditAgreement object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditAgreementMutation) OldClientID(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldClientID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldClientID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldClientID: %w", err)
	}
	return oldValue.ClientID, nil
}

// ResetClientID resets all changes to the "client_id" field.
func (m *CreditAgreementMutation) ResetClientID() {
	m.client = nil
}

// SetBranchID sets the "branch_id" field.
func (m *CreditAgreementMutation) SetBranchID(i int) {
	m.branch_id = &i
	m.addbranch_id = nil
}

// BranchID returns the value of the "branch_id" field in the mutation.
func (m *CreditAgreementMutation) BranchID() (r int, exists bool) {
	v := m.branch_id
	if v == nil {
		return
	}
	return *v, true
}

// OldBranchID returns the old "branch_id" field's value of the CreditAgreement entity.
// If the CreditAgreement object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditAgreementMutation) OldBranchID(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBranchID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBranchID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBranchID: %w", err)
	}
	return oldValue.BranchID, nil
}

// AddBranchID adds i to the "branch_id" field.
func (m *CreditAgreementMutation) AddBranchID(i int) {
	if m.addbranch_id != nil {
		*m.addbranch_id += i
	} else {
		m.addbranch_id = &i
	}
}

// AddedBranchID returns the value that was added to the "branch_id" field in this mutation.
func (m *CreditAgreementMutation) AddedBranchID() (r int, exists bool) {
	v := m.addbranch_id
	if v == nil {
		return
	}
	return *v, true
}

// ResetBranchID resets all changes to the "branch_id" field.
func (m *CreditAgreementMutation) ResetBranchID() {
	m.branch_id = nil
	m.addbranch_id = nil
}

// SetCreditCode sets the "credit_code" field.
func (m *CreditAgreementMutation) SetCreditCode(s string) {
	m.credit_code = &s
}

// CreditCode returns the value of the "credit_code" field in the mutation.
func (m *CreditAgreementMutation) CreditCode() (r string, exists bool) {
	v := m.credit_code
	if v == nil {
		return
	}
	return *v, true
}

// OldCreditCode returns the old "credit_code" field's value of the CreditAgreement entity.
// If the CreditAgreement object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditAgreementMutation) OldCreditCode(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreditCode is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreditCode requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreditCode: %w", err)
	}
	return oldValue.CreditCode, nil
}

// ResetCreditCode resets all changes to the "credit_code" field.
func (m *CreditAgreementMutation) ResetCreditCode() {
	m.credit_code = nil
}

// SetProductName sets the "product_name" field.
func (m *CreditAgreementMutation) SetProductName(s string) {
	m.product_name = &s
}

// ProductName returns the value of the "product_name" field in the mutation.
func (m *CreditAgreementMutation) ProductName() (r string, exists bool) {
	v := m.product_name
	if v == nil {
		return
	}
	return *v, true
}

// OldProductName returns the old "product_name" field's value of the CreditAgreement entity.
// If the CreditAgreement object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditAgreementMutation) OldProductName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldProductName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldProductName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldProductName: %w", err)
	}
	return oldValue.ProductName, nil
}

// ResetProductName resets all changes to the "product_name" field.
func (m *CreditAgreementMutation) ResetProductName() {
	m.product_name = nil
}

// SetFromDate sets the "from_date" field.
func (m *CreditAgreementMutation) SetFromDate(t time.Time) {
	m.from_date = &t
}

// FromDate returns the value of the "from_date" field in the mutation.
func (m *CreditAgreementMutation) FromDate() (r time.Time, exists bool) {
	v := m.from_date
	if v == nil {
		return
	}
	return *v, true
}

// OldFromDate returns the old "from_date" field's value of the CreditAgreement entity.
// If the CreditAgreement object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditAgreementMutation) OldFromDate(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFromDate is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFromDate requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFromDate: %w", err)
	}
	return oldValue.FromDate, nil
}

// ResetFromDate resets all changes to the "from_date" field.
func (m *CreditAgreementMutation) ResetFromDate() {
	m.from_date = nil
}

// SetToDate sets the "to_date" field.
func (m *CreditAgreementMutation) SetToDate(t time.Time) {
	m.to_date = &t
}

// ToDate returns the value of the "to_date" field in the mutation.
func (m *CreditAgreementMutation) ToDate() (r time.Time, exists bool) {
	v := m.to_date
	if v == nil {
		return
	}
	return *v, true
}

// OldToDate returns the old "to_date" field's value of the CreditAgreement entity.
// If the CreditAgreement object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditAgreementMutation) OldToDate(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldToDate is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldToDate requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldToDate: %w", err)
	}
	return oldValue.ToDate, nil
}

// ClearToDate clears the value of the "to_date" field.
func (m *CreditAgreementMutation) ClearToDate() {
	m.to_date = nil
	m.clearedFields[creditagreement.FieldToDate] = struct{}{}
}

// ToDateCleared returns if the "to_date" field was cleared in this mutation.
func (m *CreditAgreementMutation) ToDateCleared() bool {
	_, ok := m.clearedFields[creditagreement.FieldToDate]
	return ok
}

// ResetToDate resets all changes to the "to_date" field.
func (m *CreditAgreementMutation) ResetToDate() {
	m.to_date = nil
	delete(m.clearedFields, creditagreement.FieldToDate)
}

// SetCreditTerm sets the "credit_term" field.
func (m *CreditAgreementMutation) SetCreditTerm(i int) {
	m.credit_term = &i
	m.addcredit_term = nil
}

// CreditTerm returns the value of the "credit_term" field in the mutation.
func (m *CreditAgreementMutation) CreditTerm() (r int, exists bool) {
	v := m.credit_term
	if v == nil {
		return
	}
	return *v, true
}

// OldCreditTerm returns the old "credit_term" field's value of the CreditAgreement entity.
// If the CreditAgreement object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditAgreementMutation) OldCreditTerm(ctx context.Context) (v *int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreditTerm is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreditTerm requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreditTerm: %w", err)
	}
	return oldValue.CreditTerm, nil
}

// AddCreditTerm adds i to the "credit_term" field.
func (m *CreditAgreementMutation) AddCreditTerm(i int) {
	if m.addcredit_term != nil {
		*m.addcredit_term += i
	} else {
		m.addcredit_term = &i
	}
}

// AddedCreditTerm returns the value that was added to the "credit_term" field in this mutation.
func (m *CreditAgreementMutation) AddedCreditTerm() (r int, exists bool) {
	v := m.addcredit_term
	if v == nil {
		return
	}
	return *v, true
}

// ClearCreditTerm clears the value of the "credit_term" field.
func (m *CreditAgreementMutation) ClearCreditTerm() {
	m.credit_term = nil
	m.addcredit_term = nil
	m.clearedFields[creditagreement.FieldCreditTerm] = struct{}{}
}

// CreditTermCleared returns if the "credit_term" field was cleared in this mutation.
func (m *CreditAgreementMutation) CreditTermCleared() bool {
	_, ok := m.clearedFields[creditagreement.FieldCreditTerm]
	return ok
}

// ResetCreditTerm resets all changes to the "credit_term" field.
func (m *CreditAgreementMutation) ResetCreditTerm() {
	m.credit_term = nil
	m.addcredit_term = nil
	delete(m.clearedFields, creditagreement.FieldCreditTerm)
}

// SetCreatedAt sets the "created_at" field.
func (m *CreditAgreementMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *CreditAgreementMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the CreditAgreement entity.
// If the CreditAgreement object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditAgreementMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *CreditAgreementMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *CreditAgreementMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *CreditAgreementMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the CreditAgreement entity.
// If the CreditAgreement object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditAgreementMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *CreditAgreementMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetDeletedAt sets the "deleted_at" field.
func (m *CreditAgreementMutation) SetDeletedAt(t time.Time) {
	m.deleted_at = &t
}

// DeletedAt returns the value of the "deleted_at" field in the mutation.
func (m *CreditAgreementMutation) DeletedAt() (r time.Time, exists bool) {
	v := m.deleted_at
	if v == nil {
		return
	}
	return *v, true
}

// OldDeletedAt returns the old "deleted_at" field's value of the CreditAgreement entity.
// If the CreditAgreement object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditAgreementMutation) OldDeletedAt(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeletedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeletedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeletedAt: %w", err)
	}
	return oldValue.DeletedAt, nil
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (m *CreditAgreementMutation) ClearDeletedAt() {
	m.deleted_at = nil
	m.clearedFields[creditagreement.FieldDeletedAt] = struct{}{}
}

// DeletedAtCleared returns if the "deleted_at" field was cleared in this mutation.
func (m *CreditAgreementMutation) DeletedAtCleared() bool {
	_, ok := m.clearedFields[creditagreement.FieldDeletedAt]
	return ok
}

// ResetDeletedAt resets all changes to the "deleted_at" field.
func (m *CreditAgreementMutation) ResetDeletedAt() {
	m.deleted_at = nil
	delete(m.clearedFields, creditagreement.FieldDeletedAt)
}

// ClearClient clears the "client" edge to the Clients entity.
func (m *CreditAgreementMutation) ClearClient() {
	m.clearedclient = true
	m.clearedFields[creditagreement.FieldClientID] = struct{}{}
}

// ClientCleared reports if the "client" edge to the Clients entity was cleared.
func (m *CreditAgreementMutation) ClientCleared() bool {
	return m.clearedclient
}

// ClientIDs returns the "client" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// ClientID instead. It exists only for internal usage by the builders.
func (m *CreditAgreementMutation) ClientIDs() (ids []int) {
	if id := m.client; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetClient resets all changes to the "client" edge.
func (m *CreditAgreementMutation) ResetClient() {
	m.client = nil
	m.clearedclient = false
}

// AddDebtStatusIDs adds the "debt_statuses" edge to the CreditDebtStatus entity by ids.
func (m *CreditAgreementMutation) AddDebtStatusIDs(ids ...int) {
	if m.debt_statuses == nil {
		m.debt_statuses = make(map[int]struct{})
	}
	for i := range ids {
		m.debt_statuses[ids[i]] = struct{}{}
	}
}

// ClearDebtStatuses clears the "debt_statuses" edge to the CreditDebtStatus entity.
func (m *CreditAgreementMutation) ClearDebtStatuses() {
	m.cleareddebt_statuses = true
}

// DebtStatusesCleared reports if the "debt_statuses" edge to the CreditDebtStatus entity was cleared.
func (m *CreditAgreementMutation) DebtStatusesCleared() bool {
	return m.cleareddebt_statuses
}

// RemoveDebtStatusIDs removes the "debt_statuses" edge to the CreditDebtStatus entity by IDs.
func (m *CreditAgreementMutation) RemoveDebtStatusIDs(ids ...int) {
	if m.removeddebt_statuses == nil {
		m.removeddebt_statuses = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.debt_statuses, ids[i])
		m.removeddebt_statuses[ids[i]] = struct{}{}
	}
}

// RemovedDebtStatuses returns the removed IDs of the "debt_statuses" edge to the CreditDebtStatus entity.
func (m *CreditAgreementMutation) RemovedDebtStatusesIDs() (ids []int) {
	for id := range m.removeddebt_statuses {
		ids = append(ids, id)
	}
	return
}

// DebtStatusesIDs returns the "debt_statuses" edge IDs in the mutation.
func (m *CreditAgreementMutation) DebtStatusesIDs() (ids []int) {
	for id := range m.debt_statuses {
		ids = append(ids, id)
	}
	return
}

// ResetDebtStatuses resets all changes to the "debt_statuses" edge.
func (m *CreditAgreementMutation) ResetDebtStatuses() {
	m.debt_statuses = nil
	m.cleareddebt_statuses = false
	m.removeddebt_statuses = nil
}

// Where appends a list predicates to the CreditAgreementMutation builder.
func (m *CreditAgreementMutation) Where(ps ...predicate.CreditAgreement) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the CreditAgreementMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *CreditAgreementMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.CreditAgreement, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *CreditAgreementMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *CreditAgreementMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (CreditAgreement).
func (m *CreditAgreementMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *CreditAgreementMutation) Fields() []string {
	fields := make([]string, 0, 10)
	if m.client != nil {
		fields = append(fields, creditagreement.FieldClientID)
	}
	if m.branch_id != nil {
		fields = append(fields, creditagreement.FieldBranchID)
	}
	if m.credit_code != nil {
		fields = append(fields, creditagreement.FieldCreditCode)
	}
	if m.product_name != nil {
		fields = append(fields, creditagreement.FieldProductName)
	}
	if m.from_date != nil {
		fields = append(fields, creditagreement.FieldFromDate)
	}
	if m.to_date != nil {
		fields = append(fields, creditagreement.FieldToDate)
	}
	if m.credit_term != nil {
		fields = append(fields, creditagreement.FieldCreditTerm)
	}
	if m.created_at != nil {
		fields = append(fields, creditagreement.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, creditagreement.FieldUpdatedAt)
	}
	if m.deleted_at != nil {
		fields = append(fields, creditagreement.FieldDeletedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *CreditAgreementMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case creditagreement.FieldClientID:
		return m.ClientID()
	case creditagreement.FieldBranchID:
		return m.BranchID()
	case creditagreement.FieldCreditCode:
		return m.CreditCode()
	case creditagreement.FieldProductName:
		return m.ProductName()
	case creditagreement.FieldFromDate:
		return m.FromDate()
	case creditagreement.FieldToDate:
		return m.ToDate()
	case creditagreement.FieldCreditTerm:
		return m.CreditTerm()
	case creditagreement.FieldCreatedAt:
		return m.CreatedAt()
	case creditagreement.FieldUpdatedAt:
		return m.UpdatedAt()
	case creditagreement.FieldDeletedAt:
		return m.DeletedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *CreditAgreementMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case creditagreement.FieldClientID:
		return m.OldClientID(ctx)
	case creditagreement.FieldBranchID:
		return m.OldBranchID(ctx)
	case creditagreement.FieldCreditCode:
		return m.OldCreditCode(ctx)
	case creditagreement.FieldProductName:
		return m.OldProductName(ctx)
	case creditagreement.FieldFromDate:
		return m.OldFromDate(ctx)
	case creditagreement.FieldToDate:
		return m.OldToDate(ctx)
	case creditagreement.FieldCreditTerm:
		return m.OldCreditTerm(ctx)
	case creditagreement.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case creditagreement.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case creditagreement.FieldDeletedAt:
		return m.OldDeletedAt(ctx)
	}
	return nil, fmt.Errorf("unknown CreditAgreement field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *CreditAgreementMutation) SetField(name string, value ent.Value) error {
	switch name {
	case creditagreement.FieldClientID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetClientID(v)
		return nil
	case creditagreement.FieldBranchID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBranchID(v)
		return nil
	case creditagreement.FieldCreditCode:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreditCode(v)
		return nil
	case creditagreement.FieldProductName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetProductName(v)
		return nil
	case creditagreement.FieldFromDate:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFromDate(v)
		return nil
	case creditagreement.FieldToDate:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetToDate(v)
		return nil
	case creditagreement.FieldCreditTerm:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreditTerm(v)
		return nil
	case creditagreement.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case creditagreement.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case creditagreement.FieldDeletedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeletedAt(v)
		return nil
	}
	return fmt.Errorf("unknown CreditAgreement field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *CreditAgreementMutation) AddedFields() []string {
	var fields []string
	if m.addbranch_id != nil {
		fields = append(fields, creditagreement.FieldBranchID)
	}
	if m.addcredit_term != nil {
		fields = append(fields, creditagreement.FieldCreditTerm)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *CreditAgreementMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case creditagreement.FieldBranchID:
		return m.AddedBranchID()
	case creditagreement.FieldCreditTerm:
		return m.AddedCreditTerm()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *CreditAgreementMutation) AddField(name string, value ent.Value) error {
	switch name {
	case creditagreement.FieldBranchID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddBranchID(v)
		return nil
	case creditagreement.FieldCreditTerm:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddCreditTerm(v)
		return nil
	}
	return fmt.Errorf("unknown CreditAgreement numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *CreditAgreementMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(creditagreement.FieldToDate) {
		fields = append(fields, creditagreement.FieldToDate)
	}
	if m.FieldCleared(creditagreement.FieldCreditTerm) {
		fields = append(fields, creditagreement.FieldCreditTerm)
	}
	if m.FieldCleared(creditagreement.FieldDeletedAt) {
		fields = append(fields, creditagreement.FieldDeletedAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *CreditAgreementMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *CreditAgreementMutation) ClearField(name string) error {
	switch name {
	case creditagreement.FieldToDate:
		m.ClearToDate()
		return nil
	case creditagreement.FieldCreditTerm:
		m.ClearCreditTerm()
		return nil
	case creditagreement.FieldDeletedAt:
		m.ClearDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown CreditAgreement nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *CreditAgreementMutation) ResetField(name string) error {
	switch name {
	case creditagreement.FieldClientID:
		m.ResetClientID()
		return nil
	case creditagreement.FieldBranchID:
		m.ResetBranchID()
		return nil
	case creditagreement.FieldCreditCode:
		m.ResetCreditCode()
		return nil
	case creditagreement.FieldProductName:
		m.ResetProductName()
		return nil
	case creditagreement.FieldFromDate:
		m.ResetFromDate()
		return nil
	case creditagreement.FieldToDate:
		m.ResetToDate()
		return nil
	case creditagreement.FieldCreditTerm:
		m.ResetCreditTerm()
		return nil
	case creditagreement.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case creditagreement.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case creditagreement.FieldDeletedAt:
		m.ResetDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown CreditAgreement field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *CreditAgreementMutation) AddedEdges() []string {
	edges := make([]string, 0, 2)
	if m.client != nil {
		edges = append(edges, creditagreement.EdgeClient)
	}
	if m.debt_statuses != nil {
		edges = append(edges, creditagreement.EdgeDebtStatuses)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *CreditAgreementMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case creditagreement.EdgeClient:
		if id := m.client; id != nil {
			return []ent.Value{*id}
		}
	case creditagreement.EdgeDebtStatuses:
		ids := make([]ent.Value, 0, len(m.debt_statuses))
		for id := range m.debt_statuses {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *CreditAgreementMutation) RemovedEdges() []string {
	edges := make([]string, 0, 2)
	if m.removeddebt_statuses != nil {
		edges = append(edges, creditagreement.EdgeDebtStatuses)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *CreditAgreementMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case creditagreement.EdgeDebtStatuses:
		ids := make([]ent.Value, 0, len(m.removeddebt_statuses))
		for id := range m.removeddebt_statuses {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *CreditAgreementMutation) ClearedEdges() []string {
	edges := make([]string, 0, 2)
	if m.clearedclient {
		edges = append(edges, creditagreement.EdgeClient)
	}
	if m.cleareddebt_statuses {
		edges = append(edges, creditagreement.EdgeDebtStatuses)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *CreditAgreementMutation) EdgeCleared(name string) bool {
	switch name {
	case creditagreement.EdgeClient:
		return m.clearedclient
	case creditagreement.EdgeDebtStatuses:
		return m.cleareddebt_statuses
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *CreditAgreementMutation) ClearEdge(name string) error {
	switch name {
	case creditagreement.EdgeClient:
		m.ClearClient()
		return nil
	}
	return fmt.Errorf("unknown CreditAgreement unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *CreditAgreementMutation) ResetEdge(name string) error {
	switch name {
	case creditagreement.EdgeClient:
		m.ResetClient()
		return nil
	case creditagreement.EdgeDebtStatuses:
		m.ResetDebtStatuses()
		return nil
	}
	return fmt.Errorf("unknown CreditAgreement edge %s", name)
}

// CreditDebtStatusMutation represents an operation that mutates the CreditDebtStatus nodes in the graph.
type CreditDebtStatusMutation struct {
	config
	op                        Op
	typ                       string
	id                        *int
	delay_days_count          *int
	adddelay_days_count       *int
	main_debt_expired_days    *int
	addmain_debt_expired_days *int
	interest_expired_days     *int
	addinterest_expired_days  *int
	restriction_fl            *bool
	main_debt                 *float64
	addmain_debt              *float64
	amount_interest           *float64
	addamount_interest        *float64
	overdue_debt              *float64
	addoverdue_debt           *float64
	overdue_interest          *float64
	addoverdue_interest       *float64
	fine                      *float64
	addfine                   *float64
	status_date               *time.Time
	is_archived               *bool
	created_at                *time.Time
	updated_at                *time.Time
	deleted_at                *time.Time
	clearedFields             map[string]struct{}
	credit_agreement          *int
	clearedcredit_agreement   bool
	done                      bool
	oldValue                  func(context.Context) (*CreditDebtStatus, error)
	predicates                []predicate.CreditDebtStatus
}

var _ ent.Mutation = (*CreditDebtStatusMutation)(nil)

// creditdebtstatusOption allows management of the mutation configuration using functional options.
type creditdebtstatusOption func(*CreditDebtStatusMutation)

// newCreditDebtStatusMutation creates new mutation for the CreditDebtStatus entity.
func newCreditDebtStatusMutation(c config, op Op, opts ...creditdebtstatusOption) *CreditDebtStatusMutation {
	m := &CreditDebtStatusMutation{
		config:        c,
		op:            op,
		typ:           TypeCreditDebtStatus,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withCreditDebtStatusID sets the ID field of the mutation.
func withCreditDebtStatusID(id int) creditdebtstatusOption {
	return func(m *CreditDebtStatusMutation) {
		var (
			err   error
			once  sync.Once
			value *CreditDebtStatus
		)
		m.oldValue = func(ctx context.Context) (*CreditDebtStatus, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().CreditDebtStatus.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withCreditDebtStatus sets the old CreditDebtStatus of the mutation.
func withCreditDebtStatus(node *CreditDebtStatus) creditdebtstatusOption {
	return func(m *CreditDebtStatusMutation) {
		m.oldValue = func(context.Context) (*CreditDebtStatus, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m CreditDebtStatusMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m CreditDebtStatusMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of CreditDebtStatus entities.
func (m *CreditDebtStatusMutation) SetID(id int) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *CreditDebtStatusMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *CreditDebtStatusMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().CreditDebtStatus.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreditAgreementID sets the "credit_agreement_id" field.
func (m *CreditDebtStatusMutation) SetCreditAgreementID(i int) {
	m.credit_agreement = &i
}

// CreditAgreementID returns the value of the "credit_agreement_id" field in the mutation.
func (m *CreditDebtStatusMutation) CreditAgreementID() (r int, exists bool) {
	v := m.credit_agreement
	if v == nil {
		return
	}
	return *v, true
}

// OldCreditAgreementID returns the old "credit_agreement_id" field's value of the CreditDebtStatus entity.
// If the CreditDebtStatus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditDebtStatusMutation) OldCreditAgreementID(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreditAgreementID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreditAgreementID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreditAgreementID: %w", err)
	}
	return oldValue.CreditAgreementID, nil
}

// ResetCreditAgreementID resets all changes to the "credit_agreement_id" field.
func (m *CreditDebtStatusMutation) ResetCreditAgreementID() {
	m.credit_agreement = nil
}

// SetDelayDaysCount sets the "delay_days_count" field.
func (m *CreditDebtStatusMutation) SetDelayDaysCount(i int) {
	m.delay_days_count = &i
	m.adddelay_days_count = nil
}

// DelayDaysCount returns the value of the "delay_days_count" field in the mutation.
func (m *CreditDebtStatusMutation) DelayDaysCount() (r int, exists bool) {
	v := m.delay_days_count
	if v == nil {
		return
	}
	return *v, true
}

// OldDelayDaysCount returns the old "delay_days_count" field's value of the CreditDebtStatus entity.
// If the CreditDebtStatus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditDebtStatusMutation) OldDelayDaysCount(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDelayDaysCount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDelayDaysCount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDelayDaysCount: %w", err)
	}
	return oldValue.DelayDaysCount, nil
}

// AddDelayDaysCount adds i to the "delay_days_count" field.
func (m *CreditDebtStatusMutation) AddDelayDaysCount(i int) {
	if m.adddelay_days_count != nil {
		*m.adddelay_days_count += i
	} else {
		m.adddelay_days_count = &i
	}
}

// AddedDelayDaysCount returns the value that was added to the "delay_days_count" field in this mutation.
func (m *CreditDebtStatusMutation) AddedDelayDaysCount() (r int, exists bool) {
	v := m.adddelay_days_count
	if v == nil {
		return
	}
	return *v, true
}

// ResetDelayDaysCount resets all changes to the "delay_days_count" field.
func (m *CreditDebtStatusMutation) ResetDelayDaysCount() {
	m.delay_days_count = nil
	m.adddelay_days_count = nil
}

// SetMainDebtExpiredDays sets the "main_debt_expired_days" field.
func (m *CreditDebtStatusMutation) SetMainDebtExpiredDays(i int) {
	m.main_debt_expired_days = &i
	m.addmain_debt_expired_days = nil
}

// MainDebtExpiredDays returns the value of the "main_debt_expired_days" field in the mutation.
func (m *CreditDebtStatusMutation) MainDebtExpiredDays() (r int, exists bool) {
	v := m.main_debt_expired_days
	if v == nil {
		return
	}
	return *v, true
}

// OldMainDebtExpiredDays returns the old "main_debt_expired_days" field's value of the CreditDebtStatus entity.
// If the CreditDebtStatus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditDebtStatusMutation) OldMainDebtExpiredDays(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMainDebtExpiredDays is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMainDebtExpiredDays requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMainDebtExpiredDays: %w", err)
	}
	return oldValue.MainDebtExpiredDays, nil
}

// AddMainDebtExpiredDays adds i to the "main_debt_expired_days" field.
func (m *CreditDebtStatusMutation) AddMainDebtExpiredDays(i int) {
	if m.addmain_debt_expired_days != nil {
		*m.addmain_debt_expired_days += i
	} else {
		m.addmain_debt_expired_days = &i
	}
}

// AddedMainDebtExpiredDays returns the value that was added to the "main_debt_expired_days" field in this mutation.
func (m *CreditDebtStatusMutation) AddedMainDebtExpiredDays() (r int, exists bool) {
	v := m.addmain_debt_expired_days
	if v == nil {
		return
	}
	return *v, true
}

// ResetMainDebtExpiredDays resets all changes to the "main_debt_expired_days" field.
func (m *CreditDebtStatusMutation) ResetMainDebtExpiredDays() {
	m.main_debt_expired_days = nil
	m.addmain_debt_expired_days = nil
}

// SetInterestExpiredDays sets the "interest_expired_days" field.
func (m *CreditDebtStatusMutation) SetInterestExpiredDays(i int) {
	m.interest_expired_days = &i
	m.addinterest_expired_days = nil
}

// InterestExpiredDays returns the value of the "interest_expired_days" field in the mutation.
func (m *CreditDebtStatusMutation) InterestExpiredDays() (r int, exists bool) {
	v := m.interest_expired_days
	if v == nil {
		return
	}
	return *v, true
}

// OldInterestExpiredDays returns the old "interest_expired_days" field's value of the CreditDebtStatus entity.
// If the CreditDebtStatus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditDebtStatusMutation) OldInterestExpiredDays(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldInterestExpiredDays is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldInterestExpiredDays requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldInterestExpiredDays: %w", err)
	}
	return oldValue.InterestExpiredDays, nil
}

// AddInterestExpiredDays adds i to the "interest_expired_days" field.
func (m *CreditDebtStatusMutation) AddInterestExpiredDays(i int) {
	if m.addinterest_expired_days != nil {
		*m.addinterest_expired_days += i
	} else {
		m.addinterest_expired_days = &i
	}
}

// AddedInterestExpiredDays returns the value that was added to the "interest_expired_days" field in this mutation.
func (m *CreditDebtStatusMutation) AddedInterestExpiredDays() (r int, exists bool) {
	v := m.addinterest_expired_days
	if v == nil {
		return
	}
	return *v, true
}

// ResetInterestExpiredDays resets all changes to the "interest_expired_days" field.
func (m *CreditDebtStatusMutation) ResetInterestExpiredDays() {
	m.interest_expired_days = nil
	m.addinterest_expired_days = nil
}

// SetRestrictionFl sets the "restriction_fl" field.
func (m *CreditDebtStatusMutation) SetRestrictionFl(b bool) {
	m.restriction_fl = &b
}

// RestrictionFl returns the value of the "restriction_fl" field in the mutation.
func (m *CreditDebtStatusMutation) RestrictionFl() (r bool, exists bool) {
	v := m.restriction_fl
	if v == nil {
		return
	}
	return *v, true
}

// OldRestrictionFl returns the old "restriction_fl" field's value of the CreditDebtStatus entity.
// If the CreditDebtStatus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditDebtStatusMutation) OldRestrictionFl(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRestrictionFl is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRestrictionFl requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRestrictionFl: %w", err)
	}
	return oldValue.RestrictionFl, nil
}

// ResetRestrictionFl resets all changes to the "restriction_fl" field.
func (m *CreditDebtStatusMutation) ResetRestrictionFl() {
	m.restriction_fl = nil
}

// SetMainDebt sets the "main_debt" field.
func (m *CreditDebtStatusMutation) SetMainDebt(f float64) {
	m.main_debt = &f
	m.addmain_debt = nil
}

// MainDebt returns the value of the "main_debt" field in the mutation.
func (m *CreditDebtStatusMutation) MainDebt() (r float64, exists bool) {
	v := m.main_debt
	if v == nil {
		return
	}
	return *v, true
}

// OldMainDebt returns the old "main_debt" field's value of the CreditDebtStatus entity.
// If the CreditDebtStatus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditDebtStatusMutation) OldMainDebt(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMainDebt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMainDebt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMainDebt: %w", err)
	}
	return oldValue.MainDebt, nil
}

// AddMainDebt adds f to the "main_debt" field.
func (m *CreditDebtStatusMutation) AddMainDebt(f float64) {
	if m.addmain_debt != nil {
		*m.addmain_debt += f
	} else {
		m.addmain_debt = &f
	}
}

// AddedMainDebt returns the value that was added to the "main_debt" field in this mutation.
func (m *CreditDebtStatusMutation) AddedMainDebt() (r float64, exists bool) {
	v := m.addmain_debt
	if v == nil {
		return
	}
	return *v, true
}

// ResetMainDebt resets all changes to the "main_debt" field.
func (m *CreditDebtStatusMutation) ResetMainDebt() {
	m.main_debt = nil
	m.addmain_debt = nil
}

// SetAmountInterest sets the "amount_interest" field.
func (m *CreditDebtStatusMutation) SetAmountInterest(f float64) {
	m.amount_interest = &f
	m.addamount_interest = nil
}

// AmountInterest returns the value of the "amount_interest" field in the mutation.
func (m *CreditDebtStatusMutation) AmountInterest() (r float64, exists bool) {
	v := m.amount_interest
	if v == nil {
		return
	}
	return *v, true
}

// OldAmountInterest returns the old "amount_interest" field's value of the CreditDebtStatus entity.
// If the CreditDebtStatus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditDebtStatusMutation) OldAmountInterest(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAmountInterest is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAmountInterest requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAmountInterest: %w", err)
	}
	return oldValue.AmountInterest, nil
}

// AddAmountInterest adds f to the "amount_interest" field.
func (m *CreditDebtStatusMutation) AddAmountInterest(f float64) {
	if m.addamount_interest != nil {
		*m.addamount_interest += f
	} else {
		m.addamount_interest = &f
	}
}

// AddedAmountInterest returns the value that was added to the "amount_interest" field in this mutation.
func (m *CreditDebtStatusMutation) AddedAmountInterest() (r float64, exists bool) {
	v := m.addamount_interest
	if v == nil {
		return
	}
	return *v, true
}

// ResetAmountInterest resets all changes to the "amount_interest" field.
func (m *CreditDebtStatusMutation) ResetAmountInterest() {
	m.amount_interest = nil
	m.addamount_interest = nil
}

// SetOverdueDebt sets the "overdue_debt" field.
func (m *CreditDebtStatusMutation) SetOverdueDebt(f float64) {
	m.overdue_debt = &f
	m.addoverdue_debt = nil
}

// OverdueDebt returns the value of the "overdue_debt" field in the mutation.
func (m *CreditDebtStatusMutation) OverdueDebt() (r float64, exists bool) {
	v := m.overdue_debt
	if v == nil {
		return
	}
	return *v, true
}

// OldOverdueDebt returns the old "overdue_debt" field's value of the CreditDebtStatus entity.
// If the CreditDebtStatus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditDebtStatusMutation) OldOverdueDebt(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldOverdueDebt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldOverdueDebt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldOverdueDebt: %w", err)
	}
	return oldValue.OverdueDebt, nil
}

// AddOverdueDebt adds f to the "overdue_debt" field.
func (m *CreditDebtStatusMutation) AddOverdueDebt(f float64) {
	if m.addoverdue_debt != nil {
		*m.addoverdue_debt += f
	} else {
		m.addoverdue_debt = &f
	}
}

// AddedOverdueDebt returns the value that was added to the "overdue_debt" field in this mutation.
func (m *CreditDebtStatusMutation) AddedOverdueDebt() (r float64, exists bool) {
	v := m.addoverdue_debt
	if v == nil {
		return
	}
	return *v, true
}

// ResetOverdueDebt resets all changes to the "overdue_debt" field.
func (m *CreditDebtStatusMutation) ResetOverdueDebt() {
	m.overdue_debt = nil
	m.addoverdue_debt = nil
}

// SetOverdueInterest sets the "overdue_interest" field.
func (m *CreditDebtStatusMutation) SetOverdueInterest(f float64) {
	m.overdue_interest = &f
	m.addoverdue_interest = nil
}

// OverdueInterest returns the value of the "overdue_interest" field in the mutation.
func (m *CreditDebtStatusMutation) OverdueInterest() (r float64, exists bool) {
	v := m.overdue_interest
	if v == nil {
		return
	}
	return *v, true
}

// OldOverdueInterest returns the old "overdue_interest" field's value of the CreditDebtStatus entity.
// If the CreditDebtStatus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditDebtStatusMutation) OldOverdueInterest(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldOverdueInterest is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldOverdueInterest requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldOverdueInterest: %w", err)
	}
	return oldValue.OverdueInterest, nil
}

// AddOverdueInterest adds f to the "overdue_interest" field.
func (m *CreditDebtStatusMutation) AddOverdueInterest(f float64) {
	if m.addoverdue_interest != nil {
		*m.addoverdue_interest += f
	} else {
		m.addoverdue_interest = &f
	}
}

// AddedOverdueInterest returns the value that was added to the "overdue_interest" field in this mutation.
func (m *CreditDebtStatusMutation) AddedOverdueInterest() (r float64, exists bool) {
	v := m.addoverdue_interest
	if v == nil {
		return
	}
	return *v, true
}

// ResetOverdueInterest resets all changes to the "overdue_interest" field.
func (m *CreditDebtStatusMutation) ResetOverdueInterest() {
	m.overdue_interest = nil
	m.addoverdue_interest = nil
}

// SetFine sets the "fine" field.
func (m *CreditDebtStatusMutation) SetFine(f float64) {
	m.fine = &f
	m.addfine = nil
}

// Fine returns the value of the "fine" field in the mutation.
func (m *CreditDebtStatusMutation) Fine() (r float64, exists bool) {
	v := m.fine
	if v == nil {
		return
	}
	return *v, true
}

// OldFine returns the old "fine" field's value of the CreditDebtStatus entity.
// If the CreditDebtStatus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditDebtStatusMutation) OldFine(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFine is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFine requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFine: %w", err)
	}
	return oldValue.Fine, nil
}

// AddFine adds f to the "fine" field.
func (m *CreditDebtStatusMutation) AddFine(f float64) {
	if m.addfine != nil {
		*m.addfine += f
	} else {
		m.addfine = &f
	}
}

// AddedFine returns the value that was added to the "fine" field in this mutation.
func (m *CreditDebtStatusMutation) AddedFine() (r float64, exists bool) {
	v := m.addfine
	if v == nil {
		return
	}
	return *v, true
}

// ResetFine resets all changes to the "fine" field.
func (m *CreditDebtStatusMutation) ResetFine() {
	m.fine = nil
	m.addfine = nil
}

// SetStatusDate sets the "status_date" field.
func (m *CreditDebtStatusMutation) SetStatusDate(t time.Time) {
	m.status_date = &t
}

// StatusDate returns the value of the "status_date" field in the mutation.
func (m *CreditDebtStatusMutation) StatusDate() (r time.Time, exists bool) {
	v := m.status_date
	if v == nil {
		return
	}
	return *v, true
}

// OldStatusDate returns the old "status_date" field's value of the CreditDebtStatus entity.
// If the CreditDebtStatus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditDebtStatusMutation) OldStatusDate(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatusDate is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatusDate requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatusDate: %w", err)
	}
	return oldValue.StatusDate, nil
}

// ResetStatusDate resets all changes to the "status_date" field.
func (m *CreditDebtStatusMutation) ResetStatusDate() {
	m.status_date = nil
}

// SetIsArchived sets the "is_archived" field.
func (m *CreditDebtStatusMutation) SetIsArchived(b bool) {
	m.is_archived = &b
}

// IsArchived returns the value of the "is_archived" field in the mutation.
func (m *CreditDebtStatusMutation) IsArchived() (r bool, exists bool) {
	v := m.is_archived
	if v == nil {
		return
	}
	return *v, true
}

// OldIsArchived returns the old "is_archived" field's value of the CreditDebtStatus entity.
// If the CreditDebtStatus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditDebtStatusMutation) OldIsArchived(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsArchived is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsArchived requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsArchived: %w", err)
	}
	return oldValue.IsArchived, nil
}

// ResetIsArchived resets all changes to the "is_archived" field.
func (m *CreditDebtStatusMutation) ResetIsArchived() {
	m.is_archived = nil
}

// SetCreatedAt sets the "created_at" field.
func (m *CreditDebtStatusMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *CreditDebtStatusMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the CreditDebtStatus entity.
// If the CreditDebtStatus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditDebtStatusMutation) OldCreatedAt(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ClearCreatedAt clears the value of the "created_at" field.
func (m *CreditDebtStatusMutation) ClearCreatedAt() {
	m.created_at = nil
	m.clearedFields[creditdebtstatus.FieldCreatedAt] = struct{}{}
}

// CreatedAtCleared returns if the "created_at" field was cleared in this mutation.
func (m *CreditDebtStatusMutation) CreatedAtCleared() bool {
	_, ok := m.clearedFields[creditdebtstatus.FieldCreatedAt]
	return ok
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *CreditDebtStatusMutation) ResetCreatedAt() {
	m.created_at = nil
	delete(m.clearedFields, creditdebtstatus.FieldCreatedAt)
}

// SetUpdatedAt sets the "updated_at" field.
func (m *CreditDebtStatusMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *CreditDebtStatusMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the CreditDebtStatus entity.
// If the CreditDebtStatus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditDebtStatusMutation) OldUpdatedAt(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ClearUpdatedAt clears the value of the "updated_at" field.
func (m *CreditDebtStatusMutation) ClearUpdatedAt() {
	m.updated_at = nil
	m.clearedFields[creditdebtstatus.FieldUpdatedAt] = struct{}{}
}

// UpdatedAtCleared returns if the "updated_at" field was cleared in this mutation.
func (m *CreditDebtStatusMutation) UpdatedAtCleared() bool {
	_, ok := m.clearedFields[creditdebtstatus.FieldUpdatedAt]
	return ok
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *CreditDebtStatusMutation) ResetUpdatedAt() {
	m.updated_at = nil
	delete(m.clearedFields, creditdebtstatus.FieldUpdatedAt)
}

// SetDeletedAt sets the "deleted_at" field.
func (m *CreditDebtStatusMutation) SetDeletedAt(t time.Time) {
	m.deleted_at = &t
}

// DeletedAt returns the value of the "deleted_at" field in the mutation.
func (m *CreditDebtStatusMutation) DeletedAt() (r time.Time, exists bool) {
	v := m.deleted_at
	if v == nil {
		return
	}
	return *v, true
}

// OldDeletedAt returns the old "deleted_at" field's value of the CreditDebtStatus entity.
// If the CreditDebtStatus object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CreditDebtStatusMutation) OldDeletedAt(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeletedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeletedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeletedAt: %w", err)
	}
	return oldValue.DeletedAt, nil
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (m *CreditDebtStatusMutation) ClearDeletedAt() {
	m.deleted_at = nil
	m.clearedFields[creditdebtstatus.FieldDeletedAt] = struct{}{}
}

// DeletedAtCleared returns if the "deleted_at" field was cleared in this mutation.
func (m *CreditDebtStatusMutation) DeletedAtCleared() bool {
	_, ok := m.clearedFields[creditdebtstatus.FieldDeletedAt]
	return ok
}

// ResetDeletedAt resets all changes to the "deleted_at" field.
func (m *CreditDebtStatusMutation) ResetDeletedAt() {
	m.deleted_at = nil
	delete(m.clearedFields, creditdebtstatus.FieldDeletedAt)
}

// ClearCreditAgreement clears the "credit_agreement" edge to the CreditAgreement entity.
func (m *CreditDebtStatusMutation) ClearCreditAgreement() {
	m.clearedcredit_agreement = true
	m.clearedFields[creditdebtstatus.FieldCreditAgreementID] = struct{}{}
}

// CreditAgreementCleared reports if the "credit_agreement" edge to the CreditAgreement entity was cleared.
func (m *CreditDebtStatusMutation) CreditAgreementCleared() bool {
	return m.clearedcredit_agreement
}

// CreditAgreementIDs returns the "credit_agreement" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// CreditAgreementID instead. It exists only for internal usage by the builders.
func (m *CreditDebtStatusMutation) CreditAgreementIDs() (ids []int) {
	if id := m.credit_agreement; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetCreditAgreement resets all changes to the "credit_agreement" edge.
func (m *CreditDebtStatusMutation) ResetCreditAgreement() {
	m.credit_agreement = nil
	m.clearedcredit_agreement = false
}

// Where appends a list predicates to the CreditDebtStatusMutation builder.
func (m *CreditDebtStatusMutation) Where(ps ...predicate.CreditDebtStatus) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the CreditDebtStatusMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *CreditDebtStatusMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.CreditDebtStatus, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *CreditDebtStatusMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *CreditDebtStatusMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (CreditDebtStatus).
func (m *CreditDebtStatusMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *CreditDebtStatusMutation) Fields() []string {
	fields := make([]string, 0, 15)
	if m.credit_agreement != nil {
		fields = append(fields, creditdebtstatus.FieldCreditAgreementID)
	}
	if m.delay_days_count != nil {
		fields = append(fields, creditdebtstatus.FieldDelayDaysCount)
	}
	if m.main_debt_expired_days != nil {
		fields = append(fields, creditdebtstatus.FieldMainDebtExpiredDays)
	}
	if m.interest_expired_days != nil {
		fields = append(fields, creditdebtstatus.FieldInterestExpiredDays)
	}
	if m.restriction_fl != nil {
		fields = append(fields, creditdebtstatus.FieldRestrictionFl)
	}
	if m.main_debt != nil {
		fields = append(fields, creditdebtstatus.FieldMainDebt)
	}
	if m.amount_interest != nil {
		fields = append(fields, creditdebtstatus.FieldAmountInterest)
	}
	if m.overdue_debt != nil {
		fields = append(fields, creditdebtstatus.FieldOverdueDebt)
	}
	if m.overdue_interest != nil {
		fields = append(fields, creditdebtstatus.FieldOverdueInterest)
	}
	if m.fine != nil {
		fields = append(fields, creditdebtstatus.FieldFine)
	}
	if m.status_date != nil {
		fields = append(fields, creditdebtstatus.FieldStatusDate)
	}
	if m.is_archived != nil {
		fields = append(fields, creditdebtstatus.FieldIsArchived)
	}
	if m.created_at != nil {
		fields = append(fields, creditdebtstatus.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, creditdebtstatus.FieldUpdatedAt)
	}
	if m.deleted_at != nil {
		fields = append(fields, creditdebtstatus.FieldDeletedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *CreditDebtStatusMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case creditdebtstatus.FieldCreditAgreementID:
		return m.CreditAgreementID()
	case creditdebtstatus.FieldDelayDaysCount:
		return m.DelayDaysCount()
	case creditdebtstatus.FieldMainDebtExpiredDays:
		return m.MainDebtExpiredDays()
	case creditdebtstatus.FieldInterestExpiredDays:
		return m.InterestExpiredDays()
	case creditdebtstatus.FieldRestrictionFl:
		return m.RestrictionFl()
	case creditdebtstatus.FieldMainDebt:
		return m.MainDebt()
	case creditdebtstatus.FieldAmountInterest:
		return m.AmountInterest()
	case creditdebtstatus.FieldOverdueDebt:
		return m.OverdueDebt()
	case creditdebtstatus.FieldOverdueInterest:
		return m.OverdueInterest()
	case creditdebtstatus.FieldFine:
		return m.Fine()
	case creditdebtstatus.FieldStatusDate:
		return m.StatusDate()
	case creditdebtstatus.FieldIsArchived:
		return m.IsArchived()
	case creditdebtstatus.FieldCreatedAt:
		return m.CreatedAt()
	case creditdebtstatus.FieldUpdatedAt:
		return m.UpdatedAt()
	case creditdebtstatus.FieldDeletedAt:
		return m.DeletedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *CreditDebtStatusMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case creditdebtstatus.FieldCreditAgreementID:
		return m.OldCreditAgreementID(ctx)
	case creditdebtstatus.FieldDelayDaysCount:
		return m.OldDelayDaysCount(ctx)
	case creditdebtstatus.FieldMainDebtExpiredDays:
		return m.OldMainDebtExpiredDays(ctx)
	case creditdebtstatus.FieldInterestExpiredDays:
		return m.OldInterestExpiredDays(ctx)
	case creditdebtstatus.FieldRestrictionFl:
		return m.OldRestrictionFl(ctx)
	case creditdebtstatus.FieldMainDebt:
		return m.OldMainDebt(ctx)
	case creditdebtstatus.FieldAmountInterest:
		return m.OldAmountInterest(ctx)
	case creditdebtstatus.FieldOverdueDebt:
		return m.OldOverdueDebt(ctx)
	case creditdebtstatus.FieldOverdueInterest:
		return m.OldOverdueInterest(ctx)
	case creditdebtstatus.FieldFine:
		return m.OldFine(ctx)
	case creditdebtstatus.FieldStatusDate:
		return m.OldStatusDate(ctx)
	case creditdebtstatus.FieldIsArchived:
		return m.OldIsArchived(ctx)
	case creditdebtstatus.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case creditdebtstatus.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case creditdebtstatus.FieldDeletedAt:
		return m.OldDeletedAt(ctx)
	}
	return nil, fmt.Errorf("unknown CreditDebtStatus field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *CreditDebtStatusMutation) SetField(name string, value ent.Value) error {
	switch name {
	case creditdebtstatus.FieldCreditAgreementID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreditAgreementID(v)
		return nil
	case creditdebtstatus.FieldDelayDaysCount:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDelayDaysCount(v)
		return nil
	case creditdebtstatus.FieldMainDebtExpiredDays:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMainDebtExpiredDays(v)
		return nil
	case creditdebtstatus.FieldInterestExpiredDays:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetInterestExpiredDays(v)
		return nil
	case creditdebtstatus.FieldRestrictionFl:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRestrictionFl(v)
		return nil
	case creditdebtstatus.FieldMainDebt:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMainDebt(v)
		return nil
	case creditdebtstatus.FieldAmountInterest:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAmountInterest(v)
		return nil
	case creditdebtstatus.FieldOverdueDebt:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetOverdueDebt(v)
		return nil
	case creditdebtstatus.FieldOverdueInterest:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetOverdueInterest(v)
		return nil
	case creditdebtstatus.FieldFine:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFine(v)
		return nil
	case creditdebtstatus.FieldStatusDate:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatusDate(v)
		return nil
	case creditdebtstatus.FieldIsArchived:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsArchived(v)
		return nil
	case creditdebtstatus.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case creditdebtstatus.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case creditdebtstatus.FieldDeletedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeletedAt(v)
		return nil
	}
	return fmt.Errorf("unknown CreditDebtStatus field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *CreditDebtStatusMutation) AddedFields() []string {
	var fields []string
	if m.adddelay_days_count != nil {
		fields = append(fields, creditdebtstatus.FieldDelayDaysCount)
	}
	if m.addmain_debt_expired_days != nil {
		fields = append(fields, creditdebtstatus.FieldMainDebtExpiredDays)
	}
	if m.addinterest_expired_days != nil {
		fields = append(fields, creditdebtstatus.FieldInterestExpiredDays)
	}
	if m.addmain_debt != nil {
		fields = append(fields, creditdebtstatus.FieldMainDebt)
	}
	if m.addamount_interest != nil {
		fields = append(fields, creditdebtstatus.FieldAmountInterest)
	}
	if m.addoverdue_debt != nil {
		fields = append(fields, creditdebtstatus.FieldOverdueDebt)
	}
	if m.addoverdue_interest != nil {
		fields = append(fields, creditdebtstatus.FieldOverdueInterest)
	}
	if m.addfine != nil {
		fields = append(fields, creditdebtstatus.FieldFine)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *CreditDebtStatusMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case creditdebtstatus.FieldDelayDaysCount:
		return m.AddedDelayDaysCount()
	case creditdebtstatus.FieldMainDebtExpiredDays:
		return m.AddedMainDebtExpiredDays()
	case creditdebtstatus.FieldInterestExpiredDays:
		return m.AddedInterestExpiredDays()
	case creditdebtstatus.FieldMainDebt:
		return m.AddedMainDebt()
	case creditdebtstatus.FieldAmountInterest:
		return m.AddedAmountInterest()
	case creditdebtstatus.FieldOverdueDebt:
		return m.AddedOverdueDebt()
	case creditdebtstatus.FieldOverdueInterest:
		return m.AddedOverdueInterest()
	case creditdebtstatus.FieldFine:
		return m.AddedFine()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *CreditDebtStatusMutation) AddField(name string, value ent.Value) error {
	switch name {
	case creditdebtstatus.FieldDelayDaysCount:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddDelayDaysCount(v)
		return nil
	case creditdebtstatus.FieldMainDebtExpiredDays:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddMainDebtExpiredDays(v)
		return nil
	case creditdebtstatus.FieldInterestExpiredDays:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddInterestExpiredDays(v)
		return nil
	case creditdebtstatus.FieldMainDebt:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddMainDebt(v)
		return nil
	case creditdebtstatus.FieldAmountInterest:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddAmountInterest(v)
		return nil
	case creditdebtstatus.FieldOverdueDebt:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddOverdueDebt(v)
		return nil
	case creditdebtstatus.FieldOverdueInterest:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddOverdueInterest(v)
		return nil
	case creditdebtstatus.FieldFine:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddFine(v)
		return nil
	}
	return fmt.Errorf("unknown CreditDebtStatus numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *CreditDebtStatusMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(creditdebtstatus.FieldCreatedAt) {
		fields = append(fields, creditdebtstatus.FieldCreatedAt)
	}
	if m.FieldCleared(creditdebtstatus.FieldUpdatedAt) {
		fields = append(fields, creditdebtstatus.FieldUpdatedAt)
	}
	if m.FieldCleared(creditdebtstatus.FieldDeletedAt) {
		fields = append(fields, creditdebtstatus.FieldDeletedAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *CreditDebtStatusMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *CreditDebtStatusMutation) ClearField(name string) error {
	switch name {
	case creditdebtstatus.FieldCreatedAt:
		m.ClearCreatedAt()
		return nil
	case creditdebtstatus.FieldUpdatedAt:
		m.ClearUpdatedAt()
		return nil
	case creditdebtstatus.FieldDeletedAt:
		m.ClearDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown CreditDebtStatus nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *CreditDebtStatusMutation) ResetField(name string) error {
	switch name {
	case creditdebtstatus.FieldCreditAgreementID:
		m.ResetCreditAgreementID()
		return nil
	case creditdebtstatus.FieldDelayDaysCount:
		m.ResetDelayDaysCount()
		return nil
	case creditdebtstatus.FieldMainDebtExpiredDays:
		m.ResetMainDebtExpiredDays()
		return nil
	case creditdebtstatus.FieldInterestExpiredDays:
		m.ResetInterestExpiredDays()
		return nil
	case creditdebtstatus.FieldRestrictionFl:
		m.ResetRestrictionFl()
		return nil
	case creditdebtstatus.FieldMainDebt:
		m.ResetMainDebt()
		return nil
	case creditdebtstatus.FieldAmountInterest:
		m.ResetAmountInterest()
		return nil
	case creditdebtstatus.FieldOverdueDebt:
		m.ResetOverdueDebt()
		return nil
	case creditdebtstatus.FieldOverdueInterest:
		m.ResetOverdueInterest()
		return nil
	case creditdebtstatus.FieldFine:
		m.ResetFine()
		return nil
	case creditdebtstatus.FieldStatusDate:
		m.ResetStatusDate()
		return nil
	case creditdebtstatus.FieldIsArchived:
		m.ResetIsArchived()
		return nil
	case creditdebtstatus.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case creditdebtstatus.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case creditdebtstatus.FieldDeletedAt:
		m.ResetDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown CreditDebtStatus field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *CreditDebtStatusMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.credit_agreement != nil {
		edges = append(edges, creditdebtstatus.EdgeCreditAgreement)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *CreditDebtStatusMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case creditdebtstatus.EdgeCreditAgreement:
		if id := m.credit_agreement; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *CreditDebtStatusMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *CreditDebtStatusMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *CreditDebtStatusMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedcredit_agreement {
		edges = append(edges, creditdebtstatus.EdgeCreditAgreement)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *CreditDebtStatusMutation) EdgeCleared(name string) bool {
	switch name {
	case creditdebtstatus.EdgeCreditAgreement:
		return m.clearedcredit_agreement
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *CreditDebtStatusMutation) ClearEdge(name string) error {
	switch name {
	case creditdebtstatus.EdgeCreditAgreement:
		m.ClearCreditAgreement()
		return nil
	}
	return fmt.Errorf("unknown CreditDebtStatus unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *CreditDebtStatusMutation) ResetEdge(name string) error {
	switch name {
	case creditdebtstatus.EdgeCreditAgreement:
		m.ResetCreditAgreement()
		return nil
	}
	return fmt.Errorf("unknown CreditDebtStatus edge %s", name)
}

// HealthMutation represents an operation that mutates the Health nodes in the graph.
type HealthMutation struct {
	config
	op            Op
	typ           string
	id            *uuid.UUID
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Health, error)
	predicates    []predicate.Health
}

var _ ent.Mutation = (*HealthMutation)(nil)

// healthOption allows management of the mutation configuration using functional options.
type healthOption func(*HealthMutation)

// newHealthMutation creates new mutation for the Health entity.
func newHealthMutation(c config, op Op, opts ...healthOption) *HealthMutation {
	m := &HealthMutation{
		config:        c,
		op:            op,
		typ:           TypeHealth,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withHealthID sets the ID field of the mutation.
func withHealthID(id uuid.UUID) healthOption {
	return func(m *HealthMutation) {
		var (
			err   error
			once  sync.Once
			value *Health
		)
		m.oldValue = func(ctx context.Context) (*Health, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Health.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withHealth sets the old Health of the mutation.
func withHealth(node *Health) healthOption {
	return func(m *HealthMutation) {
		m.oldValue = func(context.Context) (*Health, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m HealthMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m HealthMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Health entities.
func (m *HealthMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *HealthMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *HealthMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Health.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// Where appends a list predicates to the HealthMutation builder.
func (m *HealthMutation) Where(ps ...predicate.Health) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the HealthMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *HealthMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Health, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *HealthMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *HealthMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Health).
func (m *HealthMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *HealthMutation) Fields() []string {
	fields := make([]string, 0, 0)
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *HealthMutation) Field(name string) (ent.Value, bool) {
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *HealthMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	return nil, fmt.Errorf("unknown Health field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *HealthMutation) SetField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Health field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *HealthMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *HealthMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *HealthMutation) AddField(name string, value ent.Value) error {
	return fmt.Errorf("unknown Health numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *HealthMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *HealthMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *HealthMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Health nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *HealthMutation) ResetField(name string) error {
	return fmt.Errorf("unknown Health field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *HealthMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *HealthMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *HealthMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *HealthMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *HealthMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *HealthMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *HealthMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Health unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *HealthMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Health edge %s", name)
}

// PaymentsMutation represents an operation that mutates the Payments nodes in the graph.
type PaymentsMutation struct {
	config
	op                       Op
	typ                      string
	id                       *int64
	credit_agreement_id      *int64
	addcredit_agreement_id   *int64
	payment_amount           *float64
	addpayment_amount        *float64
	payment_date             *time.Time
	created_at               *time.Time
	updated_at               *time.Time
	account_type             *string
	account_number           *string
	paym_main_debt           *float64
	addpaym_main_debt        *float64
	paym_amount_interest     *float64
	addpaym_amount_interest  *float64
	paym_overdue_debt        *float64
	addpaym_overdue_debt     *float64
	paym_overdue_interest    *float64
	addpaym_overdue_interest *float64
	deleted_at               *time.Time
	clearedFields            map[string]struct{}
	done                     bool
	oldValue                 func(context.Context) (*Payments, error)
	predicates               []predicate.Payments
}

var _ ent.Mutation = (*PaymentsMutation)(nil)

// paymentsOption allows management of the mutation configuration using functional options.
type paymentsOption func(*PaymentsMutation)

// newPaymentsMutation creates new mutation for the Payments entity.
func newPaymentsMutation(c config, op Op, opts ...paymentsOption) *PaymentsMutation {
	m := &PaymentsMutation{
		config:        c,
		op:            op,
		typ:           TypePayments,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withPaymentsID sets the ID field of the mutation.
func withPaymentsID(id int64) paymentsOption {
	return func(m *PaymentsMutation) {
		var (
			err   error
			once  sync.Once
			value *Payments
		)
		m.oldValue = func(ctx context.Context) (*Payments, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Payments.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withPayments sets the old Payments of the mutation.
func withPayments(node *Payments) paymentsOption {
	return func(m *PaymentsMutation) {
		m.oldValue = func(context.Context) (*Payments, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m PaymentsMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m PaymentsMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Payments entities.
func (m *PaymentsMutation) SetID(id int64) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *PaymentsMutation) ID() (id int64, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *PaymentsMutation) IDs(ctx context.Context) ([]int64, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int64{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Payments.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreditAgreementID sets the "credit_agreement_id" field.
func (m *PaymentsMutation) SetCreditAgreementID(i int64) {
	m.credit_agreement_id = &i
	m.addcredit_agreement_id = nil
}

// CreditAgreementID returns the value of the "credit_agreement_id" field in the mutation.
func (m *PaymentsMutation) CreditAgreementID() (r int64, exists bool) {
	v := m.credit_agreement_id
	if v == nil {
		return
	}
	return *v, true
}

// OldCreditAgreementID returns the old "credit_agreement_id" field's value of the Payments entity.
// If the Payments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PaymentsMutation) OldCreditAgreementID(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreditAgreementID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreditAgreementID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreditAgreementID: %w", err)
	}
	return oldValue.CreditAgreementID, nil
}

// AddCreditAgreementID adds i to the "credit_agreement_id" field.
func (m *PaymentsMutation) AddCreditAgreementID(i int64) {
	if m.addcredit_agreement_id != nil {
		*m.addcredit_agreement_id += i
	} else {
		m.addcredit_agreement_id = &i
	}
}

// AddedCreditAgreementID returns the value that was added to the "credit_agreement_id" field in this mutation.
func (m *PaymentsMutation) AddedCreditAgreementID() (r int64, exists bool) {
	v := m.addcredit_agreement_id
	if v == nil {
		return
	}
	return *v, true
}

// ResetCreditAgreementID resets all changes to the "credit_agreement_id" field.
func (m *PaymentsMutation) ResetCreditAgreementID() {
	m.credit_agreement_id = nil
	m.addcredit_agreement_id = nil
}

// SetPaymentAmount sets the "payment_amount" field.
func (m *PaymentsMutation) SetPaymentAmount(f float64) {
	m.payment_amount = &f
	m.addpayment_amount = nil
}

// PaymentAmount returns the value of the "payment_amount" field in the mutation.
func (m *PaymentsMutation) PaymentAmount() (r float64, exists bool) {
	v := m.payment_amount
	if v == nil {
		return
	}
	return *v, true
}

// OldPaymentAmount returns the old "payment_amount" field's value of the Payments entity.
// If the Payments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PaymentsMutation) OldPaymentAmount(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPaymentAmount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPaymentAmount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPaymentAmount: %w", err)
	}
	return oldValue.PaymentAmount, nil
}

// AddPaymentAmount adds f to the "payment_amount" field.
func (m *PaymentsMutation) AddPaymentAmount(f float64) {
	if m.addpayment_amount != nil {
		*m.addpayment_amount += f
	} else {
		m.addpayment_amount = &f
	}
}

// AddedPaymentAmount returns the value that was added to the "payment_amount" field in this mutation.
func (m *PaymentsMutation) AddedPaymentAmount() (r float64, exists bool) {
	v := m.addpayment_amount
	if v == nil {
		return
	}
	return *v, true
}

// ResetPaymentAmount resets all changes to the "payment_amount" field.
func (m *PaymentsMutation) ResetPaymentAmount() {
	m.payment_amount = nil
	m.addpayment_amount = nil
}

// SetPaymentDate sets the "payment_date" field.
func (m *PaymentsMutation) SetPaymentDate(t time.Time) {
	m.payment_date = &t
}

// PaymentDate returns the value of the "payment_date" field in the mutation.
func (m *PaymentsMutation) PaymentDate() (r time.Time, exists bool) {
	v := m.payment_date
	if v == nil {
		return
	}
	return *v, true
}

// OldPaymentDate returns the old "payment_date" field's value of the Payments entity.
// If the Payments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PaymentsMutation) OldPaymentDate(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPaymentDate is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPaymentDate requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPaymentDate: %w", err)
	}
	return oldValue.PaymentDate, nil
}

// ClearPaymentDate clears the value of the "payment_date" field.
func (m *PaymentsMutation) ClearPaymentDate() {
	m.payment_date = nil
	m.clearedFields[payments.FieldPaymentDate] = struct{}{}
}

// PaymentDateCleared returns if the "payment_date" field was cleared in this mutation.
func (m *PaymentsMutation) PaymentDateCleared() bool {
	_, ok := m.clearedFields[payments.FieldPaymentDate]
	return ok
}

// ResetPaymentDate resets all changes to the "payment_date" field.
func (m *PaymentsMutation) ResetPaymentDate() {
	m.payment_date = nil
	delete(m.clearedFields, payments.FieldPaymentDate)
}

// SetCreatedAt sets the "created_at" field.
func (m *PaymentsMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *PaymentsMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Payments entity.
// If the Payments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PaymentsMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *PaymentsMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *PaymentsMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *PaymentsMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Payments entity.
// If the Payments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PaymentsMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *PaymentsMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetAccountType sets the "account_type" field.
func (m *PaymentsMutation) SetAccountType(s string) {
	m.account_type = &s
}

// AccountType returns the value of the "account_type" field in the mutation.
func (m *PaymentsMutation) AccountType() (r string, exists bool) {
	v := m.account_type
	if v == nil {
		return
	}
	return *v, true
}

// OldAccountType returns the old "account_type" field's value of the Payments entity.
// If the Payments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PaymentsMutation) OldAccountType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAccountType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAccountType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAccountType: %w", err)
	}
	return oldValue.AccountType, nil
}

// ResetAccountType resets all changes to the "account_type" field.
func (m *PaymentsMutation) ResetAccountType() {
	m.account_type = nil
}

// SetAccountNumber sets the "account_number" field.
func (m *PaymentsMutation) SetAccountNumber(s string) {
	m.account_number = &s
}

// AccountNumber returns the value of the "account_number" field in the mutation.
func (m *PaymentsMutation) AccountNumber() (r string, exists bool) {
	v := m.account_number
	if v == nil {
		return
	}
	return *v, true
}

// OldAccountNumber returns the old "account_number" field's value of the Payments entity.
// If the Payments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PaymentsMutation) OldAccountNumber(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAccountNumber is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAccountNumber requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAccountNumber: %w", err)
	}
	return oldValue.AccountNumber, nil
}

// ClearAccountNumber clears the value of the "account_number" field.
func (m *PaymentsMutation) ClearAccountNumber() {
	m.account_number = nil
	m.clearedFields[payments.FieldAccountNumber] = struct{}{}
}

// AccountNumberCleared returns if the "account_number" field was cleared in this mutation.
func (m *PaymentsMutation) AccountNumberCleared() bool {
	_, ok := m.clearedFields[payments.FieldAccountNumber]
	return ok
}

// ResetAccountNumber resets all changes to the "account_number" field.
func (m *PaymentsMutation) ResetAccountNumber() {
	m.account_number = nil
	delete(m.clearedFields, payments.FieldAccountNumber)
}

// SetPaymMainDebt sets the "paym_main_debt" field.
func (m *PaymentsMutation) SetPaymMainDebt(f float64) {
	m.paym_main_debt = &f
	m.addpaym_main_debt = nil
}

// PaymMainDebt returns the value of the "paym_main_debt" field in the mutation.
func (m *PaymentsMutation) PaymMainDebt() (r float64, exists bool) {
	v := m.paym_main_debt
	if v == nil {
		return
	}
	return *v, true
}

// OldPaymMainDebt returns the old "paym_main_debt" field's value of the Payments entity.
// If the Payments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PaymentsMutation) OldPaymMainDebt(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPaymMainDebt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPaymMainDebt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPaymMainDebt: %w", err)
	}
	return oldValue.PaymMainDebt, nil
}

// AddPaymMainDebt adds f to the "paym_main_debt" field.
func (m *PaymentsMutation) AddPaymMainDebt(f float64) {
	if m.addpaym_main_debt != nil {
		*m.addpaym_main_debt += f
	} else {
		m.addpaym_main_debt = &f
	}
}

// AddedPaymMainDebt returns the value that was added to the "paym_main_debt" field in this mutation.
func (m *PaymentsMutation) AddedPaymMainDebt() (r float64, exists bool) {
	v := m.addpaym_main_debt
	if v == nil {
		return
	}
	return *v, true
}

// ResetPaymMainDebt resets all changes to the "paym_main_debt" field.
func (m *PaymentsMutation) ResetPaymMainDebt() {
	m.paym_main_debt = nil
	m.addpaym_main_debt = nil
}

// SetPaymAmountInterest sets the "paym_amount_interest" field.
func (m *PaymentsMutation) SetPaymAmountInterest(f float64) {
	m.paym_amount_interest = &f
	m.addpaym_amount_interest = nil
}

// PaymAmountInterest returns the value of the "paym_amount_interest" field in the mutation.
func (m *PaymentsMutation) PaymAmountInterest() (r float64, exists bool) {
	v := m.paym_amount_interest
	if v == nil {
		return
	}
	return *v, true
}

// OldPaymAmountInterest returns the old "paym_amount_interest" field's value of the Payments entity.
// If the Payments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PaymentsMutation) OldPaymAmountInterest(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPaymAmountInterest is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPaymAmountInterest requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPaymAmountInterest: %w", err)
	}
	return oldValue.PaymAmountInterest, nil
}

// AddPaymAmountInterest adds f to the "paym_amount_interest" field.
func (m *PaymentsMutation) AddPaymAmountInterest(f float64) {
	if m.addpaym_amount_interest != nil {
		*m.addpaym_amount_interest += f
	} else {
		m.addpaym_amount_interest = &f
	}
}

// AddedPaymAmountInterest returns the value that was added to the "paym_amount_interest" field in this mutation.
func (m *PaymentsMutation) AddedPaymAmountInterest() (r float64, exists bool) {
	v := m.addpaym_amount_interest
	if v == nil {
		return
	}
	return *v, true
}

// ResetPaymAmountInterest resets all changes to the "paym_amount_interest" field.
func (m *PaymentsMutation) ResetPaymAmountInterest() {
	m.paym_amount_interest = nil
	m.addpaym_amount_interest = nil
}

// SetPaymOverdueDebt sets the "paym_overdue_debt" field.
func (m *PaymentsMutation) SetPaymOverdueDebt(f float64) {
	m.paym_overdue_debt = &f
	m.addpaym_overdue_debt = nil
}

// PaymOverdueDebt returns the value of the "paym_overdue_debt" field in the mutation.
func (m *PaymentsMutation) PaymOverdueDebt() (r float64, exists bool) {
	v := m.paym_overdue_debt
	if v == nil {
		return
	}
	return *v, true
}

// OldPaymOverdueDebt returns the old "paym_overdue_debt" field's value of the Payments entity.
// If the Payments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PaymentsMutation) OldPaymOverdueDebt(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPaymOverdueDebt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPaymOverdueDebt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPaymOverdueDebt: %w", err)
	}
	return oldValue.PaymOverdueDebt, nil
}

// AddPaymOverdueDebt adds f to the "paym_overdue_debt" field.
func (m *PaymentsMutation) AddPaymOverdueDebt(f float64) {
	if m.addpaym_overdue_debt != nil {
		*m.addpaym_overdue_debt += f
	} else {
		m.addpaym_overdue_debt = &f
	}
}

// AddedPaymOverdueDebt returns the value that was added to the "paym_overdue_debt" field in this mutation.
func (m *PaymentsMutation) AddedPaymOverdueDebt() (r float64, exists bool) {
	v := m.addpaym_overdue_debt
	if v == nil {
		return
	}
	return *v, true
}

// ResetPaymOverdueDebt resets all changes to the "paym_overdue_debt" field.
func (m *PaymentsMutation) ResetPaymOverdueDebt() {
	m.paym_overdue_debt = nil
	m.addpaym_overdue_debt = nil
}

// SetPaymOverdueInterest sets the "paym_overdue_interest" field.
func (m *PaymentsMutation) SetPaymOverdueInterest(f float64) {
	m.paym_overdue_interest = &f
	m.addpaym_overdue_interest = nil
}

// PaymOverdueInterest returns the value of the "paym_overdue_interest" field in the mutation.
func (m *PaymentsMutation) PaymOverdueInterest() (r float64, exists bool) {
	v := m.paym_overdue_interest
	if v == nil {
		return
	}
	return *v, true
}

// OldPaymOverdueInterest returns the old "paym_overdue_interest" field's value of the Payments entity.
// If the Payments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PaymentsMutation) OldPaymOverdueInterest(ctx context.Context) (v float64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPaymOverdueInterest is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPaymOverdueInterest requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPaymOverdueInterest: %w", err)
	}
	return oldValue.PaymOverdueInterest, nil
}

// AddPaymOverdueInterest adds f to the "paym_overdue_interest" field.
func (m *PaymentsMutation) AddPaymOverdueInterest(f float64) {
	if m.addpaym_overdue_interest != nil {
		*m.addpaym_overdue_interest += f
	} else {
		m.addpaym_overdue_interest = &f
	}
}

// AddedPaymOverdueInterest returns the value that was added to the "paym_overdue_interest" field in this mutation.
func (m *PaymentsMutation) AddedPaymOverdueInterest() (r float64, exists bool) {
	v := m.addpaym_overdue_interest
	if v == nil {
		return
	}
	return *v, true
}

// ResetPaymOverdueInterest resets all changes to the "paym_overdue_interest" field.
func (m *PaymentsMutation) ResetPaymOverdueInterest() {
	m.paym_overdue_interest = nil
	m.addpaym_overdue_interest = nil
}

// SetDeletedAt sets the "deleted_at" field.
func (m *PaymentsMutation) SetDeletedAt(t time.Time) {
	m.deleted_at = &t
}

// DeletedAt returns the value of the "deleted_at" field in the mutation.
func (m *PaymentsMutation) DeletedAt() (r time.Time, exists bool) {
	v := m.deleted_at
	if v == nil {
		return
	}
	return *v, true
}

// OldDeletedAt returns the old "deleted_at" field's value of the Payments entity.
// If the Payments object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PaymentsMutation) OldDeletedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDeletedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDeletedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDeletedAt: %w", err)
	}
	return oldValue.DeletedAt, nil
}

// ResetDeletedAt resets all changes to the "deleted_at" field.
func (m *PaymentsMutation) ResetDeletedAt() {
	m.deleted_at = nil
}

// Where appends a list predicates to the PaymentsMutation builder.
func (m *PaymentsMutation) Where(ps ...predicate.Payments) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the PaymentsMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *PaymentsMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Payments, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *PaymentsMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *PaymentsMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Payments).
func (m *PaymentsMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *PaymentsMutation) Fields() []string {
	fields := make([]string, 0, 12)
	if m.credit_agreement_id != nil {
		fields = append(fields, payments.FieldCreditAgreementID)
	}
	if m.payment_amount != nil {
		fields = append(fields, payments.FieldPaymentAmount)
	}
	if m.payment_date != nil {
		fields = append(fields, payments.FieldPaymentDate)
	}
	if m.created_at != nil {
		fields = append(fields, payments.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, payments.FieldUpdatedAt)
	}
	if m.account_type != nil {
		fields = append(fields, payments.FieldAccountType)
	}
	if m.account_number != nil {
		fields = append(fields, payments.FieldAccountNumber)
	}
	if m.paym_main_debt != nil {
		fields = append(fields, payments.FieldPaymMainDebt)
	}
	if m.paym_amount_interest != nil {
		fields = append(fields, payments.FieldPaymAmountInterest)
	}
	if m.paym_overdue_debt != nil {
		fields = append(fields, payments.FieldPaymOverdueDebt)
	}
	if m.paym_overdue_interest != nil {
		fields = append(fields, payments.FieldPaymOverdueInterest)
	}
	if m.deleted_at != nil {
		fields = append(fields, payments.FieldDeletedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *PaymentsMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case payments.FieldCreditAgreementID:
		return m.CreditAgreementID()
	case payments.FieldPaymentAmount:
		return m.PaymentAmount()
	case payments.FieldPaymentDate:
		return m.PaymentDate()
	case payments.FieldCreatedAt:
		return m.CreatedAt()
	case payments.FieldUpdatedAt:
		return m.UpdatedAt()
	case payments.FieldAccountType:
		return m.AccountType()
	case payments.FieldAccountNumber:
		return m.AccountNumber()
	case payments.FieldPaymMainDebt:
		return m.PaymMainDebt()
	case payments.FieldPaymAmountInterest:
		return m.PaymAmountInterest()
	case payments.FieldPaymOverdueDebt:
		return m.PaymOverdueDebt()
	case payments.FieldPaymOverdueInterest:
		return m.PaymOverdueInterest()
	case payments.FieldDeletedAt:
		return m.DeletedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *PaymentsMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case payments.FieldCreditAgreementID:
		return m.OldCreditAgreementID(ctx)
	case payments.FieldPaymentAmount:
		return m.OldPaymentAmount(ctx)
	case payments.FieldPaymentDate:
		return m.OldPaymentDate(ctx)
	case payments.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case payments.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case payments.FieldAccountType:
		return m.OldAccountType(ctx)
	case payments.FieldAccountNumber:
		return m.OldAccountNumber(ctx)
	case payments.FieldPaymMainDebt:
		return m.OldPaymMainDebt(ctx)
	case payments.FieldPaymAmountInterest:
		return m.OldPaymAmountInterest(ctx)
	case payments.FieldPaymOverdueDebt:
		return m.OldPaymOverdueDebt(ctx)
	case payments.FieldPaymOverdueInterest:
		return m.OldPaymOverdueInterest(ctx)
	case payments.FieldDeletedAt:
		return m.OldDeletedAt(ctx)
	}
	return nil, fmt.Errorf("unknown Payments field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *PaymentsMutation) SetField(name string, value ent.Value) error {
	switch name {
	case payments.FieldCreditAgreementID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreditAgreementID(v)
		return nil
	case payments.FieldPaymentAmount:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPaymentAmount(v)
		return nil
	case payments.FieldPaymentDate:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPaymentDate(v)
		return nil
	case payments.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case payments.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case payments.FieldAccountType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAccountType(v)
		return nil
	case payments.FieldAccountNumber:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAccountNumber(v)
		return nil
	case payments.FieldPaymMainDebt:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPaymMainDebt(v)
		return nil
	case payments.FieldPaymAmountInterest:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPaymAmountInterest(v)
		return nil
	case payments.FieldPaymOverdueDebt:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPaymOverdueDebt(v)
		return nil
	case payments.FieldPaymOverdueInterest:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPaymOverdueInterest(v)
		return nil
	case payments.FieldDeletedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDeletedAt(v)
		return nil
	}
	return fmt.Errorf("unknown Payments field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *PaymentsMutation) AddedFields() []string {
	var fields []string
	if m.addcredit_agreement_id != nil {
		fields = append(fields, payments.FieldCreditAgreementID)
	}
	if m.addpayment_amount != nil {
		fields = append(fields, payments.FieldPaymentAmount)
	}
	if m.addpaym_main_debt != nil {
		fields = append(fields, payments.FieldPaymMainDebt)
	}
	if m.addpaym_amount_interest != nil {
		fields = append(fields, payments.FieldPaymAmountInterest)
	}
	if m.addpaym_overdue_debt != nil {
		fields = append(fields, payments.FieldPaymOverdueDebt)
	}
	if m.addpaym_overdue_interest != nil {
		fields = append(fields, payments.FieldPaymOverdueInterest)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *PaymentsMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case payments.FieldCreditAgreementID:
		return m.AddedCreditAgreementID()
	case payments.FieldPaymentAmount:
		return m.AddedPaymentAmount()
	case payments.FieldPaymMainDebt:
		return m.AddedPaymMainDebt()
	case payments.FieldPaymAmountInterest:
		return m.AddedPaymAmountInterest()
	case payments.FieldPaymOverdueDebt:
		return m.AddedPaymOverdueDebt()
	case payments.FieldPaymOverdueInterest:
		return m.AddedPaymOverdueInterest()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *PaymentsMutation) AddField(name string, value ent.Value) error {
	switch name {
	case payments.FieldCreditAgreementID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddCreditAgreementID(v)
		return nil
	case payments.FieldPaymentAmount:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddPaymentAmount(v)
		return nil
	case payments.FieldPaymMainDebt:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddPaymMainDebt(v)
		return nil
	case payments.FieldPaymAmountInterest:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddPaymAmountInterest(v)
		return nil
	case payments.FieldPaymOverdueDebt:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddPaymOverdueDebt(v)
		return nil
	case payments.FieldPaymOverdueInterest:
		v, ok := value.(float64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddPaymOverdueInterest(v)
		return nil
	}
	return fmt.Errorf("unknown Payments numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *PaymentsMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(payments.FieldPaymentDate) {
		fields = append(fields, payments.FieldPaymentDate)
	}
	if m.FieldCleared(payments.FieldAccountNumber) {
		fields = append(fields, payments.FieldAccountNumber)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *PaymentsMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *PaymentsMutation) ClearField(name string) error {
	switch name {
	case payments.FieldPaymentDate:
		m.ClearPaymentDate()
		return nil
	case payments.FieldAccountNumber:
		m.ClearAccountNumber()
		return nil
	}
	return fmt.Errorf("unknown Payments nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *PaymentsMutation) ResetField(name string) error {
	switch name {
	case payments.FieldCreditAgreementID:
		m.ResetCreditAgreementID()
		return nil
	case payments.FieldPaymentAmount:
		m.ResetPaymentAmount()
		return nil
	case payments.FieldPaymentDate:
		m.ResetPaymentDate()
		return nil
	case payments.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case payments.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case payments.FieldAccountType:
		m.ResetAccountType()
		return nil
	case payments.FieldAccountNumber:
		m.ResetAccountNumber()
		return nil
	case payments.FieldPaymMainDebt:
		m.ResetPaymMainDebt()
		return nil
	case payments.FieldPaymAmountInterest:
		m.ResetPaymAmountInterest()
		return nil
	case payments.FieldPaymOverdueDebt:
		m.ResetPaymOverdueDebt()
		return nil
	case payments.FieldPaymOverdueInterest:
		m.ResetPaymOverdueInterest()
		return nil
	case payments.FieldDeletedAt:
		m.ResetDeletedAt()
		return nil
	}
	return fmt.Errorf("unknown Payments field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *PaymentsMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *PaymentsMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *PaymentsMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *PaymentsMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *PaymentsMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *PaymentsMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *PaymentsMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Payments unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *PaymentsMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Payments edge %s", name)
}
