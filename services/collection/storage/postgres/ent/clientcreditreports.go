// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"

	"git.redmadrobot.com/zaman/backend/zaman/services/collection/storage/postgres/ent/clientcreditreports"
)

// ClientCreditReports is the model entity for the ClientCreditReports schema.
type ClientCreditReports struct {
	config `json:"-"`
	// ID of the ent.
	ID int64 `json:"id,omitempty"`
	// IdentityDocumentNumber holds the value of the "identity_document_number" field.
	IdentityDocumentNumber string `json:"identity_document_number,omitempty"`
	// ReportDate holds the value of the "report_date" field.
	ReportDate time.Time `json:"report_date,omitempty"`
	// DocumentType holds the value of the "document_type" field.
	DocumentType string `json:"document_type,omitempty"`
	// FileLink holds the value of the "file_link" field.
	FileLink string `json:"file_link,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt    *time.Time `json:"deleted_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*ClientCreditReports) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case clientcreditreports.FieldID:
			values[i] = new(sql.NullInt64)
		case clientcreditreports.FieldIdentityDocumentNumber, clientcreditreports.FieldDocumentType, clientcreditreports.FieldFileLink:
			values[i] = new(sql.NullString)
		case clientcreditreports.FieldReportDate, clientcreditreports.FieldCreatedAt, clientcreditreports.FieldUpdatedAt, clientcreditreports.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the ClientCreditReports fields.
func (_m *ClientCreditReports) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case clientcreditreports.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			_m.ID = int64(value.Int64)
		case clientcreditreports.FieldIdentityDocumentNumber:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field identity_document_number", values[i])
			} else if value.Valid {
				_m.IdentityDocumentNumber = value.String
			}
		case clientcreditreports.FieldReportDate:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field report_date", values[i])
			} else if value.Valid {
				_m.ReportDate = value.Time
			}
		case clientcreditreports.FieldDocumentType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field document_type", values[i])
			} else if value.Valid {
				_m.DocumentType = value.String
			}
		case clientcreditreports.FieldFileLink:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field file_link", values[i])
			} else if value.Valid {
				_m.FileLink = value.String
			}
		case clientcreditreports.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				_m.CreatedAt = value.Time
			}
		case clientcreditreports.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				_m.UpdatedAt = value.Time
			}
		case clientcreditreports.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				_m.DeletedAt = new(time.Time)
				*_m.DeletedAt = value.Time
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the ClientCreditReports.
// This includes values selected through modifiers, order, etc.
func (_m *ClientCreditReports) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// Update returns a builder for updating this ClientCreditReports.
// Note that you need to call ClientCreditReports.Unwrap() before calling this method if this ClientCreditReports
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *ClientCreditReports) Update() *ClientCreditReportsUpdateOne {
	return NewClientCreditReportsClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the ClientCreditReports entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *ClientCreditReports) Unwrap() *ClientCreditReports {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("ent: ClientCreditReports is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *ClientCreditReports) String() string {
	var builder strings.Builder
	builder.WriteString("ClientCreditReports(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("identity_document_number=")
	builder.WriteString(_m.IdentityDocumentNumber)
	builder.WriteString(", ")
	builder.WriteString("report_date=")
	builder.WriteString(_m.ReportDate.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("document_type=")
	builder.WriteString(_m.DocumentType)
	builder.WriteString(", ")
	builder.WriteString("file_link=")
	builder.WriteString(_m.FileLink)
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(_m.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(_m.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := _m.DeletedAt; v != nil {
		builder.WriteString("deleted_at=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteByte(')')
	return builder.String()
}

// ClientCreditReportsSlice is a parsable slice of ClientCreditReports.
type ClientCreditReportsSlice []*ClientCreditReports
