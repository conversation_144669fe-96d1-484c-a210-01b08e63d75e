package storage

import (
	"context"
	"database/sql"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx/transaction"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositapplication"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/document"

	pg "git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres"
)

type CloseDeposit interface {
	CloseDepositTx(ctx context.Context) (CloseDepositTx, error)
}

var _ CloseDeposit = (*storageImpl)(nil)

type CloseDepositTx interface {
	Close(ctx context.Context, err error) error
	DataForCloseDepositByDocID(ctx context.Context, documentID uuid.UUID) (*entity.DataForCloseDeposit, error)
	SignDocumentAndCloseApplication(ctx context.Context, signID uuid.UUID, closeTime time.Time) error
}

type closeDepositTxImpl struct {
	tx          *transaction.Tx[ent.Client]
	document    *ent.Document
	application *ent.DepositApplication
}

func (s *storageImpl) CloseDepositTx(ctx context.Context) (CloseDepositTx, error) {
	errTmpl := "storage:CloseDepositTx:%s"

	tx, err := pg.MakeTxFn(s.PostgresClient)(ctx, &sql.TxOptions{Isolation: sql.LevelRepeatableRead})
	if err != nil {
		return nil, errs.Wrapf(err, errTmpl, "tx")
	}

	return &closeDepositTxImpl{
		tx: tx,
	}, nil
}

func (c *closeDepositTxImpl) Close(ctx context.Context, err error) error {
	if err != nil {
		return c.tx.Cancel(ctx, err)
	}

	c.tx.Done(ctx)
	return nil
}

func (c *closeDepositTxImpl) DataForCloseDepositByDocID(ctx context.Context, documentID uuid.UUID) (*entity.DataForCloseDeposit, error) {
	errTmpl := "storage:FindDocumentByDocID:%s"

	doc, err := c.tx.Client.Document.Query().Where(document.DocIDEQ(documentID)).WithApplication().First(ctx)
	if err != nil {
		return nil, errs.Wrapf(err, errTmpl, "Document.Query")
	}

	c.document = doc
	c.application = doc.Edges.Application

	data, err := entity.MakeDataForCloseDeposit(doc)
	if err != nil {
		return nil, err
	}

	return data, nil
}

func (c *closeDepositTxImpl) SignDocumentAndCloseApplication(ctx context.Context, signID uuid.UUID, closeTime time.Time) error {
	errTmpl := "storage:SignDocumentAndCloseApplication:%s"

	if c.application == nil || c.document == nil {
		return errs.Wrapf(entity.ErrNoApplication, errTmpl, "application,document")
	}

	err := c.application.Update().
		SetStatus(depositapplication.StatusCLOSED).
		SetClosedAt(closeTime).
		Exec(ctx)
	if err != nil {
		return errs.Wrapf(entity.ErrNoApplication, errTmpl, "application.Update")
	}

	err = c.document.Update().
		SetSignedDocID(signID).
		SetDocumentSigningDate(closeTime).
		Exec(ctx)
	if err != nil {
		return errs.Wrapf(entity.ErrNoApplication, errTmpl, "document.Update")
	}

	return err
}
