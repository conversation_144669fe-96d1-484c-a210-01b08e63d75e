// Code generated by gowrap. DO NOT EDIT.
// template: ../../../etc/templates/hook.tmpl
// gowrap: http://github.com/hexdigest/gowrap

package storage

//go:generate gowrap gen -p git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage -i Storage -t ../../../etc/templates/hook.tmpl -o storage_hook.gen.go -l ""

import (
	"context"
	"time"

	_proxyLib "git.redmadrobot.com/backend-go/rmr-pkg/proxy"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/entity"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent"
)

var _ Storage = (*StorageHook)(nil)

// StorageHook implements Storage interface wrapper
type StorageHook struct {
	Storage
	_beforeCall _proxyLib.Hook
	_postCall   _proxyLib.Hook
	_onPanic    _proxyLib.PanicHook
}

// Check implements Storage
func (_w *StorageHook) Check(ctx context.Context) (err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.Storage, "Check", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "Check", _params)

	err = _w.Storage.Check(_ctx)
	_w._postCall.Hook(_ctx, _w.Storage, "Check", []any{err})
	return err
}

// CloseDepositTx implements Storage
func (_w *StorageHook) CloseDepositTx(ctx context.Context) (c2 CloseDepositTx, err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.Storage, "CloseDepositTx", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "CloseDepositTx", _params)

	c2, err = _w.Storage.CloseDepositTx(_ctx)
	_w._postCall.Hook(_ctx, _w.Storage, "CloseDepositTx", []any{c2, err})
	return c2, err
}

// CreateDepositOfferDocument implements Storage
func (_w *StorageHook) CreateDepositOfferDocument(ctx context.Context, time time.Time, rateID uuid.UUID, validateAndGetDocumentsFn ValidateAndGetDocumentsFn) (dpa1 []*ent.Document, err error) {
	_params := []any{ctx, time, rateID, validateAndGetDocumentsFn}
	defer _w._onPanic.Hook(_w.Storage, "CreateDepositOfferDocument", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "CreateDepositOfferDocument", _params)

	dpa1, err = _w.Storage.CreateDepositOfferDocument(_ctx, time, rateID, validateAndGetDocumentsFn)
	_w._postCall.Hook(_ctx, _w.Storage, "CreateDepositOfferDocument", []any{dpa1, err})
	return dpa1, err
}

// CreateOpenDepositTx implements Storage
func (_w *StorageHook) CreateOpenDepositTx(ctx context.Context) (o1 OpenDepositTx, err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.Storage, "CreateOpenDepositTx", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "CreateOpenDepositTx", _params)

	o1, err = _w.Storage.CreateOpenDepositTx(_ctx)
	_w._postCall.Hook(_ctx, _w.Storage, "CreateOpenDepositTx", []any{o1, err})
	return o1, err
}

// GetDepositApplicationByColvirRefID implements Storage
func (_w *StorageHook) GetDepositApplicationByColvirRefID(ctx context.Context, depositID string) (dp1 *ent.DepositApplication, err error) {
	_params := []any{ctx, depositID}
	defer _w._onPanic.Hook(_w.Storage, "GetDepositApplicationByColvirRefID", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetDepositApplicationByColvirRefID", _params)

	dp1, err = _w.Storage.GetDepositApplicationByColvirRefID(_ctx, depositID)
	_w._postCall.Hook(_ctx, _w.Storage, "GetDepositApplicationByColvirRefID", []any{dp1, err})
	return dp1, err
}

// GetDepositConditions implements Storage
func (_w *StorageHook) GetDepositConditions(ctx context.Context, request entity.DepositConditionsStorageInput) (ppa1 []*ent.ProfitRate, err error) {
	_params := []any{ctx, request}
	defer _w._onPanic.Hook(_w.Storage, "GetDepositConditions", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetDepositConditions", _params)

	ppa1, err = _w.Storage.GetDepositConditions(_ctx, request)
	_w._postCall.Hook(_ctx, _w.Storage, "GetDepositConditions", []any{ppa1, err})
	return ppa1, err
}

// GetProductByCode implements Storage
func (_w *StorageHook) GetProductByCode(ctx context.Context, productCode string) (dp1 *ent.DepositProduct, err error) {
	_params := []any{ctx, productCode}
	defer _w._onPanic.Hook(_w.Storage, "GetProductByCode", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "GetProductByCode", _params)

	dp1, err = _w.Storage.GetProductByCode(_ctx, productCode)
	_w._postCall.Hook(_ctx, _w.Storage, "GetProductByCode", []any{dp1, err})
	return dp1, err
}

// NextHoldingSequenceValue implements Storage
func (_w *StorageHook) NextHoldingSequenceValue(ctx context.Context) (u1 uint64, err error) {
	_params := []any{ctx}
	defer _w._onPanic.Hook(_w.Storage, "NextHoldingSequenceValue", _params)

	_ctx := _proxyLib.ExtractContext(_params)
	_ctx = _w._beforeCall.Hook(_ctx, _w.Storage, "NextHoldingSequenceValue", _params)

	u1, err = _w.Storage.NextHoldingSequenceValue(_ctx)
	_w._postCall.Hook(_ctx, _w.Storage, "NextHoldingSequenceValue", []any{u1, err})
	return u1, err
}

// NewStorageHook returns StorageHook
func NewStorageHook(object Storage, beforeCall _proxyLib.Hook, postCall _proxyLib.Hook, onPanic _proxyLib.PanicHook) *StorageHook {
	return &StorageHook{
		Storage:     object,
		_beforeCall: beforeCall,
		_postCall:   postCall,
		_onPanic:    onPanic,
	}
}
