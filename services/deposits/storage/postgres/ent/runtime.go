// Code generated by ent, DO NOT EDIT.

package ent

import (
	"time"

	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositapplication"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositcards"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/document"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/health"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/profitrate"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/schema"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	depositapplicationMixin := schema.DepositApplication{}.Mixin()
	depositapplicationMixinFields0 := depositapplicationMixin[0].Fields()
	_ = depositapplicationMixinFields0
	depositapplicationFields := schema.DepositApplication{}.Fields()
	_ = depositapplicationFields
	// depositapplicationDescCreateTime is the schema descriptor for create_time field.
	depositapplicationDescCreateTime := depositapplicationMixinFields0[0].Descriptor()
	// depositapplication.DefaultCreateTime holds the default value on creation for the create_time field.
	depositapplication.DefaultCreateTime = depositapplicationDescCreateTime.Default.(func() time.Time)
	// depositapplicationDescUpdateTime is the schema descriptor for update_time field.
	depositapplicationDescUpdateTime := depositapplicationMixinFields0[1].Descriptor()
	// depositapplication.DefaultUpdateTime holds the default value on creation for the update_time field.
	depositapplication.DefaultUpdateTime = depositapplicationDescUpdateTime.Default.(func() time.Time)
	// depositapplication.UpdateDefaultUpdateTime holds the default value on update for the update_time field.
	depositapplication.UpdateDefaultUpdateTime = depositapplicationDescUpdateTime.UpdateDefault.(func() time.Time)
	// depositapplicationDescID is the schema descriptor for id field.
	depositapplicationDescID := depositapplicationFields[0].Descriptor()
	// depositapplication.DefaultID holds the default value on creation for the id field.
	depositapplication.DefaultID = depositapplicationDescID.Default.(func() uuid.UUID)
	depositcardsMixin := schema.DepositCards{}.Mixin()
	depositcardsMixinFields0 := depositcardsMixin[0].Fields()
	_ = depositcardsMixinFields0
	depositcardsFields := schema.DepositCards{}.Fields()
	_ = depositcardsFields
	// depositcardsDescCreateTime is the schema descriptor for create_time field.
	depositcardsDescCreateTime := depositcardsMixinFields0[0].Descriptor()
	// depositcards.DefaultCreateTime holds the default value on creation for the create_time field.
	depositcards.DefaultCreateTime = depositcardsDescCreateTime.Default.(func() time.Time)
	// depositcardsDescUpdateTime is the schema descriptor for update_time field.
	depositcardsDescUpdateTime := depositcardsMixinFields0[1].Descriptor()
	// depositcards.DefaultUpdateTime holds the default value on creation for the update_time field.
	depositcards.DefaultUpdateTime = depositcardsDescUpdateTime.Default.(func() time.Time)
	// depositcards.UpdateDefaultUpdateTime holds the default value on update for the update_time field.
	depositcards.UpdateDefaultUpdateTime = depositcardsDescUpdateTime.UpdateDefault.(func() time.Time)
	// depositcardsDescID is the schema descriptor for id field.
	depositcardsDescID := depositcardsFields[0].Descriptor()
	// depositcards.DefaultID holds the default value on creation for the id field.
	depositcards.DefaultID = depositcardsDescID.Default.(func() uuid.UUID)
	documentMixin := schema.Document{}.Mixin()
	documentMixinFields0 := documentMixin[0].Fields()
	_ = documentMixinFields0
	documentFields := schema.Document{}.Fields()
	_ = documentFields
	// documentDescCreateTime is the schema descriptor for create_time field.
	documentDescCreateTime := documentMixinFields0[0].Descriptor()
	// document.DefaultCreateTime holds the default value on creation for the create_time field.
	document.DefaultCreateTime = documentDescCreateTime.Default.(func() time.Time)
	// documentDescUpdateTime is the schema descriptor for update_time field.
	documentDescUpdateTime := documentMixinFields0[1].Descriptor()
	// document.DefaultUpdateTime holds the default value on creation for the update_time field.
	document.DefaultUpdateTime = documentDescUpdateTime.Default.(func() time.Time)
	// document.UpdateDefaultUpdateTime holds the default value on update for the update_time field.
	document.UpdateDefaultUpdateTime = documentDescUpdateTime.UpdateDefault.(func() time.Time)
	// documentDescIsSignable is the schema descriptor for is_signable field.
	documentDescIsSignable := documentFields[7].Descriptor()
	// document.DefaultIsSignable holds the default value on creation for the is_signable field.
	document.DefaultIsSignable = documentDescIsSignable.Default.(bool)
	// documentDescID is the schema descriptor for id field.
	documentDescID := documentFields[0].Descriptor()
	// document.DefaultID holds the default value on creation for the id field.
	document.DefaultID = documentDescID.Default.(func() uuid.UUID)
	healthFields := schema.Health{}.Fields()
	_ = healthFields
	// healthDescID is the schema descriptor for id field.
	healthDescID := healthFields[0].Descriptor()
	// health.DefaultID holds the default value on creation for the id field.
	health.DefaultID = healthDescID.Default.(func() uuid.UUID)
	profitrateMixin := schema.ProfitRate{}.Mixin()
	profitrateMixinFields0 := profitrateMixin[0].Fields()
	_ = profitrateMixinFields0
	profitrateFields := schema.ProfitRate{}.Fields()
	_ = profitrateFields
	// profitrateDescCreateTime is the schema descriptor for create_time field.
	profitrateDescCreateTime := profitrateMixinFields0[0].Descriptor()
	// profitrate.DefaultCreateTime holds the default value on creation for the create_time field.
	profitrate.DefaultCreateTime = profitrateDescCreateTime.Default.(func() time.Time)
	// profitrateDescUpdateTime is the schema descriptor for update_time field.
	profitrateDescUpdateTime := profitrateMixinFields0[1].Descriptor()
	// profitrate.DefaultUpdateTime holds the default value on creation for the update_time field.
	profitrate.DefaultUpdateTime = profitrateDescUpdateTime.Default.(func() time.Time)
	// profitrate.UpdateDefaultUpdateTime holds the default value on update for the update_time field.
	profitrate.UpdateDefaultUpdateTime = profitrateDescUpdateTime.UpdateDefault.(func() time.Time)
	// profitrateDescID is the schema descriptor for id field.
	profitrateDescID := profitrateFields[0].Descriptor()
	// profitrate.DefaultID holds the default value on creation for the id field.
	profitrate.DefaultID = profitrateDescID.Default.(func() uuid.UUID)
}
