// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/currency"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositapplication"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositcards"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositproduct"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/document"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/predicate"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/profitrate"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeCurrency           = "Currency"
	TypeDepositApplication = "DepositApplication"
	TypeDepositCards       = "DepositCards"
	TypeDepositProduct     = "DepositProduct"
	TypeDocument           = "Document"
	TypeHealth             = "Health"
	TypeProfitRate         = "ProfitRate"
)

// CurrencyMutation represents an operation that mutates the Currency nodes in the graph.
type CurrencyMutation struct {
	config
	op                 Op
	typ                string
	id                 *uint
	currency           *currency.Currency
	is_active          *bool
	min_amount         *decimal.Decimal
	max_amount         *decimal.Decimal
	clearedFields      map[string]struct{}
	profit_rate        map[uuid.UUID]struct{}
	removedprofit_rate map[uuid.UUID]struct{}
	clearedprofit_rate bool
	product            *uint
	clearedproduct     bool
	done               bool
	oldValue           func(context.Context) (*Currency, error)
	predicates         []predicate.Currency
}

var _ ent.Mutation = (*CurrencyMutation)(nil)

// currencyOption allows management of the mutation configuration using functional options.
type currencyOption func(*CurrencyMutation)

// newCurrencyMutation creates new mutation for the Currency entity.
func newCurrencyMutation(c config, op Op, opts ...currencyOption) *CurrencyMutation {
	m := &CurrencyMutation{
		config:        c,
		op:            op,
		typ:           TypeCurrency,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withCurrencyID sets the ID field of the mutation.
func withCurrencyID(id uint) currencyOption {
	return func(m *CurrencyMutation) {
		var (
			err   error
			once  sync.Once
			value *Currency
		)
		m.oldValue = func(ctx context.Context) (*Currency, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Currency.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withCurrency sets the old Currency of the mutation.
func withCurrency(node *Currency) currencyOption {
	return func(m *CurrencyMutation) {
		m.oldValue = func(context.Context) (*Currency, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m CurrencyMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m CurrencyMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Currency entities.
func (m *CurrencyMutation) SetID(id uint) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *CurrencyMutation) ID() (id uint, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *CurrencyMutation) IDs(ctx context.Context) ([]uint, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uint{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Currency.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetProductID sets the "product_id" field.
func (m *CurrencyMutation) SetProductID(u uint) {
	m.product = &u
}

// ProductID returns the value of the "product_id" field in the mutation.
func (m *CurrencyMutation) ProductID() (r uint, exists bool) {
	v := m.product
	if v == nil {
		return
	}
	return *v, true
}

// OldProductID returns the old "product_id" field's value of the Currency entity.
// If the Currency object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CurrencyMutation) OldProductID(ctx context.Context) (v uint, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldProductID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldProductID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldProductID: %w", err)
	}
	return oldValue.ProductID, nil
}

// ResetProductID resets all changes to the "product_id" field.
func (m *CurrencyMutation) ResetProductID() {
	m.product = nil
}

// SetCurrency sets the "currency" field.
func (m *CurrencyMutation) SetCurrency(c currency.Currency) {
	m.currency = &c
}

// Currency returns the value of the "currency" field in the mutation.
func (m *CurrencyMutation) Currency() (r currency.Currency, exists bool) {
	v := m.currency
	if v == nil {
		return
	}
	return *v, true
}

// OldCurrency returns the old "currency" field's value of the Currency entity.
// If the Currency object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CurrencyMutation) OldCurrency(ctx context.Context) (v currency.Currency, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCurrency is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCurrency requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCurrency: %w", err)
	}
	return oldValue.Currency, nil
}

// ResetCurrency resets all changes to the "currency" field.
func (m *CurrencyMutation) ResetCurrency() {
	m.currency = nil
}

// SetIsActive sets the "is_active" field.
func (m *CurrencyMutation) SetIsActive(b bool) {
	m.is_active = &b
}

// IsActive returns the value of the "is_active" field in the mutation.
func (m *CurrencyMutation) IsActive() (r bool, exists bool) {
	v := m.is_active
	if v == nil {
		return
	}
	return *v, true
}

// OldIsActive returns the old "is_active" field's value of the Currency entity.
// If the Currency object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CurrencyMutation) OldIsActive(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsActive is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsActive requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsActive: %w", err)
	}
	return oldValue.IsActive, nil
}

// ResetIsActive resets all changes to the "is_active" field.
func (m *CurrencyMutation) ResetIsActive() {
	m.is_active = nil
}

// SetMinAmount sets the "min_amount" field.
func (m *CurrencyMutation) SetMinAmount(d decimal.Decimal) {
	m.min_amount = &d
}

// MinAmount returns the value of the "min_amount" field in the mutation.
func (m *CurrencyMutation) MinAmount() (r decimal.Decimal, exists bool) {
	v := m.min_amount
	if v == nil {
		return
	}
	return *v, true
}

// OldMinAmount returns the old "min_amount" field's value of the Currency entity.
// If the Currency object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CurrencyMutation) OldMinAmount(ctx context.Context) (v decimal.Decimal, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMinAmount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMinAmount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMinAmount: %w", err)
	}
	return oldValue.MinAmount, nil
}

// ResetMinAmount resets all changes to the "min_amount" field.
func (m *CurrencyMutation) ResetMinAmount() {
	m.min_amount = nil
}

// SetMaxAmount sets the "max_amount" field.
func (m *CurrencyMutation) SetMaxAmount(d decimal.Decimal) {
	m.max_amount = &d
}

// MaxAmount returns the value of the "max_amount" field in the mutation.
func (m *CurrencyMutation) MaxAmount() (r decimal.Decimal, exists bool) {
	v := m.max_amount
	if v == nil {
		return
	}
	return *v, true
}

// OldMaxAmount returns the old "max_amount" field's value of the Currency entity.
// If the Currency object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *CurrencyMutation) OldMaxAmount(ctx context.Context) (v decimal.Decimal, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMaxAmount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMaxAmount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMaxAmount: %w", err)
	}
	return oldValue.MaxAmount, nil
}

// ResetMaxAmount resets all changes to the "max_amount" field.
func (m *CurrencyMutation) ResetMaxAmount() {
	m.max_amount = nil
}

// AddProfitRateIDs adds the "profit_rate" edge to the ProfitRate entity by ids.
func (m *CurrencyMutation) AddProfitRateIDs(ids ...uuid.UUID) {
	if m.profit_rate == nil {
		m.profit_rate = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.profit_rate[ids[i]] = struct{}{}
	}
}

// ClearProfitRate clears the "profit_rate" edge to the ProfitRate entity.
func (m *CurrencyMutation) ClearProfitRate() {
	m.clearedprofit_rate = true
}

// ProfitRateCleared reports if the "profit_rate" edge to the ProfitRate entity was cleared.
func (m *CurrencyMutation) ProfitRateCleared() bool {
	return m.clearedprofit_rate
}

// RemoveProfitRateIDs removes the "profit_rate" edge to the ProfitRate entity by IDs.
func (m *CurrencyMutation) RemoveProfitRateIDs(ids ...uuid.UUID) {
	if m.removedprofit_rate == nil {
		m.removedprofit_rate = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.profit_rate, ids[i])
		m.removedprofit_rate[ids[i]] = struct{}{}
	}
}

// RemovedProfitRate returns the removed IDs of the "profit_rate" edge to the ProfitRate entity.
func (m *CurrencyMutation) RemovedProfitRateIDs() (ids []uuid.UUID) {
	for id := range m.removedprofit_rate {
		ids = append(ids, id)
	}
	return
}

// ProfitRateIDs returns the "profit_rate" edge IDs in the mutation.
func (m *CurrencyMutation) ProfitRateIDs() (ids []uuid.UUID) {
	for id := range m.profit_rate {
		ids = append(ids, id)
	}
	return
}

// ResetProfitRate resets all changes to the "profit_rate" edge.
func (m *CurrencyMutation) ResetProfitRate() {
	m.profit_rate = nil
	m.clearedprofit_rate = false
	m.removedprofit_rate = nil
}

// ClearProduct clears the "product" edge to the DepositProduct entity.
func (m *CurrencyMutation) ClearProduct() {
	m.clearedproduct = true
	m.clearedFields[currency.FieldProductID] = struct{}{}
}

// ProductCleared reports if the "product" edge to the DepositProduct entity was cleared.
func (m *CurrencyMutation) ProductCleared() bool {
	return m.clearedproduct
}

// ProductIDs returns the "product" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// ProductID instead. It exists only for internal usage by the builders.
func (m *CurrencyMutation) ProductIDs() (ids []uint) {
	if id := m.product; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetProduct resets all changes to the "product" edge.
func (m *CurrencyMutation) ResetProduct() {
	m.product = nil
	m.clearedproduct = false
}

// Where appends a list predicates to the CurrencyMutation builder.
func (m *CurrencyMutation) Where(ps ...predicate.Currency) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the CurrencyMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *CurrencyMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Currency, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *CurrencyMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *CurrencyMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Currency).
func (m *CurrencyMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *CurrencyMutation) Fields() []string {
	fields := make([]string, 0, 5)
	if m.product != nil {
		fields = append(fields, currency.FieldProductID)
	}
	if m.currency != nil {
		fields = append(fields, currency.FieldCurrency)
	}
	if m.is_active != nil {
		fields = append(fields, currency.FieldIsActive)
	}
	if m.min_amount != nil {
		fields = append(fields, currency.FieldMinAmount)
	}
	if m.max_amount != nil {
		fields = append(fields, currency.FieldMaxAmount)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *CurrencyMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case currency.FieldProductID:
		return m.ProductID()
	case currency.FieldCurrency:
		return m.Currency()
	case currency.FieldIsActive:
		return m.IsActive()
	case currency.FieldMinAmount:
		return m.MinAmount()
	case currency.FieldMaxAmount:
		return m.MaxAmount()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *CurrencyMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case currency.FieldProductID:
		return m.OldProductID(ctx)
	case currency.FieldCurrency:
		return m.OldCurrency(ctx)
	case currency.FieldIsActive:
		return m.OldIsActive(ctx)
	case currency.FieldMinAmount:
		return m.OldMinAmount(ctx)
	case currency.FieldMaxAmount:
		return m.OldMaxAmount(ctx)
	}
	return nil, fmt.Errorf("unknown Currency field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *CurrencyMutation) SetField(name string, value ent.Value) error {
	switch name {
	case currency.FieldProductID:
		v, ok := value.(uint)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetProductID(v)
		return nil
	case currency.FieldCurrency:
		v, ok := value.(currency.Currency)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCurrency(v)
		return nil
	case currency.FieldIsActive:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsActive(v)
		return nil
	case currency.FieldMinAmount:
		v, ok := value.(decimal.Decimal)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMinAmount(v)
		return nil
	case currency.FieldMaxAmount:
		v, ok := value.(decimal.Decimal)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMaxAmount(v)
		return nil
	}
	return fmt.Errorf("unknown Currency field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *CurrencyMutation) AddedFields() []string {
	var fields []string
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *CurrencyMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *CurrencyMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Currency numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *CurrencyMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *CurrencyMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *CurrencyMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Currency nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *CurrencyMutation) ResetField(name string) error {
	switch name {
	case currency.FieldProductID:
		m.ResetProductID()
		return nil
	case currency.FieldCurrency:
		m.ResetCurrency()
		return nil
	case currency.FieldIsActive:
		m.ResetIsActive()
		return nil
	case currency.FieldMinAmount:
		m.ResetMinAmount()
		return nil
	case currency.FieldMaxAmount:
		m.ResetMaxAmount()
		return nil
	}
	return fmt.Errorf("unknown Currency field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *CurrencyMutation) AddedEdges() []string {
	edges := make([]string, 0, 2)
	if m.profit_rate != nil {
		edges = append(edges, currency.EdgeProfitRate)
	}
	if m.product != nil {
		edges = append(edges, currency.EdgeProduct)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *CurrencyMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case currency.EdgeProfitRate:
		ids := make([]ent.Value, 0, len(m.profit_rate))
		for id := range m.profit_rate {
			ids = append(ids, id)
		}
		return ids
	case currency.EdgeProduct:
		if id := m.product; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *CurrencyMutation) RemovedEdges() []string {
	edges := make([]string, 0, 2)
	if m.removedprofit_rate != nil {
		edges = append(edges, currency.EdgeProfitRate)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *CurrencyMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case currency.EdgeProfitRate:
		ids := make([]ent.Value, 0, len(m.removedprofit_rate))
		for id := range m.removedprofit_rate {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *CurrencyMutation) ClearedEdges() []string {
	edges := make([]string, 0, 2)
	if m.clearedprofit_rate {
		edges = append(edges, currency.EdgeProfitRate)
	}
	if m.clearedproduct {
		edges = append(edges, currency.EdgeProduct)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *CurrencyMutation) EdgeCleared(name string) bool {
	switch name {
	case currency.EdgeProfitRate:
		return m.clearedprofit_rate
	case currency.EdgeProduct:
		return m.clearedproduct
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *CurrencyMutation) ClearEdge(name string) error {
	switch name {
	case currency.EdgeProduct:
		m.ClearProduct()
		return nil
	}
	return fmt.Errorf("unknown Currency unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *CurrencyMutation) ResetEdge(name string) error {
	switch name {
	case currency.EdgeProfitRate:
		m.ResetProfitRate()
		return nil
	case currency.EdgeProduct:
		m.ResetProduct()
		return nil
	}
	return fmt.Errorf("unknown Currency edge %s", name)
}

// DepositApplicationMutation represents an operation that mutates the DepositApplication nodes in the graph.
type DepositApplicationMutation struct {
	config
	op                        Op
	typ                       string
	id                        *uuid.UUID
	create_time               *time.Time
	update_time               *time.Time
	colvir_reference_id       *string
	status                    *depositapplication.Status
	user_id                   *uuid.UUID
	user_iin                  *string
	deposit_source_account_id *uuid.UUID
	deposit_amount            *decimal.Decimal
	deposit_payout_method     *depositapplication.DepositPayoutMethod
	closed_at                 *time.Time
	clearedFields             map[string]struct{}
	documents                 map[uuid.UUID]struct{}
	removeddocuments          map[uuid.UUID]struct{}
	cleareddocuments          bool
	rate                      *uuid.UUID
	clearedrate               bool
	done                      bool
	oldValue                  func(context.Context) (*DepositApplication, error)
	predicates                []predicate.DepositApplication
}

var _ ent.Mutation = (*DepositApplicationMutation)(nil)

// depositapplicationOption allows management of the mutation configuration using functional options.
type depositapplicationOption func(*DepositApplicationMutation)

// newDepositApplicationMutation creates new mutation for the DepositApplication entity.
func newDepositApplicationMutation(c config, op Op, opts ...depositapplicationOption) *DepositApplicationMutation {
	m := &DepositApplicationMutation{
		config:        c,
		op:            op,
		typ:           TypeDepositApplication,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withDepositApplicationID sets the ID field of the mutation.
func withDepositApplicationID(id uuid.UUID) depositapplicationOption {
	return func(m *DepositApplicationMutation) {
		var (
			err   error
			once  sync.Once
			value *DepositApplication
		)
		m.oldValue = func(ctx context.Context) (*DepositApplication, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().DepositApplication.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withDepositApplication sets the old DepositApplication of the mutation.
func withDepositApplication(node *DepositApplication) depositapplicationOption {
	return func(m *DepositApplicationMutation) {
		m.oldValue = func(context.Context) (*DepositApplication, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m DepositApplicationMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m DepositApplicationMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of DepositApplication entities.
func (m *DepositApplicationMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *DepositApplicationMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *DepositApplicationMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().DepositApplication.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreateTime sets the "create_time" field.
func (m *DepositApplicationMutation) SetCreateTime(t time.Time) {
	m.create_time = &t
}

// CreateTime returns the value of the "create_time" field in the mutation.
func (m *DepositApplicationMutation) CreateTime() (r time.Time, exists bool) {
	v := m.create_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateTime returns the old "create_time" field's value of the DepositApplication entity.
// If the DepositApplication object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositApplicationMutation) OldCreateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateTime: %w", err)
	}
	return oldValue.CreateTime, nil
}

// ClearCreateTime clears the value of the "create_time" field.
func (m *DepositApplicationMutation) ClearCreateTime() {
	m.create_time = nil
	m.clearedFields[depositapplication.FieldCreateTime] = struct{}{}
}

// CreateTimeCleared returns if the "create_time" field was cleared in this mutation.
func (m *DepositApplicationMutation) CreateTimeCleared() bool {
	_, ok := m.clearedFields[depositapplication.FieldCreateTime]
	return ok
}

// ResetCreateTime resets all changes to the "create_time" field.
func (m *DepositApplicationMutation) ResetCreateTime() {
	m.create_time = nil
	delete(m.clearedFields, depositapplication.FieldCreateTime)
}

// SetUpdateTime sets the "update_time" field.
func (m *DepositApplicationMutation) SetUpdateTime(t time.Time) {
	m.update_time = &t
}

// UpdateTime returns the value of the "update_time" field in the mutation.
func (m *DepositApplicationMutation) UpdateTime() (r time.Time, exists bool) {
	v := m.update_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateTime returns the old "update_time" field's value of the DepositApplication entity.
// If the DepositApplication object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositApplicationMutation) OldUpdateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateTime: %w", err)
	}
	return oldValue.UpdateTime, nil
}

// ClearUpdateTime clears the value of the "update_time" field.
func (m *DepositApplicationMutation) ClearUpdateTime() {
	m.update_time = nil
	m.clearedFields[depositapplication.FieldUpdateTime] = struct{}{}
}

// UpdateTimeCleared returns if the "update_time" field was cleared in this mutation.
func (m *DepositApplicationMutation) UpdateTimeCleared() bool {
	_, ok := m.clearedFields[depositapplication.FieldUpdateTime]
	return ok
}

// ResetUpdateTime resets all changes to the "update_time" field.
func (m *DepositApplicationMutation) ResetUpdateTime() {
	m.update_time = nil
	delete(m.clearedFields, depositapplication.FieldUpdateTime)
}

// SetProfitRateID sets the "profit_rate_id" field.
func (m *DepositApplicationMutation) SetProfitRateID(u uuid.UUID) {
	m.rate = &u
}

// ProfitRateID returns the value of the "profit_rate_id" field in the mutation.
func (m *DepositApplicationMutation) ProfitRateID() (r uuid.UUID, exists bool) {
	v := m.rate
	if v == nil {
		return
	}
	return *v, true
}

// OldProfitRateID returns the old "profit_rate_id" field's value of the DepositApplication entity.
// If the DepositApplication object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositApplicationMutation) OldProfitRateID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldProfitRateID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldProfitRateID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldProfitRateID: %w", err)
	}
	return oldValue.ProfitRateID, nil
}

// ResetProfitRateID resets all changes to the "profit_rate_id" field.
func (m *DepositApplicationMutation) ResetProfitRateID() {
	m.rate = nil
}

// SetColvirReferenceID sets the "colvir_reference_id" field.
func (m *DepositApplicationMutation) SetColvirReferenceID(s string) {
	m.colvir_reference_id = &s
}

// ColvirReferenceID returns the value of the "colvir_reference_id" field in the mutation.
func (m *DepositApplicationMutation) ColvirReferenceID() (r string, exists bool) {
	v := m.colvir_reference_id
	if v == nil {
		return
	}
	return *v, true
}

// OldColvirReferenceID returns the old "colvir_reference_id" field's value of the DepositApplication entity.
// If the DepositApplication object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositApplicationMutation) OldColvirReferenceID(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldColvirReferenceID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldColvirReferenceID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldColvirReferenceID: %w", err)
	}
	return oldValue.ColvirReferenceID, nil
}

// ClearColvirReferenceID clears the value of the "colvir_reference_id" field.
func (m *DepositApplicationMutation) ClearColvirReferenceID() {
	m.colvir_reference_id = nil
	m.clearedFields[depositapplication.FieldColvirReferenceID] = struct{}{}
}

// ColvirReferenceIDCleared returns if the "colvir_reference_id" field was cleared in this mutation.
func (m *DepositApplicationMutation) ColvirReferenceIDCleared() bool {
	_, ok := m.clearedFields[depositapplication.FieldColvirReferenceID]
	return ok
}

// ResetColvirReferenceID resets all changes to the "colvir_reference_id" field.
func (m *DepositApplicationMutation) ResetColvirReferenceID() {
	m.colvir_reference_id = nil
	delete(m.clearedFields, depositapplication.FieldColvirReferenceID)
}

// SetStatus sets the "status" field.
func (m *DepositApplicationMutation) SetStatus(d depositapplication.Status) {
	m.status = &d
}

// Status returns the value of the "status" field in the mutation.
func (m *DepositApplicationMutation) Status() (r depositapplication.Status, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the DepositApplication entity.
// If the DepositApplication object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositApplicationMutation) OldStatus(ctx context.Context) (v depositapplication.Status, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *DepositApplicationMutation) ResetStatus() {
	m.status = nil
}

// SetUserID sets the "user_id" field.
func (m *DepositApplicationMutation) SetUserID(u uuid.UUID) {
	m.user_id = &u
}

// UserID returns the value of the "user_id" field in the mutation.
func (m *DepositApplicationMutation) UserID() (r uuid.UUID, exists bool) {
	v := m.user_id
	if v == nil {
		return
	}
	return *v, true
}

// OldUserID returns the old "user_id" field's value of the DepositApplication entity.
// If the DepositApplication object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositApplicationMutation) OldUserID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserID: %w", err)
	}
	return oldValue.UserID, nil
}

// ResetUserID resets all changes to the "user_id" field.
func (m *DepositApplicationMutation) ResetUserID() {
	m.user_id = nil
}

// SetUserIin sets the "user_iin" field.
func (m *DepositApplicationMutation) SetUserIin(s string) {
	m.user_iin = &s
}

// UserIin returns the value of the "user_iin" field in the mutation.
func (m *DepositApplicationMutation) UserIin() (r string, exists bool) {
	v := m.user_iin
	if v == nil {
		return
	}
	return *v, true
}

// OldUserIin returns the old "user_iin" field's value of the DepositApplication entity.
// If the DepositApplication object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositApplicationMutation) OldUserIin(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserIin is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserIin requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserIin: %w", err)
	}
	return oldValue.UserIin, nil
}

// ResetUserIin resets all changes to the "user_iin" field.
func (m *DepositApplicationMutation) ResetUserIin() {
	m.user_iin = nil
}

// SetDepositSourceAccountID sets the "deposit_source_account_id" field.
func (m *DepositApplicationMutation) SetDepositSourceAccountID(u uuid.UUID) {
	m.deposit_source_account_id = &u
}

// DepositSourceAccountID returns the value of the "deposit_source_account_id" field in the mutation.
func (m *DepositApplicationMutation) DepositSourceAccountID() (r uuid.UUID, exists bool) {
	v := m.deposit_source_account_id
	if v == nil {
		return
	}
	return *v, true
}

// OldDepositSourceAccountID returns the old "deposit_source_account_id" field's value of the DepositApplication entity.
// If the DepositApplication object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositApplicationMutation) OldDepositSourceAccountID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDepositSourceAccountID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDepositSourceAccountID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDepositSourceAccountID: %w", err)
	}
	return oldValue.DepositSourceAccountID, nil
}

// ResetDepositSourceAccountID resets all changes to the "deposit_source_account_id" field.
func (m *DepositApplicationMutation) ResetDepositSourceAccountID() {
	m.deposit_source_account_id = nil
}

// SetDepositAmount sets the "deposit_amount" field.
func (m *DepositApplicationMutation) SetDepositAmount(d decimal.Decimal) {
	m.deposit_amount = &d
}

// DepositAmount returns the value of the "deposit_amount" field in the mutation.
func (m *DepositApplicationMutation) DepositAmount() (r decimal.Decimal, exists bool) {
	v := m.deposit_amount
	if v == nil {
		return
	}
	return *v, true
}

// OldDepositAmount returns the old "deposit_amount" field's value of the DepositApplication entity.
// If the DepositApplication object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositApplicationMutation) OldDepositAmount(ctx context.Context) (v decimal.Decimal, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDepositAmount is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDepositAmount requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDepositAmount: %w", err)
	}
	return oldValue.DepositAmount, nil
}

// ResetDepositAmount resets all changes to the "deposit_amount" field.
func (m *DepositApplicationMutation) ResetDepositAmount() {
	m.deposit_amount = nil
}

// SetDepositPayoutMethod sets the "deposit_payout_method" field.
func (m *DepositApplicationMutation) SetDepositPayoutMethod(dpm depositapplication.DepositPayoutMethod) {
	m.deposit_payout_method = &dpm
}

// DepositPayoutMethod returns the value of the "deposit_payout_method" field in the mutation.
func (m *DepositApplicationMutation) DepositPayoutMethod() (r depositapplication.DepositPayoutMethod, exists bool) {
	v := m.deposit_payout_method
	if v == nil {
		return
	}
	return *v, true
}

// OldDepositPayoutMethod returns the old "deposit_payout_method" field's value of the DepositApplication entity.
// If the DepositApplication object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositApplicationMutation) OldDepositPayoutMethod(ctx context.Context) (v depositapplication.DepositPayoutMethod, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDepositPayoutMethod is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDepositPayoutMethod requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDepositPayoutMethod: %w", err)
	}
	return oldValue.DepositPayoutMethod, nil
}

// ResetDepositPayoutMethod resets all changes to the "deposit_payout_method" field.
func (m *DepositApplicationMutation) ResetDepositPayoutMethod() {
	m.deposit_payout_method = nil
}

// SetClosedAt sets the "closed_at" field.
func (m *DepositApplicationMutation) SetClosedAt(t time.Time) {
	m.closed_at = &t
}

// ClosedAt returns the value of the "closed_at" field in the mutation.
func (m *DepositApplicationMutation) ClosedAt() (r time.Time, exists bool) {
	v := m.closed_at
	if v == nil {
		return
	}
	return *v, true
}

// OldClosedAt returns the old "closed_at" field's value of the DepositApplication entity.
// If the DepositApplication object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositApplicationMutation) OldClosedAt(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldClosedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldClosedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldClosedAt: %w", err)
	}
	return oldValue.ClosedAt, nil
}

// ClearClosedAt clears the value of the "closed_at" field.
func (m *DepositApplicationMutation) ClearClosedAt() {
	m.closed_at = nil
	m.clearedFields[depositapplication.FieldClosedAt] = struct{}{}
}

// ClosedAtCleared returns if the "closed_at" field was cleared in this mutation.
func (m *DepositApplicationMutation) ClosedAtCleared() bool {
	_, ok := m.clearedFields[depositapplication.FieldClosedAt]
	return ok
}

// ResetClosedAt resets all changes to the "closed_at" field.
func (m *DepositApplicationMutation) ResetClosedAt() {
	m.closed_at = nil
	delete(m.clearedFields, depositapplication.FieldClosedAt)
}

// AddDocumentIDs adds the "documents" edge to the Document entity by ids.
func (m *DepositApplicationMutation) AddDocumentIDs(ids ...uuid.UUID) {
	if m.documents == nil {
		m.documents = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.documents[ids[i]] = struct{}{}
	}
}

// ClearDocuments clears the "documents" edge to the Document entity.
func (m *DepositApplicationMutation) ClearDocuments() {
	m.cleareddocuments = true
}

// DocumentsCleared reports if the "documents" edge to the Document entity was cleared.
func (m *DepositApplicationMutation) DocumentsCleared() bool {
	return m.cleareddocuments
}

// RemoveDocumentIDs removes the "documents" edge to the Document entity by IDs.
func (m *DepositApplicationMutation) RemoveDocumentIDs(ids ...uuid.UUID) {
	if m.removeddocuments == nil {
		m.removeddocuments = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.documents, ids[i])
		m.removeddocuments[ids[i]] = struct{}{}
	}
}

// RemovedDocuments returns the removed IDs of the "documents" edge to the Document entity.
func (m *DepositApplicationMutation) RemovedDocumentsIDs() (ids []uuid.UUID) {
	for id := range m.removeddocuments {
		ids = append(ids, id)
	}
	return
}

// DocumentsIDs returns the "documents" edge IDs in the mutation.
func (m *DepositApplicationMutation) DocumentsIDs() (ids []uuid.UUID) {
	for id := range m.documents {
		ids = append(ids, id)
	}
	return
}

// ResetDocuments resets all changes to the "documents" edge.
func (m *DepositApplicationMutation) ResetDocuments() {
	m.documents = nil
	m.cleareddocuments = false
	m.removeddocuments = nil
}

// SetRateID sets the "rate" edge to the ProfitRate entity by id.
func (m *DepositApplicationMutation) SetRateID(id uuid.UUID) {
	m.rate = &id
}

// ClearRate clears the "rate" edge to the ProfitRate entity.
func (m *DepositApplicationMutation) ClearRate() {
	m.clearedrate = true
	m.clearedFields[depositapplication.FieldProfitRateID] = struct{}{}
}

// RateCleared reports if the "rate" edge to the ProfitRate entity was cleared.
func (m *DepositApplicationMutation) RateCleared() bool {
	return m.clearedrate
}

// RateID returns the "rate" edge ID in the mutation.
func (m *DepositApplicationMutation) RateID() (id uuid.UUID, exists bool) {
	if m.rate != nil {
		return *m.rate, true
	}
	return
}

// RateIDs returns the "rate" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// RateID instead. It exists only for internal usage by the builders.
func (m *DepositApplicationMutation) RateIDs() (ids []uuid.UUID) {
	if id := m.rate; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetRate resets all changes to the "rate" edge.
func (m *DepositApplicationMutation) ResetRate() {
	m.rate = nil
	m.clearedrate = false
}

// Where appends a list predicates to the DepositApplicationMutation builder.
func (m *DepositApplicationMutation) Where(ps ...predicate.DepositApplication) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the DepositApplicationMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *DepositApplicationMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.DepositApplication, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *DepositApplicationMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *DepositApplicationMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (DepositApplication).
func (m *DepositApplicationMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *DepositApplicationMutation) Fields() []string {
	fields := make([]string, 0, 11)
	if m.create_time != nil {
		fields = append(fields, depositapplication.FieldCreateTime)
	}
	if m.update_time != nil {
		fields = append(fields, depositapplication.FieldUpdateTime)
	}
	if m.rate != nil {
		fields = append(fields, depositapplication.FieldProfitRateID)
	}
	if m.colvir_reference_id != nil {
		fields = append(fields, depositapplication.FieldColvirReferenceID)
	}
	if m.status != nil {
		fields = append(fields, depositapplication.FieldStatus)
	}
	if m.user_id != nil {
		fields = append(fields, depositapplication.FieldUserID)
	}
	if m.user_iin != nil {
		fields = append(fields, depositapplication.FieldUserIin)
	}
	if m.deposit_source_account_id != nil {
		fields = append(fields, depositapplication.FieldDepositSourceAccountID)
	}
	if m.deposit_amount != nil {
		fields = append(fields, depositapplication.FieldDepositAmount)
	}
	if m.deposit_payout_method != nil {
		fields = append(fields, depositapplication.FieldDepositPayoutMethod)
	}
	if m.closed_at != nil {
		fields = append(fields, depositapplication.FieldClosedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *DepositApplicationMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case depositapplication.FieldCreateTime:
		return m.CreateTime()
	case depositapplication.FieldUpdateTime:
		return m.UpdateTime()
	case depositapplication.FieldProfitRateID:
		return m.ProfitRateID()
	case depositapplication.FieldColvirReferenceID:
		return m.ColvirReferenceID()
	case depositapplication.FieldStatus:
		return m.Status()
	case depositapplication.FieldUserID:
		return m.UserID()
	case depositapplication.FieldUserIin:
		return m.UserIin()
	case depositapplication.FieldDepositSourceAccountID:
		return m.DepositSourceAccountID()
	case depositapplication.FieldDepositAmount:
		return m.DepositAmount()
	case depositapplication.FieldDepositPayoutMethod:
		return m.DepositPayoutMethod()
	case depositapplication.FieldClosedAt:
		return m.ClosedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *DepositApplicationMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case depositapplication.FieldCreateTime:
		return m.OldCreateTime(ctx)
	case depositapplication.FieldUpdateTime:
		return m.OldUpdateTime(ctx)
	case depositapplication.FieldProfitRateID:
		return m.OldProfitRateID(ctx)
	case depositapplication.FieldColvirReferenceID:
		return m.OldColvirReferenceID(ctx)
	case depositapplication.FieldStatus:
		return m.OldStatus(ctx)
	case depositapplication.FieldUserID:
		return m.OldUserID(ctx)
	case depositapplication.FieldUserIin:
		return m.OldUserIin(ctx)
	case depositapplication.FieldDepositSourceAccountID:
		return m.OldDepositSourceAccountID(ctx)
	case depositapplication.FieldDepositAmount:
		return m.OldDepositAmount(ctx)
	case depositapplication.FieldDepositPayoutMethod:
		return m.OldDepositPayoutMethod(ctx)
	case depositapplication.FieldClosedAt:
		return m.OldClosedAt(ctx)
	}
	return nil, fmt.Errorf("unknown DepositApplication field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *DepositApplicationMutation) SetField(name string, value ent.Value) error {
	switch name {
	case depositapplication.FieldCreateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateTime(v)
		return nil
	case depositapplication.FieldUpdateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateTime(v)
		return nil
	case depositapplication.FieldProfitRateID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetProfitRateID(v)
		return nil
	case depositapplication.FieldColvirReferenceID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetColvirReferenceID(v)
		return nil
	case depositapplication.FieldStatus:
		v, ok := value.(depositapplication.Status)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case depositapplication.FieldUserID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserID(v)
		return nil
	case depositapplication.FieldUserIin:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserIin(v)
		return nil
	case depositapplication.FieldDepositSourceAccountID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDepositSourceAccountID(v)
		return nil
	case depositapplication.FieldDepositAmount:
		v, ok := value.(decimal.Decimal)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDepositAmount(v)
		return nil
	case depositapplication.FieldDepositPayoutMethod:
		v, ok := value.(depositapplication.DepositPayoutMethod)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDepositPayoutMethod(v)
		return nil
	case depositapplication.FieldClosedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetClosedAt(v)
		return nil
	}
	return fmt.Errorf("unknown DepositApplication field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *DepositApplicationMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *DepositApplicationMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *DepositApplicationMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown DepositApplication numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *DepositApplicationMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(depositapplication.FieldCreateTime) {
		fields = append(fields, depositapplication.FieldCreateTime)
	}
	if m.FieldCleared(depositapplication.FieldUpdateTime) {
		fields = append(fields, depositapplication.FieldUpdateTime)
	}
	if m.FieldCleared(depositapplication.FieldColvirReferenceID) {
		fields = append(fields, depositapplication.FieldColvirReferenceID)
	}
	if m.FieldCleared(depositapplication.FieldClosedAt) {
		fields = append(fields, depositapplication.FieldClosedAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *DepositApplicationMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *DepositApplicationMutation) ClearField(name string) error {
	switch name {
	case depositapplication.FieldCreateTime:
		m.ClearCreateTime()
		return nil
	case depositapplication.FieldUpdateTime:
		m.ClearUpdateTime()
		return nil
	case depositapplication.FieldColvirReferenceID:
		m.ClearColvirReferenceID()
		return nil
	case depositapplication.FieldClosedAt:
		m.ClearClosedAt()
		return nil
	}
	return fmt.Errorf("unknown DepositApplication nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *DepositApplicationMutation) ResetField(name string) error {
	switch name {
	case depositapplication.FieldCreateTime:
		m.ResetCreateTime()
		return nil
	case depositapplication.FieldUpdateTime:
		m.ResetUpdateTime()
		return nil
	case depositapplication.FieldProfitRateID:
		m.ResetProfitRateID()
		return nil
	case depositapplication.FieldColvirReferenceID:
		m.ResetColvirReferenceID()
		return nil
	case depositapplication.FieldStatus:
		m.ResetStatus()
		return nil
	case depositapplication.FieldUserID:
		m.ResetUserID()
		return nil
	case depositapplication.FieldUserIin:
		m.ResetUserIin()
		return nil
	case depositapplication.FieldDepositSourceAccountID:
		m.ResetDepositSourceAccountID()
		return nil
	case depositapplication.FieldDepositAmount:
		m.ResetDepositAmount()
		return nil
	case depositapplication.FieldDepositPayoutMethod:
		m.ResetDepositPayoutMethod()
		return nil
	case depositapplication.FieldClosedAt:
		m.ResetClosedAt()
		return nil
	}
	return fmt.Errorf("unknown DepositApplication field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *DepositApplicationMutation) AddedEdges() []string {
	edges := make([]string, 0, 2)
	if m.documents != nil {
		edges = append(edges, depositapplication.EdgeDocuments)
	}
	if m.rate != nil {
		edges = append(edges, depositapplication.EdgeRate)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *DepositApplicationMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case depositapplication.EdgeDocuments:
		ids := make([]ent.Value, 0, len(m.documents))
		for id := range m.documents {
			ids = append(ids, id)
		}
		return ids
	case depositapplication.EdgeRate:
		if id := m.rate; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *DepositApplicationMutation) RemovedEdges() []string {
	edges := make([]string, 0, 2)
	if m.removeddocuments != nil {
		edges = append(edges, depositapplication.EdgeDocuments)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *DepositApplicationMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case depositapplication.EdgeDocuments:
		ids := make([]ent.Value, 0, len(m.removeddocuments))
		for id := range m.removeddocuments {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *DepositApplicationMutation) ClearedEdges() []string {
	edges := make([]string, 0, 2)
	if m.cleareddocuments {
		edges = append(edges, depositapplication.EdgeDocuments)
	}
	if m.clearedrate {
		edges = append(edges, depositapplication.EdgeRate)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *DepositApplicationMutation) EdgeCleared(name string) bool {
	switch name {
	case depositapplication.EdgeDocuments:
		return m.cleareddocuments
	case depositapplication.EdgeRate:
		return m.clearedrate
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *DepositApplicationMutation) ClearEdge(name string) error {
	switch name {
	case depositapplication.EdgeRate:
		m.ClearRate()
		return nil
	}
	return fmt.Errorf("unknown DepositApplication unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *DepositApplicationMutation) ResetEdge(name string) error {
	switch name {
	case depositapplication.EdgeDocuments:
		m.ResetDocuments()
		return nil
	case depositapplication.EdgeRate:
		m.ResetRate()
		return nil
	}
	return fmt.Errorf("unknown DepositApplication edge %s", name)
}

// DepositCardsMutation represents an operation that mutates the DepositCards nodes in the graph.
type DepositCardsMutation struct {
	config
	op                           Op
	typ                          string
	id                           *uuid.UUID
	create_time                  *time.Time
	update_time                  *time.Time
	card_id                      *uuid.UUID
	account_iban                 *string
	fin_contract_id              *string
	rrn                          *string
	external_id                  *string
	processing_transaction_id    *int64
	addprocessing_transaction_id *int64
	clearedFields                map[string]struct{}
	application                  *uuid.UUID
	clearedapplication           bool
	done                         bool
	oldValue                     func(context.Context) (*DepositCards, error)
	predicates                   []predicate.DepositCards
}

var _ ent.Mutation = (*DepositCardsMutation)(nil)

// depositcardsOption allows management of the mutation configuration using functional options.
type depositcardsOption func(*DepositCardsMutation)

// newDepositCardsMutation creates new mutation for the DepositCards entity.
func newDepositCardsMutation(c config, op Op, opts ...depositcardsOption) *DepositCardsMutation {
	m := &DepositCardsMutation{
		config:        c,
		op:            op,
		typ:           TypeDepositCards,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withDepositCardsID sets the ID field of the mutation.
func withDepositCardsID(id uuid.UUID) depositcardsOption {
	return func(m *DepositCardsMutation) {
		var (
			err   error
			once  sync.Once
			value *DepositCards
		)
		m.oldValue = func(ctx context.Context) (*DepositCards, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().DepositCards.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withDepositCards sets the old DepositCards of the mutation.
func withDepositCards(node *DepositCards) depositcardsOption {
	return func(m *DepositCardsMutation) {
		m.oldValue = func(context.Context) (*DepositCards, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m DepositCardsMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m DepositCardsMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of DepositCards entities.
func (m *DepositCardsMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *DepositCardsMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *DepositCardsMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().DepositCards.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreateTime sets the "create_time" field.
func (m *DepositCardsMutation) SetCreateTime(t time.Time) {
	m.create_time = &t
}

// CreateTime returns the value of the "create_time" field in the mutation.
func (m *DepositCardsMutation) CreateTime() (r time.Time, exists bool) {
	v := m.create_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateTime returns the old "create_time" field's value of the DepositCards entity.
// If the DepositCards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositCardsMutation) OldCreateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateTime: %w", err)
	}
	return oldValue.CreateTime, nil
}

// ClearCreateTime clears the value of the "create_time" field.
func (m *DepositCardsMutation) ClearCreateTime() {
	m.create_time = nil
	m.clearedFields[depositcards.FieldCreateTime] = struct{}{}
}

// CreateTimeCleared returns if the "create_time" field was cleared in this mutation.
func (m *DepositCardsMutation) CreateTimeCleared() bool {
	_, ok := m.clearedFields[depositcards.FieldCreateTime]
	return ok
}

// ResetCreateTime resets all changes to the "create_time" field.
func (m *DepositCardsMutation) ResetCreateTime() {
	m.create_time = nil
	delete(m.clearedFields, depositcards.FieldCreateTime)
}

// SetUpdateTime sets the "update_time" field.
func (m *DepositCardsMutation) SetUpdateTime(t time.Time) {
	m.update_time = &t
}

// UpdateTime returns the value of the "update_time" field in the mutation.
func (m *DepositCardsMutation) UpdateTime() (r time.Time, exists bool) {
	v := m.update_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateTime returns the old "update_time" field's value of the DepositCards entity.
// If the DepositCards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositCardsMutation) OldUpdateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateTime: %w", err)
	}
	return oldValue.UpdateTime, nil
}

// ClearUpdateTime clears the value of the "update_time" field.
func (m *DepositCardsMutation) ClearUpdateTime() {
	m.update_time = nil
	m.clearedFields[depositcards.FieldUpdateTime] = struct{}{}
}

// UpdateTimeCleared returns if the "update_time" field was cleared in this mutation.
func (m *DepositCardsMutation) UpdateTimeCleared() bool {
	_, ok := m.clearedFields[depositcards.FieldUpdateTime]
	return ok
}

// ResetUpdateTime resets all changes to the "update_time" field.
func (m *DepositCardsMutation) ResetUpdateTime() {
	m.update_time = nil
	delete(m.clearedFields, depositcards.FieldUpdateTime)
}

// SetCardID sets the "card_id" field.
func (m *DepositCardsMutation) SetCardID(u uuid.UUID) {
	m.card_id = &u
}

// CardID returns the value of the "card_id" field in the mutation.
func (m *DepositCardsMutation) CardID() (r uuid.UUID, exists bool) {
	v := m.card_id
	if v == nil {
		return
	}
	return *v, true
}

// OldCardID returns the old "card_id" field's value of the DepositCards entity.
// If the DepositCards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositCardsMutation) OldCardID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCardID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCardID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCardID: %w", err)
	}
	return oldValue.CardID, nil
}

// ResetCardID resets all changes to the "card_id" field.
func (m *DepositCardsMutation) ResetCardID() {
	m.card_id = nil
}

// SetAccountIban sets the "account_iban" field.
func (m *DepositCardsMutation) SetAccountIban(s string) {
	m.account_iban = &s
}

// AccountIban returns the value of the "account_iban" field in the mutation.
func (m *DepositCardsMutation) AccountIban() (r string, exists bool) {
	v := m.account_iban
	if v == nil {
		return
	}
	return *v, true
}

// OldAccountIban returns the old "account_iban" field's value of the DepositCards entity.
// If the DepositCards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositCardsMutation) OldAccountIban(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAccountIban is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAccountIban requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAccountIban: %w", err)
	}
	return oldValue.AccountIban, nil
}

// ResetAccountIban resets all changes to the "account_iban" field.
func (m *DepositCardsMutation) ResetAccountIban() {
	m.account_iban = nil
}

// SetFinContractID sets the "fin_contract_id" field.
func (m *DepositCardsMutation) SetFinContractID(s string) {
	m.fin_contract_id = &s
}

// FinContractID returns the value of the "fin_contract_id" field in the mutation.
func (m *DepositCardsMutation) FinContractID() (r string, exists bool) {
	v := m.fin_contract_id
	if v == nil {
		return
	}
	return *v, true
}

// OldFinContractID returns the old "fin_contract_id" field's value of the DepositCards entity.
// If the DepositCards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositCardsMutation) OldFinContractID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFinContractID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFinContractID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFinContractID: %w", err)
	}
	return oldValue.FinContractID, nil
}

// ResetFinContractID resets all changes to the "fin_contract_id" field.
func (m *DepositCardsMutation) ResetFinContractID() {
	m.fin_contract_id = nil
}

// SetRrn sets the "rrn" field.
func (m *DepositCardsMutation) SetRrn(s string) {
	m.rrn = &s
}

// Rrn returns the value of the "rrn" field in the mutation.
func (m *DepositCardsMutation) Rrn() (r string, exists bool) {
	v := m.rrn
	if v == nil {
		return
	}
	return *v, true
}

// OldRrn returns the old "rrn" field's value of the DepositCards entity.
// If the DepositCards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositCardsMutation) OldRrn(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRrn is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRrn requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRrn: %w", err)
	}
	return oldValue.Rrn, nil
}

// ResetRrn resets all changes to the "rrn" field.
func (m *DepositCardsMutation) ResetRrn() {
	m.rrn = nil
}

// SetExternalID sets the "external_id" field.
func (m *DepositCardsMutation) SetExternalID(s string) {
	m.external_id = &s
}

// ExternalID returns the value of the "external_id" field in the mutation.
func (m *DepositCardsMutation) ExternalID() (r string, exists bool) {
	v := m.external_id
	if v == nil {
		return
	}
	return *v, true
}

// OldExternalID returns the old "external_id" field's value of the DepositCards entity.
// If the DepositCards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositCardsMutation) OldExternalID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldExternalID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldExternalID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldExternalID: %w", err)
	}
	return oldValue.ExternalID, nil
}

// ResetExternalID resets all changes to the "external_id" field.
func (m *DepositCardsMutation) ResetExternalID() {
	m.external_id = nil
}

// SetProcessingTransactionID sets the "processing_transaction_id" field.
func (m *DepositCardsMutation) SetProcessingTransactionID(i int64) {
	m.processing_transaction_id = &i
	m.addprocessing_transaction_id = nil
}

// ProcessingTransactionID returns the value of the "processing_transaction_id" field in the mutation.
func (m *DepositCardsMutation) ProcessingTransactionID() (r int64, exists bool) {
	v := m.processing_transaction_id
	if v == nil {
		return
	}
	return *v, true
}

// OldProcessingTransactionID returns the old "processing_transaction_id" field's value of the DepositCards entity.
// If the DepositCards object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositCardsMutation) OldProcessingTransactionID(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldProcessingTransactionID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldProcessingTransactionID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldProcessingTransactionID: %w", err)
	}
	return oldValue.ProcessingTransactionID, nil
}

// AddProcessingTransactionID adds i to the "processing_transaction_id" field.
func (m *DepositCardsMutation) AddProcessingTransactionID(i int64) {
	if m.addprocessing_transaction_id != nil {
		*m.addprocessing_transaction_id += i
	} else {
		m.addprocessing_transaction_id = &i
	}
}

// AddedProcessingTransactionID returns the value that was added to the "processing_transaction_id" field in this mutation.
func (m *DepositCardsMutation) AddedProcessingTransactionID() (r int64, exists bool) {
	v := m.addprocessing_transaction_id
	if v == nil {
		return
	}
	return *v, true
}

// ResetProcessingTransactionID resets all changes to the "processing_transaction_id" field.
func (m *DepositCardsMutation) ResetProcessingTransactionID() {
	m.processing_transaction_id = nil
	m.addprocessing_transaction_id = nil
}

// SetApplicationID sets the "application" edge to the DepositApplication entity by id.
func (m *DepositCardsMutation) SetApplicationID(id uuid.UUID) {
	m.application = &id
}

// ClearApplication clears the "application" edge to the DepositApplication entity.
func (m *DepositCardsMutation) ClearApplication() {
	m.clearedapplication = true
}

// ApplicationCleared reports if the "application" edge to the DepositApplication entity was cleared.
func (m *DepositCardsMutation) ApplicationCleared() bool {
	return m.clearedapplication
}

// ApplicationID returns the "application" edge ID in the mutation.
func (m *DepositCardsMutation) ApplicationID() (id uuid.UUID, exists bool) {
	if m.application != nil {
		return *m.application, true
	}
	return
}

// ApplicationIDs returns the "application" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// ApplicationID instead. It exists only for internal usage by the builders.
func (m *DepositCardsMutation) ApplicationIDs() (ids []uuid.UUID) {
	if id := m.application; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetApplication resets all changes to the "application" edge.
func (m *DepositCardsMutation) ResetApplication() {
	m.application = nil
	m.clearedapplication = false
}

// Where appends a list predicates to the DepositCardsMutation builder.
func (m *DepositCardsMutation) Where(ps ...predicate.DepositCards) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the DepositCardsMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *DepositCardsMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.DepositCards, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *DepositCardsMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *DepositCardsMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (DepositCards).
func (m *DepositCardsMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *DepositCardsMutation) Fields() []string {
	fields := make([]string, 0, 8)
	if m.create_time != nil {
		fields = append(fields, depositcards.FieldCreateTime)
	}
	if m.update_time != nil {
		fields = append(fields, depositcards.FieldUpdateTime)
	}
	if m.card_id != nil {
		fields = append(fields, depositcards.FieldCardID)
	}
	if m.account_iban != nil {
		fields = append(fields, depositcards.FieldAccountIban)
	}
	if m.fin_contract_id != nil {
		fields = append(fields, depositcards.FieldFinContractID)
	}
	if m.rrn != nil {
		fields = append(fields, depositcards.FieldRrn)
	}
	if m.external_id != nil {
		fields = append(fields, depositcards.FieldExternalID)
	}
	if m.processing_transaction_id != nil {
		fields = append(fields, depositcards.FieldProcessingTransactionID)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *DepositCardsMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case depositcards.FieldCreateTime:
		return m.CreateTime()
	case depositcards.FieldUpdateTime:
		return m.UpdateTime()
	case depositcards.FieldCardID:
		return m.CardID()
	case depositcards.FieldAccountIban:
		return m.AccountIban()
	case depositcards.FieldFinContractID:
		return m.FinContractID()
	case depositcards.FieldRrn:
		return m.Rrn()
	case depositcards.FieldExternalID:
		return m.ExternalID()
	case depositcards.FieldProcessingTransactionID:
		return m.ProcessingTransactionID()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *DepositCardsMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case depositcards.FieldCreateTime:
		return m.OldCreateTime(ctx)
	case depositcards.FieldUpdateTime:
		return m.OldUpdateTime(ctx)
	case depositcards.FieldCardID:
		return m.OldCardID(ctx)
	case depositcards.FieldAccountIban:
		return m.OldAccountIban(ctx)
	case depositcards.FieldFinContractID:
		return m.OldFinContractID(ctx)
	case depositcards.FieldRrn:
		return m.OldRrn(ctx)
	case depositcards.FieldExternalID:
		return m.OldExternalID(ctx)
	case depositcards.FieldProcessingTransactionID:
		return m.OldProcessingTransactionID(ctx)
	}
	return nil, fmt.Errorf("unknown DepositCards field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *DepositCardsMutation) SetField(name string, value ent.Value) error {
	switch name {
	case depositcards.FieldCreateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateTime(v)
		return nil
	case depositcards.FieldUpdateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateTime(v)
		return nil
	case depositcards.FieldCardID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCardID(v)
		return nil
	case depositcards.FieldAccountIban:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAccountIban(v)
		return nil
	case depositcards.FieldFinContractID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFinContractID(v)
		return nil
	case depositcards.FieldRrn:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRrn(v)
		return nil
	case depositcards.FieldExternalID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetExternalID(v)
		return nil
	case depositcards.FieldProcessingTransactionID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetProcessingTransactionID(v)
		return nil
	}
	return fmt.Errorf("unknown DepositCards field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *DepositCardsMutation) AddedFields() []string {
	var fields []string
	if m.addprocessing_transaction_id != nil {
		fields = append(fields, depositcards.FieldProcessingTransactionID)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *DepositCardsMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case depositcards.FieldProcessingTransactionID:
		return m.AddedProcessingTransactionID()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *DepositCardsMutation) AddField(name string, value ent.Value) error {
	switch name {
	case depositcards.FieldProcessingTransactionID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddProcessingTransactionID(v)
		return nil
	}
	return fmt.Errorf("unknown DepositCards numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *DepositCardsMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(depositcards.FieldCreateTime) {
		fields = append(fields, depositcards.FieldCreateTime)
	}
	if m.FieldCleared(depositcards.FieldUpdateTime) {
		fields = append(fields, depositcards.FieldUpdateTime)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *DepositCardsMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *DepositCardsMutation) ClearField(name string) error {
	switch name {
	case depositcards.FieldCreateTime:
		m.ClearCreateTime()
		return nil
	case depositcards.FieldUpdateTime:
		m.ClearUpdateTime()
		return nil
	}
	return fmt.Errorf("unknown DepositCards nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *DepositCardsMutation) ResetField(name string) error {
	switch name {
	case depositcards.FieldCreateTime:
		m.ResetCreateTime()
		return nil
	case depositcards.FieldUpdateTime:
		m.ResetUpdateTime()
		return nil
	case depositcards.FieldCardID:
		m.ResetCardID()
		return nil
	case depositcards.FieldAccountIban:
		m.ResetAccountIban()
		return nil
	case depositcards.FieldFinContractID:
		m.ResetFinContractID()
		return nil
	case depositcards.FieldRrn:
		m.ResetRrn()
		return nil
	case depositcards.FieldExternalID:
		m.ResetExternalID()
		return nil
	case depositcards.FieldProcessingTransactionID:
		m.ResetProcessingTransactionID()
		return nil
	}
	return fmt.Errorf("unknown DepositCards field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *DepositCardsMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.application != nil {
		edges = append(edges, depositcards.EdgeApplication)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *DepositCardsMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case depositcards.EdgeApplication:
		if id := m.application; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *DepositCardsMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *DepositCardsMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *DepositCardsMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedapplication {
		edges = append(edges, depositcards.EdgeApplication)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *DepositCardsMutation) EdgeCleared(name string) bool {
	switch name {
	case depositcards.EdgeApplication:
		return m.clearedapplication
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *DepositCardsMutation) ClearEdge(name string) error {
	switch name {
	case depositcards.EdgeApplication:
		m.ClearApplication()
		return nil
	}
	return fmt.Errorf("unknown DepositCards unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *DepositCardsMutation) ResetEdge(name string) error {
	switch name {
	case depositcards.EdgeApplication:
		m.ResetApplication()
		return nil
	}
	return fmt.Errorf("unknown DepositCards edge %s", name)
}

// DepositProductMutation represents an operation that mutates the DepositProduct nodes in the graph.
type DepositProductMutation struct {
	config
	op                    Op
	typ                   string
	id                    *uint
	product_code          *string
	colvir_product_code   *string
	name                  *string
	max_profit_rate       *decimal.Decimal
	is_active             *bool
	is_replenishable      *bool
	replenishable_days    *uint
	addreplenishable_days *int
	clearedFields         map[string]struct{}
	currency              map[uint]struct{}
	removedcurrency       map[uint]struct{}
	clearedcurrency       bool
	done                  bool
	oldValue              func(context.Context) (*DepositProduct, error)
	predicates            []predicate.DepositProduct
}

var _ ent.Mutation = (*DepositProductMutation)(nil)

// depositproductOption allows management of the mutation configuration using functional options.
type depositproductOption func(*DepositProductMutation)

// newDepositProductMutation creates new mutation for the DepositProduct entity.
func newDepositProductMutation(c config, op Op, opts ...depositproductOption) *DepositProductMutation {
	m := &DepositProductMutation{
		config:        c,
		op:            op,
		typ:           TypeDepositProduct,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withDepositProductID sets the ID field of the mutation.
func withDepositProductID(id uint) depositproductOption {
	return func(m *DepositProductMutation) {
		var (
			err   error
			once  sync.Once
			value *DepositProduct
		)
		m.oldValue = func(ctx context.Context) (*DepositProduct, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().DepositProduct.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withDepositProduct sets the old DepositProduct of the mutation.
func withDepositProduct(node *DepositProduct) depositproductOption {
	return func(m *DepositProductMutation) {
		m.oldValue = func(context.Context) (*DepositProduct, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m DepositProductMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m DepositProductMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of DepositProduct entities.
func (m *DepositProductMutation) SetID(id uint) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *DepositProductMutation) ID() (id uint, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *DepositProductMutation) IDs(ctx context.Context) ([]uint, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uint{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().DepositProduct.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetProductCode sets the "product_code" field.
func (m *DepositProductMutation) SetProductCode(s string) {
	m.product_code = &s
}

// ProductCode returns the value of the "product_code" field in the mutation.
func (m *DepositProductMutation) ProductCode() (r string, exists bool) {
	v := m.product_code
	if v == nil {
		return
	}
	return *v, true
}

// OldProductCode returns the old "product_code" field's value of the DepositProduct entity.
// If the DepositProduct object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositProductMutation) OldProductCode(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldProductCode is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldProductCode requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldProductCode: %w", err)
	}
	return oldValue.ProductCode, nil
}

// ResetProductCode resets all changes to the "product_code" field.
func (m *DepositProductMutation) ResetProductCode() {
	m.product_code = nil
}

// SetColvirProductCode sets the "colvir_product_code" field.
func (m *DepositProductMutation) SetColvirProductCode(s string) {
	m.colvir_product_code = &s
}

// ColvirProductCode returns the value of the "colvir_product_code" field in the mutation.
func (m *DepositProductMutation) ColvirProductCode() (r string, exists bool) {
	v := m.colvir_product_code
	if v == nil {
		return
	}
	return *v, true
}

// OldColvirProductCode returns the old "colvir_product_code" field's value of the DepositProduct entity.
// If the DepositProduct object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositProductMutation) OldColvirProductCode(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldColvirProductCode is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldColvirProductCode requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldColvirProductCode: %w", err)
	}
	return oldValue.ColvirProductCode, nil
}

// ResetColvirProductCode resets all changes to the "colvir_product_code" field.
func (m *DepositProductMutation) ResetColvirProductCode() {
	m.colvir_product_code = nil
}

// SetName sets the "name" field.
func (m *DepositProductMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *DepositProductMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the DepositProduct entity.
// If the DepositProduct object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositProductMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *DepositProductMutation) ResetName() {
	m.name = nil
}

// SetMaxProfitRate sets the "max_profit_rate" field.
func (m *DepositProductMutation) SetMaxProfitRate(d decimal.Decimal) {
	m.max_profit_rate = &d
}

// MaxProfitRate returns the value of the "max_profit_rate" field in the mutation.
func (m *DepositProductMutation) MaxProfitRate() (r decimal.Decimal, exists bool) {
	v := m.max_profit_rate
	if v == nil {
		return
	}
	return *v, true
}

// OldMaxProfitRate returns the old "max_profit_rate" field's value of the DepositProduct entity.
// If the DepositProduct object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositProductMutation) OldMaxProfitRate(ctx context.Context) (v decimal.Decimal, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMaxProfitRate is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMaxProfitRate requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMaxProfitRate: %w", err)
	}
	return oldValue.MaxProfitRate, nil
}

// ResetMaxProfitRate resets all changes to the "max_profit_rate" field.
func (m *DepositProductMutation) ResetMaxProfitRate() {
	m.max_profit_rate = nil
}

// SetIsActive sets the "is_active" field.
func (m *DepositProductMutation) SetIsActive(b bool) {
	m.is_active = &b
}

// IsActive returns the value of the "is_active" field in the mutation.
func (m *DepositProductMutation) IsActive() (r bool, exists bool) {
	v := m.is_active
	if v == nil {
		return
	}
	return *v, true
}

// OldIsActive returns the old "is_active" field's value of the DepositProduct entity.
// If the DepositProduct object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositProductMutation) OldIsActive(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsActive is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsActive requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsActive: %w", err)
	}
	return oldValue.IsActive, nil
}

// ResetIsActive resets all changes to the "is_active" field.
func (m *DepositProductMutation) ResetIsActive() {
	m.is_active = nil
}

// SetIsReplenishable sets the "is_replenishable" field.
func (m *DepositProductMutation) SetIsReplenishable(b bool) {
	m.is_replenishable = &b
}

// IsReplenishable returns the value of the "is_replenishable" field in the mutation.
func (m *DepositProductMutation) IsReplenishable() (r bool, exists bool) {
	v := m.is_replenishable
	if v == nil {
		return
	}
	return *v, true
}

// OldIsReplenishable returns the old "is_replenishable" field's value of the DepositProduct entity.
// If the DepositProduct object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositProductMutation) OldIsReplenishable(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsReplenishable is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsReplenishable requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsReplenishable: %w", err)
	}
	return oldValue.IsReplenishable, nil
}

// ResetIsReplenishable resets all changes to the "is_replenishable" field.
func (m *DepositProductMutation) ResetIsReplenishable() {
	m.is_replenishable = nil
}

// SetReplenishableDays sets the "replenishable_days" field.
func (m *DepositProductMutation) SetReplenishableDays(u uint) {
	m.replenishable_days = &u
	m.addreplenishable_days = nil
}

// ReplenishableDays returns the value of the "replenishable_days" field in the mutation.
func (m *DepositProductMutation) ReplenishableDays() (r uint, exists bool) {
	v := m.replenishable_days
	if v == nil {
		return
	}
	return *v, true
}

// OldReplenishableDays returns the old "replenishable_days" field's value of the DepositProduct entity.
// If the DepositProduct object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DepositProductMutation) OldReplenishableDays(ctx context.Context) (v uint, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldReplenishableDays is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldReplenishableDays requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldReplenishableDays: %w", err)
	}
	return oldValue.ReplenishableDays, nil
}

// AddReplenishableDays adds u to the "replenishable_days" field.
func (m *DepositProductMutation) AddReplenishableDays(u int) {
	if m.addreplenishable_days != nil {
		*m.addreplenishable_days += u
	} else {
		m.addreplenishable_days = &u
	}
}

// AddedReplenishableDays returns the value that was added to the "replenishable_days" field in this mutation.
func (m *DepositProductMutation) AddedReplenishableDays() (r int, exists bool) {
	v := m.addreplenishable_days
	if v == nil {
		return
	}
	return *v, true
}

// ResetReplenishableDays resets all changes to the "replenishable_days" field.
func (m *DepositProductMutation) ResetReplenishableDays() {
	m.replenishable_days = nil
	m.addreplenishable_days = nil
}

// AddCurrencyIDs adds the "currency" edge to the Currency entity by ids.
func (m *DepositProductMutation) AddCurrencyIDs(ids ...uint) {
	if m.currency == nil {
		m.currency = make(map[uint]struct{})
	}
	for i := range ids {
		m.currency[ids[i]] = struct{}{}
	}
}

// ClearCurrency clears the "currency" edge to the Currency entity.
func (m *DepositProductMutation) ClearCurrency() {
	m.clearedcurrency = true
}

// CurrencyCleared reports if the "currency" edge to the Currency entity was cleared.
func (m *DepositProductMutation) CurrencyCleared() bool {
	return m.clearedcurrency
}

// RemoveCurrencyIDs removes the "currency" edge to the Currency entity by IDs.
func (m *DepositProductMutation) RemoveCurrencyIDs(ids ...uint) {
	if m.removedcurrency == nil {
		m.removedcurrency = make(map[uint]struct{})
	}
	for i := range ids {
		delete(m.currency, ids[i])
		m.removedcurrency[ids[i]] = struct{}{}
	}
}

// RemovedCurrency returns the removed IDs of the "currency" edge to the Currency entity.
func (m *DepositProductMutation) RemovedCurrencyIDs() (ids []uint) {
	for id := range m.removedcurrency {
		ids = append(ids, id)
	}
	return
}

// CurrencyIDs returns the "currency" edge IDs in the mutation.
func (m *DepositProductMutation) CurrencyIDs() (ids []uint) {
	for id := range m.currency {
		ids = append(ids, id)
	}
	return
}

// ResetCurrency resets all changes to the "currency" edge.
func (m *DepositProductMutation) ResetCurrency() {
	m.currency = nil
	m.clearedcurrency = false
	m.removedcurrency = nil
}

// Where appends a list predicates to the DepositProductMutation builder.
func (m *DepositProductMutation) Where(ps ...predicate.DepositProduct) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the DepositProductMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *DepositProductMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.DepositProduct, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *DepositProductMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *DepositProductMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (DepositProduct).
func (m *DepositProductMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *DepositProductMutation) Fields() []string {
	fields := make([]string, 0, 7)
	if m.product_code != nil {
		fields = append(fields, depositproduct.FieldProductCode)
	}
	if m.colvir_product_code != nil {
		fields = append(fields, depositproduct.FieldColvirProductCode)
	}
	if m.name != nil {
		fields = append(fields, depositproduct.FieldName)
	}
	if m.max_profit_rate != nil {
		fields = append(fields, depositproduct.FieldMaxProfitRate)
	}
	if m.is_active != nil {
		fields = append(fields, depositproduct.FieldIsActive)
	}
	if m.is_replenishable != nil {
		fields = append(fields, depositproduct.FieldIsReplenishable)
	}
	if m.replenishable_days != nil {
		fields = append(fields, depositproduct.FieldReplenishableDays)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *DepositProductMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case depositproduct.FieldProductCode:
		return m.ProductCode()
	case depositproduct.FieldColvirProductCode:
		return m.ColvirProductCode()
	case depositproduct.FieldName:
		return m.Name()
	case depositproduct.FieldMaxProfitRate:
		return m.MaxProfitRate()
	case depositproduct.FieldIsActive:
		return m.IsActive()
	case depositproduct.FieldIsReplenishable:
		return m.IsReplenishable()
	case depositproduct.FieldReplenishableDays:
		return m.ReplenishableDays()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *DepositProductMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case depositproduct.FieldProductCode:
		return m.OldProductCode(ctx)
	case depositproduct.FieldColvirProductCode:
		return m.OldColvirProductCode(ctx)
	case depositproduct.FieldName:
		return m.OldName(ctx)
	case depositproduct.FieldMaxProfitRate:
		return m.OldMaxProfitRate(ctx)
	case depositproduct.FieldIsActive:
		return m.OldIsActive(ctx)
	case depositproduct.FieldIsReplenishable:
		return m.OldIsReplenishable(ctx)
	case depositproduct.FieldReplenishableDays:
		return m.OldReplenishableDays(ctx)
	}
	return nil, fmt.Errorf("unknown DepositProduct field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *DepositProductMutation) SetField(name string, value ent.Value) error {
	switch name {
	case depositproduct.FieldProductCode:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetProductCode(v)
		return nil
	case depositproduct.FieldColvirProductCode:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetColvirProductCode(v)
		return nil
	case depositproduct.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case depositproduct.FieldMaxProfitRate:
		v, ok := value.(decimal.Decimal)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMaxProfitRate(v)
		return nil
	case depositproduct.FieldIsActive:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsActive(v)
		return nil
	case depositproduct.FieldIsReplenishable:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsReplenishable(v)
		return nil
	case depositproduct.FieldReplenishableDays:
		v, ok := value.(uint)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetReplenishableDays(v)
		return nil
	}
	return fmt.Errorf("unknown DepositProduct field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *DepositProductMutation) AddedFields() []string {
	var fields []string
	if m.addreplenishable_days != nil {
		fields = append(fields, depositproduct.FieldReplenishableDays)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *DepositProductMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case depositproduct.FieldReplenishableDays:
		return m.AddedReplenishableDays()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *DepositProductMutation) AddField(name string, value ent.Value) error {
	switch name {
	case depositproduct.FieldReplenishableDays:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddReplenishableDays(v)
		return nil
	}
	return fmt.Errorf("unknown DepositProduct numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *DepositProductMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *DepositProductMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *DepositProductMutation) ClearField(name string) error {
	return fmt.Errorf("unknown DepositProduct nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *DepositProductMutation) ResetField(name string) error {
	switch name {
	case depositproduct.FieldProductCode:
		m.ResetProductCode()
		return nil
	case depositproduct.FieldColvirProductCode:
		m.ResetColvirProductCode()
		return nil
	case depositproduct.FieldName:
		m.ResetName()
		return nil
	case depositproduct.FieldMaxProfitRate:
		m.ResetMaxProfitRate()
		return nil
	case depositproduct.FieldIsActive:
		m.ResetIsActive()
		return nil
	case depositproduct.FieldIsReplenishable:
		m.ResetIsReplenishable()
		return nil
	case depositproduct.FieldReplenishableDays:
		m.ResetReplenishableDays()
		return nil
	}
	return fmt.Errorf("unknown DepositProduct field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *DepositProductMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.currency != nil {
		edges = append(edges, depositproduct.EdgeCurrency)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *DepositProductMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case depositproduct.EdgeCurrency:
		ids := make([]ent.Value, 0, len(m.currency))
		for id := range m.currency {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *DepositProductMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	if m.removedcurrency != nil {
		edges = append(edges, depositproduct.EdgeCurrency)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *DepositProductMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case depositproduct.EdgeCurrency:
		ids := make([]ent.Value, 0, len(m.removedcurrency))
		for id := range m.removedcurrency {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *DepositProductMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedcurrency {
		edges = append(edges, depositproduct.EdgeCurrency)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *DepositProductMutation) EdgeCleared(name string) bool {
	switch name {
	case depositproduct.EdgeCurrency:
		return m.clearedcurrency
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *DepositProductMutation) ClearEdge(name string) error {
	switch name {
	}
	return fmt.Errorf("unknown DepositProduct unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *DepositProductMutation) ResetEdge(name string) error {
	switch name {
	case depositproduct.EdgeCurrency:
		m.ResetCurrency()
		return nil
	}
	return fmt.Errorf("unknown DepositProduct edge %s", name)
}

// DocumentMutation represents an operation that mutates the Document nodes in the graph.
type DocumentMutation struct {
	config
	op                    Op
	typ                   string
	id                    *uuid.UUID
	create_time           *time.Time
	update_time           *time.Time
	_type                 *document.Type
	doc_id                *uuid.UUID
	number                *string
	signed_doc_id         *uuid.UUID
	document_signing_date *time.Time
	is_signable           *bool
	clearedFields         map[string]struct{}
	application           *uuid.UUID
	clearedapplication    bool
	done                  bool
	oldValue              func(context.Context) (*Document, error)
	predicates            []predicate.Document
}

var _ ent.Mutation = (*DocumentMutation)(nil)

// documentOption allows management of the mutation configuration using functional options.
type documentOption func(*DocumentMutation)

// newDocumentMutation creates new mutation for the Document entity.
func newDocumentMutation(c config, op Op, opts ...documentOption) *DocumentMutation {
	m := &DocumentMutation{
		config:        c,
		op:            op,
		typ:           TypeDocument,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withDocumentID sets the ID field of the mutation.
func withDocumentID(id uuid.UUID) documentOption {
	return func(m *DocumentMutation) {
		var (
			err   error
			once  sync.Once
			value *Document
		)
		m.oldValue = func(ctx context.Context) (*Document, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Document.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withDocument sets the old Document of the mutation.
func withDocument(node *Document) documentOption {
	return func(m *DocumentMutation) {
		m.oldValue = func(context.Context) (*Document, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m DocumentMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m DocumentMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Document entities.
func (m *DocumentMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *DocumentMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *DocumentMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Document.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreateTime sets the "create_time" field.
func (m *DocumentMutation) SetCreateTime(t time.Time) {
	m.create_time = &t
}

// CreateTime returns the value of the "create_time" field in the mutation.
func (m *DocumentMutation) CreateTime() (r time.Time, exists bool) {
	v := m.create_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateTime returns the old "create_time" field's value of the Document entity.
// If the Document object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DocumentMutation) OldCreateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateTime: %w", err)
	}
	return oldValue.CreateTime, nil
}

// ClearCreateTime clears the value of the "create_time" field.
func (m *DocumentMutation) ClearCreateTime() {
	m.create_time = nil
	m.clearedFields[document.FieldCreateTime] = struct{}{}
}

// CreateTimeCleared returns if the "create_time" field was cleared in this mutation.
func (m *DocumentMutation) CreateTimeCleared() bool {
	_, ok := m.clearedFields[document.FieldCreateTime]
	return ok
}

// ResetCreateTime resets all changes to the "create_time" field.
func (m *DocumentMutation) ResetCreateTime() {
	m.create_time = nil
	delete(m.clearedFields, document.FieldCreateTime)
}

// SetUpdateTime sets the "update_time" field.
func (m *DocumentMutation) SetUpdateTime(t time.Time) {
	m.update_time = &t
}

// UpdateTime returns the value of the "update_time" field in the mutation.
func (m *DocumentMutation) UpdateTime() (r time.Time, exists bool) {
	v := m.update_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateTime returns the old "update_time" field's value of the Document entity.
// If the Document object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DocumentMutation) OldUpdateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateTime: %w", err)
	}
	return oldValue.UpdateTime, nil
}

// ClearUpdateTime clears the value of the "update_time" field.
func (m *DocumentMutation) ClearUpdateTime() {
	m.update_time = nil
	m.clearedFields[document.FieldUpdateTime] = struct{}{}
}

// UpdateTimeCleared returns if the "update_time" field was cleared in this mutation.
func (m *DocumentMutation) UpdateTimeCleared() bool {
	_, ok := m.clearedFields[document.FieldUpdateTime]
	return ok
}

// ResetUpdateTime resets all changes to the "update_time" field.
func (m *DocumentMutation) ResetUpdateTime() {
	m.update_time = nil
	delete(m.clearedFields, document.FieldUpdateTime)
}

// SetDepositApplicationID sets the "deposit_application_id" field.
func (m *DocumentMutation) SetDepositApplicationID(u uuid.UUID) {
	m.application = &u
}

// DepositApplicationID returns the value of the "deposit_application_id" field in the mutation.
func (m *DocumentMutation) DepositApplicationID() (r uuid.UUID, exists bool) {
	v := m.application
	if v == nil {
		return
	}
	return *v, true
}

// OldDepositApplicationID returns the old "deposit_application_id" field's value of the Document entity.
// If the Document object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DocumentMutation) OldDepositApplicationID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDepositApplicationID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDepositApplicationID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDepositApplicationID: %w", err)
	}
	return oldValue.DepositApplicationID, nil
}

// ResetDepositApplicationID resets all changes to the "deposit_application_id" field.
func (m *DocumentMutation) ResetDepositApplicationID() {
	m.application = nil
}

// SetType sets the "type" field.
func (m *DocumentMutation) SetType(d document.Type) {
	m._type = &d
}

// GetType returns the value of the "type" field in the mutation.
func (m *DocumentMutation) GetType() (r document.Type, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the Document entity.
// If the Document object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DocumentMutation) OldType(ctx context.Context) (v document.Type, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *DocumentMutation) ResetType() {
	m._type = nil
}

// SetDocID sets the "doc_id" field.
func (m *DocumentMutation) SetDocID(u uuid.UUID) {
	m.doc_id = &u
}

// DocID returns the value of the "doc_id" field in the mutation.
func (m *DocumentMutation) DocID() (r uuid.UUID, exists bool) {
	v := m.doc_id
	if v == nil {
		return
	}
	return *v, true
}

// OldDocID returns the old "doc_id" field's value of the Document entity.
// If the Document object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DocumentMutation) OldDocID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDocID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDocID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDocID: %w", err)
	}
	return oldValue.DocID, nil
}

// ClearDocID clears the value of the "doc_id" field.
func (m *DocumentMutation) ClearDocID() {
	m.doc_id = nil
	m.clearedFields[document.FieldDocID] = struct{}{}
}

// DocIDCleared returns if the "doc_id" field was cleared in this mutation.
func (m *DocumentMutation) DocIDCleared() bool {
	_, ok := m.clearedFields[document.FieldDocID]
	return ok
}

// ResetDocID resets all changes to the "doc_id" field.
func (m *DocumentMutation) ResetDocID() {
	m.doc_id = nil
	delete(m.clearedFields, document.FieldDocID)
}

// SetNumber sets the "number" field.
func (m *DocumentMutation) SetNumber(s string) {
	m.number = &s
}

// Number returns the value of the "number" field in the mutation.
func (m *DocumentMutation) Number() (r string, exists bool) {
	v := m.number
	if v == nil {
		return
	}
	return *v, true
}

// OldNumber returns the old "number" field's value of the Document entity.
// If the Document object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DocumentMutation) OldNumber(ctx context.Context) (v *string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldNumber is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldNumber requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldNumber: %w", err)
	}
	return oldValue.Number, nil
}

// ClearNumber clears the value of the "number" field.
func (m *DocumentMutation) ClearNumber() {
	m.number = nil
	m.clearedFields[document.FieldNumber] = struct{}{}
}

// NumberCleared returns if the "number" field was cleared in this mutation.
func (m *DocumentMutation) NumberCleared() bool {
	_, ok := m.clearedFields[document.FieldNumber]
	return ok
}

// ResetNumber resets all changes to the "number" field.
func (m *DocumentMutation) ResetNumber() {
	m.number = nil
	delete(m.clearedFields, document.FieldNumber)
}

// SetSignedDocID sets the "signed_doc_id" field.
func (m *DocumentMutation) SetSignedDocID(u uuid.UUID) {
	m.signed_doc_id = &u
}

// SignedDocID returns the value of the "signed_doc_id" field in the mutation.
func (m *DocumentMutation) SignedDocID() (r uuid.UUID, exists bool) {
	v := m.signed_doc_id
	if v == nil {
		return
	}
	return *v, true
}

// OldSignedDocID returns the old "signed_doc_id" field's value of the Document entity.
// If the Document object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DocumentMutation) OldSignedDocID(ctx context.Context) (v *uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSignedDocID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSignedDocID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSignedDocID: %w", err)
	}
	return oldValue.SignedDocID, nil
}

// ClearSignedDocID clears the value of the "signed_doc_id" field.
func (m *DocumentMutation) ClearSignedDocID() {
	m.signed_doc_id = nil
	m.clearedFields[document.FieldSignedDocID] = struct{}{}
}

// SignedDocIDCleared returns if the "signed_doc_id" field was cleared in this mutation.
func (m *DocumentMutation) SignedDocIDCleared() bool {
	_, ok := m.clearedFields[document.FieldSignedDocID]
	return ok
}

// ResetSignedDocID resets all changes to the "signed_doc_id" field.
func (m *DocumentMutation) ResetSignedDocID() {
	m.signed_doc_id = nil
	delete(m.clearedFields, document.FieldSignedDocID)
}

// SetDocumentSigningDate sets the "document_signing_date" field.
func (m *DocumentMutation) SetDocumentSigningDate(t time.Time) {
	m.document_signing_date = &t
}

// DocumentSigningDate returns the value of the "document_signing_date" field in the mutation.
func (m *DocumentMutation) DocumentSigningDate() (r time.Time, exists bool) {
	v := m.document_signing_date
	if v == nil {
		return
	}
	return *v, true
}

// OldDocumentSigningDate returns the old "document_signing_date" field's value of the Document entity.
// If the Document object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DocumentMutation) OldDocumentSigningDate(ctx context.Context) (v *time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDocumentSigningDate is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDocumentSigningDate requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDocumentSigningDate: %w", err)
	}
	return oldValue.DocumentSigningDate, nil
}

// ClearDocumentSigningDate clears the value of the "document_signing_date" field.
func (m *DocumentMutation) ClearDocumentSigningDate() {
	m.document_signing_date = nil
	m.clearedFields[document.FieldDocumentSigningDate] = struct{}{}
}

// DocumentSigningDateCleared returns if the "document_signing_date" field was cleared in this mutation.
func (m *DocumentMutation) DocumentSigningDateCleared() bool {
	_, ok := m.clearedFields[document.FieldDocumentSigningDate]
	return ok
}

// ResetDocumentSigningDate resets all changes to the "document_signing_date" field.
func (m *DocumentMutation) ResetDocumentSigningDate() {
	m.document_signing_date = nil
	delete(m.clearedFields, document.FieldDocumentSigningDate)
}

// SetIsSignable sets the "is_signable" field.
func (m *DocumentMutation) SetIsSignable(b bool) {
	m.is_signable = &b
}

// IsSignable returns the value of the "is_signable" field in the mutation.
func (m *DocumentMutation) IsSignable() (r bool, exists bool) {
	v := m.is_signable
	if v == nil {
		return
	}
	return *v, true
}

// OldIsSignable returns the old "is_signable" field's value of the Document entity.
// If the Document object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *DocumentMutation) OldIsSignable(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsSignable is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsSignable requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsSignable: %w", err)
	}
	return oldValue.IsSignable, nil
}

// ClearIsSignable clears the value of the "is_signable" field.
func (m *DocumentMutation) ClearIsSignable() {
	m.is_signable = nil
	m.clearedFields[document.FieldIsSignable] = struct{}{}
}

// IsSignableCleared returns if the "is_signable" field was cleared in this mutation.
func (m *DocumentMutation) IsSignableCleared() bool {
	_, ok := m.clearedFields[document.FieldIsSignable]
	return ok
}

// ResetIsSignable resets all changes to the "is_signable" field.
func (m *DocumentMutation) ResetIsSignable() {
	m.is_signable = nil
	delete(m.clearedFields, document.FieldIsSignable)
}

// SetApplicationID sets the "application" edge to the DepositApplication entity by id.
func (m *DocumentMutation) SetApplicationID(id uuid.UUID) {
	m.application = &id
}

// ClearApplication clears the "application" edge to the DepositApplication entity.
func (m *DocumentMutation) ClearApplication() {
	m.clearedapplication = true
	m.clearedFields[document.FieldDepositApplicationID] = struct{}{}
}

// ApplicationCleared reports if the "application" edge to the DepositApplication entity was cleared.
func (m *DocumentMutation) ApplicationCleared() bool {
	return m.clearedapplication
}

// ApplicationID returns the "application" edge ID in the mutation.
func (m *DocumentMutation) ApplicationID() (id uuid.UUID, exists bool) {
	if m.application != nil {
		return *m.application, true
	}
	return
}

// ApplicationIDs returns the "application" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// ApplicationID instead. It exists only for internal usage by the builders.
func (m *DocumentMutation) ApplicationIDs() (ids []uuid.UUID) {
	if id := m.application; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetApplication resets all changes to the "application" edge.
func (m *DocumentMutation) ResetApplication() {
	m.application = nil
	m.clearedapplication = false
}

// Where appends a list predicates to the DocumentMutation builder.
func (m *DocumentMutation) Where(ps ...predicate.Document) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the DocumentMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *DocumentMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Document, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *DocumentMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *DocumentMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Document).
func (m *DocumentMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *DocumentMutation) Fields() []string {
	fields := make([]string, 0, 9)
	if m.create_time != nil {
		fields = append(fields, document.FieldCreateTime)
	}
	if m.update_time != nil {
		fields = append(fields, document.FieldUpdateTime)
	}
	if m.application != nil {
		fields = append(fields, document.FieldDepositApplicationID)
	}
	if m._type != nil {
		fields = append(fields, document.FieldType)
	}
	if m.doc_id != nil {
		fields = append(fields, document.FieldDocID)
	}
	if m.number != nil {
		fields = append(fields, document.FieldNumber)
	}
	if m.signed_doc_id != nil {
		fields = append(fields, document.FieldSignedDocID)
	}
	if m.document_signing_date != nil {
		fields = append(fields, document.FieldDocumentSigningDate)
	}
	if m.is_signable != nil {
		fields = append(fields, document.FieldIsSignable)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *DocumentMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case document.FieldCreateTime:
		return m.CreateTime()
	case document.FieldUpdateTime:
		return m.UpdateTime()
	case document.FieldDepositApplicationID:
		return m.DepositApplicationID()
	case document.FieldType:
		return m.GetType()
	case document.FieldDocID:
		return m.DocID()
	case document.FieldNumber:
		return m.Number()
	case document.FieldSignedDocID:
		return m.SignedDocID()
	case document.FieldDocumentSigningDate:
		return m.DocumentSigningDate()
	case document.FieldIsSignable:
		return m.IsSignable()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *DocumentMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case document.FieldCreateTime:
		return m.OldCreateTime(ctx)
	case document.FieldUpdateTime:
		return m.OldUpdateTime(ctx)
	case document.FieldDepositApplicationID:
		return m.OldDepositApplicationID(ctx)
	case document.FieldType:
		return m.OldType(ctx)
	case document.FieldDocID:
		return m.OldDocID(ctx)
	case document.FieldNumber:
		return m.OldNumber(ctx)
	case document.FieldSignedDocID:
		return m.OldSignedDocID(ctx)
	case document.FieldDocumentSigningDate:
		return m.OldDocumentSigningDate(ctx)
	case document.FieldIsSignable:
		return m.OldIsSignable(ctx)
	}
	return nil, fmt.Errorf("unknown Document field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *DocumentMutation) SetField(name string, value ent.Value) error {
	switch name {
	case document.FieldCreateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateTime(v)
		return nil
	case document.FieldUpdateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateTime(v)
		return nil
	case document.FieldDepositApplicationID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDepositApplicationID(v)
		return nil
	case document.FieldType:
		v, ok := value.(document.Type)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case document.FieldDocID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDocID(v)
		return nil
	case document.FieldNumber:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetNumber(v)
		return nil
	case document.FieldSignedDocID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSignedDocID(v)
		return nil
	case document.FieldDocumentSigningDate:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDocumentSigningDate(v)
		return nil
	case document.FieldIsSignable:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsSignable(v)
		return nil
	}
	return fmt.Errorf("unknown Document field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *DocumentMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *DocumentMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *DocumentMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Document numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *DocumentMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(document.FieldCreateTime) {
		fields = append(fields, document.FieldCreateTime)
	}
	if m.FieldCleared(document.FieldUpdateTime) {
		fields = append(fields, document.FieldUpdateTime)
	}
	if m.FieldCleared(document.FieldDocID) {
		fields = append(fields, document.FieldDocID)
	}
	if m.FieldCleared(document.FieldNumber) {
		fields = append(fields, document.FieldNumber)
	}
	if m.FieldCleared(document.FieldSignedDocID) {
		fields = append(fields, document.FieldSignedDocID)
	}
	if m.FieldCleared(document.FieldDocumentSigningDate) {
		fields = append(fields, document.FieldDocumentSigningDate)
	}
	if m.FieldCleared(document.FieldIsSignable) {
		fields = append(fields, document.FieldIsSignable)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *DocumentMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *DocumentMutation) ClearField(name string) error {
	switch name {
	case document.FieldCreateTime:
		m.ClearCreateTime()
		return nil
	case document.FieldUpdateTime:
		m.ClearUpdateTime()
		return nil
	case document.FieldDocID:
		m.ClearDocID()
		return nil
	case document.FieldNumber:
		m.ClearNumber()
		return nil
	case document.FieldSignedDocID:
		m.ClearSignedDocID()
		return nil
	case document.FieldDocumentSigningDate:
		m.ClearDocumentSigningDate()
		return nil
	case document.FieldIsSignable:
		m.ClearIsSignable()
		return nil
	}
	return fmt.Errorf("unknown Document nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *DocumentMutation) ResetField(name string) error {
	switch name {
	case document.FieldCreateTime:
		m.ResetCreateTime()
		return nil
	case document.FieldUpdateTime:
		m.ResetUpdateTime()
		return nil
	case document.FieldDepositApplicationID:
		m.ResetDepositApplicationID()
		return nil
	case document.FieldType:
		m.ResetType()
		return nil
	case document.FieldDocID:
		m.ResetDocID()
		return nil
	case document.FieldNumber:
		m.ResetNumber()
		return nil
	case document.FieldSignedDocID:
		m.ResetSignedDocID()
		return nil
	case document.FieldDocumentSigningDate:
		m.ResetDocumentSigningDate()
		return nil
	case document.FieldIsSignable:
		m.ResetIsSignable()
		return nil
	}
	return fmt.Errorf("unknown Document field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *DocumentMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.application != nil {
		edges = append(edges, document.EdgeApplication)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *DocumentMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case document.EdgeApplication:
		if id := m.application; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *DocumentMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *DocumentMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *DocumentMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedapplication {
		edges = append(edges, document.EdgeApplication)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *DocumentMutation) EdgeCleared(name string) bool {
	switch name {
	case document.EdgeApplication:
		return m.clearedapplication
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *DocumentMutation) ClearEdge(name string) error {
	switch name {
	case document.EdgeApplication:
		m.ClearApplication()
		return nil
	}
	return fmt.Errorf("unknown Document unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *DocumentMutation) ResetEdge(name string) error {
	switch name {
	case document.EdgeApplication:
		m.ResetApplication()
		return nil
	}
	return fmt.Errorf("unknown Document edge %s", name)
}

// HealthMutation represents an operation that mutates the Health nodes in the graph.
type HealthMutation struct {
	config
	op            Op
	typ           string
	id            *uuid.UUID
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Health, error)
	predicates    []predicate.Health
}

var _ ent.Mutation = (*HealthMutation)(nil)

// healthOption allows management of the mutation configuration using functional options.
type healthOption func(*HealthMutation)

// newHealthMutation creates new mutation for the Health entity.
func newHealthMutation(c config, op Op, opts ...healthOption) *HealthMutation {
	m := &HealthMutation{
		config:        c,
		op:            op,
		typ:           TypeHealth,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withHealthID sets the ID field of the mutation.
func withHealthID(id uuid.UUID) healthOption {
	return func(m *HealthMutation) {
		var (
			err   error
			once  sync.Once
			value *Health
		)
		m.oldValue = func(ctx context.Context) (*Health, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Health.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withHealth sets the old Health of the mutation.
func withHealth(node *Health) healthOption {
	return func(m *HealthMutation) {
		m.oldValue = func(context.Context) (*Health, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m HealthMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m HealthMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Health entities.
func (m *HealthMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *HealthMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *HealthMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Health.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// Where appends a list predicates to the HealthMutation builder.
func (m *HealthMutation) Where(ps ...predicate.Health) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the HealthMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *HealthMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Health, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *HealthMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *HealthMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Health).
func (m *HealthMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *HealthMutation) Fields() []string {
	fields := make([]string, 0, 0)
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *HealthMutation) Field(name string) (ent.Value, bool) {
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *HealthMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	return nil, fmt.Errorf("unknown Health field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *HealthMutation) SetField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Health field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *HealthMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *HealthMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *HealthMutation) AddField(name string, value ent.Value) error {
	return fmt.Errorf("unknown Health numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *HealthMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *HealthMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *HealthMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Health nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *HealthMutation) ResetField(name string) error {
	return fmt.Errorf("unknown Health field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *HealthMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *HealthMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *HealthMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *HealthMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *HealthMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *HealthMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *HealthMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Health unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *HealthMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Health edge %s", name)
}

// ProfitRateMutation represents an operation that mutates the ProfitRate nodes in the graph.
type ProfitRateMutation struct {
	config
	op                              Op
	typ                             string
	id                              *uuid.UUID
	create_time                     *time.Time
	update_time                     *time.Time
	month_term                      *uint32
	addmonth_term                   *int32
	profit_rate                     *decimal.Decimal
	date_from                       *time.Time
	date_to                         *time.Time
	clearedFields                   map[string]struct{}
	currency                        *uint
	clearedcurrency                 bool
	rate_deposit_application        map[uuid.UUID]struct{}
	removedrate_deposit_application map[uuid.UUID]struct{}
	clearedrate_deposit_application bool
	done                            bool
	oldValue                        func(context.Context) (*ProfitRate, error)
	predicates                      []predicate.ProfitRate
}

var _ ent.Mutation = (*ProfitRateMutation)(nil)

// profitrateOption allows management of the mutation configuration using functional options.
type profitrateOption func(*ProfitRateMutation)

// newProfitRateMutation creates new mutation for the ProfitRate entity.
func newProfitRateMutation(c config, op Op, opts ...profitrateOption) *ProfitRateMutation {
	m := &ProfitRateMutation{
		config:        c,
		op:            op,
		typ:           TypeProfitRate,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withProfitRateID sets the ID field of the mutation.
func withProfitRateID(id uuid.UUID) profitrateOption {
	return func(m *ProfitRateMutation) {
		var (
			err   error
			once  sync.Once
			value *ProfitRate
		)
		m.oldValue = func(ctx context.Context) (*ProfitRate, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().ProfitRate.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withProfitRate sets the old ProfitRate of the mutation.
func withProfitRate(node *ProfitRate) profitrateOption {
	return func(m *ProfitRateMutation) {
		m.oldValue = func(context.Context) (*ProfitRate, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m ProfitRateMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m ProfitRateMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of ProfitRate entities.
func (m *ProfitRateMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *ProfitRateMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *ProfitRateMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().ProfitRate.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreateTime sets the "create_time" field.
func (m *ProfitRateMutation) SetCreateTime(t time.Time) {
	m.create_time = &t
}

// CreateTime returns the value of the "create_time" field in the mutation.
func (m *ProfitRateMutation) CreateTime() (r time.Time, exists bool) {
	v := m.create_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreateTime returns the old "create_time" field's value of the ProfitRate entity.
// If the ProfitRate object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ProfitRateMutation) OldCreateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreateTime: %w", err)
	}
	return oldValue.CreateTime, nil
}

// ClearCreateTime clears the value of the "create_time" field.
func (m *ProfitRateMutation) ClearCreateTime() {
	m.create_time = nil
	m.clearedFields[profitrate.FieldCreateTime] = struct{}{}
}

// CreateTimeCleared returns if the "create_time" field was cleared in this mutation.
func (m *ProfitRateMutation) CreateTimeCleared() bool {
	_, ok := m.clearedFields[profitrate.FieldCreateTime]
	return ok
}

// ResetCreateTime resets all changes to the "create_time" field.
func (m *ProfitRateMutation) ResetCreateTime() {
	m.create_time = nil
	delete(m.clearedFields, profitrate.FieldCreateTime)
}

// SetUpdateTime sets the "update_time" field.
func (m *ProfitRateMutation) SetUpdateTime(t time.Time) {
	m.update_time = &t
}

// UpdateTime returns the value of the "update_time" field in the mutation.
func (m *ProfitRateMutation) UpdateTime() (r time.Time, exists bool) {
	v := m.update_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdateTime returns the old "update_time" field's value of the ProfitRate entity.
// If the ProfitRate object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ProfitRateMutation) OldUpdateTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdateTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdateTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdateTime: %w", err)
	}
	return oldValue.UpdateTime, nil
}

// ClearUpdateTime clears the value of the "update_time" field.
func (m *ProfitRateMutation) ClearUpdateTime() {
	m.update_time = nil
	m.clearedFields[profitrate.FieldUpdateTime] = struct{}{}
}

// UpdateTimeCleared returns if the "update_time" field was cleared in this mutation.
func (m *ProfitRateMutation) UpdateTimeCleared() bool {
	_, ok := m.clearedFields[profitrate.FieldUpdateTime]
	return ok
}

// ResetUpdateTime resets all changes to the "update_time" field.
func (m *ProfitRateMutation) ResetUpdateTime() {
	m.update_time = nil
	delete(m.clearedFields, profitrate.FieldUpdateTime)
}

// SetCurrencyID sets the "currency_id" field.
func (m *ProfitRateMutation) SetCurrencyID(u uint) {
	m.currency = &u
}

// CurrencyID returns the value of the "currency_id" field in the mutation.
func (m *ProfitRateMutation) CurrencyID() (r uint, exists bool) {
	v := m.currency
	if v == nil {
		return
	}
	return *v, true
}

// OldCurrencyID returns the old "currency_id" field's value of the ProfitRate entity.
// If the ProfitRate object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ProfitRateMutation) OldCurrencyID(ctx context.Context) (v uint, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCurrencyID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCurrencyID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCurrencyID: %w", err)
	}
	return oldValue.CurrencyID, nil
}

// ResetCurrencyID resets all changes to the "currency_id" field.
func (m *ProfitRateMutation) ResetCurrencyID() {
	m.currency = nil
}

// SetMonthTerm sets the "month_term" field.
func (m *ProfitRateMutation) SetMonthTerm(u uint32) {
	m.month_term = &u
	m.addmonth_term = nil
}

// MonthTerm returns the value of the "month_term" field in the mutation.
func (m *ProfitRateMutation) MonthTerm() (r uint32, exists bool) {
	v := m.month_term
	if v == nil {
		return
	}
	return *v, true
}

// OldMonthTerm returns the old "month_term" field's value of the ProfitRate entity.
// If the ProfitRate object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ProfitRateMutation) OldMonthTerm(ctx context.Context) (v uint32, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMonthTerm is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMonthTerm requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMonthTerm: %w", err)
	}
	return oldValue.MonthTerm, nil
}

// AddMonthTerm adds u to the "month_term" field.
func (m *ProfitRateMutation) AddMonthTerm(u int32) {
	if m.addmonth_term != nil {
		*m.addmonth_term += u
	} else {
		m.addmonth_term = &u
	}
}

// AddedMonthTerm returns the value that was added to the "month_term" field in this mutation.
func (m *ProfitRateMutation) AddedMonthTerm() (r int32, exists bool) {
	v := m.addmonth_term
	if v == nil {
		return
	}
	return *v, true
}

// ResetMonthTerm resets all changes to the "month_term" field.
func (m *ProfitRateMutation) ResetMonthTerm() {
	m.month_term = nil
	m.addmonth_term = nil
}

// SetProfitRate sets the "profit_rate" field.
func (m *ProfitRateMutation) SetProfitRate(d decimal.Decimal) {
	m.profit_rate = &d
}

// ProfitRate returns the value of the "profit_rate" field in the mutation.
func (m *ProfitRateMutation) ProfitRate() (r decimal.Decimal, exists bool) {
	v := m.profit_rate
	if v == nil {
		return
	}
	return *v, true
}

// OldProfitRate returns the old "profit_rate" field's value of the ProfitRate entity.
// If the ProfitRate object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ProfitRateMutation) OldProfitRate(ctx context.Context) (v decimal.Decimal, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldProfitRate is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldProfitRate requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldProfitRate: %w", err)
	}
	return oldValue.ProfitRate, nil
}

// ResetProfitRate resets all changes to the "profit_rate" field.
func (m *ProfitRateMutation) ResetProfitRate() {
	m.profit_rate = nil
}

// SetDateFrom sets the "date_from" field.
func (m *ProfitRateMutation) SetDateFrom(t time.Time) {
	m.date_from = &t
}

// DateFrom returns the value of the "date_from" field in the mutation.
func (m *ProfitRateMutation) DateFrom() (r time.Time, exists bool) {
	v := m.date_from
	if v == nil {
		return
	}
	return *v, true
}

// OldDateFrom returns the old "date_from" field's value of the ProfitRate entity.
// If the ProfitRate object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ProfitRateMutation) OldDateFrom(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDateFrom is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDateFrom requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDateFrom: %w", err)
	}
	return oldValue.DateFrom, nil
}

// ResetDateFrom resets all changes to the "date_from" field.
func (m *ProfitRateMutation) ResetDateFrom() {
	m.date_from = nil
}

// SetDateTo sets the "date_to" field.
func (m *ProfitRateMutation) SetDateTo(t time.Time) {
	m.date_to = &t
}

// DateTo returns the value of the "date_to" field in the mutation.
func (m *ProfitRateMutation) DateTo() (r time.Time, exists bool) {
	v := m.date_to
	if v == nil {
		return
	}
	return *v, true
}

// OldDateTo returns the old "date_to" field's value of the ProfitRate entity.
// If the ProfitRate object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ProfitRateMutation) OldDateTo(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDateTo is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDateTo requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDateTo: %w", err)
	}
	return oldValue.DateTo, nil
}

// ResetDateTo resets all changes to the "date_to" field.
func (m *ProfitRateMutation) ResetDateTo() {
	m.date_to = nil
}

// ClearCurrency clears the "currency" edge to the Currency entity.
func (m *ProfitRateMutation) ClearCurrency() {
	m.clearedcurrency = true
	m.clearedFields[profitrate.FieldCurrencyID] = struct{}{}
}

// CurrencyCleared reports if the "currency" edge to the Currency entity was cleared.
func (m *ProfitRateMutation) CurrencyCleared() bool {
	return m.clearedcurrency
}

// CurrencyIDs returns the "currency" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// CurrencyID instead. It exists only for internal usage by the builders.
func (m *ProfitRateMutation) CurrencyIDs() (ids []uint) {
	if id := m.currency; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetCurrency resets all changes to the "currency" edge.
func (m *ProfitRateMutation) ResetCurrency() {
	m.currency = nil
	m.clearedcurrency = false
}

// AddRateDepositApplicationIDs adds the "rate_deposit_application" edge to the DepositApplication entity by ids.
func (m *ProfitRateMutation) AddRateDepositApplicationIDs(ids ...uuid.UUID) {
	if m.rate_deposit_application == nil {
		m.rate_deposit_application = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.rate_deposit_application[ids[i]] = struct{}{}
	}
}

// ClearRateDepositApplication clears the "rate_deposit_application" edge to the DepositApplication entity.
func (m *ProfitRateMutation) ClearRateDepositApplication() {
	m.clearedrate_deposit_application = true
}

// RateDepositApplicationCleared reports if the "rate_deposit_application" edge to the DepositApplication entity was cleared.
func (m *ProfitRateMutation) RateDepositApplicationCleared() bool {
	return m.clearedrate_deposit_application
}

// RemoveRateDepositApplicationIDs removes the "rate_deposit_application" edge to the DepositApplication entity by IDs.
func (m *ProfitRateMutation) RemoveRateDepositApplicationIDs(ids ...uuid.UUID) {
	if m.removedrate_deposit_application == nil {
		m.removedrate_deposit_application = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.rate_deposit_application, ids[i])
		m.removedrate_deposit_application[ids[i]] = struct{}{}
	}
}

// RemovedRateDepositApplication returns the removed IDs of the "rate_deposit_application" edge to the DepositApplication entity.
func (m *ProfitRateMutation) RemovedRateDepositApplicationIDs() (ids []uuid.UUID) {
	for id := range m.removedrate_deposit_application {
		ids = append(ids, id)
	}
	return
}

// RateDepositApplicationIDs returns the "rate_deposit_application" edge IDs in the mutation.
func (m *ProfitRateMutation) RateDepositApplicationIDs() (ids []uuid.UUID) {
	for id := range m.rate_deposit_application {
		ids = append(ids, id)
	}
	return
}

// ResetRateDepositApplication resets all changes to the "rate_deposit_application" edge.
func (m *ProfitRateMutation) ResetRateDepositApplication() {
	m.rate_deposit_application = nil
	m.clearedrate_deposit_application = false
	m.removedrate_deposit_application = nil
}

// Where appends a list predicates to the ProfitRateMutation builder.
func (m *ProfitRateMutation) Where(ps ...predicate.ProfitRate) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the ProfitRateMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *ProfitRateMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.ProfitRate, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *ProfitRateMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *ProfitRateMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (ProfitRate).
func (m *ProfitRateMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *ProfitRateMutation) Fields() []string {
	fields := make([]string, 0, 7)
	if m.create_time != nil {
		fields = append(fields, profitrate.FieldCreateTime)
	}
	if m.update_time != nil {
		fields = append(fields, profitrate.FieldUpdateTime)
	}
	if m.currency != nil {
		fields = append(fields, profitrate.FieldCurrencyID)
	}
	if m.month_term != nil {
		fields = append(fields, profitrate.FieldMonthTerm)
	}
	if m.profit_rate != nil {
		fields = append(fields, profitrate.FieldProfitRate)
	}
	if m.date_from != nil {
		fields = append(fields, profitrate.FieldDateFrom)
	}
	if m.date_to != nil {
		fields = append(fields, profitrate.FieldDateTo)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *ProfitRateMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case profitrate.FieldCreateTime:
		return m.CreateTime()
	case profitrate.FieldUpdateTime:
		return m.UpdateTime()
	case profitrate.FieldCurrencyID:
		return m.CurrencyID()
	case profitrate.FieldMonthTerm:
		return m.MonthTerm()
	case profitrate.FieldProfitRate:
		return m.ProfitRate()
	case profitrate.FieldDateFrom:
		return m.DateFrom()
	case profitrate.FieldDateTo:
		return m.DateTo()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *ProfitRateMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case profitrate.FieldCreateTime:
		return m.OldCreateTime(ctx)
	case profitrate.FieldUpdateTime:
		return m.OldUpdateTime(ctx)
	case profitrate.FieldCurrencyID:
		return m.OldCurrencyID(ctx)
	case profitrate.FieldMonthTerm:
		return m.OldMonthTerm(ctx)
	case profitrate.FieldProfitRate:
		return m.OldProfitRate(ctx)
	case profitrate.FieldDateFrom:
		return m.OldDateFrom(ctx)
	case profitrate.FieldDateTo:
		return m.OldDateTo(ctx)
	}
	return nil, fmt.Errorf("unknown ProfitRate field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ProfitRateMutation) SetField(name string, value ent.Value) error {
	switch name {
	case profitrate.FieldCreateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreateTime(v)
		return nil
	case profitrate.FieldUpdateTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdateTime(v)
		return nil
	case profitrate.FieldCurrencyID:
		v, ok := value.(uint)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCurrencyID(v)
		return nil
	case profitrate.FieldMonthTerm:
		v, ok := value.(uint32)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMonthTerm(v)
		return nil
	case profitrate.FieldProfitRate:
		v, ok := value.(decimal.Decimal)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetProfitRate(v)
		return nil
	case profitrate.FieldDateFrom:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDateFrom(v)
		return nil
	case profitrate.FieldDateTo:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDateTo(v)
		return nil
	}
	return fmt.Errorf("unknown ProfitRate field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *ProfitRateMutation) AddedFields() []string {
	var fields []string
	if m.addmonth_term != nil {
		fields = append(fields, profitrate.FieldMonthTerm)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *ProfitRateMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case profitrate.FieldMonthTerm:
		return m.AddedMonthTerm()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ProfitRateMutation) AddField(name string, value ent.Value) error {
	switch name {
	case profitrate.FieldMonthTerm:
		v, ok := value.(int32)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddMonthTerm(v)
		return nil
	}
	return fmt.Errorf("unknown ProfitRate numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *ProfitRateMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(profitrate.FieldCreateTime) {
		fields = append(fields, profitrate.FieldCreateTime)
	}
	if m.FieldCleared(profitrate.FieldUpdateTime) {
		fields = append(fields, profitrate.FieldUpdateTime)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *ProfitRateMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *ProfitRateMutation) ClearField(name string) error {
	switch name {
	case profitrate.FieldCreateTime:
		m.ClearCreateTime()
		return nil
	case profitrate.FieldUpdateTime:
		m.ClearUpdateTime()
		return nil
	}
	return fmt.Errorf("unknown ProfitRate nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *ProfitRateMutation) ResetField(name string) error {
	switch name {
	case profitrate.FieldCreateTime:
		m.ResetCreateTime()
		return nil
	case profitrate.FieldUpdateTime:
		m.ResetUpdateTime()
		return nil
	case profitrate.FieldCurrencyID:
		m.ResetCurrencyID()
		return nil
	case profitrate.FieldMonthTerm:
		m.ResetMonthTerm()
		return nil
	case profitrate.FieldProfitRate:
		m.ResetProfitRate()
		return nil
	case profitrate.FieldDateFrom:
		m.ResetDateFrom()
		return nil
	case profitrate.FieldDateTo:
		m.ResetDateTo()
		return nil
	}
	return fmt.Errorf("unknown ProfitRate field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *ProfitRateMutation) AddedEdges() []string {
	edges := make([]string, 0, 2)
	if m.currency != nil {
		edges = append(edges, profitrate.EdgeCurrency)
	}
	if m.rate_deposit_application != nil {
		edges = append(edges, profitrate.EdgeRateDepositApplication)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *ProfitRateMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case profitrate.EdgeCurrency:
		if id := m.currency; id != nil {
			return []ent.Value{*id}
		}
	case profitrate.EdgeRateDepositApplication:
		ids := make([]ent.Value, 0, len(m.rate_deposit_application))
		for id := range m.rate_deposit_application {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *ProfitRateMutation) RemovedEdges() []string {
	edges := make([]string, 0, 2)
	if m.removedrate_deposit_application != nil {
		edges = append(edges, profitrate.EdgeRateDepositApplication)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *ProfitRateMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case profitrate.EdgeRateDepositApplication:
		ids := make([]ent.Value, 0, len(m.removedrate_deposit_application))
		for id := range m.removedrate_deposit_application {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *ProfitRateMutation) ClearedEdges() []string {
	edges := make([]string, 0, 2)
	if m.clearedcurrency {
		edges = append(edges, profitrate.EdgeCurrency)
	}
	if m.clearedrate_deposit_application {
		edges = append(edges, profitrate.EdgeRateDepositApplication)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *ProfitRateMutation) EdgeCleared(name string) bool {
	switch name {
	case profitrate.EdgeCurrency:
		return m.clearedcurrency
	case profitrate.EdgeRateDepositApplication:
		return m.clearedrate_deposit_application
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *ProfitRateMutation) ClearEdge(name string) error {
	switch name {
	case profitrate.EdgeCurrency:
		m.ClearCurrency()
		return nil
	}
	return fmt.Errorf("unknown ProfitRate unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *ProfitRateMutation) ResetEdge(name string) error {
	switch name {
	case profitrate.EdgeCurrency:
		m.ResetCurrency()
		return nil
	case profitrate.EdgeRateDepositApplication:
		m.ResetRateDepositApplication()
		return nil
	}
	return fmt.Errorf("unknown ProfitRate edge %s", name)
}
