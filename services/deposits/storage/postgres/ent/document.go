// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositapplication"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/document"
)

// Document is the model entity for the Document schema.
type Document struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// CreateTime holds the value of the "create_time" field.
	CreateTime time.Time `json:"create_time,omitempty"`
	// UpdateTime holds the value of the "update_time" field.
	UpdateTime time.Time `json:"update_time,omitempty"`
	// DepositApplicationID holds the value of the "deposit_application_id" field.
	DepositApplicationID uuid.UUID `json:"deposit_application_id,omitempty"`
	// Type holds the value of the "type" field.
	Type document.Type `json:"type,omitempty"`
	// DocID holds the value of the "doc_id" field.
	DocID uuid.UUID `json:"doc_id,omitempty"`
	// Number holds the value of the "number" field.
	Number *string `json:"number,omitempty"`
	// SignedDocID holds the value of the "signed_doc_id" field.
	SignedDocID *uuid.UUID `json:"signed_doc_id,omitempty"`
	// DocumentSigningDate holds the value of the "document_signing_date" field.
	DocumentSigningDate *time.Time `json:"document_signing_date,omitempty"`
	// IsSignable holds the value of the "is_signable" field.
	IsSignable bool `json:"is_signable,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the DocumentQuery when eager-loading is set.
	Edges        DocumentEdges `json:"edges"`
	selectValues sql.SelectValues
}

// DocumentEdges holds the relations/edges for other nodes in the graph.
type DocumentEdges struct {
	// Application holds the value of the application edge.
	Application *DepositApplication `json:"application,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// ApplicationOrErr returns the Application value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e DocumentEdges) ApplicationOrErr() (*DepositApplication, error) {
	if e.Application != nil {
		return e.Application, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: depositapplication.Label}
	}
	return nil, &NotLoadedError{edge: "application"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Document) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case document.FieldSignedDocID:
			values[i] = &sql.NullScanner{S: new(uuid.UUID)}
		case document.FieldIsSignable:
			values[i] = new(sql.NullBool)
		case document.FieldType, document.FieldNumber:
			values[i] = new(sql.NullString)
		case document.FieldCreateTime, document.FieldUpdateTime, document.FieldDocumentSigningDate:
			values[i] = new(sql.NullTime)
		case document.FieldID, document.FieldDepositApplicationID, document.FieldDocID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Document fields.
func (_m *Document) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case document.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				_m.ID = *value
			}
		case document.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				_m.CreateTime = value.Time
			}
		case document.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				_m.UpdateTime = value.Time
			}
		case document.FieldDepositApplicationID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field deposit_application_id", values[i])
			} else if value != nil {
				_m.DepositApplicationID = *value
			}
		case document.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				_m.Type = document.Type(value.String)
			}
		case document.FieldDocID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field doc_id", values[i])
			} else if value != nil {
				_m.DocID = *value
			}
		case document.FieldNumber:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field number", values[i])
			} else if value.Valid {
				_m.Number = new(string)
				*_m.Number = value.String
			}
		case document.FieldSignedDocID:
			if value, ok := values[i].(*sql.NullScanner); !ok {
				return fmt.Errorf("unexpected type %T for field signed_doc_id", values[i])
			} else if value.Valid {
				_m.SignedDocID = new(uuid.UUID)
				*_m.SignedDocID = *value.S.(*uuid.UUID)
			}
		case document.FieldDocumentSigningDate:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field document_signing_date", values[i])
			} else if value.Valid {
				_m.DocumentSigningDate = new(time.Time)
				*_m.DocumentSigningDate = value.Time
			}
		case document.FieldIsSignable:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_signable", values[i])
			} else if value.Valid {
				_m.IsSignable = value.Bool
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Document.
// This includes values selected through modifiers, order, etc.
func (_m *Document) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// QueryApplication queries the "application" edge of the Document entity.
func (_m *Document) QueryApplication() *DepositApplicationQuery {
	return NewDocumentClient(_m.config).QueryApplication(_m)
}

// Update returns a builder for updating this Document.
// Note that you need to call Document.Unwrap() before calling this method if this Document
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *Document) Update() *DocumentUpdateOne {
	return NewDocumentClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the Document entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *Document) Unwrap() *Document {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("ent: Document is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *Document) String() string {
	var builder strings.Builder
	builder.WriteString("Document(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("create_time=")
	builder.WriteString(_m.CreateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_time=")
	builder.WriteString(_m.UpdateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("deposit_application_id=")
	builder.WriteString(fmt.Sprintf("%v", _m.DepositApplicationID))
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(fmt.Sprintf("%v", _m.Type))
	builder.WriteString(", ")
	builder.WriteString("doc_id=")
	builder.WriteString(fmt.Sprintf("%v", _m.DocID))
	builder.WriteString(", ")
	if v := _m.Number; v != nil {
		builder.WriteString("number=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := _m.SignedDocID; v != nil {
		builder.WriteString("signed_doc_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := _m.DocumentSigningDate; v != nil {
		builder.WriteString("document_signing_date=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteString(", ")
	builder.WriteString("is_signable=")
	builder.WriteString(fmt.Sprintf("%v", _m.IsSignable))
	builder.WriteByte(')')
	return builder.String()
}

// Documents is a parsable slice of Document.
type Documents []*Document
