// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/shopspring/decimal"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/currency"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositproduct"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/predicate"
)

// DepositProductUpdate is the builder for updating DepositProduct entities.
type DepositProductUpdate struct {
	config
	hooks     []Hook
	mutation  *DepositProductMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the DepositProductUpdate builder.
func (_u *DepositProductUpdate) Where(ps ...predicate.DepositProduct) *DepositProductUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetProductCode sets the "product_code" field.
func (_u *DepositProductUpdate) SetProductCode(v string) *DepositProductUpdate {
	_u.mutation.SetProductCode(v)
	return _u
}

// SetNillableProductCode sets the "product_code" field if the given value is not nil.
func (_u *DepositProductUpdate) SetNillableProductCode(v *string) *DepositProductUpdate {
	if v != nil {
		_u.SetProductCode(*v)
	}
	return _u
}

// SetColvirProductCode sets the "colvir_product_code" field.
func (_u *DepositProductUpdate) SetColvirProductCode(v string) *DepositProductUpdate {
	_u.mutation.SetColvirProductCode(v)
	return _u
}

// SetNillableColvirProductCode sets the "colvir_product_code" field if the given value is not nil.
func (_u *DepositProductUpdate) SetNillableColvirProductCode(v *string) *DepositProductUpdate {
	if v != nil {
		_u.SetColvirProductCode(*v)
	}
	return _u
}

// SetName sets the "name" field.
func (_u *DepositProductUpdate) SetName(v string) *DepositProductUpdate {
	_u.mutation.SetName(v)
	return _u
}

// SetNillableName sets the "name" field if the given value is not nil.
func (_u *DepositProductUpdate) SetNillableName(v *string) *DepositProductUpdate {
	if v != nil {
		_u.SetName(*v)
	}
	return _u
}

// SetMaxProfitRate sets the "max_profit_rate" field.
func (_u *DepositProductUpdate) SetMaxProfitRate(v decimal.Decimal) *DepositProductUpdate {
	_u.mutation.SetMaxProfitRate(v)
	return _u
}

// SetNillableMaxProfitRate sets the "max_profit_rate" field if the given value is not nil.
func (_u *DepositProductUpdate) SetNillableMaxProfitRate(v *decimal.Decimal) *DepositProductUpdate {
	if v != nil {
		_u.SetMaxProfitRate(*v)
	}
	return _u
}

// SetIsActive sets the "is_active" field.
func (_u *DepositProductUpdate) SetIsActive(v bool) *DepositProductUpdate {
	_u.mutation.SetIsActive(v)
	return _u
}

// SetNillableIsActive sets the "is_active" field if the given value is not nil.
func (_u *DepositProductUpdate) SetNillableIsActive(v *bool) *DepositProductUpdate {
	if v != nil {
		_u.SetIsActive(*v)
	}
	return _u
}

// SetIsReplenishable sets the "is_replenishable" field.
func (_u *DepositProductUpdate) SetIsReplenishable(v bool) *DepositProductUpdate {
	_u.mutation.SetIsReplenishable(v)
	return _u
}

// SetNillableIsReplenishable sets the "is_replenishable" field if the given value is not nil.
func (_u *DepositProductUpdate) SetNillableIsReplenishable(v *bool) *DepositProductUpdate {
	if v != nil {
		_u.SetIsReplenishable(*v)
	}
	return _u
}

// SetReplenishableDays sets the "replenishable_days" field.
func (_u *DepositProductUpdate) SetReplenishableDays(v uint) *DepositProductUpdate {
	_u.mutation.ResetReplenishableDays()
	_u.mutation.SetReplenishableDays(v)
	return _u
}

// SetNillableReplenishableDays sets the "replenishable_days" field if the given value is not nil.
func (_u *DepositProductUpdate) SetNillableReplenishableDays(v *uint) *DepositProductUpdate {
	if v != nil {
		_u.SetReplenishableDays(*v)
	}
	return _u
}

// AddReplenishableDays adds value to the "replenishable_days" field.
func (_u *DepositProductUpdate) AddReplenishableDays(v int) *DepositProductUpdate {
	_u.mutation.AddReplenishableDays(v)
	return _u
}

// AddCurrencyIDs adds the "currency" edge to the Currency entity by IDs.
func (_u *DepositProductUpdate) AddCurrencyIDs(ids ...uint) *DepositProductUpdate {
	_u.mutation.AddCurrencyIDs(ids...)
	return _u
}

// AddCurrency adds the "currency" edges to the Currency entity.
func (_u *DepositProductUpdate) AddCurrency(v ...*Currency) *DepositProductUpdate {
	ids := make([]uint, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _u.AddCurrencyIDs(ids...)
}

// Mutation returns the DepositProductMutation object of the builder.
func (_u *DepositProductUpdate) Mutation() *DepositProductMutation {
	return _u.mutation
}

// ClearCurrency clears all "currency" edges to the Currency entity.
func (_u *DepositProductUpdate) ClearCurrency() *DepositProductUpdate {
	_u.mutation.ClearCurrency()
	return _u
}

// RemoveCurrencyIDs removes the "currency" edge to Currency entities by IDs.
func (_u *DepositProductUpdate) RemoveCurrencyIDs(ids ...uint) *DepositProductUpdate {
	_u.mutation.RemoveCurrencyIDs(ids...)
	return _u
}

// RemoveCurrency removes "currency" edges to Currency entities.
func (_u *DepositProductUpdate) RemoveCurrency(v ...*Currency) *DepositProductUpdate {
	ids := make([]uint, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _u.RemoveCurrencyIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *DepositProductUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *DepositProductUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *DepositProductUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *DepositProductUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *DepositProductUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DepositProductUpdate {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *DepositProductUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	_spec := sqlgraph.NewUpdateSpec(depositproduct.Table, depositproduct.Columns, sqlgraph.NewFieldSpec(depositproduct.FieldID, field.TypeUint))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.ProductCode(); ok {
		_spec.SetField(depositproduct.FieldProductCode, field.TypeString, value)
	}
	if value, ok := _u.mutation.ColvirProductCode(); ok {
		_spec.SetField(depositproduct.FieldColvirProductCode, field.TypeString, value)
	}
	if value, ok := _u.mutation.Name(); ok {
		_spec.SetField(depositproduct.FieldName, field.TypeString, value)
	}
	if value, ok := _u.mutation.MaxProfitRate(); ok {
		_spec.SetField(depositproduct.FieldMaxProfitRate, field.TypeString, value)
	}
	if value, ok := _u.mutation.IsActive(); ok {
		_spec.SetField(depositproduct.FieldIsActive, field.TypeBool, value)
	}
	if value, ok := _u.mutation.IsReplenishable(); ok {
		_spec.SetField(depositproduct.FieldIsReplenishable, field.TypeBool, value)
	}
	if value, ok := _u.mutation.ReplenishableDays(); ok {
		_spec.SetField(depositproduct.FieldReplenishableDays, field.TypeUint, value)
	}
	if value, ok := _u.mutation.AddedReplenishableDays(); ok {
		_spec.AddField(depositproduct.FieldReplenishableDays, field.TypeUint, value)
	}
	if _u.mutation.CurrencyCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   depositproduct.CurrencyTable,
			Columns: []string{depositproduct.CurrencyColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(currency.FieldID, field.TypeUint),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.RemovedCurrencyIDs(); len(nodes) > 0 && !_u.mutation.CurrencyCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   depositproduct.CurrencyTable,
			Columns: []string{depositproduct.CurrencyColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(currency.FieldID, field.TypeUint),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.CurrencyIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   depositproduct.CurrencyTable,
			Columns: []string{depositproduct.CurrencyColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(currency.FieldID, field.TypeUint),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(_u.modifiers...)
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{depositproduct.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// DepositProductUpdateOne is the builder for updating a single DepositProduct entity.
type DepositProductUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *DepositProductMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetProductCode sets the "product_code" field.
func (_u *DepositProductUpdateOne) SetProductCode(v string) *DepositProductUpdateOne {
	_u.mutation.SetProductCode(v)
	return _u
}

// SetNillableProductCode sets the "product_code" field if the given value is not nil.
func (_u *DepositProductUpdateOne) SetNillableProductCode(v *string) *DepositProductUpdateOne {
	if v != nil {
		_u.SetProductCode(*v)
	}
	return _u
}

// SetColvirProductCode sets the "colvir_product_code" field.
func (_u *DepositProductUpdateOne) SetColvirProductCode(v string) *DepositProductUpdateOne {
	_u.mutation.SetColvirProductCode(v)
	return _u
}

// SetNillableColvirProductCode sets the "colvir_product_code" field if the given value is not nil.
func (_u *DepositProductUpdateOne) SetNillableColvirProductCode(v *string) *DepositProductUpdateOne {
	if v != nil {
		_u.SetColvirProductCode(*v)
	}
	return _u
}

// SetName sets the "name" field.
func (_u *DepositProductUpdateOne) SetName(v string) *DepositProductUpdateOne {
	_u.mutation.SetName(v)
	return _u
}

// SetNillableName sets the "name" field if the given value is not nil.
func (_u *DepositProductUpdateOne) SetNillableName(v *string) *DepositProductUpdateOne {
	if v != nil {
		_u.SetName(*v)
	}
	return _u
}

// SetMaxProfitRate sets the "max_profit_rate" field.
func (_u *DepositProductUpdateOne) SetMaxProfitRate(v decimal.Decimal) *DepositProductUpdateOne {
	_u.mutation.SetMaxProfitRate(v)
	return _u
}

// SetNillableMaxProfitRate sets the "max_profit_rate" field if the given value is not nil.
func (_u *DepositProductUpdateOne) SetNillableMaxProfitRate(v *decimal.Decimal) *DepositProductUpdateOne {
	if v != nil {
		_u.SetMaxProfitRate(*v)
	}
	return _u
}

// SetIsActive sets the "is_active" field.
func (_u *DepositProductUpdateOne) SetIsActive(v bool) *DepositProductUpdateOne {
	_u.mutation.SetIsActive(v)
	return _u
}

// SetNillableIsActive sets the "is_active" field if the given value is not nil.
func (_u *DepositProductUpdateOne) SetNillableIsActive(v *bool) *DepositProductUpdateOne {
	if v != nil {
		_u.SetIsActive(*v)
	}
	return _u
}

// SetIsReplenishable sets the "is_replenishable" field.
func (_u *DepositProductUpdateOne) SetIsReplenishable(v bool) *DepositProductUpdateOne {
	_u.mutation.SetIsReplenishable(v)
	return _u
}

// SetNillableIsReplenishable sets the "is_replenishable" field if the given value is not nil.
func (_u *DepositProductUpdateOne) SetNillableIsReplenishable(v *bool) *DepositProductUpdateOne {
	if v != nil {
		_u.SetIsReplenishable(*v)
	}
	return _u
}

// SetReplenishableDays sets the "replenishable_days" field.
func (_u *DepositProductUpdateOne) SetReplenishableDays(v uint) *DepositProductUpdateOne {
	_u.mutation.ResetReplenishableDays()
	_u.mutation.SetReplenishableDays(v)
	return _u
}

// SetNillableReplenishableDays sets the "replenishable_days" field if the given value is not nil.
func (_u *DepositProductUpdateOne) SetNillableReplenishableDays(v *uint) *DepositProductUpdateOne {
	if v != nil {
		_u.SetReplenishableDays(*v)
	}
	return _u
}

// AddReplenishableDays adds value to the "replenishable_days" field.
func (_u *DepositProductUpdateOne) AddReplenishableDays(v int) *DepositProductUpdateOne {
	_u.mutation.AddReplenishableDays(v)
	return _u
}

// AddCurrencyIDs adds the "currency" edge to the Currency entity by IDs.
func (_u *DepositProductUpdateOne) AddCurrencyIDs(ids ...uint) *DepositProductUpdateOne {
	_u.mutation.AddCurrencyIDs(ids...)
	return _u
}

// AddCurrency adds the "currency" edges to the Currency entity.
func (_u *DepositProductUpdateOne) AddCurrency(v ...*Currency) *DepositProductUpdateOne {
	ids := make([]uint, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _u.AddCurrencyIDs(ids...)
}

// Mutation returns the DepositProductMutation object of the builder.
func (_u *DepositProductUpdateOne) Mutation() *DepositProductMutation {
	return _u.mutation
}

// ClearCurrency clears all "currency" edges to the Currency entity.
func (_u *DepositProductUpdateOne) ClearCurrency() *DepositProductUpdateOne {
	_u.mutation.ClearCurrency()
	return _u
}

// RemoveCurrencyIDs removes the "currency" edge to Currency entities by IDs.
func (_u *DepositProductUpdateOne) RemoveCurrencyIDs(ids ...uint) *DepositProductUpdateOne {
	_u.mutation.RemoveCurrencyIDs(ids...)
	return _u
}

// RemoveCurrency removes "currency" edges to Currency entities.
func (_u *DepositProductUpdateOne) RemoveCurrency(v ...*Currency) *DepositProductUpdateOne {
	ids := make([]uint, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _u.RemoveCurrencyIDs(ids...)
}

// Where appends a list predicates to the DepositProductUpdate builder.
func (_u *DepositProductUpdateOne) Where(ps ...predicate.DepositProduct) *DepositProductUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *DepositProductUpdateOne) Select(field string, fields ...string) *DepositProductUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated DepositProduct entity.
func (_u *DepositProductUpdateOne) Save(ctx context.Context) (*DepositProduct, error) {
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *DepositProductUpdateOne) SaveX(ctx context.Context) *DepositProduct {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *DepositProductUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *DepositProductUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *DepositProductUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DepositProductUpdateOne {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *DepositProductUpdateOne) sqlSave(ctx context.Context) (_node *DepositProduct, err error) {
	_spec := sqlgraph.NewUpdateSpec(depositproduct.Table, depositproduct.Columns, sqlgraph.NewFieldSpec(depositproduct.FieldID, field.TypeUint))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "DepositProduct.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, depositproduct.FieldID)
		for _, f := range fields {
			if !depositproduct.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != depositproduct.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.ProductCode(); ok {
		_spec.SetField(depositproduct.FieldProductCode, field.TypeString, value)
	}
	if value, ok := _u.mutation.ColvirProductCode(); ok {
		_spec.SetField(depositproduct.FieldColvirProductCode, field.TypeString, value)
	}
	if value, ok := _u.mutation.Name(); ok {
		_spec.SetField(depositproduct.FieldName, field.TypeString, value)
	}
	if value, ok := _u.mutation.MaxProfitRate(); ok {
		_spec.SetField(depositproduct.FieldMaxProfitRate, field.TypeString, value)
	}
	if value, ok := _u.mutation.IsActive(); ok {
		_spec.SetField(depositproduct.FieldIsActive, field.TypeBool, value)
	}
	if value, ok := _u.mutation.IsReplenishable(); ok {
		_spec.SetField(depositproduct.FieldIsReplenishable, field.TypeBool, value)
	}
	if value, ok := _u.mutation.ReplenishableDays(); ok {
		_spec.SetField(depositproduct.FieldReplenishableDays, field.TypeUint, value)
	}
	if value, ok := _u.mutation.AddedReplenishableDays(); ok {
		_spec.AddField(depositproduct.FieldReplenishableDays, field.TypeUint, value)
	}
	if _u.mutation.CurrencyCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   depositproduct.CurrencyTable,
			Columns: []string{depositproduct.CurrencyColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(currency.FieldID, field.TypeUint),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.RemovedCurrencyIDs(); len(nodes) > 0 && !_u.mutation.CurrencyCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   depositproduct.CurrencyTable,
			Columns: []string{depositproduct.CurrencyColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(currency.FieldID, field.TypeUint),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.CurrencyIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   depositproduct.CurrencyTable,
			Columns: []string{depositproduct.CurrencyColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(currency.FieldID, field.TypeUint),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(_u.modifiers...)
	_node = &DepositProduct{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{depositproduct.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
