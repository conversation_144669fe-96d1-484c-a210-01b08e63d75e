// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/migrate"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/currency"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositapplication"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositcards"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositproduct"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/document"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/health"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/profitrate"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// Currency is the client for interacting with the Currency builders.
	Currency *CurrencyClient
	// DepositApplication is the client for interacting with the DepositApplication builders.
	DepositApplication *DepositApplicationClient
	// DepositCards is the client for interacting with the DepositCards builders.
	DepositCards *DepositCardsClient
	// DepositProduct is the client for interacting with the DepositProduct builders.
	DepositProduct *DepositProductClient
	// Document is the client for interacting with the Document builders.
	Document *DocumentClient
	// Health is the client for interacting with the Health builders.
	Health *HealthClient
	// ProfitRate is the client for interacting with the ProfitRate builders.
	ProfitRate *ProfitRateClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.Currency = NewCurrencyClient(c.config)
	c.DepositApplication = NewDepositApplicationClient(c.config)
	c.DepositCards = NewDepositCardsClient(c.config)
	c.DepositProduct = NewDepositProductClient(c.config)
	c.Document = NewDocumentClient(c.config)
	c.Health = NewHealthClient(c.config)
	c.ProfitRate = NewProfitRateClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:                ctx,
		config:             cfg,
		Currency:           NewCurrencyClient(cfg),
		DepositApplication: NewDepositApplicationClient(cfg),
		DepositCards:       NewDepositCardsClient(cfg),
		DepositProduct:     NewDepositProductClient(cfg),
		Document:           NewDocumentClient(cfg),
		Health:             NewHealthClient(cfg),
		ProfitRate:         NewProfitRateClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:                ctx,
		config:             cfg,
		Currency:           NewCurrencyClient(cfg),
		DepositApplication: NewDepositApplicationClient(cfg),
		DepositCards:       NewDepositCardsClient(cfg),
		DepositProduct:     NewDepositProductClient(cfg),
		Document:           NewDocumentClient(cfg),
		Health:             NewHealthClient(cfg),
		ProfitRate:         NewProfitRateClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		Currency.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	for _, n := range []interface{ Use(...Hook) }{
		c.Currency, c.DepositApplication, c.DepositCards, c.DepositProduct, c.Document,
		c.Health, c.ProfitRate,
	} {
		n.Use(hooks...)
	}
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	for _, n := range []interface{ Intercept(...Interceptor) }{
		c.Currency, c.DepositApplication, c.DepositCards, c.DepositProduct, c.Document,
		c.Health, c.ProfitRate,
	} {
		n.Intercept(interceptors...)
	}
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *CurrencyMutation:
		return c.Currency.mutate(ctx, m)
	case *DepositApplicationMutation:
		return c.DepositApplication.mutate(ctx, m)
	case *DepositCardsMutation:
		return c.DepositCards.mutate(ctx, m)
	case *DepositProductMutation:
		return c.DepositProduct.mutate(ctx, m)
	case *DocumentMutation:
		return c.Document.mutate(ctx, m)
	case *HealthMutation:
		return c.Health.mutate(ctx, m)
	case *ProfitRateMutation:
		return c.ProfitRate.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// CurrencyClient is a client for the Currency schema.
type CurrencyClient struct {
	config
}

// NewCurrencyClient returns a client for the Currency from the given config.
func NewCurrencyClient(c config) *CurrencyClient {
	return &CurrencyClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `currency.Hooks(f(g(h())))`.
func (c *CurrencyClient) Use(hooks ...Hook) {
	c.hooks.Currency = append(c.hooks.Currency, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `currency.Intercept(f(g(h())))`.
func (c *CurrencyClient) Intercept(interceptors ...Interceptor) {
	c.inters.Currency = append(c.inters.Currency, interceptors...)
}

// Create returns a builder for creating a Currency entity.
func (c *CurrencyClient) Create() *CurrencyCreate {
	mutation := newCurrencyMutation(c.config, OpCreate)
	return &CurrencyCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Currency entities.
func (c *CurrencyClient) CreateBulk(builders ...*CurrencyCreate) *CurrencyCreateBulk {
	return &CurrencyCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *CurrencyClient) MapCreateBulk(slice any, setFunc func(*CurrencyCreate, int)) *CurrencyCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &CurrencyCreateBulk{err: fmt.Errorf("calling to CurrencyClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*CurrencyCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &CurrencyCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Currency.
func (c *CurrencyClient) Update() *CurrencyUpdate {
	mutation := newCurrencyMutation(c.config, OpUpdate)
	return &CurrencyUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *CurrencyClient) UpdateOne(_m *Currency) *CurrencyUpdateOne {
	mutation := newCurrencyMutation(c.config, OpUpdateOne, withCurrency(_m))
	return &CurrencyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *CurrencyClient) UpdateOneID(id uint) *CurrencyUpdateOne {
	mutation := newCurrencyMutation(c.config, OpUpdateOne, withCurrencyID(id))
	return &CurrencyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Currency.
func (c *CurrencyClient) Delete() *CurrencyDelete {
	mutation := newCurrencyMutation(c.config, OpDelete)
	return &CurrencyDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *CurrencyClient) DeleteOne(_m *Currency) *CurrencyDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *CurrencyClient) DeleteOneID(id uint) *CurrencyDeleteOne {
	builder := c.Delete().Where(currency.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &CurrencyDeleteOne{builder}
}

// Query returns a query builder for Currency.
func (c *CurrencyClient) Query() *CurrencyQuery {
	return &CurrencyQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeCurrency},
		inters: c.Interceptors(),
	}
}

// Get returns a Currency entity by its id.
func (c *CurrencyClient) Get(ctx context.Context, id uint) (*Currency, error) {
	return c.Query().Where(currency.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *CurrencyClient) GetX(ctx context.Context, id uint) *Currency {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryProfitRate queries the profit_rate edge of a Currency.
func (c *CurrencyClient) QueryProfitRate(_m *Currency) *ProfitRateQuery {
	query := (&ProfitRateClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := _m.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(currency.Table, currency.FieldID, id),
			sqlgraph.To(profitrate.Table, profitrate.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, currency.ProfitRateTable, currency.ProfitRateColumn),
		)
		fromV = sqlgraph.Neighbors(_m.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryProduct queries the product edge of a Currency.
func (c *CurrencyClient) QueryProduct(_m *Currency) *DepositProductQuery {
	query := (&DepositProductClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := _m.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(currency.Table, currency.FieldID, id),
			sqlgraph.To(depositproduct.Table, depositproduct.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, currency.ProductTable, currency.ProductColumn),
		)
		fromV = sqlgraph.Neighbors(_m.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *CurrencyClient) Hooks() []Hook {
	return c.hooks.Currency
}

// Interceptors returns the client interceptors.
func (c *CurrencyClient) Interceptors() []Interceptor {
	return c.inters.Currency
}

func (c *CurrencyClient) mutate(ctx context.Context, m *CurrencyMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&CurrencyCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&CurrencyUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&CurrencyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&CurrencyDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Currency mutation op: %q", m.Op())
	}
}

// DepositApplicationClient is a client for the DepositApplication schema.
type DepositApplicationClient struct {
	config
}

// NewDepositApplicationClient returns a client for the DepositApplication from the given config.
func NewDepositApplicationClient(c config) *DepositApplicationClient {
	return &DepositApplicationClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `depositapplication.Hooks(f(g(h())))`.
func (c *DepositApplicationClient) Use(hooks ...Hook) {
	c.hooks.DepositApplication = append(c.hooks.DepositApplication, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `depositapplication.Intercept(f(g(h())))`.
func (c *DepositApplicationClient) Intercept(interceptors ...Interceptor) {
	c.inters.DepositApplication = append(c.inters.DepositApplication, interceptors...)
}

// Create returns a builder for creating a DepositApplication entity.
func (c *DepositApplicationClient) Create() *DepositApplicationCreate {
	mutation := newDepositApplicationMutation(c.config, OpCreate)
	return &DepositApplicationCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of DepositApplication entities.
func (c *DepositApplicationClient) CreateBulk(builders ...*DepositApplicationCreate) *DepositApplicationCreateBulk {
	return &DepositApplicationCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DepositApplicationClient) MapCreateBulk(slice any, setFunc func(*DepositApplicationCreate, int)) *DepositApplicationCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DepositApplicationCreateBulk{err: fmt.Errorf("calling to DepositApplicationClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DepositApplicationCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DepositApplicationCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for DepositApplication.
func (c *DepositApplicationClient) Update() *DepositApplicationUpdate {
	mutation := newDepositApplicationMutation(c.config, OpUpdate)
	return &DepositApplicationUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DepositApplicationClient) UpdateOne(_m *DepositApplication) *DepositApplicationUpdateOne {
	mutation := newDepositApplicationMutation(c.config, OpUpdateOne, withDepositApplication(_m))
	return &DepositApplicationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DepositApplicationClient) UpdateOneID(id uuid.UUID) *DepositApplicationUpdateOne {
	mutation := newDepositApplicationMutation(c.config, OpUpdateOne, withDepositApplicationID(id))
	return &DepositApplicationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for DepositApplication.
func (c *DepositApplicationClient) Delete() *DepositApplicationDelete {
	mutation := newDepositApplicationMutation(c.config, OpDelete)
	return &DepositApplicationDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DepositApplicationClient) DeleteOne(_m *DepositApplication) *DepositApplicationDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DepositApplicationClient) DeleteOneID(id uuid.UUID) *DepositApplicationDeleteOne {
	builder := c.Delete().Where(depositapplication.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DepositApplicationDeleteOne{builder}
}

// Query returns a query builder for DepositApplication.
func (c *DepositApplicationClient) Query() *DepositApplicationQuery {
	return &DepositApplicationQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDepositApplication},
		inters: c.Interceptors(),
	}
}

// Get returns a DepositApplication entity by its id.
func (c *DepositApplicationClient) Get(ctx context.Context, id uuid.UUID) (*DepositApplication, error) {
	return c.Query().Where(depositapplication.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DepositApplicationClient) GetX(ctx context.Context, id uuid.UUID) *DepositApplication {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryDocuments queries the documents edge of a DepositApplication.
func (c *DepositApplicationClient) QueryDocuments(_m *DepositApplication) *DocumentQuery {
	query := (&DocumentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := _m.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(depositapplication.Table, depositapplication.FieldID, id),
			sqlgraph.To(document.Table, document.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, depositapplication.DocumentsTable, depositapplication.DocumentsColumn),
		)
		fromV = sqlgraph.Neighbors(_m.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryRate queries the rate edge of a DepositApplication.
func (c *DepositApplicationClient) QueryRate(_m *DepositApplication) *ProfitRateQuery {
	query := (&ProfitRateClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := _m.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(depositapplication.Table, depositapplication.FieldID, id),
			sqlgraph.To(profitrate.Table, profitrate.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, depositapplication.RateTable, depositapplication.RateColumn),
		)
		fromV = sqlgraph.Neighbors(_m.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *DepositApplicationClient) Hooks() []Hook {
	return c.hooks.DepositApplication
}

// Interceptors returns the client interceptors.
func (c *DepositApplicationClient) Interceptors() []Interceptor {
	return c.inters.DepositApplication
}

func (c *DepositApplicationClient) mutate(ctx context.Context, m *DepositApplicationMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DepositApplicationCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DepositApplicationUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DepositApplicationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DepositApplicationDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown DepositApplication mutation op: %q", m.Op())
	}
}

// DepositCardsClient is a client for the DepositCards schema.
type DepositCardsClient struct {
	config
}

// NewDepositCardsClient returns a client for the DepositCards from the given config.
func NewDepositCardsClient(c config) *DepositCardsClient {
	return &DepositCardsClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `depositcards.Hooks(f(g(h())))`.
func (c *DepositCardsClient) Use(hooks ...Hook) {
	c.hooks.DepositCards = append(c.hooks.DepositCards, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `depositcards.Intercept(f(g(h())))`.
func (c *DepositCardsClient) Intercept(interceptors ...Interceptor) {
	c.inters.DepositCards = append(c.inters.DepositCards, interceptors...)
}

// Create returns a builder for creating a DepositCards entity.
func (c *DepositCardsClient) Create() *DepositCardsCreate {
	mutation := newDepositCardsMutation(c.config, OpCreate)
	return &DepositCardsCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of DepositCards entities.
func (c *DepositCardsClient) CreateBulk(builders ...*DepositCardsCreate) *DepositCardsCreateBulk {
	return &DepositCardsCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DepositCardsClient) MapCreateBulk(slice any, setFunc func(*DepositCardsCreate, int)) *DepositCardsCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DepositCardsCreateBulk{err: fmt.Errorf("calling to DepositCardsClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DepositCardsCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DepositCardsCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for DepositCards.
func (c *DepositCardsClient) Update() *DepositCardsUpdate {
	mutation := newDepositCardsMutation(c.config, OpUpdate)
	return &DepositCardsUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DepositCardsClient) UpdateOne(_m *DepositCards) *DepositCardsUpdateOne {
	mutation := newDepositCardsMutation(c.config, OpUpdateOne, withDepositCards(_m))
	return &DepositCardsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DepositCardsClient) UpdateOneID(id uuid.UUID) *DepositCardsUpdateOne {
	mutation := newDepositCardsMutation(c.config, OpUpdateOne, withDepositCardsID(id))
	return &DepositCardsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for DepositCards.
func (c *DepositCardsClient) Delete() *DepositCardsDelete {
	mutation := newDepositCardsMutation(c.config, OpDelete)
	return &DepositCardsDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DepositCardsClient) DeleteOne(_m *DepositCards) *DepositCardsDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DepositCardsClient) DeleteOneID(id uuid.UUID) *DepositCardsDeleteOne {
	builder := c.Delete().Where(depositcards.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DepositCardsDeleteOne{builder}
}

// Query returns a query builder for DepositCards.
func (c *DepositCardsClient) Query() *DepositCardsQuery {
	return &DepositCardsQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDepositCards},
		inters: c.Interceptors(),
	}
}

// Get returns a DepositCards entity by its id.
func (c *DepositCardsClient) Get(ctx context.Context, id uuid.UUID) (*DepositCards, error) {
	return c.Query().Where(depositcards.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DepositCardsClient) GetX(ctx context.Context, id uuid.UUID) *DepositCards {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryApplication queries the application edge of a DepositCards.
func (c *DepositCardsClient) QueryApplication(_m *DepositCards) *DepositApplicationQuery {
	query := (&DepositApplicationClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := _m.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(depositcards.Table, depositcards.FieldID, id),
			sqlgraph.To(depositapplication.Table, depositapplication.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, depositcards.ApplicationTable, depositcards.ApplicationColumn),
		)
		fromV = sqlgraph.Neighbors(_m.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *DepositCardsClient) Hooks() []Hook {
	return c.hooks.DepositCards
}

// Interceptors returns the client interceptors.
func (c *DepositCardsClient) Interceptors() []Interceptor {
	return c.inters.DepositCards
}

func (c *DepositCardsClient) mutate(ctx context.Context, m *DepositCardsMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DepositCardsCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DepositCardsUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DepositCardsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DepositCardsDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown DepositCards mutation op: %q", m.Op())
	}
}

// DepositProductClient is a client for the DepositProduct schema.
type DepositProductClient struct {
	config
}

// NewDepositProductClient returns a client for the DepositProduct from the given config.
func NewDepositProductClient(c config) *DepositProductClient {
	return &DepositProductClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `depositproduct.Hooks(f(g(h())))`.
func (c *DepositProductClient) Use(hooks ...Hook) {
	c.hooks.DepositProduct = append(c.hooks.DepositProduct, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `depositproduct.Intercept(f(g(h())))`.
func (c *DepositProductClient) Intercept(interceptors ...Interceptor) {
	c.inters.DepositProduct = append(c.inters.DepositProduct, interceptors...)
}

// Create returns a builder for creating a DepositProduct entity.
func (c *DepositProductClient) Create() *DepositProductCreate {
	mutation := newDepositProductMutation(c.config, OpCreate)
	return &DepositProductCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of DepositProduct entities.
func (c *DepositProductClient) CreateBulk(builders ...*DepositProductCreate) *DepositProductCreateBulk {
	return &DepositProductCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DepositProductClient) MapCreateBulk(slice any, setFunc func(*DepositProductCreate, int)) *DepositProductCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DepositProductCreateBulk{err: fmt.Errorf("calling to DepositProductClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DepositProductCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DepositProductCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for DepositProduct.
func (c *DepositProductClient) Update() *DepositProductUpdate {
	mutation := newDepositProductMutation(c.config, OpUpdate)
	return &DepositProductUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DepositProductClient) UpdateOne(_m *DepositProduct) *DepositProductUpdateOne {
	mutation := newDepositProductMutation(c.config, OpUpdateOne, withDepositProduct(_m))
	return &DepositProductUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DepositProductClient) UpdateOneID(id uint) *DepositProductUpdateOne {
	mutation := newDepositProductMutation(c.config, OpUpdateOne, withDepositProductID(id))
	return &DepositProductUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for DepositProduct.
func (c *DepositProductClient) Delete() *DepositProductDelete {
	mutation := newDepositProductMutation(c.config, OpDelete)
	return &DepositProductDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DepositProductClient) DeleteOne(_m *DepositProduct) *DepositProductDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DepositProductClient) DeleteOneID(id uint) *DepositProductDeleteOne {
	builder := c.Delete().Where(depositproduct.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DepositProductDeleteOne{builder}
}

// Query returns a query builder for DepositProduct.
func (c *DepositProductClient) Query() *DepositProductQuery {
	return &DepositProductQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDepositProduct},
		inters: c.Interceptors(),
	}
}

// Get returns a DepositProduct entity by its id.
func (c *DepositProductClient) Get(ctx context.Context, id uint) (*DepositProduct, error) {
	return c.Query().Where(depositproduct.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DepositProductClient) GetX(ctx context.Context, id uint) *DepositProduct {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryCurrency queries the currency edge of a DepositProduct.
func (c *DepositProductClient) QueryCurrency(_m *DepositProduct) *CurrencyQuery {
	query := (&CurrencyClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := _m.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(depositproduct.Table, depositproduct.FieldID, id),
			sqlgraph.To(currency.Table, currency.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, depositproduct.CurrencyTable, depositproduct.CurrencyColumn),
		)
		fromV = sqlgraph.Neighbors(_m.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *DepositProductClient) Hooks() []Hook {
	return c.hooks.DepositProduct
}

// Interceptors returns the client interceptors.
func (c *DepositProductClient) Interceptors() []Interceptor {
	return c.inters.DepositProduct
}

func (c *DepositProductClient) mutate(ctx context.Context, m *DepositProductMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DepositProductCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DepositProductUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DepositProductUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DepositProductDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown DepositProduct mutation op: %q", m.Op())
	}
}

// DocumentClient is a client for the Document schema.
type DocumentClient struct {
	config
}

// NewDocumentClient returns a client for the Document from the given config.
func NewDocumentClient(c config) *DocumentClient {
	return &DocumentClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `document.Hooks(f(g(h())))`.
func (c *DocumentClient) Use(hooks ...Hook) {
	c.hooks.Document = append(c.hooks.Document, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `document.Intercept(f(g(h())))`.
func (c *DocumentClient) Intercept(interceptors ...Interceptor) {
	c.inters.Document = append(c.inters.Document, interceptors...)
}

// Create returns a builder for creating a Document entity.
func (c *DocumentClient) Create() *DocumentCreate {
	mutation := newDocumentMutation(c.config, OpCreate)
	return &DocumentCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Document entities.
func (c *DocumentClient) CreateBulk(builders ...*DocumentCreate) *DocumentCreateBulk {
	return &DocumentCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DocumentClient) MapCreateBulk(slice any, setFunc func(*DocumentCreate, int)) *DocumentCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DocumentCreateBulk{err: fmt.Errorf("calling to DocumentClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DocumentCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DocumentCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Document.
func (c *DocumentClient) Update() *DocumentUpdate {
	mutation := newDocumentMutation(c.config, OpUpdate)
	return &DocumentUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DocumentClient) UpdateOne(_m *Document) *DocumentUpdateOne {
	mutation := newDocumentMutation(c.config, OpUpdateOne, withDocument(_m))
	return &DocumentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DocumentClient) UpdateOneID(id uuid.UUID) *DocumentUpdateOne {
	mutation := newDocumentMutation(c.config, OpUpdateOne, withDocumentID(id))
	return &DocumentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Document.
func (c *DocumentClient) Delete() *DocumentDelete {
	mutation := newDocumentMutation(c.config, OpDelete)
	return &DocumentDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DocumentClient) DeleteOne(_m *Document) *DocumentDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DocumentClient) DeleteOneID(id uuid.UUID) *DocumentDeleteOne {
	builder := c.Delete().Where(document.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DocumentDeleteOne{builder}
}

// Query returns a query builder for Document.
func (c *DocumentClient) Query() *DocumentQuery {
	return &DocumentQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDocument},
		inters: c.Interceptors(),
	}
}

// Get returns a Document entity by its id.
func (c *DocumentClient) Get(ctx context.Context, id uuid.UUID) (*Document, error) {
	return c.Query().Where(document.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DocumentClient) GetX(ctx context.Context, id uuid.UUID) *Document {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryApplication queries the application edge of a Document.
func (c *DocumentClient) QueryApplication(_m *Document) *DepositApplicationQuery {
	query := (&DepositApplicationClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := _m.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(document.Table, document.FieldID, id),
			sqlgraph.To(depositapplication.Table, depositapplication.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, document.ApplicationTable, document.ApplicationColumn),
		)
		fromV = sqlgraph.Neighbors(_m.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *DocumentClient) Hooks() []Hook {
	return c.hooks.Document
}

// Interceptors returns the client interceptors.
func (c *DocumentClient) Interceptors() []Interceptor {
	return c.inters.Document
}

func (c *DocumentClient) mutate(ctx context.Context, m *DocumentMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DocumentCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DocumentUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DocumentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DocumentDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Document mutation op: %q", m.Op())
	}
}

// HealthClient is a client for the Health schema.
type HealthClient struct {
	config
}

// NewHealthClient returns a client for the Health from the given config.
func NewHealthClient(c config) *HealthClient {
	return &HealthClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `health.Hooks(f(g(h())))`.
func (c *HealthClient) Use(hooks ...Hook) {
	c.hooks.Health = append(c.hooks.Health, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `health.Intercept(f(g(h())))`.
func (c *HealthClient) Intercept(interceptors ...Interceptor) {
	c.inters.Health = append(c.inters.Health, interceptors...)
}

// Create returns a builder for creating a Health entity.
func (c *HealthClient) Create() *HealthCreate {
	mutation := newHealthMutation(c.config, OpCreate)
	return &HealthCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Health entities.
func (c *HealthClient) CreateBulk(builders ...*HealthCreate) *HealthCreateBulk {
	return &HealthCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *HealthClient) MapCreateBulk(slice any, setFunc func(*HealthCreate, int)) *HealthCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &HealthCreateBulk{err: fmt.Errorf("calling to HealthClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*HealthCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &HealthCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Health.
func (c *HealthClient) Update() *HealthUpdate {
	mutation := newHealthMutation(c.config, OpUpdate)
	return &HealthUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *HealthClient) UpdateOne(_m *Health) *HealthUpdateOne {
	mutation := newHealthMutation(c.config, OpUpdateOne, withHealth(_m))
	return &HealthUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *HealthClient) UpdateOneID(id uuid.UUID) *HealthUpdateOne {
	mutation := newHealthMutation(c.config, OpUpdateOne, withHealthID(id))
	return &HealthUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Health.
func (c *HealthClient) Delete() *HealthDelete {
	mutation := newHealthMutation(c.config, OpDelete)
	return &HealthDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *HealthClient) DeleteOne(_m *Health) *HealthDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *HealthClient) DeleteOneID(id uuid.UUID) *HealthDeleteOne {
	builder := c.Delete().Where(health.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &HealthDeleteOne{builder}
}

// Query returns a query builder for Health.
func (c *HealthClient) Query() *HealthQuery {
	return &HealthQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeHealth},
		inters: c.Interceptors(),
	}
}

// Get returns a Health entity by its id.
func (c *HealthClient) Get(ctx context.Context, id uuid.UUID) (*Health, error) {
	return c.Query().Where(health.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *HealthClient) GetX(ctx context.Context, id uuid.UUID) *Health {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *HealthClient) Hooks() []Hook {
	return c.hooks.Health
}

// Interceptors returns the client interceptors.
func (c *HealthClient) Interceptors() []Interceptor {
	return c.inters.Health
}

func (c *HealthClient) mutate(ctx context.Context, m *HealthMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&HealthCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&HealthUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&HealthUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&HealthDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Health mutation op: %q", m.Op())
	}
}

// ProfitRateClient is a client for the ProfitRate schema.
type ProfitRateClient struct {
	config
}

// NewProfitRateClient returns a client for the ProfitRate from the given config.
func NewProfitRateClient(c config) *ProfitRateClient {
	return &ProfitRateClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `profitrate.Hooks(f(g(h())))`.
func (c *ProfitRateClient) Use(hooks ...Hook) {
	c.hooks.ProfitRate = append(c.hooks.ProfitRate, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `profitrate.Intercept(f(g(h())))`.
func (c *ProfitRateClient) Intercept(interceptors ...Interceptor) {
	c.inters.ProfitRate = append(c.inters.ProfitRate, interceptors...)
}

// Create returns a builder for creating a ProfitRate entity.
func (c *ProfitRateClient) Create() *ProfitRateCreate {
	mutation := newProfitRateMutation(c.config, OpCreate)
	return &ProfitRateCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of ProfitRate entities.
func (c *ProfitRateClient) CreateBulk(builders ...*ProfitRateCreate) *ProfitRateCreateBulk {
	return &ProfitRateCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ProfitRateClient) MapCreateBulk(slice any, setFunc func(*ProfitRateCreate, int)) *ProfitRateCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ProfitRateCreateBulk{err: fmt.Errorf("calling to ProfitRateClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ProfitRateCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ProfitRateCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for ProfitRate.
func (c *ProfitRateClient) Update() *ProfitRateUpdate {
	mutation := newProfitRateMutation(c.config, OpUpdate)
	return &ProfitRateUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ProfitRateClient) UpdateOne(_m *ProfitRate) *ProfitRateUpdateOne {
	mutation := newProfitRateMutation(c.config, OpUpdateOne, withProfitRate(_m))
	return &ProfitRateUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ProfitRateClient) UpdateOneID(id uuid.UUID) *ProfitRateUpdateOne {
	mutation := newProfitRateMutation(c.config, OpUpdateOne, withProfitRateID(id))
	return &ProfitRateUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for ProfitRate.
func (c *ProfitRateClient) Delete() *ProfitRateDelete {
	mutation := newProfitRateMutation(c.config, OpDelete)
	return &ProfitRateDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ProfitRateClient) DeleteOne(_m *ProfitRate) *ProfitRateDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ProfitRateClient) DeleteOneID(id uuid.UUID) *ProfitRateDeleteOne {
	builder := c.Delete().Where(profitrate.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ProfitRateDeleteOne{builder}
}

// Query returns a query builder for ProfitRate.
func (c *ProfitRateClient) Query() *ProfitRateQuery {
	return &ProfitRateQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeProfitRate},
		inters: c.Interceptors(),
	}
}

// Get returns a ProfitRate entity by its id.
func (c *ProfitRateClient) Get(ctx context.Context, id uuid.UUID) (*ProfitRate, error) {
	return c.Query().Where(profitrate.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ProfitRateClient) GetX(ctx context.Context, id uuid.UUID) *ProfitRate {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryCurrency queries the currency edge of a ProfitRate.
func (c *ProfitRateClient) QueryCurrency(_m *ProfitRate) *CurrencyQuery {
	query := (&CurrencyClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := _m.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(profitrate.Table, profitrate.FieldID, id),
			sqlgraph.To(currency.Table, currency.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, profitrate.CurrencyTable, profitrate.CurrencyColumn),
		)
		fromV = sqlgraph.Neighbors(_m.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryRateDepositApplication queries the rate_deposit_application edge of a ProfitRate.
func (c *ProfitRateClient) QueryRateDepositApplication(_m *ProfitRate) *DepositApplicationQuery {
	query := (&DepositApplicationClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := _m.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(profitrate.Table, profitrate.FieldID, id),
			sqlgraph.To(depositapplication.Table, depositapplication.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, profitrate.RateDepositApplicationTable, profitrate.RateDepositApplicationColumn),
		)
		fromV = sqlgraph.Neighbors(_m.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *ProfitRateClient) Hooks() []Hook {
	return c.hooks.ProfitRate
}

// Interceptors returns the client interceptors.
func (c *ProfitRateClient) Interceptors() []Interceptor {
	return c.inters.ProfitRate
}

func (c *ProfitRateClient) mutate(ctx context.Context, m *ProfitRateMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ProfitRateCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ProfitRateUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ProfitRateUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ProfitRateDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown ProfitRate mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		Currency, DepositApplication, DepositCards, DepositProduct, Document, Health,
		ProfitRate []ent.Hook
	}
	inters struct {
		Currency, DepositApplication, DepositCards, DepositProduct, Document, Health,
		ProfitRate []ent.Interceptor
	}
)
