// Code generated by ent, DO NOT EDIT.

package currency

import (
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/shopspring/decimal"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uint) predicate.Currency {
	return predicate.Currency(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uint) predicate.Currency {
	return predicate.Currency(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uint) predicate.Currency {
	return predicate.Currency(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uint) predicate.Currency {
	return predicate.Currency(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uint) predicate.Currency {
	return predicate.Currency(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uint) predicate.Currency {
	return predicate.Currency(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uint) predicate.Currency {
	return predicate.Currency(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uint) predicate.Currency {
	return predicate.Currency(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uint) predicate.Currency {
	return predicate.Currency(sql.FieldLTE(FieldID, id))
}

// ProductID applies equality check predicate on the "product_id" field. It's identical to ProductIDEQ.
func ProductID(v uint) predicate.Currency {
	return predicate.Currency(sql.FieldEQ(FieldProductID, v))
}

// IsActive applies equality check predicate on the "is_active" field. It's identical to IsActiveEQ.
func IsActive(v bool) predicate.Currency {
	return predicate.Currency(sql.FieldEQ(FieldIsActive, v))
}

// MinAmount applies equality check predicate on the "min_amount" field. It's identical to MinAmountEQ.
func MinAmount(v decimal.Decimal) predicate.Currency {
	return predicate.Currency(sql.FieldEQ(FieldMinAmount, v))
}

// MaxAmount applies equality check predicate on the "max_amount" field. It's identical to MaxAmountEQ.
func MaxAmount(v decimal.Decimal) predicate.Currency {
	return predicate.Currency(sql.FieldEQ(FieldMaxAmount, v))
}

// ProductIDEQ applies the EQ predicate on the "product_id" field.
func ProductIDEQ(v uint) predicate.Currency {
	return predicate.Currency(sql.FieldEQ(FieldProductID, v))
}

// ProductIDNEQ applies the NEQ predicate on the "product_id" field.
func ProductIDNEQ(v uint) predicate.Currency {
	return predicate.Currency(sql.FieldNEQ(FieldProductID, v))
}

// ProductIDIn applies the In predicate on the "product_id" field.
func ProductIDIn(vs ...uint) predicate.Currency {
	return predicate.Currency(sql.FieldIn(FieldProductID, vs...))
}

// ProductIDNotIn applies the NotIn predicate on the "product_id" field.
func ProductIDNotIn(vs ...uint) predicate.Currency {
	return predicate.Currency(sql.FieldNotIn(FieldProductID, vs...))
}

// CurrencyEQ applies the EQ predicate on the "currency" field.
func CurrencyEQ(v Currency) predicate.Currency {
	return predicate.Currency(sql.FieldEQ(FieldCurrency, v))
}

// CurrencyNEQ applies the NEQ predicate on the "currency" field.
func CurrencyNEQ(v Currency) predicate.Currency {
	return predicate.Currency(sql.FieldNEQ(FieldCurrency, v))
}

// CurrencyIn applies the In predicate on the "currency" field.
func CurrencyIn(vs ...Currency) predicate.Currency {
	return predicate.Currency(sql.FieldIn(FieldCurrency, vs...))
}

// CurrencyNotIn applies the NotIn predicate on the "currency" field.
func CurrencyNotIn(vs ...Currency) predicate.Currency {
	return predicate.Currency(sql.FieldNotIn(FieldCurrency, vs...))
}

// IsActiveEQ applies the EQ predicate on the "is_active" field.
func IsActiveEQ(v bool) predicate.Currency {
	return predicate.Currency(sql.FieldEQ(FieldIsActive, v))
}

// IsActiveNEQ applies the NEQ predicate on the "is_active" field.
func IsActiveNEQ(v bool) predicate.Currency {
	return predicate.Currency(sql.FieldNEQ(FieldIsActive, v))
}

// MinAmountEQ applies the EQ predicate on the "min_amount" field.
func MinAmountEQ(v decimal.Decimal) predicate.Currency {
	return predicate.Currency(sql.FieldEQ(FieldMinAmount, v))
}

// MinAmountNEQ applies the NEQ predicate on the "min_amount" field.
func MinAmountNEQ(v decimal.Decimal) predicate.Currency {
	return predicate.Currency(sql.FieldNEQ(FieldMinAmount, v))
}

// MinAmountIn applies the In predicate on the "min_amount" field.
func MinAmountIn(vs ...decimal.Decimal) predicate.Currency {
	return predicate.Currency(sql.FieldIn(FieldMinAmount, vs...))
}

// MinAmountNotIn applies the NotIn predicate on the "min_amount" field.
func MinAmountNotIn(vs ...decimal.Decimal) predicate.Currency {
	return predicate.Currency(sql.FieldNotIn(FieldMinAmount, vs...))
}

// MinAmountGT applies the GT predicate on the "min_amount" field.
func MinAmountGT(v decimal.Decimal) predicate.Currency {
	return predicate.Currency(sql.FieldGT(FieldMinAmount, v))
}

// MinAmountGTE applies the GTE predicate on the "min_amount" field.
func MinAmountGTE(v decimal.Decimal) predicate.Currency {
	return predicate.Currency(sql.FieldGTE(FieldMinAmount, v))
}

// MinAmountLT applies the LT predicate on the "min_amount" field.
func MinAmountLT(v decimal.Decimal) predicate.Currency {
	return predicate.Currency(sql.FieldLT(FieldMinAmount, v))
}

// MinAmountLTE applies the LTE predicate on the "min_amount" field.
func MinAmountLTE(v decimal.Decimal) predicate.Currency {
	return predicate.Currency(sql.FieldLTE(FieldMinAmount, v))
}

// MinAmountContains applies the Contains predicate on the "min_amount" field.
func MinAmountContains(v decimal.Decimal) predicate.Currency {
	vc := v.String()
	return predicate.Currency(sql.FieldContains(FieldMinAmount, vc))
}

// MinAmountHasPrefix applies the HasPrefix predicate on the "min_amount" field.
func MinAmountHasPrefix(v decimal.Decimal) predicate.Currency {
	vc := v.String()
	return predicate.Currency(sql.FieldHasPrefix(FieldMinAmount, vc))
}

// MinAmountHasSuffix applies the HasSuffix predicate on the "min_amount" field.
func MinAmountHasSuffix(v decimal.Decimal) predicate.Currency {
	vc := v.String()
	return predicate.Currency(sql.FieldHasSuffix(FieldMinAmount, vc))
}

// MinAmountEqualFold applies the EqualFold predicate on the "min_amount" field.
func MinAmountEqualFold(v decimal.Decimal) predicate.Currency {
	vc := v.String()
	return predicate.Currency(sql.FieldEqualFold(FieldMinAmount, vc))
}

// MinAmountContainsFold applies the ContainsFold predicate on the "min_amount" field.
func MinAmountContainsFold(v decimal.Decimal) predicate.Currency {
	vc := v.String()
	return predicate.Currency(sql.FieldContainsFold(FieldMinAmount, vc))
}

// MaxAmountEQ applies the EQ predicate on the "max_amount" field.
func MaxAmountEQ(v decimal.Decimal) predicate.Currency {
	return predicate.Currency(sql.FieldEQ(FieldMaxAmount, v))
}

// MaxAmountNEQ applies the NEQ predicate on the "max_amount" field.
func MaxAmountNEQ(v decimal.Decimal) predicate.Currency {
	return predicate.Currency(sql.FieldNEQ(FieldMaxAmount, v))
}

// MaxAmountIn applies the In predicate on the "max_amount" field.
func MaxAmountIn(vs ...decimal.Decimal) predicate.Currency {
	return predicate.Currency(sql.FieldIn(FieldMaxAmount, vs...))
}

// MaxAmountNotIn applies the NotIn predicate on the "max_amount" field.
func MaxAmountNotIn(vs ...decimal.Decimal) predicate.Currency {
	return predicate.Currency(sql.FieldNotIn(FieldMaxAmount, vs...))
}

// MaxAmountGT applies the GT predicate on the "max_amount" field.
func MaxAmountGT(v decimal.Decimal) predicate.Currency {
	return predicate.Currency(sql.FieldGT(FieldMaxAmount, v))
}

// MaxAmountGTE applies the GTE predicate on the "max_amount" field.
func MaxAmountGTE(v decimal.Decimal) predicate.Currency {
	return predicate.Currency(sql.FieldGTE(FieldMaxAmount, v))
}

// MaxAmountLT applies the LT predicate on the "max_amount" field.
func MaxAmountLT(v decimal.Decimal) predicate.Currency {
	return predicate.Currency(sql.FieldLT(FieldMaxAmount, v))
}

// MaxAmountLTE applies the LTE predicate on the "max_amount" field.
func MaxAmountLTE(v decimal.Decimal) predicate.Currency {
	return predicate.Currency(sql.FieldLTE(FieldMaxAmount, v))
}

// MaxAmountContains applies the Contains predicate on the "max_amount" field.
func MaxAmountContains(v decimal.Decimal) predicate.Currency {
	vc := v.String()
	return predicate.Currency(sql.FieldContains(FieldMaxAmount, vc))
}

// MaxAmountHasPrefix applies the HasPrefix predicate on the "max_amount" field.
func MaxAmountHasPrefix(v decimal.Decimal) predicate.Currency {
	vc := v.String()
	return predicate.Currency(sql.FieldHasPrefix(FieldMaxAmount, vc))
}

// MaxAmountHasSuffix applies the HasSuffix predicate on the "max_amount" field.
func MaxAmountHasSuffix(v decimal.Decimal) predicate.Currency {
	vc := v.String()
	return predicate.Currency(sql.FieldHasSuffix(FieldMaxAmount, vc))
}

// MaxAmountEqualFold applies the EqualFold predicate on the "max_amount" field.
func MaxAmountEqualFold(v decimal.Decimal) predicate.Currency {
	vc := v.String()
	return predicate.Currency(sql.FieldEqualFold(FieldMaxAmount, vc))
}

// MaxAmountContainsFold applies the ContainsFold predicate on the "max_amount" field.
func MaxAmountContainsFold(v decimal.Decimal) predicate.Currency {
	vc := v.String()
	return predicate.Currency(sql.FieldContainsFold(FieldMaxAmount, vc))
}

// HasProfitRate applies the HasEdge predicate on the "profit_rate" edge.
func HasProfitRate() predicate.Currency {
	return predicate.Currency(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, ProfitRateTable, ProfitRateColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasProfitRateWith applies the HasEdge predicate on the "profit_rate" edge with a given conditions (other predicates).
func HasProfitRateWith(preds ...predicate.ProfitRate) predicate.Currency {
	return predicate.Currency(func(s *sql.Selector) {
		step := newProfitRateStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasProduct applies the HasEdge predicate on the "product" edge.
func HasProduct() predicate.Currency {
	return predicate.Currency(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, ProductTable, ProductColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasProductWith applies the HasEdge predicate on the "product" edge with a given conditions (other predicates).
func HasProductWith(preds ...predicate.DepositProduct) predicate.Currency {
	return predicate.Currency(func(s *sql.Selector) {
		step := newProductStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Currency) predicate.Currency {
	return predicate.Currency(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Currency) predicate.Currency {
	return predicate.Currency(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Currency) predicate.Currency {
	return predicate.Currency(sql.NotPredicates(p))
}
