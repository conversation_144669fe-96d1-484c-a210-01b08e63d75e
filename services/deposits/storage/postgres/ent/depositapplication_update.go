// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositapplication"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/document"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/predicate"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/profitrate"
)

// DepositApplicationUpdate is the builder for updating DepositApplication entities.
type DepositApplicationUpdate struct {
	config
	hooks     []Hook
	mutation  *DepositApplicationMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the DepositApplicationUpdate builder.
func (_u *DepositApplicationUpdate) Where(ps ...predicate.DepositApplication) *DepositApplicationUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetProfitRateID sets the "profit_rate_id" field.
func (_u *DepositApplicationUpdate) SetProfitRateID(v uuid.UUID) *DepositApplicationUpdate {
	_u.mutation.SetProfitRateID(v)
	return _u
}

// SetNillableProfitRateID sets the "profit_rate_id" field if the given value is not nil.
func (_u *DepositApplicationUpdate) SetNillableProfitRateID(v *uuid.UUID) *DepositApplicationUpdate {
	if v != nil {
		_u.SetProfitRateID(*v)
	}
	return _u
}

// SetColvirReferenceID sets the "colvir_reference_id" field.
func (_u *DepositApplicationUpdate) SetColvirReferenceID(v string) *DepositApplicationUpdate {
	_u.mutation.SetColvirReferenceID(v)
	return _u
}

// SetNillableColvirReferenceID sets the "colvir_reference_id" field if the given value is not nil.
func (_u *DepositApplicationUpdate) SetNillableColvirReferenceID(v *string) *DepositApplicationUpdate {
	if v != nil {
		_u.SetColvirReferenceID(*v)
	}
	return _u
}

// ClearColvirReferenceID clears the value of the "colvir_reference_id" field.
func (_u *DepositApplicationUpdate) ClearColvirReferenceID() *DepositApplicationUpdate {
	_u.mutation.ClearColvirReferenceID()
	return _u
}

// SetStatus sets the "status" field.
func (_u *DepositApplicationUpdate) SetStatus(v depositapplication.Status) *DepositApplicationUpdate {
	_u.mutation.SetStatus(v)
	return _u
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (_u *DepositApplicationUpdate) SetNillableStatus(v *depositapplication.Status) *DepositApplicationUpdate {
	if v != nil {
		_u.SetStatus(*v)
	}
	return _u
}

// SetUserID sets the "user_id" field.
func (_u *DepositApplicationUpdate) SetUserID(v uuid.UUID) *DepositApplicationUpdate {
	_u.mutation.SetUserID(v)
	return _u
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (_u *DepositApplicationUpdate) SetNillableUserID(v *uuid.UUID) *DepositApplicationUpdate {
	if v != nil {
		_u.SetUserID(*v)
	}
	return _u
}

// SetUserIin sets the "user_iin" field.
func (_u *DepositApplicationUpdate) SetUserIin(v string) *DepositApplicationUpdate {
	_u.mutation.SetUserIin(v)
	return _u
}

// SetNillableUserIin sets the "user_iin" field if the given value is not nil.
func (_u *DepositApplicationUpdate) SetNillableUserIin(v *string) *DepositApplicationUpdate {
	if v != nil {
		_u.SetUserIin(*v)
	}
	return _u
}

// SetDepositSourceAccountID sets the "deposit_source_account_id" field.
func (_u *DepositApplicationUpdate) SetDepositSourceAccountID(v uuid.UUID) *DepositApplicationUpdate {
	_u.mutation.SetDepositSourceAccountID(v)
	return _u
}

// SetNillableDepositSourceAccountID sets the "deposit_source_account_id" field if the given value is not nil.
func (_u *DepositApplicationUpdate) SetNillableDepositSourceAccountID(v *uuid.UUID) *DepositApplicationUpdate {
	if v != nil {
		_u.SetDepositSourceAccountID(*v)
	}
	return _u
}

// SetDepositAmount sets the "deposit_amount" field.
func (_u *DepositApplicationUpdate) SetDepositAmount(v decimal.Decimal) *DepositApplicationUpdate {
	_u.mutation.SetDepositAmount(v)
	return _u
}

// SetNillableDepositAmount sets the "deposit_amount" field if the given value is not nil.
func (_u *DepositApplicationUpdate) SetNillableDepositAmount(v *decimal.Decimal) *DepositApplicationUpdate {
	if v != nil {
		_u.SetDepositAmount(*v)
	}
	return _u
}

// SetDepositPayoutMethod sets the "deposit_payout_method" field.
func (_u *DepositApplicationUpdate) SetDepositPayoutMethod(v depositapplication.DepositPayoutMethod) *DepositApplicationUpdate {
	_u.mutation.SetDepositPayoutMethod(v)
	return _u
}

// SetNillableDepositPayoutMethod sets the "deposit_payout_method" field if the given value is not nil.
func (_u *DepositApplicationUpdate) SetNillableDepositPayoutMethod(v *depositapplication.DepositPayoutMethod) *DepositApplicationUpdate {
	if v != nil {
		_u.SetDepositPayoutMethod(*v)
	}
	return _u
}

// SetClosedAt sets the "closed_at" field.
func (_u *DepositApplicationUpdate) SetClosedAt(v time.Time) *DepositApplicationUpdate {
	_u.mutation.SetClosedAt(v)
	return _u
}

// SetNillableClosedAt sets the "closed_at" field if the given value is not nil.
func (_u *DepositApplicationUpdate) SetNillableClosedAt(v *time.Time) *DepositApplicationUpdate {
	if v != nil {
		_u.SetClosedAt(*v)
	}
	return _u
}

// ClearClosedAt clears the value of the "closed_at" field.
func (_u *DepositApplicationUpdate) ClearClosedAt() *DepositApplicationUpdate {
	_u.mutation.ClearClosedAt()
	return _u
}

// AddDocumentIDs adds the "documents" edge to the Document entity by IDs.
func (_u *DepositApplicationUpdate) AddDocumentIDs(ids ...uuid.UUID) *DepositApplicationUpdate {
	_u.mutation.AddDocumentIDs(ids...)
	return _u
}

// AddDocuments adds the "documents" edges to the Document entity.
func (_u *DepositApplicationUpdate) AddDocuments(v ...*Document) *DepositApplicationUpdate {
	ids := make([]uuid.UUID, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _u.AddDocumentIDs(ids...)
}

// SetRateID sets the "rate" edge to the ProfitRate entity by ID.
func (_u *DepositApplicationUpdate) SetRateID(id uuid.UUID) *DepositApplicationUpdate {
	_u.mutation.SetRateID(id)
	return _u
}

// SetRate sets the "rate" edge to the ProfitRate entity.
func (_u *DepositApplicationUpdate) SetRate(v *ProfitRate) *DepositApplicationUpdate {
	return _u.SetRateID(v.ID)
}

// Mutation returns the DepositApplicationMutation object of the builder.
func (_u *DepositApplicationUpdate) Mutation() *DepositApplicationMutation {
	return _u.mutation
}

// ClearDocuments clears all "documents" edges to the Document entity.
func (_u *DepositApplicationUpdate) ClearDocuments() *DepositApplicationUpdate {
	_u.mutation.ClearDocuments()
	return _u
}

// RemoveDocumentIDs removes the "documents" edge to Document entities by IDs.
func (_u *DepositApplicationUpdate) RemoveDocumentIDs(ids ...uuid.UUID) *DepositApplicationUpdate {
	_u.mutation.RemoveDocumentIDs(ids...)
	return _u
}

// RemoveDocuments removes "documents" edges to Document entities.
func (_u *DepositApplicationUpdate) RemoveDocuments(v ...*Document) *DepositApplicationUpdate {
	ids := make([]uuid.UUID, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _u.RemoveDocumentIDs(ids...)
}

// ClearRate clears the "rate" edge to the ProfitRate entity.
func (_u *DepositApplicationUpdate) ClearRate() *DepositApplicationUpdate {
	_u.mutation.ClearRate()
	return _u
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *DepositApplicationUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *DepositApplicationUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *DepositApplicationUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *DepositApplicationUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *DepositApplicationUpdate) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := depositapplication.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *DepositApplicationUpdate) check() error {
	if v, ok := _u.mutation.Status(); ok {
		if err := depositapplication.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "DepositApplication.status": %w`, err)}
		}
	}
	if v, ok := _u.mutation.DepositPayoutMethod(); ok {
		if err := depositapplication.DepositPayoutMethodValidator(v); err != nil {
			return &ValidationError{Name: "deposit_payout_method", err: fmt.Errorf(`ent: validator failed for field "DepositApplication.deposit_payout_method": %w`, err)}
		}
	}
	if _u.mutation.RateCleared() && len(_u.mutation.RateIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "DepositApplication.rate"`)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *DepositApplicationUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DepositApplicationUpdate {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *DepositApplicationUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(depositapplication.Table, depositapplication.Columns, sqlgraph.NewFieldSpec(depositapplication.FieldID, field.TypeUUID))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(depositapplication.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(depositapplication.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(depositapplication.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.ColvirReferenceID(); ok {
		_spec.SetField(depositapplication.FieldColvirReferenceID, field.TypeString, value)
	}
	if _u.mutation.ColvirReferenceIDCleared() {
		_spec.ClearField(depositapplication.FieldColvirReferenceID, field.TypeString)
	}
	if value, ok := _u.mutation.Status(); ok {
		_spec.SetField(depositapplication.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.UserID(); ok {
		_spec.SetField(depositapplication.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := _u.mutation.UserIin(); ok {
		_spec.SetField(depositapplication.FieldUserIin, field.TypeString, value)
	}
	if value, ok := _u.mutation.DepositSourceAccountID(); ok {
		_spec.SetField(depositapplication.FieldDepositSourceAccountID, field.TypeUUID, value)
	}
	if value, ok := _u.mutation.DepositAmount(); ok {
		_spec.SetField(depositapplication.FieldDepositAmount, field.TypeString, value)
	}
	if value, ok := _u.mutation.DepositPayoutMethod(); ok {
		_spec.SetField(depositapplication.FieldDepositPayoutMethod, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.ClosedAt(); ok {
		_spec.SetField(depositapplication.FieldClosedAt, field.TypeTime, value)
	}
	if _u.mutation.ClosedAtCleared() {
		_spec.ClearField(depositapplication.FieldClosedAt, field.TypeTime)
	}
	if _u.mutation.DocumentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   depositapplication.DocumentsTable,
			Columns: []string{depositapplication.DocumentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(document.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.RemovedDocumentsIDs(); len(nodes) > 0 && !_u.mutation.DocumentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   depositapplication.DocumentsTable,
			Columns: []string{depositapplication.DocumentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(document.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.DocumentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   depositapplication.DocumentsTable,
			Columns: []string{depositapplication.DocumentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(document.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if _u.mutation.RateCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   depositapplication.RateTable,
			Columns: []string{depositapplication.RateColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(profitrate.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.RateIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   depositapplication.RateTable,
			Columns: []string{depositapplication.RateColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(profitrate.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(_u.modifiers...)
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{depositapplication.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// DepositApplicationUpdateOne is the builder for updating a single DepositApplication entity.
type DepositApplicationUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *DepositApplicationMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetProfitRateID sets the "profit_rate_id" field.
func (_u *DepositApplicationUpdateOne) SetProfitRateID(v uuid.UUID) *DepositApplicationUpdateOne {
	_u.mutation.SetProfitRateID(v)
	return _u
}

// SetNillableProfitRateID sets the "profit_rate_id" field if the given value is not nil.
func (_u *DepositApplicationUpdateOne) SetNillableProfitRateID(v *uuid.UUID) *DepositApplicationUpdateOne {
	if v != nil {
		_u.SetProfitRateID(*v)
	}
	return _u
}

// SetColvirReferenceID sets the "colvir_reference_id" field.
func (_u *DepositApplicationUpdateOne) SetColvirReferenceID(v string) *DepositApplicationUpdateOne {
	_u.mutation.SetColvirReferenceID(v)
	return _u
}

// SetNillableColvirReferenceID sets the "colvir_reference_id" field if the given value is not nil.
func (_u *DepositApplicationUpdateOne) SetNillableColvirReferenceID(v *string) *DepositApplicationUpdateOne {
	if v != nil {
		_u.SetColvirReferenceID(*v)
	}
	return _u
}

// ClearColvirReferenceID clears the value of the "colvir_reference_id" field.
func (_u *DepositApplicationUpdateOne) ClearColvirReferenceID() *DepositApplicationUpdateOne {
	_u.mutation.ClearColvirReferenceID()
	return _u
}

// SetStatus sets the "status" field.
func (_u *DepositApplicationUpdateOne) SetStatus(v depositapplication.Status) *DepositApplicationUpdateOne {
	_u.mutation.SetStatus(v)
	return _u
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (_u *DepositApplicationUpdateOne) SetNillableStatus(v *depositapplication.Status) *DepositApplicationUpdateOne {
	if v != nil {
		_u.SetStatus(*v)
	}
	return _u
}

// SetUserID sets the "user_id" field.
func (_u *DepositApplicationUpdateOne) SetUserID(v uuid.UUID) *DepositApplicationUpdateOne {
	_u.mutation.SetUserID(v)
	return _u
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (_u *DepositApplicationUpdateOne) SetNillableUserID(v *uuid.UUID) *DepositApplicationUpdateOne {
	if v != nil {
		_u.SetUserID(*v)
	}
	return _u
}

// SetUserIin sets the "user_iin" field.
func (_u *DepositApplicationUpdateOne) SetUserIin(v string) *DepositApplicationUpdateOne {
	_u.mutation.SetUserIin(v)
	return _u
}

// SetNillableUserIin sets the "user_iin" field if the given value is not nil.
func (_u *DepositApplicationUpdateOne) SetNillableUserIin(v *string) *DepositApplicationUpdateOne {
	if v != nil {
		_u.SetUserIin(*v)
	}
	return _u
}

// SetDepositSourceAccountID sets the "deposit_source_account_id" field.
func (_u *DepositApplicationUpdateOne) SetDepositSourceAccountID(v uuid.UUID) *DepositApplicationUpdateOne {
	_u.mutation.SetDepositSourceAccountID(v)
	return _u
}

// SetNillableDepositSourceAccountID sets the "deposit_source_account_id" field if the given value is not nil.
func (_u *DepositApplicationUpdateOne) SetNillableDepositSourceAccountID(v *uuid.UUID) *DepositApplicationUpdateOne {
	if v != nil {
		_u.SetDepositSourceAccountID(*v)
	}
	return _u
}

// SetDepositAmount sets the "deposit_amount" field.
func (_u *DepositApplicationUpdateOne) SetDepositAmount(v decimal.Decimal) *DepositApplicationUpdateOne {
	_u.mutation.SetDepositAmount(v)
	return _u
}

// SetNillableDepositAmount sets the "deposit_amount" field if the given value is not nil.
func (_u *DepositApplicationUpdateOne) SetNillableDepositAmount(v *decimal.Decimal) *DepositApplicationUpdateOne {
	if v != nil {
		_u.SetDepositAmount(*v)
	}
	return _u
}

// SetDepositPayoutMethod sets the "deposit_payout_method" field.
func (_u *DepositApplicationUpdateOne) SetDepositPayoutMethod(v depositapplication.DepositPayoutMethod) *DepositApplicationUpdateOne {
	_u.mutation.SetDepositPayoutMethod(v)
	return _u
}

// SetNillableDepositPayoutMethod sets the "deposit_payout_method" field if the given value is not nil.
func (_u *DepositApplicationUpdateOne) SetNillableDepositPayoutMethod(v *depositapplication.DepositPayoutMethod) *DepositApplicationUpdateOne {
	if v != nil {
		_u.SetDepositPayoutMethod(*v)
	}
	return _u
}

// SetClosedAt sets the "closed_at" field.
func (_u *DepositApplicationUpdateOne) SetClosedAt(v time.Time) *DepositApplicationUpdateOne {
	_u.mutation.SetClosedAt(v)
	return _u
}

// SetNillableClosedAt sets the "closed_at" field if the given value is not nil.
func (_u *DepositApplicationUpdateOne) SetNillableClosedAt(v *time.Time) *DepositApplicationUpdateOne {
	if v != nil {
		_u.SetClosedAt(*v)
	}
	return _u
}

// ClearClosedAt clears the value of the "closed_at" field.
func (_u *DepositApplicationUpdateOne) ClearClosedAt() *DepositApplicationUpdateOne {
	_u.mutation.ClearClosedAt()
	return _u
}

// AddDocumentIDs adds the "documents" edge to the Document entity by IDs.
func (_u *DepositApplicationUpdateOne) AddDocumentIDs(ids ...uuid.UUID) *DepositApplicationUpdateOne {
	_u.mutation.AddDocumentIDs(ids...)
	return _u
}

// AddDocuments adds the "documents" edges to the Document entity.
func (_u *DepositApplicationUpdateOne) AddDocuments(v ...*Document) *DepositApplicationUpdateOne {
	ids := make([]uuid.UUID, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _u.AddDocumentIDs(ids...)
}

// SetRateID sets the "rate" edge to the ProfitRate entity by ID.
func (_u *DepositApplicationUpdateOne) SetRateID(id uuid.UUID) *DepositApplicationUpdateOne {
	_u.mutation.SetRateID(id)
	return _u
}

// SetRate sets the "rate" edge to the ProfitRate entity.
func (_u *DepositApplicationUpdateOne) SetRate(v *ProfitRate) *DepositApplicationUpdateOne {
	return _u.SetRateID(v.ID)
}

// Mutation returns the DepositApplicationMutation object of the builder.
func (_u *DepositApplicationUpdateOne) Mutation() *DepositApplicationMutation {
	return _u.mutation
}

// ClearDocuments clears all "documents" edges to the Document entity.
func (_u *DepositApplicationUpdateOne) ClearDocuments() *DepositApplicationUpdateOne {
	_u.mutation.ClearDocuments()
	return _u
}

// RemoveDocumentIDs removes the "documents" edge to Document entities by IDs.
func (_u *DepositApplicationUpdateOne) RemoveDocumentIDs(ids ...uuid.UUID) *DepositApplicationUpdateOne {
	_u.mutation.RemoveDocumentIDs(ids...)
	return _u
}

// RemoveDocuments removes "documents" edges to Document entities.
func (_u *DepositApplicationUpdateOne) RemoveDocuments(v ...*Document) *DepositApplicationUpdateOne {
	ids := make([]uuid.UUID, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _u.RemoveDocumentIDs(ids...)
}

// ClearRate clears the "rate" edge to the ProfitRate entity.
func (_u *DepositApplicationUpdateOne) ClearRate() *DepositApplicationUpdateOne {
	_u.mutation.ClearRate()
	return _u
}

// Where appends a list predicates to the DepositApplicationUpdate builder.
func (_u *DepositApplicationUpdateOne) Where(ps ...predicate.DepositApplication) *DepositApplicationUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *DepositApplicationUpdateOne) Select(field string, fields ...string) *DepositApplicationUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated DepositApplication entity.
func (_u *DepositApplicationUpdateOne) Save(ctx context.Context) (*DepositApplication, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *DepositApplicationUpdateOne) SaveX(ctx context.Context) *DepositApplication {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *DepositApplicationUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *DepositApplicationUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *DepositApplicationUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := depositapplication.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *DepositApplicationUpdateOne) check() error {
	if v, ok := _u.mutation.Status(); ok {
		if err := depositapplication.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "DepositApplication.status": %w`, err)}
		}
	}
	if v, ok := _u.mutation.DepositPayoutMethod(); ok {
		if err := depositapplication.DepositPayoutMethodValidator(v); err != nil {
			return &ValidationError{Name: "deposit_payout_method", err: fmt.Errorf(`ent: validator failed for field "DepositApplication.deposit_payout_method": %w`, err)}
		}
	}
	if _u.mutation.RateCleared() && len(_u.mutation.RateIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "DepositApplication.rate"`)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *DepositApplicationUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DepositApplicationUpdateOne {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *DepositApplicationUpdateOne) sqlSave(ctx context.Context) (_node *DepositApplication, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(depositapplication.Table, depositapplication.Columns, sqlgraph.NewFieldSpec(depositapplication.FieldID, field.TypeUUID))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "DepositApplication.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, depositapplication.FieldID)
		for _, f := range fields {
			if !depositapplication.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != depositapplication.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(depositapplication.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(depositapplication.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(depositapplication.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.ColvirReferenceID(); ok {
		_spec.SetField(depositapplication.FieldColvirReferenceID, field.TypeString, value)
	}
	if _u.mutation.ColvirReferenceIDCleared() {
		_spec.ClearField(depositapplication.FieldColvirReferenceID, field.TypeString)
	}
	if value, ok := _u.mutation.Status(); ok {
		_spec.SetField(depositapplication.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.UserID(); ok {
		_spec.SetField(depositapplication.FieldUserID, field.TypeUUID, value)
	}
	if value, ok := _u.mutation.UserIin(); ok {
		_spec.SetField(depositapplication.FieldUserIin, field.TypeString, value)
	}
	if value, ok := _u.mutation.DepositSourceAccountID(); ok {
		_spec.SetField(depositapplication.FieldDepositSourceAccountID, field.TypeUUID, value)
	}
	if value, ok := _u.mutation.DepositAmount(); ok {
		_spec.SetField(depositapplication.FieldDepositAmount, field.TypeString, value)
	}
	if value, ok := _u.mutation.DepositPayoutMethod(); ok {
		_spec.SetField(depositapplication.FieldDepositPayoutMethod, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.ClosedAt(); ok {
		_spec.SetField(depositapplication.FieldClosedAt, field.TypeTime, value)
	}
	if _u.mutation.ClosedAtCleared() {
		_spec.ClearField(depositapplication.FieldClosedAt, field.TypeTime)
	}
	if _u.mutation.DocumentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   depositapplication.DocumentsTable,
			Columns: []string{depositapplication.DocumentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(document.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.RemovedDocumentsIDs(); len(nodes) > 0 && !_u.mutation.DocumentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   depositapplication.DocumentsTable,
			Columns: []string{depositapplication.DocumentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(document.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.DocumentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   depositapplication.DocumentsTable,
			Columns: []string{depositapplication.DocumentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(document.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if _u.mutation.RateCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   depositapplication.RateTable,
			Columns: []string{depositapplication.RateColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(profitrate.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.RateIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   depositapplication.RateTable,
			Columns: []string{depositapplication.RateColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(profitrate.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(_u.modifiers...)
	_node = &DepositApplication{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{depositapplication.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
