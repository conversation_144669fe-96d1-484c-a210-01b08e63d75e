// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/health"
)

// Health is the model entity for the Health schema.
type Health struct {
	config
	// ID of the ent.
	ID           uuid.UUID `json:"id,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Health) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case health.FieldID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Health fields.
func (_m *Health) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case health.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				_m.ID = *value
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Health.
// This includes values selected through modifiers, order, etc.
func (_m *Health) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// Update returns a builder for updating this Health.
// Note that you need to call Health.Unwrap() before calling this method if this Health
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *Health) Update() *HealthUpdateOne {
	return NewHealthClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the Health entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *Health) Unwrap() *Health {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("ent: Health is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *Health) String() string {
	var builder strings.Builder
	builder.WriteString("Health(")
	builder.WriteString(fmt.Sprintf("id=%v", _m.ID))
	builder.WriteByte(')')
	return builder.String()
}

// Healths is a parsable slice of Health.
type Healths []*Health
