// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/currency"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositproduct"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/predicate"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/profitrate"
)

// CurrencyUpdate is the builder for updating Currency entities.
type CurrencyUpdate struct {
	config
	hooks     []Hook
	mutation  *CurrencyMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the CurrencyUpdate builder.
func (_u *CurrencyUpdate) Where(ps ...predicate.Currency) *CurrencyUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetProductID sets the "product_id" field.
func (_u *CurrencyUpdate) SetProductID(v uint) *CurrencyUpdate {
	_u.mutation.SetProductID(v)
	return _u
}

// SetNillableProductID sets the "product_id" field if the given value is not nil.
func (_u *CurrencyUpdate) SetNillableProductID(v *uint) *CurrencyUpdate {
	if v != nil {
		_u.SetProductID(*v)
	}
	return _u
}

// SetIsActive sets the "is_active" field.
func (_u *CurrencyUpdate) SetIsActive(v bool) *CurrencyUpdate {
	_u.mutation.SetIsActive(v)
	return _u
}

// SetNillableIsActive sets the "is_active" field if the given value is not nil.
func (_u *CurrencyUpdate) SetNillableIsActive(v *bool) *CurrencyUpdate {
	if v != nil {
		_u.SetIsActive(*v)
	}
	return _u
}

// SetMinAmount sets the "min_amount" field.
func (_u *CurrencyUpdate) SetMinAmount(v decimal.Decimal) *CurrencyUpdate {
	_u.mutation.SetMinAmount(v)
	return _u
}

// SetNillableMinAmount sets the "min_amount" field if the given value is not nil.
func (_u *CurrencyUpdate) SetNillableMinAmount(v *decimal.Decimal) *CurrencyUpdate {
	if v != nil {
		_u.SetMinAmount(*v)
	}
	return _u
}

// SetMaxAmount sets the "max_amount" field.
func (_u *CurrencyUpdate) SetMaxAmount(v decimal.Decimal) *CurrencyUpdate {
	_u.mutation.SetMaxAmount(v)
	return _u
}

// SetNillableMaxAmount sets the "max_amount" field if the given value is not nil.
func (_u *CurrencyUpdate) SetNillableMaxAmount(v *decimal.Decimal) *CurrencyUpdate {
	if v != nil {
		_u.SetMaxAmount(*v)
	}
	return _u
}

// AddProfitRateIDs adds the "profit_rate" edge to the ProfitRate entity by IDs.
func (_u *CurrencyUpdate) AddProfitRateIDs(ids ...uuid.UUID) *CurrencyUpdate {
	_u.mutation.AddProfitRateIDs(ids...)
	return _u
}

// AddProfitRate adds the "profit_rate" edges to the ProfitRate entity.
func (_u *CurrencyUpdate) AddProfitRate(v ...*ProfitRate) *CurrencyUpdate {
	ids := make([]uuid.UUID, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _u.AddProfitRateIDs(ids...)
}

// SetProduct sets the "product" edge to the DepositProduct entity.
func (_u *CurrencyUpdate) SetProduct(v *DepositProduct) *CurrencyUpdate {
	return _u.SetProductID(v.ID)
}

// Mutation returns the CurrencyMutation object of the builder.
func (_u *CurrencyUpdate) Mutation() *CurrencyMutation {
	return _u.mutation
}

// ClearProfitRate clears all "profit_rate" edges to the ProfitRate entity.
func (_u *CurrencyUpdate) ClearProfitRate() *CurrencyUpdate {
	_u.mutation.ClearProfitRate()
	return _u
}

// RemoveProfitRateIDs removes the "profit_rate" edge to ProfitRate entities by IDs.
func (_u *CurrencyUpdate) RemoveProfitRateIDs(ids ...uuid.UUID) *CurrencyUpdate {
	_u.mutation.RemoveProfitRateIDs(ids...)
	return _u
}

// RemoveProfitRate removes "profit_rate" edges to ProfitRate entities.
func (_u *CurrencyUpdate) RemoveProfitRate(v ...*ProfitRate) *CurrencyUpdate {
	ids := make([]uuid.UUID, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _u.RemoveProfitRateIDs(ids...)
}

// ClearProduct clears the "product" edge to the DepositProduct entity.
func (_u *CurrencyUpdate) ClearProduct() *CurrencyUpdate {
	_u.mutation.ClearProduct()
	return _u
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *CurrencyUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *CurrencyUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *CurrencyUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *CurrencyUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *CurrencyUpdate) check() error {
	if _u.mutation.ProductCleared() && len(_u.mutation.ProductIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "Currency.product"`)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *CurrencyUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *CurrencyUpdate {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *CurrencyUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(currency.Table, currency.Columns, sqlgraph.NewFieldSpec(currency.FieldID, field.TypeUint))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.IsActive(); ok {
		_spec.SetField(currency.FieldIsActive, field.TypeBool, value)
	}
	if value, ok := _u.mutation.MinAmount(); ok {
		_spec.SetField(currency.FieldMinAmount, field.TypeString, value)
	}
	if value, ok := _u.mutation.MaxAmount(); ok {
		_spec.SetField(currency.FieldMaxAmount, field.TypeString, value)
	}
	if _u.mutation.ProfitRateCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   currency.ProfitRateTable,
			Columns: []string{currency.ProfitRateColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(profitrate.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.RemovedProfitRateIDs(); len(nodes) > 0 && !_u.mutation.ProfitRateCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   currency.ProfitRateTable,
			Columns: []string{currency.ProfitRateColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(profitrate.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.ProfitRateIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   currency.ProfitRateTable,
			Columns: []string{currency.ProfitRateColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(profitrate.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if _u.mutation.ProductCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   currency.ProductTable,
			Columns: []string{currency.ProductColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(depositproduct.FieldID, field.TypeUint),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.ProductIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   currency.ProductTable,
			Columns: []string{currency.ProductColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(depositproduct.FieldID, field.TypeUint),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(_u.modifiers...)
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{currency.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// CurrencyUpdateOne is the builder for updating a single Currency entity.
type CurrencyUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *CurrencyMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetProductID sets the "product_id" field.
func (_u *CurrencyUpdateOne) SetProductID(v uint) *CurrencyUpdateOne {
	_u.mutation.SetProductID(v)
	return _u
}

// SetNillableProductID sets the "product_id" field if the given value is not nil.
func (_u *CurrencyUpdateOne) SetNillableProductID(v *uint) *CurrencyUpdateOne {
	if v != nil {
		_u.SetProductID(*v)
	}
	return _u
}

// SetIsActive sets the "is_active" field.
func (_u *CurrencyUpdateOne) SetIsActive(v bool) *CurrencyUpdateOne {
	_u.mutation.SetIsActive(v)
	return _u
}

// SetNillableIsActive sets the "is_active" field if the given value is not nil.
func (_u *CurrencyUpdateOne) SetNillableIsActive(v *bool) *CurrencyUpdateOne {
	if v != nil {
		_u.SetIsActive(*v)
	}
	return _u
}

// SetMinAmount sets the "min_amount" field.
func (_u *CurrencyUpdateOne) SetMinAmount(v decimal.Decimal) *CurrencyUpdateOne {
	_u.mutation.SetMinAmount(v)
	return _u
}

// SetNillableMinAmount sets the "min_amount" field if the given value is not nil.
func (_u *CurrencyUpdateOne) SetNillableMinAmount(v *decimal.Decimal) *CurrencyUpdateOne {
	if v != nil {
		_u.SetMinAmount(*v)
	}
	return _u
}

// SetMaxAmount sets the "max_amount" field.
func (_u *CurrencyUpdateOne) SetMaxAmount(v decimal.Decimal) *CurrencyUpdateOne {
	_u.mutation.SetMaxAmount(v)
	return _u
}

// SetNillableMaxAmount sets the "max_amount" field if the given value is not nil.
func (_u *CurrencyUpdateOne) SetNillableMaxAmount(v *decimal.Decimal) *CurrencyUpdateOne {
	if v != nil {
		_u.SetMaxAmount(*v)
	}
	return _u
}

// AddProfitRateIDs adds the "profit_rate" edge to the ProfitRate entity by IDs.
func (_u *CurrencyUpdateOne) AddProfitRateIDs(ids ...uuid.UUID) *CurrencyUpdateOne {
	_u.mutation.AddProfitRateIDs(ids...)
	return _u
}

// AddProfitRate adds the "profit_rate" edges to the ProfitRate entity.
func (_u *CurrencyUpdateOne) AddProfitRate(v ...*ProfitRate) *CurrencyUpdateOne {
	ids := make([]uuid.UUID, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _u.AddProfitRateIDs(ids...)
}

// SetProduct sets the "product" edge to the DepositProduct entity.
func (_u *CurrencyUpdateOne) SetProduct(v *DepositProduct) *CurrencyUpdateOne {
	return _u.SetProductID(v.ID)
}

// Mutation returns the CurrencyMutation object of the builder.
func (_u *CurrencyUpdateOne) Mutation() *CurrencyMutation {
	return _u.mutation
}

// ClearProfitRate clears all "profit_rate" edges to the ProfitRate entity.
func (_u *CurrencyUpdateOne) ClearProfitRate() *CurrencyUpdateOne {
	_u.mutation.ClearProfitRate()
	return _u
}

// RemoveProfitRateIDs removes the "profit_rate" edge to ProfitRate entities by IDs.
func (_u *CurrencyUpdateOne) RemoveProfitRateIDs(ids ...uuid.UUID) *CurrencyUpdateOne {
	_u.mutation.RemoveProfitRateIDs(ids...)
	return _u
}

// RemoveProfitRate removes "profit_rate" edges to ProfitRate entities.
func (_u *CurrencyUpdateOne) RemoveProfitRate(v ...*ProfitRate) *CurrencyUpdateOne {
	ids := make([]uuid.UUID, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _u.RemoveProfitRateIDs(ids...)
}

// ClearProduct clears the "product" edge to the DepositProduct entity.
func (_u *CurrencyUpdateOne) ClearProduct() *CurrencyUpdateOne {
	_u.mutation.ClearProduct()
	return _u
}

// Where appends a list predicates to the CurrencyUpdate builder.
func (_u *CurrencyUpdateOne) Where(ps ...predicate.Currency) *CurrencyUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *CurrencyUpdateOne) Select(field string, fields ...string) *CurrencyUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated Currency entity.
func (_u *CurrencyUpdateOne) Save(ctx context.Context) (*Currency, error) {
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *CurrencyUpdateOne) SaveX(ctx context.Context) *Currency {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *CurrencyUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *CurrencyUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *CurrencyUpdateOne) check() error {
	if _u.mutation.ProductCleared() && len(_u.mutation.ProductIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "Currency.product"`)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *CurrencyUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *CurrencyUpdateOne {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *CurrencyUpdateOne) sqlSave(ctx context.Context) (_node *Currency, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(currency.Table, currency.Columns, sqlgraph.NewFieldSpec(currency.FieldID, field.TypeUint))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Currency.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, currency.FieldID)
		for _, f := range fields {
			if !currency.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != currency.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.IsActive(); ok {
		_spec.SetField(currency.FieldIsActive, field.TypeBool, value)
	}
	if value, ok := _u.mutation.MinAmount(); ok {
		_spec.SetField(currency.FieldMinAmount, field.TypeString, value)
	}
	if value, ok := _u.mutation.MaxAmount(); ok {
		_spec.SetField(currency.FieldMaxAmount, field.TypeString, value)
	}
	if _u.mutation.ProfitRateCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   currency.ProfitRateTable,
			Columns: []string{currency.ProfitRateColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(profitrate.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.RemovedProfitRateIDs(); len(nodes) > 0 && !_u.mutation.ProfitRateCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   currency.ProfitRateTable,
			Columns: []string{currency.ProfitRateColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(profitrate.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.ProfitRateIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   currency.ProfitRateTable,
			Columns: []string{currency.ProfitRateColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(profitrate.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if _u.mutation.ProductCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   currency.ProductTable,
			Columns: []string{currency.ProductColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(depositproduct.FieldID, field.TypeUint),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.ProductIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   currency.ProductTable,
			Columns: []string{currency.ProductColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(depositproduct.FieldID, field.TypeUint),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(_u.modifiers...)
	_node = &Currency{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{currency.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
