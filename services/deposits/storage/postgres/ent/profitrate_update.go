// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/currency"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositapplication"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/predicate"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/profitrate"
)

// ProfitRateUpdate is the builder for updating ProfitRate entities.
type ProfitRateUpdate struct {
	config
	hooks     []Hook
	mutation  *ProfitRateMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the ProfitRateUpdate builder.
func (_u *ProfitRateUpdate) Where(ps ...predicate.ProfitRate) *ProfitRateUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetCurrencyID sets the "currency_id" field.
func (_u *ProfitRateUpdate) SetCurrencyID(v uint) *ProfitRateUpdate {
	_u.mutation.SetCurrencyID(v)
	return _u
}

// SetNillableCurrencyID sets the "currency_id" field if the given value is not nil.
func (_u *ProfitRateUpdate) SetNillableCurrencyID(v *uint) *ProfitRateUpdate {
	if v != nil {
		_u.SetCurrencyID(*v)
	}
	return _u
}

// SetDateFrom sets the "date_from" field.
func (_u *ProfitRateUpdate) SetDateFrom(v time.Time) *ProfitRateUpdate {
	_u.mutation.SetDateFrom(v)
	return _u
}

// SetNillableDateFrom sets the "date_from" field if the given value is not nil.
func (_u *ProfitRateUpdate) SetNillableDateFrom(v *time.Time) *ProfitRateUpdate {
	if v != nil {
		_u.SetDateFrom(*v)
	}
	return _u
}

// SetDateTo sets the "date_to" field.
func (_u *ProfitRateUpdate) SetDateTo(v time.Time) *ProfitRateUpdate {
	_u.mutation.SetDateTo(v)
	return _u
}

// SetNillableDateTo sets the "date_to" field if the given value is not nil.
func (_u *ProfitRateUpdate) SetNillableDateTo(v *time.Time) *ProfitRateUpdate {
	if v != nil {
		_u.SetDateTo(*v)
	}
	return _u
}

// SetCurrency sets the "currency" edge to the Currency entity.
func (_u *ProfitRateUpdate) SetCurrency(v *Currency) *ProfitRateUpdate {
	return _u.SetCurrencyID(v.ID)
}

// AddRateDepositApplicationIDs adds the "rate_deposit_application" edge to the DepositApplication entity by IDs.
func (_u *ProfitRateUpdate) AddRateDepositApplicationIDs(ids ...uuid.UUID) *ProfitRateUpdate {
	_u.mutation.AddRateDepositApplicationIDs(ids...)
	return _u
}

// AddRateDepositApplication adds the "rate_deposit_application" edges to the DepositApplication entity.
func (_u *ProfitRateUpdate) AddRateDepositApplication(v ...*DepositApplication) *ProfitRateUpdate {
	ids := make([]uuid.UUID, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _u.AddRateDepositApplicationIDs(ids...)
}

// Mutation returns the ProfitRateMutation object of the builder.
func (_u *ProfitRateUpdate) Mutation() *ProfitRateMutation {
	return _u.mutation
}

// ClearCurrency clears the "currency" edge to the Currency entity.
func (_u *ProfitRateUpdate) ClearCurrency() *ProfitRateUpdate {
	_u.mutation.ClearCurrency()
	return _u
}

// ClearRateDepositApplication clears all "rate_deposit_application" edges to the DepositApplication entity.
func (_u *ProfitRateUpdate) ClearRateDepositApplication() *ProfitRateUpdate {
	_u.mutation.ClearRateDepositApplication()
	return _u
}

// RemoveRateDepositApplicationIDs removes the "rate_deposit_application" edge to DepositApplication entities by IDs.
func (_u *ProfitRateUpdate) RemoveRateDepositApplicationIDs(ids ...uuid.UUID) *ProfitRateUpdate {
	_u.mutation.RemoveRateDepositApplicationIDs(ids...)
	return _u
}

// RemoveRateDepositApplication removes "rate_deposit_application" edges to DepositApplication entities.
func (_u *ProfitRateUpdate) RemoveRateDepositApplication(v ...*DepositApplication) *ProfitRateUpdate {
	ids := make([]uuid.UUID, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _u.RemoveRateDepositApplicationIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *ProfitRateUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *ProfitRateUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *ProfitRateUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *ProfitRateUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *ProfitRateUpdate) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := profitrate.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *ProfitRateUpdate) check() error {
	if _u.mutation.CurrencyCleared() && len(_u.mutation.CurrencyIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "ProfitRate.currency"`)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *ProfitRateUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *ProfitRateUpdate {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *ProfitRateUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(profitrate.Table, profitrate.Columns, sqlgraph.NewFieldSpec(profitrate.FieldID, field.TypeUUID))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(profitrate.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(profitrate.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(profitrate.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.DateFrom(); ok {
		_spec.SetField(profitrate.FieldDateFrom, field.TypeTime, value)
	}
	if value, ok := _u.mutation.DateTo(); ok {
		_spec.SetField(profitrate.FieldDateTo, field.TypeTime, value)
	}
	if _u.mutation.CurrencyCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   profitrate.CurrencyTable,
			Columns: []string{profitrate.CurrencyColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(currency.FieldID, field.TypeUint),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.CurrencyIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   profitrate.CurrencyTable,
			Columns: []string{profitrate.CurrencyColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(currency.FieldID, field.TypeUint),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if _u.mutation.RateDepositApplicationCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   profitrate.RateDepositApplicationTable,
			Columns: []string{profitrate.RateDepositApplicationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(depositapplication.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.RemovedRateDepositApplicationIDs(); len(nodes) > 0 && !_u.mutation.RateDepositApplicationCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   profitrate.RateDepositApplicationTable,
			Columns: []string{profitrate.RateDepositApplicationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(depositapplication.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.RateDepositApplicationIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   profitrate.RateDepositApplicationTable,
			Columns: []string{profitrate.RateDepositApplicationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(depositapplication.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(_u.modifiers...)
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{profitrate.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// ProfitRateUpdateOne is the builder for updating a single ProfitRate entity.
type ProfitRateUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *ProfitRateMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetCurrencyID sets the "currency_id" field.
func (_u *ProfitRateUpdateOne) SetCurrencyID(v uint) *ProfitRateUpdateOne {
	_u.mutation.SetCurrencyID(v)
	return _u
}

// SetNillableCurrencyID sets the "currency_id" field if the given value is not nil.
func (_u *ProfitRateUpdateOne) SetNillableCurrencyID(v *uint) *ProfitRateUpdateOne {
	if v != nil {
		_u.SetCurrencyID(*v)
	}
	return _u
}

// SetDateFrom sets the "date_from" field.
func (_u *ProfitRateUpdateOne) SetDateFrom(v time.Time) *ProfitRateUpdateOne {
	_u.mutation.SetDateFrom(v)
	return _u
}

// SetNillableDateFrom sets the "date_from" field if the given value is not nil.
func (_u *ProfitRateUpdateOne) SetNillableDateFrom(v *time.Time) *ProfitRateUpdateOne {
	if v != nil {
		_u.SetDateFrom(*v)
	}
	return _u
}

// SetDateTo sets the "date_to" field.
func (_u *ProfitRateUpdateOne) SetDateTo(v time.Time) *ProfitRateUpdateOne {
	_u.mutation.SetDateTo(v)
	return _u
}

// SetNillableDateTo sets the "date_to" field if the given value is not nil.
func (_u *ProfitRateUpdateOne) SetNillableDateTo(v *time.Time) *ProfitRateUpdateOne {
	if v != nil {
		_u.SetDateTo(*v)
	}
	return _u
}

// SetCurrency sets the "currency" edge to the Currency entity.
func (_u *ProfitRateUpdateOne) SetCurrency(v *Currency) *ProfitRateUpdateOne {
	return _u.SetCurrencyID(v.ID)
}

// AddRateDepositApplicationIDs adds the "rate_deposit_application" edge to the DepositApplication entity by IDs.
func (_u *ProfitRateUpdateOne) AddRateDepositApplicationIDs(ids ...uuid.UUID) *ProfitRateUpdateOne {
	_u.mutation.AddRateDepositApplicationIDs(ids...)
	return _u
}

// AddRateDepositApplication adds the "rate_deposit_application" edges to the DepositApplication entity.
func (_u *ProfitRateUpdateOne) AddRateDepositApplication(v ...*DepositApplication) *ProfitRateUpdateOne {
	ids := make([]uuid.UUID, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _u.AddRateDepositApplicationIDs(ids...)
}

// Mutation returns the ProfitRateMutation object of the builder.
func (_u *ProfitRateUpdateOne) Mutation() *ProfitRateMutation {
	return _u.mutation
}

// ClearCurrency clears the "currency" edge to the Currency entity.
func (_u *ProfitRateUpdateOne) ClearCurrency() *ProfitRateUpdateOne {
	_u.mutation.ClearCurrency()
	return _u
}

// ClearRateDepositApplication clears all "rate_deposit_application" edges to the DepositApplication entity.
func (_u *ProfitRateUpdateOne) ClearRateDepositApplication() *ProfitRateUpdateOne {
	_u.mutation.ClearRateDepositApplication()
	return _u
}

// RemoveRateDepositApplicationIDs removes the "rate_deposit_application" edge to DepositApplication entities by IDs.
func (_u *ProfitRateUpdateOne) RemoveRateDepositApplicationIDs(ids ...uuid.UUID) *ProfitRateUpdateOne {
	_u.mutation.RemoveRateDepositApplicationIDs(ids...)
	return _u
}

// RemoveRateDepositApplication removes "rate_deposit_application" edges to DepositApplication entities.
func (_u *ProfitRateUpdateOne) RemoveRateDepositApplication(v ...*DepositApplication) *ProfitRateUpdateOne {
	ids := make([]uuid.UUID, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _u.RemoveRateDepositApplicationIDs(ids...)
}

// Where appends a list predicates to the ProfitRateUpdate builder.
func (_u *ProfitRateUpdateOne) Where(ps ...predicate.ProfitRate) *ProfitRateUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *ProfitRateUpdateOne) Select(field string, fields ...string) *ProfitRateUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated ProfitRate entity.
func (_u *ProfitRateUpdateOne) Save(ctx context.Context) (*ProfitRate, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *ProfitRateUpdateOne) SaveX(ctx context.Context) *ProfitRate {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *ProfitRateUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *ProfitRateUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *ProfitRateUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := profitrate.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *ProfitRateUpdateOne) check() error {
	if _u.mutation.CurrencyCleared() && len(_u.mutation.CurrencyIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "ProfitRate.currency"`)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *ProfitRateUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *ProfitRateUpdateOne {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *ProfitRateUpdateOne) sqlSave(ctx context.Context) (_node *ProfitRate, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(profitrate.Table, profitrate.Columns, sqlgraph.NewFieldSpec(profitrate.FieldID, field.TypeUUID))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "ProfitRate.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, profitrate.FieldID)
		for _, f := range fields {
			if !profitrate.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != profitrate.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(profitrate.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(profitrate.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(profitrate.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.DateFrom(); ok {
		_spec.SetField(profitrate.FieldDateFrom, field.TypeTime, value)
	}
	if value, ok := _u.mutation.DateTo(); ok {
		_spec.SetField(profitrate.FieldDateTo, field.TypeTime, value)
	}
	if _u.mutation.CurrencyCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   profitrate.CurrencyTable,
			Columns: []string{profitrate.CurrencyColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(currency.FieldID, field.TypeUint),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.CurrencyIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   profitrate.CurrencyTable,
			Columns: []string{profitrate.CurrencyColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(currency.FieldID, field.TypeUint),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if _u.mutation.RateDepositApplicationCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   profitrate.RateDepositApplicationTable,
			Columns: []string{profitrate.RateDepositApplicationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(depositapplication.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.RemovedRateDepositApplicationIDs(); len(nodes) > 0 && !_u.mutation.RateDepositApplicationCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   profitrate.RateDepositApplicationTable,
			Columns: []string{profitrate.RateDepositApplicationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(depositapplication.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.RateDepositApplicationIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   profitrate.RateDepositApplicationTable,
			Columns: []string{profitrate.RateDepositApplicationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(depositapplication.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(_u.modifiers...)
	_node = &ProfitRate{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{profitrate.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
