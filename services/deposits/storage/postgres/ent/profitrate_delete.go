// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/predicate"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/profitrate"
)

// ProfitRateDelete is the builder for deleting a ProfitRate entity.
type ProfitRateDelete struct {
	config
	hooks    []Hook
	mutation *ProfitRateMutation
}

// Where appends a list predicates to the ProfitRateDelete builder.
func (_d *ProfitRateDelete) Where(ps ...predicate.ProfitRate) *ProfitRateDelete {
	_d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (_d *ProfitRateDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, _d.sqlExec, _d.mutation, _d.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *ProfitRateDelete) ExecX(ctx context.Context) int {
	n, err := _d.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (_d *ProfitRateDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(profitrate.Table, sqlgraph.NewFieldSpec(profitrate.FieldID, field.TypeUUID))
	if ps := _d.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, _d.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	_d.mutation.done = true
	return affected, err
}

// ProfitRateDeleteOne is the builder for deleting a single ProfitRate entity.
type ProfitRateDeleteOne struct {
	_d *ProfitRateDelete
}

// Where appends a list predicates to the ProfitRateDelete builder.
func (_d *ProfitRateDeleteOne) Where(ps ...predicate.ProfitRate) *ProfitRateDeleteOne {
	_d._d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query.
func (_d *ProfitRateDeleteOne) Exec(ctx context.Context) error {
	n, err := _d._d.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{profitrate.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *ProfitRateDeleteOne) ExecX(ctx context.Context) {
	if err := _d.Exec(ctx); err != nil {
		panic(err)
	}
}
