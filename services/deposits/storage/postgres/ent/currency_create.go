// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/currency"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositproduct"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/profitrate"
)

// CurrencyCreate is the builder for creating a Currency entity.
type CurrencyCreate struct {
	config
	mutation *CurrencyMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetProductID sets the "product_id" field.
func (_c *CurrencyCreate) SetProductID(v uint) *CurrencyCreate {
	_c.mutation.SetProductID(v)
	return _c
}

// SetCurrency sets the "currency" field.
func (_c *CurrencyCreate) SetCurrency(v currency.Currency) *CurrencyCreate {
	_c.mutation.SetCurrency(v)
	return _c
}

// SetIsActive sets the "is_active" field.
func (_c *CurrencyCreate) SetIsActive(v bool) *CurrencyCreate {
	_c.mutation.SetIsActive(v)
	return _c
}

// SetMinAmount sets the "min_amount" field.
func (_c *CurrencyCreate) SetMinAmount(v decimal.Decimal) *CurrencyCreate {
	_c.mutation.SetMinAmount(v)
	return _c
}

// SetMaxAmount sets the "max_amount" field.
func (_c *CurrencyCreate) SetMaxAmount(v decimal.Decimal) *CurrencyCreate {
	_c.mutation.SetMaxAmount(v)
	return _c
}

// SetID sets the "id" field.
func (_c *CurrencyCreate) SetID(v uint) *CurrencyCreate {
	_c.mutation.SetID(v)
	return _c
}

// AddProfitRateIDs adds the "profit_rate" edge to the ProfitRate entity by IDs.
func (_c *CurrencyCreate) AddProfitRateIDs(ids ...uuid.UUID) *CurrencyCreate {
	_c.mutation.AddProfitRateIDs(ids...)
	return _c
}

// AddProfitRate adds the "profit_rate" edges to the ProfitRate entity.
func (_c *CurrencyCreate) AddProfitRate(v ...*ProfitRate) *CurrencyCreate {
	ids := make([]uuid.UUID, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _c.AddProfitRateIDs(ids...)
}

// SetProduct sets the "product" edge to the DepositProduct entity.
func (_c *CurrencyCreate) SetProduct(v *DepositProduct) *CurrencyCreate {
	return _c.SetProductID(v.ID)
}

// Mutation returns the CurrencyMutation object of the builder.
func (_c *CurrencyCreate) Mutation() *CurrencyMutation {
	return _c.mutation
}

// Save creates the Currency in the database.
func (_c *CurrencyCreate) Save(ctx context.Context) (*Currency, error) {
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *CurrencyCreate) SaveX(ctx context.Context) *Currency {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *CurrencyCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *CurrencyCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *CurrencyCreate) check() error {
	if _, ok := _c.mutation.ProductID(); !ok {
		return &ValidationError{Name: "product_id", err: errors.New(`ent: missing required field "Currency.product_id"`)}
	}
	if _, ok := _c.mutation.Currency(); !ok {
		return &ValidationError{Name: "currency", err: errors.New(`ent: missing required field "Currency.currency"`)}
	}
	if v, ok := _c.mutation.Currency(); ok {
		if err := currency.CurrencyValidator(v); err != nil {
			return &ValidationError{Name: "currency", err: fmt.Errorf(`ent: validator failed for field "Currency.currency": %w`, err)}
		}
	}
	if _, ok := _c.mutation.IsActive(); !ok {
		return &ValidationError{Name: "is_active", err: errors.New(`ent: missing required field "Currency.is_active"`)}
	}
	if _, ok := _c.mutation.MinAmount(); !ok {
		return &ValidationError{Name: "min_amount", err: errors.New(`ent: missing required field "Currency.min_amount"`)}
	}
	if _, ok := _c.mutation.MaxAmount(); !ok {
		return &ValidationError{Name: "max_amount", err: errors.New(`ent: missing required field "Currency.max_amount"`)}
	}
	if len(_c.mutation.ProductIDs()) == 0 {
		return &ValidationError{Name: "product", err: errors.New(`ent: missing required edge "Currency.product"`)}
	}
	return nil
}

func (_c *CurrencyCreate) sqlSave(ctx context.Context) (*Currency, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = uint(id)
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *CurrencyCreate) createSpec() (*Currency, *sqlgraph.CreateSpec) {
	var (
		_node = &Currency{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(currency.Table, sqlgraph.NewFieldSpec(currency.FieldID, field.TypeUint))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := _c.mutation.Currency(); ok {
		_spec.SetField(currency.FieldCurrency, field.TypeEnum, value)
		_node.Currency = value
	}
	if value, ok := _c.mutation.IsActive(); ok {
		_spec.SetField(currency.FieldIsActive, field.TypeBool, value)
		_node.IsActive = value
	}
	if value, ok := _c.mutation.MinAmount(); ok {
		_spec.SetField(currency.FieldMinAmount, field.TypeString, value)
		_node.MinAmount = value
	}
	if value, ok := _c.mutation.MaxAmount(); ok {
		_spec.SetField(currency.FieldMaxAmount, field.TypeString, value)
		_node.MaxAmount = value
	}
	if nodes := _c.mutation.ProfitRateIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   currency.ProfitRateTable,
			Columns: []string{currency.ProfitRateColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(profitrate.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := _c.mutation.ProductIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   currency.ProductTable,
			Columns: []string{currency.ProductColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(depositproduct.FieldID, field.TypeUint),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.ProductID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Currency.Create().
//		SetProductID(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.CurrencyUpsert) {
//			SetProductID(v+v).
//		}).
//		Exec(ctx)
func (_c *CurrencyCreate) OnConflict(opts ...sql.ConflictOption) *CurrencyUpsertOne {
	_c.conflict = opts
	return &CurrencyUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Currency.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *CurrencyCreate) OnConflictColumns(columns ...string) *CurrencyUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &CurrencyUpsertOne{
		create: _c,
	}
}

type (
	// CurrencyUpsertOne is the builder for "upsert"-ing
	//  one Currency node.
	CurrencyUpsertOne struct {
		create *CurrencyCreate
	}

	// CurrencyUpsert is the "OnConflict" setter.
	CurrencyUpsert struct {
		*sql.UpdateSet
	}
)

// SetProductID sets the "product_id" field.
func (u *CurrencyUpsert) SetProductID(v uint) *CurrencyUpsert {
	u.Set(currency.FieldProductID, v)
	return u
}

// UpdateProductID sets the "product_id" field to the value that was provided on create.
func (u *CurrencyUpsert) UpdateProductID() *CurrencyUpsert {
	u.SetExcluded(currency.FieldProductID)
	return u
}

// SetIsActive sets the "is_active" field.
func (u *CurrencyUpsert) SetIsActive(v bool) *CurrencyUpsert {
	u.Set(currency.FieldIsActive, v)
	return u
}

// UpdateIsActive sets the "is_active" field to the value that was provided on create.
func (u *CurrencyUpsert) UpdateIsActive() *CurrencyUpsert {
	u.SetExcluded(currency.FieldIsActive)
	return u
}

// SetMinAmount sets the "min_amount" field.
func (u *CurrencyUpsert) SetMinAmount(v decimal.Decimal) *CurrencyUpsert {
	u.Set(currency.FieldMinAmount, v)
	return u
}

// UpdateMinAmount sets the "min_amount" field to the value that was provided on create.
func (u *CurrencyUpsert) UpdateMinAmount() *CurrencyUpsert {
	u.SetExcluded(currency.FieldMinAmount)
	return u
}

// SetMaxAmount sets the "max_amount" field.
func (u *CurrencyUpsert) SetMaxAmount(v decimal.Decimal) *CurrencyUpsert {
	u.Set(currency.FieldMaxAmount, v)
	return u
}

// UpdateMaxAmount sets the "max_amount" field to the value that was provided on create.
func (u *CurrencyUpsert) UpdateMaxAmount() *CurrencyUpsert {
	u.SetExcluded(currency.FieldMaxAmount)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Currency.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(currency.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *CurrencyUpsertOne) UpdateNewValues() *CurrencyUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(currency.FieldID)
		}
		if _, exists := u.create.mutation.Currency(); exists {
			s.SetIgnore(currency.FieldCurrency)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Currency.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *CurrencyUpsertOne) Ignore() *CurrencyUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *CurrencyUpsertOne) DoNothing() *CurrencyUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the CurrencyCreate.OnConflict
// documentation for more info.
func (u *CurrencyUpsertOne) Update(set func(*CurrencyUpsert)) *CurrencyUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&CurrencyUpsert{UpdateSet: update})
	}))
	return u
}

// SetProductID sets the "product_id" field.
func (u *CurrencyUpsertOne) SetProductID(v uint) *CurrencyUpsertOne {
	return u.Update(func(s *CurrencyUpsert) {
		s.SetProductID(v)
	})
}

// UpdateProductID sets the "product_id" field to the value that was provided on create.
func (u *CurrencyUpsertOne) UpdateProductID() *CurrencyUpsertOne {
	return u.Update(func(s *CurrencyUpsert) {
		s.UpdateProductID()
	})
}

// SetIsActive sets the "is_active" field.
func (u *CurrencyUpsertOne) SetIsActive(v bool) *CurrencyUpsertOne {
	return u.Update(func(s *CurrencyUpsert) {
		s.SetIsActive(v)
	})
}

// UpdateIsActive sets the "is_active" field to the value that was provided on create.
func (u *CurrencyUpsertOne) UpdateIsActive() *CurrencyUpsertOne {
	return u.Update(func(s *CurrencyUpsert) {
		s.UpdateIsActive()
	})
}

// SetMinAmount sets the "min_amount" field.
func (u *CurrencyUpsertOne) SetMinAmount(v decimal.Decimal) *CurrencyUpsertOne {
	return u.Update(func(s *CurrencyUpsert) {
		s.SetMinAmount(v)
	})
}

// UpdateMinAmount sets the "min_amount" field to the value that was provided on create.
func (u *CurrencyUpsertOne) UpdateMinAmount() *CurrencyUpsertOne {
	return u.Update(func(s *CurrencyUpsert) {
		s.UpdateMinAmount()
	})
}

// SetMaxAmount sets the "max_amount" field.
func (u *CurrencyUpsertOne) SetMaxAmount(v decimal.Decimal) *CurrencyUpsertOne {
	return u.Update(func(s *CurrencyUpsert) {
		s.SetMaxAmount(v)
	})
}

// UpdateMaxAmount sets the "max_amount" field to the value that was provided on create.
func (u *CurrencyUpsertOne) UpdateMaxAmount() *CurrencyUpsertOne {
	return u.Update(func(s *CurrencyUpsert) {
		s.UpdateMaxAmount()
	})
}

// Exec executes the query.
func (u *CurrencyUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for CurrencyCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *CurrencyUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *CurrencyUpsertOne) ID(ctx context.Context) (id uint, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *CurrencyUpsertOne) IDX(ctx context.Context) uint {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// CurrencyCreateBulk is the builder for creating many Currency entities in bulk.
type CurrencyCreateBulk struct {
	config
	err      error
	builders []*CurrencyCreate
	conflict []sql.ConflictOption
}

// Save creates the Currency entities in the database.
func (_c *CurrencyCreateBulk) Save(ctx context.Context) ([]*Currency, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*Currency, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*CurrencyMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = uint(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *CurrencyCreateBulk) SaveX(ctx context.Context) []*Currency {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *CurrencyCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *CurrencyCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Currency.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.CurrencyUpsert) {
//			SetProductID(v+v).
//		}).
//		Exec(ctx)
func (_c *CurrencyCreateBulk) OnConflict(opts ...sql.ConflictOption) *CurrencyUpsertBulk {
	_c.conflict = opts
	return &CurrencyUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Currency.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *CurrencyCreateBulk) OnConflictColumns(columns ...string) *CurrencyUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &CurrencyUpsertBulk{
		create: _c,
	}
}

// CurrencyUpsertBulk is the builder for "upsert"-ing
// a bulk of Currency nodes.
type CurrencyUpsertBulk struct {
	create *CurrencyCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Currency.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(currency.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *CurrencyUpsertBulk) UpdateNewValues() *CurrencyUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(currency.FieldID)
			}
			if _, exists := b.mutation.Currency(); exists {
				s.SetIgnore(currency.FieldCurrency)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Currency.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *CurrencyUpsertBulk) Ignore() *CurrencyUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *CurrencyUpsertBulk) DoNothing() *CurrencyUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the CurrencyCreateBulk.OnConflict
// documentation for more info.
func (u *CurrencyUpsertBulk) Update(set func(*CurrencyUpsert)) *CurrencyUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&CurrencyUpsert{UpdateSet: update})
	}))
	return u
}

// SetProductID sets the "product_id" field.
func (u *CurrencyUpsertBulk) SetProductID(v uint) *CurrencyUpsertBulk {
	return u.Update(func(s *CurrencyUpsert) {
		s.SetProductID(v)
	})
}

// UpdateProductID sets the "product_id" field to the value that was provided on create.
func (u *CurrencyUpsertBulk) UpdateProductID() *CurrencyUpsertBulk {
	return u.Update(func(s *CurrencyUpsert) {
		s.UpdateProductID()
	})
}

// SetIsActive sets the "is_active" field.
func (u *CurrencyUpsertBulk) SetIsActive(v bool) *CurrencyUpsertBulk {
	return u.Update(func(s *CurrencyUpsert) {
		s.SetIsActive(v)
	})
}

// UpdateIsActive sets the "is_active" field to the value that was provided on create.
func (u *CurrencyUpsertBulk) UpdateIsActive() *CurrencyUpsertBulk {
	return u.Update(func(s *CurrencyUpsert) {
		s.UpdateIsActive()
	})
}

// SetMinAmount sets the "min_amount" field.
func (u *CurrencyUpsertBulk) SetMinAmount(v decimal.Decimal) *CurrencyUpsertBulk {
	return u.Update(func(s *CurrencyUpsert) {
		s.SetMinAmount(v)
	})
}

// UpdateMinAmount sets the "min_amount" field to the value that was provided on create.
func (u *CurrencyUpsertBulk) UpdateMinAmount() *CurrencyUpsertBulk {
	return u.Update(func(s *CurrencyUpsert) {
		s.UpdateMinAmount()
	})
}

// SetMaxAmount sets the "max_amount" field.
func (u *CurrencyUpsertBulk) SetMaxAmount(v decimal.Decimal) *CurrencyUpsertBulk {
	return u.Update(func(s *CurrencyUpsert) {
		s.SetMaxAmount(v)
	})
}

// UpdateMaxAmount sets the "max_amount" field to the value that was provided on create.
func (u *CurrencyUpsertBulk) UpdateMaxAmount() *CurrencyUpsertBulk {
	return u.Update(func(s *CurrencyUpsert) {
		s.UpdateMaxAmount()
	})
}

// Exec executes the query.
func (u *CurrencyUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the CurrencyCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for CurrencyCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *CurrencyUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
