// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/currency"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/profitrate"
)

// ProfitRate is the model entity for the ProfitRate schema.
type ProfitRate struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// CreateTime holds the value of the "create_time" field.
	CreateTime time.Time `json:"create_time,omitempty"`
	// UpdateTime holds the value of the "update_time" field.
	UpdateTime time.Time `json:"update_time,omitempty"`
	// CurrencyID holds the value of the "currency_id" field.
	CurrencyID uint `json:"currency_id,omitempty"`
	// MonthTerm holds the value of the "month_term" field.
	MonthTerm uint32 `json:"month_term,omitempty"`
	// ProfitRate holds the value of the "profit_rate" field.
	ProfitRate decimal.Decimal `json:"profit_rate,omitempty"`
	// DateFrom holds the value of the "date_from" field.
	DateFrom time.Time `json:"date_from,omitempty"`
	// DateTo holds the value of the "date_to" field.
	DateTo time.Time `json:"date_to,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the ProfitRateQuery when eager-loading is set.
	Edges        ProfitRateEdges `json:"edges"`
	selectValues sql.SelectValues
}

// ProfitRateEdges holds the relations/edges for other nodes in the graph.
type ProfitRateEdges struct {
	// Currency holds the value of the currency edge.
	Currency *Currency `json:"currency,omitempty"`
	// RateDepositApplication holds the value of the rate_deposit_application edge.
	RateDepositApplication []*DepositApplication `json:"rate_deposit_application,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// CurrencyOrErr returns the Currency value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e ProfitRateEdges) CurrencyOrErr() (*Currency, error) {
	if e.Currency != nil {
		return e.Currency, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: currency.Label}
	}
	return nil, &NotLoadedError{edge: "currency"}
}

// RateDepositApplicationOrErr returns the RateDepositApplication value or an error if the edge
// was not loaded in eager-loading.
func (e ProfitRateEdges) RateDepositApplicationOrErr() ([]*DepositApplication, error) {
	if e.loadedTypes[1] {
		return e.RateDepositApplication, nil
	}
	return nil, &NotLoadedError{edge: "rate_deposit_application"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*ProfitRate) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case profitrate.FieldProfitRate:
			values[i] = new(decimal.Decimal)
		case profitrate.FieldCurrencyID, profitrate.FieldMonthTerm:
			values[i] = new(sql.NullInt64)
		case profitrate.FieldCreateTime, profitrate.FieldUpdateTime, profitrate.FieldDateFrom, profitrate.FieldDateTo:
			values[i] = new(sql.NullTime)
		case profitrate.FieldID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the ProfitRate fields.
func (_m *ProfitRate) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case profitrate.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				_m.ID = *value
			}
		case profitrate.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				_m.CreateTime = value.Time
			}
		case profitrate.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				_m.UpdateTime = value.Time
			}
		case profitrate.FieldCurrencyID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field currency_id", values[i])
			} else if value.Valid {
				_m.CurrencyID = uint(value.Int64)
			}
		case profitrate.FieldMonthTerm:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field month_term", values[i])
			} else if value.Valid {
				_m.MonthTerm = uint32(value.Int64)
			}
		case profitrate.FieldProfitRate:
			if value, ok := values[i].(*decimal.Decimal); !ok {
				return fmt.Errorf("unexpected type %T for field profit_rate", values[i])
			} else if value != nil {
				_m.ProfitRate = *value
			}
		case profitrate.FieldDateFrom:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field date_from", values[i])
			} else if value.Valid {
				_m.DateFrom = value.Time
			}
		case profitrate.FieldDateTo:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field date_to", values[i])
			} else if value.Valid {
				_m.DateTo = value.Time
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the ProfitRate.
// This includes values selected through modifiers, order, etc.
func (_m *ProfitRate) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// QueryCurrency queries the "currency" edge of the ProfitRate entity.
func (_m *ProfitRate) QueryCurrency() *CurrencyQuery {
	return NewProfitRateClient(_m.config).QueryCurrency(_m)
}

// QueryRateDepositApplication queries the "rate_deposit_application" edge of the ProfitRate entity.
func (_m *ProfitRate) QueryRateDepositApplication() *DepositApplicationQuery {
	return NewProfitRateClient(_m.config).QueryRateDepositApplication(_m)
}

// Update returns a builder for updating this ProfitRate.
// Note that you need to call ProfitRate.Unwrap() before calling this method if this ProfitRate
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *ProfitRate) Update() *ProfitRateUpdateOne {
	return NewProfitRateClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the ProfitRate entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *ProfitRate) Unwrap() *ProfitRate {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("ent: ProfitRate is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *ProfitRate) String() string {
	var builder strings.Builder
	builder.WriteString("ProfitRate(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("create_time=")
	builder.WriteString(_m.CreateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_time=")
	builder.WriteString(_m.UpdateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("currency_id=")
	builder.WriteString(fmt.Sprintf("%v", _m.CurrencyID))
	builder.WriteString(", ")
	builder.WriteString("month_term=")
	builder.WriteString(fmt.Sprintf("%v", _m.MonthTerm))
	builder.WriteString(", ")
	builder.WriteString("profit_rate=")
	builder.WriteString(fmt.Sprintf("%v", _m.ProfitRate))
	builder.WriteString(", ")
	builder.WriteString("date_from=")
	builder.WriteString(_m.DateFrom.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("date_to=")
	builder.WriteString(_m.DateTo.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// ProfitRates is a parsable slice of ProfitRate.
type ProfitRates []*ProfitRate
