// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/shopspring/decimal"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/currency"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositproduct"
)

// DepositProductCreate is the builder for creating a DepositProduct entity.
type DepositProductCreate struct {
	config
	mutation *DepositProductMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetProductCode sets the "product_code" field.
func (_c *DepositProductCreate) SetProductCode(v string) *DepositProductCreate {
	_c.mutation.SetProductCode(v)
	return _c
}

// SetColvirProductCode sets the "colvir_product_code" field.
func (_c *DepositProductCreate) SetColvirProductCode(v string) *DepositProductCreate {
	_c.mutation.SetColvirProductCode(v)
	return _c
}

// SetName sets the "name" field.
func (_c *DepositProductCreate) SetName(v string) *DepositProductCreate {
	_c.mutation.SetName(v)
	return _c
}

// SetMaxProfitRate sets the "max_profit_rate" field.
func (_c *DepositProductCreate) SetMaxProfitRate(v decimal.Decimal) *DepositProductCreate {
	_c.mutation.SetMaxProfitRate(v)
	return _c
}

// SetIsActive sets the "is_active" field.
func (_c *DepositProductCreate) SetIsActive(v bool) *DepositProductCreate {
	_c.mutation.SetIsActive(v)
	return _c
}

// SetIsReplenishable sets the "is_replenishable" field.
func (_c *DepositProductCreate) SetIsReplenishable(v bool) *DepositProductCreate {
	_c.mutation.SetIsReplenishable(v)
	return _c
}

// SetReplenishableDays sets the "replenishable_days" field.
func (_c *DepositProductCreate) SetReplenishableDays(v uint) *DepositProductCreate {
	_c.mutation.SetReplenishableDays(v)
	return _c
}

// SetID sets the "id" field.
func (_c *DepositProductCreate) SetID(v uint) *DepositProductCreate {
	_c.mutation.SetID(v)
	return _c
}

// AddCurrencyIDs adds the "currency" edge to the Currency entity by IDs.
func (_c *DepositProductCreate) AddCurrencyIDs(ids ...uint) *DepositProductCreate {
	_c.mutation.AddCurrencyIDs(ids...)
	return _c
}

// AddCurrency adds the "currency" edges to the Currency entity.
func (_c *DepositProductCreate) AddCurrency(v ...*Currency) *DepositProductCreate {
	ids := make([]uint, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _c.AddCurrencyIDs(ids...)
}

// Mutation returns the DepositProductMutation object of the builder.
func (_c *DepositProductCreate) Mutation() *DepositProductMutation {
	return _c.mutation
}

// Save creates the DepositProduct in the database.
func (_c *DepositProductCreate) Save(ctx context.Context) (*DepositProduct, error) {
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *DepositProductCreate) SaveX(ctx context.Context) *DepositProduct {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *DepositProductCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *DepositProductCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *DepositProductCreate) check() error {
	if _, ok := _c.mutation.ProductCode(); !ok {
		return &ValidationError{Name: "product_code", err: errors.New(`ent: missing required field "DepositProduct.product_code"`)}
	}
	if _, ok := _c.mutation.ColvirProductCode(); !ok {
		return &ValidationError{Name: "colvir_product_code", err: errors.New(`ent: missing required field "DepositProduct.colvir_product_code"`)}
	}
	if _, ok := _c.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "DepositProduct.name"`)}
	}
	if _, ok := _c.mutation.MaxProfitRate(); !ok {
		return &ValidationError{Name: "max_profit_rate", err: errors.New(`ent: missing required field "DepositProduct.max_profit_rate"`)}
	}
	if _, ok := _c.mutation.IsActive(); !ok {
		return &ValidationError{Name: "is_active", err: errors.New(`ent: missing required field "DepositProduct.is_active"`)}
	}
	if _, ok := _c.mutation.IsReplenishable(); !ok {
		return &ValidationError{Name: "is_replenishable", err: errors.New(`ent: missing required field "DepositProduct.is_replenishable"`)}
	}
	if _, ok := _c.mutation.ReplenishableDays(); !ok {
		return &ValidationError{Name: "replenishable_days", err: errors.New(`ent: missing required field "DepositProduct.replenishable_days"`)}
	}
	return nil
}

func (_c *DepositProductCreate) sqlSave(ctx context.Context) (*DepositProduct, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = uint(id)
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *DepositProductCreate) createSpec() (*DepositProduct, *sqlgraph.CreateSpec) {
	var (
		_node = &DepositProduct{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(depositproduct.Table, sqlgraph.NewFieldSpec(depositproduct.FieldID, field.TypeUint))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := _c.mutation.ProductCode(); ok {
		_spec.SetField(depositproduct.FieldProductCode, field.TypeString, value)
		_node.ProductCode = value
	}
	if value, ok := _c.mutation.ColvirProductCode(); ok {
		_spec.SetField(depositproduct.FieldColvirProductCode, field.TypeString, value)
		_node.ColvirProductCode = value
	}
	if value, ok := _c.mutation.Name(); ok {
		_spec.SetField(depositproduct.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := _c.mutation.MaxProfitRate(); ok {
		_spec.SetField(depositproduct.FieldMaxProfitRate, field.TypeString, value)
		_node.MaxProfitRate = value
	}
	if value, ok := _c.mutation.IsActive(); ok {
		_spec.SetField(depositproduct.FieldIsActive, field.TypeBool, value)
		_node.IsActive = value
	}
	if value, ok := _c.mutation.IsReplenishable(); ok {
		_spec.SetField(depositproduct.FieldIsReplenishable, field.TypeBool, value)
		_node.IsReplenishable = value
	}
	if value, ok := _c.mutation.ReplenishableDays(); ok {
		_spec.SetField(depositproduct.FieldReplenishableDays, field.TypeUint, value)
		_node.ReplenishableDays = value
	}
	if nodes := _c.mutation.CurrencyIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   depositproduct.CurrencyTable,
			Columns: []string{depositproduct.CurrencyColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(currency.FieldID, field.TypeUint),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.DepositProduct.Create().
//		SetProductCode(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.DepositProductUpsert) {
//			SetProductCode(v+v).
//		}).
//		Exec(ctx)
func (_c *DepositProductCreate) OnConflict(opts ...sql.ConflictOption) *DepositProductUpsertOne {
	_c.conflict = opts
	return &DepositProductUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.DepositProduct.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *DepositProductCreate) OnConflictColumns(columns ...string) *DepositProductUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &DepositProductUpsertOne{
		create: _c,
	}
}

type (
	// DepositProductUpsertOne is the builder for "upsert"-ing
	//  one DepositProduct node.
	DepositProductUpsertOne struct {
		create *DepositProductCreate
	}

	// DepositProductUpsert is the "OnConflict" setter.
	DepositProductUpsert struct {
		*sql.UpdateSet
	}
)

// SetProductCode sets the "product_code" field.
func (u *DepositProductUpsert) SetProductCode(v string) *DepositProductUpsert {
	u.Set(depositproduct.FieldProductCode, v)
	return u
}

// UpdateProductCode sets the "product_code" field to the value that was provided on create.
func (u *DepositProductUpsert) UpdateProductCode() *DepositProductUpsert {
	u.SetExcluded(depositproduct.FieldProductCode)
	return u
}

// SetColvirProductCode sets the "colvir_product_code" field.
func (u *DepositProductUpsert) SetColvirProductCode(v string) *DepositProductUpsert {
	u.Set(depositproduct.FieldColvirProductCode, v)
	return u
}

// UpdateColvirProductCode sets the "colvir_product_code" field to the value that was provided on create.
func (u *DepositProductUpsert) UpdateColvirProductCode() *DepositProductUpsert {
	u.SetExcluded(depositproduct.FieldColvirProductCode)
	return u
}

// SetName sets the "name" field.
func (u *DepositProductUpsert) SetName(v string) *DepositProductUpsert {
	u.Set(depositproduct.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *DepositProductUpsert) UpdateName() *DepositProductUpsert {
	u.SetExcluded(depositproduct.FieldName)
	return u
}

// SetMaxProfitRate sets the "max_profit_rate" field.
func (u *DepositProductUpsert) SetMaxProfitRate(v decimal.Decimal) *DepositProductUpsert {
	u.Set(depositproduct.FieldMaxProfitRate, v)
	return u
}

// UpdateMaxProfitRate sets the "max_profit_rate" field to the value that was provided on create.
func (u *DepositProductUpsert) UpdateMaxProfitRate() *DepositProductUpsert {
	u.SetExcluded(depositproduct.FieldMaxProfitRate)
	return u
}

// SetIsActive sets the "is_active" field.
func (u *DepositProductUpsert) SetIsActive(v bool) *DepositProductUpsert {
	u.Set(depositproduct.FieldIsActive, v)
	return u
}

// UpdateIsActive sets the "is_active" field to the value that was provided on create.
func (u *DepositProductUpsert) UpdateIsActive() *DepositProductUpsert {
	u.SetExcluded(depositproduct.FieldIsActive)
	return u
}

// SetIsReplenishable sets the "is_replenishable" field.
func (u *DepositProductUpsert) SetIsReplenishable(v bool) *DepositProductUpsert {
	u.Set(depositproduct.FieldIsReplenishable, v)
	return u
}

// UpdateIsReplenishable sets the "is_replenishable" field to the value that was provided on create.
func (u *DepositProductUpsert) UpdateIsReplenishable() *DepositProductUpsert {
	u.SetExcluded(depositproduct.FieldIsReplenishable)
	return u
}

// SetReplenishableDays sets the "replenishable_days" field.
func (u *DepositProductUpsert) SetReplenishableDays(v uint) *DepositProductUpsert {
	u.Set(depositproduct.FieldReplenishableDays, v)
	return u
}

// UpdateReplenishableDays sets the "replenishable_days" field to the value that was provided on create.
func (u *DepositProductUpsert) UpdateReplenishableDays() *DepositProductUpsert {
	u.SetExcluded(depositproduct.FieldReplenishableDays)
	return u
}

// AddReplenishableDays adds v to the "replenishable_days" field.
func (u *DepositProductUpsert) AddReplenishableDays(v uint) *DepositProductUpsert {
	u.Add(depositproduct.FieldReplenishableDays, v)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.DepositProduct.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(depositproduct.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *DepositProductUpsertOne) UpdateNewValues() *DepositProductUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(depositproduct.FieldID)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.DepositProduct.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *DepositProductUpsertOne) Ignore() *DepositProductUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *DepositProductUpsertOne) DoNothing() *DepositProductUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the DepositProductCreate.OnConflict
// documentation for more info.
func (u *DepositProductUpsertOne) Update(set func(*DepositProductUpsert)) *DepositProductUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&DepositProductUpsert{UpdateSet: update})
	}))
	return u
}

// SetProductCode sets the "product_code" field.
func (u *DepositProductUpsertOne) SetProductCode(v string) *DepositProductUpsertOne {
	return u.Update(func(s *DepositProductUpsert) {
		s.SetProductCode(v)
	})
}

// UpdateProductCode sets the "product_code" field to the value that was provided on create.
func (u *DepositProductUpsertOne) UpdateProductCode() *DepositProductUpsertOne {
	return u.Update(func(s *DepositProductUpsert) {
		s.UpdateProductCode()
	})
}

// SetColvirProductCode sets the "colvir_product_code" field.
func (u *DepositProductUpsertOne) SetColvirProductCode(v string) *DepositProductUpsertOne {
	return u.Update(func(s *DepositProductUpsert) {
		s.SetColvirProductCode(v)
	})
}

// UpdateColvirProductCode sets the "colvir_product_code" field to the value that was provided on create.
func (u *DepositProductUpsertOne) UpdateColvirProductCode() *DepositProductUpsertOne {
	return u.Update(func(s *DepositProductUpsert) {
		s.UpdateColvirProductCode()
	})
}

// SetName sets the "name" field.
func (u *DepositProductUpsertOne) SetName(v string) *DepositProductUpsertOne {
	return u.Update(func(s *DepositProductUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *DepositProductUpsertOne) UpdateName() *DepositProductUpsertOne {
	return u.Update(func(s *DepositProductUpsert) {
		s.UpdateName()
	})
}

// SetMaxProfitRate sets the "max_profit_rate" field.
func (u *DepositProductUpsertOne) SetMaxProfitRate(v decimal.Decimal) *DepositProductUpsertOne {
	return u.Update(func(s *DepositProductUpsert) {
		s.SetMaxProfitRate(v)
	})
}

// UpdateMaxProfitRate sets the "max_profit_rate" field to the value that was provided on create.
func (u *DepositProductUpsertOne) UpdateMaxProfitRate() *DepositProductUpsertOne {
	return u.Update(func(s *DepositProductUpsert) {
		s.UpdateMaxProfitRate()
	})
}

// SetIsActive sets the "is_active" field.
func (u *DepositProductUpsertOne) SetIsActive(v bool) *DepositProductUpsertOne {
	return u.Update(func(s *DepositProductUpsert) {
		s.SetIsActive(v)
	})
}

// UpdateIsActive sets the "is_active" field to the value that was provided on create.
func (u *DepositProductUpsertOne) UpdateIsActive() *DepositProductUpsertOne {
	return u.Update(func(s *DepositProductUpsert) {
		s.UpdateIsActive()
	})
}

// SetIsReplenishable sets the "is_replenishable" field.
func (u *DepositProductUpsertOne) SetIsReplenishable(v bool) *DepositProductUpsertOne {
	return u.Update(func(s *DepositProductUpsert) {
		s.SetIsReplenishable(v)
	})
}

// UpdateIsReplenishable sets the "is_replenishable" field to the value that was provided on create.
func (u *DepositProductUpsertOne) UpdateIsReplenishable() *DepositProductUpsertOne {
	return u.Update(func(s *DepositProductUpsert) {
		s.UpdateIsReplenishable()
	})
}

// SetReplenishableDays sets the "replenishable_days" field.
func (u *DepositProductUpsertOne) SetReplenishableDays(v uint) *DepositProductUpsertOne {
	return u.Update(func(s *DepositProductUpsert) {
		s.SetReplenishableDays(v)
	})
}

// AddReplenishableDays adds v to the "replenishable_days" field.
func (u *DepositProductUpsertOne) AddReplenishableDays(v uint) *DepositProductUpsertOne {
	return u.Update(func(s *DepositProductUpsert) {
		s.AddReplenishableDays(v)
	})
}

// UpdateReplenishableDays sets the "replenishable_days" field to the value that was provided on create.
func (u *DepositProductUpsertOne) UpdateReplenishableDays() *DepositProductUpsertOne {
	return u.Update(func(s *DepositProductUpsert) {
		s.UpdateReplenishableDays()
	})
}

// Exec executes the query.
func (u *DepositProductUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for DepositProductCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *DepositProductUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *DepositProductUpsertOne) ID(ctx context.Context) (id uint, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *DepositProductUpsertOne) IDX(ctx context.Context) uint {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// DepositProductCreateBulk is the builder for creating many DepositProduct entities in bulk.
type DepositProductCreateBulk struct {
	config
	err      error
	builders []*DepositProductCreate
	conflict []sql.ConflictOption
}

// Save creates the DepositProduct entities in the database.
func (_c *DepositProductCreateBulk) Save(ctx context.Context) ([]*DepositProduct, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*DepositProduct, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*DepositProductMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = uint(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *DepositProductCreateBulk) SaveX(ctx context.Context) []*DepositProduct {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *DepositProductCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *DepositProductCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.DepositProduct.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.DepositProductUpsert) {
//			SetProductCode(v+v).
//		}).
//		Exec(ctx)
func (_c *DepositProductCreateBulk) OnConflict(opts ...sql.ConflictOption) *DepositProductUpsertBulk {
	_c.conflict = opts
	return &DepositProductUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.DepositProduct.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *DepositProductCreateBulk) OnConflictColumns(columns ...string) *DepositProductUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &DepositProductUpsertBulk{
		create: _c,
	}
}

// DepositProductUpsertBulk is the builder for "upsert"-ing
// a bulk of DepositProduct nodes.
type DepositProductUpsertBulk struct {
	create *DepositProductCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.DepositProduct.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(depositproduct.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *DepositProductUpsertBulk) UpdateNewValues() *DepositProductUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(depositproduct.FieldID)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.DepositProduct.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *DepositProductUpsertBulk) Ignore() *DepositProductUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *DepositProductUpsertBulk) DoNothing() *DepositProductUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the DepositProductCreateBulk.OnConflict
// documentation for more info.
func (u *DepositProductUpsertBulk) Update(set func(*DepositProductUpsert)) *DepositProductUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&DepositProductUpsert{UpdateSet: update})
	}))
	return u
}

// SetProductCode sets the "product_code" field.
func (u *DepositProductUpsertBulk) SetProductCode(v string) *DepositProductUpsertBulk {
	return u.Update(func(s *DepositProductUpsert) {
		s.SetProductCode(v)
	})
}

// UpdateProductCode sets the "product_code" field to the value that was provided on create.
func (u *DepositProductUpsertBulk) UpdateProductCode() *DepositProductUpsertBulk {
	return u.Update(func(s *DepositProductUpsert) {
		s.UpdateProductCode()
	})
}

// SetColvirProductCode sets the "colvir_product_code" field.
func (u *DepositProductUpsertBulk) SetColvirProductCode(v string) *DepositProductUpsertBulk {
	return u.Update(func(s *DepositProductUpsert) {
		s.SetColvirProductCode(v)
	})
}

// UpdateColvirProductCode sets the "colvir_product_code" field to the value that was provided on create.
func (u *DepositProductUpsertBulk) UpdateColvirProductCode() *DepositProductUpsertBulk {
	return u.Update(func(s *DepositProductUpsert) {
		s.UpdateColvirProductCode()
	})
}

// SetName sets the "name" field.
func (u *DepositProductUpsertBulk) SetName(v string) *DepositProductUpsertBulk {
	return u.Update(func(s *DepositProductUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *DepositProductUpsertBulk) UpdateName() *DepositProductUpsertBulk {
	return u.Update(func(s *DepositProductUpsert) {
		s.UpdateName()
	})
}

// SetMaxProfitRate sets the "max_profit_rate" field.
func (u *DepositProductUpsertBulk) SetMaxProfitRate(v decimal.Decimal) *DepositProductUpsertBulk {
	return u.Update(func(s *DepositProductUpsert) {
		s.SetMaxProfitRate(v)
	})
}

// UpdateMaxProfitRate sets the "max_profit_rate" field to the value that was provided on create.
func (u *DepositProductUpsertBulk) UpdateMaxProfitRate() *DepositProductUpsertBulk {
	return u.Update(func(s *DepositProductUpsert) {
		s.UpdateMaxProfitRate()
	})
}

// SetIsActive sets the "is_active" field.
func (u *DepositProductUpsertBulk) SetIsActive(v bool) *DepositProductUpsertBulk {
	return u.Update(func(s *DepositProductUpsert) {
		s.SetIsActive(v)
	})
}

// UpdateIsActive sets the "is_active" field to the value that was provided on create.
func (u *DepositProductUpsertBulk) UpdateIsActive() *DepositProductUpsertBulk {
	return u.Update(func(s *DepositProductUpsert) {
		s.UpdateIsActive()
	})
}

// SetIsReplenishable sets the "is_replenishable" field.
func (u *DepositProductUpsertBulk) SetIsReplenishable(v bool) *DepositProductUpsertBulk {
	return u.Update(func(s *DepositProductUpsert) {
		s.SetIsReplenishable(v)
	})
}

// UpdateIsReplenishable sets the "is_replenishable" field to the value that was provided on create.
func (u *DepositProductUpsertBulk) UpdateIsReplenishable() *DepositProductUpsertBulk {
	return u.Update(func(s *DepositProductUpsert) {
		s.UpdateIsReplenishable()
	})
}

// SetReplenishableDays sets the "replenishable_days" field.
func (u *DepositProductUpsertBulk) SetReplenishableDays(v uint) *DepositProductUpsertBulk {
	return u.Update(func(s *DepositProductUpsert) {
		s.SetReplenishableDays(v)
	})
}

// AddReplenishableDays adds v to the "replenishable_days" field.
func (u *DepositProductUpsertBulk) AddReplenishableDays(v uint) *DepositProductUpsertBulk {
	return u.Update(func(s *DepositProductUpsert) {
		s.AddReplenishableDays(v)
	})
}

// UpdateReplenishableDays sets the "replenishable_days" field to the value that was provided on create.
func (u *DepositProductUpsertBulk) UpdateReplenishableDays() *DepositProductUpsertBulk {
	return u.Update(func(s *DepositProductUpsert) {
		s.UpdateReplenishableDays()
	})
}

// Exec executes the query.
func (u *DepositProductUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the DepositProductCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for DepositProductCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *DepositProductUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
