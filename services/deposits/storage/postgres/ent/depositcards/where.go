// Code generated by ent, DO NOT EDIT.

package depositcards

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldEQ(FieldUpdateTime, v))
}

// CardID applies equality check predicate on the "card_id" field. It's identical to CardIDEQ.
func CardID(v uuid.UUID) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldEQ(FieldCardID, v))
}

// AccountIban applies equality check predicate on the "account_iban" field. It's identical to AccountIbanEQ.
func AccountIban(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldEQ(FieldAccountIban, v))
}

// FinContractID applies equality check predicate on the "fin_contract_id" field. It's identical to FinContractIDEQ.
func FinContractID(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldEQ(FieldFinContractID, v))
}

// Rrn applies equality check predicate on the "rrn" field. It's identical to RrnEQ.
func Rrn(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldEQ(FieldRrn, v))
}

// ExternalID applies equality check predicate on the "external_id" field. It's identical to ExternalIDEQ.
func ExternalID(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldEQ(FieldExternalID, v))
}

// ProcessingTransactionID applies equality check predicate on the "processing_transaction_id" field. It's identical to ProcessingTransactionIDEQ.
func ProcessingTransactionID(v int64) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldEQ(FieldProcessingTransactionID, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldLTE(FieldCreateTime, v))
}

// CreateTimeIsNil applies the IsNil predicate on the "create_time" field.
func CreateTimeIsNil() predicate.DepositCards {
	return predicate.DepositCards(sql.FieldIsNull(FieldCreateTime))
}

// CreateTimeNotNil applies the NotNil predicate on the "create_time" field.
func CreateTimeNotNil() predicate.DepositCards {
	return predicate.DepositCards(sql.FieldNotNull(FieldCreateTime))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldLTE(FieldUpdateTime, v))
}

// UpdateTimeIsNil applies the IsNil predicate on the "update_time" field.
func UpdateTimeIsNil() predicate.DepositCards {
	return predicate.DepositCards(sql.FieldIsNull(FieldUpdateTime))
}

// UpdateTimeNotNil applies the NotNil predicate on the "update_time" field.
func UpdateTimeNotNil() predicate.DepositCards {
	return predicate.DepositCards(sql.FieldNotNull(FieldUpdateTime))
}

// CardIDEQ applies the EQ predicate on the "card_id" field.
func CardIDEQ(v uuid.UUID) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldEQ(FieldCardID, v))
}

// CardIDNEQ applies the NEQ predicate on the "card_id" field.
func CardIDNEQ(v uuid.UUID) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldNEQ(FieldCardID, v))
}

// CardIDIn applies the In predicate on the "card_id" field.
func CardIDIn(vs ...uuid.UUID) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldIn(FieldCardID, vs...))
}

// CardIDNotIn applies the NotIn predicate on the "card_id" field.
func CardIDNotIn(vs ...uuid.UUID) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldNotIn(FieldCardID, vs...))
}

// CardIDGT applies the GT predicate on the "card_id" field.
func CardIDGT(v uuid.UUID) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldGT(FieldCardID, v))
}

// CardIDGTE applies the GTE predicate on the "card_id" field.
func CardIDGTE(v uuid.UUID) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldGTE(FieldCardID, v))
}

// CardIDLT applies the LT predicate on the "card_id" field.
func CardIDLT(v uuid.UUID) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldLT(FieldCardID, v))
}

// CardIDLTE applies the LTE predicate on the "card_id" field.
func CardIDLTE(v uuid.UUID) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldLTE(FieldCardID, v))
}

// AccountIbanEQ applies the EQ predicate on the "account_iban" field.
func AccountIbanEQ(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldEQ(FieldAccountIban, v))
}

// AccountIbanNEQ applies the NEQ predicate on the "account_iban" field.
func AccountIbanNEQ(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldNEQ(FieldAccountIban, v))
}

// AccountIbanIn applies the In predicate on the "account_iban" field.
func AccountIbanIn(vs ...string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldIn(FieldAccountIban, vs...))
}

// AccountIbanNotIn applies the NotIn predicate on the "account_iban" field.
func AccountIbanNotIn(vs ...string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldNotIn(FieldAccountIban, vs...))
}

// AccountIbanGT applies the GT predicate on the "account_iban" field.
func AccountIbanGT(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldGT(FieldAccountIban, v))
}

// AccountIbanGTE applies the GTE predicate on the "account_iban" field.
func AccountIbanGTE(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldGTE(FieldAccountIban, v))
}

// AccountIbanLT applies the LT predicate on the "account_iban" field.
func AccountIbanLT(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldLT(FieldAccountIban, v))
}

// AccountIbanLTE applies the LTE predicate on the "account_iban" field.
func AccountIbanLTE(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldLTE(FieldAccountIban, v))
}

// AccountIbanContains applies the Contains predicate on the "account_iban" field.
func AccountIbanContains(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldContains(FieldAccountIban, v))
}

// AccountIbanHasPrefix applies the HasPrefix predicate on the "account_iban" field.
func AccountIbanHasPrefix(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldHasPrefix(FieldAccountIban, v))
}

// AccountIbanHasSuffix applies the HasSuffix predicate on the "account_iban" field.
func AccountIbanHasSuffix(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldHasSuffix(FieldAccountIban, v))
}

// AccountIbanEqualFold applies the EqualFold predicate on the "account_iban" field.
func AccountIbanEqualFold(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldEqualFold(FieldAccountIban, v))
}

// AccountIbanContainsFold applies the ContainsFold predicate on the "account_iban" field.
func AccountIbanContainsFold(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldContainsFold(FieldAccountIban, v))
}

// FinContractIDEQ applies the EQ predicate on the "fin_contract_id" field.
func FinContractIDEQ(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldEQ(FieldFinContractID, v))
}

// FinContractIDNEQ applies the NEQ predicate on the "fin_contract_id" field.
func FinContractIDNEQ(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldNEQ(FieldFinContractID, v))
}

// FinContractIDIn applies the In predicate on the "fin_contract_id" field.
func FinContractIDIn(vs ...string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldIn(FieldFinContractID, vs...))
}

// FinContractIDNotIn applies the NotIn predicate on the "fin_contract_id" field.
func FinContractIDNotIn(vs ...string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldNotIn(FieldFinContractID, vs...))
}

// FinContractIDGT applies the GT predicate on the "fin_contract_id" field.
func FinContractIDGT(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldGT(FieldFinContractID, v))
}

// FinContractIDGTE applies the GTE predicate on the "fin_contract_id" field.
func FinContractIDGTE(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldGTE(FieldFinContractID, v))
}

// FinContractIDLT applies the LT predicate on the "fin_contract_id" field.
func FinContractIDLT(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldLT(FieldFinContractID, v))
}

// FinContractIDLTE applies the LTE predicate on the "fin_contract_id" field.
func FinContractIDLTE(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldLTE(FieldFinContractID, v))
}

// FinContractIDContains applies the Contains predicate on the "fin_contract_id" field.
func FinContractIDContains(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldContains(FieldFinContractID, v))
}

// FinContractIDHasPrefix applies the HasPrefix predicate on the "fin_contract_id" field.
func FinContractIDHasPrefix(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldHasPrefix(FieldFinContractID, v))
}

// FinContractIDHasSuffix applies the HasSuffix predicate on the "fin_contract_id" field.
func FinContractIDHasSuffix(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldHasSuffix(FieldFinContractID, v))
}

// FinContractIDEqualFold applies the EqualFold predicate on the "fin_contract_id" field.
func FinContractIDEqualFold(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldEqualFold(FieldFinContractID, v))
}

// FinContractIDContainsFold applies the ContainsFold predicate on the "fin_contract_id" field.
func FinContractIDContainsFold(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldContainsFold(FieldFinContractID, v))
}

// RrnEQ applies the EQ predicate on the "rrn" field.
func RrnEQ(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldEQ(FieldRrn, v))
}

// RrnNEQ applies the NEQ predicate on the "rrn" field.
func RrnNEQ(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldNEQ(FieldRrn, v))
}

// RrnIn applies the In predicate on the "rrn" field.
func RrnIn(vs ...string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldIn(FieldRrn, vs...))
}

// RrnNotIn applies the NotIn predicate on the "rrn" field.
func RrnNotIn(vs ...string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldNotIn(FieldRrn, vs...))
}

// RrnGT applies the GT predicate on the "rrn" field.
func RrnGT(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldGT(FieldRrn, v))
}

// RrnGTE applies the GTE predicate on the "rrn" field.
func RrnGTE(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldGTE(FieldRrn, v))
}

// RrnLT applies the LT predicate on the "rrn" field.
func RrnLT(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldLT(FieldRrn, v))
}

// RrnLTE applies the LTE predicate on the "rrn" field.
func RrnLTE(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldLTE(FieldRrn, v))
}

// RrnContains applies the Contains predicate on the "rrn" field.
func RrnContains(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldContains(FieldRrn, v))
}

// RrnHasPrefix applies the HasPrefix predicate on the "rrn" field.
func RrnHasPrefix(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldHasPrefix(FieldRrn, v))
}

// RrnHasSuffix applies the HasSuffix predicate on the "rrn" field.
func RrnHasSuffix(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldHasSuffix(FieldRrn, v))
}

// RrnEqualFold applies the EqualFold predicate on the "rrn" field.
func RrnEqualFold(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldEqualFold(FieldRrn, v))
}

// RrnContainsFold applies the ContainsFold predicate on the "rrn" field.
func RrnContainsFold(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldContainsFold(FieldRrn, v))
}

// ExternalIDEQ applies the EQ predicate on the "external_id" field.
func ExternalIDEQ(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldEQ(FieldExternalID, v))
}

// ExternalIDNEQ applies the NEQ predicate on the "external_id" field.
func ExternalIDNEQ(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldNEQ(FieldExternalID, v))
}

// ExternalIDIn applies the In predicate on the "external_id" field.
func ExternalIDIn(vs ...string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldIn(FieldExternalID, vs...))
}

// ExternalIDNotIn applies the NotIn predicate on the "external_id" field.
func ExternalIDNotIn(vs ...string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldNotIn(FieldExternalID, vs...))
}

// ExternalIDGT applies the GT predicate on the "external_id" field.
func ExternalIDGT(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldGT(FieldExternalID, v))
}

// ExternalIDGTE applies the GTE predicate on the "external_id" field.
func ExternalIDGTE(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldGTE(FieldExternalID, v))
}

// ExternalIDLT applies the LT predicate on the "external_id" field.
func ExternalIDLT(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldLT(FieldExternalID, v))
}

// ExternalIDLTE applies the LTE predicate on the "external_id" field.
func ExternalIDLTE(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldLTE(FieldExternalID, v))
}

// ExternalIDContains applies the Contains predicate on the "external_id" field.
func ExternalIDContains(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldContains(FieldExternalID, v))
}

// ExternalIDHasPrefix applies the HasPrefix predicate on the "external_id" field.
func ExternalIDHasPrefix(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldHasPrefix(FieldExternalID, v))
}

// ExternalIDHasSuffix applies the HasSuffix predicate on the "external_id" field.
func ExternalIDHasSuffix(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldHasSuffix(FieldExternalID, v))
}

// ExternalIDEqualFold applies the EqualFold predicate on the "external_id" field.
func ExternalIDEqualFold(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldEqualFold(FieldExternalID, v))
}

// ExternalIDContainsFold applies the ContainsFold predicate on the "external_id" field.
func ExternalIDContainsFold(v string) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldContainsFold(FieldExternalID, v))
}

// ProcessingTransactionIDEQ applies the EQ predicate on the "processing_transaction_id" field.
func ProcessingTransactionIDEQ(v int64) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldEQ(FieldProcessingTransactionID, v))
}

// ProcessingTransactionIDNEQ applies the NEQ predicate on the "processing_transaction_id" field.
func ProcessingTransactionIDNEQ(v int64) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldNEQ(FieldProcessingTransactionID, v))
}

// ProcessingTransactionIDIn applies the In predicate on the "processing_transaction_id" field.
func ProcessingTransactionIDIn(vs ...int64) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldIn(FieldProcessingTransactionID, vs...))
}

// ProcessingTransactionIDNotIn applies the NotIn predicate on the "processing_transaction_id" field.
func ProcessingTransactionIDNotIn(vs ...int64) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldNotIn(FieldProcessingTransactionID, vs...))
}

// ProcessingTransactionIDGT applies the GT predicate on the "processing_transaction_id" field.
func ProcessingTransactionIDGT(v int64) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldGT(FieldProcessingTransactionID, v))
}

// ProcessingTransactionIDGTE applies the GTE predicate on the "processing_transaction_id" field.
func ProcessingTransactionIDGTE(v int64) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldGTE(FieldProcessingTransactionID, v))
}

// ProcessingTransactionIDLT applies the LT predicate on the "processing_transaction_id" field.
func ProcessingTransactionIDLT(v int64) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldLT(FieldProcessingTransactionID, v))
}

// ProcessingTransactionIDLTE applies the LTE predicate on the "processing_transaction_id" field.
func ProcessingTransactionIDLTE(v int64) predicate.DepositCards {
	return predicate.DepositCards(sql.FieldLTE(FieldProcessingTransactionID, v))
}

// HasApplication applies the HasEdge predicate on the "application" edge.
func HasApplication() predicate.DepositCards {
	return predicate.DepositCards(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, ApplicationTable, ApplicationColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasApplicationWith applies the HasEdge predicate on the "application" edge with a given conditions (other predicates).
func HasApplicationWith(preds ...predicate.DepositApplication) predicate.DepositCards {
	return predicate.DepositCards(func(s *sql.Selector) {
		step := newApplicationStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.DepositCards) predicate.DepositCards {
	return predicate.DepositCards(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.DepositCards) predicate.DepositCards {
	return predicate.DepositCards(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.DepositCards) predicate.DepositCards {
	return predicate.DepositCards(sql.NotPredicates(p))
}
