// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositapplication"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/document"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/predicate"
)

// DocumentUpdate is the builder for updating Document entities.
type DocumentUpdate struct {
	config
	hooks     []Hook
	mutation  *DocumentMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the DocumentUpdate builder.
func (_u *DocumentUpdate) Where(ps ...predicate.Document) *DocumentUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetDepositApplicationID sets the "deposit_application_id" field.
func (_u *DocumentUpdate) SetDepositApplicationID(v uuid.UUID) *DocumentUpdate {
	_u.mutation.SetDepositApplicationID(v)
	return _u
}

// SetNillableDepositApplicationID sets the "deposit_application_id" field if the given value is not nil.
func (_u *DocumentUpdate) SetNillableDepositApplicationID(v *uuid.UUID) *DocumentUpdate {
	if v != nil {
		_u.SetDepositApplicationID(*v)
	}
	return _u
}

// SetType sets the "type" field.
func (_u *DocumentUpdate) SetType(v document.Type) *DocumentUpdate {
	_u.mutation.SetType(v)
	return _u
}

// SetNillableType sets the "type" field if the given value is not nil.
func (_u *DocumentUpdate) SetNillableType(v *document.Type) *DocumentUpdate {
	if v != nil {
		_u.SetType(*v)
	}
	return _u
}

// SetDocID sets the "doc_id" field.
func (_u *DocumentUpdate) SetDocID(v uuid.UUID) *DocumentUpdate {
	_u.mutation.SetDocID(v)
	return _u
}

// SetNillableDocID sets the "doc_id" field if the given value is not nil.
func (_u *DocumentUpdate) SetNillableDocID(v *uuid.UUID) *DocumentUpdate {
	if v != nil {
		_u.SetDocID(*v)
	}
	return _u
}

// ClearDocID clears the value of the "doc_id" field.
func (_u *DocumentUpdate) ClearDocID() *DocumentUpdate {
	_u.mutation.ClearDocID()
	return _u
}

// SetNumber sets the "number" field.
func (_u *DocumentUpdate) SetNumber(v string) *DocumentUpdate {
	_u.mutation.SetNumber(v)
	return _u
}

// SetNillableNumber sets the "number" field if the given value is not nil.
func (_u *DocumentUpdate) SetNillableNumber(v *string) *DocumentUpdate {
	if v != nil {
		_u.SetNumber(*v)
	}
	return _u
}

// ClearNumber clears the value of the "number" field.
func (_u *DocumentUpdate) ClearNumber() *DocumentUpdate {
	_u.mutation.ClearNumber()
	return _u
}

// SetSignedDocID sets the "signed_doc_id" field.
func (_u *DocumentUpdate) SetSignedDocID(v uuid.UUID) *DocumentUpdate {
	_u.mutation.SetSignedDocID(v)
	return _u
}

// SetNillableSignedDocID sets the "signed_doc_id" field if the given value is not nil.
func (_u *DocumentUpdate) SetNillableSignedDocID(v *uuid.UUID) *DocumentUpdate {
	if v != nil {
		_u.SetSignedDocID(*v)
	}
	return _u
}

// ClearSignedDocID clears the value of the "signed_doc_id" field.
func (_u *DocumentUpdate) ClearSignedDocID() *DocumentUpdate {
	_u.mutation.ClearSignedDocID()
	return _u
}

// SetDocumentSigningDate sets the "document_signing_date" field.
func (_u *DocumentUpdate) SetDocumentSigningDate(v time.Time) *DocumentUpdate {
	_u.mutation.SetDocumentSigningDate(v)
	return _u
}

// SetNillableDocumentSigningDate sets the "document_signing_date" field if the given value is not nil.
func (_u *DocumentUpdate) SetNillableDocumentSigningDate(v *time.Time) *DocumentUpdate {
	if v != nil {
		_u.SetDocumentSigningDate(*v)
	}
	return _u
}

// ClearDocumentSigningDate clears the value of the "document_signing_date" field.
func (_u *DocumentUpdate) ClearDocumentSigningDate() *DocumentUpdate {
	_u.mutation.ClearDocumentSigningDate()
	return _u
}

// SetIsSignable sets the "is_signable" field.
func (_u *DocumentUpdate) SetIsSignable(v bool) *DocumentUpdate {
	_u.mutation.SetIsSignable(v)
	return _u
}

// SetNillableIsSignable sets the "is_signable" field if the given value is not nil.
func (_u *DocumentUpdate) SetNillableIsSignable(v *bool) *DocumentUpdate {
	if v != nil {
		_u.SetIsSignable(*v)
	}
	return _u
}

// ClearIsSignable clears the value of the "is_signable" field.
func (_u *DocumentUpdate) ClearIsSignable() *DocumentUpdate {
	_u.mutation.ClearIsSignable()
	return _u
}

// SetApplicationID sets the "application" edge to the DepositApplication entity by ID.
func (_u *DocumentUpdate) SetApplicationID(id uuid.UUID) *DocumentUpdate {
	_u.mutation.SetApplicationID(id)
	return _u
}

// SetApplication sets the "application" edge to the DepositApplication entity.
func (_u *DocumentUpdate) SetApplication(v *DepositApplication) *DocumentUpdate {
	return _u.SetApplicationID(v.ID)
}

// Mutation returns the DocumentMutation object of the builder.
func (_u *DocumentUpdate) Mutation() *DocumentMutation {
	return _u.mutation
}

// ClearApplication clears the "application" edge to the DepositApplication entity.
func (_u *DocumentUpdate) ClearApplication() *DocumentUpdate {
	_u.mutation.ClearApplication()
	return _u
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *DocumentUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *DocumentUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *DocumentUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *DocumentUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *DocumentUpdate) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := document.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *DocumentUpdate) check() error {
	if v, ok := _u.mutation.GetType(); ok {
		if err := document.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Document.type": %w`, err)}
		}
	}
	if _u.mutation.ApplicationCleared() && len(_u.mutation.ApplicationIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "Document.application"`)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *DocumentUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DocumentUpdate {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *DocumentUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(document.Table, document.Columns, sqlgraph.NewFieldSpec(document.FieldID, field.TypeUUID))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(document.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(document.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(document.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.GetType(); ok {
		_spec.SetField(document.FieldType, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.DocID(); ok {
		_spec.SetField(document.FieldDocID, field.TypeUUID, value)
	}
	if _u.mutation.DocIDCleared() {
		_spec.ClearField(document.FieldDocID, field.TypeUUID)
	}
	if value, ok := _u.mutation.Number(); ok {
		_spec.SetField(document.FieldNumber, field.TypeString, value)
	}
	if _u.mutation.NumberCleared() {
		_spec.ClearField(document.FieldNumber, field.TypeString)
	}
	if value, ok := _u.mutation.SignedDocID(); ok {
		_spec.SetField(document.FieldSignedDocID, field.TypeUUID, value)
	}
	if _u.mutation.SignedDocIDCleared() {
		_spec.ClearField(document.FieldSignedDocID, field.TypeUUID)
	}
	if value, ok := _u.mutation.DocumentSigningDate(); ok {
		_spec.SetField(document.FieldDocumentSigningDate, field.TypeTime, value)
	}
	if _u.mutation.DocumentSigningDateCleared() {
		_spec.ClearField(document.FieldDocumentSigningDate, field.TypeTime)
	}
	if value, ok := _u.mutation.IsSignable(); ok {
		_spec.SetField(document.FieldIsSignable, field.TypeBool, value)
	}
	if _u.mutation.IsSignableCleared() {
		_spec.ClearField(document.FieldIsSignable, field.TypeBool)
	}
	if _u.mutation.ApplicationCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   document.ApplicationTable,
			Columns: []string{document.ApplicationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(depositapplication.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.ApplicationIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   document.ApplicationTable,
			Columns: []string{document.ApplicationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(depositapplication.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(_u.modifiers...)
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{document.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// DocumentUpdateOne is the builder for updating a single Document entity.
type DocumentUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *DocumentMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetDepositApplicationID sets the "deposit_application_id" field.
func (_u *DocumentUpdateOne) SetDepositApplicationID(v uuid.UUID) *DocumentUpdateOne {
	_u.mutation.SetDepositApplicationID(v)
	return _u
}

// SetNillableDepositApplicationID sets the "deposit_application_id" field if the given value is not nil.
func (_u *DocumentUpdateOne) SetNillableDepositApplicationID(v *uuid.UUID) *DocumentUpdateOne {
	if v != nil {
		_u.SetDepositApplicationID(*v)
	}
	return _u
}

// SetType sets the "type" field.
func (_u *DocumentUpdateOne) SetType(v document.Type) *DocumentUpdateOne {
	_u.mutation.SetType(v)
	return _u
}

// SetNillableType sets the "type" field if the given value is not nil.
func (_u *DocumentUpdateOne) SetNillableType(v *document.Type) *DocumentUpdateOne {
	if v != nil {
		_u.SetType(*v)
	}
	return _u
}

// SetDocID sets the "doc_id" field.
func (_u *DocumentUpdateOne) SetDocID(v uuid.UUID) *DocumentUpdateOne {
	_u.mutation.SetDocID(v)
	return _u
}

// SetNillableDocID sets the "doc_id" field if the given value is not nil.
func (_u *DocumentUpdateOne) SetNillableDocID(v *uuid.UUID) *DocumentUpdateOne {
	if v != nil {
		_u.SetDocID(*v)
	}
	return _u
}

// ClearDocID clears the value of the "doc_id" field.
func (_u *DocumentUpdateOne) ClearDocID() *DocumentUpdateOne {
	_u.mutation.ClearDocID()
	return _u
}

// SetNumber sets the "number" field.
func (_u *DocumentUpdateOne) SetNumber(v string) *DocumentUpdateOne {
	_u.mutation.SetNumber(v)
	return _u
}

// SetNillableNumber sets the "number" field if the given value is not nil.
func (_u *DocumentUpdateOne) SetNillableNumber(v *string) *DocumentUpdateOne {
	if v != nil {
		_u.SetNumber(*v)
	}
	return _u
}

// ClearNumber clears the value of the "number" field.
func (_u *DocumentUpdateOne) ClearNumber() *DocumentUpdateOne {
	_u.mutation.ClearNumber()
	return _u
}

// SetSignedDocID sets the "signed_doc_id" field.
func (_u *DocumentUpdateOne) SetSignedDocID(v uuid.UUID) *DocumentUpdateOne {
	_u.mutation.SetSignedDocID(v)
	return _u
}

// SetNillableSignedDocID sets the "signed_doc_id" field if the given value is not nil.
func (_u *DocumentUpdateOne) SetNillableSignedDocID(v *uuid.UUID) *DocumentUpdateOne {
	if v != nil {
		_u.SetSignedDocID(*v)
	}
	return _u
}

// ClearSignedDocID clears the value of the "signed_doc_id" field.
func (_u *DocumentUpdateOne) ClearSignedDocID() *DocumentUpdateOne {
	_u.mutation.ClearSignedDocID()
	return _u
}

// SetDocumentSigningDate sets the "document_signing_date" field.
func (_u *DocumentUpdateOne) SetDocumentSigningDate(v time.Time) *DocumentUpdateOne {
	_u.mutation.SetDocumentSigningDate(v)
	return _u
}

// SetNillableDocumentSigningDate sets the "document_signing_date" field if the given value is not nil.
func (_u *DocumentUpdateOne) SetNillableDocumentSigningDate(v *time.Time) *DocumentUpdateOne {
	if v != nil {
		_u.SetDocumentSigningDate(*v)
	}
	return _u
}

// ClearDocumentSigningDate clears the value of the "document_signing_date" field.
func (_u *DocumentUpdateOne) ClearDocumentSigningDate() *DocumentUpdateOne {
	_u.mutation.ClearDocumentSigningDate()
	return _u
}

// SetIsSignable sets the "is_signable" field.
func (_u *DocumentUpdateOne) SetIsSignable(v bool) *DocumentUpdateOne {
	_u.mutation.SetIsSignable(v)
	return _u
}

// SetNillableIsSignable sets the "is_signable" field if the given value is not nil.
func (_u *DocumentUpdateOne) SetNillableIsSignable(v *bool) *DocumentUpdateOne {
	if v != nil {
		_u.SetIsSignable(*v)
	}
	return _u
}

// ClearIsSignable clears the value of the "is_signable" field.
func (_u *DocumentUpdateOne) ClearIsSignable() *DocumentUpdateOne {
	_u.mutation.ClearIsSignable()
	return _u
}

// SetApplicationID sets the "application" edge to the DepositApplication entity by ID.
func (_u *DocumentUpdateOne) SetApplicationID(id uuid.UUID) *DocumentUpdateOne {
	_u.mutation.SetApplicationID(id)
	return _u
}

// SetApplication sets the "application" edge to the DepositApplication entity.
func (_u *DocumentUpdateOne) SetApplication(v *DepositApplication) *DocumentUpdateOne {
	return _u.SetApplicationID(v.ID)
}

// Mutation returns the DocumentMutation object of the builder.
func (_u *DocumentUpdateOne) Mutation() *DocumentMutation {
	return _u.mutation
}

// ClearApplication clears the "application" edge to the DepositApplication entity.
func (_u *DocumentUpdateOne) ClearApplication() *DocumentUpdateOne {
	_u.mutation.ClearApplication()
	return _u
}

// Where appends a list predicates to the DocumentUpdate builder.
func (_u *DocumentUpdateOne) Where(ps ...predicate.Document) *DocumentUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *DocumentUpdateOne) Select(field string, fields ...string) *DocumentUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated Document entity.
func (_u *DocumentUpdateOne) Save(ctx context.Context) (*Document, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *DocumentUpdateOne) SaveX(ctx context.Context) *Document {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *DocumentUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *DocumentUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *DocumentUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := document.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *DocumentUpdateOne) check() error {
	if v, ok := _u.mutation.GetType(); ok {
		if err := document.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Document.type": %w`, err)}
		}
	}
	if _u.mutation.ApplicationCleared() && len(_u.mutation.ApplicationIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "Document.application"`)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *DocumentUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DocumentUpdateOne {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *DocumentUpdateOne) sqlSave(ctx context.Context) (_node *Document, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(document.Table, document.Columns, sqlgraph.NewFieldSpec(document.FieldID, field.TypeUUID))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Document.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, document.FieldID)
		for _, f := range fields {
			if !document.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != document.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(document.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(document.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(document.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.GetType(); ok {
		_spec.SetField(document.FieldType, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.DocID(); ok {
		_spec.SetField(document.FieldDocID, field.TypeUUID, value)
	}
	if _u.mutation.DocIDCleared() {
		_spec.ClearField(document.FieldDocID, field.TypeUUID)
	}
	if value, ok := _u.mutation.Number(); ok {
		_spec.SetField(document.FieldNumber, field.TypeString, value)
	}
	if _u.mutation.NumberCleared() {
		_spec.ClearField(document.FieldNumber, field.TypeString)
	}
	if value, ok := _u.mutation.SignedDocID(); ok {
		_spec.SetField(document.FieldSignedDocID, field.TypeUUID, value)
	}
	if _u.mutation.SignedDocIDCleared() {
		_spec.ClearField(document.FieldSignedDocID, field.TypeUUID)
	}
	if value, ok := _u.mutation.DocumentSigningDate(); ok {
		_spec.SetField(document.FieldDocumentSigningDate, field.TypeTime, value)
	}
	if _u.mutation.DocumentSigningDateCleared() {
		_spec.ClearField(document.FieldDocumentSigningDate, field.TypeTime)
	}
	if value, ok := _u.mutation.IsSignable(); ok {
		_spec.SetField(document.FieldIsSignable, field.TypeBool, value)
	}
	if _u.mutation.IsSignableCleared() {
		_spec.ClearField(document.FieldIsSignable, field.TypeBool)
	}
	if _u.mutation.ApplicationCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   document.ApplicationTable,
			Columns: []string{document.ApplicationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(depositapplication.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.ApplicationIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   document.ApplicationTable,
			Columns: []string{document.ApplicationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(depositapplication.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(_u.modifiers...)
	_node = &Document{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{document.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
