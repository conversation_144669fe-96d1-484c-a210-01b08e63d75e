// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositapplication"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositcards"
)

// DepositCardsCreate is the builder for creating a DepositCards entity.
type DepositCardsCreate struct {
	config
	mutation *DepositCardsMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (_c *DepositCardsCreate) SetCreateTime(v time.Time) *DepositCardsCreate {
	_c.mutation.SetCreateTime(v)
	return _c
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (_c *DepositCardsCreate) SetNillableCreateTime(v *time.Time) *DepositCardsCreate {
	if v != nil {
		_c.SetCreateTime(*v)
	}
	return _c
}

// SetUpdateTime sets the "update_time" field.
func (_c *DepositCardsCreate) SetUpdateTime(v time.Time) *DepositCardsCreate {
	_c.mutation.SetUpdateTime(v)
	return _c
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (_c *DepositCardsCreate) SetNillableUpdateTime(v *time.Time) *DepositCardsCreate {
	if v != nil {
		_c.SetUpdateTime(*v)
	}
	return _c
}

// SetCardID sets the "card_id" field.
func (_c *DepositCardsCreate) SetCardID(v uuid.UUID) *DepositCardsCreate {
	_c.mutation.SetCardID(v)
	return _c
}

// SetAccountIban sets the "account_iban" field.
func (_c *DepositCardsCreate) SetAccountIban(v string) *DepositCardsCreate {
	_c.mutation.SetAccountIban(v)
	return _c
}

// SetFinContractID sets the "fin_contract_id" field.
func (_c *DepositCardsCreate) SetFinContractID(v string) *DepositCardsCreate {
	_c.mutation.SetFinContractID(v)
	return _c
}

// SetRrn sets the "rrn" field.
func (_c *DepositCardsCreate) SetRrn(v string) *DepositCardsCreate {
	_c.mutation.SetRrn(v)
	return _c
}

// SetExternalID sets the "external_id" field.
func (_c *DepositCardsCreate) SetExternalID(v string) *DepositCardsCreate {
	_c.mutation.SetExternalID(v)
	return _c
}

// SetProcessingTransactionID sets the "processing_transaction_id" field.
func (_c *DepositCardsCreate) SetProcessingTransactionID(v int64) *DepositCardsCreate {
	_c.mutation.SetProcessingTransactionID(v)
	return _c
}

// SetID sets the "id" field.
func (_c *DepositCardsCreate) SetID(v uuid.UUID) *DepositCardsCreate {
	_c.mutation.SetID(v)
	return _c
}

// SetNillableID sets the "id" field if the given value is not nil.
func (_c *DepositCardsCreate) SetNillableID(v *uuid.UUID) *DepositCardsCreate {
	if v != nil {
		_c.SetID(*v)
	}
	return _c
}

// SetApplicationID sets the "application" edge to the DepositApplication entity by ID.
func (_c *DepositCardsCreate) SetApplicationID(id uuid.UUID) *DepositCardsCreate {
	_c.mutation.SetApplicationID(id)
	return _c
}

// SetNillableApplicationID sets the "application" edge to the DepositApplication entity by ID if the given value is not nil.
func (_c *DepositCardsCreate) SetNillableApplicationID(id *uuid.UUID) *DepositCardsCreate {
	if id != nil {
		_c = _c.SetApplicationID(*id)
	}
	return _c
}

// SetApplication sets the "application" edge to the DepositApplication entity.
func (_c *DepositCardsCreate) SetApplication(v *DepositApplication) *DepositCardsCreate {
	return _c.SetApplicationID(v.ID)
}

// Mutation returns the DepositCardsMutation object of the builder.
func (_c *DepositCardsCreate) Mutation() *DepositCardsMutation {
	return _c.mutation
}

// Save creates the DepositCards in the database.
func (_c *DepositCardsCreate) Save(ctx context.Context) (*DepositCards, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *DepositCardsCreate) SaveX(ctx context.Context) *DepositCards {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *DepositCardsCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *DepositCardsCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *DepositCardsCreate) defaults() {
	if _, ok := _c.mutation.CreateTime(); !ok {
		v := depositcards.DefaultCreateTime()
		_c.mutation.SetCreateTime(v)
	}
	if _, ok := _c.mutation.UpdateTime(); !ok {
		v := depositcards.DefaultUpdateTime()
		_c.mutation.SetUpdateTime(v)
	}
	if _, ok := _c.mutation.ID(); !ok {
		v := depositcards.DefaultID()
		_c.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *DepositCardsCreate) check() error {
	if _, ok := _c.mutation.CardID(); !ok {
		return &ValidationError{Name: "card_id", err: errors.New(`ent: missing required field "DepositCards.card_id"`)}
	}
	if _, ok := _c.mutation.AccountIban(); !ok {
		return &ValidationError{Name: "account_iban", err: errors.New(`ent: missing required field "DepositCards.account_iban"`)}
	}
	if _, ok := _c.mutation.FinContractID(); !ok {
		return &ValidationError{Name: "fin_contract_id", err: errors.New(`ent: missing required field "DepositCards.fin_contract_id"`)}
	}
	if _, ok := _c.mutation.Rrn(); !ok {
		return &ValidationError{Name: "rrn", err: errors.New(`ent: missing required field "DepositCards.rrn"`)}
	}
	if _, ok := _c.mutation.ExternalID(); !ok {
		return &ValidationError{Name: "external_id", err: errors.New(`ent: missing required field "DepositCards.external_id"`)}
	}
	if _, ok := _c.mutation.ProcessingTransactionID(); !ok {
		return &ValidationError{Name: "processing_transaction_id", err: errors.New(`ent: missing required field "DepositCards.processing_transaction_id"`)}
	}
	return nil
}

func (_c *DepositCardsCreate) sqlSave(ctx context.Context) (*DepositCards, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *DepositCardsCreate) createSpec() (*DepositCards, *sqlgraph.CreateSpec) {
	var (
		_node = &DepositCards{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(depositcards.Table, sqlgraph.NewFieldSpec(depositcards.FieldID, field.TypeUUID))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := _c.mutation.CreateTime(); ok {
		_spec.SetField(depositcards.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = value
	}
	if value, ok := _c.mutation.UpdateTime(); ok {
		_spec.SetField(depositcards.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = value
	}
	if value, ok := _c.mutation.CardID(); ok {
		_spec.SetField(depositcards.FieldCardID, field.TypeUUID, value)
		_node.CardID = value
	}
	if value, ok := _c.mutation.AccountIban(); ok {
		_spec.SetField(depositcards.FieldAccountIban, field.TypeString, value)
		_node.AccountIban = value
	}
	if value, ok := _c.mutation.FinContractID(); ok {
		_spec.SetField(depositcards.FieldFinContractID, field.TypeString, value)
		_node.FinContractID = value
	}
	if value, ok := _c.mutation.Rrn(); ok {
		_spec.SetField(depositcards.FieldRrn, field.TypeString, value)
		_node.Rrn = value
	}
	if value, ok := _c.mutation.ExternalID(); ok {
		_spec.SetField(depositcards.FieldExternalID, field.TypeString, value)
		_node.ExternalID = value
	}
	if value, ok := _c.mutation.ProcessingTransactionID(); ok {
		_spec.SetField(depositcards.FieldProcessingTransactionID, field.TypeInt64, value)
		_node.ProcessingTransactionID = value
	}
	if nodes := _c.mutation.ApplicationIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   depositcards.ApplicationTable,
			Columns: []string{depositcards.ApplicationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(depositapplication.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.deposit_cards_application = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.DepositCards.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.DepositCardsUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *DepositCardsCreate) OnConflict(opts ...sql.ConflictOption) *DepositCardsUpsertOne {
	_c.conflict = opts
	return &DepositCardsUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.DepositCards.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *DepositCardsCreate) OnConflictColumns(columns ...string) *DepositCardsUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &DepositCardsUpsertOne{
		create: _c,
	}
}

type (
	// DepositCardsUpsertOne is the builder for "upsert"-ing
	//  one DepositCards node.
	DepositCardsUpsertOne struct {
		create *DepositCardsCreate
	}

	// DepositCardsUpsert is the "OnConflict" setter.
	DepositCardsUpsert struct {
		*sql.UpdateSet
	}
)

// SetCardID sets the "card_id" field.
func (u *DepositCardsUpsert) SetCardID(v uuid.UUID) *DepositCardsUpsert {
	u.Set(depositcards.FieldCardID, v)
	return u
}

// UpdateCardID sets the "card_id" field to the value that was provided on create.
func (u *DepositCardsUpsert) UpdateCardID() *DepositCardsUpsert {
	u.SetExcluded(depositcards.FieldCardID)
	return u
}

// SetAccountIban sets the "account_iban" field.
func (u *DepositCardsUpsert) SetAccountIban(v string) *DepositCardsUpsert {
	u.Set(depositcards.FieldAccountIban, v)
	return u
}

// UpdateAccountIban sets the "account_iban" field to the value that was provided on create.
func (u *DepositCardsUpsert) UpdateAccountIban() *DepositCardsUpsert {
	u.SetExcluded(depositcards.FieldAccountIban)
	return u
}

// SetFinContractID sets the "fin_contract_id" field.
func (u *DepositCardsUpsert) SetFinContractID(v string) *DepositCardsUpsert {
	u.Set(depositcards.FieldFinContractID, v)
	return u
}

// UpdateFinContractID sets the "fin_contract_id" field to the value that was provided on create.
func (u *DepositCardsUpsert) UpdateFinContractID() *DepositCardsUpsert {
	u.SetExcluded(depositcards.FieldFinContractID)
	return u
}

// SetRrn sets the "rrn" field.
func (u *DepositCardsUpsert) SetRrn(v string) *DepositCardsUpsert {
	u.Set(depositcards.FieldRrn, v)
	return u
}

// UpdateRrn sets the "rrn" field to the value that was provided on create.
func (u *DepositCardsUpsert) UpdateRrn() *DepositCardsUpsert {
	u.SetExcluded(depositcards.FieldRrn)
	return u
}

// SetExternalID sets the "external_id" field.
func (u *DepositCardsUpsert) SetExternalID(v string) *DepositCardsUpsert {
	u.Set(depositcards.FieldExternalID, v)
	return u
}

// UpdateExternalID sets the "external_id" field to the value that was provided on create.
func (u *DepositCardsUpsert) UpdateExternalID() *DepositCardsUpsert {
	u.SetExcluded(depositcards.FieldExternalID)
	return u
}

// SetProcessingTransactionID sets the "processing_transaction_id" field.
func (u *DepositCardsUpsert) SetProcessingTransactionID(v int64) *DepositCardsUpsert {
	u.Set(depositcards.FieldProcessingTransactionID, v)
	return u
}

// UpdateProcessingTransactionID sets the "processing_transaction_id" field to the value that was provided on create.
func (u *DepositCardsUpsert) UpdateProcessingTransactionID() *DepositCardsUpsert {
	u.SetExcluded(depositcards.FieldProcessingTransactionID)
	return u
}

// AddProcessingTransactionID adds v to the "processing_transaction_id" field.
func (u *DepositCardsUpsert) AddProcessingTransactionID(v int64) *DepositCardsUpsert {
	u.Add(depositcards.FieldProcessingTransactionID, v)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.DepositCards.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(depositcards.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *DepositCardsUpsertOne) UpdateNewValues() *DepositCardsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(depositcards.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(depositcards.FieldCreateTime)
		}
		if _, exists := u.create.mutation.UpdateTime(); exists {
			s.SetIgnore(depositcards.FieldUpdateTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.DepositCards.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *DepositCardsUpsertOne) Ignore() *DepositCardsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *DepositCardsUpsertOne) DoNothing() *DepositCardsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the DepositCardsCreate.OnConflict
// documentation for more info.
func (u *DepositCardsUpsertOne) Update(set func(*DepositCardsUpsert)) *DepositCardsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&DepositCardsUpsert{UpdateSet: update})
	}))
	return u
}

// SetCardID sets the "card_id" field.
func (u *DepositCardsUpsertOne) SetCardID(v uuid.UUID) *DepositCardsUpsertOne {
	return u.Update(func(s *DepositCardsUpsert) {
		s.SetCardID(v)
	})
}

// UpdateCardID sets the "card_id" field to the value that was provided on create.
func (u *DepositCardsUpsertOne) UpdateCardID() *DepositCardsUpsertOne {
	return u.Update(func(s *DepositCardsUpsert) {
		s.UpdateCardID()
	})
}

// SetAccountIban sets the "account_iban" field.
func (u *DepositCardsUpsertOne) SetAccountIban(v string) *DepositCardsUpsertOne {
	return u.Update(func(s *DepositCardsUpsert) {
		s.SetAccountIban(v)
	})
}

// UpdateAccountIban sets the "account_iban" field to the value that was provided on create.
func (u *DepositCardsUpsertOne) UpdateAccountIban() *DepositCardsUpsertOne {
	return u.Update(func(s *DepositCardsUpsert) {
		s.UpdateAccountIban()
	})
}

// SetFinContractID sets the "fin_contract_id" field.
func (u *DepositCardsUpsertOne) SetFinContractID(v string) *DepositCardsUpsertOne {
	return u.Update(func(s *DepositCardsUpsert) {
		s.SetFinContractID(v)
	})
}

// UpdateFinContractID sets the "fin_contract_id" field to the value that was provided on create.
func (u *DepositCardsUpsertOne) UpdateFinContractID() *DepositCardsUpsertOne {
	return u.Update(func(s *DepositCardsUpsert) {
		s.UpdateFinContractID()
	})
}

// SetRrn sets the "rrn" field.
func (u *DepositCardsUpsertOne) SetRrn(v string) *DepositCardsUpsertOne {
	return u.Update(func(s *DepositCardsUpsert) {
		s.SetRrn(v)
	})
}

// UpdateRrn sets the "rrn" field to the value that was provided on create.
func (u *DepositCardsUpsertOne) UpdateRrn() *DepositCardsUpsertOne {
	return u.Update(func(s *DepositCardsUpsert) {
		s.UpdateRrn()
	})
}

// SetExternalID sets the "external_id" field.
func (u *DepositCardsUpsertOne) SetExternalID(v string) *DepositCardsUpsertOne {
	return u.Update(func(s *DepositCardsUpsert) {
		s.SetExternalID(v)
	})
}

// UpdateExternalID sets the "external_id" field to the value that was provided on create.
func (u *DepositCardsUpsertOne) UpdateExternalID() *DepositCardsUpsertOne {
	return u.Update(func(s *DepositCardsUpsert) {
		s.UpdateExternalID()
	})
}

// SetProcessingTransactionID sets the "processing_transaction_id" field.
func (u *DepositCardsUpsertOne) SetProcessingTransactionID(v int64) *DepositCardsUpsertOne {
	return u.Update(func(s *DepositCardsUpsert) {
		s.SetProcessingTransactionID(v)
	})
}

// AddProcessingTransactionID adds v to the "processing_transaction_id" field.
func (u *DepositCardsUpsertOne) AddProcessingTransactionID(v int64) *DepositCardsUpsertOne {
	return u.Update(func(s *DepositCardsUpsert) {
		s.AddProcessingTransactionID(v)
	})
}

// UpdateProcessingTransactionID sets the "processing_transaction_id" field to the value that was provided on create.
func (u *DepositCardsUpsertOne) UpdateProcessingTransactionID() *DepositCardsUpsertOne {
	return u.Update(func(s *DepositCardsUpsert) {
		s.UpdateProcessingTransactionID()
	})
}

// Exec executes the query.
func (u *DepositCardsUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for DepositCardsCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *DepositCardsUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *DepositCardsUpsertOne) ID(ctx context.Context) (id uuid.UUID, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: DepositCardsUpsertOne.ID is not supported by MySQL driver. Use DepositCardsUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *DepositCardsUpsertOne) IDX(ctx context.Context) uuid.UUID {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// DepositCardsCreateBulk is the builder for creating many DepositCards entities in bulk.
type DepositCardsCreateBulk struct {
	config
	err      error
	builders []*DepositCardsCreate
	conflict []sql.ConflictOption
}

// Save creates the DepositCards entities in the database.
func (_c *DepositCardsCreateBulk) Save(ctx context.Context) ([]*DepositCards, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*DepositCards, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*DepositCardsMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *DepositCardsCreateBulk) SaveX(ctx context.Context) []*DepositCards {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *DepositCardsCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *DepositCardsCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.DepositCards.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.DepositCardsUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *DepositCardsCreateBulk) OnConflict(opts ...sql.ConflictOption) *DepositCardsUpsertBulk {
	_c.conflict = opts
	return &DepositCardsUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.DepositCards.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *DepositCardsCreateBulk) OnConflictColumns(columns ...string) *DepositCardsUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &DepositCardsUpsertBulk{
		create: _c,
	}
}

// DepositCardsUpsertBulk is the builder for "upsert"-ing
// a bulk of DepositCards nodes.
type DepositCardsUpsertBulk struct {
	create *DepositCardsCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.DepositCards.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(depositcards.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *DepositCardsUpsertBulk) UpdateNewValues() *DepositCardsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(depositcards.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(depositcards.FieldCreateTime)
			}
			if _, exists := b.mutation.UpdateTime(); exists {
				s.SetIgnore(depositcards.FieldUpdateTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.DepositCards.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *DepositCardsUpsertBulk) Ignore() *DepositCardsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *DepositCardsUpsertBulk) DoNothing() *DepositCardsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the DepositCardsCreateBulk.OnConflict
// documentation for more info.
func (u *DepositCardsUpsertBulk) Update(set func(*DepositCardsUpsert)) *DepositCardsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&DepositCardsUpsert{UpdateSet: update})
	}))
	return u
}

// SetCardID sets the "card_id" field.
func (u *DepositCardsUpsertBulk) SetCardID(v uuid.UUID) *DepositCardsUpsertBulk {
	return u.Update(func(s *DepositCardsUpsert) {
		s.SetCardID(v)
	})
}

// UpdateCardID sets the "card_id" field to the value that was provided on create.
func (u *DepositCardsUpsertBulk) UpdateCardID() *DepositCardsUpsertBulk {
	return u.Update(func(s *DepositCardsUpsert) {
		s.UpdateCardID()
	})
}

// SetAccountIban sets the "account_iban" field.
func (u *DepositCardsUpsertBulk) SetAccountIban(v string) *DepositCardsUpsertBulk {
	return u.Update(func(s *DepositCardsUpsert) {
		s.SetAccountIban(v)
	})
}

// UpdateAccountIban sets the "account_iban" field to the value that was provided on create.
func (u *DepositCardsUpsertBulk) UpdateAccountIban() *DepositCardsUpsertBulk {
	return u.Update(func(s *DepositCardsUpsert) {
		s.UpdateAccountIban()
	})
}

// SetFinContractID sets the "fin_contract_id" field.
func (u *DepositCardsUpsertBulk) SetFinContractID(v string) *DepositCardsUpsertBulk {
	return u.Update(func(s *DepositCardsUpsert) {
		s.SetFinContractID(v)
	})
}

// UpdateFinContractID sets the "fin_contract_id" field to the value that was provided on create.
func (u *DepositCardsUpsertBulk) UpdateFinContractID() *DepositCardsUpsertBulk {
	return u.Update(func(s *DepositCardsUpsert) {
		s.UpdateFinContractID()
	})
}

// SetRrn sets the "rrn" field.
func (u *DepositCardsUpsertBulk) SetRrn(v string) *DepositCardsUpsertBulk {
	return u.Update(func(s *DepositCardsUpsert) {
		s.SetRrn(v)
	})
}

// UpdateRrn sets the "rrn" field to the value that was provided on create.
func (u *DepositCardsUpsertBulk) UpdateRrn() *DepositCardsUpsertBulk {
	return u.Update(func(s *DepositCardsUpsert) {
		s.UpdateRrn()
	})
}

// SetExternalID sets the "external_id" field.
func (u *DepositCardsUpsertBulk) SetExternalID(v string) *DepositCardsUpsertBulk {
	return u.Update(func(s *DepositCardsUpsert) {
		s.SetExternalID(v)
	})
}

// UpdateExternalID sets the "external_id" field to the value that was provided on create.
func (u *DepositCardsUpsertBulk) UpdateExternalID() *DepositCardsUpsertBulk {
	return u.Update(func(s *DepositCardsUpsert) {
		s.UpdateExternalID()
	})
}

// SetProcessingTransactionID sets the "processing_transaction_id" field.
func (u *DepositCardsUpsertBulk) SetProcessingTransactionID(v int64) *DepositCardsUpsertBulk {
	return u.Update(func(s *DepositCardsUpsert) {
		s.SetProcessingTransactionID(v)
	})
}

// AddProcessingTransactionID adds v to the "processing_transaction_id" field.
func (u *DepositCardsUpsertBulk) AddProcessingTransactionID(v int64) *DepositCardsUpsertBulk {
	return u.Update(func(s *DepositCardsUpsert) {
		s.AddProcessingTransactionID(v)
	})
}

// UpdateProcessingTransactionID sets the "processing_transaction_id" field to the value that was provided on create.
func (u *DepositCardsUpsertBulk) UpdateProcessingTransactionID() *DepositCardsUpsertBulk {
	return u.Update(func(s *DepositCardsUpsert) {
		s.UpdateProcessingTransactionID()
	})
}

// Exec executes the query.
func (u *DepositCardsUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the DepositCardsCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for DepositCardsCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *DepositCardsUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
