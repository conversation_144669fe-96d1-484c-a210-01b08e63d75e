// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositapplication"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/document"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/profitrate"
)

// DepositApplicationCreate is the builder for creating a DepositApplication entity.
type DepositApplicationCreate struct {
	config
	mutation *DepositApplicationMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (_c *DepositApplicationCreate) SetCreateTime(v time.Time) *DepositApplicationCreate {
	_c.mutation.SetCreateTime(v)
	return _c
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (_c *DepositApplicationCreate) SetNillableCreateTime(v *time.Time) *DepositApplicationCreate {
	if v != nil {
		_c.SetCreateTime(*v)
	}
	return _c
}

// SetUpdateTime sets the "update_time" field.
func (_c *DepositApplicationCreate) SetUpdateTime(v time.Time) *DepositApplicationCreate {
	_c.mutation.SetUpdateTime(v)
	return _c
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (_c *DepositApplicationCreate) SetNillableUpdateTime(v *time.Time) *DepositApplicationCreate {
	if v != nil {
		_c.SetUpdateTime(*v)
	}
	return _c
}

// SetProfitRateID sets the "profit_rate_id" field.
func (_c *DepositApplicationCreate) SetProfitRateID(v uuid.UUID) *DepositApplicationCreate {
	_c.mutation.SetProfitRateID(v)
	return _c
}

// SetColvirReferenceID sets the "colvir_reference_id" field.
func (_c *DepositApplicationCreate) SetColvirReferenceID(v string) *DepositApplicationCreate {
	_c.mutation.SetColvirReferenceID(v)
	return _c
}

// SetNillableColvirReferenceID sets the "colvir_reference_id" field if the given value is not nil.
func (_c *DepositApplicationCreate) SetNillableColvirReferenceID(v *string) *DepositApplicationCreate {
	if v != nil {
		_c.SetColvirReferenceID(*v)
	}
	return _c
}

// SetStatus sets the "status" field.
func (_c *DepositApplicationCreate) SetStatus(v depositapplication.Status) *DepositApplicationCreate {
	_c.mutation.SetStatus(v)
	return _c
}

// SetUserID sets the "user_id" field.
func (_c *DepositApplicationCreate) SetUserID(v uuid.UUID) *DepositApplicationCreate {
	_c.mutation.SetUserID(v)
	return _c
}

// SetUserIin sets the "user_iin" field.
func (_c *DepositApplicationCreate) SetUserIin(v string) *DepositApplicationCreate {
	_c.mutation.SetUserIin(v)
	return _c
}

// SetDepositSourceAccountID sets the "deposit_source_account_id" field.
func (_c *DepositApplicationCreate) SetDepositSourceAccountID(v uuid.UUID) *DepositApplicationCreate {
	_c.mutation.SetDepositSourceAccountID(v)
	return _c
}

// SetDepositAmount sets the "deposit_amount" field.
func (_c *DepositApplicationCreate) SetDepositAmount(v decimal.Decimal) *DepositApplicationCreate {
	_c.mutation.SetDepositAmount(v)
	return _c
}

// SetDepositPayoutMethod sets the "deposit_payout_method" field.
func (_c *DepositApplicationCreate) SetDepositPayoutMethod(v depositapplication.DepositPayoutMethod) *DepositApplicationCreate {
	_c.mutation.SetDepositPayoutMethod(v)
	return _c
}

// SetClosedAt sets the "closed_at" field.
func (_c *DepositApplicationCreate) SetClosedAt(v time.Time) *DepositApplicationCreate {
	_c.mutation.SetClosedAt(v)
	return _c
}

// SetNillableClosedAt sets the "closed_at" field if the given value is not nil.
func (_c *DepositApplicationCreate) SetNillableClosedAt(v *time.Time) *DepositApplicationCreate {
	if v != nil {
		_c.SetClosedAt(*v)
	}
	return _c
}

// SetID sets the "id" field.
func (_c *DepositApplicationCreate) SetID(v uuid.UUID) *DepositApplicationCreate {
	_c.mutation.SetID(v)
	return _c
}

// SetNillableID sets the "id" field if the given value is not nil.
func (_c *DepositApplicationCreate) SetNillableID(v *uuid.UUID) *DepositApplicationCreate {
	if v != nil {
		_c.SetID(*v)
	}
	return _c
}

// AddDocumentIDs adds the "documents" edge to the Document entity by IDs.
func (_c *DepositApplicationCreate) AddDocumentIDs(ids ...uuid.UUID) *DepositApplicationCreate {
	_c.mutation.AddDocumentIDs(ids...)
	return _c
}

// AddDocuments adds the "documents" edges to the Document entity.
func (_c *DepositApplicationCreate) AddDocuments(v ...*Document) *DepositApplicationCreate {
	ids := make([]uuid.UUID, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _c.AddDocumentIDs(ids...)
}

// SetRateID sets the "rate" edge to the ProfitRate entity by ID.
func (_c *DepositApplicationCreate) SetRateID(id uuid.UUID) *DepositApplicationCreate {
	_c.mutation.SetRateID(id)
	return _c
}

// SetRate sets the "rate" edge to the ProfitRate entity.
func (_c *DepositApplicationCreate) SetRate(v *ProfitRate) *DepositApplicationCreate {
	return _c.SetRateID(v.ID)
}

// Mutation returns the DepositApplicationMutation object of the builder.
func (_c *DepositApplicationCreate) Mutation() *DepositApplicationMutation {
	return _c.mutation
}

// Save creates the DepositApplication in the database.
func (_c *DepositApplicationCreate) Save(ctx context.Context) (*DepositApplication, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *DepositApplicationCreate) SaveX(ctx context.Context) *DepositApplication {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *DepositApplicationCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *DepositApplicationCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *DepositApplicationCreate) defaults() {
	if _, ok := _c.mutation.CreateTime(); !ok {
		v := depositapplication.DefaultCreateTime()
		_c.mutation.SetCreateTime(v)
	}
	if _, ok := _c.mutation.UpdateTime(); !ok {
		v := depositapplication.DefaultUpdateTime()
		_c.mutation.SetUpdateTime(v)
	}
	if _, ok := _c.mutation.ID(); !ok {
		v := depositapplication.DefaultID()
		_c.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *DepositApplicationCreate) check() error {
	if _, ok := _c.mutation.ProfitRateID(); !ok {
		return &ValidationError{Name: "profit_rate_id", err: errors.New(`ent: missing required field "DepositApplication.profit_rate_id"`)}
	}
	if _, ok := _c.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "DepositApplication.status"`)}
	}
	if v, ok := _c.mutation.Status(); ok {
		if err := depositapplication.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "DepositApplication.status": %w`, err)}
		}
	}
	if _, ok := _c.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "DepositApplication.user_id"`)}
	}
	if _, ok := _c.mutation.UserIin(); !ok {
		return &ValidationError{Name: "user_iin", err: errors.New(`ent: missing required field "DepositApplication.user_iin"`)}
	}
	if _, ok := _c.mutation.DepositSourceAccountID(); !ok {
		return &ValidationError{Name: "deposit_source_account_id", err: errors.New(`ent: missing required field "DepositApplication.deposit_source_account_id"`)}
	}
	if _, ok := _c.mutation.DepositAmount(); !ok {
		return &ValidationError{Name: "deposit_amount", err: errors.New(`ent: missing required field "DepositApplication.deposit_amount"`)}
	}
	if _, ok := _c.mutation.DepositPayoutMethod(); !ok {
		return &ValidationError{Name: "deposit_payout_method", err: errors.New(`ent: missing required field "DepositApplication.deposit_payout_method"`)}
	}
	if v, ok := _c.mutation.DepositPayoutMethod(); ok {
		if err := depositapplication.DepositPayoutMethodValidator(v); err != nil {
			return &ValidationError{Name: "deposit_payout_method", err: fmt.Errorf(`ent: validator failed for field "DepositApplication.deposit_payout_method": %w`, err)}
		}
	}
	if len(_c.mutation.RateIDs()) == 0 {
		return &ValidationError{Name: "rate", err: errors.New(`ent: missing required edge "DepositApplication.rate"`)}
	}
	return nil
}

func (_c *DepositApplicationCreate) sqlSave(ctx context.Context) (*DepositApplication, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *DepositApplicationCreate) createSpec() (*DepositApplication, *sqlgraph.CreateSpec) {
	var (
		_node = &DepositApplication{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(depositapplication.Table, sqlgraph.NewFieldSpec(depositapplication.FieldID, field.TypeUUID))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := _c.mutation.CreateTime(); ok {
		_spec.SetField(depositapplication.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = value
	}
	if value, ok := _c.mutation.UpdateTime(); ok {
		_spec.SetField(depositapplication.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = value
	}
	if value, ok := _c.mutation.ColvirReferenceID(); ok {
		_spec.SetField(depositapplication.FieldColvirReferenceID, field.TypeString, value)
		_node.ColvirReferenceID = &value
	}
	if value, ok := _c.mutation.Status(); ok {
		_spec.SetField(depositapplication.FieldStatus, field.TypeEnum, value)
		_node.Status = value
	}
	if value, ok := _c.mutation.UserID(); ok {
		_spec.SetField(depositapplication.FieldUserID, field.TypeUUID, value)
		_node.UserID = value
	}
	if value, ok := _c.mutation.UserIin(); ok {
		_spec.SetField(depositapplication.FieldUserIin, field.TypeString, value)
		_node.UserIin = value
	}
	if value, ok := _c.mutation.DepositSourceAccountID(); ok {
		_spec.SetField(depositapplication.FieldDepositSourceAccountID, field.TypeUUID, value)
		_node.DepositSourceAccountID = value
	}
	if value, ok := _c.mutation.DepositAmount(); ok {
		_spec.SetField(depositapplication.FieldDepositAmount, field.TypeString, value)
		_node.DepositAmount = value
	}
	if value, ok := _c.mutation.DepositPayoutMethod(); ok {
		_spec.SetField(depositapplication.FieldDepositPayoutMethod, field.TypeEnum, value)
		_node.DepositPayoutMethod = value
	}
	if value, ok := _c.mutation.ClosedAt(); ok {
		_spec.SetField(depositapplication.FieldClosedAt, field.TypeTime, value)
		_node.ClosedAt = &value
	}
	if nodes := _c.mutation.DocumentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   depositapplication.DocumentsTable,
			Columns: []string{depositapplication.DocumentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(document.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := _c.mutation.RateIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   depositapplication.RateTable,
			Columns: []string{depositapplication.RateColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(profitrate.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.ProfitRateID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.DepositApplication.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.DepositApplicationUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *DepositApplicationCreate) OnConflict(opts ...sql.ConflictOption) *DepositApplicationUpsertOne {
	_c.conflict = opts
	return &DepositApplicationUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.DepositApplication.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *DepositApplicationCreate) OnConflictColumns(columns ...string) *DepositApplicationUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &DepositApplicationUpsertOne{
		create: _c,
	}
}

type (
	// DepositApplicationUpsertOne is the builder for "upsert"-ing
	//  one DepositApplication node.
	DepositApplicationUpsertOne struct {
		create *DepositApplicationCreate
	}

	// DepositApplicationUpsert is the "OnConflict" setter.
	DepositApplicationUpsert struct {
		*sql.UpdateSet
	}
)

// SetProfitRateID sets the "profit_rate_id" field.
func (u *DepositApplicationUpsert) SetProfitRateID(v uuid.UUID) *DepositApplicationUpsert {
	u.Set(depositapplication.FieldProfitRateID, v)
	return u
}

// UpdateProfitRateID sets the "profit_rate_id" field to the value that was provided on create.
func (u *DepositApplicationUpsert) UpdateProfitRateID() *DepositApplicationUpsert {
	u.SetExcluded(depositapplication.FieldProfitRateID)
	return u
}

// SetColvirReferenceID sets the "colvir_reference_id" field.
func (u *DepositApplicationUpsert) SetColvirReferenceID(v string) *DepositApplicationUpsert {
	u.Set(depositapplication.FieldColvirReferenceID, v)
	return u
}

// UpdateColvirReferenceID sets the "colvir_reference_id" field to the value that was provided on create.
func (u *DepositApplicationUpsert) UpdateColvirReferenceID() *DepositApplicationUpsert {
	u.SetExcluded(depositapplication.FieldColvirReferenceID)
	return u
}

// ClearColvirReferenceID clears the value of the "colvir_reference_id" field.
func (u *DepositApplicationUpsert) ClearColvirReferenceID() *DepositApplicationUpsert {
	u.SetNull(depositapplication.FieldColvirReferenceID)
	return u
}

// SetStatus sets the "status" field.
func (u *DepositApplicationUpsert) SetStatus(v depositapplication.Status) *DepositApplicationUpsert {
	u.Set(depositapplication.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *DepositApplicationUpsert) UpdateStatus() *DepositApplicationUpsert {
	u.SetExcluded(depositapplication.FieldStatus)
	return u
}

// SetUserID sets the "user_id" field.
func (u *DepositApplicationUpsert) SetUserID(v uuid.UUID) *DepositApplicationUpsert {
	u.Set(depositapplication.FieldUserID, v)
	return u
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *DepositApplicationUpsert) UpdateUserID() *DepositApplicationUpsert {
	u.SetExcluded(depositapplication.FieldUserID)
	return u
}

// SetUserIin sets the "user_iin" field.
func (u *DepositApplicationUpsert) SetUserIin(v string) *DepositApplicationUpsert {
	u.Set(depositapplication.FieldUserIin, v)
	return u
}

// UpdateUserIin sets the "user_iin" field to the value that was provided on create.
func (u *DepositApplicationUpsert) UpdateUserIin() *DepositApplicationUpsert {
	u.SetExcluded(depositapplication.FieldUserIin)
	return u
}

// SetDepositSourceAccountID sets the "deposit_source_account_id" field.
func (u *DepositApplicationUpsert) SetDepositSourceAccountID(v uuid.UUID) *DepositApplicationUpsert {
	u.Set(depositapplication.FieldDepositSourceAccountID, v)
	return u
}

// UpdateDepositSourceAccountID sets the "deposit_source_account_id" field to the value that was provided on create.
func (u *DepositApplicationUpsert) UpdateDepositSourceAccountID() *DepositApplicationUpsert {
	u.SetExcluded(depositapplication.FieldDepositSourceAccountID)
	return u
}

// SetDepositAmount sets the "deposit_amount" field.
func (u *DepositApplicationUpsert) SetDepositAmount(v decimal.Decimal) *DepositApplicationUpsert {
	u.Set(depositapplication.FieldDepositAmount, v)
	return u
}

// UpdateDepositAmount sets the "deposit_amount" field to the value that was provided on create.
func (u *DepositApplicationUpsert) UpdateDepositAmount() *DepositApplicationUpsert {
	u.SetExcluded(depositapplication.FieldDepositAmount)
	return u
}

// SetDepositPayoutMethod sets the "deposit_payout_method" field.
func (u *DepositApplicationUpsert) SetDepositPayoutMethod(v depositapplication.DepositPayoutMethod) *DepositApplicationUpsert {
	u.Set(depositapplication.FieldDepositPayoutMethod, v)
	return u
}

// UpdateDepositPayoutMethod sets the "deposit_payout_method" field to the value that was provided on create.
func (u *DepositApplicationUpsert) UpdateDepositPayoutMethod() *DepositApplicationUpsert {
	u.SetExcluded(depositapplication.FieldDepositPayoutMethod)
	return u
}

// SetClosedAt sets the "closed_at" field.
func (u *DepositApplicationUpsert) SetClosedAt(v time.Time) *DepositApplicationUpsert {
	u.Set(depositapplication.FieldClosedAt, v)
	return u
}

// UpdateClosedAt sets the "closed_at" field to the value that was provided on create.
func (u *DepositApplicationUpsert) UpdateClosedAt() *DepositApplicationUpsert {
	u.SetExcluded(depositapplication.FieldClosedAt)
	return u
}

// ClearClosedAt clears the value of the "closed_at" field.
func (u *DepositApplicationUpsert) ClearClosedAt() *DepositApplicationUpsert {
	u.SetNull(depositapplication.FieldClosedAt)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.DepositApplication.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(depositapplication.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *DepositApplicationUpsertOne) UpdateNewValues() *DepositApplicationUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(depositapplication.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(depositapplication.FieldCreateTime)
		}
		if _, exists := u.create.mutation.UpdateTime(); exists {
			s.SetIgnore(depositapplication.FieldUpdateTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.DepositApplication.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *DepositApplicationUpsertOne) Ignore() *DepositApplicationUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *DepositApplicationUpsertOne) DoNothing() *DepositApplicationUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the DepositApplicationCreate.OnConflict
// documentation for more info.
func (u *DepositApplicationUpsertOne) Update(set func(*DepositApplicationUpsert)) *DepositApplicationUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&DepositApplicationUpsert{UpdateSet: update})
	}))
	return u
}

// SetProfitRateID sets the "profit_rate_id" field.
func (u *DepositApplicationUpsertOne) SetProfitRateID(v uuid.UUID) *DepositApplicationUpsertOne {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.SetProfitRateID(v)
	})
}

// UpdateProfitRateID sets the "profit_rate_id" field to the value that was provided on create.
func (u *DepositApplicationUpsertOne) UpdateProfitRateID() *DepositApplicationUpsertOne {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.UpdateProfitRateID()
	})
}

// SetColvirReferenceID sets the "colvir_reference_id" field.
func (u *DepositApplicationUpsertOne) SetColvirReferenceID(v string) *DepositApplicationUpsertOne {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.SetColvirReferenceID(v)
	})
}

// UpdateColvirReferenceID sets the "colvir_reference_id" field to the value that was provided on create.
func (u *DepositApplicationUpsertOne) UpdateColvirReferenceID() *DepositApplicationUpsertOne {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.UpdateColvirReferenceID()
	})
}

// ClearColvirReferenceID clears the value of the "colvir_reference_id" field.
func (u *DepositApplicationUpsertOne) ClearColvirReferenceID() *DepositApplicationUpsertOne {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.ClearColvirReferenceID()
	})
}

// SetStatus sets the "status" field.
func (u *DepositApplicationUpsertOne) SetStatus(v depositapplication.Status) *DepositApplicationUpsertOne {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *DepositApplicationUpsertOne) UpdateStatus() *DepositApplicationUpsertOne {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.UpdateStatus()
	})
}

// SetUserID sets the "user_id" field.
func (u *DepositApplicationUpsertOne) SetUserID(v uuid.UUID) *DepositApplicationUpsertOne {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.SetUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *DepositApplicationUpsertOne) UpdateUserID() *DepositApplicationUpsertOne {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.UpdateUserID()
	})
}

// SetUserIin sets the "user_iin" field.
func (u *DepositApplicationUpsertOne) SetUserIin(v string) *DepositApplicationUpsertOne {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.SetUserIin(v)
	})
}

// UpdateUserIin sets the "user_iin" field to the value that was provided on create.
func (u *DepositApplicationUpsertOne) UpdateUserIin() *DepositApplicationUpsertOne {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.UpdateUserIin()
	})
}

// SetDepositSourceAccountID sets the "deposit_source_account_id" field.
func (u *DepositApplicationUpsertOne) SetDepositSourceAccountID(v uuid.UUID) *DepositApplicationUpsertOne {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.SetDepositSourceAccountID(v)
	})
}

// UpdateDepositSourceAccountID sets the "deposit_source_account_id" field to the value that was provided on create.
func (u *DepositApplicationUpsertOne) UpdateDepositSourceAccountID() *DepositApplicationUpsertOne {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.UpdateDepositSourceAccountID()
	})
}

// SetDepositAmount sets the "deposit_amount" field.
func (u *DepositApplicationUpsertOne) SetDepositAmount(v decimal.Decimal) *DepositApplicationUpsertOne {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.SetDepositAmount(v)
	})
}

// UpdateDepositAmount sets the "deposit_amount" field to the value that was provided on create.
func (u *DepositApplicationUpsertOne) UpdateDepositAmount() *DepositApplicationUpsertOne {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.UpdateDepositAmount()
	})
}

// SetDepositPayoutMethod sets the "deposit_payout_method" field.
func (u *DepositApplicationUpsertOne) SetDepositPayoutMethod(v depositapplication.DepositPayoutMethod) *DepositApplicationUpsertOne {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.SetDepositPayoutMethod(v)
	})
}

// UpdateDepositPayoutMethod sets the "deposit_payout_method" field to the value that was provided on create.
func (u *DepositApplicationUpsertOne) UpdateDepositPayoutMethod() *DepositApplicationUpsertOne {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.UpdateDepositPayoutMethod()
	})
}

// SetClosedAt sets the "closed_at" field.
func (u *DepositApplicationUpsertOne) SetClosedAt(v time.Time) *DepositApplicationUpsertOne {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.SetClosedAt(v)
	})
}

// UpdateClosedAt sets the "closed_at" field to the value that was provided on create.
func (u *DepositApplicationUpsertOne) UpdateClosedAt() *DepositApplicationUpsertOne {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.UpdateClosedAt()
	})
}

// ClearClosedAt clears the value of the "closed_at" field.
func (u *DepositApplicationUpsertOne) ClearClosedAt() *DepositApplicationUpsertOne {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.ClearClosedAt()
	})
}

// Exec executes the query.
func (u *DepositApplicationUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for DepositApplicationCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *DepositApplicationUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *DepositApplicationUpsertOne) ID(ctx context.Context) (id uuid.UUID, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: DepositApplicationUpsertOne.ID is not supported by MySQL driver. Use DepositApplicationUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *DepositApplicationUpsertOne) IDX(ctx context.Context) uuid.UUID {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// DepositApplicationCreateBulk is the builder for creating many DepositApplication entities in bulk.
type DepositApplicationCreateBulk struct {
	config
	err      error
	builders []*DepositApplicationCreate
	conflict []sql.ConflictOption
}

// Save creates the DepositApplication entities in the database.
func (_c *DepositApplicationCreateBulk) Save(ctx context.Context) ([]*DepositApplication, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*DepositApplication, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*DepositApplicationMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *DepositApplicationCreateBulk) SaveX(ctx context.Context) []*DepositApplication {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *DepositApplicationCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *DepositApplicationCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.DepositApplication.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.DepositApplicationUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *DepositApplicationCreateBulk) OnConflict(opts ...sql.ConflictOption) *DepositApplicationUpsertBulk {
	_c.conflict = opts
	return &DepositApplicationUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.DepositApplication.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *DepositApplicationCreateBulk) OnConflictColumns(columns ...string) *DepositApplicationUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &DepositApplicationUpsertBulk{
		create: _c,
	}
}

// DepositApplicationUpsertBulk is the builder for "upsert"-ing
// a bulk of DepositApplication nodes.
type DepositApplicationUpsertBulk struct {
	create *DepositApplicationCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.DepositApplication.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(depositapplication.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *DepositApplicationUpsertBulk) UpdateNewValues() *DepositApplicationUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(depositapplication.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(depositapplication.FieldCreateTime)
			}
			if _, exists := b.mutation.UpdateTime(); exists {
				s.SetIgnore(depositapplication.FieldUpdateTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.DepositApplication.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *DepositApplicationUpsertBulk) Ignore() *DepositApplicationUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *DepositApplicationUpsertBulk) DoNothing() *DepositApplicationUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the DepositApplicationCreateBulk.OnConflict
// documentation for more info.
func (u *DepositApplicationUpsertBulk) Update(set func(*DepositApplicationUpsert)) *DepositApplicationUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&DepositApplicationUpsert{UpdateSet: update})
	}))
	return u
}

// SetProfitRateID sets the "profit_rate_id" field.
func (u *DepositApplicationUpsertBulk) SetProfitRateID(v uuid.UUID) *DepositApplicationUpsertBulk {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.SetProfitRateID(v)
	})
}

// UpdateProfitRateID sets the "profit_rate_id" field to the value that was provided on create.
func (u *DepositApplicationUpsertBulk) UpdateProfitRateID() *DepositApplicationUpsertBulk {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.UpdateProfitRateID()
	})
}

// SetColvirReferenceID sets the "colvir_reference_id" field.
func (u *DepositApplicationUpsertBulk) SetColvirReferenceID(v string) *DepositApplicationUpsertBulk {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.SetColvirReferenceID(v)
	})
}

// UpdateColvirReferenceID sets the "colvir_reference_id" field to the value that was provided on create.
func (u *DepositApplicationUpsertBulk) UpdateColvirReferenceID() *DepositApplicationUpsertBulk {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.UpdateColvirReferenceID()
	})
}

// ClearColvirReferenceID clears the value of the "colvir_reference_id" field.
func (u *DepositApplicationUpsertBulk) ClearColvirReferenceID() *DepositApplicationUpsertBulk {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.ClearColvirReferenceID()
	})
}

// SetStatus sets the "status" field.
func (u *DepositApplicationUpsertBulk) SetStatus(v depositapplication.Status) *DepositApplicationUpsertBulk {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *DepositApplicationUpsertBulk) UpdateStatus() *DepositApplicationUpsertBulk {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.UpdateStatus()
	})
}

// SetUserID sets the "user_id" field.
func (u *DepositApplicationUpsertBulk) SetUserID(v uuid.UUID) *DepositApplicationUpsertBulk {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.SetUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *DepositApplicationUpsertBulk) UpdateUserID() *DepositApplicationUpsertBulk {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.UpdateUserID()
	})
}

// SetUserIin sets the "user_iin" field.
func (u *DepositApplicationUpsertBulk) SetUserIin(v string) *DepositApplicationUpsertBulk {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.SetUserIin(v)
	})
}

// UpdateUserIin sets the "user_iin" field to the value that was provided on create.
func (u *DepositApplicationUpsertBulk) UpdateUserIin() *DepositApplicationUpsertBulk {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.UpdateUserIin()
	})
}

// SetDepositSourceAccountID sets the "deposit_source_account_id" field.
func (u *DepositApplicationUpsertBulk) SetDepositSourceAccountID(v uuid.UUID) *DepositApplicationUpsertBulk {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.SetDepositSourceAccountID(v)
	})
}

// UpdateDepositSourceAccountID sets the "deposit_source_account_id" field to the value that was provided on create.
func (u *DepositApplicationUpsertBulk) UpdateDepositSourceAccountID() *DepositApplicationUpsertBulk {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.UpdateDepositSourceAccountID()
	})
}

// SetDepositAmount sets the "deposit_amount" field.
func (u *DepositApplicationUpsertBulk) SetDepositAmount(v decimal.Decimal) *DepositApplicationUpsertBulk {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.SetDepositAmount(v)
	})
}

// UpdateDepositAmount sets the "deposit_amount" field to the value that was provided on create.
func (u *DepositApplicationUpsertBulk) UpdateDepositAmount() *DepositApplicationUpsertBulk {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.UpdateDepositAmount()
	})
}

// SetDepositPayoutMethod sets the "deposit_payout_method" field.
func (u *DepositApplicationUpsertBulk) SetDepositPayoutMethod(v depositapplication.DepositPayoutMethod) *DepositApplicationUpsertBulk {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.SetDepositPayoutMethod(v)
	})
}

// UpdateDepositPayoutMethod sets the "deposit_payout_method" field to the value that was provided on create.
func (u *DepositApplicationUpsertBulk) UpdateDepositPayoutMethod() *DepositApplicationUpsertBulk {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.UpdateDepositPayoutMethod()
	})
}

// SetClosedAt sets the "closed_at" field.
func (u *DepositApplicationUpsertBulk) SetClosedAt(v time.Time) *DepositApplicationUpsertBulk {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.SetClosedAt(v)
	})
}

// UpdateClosedAt sets the "closed_at" field to the value that was provided on create.
func (u *DepositApplicationUpsertBulk) UpdateClosedAt() *DepositApplicationUpsertBulk {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.UpdateClosedAt()
	})
}

// ClearClosedAt clears the value of the "closed_at" field.
func (u *DepositApplicationUpsertBulk) ClearClosedAt() *DepositApplicationUpsertBulk {
	return u.Update(func(s *DepositApplicationUpsert) {
		s.ClearClosedAt()
	})
}

// Exec executes the query.
func (u *DepositApplicationUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the DepositApplicationCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for DepositApplicationCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *DepositApplicationUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
