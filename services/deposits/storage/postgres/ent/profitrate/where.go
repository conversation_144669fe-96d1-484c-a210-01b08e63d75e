// Code generated by ent, DO NOT EDIT.

package profitrate

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldEQ(FieldUpdateTime, v))
}

// CurrencyID applies equality check predicate on the "currency_id" field. It's identical to CurrencyIDEQ.
func CurrencyID(v uint) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldEQ(FieldCurrencyID, v))
}

// MonthTerm applies equality check predicate on the "month_term" field. It's identical to MonthTermEQ.
func MonthTerm(v uint32) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldEQ(FieldMonthTerm, v))
}

// ProfitRate applies equality check predicate on the "profit_rate" field. It's identical to ProfitRateEQ.
func ProfitRate(v decimal.Decimal) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldEQ(FieldProfitRate, v))
}

// DateFrom applies equality check predicate on the "date_from" field. It's identical to DateFromEQ.
func DateFrom(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldEQ(FieldDateFrom, v))
}

// DateTo applies equality check predicate on the "date_to" field. It's identical to DateToEQ.
func DateTo(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldEQ(FieldDateTo, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldLTE(FieldCreateTime, v))
}

// CreateTimeIsNil applies the IsNil predicate on the "create_time" field.
func CreateTimeIsNil() predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldIsNull(FieldCreateTime))
}

// CreateTimeNotNil applies the NotNil predicate on the "create_time" field.
func CreateTimeNotNil() predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldNotNull(FieldCreateTime))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldLTE(FieldUpdateTime, v))
}

// UpdateTimeIsNil applies the IsNil predicate on the "update_time" field.
func UpdateTimeIsNil() predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldIsNull(FieldUpdateTime))
}

// UpdateTimeNotNil applies the NotNil predicate on the "update_time" field.
func UpdateTimeNotNil() predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldNotNull(FieldUpdateTime))
}

// CurrencyIDEQ applies the EQ predicate on the "currency_id" field.
func CurrencyIDEQ(v uint) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldEQ(FieldCurrencyID, v))
}

// CurrencyIDNEQ applies the NEQ predicate on the "currency_id" field.
func CurrencyIDNEQ(v uint) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldNEQ(FieldCurrencyID, v))
}

// CurrencyIDIn applies the In predicate on the "currency_id" field.
func CurrencyIDIn(vs ...uint) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldIn(FieldCurrencyID, vs...))
}

// CurrencyIDNotIn applies the NotIn predicate on the "currency_id" field.
func CurrencyIDNotIn(vs ...uint) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldNotIn(FieldCurrencyID, vs...))
}

// MonthTermEQ applies the EQ predicate on the "month_term" field.
func MonthTermEQ(v uint32) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldEQ(FieldMonthTerm, v))
}

// MonthTermNEQ applies the NEQ predicate on the "month_term" field.
func MonthTermNEQ(v uint32) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldNEQ(FieldMonthTerm, v))
}

// MonthTermIn applies the In predicate on the "month_term" field.
func MonthTermIn(vs ...uint32) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldIn(FieldMonthTerm, vs...))
}

// MonthTermNotIn applies the NotIn predicate on the "month_term" field.
func MonthTermNotIn(vs ...uint32) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldNotIn(FieldMonthTerm, vs...))
}

// MonthTermGT applies the GT predicate on the "month_term" field.
func MonthTermGT(v uint32) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldGT(FieldMonthTerm, v))
}

// MonthTermGTE applies the GTE predicate on the "month_term" field.
func MonthTermGTE(v uint32) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldGTE(FieldMonthTerm, v))
}

// MonthTermLT applies the LT predicate on the "month_term" field.
func MonthTermLT(v uint32) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldLT(FieldMonthTerm, v))
}

// MonthTermLTE applies the LTE predicate on the "month_term" field.
func MonthTermLTE(v uint32) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldLTE(FieldMonthTerm, v))
}

// ProfitRateEQ applies the EQ predicate on the "profit_rate" field.
func ProfitRateEQ(v decimal.Decimal) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldEQ(FieldProfitRate, v))
}

// ProfitRateNEQ applies the NEQ predicate on the "profit_rate" field.
func ProfitRateNEQ(v decimal.Decimal) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldNEQ(FieldProfitRate, v))
}

// ProfitRateIn applies the In predicate on the "profit_rate" field.
func ProfitRateIn(vs ...decimal.Decimal) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldIn(FieldProfitRate, vs...))
}

// ProfitRateNotIn applies the NotIn predicate on the "profit_rate" field.
func ProfitRateNotIn(vs ...decimal.Decimal) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldNotIn(FieldProfitRate, vs...))
}

// ProfitRateGT applies the GT predicate on the "profit_rate" field.
func ProfitRateGT(v decimal.Decimal) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldGT(FieldProfitRate, v))
}

// ProfitRateGTE applies the GTE predicate on the "profit_rate" field.
func ProfitRateGTE(v decimal.Decimal) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldGTE(FieldProfitRate, v))
}

// ProfitRateLT applies the LT predicate on the "profit_rate" field.
func ProfitRateLT(v decimal.Decimal) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldLT(FieldProfitRate, v))
}

// ProfitRateLTE applies the LTE predicate on the "profit_rate" field.
func ProfitRateLTE(v decimal.Decimal) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldLTE(FieldProfitRate, v))
}

// ProfitRateContains applies the Contains predicate on the "profit_rate" field.
func ProfitRateContains(v decimal.Decimal) predicate.ProfitRate {
	vc := v.String()
	return predicate.ProfitRate(sql.FieldContains(FieldProfitRate, vc))
}

// ProfitRateHasPrefix applies the HasPrefix predicate on the "profit_rate" field.
func ProfitRateHasPrefix(v decimal.Decimal) predicate.ProfitRate {
	vc := v.String()
	return predicate.ProfitRate(sql.FieldHasPrefix(FieldProfitRate, vc))
}

// ProfitRateHasSuffix applies the HasSuffix predicate on the "profit_rate" field.
func ProfitRateHasSuffix(v decimal.Decimal) predicate.ProfitRate {
	vc := v.String()
	return predicate.ProfitRate(sql.FieldHasSuffix(FieldProfitRate, vc))
}

// ProfitRateEqualFold applies the EqualFold predicate on the "profit_rate" field.
func ProfitRateEqualFold(v decimal.Decimal) predicate.ProfitRate {
	vc := v.String()
	return predicate.ProfitRate(sql.FieldEqualFold(FieldProfitRate, vc))
}

// ProfitRateContainsFold applies the ContainsFold predicate on the "profit_rate" field.
func ProfitRateContainsFold(v decimal.Decimal) predicate.ProfitRate {
	vc := v.String()
	return predicate.ProfitRate(sql.FieldContainsFold(FieldProfitRate, vc))
}

// DateFromEQ applies the EQ predicate on the "date_from" field.
func DateFromEQ(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldEQ(FieldDateFrom, v))
}

// DateFromNEQ applies the NEQ predicate on the "date_from" field.
func DateFromNEQ(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldNEQ(FieldDateFrom, v))
}

// DateFromIn applies the In predicate on the "date_from" field.
func DateFromIn(vs ...time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldIn(FieldDateFrom, vs...))
}

// DateFromNotIn applies the NotIn predicate on the "date_from" field.
func DateFromNotIn(vs ...time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldNotIn(FieldDateFrom, vs...))
}

// DateFromGT applies the GT predicate on the "date_from" field.
func DateFromGT(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldGT(FieldDateFrom, v))
}

// DateFromGTE applies the GTE predicate on the "date_from" field.
func DateFromGTE(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldGTE(FieldDateFrom, v))
}

// DateFromLT applies the LT predicate on the "date_from" field.
func DateFromLT(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldLT(FieldDateFrom, v))
}

// DateFromLTE applies the LTE predicate on the "date_from" field.
func DateFromLTE(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldLTE(FieldDateFrom, v))
}

// DateToEQ applies the EQ predicate on the "date_to" field.
func DateToEQ(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldEQ(FieldDateTo, v))
}

// DateToNEQ applies the NEQ predicate on the "date_to" field.
func DateToNEQ(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldNEQ(FieldDateTo, v))
}

// DateToIn applies the In predicate on the "date_to" field.
func DateToIn(vs ...time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldIn(FieldDateTo, vs...))
}

// DateToNotIn applies the NotIn predicate on the "date_to" field.
func DateToNotIn(vs ...time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldNotIn(FieldDateTo, vs...))
}

// DateToGT applies the GT predicate on the "date_to" field.
func DateToGT(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldGT(FieldDateTo, v))
}

// DateToGTE applies the GTE predicate on the "date_to" field.
func DateToGTE(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldGTE(FieldDateTo, v))
}

// DateToLT applies the LT predicate on the "date_to" field.
func DateToLT(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldLT(FieldDateTo, v))
}

// DateToLTE applies the LTE predicate on the "date_to" field.
func DateToLTE(v time.Time) predicate.ProfitRate {
	return predicate.ProfitRate(sql.FieldLTE(FieldDateTo, v))
}

// HasCurrency applies the HasEdge predicate on the "currency" edge.
func HasCurrency() predicate.ProfitRate {
	return predicate.ProfitRate(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, CurrencyTable, CurrencyColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasCurrencyWith applies the HasEdge predicate on the "currency" edge with a given conditions (other predicates).
func HasCurrencyWith(preds ...predicate.Currency) predicate.ProfitRate {
	return predicate.ProfitRate(func(s *sql.Selector) {
		step := newCurrencyStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasRateDepositApplication applies the HasEdge predicate on the "rate_deposit_application" edge.
func HasRateDepositApplication() predicate.ProfitRate {
	return predicate.ProfitRate(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, RateDepositApplicationTable, RateDepositApplicationColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasRateDepositApplicationWith applies the HasEdge predicate on the "rate_deposit_application" edge with a given conditions (other predicates).
func HasRateDepositApplicationWith(preds ...predicate.DepositApplication) predicate.ProfitRate {
	return predicate.ProfitRate(func(s *sql.Selector) {
		step := newRateDepositApplicationStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.ProfitRate) predicate.ProfitRate {
	return predicate.ProfitRate(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.ProfitRate) predicate.ProfitRate {
	return predicate.ProfitRate(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.ProfitRate) predicate.ProfitRate {
	return predicate.ProfitRate(sql.NotPredicates(p))
}
