// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"database/sql/driver"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/currency"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositproduct"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/predicate"
)

// DepositProductQuery is the builder for querying DepositProduct entities.
type DepositProductQuery struct {
	config
	ctx          *QueryContext
	order        []depositproduct.OrderOption
	inters       []Interceptor
	predicates   []predicate.DepositProduct
	withCurrency *CurrencyQuery
	modifiers    []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the DepositProductQuery builder.
func (_q *DepositProductQuery) Where(ps ...predicate.DepositProduct) *DepositProductQuery {
	_q.predicates = append(_q.predicates, ps...)
	return _q
}

// Limit the number of records to be returned by this query.
func (_q *DepositProductQuery) Limit(limit int) *DepositProductQuery {
	_q.ctx.Limit = &limit
	return _q
}

// Offset to start from.
func (_q *DepositProductQuery) Offset(offset int) *DepositProductQuery {
	_q.ctx.Offset = &offset
	return _q
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (_q *DepositProductQuery) Unique(unique bool) *DepositProductQuery {
	_q.ctx.Unique = &unique
	return _q
}

// Order specifies how the records should be ordered.
func (_q *DepositProductQuery) Order(o ...depositproduct.OrderOption) *DepositProductQuery {
	_q.order = append(_q.order, o...)
	return _q
}

// QueryCurrency chains the current query on the "currency" edge.
func (_q *DepositProductQuery) QueryCurrency() *CurrencyQuery {
	query := (&CurrencyClient{config: _q.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := _q.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := _q.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(depositproduct.Table, depositproduct.FieldID, selector),
			sqlgraph.To(currency.Table, currency.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, depositproduct.CurrencyTable, depositproduct.CurrencyColumn),
		)
		fromU = sqlgraph.SetNeighbors(_q.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first DepositProduct entity from the query.
// Returns a *NotFoundError when no DepositProduct was found.
func (_q *DepositProductQuery) First(ctx context.Context) (*DepositProduct, error) {
	nodes, err := _q.Limit(1).All(setContextOp(ctx, _q.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{depositproduct.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (_q *DepositProductQuery) FirstX(ctx context.Context) *DepositProduct {
	node, err := _q.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first DepositProduct ID from the query.
// Returns a *NotFoundError when no DepositProduct ID was found.
func (_q *DepositProductQuery) FirstID(ctx context.Context) (id uint, err error) {
	var ids []uint
	if ids, err = _q.Limit(1).IDs(setContextOp(ctx, _q.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{depositproduct.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (_q *DepositProductQuery) FirstIDX(ctx context.Context) uint {
	id, err := _q.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single DepositProduct entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one DepositProduct entity is found.
// Returns a *NotFoundError when no DepositProduct entities are found.
func (_q *DepositProductQuery) Only(ctx context.Context) (*DepositProduct, error) {
	nodes, err := _q.Limit(2).All(setContextOp(ctx, _q.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{depositproduct.Label}
	default:
		return nil, &NotSingularError{depositproduct.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (_q *DepositProductQuery) OnlyX(ctx context.Context) *DepositProduct {
	node, err := _q.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only DepositProduct ID in the query.
// Returns a *NotSingularError when more than one DepositProduct ID is found.
// Returns a *NotFoundError when no entities are found.
func (_q *DepositProductQuery) OnlyID(ctx context.Context) (id uint, err error) {
	var ids []uint
	if ids, err = _q.Limit(2).IDs(setContextOp(ctx, _q.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{depositproduct.Label}
	default:
		err = &NotSingularError{depositproduct.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (_q *DepositProductQuery) OnlyIDX(ctx context.Context) uint {
	id, err := _q.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of DepositProducts.
func (_q *DepositProductQuery) All(ctx context.Context) ([]*DepositProduct, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryAll)
	if err := _q.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*DepositProduct, *DepositProductQuery]()
	return withInterceptors[[]*DepositProduct](ctx, _q, qr, _q.inters)
}

// AllX is like All, but panics if an error occurs.
func (_q *DepositProductQuery) AllX(ctx context.Context) []*DepositProduct {
	nodes, err := _q.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of DepositProduct IDs.
func (_q *DepositProductQuery) IDs(ctx context.Context) (ids []uint, err error) {
	if _q.ctx.Unique == nil && _q.path != nil {
		_q.Unique(true)
	}
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryIDs)
	if err = _q.Select(depositproduct.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (_q *DepositProductQuery) IDsX(ctx context.Context) []uint {
	ids, err := _q.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (_q *DepositProductQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryCount)
	if err := _q.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, _q, querierCount[*DepositProductQuery](), _q.inters)
}

// CountX is like Count, but panics if an error occurs.
func (_q *DepositProductQuery) CountX(ctx context.Context) int {
	count, err := _q.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (_q *DepositProductQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryExist)
	switch _, err := _q.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (_q *DepositProductQuery) ExistX(ctx context.Context) bool {
	exist, err := _q.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the DepositProductQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (_q *DepositProductQuery) Clone() *DepositProductQuery {
	if _q == nil {
		return nil
	}
	return &DepositProductQuery{
		config:       _q.config,
		ctx:          _q.ctx.Clone(),
		order:        append([]depositproduct.OrderOption{}, _q.order...),
		inters:       append([]Interceptor{}, _q.inters...),
		predicates:   append([]predicate.DepositProduct{}, _q.predicates...),
		withCurrency: _q.withCurrency.Clone(),
		// clone intermediate query.
		sql:       _q.sql.Clone(),
		path:      _q.path,
		modifiers: append([]func(*sql.Selector){}, _q.modifiers...),
	}
}

// WithCurrency tells the query-builder to eager-load the nodes that are connected to
// the "currency" edge. The optional arguments are used to configure the query builder of the edge.
func (_q *DepositProductQuery) WithCurrency(opts ...func(*CurrencyQuery)) *DepositProductQuery {
	query := (&CurrencyClient{config: _q.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	_q.withCurrency = query
	return _q
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		ProductCode string `json:"product_code,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.DepositProduct.Query().
//		GroupBy(depositproduct.FieldProductCode).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (_q *DepositProductQuery) GroupBy(field string, fields ...string) *DepositProductGroupBy {
	_q.ctx.Fields = append([]string{field}, fields...)
	grbuild := &DepositProductGroupBy{build: _q}
	grbuild.flds = &_q.ctx.Fields
	grbuild.label = depositproduct.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		ProductCode string `json:"product_code,omitempty"`
//	}
//
//	client.DepositProduct.Query().
//		Select(depositproduct.FieldProductCode).
//		Scan(ctx, &v)
func (_q *DepositProductQuery) Select(fields ...string) *DepositProductSelect {
	_q.ctx.Fields = append(_q.ctx.Fields, fields...)
	sbuild := &DepositProductSelect{DepositProductQuery: _q}
	sbuild.label = depositproduct.Label
	sbuild.flds, sbuild.scan = &_q.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a DepositProductSelect configured with the given aggregations.
func (_q *DepositProductQuery) Aggregate(fns ...AggregateFunc) *DepositProductSelect {
	return _q.Select().Aggregate(fns...)
}

func (_q *DepositProductQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range _q.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, _q); err != nil {
				return err
			}
		}
	}
	for _, f := range _q.ctx.Fields {
		if !depositproduct.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if _q.path != nil {
		prev, err := _q.path(ctx)
		if err != nil {
			return err
		}
		_q.sql = prev
	}
	return nil
}

func (_q *DepositProductQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*DepositProduct, error) {
	var (
		nodes       = []*DepositProduct{}
		_spec       = _q.querySpec()
		loadedTypes = [1]bool{
			_q.withCurrency != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*DepositProduct).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &DepositProduct{config: _q.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	if len(_q.modifiers) > 0 {
		_spec.Modifiers = _q.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, _q.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := _q.withCurrency; query != nil {
		if err := _q.loadCurrency(ctx, query, nodes,
			func(n *DepositProduct) { n.Edges.Currency = []*Currency{} },
			func(n *DepositProduct, e *Currency) { n.Edges.Currency = append(n.Edges.Currency, e) }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (_q *DepositProductQuery) loadCurrency(ctx context.Context, query *CurrencyQuery, nodes []*DepositProduct, init func(*DepositProduct), assign func(*DepositProduct, *Currency)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[uint]*DepositProduct)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(currency.FieldProductID)
	}
	query.Where(predicate.Currency(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(depositproduct.CurrencyColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.ProductID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "product_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}

func (_q *DepositProductQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := _q.querySpec()
	if len(_q.modifiers) > 0 {
		_spec.Modifiers = _q.modifiers
	}
	_spec.Node.Columns = _q.ctx.Fields
	if len(_q.ctx.Fields) > 0 {
		_spec.Unique = _q.ctx.Unique != nil && *_q.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, _q.driver, _spec)
}

func (_q *DepositProductQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(depositproduct.Table, depositproduct.Columns, sqlgraph.NewFieldSpec(depositproduct.FieldID, field.TypeUint))
	_spec.From = _q.sql
	if unique := _q.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if _q.path != nil {
		_spec.Unique = true
	}
	if fields := _q.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, depositproduct.FieldID)
		for i := range fields {
			if fields[i] != depositproduct.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := _q.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := _q.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := _q.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := _q.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (_q *DepositProductQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(_q.driver.Dialect())
	t1 := builder.Table(depositproduct.Table)
	columns := _q.ctx.Fields
	if len(columns) == 0 {
		columns = depositproduct.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if _q.sql != nil {
		selector = _q.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if _q.ctx.Unique != nil && *_q.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range _q.modifiers {
		m(selector)
	}
	for _, p := range _q.predicates {
		p(selector)
	}
	for _, p := range _q.order {
		p(selector)
	}
	if offset := _q.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := _q.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (_q *DepositProductQuery) ForUpdate(opts ...sql.LockOption) *DepositProductQuery {
	if _q.driver.Dialect() == dialect.Postgres {
		_q.Unique(false)
	}
	_q.modifiers = append(_q.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return _q
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (_q *DepositProductQuery) ForShare(opts ...sql.LockOption) *DepositProductQuery {
	if _q.driver.Dialect() == dialect.Postgres {
		_q.Unique(false)
	}
	_q.modifiers = append(_q.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return _q
}

// Modify adds a query modifier for attaching custom logic to queries.
func (_q *DepositProductQuery) Modify(modifiers ...func(s *sql.Selector)) *DepositProductSelect {
	_q.modifiers = append(_q.modifiers, modifiers...)
	return _q.Select()
}

// DepositProductGroupBy is the group-by builder for DepositProduct entities.
type DepositProductGroupBy struct {
	selector
	build *DepositProductQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (_g *DepositProductGroupBy) Aggregate(fns ...AggregateFunc) *DepositProductGroupBy {
	_g.fns = append(_g.fns, fns...)
	return _g
}

// Scan applies the selector query and scans the result into the given value.
func (_g *DepositProductGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, _g.build.ctx, ent.OpQueryGroupBy)
	if err := _g.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*DepositProductQuery, *DepositProductGroupBy](ctx, _g.build, _g, _g.build.inters, v)
}

func (_g *DepositProductGroupBy) sqlScan(ctx context.Context, root *DepositProductQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(_g.fns))
	for _, fn := range _g.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*_g.flds)+len(_g.fns))
		for _, f := range *_g.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*_g.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := _g.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// DepositProductSelect is the builder for selecting fields of DepositProduct entities.
type DepositProductSelect struct {
	*DepositProductQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (_s *DepositProductSelect) Aggregate(fns ...AggregateFunc) *DepositProductSelect {
	_s.fns = append(_s.fns, fns...)
	return _s
}

// Scan applies the selector query and scans the result into the given value.
func (_s *DepositProductSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, _s.ctx, ent.OpQuerySelect)
	if err := _s.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*DepositProductQuery, *DepositProductSelect](ctx, _s.DepositProductQuery, _s, _s.inters, v)
}

func (_s *DepositProductSelect) sqlScan(ctx context.Context, root *DepositProductQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(_s.fns))
	for _, fn := range _s.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*_s.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := _s.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (_s *DepositProductSelect) Modify(modifiers ...func(s *sql.Selector)) *DepositProductSelect {
	_s.modifiers = append(_s.modifiers, modifiers...)
	return _s
}
