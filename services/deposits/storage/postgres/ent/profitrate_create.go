// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/currency"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositapplication"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/profitrate"
)

// ProfitRateCreate is the builder for creating a ProfitRate entity.
type ProfitRateCreate struct {
	config
	mutation *ProfitRateMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (_c *ProfitRateCreate) SetCreateTime(v time.Time) *ProfitRateCreate {
	_c.mutation.SetCreateTime(v)
	return _c
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (_c *ProfitRateCreate) SetNillableCreateTime(v *time.Time) *ProfitRateCreate {
	if v != nil {
		_c.SetCreateTime(*v)
	}
	return _c
}

// SetUpdateTime sets the "update_time" field.
func (_c *ProfitRateCreate) SetUpdateTime(v time.Time) *ProfitRateCreate {
	_c.mutation.SetUpdateTime(v)
	return _c
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (_c *ProfitRateCreate) SetNillableUpdateTime(v *time.Time) *ProfitRateCreate {
	if v != nil {
		_c.SetUpdateTime(*v)
	}
	return _c
}

// SetCurrencyID sets the "currency_id" field.
func (_c *ProfitRateCreate) SetCurrencyID(v uint) *ProfitRateCreate {
	_c.mutation.SetCurrencyID(v)
	return _c
}

// SetMonthTerm sets the "month_term" field.
func (_c *ProfitRateCreate) SetMonthTerm(v uint32) *ProfitRateCreate {
	_c.mutation.SetMonthTerm(v)
	return _c
}

// SetProfitRate sets the "profit_rate" field.
func (_c *ProfitRateCreate) SetProfitRate(v decimal.Decimal) *ProfitRateCreate {
	_c.mutation.SetProfitRate(v)
	return _c
}

// SetDateFrom sets the "date_from" field.
func (_c *ProfitRateCreate) SetDateFrom(v time.Time) *ProfitRateCreate {
	_c.mutation.SetDateFrom(v)
	return _c
}

// SetDateTo sets the "date_to" field.
func (_c *ProfitRateCreate) SetDateTo(v time.Time) *ProfitRateCreate {
	_c.mutation.SetDateTo(v)
	return _c
}

// SetID sets the "id" field.
func (_c *ProfitRateCreate) SetID(v uuid.UUID) *ProfitRateCreate {
	_c.mutation.SetID(v)
	return _c
}

// SetNillableID sets the "id" field if the given value is not nil.
func (_c *ProfitRateCreate) SetNillableID(v *uuid.UUID) *ProfitRateCreate {
	if v != nil {
		_c.SetID(*v)
	}
	return _c
}

// SetCurrency sets the "currency" edge to the Currency entity.
func (_c *ProfitRateCreate) SetCurrency(v *Currency) *ProfitRateCreate {
	return _c.SetCurrencyID(v.ID)
}

// AddRateDepositApplicationIDs adds the "rate_deposit_application" edge to the DepositApplication entity by IDs.
func (_c *ProfitRateCreate) AddRateDepositApplicationIDs(ids ...uuid.UUID) *ProfitRateCreate {
	_c.mutation.AddRateDepositApplicationIDs(ids...)
	return _c
}

// AddRateDepositApplication adds the "rate_deposit_application" edges to the DepositApplication entity.
func (_c *ProfitRateCreate) AddRateDepositApplication(v ...*DepositApplication) *ProfitRateCreate {
	ids := make([]uuid.UUID, len(v))
	for i := range v {
		ids[i] = v[i].ID
	}
	return _c.AddRateDepositApplicationIDs(ids...)
}

// Mutation returns the ProfitRateMutation object of the builder.
func (_c *ProfitRateCreate) Mutation() *ProfitRateMutation {
	return _c.mutation
}

// Save creates the ProfitRate in the database.
func (_c *ProfitRateCreate) Save(ctx context.Context) (*ProfitRate, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *ProfitRateCreate) SaveX(ctx context.Context) *ProfitRate {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *ProfitRateCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *ProfitRateCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *ProfitRateCreate) defaults() {
	if _, ok := _c.mutation.CreateTime(); !ok {
		v := profitrate.DefaultCreateTime()
		_c.mutation.SetCreateTime(v)
	}
	if _, ok := _c.mutation.UpdateTime(); !ok {
		v := profitrate.DefaultUpdateTime()
		_c.mutation.SetUpdateTime(v)
	}
	if _, ok := _c.mutation.ID(); !ok {
		v := profitrate.DefaultID()
		_c.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *ProfitRateCreate) check() error {
	if _, ok := _c.mutation.CurrencyID(); !ok {
		return &ValidationError{Name: "currency_id", err: errors.New(`ent: missing required field "ProfitRate.currency_id"`)}
	}
	if _, ok := _c.mutation.MonthTerm(); !ok {
		return &ValidationError{Name: "month_term", err: errors.New(`ent: missing required field "ProfitRate.month_term"`)}
	}
	if _, ok := _c.mutation.ProfitRate(); !ok {
		return &ValidationError{Name: "profit_rate", err: errors.New(`ent: missing required field "ProfitRate.profit_rate"`)}
	}
	if _, ok := _c.mutation.DateFrom(); !ok {
		return &ValidationError{Name: "date_from", err: errors.New(`ent: missing required field "ProfitRate.date_from"`)}
	}
	if _, ok := _c.mutation.DateTo(); !ok {
		return &ValidationError{Name: "date_to", err: errors.New(`ent: missing required field "ProfitRate.date_to"`)}
	}
	if len(_c.mutation.CurrencyIDs()) == 0 {
		return &ValidationError{Name: "currency", err: errors.New(`ent: missing required edge "ProfitRate.currency"`)}
	}
	return nil
}

func (_c *ProfitRateCreate) sqlSave(ctx context.Context) (*ProfitRate, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *ProfitRateCreate) createSpec() (*ProfitRate, *sqlgraph.CreateSpec) {
	var (
		_node = &ProfitRate{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(profitrate.Table, sqlgraph.NewFieldSpec(profitrate.FieldID, field.TypeUUID))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := _c.mutation.CreateTime(); ok {
		_spec.SetField(profitrate.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = value
	}
	if value, ok := _c.mutation.UpdateTime(); ok {
		_spec.SetField(profitrate.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = value
	}
	if value, ok := _c.mutation.MonthTerm(); ok {
		_spec.SetField(profitrate.FieldMonthTerm, field.TypeUint32, value)
		_node.MonthTerm = value
	}
	if value, ok := _c.mutation.ProfitRate(); ok {
		_spec.SetField(profitrate.FieldProfitRate, field.TypeString, value)
		_node.ProfitRate = value
	}
	if value, ok := _c.mutation.DateFrom(); ok {
		_spec.SetField(profitrate.FieldDateFrom, field.TypeTime, value)
		_node.DateFrom = value
	}
	if value, ok := _c.mutation.DateTo(); ok {
		_spec.SetField(profitrate.FieldDateTo, field.TypeTime, value)
		_node.DateTo = value
	}
	if nodes := _c.mutation.CurrencyIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   profitrate.CurrencyTable,
			Columns: []string{profitrate.CurrencyColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(currency.FieldID, field.TypeUint),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.CurrencyID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := _c.mutation.RateDepositApplicationIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   profitrate.RateDepositApplicationTable,
			Columns: []string{profitrate.RateDepositApplicationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(depositapplication.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.ProfitRate.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.ProfitRateUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *ProfitRateCreate) OnConflict(opts ...sql.ConflictOption) *ProfitRateUpsertOne {
	_c.conflict = opts
	return &ProfitRateUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.ProfitRate.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *ProfitRateCreate) OnConflictColumns(columns ...string) *ProfitRateUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &ProfitRateUpsertOne{
		create: _c,
	}
}

type (
	// ProfitRateUpsertOne is the builder for "upsert"-ing
	//  one ProfitRate node.
	ProfitRateUpsertOne struct {
		create *ProfitRateCreate
	}

	// ProfitRateUpsert is the "OnConflict" setter.
	ProfitRateUpsert struct {
		*sql.UpdateSet
	}
)

// SetCurrencyID sets the "currency_id" field.
func (u *ProfitRateUpsert) SetCurrencyID(v uint) *ProfitRateUpsert {
	u.Set(profitrate.FieldCurrencyID, v)
	return u
}

// UpdateCurrencyID sets the "currency_id" field to the value that was provided on create.
func (u *ProfitRateUpsert) UpdateCurrencyID() *ProfitRateUpsert {
	u.SetExcluded(profitrate.FieldCurrencyID)
	return u
}

// SetDateFrom sets the "date_from" field.
func (u *ProfitRateUpsert) SetDateFrom(v time.Time) *ProfitRateUpsert {
	u.Set(profitrate.FieldDateFrom, v)
	return u
}

// UpdateDateFrom sets the "date_from" field to the value that was provided on create.
func (u *ProfitRateUpsert) UpdateDateFrom() *ProfitRateUpsert {
	u.SetExcluded(profitrate.FieldDateFrom)
	return u
}

// SetDateTo sets the "date_to" field.
func (u *ProfitRateUpsert) SetDateTo(v time.Time) *ProfitRateUpsert {
	u.Set(profitrate.FieldDateTo, v)
	return u
}

// UpdateDateTo sets the "date_to" field to the value that was provided on create.
func (u *ProfitRateUpsert) UpdateDateTo() *ProfitRateUpsert {
	u.SetExcluded(profitrate.FieldDateTo)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.ProfitRate.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(profitrate.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *ProfitRateUpsertOne) UpdateNewValues() *ProfitRateUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(profitrate.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(profitrate.FieldCreateTime)
		}
		if _, exists := u.create.mutation.UpdateTime(); exists {
			s.SetIgnore(profitrate.FieldUpdateTime)
		}
		if _, exists := u.create.mutation.MonthTerm(); exists {
			s.SetIgnore(profitrate.FieldMonthTerm)
		}
		if _, exists := u.create.mutation.ProfitRate(); exists {
			s.SetIgnore(profitrate.FieldProfitRate)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.ProfitRate.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *ProfitRateUpsertOne) Ignore() *ProfitRateUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *ProfitRateUpsertOne) DoNothing() *ProfitRateUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the ProfitRateCreate.OnConflict
// documentation for more info.
func (u *ProfitRateUpsertOne) Update(set func(*ProfitRateUpsert)) *ProfitRateUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&ProfitRateUpsert{UpdateSet: update})
	}))
	return u
}

// SetCurrencyID sets the "currency_id" field.
func (u *ProfitRateUpsertOne) SetCurrencyID(v uint) *ProfitRateUpsertOne {
	return u.Update(func(s *ProfitRateUpsert) {
		s.SetCurrencyID(v)
	})
}

// UpdateCurrencyID sets the "currency_id" field to the value that was provided on create.
func (u *ProfitRateUpsertOne) UpdateCurrencyID() *ProfitRateUpsertOne {
	return u.Update(func(s *ProfitRateUpsert) {
		s.UpdateCurrencyID()
	})
}

// SetDateFrom sets the "date_from" field.
func (u *ProfitRateUpsertOne) SetDateFrom(v time.Time) *ProfitRateUpsertOne {
	return u.Update(func(s *ProfitRateUpsert) {
		s.SetDateFrom(v)
	})
}

// UpdateDateFrom sets the "date_from" field to the value that was provided on create.
func (u *ProfitRateUpsertOne) UpdateDateFrom() *ProfitRateUpsertOne {
	return u.Update(func(s *ProfitRateUpsert) {
		s.UpdateDateFrom()
	})
}

// SetDateTo sets the "date_to" field.
func (u *ProfitRateUpsertOne) SetDateTo(v time.Time) *ProfitRateUpsertOne {
	return u.Update(func(s *ProfitRateUpsert) {
		s.SetDateTo(v)
	})
}

// UpdateDateTo sets the "date_to" field to the value that was provided on create.
func (u *ProfitRateUpsertOne) UpdateDateTo() *ProfitRateUpsertOne {
	return u.Update(func(s *ProfitRateUpsert) {
		s.UpdateDateTo()
	})
}

// Exec executes the query.
func (u *ProfitRateUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for ProfitRateCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *ProfitRateUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *ProfitRateUpsertOne) ID(ctx context.Context) (id uuid.UUID, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: ProfitRateUpsertOne.ID is not supported by MySQL driver. Use ProfitRateUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *ProfitRateUpsertOne) IDX(ctx context.Context) uuid.UUID {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// ProfitRateCreateBulk is the builder for creating many ProfitRate entities in bulk.
type ProfitRateCreateBulk struct {
	config
	err      error
	builders []*ProfitRateCreate
	conflict []sql.ConflictOption
}

// Save creates the ProfitRate entities in the database.
func (_c *ProfitRateCreateBulk) Save(ctx context.Context) ([]*ProfitRate, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*ProfitRate, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*ProfitRateMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *ProfitRateCreateBulk) SaveX(ctx context.Context) []*ProfitRate {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *ProfitRateCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *ProfitRateCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.ProfitRate.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.ProfitRateUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *ProfitRateCreateBulk) OnConflict(opts ...sql.ConflictOption) *ProfitRateUpsertBulk {
	_c.conflict = opts
	return &ProfitRateUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.ProfitRate.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *ProfitRateCreateBulk) OnConflictColumns(columns ...string) *ProfitRateUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &ProfitRateUpsertBulk{
		create: _c,
	}
}

// ProfitRateUpsertBulk is the builder for "upsert"-ing
// a bulk of ProfitRate nodes.
type ProfitRateUpsertBulk struct {
	create *ProfitRateCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.ProfitRate.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(profitrate.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *ProfitRateUpsertBulk) UpdateNewValues() *ProfitRateUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(profitrate.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(profitrate.FieldCreateTime)
			}
			if _, exists := b.mutation.UpdateTime(); exists {
				s.SetIgnore(profitrate.FieldUpdateTime)
			}
			if _, exists := b.mutation.MonthTerm(); exists {
				s.SetIgnore(profitrate.FieldMonthTerm)
			}
			if _, exists := b.mutation.ProfitRate(); exists {
				s.SetIgnore(profitrate.FieldProfitRate)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.ProfitRate.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *ProfitRateUpsertBulk) Ignore() *ProfitRateUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *ProfitRateUpsertBulk) DoNothing() *ProfitRateUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the ProfitRateCreateBulk.OnConflict
// documentation for more info.
func (u *ProfitRateUpsertBulk) Update(set func(*ProfitRateUpsert)) *ProfitRateUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&ProfitRateUpsert{UpdateSet: update})
	}))
	return u
}

// SetCurrencyID sets the "currency_id" field.
func (u *ProfitRateUpsertBulk) SetCurrencyID(v uint) *ProfitRateUpsertBulk {
	return u.Update(func(s *ProfitRateUpsert) {
		s.SetCurrencyID(v)
	})
}

// UpdateCurrencyID sets the "currency_id" field to the value that was provided on create.
func (u *ProfitRateUpsertBulk) UpdateCurrencyID() *ProfitRateUpsertBulk {
	return u.Update(func(s *ProfitRateUpsert) {
		s.UpdateCurrencyID()
	})
}

// SetDateFrom sets the "date_from" field.
func (u *ProfitRateUpsertBulk) SetDateFrom(v time.Time) *ProfitRateUpsertBulk {
	return u.Update(func(s *ProfitRateUpsert) {
		s.SetDateFrom(v)
	})
}

// UpdateDateFrom sets the "date_from" field to the value that was provided on create.
func (u *ProfitRateUpsertBulk) UpdateDateFrom() *ProfitRateUpsertBulk {
	return u.Update(func(s *ProfitRateUpsert) {
		s.UpdateDateFrom()
	})
}

// SetDateTo sets the "date_to" field.
func (u *ProfitRateUpsertBulk) SetDateTo(v time.Time) *ProfitRateUpsertBulk {
	return u.Update(func(s *ProfitRateUpsert) {
		s.SetDateTo(v)
	})
}

// UpdateDateTo sets the "date_to" field to the value that was provided on create.
func (u *ProfitRateUpsertBulk) UpdateDateTo() *ProfitRateUpsertBulk {
	return u.Update(func(s *ProfitRateUpsert) {
		s.UpdateDateTo()
	})
}

// Exec executes the query.
func (u *ProfitRateUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the ProfitRateCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for ProfitRateCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *ProfitRateUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
