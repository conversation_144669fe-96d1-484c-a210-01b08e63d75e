// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/shopspring/decimal"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositproduct"
)

// DepositProduct is the model entity for the DepositProduct schema.
type DepositProduct struct {
	config `json:"-"`
	// ID of the ent.
	ID uint `json:"id,omitempty"`
	// ProductCode holds the value of the "product_code" field.
	ProductCode string `json:"product_code,omitempty"`
	// ColvirProductCode holds the value of the "colvir_product_code" field.
	ColvirProductCode string `json:"colvir_product_code,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// MaxProfitRate holds the value of the "max_profit_rate" field.
	MaxProfitRate decimal.Decimal `json:"max_profit_rate,omitempty"`
	// IsActive holds the value of the "is_active" field.
	IsActive bool `json:"is_active,omitempty"`
	// IsReplenishable holds the value of the "is_replenishable" field.
	IsReplenishable bool `json:"is_replenishable,omitempty"`
	// ReplenishableDays holds the value of the "replenishable_days" field.
	ReplenishableDays uint `json:"replenishable_days,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the DepositProductQuery when eager-loading is set.
	Edges        DepositProductEdges `json:"edges"`
	selectValues sql.SelectValues
}

// DepositProductEdges holds the relations/edges for other nodes in the graph.
type DepositProductEdges struct {
	// Currency holds the value of the currency edge.
	Currency []*Currency `json:"currency,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// CurrencyOrErr returns the Currency value or an error if the edge
// was not loaded in eager-loading.
func (e DepositProductEdges) CurrencyOrErr() ([]*Currency, error) {
	if e.loadedTypes[0] {
		return e.Currency, nil
	}
	return nil, &NotLoadedError{edge: "currency"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*DepositProduct) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case depositproduct.FieldMaxProfitRate:
			values[i] = new(decimal.Decimal)
		case depositproduct.FieldIsActive, depositproduct.FieldIsReplenishable:
			values[i] = new(sql.NullBool)
		case depositproduct.FieldID, depositproduct.FieldReplenishableDays:
			values[i] = new(sql.NullInt64)
		case depositproduct.FieldProductCode, depositproduct.FieldColvirProductCode, depositproduct.FieldName:
			values[i] = new(sql.NullString)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the DepositProduct fields.
func (_m *DepositProduct) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case depositproduct.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			_m.ID = uint(value.Int64)
		case depositproduct.FieldProductCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field product_code", values[i])
			} else if value.Valid {
				_m.ProductCode = value.String
			}
		case depositproduct.FieldColvirProductCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field colvir_product_code", values[i])
			} else if value.Valid {
				_m.ColvirProductCode = value.String
			}
		case depositproduct.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				_m.Name = value.String
			}
		case depositproduct.FieldMaxProfitRate:
			if value, ok := values[i].(*decimal.Decimal); !ok {
				return fmt.Errorf("unexpected type %T for field max_profit_rate", values[i])
			} else if value != nil {
				_m.MaxProfitRate = *value
			}
		case depositproduct.FieldIsActive:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_active", values[i])
			} else if value.Valid {
				_m.IsActive = value.Bool
			}
		case depositproduct.FieldIsReplenishable:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_replenishable", values[i])
			} else if value.Valid {
				_m.IsReplenishable = value.Bool
			}
		case depositproduct.FieldReplenishableDays:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field replenishable_days", values[i])
			} else if value.Valid {
				_m.ReplenishableDays = uint(value.Int64)
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the DepositProduct.
// This includes values selected through modifiers, order, etc.
func (_m *DepositProduct) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// QueryCurrency queries the "currency" edge of the DepositProduct entity.
func (_m *DepositProduct) QueryCurrency() *CurrencyQuery {
	return NewDepositProductClient(_m.config).QueryCurrency(_m)
}

// Update returns a builder for updating this DepositProduct.
// Note that you need to call DepositProduct.Unwrap() before calling this method if this DepositProduct
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *DepositProduct) Update() *DepositProductUpdateOne {
	return NewDepositProductClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the DepositProduct entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *DepositProduct) Unwrap() *DepositProduct {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("ent: DepositProduct is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *DepositProduct) String() string {
	var builder strings.Builder
	builder.WriteString("DepositProduct(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("product_code=")
	builder.WriteString(_m.ProductCode)
	builder.WriteString(", ")
	builder.WriteString("colvir_product_code=")
	builder.WriteString(_m.ColvirProductCode)
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(_m.Name)
	builder.WriteString(", ")
	builder.WriteString("max_profit_rate=")
	builder.WriteString(fmt.Sprintf("%v", _m.MaxProfitRate))
	builder.WriteString(", ")
	builder.WriteString("is_active=")
	builder.WriteString(fmt.Sprintf("%v", _m.IsActive))
	builder.WriteString(", ")
	builder.WriteString("is_replenishable=")
	builder.WriteString(fmt.Sprintf("%v", _m.IsReplenishable))
	builder.WriteString(", ")
	builder.WriteString("replenishable_days=")
	builder.WriteString(fmt.Sprintf("%v", _m.ReplenishableDays))
	builder.WriteByte(')')
	return builder.String()
}

// DepositProducts is a parsable slice of DepositProduct.
type DepositProducts []*DepositProduct
