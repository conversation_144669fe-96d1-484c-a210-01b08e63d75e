// Code generated by ent, DO NOT EDIT.

package depositapplication

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEQ(FieldUpdateTime, v))
}

// ProfitRateID applies equality check predicate on the "profit_rate_id" field. It's identical to ProfitRateIDEQ.
func ProfitRateID(v uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEQ(FieldProfitRateID, v))
}

// ColvirReferenceID applies equality check predicate on the "colvir_reference_id" field. It's identical to ColvirReferenceIDEQ.
func ColvirReferenceID(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEQ(FieldColvirReferenceID, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEQ(FieldUserID, v))
}

// UserIin applies equality check predicate on the "user_iin" field. It's identical to UserIinEQ.
func UserIin(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEQ(FieldUserIin, v))
}

// DepositSourceAccountID applies equality check predicate on the "deposit_source_account_id" field. It's identical to DepositSourceAccountIDEQ.
func DepositSourceAccountID(v uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEQ(FieldDepositSourceAccountID, v))
}

// DepositAmount applies equality check predicate on the "deposit_amount" field. It's identical to DepositAmountEQ.
func DepositAmount(v decimal.Decimal) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEQ(FieldDepositAmount, v))
}

// ClosedAt applies equality check predicate on the "closed_at" field. It's identical to ClosedAtEQ.
func ClosedAt(v time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEQ(FieldClosedAt, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldLTE(FieldCreateTime, v))
}

// CreateTimeIsNil applies the IsNil predicate on the "create_time" field.
func CreateTimeIsNil() predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldIsNull(FieldCreateTime))
}

// CreateTimeNotNil applies the NotNil predicate on the "create_time" field.
func CreateTimeNotNil() predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNotNull(FieldCreateTime))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldLTE(FieldUpdateTime, v))
}

// UpdateTimeIsNil applies the IsNil predicate on the "update_time" field.
func UpdateTimeIsNil() predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldIsNull(FieldUpdateTime))
}

// UpdateTimeNotNil applies the NotNil predicate on the "update_time" field.
func UpdateTimeNotNil() predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNotNull(FieldUpdateTime))
}

// ProfitRateIDEQ applies the EQ predicate on the "profit_rate_id" field.
func ProfitRateIDEQ(v uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEQ(FieldProfitRateID, v))
}

// ProfitRateIDNEQ applies the NEQ predicate on the "profit_rate_id" field.
func ProfitRateIDNEQ(v uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNEQ(FieldProfitRateID, v))
}

// ProfitRateIDIn applies the In predicate on the "profit_rate_id" field.
func ProfitRateIDIn(vs ...uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldIn(FieldProfitRateID, vs...))
}

// ProfitRateIDNotIn applies the NotIn predicate on the "profit_rate_id" field.
func ProfitRateIDNotIn(vs ...uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNotIn(FieldProfitRateID, vs...))
}

// ColvirReferenceIDEQ applies the EQ predicate on the "colvir_reference_id" field.
func ColvirReferenceIDEQ(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEQ(FieldColvirReferenceID, v))
}

// ColvirReferenceIDNEQ applies the NEQ predicate on the "colvir_reference_id" field.
func ColvirReferenceIDNEQ(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNEQ(FieldColvirReferenceID, v))
}

// ColvirReferenceIDIn applies the In predicate on the "colvir_reference_id" field.
func ColvirReferenceIDIn(vs ...string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldIn(FieldColvirReferenceID, vs...))
}

// ColvirReferenceIDNotIn applies the NotIn predicate on the "colvir_reference_id" field.
func ColvirReferenceIDNotIn(vs ...string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNotIn(FieldColvirReferenceID, vs...))
}

// ColvirReferenceIDGT applies the GT predicate on the "colvir_reference_id" field.
func ColvirReferenceIDGT(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldGT(FieldColvirReferenceID, v))
}

// ColvirReferenceIDGTE applies the GTE predicate on the "colvir_reference_id" field.
func ColvirReferenceIDGTE(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldGTE(FieldColvirReferenceID, v))
}

// ColvirReferenceIDLT applies the LT predicate on the "colvir_reference_id" field.
func ColvirReferenceIDLT(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldLT(FieldColvirReferenceID, v))
}

// ColvirReferenceIDLTE applies the LTE predicate on the "colvir_reference_id" field.
func ColvirReferenceIDLTE(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldLTE(FieldColvirReferenceID, v))
}

// ColvirReferenceIDContains applies the Contains predicate on the "colvir_reference_id" field.
func ColvirReferenceIDContains(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldContains(FieldColvirReferenceID, v))
}

// ColvirReferenceIDHasPrefix applies the HasPrefix predicate on the "colvir_reference_id" field.
func ColvirReferenceIDHasPrefix(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldHasPrefix(FieldColvirReferenceID, v))
}

// ColvirReferenceIDHasSuffix applies the HasSuffix predicate on the "colvir_reference_id" field.
func ColvirReferenceIDHasSuffix(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldHasSuffix(FieldColvirReferenceID, v))
}

// ColvirReferenceIDIsNil applies the IsNil predicate on the "colvir_reference_id" field.
func ColvirReferenceIDIsNil() predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldIsNull(FieldColvirReferenceID))
}

// ColvirReferenceIDNotNil applies the NotNil predicate on the "colvir_reference_id" field.
func ColvirReferenceIDNotNil() predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNotNull(FieldColvirReferenceID))
}

// ColvirReferenceIDEqualFold applies the EqualFold predicate on the "colvir_reference_id" field.
func ColvirReferenceIDEqualFold(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEqualFold(FieldColvirReferenceID, v))
}

// ColvirReferenceIDContainsFold applies the ContainsFold predicate on the "colvir_reference_id" field.
func ColvirReferenceIDContainsFold(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldContainsFold(FieldColvirReferenceID, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNotIn(FieldStatus, vs...))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldLTE(FieldUserID, v))
}

// UserIinEQ applies the EQ predicate on the "user_iin" field.
func UserIinEQ(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEQ(FieldUserIin, v))
}

// UserIinNEQ applies the NEQ predicate on the "user_iin" field.
func UserIinNEQ(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNEQ(FieldUserIin, v))
}

// UserIinIn applies the In predicate on the "user_iin" field.
func UserIinIn(vs ...string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldIn(FieldUserIin, vs...))
}

// UserIinNotIn applies the NotIn predicate on the "user_iin" field.
func UserIinNotIn(vs ...string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNotIn(FieldUserIin, vs...))
}

// UserIinGT applies the GT predicate on the "user_iin" field.
func UserIinGT(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldGT(FieldUserIin, v))
}

// UserIinGTE applies the GTE predicate on the "user_iin" field.
func UserIinGTE(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldGTE(FieldUserIin, v))
}

// UserIinLT applies the LT predicate on the "user_iin" field.
func UserIinLT(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldLT(FieldUserIin, v))
}

// UserIinLTE applies the LTE predicate on the "user_iin" field.
func UserIinLTE(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldLTE(FieldUserIin, v))
}

// UserIinContains applies the Contains predicate on the "user_iin" field.
func UserIinContains(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldContains(FieldUserIin, v))
}

// UserIinHasPrefix applies the HasPrefix predicate on the "user_iin" field.
func UserIinHasPrefix(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldHasPrefix(FieldUserIin, v))
}

// UserIinHasSuffix applies the HasSuffix predicate on the "user_iin" field.
func UserIinHasSuffix(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldHasSuffix(FieldUserIin, v))
}

// UserIinEqualFold applies the EqualFold predicate on the "user_iin" field.
func UserIinEqualFold(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEqualFold(FieldUserIin, v))
}

// UserIinContainsFold applies the ContainsFold predicate on the "user_iin" field.
func UserIinContainsFold(v string) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldContainsFold(FieldUserIin, v))
}

// DepositSourceAccountIDEQ applies the EQ predicate on the "deposit_source_account_id" field.
func DepositSourceAccountIDEQ(v uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEQ(FieldDepositSourceAccountID, v))
}

// DepositSourceAccountIDNEQ applies the NEQ predicate on the "deposit_source_account_id" field.
func DepositSourceAccountIDNEQ(v uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNEQ(FieldDepositSourceAccountID, v))
}

// DepositSourceAccountIDIn applies the In predicate on the "deposit_source_account_id" field.
func DepositSourceAccountIDIn(vs ...uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldIn(FieldDepositSourceAccountID, vs...))
}

// DepositSourceAccountIDNotIn applies the NotIn predicate on the "deposit_source_account_id" field.
func DepositSourceAccountIDNotIn(vs ...uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNotIn(FieldDepositSourceAccountID, vs...))
}

// DepositSourceAccountIDGT applies the GT predicate on the "deposit_source_account_id" field.
func DepositSourceAccountIDGT(v uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldGT(FieldDepositSourceAccountID, v))
}

// DepositSourceAccountIDGTE applies the GTE predicate on the "deposit_source_account_id" field.
func DepositSourceAccountIDGTE(v uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldGTE(FieldDepositSourceAccountID, v))
}

// DepositSourceAccountIDLT applies the LT predicate on the "deposit_source_account_id" field.
func DepositSourceAccountIDLT(v uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldLT(FieldDepositSourceAccountID, v))
}

// DepositSourceAccountIDLTE applies the LTE predicate on the "deposit_source_account_id" field.
func DepositSourceAccountIDLTE(v uuid.UUID) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldLTE(FieldDepositSourceAccountID, v))
}

// DepositAmountEQ applies the EQ predicate on the "deposit_amount" field.
func DepositAmountEQ(v decimal.Decimal) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEQ(FieldDepositAmount, v))
}

// DepositAmountNEQ applies the NEQ predicate on the "deposit_amount" field.
func DepositAmountNEQ(v decimal.Decimal) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNEQ(FieldDepositAmount, v))
}

// DepositAmountIn applies the In predicate on the "deposit_amount" field.
func DepositAmountIn(vs ...decimal.Decimal) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldIn(FieldDepositAmount, vs...))
}

// DepositAmountNotIn applies the NotIn predicate on the "deposit_amount" field.
func DepositAmountNotIn(vs ...decimal.Decimal) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNotIn(FieldDepositAmount, vs...))
}

// DepositAmountGT applies the GT predicate on the "deposit_amount" field.
func DepositAmountGT(v decimal.Decimal) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldGT(FieldDepositAmount, v))
}

// DepositAmountGTE applies the GTE predicate on the "deposit_amount" field.
func DepositAmountGTE(v decimal.Decimal) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldGTE(FieldDepositAmount, v))
}

// DepositAmountLT applies the LT predicate on the "deposit_amount" field.
func DepositAmountLT(v decimal.Decimal) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldLT(FieldDepositAmount, v))
}

// DepositAmountLTE applies the LTE predicate on the "deposit_amount" field.
func DepositAmountLTE(v decimal.Decimal) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldLTE(FieldDepositAmount, v))
}

// DepositAmountContains applies the Contains predicate on the "deposit_amount" field.
func DepositAmountContains(v decimal.Decimal) predicate.DepositApplication {
	vc := v.String()
	return predicate.DepositApplication(sql.FieldContains(FieldDepositAmount, vc))
}

// DepositAmountHasPrefix applies the HasPrefix predicate on the "deposit_amount" field.
func DepositAmountHasPrefix(v decimal.Decimal) predicate.DepositApplication {
	vc := v.String()
	return predicate.DepositApplication(sql.FieldHasPrefix(FieldDepositAmount, vc))
}

// DepositAmountHasSuffix applies the HasSuffix predicate on the "deposit_amount" field.
func DepositAmountHasSuffix(v decimal.Decimal) predicate.DepositApplication {
	vc := v.String()
	return predicate.DepositApplication(sql.FieldHasSuffix(FieldDepositAmount, vc))
}

// DepositAmountEqualFold applies the EqualFold predicate on the "deposit_amount" field.
func DepositAmountEqualFold(v decimal.Decimal) predicate.DepositApplication {
	vc := v.String()
	return predicate.DepositApplication(sql.FieldEqualFold(FieldDepositAmount, vc))
}

// DepositAmountContainsFold applies the ContainsFold predicate on the "deposit_amount" field.
func DepositAmountContainsFold(v decimal.Decimal) predicate.DepositApplication {
	vc := v.String()
	return predicate.DepositApplication(sql.FieldContainsFold(FieldDepositAmount, vc))
}

// DepositPayoutMethodEQ applies the EQ predicate on the "deposit_payout_method" field.
func DepositPayoutMethodEQ(v DepositPayoutMethod) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEQ(FieldDepositPayoutMethod, v))
}

// DepositPayoutMethodNEQ applies the NEQ predicate on the "deposit_payout_method" field.
func DepositPayoutMethodNEQ(v DepositPayoutMethod) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNEQ(FieldDepositPayoutMethod, v))
}

// DepositPayoutMethodIn applies the In predicate on the "deposit_payout_method" field.
func DepositPayoutMethodIn(vs ...DepositPayoutMethod) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldIn(FieldDepositPayoutMethod, vs...))
}

// DepositPayoutMethodNotIn applies the NotIn predicate on the "deposit_payout_method" field.
func DepositPayoutMethodNotIn(vs ...DepositPayoutMethod) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNotIn(FieldDepositPayoutMethod, vs...))
}

// ClosedAtEQ applies the EQ predicate on the "closed_at" field.
func ClosedAtEQ(v time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldEQ(FieldClosedAt, v))
}

// ClosedAtNEQ applies the NEQ predicate on the "closed_at" field.
func ClosedAtNEQ(v time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNEQ(FieldClosedAt, v))
}

// ClosedAtIn applies the In predicate on the "closed_at" field.
func ClosedAtIn(vs ...time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldIn(FieldClosedAt, vs...))
}

// ClosedAtNotIn applies the NotIn predicate on the "closed_at" field.
func ClosedAtNotIn(vs ...time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNotIn(FieldClosedAt, vs...))
}

// ClosedAtGT applies the GT predicate on the "closed_at" field.
func ClosedAtGT(v time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldGT(FieldClosedAt, v))
}

// ClosedAtGTE applies the GTE predicate on the "closed_at" field.
func ClosedAtGTE(v time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldGTE(FieldClosedAt, v))
}

// ClosedAtLT applies the LT predicate on the "closed_at" field.
func ClosedAtLT(v time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldLT(FieldClosedAt, v))
}

// ClosedAtLTE applies the LTE predicate on the "closed_at" field.
func ClosedAtLTE(v time.Time) predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldLTE(FieldClosedAt, v))
}

// ClosedAtIsNil applies the IsNil predicate on the "closed_at" field.
func ClosedAtIsNil() predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldIsNull(FieldClosedAt))
}

// ClosedAtNotNil applies the NotNil predicate on the "closed_at" field.
func ClosedAtNotNil() predicate.DepositApplication {
	return predicate.DepositApplication(sql.FieldNotNull(FieldClosedAt))
}

// HasDocuments applies the HasEdge predicate on the "documents" edge.
func HasDocuments() predicate.DepositApplication {
	return predicate.DepositApplication(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, DocumentsTable, DocumentsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasDocumentsWith applies the HasEdge predicate on the "documents" edge with a given conditions (other predicates).
func HasDocumentsWith(preds ...predicate.Document) predicate.DepositApplication {
	return predicate.DepositApplication(func(s *sql.Selector) {
		step := newDocumentsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasRate applies the HasEdge predicate on the "rate" edge.
func HasRate() predicate.DepositApplication {
	return predicate.DepositApplication(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, RateTable, RateColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasRateWith applies the HasEdge predicate on the "rate" edge with a given conditions (other predicates).
func HasRateWith(preds ...predicate.ProfitRate) predicate.DepositApplication {
	return predicate.DepositApplication(func(s *sql.Selector) {
		step := newRateStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.DepositApplication) predicate.DepositApplication {
	return predicate.DepositApplication(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.DepositApplication) predicate.DepositApplication {
	return predicate.DepositApplication(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.DepositApplication) predicate.DepositApplication {
	return predicate.DepositApplication(sql.NotPredicates(p))
}
