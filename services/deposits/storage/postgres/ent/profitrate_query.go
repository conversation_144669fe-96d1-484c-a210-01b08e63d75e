// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"database/sql/driver"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/currency"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositapplication"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/predicate"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/profitrate"
)

// ProfitRateQuery is the builder for querying ProfitRate entities.
type ProfitRateQuery struct {
	config
	ctx                        *QueryContext
	order                      []profitrate.OrderOption
	inters                     []Interceptor
	predicates                 []predicate.ProfitRate
	withCurrency               *CurrencyQuery
	withRateDepositApplication *DepositApplicationQuery
	modifiers                  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the ProfitRateQuery builder.
func (_q *ProfitRateQuery) Where(ps ...predicate.ProfitRate) *ProfitRateQuery {
	_q.predicates = append(_q.predicates, ps...)
	return _q
}

// Limit the number of records to be returned by this query.
func (_q *ProfitRateQuery) Limit(limit int) *ProfitRateQuery {
	_q.ctx.Limit = &limit
	return _q
}

// Offset to start from.
func (_q *ProfitRateQuery) Offset(offset int) *ProfitRateQuery {
	_q.ctx.Offset = &offset
	return _q
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (_q *ProfitRateQuery) Unique(unique bool) *ProfitRateQuery {
	_q.ctx.Unique = &unique
	return _q
}

// Order specifies how the records should be ordered.
func (_q *ProfitRateQuery) Order(o ...profitrate.OrderOption) *ProfitRateQuery {
	_q.order = append(_q.order, o...)
	return _q
}

// QueryCurrency chains the current query on the "currency" edge.
func (_q *ProfitRateQuery) QueryCurrency() *CurrencyQuery {
	query := (&CurrencyClient{config: _q.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := _q.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := _q.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(profitrate.Table, profitrate.FieldID, selector),
			sqlgraph.To(currency.Table, currency.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, profitrate.CurrencyTable, profitrate.CurrencyColumn),
		)
		fromU = sqlgraph.SetNeighbors(_q.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryRateDepositApplication chains the current query on the "rate_deposit_application" edge.
func (_q *ProfitRateQuery) QueryRateDepositApplication() *DepositApplicationQuery {
	query := (&DepositApplicationClient{config: _q.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := _q.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := _q.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(profitrate.Table, profitrate.FieldID, selector),
			sqlgraph.To(depositapplication.Table, depositapplication.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, profitrate.RateDepositApplicationTable, profitrate.RateDepositApplicationColumn),
		)
		fromU = sqlgraph.SetNeighbors(_q.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first ProfitRate entity from the query.
// Returns a *NotFoundError when no ProfitRate was found.
func (_q *ProfitRateQuery) First(ctx context.Context) (*ProfitRate, error) {
	nodes, err := _q.Limit(1).All(setContextOp(ctx, _q.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{profitrate.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (_q *ProfitRateQuery) FirstX(ctx context.Context) *ProfitRate {
	node, err := _q.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first ProfitRate ID from the query.
// Returns a *NotFoundError when no ProfitRate ID was found.
func (_q *ProfitRateQuery) FirstID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = _q.Limit(1).IDs(setContextOp(ctx, _q.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{profitrate.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (_q *ProfitRateQuery) FirstIDX(ctx context.Context) uuid.UUID {
	id, err := _q.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single ProfitRate entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one ProfitRate entity is found.
// Returns a *NotFoundError when no ProfitRate entities are found.
func (_q *ProfitRateQuery) Only(ctx context.Context) (*ProfitRate, error) {
	nodes, err := _q.Limit(2).All(setContextOp(ctx, _q.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{profitrate.Label}
	default:
		return nil, &NotSingularError{profitrate.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (_q *ProfitRateQuery) OnlyX(ctx context.Context) *ProfitRate {
	node, err := _q.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only ProfitRate ID in the query.
// Returns a *NotSingularError when more than one ProfitRate ID is found.
// Returns a *NotFoundError when no entities are found.
func (_q *ProfitRateQuery) OnlyID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = _q.Limit(2).IDs(setContextOp(ctx, _q.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{profitrate.Label}
	default:
		err = &NotSingularError{profitrate.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (_q *ProfitRateQuery) OnlyIDX(ctx context.Context) uuid.UUID {
	id, err := _q.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of ProfitRates.
func (_q *ProfitRateQuery) All(ctx context.Context) ([]*ProfitRate, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryAll)
	if err := _q.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*ProfitRate, *ProfitRateQuery]()
	return withInterceptors[[]*ProfitRate](ctx, _q, qr, _q.inters)
}

// AllX is like All, but panics if an error occurs.
func (_q *ProfitRateQuery) AllX(ctx context.Context) []*ProfitRate {
	nodes, err := _q.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of ProfitRate IDs.
func (_q *ProfitRateQuery) IDs(ctx context.Context) (ids []uuid.UUID, err error) {
	if _q.ctx.Unique == nil && _q.path != nil {
		_q.Unique(true)
	}
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryIDs)
	if err = _q.Select(profitrate.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (_q *ProfitRateQuery) IDsX(ctx context.Context) []uuid.UUID {
	ids, err := _q.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (_q *ProfitRateQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryCount)
	if err := _q.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, _q, querierCount[*ProfitRateQuery](), _q.inters)
}

// CountX is like Count, but panics if an error occurs.
func (_q *ProfitRateQuery) CountX(ctx context.Context) int {
	count, err := _q.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (_q *ProfitRateQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryExist)
	switch _, err := _q.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (_q *ProfitRateQuery) ExistX(ctx context.Context) bool {
	exist, err := _q.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the ProfitRateQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (_q *ProfitRateQuery) Clone() *ProfitRateQuery {
	if _q == nil {
		return nil
	}
	return &ProfitRateQuery{
		config:                     _q.config,
		ctx:                        _q.ctx.Clone(),
		order:                      append([]profitrate.OrderOption{}, _q.order...),
		inters:                     append([]Interceptor{}, _q.inters...),
		predicates:                 append([]predicate.ProfitRate{}, _q.predicates...),
		withCurrency:               _q.withCurrency.Clone(),
		withRateDepositApplication: _q.withRateDepositApplication.Clone(),
		// clone intermediate query.
		sql:       _q.sql.Clone(),
		path:      _q.path,
		modifiers: append([]func(*sql.Selector){}, _q.modifiers...),
	}
}

// WithCurrency tells the query-builder to eager-load the nodes that are connected to
// the "currency" edge. The optional arguments are used to configure the query builder of the edge.
func (_q *ProfitRateQuery) WithCurrency(opts ...func(*CurrencyQuery)) *ProfitRateQuery {
	query := (&CurrencyClient{config: _q.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	_q.withCurrency = query
	return _q
}

// WithRateDepositApplication tells the query-builder to eager-load the nodes that are connected to
// the "rate_deposit_application" edge. The optional arguments are used to configure the query builder of the edge.
func (_q *ProfitRateQuery) WithRateDepositApplication(opts ...func(*DepositApplicationQuery)) *ProfitRateQuery {
	query := (&DepositApplicationClient{config: _q.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	_q.withRateDepositApplication = query
	return _q
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.ProfitRate.Query().
//		GroupBy(profitrate.FieldCreateTime).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (_q *ProfitRateQuery) GroupBy(field string, fields ...string) *ProfitRateGroupBy {
	_q.ctx.Fields = append([]string{field}, fields...)
	grbuild := &ProfitRateGroupBy{build: _q}
	grbuild.flds = &_q.ctx.Fields
	grbuild.label = profitrate.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//	}
//
//	client.ProfitRate.Query().
//		Select(profitrate.FieldCreateTime).
//		Scan(ctx, &v)
func (_q *ProfitRateQuery) Select(fields ...string) *ProfitRateSelect {
	_q.ctx.Fields = append(_q.ctx.Fields, fields...)
	sbuild := &ProfitRateSelect{ProfitRateQuery: _q}
	sbuild.label = profitrate.Label
	sbuild.flds, sbuild.scan = &_q.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a ProfitRateSelect configured with the given aggregations.
func (_q *ProfitRateQuery) Aggregate(fns ...AggregateFunc) *ProfitRateSelect {
	return _q.Select().Aggregate(fns...)
}

func (_q *ProfitRateQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range _q.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, _q); err != nil {
				return err
			}
		}
	}
	for _, f := range _q.ctx.Fields {
		if !profitrate.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if _q.path != nil {
		prev, err := _q.path(ctx)
		if err != nil {
			return err
		}
		_q.sql = prev
	}
	return nil
}

func (_q *ProfitRateQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*ProfitRate, error) {
	var (
		nodes       = []*ProfitRate{}
		_spec       = _q.querySpec()
		loadedTypes = [2]bool{
			_q.withCurrency != nil,
			_q.withRateDepositApplication != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*ProfitRate).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &ProfitRate{config: _q.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	if len(_q.modifiers) > 0 {
		_spec.Modifiers = _q.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, _q.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := _q.withCurrency; query != nil {
		if err := _q.loadCurrency(ctx, query, nodes, nil,
			func(n *ProfitRate, e *Currency) { n.Edges.Currency = e }); err != nil {
			return nil, err
		}
	}
	if query := _q.withRateDepositApplication; query != nil {
		if err := _q.loadRateDepositApplication(ctx, query, nodes,
			func(n *ProfitRate) { n.Edges.RateDepositApplication = []*DepositApplication{} },
			func(n *ProfitRate, e *DepositApplication) {
				n.Edges.RateDepositApplication = append(n.Edges.RateDepositApplication, e)
			}); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (_q *ProfitRateQuery) loadCurrency(ctx context.Context, query *CurrencyQuery, nodes []*ProfitRate, init func(*ProfitRate), assign func(*ProfitRate, *Currency)) error {
	ids := make([]uint, 0, len(nodes))
	nodeids := make(map[uint][]*ProfitRate)
	for i := range nodes {
		fk := nodes[i].CurrencyID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(currency.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "currency_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (_q *ProfitRateQuery) loadRateDepositApplication(ctx context.Context, query *DepositApplicationQuery, nodes []*ProfitRate, init func(*ProfitRate), assign func(*ProfitRate, *DepositApplication)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[uuid.UUID]*ProfitRate)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(depositapplication.FieldProfitRateID)
	}
	query.Where(predicate.DepositApplication(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(profitrate.RateDepositApplicationColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.ProfitRateID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "profit_rate_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}

func (_q *ProfitRateQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := _q.querySpec()
	if len(_q.modifiers) > 0 {
		_spec.Modifiers = _q.modifiers
	}
	_spec.Node.Columns = _q.ctx.Fields
	if len(_q.ctx.Fields) > 0 {
		_spec.Unique = _q.ctx.Unique != nil && *_q.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, _q.driver, _spec)
}

func (_q *ProfitRateQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(profitrate.Table, profitrate.Columns, sqlgraph.NewFieldSpec(profitrate.FieldID, field.TypeUUID))
	_spec.From = _q.sql
	if unique := _q.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if _q.path != nil {
		_spec.Unique = true
	}
	if fields := _q.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, profitrate.FieldID)
		for i := range fields {
			if fields[i] != profitrate.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if _q.withCurrency != nil {
			_spec.Node.AddColumnOnce(profitrate.FieldCurrencyID)
		}
	}
	if ps := _q.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := _q.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := _q.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := _q.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (_q *ProfitRateQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(_q.driver.Dialect())
	t1 := builder.Table(profitrate.Table)
	columns := _q.ctx.Fields
	if len(columns) == 0 {
		columns = profitrate.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if _q.sql != nil {
		selector = _q.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if _q.ctx.Unique != nil && *_q.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range _q.modifiers {
		m(selector)
	}
	for _, p := range _q.predicates {
		p(selector)
	}
	for _, p := range _q.order {
		p(selector)
	}
	if offset := _q.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := _q.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (_q *ProfitRateQuery) ForUpdate(opts ...sql.LockOption) *ProfitRateQuery {
	if _q.driver.Dialect() == dialect.Postgres {
		_q.Unique(false)
	}
	_q.modifiers = append(_q.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return _q
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (_q *ProfitRateQuery) ForShare(opts ...sql.LockOption) *ProfitRateQuery {
	if _q.driver.Dialect() == dialect.Postgres {
		_q.Unique(false)
	}
	_q.modifiers = append(_q.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return _q
}

// Modify adds a query modifier for attaching custom logic to queries.
func (_q *ProfitRateQuery) Modify(modifiers ...func(s *sql.Selector)) *ProfitRateSelect {
	_q.modifiers = append(_q.modifiers, modifiers...)
	return _q.Select()
}

// ProfitRateGroupBy is the group-by builder for ProfitRate entities.
type ProfitRateGroupBy struct {
	selector
	build *ProfitRateQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (_g *ProfitRateGroupBy) Aggregate(fns ...AggregateFunc) *ProfitRateGroupBy {
	_g.fns = append(_g.fns, fns...)
	return _g
}

// Scan applies the selector query and scans the result into the given value.
func (_g *ProfitRateGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, _g.build.ctx, ent.OpQueryGroupBy)
	if err := _g.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*ProfitRateQuery, *ProfitRateGroupBy](ctx, _g.build, _g, _g.build.inters, v)
}

func (_g *ProfitRateGroupBy) sqlScan(ctx context.Context, root *ProfitRateQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(_g.fns))
	for _, fn := range _g.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*_g.flds)+len(_g.fns))
		for _, f := range *_g.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*_g.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := _g.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// ProfitRateSelect is the builder for selecting fields of ProfitRate entities.
type ProfitRateSelect struct {
	*ProfitRateQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (_s *ProfitRateSelect) Aggregate(fns ...AggregateFunc) *ProfitRateSelect {
	_s.fns = append(_s.fns, fns...)
	return _s
}

// Scan applies the selector query and scans the result into the given value.
func (_s *ProfitRateSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, _s.ctx, ent.OpQuerySelect)
	if err := _s.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*ProfitRateQuery, *ProfitRateSelect](ctx, _s.ProfitRateQuery, _s, _s.inters, v)
}

func (_s *ProfitRateSelect) sqlScan(ctx context.Context, root *ProfitRateQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(_s.fns))
	for _, fn := range _s.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*_s.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := _s.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (_s *ProfitRateSelect) Modify(modifiers ...func(s *sql.Selector)) *ProfitRateSelect {
	_s.modifiers = append(_s.modifiers, modifiers...)
	return _s
}
