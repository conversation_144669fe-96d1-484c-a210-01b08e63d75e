// Code generated by ent, DO NOT EDIT.

package depositproduct

import (
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/shopspring/decimal"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uint) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uint) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uint) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uint) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uint) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uint) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uint) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uint) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uint) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldLTE(FieldID, id))
}

// ProductCode applies equality check predicate on the "product_code" field. It's identical to ProductCodeEQ.
func ProductCode(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldEQ(FieldProductCode, v))
}

// ColvirProductCode applies equality check predicate on the "colvir_product_code" field. It's identical to ColvirProductCodeEQ.
func ColvirProductCode(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldEQ(FieldColvirProductCode, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldEQ(FieldName, v))
}

// MaxProfitRate applies equality check predicate on the "max_profit_rate" field. It's identical to MaxProfitRateEQ.
func MaxProfitRate(v decimal.Decimal) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldEQ(FieldMaxProfitRate, v))
}

// IsActive applies equality check predicate on the "is_active" field. It's identical to IsActiveEQ.
func IsActive(v bool) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldEQ(FieldIsActive, v))
}

// IsReplenishable applies equality check predicate on the "is_replenishable" field. It's identical to IsReplenishableEQ.
func IsReplenishable(v bool) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldEQ(FieldIsReplenishable, v))
}

// ReplenishableDays applies equality check predicate on the "replenishable_days" field. It's identical to ReplenishableDaysEQ.
func ReplenishableDays(v uint) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldEQ(FieldReplenishableDays, v))
}

// ProductCodeEQ applies the EQ predicate on the "product_code" field.
func ProductCodeEQ(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldEQ(FieldProductCode, v))
}

// ProductCodeNEQ applies the NEQ predicate on the "product_code" field.
func ProductCodeNEQ(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldNEQ(FieldProductCode, v))
}

// ProductCodeIn applies the In predicate on the "product_code" field.
func ProductCodeIn(vs ...string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldIn(FieldProductCode, vs...))
}

// ProductCodeNotIn applies the NotIn predicate on the "product_code" field.
func ProductCodeNotIn(vs ...string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldNotIn(FieldProductCode, vs...))
}

// ProductCodeGT applies the GT predicate on the "product_code" field.
func ProductCodeGT(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldGT(FieldProductCode, v))
}

// ProductCodeGTE applies the GTE predicate on the "product_code" field.
func ProductCodeGTE(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldGTE(FieldProductCode, v))
}

// ProductCodeLT applies the LT predicate on the "product_code" field.
func ProductCodeLT(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldLT(FieldProductCode, v))
}

// ProductCodeLTE applies the LTE predicate on the "product_code" field.
func ProductCodeLTE(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldLTE(FieldProductCode, v))
}

// ProductCodeContains applies the Contains predicate on the "product_code" field.
func ProductCodeContains(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldContains(FieldProductCode, v))
}

// ProductCodeHasPrefix applies the HasPrefix predicate on the "product_code" field.
func ProductCodeHasPrefix(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldHasPrefix(FieldProductCode, v))
}

// ProductCodeHasSuffix applies the HasSuffix predicate on the "product_code" field.
func ProductCodeHasSuffix(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldHasSuffix(FieldProductCode, v))
}

// ProductCodeEqualFold applies the EqualFold predicate on the "product_code" field.
func ProductCodeEqualFold(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldEqualFold(FieldProductCode, v))
}

// ProductCodeContainsFold applies the ContainsFold predicate on the "product_code" field.
func ProductCodeContainsFold(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldContainsFold(FieldProductCode, v))
}

// ColvirProductCodeEQ applies the EQ predicate on the "colvir_product_code" field.
func ColvirProductCodeEQ(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldEQ(FieldColvirProductCode, v))
}

// ColvirProductCodeNEQ applies the NEQ predicate on the "colvir_product_code" field.
func ColvirProductCodeNEQ(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldNEQ(FieldColvirProductCode, v))
}

// ColvirProductCodeIn applies the In predicate on the "colvir_product_code" field.
func ColvirProductCodeIn(vs ...string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldIn(FieldColvirProductCode, vs...))
}

// ColvirProductCodeNotIn applies the NotIn predicate on the "colvir_product_code" field.
func ColvirProductCodeNotIn(vs ...string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldNotIn(FieldColvirProductCode, vs...))
}

// ColvirProductCodeGT applies the GT predicate on the "colvir_product_code" field.
func ColvirProductCodeGT(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldGT(FieldColvirProductCode, v))
}

// ColvirProductCodeGTE applies the GTE predicate on the "colvir_product_code" field.
func ColvirProductCodeGTE(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldGTE(FieldColvirProductCode, v))
}

// ColvirProductCodeLT applies the LT predicate on the "colvir_product_code" field.
func ColvirProductCodeLT(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldLT(FieldColvirProductCode, v))
}

// ColvirProductCodeLTE applies the LTE predicate on the "colvir_product_code" field.
func ColvirProductCodeLTE(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldLTE(FieldColvirProductCode, v))
}

// ColvirProductCodeContains applies the Contains predicate on the "colvir_product_code" field.
func ColvirProductCodeContains(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldContains(FieldColvirProductCode, v))
}

// ColvirProductCodeHasPrefix applies the HasPrefix predicate on the "colvir_product_code" field.
func ColvirProductCodeHasPrefix(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldHasPrefix(FieldColvirProductCode, v))
}

// ColvirProductCodeHasSuffix applies the HasSuffix predicate on the "colvir_product_code" field.
func ColvirProductCodeHasSuffix(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldHasSuffix(FieldColvirProductCode, v))
}

// ColvirProductCodeEqualFold applies the EqualFold predicate on the "colvir_product_code" field.
func ColvirProductCodeEqualFold(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldEqualFold(FieldColvirProductCode, v))
}

// ColvirProductCodeContainsFold applies the ContainsFold predicate on the "colvir_product_code" field.
func ColvirProductCodeContainsFold(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldContainsFold(FieldColvirProductCode, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldContainsFold(FieldName, v))
}

// MaxProfitRateEQ applies the EQ predicate on the "max_profit_rate" field.
func MaxProfitRateEQ(v decimal.Decimal) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldEQ(FieldMaxProfitRate, v))
}

// MaxProfitRateNEQ applies the NEQ predicate on the "max_profit_rate" field.
func MaxProfitRateNEQ(v decimal.Decimal) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldNEQ(FieldMaxProfitRate, v))
}

// MaxProfitRateIn applies the In predicate on the "max_profit_rate" field.
func MaxProfitRateIn(vs ...decimal.Decimal) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldIn(FieldMaxProfitRate, vs...))
}

// MaxProfitRateNotIn applies the NotIn predicate on the "max_profit_rate" field.
func MaxProfitRateNotIn(vs ...decimal.Decimal) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldNotIn(FieldMaxProfitRate, vs...))
}

// MaxProfitRateGT applies the GT predicate on the "max_profit_rate" field.
func MaxProfitRateGT(v decimal.Decimal) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldGT(FieldMaxProfitRate, v))
}

// MaxProfitRateGTE applies the GTE predicate on the "max_profit_rate" field.
func MaxProfitRateGTE(v decimal.Decimal) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldGTE(FieldMaxProfitRate, v))
}

// MaxProfitRateLT applies the LT predicate on the "max_profit_rate" field.
func MaxProfitRateLT(v decimal.Decimal) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldLT(FieldMaxProfitRate, v))
}

// MaxProfitRateLTE applies the LTE predicate on the "max_profit_rate" field.
func MaxProfitRateLTE(v decimal.Decimal) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldLTE(FieldMaxProfitRate, v))
}

// MaxProfitRateContains applies the Contains predicate on the "max_profit_rate" field.
func MaxProfitRateContains(v decimal.Decimal) predicate.DepositProduct {
	vc := v.String()
	return predicate.DepositProduct(sql.FieldContains(FieldMaxProfitRate, vc))
}

// MaxProfitRateHasPrefix applies the HasPrefix predicate on the "max_profit_rate" field.
func MaxProfitRateHasPrefix(v decimal.Decimal) predicate.DepositProduct {
	vc := v.String()
	return predicate.DepositProduct(sql.FieldHasPrefix(FieldMaxProfitRate, vc))
}

// MaxProfitRateHasSuffix applies the HasSuffix predicate on the "max_profit_rate" field.
func MaxProfitRateHasSuffix(v decimal.Decimal) predicate.DepositProduct {
	vc := v.String()
	return predicate.DepositProduct(sql.FieldHasSuffix(FieldMaxProfitRate, vc))
}

// MaxProfitRateEqualFold applies the EqualFold predicate on the "max_profit_rate" field.
func MaxProfitRateEqualFold(v decimal.Decimal) predicate.DepositProduct {
	vc := v.String()
	return predicate.DepositProduct(sql.FieldEqualFold(FieldMaxProfitRate, vc))
}

// MaxProfitRateContainsFold applies the ContainsFold predicate on the "max_profit_rate" field.
func MaxProfitRateContainsFold(v decimal.Decimal) predicate.DepositProduct {
	vc := v.String()
	return predicate.DepositProduct(sql.FieldContainsFold(FieldMaxProfitRate, vc))
}

// IsActiveEQ applies the EQ predicate on the "is_active" field.
func IsActiveEQ(v bool) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldEQ(FieldIsActive, v))
}

// IsActiveNEQ applies the NEQ predicate on the "is_active" field.
func IsActiveNEQ(v bool) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldNEQ(FieldIsActive, v))
}

// IsReplenishableEQ applies the EQ predicate on the "is_replenishable" field.
func IsReplenishableEQ(v bool) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldEQ(FieldIsReplenishable, v))
}

// IsReplenishableNEQ applies the NEQ predicate on the "is_replenishable" field.
func IsReplenishableNEQ(v bool) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldNEQ(FieldIsReplenishable, v))
}

// ReplenishableDaysEQ applies the EQ predicate on the "replenishable_days" field.
func ReplenishableDaysEQ(v uint) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldEQ(FieldReplenishableDays, v))
}

// ReplenishableDaysNEQ applies the NEQ predicate on the "replenishable_days" field.
func ReplenishableDaysNEQ(v uint) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldNEQ(FieldReplenishableDays, v))
}

// ReplenishableDaysIn applies the In predicate on the "replenishable_days" field.
func ReplenishableDaysIn(vs ...uint) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldIn(FieldReplenishableDays, vs...))
}

// ReplenishableDaysNotIn applies the NotIn predicate on the "replenishable_days" field.
func ReplenishableDaysNotIn(vs ...uint) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldNotIn(FieldReplenishableDays, vs...))
}

// ReplenishableDaysGT applies the GT predicate on the "replenishable_days" field.
func ReplenishableDaysGT(v uint) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldGT(FieldReplenishableDays, v))
}

// ReplenishableDaysGTE applies the GTE predicate on the "replenishable_days" field.
func ReplenishableDaysGTE(v uint) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldGTE(FieldReplenishableDays, v))
}

// ReplenishableDaysLT applies the LT predicate on the "replenishable_days" field.
func ReplenishableDaysLT(v uint) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldLT(FieldReplenishableDays, v))
}

// ReplenishableDaysLTE applies the LTE predicate on the "replenishable_days" field.
func ReplenishableDaysLTE(v uint) predicate.DepositProduct {
	return predicate.DepositProduct(sql.FieldLTE(FieldReplenishableDays, v))
}

// HasCurrency applies the HasEdge predicate on the "currency" edge.
func HasCurrency() predicate.DepositProduct {
	return predicate.DepositProduct(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, CurrencyTable, CurrencyColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasCurrencyWith applies the HasEdge predicate on the "currency" edge with a given conditions (other predicates).
func HasCurrencyWith(preds ...predicate.Currency) predicate.DepositProduct {
	return predicate.DepositProduct(func(s *sql.Selector) {
		step := newCurrencyStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.DepositProduct) predicate.DepositProduct {
	return predicate.DepositProduct(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.DepositProduct) predicate.DepositProduct {
	return predicate.DepositProduct(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.DepositProduct) predicate.DepositProduct {
	return predicate.DepositProduct(sql.NotPredicates(p))
}
