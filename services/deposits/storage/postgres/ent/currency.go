// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/shopspring/decimal"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/currency"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositproduct"
)

// Currency is the model entity for the Currency schema.
type Currency struct {
	config `json:"-"`
	// ID of the ent.
	ID uint `json:"id,omitempty"`
	// ProductID holds the value of the "product_id" field.
	ProductID uint `json:"product_id,omitempty"`
	// Currency holds the value of the "currency" field.
	Currency currency.Currency `json:"currency,omitempty"`
	// IsActive holds the value of the "is_active" field.
	IsActive bool `json:"is_active,omitempty"`
	// MinAmount holds the value of the "min_amount" field.
	MinAmount decimal.Decimal `json:"min_amount,omitempty"`
	// MaxAmount holds the value of the "max_amount" field.
	MaxAmount decimal.Decimal `json:"max_amount,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the CurrencyQuery when eager-loading is set.
	Edges        CurrencyEdges `json:"edges"`
	selectValues sql.SelectValues
}

// CurrencyEdges holds the relations/edges for other nodes in the graph.
type CurrencyEdges struct {
	// ProfitRate holds the value of the profit_rate edge.
	ProfitRate []*ProfitRate `json:"profit_rate,omitempty"`
	// Product holds the value of the product edge.
	Product *DepositProduct `json:"product,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// ProfitRateOrErr returns the ProfitRate value or an error if the edge
// was not loaded in eager-loading.
func (e CurrencyEdges) ProfitRateOrErr() ([]*ProfitRate, error) {
	if e.loadedTypes[0] {
		return e.ProfitRate, nil
	}
	return nil, &NotLoadedError{edge: "profit_rate"}
}

// ProductOrErr returns the Product value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e CurrencyEdges) ProductOrErr() (*DepositProduct, error) {
	if e.Product != nil {
		return e.Product, nil
	} else if e.loadedTypes[1] {
		return nil, &NotFoundError{label: depositproduct.Label}
	}
	return nil, &NotLoadedError{edge: "product"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Currency) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case currency.FieldMinAmount, currency.FieldMaxAmount:
			values[i] = new(decimal.Decimal)
		case currency.FieldIsActive:
			values[i] = new(sql.NullBool)
		case currency.FieldID, currency.FieldProductID:
			values[i] = new(sql.NullInt64)
		case currency.FieldCurrency:
			values[i] = new(sql.NullString)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Currency fields.
func (_m *Currency) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case currency.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			_m.ID = uint(value.Int64)
		case currency.FieldProductID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field product_id", values[i])
			} else if value.Valid {
				_m.ProductID = uint(value.Int64)
			}
		case currency.FieldCurrency:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field currency", values[i])
			} else if value.Valid {
				_m.Currency = currency.Currency(value.String)
			}
		case currency.FieldIsActive:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_active", values[i])
			} else if value.Valid {
				_m.IsActive = value.Bool
			}
		case currency.FieldMinAmount:
			if value, ok := values[i].(*decimal.Decimal); !ok {
				return fmt.Errorf("unexpected type %T for field min_amount", values[i])
			} else if value != nil {
				_m.MinAmount = *value
			}
		case currency.FieldMaxAmount:
			if value, ok := values[i].(*decimal.Decimal); !ok {
				return fmt.Errorf("unexpected type %T for field max_amount", values[i])
			} else if value != nil {
				_m.MaxAmount = *value
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Currency.
// This includes values selected through modifiers, order, etc.
func (_m *Currency) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// QueryProfitRate queries the "profit_rate" edge of the Currency entity.
func (_m *Currency) QueryProfitRate() *ProfitRateQuery {
	return NewCurrencyClient(_m.config).QueryProfitRate(_m)
}

// QueryProduct queries the "product" edge of the Currency entity.
func (_m *Currency) QueryProduct() *DepositProductQuery {
	return NewCurrencyClient(_m.config).QueryProduct(_m)
}

// Update returns a builder for updating this Currency.
// Note that you need to call Currency.Unwrap() before calling this method if this Currency
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *Currency) Update() *CurrencyUpdateOne {
	return NewCurrencyClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the Currency entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *Currency) Unwrap() *Currency {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("ent: Currency is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *Currency) String() string {
	var builder strings.Builder
	builder.WriteString("Currency(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("product_id=")
	builder.WriteString(fmt.Sprintf("%v", _m.ProductID))
	builder.WriteString(", ")
	builder.WriteString("currency=")
	builder.WriteString(fmt.Sprintf("%v", _m.Currency))
	builder.WriteString(", ")
	builder.WriteString("is_active=")
	builder.WriteString(fmt.Sprintf("%v", _m.IsActive))
	builder.WriteString(", ")
	builder.WriteString("min_amount=")
	builder.WriteString(fmt.Sprintf("%v", _m.MinAmount))
	builder.WriteString(", ")
	builder.WriteString("max_amount=")
	builder.WriteString(fmt.Sprintf("%v", _m.MaxAmount))
	builder.WriteByte(')')
	return builder.String()
}

// Currencies is a parsable slice of Currency.
type Currencies []*Currency
