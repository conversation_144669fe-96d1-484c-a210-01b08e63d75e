// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositapplication"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositcards"
)

// DepositCards is the model entity for the DepositCards schema.
type DepositCards struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// CreateTime holds the value of the "create_time" field.
	CreateTime time.Time `json:"create_time,omitempty"`
	// UpdateTime holds the value of the "update_time" field.
	UpdateTime time.Time `json:"update_time,omitempty"`
	// CardID holds the value of the "card_id" field.
	CardID uuid.UUID `json:"card_id,omitempty"`
	// Account<PERSON>ban holds the value of the "account_iban" field.
	AccountIban string `json:"account_iban,omitempty"`
	// FinContractID holds the value of the "fin_contract_id" field.
	FinContractID string `json:"fin_contract_id,omitempty"`
	// Rrn holds the value of the "rrn" field.
	Rrn string `json:"rrn,omitempty"`
	// ExternalID holds the value of the "external_id" field.
	ExternalID string `json:"external_id,omitempty"`
	// ProcessingTransactionID holds the value of the "processing_transaction_id" field.
	ProcessingTransactionID int64 `json:"processing_transaction_id,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the DepositCardsQuery when eager-loading is set.
	Edges                     DepositCardsEdges `json:"edges"`
	deposit_cards_application *uuid.UUID
	selectValues              sql.SelectValues
}

// DepositCardsEdges holds the relations/edges for other nodes in the graph.
type DepositCardsEdges struct {
	// Application holds the value of the application edge.
	Application *DepositApplication `json:"application,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// ApplicationOrErr returns the Application value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e DepositCardsEdges) ApplicationOrErr() (*DepositApplication, error) {
	if e.Application != nil {
		return e.Application, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: depositapplication.Label}
	}
	return nil, &NotLoadedError{edge: "application"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*DepositCards) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case depositcards.FieldProcessingTransactionID:
			values[i] = new(sql.NullInt64)
		case depositcards.FieldAccountIban, depositcards.FieldFinContractID, depositcards.FieldRrn, depositcards.FieldExternalID:
			values[i] = new(sql.NullString)
		case depositcards.FieldCreateTime, depositcards.FieldUpdateTime:
			values[i] = new(sql.NullTime)
		case depositcards.FieldID, depositcards.FieldCardID:
			values[i] = new(uuid.UUID)
		case depositcards.ForeignKeys[0]: // deposit_cards_application
			values[i] = &sql.NullScanner{S: new(uuid.UUID)}
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the DepositCards fields.
func (_m *DepositCards) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case depositcards.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				_m.ID = *value
			}
		case depositcards.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				_m.CreateTime = value.Time
			}
		case depositcards.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				_m.UpdateTime = value.Time
			}
		case depositcards.FieldCardID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field card_id", values[i])
			} else if value != nil {
				_m.CardID = *value
			}
		case depositcards.FieldAccountIban:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field account_iban", values[i])
			} else if value.Valid {
				_m.AccountIban = value.String
			}
		case depositcards.FieldFinContractID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field fin_contract_id", values[i])
			} else if value.Valid {
				_m.FinContractID = value.String
			}
		case depositcards.FieldRrn:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field rrn", values[i])
			} else if value.Valid {
				_m.Rrn = value.String
			}
		case depositcards.FieldExternalID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field external_id", values[i])
			} else if value.Valid {
				_m.ExternalID = value.String
			}
		case depositcards.FieldProcessingTransactionID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field processing_transaction_id", values[i])
			} else if value.Valid {
				_m.ProcessingTransactionID = value.Int64
			}
		case depositcards.ForeignKeys[0]:
			if value, ok := values[i].(*sql.NullScanner); !ok {
				return fmt.Errorf("unexpected type %T for field deposit_cards_application", values[i])
			} else if value.Valid {
				_m.deposit_cards_application = new(uuid.UUID)
				*_m.deposit_cards_application = *value.S.(*uuid.UUID)
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the DepositCards.
// This includes values selected through modifiers, order, etc.
func (_m *DepositCards) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// QueryApplication queries the "application" edge of the DepositCards entity.
func (_m *DepositCards) QueryApplication() *DepositApplicationQuery {
	return NewDepositCardsClient(_m.config).QueryApplication(_m)
}

// Update returns a builder for updating this DepositCards.
// Note that you need to call DepositCards.Unwrap() before calling this method if this DepositCards
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *DepositCards) Update() *DepositCardsUpdateOne {
	return NewDepositCardsClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the DepositCards entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *DepositCards) Unwrap() *DepositCards {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("ent: DepositCards is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *DepositCards) String() string {
	var builder strings.Builder
	builder.WriteString("DepositCards(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("create_time=")
	builder.WriteString(_m.CreateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_time=")
	builder.WriteString(_m.UpdateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("card_id=")
	builder.WriteString(fmt.Sprintf("%v", _m.CardID))
	builder.WriteString(", ")
	builder.WriteString("account_iban=")
	builder.WriteString(_m.AccountIban)
	builder.WriteString(", ")
	builder.WriteString("fin_contract_id=")
	builder.WriteString(_m.FinContractID)
	builder.WriteString(", ")
	builder.WriteString("rrn=")
	builder.WriteString(_m.Rrn)
	builder.WriteString(", ")
	builder.WriteString("external_id=")
	builder.WriteString(_m.ExternalID)
	builder.WriteString(", ")
	builder.WriteString("processing_transaction_id=")
	builder.WriteString(fmt.Sprintf("%v", _m.ProcessingTransactionID))
	builder.WriteByte(')')
	return builder.String()
}

// DepositCardsSlice is a parsable slice of DepositCards.
type DepositCardsSlice []*DepositCards
