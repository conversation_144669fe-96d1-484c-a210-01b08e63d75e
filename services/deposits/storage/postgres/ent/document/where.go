// Code generated by ent, DO NOT EDIT.

package document

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldLTE(FieldID, id))
}

// CreateTime applies equality check predicate on the "create_time" field. It's identical to CreateTimeEQ.
func CreateTime(v time.Time) predicate.Document {
	return predicate.Document(sql.FieldEQ(FieldCreateTime, v))
}

// UpdateTime applies equality check predicate on the "update_time" field. It's identical to UpdateTimeEQ.
func UpdateTime(v time.Time) predicate.Document {
	return predicate.Document(sql.FieldEQ(FieldUpdateTime, v))
}

// DepositApplicationID applies equality check predicate on the "deposit_application_id" field. It's identical to DepositApplicationIDEQ.
func DepositApplicationID(v uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldEQ(FieldDepositApplicationID, v))
}

// DocID applies equality check predicate on the "doc_id" field. It's identical to DocIDEQ.
func DocID(v uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldEQ(FieldDocID, v))
}

// Number applies equality check predicate on the "number" field. It's identical to NumberEQ.
func Number(v string) predicate.Document {
	return predicate.Document(sql.FieldEQ(FieldNumber, v))
}

// SignedDocID applies equality check predicate on the "signed_doc_id" field. It's identical to SignedDocIDEQ.
func SignedDocID(v uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldEQ(FieldSignedDocID, v))
}

// DocumentSigningDate applies equality check predicate on the "document_signing_date" field. It's identical to DocumentSigningDateEQ.
func DocumentSigningDate(v time.Time) predicate.Document {
	return predicate.Document(sql.FieldEQ(FieldDocumentSigningDate, v))
}

// IsSignable applies equality check predicate on the "is_signable" field. It's identical to IsSignableEQ.
func IsSignable(v bool) predicate.Document {
	return predicate.Document(sql.FieldEQ(FieldIsSignable, v))
}

// CreateTimeEQ applies the EQ predicate on the "create_time" field.
func CreateTimeEQ(v time.Time) predicate.Document {
	return predicate.Document(sql.FieldEQ(FieldCreateTime, v))
}

// CreateTimeNEQ applies the NEQ predicate on the "create_time" field.
func CreateTimeNEQ(v time.Time) predicate.Document {
	return predicate.Document(sql.FieldNEQ(FieldCreateTime, v))
}

// CreateTimeIn applies the In predicate on the "create_time" field.
func CreateTimeIn(vs ...time.Time) predicate.Document {
	return predicate.Document(sql.FieldIn(FieldCreateTime, vs...))
}

// CreateTimeNotIn applies the NotIn predicate on the "create_time" field.
func CreateTimeNotIn(vs ...time.Time) predicate.Document {
	return predicate.Document(sql.FieldNotIn(FieldCreateTime, vs...))
}

// CreateTimeGT applies the GT predicate on the "create_time" field.
func CreateTimeGT(v time.Time) predicate.Document {
	return predicate.Document(sql.FieldGT(FieldCreateTime, v))
}

// CreateTimeGTE applies the GTE predicate on the "create_time" field.
func CreateTimeGTE(v time.Time) predicate.Document {
	return predicate.Document(sql.FieldGTE(FieldCreateTime, v))
}

// CreateTimeLT applies the LT predicate on the "create_time" field.
func CreateTimeLT(v time.Time) predicate.Document {
	return predicate.Document(sql.FieldLT(FieldCreateTime, v))
}

// CreateTimeLTE applies the LTE predicate on the "create_time" field.
func CreateTimeLTE(v time.Time) predicate.Document {
	return predicate.Document(sql.FieldLTE(FieldCreateTime, v))
}

// CreateTimeIsNil applies the IsNil predicate on the "create_time" field.
func CreateTimeIsNil() predicate.Document {
	return predicate.Document(sql.FieldIsNull(FieldCreateTime))
}

// CreateTimeNotNil applies the NotNil predicate on the "create_time" field.
func CreateTimeNotNil() predicate.Document {
	return predicate.Document(sql.FieldNotNull(FieldCreateTime))
}

// UpdateTimeEQ applies the EQ predicate on the "update_time" field.
func UpdateTimeEQ(v time.Time) predicate.Document {
	return predicate.Document(sql.FieldEQ(FieldUpdateTime, v))
}

// UpdateTimeNEQ applies the NEQ predicate on the "update_time" field.
func UpdateTimeNEQ(v time.Time) predicate.Document {
	return predicate.Document(sql.FieldNEQ(FieldUpdateTime, v))
}

// UpdateTimeIn applies the In predicate on the "update_time" field.
func UpdateTimeIn(vs ...time.Time) predicate.Document {
	return predicate.Document(sql.FieldIn(FieldUpdateTime, vs...))
}

// UpdateTimeNotIn applies the NotIn predicate on the "update_time" field.
func UpdateTimeNotIn(vs ...time.Time) predicate.Document {
	return predicate.Document(sql.FieldNotIn(FieldUpdateTime, vs...))
}

// UpdateTimeGT applies the GT predicate on the "update_time" field.
func UpdateTimeGT(v time.Time) predicate.Document {
	return predicate.Document(sql.FieldGT(FieldUpdateTime, v))
}

// UpdateTimeGTE applies the GTE predicate on the "update_time" field.
func UpdateTimeGTE(v time.Time) predicate.Document {
	return predicate.Document(sql.FieldGTE(FieldUpdateTime, v))
}

// UpdateTimeLT applies the LT predicate on the "update_time" field.
func UpdateTimeLT(v time.Time) predicate.Document {
	return predicate.Document(sql.FieldLT(FieldUpdateTime, v))
}

// UpdateTimeLTE applies the LTE predicate on the "update_time" field.
func UpdateTimeLTE(v time.Time) predicate.Document {
	return predicate.Document(sql.FieldLTE(FieldUpdateTime, v))
}

// UpdateTimeIsNil applies the IsNil predicate on the "update_time" field.
func UpdateTimeIsNil() predicate.Document {
	return predicate.Document(sql.FieldIsNull(FieldUpdateTime))
}

// UpdateTimeNotNil applies the NotNil predicate on the "update_time" field.
func UpdateTimeNotNil() predicate.Document {
	return predicate.Document(sql.FieldNotNull(FieldUpdateTime))
}

// DepositApplicationIDEQ applies the EQ predicate on the "deposit_application_id" field.
func DepositApplicationIDEQ(v uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldEQ(FieldDepositApplicationID, v))
}

// DepositApplicationIDNEQ applies the NEQ predicate on the "deposit_application_id" field.
func DepositApplicationIDNEQ(v uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldNEQ(FieldDepositApplicationID, v))
}

// DepositApplicationIDIn applies the In predicate on the "deposit_application_id" field.
func DepositApplicationIDIn(vs ...uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldIn(FieldDepositApplicationID, vs...))
}

// DepositApplicationIDNotIn applies the NotIn predicate on the "deposit_application_id" field.
func DepositApplicationIDNotIn(vs ...uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldNotIn(FieldDepositApplicationID, vs...))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v Type) predicate.Document {
	return predicate.Document(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v Type) predicate.Document {
	return predicate.Document(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...Type) predicate.Document {
	return predicate.Document(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...Type) predicate.Document {
	return predicate.Document(sql.FieldNotIn(FieldType, vs...))
}

// DocIDEQ applies the EQ predicate on the "doc_id" field.
func DocIDEQ(v uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldEQ(FieldDocID, v))
}

// DocIDNEQ applies the NEQ predicate on the "doc_id" field.
func DocIDNEQ(v uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldNEQ(FieldDocID, v))
}

// DocIDIn applies the In predicate on the "doc_id" field.
func DocIDIn(vs ...uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldIn(FieldDocID, vs...))
}

// DocIDNotIn applies the NotIn predicate on the "doc_id" field.
func DocIDNotIn(vs ...uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldNotIn(FieldDocID, vs...))
}

// DocIDGT applies the GT predicate on the "doc_id" field.
func DocIDGT(v uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldGT(FieldDocID, v))
}

// DocIDGTE applies the GTE predicate on the "doc_id" field.
func DocIDGTE(v uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldGTE(FieldDocID, v))
}

// DocIDLT applies the LT predicate on the "doc_id" field.
func DocIDLT(v uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldLT(FieldDocID, v))
}

// DocIDLTE applies the LTE predicate on the "doc_id" field.
func DocIDLTE(v uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldLTE(FieldDocID, v))
}

// DocIDIsNil applies the IsNil predicate on the "doc_id" field.
func DocIDIsNil() predicate.Document {
	return predicate.Document(sql.FieldIsNull(FieldDocID))
}

// DocIDNotNil applies the NotNil predicate on the "doc_id" field.
func DocIDNotNil() predicate.Document {
	return predicate.Document(sql.FieldNotNull(FieldDocID))
}

// NumberEQ applies the EQ predicate on the "number" field.
func NumberEQ(v string) predicate.Document {
	return predicate.Document(sql.FieldEQ(FieldNumber, v))
}

// NumberNEQ applies the NEQ predicate on the "number" field.
func NumberNEQ(v string) predicate.Document {
	return predicate.Document(sql.FieldNEQ(FieldNumber, v))
}

// NumberIn applies the In predicate on the "number" field.
func NumberIn(vs ...string) predicate.Document {
	return predicate.Document(sql.FieldIn(FieldNumber, vs...))
}

// NumberNotIn applies the NotIn predicate on the "number" field.
func NumberNotIn(vs ...string) predicate.Document {
	return predicate.Document(sql.FieldNotIn(FieldNumber, vs...))
}

// NumberGT applies the GT predicate on the "number" field.
func NumberGT(v string) predicate.Document {
	return predicate.Document(sql.FieldGT(FieldNumber, v))
}

// NumberGTE applies the GTE predicate on the "number" field.
func NumberGTE(v string) predicate.Document {
	return predicate.Document(sql.FieldGTE(FieldNumber, v))
}

// NumberLT applies the LT predicate on the "number" field.
func NumberLT(v string) predicate.Document {
	return predicate.Document(sql.FieldLT(FieldNumber, v))
}

// NumberLTE applies the LTE predicate on the "number" field.
func NumberLTE(v string) predicate.Document {
	return predicate.Document(sql.FieldLTE(FieldNumber, v))
}

// NumberContains applies the Contains predicate on the "number" field.
func NumberContains(v string) predicate.Document {
	return predicate.Document(sql.FieldContains(FieldNumber, v))
}

// NumberHasPrefix applies the HasPrefix predicate on the "number" field.
func NumberHasPrefix(v string) predicate.Document {
	return predicate.Document(sql.FieldHasPrefix(FieldNumber, v))
}

// NumberHasSuffix applies the HasSuffix predicate on the "number" field.
func NumberHasSuffix(v string) predicate.Document {
	return predicate.Document(sql.FieldHasSuffix(FieldNumber, v))
}

// NumberIsNil applies the IsNil predicate on the "number" field.
func NumberIsNil() predicate.Document {
	return predicate.Document(sql.FieldIsNull(FieldNumber))
}

// NumberNotNil applies the NotNil predicate on the "number" field.
func NumberNotNil() predicate.Document {
	return predicate.Document(sql.FieldNotNull(FieldNumber))
}

// NumberEqualFold applies the EqualFold predicate on the "number" field.
func NumberEqualFold(v string) predicate.Document {
	return predicate.Document(sql.FieldEqualFold(FieldNumber, v))
}

// NumberContainsFold applies the ContainsFold predicate on the "number" field.
func NumberContainsFold(v string) predicate.Document {
	return predicate.Document(sql.FieldContainsFold(FieldNumber, v))
}

// SignedDocIDEQ applies the EQ predicate on the "signed_doc_id" field.
func SignedDocIDEQ(v uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldEQ(FieldSignedDocID, v))
}

// SignedDocIDNEQ applies the NEQ predicate on the "signed_doc_id" field.
func SignedDocIDNEQ(v uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldNEQ(FieldSignedDocID, v))
}

// SignedDocIDIn applies the In predicate on the "signed_doc_id" field.
func SignedDocIDIn(vs ...uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldIn(FieldSignedDocID, vs...))
}

// SignedDocIDNotIn applies the NotIn predicate on the "signed_doc_id" field.
func SignedDocIDNotIn(vs ...uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldNotIn(FieldSignedDocID, vs...))
}

// SignedDocIDGT applies the GT predicate on the "signed_doc_id" field.
func SignedDocIDGT(v uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldGT(FieldSignedDocID, v))
}

// SignedDocIDGTE applies the GTE predicate on the "signed_doc_id" field.
func SignedDocIDGTE(v uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldGTE(FieldSignedDocID, v))
}

// SignedDocIDLT applies the LT predicate on the "signed_doc_id" field.
func SignedDocIDLT(v uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldLT(FieldSignedDocID, v))
}

// SignedDocIDLTE applies the LTE predicate on the "signed_doc_id" field.
func SignedDocIDLTE(v uuid.UUID) predicate.Document {
	return predicate.Document(sql.FieldLTE(FieldSignedDocID, v))
}

// SignedDocIDIsNil applies the IsNil predicate on the "signed_doc_id" field.
func SignedDocIDIsNil() predicate.Document {
	return predicate.Document(sql.FieldIsNull(FieldSignedDocID))
}

// SignedDocIDNotNil applies the NotNil predicate on the "signed_doc_id" field.
func SignedDocIDNotNil() predicate.Document {
	return predicate.Document(sql.FieldNotNull(FieldSignedDocID))
}

// DocumentSigningDateEQ applies the EQ predicate on the "document_signing_date" field.
func DocumentSigningDateEQ(v time.Time) predicate.Document {
	return predicate.Document(sql.FieldEQ(FieldDocumentSigningDate, v))
}

// DocumentSigningDateNEQ applies the NEQ predicate on the "document_signing_date" field.
func DocumentSigningDateNEQ(v time.Time) predicate.Document {
	return predicate.Document(sql.FieldNEQ(FieldDocumentSigningDate, v))
}

// DocumentSigningDateIn applies the In predicate on the "document_signing_date" field.
func DocumentSigningDateIn(vs ...time.Time) predicate.Document {
	return predicate.Document(sql.FieldIn(FieldDocumentSigningDate, vs...))
}

// DocumentSigningDateNotIn applies the NotIn predicate on the "document_signing_date" field.
func DocumentSigningDateNotIn(vs ...time.Time) predicate.Document {
	return predicate.Document(sql.FieldNotIn(FieldDocumentSigningDate, vs...))
}

// DocumentSigningDateGT applies the GT predicate on the "document_signing_date" field.
func DocumentSigningDateGT(v time.Time) predicate.Document {
	return predicate.Document(sql.FieldGT(FieldDocumentSigningDate, v))
}

// DocumentSigningDateGTE applies the GTE predicate on the "document_signing_date" field.
func DocumentSigningDateGTE(v time.Time) predicate.Document {
	return predicate.Document(sql.FieldGTE(FieldDocumentSigningDate, v))
}

// DocumentSigningDateLT applies the LT predicate on the "document_signing_date" field.
func DocumentSigningDateLT(v time.Time) predicate.Document {
	return predicate.Document(sql.FieldLT(FieldDocumentSigningDate, v))
}

// DocumentSigningDateLTE applies the LTE predicate on the "document_signing_date" field.
func DocumentSigningDateLTE(v time.Time) predicate.Document {
	return predicate.Document(sql.FieldLTE(FieldDocumentSigningDate, v))
}

// DocumentSigningDateIsNil applies the IsNil predicate on the "document_signing_date" field.
func DocumentSigningDateIsNil() predicate.Document {
	return predicate.Document(sql.FieldIsNull(FieldDocumentSigningDate))
}

// DocumentSigningDateNotNil applies the NotNil predicate on the "document_signing_date" field.
func DocumentSigningDateNotNil() predicate.Document {
	return predicate.Document(sql.FieldNotNull(FieldDocumentSigningDate))
}

// IsSignableEQ applies the EQ predicate on the "is_signable" field.
func IsSignableEQ(v bool) predicate.Document {
	return predicate.Document(sql.FieldEQ(FieldIsSignable, v))
}

// IsSignableNEQ applies the NEQ predicate on the "is_signable" field.
func IsSignableNEQ(v bool) predicate.Document {
	return predicate.Document(sql.FieldNEQ(FieldIsSignable, v))
}

// IsSignableIsNil applies the IsNil predicate on the "is_signable" field.
func IsSignableIsNil() predicate.Document {
	return predicate.Document(sql.FieldIsNull(FieldIsSignable))
}

// IsSignableNotNil applies the NotNil predicate on the "is_signable" field.
func IsSignableNotNil() predicate.Document {
	return predicate.Document(sql.FieldNotNull(FieldIsSignable))
}

// HasApplication applies the HasEdge predicate on the "application" edge.
func HasApplication() predicate.Document {
	return predicate.Document(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, ApplicationTable, ApplicationColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasApplicationWith applies the HasEdge predicate on the "application" edge with a given conditions (other predicates).
func HasApplicationWith(preds ...predicate.DepositApplication) predicate.Document {
	return predicate.Document(func(s *sql.Selector) {
		step := newApplicationStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Document) predicate.Document {
	return predicate.Document(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Document) predicate.Document {
	return predicate.Document(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Document) predicate.Document {
	return predicate.Document(sql.NotPredicates(p))
}
