// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositapplication"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/document"
)

// DocumentCreate is the builder for creating a Document entity.
type DocumentCreate struct {
	config
	mutation *DocumentMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreateTime sets the "create_time" field.
func (_c *DocumentCreate) SetCreateTime(v time.Time) *DocumentCreate {
	_c.mutation.SetCreateTime(v)
	return _c
}

// SetNillableCreateTime sets the "create_time" field if the given value is not nil.
func (_c *DocumentCreate) SetNillableCreateTime(v *time.Time) *DocumentCreate {
	if v != nil {
		_c.SetCreateTime(*v)
	}
	return _c
}

// SetUpdateTime sets the "update_time" field.
func (_c *DocumentCreate) SetUpdateTime(v time.Time) *DocumentCreate {
	_c.mutation.SetUpdateTime(v)
	return _c
}

// SetNillableUpdateTime sets the "update_time" field if the given value is not nil.
func (_c *DocumentCreate) SetNillableUpdateTime(v *time.Time) *DocumentCreate {
	if v != nil {
		_c.SetUpdateTime(*v)
	}
	return _c
}

// SetDepositApplicationID sets the "deposit_application_id" field.
func (_c *DocumentCreate) SetDepositApplicationID(v uuid.UUID) *DocumentCreate {
	_c.mutation.SetDepositApplicationID(v)
	return _c
}

// SetType sets the "type" field.
func (_c *DocumentCreate) SetType(v document.Type) *DocumentCreate {
	_c.mutation.SetType(v)
	return _c
}

// SetDocID sets the "doc_id" field.
func (_c *DocumentCreate) SetDocID(v uuid.UUID) *DocumentCreate {
	_c.mutation.SetDocID(v)
	return _c
}

// SetNillableDocID sets the "doc_id" field if the given value is not nil.
func (_c *DocumentCreate) SetNillableDocID(v *uuid.UUID) *DocumentCreate {
	if v != nil {
		_c.SetDocID(*v)
	}
	return _c
}

// SetNumber sets the "number" field.
func (_c *DocumentCreate) SetNumber(v string) *DocumentCreate {
	_c.mutation.SetNumber(v)
	return _c
}

// SetNillableNumber sets the "number" field if the given value is not nil.
func (_c *DocumentCreate) SetNillableNumber(v *string) *DocumentCreate {
	if v != nil {
		_c.SetNumber(*v)
	}
	return _c
}

// SetSignedDocID sets the "signed_doc_id" field.
func (_c *DocumentCreate) SetSignedDocID(v uuid.UUID) *DocumentCreate {
	_c.mutation.SetSignedDocID(v)
	return _c
}

// SetNillableSignedDocID sets the "signed_doc_id" field if the given value is not nil.
func (_c *DocumentCreate) SetNillableSignedDocID(v *uuid.UUID) *DocumentCreate {
	if v != nil {
		_c.SetSignedDocID(*v)
	}
	return _c
}

// SetDocumentSigningDate sets the "document_signing_date" field.
func (_c *DocumentCreate) SetDocumentSigningDate(v time.Time) *DocumentCreate {
	_c.mutation.SetDocumentSigningDate(v)
	return _c
}

// SetNillableDocumentSigningDate sets the "document_signing_date" field if the given value is not nil.
func (_c *DocumentCreate) SetNillableDocumentSigningDate(v *time.Time) *DocumentCreate {
	if v != nil {
		_c.SetDocumentSigningDate(*v)
	}
	return _c
}

// SetIsSignable sets the "is_signable" field.
func (_c *DocumentCreate) SetIsSignable(v bool) *DocumentCreate {
	_c.mutation.SetIsSignable(v)
	return _c
}

// SetNillableIsSignable sets the "is_signable" field if the given value is not nil.
func (_c *DocumentCreate) SetNillableIsSignable(v *bool) *DocumentCreate {
	if v != nil {
		_c.SetIsSignable(*v)
	}
	return _c
}

// SetID sets the "id" field.
func (_c *DocumentCreate) SetID(v uuid.UUID) *DocumentCreate {
	_c.mutation.SetID(v)
	return _c
}

// SetNillableID sets the "id" field if the given value is not nil.
func (_c *DocumentCreate) SetNillableID(v *uuid.UUID) *DocumentCreate {
	if v != nil {
		_c.SetID(*v)
	}
	return _c
}

// SetApplicationID sets the "application" edge to the DepositApplication entity by ID.
func (_c *DocumentCreate) SetApplicationID(id uuid.UUID) *DocumentCreate {
	_c.mutation.SetApplicationID(id)
	return _c
}

// SetApplication sets the "application" edge to the DepositApplication entity.
func (_c *DocumentCreate) SetApplication(v *DepositApplication) *DocumentCreate {
	return _c.SetApplicationID(v.ID)
}

// Mutation returns the DocumentMutation object of the builder.
func (_c *DocumentCreate) Mutation() *DocumentMutation {
	return _c.mutation
}

// Save creates the Document in the database.
func (_c *DocumentCreate) Save(ctx context.Context) (*Document, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *DocumentCreate) SaveX(ctx context.Context) *Document {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *DocumentCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *DocumentCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *DocumentCreate) defaults() {
	if _, ok := _c.mutation.CreateTime(); !ok {
		v := document.DefaultCreateTime()
		_c.mutation.SetCreateTime(v)
	}
	if _, ok := _c.mutation.UpdateTime(); !ok {
		v := document.DefaultUpdateTime()
		_c.mutation.SetUpdateTime(v)
	}
	if _, ok := _c.mutation.IsSignable(); !ok {
		v := document.DefaultIsSignable
		_c.mutation.SetIsSignable(v)
	}
	if _, ok := _c.mutation.ID(); !ok {
		v := document.DefaultID()
		_c.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *DocumentCreate) check() error {
	if _, ok := _c.mutation.DepositApplicationID(); !ok {
		return &ValidationError{Name: "deposit_application_id", err: errors.New(`ent: missing required field "Document.deposit_application_id"`)}
	}
	if _, ok := _c.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "Document.type"`)}
	}
	if v, ok := _c.mutation.GetType(); ok {
		if err := document.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "Document.type": %w`, err)}
		}
	}
	if len(_c.mutation.ApplicationIDs()) == 0 {
		return &ValidationError{Name: "application", err: errors.New(`ent: missing required edge "Document.application"`)}
	}
	return nil
}

func (_c *DocumentCreate) sqlSave(ctx context.Context) (*Document, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *DocumentCreate) createSpec() (*Document, *sqlgraph.CreateSpec) {
	var (
		_node = &Document{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(document.Table, sqlgraph.NewFieldSpec(document.FieldID, field.TypeUUID))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := _c.mutation.CreateTime(); ok {
		_spec.SetField(document.FieldCreateTime, field.TypeTime, value)
		_node.CreateTime = value
	}
	if value, ok := _c.mutation.UpdateTime(); ok {
		_spec.SetField(document.FieldUpdateTime, field.TypeTime, value)
		_node.UpdateTime = value
	}
	if value, ok := _c.mutation.GetType(); ok {
		_spec.SetField(document.FieldType, field.TypeEnum, value)
		_node.Type = value
	}
	if value, ok := _c.mutation.DocID(); ok {
		_spec.SetField(document.FieldDocID, field.TypeUUID, value)
		_node.DocID = value
	}
	if value, ok := _c.mutation.Number(); ok {
		_spec.SetField(document.FieldNumber, field.TypeString, value)
		_node.Number = &value
	}
	if value, ok := _c.mutation.SignedDocID(); ok {
		_spec.SetField(document.FieldSignedDocID, field.TypeUUID, value)
		_node.SignedDocID = &value
	}
	if value, ok := _c.mutation.DocumentSigningDate(); ok {
		_spec.SetField(document.FieldDocumentSigningDate, field.TypeTime, value)
		_node.DocumentSigningDate = &value
	}
	if value, ok := _c.mutation.IsSignable(); ok {
		_spec.SetField(document.FieldIsSignable, field.TypeBool, value)
		_node.IsSignable = value
	}
	if nodes := _c.mutation.ApplicationIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   document.ApplicationTable,
			Columns: []string{document.ApplicationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(depositapplication.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.DepositApplicationID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Document.Create().
//		SetCreateTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.DocumentUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *DocumentCreate) OnConflict(opts ...sql.ConflictOption) *DocumentUpsertOne {
	_c.conflict = opts
	return &DocumentUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Document.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *DocumentCreate) OnConflictColumns(columns ...string) *DocumentUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &DocumentUpsertOne{
		create: _c,
	}
}

type (
	// DocumentUpsertOne is the builder for "upsert"-ing
	//  one Document node.
	DocumentUpsertOne struct {
		create *DocumentCreate
	}

	// DocumentUpsert is the "OnConflict" setter.
	DocumentUpsert struct {
		*sql.UpdateSet
	}
)

// SetDepositApplicationID sets the "deposit_application_id" field.
func (u *DocumentUpsert) SetDepositApplicationID(v uuid.UUID) *DocumentUpsert {
	u.Set(document.FieldDepositApplicationID, v)
	return u
}

// UpdateDepositApplicationID sets the "deposit_application_id" field to the value that was provided on create.
func (u *DocumentUpsert) UpdateDepositApplicationID() *DocumentUpsert {
	u.SetExcluded(document.FieldDepositApplicationID)
	return u
}

// SetType sets the "type" field.
func (u *DocumentUpsert) SetType(v document.Type) *DocumentUpsert {
	u.Set(document.FieldType, v)
	return u
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *DocumentUpsert) UpdateType() *DocumentUpsert {
	u.SetExcluded(document.FieldType)
	return u
}

// SetDocID sets the "doc_id" field.
func (u *DocumentUpsert) SetDocID(v uuid.UUID) *DocumentUpsert {
	u.Set(document.FieldDocID, v)
	return u
}

// UpdateDocID sets the "doc_id" field to the value that was provided on create.
func (u *DocumentUpsert) UpdateDocID() *DocumentUpsert {
	u.SetExcluded(document.FieldDocID)
	return u
}

// ClearDocID clears the value of the "doc_id" field.
func (u *DocumentUpsert) ClearDocID() *DocumentUpsert {
	u.SetNull(document.FieldDocID)
	return u
}

// SetNumber sets the "number" field.
func (u *DocumentUpsert) SetNumber(v string) *DocumentUpsert {
	u.Set(document.FieldNumber, v)
	return u
}

// UpdateNumber sets the "number" field to the value that was provided on create.
func (u *DocumentUpsert) UpdateNumber() *DocumentUpsert {
	u.SetExcluded(document.FieldNumber)
	return u
}

// ClearNumber clears the value of the "number" field.
func (u *DocumentUpsert) ClearNumber() *DocumentUpsert {
	u.SetNull(document.FieldNumber)
	return u
}

// SetSignedDocID sets the "signed_doc_id" field.
func (u *DocumentUpsert) SetSignedDocID(v uuid.UUID) *DocumentUpsert {
	u.Set(document.FieldSignedDocID, v)
	return u
}

// UpdateSignedDocID sets the "signed_doc_id" field to the value that was provided on create.
func (u *DocumentUpsert) UpdateSignedDocID() *DocumentUpsert {
	u.SetExcluded(document.FieldSignedDocID)
	return u
}

// ClearSignedDocID clears the value of the "signed_doc_id" field.
func (u *DocumentUpsert) ClearSignedDocID() *DocumentUpsert {
	u.SetNull(document.FieldSignedDocID)
	return u
}

// SetDocumentSigningDate sets the "document_signing_date" field.
func (u *DocumentUpsert) SetDocumentSigningDate(v time.Time) *DocumentUpsert {
	u.Set(document.FieldDocumentSigningDate, v)
	return u
}

// UpdateDocumentSigningDate sets the "document_signing_date" field to the value that was provided on create.
func (u *DocumentUpsert) UpdateDocumentSigningDate() *DocumentUpsert {
	u.SetExcluded(document.FieldDocumentSigningDate)
	return u
}

// ClearDocumentSigningDate clears the value of the "document_signing_date" field.
func (u *DocumentUpsert) ClearDocumentSigningDate() *DocumentUpsert {
	u.SetNull(document.FieldDocumentSigningDate)
	return u
}

// SetIsSignable sets the "is_signable" field.
func (u *DocumentUpsert) SetIsSignable(v bool) *DocumentUpsert {
	u.Set(document.FieldIsSignable, v)
	return u
}

// UpdateIsSignable sets the "is_signable" field to the value that was provided on create.
func (u *DocumentUpsert) UpdateIsSignable() *DocumentUpsert {
	u.SetExcluded(document.FieldIsSignable)
	return u
}

// ClearIsSignable clears the value of the "is_signable" field.
func (u *DocumentUpsert) ClearIsSignable() *DocumentUpsert {
	u.SetNull(document.FieldIsSignable)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Document.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(document.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *DocumentUpsertOne) UpdateNewValues() *DocumentUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(document.FieldID)
		}
		if _, exists := u.create.mutation.CreateTime(); exists {
			s.SetIgnore(document.FieldCreateTime)
		}
		if _, exists := u.create.mutation.UpdateTime(); exists {
			s.SetIgnore(document.FieldUpdateTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Document.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *DocumentUpsertOne) Ignore() *DocumentUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *DocumentUpsertOne) DoNothing() *DocumentUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the DocumentCreate.OnConflict
// documentation for more info.
func (u *DocumentUpsertOne) Update(set func(*DocumentUpsert)) *DocumentUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&DocumentUpsert{UpdateSet: update})
	}))
	return u
}

// SetDepositApplicationID sets the "deposit_application_id" field.
func (u *DocumentUpsertOne) SetDepositApplicationID(v uuid.UUID) *DocumentUpsertOne {
	return u.Update(func(s *DocumentUpsert) {
		s.SetDepositApplicationID(v)
	})
}

// UpdateDepositApplicationID sets the "deposit_application_id" field to the value that was provided on create.
func (u *DocumentUpsertOne) UpdateDepositApplicationID() *DocumentUpsertOne {
	return u.Update(func(s *DocumentUpsert) {
		s.UpdateDepositApplicationID()
	})
}

// SetType sets the "type" field.
func (u *DocumentUpsertOne) SetType(v document.Type) *DocumentUpsertOne {
	return u.Update(func(s *DocumentUpsert) {
		s.SetType(v)
	})
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *DocumentUpsertOne) UpdateType() *DocumentUpsertOne {
	return u.Update(func(s *DocumentUpsert) {
		s.UpdateType()
	})
}

// SetDocID sets the "doc_id" field.
func (u *DocumentUpsertOne) SetDocID(v uuid.UUID) *DocumentUpsertOne {
	return u.Update(func(s *DocumentUpsert) {
		s.SetDocID(v)
	})
}

// UpdateDocID sets the "doc_id" field to the value that was provided on create.
func (u *DocumentUpsertOne) UpdateDocID() *DocumentUpsertOne {
	return u.Update(func(s *DocumentUpsert) {
		s.UpdateDocID()
	})
}

// ClearDocID clears the value of the "doc_id" field.
func (u *DocumentUpsertOne) ClearDocID() *DocumentUpsertOne {
	return u.Update(func(s *DocumentUpsert) {
		s.ClearDocID()
	})
}

// SetNumber sets the "number" field.
func (u *DocumentUpsertOne) SetNumber(v string) *DocumentUpsertOne {
	return u.Update(func(s *DocumentUpsert) {
		s.SetNumber(v)
	})
}

// UpdateNumber sets the "number" field to the value that was provided on create.
func (u *DocumentUpsertOne) UpdateNumber() *DocumentUpsertOne {
	return u.Update(func(s *DocumentUpsert) {
		s.UpdateNumber()
	})
}

// ClearNumber clears the value of the "number" field.
func (u *DocumentUpsertOne) ClearNumber() *DocumentUpsertOne {
	return u.Update(func(s *DocumentUpsert) {
		s.ClearNumber()
	})
}

// SetSignedDocID sets the "signed_doc_id" field.
func (u *DocumentUpsertOne) SetSignedDocID(v uuid.UUID) *DocumentUpsertOne {
	return u.Update(func(s *DocumentUpsert) {
		s.SetSignedDocID(v)
	})
}

// UpdateSignedDocID sets the "signed_doc_id" field to the value that was provided on create.
func (u *DocumentUpsertOne) UpdateSignedDocID() *DocumentUpsertOne {
	return u.Update(func(s *DocumentUpsert) {
		s.UpdateSignedDocID()
	})
}

// ClearSignedDocID clears the value of the "signed_doc_id" field.
func (u *DocumentUpsertOne) ClearSignedDocID() *DocumentUpsertOne {
	return u.Update(func(s *DocumentUpsert) {
		s.ClearSignedDocID()
	})
}

// SetDocumentSigningDate sets the "document_signing_date" field.
func (u *DocumentUpsertOne) SetDocumentSigningDate(v time.Time) *DocumentUpsertOne {
	return u.Update(func(s *DocumentUpsert) {
		s.SetDocumentSigningDate(v)
	})
}

// UpdateDocumentSigningDate sets the "document_signing_date" field to the value that was provided on create.
func (u *DocumentUpsertOne) UpdateDocumentSigningDate() *DocumentUpsertOne {
	return u.Update(func(s *DocumentUpsert) {
		s.UpdateDocumentSigningDate()
	})
}

// ClearDocumentSigningDate clears the value of the "document_signing_date" field.
func (u *DocumentUpsertOne) ClearDocumentSigningDate() *DocumentUpsertOne {
	return u.Update(func(s *DocumentUpsert) {
		s.ClearDocumentSigningDate()
	})
}

// SetIsSignable sets the "is_signable" field.
func (u *DocumentUpsertOne) SetIsSignable(v bool) *DocumentUpsertOne {
	return u.Update(func(s *DocumentUpsert) {
		s.SetIsSignable(v)
	})
}

// UpdateIsSignable sets the "is_signable" field to the value that was provided on create.
func (u *DocumentUpsertOne) UpdateIsSignable() *DocumentUpsertOne {
	return u.Update(func(s *DocumentUpsert) {
		s.UpdateIsSignable()
	})
}

// ClearIsSignable clears the value of the "is_signable" field.
func (u *DocumentUpsertOne) ClearIsSignable() *DocumentUpsertOne {
	return u.Update(func(s *DocumentUpsert) {
		s.ClearIsSignable()
	})
}

// Exec executes the query.
func (u *DocumentUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for DocumentCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *DocumentUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *DocumentUpsertOne) ID(ctx context.Context) (id uuid.UUID, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: DocumentUpsertOne.ID is not supported by MySQL driver. Use DocumentUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *DocumentUpsertOne) IDX(ctx context.Context) uuid.UUID {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// DocumentCreateBulk is the builder for creating many Document entities in bulk.
type DocumentCreateBulk struct {
	config
	err      error
	builders []*DocumentCreate
	conflict []sql.ConflictOption
}

// Save creates the Document entities in the database.
func (_c *DocumentCreateBulk) Save(ctx context.Context) ([]*Document, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*Document, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*DocumentMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *DocumentCreateBulk) SaveX(ctx context.Context) []*Document {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *DocumentCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *DocumentCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Document.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.DocumentUpsert) {
//			SetCreateTime(v+v).
//		}).
//		Exec(ctx)
func (_c *DocumentCreateBulk) OnConflict(opts ...sql.ConflictOption) *DocumentUpsertBulk {
	_c.conflict = opts
	return &DocumentUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Document.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *DocumentCreateBulk) OnConflictColumns(columns ...string) *DocumentUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &DocumentUpsertBulk{
		create: _c,
	}
}

// DocumentUpsertBulk is the builder for "upsert"-ing
// a bulk of Document nodes.
type DocumentUpsertBulk struct {
	create *DocumentCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Document.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(document.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *DocumentUpsertBulk) UpdateNewValues() *DocumentUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(document.FieldID)
			}
			if _, exists := b.mutation.CreateTime(); exists {
				s.SetIgnore(document.FieldCreateTime)
			}
			if _, exists := b.mutation.UpdateTime(); exists {
				s.SetIgnore(document.FieldUpdateTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Document.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *DocumentUpsertBulk) Ignore() *DocumentUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *DocumentUpsertBulk) DoNothing() *DocumentUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the DocumentCreateBulk.OnConflict
// documentation for more info.
func (u *DocumentUpsertBulk) Update(set func(*DocumentUpsert)) *DocumentUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&DocumentUpsert{UpdateSet: update})
	}))
	return u
}

// SetDepositApplicationID sets the "deposit_application_id" field.
func (u *DocumentUpsertBulk) SetDepositApplicationID(v uuid.UUID) *DocumentUpsertBulk {
	return u.Update(func(s *DocumentUpsert) {
		s.SetDepositApplicationID(v)
	})
}

// UpdateDepositApplicationID sets the "deposit_application_id" field to the value that was provided on create.
func (u *DocumentUpsertBulk) UpdateDepositApplicationID() *DocumentUpsertBulk {
	return u.Update(func(s *DocumentUpsert) {
		s.UpdateDepositApplicationID()
	})
}

// SetType sets the "type" field.
func (u *DocumentUpsertBulk) SetType(v document.Type) *DocumentUpsertBulk {
	return u.Update(func(s *DocumentUpsert) {
		s.SetType(v)
	})
}

// UpdateType sets the "type" field to the value that was provided on create.
func (u *DocumentUpsertBulk) UpdateType() *DocumentUpsertBulk {
	return u.Update(func(s *DocumentUpsert) {
		s.UpdateType()
	})
}

// SetDocID sets the "doc_id" field.
func (u *DocumentUpsertBulk) SetDocID(v uuid.UUID) *DocumentUpsertBulk {
	return u.Update(func(s *DocumentUpsert) {
		s.SetDocID(v)
	})
}

// UpdateDocID sets the "doc_id" field to the value that was provided on create.
func (u *DocumentUpsertBulk) UpdateDocID() *DocumentUpsertBulk {
	return u.Update(func(s *DocumentUpsert) {
		s.UpdateDocID()
	})
}

// ClearDocID clears the value of the "doc_id" field.
func (u *DocumentUpsertBulk) ClearDocID() *DocumentUpsertBulk {
	return u.Update(func(s *DocumentUpsert) {
		s.ClearDocID()
	})
}

// SetNumber sets the "number" field.
func (u *DocumentUpsertBulk) SetNumber(v string) *DocumentUpsertBulk {
	return u.Update(func(s *DocumentUpsert) {
		s.SetNumber(v)
	})
}

// UpdateNumber sets the "number" field to the value that was provided on create.
func (u *DocumentUpsertBulk) UpdateNumber() *DocumentUpsertBulk {
	return u.Update(func(s *DocumentUpsert) {
		s.UpdateNumber()
	})
}

// ClearNumber clears the value of the "number" field.
func (u *DocumentUpsertBulk) ClearNumber() *DocumentUpsertBulk {
	return u.Update(func(s *DocumentUpsert) {
		s.ClearNumber()
	})
}

// SetSignedDocID sets the "signed_doc_id" field.
func (u *DocumentUpsertBulk) SetSignedDocID(v uuid.UUID) *DocumentUpsertBulk {
	return u.Update(func(s *DocumentUpsert) {
		s.SetSignedDocID(v)
	})
}

// UpdateSignedDocID sets the "signed_doc_id" field to the value that was provided on create.
func (u *DocumentUpsertBulk) UpdateSignedDocID() *DocumentUpsertBulk {
	return u.Update(func(s *DocumentUpsert) {
		s.UpdateSignedDocID()
	})
}

// ClearSignedDocID clears the value of the "signed_doc_id" field.
func (u *DocumentUpsertBulk) ClearSignedDocID() *DocumentUpsertBulk {
	return u.Update(func(s *DocumentUpsert) {
		s.ClearSignedDocID()
	})
}

// SetDocumentSigningDate sets the "document_signing_date" field.
func (u *DocumentUpsertBulk) SetDocumentSigningDate(v time.Time) *DocumentUpsertBulk {
	return u.Update(func(s *DocumentUpsert) {
		s.SetDocumentSigningDate(v)
	})
}

// UpdateDocumentSigningDate sets the "document_signing_date" field to the value that was provided on create.
func (u *DocumentUpsertBulk) UpdateDocumentSigningDate() *DocumentUpsertBulk {
	return u.Update(func(s *DocumentUpsert) {
		s.UpdateDocumentSigningDate()
	})
}

// ClearDocumentSigningDate clears the value of the "document_signing_date" field.
func (u *DocumentUpsertBulk) ClearDocumentSigningDate() *DocumentUpsertBulk {
	return u.Update(func(s *DocumentUpsert) {
		s.ClearDocumentSigningDate()
	})
}

// SetIsSignable sets the "is_signable" field.
func (u *DocumentUpsertBulk) SetIsSignable(v bool) *DocumentUpsertBulk {
	return u.Update(func(s *DocumentUpsert) {
		s.SetIsSignable(v)
	})
}

// UpdateIsSignable sets the "is_signable" field to the value that was provided on create.
func (u *DocumentUpsertBulk) UpdateIsSignable() *DocumentUpsertBulk {
	return u.Update(func(s *DocumentUpsert) {
		s.UpdateIsSignable()
	})
}

// ClearIsSignable clears the value of the "is_signable" field.
func (u *DocumentUpsertBulk) ClearIsSignable() *DocumentUpsertBulk {
	return u.Update(func(s *DocumentUpsert) {
		s.ClearIsSignable()
	})
}

// Exec executes the query.
func (u *DocumentUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the DocumentCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for DocumentCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *DocumentUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
