// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"database/sql/driver"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositapplication"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/document"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/predicate"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/profitrate"
)

// DepositApplicationQuery is the builder for querying DepositApplication entities.
type DepositApplicationQuery struct {
	config
	ctx           *QueryContext
	order         []depositapplication.OrderOption
	inters        []Interceptor
	predicates    []predicate.DepositApplication
	withDocuments *DocumentQuery
	withRate      *ProfitRateQuery
	modifiers     []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the DepositApplicationQuery builder.
func (_q *DepositApplicationQuery) Where(ps ...predicate.DepositApplication) *DepositApplicationQuery {
	_q.predicates = append(_q.predicates, ps...)
	return _q
}

// Limit the number of records to be returned by this query.
func (_q *DepositApplicationQuery) Limit(limit int) *DepositApplicationQuery {
	_q.ctx.Limit = &limit
	return _q
}

// Offset to start from.
func (_q *DepositApplicationQuery) Offset(offset int) *DepositApplicationQuery {
	_q.ctx.Offset = &offset
	return _q
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (_q *DepositApplicationQuery) Unique(unique bool) *DepositApplicationQuery {
	_q.ctx.Unique = &unique
	return _q
}

// Order specifies how the records should be ordered.
func (_q *DepositApplicationQuery) Order(o ...depositapplication.OrderOption) *DepositApplicationQuery {
	_q.order = append(_q.order, o...)
	return _q
}

// QueryDocuments chains the current query on the "documents" edge.
func (_q *DepositApplicationQuery) QueryDocuments() *DocumentQuery {
	query := (&DocumentClient{config: _q.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := _q.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := _q.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(depositapplication.Table, depositapplication.FieldID, selector),
			sqlgraph.To(document.Table, document.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, depositapplication.DocumentsTable, depositapplication.DocumentsColumn),
		)
		fromU = sqlgraph.SetNeighbors(_q.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryRate chains the current query on the "rate" edge.
func (_q *DepositApplicationQuery) QueryRate() *ProfitRateQuery {
	query := (&ProfitRateClient{config: _q.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := _q.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := _q.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(depositapplication.Table, depositapplication.FieldID, selector),
			sqlgraph.To(profitrate.Table, profitrate.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, depositapplication.RateTable, depositapplication.RateColumn),
		)
		fromU = sqlgraph.SetNeighbors(_q.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first DepositApplication entity from the query.
// Returns a *NotFoundError when no DepositApplication was found.
func (_q *DepositApplicationQuery) First(ctx context.Context) (*DepositApplication, error) {
	nodes, err := _q.Limit(1).All(setContextOp(ctx, _q.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{depositapplication.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (_q *DepositApplicationQuery) FirstX(ctx context.Context) *DepositApplication {
	node, err := _q.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first DepositApplication ID from the query.
// Returns a *NotFoundError when no DepositApplication ID was found.
func (_q *DepositApplicationQuery) FirstID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = _q.Limit(1).IDs(setContextOp(ctx, _q.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{depositapplication.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (_q *DepositApplicationQuery) FirstIDX(ctx context.Context) uuid.UUID {
	id, err := _q.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single DepositApplication entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one DepositApplication entity is found.
// Returns a *NotFoundError when no DepositApplication entities are found.
func (_q *DepositApplicationQuery) Only(ctx context.Context) (*DepositApplication, error) {
	nodes, err := _q.Limit(2).All(setContextOp(ctx, _q.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{depositapplication.Label}
	default:
		return nil, &NotSingularError{depositapplication.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (_q *DepositApplicationQuery) OnlyX(ctx context.Context) *DepositApplication {
	node, err := _q.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only DepositApplication ID in the query.
// Returns a *NotSingularError when more than one DepositApplication ID is found.
// Returns a *NotFoundError when no entities are found.
func (_q *DepositApplicationQuery) OnlyID(ctx context.Context) (id uuid.UUID, err error) {
	var ids []uuid.UUID
	if ids, err = _q.Limit(2).IDs(setContextOp(ctx, _q.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{depositapplication.Label}
	default:
		err = &NotSingularError{depositapplication.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (_q *DepositApplicationQuery) OnlyIDX(ctx context.Context) uuid.UUID {
	id, err := _q.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of DepositApplications.
func (_q *DepositApplicationQuery) All(ctx context.Context) ([]*DepositApplication, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryAll)
	if err := _q.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*DepositApplication, *DepositApplicationQuery]()
	return withInterceptors[[]*DepositApplication](ctx, _q, qr, _q.inters)
}

// AllX is like All, but panics if an error occurs.
func (_q *DepositApplicationQuery) AllX(ctx context.Context) []*DepositApplication {
	nodes, err := _q.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of DepositApplication IDs.
func (_q *DepositApplicationQuery) IDs(ctx context.Context) (ids []uuid.UUID, err error) {
	if _q.ctx.Unique == nil && _q.path != nil {
		_q.Unique(true)
	}
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryIDs)
	if err = _q.Select(depositapplication.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (_q *DepositApplicationQuery) IDsX(ctx context.Context) []uuid.UUID {
	ids, err := _q.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (_q *DepositApplicationQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryCount)
	if err := _q.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, _q, querierCount[*DepositApplicationQuery](), _q.inters)
}

// CountX is like Count, but panics if an error occurs.
func (_q *DepositApplicationQuery) CountX(ctx context.Context) int {
	count, err := _q.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (_q *DepositApplicationQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryExist)
	switch _, err := _q.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (_q *DepositApplicationQuery) ExistX(ctx context.Context) bool {
	exist, err := _q.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the DepositApplicationQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (_q *DepositApplicationQuery) Clone() *DepositApplicationQuery {
	if _q == nil {
		return nil
	}
	return &DepositApplicationQuery{
		config:        _q.config,
		ctx:           _q.ctx.Clone(),
		order:         append([]depositapplication.OrderOption{}, _q.order...),
		inters:        append([]Interceptor{}, _q.inters...),
		predicates:    append([]predicate.DepositApplication{}, _q.predicates...),
		withDocuments: _q.withDocuments.Clone(),
		withRate:      _q.withRate.Clone(),
		// clone intermediate query.
		sql:       _q.sql.Clone(),
		path:      _q.path,
		modifiers: append([]func(*sql.Selector){}, _q.modifiers...),
	}
}

// WithDocuments tells the query-builder to eager-load the nodes that are connected to
// the "documents" edge. The optional arguments are used to configure the query builder of the edge.
func (_q *DepositApplicationQuery) WithDocuments(opts ...func(*DocumentQuery)) *DepositApplicationQuery {
	query := (&DocumentClient{config: _q.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	_q.withDocuments = query
	return _q
}

// WithRate tells the query-builder to eager-load the nodes that are connected to
// the "rate" edge. The optional arguments are used to configure the query builder of the edge.
func (_q *DepositApplicationQuery) WithRate(opts ...func(*ProfitRateQuery)) *DepositApplicationQuery {
	query := (&ProfitRateClient{config: _q.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	_q.withRate = query
	return _q
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.DepositApplication.Query().
//		GroupBy(depositapplication.FieldCreateTime).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (_q *DepositApplicationQuery) GroupBy(field string, fields ...string) *DepositApplicationGroupBy {
	_q.ctx.Fields = append([]string{field}, fields...)
	grbuild := &DepositApplicationGroupBy{build: _q}
	grbuild.flds = &_q.ctx.Fields
	grbuild.label = depositapplication.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreateTime time.Time `json:"create_time,omitempty"`
//	}
//
//	client.DepositApplication.Query().
//		Select(depositapplication.FieldCreateTime).
//		Scan(ctx, &v)
func (_q *DepositApplicationQuery) Select(fields ...string) *DepositApplicationSelect {
	_q.ctx.Fields = append(_q.ctx.Fields, fields...)
	sbuild := &DepositApplicationSelect{DepositApplicationQuery: _q}
	sbuild.label = depositapplication.Label
	sbuild.flds, sbuild.scan = &_q.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a DepositApplicationSelect configured with the given aggregations.
func (_q *DepositApplicationQuery) Aggregate(fns ...AggregateFunc) *DepositApplicationSelect {
	return _q.Select().Aggregate(fns...)
}

func (_q *DepositApplicationQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range _q.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, _q); err != nil {
				return err
			}
		}
	}
	for _, f := range _q.ctx.Fields {
		if !depositapplication.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if _q.path != nil {
		prev, err := _q.path(ctx)
		if err != nil {
			return err
		}
		_q.sql = prev
	}
	return nil
}

func (_q *DepositApplicationQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*DepositApplication, error) {
	var (
		nodes       = []*DepositApplication{}
		_spec       = _q.querySpec()
		loadedTypes = [2]bool{
			_q.withDocuments != nil,
			_q.withRate != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*DepositApplication).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &DepositApplication{config: _q.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	if len(_q.modifiers) > 0 {
		_spec.Modifiers = _q.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, _q.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := _q.withDocuments; query != nil {
		if err := _q.loadDocuments(ctx, query, nodes,
			func(n *DepositApplication) { n.Edges.Documents = []*Document{} },
			func(n *DepositApplication, e *Document) { n.Edges.Documents = append(n.Edges.Documents, e) }); err != nil {
			return nil, err
		}
	}
	if query := _q.withRate; query != nil {
		if err := _q.loadRate(ctx, query, nodes, nil,
			func(n *DepositApplication, e *ProfitRate) { n.Edges.Rate = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (_q *DepositApplicationQuery) loadDocuments(ctx context.Context, query *DocumentQuery, nodes []*DepositApplication, init func(*DepositApplication), assign func(*DepositApplication, *Document)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[uuid.UUID]*DepositApplication)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(document.FieldDepositApplicationID)
	}
	query.Where(predicate.Document(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(depositapplication.DocumentsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.DepositApplicationID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "deposit_application_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (_q *DepositApplicationQuery) loadRate(ctx context.Context, query *ProfitRateQuery, nodes []*DepositApplication, init func(*DepositApplication), assign func(*DepositApplication, *ProfitRate)) error {
	ids := make([]uuid.UUID, 0, len(nodes))
	nodeids := make(map[uuid.UUID][]*DepositApplication)
	for i := range nodes {
		fk := nodes[i].ProfitRateID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(profitrate.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "profit_rate_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (_q *DepositApplicationQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := _q.querySpec()
	if len(_q.modifiers) > 0 {
		_spec.Modifiers = _q.modifiers
	}
	_spec.Node.Columns = _q.ctx.Fields
	if len(_q.ctx.Fields) > 0 {
		_spec.Unique = _q.ctx.Unique != nil && *_q.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, _q.driver, _spec)
}

func (_q *DepositApplicationQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(depositapplication.Table, depositapplication.Columns, sqlgraph.NewFieldSpec(depositapplication.FieldID, field.TypeUUID))
	_spec.From = _q.sql
	if unique := _q.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if _q.path != nil {
		_spec.Unique = true
	}
	if fields := _q.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, depositapplication.FieldID)
		for i := range fields {
			if fields[i] != depositapplication.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if _q.withRate != nil {
			_spec.Node.AddColumnOnce(depositapplication.FieldProfitRateID)
		}
	}
	if ps := _q.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := _q.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := _q.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := _q.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (_q *DepositApplicationQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(_q.driver.Dialect())
	t1 := builder.Table(depositapplication.Table)
	columns := _q.ctx.Fields
	if len(columns) == 0 {
		columns = depositapplication.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if _q.sql != nil {
		selector = _q.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if _q.ctx.Unique != nil && *_q.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range _q.modifiers {
		m(selector)
	}
	for _, p := range _q.predicates {
		p(selector)
	}
	for _, p := range _q.order {
		p(selector)
	}
	if offset := _q.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := _q.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (_q *DepositApplicationQuery) ForUpdate(opts ...sql.LockOption) *DepositApplicationQuery {
	if _q.driver.Dialect() == dialect.Postgres {
		_q.Unique(false)
	}
	_q.modifiers = append(_q.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return _q
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (_q *DepositApplicationQuery) ForShare(opts ...sql.LockOption) *DepositApplicationQuery {
	if _q.driver.Dialect() == dialect.Postgres {
		_q.Unique(false)
	}
	_q.modifiers = append(_q.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return _q
}

// Modify adds a query modifier for attaching custom logic to queries.
func (_q *DepositApplicationQuery) Modify(modifiers ...func(s *sql.Selector)) *DepositApplicationSelect {
	_q.modifiers = append(_q.modifiers, modifiers...)
	return _q.Select()
}

// DepositApplicationGroupBy is the group-by builder for DepositApplication entities.
type DepositApplicationGroupBy struct {
	selector
	build *DepositApplicationQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (_g *DepositApplicationGroupBy) Aggregate(fns ...AggregateFunc) *DepositApplicationGroupBy {
	_g.fns = append(_g.fns, fns...)
	return _g
}

// Scan applies the selector query and scans the result into the given value.
func (_g *DepositApplicationGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, _g.build.ctx, ent.OpQueryGroupBy)
	if err := _g.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*DepositApplicationQuery, *DepositApplicationGroupBy](ctx, _g.build, _g, _g.build.inters, v)
}

func (_g *DepositApplicationGroupBy) sqlScan(ctx context.Context, root *DepositApplicationQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(_g.fns))
	for _, fn := range _g.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*_g.flds)+len(_g.fns))
		for _, f := range *_g.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*_g.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := _g.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// DepositApplicationSelect is the builder for selecting fields of DepositApplication entities.
type DepositApplicationSelect struct {
	*DepositApplicationQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (_s *DepositApplicationSelect) Aggregate(fns ...AggregateFunc) *DepositApplicationSelect {
	_s.fns = append(_s.fns, fns...)
	return _s
}

// Scan applies the selector query and scans the result into the given value.
func (_s *DepositApplicationSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, _s.ctx, ent.OpQuerySelect)
	if err := _s.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*DepositApplicationQuery, *DepositApplicationSelect](ctx, _s.DepositApplicationQuery, _s, _s.inters, v)
}

func (_s *DepositApplicationSelect) sqlScan(ctx context.Context, root *DepositApplicationQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(_s.fns))
	for _, fn := range _s.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*_s.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := _s.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (_s *DepositApplicationSelect) Modify(modifiers ...func(s *sql.Selector)) *DepositApplicationSelect {
	_s.modifiers = append(_s.modifiers, modifiers...)
	return _s
}
