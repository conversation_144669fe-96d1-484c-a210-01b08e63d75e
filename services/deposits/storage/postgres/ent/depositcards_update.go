// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositapplication"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositcards"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/predicate"
)

// DepositCardsUpdate is the builder for updating DepositCards entities.
type DepositCardsUpdate struct {
	config
	hooks     []Hook
	mutation  *DepositCardsMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the DepositCardsUpdate builder.
func (_u *DepositCardsUpdate) Where(ps ...predicate.DepositCards) *DepositCardsUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetCardID sets the "card_id" field.
func (_u *DepositCardsUpdate) SetCardID(v uuid.UUID) *DepositCardsUpdate {
	_u.mutation.SetCardID(v)
	return _u
}

// SetNillableCardID sets the "card_id" field if the given value is not nil.
func (_u *DepositCardsUpdate) SetNillableCardID(v *uuid.UUID) *DepositCardsUpdate {
	if v != nil {
		_u.SetCardID(*v)
	}
	return _u
}

// SetAccountIban sets the "account_iban" field.
func (_u *DepositCardsUpdate) SetAccountIban(v string) *DepositCardsUpdate {
	_u.mutation.SetAccountIban(v)
	return _u
}

// SetNillableAccountIban sets the "account_iban" field if the given value is not nil.
func (_u *DepositCardsUpdate) SetNillableAccountIban(v *string) *DepositCardsUpdate {
	if v != nil {
		_u.SetAccountIban(*v)
	}
	return _u
}

// SetFinContractID sets the "fin_contract_id" field.
func (_u *DepositCardsUpdate) SetFinContractID(v string) *DepositCardsUpdate {
	_u.mutation.SetFinContractID(v)
	return _u
}

// SetNillableFinContractID sets the "fin_contract_id" field if the given value is not nil.
func (_u *DepositCardsUpdate) SetNillableFinContractID(v *string) *DepositCardsUpdate {
	if v != nil {
		_u.SetFinContractID(*v)
	}
	return _u
}

// SetRrn sets the "rrn" field.
func (_u *DepositCardsUpdate) SetRrn(v string) *DepositCardsUpdate {
	_u.mutation.SetRrn(v)
	return _u
}

// SetNillableRrn sets the "rrn" field if the given value is not nil.
func (_u *DepositCardsUpdate) SetNillableRrn(v *string) *DepositCardsUpdate {
	if v != nil {
		_u.SetRrn(*v)
	}
	return _u
}

// SetExternalID sets the "external_id" field.
func (_u *DepositCardsUpdate) SetExternalID(v string) *DepositCardsUpdate {
	_u.mutation.SetExternalID(v)
	return _u
}

// SetNillableExternalID sets the "external_id" field if the given value is not nil.
func (_u *DepositCardsUpdate) SetNillableExternalID(v *string) *DepositCardsUpdate {
	if v != nil {
		_u.SetExternalID(*v)
	}
	return _u
}

// SetProcessingTransactionID sets the "processing_transaction_id" field.
func (_u *DepositCardsUpdate) SetProcessingTransactionID(v int64) *DepositCardsUpdate {
	_u.mutation.ResetProcessingTransactionID()
	_u.mutation.SetProcessingTransactionID(v)
	return _u
}

// SetNillableProcessingTransactionID sets the "processing_transaction_id" field if the given value is not nil.
func (_u *DepositCardsUpdate) SetNillableProcessingTransactionID(v *int64) *DepositCardsUpdate {
	if v != nil {
		_u.SetProcessingTransactionID(*v)
	}
	return _u
}

// AddProcessingTransactionID adds value to the "processing_transaction_id" field.
func (_u *DepositCardsUpdate) AddProcessingTransactionID(v int64) *DepositCardsUpdate {
	_u.mutation.AddProcessingTransactionID(v)
	return _u
}

// SetApplicationID sets the "application" edge to the DepositApplication entity by ID.
func (_u *DepositCardsUpdate) SetApplicationID(id uuid.UUID) *DepositCardsUpdate {
	_u.mutation.SetApplicationID(id)
	return _u
}

// SetNillableApplicationID sets the "application" edge to the DepositApplication entity by ID if the given value is not nil.
func (_u *DepositCardsUpdate) SetNillableApplicationID(id *uuid.UUID) *DepositCardsUpdate {
	if id != nil {
		_u = _u.SetApplicationID(*id)
	}
	return _u
}

// SetApplication sets the "application" edge to the DepositApplication entity.
func (_u *DepositCardsUpdate) SetApplication(v *DepositApplication) *DepositCardsUpdate {
	return _u.SetApplicationID(v.ID)
}

// Mutation returns the DepositCardsMutation object of the builder.
func (_u *DepositCardsUpdate) Mutation() *DepositCardsMutation {
	return _u.mutation
}

// ClearApplication clears the "application" edge to the DepositApplication entity.
func (_u *DepositCardsUpdate) ClearApplication() *DepositCardsUpdate {
	_u.mutation.ClearApplication()
	return _u
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *DepositCardsUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *DepositCardsUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *DepositCardsUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *DepositCardsUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *DepositCardsUpdate) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := depositcards.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *DepositCardsUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DepositCardsUpdate {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *DepositCardsUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	_spec := sqlgraph.NewUpdateSpec(depositcards.Table, depositcards.Columns, sqlgraph.NewFieldSpec(depositcards.FieldID, field.TypeUUID))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(depositcards.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(depositcards.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(depositcards.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.CardID(); ok {
		_spec.SetField(depositcards.FieldCardID, field.TypeUUID, value)
	}
	if value, ok := _u.mutation.AccountIban(); ok {
		_spec.SetField(depositcards.FieldAccountIban, field.TypeString, value)
	}
	if value, ok := _u.mutation.FinContractID(); ok {
		_spec.SetField(depositcards.FieldFinContractID, field.TypeString, value)
	}
	if value, ok := _u.mutation.Rrn(); ok {
		_spec.SetField(depositcards.FieldRrn, field.TypeString, value)
	}
	if value, ok := _u.mutation.ExternalID(); ok {
		_spec.SetField(depositcards.FieldExternalID, field.TypeString, value)
	}
	if value, ok := _u.mutation.ProcessingTransactionID(); ok {
		_spec.SetField(depositcards.FieldProcessingTransactionID, field.TypeInt64, value)
	}
	if value, ok := _u.mutation.AddedProcessingTransactionID(); ok {
		_spec.AddField(depositcards.FieldProcessingTransactionID, field.TypeInt64, value)
	}
	if _u.mutation.ApplicationCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   depositcards.ApplicationTable,
			Columns: []string{depositcards.ApplicationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(depositapplication.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.ApplicationIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   depositcards.ApplicationTable,
			Columns: []string{depositcards.ApplicationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(depositapplication.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(_u.modifiers...)
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{depositcards.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// DepositCardsUpdateOne is the builder for updating a single DepositCards entity.
type DepositCardsUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *DepositCardsMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetCardID sets the "card_id" field.
func (_u *DepositCardsUpdateOne) SetCardID(v uuid.UUID) *DepositCardsUpdateOne {
	_u.mutation.SetCardID(v)
	return _u
}

// SetNillableCardID sets the "card_id" field if the given value is not nil.
func (_u *DepositCardsUpdateOne) SetNillableCardID(v *uuid.UUID) *DepositCardsUpdateOne {
	if v != nil {
		_u.SetCardID(*v)
	}
	return _u
}

// SetAccountIban sets the "account_iban" field.
func (_u *DepositCardsUpdateOne) SetAccountIban(v string) *DepositCardsUpdateOne {
	_u.mutation.SetAccountIban(v)
	return _u
}

// SetNillableAccountIban sets the "account_iban" field if the given value is not nil.
func (_u *DepositCardsUpdateOne) SetNillableAccountIban(v *string) *DepositCardsUpdateOne {
	if v != nil {
		_u.SetAccountIban(*v)
	}
	return _u
}

// SetFinContractID sets the "fin_contract_id" field.
func (_u *DepositCardsUpdateOne) SetFinContractID(v string) *DepositCardsUpdateOne {
	_u.mutation.SetFinContractID(v)
	return _u
}

// SetNillableFinContractID sets the "fin_contract_id" field if the given value is not nil.
func (_u *DepositCardsUpdateOne) SetNillableFinContractID(v *string) *DepositCardsUpdateOne {
	if v != nil {
		_u.SetFinContractID(*v)
	}
	return _u
}

// SetRrn sets the "rrn" field.
func (_u *DepositCardsUpdateOne) SetRrn(v string) *DepositCardsUpdateOne {
	_u.mutation.SetRrn(v)
	return _u
}

// SetNillableRrn sets the "rrn" field if the given value is not nil.
func (_u *DepositCardsUpdateOne) SetNillableRrn(v *string) *DepositCardsUpdateOne {
	if v != nil {
		_u.SetRrn(*v)
	}
	return _u
}

// SetExternalID sets the "external_id" field.
func (_u *DepositCardsUpdateOne) SetExternalID(v string) *DepositCardsUpdateOne {
	_u.mutation.SetExternalID(v)
	return _u
}

// SetNillableExternalID sets the "external_id" field if the given value is not nil.
func (_u *DepositCardsUpdateOne) SetNillableExternalID(v *string) *DepositCardsUpdateOne {
	if v != nil {
		_u.SetExternalID(*v)
	}
	return _u
}

// SetProcessingTransactionID sets the "processing_transaction_id" field.
func (_u *DepositCardsUpdateOne) SetProcessingTransactionID(v int64) *DepositCardsUpdateOne {
	_u.mutation.ResetProcessingTransactionID()
	_u.mutation.SetProcessingTransactionID(v)
	return _u
}

// SetNillableProcessingTransactionID sets the "processing_transaction_id" field if the given value is not nil.
func (_u *DepositCardsUpdateOne) SetNillableProcessingTransactionID(v *int64) *DepositCardsUpdateOne {
	if v != nil {
		_u.SetProcessingTransactionID(*v)
	}
	return _u
}

// AddProcessingTransactionID adds value to the "processing_transaction_id" field.
func (_u *DepositCardsUpdateOne) AddProcessingTransactionID(v int64) *DepositCardsUpdateOne {
	_u.mutation.AddProcessingTransactionID(v)
	return _u
}

// SetApplicationID sets the "application" edge to the DepositApplication entity by ID.
func (_u *DepositCardsUpdateOne) SetApplicationID(id uuid.UUID) *DepositCardsUpdateOne {
	_u.mutation.SetApplicationID(id)
	return _u
}

// SetNillableApplicationID sets the "application" edge to the DepositApplication entity by ID if the given value is not nil.
func (_u *DepositCardsUpdateOne) SetNillableApplicationID(id *uuid.UUID) *DepositCardsUpdateOne {
	if id != nil {
		_u = _u.SetApplicationID(*id)
	}
	return _u
}

// SetApplication sets the "application" edge to the DepositApplication entity.
func (_u *DepositCardsUpdateOne) SetApplication(v *DepositApplication) *DepositCardsUpdateOne {
	return _u.SetApplicationID(v.ID)
}

// Mutation returns the DepositCardsMutation object of the builder.
func (_u *DepositCardsUpdateOne) Mutation() *DepositCardsMutation {
	return _u.mutation
}

// ClearApplication clears the "application" edge to the DepositApplication entity.
func (_u *DepositCardsUpdateOne) ClearApplication() *DepositCardsUpdateOne {
	_u.mutation.ClearApplication()
	return _u
}

// Where appends a list predicates to the DepositCardsUpdate builder.
func (_u *DepositCardsUpdateOne) Where(ps ...predicate.DepositCards) *DepositCardsUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *DepositCardsUpdateOne) Select(field string, fields ...string) *DepositCardsUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated DepositCards entity.
func (_u *DepositCardsUpdateOne) Save(ctx context.Context) (*DepositCards, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *DepositCardsUpdateOne) SaveX(ctx context.Context) *DepositCards {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *DepositCardsUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *DepositCardsUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *DepositCardsUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdateTime(); !ok && !_u.mutation.UpdateTimeCleared() {
		v := depositcards.UpdateDefaultUpdateTime()
		_u.mutation.SetUpdateTime(v)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (_u *DepositCardsUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DepositCardsUpdateOne {
	_u.modifiers = append(_u.modifiers, modifiers...)
	return _u
}

func (_u *DepositCardsUpdateOne) sqlSave(ctx context.Context) (_node *DepositCards, err error) {
	_spec := sqlgraph.NewUpdateSpec(depositcards.Table, depositcards.Columns, sqlgraph.NewFieldSpec(depositcards.FieldID, field.TypeUUID))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "DepositCards.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, depositcards.FieldID)
		for _, f := range fields {
			if !depositcards.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != depositcards.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if _u.mutation.CreateTimeCleared() {
		_spec.ClearField(depositcards.FieldCreateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.UpdateTime(); ok {
		_spec.SetField(depositcards.FieldUpdateTime, field.TypeTime, value)
	}
	if _u.mutation.UpdateTimeCleared() {
		_spec.ClearField(depositcards.FieldUpdateTime, field.TypeTime)
	}
	if value, ok := _u.mutation.CardID(); ok {
		_spec.SetField(depositcards.FieldCardID, field.TypeUUID, value)
	}
	if value, ok := _u.mutation.AccountIban(); ok {
		_spec.SetField(depositcards.FieldAccountIban, field.TypeString, value)
	}
	if value, ok := _u.mutation.FinContractID(); ok {
		_spec.SetField(depositcards.FieldFinContractID, field.TypeString, value)
	}
	if value, ok := _u.mutation.Rrn(); ok {
		_spec.SetField(depositcards.FieldRrn, field.TypeString, value)
	}
	if value, ok := _u.mutation.ExternalID(); ok {
		_spec.SetField(depositcards.FieldExternalID, field.TypeString, value)
	}
	if value, ok := _u.mutation.ProcessingTransactionID(); ok {
		_spec.SetField(depositcards.FieldProcessingTransactionID, field.TypeInt64, value)
	}
	if value, ok := _u.mutation.AddedProcessingTransactionID(); ok {
		_spec.AddField(depositcards.FieldProcessingTransactionID, field.TypeInt64, value)
	}
	if _u.mutation.ApplicationCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   depositcards.ApplicationTable,
			Columns: []string{depositcards.ApplicationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(depositapplication.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := _u.mutation.ApplicationIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   depositcards.ApplicationTable,
			Columns: []string{depositcards.ApplicationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(depositapplication.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(_u.modifiers...)
	_node = &DepositCards{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{depositcards.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
