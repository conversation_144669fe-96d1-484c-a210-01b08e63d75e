// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/depositapplication"
	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/storage/postgres/ent/profitrate"
)

// DepositApplication is the model entity for the DepositApplication schema.
type DepositApplication struct {
	config `json:"-"`
	// ID of the ent.
	ID uuid.UUID `json:"id,omitempty"`
	// CreateTime holds the value of the "create_time" field.
	CreateTime time.Time `json:"create_time,omitempty"`
	// UpdateTime holds the value of the "update_time" field.
	UpdateTime time.Time `json:"update_time,omitempty"`
	// ProfitRateID holds the value of the "profit_rate_id" field.
	ProfitRateID uuid.UUID `json:"profit_rate_id,omitempty"`
	// ColvirReferenceID holds the value of the "colvir_reference_id" field.
	ColvirReferenceID *string `json:"colvir_reference_id,omitempty"`
	// Status holds the value of the "status" field.
	Status depositapplication.Status `json:"status,omitempty"`
	// UserID holds the value of the "user_id" field.
	UserID uuid.UUID `json:"user_id,omitempty"`
	// UserIin holds the value of the "user_iin" field.
	UserIin string `json:"user_iin,omitempty"`
	// DepositSourceAccountID holds the value of the "deposit_source_account_id" field.
	DepositSourceAccountID uuid.UUID `json:"deposit_source_account_id,omitempty"`
	// DepositAmount holds the value of the "deposit_amount" field.
	DepositAmount decimal.Decimal `json:"deposit_amount,omitempty"`
	// DepositPayoutMethod holds the value of the "deposit_payout_method" field.
	DepositPayoutMethod depositapplication.DepositPayoutMethod `json:"deposit_payout_method,omitempty"`
	// ClosedAt holds the value of the "closed_at" field.
	ClosedAt *time.Time `json:"closed_at,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the DepositApplicationQuery when eager-loading is set.
	Edges        DepositApplicationEdges `json:"edges"`
	selectValues sql.SelectValues
}

// DepositApplicationEdges holds the relations/edges for other nodes in the graph.
type DepositApplicationEdges struct {
	// Documents holds the value of the documents edge.
	Documents []*Document `json:"documents,omitempty"`
	// Rate holds the value of the rate edge.
	Rate *ProfitRate `json:"rate,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// DocumentsOrErr returns the Documents value or an error if the edge
// was not loaded in eager-loading.
func (e DepositApplicationEdges) DocumentsOrErr() ([]*Document, error) {
	if e.loadedTypes[0] {
		return e.Documents, nil
	}
	return nil, &NotLoadedError{edge: "documents"}
}

// RateOrErr returns the Rate value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e DepositApplicationEdges) RateOrErr() (*ProfitRate, error) {
	if e.Rate != nil {
		return e.Rate, nil
	} else if e.loadedTypes[1] {
		return nil, &NotFoundError{label: profitrate.Label}
	}
	return nil, &NotLoadedError{edge: "rate"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*DepositApplication) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case depositapplication.FieldDepositAmount:
			values[i] = new(decimal.Decimal)
		case depositapplication.FieldColvirReferenceID, depositapplication.FieldStatus, depositapplication.FieldUserIin, depositapplication.FieldDepositPayoutMethod:
			values[i] = new(sql.NullString)
		case depositapplication.FieldCreateTime, depositapplication.FieldUpdateTime, depositapplication.FieldClosedAt:
			values[i] = new(sql.NullTime)
		case depositapplication.FieldID, depositapplication.FieldProfitRateID, depositapplication.FieldUserID, depositapplication.FieldDepositSourceAccountID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the DepositApplication fields.
func (_m *DepositApplication) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case depositapplication.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				_m.ID = *value
			}
		case depositapplication.FieldCreateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field create_time", values[i])
			} else if value.Valid {
				_m.CreateTime = value.Time
			}
		case depositapplication.FieldUpdateTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field update_time", values[i])
			} else if value.Valid {
				_m.UpdateTime = value.Time
			}
		case depositapplication.FieldProfitRateID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field profit_rate_id", values[i])
			} else if value != nil {
				_m.ProfitRateID = *value
			}
		case depositapplication.FieldColvirReferenceID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field colvir_reference_id", values[i])
			} else if value.Valid {
				_m.ColvirReferenceID = new(string)
				*_m.ColvirReferenceID = value.String
			}
		case depositapplication.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				_m.Status = depositapplication.Status(value.String)
			}
		case depositapplication.FieldUserID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value != nil {
				_m.UserID = *value
			}
		case depositapplication.FieldUserIin:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field user_iin", values[i])
			} else if value.Valid {
				_m.UserIin = value.String
			}
		case depositapplication.FieldDepositSourceAccountID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field deposit_source_account_id", values[i])
			} else if value != nil {
				_m.DepositSourceAccountID = *value
			}
		case depositapplication.FieldDepositAmount:
			if value, ok := values[i].(*decimal.Decimal); !ok {
				return fmt.Errorf("unexpected type %T for field deposit_amount", values[i])
			} else if value != nil {
				_m.DepositAmount = *value
			}
		case depositapplication.FieldDepositPayoutMethod:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field deposit_payout_method", values[i])
			} else if value.Valid {
				_m.DepositPayoutMethod = depositapplication.DepositPayoutMethod(value.String)
			}
		case depositapplication.FieldClosedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field closed_at", values[i])
			} else if value.Valid {
				_m.ClosedAt = new(time.Time)
				*_m.ClosedAt = value.Time
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the DepositApplication.
// This includes values selected through modifiers, order, etc.
func (_m *DepositApplication) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// QueryDocuments queries the "documents" edge of the DepositApplication entity.
func (_m *DepositApplication) QueryDocuments() *DocumentQuery {
	return NewDepositApplicationClient(_m.config).QueryDocuments(_m)
}

// QueryRate queries the "rate" edge of the DepositApplication entity.
func (_m *DepositApplication) QueryRate() *ProfitRateQuery {
	return NewDepositApplicationClient(_m.config).QueryRate(_m)
}

// Update returns a builder for updating this DepositApplication.
// Note that you need to call DepositApplication.Unwrap() before calling this method if this DepositApplication
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *DepositApplication) Update() *DepositApplicationUpdateOne {
	return NewDepositApplicationClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the DepositApplication entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *DepositApplication) Unwrap() *DepositApplication {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("ent: DepositApplication is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *DepositApplication) String() string {
	var builder strings.Builder
	builder.WriteString("DepositApplication(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("create_time=")
	builder.WriteString(_m.CreateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("update_time=")
	builder.WriteString(_m.UpdateTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("profit_rate_id=")
	builder.WriteString(fmt.Sprintf("%v", _m.ProfitRateID))
	builder.WriteString(", ")
	if v := _m.ColvirReferenceID; v != nil {
		builder.WriteString("colvir_reference_id=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", _m.Status))
	builder.WriteString(", ")
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", _m.UserID))
	builder.WriteString(", ")
	builder.WriteString("user_iin=")
	builder.WriteString(_m.UserIin)
	builder.WriteString(", ")
	builder.WriteString("deposit_source_account_id=")
	builder.WriteString(fmt.Sprintf("%v", _m.DepositSourceAccountID))
	builder.WriteString(", ")
	builder.WriteString("deposit_amount=")
	builder.WriteString(fmt.Sprintf("%v", _m.DepositAmount))
	builder.WriteString(", ")
	builder.WriteString("deposit_payout_method=")
	builder.WriteString(fmt.Sprintf("%v", _m.DepositPayoutMethod))
	builder.WriteString(", ")
	if v := _m.ClosedAt; v != nil {
		builder.WriteString("closed_at=")
		builder.WriteString(v.Format(time.ANSIC))
	}
	builder.WriteByte(')')
	return builder.String()
}

// DepositApplications is a parsable slice of DepositApplication.
type DepositApplications []*DepositApplication
