package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"git.redmadrobot.com/backend-go/rmr-pkg/core/gen"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx/annotation"
	"git.redmadrobot.com/backend-go/rmr-pkg/db/entx/mixin"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"

	"git.redmadrobot.com/zaman/backend/zaman/services/deposits/consts"
)

// DepositApplication приложение к документу, информация о депозите
type DepositApplication struct {
	ent.Schema
}

func (DepositApplication) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}).
			Default((func() uuid.UUID)(gen.UUID())),
		field.UUID("profit_rate_id", uuid.UUID{}).
			Annotations(annotation.CustomAnnotation{Description: "Ссылка на ProfitRate"}),
		field.String("colvir_reference_id").
			Optional().
			Nillable().
			Annotations(
				annotation.CustomAnnotation{Description: "id депозита в колвер"},
			),
		field.Enum("status").
			Values(
				consts.DepositApplicationStatusDraft.String(),
				consts.DepositApplicationStatusSigned.String(),
				consts.DepositApplicationStatusClosed.String(),
				consts.DepositApplicationStatusFailed.String(),
			).
			Annotations(annotation.CustomAnnotation{Description: "Статус депозитной заявки"}),
		field.UUID("user_id", uuid.UUID{}).
			Annotations(annotation.CustomAnnotation{Description: "ID пользователя"}),
		field.String("user_iin").
			Annotations(annotation.CustomAnnotation{Description: "ИИН пользователя"}),
		field.UUID("deposit_source_account_id", uuid.UUID{}).
			Annotations(annotation.CustomAnnotation{Description: "ID счета для списания на депозит"}),
		field.String("deposit_amount").
			SchemaType(map[string]string{"postgres": "numeric"}).
			GoType(decimal.Decimal{}).
			Annotations(
				annotation.CustomAnnotation{Description: "Сумма депозита"},
			),
		field.Enum("deposit_payout_method").
			Values(
				consts.PayoutMethodDeposit.String(),
				consts.PayoutMethodCard.String(),
			).
			Annotations(
				annotation.CustomAnnotation{Description: "Метод выплаты депозита"},
			),
		field.Time("closed_at").
			Optional().
			Nillable().
			Annotations(
				annotation.CustomAnnotation{Description: "Дата-время закрытия депозита"},
			),
	}
}

func (DepositApplication) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("documents", Document.Type),
		edge.From("rate", ProfitRate.Type).
			Field("profit_rate_id").
			Ref("rate_deposit_application").
			Unique().
			Required(),
	}
}

func (DepositApplication) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("colvir_reference_id"),
	}
}

func (DepositApplication) Mixin() []ent.Mixin {
	return []ent.Mixin{mixin.Time{}}
}
