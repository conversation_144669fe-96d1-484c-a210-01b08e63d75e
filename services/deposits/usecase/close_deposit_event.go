package usecase

import (
	"context"
	"time"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/errs"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/utils"
	"git.redmadrobot.com/zaman/backend/zaman/topics/documents/message"
)

const signCloseDepositErrTmpl = "usecase:handleSignCloseDepositApplication:%s"

// handleSignCloseDepositApplication хендлер для подписания закрытия депозита
func (u *useCasesImpl) handleSignCloseDepositApplication(ctx context.Context, sign message.DocumentSignReq) error {
	userID := sign.UserID
	ctx = utils.PutUserDataIntoContext(ctx, &utils.UserInfoCtx{
		UserID: userID,
	})

	closeDepositTx, err := u.Providers.Storage.CloseDepositTx(ctx)
	if err != nil {
		return errs.Wrapf(closeDepositTx.Close(ctx, err), signCloseDepositErrTmpl, "CloseDepositTransaction")
	}

	dataForClose, err := closeDepositTx.DataForCloseDepositByDocID(ctx, sign.DocumentID)
	if err != nil {
		return errs.Wrapf(closeDepositTx.Close(ctx, err), signCloseDepositErrTmpl, "DataForCloseDepositByDocID")
	}

	_, err = u.Providers.Colvirbridge.ExecuteDepositOperation(ctx, dataForClose.ExecuteDepositOperationRequest())
	if err != nil {
		return errs.Wrapf(closeDepositTx.Close(ctx, err), signCloseDepositErrTmpl, "ExecuteDepositOperation")
	}

	err = closeDepositTx.SignDocumentAndCloseApplication(ctx, sign.ID, time.Now())

	return closeDepositTx.Close(ctx, err)
}
