package colvir

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"

	"git.redmadrobot.com/backend-go/rmr-pkg/core/logs"

	"git.redmadrobot.com/zaman/backend/zaman/pkg/colvir/entity"
)

// RequestCreateClient - Метод создания карточки клиента в АБИС Колвир
// https://rmrkz.atlassian.net/wiki/spaces/ZMNRET/pages/25265370
func (p *ColvirProviderImpl) RequestCreateClient(ctx context.Context, request *entity.CreateClientRequest) (*entity.CreateClientResp, error) {
	targetURL, err := p.getRequestCreateClientURL()
	if err != nil {
		return nil, err
	}

	reqBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal body for create client request: %w", err)
	}

	logs.FromContext(ctx).Debug().Interface("RequestCreateClient req body", string(reqBody))

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, targetURL, bytes.NewReader(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request for create client request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := p.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to do request for create client request: %w", err)
	}
	defer resp.Body.Close()

	var respBody entity.CreateClientResp
	if resp.StatusCode != http.StatusOK {
		errResp, err := p.handleColvirRespErrStatusCode(ctx, resp)
		if err != nil {
			return nil, err
		}

		respBody.ColvirError = errResp
		return &respBody, ErrInternalServerError
	}

	err = json.NewDecoder(resp.Body).Decode(&respBody)
	if err != nil {
		return nil, fmt.Errorf("failed to decode response for create client request: %w", err)
	}

	return &respBody, nil
}

func (p *ColvirProviderImpl) handleColvirRespErrStatusCode(ctx context.Context, resp *http.Response) (*entity.ColvirErrResp, error) {
	logger := logs.FromContext(ctx)
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body for create client request: %w", err)
	}
	logger.Error().Msgf("failed to get response from COLVIR, status code: %d, body: %s",
		resp.StatusCode, string(bodyBytes))
	errResp, err := handleColvirErrorResp(resp.StatusCode, bodyBytes)
	if err != nil {
		return nil, err
	}

	return errResp, nil
}

// RequestUpdateClient - Метод обновления карточки клиента в АБИС Колвир
// https://rmrkz.atlassian.net/wiki/spaces/ZMNRET/pages/27394079
func (p *ColvirProviderImpl) RequestUpdateClient(ctx context.Context, request *entity.UpdateClientRequest) (*entity.UpdateClientResp, error) {
	targetURL, err := p.getRequestUpdateClientURL()
	if err != nil {
		return nil, err
	}

	reqBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal body for update client request: %w", err)
	}

	logs.FromContext(ctx).Debug().Interface("RequestUpdateClient req body", string(reqBody))

	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		targetURL,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create new http request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := p.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to do http request: %w", err)
	}
	defer resp.Body.Close()

	var respBody entity.UpdateClientResp

	if resp.StatusCode != http.StatusOK {
		errResp, err := p.handleColvirRespErrStatusCode(ctx, resp)
		if err != nil {
			return nil, err
		}

		respBody.ColvirError = errResp
		return &respBody, ErrInternalServerError
	}

	err = json.NewDecoder(resp.Body).Decode(&respBody)
	if err != nil {
		return nil, fmt.Errorf("failed to decode http response body: %w", err)
	}

	return &respBody, nil
}

// RequestGetClient - Метод получения карточки клиента в АБИС Колвир
// https://rmrkz.atlassian.net/wiki/spaces/ZMNRET/pages/24282306
func (p *ColvirProviderImpl) RequestGetClient(ctx context.Context, req *entity.GetClientRequest) (*entity.GetClientResponse, error) {
	targetURL, err := p.getRequestClientURL()
	if err != nil {
		return nil, fmt.Errorf("failed to get request get client url: %w", err)
	}

	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	request, err := http.NewRequestWithContext(ctx, http.MethodGet, targetURL, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create new http request: %w", err)
	}
	request.Header.Set("Content-Type", "application/json")

	resp, err := p.HTTPClient.Do(request)
	if err != nil {
		return nil, fmt.Errorf("failed to do http request: %w", err)
	}
	defer resp.Body.Close()

	var respBody entity.GetClientResponse
	if resp.StatusCode != http.StatusOK {
		errResp, err := p.handleColvirRespErrStatusCode(ctx, resp)
		if err != nil {
			return nil, err
		}

		respBody.ColvirError = errResp
		return &respBody, ErrInternalServerError
	}

	err = json.NewDecoder(resp.Body).Decode(&respBody)
	if err != nil {
		return nil, fmt.Errorf("failed to decode http response body: %w", err)
	}

	return &respBody, nil
}

// RequestGetClientIP - Метод получения карточки клиента ИП в АБИС Колвир
func (p *ColvirProviderImpl) RequestGetClientIP(ctx context.Context, req *entity.GetClientIPRequest) (*entity.GetClientIPResult, error) {
	targetURL, err := p.getRequestClientIPURL()
	if err != nil {
		return nil, fmt.Errorf("failed to get request get client ip url: %w", err)
	}

	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	request, err := http.NewRequestWithContext(ctx, http.MethodGet, targetURL, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create new http request: %w", err)
	}
	request.Header.Set("Content-Type", "application/json")

	resp, err := p.HTTPClient.Do(request)
	if err != nil {
		return nil, fmt.Errorf("failed to do http request: %w", err)
	}
	defer resp.Body.Close()

	var respBody entity.GetClientIPResult
	if resp.StatusCode != http.StatusOK {
		errResp, err := p.handleColvirRespErrStatusCode(ctx, resp)
		if err != nil {
			return nil, err
		}

		respBody.ColvirError = errResp
		return &respBody, ErrInternalServerError
	}

	err = json.NewDecoder(resp.Body).Decode(&respBody)
	if err != nil {
		return nil, fmt.Errorf("failed to decode http response body: %w", err)
	}

	return &respBody, nil
}

func (p *ColvirProviderImpl) RequestOpenClientCard(ctx context.Context, req *entity.OpenClientCardRequest) (*entity.OpenClientCardResponse, error) {
	targetURL, err := p.getRequestOpenClientCardURL()
	if err != nil {
		return nil, fmt.Errorf("failed to get request open client card url: %w", err)
	}

	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	request, err := http.NewRequestWithContext(ctx, http.MethodPost, targetURL, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create new http request: %w", err)
	}
	request.Header.Set("Content-Type", "application/json")

	resp, err := p.HTTPClient.Do(request)
	if err != nil {
		return nil, fmt.Errorf("failed to do http request: %w", err)
	}
	defer resp.Body.Close()

	var respBody entity.OpenClientCardResponse
	if resp.StatusCode != http.StatusOK {
		errResp, err := p.handleColvirRespErrStatusCode(ctx, resp)
		if err != nil {
			return nil, err
		}

		respBody.ColvirError = errResp
		return &respBody, ErrInternalServerError
	}

	err = json.NewDecoder(resp.Body).Decode(&respBody)
	if err != nil {
		return nil, fmt.Errorf("failed to decode http response body: %w", err)
	}

	return &respBody, nil
}

func (p *ColvirProviderImpl) RequestGetClientCard(ctx context.Context, req *entity.GetClientCardRequest) (*entity.GetClientCardResponse, error) {
	targetURL, err := p.getRequestGetClientCardURL()
	if err != nil {
		return nil, fmt.Errorf("failed to get request get client card url: %w", err)
	}

	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	request, err := http.NewRequestWithContext(ctx, http.MethodGet, targetURL, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create new http request: %w", err)
	}
	request.Header.Set("Content-Type", "application/json")

	resp, err := p.HTTPClient.Do(request)
	if err != nil {
		return nil, fmt.Errorf("failed to do http request: %w", err)
	}
	defer resp.Body.Close()

	var respBody entity.GetClientCardResponse
	if resp.StatusCode != http.StatusOK {
		errResp, err := p.handleColvirRespErrStatusCode(ctx, resp)
		if err != nil {
			return nil, err
		}

		respBody.ColvirError = errResp
		return &respBody, ErrInternalServerError
	}

	err = json.NewDecoder(resp.Body).Decode(&respBody)
	if err != nil {
		return nil, fmt.Errorf("failed to decode http response body: %w", err)
	}

	return &respBody, nil
}

func (p *ColvirProviderImpl) RequestGetClientIPCard(ctx context.Context, req *entity.GetClientIPCardRequest) (*entity.GetClientIPCardResponse, error) {
	targetURL, err := p.getRequestGetClientIPCardURL()
	if err != nil {
		return nil, fmt.Errorf("failed to get request get client ip card url: %w", err)
	}

	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	request, err := http.NewRequestWithContext(ctx, http.MethodGet, targetURL, bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create new http request: %w", err)
	}
	request.Header.Set("Content-Type", "application/json")

	resp, err := p.HTTPClient.Do(request)
	if err != nil {
		return nil, fmt.Errorf("failed to do http request: %w", err)
	}
	defer resp.Body.Close()

	var respBody entity.GetClientIPCardResponse
	if resp.StatusCode != http.StatusOK {
		errResp, err := p.handleColvirRespErrStatusCode(ctx, resp)
		if err != nil {
			return nil, err
		}

		respBody.ColvirError = errResp
		return &respBody, ErrInternalServerError
	}

	err = json.NewDecoder(resp.Body).Decode(&respBody)
	if err != nil {
		return nil, fmt.Errorf("failed to decode http response body: %w", err)
	}

	return &respBody, nil
}

func (p *ColvirProviderImpl) getRequestCreateClientURL() (string, error) {
	result, err := url.JoinPath(p.V2BaseURL, "/IBClients/Customer/CreateFiz")
	if err != nil {
		return "", fmt.Errorf("failed to join path for create client request: %w", err)
	}

	return result, nil
}

func (p *ColvirProviderImpl) getRequestUpdateClientURL() (string, error) {
	result, err := url.JoinPath(p.V2BaseURL, "/IBClients/Customer/UpdateFiz")
	if err != nil {
		return "", fmt.Errorf("failed to join path for update client request: %w", err)
	}

	return result, nil
}

func (p *ColvirProviderImpl) getRequestClientURL() (string, error) {
	result, err := url.JoinPath(p.V2BaseURL, "/IBClients/Customer/GetFiz")
	if err != nil {
		return "", fmt.Errorf("failed to join path for get client card request: %w", err)
	}

	return result, nil
}

func (p *ColvirProviderImpl) getRequestClientIPURL() (string, error) {
	result, err := url.JoinPath(p.V2BaseURL, "/IBClients/Customer/GetIP")
	if err != nil {
		return "", fmt.Errorf("failed to join path for get client card request: %w", err)
	}

	return result, nil
}

func (p *ColvirProviderImpl) getRequestOpenClientCardURL() (string, error) {
	result, err := url.JoinPath(p.V2BaseURL, "/IBClients/Customer/Open")
	if err != nil {
		return "", fmt.Errorf("failed to join path for open client card request: %w", err)
	}

	return result, nil
}

func (p *ColvirProviderImpl) getRequestGetClientCardURL() (string, error) {
	result, err := url.JoinPath(p.V2BaseURL, "/IBClients/Customer/GetFiz")
	if err != nil {
		return "", fmt.Errorf("failed to join path for get client card request: %w", err)
	}

	return result, nil
}

func (p *ColvirProviderImpl) getRequestGetClientIPCardURL() (string, error) {
	result, err := url.JoinPath(p.V2BaseURL, "/IBClients/Customer/GetIP")
	if err != nil {
		return "", fmt.Errorf("failed to join path for get client ip card request: %w", err)
	}

	return result, nil
}
